package routers

import (
	"acg-ai-go-common/logger"
	"dhlive-dialogue/handler"

	"github.com/gin-gonic/gin"
)

var (
	GinRouter = gin.Default()
)

// InitRouter 初始化gin routers
func InitRouter() {
	// 统一gin的日志到统一的logger里面
	GinRouter.Use(logger.GinLog())
	// Router
	CgiRouter(GinRouter)
	HealthRouter(GinRouter)
}

func CgiRouter(e *gin.Engine) {
	// 对话接口
	GinRouter.POST("/chat", handler.ChatRequest)

	// 用户相关的接口
	GinRouter.POST("/user/doclist", handler.DocUserList)
	GinRouter.POST("/user/config", handler.UserConfig)

	// 产品相关
	GinRouter.POST("/product/doclist", handler.DocGoodsList)

	// 文档相关
	GinRouter.POST("/document/docdetail", handler.DocDetail)
}
