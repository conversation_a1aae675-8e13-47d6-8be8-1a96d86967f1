package main

import (
	"acg-ai-go-common/global"
	"acg-ai-go-common/gomysql"
	"acg-ai-go-common/goredis"
	"acg-ai-go-common/logger"
	"acg-ai-go-common/server"
	"dhlive-dialogue/config"
	"dhlive-dialogue/routers"
)

func main() {
	// 初始化公共配置
	server.InitGlobalSetting()

	// 初始化redis
	goredis.InitRedisV2()
	// 初始化mysql
	gomysql.InitDB(global.ServerSetting.MysqlSetting)

	logger.SetLogger()
	// 初始化路由
	routers.InitRouter()
	// 初始化配置
	config.InitConfig()

	// 启动服务
	server.Run(routers.GinRouter)
}
