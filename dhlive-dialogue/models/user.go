package models

// 用户配置
type UserConfig struct {
	Id           string `json:"id" gorm:"column:id;primarykey;autoIncrement"` // id
	UserId       string `json:"userId" gorm:"column:user_id"`                 // user_id
	UserName     string `json:"userName" gorm:"column:user_name"`             // user_name
	PayTime      int32  `json:"payTime" gorm:"column:pay_time"`               // paytime
	UsedTime     int32  `json:"usedTime" gorm:"column:used_time"`             // paytime
	Permission   string `json:"permission" gorm:"column:permission"`          // permission
	UserType     string `json:"userType" gorm:"column:user_type"`             // userType
	Source       string `json:"source" gorm:"column:source"`                  // userType
	EnableBanner bool   `json:"enableBanner" gorm:"column:enable_banner"`     // enableBanner
}

func (ot *UserConfig) TableName() string {
	return "user_config"
}

// 用户配置信息
type UserConfReq struct {
	UserName string `json:"userName"`
}
