package models

type DocumentType struct {
	TextFaqLib   string
	AudioFaqLib  string
	GoodsContent string
}

var DocType DocumentType = DocumentType{
	TextFaqLib:   "TEXT_FAQ_LIB",
	AudioFaqLib:  "AUDIO_FAQ_LIB",
	GoodsContent: "GOODS_CONTENT",
}

// 产品文档记录
type GoodDocument struct {
	ID         uint64 `json:"id" gorm:"column:id;primarykey;autoIncrement"` // 数据ID
	GoodId     uint64 `json:"goodId" gorm:"column:goods_id"`                // 产品ID
	DocumentId string `json:"document_id" gorm:"column:document_id"`        // 文档id
	Type       string `json:"type" gorm:"column:type"`                      // 文档类别
}

func (ot *GoodDocument) TableName() string {
	return "goods_document"
}

// document
type Document struct {
	ID         uint64 `json:"id" gorm:"column:id;primarykey;autoIncrement"` // 数据ID
	UserId     string `json:"userId" gorm:"column:user_id"`                 // user_id
	DatasetId  string `json:"dataSetId" gorm:"column:dataset_id"`           // dataset_id
	DocumentId string `json:"document_id" gorm:"column:document_id"`        // 文档id
	Status     string `json:"status" gorm:"column:status"`                  // status
	Content    string `json:"content" gorm:"column:content"`                // content
	CreateTime string `json:"createTime" gorm:"column:create_time"`         // create_time
	UpdateTime string `json:"updateTime" gorm:"column:update_time"`         // update_time
}

func (ot *Document) TableName() string {
	return "documents"
}
