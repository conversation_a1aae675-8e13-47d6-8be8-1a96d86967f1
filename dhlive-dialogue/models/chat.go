package models

type ChatSoreThreshold struct {
	FaqScoreThreshold          float32 `toml:"FaqScoreThreshold"`          // faq阈值
	GoodsContentScoreThreshold float32 `toml:"goodsContentScoreThreshold"` // 商品内容阈值
}

// chat请求
type ChatReq struct {
	LogId          string            `json:"logId"`
	CurGoodId      int32             `json:"curGoodId"`     // 当前的讲解商品
	Query          string            `json:"query"`         // query
	ScoreThreshold ChatSoreThreshold `json:"soreThreshold"` // 阈值
	FaqEnable      bool              `json:"faqEnable"`     // faq是否开启
	AiFaqEnable    bool              `json:"aiFaqEnable"`   // ai智能是否开启
	IsDebug        bool              `json:"isDebug"`       // isDebug
}

// chatRsp AudioModel
type ChatRspAudioModel struct {
	Id                     int32   `json:"id"`
	UserId                 string  `json:"userId"`
	Name                   string  `json:"name"`
	Url                    string  `json:"url"`
	Size                   float32 `json:"size"`
	Length                 float32 `json:"length"`
	Format                 string  `json:"format"`
	VisibleForAudioLibrary int     `json:"visibleForAudioLibrary"`
	CreateTime             string  `json:"createTime"`
	UpdateTime             string  `json:"updateTime"`
}

// chatRsp answer
type ChatRspAnswer struct {
	Text        string             `json:"text"`
	AudioUrl    string             `json:"audioUrl"`
	Audio       *ChatRspAudioModel `json:"audio"`
	LastSegment bool               `json:"lastSegment"`
}

type ChatRsp struct {
	Code   int                    `json:"code"`
	Msg    string                 `json:"msg"`
	Data   map[string]interface{} `json:"data"`
	CallOk bool                   `json:"callOk"`
}
