package service

import (
	"acg-ai-go-common/gomysql"
	"acg-ai-go-common/logger"
	"dhlive-dialogue/models"
)

// 查询用户的配置信息
func UserConfig(userName string) (*[]models.UserConfig, error) {
	var userConfList []models.UserConfig
	err := gomysql.DB.Where("user_name = ?", userName).Find(&userConfList).Error
	return &userConfList, err
}

// 查询用户的文档列表
func UserDocumentList(userName string) (*[]models.Document, error) {
	userList, err := UserConfig(userName)
	logger.Log.Printf("userName:%v err:%v userList len:%v", userName, err, len(*userList))
	if err != nil {
		return nil, err
	}

	var modelsList []models.Document
	if len(*userList) == 0 {
		return &modelsList, nil
	}

	userConf := (*userList)[0]
	err = gomysql.DB.Where("user_id = ?", userConf.UserId).Find(&modelsList).Error
	return &modelsList, err
}
