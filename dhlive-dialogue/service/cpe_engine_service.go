package service

import (
	"acg-ai-go-common/logger"
	"context"
	"dhlive-dialogue/config"
	"dhlive-dialogue/service/beans"
	"dhlive-dialogue/utils"
	"encoding/json"

	dialogueutil "dhlive-dialogue/utils"
)

// cpe文档问答函数
func CpeHitTesting(logCtx context.Context, documentId string, query string) (*beans.CpeHitTestRsp, error) {
	// 创建一个User结构体实例，并转换成JSON格式
	hitTestReq := beans.CpeHitTestReq{
		DatasetId:  config.ConfIns.DocCfg.DatasetId,
		DocumentId: documentId,
		Query:      query,
	}

	logger.Log.Infof(utils.MMark(logCtx)+"documentId:%v hitTestRsp is nil:[%v] \n", documentId)
	rspData, err := dialogueutil.HttpPost(logCtx, config.ConfIns.DocCfg.HostPort+config.ConfIns.DocCfg.UrlHitTest,
		GetCpeCommHeader(), hitTestReq)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"HttpPost:%v \n", err)
		return nil, err
	}

	rsp := beans.CpeHitTestRsp{}
	err = json.Unmarshal(*rspData, &rsp)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"rspData:%v\n", string(*rspData))
		logger.Log.Errorf(utils.MMark(logCtx)+"json.Unmarshal err:%v \n", err)
		return nil, err
	}

	return &rsp, nil
}
