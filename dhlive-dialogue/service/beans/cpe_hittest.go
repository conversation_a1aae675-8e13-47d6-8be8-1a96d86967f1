package beans

// copilot engine hittest接口的请求
type CpeHitTestReq struct {
	DatasetId  string `json:"dataset_id"`  // 数据集id
	DocumentId string `json:"document_id"` // 文档id
	Query      string `json:"query"`       // query
}

type CpeHitTestRspResultQuery struct {
	Content string `json:"content"`
}

type CpeHitTestRspSentence struct {
	Id      string  `json:"id"`
	Content string  `json:"content"`
	Source  string  `json:"source"`
	Score   float32 `json:"score"`
}

type CpeHitTestRspSement struct {
	Id           string                  `json:"id"`
	Position     int16                   `json:"position"`
	DocumentId   string                  `json:"document_id"`
	DocumentName string                  `json:"document_name"`
	Content      string                  `json:"content"`
	WordCount    int                     `json:"word_count"`
	Enabled      bool                    `json:"enabled"`
	Status       string                  `json:"status"`
	Sentences    []CpeHitTestRspSentence `json:"sentences"`
}

type CpeHitTestRspRecord struct {
	Segment CpeHitTestRspSement `json:"segment"`
	Score   float32             `json:"score"`
}

type CpeHitTestRspResult struct {
	Query   CpeHitTestRspResultQuery `json:"query"`
	Records []CpeHitTestRspRecord    `json:"records"`
}

// copilot engine hittest接口的返回值
type CpeHitTestRsp struct {
	Code    int                 `json:"code"`
	Message string              `json:"message"`
	Result  CpeHitTestRspResult `json:"result"`
}
