package service

import (
	"acg-ai-go-common/gomysql"
	"dhlive-dialogue/models"
)

// 查询document
func GoodsDocuments(goodId uint64) (*[]models.GoodDocument, error) {
	var modelsList []models.GoodDocument
	err := gomysql.DB.Where("goods_id = ?", goodId).Find(&modelsList).Error
	return &modelsList, err
}

// 查询一个文档的详情
func DocumentDetail(docId string) (*models.Document, error) {
	var docDetail models.Document
	err := gomysql.DB.Where("document_id = ?", docId).Find(&docDetail).Error
	return &docDetail, err
}
