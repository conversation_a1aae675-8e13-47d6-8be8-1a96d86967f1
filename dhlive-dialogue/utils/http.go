package utils

import (
	"acg-ai-go-common/logger"
	"bytes"
	"context"
	"crypto/tls"
	"encoding/json"
	"io"
	"net/http"
	"time"
)

// http post请求
func HttpPost(logCtx context.Context, url string, headers map[string]string, reqData interface{}) (*[]byte, error) {
	jsonData, _ := json.Marshal(reqData)
	headerData, _ := json.Marshal(headers)
	logger.Log.Infof(MMark(logCtx)+"request info:\n\turl:%v\n\treq header:%v\n\treq body:%v", url, string(headerData), string(jsonData))
	// 创建请求
	req, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonData))
	if err != nil {
		logger.Log.Errorf(MMark(logCtx)+"post err:%v \n", err)
		return nil, err
	}

	// 设置请求头，告诉服务器我们发送的是JSON格式数据
	req.Header.Set("Content-Type", "application/json")

	for k, v := range headers {
		req.Header.Set(k, v)
	}

	t := http.DefaultTransport.(*http.Transport).Clone()
	//不对证书进行校验
	t.TLSClientConfig = &tls.Config{InsecureSkipVerify: true}
	t.MaxConnsPerHost = 200
	t.MaxIdleConnsPerHost = 200
	// 发送请求
	client := &http.Client{
		Timeout:   20 * time.Second,
		Transport: t,
	}
	resp, err := client.Do(req)
	if err != nil {
		logger.Log.Errorf("client.Do err:%v \n", err)
		return nil, err
	}

	defer func() {
		if resp != nil && resp.Body != nil {
			resp.Body.Close()
		}
	}()

	// 读取响应体
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		logger.Log.Errorf("io.ReadAll err:%v \n", err)
		return nil, err
	}
	logger.Log.Infof(MMark(logCtx)+"rsp body:%v\n", string(body))
	return &body, nil
}
