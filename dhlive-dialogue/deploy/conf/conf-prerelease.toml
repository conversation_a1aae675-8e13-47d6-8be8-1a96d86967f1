####################################################### 服务配置-开发环境 #######################################################
server-port = 8080
server-name = "dhlive-dialogue"
log-file-prefix = "localhost"
log-file-path = "./logs/"
log-show-code-file = true
log-show-code-func = false

# consul配置
[consul-setting]
host = "127.0.0.1"
port = 8500
name = ""
# 健康检查地址
health-url = "/health"
check-timeout = "10s"
check-interval = "10s"
deregister-critical-service-after = "30s"

# mysql配置
[mysql-setting]
host = "mysql57.rdsml05scrp4zn8.rds.bj.baidubce.com"
port = 3306
database = "dhlive_third_platform_ppe"
username = "dhlive_tp_plat"
password = "Hi109@123"
maxOpenConns = 1000
maxIdlenConns = 100

# redis配置
[redis-setting]
addr = "redis.zytsboaklxlp.scs.bj.baidubce.com:6379"
username = ""
password = ""

# elasticsearch配置
[es-setting]
host = "127.0.0.1"
port = 9200
user-name = ""
password = ""

# copilot engine配置
[copilot-engine]
hostPort = "https://127.0.0.1:38000/"
accountToken = "Bearer app-ye5gCzxvrLE3iWazB3NOIxa5"
datasetId = "665de7c5-075d-4ed8-9728-8906262c0dde"
faqScoreThreshold = 0.7
goodsContentScoreThreshold = 0.28
urlHitTest = "v1/api/datasets/hit-testing"