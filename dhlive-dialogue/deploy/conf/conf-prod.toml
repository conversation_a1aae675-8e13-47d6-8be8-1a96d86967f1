####################################################### 服务配置-生产环境 #######################################################
server-port = 8080
server-name = "dhlive-dialogue"
log-file-prefix = "localhost"
log-file-path = "./logs/"
log-show-code-file = true
log-show-code-func = false
# consul配置
[consul-setting]
host = "127.0.0.1"
port = 8500
name = ""

# consul配置
[consul-setting]
host = "127.0.0.1"
port = 8500
name = ""
# 健康检查地址
health-url = "/health"
check-timeout = "10s"
check-interval = "10s"
deregister-critical-service-after = "30s"

# mysql配置
[mysql-setting]
url = "**********************************************************************"
user-name = "root"
password = "123456"

# redis配置
[redis-setting]
sentinelNodes = "************:27110,*************:27110,************:27110" # 哨兵集群节点，ip:port,ip:port,ip:port...，多个节点使用,分割，eg: "127.0.0.1:26379,127.0.0.1:26380,127.0.0.1:26381"
sentinelUsername = ""
sentinelPassword = ""
routeByLatency = false
masterName = "mymaster"
username = ""
password = "Hi109.3"
poolSize = 1000
minIdleConns = 100

# elasticsearch配置
[es-setting]
host = "127.0.0.1"
port = 9200
user-name = ""
password = ""

# copilot engine配置
[copilot-engine]
hostPort = "https://127.0.0.1:38000/"
accountToken = "Bearer app-ye5gCzxvrLE3iWazB3NOIxa5"
datasetId = "665de7c5-075d-4ed8-9728-8906262c0dde"
faqScoreThreshold = 0.7
goodsContentScoreThreshold = 0.28
urlHitTest = "v1/api/datasets/hit-testing"