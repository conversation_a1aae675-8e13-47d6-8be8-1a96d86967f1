package config

import (
	"acg-ai-go-common/global"
	"acg-ai-go-common/logger"
	"encoding/json"

	"github.com/BurntSushi/toml"
)

type DocumentCfg struct {
	HostPort                   string  `toml:"hostPort"`                   // 请求地址
	AccountToken               string  `toml:"accountToken"`               // token
	DatasetId                  string  `toml:"datasetId"`                  // datasetId
	FaqScoreThreshold          float32 `toml:"FaqScoreThreshold"`          // faq阈值
	GoodsContentScoreThreshold float32 `toml:"goodsContentScoreThreshold"` // 商品内容阈值
	UrlHitTest                 string  `toml:"urlHitTest"`                 // hitTest接口
}

type Config struct {
	DocCfg DocumentCfg `toml:"copilot-engine"`
}

const (
	CtxKeyLogId = "logId"
	ReplyPrefix = "公屏有宝宝说,直播间有家人说,主播看到有宝宝发弹幕说,回答一下公屏上宝宝的留言哈,刚刚看到一条弹幕"
)

var ConfIns Config

// 初始化配置信息
func InitConfig() {
	// 加载配置
	if _, err := toml.DecodeFile(global.ConfFilePath, &ConfIns); err != nil {
		logger.Log.Errorf("配置加载失败: {%v}", err)
		// 这个配置加载失败，后面无法执行
		panic(err)
	}

	b, _ := json.Marshal(ConfIns)
	logger.Log.Printf("InitConfig:%v \n", string(b))
}
