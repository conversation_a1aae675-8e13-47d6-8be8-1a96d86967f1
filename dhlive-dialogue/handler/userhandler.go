package handler

import (
	"acg-ai-go-common/logger"
	"dhlive-dialogue/models"
	"dhlive-dialogue/service"
	"encoding/json"
	"io"
	"net/http"

	"github.com/gin-gonic/gin"
)

// 根据用户名查询它名下的文档列表
func UserConfig(c *gin.Context) {
	// 1-解析请求参数
	data, _ := io.ReadAll(c.Request.Body)
	logger.Log.Printf("body: %v\n", string(data))

	var req models.UserConfReq
	err := json.Unmarshal(data, &req)
	if err != nil {
		c.JSON(http.StatusOK, models.ChatRsp{Code: ErrCodeParamErr, Msg: ErrMsgParamErr})
		return
	}

	var rspData map[string]interface{} = map[string]interface{}{}
	userConfs, err := service.UserConfig(req.UserName)
	if err != nil {
		logger.Log.Errorf("err:%v\n", err)
		c.<PERSON>(http.StatusOK, models.ReqRsp{
			Code: -1,
			Msg:  err.Error(),
			Data: rspData,
		})
		return
	}

	rspData["userConfs"] = userConfs
	c.JSON(http.StatusOK, models.ReqRsp{
		Code: -1,
		Msg:  "sucess",
		Data: rspData,
	})
}
