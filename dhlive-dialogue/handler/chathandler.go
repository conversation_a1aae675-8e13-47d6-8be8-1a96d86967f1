package handler

import (
	"acg-ai-go-common/logger"
	"context"
	"dhlive-dialogue/config"
	"dhlive-dialogue/models"
	"dhlive-dialogue/service"
	"dhlive-dialogue/service/beans"
	"dhlive-dialogue/utils"
	"encoding/json"
	"io"
	"math/rand"
	"net/http"
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"
)

const (
	ErrCodeParamErr  = -1000 // 参数异常
	ErrCodeNoGoods   = -1001 // 没有对应的商品
	ErrCodeGoodsExce = -1002 // 查询商品文档异常

	ErrMsgParamErr      = "param is error"
	ErrMsgParamValueErr = "params must be: 'curGoodId'>0 and 'logId'!=empty and 'query'!=empty"
	ErrMsgNodGoods      = "goods can't find"
	ErrMsgNodGoodsExce  = "query goods err: "
)

// 问答的入口函数
func ChatRequest(c *gin.Context) {
	// 1-解析请求参数
	data, _ := io.ReadAll(c.Request.Body)
	logger.Log.Printf("body:%v\n", string(data))

	var req models.ChatReq
	err := json.Unmarshal(data, &req)
	if err != nil {
		c.JSON(http.StatusOK, models.ChatRsp{Code: ErrCodeParamErr, Msg: ErrMsgParamErr})
		return
	}

	// 参数判断
	if req.CurGoodId <= 0 || req.LogId == "" || req.Query == "" {
		c.JSON(http.StatusOK, models.ChatRsp{Code: ErrCodeParamErr, Msg: ErrMsgParamValueErr})
		return
	}

	if req.ScoreThreshold.FaqScoreThreshold == 0 || req.ScoreThreshold.GoodsContentScoreThreshold == 0 {
		req.ScoreThreshold.FaqScoreThreshold = config.ConfIns.DocCfg.FaqScoreThreshold
		req.ScoreThreshold.GoodsContentScoreThreshold = config.ConfIns.DocCfg.GoodsContentScoreThreshold
	}

	logId := strconv.FormatInt(int64(req.CurGoodId), 10) + "_" + req.LogId
	logCtx := context.WithValue(context.TODO(), config.CtxKeyLogId, logId)

	b, _ := json.Marshal(req)
	logger.Log.Printf(utils.MMark(logCtx)+"req:[%v]\n", string(b))

	var rspData map[string]interface{} = map[string]interface{}{}
	var rspDebugData map[string]interface{} = map[string]interface{}{}
	docs, err := service.GoodsDocuments(uint64(req.CurGoodId))
	if req.IsDebug {
		rspDebugData["doc"] = docs
	}
	if err != nil {
		c.JSON(http.StatusOK, models.ChatRsp{
			Code: ErrCodeGoodsExce, Msg: ErrMsgNodGoodsExce + err.Error(), Data: rspData,
		})
		return
	}

	// 召回成功的处理函数
	FuncCallOk := func(isOk bool, rspAnswer *models.ChatRspAnswer, hitTestRsp *beans.CpeHitTestRsp) {
		rspData["answer"] = rspAnswer
		rspData["callOk"] = true
		if req.IsDebug {
			rspDebugData["hitRsp"] = hitTestRsp
			rspData["debug"] = rspDebugData
		}
		c.JSON(http.StatusOK, models.ChatRsp{
			Code: 0,
			Msg:  "sucess",
			Data: rspData,
		})
	}

	// 2,Faq召回
	if req.FaqEnable {
		isOk, rspAnswer, hitTestRsp, _ := CallFaq(logCtx, req, docs)
		if isOk {
			FuncCallOk(isOk, rspAnswer, hitTestRsp)
			return
		}
	}
	// 3,智能文档召回
	if req.AiFaqEnable {
		isOk, rspAnswer, hitTestRsp, _ := CallGoodsContent(logCtx, req, docs)
		if isOk {
			FuncCallOk(isOk, rspAnswer, hitTestRsp)
			return
		}
	}

	if req.IsDebug {
		rspData["debug"] = rspDebugData
	}
	rspData["callOk"] = false
	c.JSON(http.StatusOK, models.ChatRsp{
		Code: 0,
		Msg:  "sucess",
		Data: rspData,
	})
}

// faq召回
func CallFaq(logCtx context.Context, req models.ChatReq, docs *[]models.GoodDocument) (bool,
	*models.ChatRspAnswer, *beans.CpeHitTestRsp, error) {
	if len(*docs) <= 0 {
		return false, nil, nil, nil
	}

	// 调用copilot engine接口
	for _, doc := range *docs {
		// 只召回faq类型
		if doc.Type == models.DocType.GoodsContent {
			continue
		}

		b, _ := json.Marshal(doc)
		logger.Log.Infof(utils.MMark(logCtx)+"doc:%v \n", string(b))
		hitTestRsp, err := service.CpeHitTesting(logCtx, doc.DocumentId, req.Query)
		if err != nil || hitTestRsp == nil {
			logger.Log.Infof(utils.MMark(logCtx)+"err:%v hitTestRsp is nil:[%v] \n", err, hitTestRsp == nil)
			continue
		}

		for _, record := range hitTestRsp.Result.Records {
			if record.Score >= req.ScoreThreshold.FaqScoreThreshold {
				var lists []string
				err = json.Unmarshal([]byte(record.Segment.Content), &lists)
				if err != nil {
					continue
				}

				var answer models.ChatRspAnswer
				// 文本faq类型
				if models.DocType.TextFaqLib == doc.Type {
					// 召回当前的结果
					str := lists[rand.Intn(len(lists))]
					answer.Text = embellishReply(req.Query, str)
					answer.LastSegment = true
					return true, &answer, hitTestRsp, nil
				} else if models.DocType.AudioFaqLib == doc.Type {
					// todo: 音频faq
					return true, &answer, hitTestRsp, nil
				} else {
					continue
				}
			}
		}
	}

	return false, nil, nil, nil
}

// faq召回
func CallGoodsContent(logCtx context.Context, req models.ChatReq, docs *[]models.GoodDocument) (bool,
	*models.ChatRspAnswer, *beans.CpeHitTestRsp, error) {
	if len(*docs) <= 0 {
		return false, nil, nil, nil
	}

	// 调用copilot engine接口
	for _, doc := range *docs {
		// 只商品内容类型
		if doc.Type != models.DocType.GoodsContent {
			continue
		}

		b, _ := json.Marshal(doc)
		logger.Log.Infof(utils.MMark(logCtx)+"doc:%v \n", string(b))
		hitTestRsp, err := service.CpeHitTesting(logCtx, doc.DocumentId, req.Query)
		if err != nil || hitTestRsp == nil {
			logger.Log.Infof(utils.MMark(logCtx)+"err:%v hitTestRsp is nil:[%v] \n", err, hitTestRsp == nil)
			continue
		}

		for _, record := range hitTestRsp.Result.Records {
			if record.Score >= req.ScoreThreshold.GoodsContentScoreThreshold {
				if record.Segment.Content != "" {
					var answer models.ChatRspAnswer
					// 商品文档类型
					answer.Text = record.Segment.Content
					answer.LastSegment = true
					return true, &answer, hitTestRsp, nil
				}
			}
		}
	}

	return false, nil, nil, nil
}

// 组合回复结果
func embellishReply(query string, anwser string) string {
	lists := strings.Split(config.ReplyPrefix, ",")
	return lists[rand.Intn(len(lists))] + "," + query + "," + anwser
}
