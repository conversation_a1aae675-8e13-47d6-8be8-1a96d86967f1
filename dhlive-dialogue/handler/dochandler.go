package handler

import (
	"acg-ai-go-common/logger"
	"dhlive-dialogue/models"
	"dhlive-dialogue/service"
	"encoding/json"
	"io"
	"net/http"

	"github.com/gin-gonic/gin"
)

// 查询一个用户名下面的文档列表
type DocUserDocListReq struct {
	UserName    string `json:"userName"`    // 用户名，2D平台的用户名
	ShowContent bool   `json:"showContent"` // 是否显示content
}

type DocUserDocListRsp struct {
	Code    int                `json:"code"`
	Msg     string             `json:"msg"`
	DocList *[]models.Document `json:"docList"`
}

// 根据一个产品查询文档列表
type DocGoodsDocListReq struct {
	GoodsId int `json:"goodsId"`
}

type DocUserGoodsDocListRsp struct {
	Code         int                    `json:"code"`
	Msg          string                 `json:"msg"`
	GoodsDocList *[]models.GoodDocument `json:"goodsDocList"`
}

// 根据一个文档id获取文档详情
type DocDetailReq struct {
	DocId string `json:"docId"`
}

type DocDetaiRsp struct {
	Code      int              `json:"code"`
	Msg       string           `json:"msg"`
	DocDetail *models.Document `json:"docDetail"`
}

// 根据用户名查询它名下的文档列表
func DocUserList(c *gin.Context) {
	// 1-解析请求参数
	data, _ := io.ReadAll(c.Request.Body)
	logger.Log.Printf("body:%v\n", string(data))

	var req DocUserDocListReq
	err := json.Unmarshal(data, &req)
	if err != nil {
		c.JSON(http.StatusOK, models.ChatRsp{Code: ErrCodeParamErr, Msg: ErrMsgParamErr})
		return
	}

	// 2-查询sql数据库
	originRes, err := service.UserDocumentList(req.UserName)
	var finalRes []models.Document
	if req.ShowContent {
		finalRes = append(finalRes, (*originRes)...)
	} else {
		for _, doc := range *originRes {
			doc.Content = ""
			finalRes = append(finalRes, doc)
		}
	}
	if err != nil {
		c.JSON(http.StatusOK, DocUserDocListRsp{
			Code:    -1000,
			Msg:     err.Error(),
			DocList: &finalRes,
		})
		return
	}
	c.JSON(http.StatusOK, DocUserDocListRsp{
		Code:    0,
		Msg:     "sucess",
		DocList: &finalRes,
	})
}

// 根据一个产品id查询文档列表
func DocGoodsList(c *gin.Context) {
	// 1-解析请求参数
	data, _ := io.ReadAll(c.Request.Body)
	logger.Log.Printf("body:%v\n", string(data))

	var req DocGoodsDocListReq
	err := json.Unmarshal(data, &req)
	if err != nil {
		c.JSON(http.StatusOK, models.ChatRsp{Code: ErrCodeParamErr, Msg: ErrMsgParamErr})
		return
	}

	goodsList, err := service.GoodsDocuments(uint64(req.GoodsId))
	if err != nil {
		c.JSON(http.StatusOK, DocUserGoodsDocListRsp{
			Code:         -1000,
			Msg:          err.Error(),
			GoodsDocList: goodsList,
		})
		return
	}

	c.JSON(http.StatusOK, DocUserGoodsDocListRsp{
		Code:         0,
		Msg:          "sucess",
		GoodsDocList: goodsList,
	})
}

// 根据一个文档id查询文档详情
func DocDetail(c *gin.Context) {
	// 1-解析请求参数
	data, _ := io.ReadAll(c.Request.Body)
	logger.Log.Printf("body:%v\n", string(data))

	var req DocDetailReq
	err := json.Unmarshal(data, &req)
	if err != nil {
		c.JSON(http.StatusOK, models.ChatRsp{Code: ErrCodeParamErr, Msg: ErrMsgParamErr})
		return
	}

	docDetail, err := service.DocumentDetail(req.DocId)
	if err != nil {
		c.JSON(http.StatusOK, DocDetaiRsp{
			Code:      -1000,
			Msg:       err.Error(),
			DocDetail: docDetail,
		})
		return
	}

	c.JSON(http.StatusOK, DocDetaiRsp{
		Code:      0,
		Msg:       "sucess",
		DocDetail: docDetail,
	})
}
