####################################################### 服务配置-开发环境 #######################################################
server-port = 8080
server-name = "dh-video-ai"
log-file-prefix = "localhost"
log-file-path = "./logs/"
# consul配置
[consul-setting]
host = "127.0.0.1"
port = 8500
name = ""
# 健康检查地址
health-url = "/health"
check-timeout = "10s"
check-interval = "10s"
deregister-critical-service-after = "30s"

# mysql配置
[mysql-setting]
host = "localhost"
port = 3306
database = "meta_human_editor_saas"
username = "root"
password = "123456"
maxOpenConns = 1000
maxIdlenConns = 10

# redis配置
[redis-setting]
addr = "127.0.0.1:6379"
username = ""
password = ""

# 日志上传的elasticsearch配置
[log-es-setting]
host = "http://127.0.0.1:9200"
username = ""
password = ""

[dh-user-setting]
baseUrl = "http://dh-user:80"

[large-model-setting]
baseUrl = "http://127.0.0.1:8114"

[video-home-setting]
baseUrl = "http://127.0.0.1:8115"