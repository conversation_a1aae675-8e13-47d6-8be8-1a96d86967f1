---
apiVersion: v1
kind: ConfigMap
metadata:
  labels:
    app: digital-human
    module: dh-video-ai
  name: dh-video-ai
  namespace: dh-v3
data:
  conf.toml: |
    ####################################################### 服务配置-PPE环境 #######################################################
    server-port = 8080
    server-name = "dh-video-ai"
    log-file-prefix = "localhost"
    log-file-path = "./logs/"
    log-show-code-file = true
    log-show-code-func = true
    # consul配置
    [consul-setting]
    host = "127.0.0.1"
    port = 8500
    name = ""
    
    # 健康检查地址
    health-url = "/health"
    check-timeout = "10s"
    check-interval = "10s"
    deregister-critical-service-after = "30s"
    
    # mysql配置
    [mysql-setting]
    host = "mysql57-test.rdsmltt6s1aa9rz.rds.bj.baidubce.com"
    port = 3306
    database = "meta_human_editor_saas"
    username = "saas"
    password = "!Digitalhuman@baidu123"
    maxOpenConns = 1000
    maxIdlenConns = 100
    
    # redis配置
    [redis-setting]
    addr = "************:6379"
    username = ""
    password = ""
    
    # 日志上传的elasticsearch配置
    [log-es-setting]
    host = "http://*************:8200"
    username = "superuser"
    password = "Baidu_dh123"
    
    [dh-user-setting]
    baseUrl = "http://dh-user:80"
    
    [large-model-setting]
    baseUrl = "http://ecloud-large-model:8080"
---
apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: digital-human
    module: dh-video-ai
  name: dh-video-ai
  namespace: dh-v3
spec:
  replicas: 1
  minReadySeconds: 0
  strategy:
    type: RollingUpdate # 策略：滚动更新
    rollingUpdate:
      maxSurge: 25%
      maxUnavailable: 25%
  selector:
    matchLabels:
      app: digital-human
      module: dh-video-ai
  template:
    metadata:
      labels:
        app: digital-human
        module: dh-video-ai
    spec:
      affinity:
        podAntiAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            - labelSelector:
                matchExpressions:
                  - key: app
                    operator: In
                    values:
                      - digital-human
                  - key: module
                    operator: In
                    values:
                      - dh-video-ai
              topologyKey: kubernetes.io/hostname
      restartPolicy: Always
      tolerations:
        - key: "limit"
          operator: "Equal"
          value: "lite"
          effect: "NoSchedule"
      containers:
        - name: dh-video-ai
          image: ccr-246im3mw-pub.cnc.bj.baidubce.com/digitalhuman/dh-video-ai:20250414_1744612003711
          imagePullPolicy: Always
          command: [ "sh" ]
          env:
            - name: POD_NAMESPACE
              valueFrom:
                fieldRef:
                  fieldPath: metadata.namespace
          args: [ "/home/<USER>/sbin/start.sh" ]
          # imagePullPolicy: IfNotPresent
          # imagePullPolicy: Never
          ports:
            - containerPort: 8080
          livenessProbe: # 健康检查：存活探针
            httpGet:
              path: /health
              port: 8080
            initialDelaySeconds: 20
            timeoutSeconds: 5
            periodSeconds: 5
          readinessProbe: # 健康检查：就绪探针
            httpGet:
              path: /health
              port: 8080
            initialDelaySeconds: 5
            timeoutSeconds: 1
            periodSeconds: 5
          volumeMounts:
            - mountPath: /home/<USER>/conf/conf.toml
              name: config-volume
              subPath: conf.toml
      volumes:
        - configMap:
            defaultMode: 420
            name: dh-video-ai
          name: config-volume
      imagePullSecrets:
        - name: regcred
        - name: regcred-ccr
        - name: regcred-eccr
---
apiVersion: v1
kind: Service
metadata:
  labels:
    app: digital-human
    module: dh-video-ai
  name: dh-video-ai
  namespace: dh-v3
spec:
  selector:
    app: digital-human
    module: dh-video-ai
  ports:
    - protocol: TCP
      port: 8080
      targetPort: 8080
  type: ClusterIP
---
apiVersion: extensions/v1beta1
kind: Ingress
metadata:
  name: dh-video-ai-ingress
  namespace: dh-v3
  annotations:
    nginx.ingress.kubernetes.io/use-regex: "true"
spec:
  rules:
    - http:
        paths:
          - backend:
              serviceName: dh-video-ai
              servicePort: 8080
            path: /api/digitalhuman/ai-video/v1