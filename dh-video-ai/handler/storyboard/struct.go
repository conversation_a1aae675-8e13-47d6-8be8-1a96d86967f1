package storyboard

import (
	commProto2 "acg-ai-go-common/beans/proto2"
	"dh-video-ai/handler/llm"
	"encoding/json"
)

type OutlineInfo struct {
	Title    string   `json:"title"`              // 大纲标题
	Describe string   `json:"describe,omitempty"` // 大纲副标题
	Content  []string `json:"content,omitempty"`  // 大纲内容
	PageNo   string   `json:"pageNo"`             // 大纲索引
}

func (oi *OutlineInfo) ToJsonString() string {
	bts, _ := json.Marshal(oi)
	compress := jsonBytesCompress(bts)
	return string(compress)
}

type ScriptInfo struct {
	Script string `json:"script"` // 脚本内容
	PageNo string `json:"pageNo"` // 脚本索引
}

func (si *ScriptInfo) ToJsonString() string {
	bts, _ := json.Marshal(si)
	compress := jsonBytesCompress(bts)
	return string(compress)
}

func findScriptByPageNo(scriptAllContent []commProto2.CommDataRsp, pageNo string) (scriptInfo ScriptInfo, scriptInfos []ScriptInfo, index int) {
	notFoundIndex := -1
	for _, item := range scriptAllContent {
		scriptAnswer, err := InterfaceToStruct[llm.AIAnswerResult](item.Result)
		if err != nil {
			continue
		}
		scriptInfo, err = JSONStringToStruct[ScriptInfo](scriptAnswer.Content)
		if err != nil { // 解析失败，说明该item非真正的脚本信息，可能是think，直接跳过
			continue
		}
		scriptInfos = append(scriptInfos, scriptInfo)
	}
	for i, info := range scriptInfos {
		if info.PageNo == pageNo {
			return info, scriptInfos, i
		}
	}
	return scriptInfo, scriptInfos, notFoundIndex
}
