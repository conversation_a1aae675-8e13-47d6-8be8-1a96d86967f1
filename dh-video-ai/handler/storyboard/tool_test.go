package storyboard

import "testing"

func TestJsonStringParseCheck(t *testing.T) {
	var outline = "{\"pageNo\":\"1\",\"title\":\"大模型技术演进与未来布局\",\"describe\":\"深度解析千亿级参数模型的突破路径，预判行业应用边界拓展方向，为企业数字化转型提供认知升级指南\"}"
	newOutline, _, err := JsonStringParseCheck[OutlineInfo](outline)
	if err != nil {
		t.Error(err)
		return
	}
	t.Log(newOutline)
	outline = "\n{\"pageNo\":\"6\",\"title\":\"学生群体的学习启示\",\"content\":[\"掌握Prompt Engineering提升AI使用效能\",\"关注LangChain等模型集成技术\",\"理解AI伦理与数字版权保护规范\",\"培养跨学科应用创新能力\"]}"
	newOutline, _, err = JsonStringParseCheck[OutlineInfo](outline)
	if err != nil {
		t.<PERSON><PERSON><PERSON>(err)
		return
	}
	t.Log(newOutline)
	outline = "\n```json\n{\"pageNo\":\"6\",\"title\":\"学生群体的学习启示\",\"content\":[\"掌握Prompt Engineering提升AI使用效能\",\"关注LangChain等模型集成技术\",\"理解AI伦理与数字版权保护规范\",\"培养跨学科应用创新能力\"]}"
	newOutline, _, err = JsonStringParseCheck[OutlineInfo](outline)
	if err != nil {
		t.Error(err)
		return
	}
	t.Log(newOutline)
	outline = "{\"pageNo\":\"6\",\"title\":\"学生群体的学习启示\",\"content\":[\"掌握Prompt Engineering提升AI使用效能\",\"关注LangChain等模型集成技术\",\"理解AI伦理与数字版权保护规范\",\"培养跨学科应用创新能力\"]}\n```\n"
	newOutline, _, err = JsonStringParseCheck[OutlineInfo](outline)
	if err != nil {
		t.Error(err)
		return
	}
	t.Log(newOutline)
	outline = "{\\\"pageNo\\\":\\\"1\\\",\\\"title\\\":\\\"大模型技术演进与企业赋能路径\\\",\\\"describe\\\":\\\"深度解读大模型底层技术逻辑与发展图谱，剖析行业应用痛点与创新机遇，助力企业构建智能化转型战略认知\\\"}"
	newOutline, _, err = JsonStringParseCheck[OutlineInfo](outline)
	if err != nil {
		t.Error(err)
		return
	}
	t.Log(newOutline)
}
