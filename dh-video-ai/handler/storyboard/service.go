// 543331-543999为输出思考内容阶段失败，545551-545999为输出主要内容阶段失败

package storyboard

import (
	commProto2 "acg-ai-go-common/beans/proto2"
	"acg-ai-go-common/gomysql"
	"acg-ai-go-common/logger"
	"dh-video-ai/beans/enum"
	"dh-video-ai/beans/model"
	"dh-video-ai/beans/proto"
	dh_user "dh-video-ai/handler/dh-user"
	"dh-video-ai/handler/llm"
	"encoding/json"
	"fmt"
	"github.com/gin-gonic/gin"
	"golang.org/x/exp/rand"
	"gorm.io/gorm"
	"net/http"
	"strings"
	"time"
)

const (
	taskExistsErrorCode   = 332100                     // 任务重复
	thinkErrorCode        = 543331                     // 输出思考内容阶段失败错误码
	contentErrorCode      = 545551                     // 输出主要内容阶段失败错误码
	commErrorMsg          = "busy service"             // 通用服务错误信息
	commParamErrorMsg     = "param exception"          // 通用参数错误信息
	commStopMsg           = "paused"                   // 通用暂停信息
	generateOutlinePtName = "XiLing-SAAS-VideoOutline" // 生成大纲prompt模板名称
	generateScriptPtName  = "XiLing-SAAS-VideoScript"  // 生成脚本prompt模板名称
	generatePicturePtName = "XiLing-SAAS-VideoPicture" // 生成画面prompt模板名称
	itemEndFlag           = "#ItemEnd#"                // 大模型输出每组数据结束标识
	allEndFlag            = "#AllEnd#"                 // 大模型输出数据全部结束标识
	modelDeepseek_r1      = "deepseek-r1"              // 模型名称deepseek-r1
	modelDeepseek_v3      = "deepseek-v3"              // 模型名称deepseek-v3
	modelErnie_3_5_8k     = "Ernie-3.5-8k"             // 模型名称ernie-3.5-8k
	modelErnie_3_5_128k   = "Ernie-3.5-128k"           // 模型名称ernie-3.5-128k
)

const (
	updateStoryboardLockFmt = "%v:updateStoryboardLock:%v:%v" // updateStoryboardLockFmt 分镜信息修改锁名称格式，${runEnvName}:updateStoryboardLock:${draftId}:${storyboardId}
	generatePictureLockFmt  = "%v:generatePicture:%v:%v:%v"   // generatePictureLockFmt 画面生成锁名称格式，${runEnvName}:generateScript:${draftId}:${storyboardId}:${scriptPageNo}
)

func storyboardUpdate(c *gin.Context, data *proto.StoryboardSaveReq) (*model.AIVideoStoryboard, error) {
	accountId, username, err := dh_user.CtxGetAccountIdAndUsername(c)
	if err != nil {
		logger.CtxLog(c).Errorf("GetAccountIdAndUsername fail, err: %v", err)
		return nil, err
	}
	draft := &model.AIVideoDraft{}
	if err := draft.FindByDraftID(gomysql.DB, accountId, data.DraftID, model.NotDeleted); err != nil {
		logger.CtxLog(c).Errorf("FindByDraftID fail, err: %v", err)
		return nil, err
	} else if draft.ID == 0 {
		return nil, fmt.Errorf("draft not found, userId:%v, draftId: %s", accountId, data.DraftID)
	}
	storyboard := &model.AIVideoStoryboard{}
	if err := storyboard.FindByStoryboardID(gomysql.DB,
		accountId, data.DraftID, data.StoryboardId); err != nil {
		logger.CtxLog(c).Errorf("FindByStoryboardID fail, err: %v", err)
		return nil, err
	} else if storyboard.ID == 0 {
		return nil, fmt.Errorf("storyboard not found, userId:%v, draftId: %s, storyboardId: %s",
			accountId, data.DraftID, data.StoryboardId)
	}
	storyboard.Source = data.Source
	storyboard.Prompt = data.Prompt
	storyboard.PresetDuration = data.PresetDuration
	storyboard.DigitalHuman = data.DigitalHuman
	storyboard.TemplateID = data.TemplateId
	storyboard.Template = data.Template
	storyboard.VideoPurpose = data.VideoPurpose
	storyboard.SpeakerIdentity = data.SpeakerIdentity
	storyboard.Audience = data.Audience
	storyboard.OutlineContent = data.OutlineContent
	storyboard.ScriptContent = data.ScriptContent
	if data.Source == enum.VideoGenerateByInput {
		scriptContent, err := inputScript2ScriptContent(data.Prompt)
		if err != nil {
			logger.CtxLog(c).Errorf("inputScript2ScriptContent fail, err: %v", err)
			return nil, err
		}
		scriptContentBts, _ := json.Marshal(scriptContent)
		storyboard.ScriptContent = scriptContentBts
	}
	storyboard.LastUpdateBy = username
	if err := gomysql.DB.Transaction(func(tx *gorm.DB) error {
		logger.CtxLog(c).Infof("storyboard.Update ScriptContent : %s", string(storyboard.ScriptContent))
		if err = storyboard.Update(tx); err != nil {
			return fmt.Errorf("storyboard.Update fail, err: %v", err)
		}
		// 如果脚本或者画面是空的，删除已生成的画面
		if len(storyboard.ScriptContent) == 0 || len(data.PictureContent) == 0 {
			logger.CtxLog(c).Infof("script is empty, clear picture")
			return (&model.StoryboardPicture{}).DeleteByStoryboardID(tx, storyboard.StoryboardID)
		}
		return nil
	}); err != nil {
		logger.CtxLog(c).Errorf(err.Error())
	}

	return storyboard, nil
}

func storyboardCreate(c *gin.Context, data *proto.StoryboardSaveReq) (*model.AIVideoStoryboard, error) {
	accountId, username, err := dh_user.CtxGetAccountIdAndUsername(c)
	if err != nil {
		logger.CtxLog(c).Errorf("GetAccountIdAndUsername fail, err: %v", err)
		return nil, err
	}
	draft := &model.AIVideoDraft{}
	if err := draft.FindByDraftID(gomysql.DB, accountId, data.DraftID, model.NotDeleted); err != nil {
		logger.CtxLog(c).Errorf("FindByDraftID fail, err: %v", err)
		return nil, err
	} else if draft.ID == 0 {
		return nil, fmt.Errorf("draft not found, userId:%v, draftId: %s", accountId, data.DraftID)
	}
	var scriptContentBts []byte
	if data.Source == enum.VideoGenerateByInput {
		scriptContent, err := inputScript2ScriptContent(data.Prompt)
		if err != nil {
			logger.CtxLog(c).Errorf("inputScript2ScriptContent fail, err: %v", err)
			return nil, err
		}
		scriptContentBts, _ = json.Marshal(scriptContent)
	}
	storyboard := &model.AIVideoStoryboard{
		DraftID:         data.DraftID,
		Source:          data.Source,
		Prompt:          data.Prompt,
		PresetDuration:  data.PresetDuration,
		DigitalHuman:    data.DigitalHuman,
		TemplateID:      data.TemplateId,
		Template:        data.Template,
		VideoPurpose:    data.VideoPurpose,
		SpeakerIdentity: data.SpeakerIdentity,
		OutlineContent:  data.OutlineContent,
		ScriptContent:   scriptContentBts,
		Audience:        data.Audience,
		UserID:          accountId,
		LastUpdateBy:    username,
	}
	if err = storyboard.Insert(gomysql.DB); err != nil {
		logger.CtxLog(c).Errorf("Insert fail, err: %v", err)
		return nil, err
	}
	return storyboard, nil
}

// sseGenerateOutline background生成outline SSE流式下发并保存到outlineContent字段中
func sseGenerateOutline(c *gin.Context, storyboard *model.AIVideoStoryboard) {
	background := toVideoBackground(storyboard.Prompt, storyboard.SpeakerIdentity,
		storyboard.Audience, storyboard.VideoPurpose)
	logger.CtxLog(c).Infof("storyboard generateOutline to background: %v", background)
	var outlineContent []commProto2.CommDataRsp // 最终通过background生成的大纲内容
	var err error
	var itemContent string
	var needSend bool
	var sentenceId int
	var isEnd = false
	var contentNum = 0
	var errCode = thinkErrorCode // 默认输出思考内容阶段失败,输出内容阶段失败返回545551
	defer func() {
		if rec := recover(); rec != nil {
			logger.CtxLog(c).Errorf("sendSSEData panic recover: %v", rec)
			endRsp := commProto2.NewCommDataRsp(errCode, commErrorMsg, rec)
			// 判断是否为客户端断开连接导致的panic
			if strings.Contains(fmt.Sprintf("%v", rec), "broken pipe") {
				logger.CtxLog(c).Errorf("send broken pipe: %v", rec)
				endRsp.Code = 0
				endRsp.Message.Global = commStopMsg
			} else {
				outlineContent = append(outlineContent, endRsp)
			}
		}
		// 保存数据到分镜生成记录
		storyboard.OutlineContent, _ = StructToJsonBytes(outlineContent)
		// 保存生成的数据&&清空之前生成的脚本和画面
		if err = gomysql.DB.Transaction(func(tx *gorm.DB) error {
			storyboard.ScriptContent = json.RawMessage("[]")
			if err := (&model.StoryboardPicture{}).DeleteByStoryboardID(tx, storyboard.StoryboardID); err != nil {
				return fmt.Errorf("storyboardPicture.DeleteByStoryboardID fail, err: %v", err)
			}
			return storyboard.Update(tx)
		}); err != nil {
			logger.CtxLog(c).Errorf("GenerateOutline Update storyboard fail, err: %v", err)
		}
	}()
	// 调用llm生成并保存信息-----
	// 组装query参数
	llmReq := &llm.AIPromptTemplateAnswerV3Req{
		DraftID: storyboard.DraftID,
		Stream:  true,
		User:    storyboard.UserID,
		PtName:  generateOutlinePtName,
		Input: map[string]string{
			"background": background,
		},
		LLMModel:  modelDeepseek_r1,
		SessionID: storyboard.StoryboardID,
	}
	llm.CallPtChatSSE(c, llmReq,
		func(message *llm.AIAnswerResult) error {
			if isEnd {
				return nil
			}
			isEnd = message.IsEnd
			sendHeartbeat(c)
			// 存在思考内容时，需要直接下发数据
			if len(message.Think) > 0 {
				needSend = true
			} else {
				errCode = contentErrorCode
			}
			// 判断已生成一个大纲分片，那么下发数据
			curContent := message.Content
			if strings.Contains(itemContent, itemEndFlag) {
				needSend = true
				message.Content = itemContent[0:strings.Index(itemContent, itemEndFlag)]
				itemContent = itemContent[strings.Index(itemContent, itemEndFlag)+len(itemEndFlag):]
			}
			if strings.Contains(itemContent, allEndFlag) {
				needSend = true
				isEnd = true
				message.IsEnd = isEnd
				message.Content = itemContent[0:strings.Index(itemContent, allEndFlag)]
				itemContent = itemContent[strings.Index(itemContent, allEndFlag)+len(allEndFlag):]
			}
			itemContent += curContent
			if needSend {
				message.SentenceID = sentenceId
				// content有内容时打印日志，避免日志过多影响分析
				if len(message.Content) > 0 {
					// 解析判断是否是json
					newContentStr, _, err := JsonStringParseCheck[OutlineInfo](message.Content)
					if err != nil {
						logger.CtxLog(c).Errorf("GenerateOutline jsonParseCheck fail, content:%v, err:%v ",
							message.Content, err)
						outlineContent = sseSendAndAddRspByThinkError(c, outlineContent, "outline parse fail")
						needSend = false
						return err
					}
					message.Content = newContentStr
					contentNum++
					logger.CtxLog(c).Infof("GenerateOutline content message: %v", message.ToJsonString())
				}
				rsp := commProto2.NewSuccessRsp(message)
				sseSendRsp(c, rsp)
				// 保存生成内容到storyboard
				outlineContent = append(outlineContent, rsp)
				// 保存数据到分镜生成记录
				storyboard.OutlineContent, _ = StructToJsonBytes(outlineContent)
				if err = storyboard.Update(gomysql.DB); err != nil {
					logger.CtxLog(c).Errorf("GenerateOutline Update storyboard fail, err: %v", err)
				}
				needSend = false
				sentenceId++
			} else if isEnd { // 走到这里说明大模型产生的内容不规范，导致最后一包有效数据的isEnd为false 需要下发一个isEnd为true的空包
				logger.CtxLog(c).Warnf("GenerateOutline content is non-standard, itemContent: %v", itemContent)
				message.Content = ""
				message.Think = ""
				message.IsEnd = true
				message.SentenceID = sentenceId
				customEndRsp := commProto2.NewSuccessRsp(message)
				// 判断是否下发过正常内容数据，下发过说明大模型产生了有效数据，否则响应生成失败错误码
				if contentNum == 0 {
					customEndRsp = commProto2.NewCommDataRsp(errCode, "generate content is non-standard", message)
				}
				outlineContent = append(outlineContent, customEndRsp)
				sseSendRsp(c, customEndRsp)
			}
			return nil
		},
		func(err error) {
			logger.CtxLog(c).Errorf("receive storyboard GenerateOutline err: %v", err)
			// 组装响应结果
			rsp := commProto2.NewCommDataRsp(errCode, commErrorMsg, err.Error())
			sseSendRsp(c, rsp)
			logger.CtxLog(c).Infof("GenerateOutline send message: %v", rsp)
			// 保存生成内容到storyboard
			outlineContent = append(outlineContent, rsp)
			// 保存数据到分镜生成记录
			storyboard.OutlineContent, _ = StructToJsonBytes(outlineContent)
			// 实时保存数据
			logger.CtxLog(c).Infof("GenerateOutline Update storyboard: %s", string(storyboard.ScriptContent))
			if err = storyboard.Update(gomysql.DB); err != nil {
				logger.CtxLog(c).Errorf("GenerateOutline Update storyboard fail, err: %v", err)
			}
		})
}

// sseGenerateScript outline生成script SSE流式下发并保存到scriptContent字段中
func sseGenerateScript(c *gin.Context, storyboard *model.AIVideoStoryboard) {
	background := toVideoBackground(storyboard.Prompt, storyboard.SpeakerIdentity,
		storyboard.Audience, storyboard.VideoPurpose)
	var outlineContent []commProto2.CommDataRsp // 大纲原始响应数据
	var outline string                          // 组装后的大纲
	var scriptContent []commProto2.CommDataRsp  // 最终通过大纲生成的脚本内容
	var errCode = thinkErrorCode                // 默认输出思考内容阶段失败,输出内容阶段失败返回545551
	var needSend = false
	var itemContent string
	var contentNum = 0
	var sentenceId int
	var isEnd = false
	var err error
	defer func() {
		if rec := recover(); rec != nil {
			logger.CtxLog(c).Errorf("sendSSEData panic recover: %v", rec)
			endRsp := commProto2.NewCommDataRsp(errCode, commErrorMsg, rec)
			// 判断是否为客户端断开连接导致的panic
			if strings.Contains(fmt.Sprintf("%v", rec), "broken pipe") {
				logger.CtxLog(c).Errorf("send broken pipe: %v", rec)
				endRsp.Code = 0
				endRsp.Message.Global = commStopMsg
			} else {
				scriptContent = append(scriptContent, endRsp)
			}
			// 保存数据到分镜生成记录
			storyboard.ScriptContent, _ = StructToJsonBytes(scriptContent)
			logger.CtxLog(c).Infof("defer ScriptContent  message: %s", string(storyboard.ScriptContent))
			// 保存数据
			if err = storyboard.Update(gomysql.DB); err != nil {
				logger.CtxLog(c).Errorf("GenerateScript Update storyboard fail, err: %v", err)
			}
		}
	}()
	bts, _ := storyboard.OutlineContent.MarshalJSON()
	if err = json.Unmarshal(bts, &outlineContent); err != nil {
		logger.CtxLog(c).Errorf("json.Unmarshal outlineContent fail, bts:%v, err:%v ", string(bts), err)
		scriptContent = sseSendAndAddRspByThinkError(c, scriptContent, "outline unmarshal fail")
		return
	}
	if len(outlineContent) == 0 {
		logger.CtxLog(c).Errorf("outlineContent is empty, bts:%v", string(bts))
		scriptContent = sseSendAndAddRspByThinkError(c, scriptContent, "outline is empty")
		return
	}
	outline, err = toVideoOutline(outlineContent)
	if err != nil || outline == "" {
		logger.CtxLog(c).Errorf("toVideoOutline fail, outline:%v, err:%v ", outline, err)
		scriptContent = sseSendAndAddRspByThinkError(c, scriptContent, "outline is bad")
		return
	}
	logger.CtxLog(c).Infof("outline: %v", outline)
	// 调用llm生成并保存信息-----
	// 组装query参数
	llmReq := &llm.AIPromptTemplateAnswerV3Req{
		DraftID: storyboard.DraftID,
		Stream:  true,
		User:    storyboard.UserID,
		PtName:  generateScriptPtName,
		Input: map[string]string{
			"background": background,
			"outline":    outline,
			"minNum":     "2000",
			"maxNum":     "2200",
		},
		LLMModel:  modelDeepseek_v3,
		SessionID: storyboard.StoryboardID,
	}
	llm.CallPtChatSSE(c, llmReq,
		func(message *llm.AIAnswerResult) error {
			if isEnd {
				return nil
			}
			isEnd = message.IsEnd
			sendHeartbeat(c)
			// 存在思考内容时，需要直接下发数据
			if len(message.Think) > 0 {
				needSend = true
			} else {
				errCode = contentErrorCode
			}
			curContent := message.Content
			// 判断已生成一个大纲分片，那么下发数据
			if strings.Contains(itemContent, itemEndFlag) {
				needSend = true
				message.Content = itemContent[0:strings.Index(itemContent, itemEndFlag)]
				itemContent = itemContent[strings.Index(itemContent, itemEndFlag)+len(itemEndFlag):]
			}
			if strings.Contains(itemContent, allEndFlag) {
				needSend = true
				isEnd = true
				message.IsEnd = isEnd
				message.Content = itemContent[0:strings.Index(itemContent, allEndFlag)]
				itemContent = itemContent[strings.Index(itemContent, allEndFlag)+len(allEndFlag):]
			}
			itemContent += curContent
			if needSend {
				message.SentenceID = sentenceId
				// content有内容时打印日志，避免日志过多影响分析
				if len(message.Content) > 0 {
					// 解析判断是否是json
					newContentStr, _, err := JsonStringParseCheck[ScriptInfo](message.Content)
					if err != nil {
						//if err != nil || len(strings.TrimSpace(newContent.Script)) == 0 {
						errInfo := fmt.Sprintf("GenerateScript jsonParseCheck fail, content:%v, err:%v",
							message.Content, err)
						logger.CtxLog(c).Errorf(errInfo)
						scriptContent = sseSendAndAddRspByThinkError(c, scriptContent, "script parse fail")
						needSend = false
						return fmt.Errorf(errInfo)
					}
					message.Content = newContentStr
					contentNum++
					logger.CtxLog(c).Infof("GenerateScript content message: %v", message.ToJsonString())
				}
				rsp := commProto2.NewSuccessRsp(message)
				// 保存生成内容到storyboard
				scriptContent = append(scriptContent, rsp)
				// 保存数据到分镜生成记录
				storyboard.ScriptContent, _ = StructToJsonBytes(scriptContent)
				// logger.CtxLog(c).Infof("ScriptContent  message: %s", string(storyboard.ScriptContent))
				// 实时保存数据
				if err = storyboard.Update(gomysql.DB); err != nil {
					logger.CtxLog(c).Errorf("GenerateScript Update storyboard fail, err: %v", err)
				}
				sseSendRsp(c, rsp)
				needSend = false
				sentenceId++
			} else if isEnd { // 走到这里说明大模型产生的内容不规范，导致最后一包有效数据的isEnd为false 需要下发一个isEnd为true的包
				logger.CtxLog(c).Warnf("GenerateScript content is non-standard, itemContent: %v", itemContent)
				message.Content = ""
				message.Think = ""
				message.IsEnd = true
				message.SentenceID = sentenceId
				customEndRsp := commProto2.NewSuccessRsp(message)
				// 判断是否下发过正常内容数据，下发过说明大模型产生了有效数据，否则响应生成失败错误码
				if contentNum == 0 {
					customEndRsp = commProto2.NewCommDataRsp(errCode, "generate content is non-standard", message)
				}
				scriptContent = append(scriptContent, customEndRsp)
				storyboard.ScriptContent, _ = StructToJsonBytes(scriptContent)
				// 实时保存数据
				if err = storyboard.Update(gomysql.DB); err != nil {
					logger.CtxLog(c).Errorf("GenerateScript Update storyboard fail, err: %v", err)
				}
				sseSendRsp(c, customEndRsp)
			}
			return nil
		},
		func(err error) {
			logger.CtxLog(c).Errorf("receive storyboard GenerateScript err: %v", err)
			// 组装响应结果
			rsp := commProto2.NewCommDataRsp(errCode, commErrorMsg, err.Error())
			sseSendRsp(c, rsp)
			logger.CtxLog(c).Infof("GenerateScript send message: %v", rsp)
			// 保存生成内容到storyboard
			scriptContent = append(scriptContent, rsp)
			// 保存数据到分镜生成记录
			storyboard.ScriptContent, _ = StructToJsonBytes(scriptContent)
			// 实时保存数据
			logger.CtxLog(c).Infof("ScriptContent  message: %v", string(storyboard.ScriptContent))
			if err = storyboard.Update(gomysql.DB); err != nil {
				logger.CtxLog(c).Errorf("GenerateScript Update storyboard fail, err: %v", err)
			}
		})
}

// generatePicture script生成picture 非流式下发并保存到pictureContent字段中
func generatePicture(c *gin.Context, pageNo string, storyboard *model.AIVideoStoryboard, templateVariables model.AITemplateVariables) {
	var sp = &model.StoryboardPicture{
		UserID:       storyboard.UserID,
		DraftID:      storyboard.DraftID,
		StoryboardID: storyboard.StoryboardID,
		PageNo:       pageNo,
		Status:       enum.StatusSucceed,
		Reason:       "success",
	}
	var err error
	var curPictureRsp = commProto2.NewCommDataRsp(contentErrorCode, commErrorMsg, proto.GeneratePictureResult{
		PageNo: pageNo,
	})
	scriptContent, err := JSONStringToStruct[[]commProto2.CommDataRsp](string(storyboard.ScriptContent)) // 分镜的所有脚本内容
	if err != nil {
		logger.CtxLog(c).Errorf("ScriptContent JSONStringToStruct fail, err:%v ", err)
		c.JSON(http.StatusOK, commProto2.NewCommDataRsp(contentErrorCode, commErrorMsg, "script unmarshal fail"))
		return
	}
	// 取出pageNo对应的脚本
	// scriptInfo: 当前需要用来生成画面的脚本
	// scriptIndex: 当前脚本在分镜的所有脚本的索引,一个脚本对应一个画面，因此也是本次要生成画面在分镜的所有画面的索引
	scriptInfo, _, scriptIndex := findScriptByPageNo(scriptContent, pageNo)
	if scriptIndex < 0 {
		logger.CtxLog(c).Warnf("script is not found, pageNo:%v, scriptContent:%s", pageNo, string(storyboard.ScriptContent))
		c.JSON(http.StatusOK, commProto2.NewCommRsp(contentErrorCode, "script not found"))
		return
	} else if len(strings.TrimSpace(scriptInfo.Script)) == 0 {
		logger.CtxLog(c).Errorf("scriptContent is empty, script:%v", scriptInfo.Script)
		c.JSON(http.StatusOK, commProto2.NewCommDataRsp(contentErrorCode, commErrorMsg, "script is empty"))
		return
	}
	logger.CtxLog(c).Infof("scriptInfo: %v", scriptInfo)
	// 最终要将本次请求结果存储到video_storyboard_picture表中
	defer func() {
		if err != nil {
			sp.Reason = err.Error()
			sp.Status = enum.StatusFailed
		}
		sp.PictureContent, _ = StructToJsonBytes(curPictureRsp)
		if err := sp.Save(gomysql.DB); err != nil {
			logger.CtxLog(c).Errorf("SavePicture fail, err: %v", err)
		}
	}()
	digitalHuman, err := JSONStringToStruct[model.DigitalHuman](storyboard.DigitalHuman)
	if err != nil || digitalHuman.Human == nil {
		logger.CtxLog(c).Errorf("DigitalHuman JSONStringToStruct fail, digitalHuman:%v, err: %v",
			storyboard.DigitalHuman, err)
		curPictureRsp.Code = contentErrorCode
		curPictureRsp.Message.Global = "DigitalHuman parse fail"
		c.JSON(http.StatusOK, curPictureRsp)
		return
	}
	pageVariables := choiceTemplatePage(c, scriptIndex == 0, digitalHuman.Human, templateVariables)
	if pageVariables == nil {
		logger.CtxLog(c).Errorf("choiceTemplatePage fail, figureName:%v, isCover:%v, templateId:%v",
			digitalHuman.Human.Name, scriptIndex == 0, storyboard.TemplateID)
		curPictureRsp.Code = contentErrorCode
		curPictureRsp.Message.Global = "no suitable page found"
		c.JSON(http.StatusOK, curPictureRsp)
		return
	}
	// 根据所选页面信息组装prompt
	llmReq := &llm.AIPromptTemplateAnswerV3Req{
		DraftID: storyboard.DraftID,
		User:    storyboard.UserID,
		PtName:  generatePicturePtName,
		Input: map[string]string{
			"script":   scriptInfo.Script,
			"picQuery": pageVariables.Prompt,
		},
		LLMModel:  modelDeepseek_v3,
		SessionID: storyboard.StoryboardID,
	}
	sp.Params, _ = json.Marshal(llmReq)
	// 调用大模型生成内容
	llmResult, err := llm.CallPtChat[llm.AIAnswerResult](c, llmReq)
	if err != nil {
		logger.CtxLog(c).Errorf("CallPtChat fail, err: %v", err)
		c.JSON(http.StatusOK, curPictureRsp)
		return
	}
	logger.CtxLog(c).Infof("CallPtChat success, result:%v", llmResult)
	jsonContent, err := ExtractJSONFromText2Map(llmResult.Content)
	if err != nil {
		logger.CtxLog(c).Errorf("ExtractJSONFromText2Map fail, err: %v", err)
		c.JSON(http.StatusOK, curPictureRsp)
		return
	}
	curPictureRsp = commProto2.NewSuccessRsp(proto.GeneratePictureResult{
		ID:        pageVariables.ID,
		PageNo:    pageNo,
		Variables: jsonContent,
	})
	c.JSON(http.StatusOK, curPictureRsp)
}

// choiceTemplatePage 选择模板页面，封面除了第一页外都不能，第一页必需使用封面
//
//	1.解析人像信息，获取人像分辨率+宽高比+是否带背景（未扣绿为带背景，扣绿为不带背景，A2A人像默认为不带背景，选全身机位的分辨率）；
//	2.根据人像类型及模板信息中标签信息筛选出所有合适页面，从中随机选取一个页面；
//	人像带背景时，匹配圆形头像的页面；
//	人像不带背景时，1）人像分辨率==模板人像分辨率 && 人像宽高比==模板人像宽高比，随机匹配带有全身或仅头像标签的页面； 2）人像分辨率!=模板人像分辨率 || 人像宽高比!=模板人像宽高比，随机匹配圆形头像标签页面；
func choiceTemplatePage(c *gin.Context, isCover bool, figureInfo *model.FigureInfo,
	templatePageVariables model.AITemplateVariables) *model.AITemplateVariableItem {
	var hasBackground = true // 当前人像是否带背景
	var resolutionX int
	var resolutionY int
	rand.Seed(uint64(time.Now().UnixNano()))
	// 判断人像是否带背景, 未扣绿为带背景，扣绿为不带背景，A2A人像默认为不带背景，选全身机位的分辨率
	if len(figureInfo.CharacterID) != 0 ||
		(figureInfo.Effects != nil && figureInfo.Effects.ChromaKey != nil) {
		hasBackground = false
	}

	// 取出人像宽高
	if len(figureInfo.CharacterID) != 0 {
		// A2A人像宽高在faceLocation下，默认选择全身的
		bts, _ := json.Marshal(figureInfo.FaceLocation)
		a2aFigureFaceLocation, err := JSONStringToStruct[[]model.A2AFaceLocation](string(bts))
		if err != nil {
			logger.CtxLog(c).Errorf("parse A2AFaceLocation fail, err: %v", err)
			return nil
		} else {
			for _, faceLocation := range a2aFigureFaceLocation {
				if faceLocation.Name == "全身" || faceLocation.Id == "1" {
					resolutionX = faceLocation.ResolutionWidth
					resolutionY = faceLocation.ResolutionHeight
					continue
				}
			}
		}
	} else {
		// 非A2A人像
		resolutionX = figureInfo.ResolutionWidth
		resolutionY = figureInfo.ResolutionHeight
	}
	logger.CtxLog(c).Infof("id:[%v], characterId:[%v], hasBackground:%v, resolutionX:%v, resolutionY:%v",
		figureInfo.ID, figureInfo.CharacterID, hasBackground, resolutionX, resolutionY)
	var candidates model.AITemplateVariables
	for _, page := range templatePageVariables {
		logger.CtxLog(c).Infof("template page, id:%v, cover:%v, tag:%v, figureInfo:%v", page.ID, page.Cover, page.Tag, figureInfo)
		// 人像带背景时，匹配带“圆形头像”标签的模板
		if hasBackground {
			if isCover == page.Cover && Contains(page.Tag, enum.TemplateTagHead) {
				candidates = append(candidates, page)
			}
		}
		// 人像不带背景时
		if !hasBackground {
			if resolutionX == page.FigureInfo.ResolutionX &&
				resolutionY == page.FigureInfo.ResolutionY {
				// 分辨率、宽高比均匹配，匹配带有“全身”或“圆形头像”标签的页面
				if isCover == page.Cover &&
					(Contains(page.Tag, enum.TemplateTagBody) || Contains(page.Tag, enum.TemplateTagHead)) {
					candidates = append(candidates, page)
				}
			} else {
				// 分辨率或宽高比不匹配，只匹配带有“圆形头像”标签的页面
				if isCover == page.Cover && Contains(page.Tag, enum.TemplateTagHead) {
					candidates = append(candidates, page)
				}
			}
		}
	}
	if len(candidates) == 0 {
		return nil
	}
	return &candidates[rand.Intn(len(candidates))]
}
