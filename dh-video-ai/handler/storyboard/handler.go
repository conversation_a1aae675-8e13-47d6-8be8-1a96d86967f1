// 错误码543331-543999为输出思考内容阶段失败，545551-545999为输出主要内容阶段失败

package storyboard

import (
	commProto2 "acg-ai-go-common/beans/proto2"
	"acg-ai-go-common/gomysql"
	"acg-ai-go-common/goredis"
	"acg-ai-go-common/logger"
	"acg-ai-go-common/utils"
	"dh-video-ai/beans/enum"
	"dh-video-ai/beans/model"
	"dh-video-ai/beans/proto"
	rds_v9 "dh-video-ai/beans/utils/rds-v9"
	dh_user "dh-video-ai/handler/dh-user"
	video_template "dh-video-ai/handler/video-template"
	"encoding/json"
	"fmt"
	"github.com/gin-gonic/gin"
	"net/http"
	"time"
)

const (
	generateLockMaxRetry = 30              // 内容生成锁获取最大重试次数
	generateLockWaitTime = 1 * time.Second // 内容生成锁获取重试等待时间
)

// GetHistory 获取分镜生成历史记录
func GetHistory(c *gin.Context) {
	req := &proto.GetHistoryReq{}
	if err := c.BindQuery(req); err != nil {
		logger.CtxLog(c).Errorf("GetHistory bind param fail, err: %v", err)
		c.JSON(http.StatusOK, commProto2.NewCommRsp(100001, proto.CommParamErrorMsg))
		return
	}
	req.PageSize = 1 // 清除传入的pageSize，默认每次1条记录
	if req.PageNo <= 1 {
		req.PageNo = 1
	}
	accountId, _, err := dh_user.CtxGetAccountIdAndUsername(c)
	if err != nil {
		logger.CtxLog(c).Errorf("GetAccountIdAndUsername fail, err: %v", err)
		c.JSON(http.StatusOK, commProto2.NewCommRsp(530001, proto.CommErrorMsg))
		return
	}
	// 查询生成历史记录
	storyboards, err := (&model.AIVideoStoryboard{}).FindByDraftID(gomysql.DB, accountId, req.DraftID)
	if err != nil {
		logger.CtxLog(c).Errorf("FindByDraftID fail, err: %v", err)
		c.JSON(http.StatusOK, commProto2.NewCommRsp(530002, proto.CommErrorMsg))
		return
	}
	totalCount := int64(len(storyboards))
	var resultList []*model.AIVideoStoryboard
	if len(storyboards) == 0 {
		c.JSON(http.StatusOK, commProto2.NewSuccessRsp(commProto2.CommPageRsp{
			PageNo:     req.PageNo,
			PageSize:   req.PageSize,
			TotalCount: totalCount,
			List:       resultList,
		}))
		return
	}
	// 处理不同情况下的pageNo、pageSize
	if len(req.StoryboardId) > 0 {
		req.PageNo = 1
		// StoryboardId存在时要返回当前数据信息，及在这些数据中的pageNo、pageSize
		for i, storyboard := range storyboards {
			if storyboard.StoryboardID == req.StoryboardId {
				req.PageNo = int64(i + 1)
				resultList = append(resultList, storyboard)
				break
			}
		}
	} else {
		if req.PageNo > totalCount {
			// 如果页码超出总条数，响应最后一条数据及页码
			req.PageNo = totalCount
		}
		resultList = append(resultList, storyboards[req.PageNo-1])
	}
	for _, storyboard := range resultList {
		pictures, err := (&model.StoryboardPicture{}).FindByStoryboardID(gomysql.DB, storyboard.StoryboardID)
		if err != nil {
			logger.CtxLog(c).Errorf("StoryboardPicture FindByStoryboardID fail, err: %v", err)
			c.JSON(http.StatusOK, commProto2.NewCommRsp(530003, proto.CommErrorMsg))
			return
		}
		var pictureRaws []commProto2.CommDataRsp
		for _, picture := range pictures {
			pictureContent, err := JSONStringToStruct[commProto2.CommDataRsp](string(picture.PictureContent))
			if err != nil {
				logger.CtxLog(c).Errorf("JSONStringToStruct fail, err: %v", err)
				continue
			}
			if pictureContent.Code != 0 || pictureContent.Result == nil {
				pictureContent.Result = proto.GeneratePictureResult{
					PageNo: picture.PageNo,
				}
			}
			pictureRaws = append(pictureRaws, pictureContent)
		}
		storyboard.PictureContent, _ = json.Marshal(pictureRaws)
	}
	c.JSON(http.StatusOK, commProto2.NewSuccessRsp(commProto2.CommPageRsp{
		PageNo:     req.PageNo,
		PageSize:   req.PageSize,
		TotalCount: totalCount,
		List:       resultList,
	}))
}

// Save 创建或更新分镜记录
func Save(c *gin.Context) {
	req := &proto.StoryboardSaveReq{}
	if err := c.BindJSON(req); err != nil {
		logger.CtxLog(c).Errorf("storyboard Save bind param fail, err: %v", err)
		c.JSON(http.StatusOK, commProto2.NewCommRsp(100001, proto.CommParamErrorMsg))
		return
	}
	// 判断storyboardId是否存在，存在则更新，不存在则创建
	var storyboard *model.AIVideoStoryboard
	var err error
	if len(req.StoryboardId) > 0 {
		// 对当前用户分镜生成进行并发控制
		key := fmt.Sprintf(updateStoryboardLockFmt,
			utils.GetNameByRunEnv(), req.DraftID, req.StoryboardId)
		spinLock := rds_v9.NewWaitSpinLock(c, goredis.GetClientV2(), key, "",
			generateLockMaxRetry, generateLockWaitTime)
		if err := spinLock.Exec(func() {
			// 更新分镜记录
			storyboard, err = storyboardUpdate(c, req)
		}); err != nil {
			logger.CtxLog(c).Errorf("update storyboard too frequent, lockKey:%v", key)
			c.JSON(http.StatusOK, commProto2.NewCommRsp(520001,
				"requests are too frequent"))
			return
		}
	} else {
		// 创建分镜记录
		storyboard, err = storyboardCreate(c, req)
	}
	if err != nil {
		logger.CtxLog(c).Errorf("storyboardSave fail, err: %v", err)
		c.JSON(http.StatusOK, commProto2.NewCommDataRsp(530001, proto.CommErrorMsg, err.Error()))
		return
	}
	c.JSON(http.StatusOK, commProto2.NewSuccessRsp(storyboard))
}

func GenerateOutline(c *gin.Context) {
	accountId, _, _ := dh_user.CtxGetAccountIdAndUsername(c)
	req := &proto.GenerateOutlineReq{}
	if err := c.BindJSON(req); err != nil {
		logger.CtxLog(c).Errorf("storyboard GenerateOutline bind param fail, err: %v", err)
		c.JSON(http.StatusOK, commProto2.NewCommRsp(100001, proto.CommParamErrorMsg))
		return
	}
	var err error
	// 查询storyboard
	storyboard := &model.AIVideoStoryboard{}
	err = storyboard.FindByStoryboardID(gomysql.DB, accountId, req.DraftID, req.StoryboardId)
	if err != nil {
		logger.CtxLog(c).Errorf("storyboardGenerateOutline fail, err: %v", err)
		c.JSON(http.StatusOK, commProto2.NewCommRsp(540001, proto.CommErrorMsg))
		return
	} else if storyboard.ID == 0 {
		c.JSON(http.StatusOK, commProto2.NewCommRsp(100002, "storyboard is not exist"))
		return
	} else if len(storyboard.Prompt) == 0 || storyboard.Source != enum.VideoGenerateByAI {
		c.JSON(http.StatusOK, commProto2.NewCommRsp(100003, "prompt is empty or source is not ai"))
		return
	}
	// 对当前用户分镜生成进行并发控制
	key := fmt.Sprintf(updateStoryboardLockFmt,
		utils.GetNameByRunEnv(), storyboard.DraftID, storyboard.StoryboardID)
	spinLock := rds_v9.NewWaitSpinLock(c, goredis.GetClientV2(), key, "",
		generateLockMaxRetry, generateLockWaitTime)
	if err := spinLock.Exec(func() {
		sseGenerateOutline(c, storyboard)
	}); err != nil {
		logger.CtxLog(c).Errorf("generateOutline repeat, lockKey:%v", key)
		c.JSON(http.StatusOK, commProto2.NewCommRsp(taskExistsErrorCode,
			"storyboard generation in progress, outline generate failed"))
		return
	}
	return
}

func GenerateScript(c *gin.Context) {
	accountId, _, _ := dh_user.CtxGetAccountIdAndUsername(c)
	req := &proto.GenerateScriptReq{}
	if err := c.BindJSON(req); err != nil {
		logger.CtxLog(c).Errorf("storyboard GenerateScript bind param fail, err: %v", err)
		c.JSON(http.StatusOK, commProto2.NewCommRsp(100001, proto.CommParamErrorMsg))
		return
	}
	var err error
	// 查询storyboard
	storyboard := &model.AIVideoStoryboard{}
	err = storyboard.FindByStoryboardID(gomysql.DB, accountId, req.DraftID, req.StoryboardId)
	if err != nil {
		logger.CtxLog(c).Errorf("storyboardGenerateOutline fail, err: %v", err)
		c.JSON(http.StatusOK, commProto2.NewCommRsp(540001, proto.CommErrorMsg))
		return
	} else if storyboard.ID == 0 {
		c.JSON(http.StatusOK, commProto2.NewCommRsp(100002, "storyboard is not exist"))
		return
	} else if len(storyboard.Prompt) == 0 {
		c.JSON(http.StatusOK, commProto2.NewCommRsp(100003, "prompt is empty"))
		return
	}
	switch storyboard.Source {
	case enum.VideoGenerateByAI:
		logger.CtxLog(c).Infof("recive storyboardGenerateOutline, storyboardId: %v", storyboard.StoryboardID)
		// 对当前用户分镜生成进行并发控制
		key := fmt.Sprintf(updateStoryboardLockFmt,
			utils.GetNameByRunEnv(), storyboard.DraftID, storyboard.StoryboardID)
		spinLock := rds_v9.NewWaitSpinLock(c, goredis.GetClientV2(), key, "",
			generateLockMaxRetry, generateLockWaitTime)
		if err = spinLock.Exec(func() {
			sseGenerateScript(c, storyboard)
		}); err != nil {
			logger.CtxLog(c).Errorf("generateScript repeat, lockKey:%v ，error : %s", key, err.Error())
			c.JSON(http.StatusOK, commProto2.NewCommRsp(taskExistsErrorCode,
				"storyboard generation in progress, script generate failed"))
			return
		}
		return
	case enum.VideoGenerateByInput:
		// TODO 实现大模型对用户输入脚本分段得到script SSE流式下发并保存到scriptContent字段中
		if len(storyboard.Prompt) == 0 {
			c.JSON(http.StatusOK, commProto2.NewCommRsp(100004, "input script is empty"))
			return
		}
		c.JSON(http.StatusOK, commProto2.NewCommRsp(300001, "not currently supported"))
		return
	default:
		c.JSON(http.StatusOK, commProto2.NewCommRsp(100005,
			fmt.Sprintf("source [%v] is not support", storyboard.Source)))
		return
	}
}

func GeneratePicture(c *gin.Context) {
	accountId, _, _ := dh_user.CtxGetAccountIdAndUsername(c)
	req := &proto.GeneratePictureReq{}
	if err := c.BindJSON(req); err != nil {
		logger.CtxLog(c).Errorf("storyboard GeneratePicture bind param fail, err: %v", err)
		c.JSON(http.StatusOK, commProto2.NewCommRsp(100001, proto.CommParamErrorMsg))
		return
	}
	var err error
	// 查询storyboard
	storyboard := &model.AIVideoStoryboard{}
	err = storyboard.FindByStoryboardID(gomysql.DB, accountId, req.DraftID, req.StoryboardId)
	if err != nil {
		logger.CtxLog(c).Errorf("storyboardGenerateOutline fail, err: %v", err)
		c.JSON(http.StatusOK, commProto2.NewCommRsp(540001, proto.CommErrorMsg))
		return
	} else if storyboard.ID == 0 {
		c.JSON(http.StatusOK, commProto2.NewCommRsp(100002, "storyboard is not exist"))
		return
	} else if len(storyboard.Prompt) == 0 {
		c.JSON(http.StatusOK, commProto2.NewCommRsp(100003, "prompt is empty"))
		return
	}
	if len(storyboard.TemplateID) == 0 {
		c.JSON(http.StatusOK, commProto2.NewCommRsp(100004, "请选择非空白模板"))
		return
	}
	logger.CtxLog(c).Infof("storyboard scripts:%v", string(storyboard.ScriptContent))
	// 从video-home服务的/api/digitalhuman/homepage/v1/template/{templateId}接口获取模板信息
	template, err := video_template.GetTemplateDetail(c, storyboard.TemplateID)
	if err != nil {
		logger.CtxLog(c).Errorf("storyboardGenerateOutline GetTemplateDetail fail, err: %v", err)
		c.JSON(http.StatusOK, commProto2.NewCommRsp(540002, proto.CommErrorMsg))
		return
	} else if len(template.TemplateID) == 0 {
		c.JSON(http.StatusOK, commProto2.NewCommRsp(100004, "template is not exist"))
		return
	}
	templateVariables, err := JSONStringToStruct[model.AITemplateVariables](template.Variables)
	if err != nil {
		logger.CtxLog(c).Errorf("template.Variables JSONStringToStruct fail, err: %v", err)
		c.JSON(http.StatusOK, commProto2.NewCommRsp(540003, proto.CommErrorMsg))
		return
	} else if len(templateVariables) == 0 {
		logger.CtxLog(c).Errorf("template.Variables is empty, varibales: %v", template.Variables)
		c.JSON(http.StatusOK, commProto2.NewCommDataRsp(540004, proto.CommErrorMsg, "template variables is empty"))
		return
	}
	// 对当前用户分镜下画面生成进行并发控制
	pictureLockKey := fmt.Sprintf(generatePictureLockFmt,
		utils.GetNameByRunEnv(), storyboard.DraftID, storyboard.StoryboardID, req.PageNo)
	pictureLock := rds_v9.NewWaitSpinLock(c, goredis.GetClientV2(), pictureLockKey, "",
		generateLockMaxRetry, generateLockWaitTime)
	if err = pictureLock.Exec(func() {
		generatePicture(c, req.PageNo, storyboard, templateVariables)
	}); err != nil {
		logger.CtxLog(c).Errorf("generatePicture repeat, lockKey:%v", pictureLock)
		c.JSON(http.StatusOK, commProto2.NewCommRsp(taskExistsErrorCode, "picture generate repeat"))
		return
	}
}
