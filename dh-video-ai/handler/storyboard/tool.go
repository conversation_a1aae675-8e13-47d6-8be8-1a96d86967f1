package storyboard

import (
	commProto2 "acg-ai-go-common/beans/proto2"
	"bytes"
	"dh-video-ai/handler/llm"
	"encoding/json"
	"fmt"
	"github.com/gin-gonic/gin"
	"regexp"
	"strconv"
	"strings"
	"time"
)

// JsonStringParseCheck Json内容解析校验,返回解析成功后的JsonString
func JsonStringParseCheck[T any](content string) (string, T, error) {
	var result T
	// 去除首尾空格
	content = strings.TrimSpace(content)
	// 文本中提取json字符串
	jsonString, err := ExtractJSONFromText(content)
	if err != nil {
		return "", result, fmt.Errorf("jsonParseCheck ExtractJSONFromText fail, err:%v", err)
	}
	// 转换json字符串到对应的结构体，以校验json格式
	result, err = JSONStringToStruct[T](jsonString)
	if err != nil {
		return "", result, fmt.<PERSON><PERSON><PERSON>("jsonParseCheck JSONStringToStruct fail, err:%v", err)
	}
	// 结构体转回json字节
	jsonBytes, err := StructToJsonBytes(result)
	if err != nil {
		return "", result, fmt.Errorf("jsonParseCheck StructToJsonBytes fail, err:%v", err)
	}
	// 返回新的校验过的json数据
	return string(jsonBytes), result, nil
}

// jsonBytesCompress 压缩json字节数据
func jsonBytesCompress(srcBytes []byte) []byte {
	// 对 rawData 进行 JSON 压缩
	var compressed bytes.Buffer
	if err := json.Compact(&compressed, srcBytes); err != nil {
		return srcBytes
	}

	// 存储压缩后的 JSON
	return compressed.Bytes()
}

// toVideoBackground 通过提交的视频生成信息，组装提示词。
// 示例：现在需要制作一条内容讲解的口播视频，视频的主题为{{prompt}}，视频内的演讲者身份是{{SpeakerIdentity}}，在视频中他需要面向{{Audience}进行讲解，讲解的目的是{VideoPurpose}}。
func toVideoBackground(prompt, speakerIdentity, audience, videoPurpose string) string {
	// TODO 需要放入配置文件的放入配置文件
	background := fmt.Sprintf("现在需要制作一条内容讲解的口播视频，视频的主题为%v", prompt)
	if len(speakerIdentity) > 0 {
		background += fmt.Sprintf("，视频内的演讲者身份是%v", speakerIdentity)
	}
	if len(audience) > 0 {
		background += fmt.Sprintf("，在视频中他需要面向%v进行讲解", audience)
	}
	if len(videoPurpose) > 0 {
		background += fmt.Sprintf("，讲解的目的是%v", videoPurpose)
	}
	background += "。"
	return background
}

func toVideoOutline(outlineContent []commProto2.CommDataRsp) (string, error) {
	var outline string
	for _, rsp := range outlineContent {
		if rsp.Code == 0 {
			var aiAnswer *llm.AIAnswerResult
			bts, err := json.Marshal(rsp.Result)
			if err != nil {
				return "", err
			}
			if err = json.Unmarshal(bts, &aiAnswer); err != nil {
				return "", err
			}
			if len(aiAnswer.Content) > 0 {
				outline += aiAnswer.Content + "\n"
			}
		}
	}
	return outline, nil
}

// sseSendAndAddRspByThinkError 组装思考过程中失败响应结果下发到客户端并保存到responses集合中
func sseSendAndAddRspByThinkError(c *gin.Context, responses []commProto2.CommDataRsp, result interface{}) []commProto2.CommDataRsp {
	return sseSendAndAddRspByError(c, responses, thinkErrorCode, result)
}

// sseSendAndAddRspByContentError 组装输出内容过程中失败响应结果下发到客户端并保存到responses集合中
func sseSendAndAddRspByContentError(c *gin.Context, responses []commProto2.CommDataRsp, result interface{}) []commProto2.CommDataRsp {
	return sseSendAndAddRspByError(c, responses, contentErrorCode, result)
}

// sseSendAndAddRspByError 组装失败响应结果下发到客户端并保存到responses集合中
func sseSendAndAddRspByError(c *gin.Context, responses []commProto2.CommDataRsp, code int, result interface{}) []commProto2.CommDataRsp {
	return sseSendAndAddRsp(c, responses, code, commErrorMsg, result)
}

// sseSendAndAddRsp 组装响应结果下发到客户端并保存到responses集合中
func sseSendAndAddRsp(c *gin.Context, responses []commProto2.CommDataRsp, code int, message string, result interface{}) []commProto2.CommDataRsp {
	rsp := commProto2.NewCommDataRsp(code, message, result)
	sseSendRsp(c, rsp)
	return addRspToResponses(responses, rsp)
}

// sseSendRsp 组装响应结果下发到客户端
func sseSendRsp(c *gin.Context, rsp commProto2.CommDataRsp) {
	c.SSEvent("", rsp)
	c.Writer.Flush()
}

func sendHeartbeat(c *gin.Context) {
	c.SSEvent("heartbeat", "")
	c.Writer.Flush()
}

// addRspToResponses 组装响应结果保存到responses集合中
func addRspToResponses(responses []commProto2.CommDataRsp, rsp commProto2.CommDataRsp) []commProto2.CommDataRsp {
	return append(responses, rsp)
}

func inputScript2ScriptContent(scriptPrompt string) ([]commProto2.CommDataRsp, error) {
	var scripts []string
	if err := json.Unmarshal([]byte(scriptPrompt), &scripts); err != nil {
		return nil, err
	}
	var scriptContent []commProto2.CommDataRsp
	for i, script := range scripts {
		pageNo := i + 1
		scriptItem := ScriptInfo{
			PageNo: fmt.Sprintf("%v", pageNo),
			Script: script,
		}
		result := llm.AIAnswerResult{
			QueryID:    "UserInputScript",
			Content:    scriptItem.ToJsonString(),
			IsEnd:      pageNo == len(scripts),
			SentenceID: i,
			Timestamp:  time.Now().UnixMilli(),
		}
		scriptContent = append(scriptContent, commProto2.NewSuccessRsp(result))
	}
	return scriptContent, nil
}

func StructToJsonBytes(src interface{}) ([]byte, error) {
	var err error
	bts, err := json.Marshal(src)
	if err != nil {
		return nil, err
	}
	return jsonBytesCompress(bts), err
}

// JSONStringToStruct 泛型 JSON 解析函数（支持带转义字符的 JSON）
func JSONStringToStruct[T any](jsonStr string) (T, error) {
	var result T
	// 先去掉转义字符
	if unescaped, err := strconv.Unquote(fmt.Sprintf("\"%s\"", jsonStr)); err == nil {
		jsonStr = unescaped
	}

	// 解析最终 JSON
	err := json.Unmarshal([]byte(jsonStr), &result)
	return result, err
}

// InterfaceToStruct 将 `interface{}` 转换为 `struct`
func InterfaceToStruct[T any](data interface{}) (T, error) {
	var result T

	// 先把 interface{} 转成 JSON
	bts, err := json.Marshal(data)
	if err != nil {
		return result, err
	}

	// 解析 JSON 到目标结构体
	err = json.Unmarshal(bts, &result)
	return result, err
}

func Contains(slice []string, item string) bool {
	for _, v := range slice {
		if v == item {
			return true
		}
	}
	return false
}

func ExtractJSONFromText2Map(text string) (map[string]interface{}, error) {
	// 正则匹配 JSON 格式
	re := regexp.MustCompile(`\{[^{}]*}`)
	match := re.FindString(text)
	if match == "" {
		return nil, fmt.Errorf("未找到 JSON")
	}

	// 解析 JSON
	var result map[string]interface{}
	err := json.Unmarshal([]byte(match), &result)
	if err != nil {
		return nil, fmt.Errorf("JSON 解析失败: %v", err)
	}

	return result, nil
}

func ExtractJSONFromText(text string) (string, error) {
	// 正则匹配 JSON 格式
	re := regexp.MustCompile(`\{[^{}]*}`)
	match := re.FindString(text)
	if match == "" {
		return "", fmt.Errorf("未找到 JSON")
	}

	return match, nil
}
