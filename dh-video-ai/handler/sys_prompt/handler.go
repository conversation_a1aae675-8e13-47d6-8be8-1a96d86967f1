package sys_prompt

import (
	commProto "acg-ai-go-common/beans/proto"
	commProto2 "acg-ai-go-common/beans/proto2"
	"acg-ai-go-common/logger"
	"dh-video-ai/beans/model"
	"dh-video-ai/beans/proto"
	"github.com/gin-gonic/gin"
	"net/http"
)

type SysRomptQuery struct {
	PageNo    int64  `json:"pageNo" form:"pageNo"`     // 页码
	PageSize  int64  `json:"pageSize" form:"pageSize"` // 每页大小
	Type      string `json:"type" form:"type"`
	Title     string `json:"title" form:"title"`
	Prompt    string `json:"prompt" form:"prompt"`
	CreatedAt string `json:"createdAt" form:"createdAt"`
}

type SysRomptInsert struct {
	Type    string `json:"type" form:"type" binding:"required"`
	Title   string `json:"title" form:"title" binding:"required"`
	Prompt  string `json:"prompt" form:"prompt" binding:"required"`
	Sort    int64  `json:"sort" form:"sort" binding:"required"`
	Creator string `json:"creator" form:"creator"`
}

type SysRomptUpdate struct {
	Id       int64  `json:"id" form:"id" binding:"required"`
	Title    string `json:"title" form:"title" binding:"required"`
	Prompt   string `json:"prompt" form:"prompt" binding:"required"`
	Operator string `json:"operator" form:"operator" binding:"required"`
}
type SysRomptDel struct {
	Id       int64  `form:"id" binding:"required"`
	Operator string `form:"operator" binding:"required"`
}

func GetPage(c *gin.Context) {
	req := &proto.SystemPromptGetPageReq{}
	if err := c.ShouldBindQuery(req); err != nil {
		logger.CtxLog(c).Errorf("SystemPromptGetPage bind param fail, err: [%v]", err)
		c.JSON(http.StatusOK, commProto2.NewCommRsp(100001, proto.CommParamErrorMsg))
		return
	}
	logger.CtxLog(c).Info("SystemPromptGetPage bind param success")
	pt := &model.SysPrompt{}
	count, pts, err := pt.Search("video-ai", "", req.PageNo, req.PageSize)
	if err != nil {
		logger.CtxLog(c).Errorf("SystemPromptGetPage Search fail, err: [%v]", err)
		c.JSON(http.StatusOK, commProto2.NewCommRsp(530001, proto.CommErrorMsg))
		return
	} else if len(pts) == 0 {
		c.JSON(http.StatusOK, commProto2.NewSuccessRsp(commProto2.CommPageRsp{
			PageNo:     req.PageNo,
			PageSize:   req.PageSize,
			TotalCount: count,
			List:       []*model.SysPromptDTO{},
		}))
		return
	}
	dto := make([]*model.SysPromptDTO, 0, len(pts))
	for _, sysPrompt := range pts {
		dto = append(dto, sysPrompt.ToMultiLanguageDTO(c))
	}
	c.JSON(http.StatusOK, commProto2.NewSuccessRsp(commProto2.CommPageRsp{
		PageNo:     req.PageNo,
		PageSize:   req.PageSize,
		TotalCount: count,
		List:       dto,
	}))
}

func Search(c *gin.Context) {
	req := &SysRomptQuery{}
	if err := c.ShouldBindJSON(req); err != nil {
		logger.CtxLog(c).Errorf("SystemPromptSearch bind param fail, err: [%v]", err)
		c.JSON(http.StatusOK, commProto.NewCommRsp(100001, proto.CommParamErrorMsg))
		return
	}
	sysPrompt := &model.SysPrompt{
		Type:   req.Type,
		Title:  req.Title,
		Prompt: req.Prompt,
	}
	count, pts, err := sysPrompt.SearchList(req.PageNo, req.PageSize, req.CreatedAt)
	if err != nil {
		logger.CtxLog(c).Errorf("SystemPromptSearch sysPrompt.SearchList fail, err: [%v]", err)
		c.JSON(http.StatusOK, commProto.NewCommRsp(530001, proto.CommErrorMsg))
		return
	} else if len(pts) == 0 {
		c.JSON(http.StatusOK, commProto.NewSuccessRsp(commProto.CommPageRsp{
			Count: count,
			List:  []*model.SysPromptDTO{},
		}))
		return
	}
	dto := make([]*model.SysPromptDTO, 0, len(pts))
	for _, sysPrompt := range pts {
		dto = append(dto, sysPrompt.ToDTO())
	}
	c.JSON(http.StatusOK, commProto.NewSuccessRsp(commProto.CommPageRsp{
		Count: count,
		List:  dto,
	}))
}

func Create(c *gin.Context) {
	req := &SysRomptInsert{}
	if err := c.ShouldBindJSON(req); err != nil {
		logger.CtxLog(c).Errorf("SystemPromptCreate bind param fail, err: [%v]", err)
		c.JSON(http.StatusOK, commProto.NewCommRsp(100001, proto.CommParamErrorMsg))
		return
	}
	if len(req.Type) == 0 || len(req.Title) == 0 || len(req.Prompt) == 0 || req.Sort <= 0 {
		logger.CtxLog(c).Errorf("SystemPromptCreate check param fail")
		c.JSON(http.StatusOK, commProto.NewCommRsp(100002, "参数错误"))
		return
	}
	sysPrompt := &model.SysPrompt{
		Type:    req.Type,
		Title:   req.Title,
		Prompt:  req.Prompt,
		Sort:    req.Sort,
		Creator: req.Creator,
	}
	err := sysPrompt.Insert()
	if err != nil {
		logger.CtxLog(c).Errorf("SystemPromptCreate sysPrompt.Insert fail, err: [%v]", err)
		c.JSON(http.StatusOK, commProto.NewCommRsp(530001, err.Error()))
		return
	}
	c.JSON(http.StatusOK, commProto.NewSuccessRsp(nil))
}

func Update(c *gin.Context) {
	req := &SysRomptUpdate{}
	if err := c.ShouldBindJSON(req); err != nil {
		logger.CtxLog(c).Errorf("SystemPromptUpdate bind param fail, err: [%v]", err)
		c.JSON(http.StatusOK, commProto.NewCommRsp(100001, "参数解析异常"))
		return
	}
	sysPrompt := &model.SysPrompt{
		ID:       uint64(req.Id),
		Title:    req.Title,
		Prompt:   req.Prompt,
		Operator: req.Operator,
	}
	err := sysPrompt.Update()
	if err != nil {
		logger.CtxLog(c).Errorf("SystemPromptUpdate sysPrompt.Update fail, err: [%v]", err)
		c.JSON(http.StatusOK, commProto.NewCommRsp(530001, err.Error()))
		return
	}
	c.JSON(http.StatusOK, commProto.NewSuccessRsp(nil))
}

func Delete(c *gin.Context) {
	req := &SysRomptDel{}
	if err := c.ShouldBindQuery(req); err != nil {
		logger.CtxLog(c).Errorf("SystemPromptDelete bind param fail, err: [%v]", err)
		c.JSON(http.StatusOK, commProto.NewCommRsp(100001, "参数解析异常"))
		return
	}
	sysPrompt := &model.SysPrompt{
		ID:       uint64(req.Id),
		Operator: req.Operator,
	}
	err := sysPrompt.UpdateByOperator()
	if err != nil {
		logger.CtxLog(c).Errorf("SystemPromptDelete sysPrompt.UpdateByOperator fail, err: [%v]", err)
		c.JSON(http.StatusOK, commProto.NewCommRsp(530001, err.Error()))
		return
	}
	err = sysPrompt.DeleteByID(uint64(req.Id))
	if err != nil {
		logger.CtxLog(c).Errorf("SystemPromptDelete sysPrompt.DeleteByID fail, err: [%v]", err)
		c.JSON(http.StatusOK, commProto.NewCommRsp(530002, err.Error()))
		return
	}
	c.JSON(http.StatusOK, commProto.NewSuccessRsp(nil))
}
