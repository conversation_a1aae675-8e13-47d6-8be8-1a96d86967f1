package llm

import (
	"acg-ai-go-common/logger"
	"bufio"
	"dh-video-ai/beans/utils/ginctx"
	"fmt"
	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"io"
	"io/ioutil"
	"net/http"
	"strings"
	"time"
)

// SSEClient represents a simple SSE client
type SSEClient struct {
	URL        string
	Timeout    time.Duration
	Method     string
	Headers    map[string]string
	Body       io.Reader
	RetryWait  time.Duration // Time to wait before retrying
	MaxRetries int           // Maximum retry attempts
	RequestId  string
}

// Listen starts listening to the SSE stream with retry mechanism
func (c *SSEClient) Listen(ctx *gin.Context, onMessage func(message string) error,
	onError func(err error)) {
	var err error
	var req *http.Request
	var resp *http.Response
	defer func() {
		if resp != nil {
			_ = resp.Body.Close()
		}
	}()
	if c.RequestId == "" {
		c.RequestId = uuid.NewString()
	}
	if ctx == nil || ctx.Request == nil {
		requestId := uuid.NewString()
		ctx = ginctx.Init(requestId, "trace-"+requestId)
	}
	var attempt int
	for {
		if attempt >= c.MaxRetries {
			onError(err)
			return
		}

		req, err = http.NewRequestWithContext(ctx, c.Method, c.URL, c.Body)
		if err != nil {
			// onError(commProto2.NewCommDataRsp(543331, "listener: ", nil))
			logger.CtxLog(ctx).Errorf("listener: create http request fail, err:%v", err)
			attempt++
			time.Sleep(c.RetryWait)
			continue
		}
		for key, value := range c.Headers {
			req.Header.Set(key, value)
		}
		req.Header.Set("Accept", "text/event-stream")

		client := &http.Client{Timeout: c.Timeout}
		resp, err = client.Do(req)
		if err != nil {
			logger.CtxLog(ctx).Errorf("listener: do http request fail, err:%v", err)
			attempt++
			time.Sleep(c.RetryWait)
			continue
		}

		if resp.StatusCode != http.StatusOK {
			logger.CtxLog(ctx).Errorf("listener: status:%v, unexpected response:%v", resp.StatusCode, resp)
			attempt++
			time.Sleep(c.RetryWait)
			continue
		}

		// 这里预期是非http sse流式返回，因此返回非sse也视为错误
		contentType := resp.Header.Get("Content-Type")
		if strings.Contains(contentType, "application/json") || contentType != "text/event-stream" {
			bytes, _ := ioutil.ReadAll(resp.Body)
			err = fmt.Errorf(string(bytes))
			onError(err)
			return
		}

		scanner := bufio.NewScanner(resp.Body)
		for scanner.Scan() {
			select {
			case <-ctx.Done():
				logger.CtxLog(ctx).Errorf("listener: close http request fail, ctx:%v", ctx)
				onError(fmt.Errorf("connection closed: %v", ctx.Err()))
				return
			default:
				line := scanner.Text()
				if strings.HasPrefix(line, "data:") {
					if err = onMessage(strings.TrimPrefix(line, "data:")); err != nil {
						logger.CtxLog(ctx).Errorf("listener: onMessage fail, err:%v", err)
						return
					}
				}
			}
		}

		if err = scanner.Err(); err != nil {
			logger.CtxLog(ctx).Errorf("listener: scan sse message fail, err:%v", err)
			attempt++
			time.Sleep(c.RetryWait)
			continue
		}
		return
	}
}
