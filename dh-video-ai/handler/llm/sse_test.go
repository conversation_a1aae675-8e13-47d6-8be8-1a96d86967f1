package llm

import (
	"acg-ai-go-common/logger"
	"bytes"
	"dh-video-ai/beans/utils/ginctx"
	"encoding/json"
	"fmt"
	"github.com/google/uuid"
	"net/http"
	"strings"
	"testing"
	"time"
)

func TestSSE(t *testing.T) {
	ctx := ginctx.Init(uuid.NewString(), uuid.NewString())

	// 创建请求参数
	bodyParam := []byte(
		`{
			"ptName": "XiLing-SAAS-VideoOutline",
			"input": {
				"query": "生成一个大模型发展趋势科普性文章，主要向企业内部员工科普技术发展现状和趋势，帮助员工与时俱进"
			},
			"stream": true,
			"llmModel": "deepseek-r1"
		}`)
	// 创建 SSEClient 实例
	client := SSEClient{
		URL:        "http://localhost:8114/api/digitalhuman/ecloud/v1/llm/chat/ernie/pt",
		Method:     http.MethodPost,
		Headers:    map[string]string{},
		Body:       bytes.NewReader(bodyParam),
		Timeout:    10 * time.Minute,
		RetryWait:  5 * time.Second,
		MaxRetries: 3,
	}
	var think string
	var content string
	var thinkEndFlag = "</think>"
	var isThinkEnd = false
	client.Listen(ctx, func(message string) error {
		var data *AIAnswerResult
		if err := json.Unmarshal([]byte(message), &data); err != nil {
			logger.CtxLog(ctx).Errorf("json.Unmarshal fail, message:%v, err:%v ", message, err)
			return err
		}
		if isThinkEnd {
			content += data.Content
		}
		if !isThinkEnd {
			think += data.Content
			thinkEndIndex := strings.Index(think, thinkEndFlag)
			if thinkEndIndex != -1 {
				content = think[thinkEndIndex+len(thinkEndFlag):]
				think = think[0 : thinkEndIndex+len(thinkEndFlag)]
				isThinkEnd = true
			}
		}
		fmt.Println("-------华丽的分割线-------")
		fmt.Printf("think: %v\n content: %v\n", think, content)
		return nil
	}, func(err error) {
		fmt.Printf("err: %v\n", err)
	})
}
