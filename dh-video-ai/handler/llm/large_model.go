package llm

import (
	commProto2 "acg-ai-go-common/beans/proto2"
	"acg-ai-go-common/logger"
	"bytes"
	"dh-video-ai/beans/utils/ginctx"
	"dh-video-ai/beans/utils/httputil"
	config "dh-video-ai/conf"
	"encoding/json"
	"fmt"
	"github.com/gin-gonic/gin"
	"net/http"
	"time"
)

const (
	PromptTemplateChatV3URL = "/api/digitalhuman/ecloud/v3/llm/chat/ernie/pt"
	PromptTemplateChatV1URL = "/api/digitalhuman/ecloud/v1/llm/chat/ernie/pt"
)

type AIAnswerResult struct {
	QueryID    string `json:"queryId"`
	User       string `json:"user"`
	Content    string `json:"content"`
	Think      string `json:"think"`
	SessionID  string `json:"sessionId"`
	IsRisk     bool   `json:"isRisk,omitempty"`
	IsEnd      bool   `json:"isEnd"`
	SentenceID int    `json:"sentenceId"`
	Timestamp  int64  `json:"timestamp,omitempty"`
}

func (p *AIAnswerResult) ToJsonString() string {
	marshal, _ := json.Marshal(p)
	return string(marshal)
}

type AIPromptTemplateAnswerV3Req struct {
	DraftID   string            `json:"draftId"`
	Stream    bool              `json:"stream"`
	User      string            `json:"user"`
	PtName    string            `json:"ptName" binding:"required"`
	Input     map[string]string `json:"input" binding:"required"`
	LLMModel  string            `json:"llmModel"`
	SessionID string            `json:"sessionId"`
}

// CallPtChatSSE 流式请求ecloud-large-model服务的prompt模板对话
func CallPtChatSSE(c *gin.Context, bodyParams *AIPromptTemplateAnswerV3Req,
	onMessage func(message *AIAnswerResult) error, onError func(err error)) {
	bodyParams.Stream = true
	reqURL := config.LocalConfig.LargeModelSetting.BaseUrl + PromptTemplateChatV3URL
	bodyBytes, _ := json.Marshal(bodyParams)
	body := bytes.NewBuffer(bodyBytes)
	client := SSEClient{
		URL:        reqURL,
		Method:     http.MethodPost,
		Headers:    ginctx.GetSaasTokenHeader(c),
		Body:       body,
		Timeout:    10 * time.Minute,
		RetryWait:  5 * time.Second,
		MaxRetries: 3,
	}
	// 监听流式返回结果
	client.Listen(c, func(message string) error {
		var data *AIAnswerResult
		if err := json.Unmarshal([]byte(message), &data); err != nil {
			logger.CtxLog(c).Errorf("json.Unmarshal fail, message:%v, err:%v ", message, err)
			return err
		}
		return onMessage(data)
	}, onError)
}

// CallPtChat 非流式请求ecloud-large-model服务的prompt模板对话
func CallPtChat[T any](c *gin.Context, bodyParams *AIPromptTemplateAnswerV3Req) (*T, error) {
	bodyParams.Stream = false
	reqURL := config.LocalConfig.LargeModelSetting.BaseUrl + PromptTemplateChatV3URL
	client := httputil.NewRetryHTTPClient(c, 3, 1*time.Second, 120*time.Second)
	bodyBytes, _ := json.Marshal(bodyParams)
	reqHeader := ginctx.GetSaasTokenHeader(c)
	response, err := client.Do(http.MethodPost, reqURL, reqHeader, bodyBytes)
	if err != nil {
		return nil, err
	}
	var rsp *commProto2.CommDataRsp
	if err = json.Unmarshal(response, &rsp); err != nil {
		return nil, err
	}
	if !rsp.Success {
		return nil, fmt.Errorf("code:%v, msg:%v", rsp.Code, rsp.Message.Global)
	}
	resultBts, err := json.Marshal(rsp.Result)
	if err != nil {
		return nil, err
	}
	var result T
	if err = json.Unmarshal(resultBts, &result); err != nil {
		return nil, err
	}
	return &result, err
}
