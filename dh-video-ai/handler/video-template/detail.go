package video_template

import (
	commProto2 "acg-ai-go-common/beans/proto2"
	"acg-ai-go-common/logger"
	"acg-ai-go-common/utils/httputil"
	"dh-video-ai/beans/model"
	"dh-video-ai/beans/utils/ginctx"
	config "dh-video-ai/conf"
	"fmt"
	"github.com/gin-gonic/gin"
)

const (
	TemplateDetail = "/api/digitalhuman/homepage/v1/template/%v" // 视频模板详情API 使用时注意拼接上路径参数templateId, 示例/api/digitalhuman/homepage/v1/template/t-123456
)

type TemplateDetailRsp struct {
	commProto2.CommRsp
	Result *model.VideoTemplateVo `json:"result"`
}

func GetTemplateDetail(c *gin.Context, templateId string) (*model.VideoTemplateVo, error) {
	header := ginctx.GetSaasTokenHeader(c)
	url := config.LocalConfig.VideoHomeSetting.BaseUrl + fmt.Sprintf(TemplateDetail, templateId)
	var rsp *TemplateDetailRsp
	if err := httputil.GetV2(url, header, &rsp); err != nil {
		logger.CtxLog(c).Errorf("GetTemplateDetail fail, reqHeader:%v, err:%v", header, err)
		return nil, err
	} else if rsp.Code != 0 {
		errMsg := fmt.Sprintf("code:%v, msg:%v", rsp.Code, rsp.Message)
		logger.CtxLog(c).Errorf("GetTemplateDetail fail, reqHeader:%v, errMsg:%v", header, errMsg)
		return nil, fmt.Errorf(errMsg)
	}
	return rsp.Result, nil
}
