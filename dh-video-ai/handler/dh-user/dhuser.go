package dh_user

import (
	saasProto "acg-ai-go-common/beans/proto2"
	"acg-ai-go-common/global"
	"acg-ai-go-common/logger"
	"acg-ai-go-common/utils/httputil"
	"dh-video-ai/beans/utils/ginctx"
	config "dh-video-ai/conf"
	"errors"
	"github.com/gin-gonic/gin"
	"net/http"
)

const (
	DhUserInfoPath           = "/api/internal/workflow/v2/user/auth/info"              // 获取用户信息
	DhUserQuotaFreeze        = "/api/internal/workflow/v1/account/quota/freezeQuota"   // 冻结
	DhUserQuotaUnFreeze      = "/api/internal/workflow/v1/account/quota/unFreezeQuota" // 解冻
	DhUserQuotaConfirmedCost = "/api/internal/workflow/v1/account/quota/confirmedCost" // 确认消耗
)

func DhUserCheck(c *gin.Context) {
	qaMockAccount := c.GetHeader("X-XL-QA-MOCK")
	if len(qaMockAccount) > 0 {
		mockDhUserCheckQaTest(c, qaMockAccount)
		logger.CtxLog(c).Infof("DhUserCheck mockDhUserCheckQaTest, qaMockAccount:%v", qaMockAccount)
		return
	}
	if global.DevEnv == global.ServerSetting.RunEnv {
		mockDhUserCheckWs11Test(c)
		return
	}
	var rsp *DhUserInfoRsp
	var err error
	if rsp, err = GetDhUserInfo(c); err != nil {
		logger.CtxLog(c).Errorf("DhUserCheck GetDhUserInfo fail, err:%v", err)
		c.AbortWithStatusJSON(http.StatusOK, saasProto.NewCommRsp(590001, "服务内部异常"))
		return
	} else if rsp == nil || !rsp.Success {
		logger.CtxLog(c).Errorf("DhUserCheck GetDhUserInfo fail, rsp:%v", rsp)
		c.AbortWithStatusJSON(http.StatusOK, rsp)
		return
	}
	logger.CtxLog(c).Infof("DhUserCheck GetDhUserInfo success, info:%v", rsp.Result)
	c.Set("AccountId", rsp.Result.AccountId)
	c.Set("UserId", rsp.Result.Uid)
	c.Set("Username", rsp.Result.Username)
	c.Set("Userinfo", rsp.Result)
	c.Next()
}

func CtxGetAccountIdAndUsername(c *gin.Context) (accountId string, username string, err error) {
	accountIdInterface, ok := c.Get("AccountId")
	if !ok {
		return "", "", errors.New("未获取到用户AccountID")
	}
	usernameInterface, ok := c.Get("Username")
	if !ok {
		return "", "", errors.New("未获取到用户Username")
	}
	return accountIdInterface.(string), usernameInterface.(string), nil
}

func CtxGetUserInfo(c *gin.Context) (*DhUserInfo, error) {
	info, ok := c.Get("Userinfo")
	if !ok {
		return nil, errors.New("未获取到用户信息")
	}
	return info.(*DhUserInfo), nil
}

func mockDhUserCheckWs11Test(c *gin.Context) {
	rsp := &DhUserInfoRsp{
		CommRsp: CommRsp{},
		Result: &DhUserInfo{
			Username:  "ws11TestUserName",
			AccountId: "681f7d66-fad0-42b4-93b5-5f071ae0b283",
			Uid:       "ws11TestUid",
			WxOpenID:  "ws11TestWxOpenID",
			WxUnionID: "ws11TestWxUnionID",
		},
	}
	c.Set("AccountId", rsp.Result.AccountId)
	c.Set("UserId", rsp.Result.Uid)
	c.Set("Username", rsp.Result.Username)
	c.Set("Userinfo", rsp.Result)
	c.Next()
}

func mockDhUserCheckQaTest(c *gin.Context, accountId string) {
	rsp := &DhUserInfoRsp{
		CommRsp: CommRsp{},
		Result: &DhUserInfo{
			Username:  "QATest",
			AccountId: accountId,
			Uid:       "QATestUid",
			WxOpenID:  "QATestWxOpenID",
			WxUnionID: "QATestWxUnionID",
		},
	}
	c.Set("AccountId", rsp.Result.AccountId)
	c.Set("UserId", rsp.Result.Uid)
	c.Set("Username", rsp.Result.Username)
	c.Set("Userinfo", rsp.Result)
	c.Next()
}

func GetDhUserInfo(c *gin.Context) (*DhUserInfoRsp, error) {
	header := ginctx.GetSaasTokenHeader(c)
	url := config.LocalConfig.DhUserSetting.BaseUrl + DhUserInfoPath
	var rsp *DhUserInfoRsp
	if err := httputil.GetV2(url, header, &rsp); err != nil {
		logger.CtxLog(c).Errorf("GetDhUserInfo fail, reqHeader:%v, err:%v", header, err)
		return nil, err
	}
	return rsp, nil
}

// QuotaFreeze 配额冻结
func QuotaFreeze(req *QuotaFreezeReq) (*QuotaFreezeRsp, error) {
	url := config.LocalConfig.DhUserSetting.BaseUrl + DhUserQuotaFreeze
	var rsp *QuotaFreezeRsp
	err := httputil.PostJson(url, req, &rsp)
	return rsp, err
}

// QuotaUnFreeze 配额解冻
func QuotaUnFreeze(req *QuotaUnFreezeReq) (*QuotaUnFreezeRsp, error) {
	url := config.LocalConfig.DhUserSetting.BaseUrl + DhUserQuotaUnFreeze
	var rsp *QuotaUnFreezeRsp
	err := httputil.PostJson(url, req, &rsp)
	return rsp, err
}

// QuotaConfirmedCost 配额确认扣除
func QuotaConfirmedCost(req *QuotaConfirmedCostReq) (*QuotaConfirmedCostRsp, error) {
	url := config.LocalConfig.DhUserSetting.BaseUrl + DhUserQuotaConfirmedCost
	var rsp *QuotaConfirmedCostRsp
	err := httputil.PostJson(url, req, &rsp)
	return rsp, err
}
