package draft

import (
	commProto2 "acg-ai-go-common/beans/proto2"
	"acg-ai-go-common/gomysql"
	"acg-ai-go-common/logger"
	"dh-video-ai/beans/model"
	"dh-video-ai/beans/proto"
	"github.com/gin-gonic/gin"
	"net/http"
)

func Get(c *gin.Context) {
	req := &proto.DraftGetReq{}
	if err := c.ShouldBindUri(req); err != nil {
		logger.CtxLog(c).<PERSON><PERSON><PERSON>("DraftGet bind param fail, err: %v", err)
		c.<PERSON><PERSON><PERSON>(http.StatusOK, commProto2.NewCommRsp(100001, "参数错误"))
		return
	}
	draft, err := draftGetByDraftID(c, req.DraftID)
	if err != nil {
		logger.CtxLog(c).Errorf("DraftGet draftGetByDraftID fail, err: %v", err)
		c.<PERSON>(http.StatusOK, commProto2.NewCommRsp(530001, "服务繁忙"))
		return
	} else if draft == nil || draft.ID == 0 {
		logger.CtxLog(c).Warnf("DraftGet draft not exist")
		c.JSON(http.StatusOK, commProto2.NewCommRsp(100002, "数据不存在"))
		return
	}
	c.JSON(http.StatusOK, commProto2.NewSuccessRsp(draft))
}

func Create(c *gin.Context) {
	req := &proto.DraftCreateReq{}
	if err := c.ShouldBindJSON(req); err != nil {
		logger.CtxLog(c).Errorf("DraftCreate bind param fail, err: %v", err)
		c.JSON(http.StatusOK, commProto2.NewCommRsp(100001, "参数错误"))
		return
	}
	draft, err := draftInit(c, req.Name)
	if err != nil {
		logger.CtxLog(c).Errorf("DraftCreate init fail, err: %v", err)
		c.JSON(http.StatusOK, commProto2.NewCommRsp(530001, "服务繁忙"))
		return
	}
	c.JSON(http.StatusOK, commProto2.NewSuccessRsp(draft))
}

func Update(c *gin.Context) {
	req := &proto.DraftUpdateReq{}
	if err := c.ShouldBind(req); err != nil {
		logger.CtxLog(c).Errorf("DraftUpdate bind param fail, err: %v", err)
		c.JSON(http.StatusOK, commProto2.NewCommRsp(100001, "参数错误"))
		return
	}
	draft, err := draftUpdateByDraftID(c, req)
	if err != nil {
		logger.CtxLog(c).Errorf("DraftUpdate draftUpdate fail, err: %v", err)
	} else if draft == nil || draft.ID == 0 {
		logger.CtxLog(c).Warnf("DraftUpdate draft not exist")
		c.JSON(http.StatusOK, commProto2.NewCommRsp(100002, "数据不存在"))
		return
	}
	c.JSON(http.StatusOK, commProto2.NewSuccessRsp(draft))
}

func Delete(c *gin.Context) {
	req := &proto.DraftDeleteReq{}
	if err := c.ShouldBindUri(req); err != nil {
		logger.CtxLog(c).Errorf("DraftDelete bind param fail, err: %v", err)
		c.JSON(http.StatusOK, commProto2.NewCommRsp(100001, "参数错误"))
		return
	}
	if err := draftDeleteByDraftID(c, req.DraftID); err != nil {
		logger.CtxLog(c).Errorf("DraftDelete DeleteByDraftID fail, err: %v", err)
		c.JSON(http.StatusOK, commProto2.NewCommRsp(530001, "服务繁忙"))
		return
	}
	c.JSON(http.StatusOK, commProto2.NewSuccessRsp(nil))
}

func Copy(c *gin.Context) {
	req := &proto.DraftCopyReq{}
	if err := c.ShouldBindJSON(req); err != nil {
		logger.CtxLog(c).Errorf("DraftCopy bind param fail, err: %v", err)
		c.JSON(http.StatusOK, commProto2.NewCommRsp(100001, "参数错误"))
		return
	}
	srcDraft := &model.AIVideoDraft{}
	if err := srcDraft.FindByDraftID(gomysql.DB, req.UserID, req.DraftID, model.NotDeleted); err != nil {
		logger.CtxLog(c).Errorf("draftCopy FindByDraftID fail, err:%v", err)
		c.JSON(http.StatusOK, commProto2.NewCommRsp(530001, "服务繁忙"))
		return
	} else if srcDraft.ID == 0 {
		logger.CtxLog(c).Warnf("draftCopy FindByDraftID not found, draftId:%v", req.DraftID)
		c.JSON(http.StatusOK, commProto2.NewCommRsp(100002, "draft not exist"))
		return
	}
	newDraft, err := draftCopy(c, srcDraft, req.Name)
	if err != nil {
		logger.CtxLog(c).Errorf("DraftCopy draftCopy fail, err: %v", err)
		c.JSON(http.StatusOK, commProto2.NewCommRsp(530002, "服务繁忙"))
		return
	}
	c.JSON(http.StatusOK, commProto2.NewSuccessRsp(newDraft.ToViewObject()))
}
