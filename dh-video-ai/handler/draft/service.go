package draft

import (
	"acg-ai-go-common/gomysql"
	"acg-ai-go-common/logger"
	"dh-video-ai/beans/model"
	"dh-video-ai/beans/proto"
	dhUser "dh-video-ai/handler/dh-user"
	"encoding/json"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	"time"
)

func draftGetByDraftID(c *gin.Context, draftId string) (draft *model.AIVideoDraft, err error) {
	draft = &model.AIVideoDraft{}
	accountId, username, err := dhUser.CtxGetAccountIdAndUsername(c)
	if err != nil {
		logger.CtxLog(c).E<PERSON>rf("draftInit CtxGetAccountIdAndUsername fail, err:%v", err)
		return nil, err
	}
	logger.CtxLog(c).Infof("draftInit accountId:%v, username:%v", accountId, username)
	if err = draft.FindByDraftID(gomysql.DB, accountId, draftId, model.NotDeleted); err != nil {
		logger.CtxLog(c).<PERSON>rrorf("draftUpdate FindByDraftID fail, err:%v", err)
		return nil, err
	} else if draft.ID == 0 {
		logger.CtxLog(c).Warnf("draftUpdate FindByDraftID not found, draftId:%v", draftId)
		return draft, nil
	}
	return draft, nil
}

// draftInit 初始化草稿
func draftInit(c *gin.Context, name string) (*model.AIVideoDraft, error) {
	accountId, username, err := dhUser.CtxGetAccountIdAndUsername(c)
	if err != nil {
		logger.CtxLog(c).Errorf("draftInit CtxGetAccountIdAndUsername fail, err:%v", err)
		return nil, err
	}
	logger.CtxLog(c).Infof("draftInit accountId:%v, username:%v", accountId, username)
	// 创建草稿
	draft := &model.AIVideoDraft{
		Name:         name,
		UserID:       accountId,
		LastUpdateBy: username,
		AspectWidth:  16,
		AspectHeight: 9,
	}
	if err = draft.Insert(gomysql.DB); err != nil {
		logger.CtxLog(c).Errorf("draftInit Insert draft fail, err:%v", err)
		return nil, err
	}
	return draft, nil
}

func draftUpdateByDraftID(c *gin.Context, draftData *proto.DraftUpdateReq) (draft *model.AIVideoDraft, err error) {
	draft = &model.AIVideoDraft{}
	accountId, username, err := dhUser.CtxGetAccountIdAndUsername(c)
	if err != nil {
		logger.CtxLog(c).Errorf("draftUpdate CtxGetAccountIdAndUsername fail, err:%v", err)
		return nil, err
	}
	logger.CtxLog(c).Infof("draftUpdate accountId:%v, username:%v", accountId, username)
	if err = draft.FindByDraftID(gomysql.DB, accountId, draftData.DraftID, model.NotDeleted); err != nil {
		logger.CtxLog(c).Errorf("draftUpdate FindByDraftID fail, err:%v", err)
		return nil, err
	} else if draft.ID == 0 {
		logger.CtxLog(c).Warnf("draftUpdate FindByDraftID not found, draftId:%v", draftData.DraftID)
		return draft, nil
	}
	// tracks json string 转 json.RawMessage
	tracksBytes, err := json.Marshal(draftData.Tracks)
	if err != nil {
		logger.CtxLog(c).Errorf("draftUpdate json.Marshal draftData.Tracks fail, err:%v", err)
		return draft, err
	}
	draft.LastUpdateBy = username
	draft.Name = draftData.Name
	draft.TemplateID = draftData.TemplateID
	draft.StoryboardID = draftData.StoryBoardID
	draft.Duration = draftData.Duration
	draft.AspectWidth = draftData.AspectWidth
	draft.AspectHeight = draftData.AspectHeight
	draft.Status = draftData.Status
	draft.Tracks = tracksBytes
	if err = draft.Update(gomysql.DB); err != nil {
		logger.CtxLog(c).Errorf("draftInit Insert draft fail, err:%v", err)
		return draft, err
	}
	return draft, nil
}

func draftDeleteByDraftID(c *gin.Context, draftId string) error {
	accountId, username, err := dhUser.CtxGetAccountIdAndUsername(c)
	if err != nil {
		logger.CtxLog(c).Errorf("draftDeleteByDraftID CtxGetAccountIdAndUsername fail, err:%v", err)
		return err
	}
	draft := &model.AIVideoDraft{}
	if err := draft.FindByDraftID(gomysql.DB, accountId, draftId, model.NotDeleted); err != nil {
		logger.CtxLog(c).Errorf("draftDeleteByDraftID FindByDraftID fail, err:%v", err)
		return err
	} else if draft.ID == 0 {
		logger.CtxLog(c).Warnf("draftDeleteByDraftID FindByDraftID not found, draftId:%v", draftId)
		return nil
	}
	logger.CtxLog(c).Infof("draftDeleteByDraftID accountId:%v, username:%v", accountId, username)
	return draft.Delete(gomysql.DB)
}

func draftCopy(c *gin.Context, srcDraft *model.AIVideoDraft, name string) (*model.AIVideoDraft, error) {
	startTime := time.Now()
	userId := srcDraft.UserID
	newDraft := &model.AIVideoDraft{
		Name:         name,
		Source:       srcDraft.Source,
		TemplateID:   srcDraft.TemplateID,
		UserID:       userId,
		LastUpdateBy: srcDraft.LastUpdateBy,
		Duration:     srcDraft.Duration,
		AspectWidth:  srcDraft.AspectWidth,
		AspectHeight: srcDraft.AspectHeight,
		Status:       srcDraft.Status,
		Tracks:       srcDraft.Tracks,
		ThumbnailURL: srcDraft.ThumbnailURL,
	}
	// 开启事务拷贝数据
	err := gomysql.DB.Transaction(func(tx *gorm.DB) error {
		// 拷贝草稿数据
		if err := newDraft.Insert(tx); err != nil {
			return err
		}
		logger.CtxLog(c).Infof("copy draft end, srcDraftId:%v, newDraftId:%v",
			srcDraft.DraftID, newDraft.DraftID)
		// 查询原始草稿的所有分镜数据
		srcStoryboards, err := (&model.AIVideoStoryboard{}).FindByDraftID(gomysql.DB, srcDraft.UserID, srcDraft.DraftID)
		if err != nil {
			return err
		}
		// 组装copy草稿要新插入的分镜数据
		var newDraftStoryboards []*model.AIVideoStoryboard
		for _, storyboard := range srcStoryboards {
			newDraftStoryboards = append(newDraftStoryboards, &model.AIVideoStoryboard{
				Source:          storyboard.Source,
				DraftID:         newDraft.DraftID,
				UserID:          userId,
				LastUpdateBy:    storyboard.LastUpdateBy,
				Prompt:          storyboard.Prompt,
				PresetDuration:  storyboard.PresetDuration,
				DigitalHuman:    storyboard.DigitalHuman,
				TemplateID:      storyboard.TemplateID,
				Template:        storyboard.Template,
				VideoPurpose:    storyboard.VideoPurpose,
				SpeakerIdentity: storyboard.SpeakerIdentity,
				Audience:        storyboard.Audience,
				OutlineContent:  storyboard.OutlineContent,
				ScriptContent:   storyboard.ScriptContent,
				PictureContent:  storyboard.PictureContent,
				Status:          storyboard.Status,
			})
		}
		// 拷贝分镜数据
		if err = (&model.AIVideoStoryboard{}).InsertBatch(tx, newDraftStoryboards); err != nil {
			return err
		}

		logger.CtxLog(c).Infof("copy draft storyboards end, len:%v", len(srcStoryboards))
		// 原始分镜与拷贝新分镜ID映射关系
		srcStboNewStboMap := make(map[string]string)
		for i, srcStoryboard := range srcStoryboards {
			srcStboNewStboMap[srcStoryboard.StoryboardID] = newDraftStoryboards[i].StoryboardID
		}
		newDraft.StoryboardID = srcStboNewStboMap[srcDraft.StoryboardID]
		if err = newDraft.Update(tx); err != nil {
			return err
		}

		// 查询原始草稿的所有分镜画面数据
		var newDraftStoryboardPictures []*model.StoryboardPicture
		srcStoryboardPictures, err := (&model.StoryboardPicture{}).FindByDraftID(tx, srcDraft.DraftID)
		if err != nil {
			return err
		} else if len(srcStoryboardPictures) > 0 {
			for _, srcPicture := range srcStoryboardPictures {
				newDraftStoryboardPictures = append(newDraftStoryboardPictures, &model.StoryboardPicture{
					UserID:         newDraft.UserID,
					DraftID:        newDraft.DraftID,
					StoryboardID:   srcStboNewStboMap[srcPicture.StoryboardID],
					PageNo:         srcPicture.PageNo,
					Params:         srcPicture.Params,
					PictureContent: srcPicture.PictureContent,
					Status:         srcPicture.Status,
					Reason:         srcPicture.Reason,
				})
			}
			if err = (&model.StoryboardPicture{}).InsertBatch(tx, newDraftStoryboardPictures); err != nil {
				return err
			}
		}
		logger.CtxLog(c).Infof("copy draft storyboard pictures end, len:%v", len(newDraftStoryboardPictures))
		logger.CtxLog(c).Infof("copy draft and storyboards success, cost:%v", time.Since(startTime))
		return nil
	})
	return newDraft, err
}
