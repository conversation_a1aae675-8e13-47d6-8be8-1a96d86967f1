package beans

import (
	"database/sql/driver"
	"encoding/json"
	"errors"
)

// Duration 结构体用于存储时间，包含数值和单位。
type Duration struct {
	Num  *float64 `json:"num" binding:"required"`
	Unit *string  `json:"unit" binding:"required"`
}

func (d *Duration) Value() (driver.Value, error) {
	if d == nil {
		return nil, nil
	}
	b, err := json.Marshal(d)
	if err != nil {
		return nil, err
	}
	return string(b), nil
}

// Scan 方法实现 sql.Scanner 接口，用于将数据库中的 JSON 字符串转换回结构体。
func (d *Duration) Scan(input interface{}) error {
	if input == nil {
		return nil
	}
	bytes, ok := input.([]byte)
	if !ok {
		return errors.New("type assertion to []byte failed")
	}
	return json.Unmarshal(bytes, d)
}

type JSONMap map[string]interface{}

// Value 方法实现 driver.Valuer 接口，用于将 JSONMap 转换为 JSON 字符串存储在数据库中。
func (jm *JSONMap) Value() (driver.Value, error) {
	if jm == nil {
		return nil, nil
	}
	b, err := json.Marshal(jm)
	if err != nil {
		return nil, err
	}
	return string(b), nil
}

// Scan 方法实现 sql.Scanner 接口，用于将数据库中的 JSON 字符串转换回 JSONMap。
func (jm *JSONMap) Scan(input interface{}) error {
	if input == nil {
		return nil
	}
	bytes, ok := input.([]byte)
	if !ok {
		return errors.New("type assertion to []byte failed")
	}
	return json.Unmarshal(bytes, jm)
}
