package proto

type AIVideoDraft struct {
	ID           uint64 `json:"id"`
	DraftID      string `json:"draftId"`
	Name         string `json:"name"`
	Source       string `json:"source"` // 草稿来源: ai, template
	TemplateID   string `json:"templateId"`
	StoryboardID string `json:"storyboardId"`
	UserID       string `json:"userId"`
	LastUpdateBy string `json:"lastUpdateBy"`
	Duration     int    `json:"duration"`
	AspectWidth  int8   `json:"aspectWidth"`
	AspectHeight int8   `json:"aspectHeight"`
	Status       string `json:"status"` // 草稿状态: outline, editor
	Tracks       string `json:"tracks"`
	ThumbnailURL string `json:"thumbnailUrl"`
	CreateTime   string `json:"createTime"`
	UpdateTime   string `json:"updateTime"`
	IsDelete     int8   `json:"isDelete"`
}

type DraftIDRequired struct {
	DraftID string `json:"draftId" form:"draftId" uri:"draftId" binding:"required"`
}

type DraftGetReq struct {
	DraftIDRequired
}

type DraftCreateReq struct {
	Name string `json:"name" binding:"required"`
}

type DraftUpdateReq struct {
	DraftIDRequired
	Name         string `json:"name" binding:"required,min=1"`
	Status       string `json:"status" binding:"required,min=1"`
	TemplateID   string `json:"templateId" binding:"required,min=1"`
	StoryBoardID string `json:"storyBoardId" binding:"required,min=1"`
	Duration     int    `json:"duration"`
	AspectWidth  int8   `json:"aspectWidth" binding:"required,min=1"`
	AspectHeight int8   `json:"aspectHeight" binding:"required,min=1"`
	Tracks       string `json:"tracks" binding:"required,min=1"`
}

type DraftDeleteReq struct {
	DraftIDRequired
}

type DraftCopyReq struct {
	DraftIDRequired
	Name   string `json:"name" binding:"required"`
	Module string `json:"module"`
	UserID string `json:"userId" binding:"required"`
}
