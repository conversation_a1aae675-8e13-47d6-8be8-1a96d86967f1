package proto

import (
	commProto2 "acg-ai-go-common/beans/proto2"
	"dh-video-ai/beans"
	"encoding/json"
)

type StoryboardIdNotRequired struct {
	StoryboardId string `json:"storyboardId" form:"storyboardId" uri:"storyboardId"`
}

type StoryboardIdRequired struct {
	StoryboardId string `json:"storyboardId" form:"storyboardId" uri:"storyboardId" binding:"required"`
}

type GetHistoryReq struct {
	DraftIDRequired
	commProto2.CommSkipPage
	StoryboardIdNotRequired // 分镜ID，传该参数时返回的pageNo和totalCount需要时该用户全局的信息
}

type StoryboardSaveReq struct {
	DraftIDRequired
	StoryboardIdNotRequired
	Source          string          `json:"source" form:"source" uri:"source" binding:"required"`
	Prompt          string          `json:"prompt" form:"prompt" uri:"prompt" binding:"required"`
	TemplateId      string          `json:"templateId" form:"templateId" uri:"templateId"`
	Template        json.RawMessage `json:"template" form:"template" uri:"template"`
	PresetDuration  *beans.Duration `json:"presetDuration" form:"presetDuration" uri:"presetDuration"`
	DigitalHuman    string          `json:"digitalHuman" form:"digitalHuman" uri:"digitalHuman"`
	VideoPurpose    string          `json:"videoPurpose" form:"videoPurpose" uri:"videoPurpose"`
	SpeakerIdentity string          `json:"speakerIdentity" form:"speakerIdentity" uri:"speakerIdentity"`
	Audience        string          `json:"audience" form:"audience" uri:"audience"`
	OutlineContent  json.RawMessage `json:"outlineContent" form:"outlineContent" uri:"outlineContent"`
	ScriptContent   json.RawMessage `json:"scriptContent" form:"scriptContent" uri:"scriptContent"`
	PictureContent  json.RawMessage `json:"pictureContent" form:"pictureContent" uri:"pictureContent"`
}

type GenerateOutlineReq struct {
	DraftIDRequired
	StoryboardIdRequired
}

type GenerateScriptReq struct {
	DraftIDRequired
	StoryboardIdRequired
}

type GeneratePictureReq struct {
	DraftIDRequired
	StoryboardIdRequired
	PageNo string `json:"pageNo" form:"pageNo" uri:"pageNo" binding:"required"`
}

type GeneratePictureResult struct {
	ID        string      `json:"id"`
	PageNo    string      `json:"pageNo"`
	Variables interface{} `json:"variables"`
}
