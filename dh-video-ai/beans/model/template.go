package model

import (
	"gorm.io/gorm"
	"time"
)

// VideoTemplate 结构体对应 meta_human_editor_saas.template 表
type VideoTemplate struct {
	ID               int64     `json:"id" gorm:"primaryKey;autoIncrement;column:id"`                                                        // 主键 ID
	TemplateID       string    `json:"templateId" gorm:"unique;size:32;not null;column:template_id"`                                        // 模板 ID
	Name             string    `json:"name" gorm:"size:32;not null;column:name"`                                                            // 模板名称
	Type             string    `json:"type" gorm:"size:32;not null;column:type"`                                                            // 模板类型 (BusinessCard/Slide)
	Tag              string    `json:"tag" gorm:"size:32;not null;column:tag"`                                                              // 模板标签
	SubTag           string    `json:"subTag" gorm:"size:512;not null;default:'';column:sub_tag"`                                           // 子标签
	AspectWidth      int       `json:"aspectWidth" gorm:"not null;default:16;column:aspect_width"`                                          // 画布宽度比例
	AspectHeight     int       `json:"aspectHeight" gorm:"not null;default:9;column:aspect_height"`                                         // 画布高度比例
	WhetherSystem    int8      `json:"whetherSystem" gorm:"not null;default:1;column:whether_system"`                                       // 是否属于系统库 (1=是, 0=否)
	Config           string    `json:"config" gorm:"type:mediumtext;not null;column:config"`                                                // 模板信息 (JSON 格式)
	TrackConfig      string    `json:"trackConfig" gorm:"type:text;column:track_config"`                                                    // 轨道配置信息
	Variables        string    `json:"variables" gorm:"type:text;not null;column:variables"`                                                // 变量表
	OriginContent    string    `json:"originContent" gorm:"type:text;not null;column:origin_content"`                                       // 原始文本内容
	ThumbnailURL     string    `json:"thumbnailUrl" gorm:"size:512;not null;default:'';column:thumbnail_url"`                               // 缩略图地址
	VideoURL         string    `json:"videoUrl" gorm:"size:512;not null;default:'';column:video_url"`                                       // 模板视频地址
	AICsvURL         string    `json:"aiCsvUrl" gorm:"size:512;not null;default:'';column:ai_csv_url"`                                      // AI 配音 CSV 文件地址
	RealPersonCSVURL string    `json:"realPersonCsvUrl" gorm:"size:512;not null;default:'';column:real_person_csv_url"`                     // 真人配音 CSV 文件地址
	CreateTime       time.Time `json:"createTime" gorm:"not null;default:CURRENT_TIMESTAMP;column:create_time"`                             // 创建时间
	UpdateTime       time.Time `json:"updateTime" gorm:"not null;default:CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP;column:update_time"` // 更新时间
	Duration         int64     `json:"duration" gorm:"not null;default:0;column:duration"`                                                  // 时长 (毫秒)
	LLMConfigID      string    `json:"llmConfigId" gorm:"size:128;column:llm_config_id"`                                                    // 大模型 ID
	IsDelete         int8      `json:"isDelete" gorm:"not null;default:0;column:is_delete"`                                                 // 是否删除 (0=正常, 1=删除)
	DefaultNum       int64     `json:"defaultNum" gorm:"not null;default:0;column:default_num"`                                             // 默认使用数量
	Sort             int64     `json:"sort" gorm:"not null;default:0;column:sort"`                                                          // 排序权重
	IsRecommended    int8      `json:"isRecommended" gorm:"not null;default:0;column:is_recommended"`                                       // 是否为推荐模板 (1=推荐, 0=普通)
}

type VideoTemplateVo struct {
	ID               int64  `json:"id"`               // 主键 ID
	TemplateID       string `json:"templateId"`       // 模板 ID
	Name             string `json:"name"`             // 模板名称
	Type             string `json:"type"`             // 模板类型 (BusinessCard/Slide)
	Tag              string `json:"tag"`              // 模板标签
	SubTag           string `json:"subTag"`           // 子标签
	AspectWidth      int    `json:"aspectWidth" `     // 画布宽度比例
	AspectHeight     int    `json:"aspectHeight"`     // 画布高度比例
	WhetherSystem    bool   `json:"whetherSystem"`    // 是否属于系统库
	Config           string `json:"config"`           // 模板信息 (JSON 格式)
	TrackConfig      string `json:"trackConfig"`      // 轨道配置信息
	Variables        string `json:"variables"`        // 变量表
	OriginContent    string `json:"originContent"`    // 原始文本内容
	ThumbnailURL     string `json:"thumbnailUrl"`     // 缩略图地址
	VideoURL         string `json:"videoUrl"`         // 模板视频地址
	AICsvURL         string `json:"aiCsvUrl"`         // AI 配音 CSV 文件地址
	RealPersonCSVURL string `json:"realPersonCsvUrl"` // 真人配音 CSV 文件地址
	CreateTime       string `json:"createTime"`       // 创建时间
	UpdateTime       string `json:"updateTime"`       // 更新时间
	Duration         int64  `json:"duration"`         // 时长 (毫秒)
	LLMConfigID      string `json:"llmConfigId"`      // 大模型 ID
	IsDelete         int8   `json:"isDelete"`         // 是否删除 (0=正常, 1=删除)
	DefaultNum       int64  `json:"defaultNum"`       // 默认使用数量
	Sort             int64  `json:"sort"`             // 排序权重
	IsRecommended    int8   `json:"isRecommended"`    // 是否为推荐模板 (1=推荐, 0=普通)
}

func (t *VideoTemplate) TableName() string {
	return "template"
}

func (t *VideoTemplate) FindByTemplateID(tx *gorm.DB, templateId string, isDelete int8) error {
	err := tx.Where("template_id = ? And is_delete = ?", templateId, isDelete).Find(&t).Error
	if err == nil || err == gorm.ErrRecordNotFound {
		return nil
	}
	return err
}

type AITemplateVariables []AITemplateVariableItem

type AITemplateVariableItem struct {
	ID         string   `json:"id"`
	Cover      bool     `json:"cover"`
	Tag        []string `json:"tag"`
	FigureInfo struct {
		ResolutionX int      `json:"resolutionX"` // 分辨率-X轴
		ResolutionY int      `json:"resolutionY"` // 分辨率-Y轴
		AspectRatio float64  `json:"aspectRatio"` // 宽高比
		Tags        []string `json:"tags"`
	} `json:"figureInfo"`
	Prompt    string `json:"prompt"`
	Variables []struct {
		Key   string `json:"key"`
		Title bool   `json:"title"`
	} `json:"variables"`
}
