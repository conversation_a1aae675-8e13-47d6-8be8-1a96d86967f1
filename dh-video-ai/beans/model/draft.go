package model

import (
	"acg-ai-go-common/utils"
	"dh-video-ai/beans/proto"
	"encoding/json"
	"errors"
	"github.com/google/uuid"
	"gorm.io/gorm"
	"strings"
	"time"
)

// AIVideoDraft AI视频草稿表
type AIVideoDraft struct {
	ID           uint64          `json:"id" gorm:"primaryKey;autoIncrement;column:id;comment:数据主键自增ID"`
	DraftID      string          `json:"draftId" gorm:"size:64;unique;not null;column:draft_id;comment:草稿ID"`
	Name         string          `json:"name" gorm:"size:100;not null;column:name;comment:草稿名字"`
	Source       string          `json:"source" gorm:"size:100;not null;column:source;comment:草稿来源"` // 草稿来源: ai, template
	TemplateID   string          `json:"templateId" gorm:"size:100;not null;index;column:template_id;comment:草稿关联模板ID"`
	StoryboardID string          `json:"storyboardId" gorm:"size:100;column:storyboard_id;comment:当前选择的分镜ID"`
	UserID       string          `json:"userId" gorm:"size:100;not null;index;column:user_id;comment:所属账号ID"`
	LastUpdateBy string          `json:"lastUpdateBy" gorm:"size:100;column:last_update_by;comment:最后修改者名称"`
	Duration     int             `json:"duration" gorm:"type:int(11);not null;column:duration;comment:草稿时长/单位毫秒"`
	AspectWidth  int8            `json:"aspectWidth" gorm:"type:tinyint(4);not null;column:aspect_width;comment:画面比例-宽"`
	AspectHeight int8            `json:"aspectHeight" gorm:"type:tinyint(4);not null;column:aspect_height;comment:画面比例-高"`
	Status       string          `json:"status" gorm:"size:100;not null;default:outline;column:status;comment:草稿状态"` // 草稿状态: outline, editor
	Tracks       json.RawMessage `json:"tracks" gorm:"type:mediumtext;column:tracks;comment:制作视频的基础数据"`
	ThumbnailURL string          `json:"thumbnailUrl" gorm:"size:512;column:thumbnail_url;comment:缩略图"`
	CreateTime   time.Time       `json:"createTime" gorm:"not null;autoCreateTime;column:create_time;comment:创建时间"`
	UpdateTime   time.Time       `json:"updateTime" gorm:"not null;autoUpdateTime;column:update_time;comment:更新时间"`
	IsDelete     int8            `json:"isDelete" gorm:"not null;default:0;index;column:is_delete;comment:是否删除（0:未删除, 1:已删除）"`
}

// TableName 自定义表名
func (aid *AIVideoDraft) TableName() string {
	return "slide_draft"
}

func (aid *AIVideoDraft) BeforeCreate(tx *gorm.DB) (err error) {
	aid.DraftID = "sd-" + strings.ReplaceAll(uuid.NewString(), "-", "")
	return
}

func (aid *AIVideoDraft) BeforeUpdate(tx *gorm.DB) (err error) {
	if len(aid.DraftID) == 0 {
		return errors.New("DraftID不能为空")
	}
	return
}

func (aid *AIVideoDraft) Insert(tx *gorm.DB) (err error) {
	return tx.Create(&aid).Error
}

func (aid *AIVideoDraft) Update(tx *gorm.DB) (err error) {
	return tx.Where("draft_id = ?", aid.DraftID).Updates(&aid).Error
}

func (aid *AIVideoDraft) Delete(tx *gorm.DB) (err error) {
	return tx.Model(aid).Where("draft_id = ?", aid.DraftID).Update("is_delete", Deleted).Error
}

func (aid *AIVideoDraft) FindByDraftID(tx *gorm.DB, userId, draftId string, isDelete int8) error {
	err := tx.Where("user_id = ? And draft_id = ? And is_delete = ?", userId, draftId, isDelete).Find(&aid).Error
	if err == nil || err == gorm.ErrRecordNotFound {
		return nil
	}
	return err
}

func (aid *AIVideoDraft) Search(tx *gorm.DB, userId, draftName string,
	pageNo, pageSize int64) (int64, []*AIVideoDraft, error) {
	var count int64
	var aids []*AIVideoDraft
	conn := tx.Model(&AIVideoDraft{}).Where("is_delete = ?", NotDeleted)
	if len(userId) > 0 {
		conn.Where("user_id = ?", userId)
	}
	if len(draftName) > 0 {
		conn.Where("name like ?", "%"+draftName+"%")
	}
	skip, limit := utils.ConvertPageNumSize2Skip(pageNo, pageSize)
	if err := conn.Count(&count).Order("update_time desc").
		Limit(int(limit)).Offset(int(skip)).Find(&aids).Error; err != nil {
		return 0, nil, err
	}
	return count, aids, nil
}

func (aid *AIVideoDraft) ToViewObject() proto.AIVideoDraft {
	return proto.AIVideoDraft{
		ID:           aid.ID,
		DraftID:      aid.DraftID,
		Name:         aid.Name,
		Source:       aid.Source,
		TemplateID:   aid.TemplateID,
		StoryboardID: aid.StoryboardID,
		UserID:       aid.UserID,
		LastUpdateBy: aid.LastUpdateBy,
		Duration:     aid.Duration,
		AspectWidth:  aid.AspectWidth,
		AspectHeight: aid.AspectHeight,
		Status:       aid.Status,
		Tracks:       string(aid.Tracks),
		ThumbnailURL: aid.ThumbnailURL,
		CreateTime:   aid.CreateTime.Format("2006-01-02T15:04:05.000"),
		UpdateTime:   aid.UpdateTime.Format("2006-01-02T15:04:05.000"),
		IsDelete:     aid.IsDelete,
	}
}
