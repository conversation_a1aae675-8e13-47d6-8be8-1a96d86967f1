package model

import (
	"encoding/json"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"time"
)

// StoryboardPicture AI视频分镜画面表
type StoryboardPicture struct {
	ID             uint64          `json:"id" gorm:"primaryKey;autoIncrement;column:id;comment:数据主键自增ID"`
	UserID         string          `json:"userId" gorm:"size:64;not null;index;column:user_id;comment:所属账号ID"`
	DraftID        string          `json:"draftId" gorm:"size:64;not null;index;column:draft_id;comment:草稿ID"`
	StoryboardID   string          `json:"storyboardId" gorm:"size:64;index:storyboard_page_idx;not null;column:storyboard_id;comment:分镜ID"`
	PageNo         string          `json:"pageNo" gorm:"size:64;index:storyboard_page_idx;not null;column:page_no;comment:分镜页ID"`
	Params         json.RawMessage `json:"params" gorm:"type:json;column:params;comment:请求参数"`
	PictureContent json.RawMessage `json:"pictureContent" gorm:"type:json;column:picture_content;comment:画面分镜数据"`
	Status         string          `json:"status" gorm:"size:64;column:status;comment:状态"`
	Reason         string          `json:"reason" gorm:"size:255;colum:reason;comment:原因"`
	CreateTime     time.Time       `json:"createTime" gorm:"not null;autoCreateTime;column:create_time;comment:创建时间"`
	UpdateTime     time.Time       `json:"updateTime" gorm:"not null;autoUpdateTime;column:update_time;comment:更新时间"`
	DeleteTime     gorm.DeletedAt  `json:"-" gorm:"index;column:delete_time;comment:删除时间/标记删除"`
}

// TableName 自定义表名
func (sp *StoryboardPicture) TableName() string {
	return "video_storyboard_picture"
}

func (sp *StoryboardPicture) Insert(tx *gorm.DB) (err error) {
	return tx.Create(&sp).Error
}

// InsertBatch 批量创建数据
func (sp *StoryboardPicture) InsertBatch(tx *gorm.DB, storyboardPictures []*StoryboardPicture) (err error) {
	return tx.Clauses(clause.OnConflict{DoNothing: true}).
		CreateInBatches(&storyboardPictures, len(storyboardPictures)).Error
}

func (sp *StoryboardPicture) Update(tx *gorm.DB) (err error) {
	return tx.Where("storyboard_id = ? and pageNo = ?", sp.StoryboardID, sp.PageNo).Updates(&sp).Error
}

func (sp *StoryboardPicture) Save(tx *gorm.DB) error {
	curSp := &StoryboardPicture{}
	if err := curSp.FindByStoryboardIDAndPageNo(tx, sp.StoryboardID, sp.PageNo); err != nil {
		return err
	}
	if curSp.ID > 0 {
		curSp.Params = sp.Params
		curSp.PictureContent = sp.PictureContent
		curSp.Status = sp.Status
		curSp.Reason = sp.Reason
		return tx.Save(curSp).Error
	}
	return tx.Save(sp).Error
}

func (sp *StoryboardPicture) Delete(tx *gorm.DB) (err error) {
	return tx.Where("storyboard_id = ? and pageNo = ?", sp.StoryboardID, sp.PageNo).Delete(&sp).Error
}

func (sp *StoryboardPicture) DeleteByStoryboardID(tx *gorm.DB, storyboardID string) (err error) {
	return tx.Where("storyboard_id = ?", storyboardID).Delete(&sp).Error
}

func (sp *StoryboardPicture) FindByDraftID(tx *gorm.DB, draftId string) ([]*StoryboardPicture, error) {
	var spList []*StoryboardPicture
	err := tx.Where("draft_id = ?", draftId).Find(&spList).Error
	if err == nil || err == gorm.ErrRecordNotFound {
		return spList, nil
	}
	return spList, err
}

func (sp *StoryboardPicture) FindByStoryboardID(tx *gorm.DB, storyboardId string) ([]*StoryboardPicture, error) {
	var spList []*StoryboardPicture
	err := tx.Where("storyboard_id = ?", storyboardId).Find(&spList).Error
	if err == nil || err == gorm.ErrRecordNotFound {
		return spList, nil
	}
	return spList, err
}

func (sp *StoryboardPicture) FindByStoryboardIDAndPageNo(tx *gorm.DB, storyboardId string, pageNo string) error {
	err := tx.Where("storyboard_id = ? and page_no = ?", storyboardId, pageNo).First(&sp).Error
	if err == nil || err == gorm.ErrRecordNotFound {
		return nil
	}
	return err
}
