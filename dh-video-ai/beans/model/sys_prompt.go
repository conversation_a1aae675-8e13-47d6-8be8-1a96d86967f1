package model

import (
	"acg-ai-go-common/gomysql"
	"acg-ai-go-common/utils"
	"dh-video-ai/beans/utils/language"
	"errors"
	"fmt"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	"strconv"
	"strings"
	"time"
)

// SysPrompt 系统数据-示例prompt
type SysPrompt struct {
	ID        uint64         `json:"id" gorm:"column:id;primarykey;autoIncrement;comment:数据主键自增ID"`
	Type      string         `json:"type" gorm:"size:100;not null;default:video-ai;column:type;index;comment:类型"`
	Title     string         `json:"title" gorm:"size:255;not null;default:'';column:title;index;comment:标题"`
	Prompt    string         `json:"prompt" gorm:"size:2048;not null;default:'';column:prompt;comment:提示词"`
	Sort      int64          `json:"sort" gorm:"column:sort;not null;default:0;index;comment:排序权重"`
	Creator   string         `json:"creator" gorm:"size:100;column:creator;comment:创建者"`
	CreatedAt time.Time      `json:"createdAt" gorm:"not null;autoCreateTime;column:createdAt;comment:创建时间"`
	Operator  string         `json:"operator" gorm:"size:100;column:operator;comment:最后操作者"`
	UpdatedAt time.Time      `json:"updatedAt" gorm:"not null;autoUpdateTime;column:updatedAt;comment:更新时间"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"column:deletedAt;index;comment:删除时间/标记删除"`
}

func (sc *SysPrompt) TableName() string {
	return "sys_prompt_v1"
}

type SysPromptDTO struct {
	ID        uint64    `json:"id" gorm:"column:id;primarykey;autoIncrement;comment:数据主键自增ID"`
	Type      string    `json:"type" gorm:"size:100;column:type;index;comment:类型"`
	Title     string    `json:"title" gorm:"size:255;column:title;index;comment:标题"`
	Prompt    string    `json:"prompt" gorm:"size:2048;column:prompt;comment:提示词"`
	Sort      int64     `json:"sort" gorm:"column:sort;index;comment:排序权重"`
	CreatedAt time.Time `json:"createTime" gorm:"not null;autoCreateTime;column:createdAt;comment:创建时间"`
	UpdatedAt time.Time `json:"updateTime" gorm:"not null;autoUpdateTime;column:updatedAt;comment:更新时间"`
}

func (sc *SysPrompt) ToDTO() *SysPromptDTO {
	return &SysPromptDTO{
		ID:        sc.ID,
		Type:      sc.Type,
		Title:     sc.Title,
		Prompt:    sc.Prompt,
		Sort:      sc.Sort,
		CreatedAt: sc.CreatedAt,
		UpdatedAt: sc.UpdatedAt,
	}
}

func (sc *SysPrompt) ToMultiLanguageDTO(c *gin.Context) *SysPromptDTO {
	return &SysPromptDTO{
		ID:        sc.ID,
		Type:      sc.Type,
		Title:     language.GetSysPromptContent(c, sc.Title),
		Prompt:    language.GetSysPromptContent(c, sc.Prompt),
		Sort:      sc.Sort,
		CreatedAt: sc.CreatedAt,
		UpdatedAt: sc.UpdatedAt,
	}
}

func (sc *SysPrompt) Insert() error {
	// 创建数据
	return gomysql.DB.Create(&sc).Error
}

func (sc *SysPrompt) InsertBatch(results []*SysPrompt) error {
	// 创建数据
	return gomysql.DB.CreateInBatches(&results, len(results)).Error
}

func (sc *SysPrompt) SaveOrUpdate() error {
	// 创建或更新数据
	return gomysql.DB.Save(&sc).Error
}

func (sc *SysPrompt) Update() error {
	if sc.ID == 0 {
		return errors.New("ID不能等于0")
	}
	result := gomysql.DB.Model(&SysPrompt{}).Where("id=?", sc.ID).Updates(SysPrompt{
		Title:    sc.Title,
		Prompt:   sc.Prompt,
		Operator: sc.Operator,
	})
	if result.Error != nil {
		return result.Error
	}
	return nil
}
func (sc *SysPrompt) UpdateByOperator() error {
	result := gomysql.DB.Model(&SysPrompt{}).Where("id=?", sc.ID).Updates(SysPrompt{
		Operator: sc.Operator,
	})
	if result.Error != nil {
		return result.Error
	}
	return nil
}

func (sc *SysPrompt) FindByID(id uint64) error {
	err := gomysql.DB.Where("id = ?", id).Find(&sc).Error
	if err == nil || err == gorm.ErrRecordNotFound {
		return nil
	}
	return err
}

func (sc *SysPrompt) FindByIds(ids []uint64) ([]*SysPrompt, error) {
	var cases []*SysPrompt
	err := gomysql.DB.Where("id in ?", ids).Find(&cases).Error
	return cases, err
}

func (sc *SysPrompt) FindByType(promptType string) error {
	err := gomysql.DB.Where("type = ?", promptType).Find(&sc).Error
	if err == nil || err == gorm.ErrRecordNotFound {
		return nil
	}
	return err
}

func (sc *SysPrompt) FindByTime(startTime, endTime time.Time) ([]*SysPrompt, error) {
	var cases []*SysPrompt
	err := gomysql.DB.Where("createdAt BETWEEN ? AND ?", startTime, endTime).Find(&cases).Error
	return cases, err
}

func (sc *SysPrompt) FindAll() ([]*SysPrompt, error) {
	var cases []*SysPrompt
	err := gomysql.DB.Find(&cases).Error
	return cases, err
}

func (sc *SysPrompt) Search(promptType string, keyword string, pageNo, pageSize int64) (int64, []*SysPrompt, error) {
	var count int64
	var figures []*SysPrompt
	conn := gomysql.DB.Model(&SysPrompt{})
	if len(promptType) > 0 {
		conn.Where("type = ?", promptType)
	}
	if len(keyword) > 0 {
		conn.Where("prompt like ?", "%"+keyword+"%")
	}
	skip, limit := utils.ConvertPageNumSize2Skip(pageNo, pageSize)
	if err := conn.Count(&count).Order("sort desc, updatedAt desc").
		Limit(int(limit)).Offset(int(skip)).Find(&figures).Error; err != nil {
		return 0, nil, err
	}
	return count, figures, nil
}

func (sc *SysPrompt) SearchList(pageNo, pageSize int64, createAt string) (int64, []*SysPrompt, error) {
	conn := gomysql.DB.Model(sc)
	if len(sc.Type) > 0 {
		conn.Where("type = ?", sc.Type)
	}
	if len(sc.Title) > 0 {
		conn.Where("title like ?", "%"+sc.Title+"%")
	}
	if len(sc.Prompt) > 0 {
		conn.Where("prompt like ?", "%"+sc.Prompt+"%")
	}
	if len(createAt) > 0 {
		timestamps := strings.Split(createAt, ",")
		if len(timestamps) == 2 {
			start, err1 := strconv.ParseInt(timestamps[0], 10, 64)
			end, err2 := strconv.ParseInt(timestamps[1], 10, 64)
			if err1 != nil || err2 != nil {
				return 0, nil, fmt.Errorf("无效的时间戳范围")
			}
			conn.Where("createdAt > ? AND createdAt < ?", time.Unix(start, 0), time.Unix(end, 0))
		} else {
			return 0, nil, fmt.Errorf("时间戳格式无效")
		}
	}
	var total int64
	if err := conn.Count(&total).Error; err != nil {
		return 0, nil, err
	}
	skip, limit := utils.ConvertPageNumSize2Skip(pageNo, pageSize)
	var sypros []*SysPrompt
	if err := conn.Offset(int(skip)).Limit(int(limit)).Find(&sypros).Error; err != nil {
		return 0, nil, err
	}
	return total, sypros, nil
}

func (sc *SysPrompt) DeleteByID(id uint64) error {
	return gomysql.DB.
		Where("id = ?", id).
		Delete(&sc).Error
}
