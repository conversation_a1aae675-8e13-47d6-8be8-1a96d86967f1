package model

import (
	"acg-ai-go-common/utils"
	"dh-video-ai/beans"
	"encoding/json"
	"errors"
	"github.com/google/uuid"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"strings"
	"time"
)

// AIVideoStoryboard AI视频分镜表
type AIVideoStoryboard struct {
	ID              uint64          `json:"id" gorm:"primaryKey;autoIncrement;column:id;comment:数据主键自增ID"`
	StoryboardID    string          `json:"storyboardId" gorm:"size:100;unique;not null;column:storyboard_id;comment:分镜ID"`
	Source          string          `json:"source" gorm:"size:100;not null;default:ai;column:source;comment:生成来源,ai(prompt)/input（用户输入脚本）"`
	DraftID         string          `json:"draftId" gorm:"size:64;not null;index;column:draft_id;comment:草稿ID"`
	UserID          string          `json:"userId" gorm:"size:100;not null;index;column:user_id;comment:所属账号ID"`
	LastUpdateBy    string          `json:"lastUpdateBy,omitempty" gorm:"size:100;column:last_update_by;comment:最后修改者名称"`
	Prompt          string          `json:"prompt" gorm:"type:mediumtext;column:prompt;comment:提示词/用户输入脚本"`
	PresetDuration  *beans.Duration `json:"presetDuration" gorm:"type:json;column:preset_duration;comment:预设时长/单位ms"`
	DigitalHuman    string          `json:"digitalHuman" gorm:"type:mediumtext;column:digital_human;comment:数字人+音色信息"`
	TemplateID      string          `json:"templateId" gorm:"size:100;column:template_id;comment:模板ID"`
	Template        json.RawMessage `json:"template" gorm:"type:json;column:template;comment:模板json数据,由前端生成"`
	VideoPurpose    string          `json:"videoPurpose" gorm:"size:500;column:video_purpose;comment:视频目的"`
	SpeakerIdentity string          `json:"speakerIdentity" gorm:"size:500;column:speaker_identity;comment:演讲者身份"`
	Audience        string          `json:"audience" gorm:"size:500;column:audience;comment:受众"`
	OutlineContent  json.RawMessage `json:"outlineContent" gorm:"type:json;column:outline_content;comment:大纲分镜数据"`
	ScriptContent   json.RawMessage `json:"scriptContent" gorm:"type:json;column:script_content;comment:脚本分镜数据"`
	PictureContent  json.RawMessage `json:"pictureContent,omitempty" gorm:"-"`
	Status          string          `json:"status" gorm:"size:50;default:'';column:status;comment:分镜状态"`
	CreateTime      time.Time       `json:"createTime" gorm:"not null;autoCreateTime;column:create_time;comment:创建时间"`
	UpdateTime      time.Time       `json:"updateTime" gorm:"not null;autoUpdateTime;column:update_time;comment:更新时间"`
	DeleteTime      gorm.DeletedAt  `json:"-" gorm:"index;column:delete_time;comment:删除时间/标记删除"`
}

// TableName 自定义表名
func (ais *AIVideoStoryboard) TableName() string {
	return "video_storyboard"
}

func (ais *AIVideoStoryboard) BeforeCreate(tx *gorm.DB) (err error) {
	ais.StoryboardID = "ais-" + strings.ReplaceAll(uuid.NewString(), "-", "")
	if len(ais.OutlineContent) == 0 {
		ais.OutlineContent = json.RawMessage("[]")
	}
	if len(ais.ScriptContent) == 0 {
		ais.ScriptContent = json.RawMessage("[]")
	}
	return
}

func (ais *AIVideoStoryboard) BeforeUpdate(tx *gorm.DB) (err error) {
	if len(ais.StoryboardID) == 0 {
		return errors.New("StoryboardID is empty")
	}
	return
}

func (ais *AIVideoStoryboard) Insert(tx *gorm.DB) (err error) {
	return tx.Create(&ais).Error
}

// InsertBatch 批量创建数据
func (ais *AIVideoStoryboard) InsertBatch(tx *gorm.DB, storyboards []*AIVideoStoryboard) (err error) {
	return tx.Clauses(clause.OnConflict{DoNothing: true}).CreateInBatches(&storyboards, len(storyboards)).Error
}

func (ais *AIVideoStoryboard) Update(tx *gorm.DB) (err error) {
	return tx.Where("storyboard_id = ?", ais.StoryboardID).Updates(&ais).Error
}

func (ais *AIVideoStoryboard) Delete(tx *gorm.DB) (err error) {
	return tx.Where("storyboard_id = ?", ais.StoryboardID).Delete(&ais).Error
}

func (ais *AIVideoStoryboard) FindByStoryboardID(tx *gorm.DB, userId,
	draftId string, storyboardId string) error {
	err := tx.Where("user_id = ? And draft_id = ? And storyboard_id = ?",
		userId, draftId, storyboardId).First(&ais).Error
	if err == nil || err == gorm.ErrRecordNotFound {
		return nil
	}
	return err
}

func (ais *AIVideoStoryboard) FindByDraftID(tx *gorm.DB, userId, draftId string) (storyboards []*AIVideoStoryboard, err error) {
	conn := tx.Model(&AIVideoStoryboard{}).
		Where("user_id = ? And draft_id = ?", userId, draftId)
	err = conn.Order("id asc").Find(&storyboards).Error
	return storyboards, err
}

func (ais *AIVideoStoryboard) Search(tx *gorm.DB, userId, draftId string,
	pageNo, pageSize int64) (count int64, storyboards []*AIVideoStoryboard, err error) {
	conn := tx.Model(&AIVideoStoryboard{})
	if len(userId) > 0 {
		conn.Where("user_id = ?", userId)
	}
	if len(draftId) > 0 {
		conn.Where("draft_id = ?", draftId)
	}
	skip, limit := utils.ConvertPageNumSize2Skip(pageNo, pageSize)
	if err = conn.Count(&count).Order("id asc").
		Limit(int(limit)).Offset(int(skip)).Find(&storyboards).Error; err != nil {
		return 0, nil, err
	}
	return count, storyboards, nil
}

type DigitalHuman struct {
	Human *FigureInfo `json:"human"`
	TTS   *TtsInfo    `json:"tts"`
}

type BaseFaceLocation struct {
	Width    float32 `json:"width"`
	Height   float32 `json:"height"`
	Top      float32 `json:"top"`
	Left     float32 `json:"left"`
	Rotation float32 `json:"rotation"`
}

type A2AFaceLocation struct {
	Id               string           `json:"id"`
	Name             string           `json:"name"`
	ResolutionWidth  int              `json:"resolutionWidth"`
	ResolutionHeight int              `json:"resolutionHeight"`
	FaceLocation     BaseFaceLocation `json:"faceLocation"`
}
type TtsInfo struct {
	Id     int    `json:"id"`
	UserId string `json:"userId"`
	Name   string `json:"name"`
	Gender int    `json:"gender"`
	Per    string `json:"per"`
	Sort   int    `json:"sort"`
	Config struct {
		Pit int `json:"pit"`
		Spd int `json:"spd"`
		Vol int `json:"vol"`
	} `json:"config"`
	Extra           interface{} `json:"extra"`
	Describe        string      `json:"describe"`
	ThumbnailUrl    string      `json:"thumbnailUrl"`
	ExampleText     string      `json:"exampleText"`
	ExampleAudioUrl string      `json:"exampleAudioUrl"`
	IsCollect       int         `json:"isCollect"`
	TagList         string      `json:"tagList"`
	TagListDetail   []string    `json:"tagListDetail"`
	NewID           string      `json:"newID"`
	IsNatural       int         `json:"isNatural"`
	IsPublish       int         `json:"isPublish"`
	Language        string      `json:"language"`
	EngineType      int         `json:"engineType"`
	ThirdId         string      `json:"thirdId"`
	Platform        int         `json:"platform"`
}

type FigureInfo struct {
	ID               int64       `json:"id"`
	Name             string      `json:"name"`
	CharacterID      string      `json:"characterId"`
	Type             string      `json:"type"`
	ResolutionWidth  int         `json:"resolutionWidth"`
	ResolutionHeight int         `json:"resolutionHeight"`
	Thumbnail        string      `json:"thumbnail"`
	TemplateImg      string      `json:"templateImg"`
	Effects          *Effects    `json:"effects"`
	FaceLocation     interface{} `json:"faceLocation"`
	Sort             int         `json:"sort"`
	VideoUrl         string      `json:"videoUrl"`
	ResourceLabel    string      `json:"resourceLabel"`
	Collected        bool        `json:"collected"`
	RecentlyUsed     bool        `json:"recentlyUsed"`
	SystemProvided   bool        `json:"systemProvided"`
	ConfigId         string      `json:"configId"`
	UserId           string      `json:"userId"`
	CharacterName    string      `json:"characterName"`
	Label            string      `json:"label"`
	Source           string      `json:"source"`
	Status           string      `json:"status"`
	TemplateVideo    string      `json:"templateVideo"`
	FigureName       string      `json:"figureName"`
	FigureResult     string      `json:"figureResult"`
}

type Effects struct {
	ChromaKey *ChromaKey `json:"chromaKey"`
	Version   float32    `json:"version"`
}

type ChromaKey struct {
	Screen     []int `json:"screen"`
	Similarity int   `json:"similarity"`
	Smoothness int   `json:"smoothness"`
	Opacity    int   `json:"opacity"`
	Spill      int   `json:"spill"`
}
