package model

import (
	"time"
)

// AIVideo AI视频表结构
type AIVideo struct {
	ID           uint64    `json:"id" gorm:"primaryKey;autoIncrement;column:id;comment:自增主键"`
	VideoID      string    `json:"videoId" gorm:"size:64;unique;not null;column:video_id;comment:视频id"`
	TaskID       string    `json:"taskId" gorm:"size:64;not null;column:task_id;comment:视频合成任务id"`
	Name         string    `json:"name" gorm:"size:100;not null;column:name;comment:作品名字"`
	DraftID      string    `json:"draftId" gorm:"size:64;not null;column:draft_id;comment:草稿id"`
	UserID       string    `json:"userId" gorm:"size:64;not null;index;column:user_id;comment:用户ID(dh-user返回的accountId)"`
	LastUpdateBy string    `json:"lastUpdateBy" gorm:"size:64;not null;column:last_update_by;comment:最后修改者"`
	VideoURL     string    `json:"videoUrl" gorm:"size:512;not null;default:'';column:video_url;comment:视频地址"`
	AudioURL     string    `json:"audioUrl" gorm:"size:512;not null;default:'';column:audio_url;comment:音频地址"`
	PreviewURL   string    `json:"previewUrl" gorm:"size:512;not null;default:'';column:preview_url;comment:预览地址"`
	Duration     int       `json:"duration" gorm:"type:int(11);not null;default:0;column:duration;comment:视频时长单位毫秒"`
	Size         int       `json:"size" gorm:"type:int(11);not null;default:0;column:size;comment:视频大小单位字节"`
	Width        int       `json:"width" gorm:"type:int(11);not null;default:0;column:width;comment:视频宽"`
	Height       int       `json:"height" gorm:"type:int(11);not null;default:0;column:height;comment:视频高"`
	Status       string    `json:"status" gorm:"size:50;not null;column:status;comment:状态"`
	Thumbnail    string    `json:"thumbnail" gorm:"size:512;default:'';column:thumbnail;comment:缩略url地址"`
	Tracks       string    `json:"tracks" gorm:"type:mediumtext;column:tracks;comment:制作视频的基础数据"`
	IsDelete     int8      `json:"isDelete" gorm:"not null;default:0;index;column:is_delete;comment:删除标识符"`
	CreateTime   time.Time `json:"createTime" gorm:"not null;autoCreateTime;column:create_time;comment:创建时间"`
	UpdateTime   time.Time `json:"updateTime" gorm:"not null;autoUpdateTime;column:update_time;comment:更新时间"`
}

// TableName 自定义表名
func (AIVideo) TableName() string {
	return "slide_video"
}
