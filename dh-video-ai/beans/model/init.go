package model

import (
	"acg-ai-go-common/gomysql"
	"acg-ai-go-common/logger"
)

// InitMysqlDBTable 自动初始化数据库表结构（自动建表｜自动更新表结构）
func InitMysqlDBTable() error {
	if err := gomysql.DB.AutoMigrate(&AIVideoStoryboard{}); err != nil {
		logger.Log.<PERSON>("init mysql AIVideoStoryboard table fail, err:%v", err)
		return err
	}
	logger.Log.Info("init mysql AIVideoStoryboard table success")
	if err := gomysql.DB.AutoMigrate(&StoryboardPicture{}); err != nil {
		logger.Log.<PERSON>rf("init mysql StoryboardPicture table fail, err:%v", err)
		return err
	}
	logger.Log.Info("init mysql StoryboardPicture table success")
	if err := gomysql.DB.AutoMigrate(&SysPrompt{}); err != nil {
		logger.Log.<PERSON>rf("init mysql SysPrompt table fail, err:%v", err)
		return err
	}
	logger.Log.Info("init mysql SysPrompt table success")
	return nil
}
