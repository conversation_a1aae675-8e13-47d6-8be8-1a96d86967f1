package rds_v9

import (
	"acg-ai-go-common/global"
	"acg-ai-go-common/gomysql"
	"acg-ai-go-common/goredis"
	"sync"
	"testing"
	"time"
)

func init() {
	global.ServerSetting = &global.ServerConfig{
		RedisSetting: &global.RedisSetting{
			Addr:     "127.0.0.1:6379",
			Username: "",
			Password: "",
		},
		MysqlSetting: &global.MysqlSetting{
			HostPortSetting: global.HostPortSetting{
				Host: "127.0.0.1",
				Port: 3306,
			},
			UserNamePwdSetting: global.UserNamePwdSetting{
				Username: "root",
				Password: "123456",
			},
			Database:     "meta_human_editor_saas",
			MaxIdleConns: 10,
			MaxOpenConns: 100,
		},
	}
	goredis.InitRedisV2()
	gomysql.InitDB(global.ServerSetting.MysqlSetting)
}

func TestDefaultSpanLock(t *testing.T) {
	lockKey := "localTestDefaultSpanLock"
	coroutineNum := 3
	wg := sync.WaitGroup{}
	wg.Add(coroutineNum)
	for i := 0; i < coroutineNum; i++ {
		go func(i int) {
			defer wg.Done()
			spanLock := DefaultSpinLock(nil, goredis.GetClientV2(), lockKey, "")
			if err := spanLock.Exec(mockFunc); err != nil {
				t.Logf("i:%v Failed, err:%v", i, err)
				return
			}
			t.Logf("i:%v Pass", i)
		}(i)
	}
	wg.Wait()
}

func TestNewSpanLock(t *testing.T) {
	lockKey := "localTestNewSpanLock"
	waitTime := 1 * time.Second
	coroutineNum := 3
	wg := sync.WaitGroup{}
	wg.Add(coroutineNum)
	for i := 0; i < coroutineNum; i++ {
		go func(i int) {
			defer wg.Done()
			spanLock := NewSpinLock(nil, goredis.GetClientV2(), lockKey, "",
				11*time.Second, 20, waitTime, 0)
			if err := spanLock.Exec(mockFunc); err != nil {
				t.Logf("i:%v Failed, err:%v", i, err)
				return
			}
			t.Logf("i:%v Pass", i)
		}(i)
	}
	wg.Wait()
}

func mockFunc() error {
	time.Sleep(15 * time.Second)
	return nil
}
