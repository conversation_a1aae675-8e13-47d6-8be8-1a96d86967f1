package rds_v9

import (
	"acg-ai-go-common/logger"
	"context"
	"dh-video-ai/beans/utils/ginctx"
	"fmt"
	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/redis/go-redis/v9"
	"time"
)

// SpinLock 自旋锁，支持启动WatchDog模式为锁自动续期。
// client：redis 连接；
// key：锁的名称；
// value：锁下额外存储数据；
// expire：锁的过期时间,expire<=0时expire=10s；
// waitTime：自旋等待时间,waitTime<=0时不等待；
// maxRetry：获取锁最大重试次数, maxRetry<=0时执行一次，maxRetry>0时执行maxRetry+1次；
// keepAlive：看门狗续期间隔时间, keepAlive<=0时续期间隔时间=expire/3；
// cancelFunc：释放/取消锁的通知函数；
// ginCtx：gin上下文，用于日志打印时自动携带请求头的requestId、traceId等，传nil自动初始化有requestId、traceId请求头的ginCtx。
type SpinLock struct {
	client     *redis.Client      // redis 连接
	key        string             // 锁的名称
	value      string             // 锁下额外存储数据
	expire     time.Duration      // 锁的过期时间,expire<=0时expire=10s
	waitTime   time.Duration      // 自旋等待时间,waitTime<=0时不等待
	maxRetry   int                // 获取锁最大重试次数, maxRetry<=0时执行一次，maxRetry>0时执行maxRetry+1次
	keepAlive  time.Duration      // 看门狗续期间隔时间, keepAlive<=0时续期间隔时间=expire/3
	cancelFunc context.CancelFunc // 释放/取消锁的通知函数
	ginCtx     *gin.Context       // gin上下文，用于日志打印时自动携带请求头的requestId、traceId等
}

func DefaultSpinLock(ginCtx *gin.Context, client *redis.Client, key string, value string) *SpinLock {
	return NewSpinLock(ginCtx, client, key, value, 0, 0, 0, 0)
}

func NewWaitSpinLock(ginCtx *gin.Context, client *redis.Client, key string, value string,
	maxRetry int, waitTime time.Duration) *SpinLock {
	return NewSpinLock(ginCtx, client, key, value, 0, maxRetry, waitTime, 0)
}

func NewSpinLock(ginCtx *gin.Context, client *redis.Client, key string, value string,
	expire time.Duration, maxRetry int, waitTime, keepAlive time.Duration) *SpinLock {
	if ginCtx == nil {
		requestId := uuid.NewString()
		ginCtx = ginctx.Init(requestId, "trace-"+requestId)
	}
	if expire <= 0 {
		expire = 10 * time.Second
	}
	if maxRetry < 0 {
		maxRetry = 0
	}
	if waitTime < 0 {
		waitTime = 0
	}
	if keepAlive <= 0 {
		keepAlive = expire / 3
	}
	return &SpinLock{
		client:    client,
		key:       key,
		value:     value,
		expire:    expire,
		waitTime:  waitTime,
		maxRetry:  maxRetry,
		keepAlive: keepAlive,
		ginCtx:    ginCtx,
	}
}

// TryLock 尝试获取锁，看门狗模式
func (l *SpinLock) TryLock() (bool, error) {
	for i := -1; i < l.maxRetry; i++ {
		ok, err := l.client.SetNX(l.ginCtx, l.key, l.value, l.expire).Result()
		if err != nil {
			return false, err
		}
		if ok {
			// 启动自动续期协程
			l.startRenewal()
			return true, nil
		}
		time.Sleep(l.waitTime)
	}
	return false, nil
}

func (l *SpinLock) startRenewal() {
	renewCtx, cancel := context.WithCancel(context.Background())
	l.cancelFunc = cancel
	renewalNum := 0
	go func() {
		ticker := time.NewTicker(l.keepAlive)
		defer ticker.Stop()
		for {
			select {
			case <-renewCtx.Done():
				logger.CtxLog(l.ginCtx).Infof("renewal done, lockKey:%v, expire:%v, keepAlive:%v, renewalNum:%v",
					l.key, l.expire, l.keepAlive, renewalNum)
				return
			case <-ticker.C:
				renewalNum++
				lua := `if redis.call("get", KEYS[1]) == ARGV[1] then
							return redis.call("pexpire", KEYS[1], ARGV[2])
						else
							return 0
						end`
				_, err := l.client.Eval(l.ginCtx, lua, []string{l.key}, l.value, int(l.expire.Milliseconds())).Result()
				if err != nil {
					logger.CtxLog(l.ginCtx).Errorf("renewal failed, lockKey:%v, expire:%v, keepAlive:%v, renewalNum:%v, err:%v",
						l.key, l.expire, l.keepAlive, renewalNum, err)
					continue
				}
				logger.CtxLog(l.ginCtx).Debugf("renewal success, lockKey:%v, expire:%v, keepAlive:%v, renewalNum:%v",
					l.key, l.expire, l.keepAlive, renewalNum)
			}
		}
	}()
}

func (l *SpinLock) Unlock() error {
	if l.cancelFunc != nil {
		l.cancelFunc()
	}

	lua := `if redis.call("get", KEYS[1]) == ARGV[1] then
				return redis.call("del", KEYS[1])
			else
				return 0
			end`
	return l.client.Eval(l.ginCtx, lua, []string{l.key}, l.value).Err()
}

// Exec 自动加redis锁执行
func (l *SpinLock) Exec(fn func()) error {
	acquired, err := l.TryLock()
	if err != nil {
		return err
	}
	if !acquired {
		return fmt.Errorf("tryLock failed, lockKey:%v", l.key)
	}
	defer func(l *SpinLock) {
		if err := l.Unlock(); err != nil {
			logger.CtxLog(l.ginCtx).Errorf("unlock failed, lockKey:%v, err:%v", l.key, err)
		}
	}(l)
	fn()
	return nil
}
