package httputil

import (
	"acg-ai-go-common/logger"
	"bytes"
	"fmt"
	"github.com/gin-gonic/gin"
	"io"
	"io/ioutil"
	"net/http"
	"time"
)

// RetryHTTPClient 用于配置请求重试的选项
type RetryHTTPClient struct {
	client     *http.Client  // 用于发起 HTTP 请求的客户端
	maxRetries int           // 最大重试次数，MaxRetries<=0 最大请求1次 最大请求 MaxRetries+1 次
	retryDelay time.Duration // 每次重试间隔
	ginCtx     *gin.Context  // gin.Context 用于获取请求上下文的requestId和traceId
}

func DefaultRetryHTTPClient(maxRetries int) *RetryHTTPClient {
	return &RetryHTTPClient{
		client: &http.Client{
			Timeout: 30 * time.Second,
		},
		maxRetries: maxRetries,
		retryDelay: 1 * time.Second,
	}
}

func NewRetryHTTPClient(ctx *gin.Context, maxRetries int, retryDelay time.Duration, timeout time.Duration) *RetryHTTPClient {
	return &RetryHTTPClient{
		client: &http.Client{
			Timeout: timeout,
		},
		maxRetries: maxRetries,
		retryDelay: retryDelay,
		ginCtx:     ctx,
	}
}

// Do 执行带重试机制的 HTTP 请求
func (rhc *RetryHTTPClient) Do(method, url string, headers map[string]string, body []byte) ([]byte, error) {
	if rhc.maxRetries < 0 {
		rhc.maxRetries = 0
	}
	if rhc.retryDelay == 0 {
		rhc.retryDelay = time.Second
	}
	client := rhc.client
	if client == nil {
		client = http.DefaultClient
	}

	var lastErr error
	for attempt := -1; attempt < rhc.maxRetries; attempt++ {
		req, err := http.NewRequest(method, url, bytes.NewReader(body))
		if err != nil {
			return nil, err
		}
		for k, v := range headers {
			req.Header.Set(k, v)
		}

		resp, err := client.Do(req)
		if err == nil {
			return ioutil.ReadAll(resp.Body)
		}
		// 关闭 resp.Body 避免泄漏
		if resp != nil {
			io.Copy(io.Discard, resp.Body)
			resp.Body.Close()
		}

		lastErr = err
		logger.CtxLog(rhc.ginCtx).Errorf("attempt:[%d] fail: %v", attempt+1, err)
		time.Sleep(rhc.retryDelay)
	}
	return nil, fmt.Errorf("request failed after retries: %v", lastErr)
}
