package ginctx

import (
	"acg-ai-go-common/global"
	"github.com/gin-gonic/gin"
	"net/http"
)

func Init(requestId, traceId string) *gin.Context {
	ginCtx := &gin.Context{
		Request: &http.Request{
			Header: make(http.Header),
		},
	}
	ginCtx.Request.Header.Set(global.HeaderRequestID, requestId)
	ginCtx.Request.Header.Set(global.HeaderTraceID, traceId)
	return ginCtx
}

func GetSaasTokenHeader(c *gin.Context) map[string]string {
	return map[string]string{
		"<PERSON>ie":               c<PERSON>("<PERSON><PERSON>"),
		"Host":                 <PERSON><PERSON>("Host"),
		"Origin":               c.<PERSON>("Origin"),
		"Referer":              c.<PERSON>("Referer"),
		"weixin-session":       c.<PERSON>Header("weixin-session"),
		"Token":                c.<PERSON>("Token"),
		"Language":             <PERSON><PERSON>("Language"),
		global.HeaderRequestID: <PERSON><PERSON>(global.HeaderRequestID),
		global.HeaderTraceID:   <PERSON><PERSON>(global.HeaderTraceID),
	}
}

func GetSaasLanguageHeader(c *gin.Context) string {
	value := c.GetHeader("Language")
	if value == "" {
		return "zh"
	}
	return value
}
