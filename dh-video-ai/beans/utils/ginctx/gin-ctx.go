package ginctx

import (
	"acg-ai-go-common/global"
	"github.com/gin-gonic/gin"
	"net/http"
)

func Init(requestId, traceId string) *gin.Context {
	ginCtx := &gin.Context{
		Request: &http.Request{
			Header: make(http.Header),
		},
	}
	ginCtx.Request.Header.Set(global.HeaderRequestID, requestId)
	ginCtx.Request.Header.Set(global.HeaderTraceID, traceId)
	return ginCtx
}

func GetSaasTokenHeader(c *gin.Context) map[string]string {
	return map[string]string{
		"<PERSON>ie":               c<PERSON>("<PERSON><PERSON>"),
		"Host":                 <PERSON><PERSON>("Host"),
		"Origin":               <PERSON><PERSON>("Origin"),
		"Referer":              <PERSON>.<PERSON>("Referer"),
		"weixin-session":       c.<PERSON><PERSON><PERSON>("weixin-session"),
		"Token":                c<PERSON>("Token"),
		global.HeaderRequestID: c<PERSON>(global.HeaderRequestID),
		global.HeaderTraceID:   <PERSON><PERSON>(global.HeaderTraceID),
	}
}
