package config

var (
	LocalConfig = &localConfig{}
)

type localConfig struct {
	DhUserSetting     *dhUserSetting     `toml:"dh-user-setting"`
	LargeModelSetting *largeModelSetting `toml:"large-model-setting"`
	VideoHomeSetting  *videoHomeSetting  `toml:"video-home-setting"`
	StarLightSetting  *starLightSetting  `toml:"star-light-setting"`
}

type dhUserSetting struct {
	BaseUrl string `toml:"baseUrl"`
}

type largeModelSetting struct {
	BaseUrl string `toml:"baseUrl"`
}

type videoHomeSetting struct {
	BaseUrl string `toml:"baseUrl"`
}

type starLightSetting struct {
	BaseUrl string `toml:"baseUrl"`
}
