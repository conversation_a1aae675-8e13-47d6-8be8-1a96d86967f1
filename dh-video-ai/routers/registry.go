package routers

import (
	"acg-ai-go-common/global"
	"acg-ai-go-common/logger"
	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
)

var (
	GinRouter *gin.Engine
)

// InitRouter 初始化gin routers
func InitRouter() {
	switch global.ServerSetting.RunEnv {
	case global.DevEnv:
		gin.SetMode(gin.DebugMode)
		GinRouter = gin.New()
	case global.TestEnv:
		gin.SetMode(gin.DebugMode)
		GinRouter = gin.New()
	case global.PreReleaseEnv:
		gin.SetMode(gin.TestMode)
		GinRouter = gin.New()
	case global.ProdEnv:
		gin.SetMode(gin.ReleaseMode)
		GinRouter = gin.New()
	default:
		gin.SetMode(gin.ReleaseMode)
		GinRouter = gin.New()
	}
	// 统一gin的日志到统一的logger里面
	GinRouter.Use(
		logger.GinLog(
			logger.SetOpenReportEs(true),
			logger.SetSyncReportEs(false),
			logger.SetLogLevel(logrus.InfoLevel),
		),
	)
	// 健康检查Router
	HealthRouter(GinRouter)
	// AI视频创作Router
	AIVideoRouter(GinRouter)
	// todo 新加的Router需要在这里配置加载
}
