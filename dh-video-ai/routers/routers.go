package routers

import (
	commProto "acg-ai-go-common/beans/proto"
	"acg-ai-go-common/logger"
	dhUser "dh-video-ai/handler/dh-user"
	"dh-video-ai/handler/draft"
	"dh-video-ai/handler/storyboard"
	"dh-video-ai/handler/sys_prompt"
	"github.com/gin-gonic/gin"
	"net/http"
)

// AIVideoRouter AI视频创作路由
func AIVideoRouter(e *gin.Engine) {
	{
		consoleGroup := e.Group("/api/digitalhuman/ai-video/v1", dhUser.DhUserCheck, corsMiddleware())
		// 预设prompt
		consoleGroup.GET("/system/prompt", sys_prompt.GetPage)

		// 草稿创建
		consoleGroup.POST("/draft", draft.Create)
		// 草稿修改
		consoleGroup.PUT("/draft", draft.Update)
		// 草稿查询
		consoleGroup.GET("/draft/:draftId", draft.Get)
		// 草稿删除
		consoleGroup.DELETE("/draft/:draftId", draft.Delete)

		// 分镜历史记录
		consoleGroup.GET("/storyboard/history", storyboard.GetHistory)
		// 创建或修改分镜记录
		consoleGroup.POST("/storyboard", storyboard.Save)
		// 分镜大纲生成
		consoleGroup.POST("/storyboard/outline", storyboard.GenerateOutline)
		// 分镜脚本生成
		consoleGroup.POST("/storyboard/script", storyboard.GenerateScript)
		// 分镜画面生成
		consoleGroup.POST("/storyboard/picture", storyboard.GeneratePicture)
	}

	{
		// 内部接口
		innerGroup := e.Group("/api/digitalhuman/ai-video/inner/v1")
		innerGroup.POST("/draft/copy", draft.Copy)

		managerGroup := e.Group("/api/digitalhuman/ai-video/inner/v1", checkUserMiddleware)
		// 拷贝草稿
		// TODO 预设prompt获取
		managerGroup.POST("/system/prompt/list", sys_prompt.Search)
		// TODO 预设prompt新增
		managerGroup.POST("/system/prompt", sys_prompt.Create)
		// TODO 预设prompt修改
		managerGroup.PUT("/system/prompt", sys_prompt.Update)
		// TODO 预设prompt删除
		managerGroup.DELETE("/system/prompt", sys_prompt.Delete)
	}
}

func checkUserMiddleware(c *gin.Context) {
	managerUserName := c.GetHeader("x-manager-user-name")
	logger.CtxLog(c).Infof("checkInstanceMiddleware GetHeader x-manager-user-name:%v", managerUserName)
	// 查询instanceName
	if managerUserName == "" {
		c.AbortWithStatusJSON(http.StatusOK, commProto.NewCommRsp(400001, "权限不足"))
		return
	}
	c.Next()
}

// 跨域中间件
func corsMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Writer.Header().Set("Access-Control-Allow-Origin", "*")
		c.Writer.Header().Set("Access-Control-Allow-Methods", "POST, GET, OPTIONS, PUT, DELETE")
		c.Writer.Header().Set("Access-Control-Max-Age", "86400")
		c.Writer.Header().Set("Access-Control-Allow-Headers",
			"Content-Type, Content-Length, Accept-Encoding, X-CSRF-Token, Authorization")
		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(http.StatusOK)
			return
		}
		c.Next()
	}
}
