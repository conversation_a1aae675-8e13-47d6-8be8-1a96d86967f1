server:
  port: 8480
spring:
  application:
    name: digital-human-plat
  profiles:
    active: dev
  jpa:
    database: MYSQL
    show-sql: false
    open-in-view: false
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    #    url: ****************************************************************************************************************************************************************************************************************************
    #    username: dhsandbox
    #    password: '!Digitalhuman@baidu123'
    url: ***************************************************************************************************************************************************************************************************************
    username: root
    password: 123456
    maxActive: 100
    validationInterval: 5000
    timeBetweenEvictionRunsMillis: 2000
    maxWait: 15000

management:
  server:
    port: 8482
  endpoint:
    prometheus:
      enabled: true
    metrics:
      enabled: true
  metrics:
    tags:
      application: digital-human-plat
    export:
      prometheus:
        enabled: true
    web:
      server:
        request:
          autotime:
            enabled: true
#  endpoints:
#    web:
#      exposure:
#        include: '*'
jwt:
  expire:
    time: 30 * 60 * 1000
    refresh:
      threshold: 30 * 60 * 1000

digitalhuman:
  starlight:
    client:
      config:
        baseUrl: "http://localhost:80"
  resource-pool:
    client:
      resourcePoolUrl: http://digital-human-resource-pool:8080
  datasource:
    config:
      doubleWrite: false
  auth:
    enabled: true
    paths: /api/digitalhuman/plat/v2/**,/api/digitalhuman/v2/**,/api/external/digitalhuman/**
    exclude-paths: /api/digitalhuman/v1/**,/api/internal/digitalhuman/**
    url:
      base: http://localhost:8010
      login: /dep/login
  plat:
    upload:
      fileSuffix: jpg, gif, png, ico, bmp, jpeg
    opt-log:
      csv-size: 100000
      query-batch: 2000
      pool-core-size: 12
  videopipeline.baseUrl: http://localhost:8888
  storage:
    type: bos
    bos:
      endpoint: https://digital-human-pipeline-output.cdn.bcebos.com
      accessKeyId: 931b804661f2462a9ae36eea84357241
      secretAccessKey: ********************************
      bucket: digital-human-pipeline-output
      retryTimes: 1
      retrySleepMillis: 100
      urlExpireSeconds: -1
  project:
    html5Url: http://persona.baidu.com/cloud/bizlayer
  user:
    config:
      baseUrl: http://localhost:8083
  prometheus:
    config:
      url: http://localhost:9090
  access:
    app:
      generate-config:
        env-prefix: s
        retry-times: 3
        retry-sleep-millis: 100
  character:
    figure-cut:
      # 人像类型-竖屏-相机位
      vertical-1:
        cutXPercent: "5"
        cutYPercent: "12"
        cutWidthPercent: "83"
        cutHeightPercent: "88"
        widthRatio: "76"
        positionCenterXPercent: "0"
        positionBottomYPercent: "0"
      vertical-2:
        cutXPercent: "0"
        cutYPercent: "0"
        cutWidthPercent: "100"
        cutHeightPercent: "70"
        widthRatio: "100"
        positionCenterXPercent: "0"
        positionBottomYPercent: "0"
  grpc:
    serverConfig:
      port: 8481
      terminateAwaitSeconds: 3