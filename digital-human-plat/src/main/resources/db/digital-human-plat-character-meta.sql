USE digital_human_plat;

alter table character_meta add visible_for_sce TINYINT(4) NOT NULL DEFAULT '' COMMENT 'character visible for sce' after support_rtc_datachannel;
alter table character_meta add camera_list VARCHAR(512) NOT NULL DEFAULT '' COMMENT 'camera list' after style;
alter table character_meta add hair_style_list VARCHAR(512) NOT NULL DEFAULT '' COMMENT 'hair style list' after camera_list;
alter table character_meta add clothing_style_list VARCHAR(512) NOT NULL DEFAULT '' COMMENT 'clothing style list' after hair_style_list;
alter table character_meta add badge_style_list VARCHAR(256) NOT NULL DEFAULT '' COMMENT 'badge style list' after clothing_style_list;
alter table character_meta add shoe_style_list VARCHAR(256) NOT NULL DEFAULT '' COMMENT 'shoe style list' after badge_style_list;
alter table character_meta modify animoji_list VARCHAR(1024) NOT NULL DEFAULT '' COMMENT 'animoji list' after badge_style;
alter table character_meta add emotion_list VARCHAR(1024) NOT NULL DEFAULT '' COMMENT 'emotion list' after animoji_list;

alter table richtext_config add project_name VARCHAR(100) NOT NULL DEFAULT '' COMMENT 'project name' after project_id;
alter table richtext_config add user_id VARCHAR(40) NOT NULL DEFAULT '' COMMENT 'user id' after project_name;

alter table project add camera varchar(256) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT 'camera params' after character_image;