alter table character_meta add api_version TINYINT(4) NOT NULL DEFAULT 1 COMMENT 'api version';
alter table character_meta add config_schema TEXT COMMENT 'character config schema';
alter table character_meta add config_mode TINYINT(4) NOT NULL DEFAULT 0 COMMENT 'character config mode for fe';

DROP TABLE IF EXISTS `character_config`;
CREATE TABLE `character_config` (
    `config_id` VARCHAR(32) NOT NULL,
    `user_id` VARCHAR(64) NOT NULL,
    `name` VARCHAR(128) NOT NULL,
    `type` VARCHAR(128) NOT NULL,
    `character_name` VARCHAR(128) NOT NULL,
    `description` VARCHAR(512) DEFAULT '',
    `config` TEXT NOT NULL,
    `config_mode` TINYINT(4) NOT NULL DEFAULT 0,
    `thumbnail` VARCHAR(512),
    `label` VARCHAR(32),
    `create_time` TIMESTAMP NOT NULL,
    `update_time` TIMESTAMP NOT NULL,
    <PERSON><PERSON>AR<PERSON> KEY (`config_id`),
    UNIQUE KEY `uk_user_id_name` (`user_id`, `name`),
    INDEX `idx_user_id_type`(`user_id`,`type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT 'character config';

alter table character_config add index `idx_user_id_type`(`user_id`,`type`);