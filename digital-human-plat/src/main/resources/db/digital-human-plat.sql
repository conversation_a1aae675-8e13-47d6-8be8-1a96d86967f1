USE digital_human_plat;

DROP TABLE IF EXISTS `access_user`;
CREATE TABLE `access_user` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT 'id',
    `user_id` VARCHAR(100) NOT NULL DEFAULT '' COMMENT 'user id',
    `name` VARCHAR(100) NOT NULL DEFAULT '' COMMENT 'name',
    `password` VARCHAR(100) NOT NULL DEFAULT '' COMMENT 'password',
    `description` VARCHAR(100) NOT NULL DEFAULT '' COMMENT 'description',
    `tags` VARCHAR(1024) NOT NULL DEFAULT '' COMMENT 'tags',
    `iam_user_id` VARCHAR(100) NOT NULL DEFAULT '' COMMENT 'iam user id',
    `create_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'create time',
    `update_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'last update time',
    `version` INT(11) NOT NULL COMMENT 'for optimistic locking',
    <PERSON><PERSON><PERSON><PERSON> KEY `pk_access_user` (`id`),
    UNIQUE KEY `uk_user_id` (`user_id`),
    UNIQUE KEY `uk_name` (`name`),
    INDEX `idx_iam_user_id` (`iam_user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT 'access user table';

DROP TABLE IF EXISTS `access_app`;
CREATE TABLE `access_app` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT 'id',
    `app_id` VARCHAR(100) NOT NULL DEFAULT '' COMMENT 'app id',
    `app_key` VARCHAR(100) NOT NULL DEFAULT '' COMMENT 'app key',
    `user_id` VARCHAR(100) NOT NULL DEFAULT '' COMMENT 'user id',
    `name` VARCHAR(100) NOT NULL DEFAULT '' COMMENT 'name',
    `description` VARCHAR(100) NOT NULL DEFAULT '' COMMENT 'description',
    `project_id` VARCHAR(100) NOT NULL DEFAULT '' COMMENT 'project id',
    `project_name` VARCHAR(100) NOT NULL DEFAULT '' COMMENT 'project name',
    `tags` VARCHAR(1024) NOT NULL DEFAULT '' COMMENT 'tags',
    `resource_quota` VARCHAR(100) NOT NULL DEFAULT '' COMMENT 'resource quota',
    `max_idle_in_second` INT NOT NULL DEFAULT 30 COMMENT 'max idle in second',
    `character_image` VARCHAR(100) NOT NULL DEFAULT '' COMMENT 'character image',
    `enabled` TINYINT NOT NULL DEFAULT 1 COMMENT 'enabled',
    `create_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'create time',
    `update_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'last update time',
    `version` INT(11) NOT NULL COMMENT 'for optimistic locking',
    PRIMARY KEY `pk_access_app` (`id`),
    UNIQUE KEY `uk_app_id` (`app_id`),
    INDEX `idx_user_id_name` (`user_id`, `name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT 'access app table';

DROP TABLE IF EXISTS `room`;
CREATE TABLE `room` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT 'id',
    `room_id` VARCHAR(100) NOT NULL COMMENT 'room id',
    `user_id` VARCHAR(100) NOT NULL DEFAULT '' COMMENT 'user id',
    `app_id` VARCHAR(100) NOT NULL DEFAULT '' COMMENT 'app id',
    `room_name` VARCHAR(100) NOT NULL DEFAULT '' COMMENT 'room name',
    `status` VARCHAR(100) NOT NULL COMMENT 'status',
    `create_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'create time',
    `update_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'last update time',
    `version` INT(11) NOT NULL COMMENT 'for optimistic locking',
    PRIMARY KEY `pk_room` (`id`),
    UNIQUE KEY `uk_room_id` (`room_id`),
    INDEX `idx_user_id_room_name` (`user_id`, `room_name`),
    INDEX `idx_app_id_room_name` (`app_id`, `room_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT 'room table';

DROP TABLE IF EXISTS `room_session`;
CREATE TABLE `room_session` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT 'id',
    `room_session_id` VARCHAR(100) NOT NULL DEFAULT '' COMMENT 'room session id',
    `session_id` VARCHAR(100) NOT NULL DEFAULT '' COMMENT 'session id',
    `room_id` VARCHAR(100) NOT NULL DEFAULT '' COMMENT 'room id',
    `app_id` VARCHAR(100) NOT NULL DEFAULT '' COMMENT 'app id',
    `app_token` VARCHAR(100) NOT NULL DEFAULT '' COMMENT 'app token',
    `project_id` VARCHAR(100) NOT NULL DEFAULT '' COMMENT 'project id',
    `status` VARCHAR(100) NOT NULL DEFAULT '' COMMENT 'status',
    `duration` BIGINT NOT NULL DEFAULT 0 COMMENT 'duration',
    `time_stamp` BIGINT NOT NULL DEFAULT 0 COMMENT 'time stamp',
    `create_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'create time',
    `update_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'last update time',
    `version` INT(11) NOT NULL COMMENT 'for optimistic locking',
    PRIMARY KEY `pk_room_session` (`id`),
    UNIQUE KEY `uk_room_session_id` (`room_session_id`),
    INDEX `idx_session_id` (`session_id`),
    INDEX `idx_room_id` (`room_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT 'room session table';

DROP TABLE IF EXISTS `dialog`;
CREATE TABLE `dialog` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT 'id',
    `dialog_id` VARCHAR(100) NOT NULL DEFAULT '' COMMENT 'dialog id',
    `app_id` VARCHAR(100) NOT NULL DEFAULT '' COMMENT 'app id',
    `session_id` VARCHAR(100) NOT NULL DEFAULT '' COMMENT 'session id',
    `room_id` VARCHAR(100) NOT NULL DEFAULT '' COMMENT 'room id',
    `speaker` VARCHAR(100) NOT NULL DEFAULT '' COMMENT 'speaker',
    `type` VARCHAR(100) NOT NULL DEFAULT '' COMMENT 'type',
    `content` VARCHAR(7168) NOT NULL DEFAULT '' COMMENT 'content',
    `time_stamp` BIGINT NOT NULL DEFAULT 0 COMMENT 'time stamp',
    `create_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'create time',
    `update_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'last update time',
    `version` INT(11) NOT NULL COMMENT 'for optimistic locking',
    PRIMARY KEY `pk_dialog` (`id`),
    UNIQUE KEY `uk_dialog_id` (`dialog_id`),
    INDEX `idx_app_timestamp` (`app_id`, `time_stamp`),
    INDEX `idx_session_timestamp` (`session_id`, `time_stamp`),
    INDEX `idx_room_timestamp` (`room_id`, `time_stamp`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT 'dialog table';

DROP TABLE IF EXISTS `background`;
CREATE TABLE `background` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT 'id',
    `background_id` VARCHAR(100) NOT NULL DEFAULT '' COMMENT 'background id',
    `user_id` VARCHAR(100) NOT NULL DEFAULT '' COMMENT 'user id',
    `name` VARCHAR(100) NOT NULL DEFAULT '' COMMENT 'name',
    `description` VARCHAR(100) NOT NULL DEFAULT '' COMMENT 'description',
    `url` VARCHAR(1024) NOT NULL DEFAULT '' COMMENT 'url',
    `create_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'create time',
    `update_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'last update time',
    `version` INT(11) NOT NULL COMMENT 'for optimistic locking',
    PRIMARY KEY `pk_background` (`id`),
    UNIQUE KEY `uk_background_id` (`background_id`),
    INDEX `idx_user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT 'background table';

DROP TABLE IF EXISTS `character_meta`;
CREATE TABLE `character_meta` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT 'id',
    `character_id` VARCHAR(100) NOT NULL DEFAULT '' COMMENT 'character id',
    `type` VARCHAR(100) NOT NULL DEFAULT '' COMMENT 'type',
    `name` VARCHAR(100) NOT NULL DEFAULT '' COMMENT 'name',
    `description` VARCHAR(100) NOT NULL DEFAULT '' COMMENT 'description',
    `style` VARCHAR(100) NOT NULL DEFAULT '' COMMENT 'style',
    `camera_list` VARCHAR(512) NOT NULL DEFAULT '' COMMENT 'camera list',
    `hair_style_list` VARCHAR(512) NOT NULL DEFAULT '' COMMENT 'hair style list',
    `clothing_style_list` VARCHAR(512) NOT NULL DEFAULT '' COMMENT 'clothing style list',
    `badge_style_list` VARCHAR(256) NOT NULL DEFAULT '' COMMENT 'badge style list',
    `shoe_style_list` VARCHAR(256) NOT NULL DEFAULT '' COMMENT 'shoe style list',
    `hair_style` VARCHAR(512) NOT NULL DEFAULT '' COMMENT 'hair style',
    `clothing_style` VARCHAR(512) NOT NULL DEFAULT '' COMMENT 'clothing style',
    `badge_style` VARCHAR(512) NOT NULL DEFAULT '' COMMENT 'badge style',
    `animoji_list` VARCHAR(512) NOT NULL DEFAULT '' COMMENT 'animoji list',
    `emotion_list` VARCHAR(1024) NOT NULL DEFAULT '' COMMENT 'emotion list',
    `animojis` VARCHAR(512) NOT NULL DEFAULT '' COMMENT 'animojis',
    `front_image_url` VARCHAR(1024) NOT NULL DEFAULT '' COMMENT 'front image url',
    `mask_image_url` VARCHAR(1024) NOT NULL DEFAULT '' COMMENT 'mask image url',
    `background_image_url` VARCHAR(1024) NOT NULL DEFAULT '' COMMENT 'background image url',
    `thumbnail_image_url` VARCHAR(1024) NOT NULL DEFAULT '' COMMENT 'thumbnail image url',
    `create_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'create time',
    `update_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'last update time',
    `version` INT(11) NOT NULL COMMENT 'for optimistic locking',
    PRIMARY KEY `pk_character_meta` (`id`),
    UNIQUE KEY `uk_character_id` (`character_id`),
    UNIQUE KEY `uk_type` (`type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT 'character meta table';

DROP TABLE IF EXISTS `project`;
CREATE TABLE `project` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT 'id',
    `project_id` VARCHAR(100) NOT NULL DEFAULT '' COMMENT 'project id',
    `name` VARCHAR(100) NOT NULL DEFAULT '' COMMENT 'name',
    `description` VARCHAR(100) NOT NULL DEFAULT '' COMMENT 'description',
    `user_id` VARCHAR(100) NOT NULL DEFAULT '' COMMENT 'user id',
    `is_default` TINYINT NOT NULL DEFAULT 0 COMMENT 'is default',
    `project_version` VARCHAR(100) NOT NULL DEFAULT '1.0.0' COMMENT 'project version',
    `preset` VARCHAR(1024) NOT NULL DEFAULT '' COMMENT 'preset',
    `background_image_id` VARCHAR(100) NOT NULL DEFAULT '' COMMENT 'background image id',
    `background_image_url` VARCHAR(1024) NOT NULL DEFAULT '' COMMENT 'background image url',
    `bot_params` VARCHAR(256) NOT NULL DEFAULT '' COMMENT 'bot params',
    `tts_params` VARCHAR(256) NOT NULL DEFAULT '' COMMENT 'tts params',
    `character_image` VARCHAR(100) NOT NULL DEFAULT '' COMMENT 'character image',
    `camera` varchar(256) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT 'camera params',
    `resolution_params` VARCHAR(256) NOT NULL DEFAULT '' COMMENT 'resolution params',
    `figure_cut_params` VARCHAR(1024) NOT NULL DEFAULT '' COMMENT 'figure cut params',
    `character_params` VARCHAR(1024) NOT NULL DEFAULT '' COMMENT 'character params',
    `paint_chart_on_picture_params` VARCHAR(100) NOT NULL DEFAULT '' COMMENT 'paint chart on picture params',
    `paint_subtitle_on_picture_params` VARCHAR(256) NOT NULL DEFAULT '' COMMENT 'paint subtitle on picture params',
    `media_output` VARCHAR(256) NOT NULL DEFAULT '' COMMENT 'media output',
    `conversation_config_id` BIGINT NULL COMMENT 'conversation config id',
    `hot_words` VARCHAR(1024) NOT NULL DEFAULT '' COMMENT 'hot words',
    `user_inactive` VARCHAR(256) NOT NULL DEFAULT '' COMMENT 'user inactive',
    `prologue_params` VARCHAR(512) NOT NULL DEFAULT '' COMMENT 'prologue params',
    `hit_shield_reply` VARCHAR(1024) NOT NULL DEFAULT '' COMMENT 'hit shield reply',
    `create_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'create time',
    `update_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'last update time',
    `version` INT(11) NOT NULL COMMENT 'for optimistic locking',
    PRIMARY KEY `pk_project` (`id`),
    UNIQUE KEY `uk_user_id_name_project_version` (`user_id`, `name`, `project_version`),
    INDEX `idx_project_id` (`project_id`),
    INDEX `idx_user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT 'project table';

DROP TABLE IF EXISTS `richtext_config`;
CREATE TABLE `richtext_config` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT 'id',
    `config_id` VARCHAR(100) NOT NULL DEFAULT '' COMMENT 'config id',
    `project_id` VARCHAR(100) NOT NULL DEFAULT '' COMMENT 'config id',
    `project_name` VARCHAR(100) NOT NULL DEFAULT '' COMMENT 'project name',
    `user_id` VARCHAR(40) NOT NULL DEFAULT '' COMMENT 'user id',
    `content` VARCHAR(7168) NOT NULL DEFAULT '' COMMENT 'content',
    `create_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'create time',
    `update_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'last update time',
    `version` INT(11) NOT NULL COMMENT 'for optimistic locking',
    PRIMARY KEY `pk_richtext_config` (`id`),
    UNIQUE KEY `uk_config_id` (`config_id`),
    INDEX `idx_project_id` (`project_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT 'richtext config table';

DROP TABLE IF EXISTS `conversation_config`;
CREATE TABLE `conversation_config` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT 'id',
    `pullback_switch` TINYINT NOT NULL DEFAULT false COMMENT 'pullback switch',
    `pullback_trigger_round` INT NOT NULL DEFAULT 0 COMMENT 'pullback trigger round',
    PRIMARY KEY `pk_conversation_config` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT 'conversation config table';

DROP TABLE IF EXISTS `pullback_config`;
CREATE TABLE `pullback_config` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT 'id',
    `conversation_config_id` BIGINT NULL COMMENT 'conversation config id',
    `percentage` INT NOT NULL DEFAULT 0 COMMENT 'percentage',
    `pullback_mode` VARCHAR(100) NOT NULL DEFAULT '' COMMENT 'pullback mode',
    `target_skill` VARCHAR(100) NOT NULL DEFAULT '' COMMENT 'target skill',
    `enter_speeches` VARCHAR(1024) NOT NULL DEFAULT '' COMMENT 'enter speeches',
    `direct_guide_speeches` VARCHAR(1024) NOT NULL DEFAULT '' COMMENT 'direct guide speeches',
    `enter_guide_speeches` VARCHAR(1024) NOT NULL DEFAULT '' COMMENT 'enter guide speeches',
    `relate_skills` VARCHAR(1024) NOT NULL DEFAULT '' COMMENT 'related skills',
    `direct_guide_percentage` INT NOT NULL DEFAULT 0 COMMENT 'direct guide percentage',
    `enter_guide_percentage` INT NOT NULL DEFAULT 0 COMMENT 'enter guide percentage',
    PRIMARY KEY `pk_pullback_config` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT 'pullback config table';

ALTER TABLE `project` ADD COLUMN `character_thumbnail` VARCHAR(512)  NOT NULL DEFAULT '' COMMENT 'project character thumbnail';