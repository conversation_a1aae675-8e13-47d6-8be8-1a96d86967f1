USE digital_human_plat;

alter table access_app add project_version VARCHAR(40) NOT NULL DEFAULT '' COMMENT 'project version' after project_name;
alter table project add is_system TINYINT NOT NULL DEFAULT 0 COMMENT 'is system project' after is_default;
alter table project add project_version VARCHAR(40) NOT NULL DEFAULT '1.0.0' COMMENT 'project version' after is_system;
alter table project add conversation_config_id BIGINT NULL COMMENT 'conversation config id' after media_output;
alter table project add hot_words VARCHAR(1024) NOT NULL DEFAULT '' COMMENT 'hot words' after conversation_config_id;
alter table project add user_inactive VARCHAR(256) NOT NULL DEFAULT '' COMMENT 'user inactive' after hot_words;
alter table project add prologue_params VARCHAR(512) NOT NULL DEFAULT '' COMMENT 'prologue params' after user_inactive;
alter table project add hit_shield_reply VARCHAR(1024) NOT NULL DEFAULT '' COMMENT 'hit shield reply' after prologue_params;

alter database digital_human_plat character set=utf8mb4 collate=utf8mb4_bin;
alter table project convert to character set utf8mb4 collate utf8mb4_bin;

alter table project drop index uk_project_id;
alter table project add UNIQUE uk_user_id_name_project_version(`user_id`, `name`, `project_version`);
alter table project add index idx_project_id(`project_id`);

DROP TABLE IF EXISTS `conversation_config`;
CREATE TABLE `conversation_config` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT 'id',
    `pullback_switch` TINYINT NOT NULL DEFAULT false COMMENT 'pullback switch',
    `pullback_trigger_round` INT NOT NULL DEFAULT 0 COMMENT 'pullback trigger round',
    PRIMARY KEY `pk_conversation_config` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT 'conversation config table';

alter table conversation_config add pullback_config_list VARCHAR(2048) NOT NULL DEFAULT '' COMMENT 'pullback config list' after pullback_trigger_round;
