package com.baidu.acg.piat.digitalhuman.plat.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import javax.transaction.Transactional;

import com.baidu.acg.piat.digitalhuman.common.exception.DigitalHumanCommonException;
import com.baidu.acg.piat.digitalhuman.common.model.PageResponse;
import com.baidu.acg.piat.digitalhuman.common.model.PageResult;
import com.baidu.acg.piat.digitalhuman.common.room.Room;
import com.baidu.acg.piat.digitalhuman.common.room.RoomStatus;
import com.baidu.acg.piat.digitalhuman.plat.dao.RoomRepository;
import com.baidu.acg.piat.digitalhuman.plat.model.room.RoomModel;
import com.baidu.acg.piat.digitalhuman.plat.service.RoomService;

/**
 * The impl of {@link RoomService}.
 *
 * <AUTHOR> Mao (<EMAIL>)
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class RoomServiceImpl implements RoomService {

    private final RoomRepository repository;

    @Override
    public Room create(Room room) {
        validateRequest(room);
        validateRoomName(room.getUserId(), room.getRoomName());
        RoomModel roomModel = RoomModel.builder()
                .roomId(ObjectId.get().toHexString())
                .userId(room.getUserId())
                .roomName(room.getRoomName())
                .appId(room.getAppId())
                .status(room.getStatus() != null ? room.getStatus() : RoomStatus.ENABLED)
                .build();
        roomModel = repository.save(roomModel);
        return roomModel.toRoom();
    }

    @Override
    @Transactional
    public void delete(String roomId) {
        var one = get(roomId);
        repository.delete(one);
    }

    @Override
    public Room detail(String roomId) {
        var optional = repository.findByRoomId(roomId);
        return optional.map(RoomModel::toRoom).orElse(null);
    }

    @Override
    public Room getByUserIdAndRoomName(String userId, String name) {
        var optional = repository.findByUserIdAndRoomName(userId, name);
        return optional.map(RoomModel::toRoom).orElse(null);
    }

    @Override
    public Room getByAppIdAndRoomName(String appId, String roomName) {
        var optional = repository.findByAppIdAndRoomName(appId, roomName);
        return optional.map(RoomModel::toRoom).orElse(null);
    }

    @Override
    public Room update(String roomId, Room request) {
        var updated = get(roomId);
        if ((StringUtils.isNotEmpty(request.getRoomName())
                && !request.getRoomName().equals(updated.getRoomName()))) {
            validateRoomName(updated.getUserId(), request.getRoomName());
            updated.setRoomName(request.getRoomName());
        }
        if (StringUtils.isNotEmpty(request.getAppId())) {
            updated.setAppId(request.getAppId());
        }
        if (request.getStatus() != null) {
            updated.setStatus(request.getStatus());
        }
        var result = repository.save(updated);
        return result.toRoom();
    }

    @Override
    public PageResponse<Room> list(String userId, String appId, String roomName, int pageNo, int pageSize) {
        PageRequest pageRequest = PageRequest.of(pageNo - 1, pageSize);
        List<Room> roomList = new ArrayList<>();
        var totalCount = 0L;
        if (StringUtils.isEmpty(appId) && StringUtils.isEmpty(roomName)) {
            var roomPage = repository.findByUserIdOrderByCreateTimeDesc(userId, pageRequest);
            totalCount = roomPage.getTotalElements();
            roomList = roomPage.getContent().stream().map(RoomModel::toRoom).collect(Collectors.toList());
        } else if (StringUtils.isNotEmpty(appId) && StringUtils.isNotEmpty(roomName)) {
            var optional = repository.findByUserIdAndAppIdAndRoomName(userId, appId, roomName);
            if (optional.isPresent()) {
                totalCount = 1;
                roomList.add(optional.get().toRoom());
            }
        } else if (StringUtils.isNotEmpty(appId) && StringUtils.isEmpty(roomName)) {
            var roomPage = repository.findByUserIdAndAppIdOrderByCreateTimeDesc(
                    userId, appId, pageRequest);
            totalCount = roomPage.getTotalElements();
            roomList = roomPage.getContent().stream().map(RoomModel::toRoom).collect(Collectors.toList());
        } else {
            var optional = repository.findByUserIdAndRoomName(userId, roomName);
            if (optional.isPresent()) {
                totalCount = 1;
                roomList.add(optional.get().toRoom());
            }
        }
        return PageResponse.<Room>builder()
                .page(PageResult.<Room>builder()
                        .pageNo(pageNo)
                        .pageSize(pageSize)
                        .totalCount(totalCount)
                        .result(roomList)
                        .build())
                .build();
    }

    @Override
    public PageResponse<Room> listByAppId(String appId, int pageNo, int pageSize) {
        PageRequest pageRequest = PageRequest.of(pageNo - 1, pageSize);
        Page<RoomModel> roomPage = repository.findByAppIdOrderByCreateTimeDesc(appId, pageRequest);
        return PageResponse.<Room>builder()
                .page(PageResult.<Room>builder()
                        .pageNo(pageNo)
                        .pageSize(pageSize)
                        .totalCount(roomPage.getTotalElements())
                        .result(roomPage.getContent().stream().map(RoomModel::toRoom).collect(Collectors.toList()))
                        .build())
                .build();
    }

    private void validateRequest(Room room) {
        if (StringUtils.isBlank(room.getRoomName())) {
            throw new DigitalHumanCommonException("RoomName cannot be blank");
        }
        if (StringUtils.isBlank(room.getAppId())) {
            throw new DigitalHumanCommonException("AppId cannot be blank");
        }
        if (StringUtils.isBlank(room.getUserId())) {
            throw new DigitalHumanCommonException("UserId cannot be blank");
        }
    }

    private void validateRoomName(String userId, String roomName) {
        var result = repository.findByUserIdAndRoomName(userId, roomName);
        if (result.isPresent()) {
            throw new DigitalHumanCommonException("Room name already existed");
        }
    }

    private RoomModel get(String roomId) {
        var optional = repository.findByRoomId(roomId);
        if (optional.isEmpty()) {
            throw new DigitalHumanCommonException("Room does not exist");
        }
        return optional.get();
    }
}