package com.baidu.acg.piat.digitalhuman.plat.v2.controller;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestAttribute;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

import com.baidu.acg.piat.digitalhuman.common.entity.CharacterConfig;
import com.baidu.acg.piat.digitalhuman.common.entity.CharacterConfigRelatedModule;
import com.baidu.acg.piat.digitalhuman.common.model.PageResponse;
import com.baidu.acg.piat.digitalhuman.common.model.Response;
import com.baidu.acg.piat.digitalhuman.plat.model.common.BatchDeleteRequest;
import com.baidu.acg.piat.digitalhuman.plat.v2.bo.character.CharacterConfigBo;
import com.baidu.acg.piat.digitalhuman.plat.v2.service.CharacterConfigService;
import com.baidu.acg.piat.digitalhuman.plat.v2.service.ServiceException;

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping({"/api/external/digitalhuman/plat/v2/character-configs", "/api/digitalhuman/v2/character-configs"})
public class CharacterConfigController {

    private final CharacterConfigService characterConfigService;

    @PostMapping
    public Response<CharacterConfig> create(@RequestAttribute("accountId") String accountId,
                                            @RequestBody CharacterConfig config) {
        try {
            return Response.success(characterConfigService.create(accountId, config));
        } catch (Throwable e) {
            log.error("Failed to create character config, accountId={} config={}", accountId, config, e);
            return Response.fail(e instanceof ServiceException ? e.getMessage() : "internal server error");
        }
    }

    @PostMapping("/image")
    public Response<CharacterConfig> create(@RequestAttribute("accountId") String accountId,
                                            @RequestBody CharacterConfigBo configBo) {
        try {
            return Response.success(characterConfigService.create(accountId, configBo));
        } catch (Throwable e) {
            log.error("Failed to create character config, userId={} config={}", accountId, configBo, e);
            return Response.fail(e instanceof ServiceException ? e.getMessage() : "internal server error");
        }
    }

    @GetMapping("/{id}")
    public Response<CharacterConfig> retrieve(@RequestAttribute("accountId") String accountId,
                                              @PathVariable String id) {
        try {
            return Response.success(characterConfigService.retrieve(accountId, id));
        } catch (Throwable e) {
            log.error("Failed to retrieve character config, accountId={} configId={}", accountId, id, e);
            return Response.fail(e instanceof ServiceException ? e.getMessage() : "internal server error");
        }
    }

    @PutMapping("/{id}")
    public Response<CharacterConfig> update(@RequestAttribute("accountId") String accountId,
                                            @PathVariable String id,
                                            @RequestBody CharacterConfig config) {
        if (StringUtils.isBlank(config.getConfigId())) {
            config.setConfigId(id);
        } else if (!id.equals(config.getConfigId())) {
            return Response.fail("bad request");
        }

        try {
            return Response.success(characterConfigService.update(accountId, config));
        } catch (Throwable e) {
            log.error("Failed to update character config, accountId={} config={}", accountId, config, e);
            return Response.fail(e instanceof ServiceException ? e.getMessage() : "internal server error");
        }
    }

    @DeleteMapping("/{id}")
    public Response<CharacterConfig> delete(@RequestAttribute("accountId") String accountId,
                                            @PathVariable String id) {
        try {
            return Response.success(characterConfigService.delete(accountId, id));
        } catch (Throwable e) {
            log.error("Failed to delete character config, accountId={} configId={}", accountId, id, e);
            return Response.fail(e instanceof ServiceException ? e.getMessage() : "internal server error");
        }
    }

    /**
     * 分页查询人设；
     * 1.只能查询到用户有权限访问的人像类型
     * 用户在1.0可见的人像通过tags中的sceVisibleCharacters字段控制，json字符串格式，为空则不限制
     * 用户在2.0可见的人像通过tags中的visibleCharacters字段控制，逗号隔开，为 'all' 则不限制
     * 2.数据隔离级别：所有子账户可见
     */
    @GetMapping
    public PageResponse<CharacterConfig> list(@RequestAttribute("accountId") String accountId,
                                              @RequestAttribute("tags") Map<String, String> tags,
                                              @RequestParam int pageNo, @RequestParam int pageSize,
                                              @RequestParam(required = false) String name,
                                              @RequestParam(required = false) String type,
                                              @RequestParam(required = false) String characterName,
                                              @RequestParam(required = false, defaultValue = "brand") String belongTag,
                                              @RequestParam(required = false) String characterId,
                                              @RequestParam(required = false, defaultValue = "true") String visibleText3d) {
        try {
            String visibleCharacters = tags.get("visibleCharacters");
            log.debug("list character config ,account id is {}, visibleCharacters is {}, visibleText3d is {}", accountId, visibleCharacters, visibleText3d);
            Page<CharacterConfig> page = characterConfigService.list(accountId, visibleCharacters,
                    pageNo - 1, pageSize, name, type, characterName, belongTag, characterId, visibleText3d);
            return PageResponse.success(pageNo, pageSize, page.getTotalElements(), page.getContent());
        } catch (Throwable e) {
            log.error("Failed to list character config, accountId={}, e={}", accountId, e);
            return PageResponse.fail(e instanceof ServiceException ? e.getMessage() : "internal server error");
        }
    }

    @PostMapping("/validate")
    public Response<List<CharacterConfigRelatedModule>> retrieveBeforeDelete(
            @RequestAttribute("accountId") String accountId, @RequestBody BatchDeleteRequest request) {
        try {
            return Response.success(characterConfigService.retrieveBeforeDelete(accountId, request.getIds()));
        } catch (Throwable e) {
            log.error("Failed to retrieve all related modules, accountId={} ids={}", accountId, request.getIds(), e);
            return Response.fail(e instanceof ServiceException ? e.getMessage() : "internal server error");
        }
    }


    @DeleteMapping
    public PageResponse<CharacterConfig> deleteAll(@RequestAttribute("accountId") String accountId,
                                                   @RequestBody BatchDeleteRequest request) {
        try {
            return PageResponse.success(characterConfigService.deleteAll(accountId, request.getIds()));
        } catch (Throwable e) {
            log.error("Failed to delete all character config, accountId={} ids={}", accountId, request.getIds(), e);
            return PageResponse.fail(e instanceof ServiceException ? e.getMessage() : "internal server error");
        }
    }

}
