// Copyright (C) 2019 Baidu Inc. All rights reserved.
package com.baidu.acg.piat.digitalhuman.plat.service.impl;

import com.baidu.acg.piat.digitalhuman.common.entity.CharacterConfig;
import com.baidu.acg.piat.digitalhuman.plat.config.database.DataSourceNames;
import com.baidu.acg.piat.digitalhuman.plat.config.database.TargetDataSource;
import com.baidu.acg.piat.digitalhuman.plat.dao.AppRepository;
import com.baidu.acg.piat.digitalhuman.plat.dao.ProjectRepository;
import com.baidu.acg.piat.digitalhuman.plat.model.app.AccessAppModel;
import com.baidu.acg.piat.digitalhuman.plat.model.project.ProjectModel;
import com.baidu.acg.piat.digitalhuman.plat.v2.model.AILiveConfig;
import com.baidu.acg.piat.digitalhuman.plat.v2.repository.AILiveConfigRepository;
import com.baidu.acg.piat.digitalhuman.plat.v2.repository.CharacterConfigRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;


@Slf4j
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class SlaveDbServiceImpl {

    @Value("${digitalhuman.datasource.config.doubleWrite}")
    private boolean doubleWrite;

    private final AppRepository appRepository;

    private final ProjectRepository repository;

    private final CharacterConfigRepository characterConfigRepository;

    private final AILiveConfigRepository aiLiveConfigRepository;

    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    @TargetDataSource(value = DataSourceNames.slave)
    public void saveAppSlave(AccessAppModel accessAppModel) {
        if (!doubleWrite) {
            return;
        }
        appRepository.save(accessAppModel);
    }


    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    @TargetDataSource(value = DataSourceNames.slave)
    public void deleteByAppIdSlave(String appId) {
        if (!doubleWrite) {
            return;
        }
        appRepository.deleteByAppId(appId);
    }

    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    @TargetDataSource(value = DataSourceNames.slave)
    public void saveProjectSlave(ProjectModel projectModel) {
        if (!doubleWrite) {
            return;
        }
        repository.saveAndFlush(projectModel);
    }

    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    @TargetDataSource(value = DataSourceNames.slave)
    public void deleteByProjectIdSlave(String projectId) {
        if (!doubleWrite) {
            return;
        }
        repository.deleteByProjectId(projectId);

    }

    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    @TargetDataSource(value = DataSourceNames.slave)
    public void saveCharacterConfigSlave(CharacterConfig characterConfig) {
        if (!doubleWrite) {
            return;
        }
        characterConfigRepository.save(characterConfig);
    }

    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    @TargetDataSource(value = DataSourceNames.slave)
    public void deleteCharacterConfigSlave(CharacterConfig characterConfig) {
        if (!doubleWrite) {
            return;
        }
        characterConfigRepository.delete(characterConfig);
    }

    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    @TargetDataSource(value = DataSourceNames.slave)
    public void deleteAllCharacterConfigSlave(List<CharacterConfig> list) {
        if (!doubleWrite) {
            return;
        }
        characterConfigRepository.deleteAll(list);
    }

    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    @TargetDataSource(value = DataSourceNames.slave)
    public void deleteAllAiLiveConfigSlave(List<AILiveConfig> list) {
        if (!doubleWrite) {
            return;
        }
        aiLiveConfigRepository.deleteAll(list);
    }
}
