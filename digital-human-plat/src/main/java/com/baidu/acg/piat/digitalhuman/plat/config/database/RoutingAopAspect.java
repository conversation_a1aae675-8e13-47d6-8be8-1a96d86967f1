package com.baidu.acg.piat.digitalhuman.plat.config.database;

import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

@Order(-1)
@Aspect
@Component
public class RoutingAopAspect {

    @Around("@annotation(targetDataSource)")
    public Object routingWithDataSource(ProceedingJoinPoint joinPoint, TargetDataSource targetDataSource)
        throws Throwable {
        if (targetDataSource == null) {
            RoutingDataSource.setDataSource(DataSourceNames.master);
        } else {
            RoutingDataSource.setDataSource(targetDataSource.value());
        }
        try {
            return joinPoint.proceed();
        } finally {
            RoutingDataSource.clearDataSource();
        }
    }
}
