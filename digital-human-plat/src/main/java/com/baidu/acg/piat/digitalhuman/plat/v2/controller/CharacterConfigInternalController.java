package com.baidu.acg.piat.digitalhuman.plat.v2.controller;

import com.baidu.acg.piat.digitalhuman.plat.v2.service.ServiceException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import com.baidu.acg.piat.digitalhuman.common.entity.CharacterConfig;
import com.baidu.acg.piat.digitalhuman.common.model.Response;
import com.baidu.acg.piat.digitalhuman.plat.v2.service.CharacterConfigService;

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping({"/api/internal/digitalhuman/plat/v2/character-configs"})
public class CharacterConfigInternalController {

    private final CharacterConfigService characterConfigService;

    @GetMapping("/{id}")
    public Response<CharacterConfig> retrieve(@PathVariable String id) {
        try {
            return Response.success(characterConfigService.retrieve(id));
        } catch (Throwable e) {
            return Response.fail(e.getMessage());
        }
    }

    @PostMapping("/insert")
    public Response<CharacterConfig> insert(@RequestBody CharacterConfig config){
        try {
            return Response.success(characterConfigService.insert(config));
        } catch (Throwable e) {
            log.error("Failed to insert character config, accountId={} config={}", config.getUserId(), config, e);
            return Response.fail(e instanceof ServiceException ? e.getMessage() : "internal server error");
        }
    }
}
