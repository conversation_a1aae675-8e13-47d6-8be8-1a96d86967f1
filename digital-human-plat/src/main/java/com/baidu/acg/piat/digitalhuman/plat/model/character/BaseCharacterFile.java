package com.baidu.acg.piat.digitalhuman.plat.model.character;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
public class BaseCharacterFile {

    private List<NodeList> nodeList;

    @Data
    @NoArgsConstructor
    public static class NodeList {

        private String name;

        private String type;

        private int visible;

        private int touchable;

        private String meshFileName;

        private String position;

        private String rotation;

    }
}
