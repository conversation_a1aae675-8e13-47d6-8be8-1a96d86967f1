package com.baidu.acg.piat.digitalhuman.plat.client;

import com.baidu.acg.piat.digitalhuman.common.entity.CharacterConfigRelatedModule;
import com.baidu.acg.piat.digitalhuman.common.entity.CharacterConfigRequest;
import com.baidu.acg.piat.digitalhuman.common.exception.DigitalHumanCommonException;
import com.baidu.acg.piat.digitalhuman.common.model.Response;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import lombok.extern.slf4j.Slf4j;
import okhttp3.ResponseBody;
import retrofit2.Call;
import retrofit2.Retrofit;
import retrofit2.converter.jackson.JacksonConverterFactory;

import java.util.List;

@Slf4j
public class AidaClient {
    private final String baseUrl;

    private final AidaService aidaService;

    public AidaClient(String baseUrl) {
        this.baseUrl = baseUrl;
        var retrofit = new Retrofit.Builder()
                .addConverterFactory(
                        JacksonConverterFactory.create(new ObjectMapper().registerModule(new JavaTimeModule())))
                .baseUrl(baseUrl)
                .build();
        this.aidaService = retrofit.create(AidaService.class);
    }

    public List<CharacterConfigRelatedModule> listByCharacterConfigIds(CharacterConfigRequest request) {
        return callService(aidaService.listByCharacterConfigIds(request), "listByCharacterConfigId");
    }

    private <T> T callService(Call<Response<T>> call, String methodName) throws DigitalHumanCommonException {
        try {
            var response = call.execute();
            return getResult(response, methodName);
        } catch (DigitalHumanCommonException e) {
            log.warn("Fail to call aida service methodName={} url={}, ex=",
                    methodName, call.request().url(), e);
            throw e;
        } catch (Exception e) {
            log.error("Fail to call aida service methodName={} url={}, ex=",
                    methodName, call.request().url(), e);
            throw new DigitalHumanCommonException(buildMessage(methodName, "call aida service failed"), e);
        }
    }

    private <T> T getResult(retrofit2.Response<Response<T>> response, String method)
            throws DigitalHumanCommonException {
        if (response.isSuccessful()) {
            var body = response.body();
            if (body == null) {
                return null;
            } else if (body.isSuccess()) {
                return body.getResult();
            } else {
                throw new DigitalHumanCommonException(body.getCode(),
                        buildMessage(method, body.getMessage().getGlobal()));
            }
        }

        throw handleResponseUnsuccessful(response, method);
    }

    private <T> DigitalHumanCommonException handleResponseUnsuccessful(retrofit2.Response<T> response, String method) {
        try (ResponseBody errorBody = response.errorBody()) {
            String errorMessage = errorBody == null ? "unknown" : errorBody.string();
            return new DigitalHumanCommonException(response.code(), buildMessage(method, errorMessage));
        } catch (Exception e) {
            return new DigitalHumanCommonException(buildMessage(method, "unknown server error"), e);
        }
    }

    private String buildMessage(String method, String cause) {
        return method + "失败, 原因: " + cause;
    }
}
