package com.baidu.acg.piat.digitalhuman.plat.model.position;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;
import org.hibernate.annotations.UpdateTimestamp;

import java.time.ZonedDateTime;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;

import com.baidu.acg.piat.digitalhuman.common.position.PositionVo;

/**
 * Created on 2021/8/6 11:46 上午
 *
 * <AUTHOR>
 */
@Data
@Slf4j
@Builder
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
@DynamicInsert
@DynamicUpdate
@Entity(name = "position")
public class PositionModel {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    private String positionId;

    private String userId;

    private String name;

    private String content;

    private Integer width;

    private Integer height;

    @CreationTimestamp
    @Column(insertable = false, updatable = false)
    private ZonedDateTime createTime;

    @UpdateTimestamp
    @Column(insertable = false, updatable = false)
    private ZonedDateTime updateTime;

    public PositionVo toPositionVo() {
        return PositionVo.builder()
                .positionId(positionId)
                .userId(userId)
                .name(name)
                .content(content)
                .width(width)
                .height(height)
                .build();
    }
}
