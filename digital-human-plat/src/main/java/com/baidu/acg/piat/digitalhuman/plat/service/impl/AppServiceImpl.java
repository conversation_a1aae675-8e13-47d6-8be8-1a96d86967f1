// Copyright (C) 2019 Baidu Inc. All rights reserved.
package com.baidu.acg.piat.digitalhuman.plat.service.impl;

import com.baidu.acg.piat.digitalhuman.common.helper.OptLogHelper;
import com.baidu.acg.piat.digitalhuman.common.optlog.OptContent;
import com.baidu.acg.piat.digitalhuman.plat.service.OptLogService;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import com.baidu.acg.piat.digitalhuman.common.access.AccessApp;
import com.baidu.acg.piat.digitalhuman.common.model.PageResponse;
import com.baidu.acg.piat.digitalhuman.common.model.PageResult;
import com.baidu.acg.piat.digitalhuman.common.project.Project;
import com.baidu.acg.piat.digitalhuman.common.utils.IDGenerator;
import com.baidu.acg.piat.digitalhuman.plat.dao.AppRepository;
import com.baidu.acg.piat.digitalhuman.plat.exception.DigitalHumanAccessException;
import com.baidu.acg.piat.digitalhuman.plat.model.app.AccessAppModel;
import com.baidu.acg.piat.digitalhuman.plat.service.AppService;
import com.baidu.acg.piat.digitalhuman.plat.service.CharacterService;
import com.baidu.acg.piat.digitalhuman.plat.service.ProjectService;

/**
 * AppServiceImpl
 *
 * <AUTHOR> Zhensheng(<EMAIL>)
 * @since 2019-09-19
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class AppServiceImpl implements AppService {

    private static final Integer API_2VERSION = 2;

    private final AppRepository appRepository;

    private final MoreDbEditTransactionServiceImpl moreDbEditTransactionService;

    private final IDGenerator idGenerator;

    private final ProjectService projectService;

    private final CharacterService characterService;

    private final OptLogService optLogService;

    @Value("${digitalhuman.access.app.generate-config.env-prefix}")
    private String envPrefix;

    @Value("${digitalhuman.access.app.generate-config.retry-times:3}")
    private int retryTimes;

    @Value("${digitalhuman.access.app.generate-config.retry-sleep-millis:100}")
    private long retrySleepMillis;

    @Value("${digitalhuman.access.app.max-idle-in-second:60}")
    private int maxIdleInSecond;

    @Override
    public AccessApp create(AccessApp appRequest, Boolean needValidate) {
        log.debug("accept app create request {} ", appRequest);
        if (needValidate) {
            validateAppRequest(appRequest);
            validateName(appRequest.getUserId(), appRequest.getName());
            validateProject(appRequest);
            validateCharacter(appRequest.getCharacterImage());
        }
        if (!appRequest.resourceQuotaValid()) {
            throw new DigitalHumanAccessException("App resource quota invalid");
        }
        AccessAppModel result = null;
        int retryTimes = 0;
        do {
            try {
                Thread.sleep(retrySleepMillis * retryTimes);
            } catch (InterruptedException e) {
                log.error("", e);
            }
            try {
                var appCredential = generateAppIdAndKey();
                appRequest.setAppId(appCredential.getFirst()).setAppKey(appCredential.getSecond());
                appRequest.setMaxIdleInSecond(maxIdleInSecond);
                result = save(AccessAppModel.fromAccessApp(appRequest));
            } catch (Exception e) {
                log.error("Fail to create app and do retry, cause: ", e);
            }
            retryTimes++;
        }
        while (result == null && (this.retryTimes == -1 || retryTimes < this.retryTimes));
        if (result == null) {
            log.error("Fail to create app {} , {} ", retryTimes, appRequest);
            throw new DigitalHumanAccessException("App generate failed");
        }
        if (API_2VERSION.equals(appRequest.getApiVersion())
                && StringUtils.isNotEmpty(appRequest.getUid())) {
            optLogService.create(OptLogHelper.buildOptLog(OptContent.APP_ADD
                    , appRequest.getName(), appRequest.getUserId(), appRequest.getUid()));
        }
        return result.toAccessApp();
    }

    private Pair<String, String> generateAppIdAndKey() {
        // 生成以"${envPrefix}-"开头的、包含时间信息的、15位的AppId; 生成纯random的、20位的key
        return Pair.of(idGenerator.generate(envPrefix).substring(0, 15), idGenerator.generate(20));
    }

    @Override
    public AccessApp get(String appId) {
        log.debug("accept app get request {} ", appId);
        var optional = appRepository.findByAppId(appId);
        if (optional.isEmpty()) {
            throw new DigitalHumanAccessException("App not existed");
        }
        return optional.get().toAccessApp();
    }

    @Override
    public List<AccessApp> list(List<String> appIds) {
        log.debug("Accept app get batch request={}", appIds);
        return Lists.newArrayList(appRepository.findAllByAppIdIn(appIds))
                .stream()
                .map(AccessAppModel::toAccessApp)
                .collect(Collectors.toList());
    }

    @Override
    public AccessApp update(String appId, AccessApp appRequest) {
        log.debug("accept app update request {} {} ", appId, appRequest);

        validateAppRequest(appRequest);
        validateProject(appRequest);
        validateCharacter(appRequest.getCharacterImage());
        if (!appRequest.resourceQuotaValid()) {
            throw new DigitalHumanAccessException("App resource quota invalid");
        }
        var optional = appRepository.findByAppId(appId);
        if (optional.isEmpty()) {
            throw new DigitalHumanAccessException("App not existed");
        }
        if (StringUtils.isNotEmpty(appRequest.getUserId()) &&
                !optional.get().getUserId().equals(appRequest.getUserId())) {
            throw new DigitalHumanAccessException("App not belong to the user");
        }
        var updated = optional.get();
        if (!updated.getName().equals(appRequest.getName())) {
            validateName(appRequest.getUserId(), appRequest.getName());
        }
        Optional.ofNullable(appRequest.getName()).ifPresent(updated::setName);
        Optional.ofNullable(appRequest.getCharacterImage()).ifPresent(updated::setCharacterImage);
        Optional.ofNullable(appRequest.getDescription()).ifPresent(updated::setDescription);
        Optional.ofNullable(appRequest.getResourceQuota()).ifPresent(updated::setResourceQuota);
        Optional.ofNullable(appRequest.getTags()).ifPresent(updated::setTags);
        if (Optional.of(appRequest.isEnabled()).isPresent()) {
            updated.setEnabled(appRequest.isEnabled() ? 1 : 0);
        }

        updated.setProjectId(appRequest.getProjectId())
                .setProjectName(appRequest.getProjectName())
                .setProjectVersion(appRequest.getProjectVersion());

        updated.setEditor(appRequest.getEditor());

        var result = save(updated);
        if (API_2VERSION.equals(appRequest.getApiVersion())
                && StringUtils.isNotEmpty(appRequest.getUid())) {
            optLogService.create(OptLogHelper.buildOptLog(OptContent.APP_UPDATE
                    , updated.getName(), updated.getUserId(), appRequest.getUid()));
        }
        return result.toAccessApp();
    }

    @Override
    public void delete(String appId, String uid, int apiVersion) {
        log.debug("accept app delete request {} ", appId);
        var optional = appRepository.findByAppId(appId);
        if (optional.isEmpty()) {
            throw new DigitalHumanAccessException("App not existed");
        }
        deleteByAppId(optional.get());
        if (API_2VERSION.equals(apiVersion)
                && StringUtils.isNotEmpty(uid)) {
            optLogService.create(OptLogHelper.buildOptLog(OptContent.APP_DELETE
                    , optional.get().getName(), optional.get().getUserId(), uid));
        }
    }

    @Override
    public void disable(String appId) {
        log.debug("accept app disable request {} ", appId);
        var optional = appRepository.findByAppId(appId);
        if (optional.isEmpty()) {
            throw new DigitalHumanAccessException("App not existed");
        }
        var app = optional.get();
        if (app.getEnabled() != 1) {
            throw new DigitalHumanAccessException("App already disabled");
        }
        save(app.setEnabled(0));
    }


    @Override
    public PageResponse<AccessApp> listAll(String userId, String name,
                                           String characterTypes, String projectIds, int apiVersion,
                                           Pageable pageable) {
        log.debug("list app by userId={}, name={}, characterTypes={}, apiVersion={}", userId, name,
                characterTypes, apiVersion);
        Page<AccessAppModel> result = null;

        List<String> characters = null;
        List<String> projectidList = null;
        boolean inCharacters = false;
        boolean inProjectid = false;
        if (!StringUtils.isBlank(characterTypes) && !"all".equals(characterTypes)) {
            characters = Arrays.asList(characterTypes.split(","));
            inCharacters = true;
        }
        if (!StringUtils.isBlank(projectIds) && !"all".equals(projectIds)) {
            projectidList = Arrays.asList(projectIds.split(","));
            inProjectid = true;
        }

        name = name == null ? "" : name;

        if (inCharacters && inProjectid) {
            result = appRepository.
                    findAllByUserIdAndApiVersionAndNameContainingAndProjectIdInAndCharacterImageInOrderByCreateTimeDesc(
                    userId, apiVersion, name, projectidList, characters, pageable);
        } else if (inCharacters) {
            result = appRepository.
                    findAllByUserIdAndApiVersionAndNameContainingAndCharacterImageInOrderByCreateTimeDesc(
                    userId, apiVersion, name, characters, pageable);
        } else if (inProjectid) {
            result = appRepository.findAllByUserIdAndApiVersionAndNameContainingAndProjectIdInOrderByCreateTimeDesc(
                    userId, apiVersion, name, projectidList, pageable);
        } else {
            result = appRepository.findAllByUserIdAndApiVersionAndNameContainingOrderByCreateTimeDesc(
                    userId, apiVersion, name, pageable);
        }

        List<AccessApp> list = Lists.newArrayList();
        long totalCount = 0;
        if (result != null) {
            result.forEach(accessAppModel -> list.add(accessAppModel.toAccessApp()));
            totalCount = result.getTotalElements();
        }
        return PageResponse.<AccessApp>builder()
                .page(PageResult.<AccessApp>builder()
                        .pageNo(pageable.getPageNumber() + 1)
                        .pageSize(pageable.getPageSize())
                        .totalCount(totalCount)
                        .result(list)
                        .build())
                .build();
    }

    @Override
    public List<AccessApp> listAll(int apiVersion) {
        return Lists.newArrayList(appRepository
                        .findAllByApiVersion(apiVersion))
                .stream()
                .map(AccessAppModel::toAccessApp)
                .collect(Collectors.toList());
    }

    @Override
    public AccessApp findByAppIdAndCharacterImage(String appId, String characterImage) {
        log.debug("AppServiceImpl findByAppIdAndCharacterImage appId:{}, characterImage:{}", appId, characterImage);
        var result = appRepository.findByAppIdAndCharacterImage(appId, characterImage);
        return result.get().toAccessApp();
    }

    private void validateAppRequest(AccessApp appRequest) {
        if (appRequest.getAppId() != null) {
            throw new DigitalHumanAccessException("AppId must be assigned by server");
        }
        if (appRequest.getAppKey() != null) {
            throw new DigitalHumanAccessException("AppKey must be assigned by server");
        }
        if (StringUtils.isEmpty(appRequest.getCharacterImage())) {
            throw new DigitalHumanAccessException("CharacterImage cannot be empty");
        }
    }

    private void validateName(String userId, String name) {
        if (StringUtils.isEmpty(name)) {
            throw new DigitalHumanAccessException("应用名称不能为空");
        }
        var pageResult = appRepository.findByUserIdAndName(userId, name, null);
        if (!pageResult.isEmpty()) {
            throw new DigitalHumanAccessException("应用名称已存在");
        }
    }

    private void validateProject(AccessApp request) {
        if (StringUtils.isNotEmpty(request.getProjectId()) && StringUtils.isNotEmpty(request.getProjectName())) {
            return;
        }
        var project = new Project();
        if (StringUtils.isNotEmpty(request.getProjectName())) {
            project = projectService.getByUserIdNameAndVersion(request.getUserId(), request.getProjectName(),
                    request.getProjectVersion(), request.getApiVersion());
            request.setProjectId(project.getId());
        }
        if (StringUtils.isNotEmpty(request.getProjectId())) {
            project = projectService.detail(request.getProjectId());
            request.setProjectName(project.getName());
        }
        if (StringUtils.isNotEmpty(project.getUserId())
                && !request.getUserId().equals(project.getUserId())) {
            throw new DigitalHumanAccessException("Project must belong to access user.");
        }
    }

    private void validateCharacter(String character) {

        characterService.selectByType(character);
    }

    @Override
    public AccessApp findDefaultByCharacterImage(String characterImage, int apiVersion) {
        log.debug("Get default app of character={} ", characterImage);
        var selectByType = characterService.selectByType(characterImage, apiVersion);
        if (selectByType == null) {
            throw new DigitalHumanAccessException("CharacterImage not existed");
        }

        var optional = appRepository.findByAppId(selectByType.getAppId());
        if (optional.isEmpty()) {
            throw new DigitalHumanAccessException("App not existed");
        }
        return optional.get().toAccessApp();
    }

    @Override
    public List<AccessApp> listByCharacterImage(String characterImage, int apiVersion) {
        return null;
    }

    @Override
    public List<AccessApp> listByCharacterImages(List<String> characterImages, int apiVersion) {

        return Lists.newArrayList(appRepository
                        .findAllByApiVersionAndCharacterImageIn(apiVersion, characterImages))
                .stream()
                .map(AccessAppModel::toAccessApp)
                .collect(Collectors.toList());
    }

    public AccessAppModel save(AccessAppModel accessAppModel) {
        return moreDbEditTransactionService.saveApp(accessAppModel);
    }

    public void deleteByAppId(AccessAppModel accessAppModel) {
        moreDbEditTransactionService.deleteByAppId(accessAppModel);
    }
}
