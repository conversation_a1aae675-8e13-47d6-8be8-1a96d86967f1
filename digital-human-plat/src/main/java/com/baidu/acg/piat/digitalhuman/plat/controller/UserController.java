// Copyright (C) 2019 Baidu Inc. All rights reserved.
package com.baidu.acg.piat.digitalhuman.plat.controller;

import com.baidu.acg.piat.digitalhuman.common.access.AccessResponse;
import com.baidu.acg.piat.digitalhuman.common.access.AccessUser;
import com.baidu.acg.piat.digitalhuman.plat.service.UserService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * UserController
 *
 * <AUTHOR>
 * @since 2019-09-18
 */
@Deprecated
@RestController
@RequestMapping("/api/digitalhuman/v1/access")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class UserController {

    private final UserService userService;

    @PostMapping("/user")
    public AccessResponse<AccessUser> create(@RequestBody AccessUser userRequest) {
        return AccessResponse.success(userService.create(userRequest));
    }


    @DeleteMapping("/user/{userId}")
    public AccessResponse<Void> delete(@PathVariable(value = "userId") String userId) {
        userService.delete(userId);
        return AccessResponse.success(null);
    }


    @PutMapping("/user/{userId}")
    public AccessResponse<AccessUser> update(@PathVariable(value = "userId") String userId,
                                             @RequestBody AccessUser userRequest) {
        return AccessResponse.success(userService.update(userId, userRequest));
    }


    @GetMapping("/user/{userId}")
    public AccessResponse<AccessUser> get(@PathVariable(value = "userId") String userId) {
        return AccessResponse.success(userService.get(userId));
    }

    @GetMapping("/user")
    public AccessResponse<AccessUser> getByName(@RequestParam(value = "name") String name) {
        return AccessResponse.success(userService.getByName(name));
    }

    @GetMapping("/user/iamUserId")
    public AccessResponse<AccessUser> getByIamUserId(@RequestBody AccessUser accessUser) {
        return AccessResponse.success(userService.getByIamUserId(accessUser));
    }

    @PostMapping("/user/validate")
    public AccessResponse<AccessUser> validate(@RequestBody AccessUser accessUser) {
        return AccessResponse.success(userService.validate(accessUser));
    }

    @PostMapping("/user/name/validate")
    public AccessResponse<AccessUser> validateByName(@RequestBody AccessUser accessUser) {
        return AccessResponse.success(userService.validateByName(accessUser));
    }

}
