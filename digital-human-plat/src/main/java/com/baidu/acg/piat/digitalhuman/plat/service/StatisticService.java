package com.baidu.acg.piat.digitalhuman.plat.service;

import com.baidu.acg.piat.digitalhuman.common.model.PageResult;
import com.baidu.acg.piat.digitalhuman.common.statistic.CharacterPreferencesData;
import com.baidu.acg.piat.digitalhuman.common.statistic.CharacterResourceResponse;
import com.baidu.acg.piat.digitalhuman.common.statistic.CharacterUsageTrendData;
import com.baidu.acg.piat.digitalhuman.common.statistic.ProductionStatisticData;
import com.baidu.acg.piat.digitalhuman.common.statistic.SessionStatisticPageResult;
import com.baidu.acg.piat.digitalhuman.common.statistic.StatisticResponse;
import com.baidu.acg.piat.digitalhuman.common.common.PlatCode;

import java.util.List;
import java.util.concurrent.ExecutionException;

public interface StatisticService {

    StatisticResponse getActiveSession(String appId, long startInMs, long endInMs);

    StatisticResponse getTotalSession(String appId, long startInMs, long endInMs);

    StatisticResponse getAvgSessionDuration(String appId, long startInMs, long endInMs);

    StatisticResponse getTotalDialog(String appId, long startInMs, long endInMs);

    StatisticResponse getSessionTrend(String appId, long startInMs, long endInMs, long intervalInMs);

    CharacterResourceResponse characterResource(PlatCode platCode, String accountId
            , String userVisibleTypes, int roleLevel);

    List<CharacterUsageTrendData> characterUsageTrend(PlatCode platCode, String accountId
            , int roleLevel, String accountVisibleCharacters
            , long startInMs, long endInMs, long intervalInSeconds);

    List<CharacterPreferencesData> characterPreferences(PlatCode platCode, String accountId
            , int roleLevel, String accountVisibleCharacters, List<String> accountMenus)
            throws ExecutionException;

    SessionStatisticPageResult sessionService(PlatCode platCode
            , String accountId, int roleLevel
            , int pageNo, int pageSize
            , int startInDate, int endInDate, String orderBy, String order);

    PageResult<ProductionStatisticData> production(PlatCode platCode
            , String accountId, int roleLevel
            , int pageNo, int pageSize
            , int startInDate, int endInDate);
}
