package com.baidu.acg.piat.digitalhuman.plat.model.property;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Transient;
import javax.validation.constraints.NotBlank;
import java.time.ZonedDateTime;
import java.util.Objects;

@Data
@Slf4j
@Builder
@Accessors(chain = true)
@DynamicInsert
@DynamicUpdate
@Entity(name = "property")
@JsonInclude(JsonInclude.Include.NON_NULL)
@AllArgsConstructor
@NoArgsConstructor
public class PropertyModel {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @NotBlank
    private String name;

    @NotBlank
    private String propertyId;

    @NotBlank
    private String type;

    @NotBlank
    private String characterId;

    @CreationTimestamp
    @Column(insertable = false)
    private ZonedDateTime createTime;

    private String frontImageUrl;

    private String propertyUrl;

    private Long frameCount;

    @Builder.Default
    private  boolean enable = true;

    private String md5;

    private String propertyJson;

    @Builder.Default
    private boolean basicResource = false;

    private String dhDrml;

    private String userName;

    private String description;

    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    @Transient
    private String thumbnail;

    public static PropertyModel from(PropertyModel model) {
        PropertyType verifyType;
        try {
            verifyType = PropertyType.valueOf(model.getType().toUpperCase());
        } catch (IllegalArgumentException e) {
            verifyType = PropertyType.ADDITIONAL;
        }

        return PropertyModel.builder().propertyId(model.getPropertyId())
                .type(model.getType())
                .name(model.getName())
                .characterId(model.getCharacterId())
                .frontImageUrl(model.getFrontImageUrl())
                .propertyUrl(StringUtils.isNotEmpty(model.getPropertyUrl()) ? model.getPropertyUrl() : "")
                .frameCount(model.getFrameCount() == null ? 0L : model.getFrameCount())
                .createTime(ZonedDateTime.now())
                .propertyJson(StringUtils.isNotEmpty(model.getPropertyJson()) ? model.getPropertyJson() : "")
                .md5(StringUtils.isNotEmpty(model.getMd5()) ? model.getMd5() : "")
                .basicResource(model.isBasicResource())
                .enable(model.isEnable())
                .dhDrml(StringUtils.isNotEmpty(model.getDhDrml()) ? model.getDhDrml() : verifyType.getDhDrml())
                .userName(StringUtils.isNotEmpty(model.getUserName()) ? model.getUserName() : "")
                .description(StringUtils.isNotEmpty(model.getDescription()) ? model.getDescription() : "")
                .build();
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        PropertyModel that = (PropertyModel) o;
        return Objects.equals(name, that.name)
                && Objects.equals(propertyId, that.propertyId)
                && Objects.equals(type, that.type)
                && Objects.equals(characterId, that.characterId)
                && Objects.equals(frontImageUrl, that.frontImageUrl)
                && Objects.equals(propertyUrl, that.propertyUrl)
                && Objects.equals(frameCount, that.frameCount)
                && Objects.equals(md5, that.md5)
                && Objects.equals(propertyJson, that.propertyJson)
                && Objects.equals(dhDrml, that.dhDrml)
                && Objects.equals(userName, that.userName)
                && Objects.equals(description, that.description);
    }

    @Override
    public int hashCode() {
        return Objects.hash(name, propertyId, type, characterId, frontImageUrl, propertyUrl, frameCount, md5,
                propertyJson, dhDrml, userName, description);
    }
}
