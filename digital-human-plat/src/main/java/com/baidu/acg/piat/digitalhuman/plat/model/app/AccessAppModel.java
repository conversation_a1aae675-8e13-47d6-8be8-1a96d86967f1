// Copyright (C) 2019 Baidu Inc. All rights reserved.
package com.baidu.acg.piat.digitalhuman.plat.model.app;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.UpdateTimestamp;
import org.springframework.util.CollectionUtils;

import java.io.IOException;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Map;

import javax.persistence.Access;
import javax.persistence.AccessType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Transient;
import javax.persistence.Version;
import javax.validation.constraints.Min;

import com.baidu.acg.piat.digitalhuman.common.access.AccessApp;
import com.baidu.acg.piat.digitalhuman.common.quota.ResourceQuota;

/**
 * AccessAppModel
 *
 * <AUTHOR> Zhensheng(<EMAIL>)
 * @since 2019-09-19
 */
@Data
@Slf4j
@Builder
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
@DynamicInsert
@DynamicUpdate
@Entity(name = "access_app")
public class AccessAppModel {

    private static final ResourceQuota DEFAULT_QUOTA = ResourceQuota.builder().build();
    private static final ObjectMapper mapper = new ObjectMapper();

    @Id
    @GeneratedValue(strategy = GenerationType.AUTO, generator = "myid")
    @GenericGenerator(name = "myid",
            strategy = "com.baidu.acg.piat.digitalhuman.plat.config.database.ManulInsertGenerator")
    private Long id;

    private String appId;

    private String appKey;

    private String userId;

    private String name;

    private String description;

    private String projectId;

    private String projectName;

    private String projectVersion;

    private String characterImage;

    @Transient
    private Map<String, String> tags;

    @Transient
    @Builder.Default
    private ResourceQuota resourceQuota = DEFAULT_QUOTA;

    @Min(30)
    private Integer maxIdleInSecond;

    private String editor;

    @Builder.Default
    private int enabled = 1;

    private int apiVersion;

    @CreationTimestamp
    @Column(insertable = false, updatable = false)
    private ZonedDateTime createTime;

    @UpdateTimestamp
    @Column(insertable = false, updatable = false)
    private ZonedDateTime updateTime;

    @Version
    private Integer version;

    @Column(name = "tags")
    @Access(AccessType.PROPERTY)
    public String getTagsString() {
        if (!CollectionUtils.isEmpty(tags)) {
            try {
                return mapper.writeValueAsString(tags);
            } catch (JsonProcessingException e) {
                log.error("Parse tags to json failed.", e);
            }
        }
        return StringUtils.EMPTY;
    }

    public void setTagsString(String tags) {
        if (StringUtils.isNotEmpty(tags)) {
            try {
                this.tags = mapper.readValue(tags,
                        mapper.getTypeFactory().constructParametricType(Map.class, String.class, String.class));
            } catch (IOException e) {
                log.error("Exception when parse tags from json string, string={}.", tags, e);
            }
        } else {
            this.tags = null;
        }
    }

    @Column(name = "resource_quota")
    @Access(AccessType.PROPERTY)
    public String getResourceQuotaString() {
        if (resourceQuota != null) {
            try {
                return mapper.writeValueAsString(resourceQuota);
            } catch (JsonProcessingException e) {
                log.error("Parse resource quota to json failed.", e);
            }
        }
        return StringUtils.EMPTY;
    }

    public void setResourceQuotaString(String resourceQuota) {
        if (StringUtils.isNotEmpty(resourceQuota)) {
            try {
                this.resourceQuota = mapper.readValue(resourceQuota, ResourceQuota.class);
            } catch (IOException e) {
                log.error("Exception when parse resource quota from json string, string={}.", resourceQuota, e);
            }
        } else {
            this.resourceQuota = null;
        }
    }

    public AccessApp toAccessApp() {
        return AccessApp.builder()
                .appId(appId)
                .appKey(appKey)
                .userId(userId)
                .name(name)
                .description(description)
                .projectId(projectId)
                .projectName(projectName)
                .projectVersion(projectVersion)
                .tags(tags)
                .resourceQuota(resourceQuota)
                .maxIdleInSecond(maxIdleInSecond)
                .editor(editor)
                .createTime(DateTimeFormatter.ISO_OFFSET_DATE_TIME.format(createTime))
                .updateTime(DateTimeFormatter.ISO_OFFSET_DATE_TIME.format(updateTime))
                .enabled(enabled == 1 ? true : false)
                .characterImage(characterImage)
                .apiVersion(apiVersion)
                .build();
    }


    public static AccessAppModel fromAccessApp(AccessApp appRequest) {
        return AccessAppModel.builder()
                .appId(appRequest.getAppId())
                .appKey(appRequest.getAppKey())
                .userId(appRequest.getUserId())
                .name(appRequest.getName())
                .description(appRequest.getDescription() == null ?
                        "" : appRequest.getDescription())
                .projectId(appRequest.getProjectId())
                .projectName(appRequest.getProjectName())
                .projectVersion(appRequest.getProjectVersion())
                .tags(appRequest.getTags())
                .resourceQuota(appRequest.resourceQuotaValid() ?
                        appRequest.getResourceQuota() : DEFAULT_QUOTA)
                .maxIdleInSecond(appRequest.getMaxIdleInSecond())
                .editor(appRequest.getEditor())
                .characterImage(appRequest.getCharacterImage())
                .enabled(1)
                .apiVersion(appRequest.getApiVersion())
                .build();
    }

}
