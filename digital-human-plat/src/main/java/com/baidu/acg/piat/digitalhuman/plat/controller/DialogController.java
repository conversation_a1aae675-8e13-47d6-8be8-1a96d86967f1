package com.baidu.acg.piat.digitalhuman.plat.controller;

import static com.baidu.acg.piat.digitalhuman.common.utils.DateTimeUtil.getTodayStartTimeMs;

import com.baidu.acg.piat.digitalhuman.common.exception.DigitalHumanCommonException;
import com.baidu.acg.piat.digitalhuman.common.model.MarkIdWrapper;
import com.baidu.acg.piat.digitalhuman.common.model.PageResponse;
import com.baidu.acg.piat.digitalhuman.common.model.Response;
import com.baidu.acg.piat.digitalhuman.common.session.Dialog;
import com.baidu.acg.piat.digitalhuman.plat.service.DialogService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * The dialog controller.
 *
 * <AUTHOR> Mao (<EMAIL>)
 */
@Slf4j
@RestController
@RequestMapping("/api/digitalhuman/v1")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class DialogController {

    private final DialogService dialogService;

    @PostMapping("/room/dialog")
    public Dialog create(@RequestBody Dialog dialog) {
        return dialogService.create(dialog);
    }

    @GetMapping("/room/dialog")
    public PageResponse<Dialog> listByRoomId(
            @RequestParam(name = "roomId", required = false) String roomId,
            @RequestParam(name = "appId", required = false) String appId,
            @RequestParam(name = "roomName", required = false) String roomName,
            @RequestParam(required = false, defaultValue = "1") int pageNo,
            @RequestParam(required = false, defaultValue = "20") int pageSize) {
        if (StringUtils.isNotBlank(roomId)) {
            return dialogService.listByRoomId(roomId, pageNo, pageSize);
        } else if (StringUtils.isNotBlank(appId)) {
            return dialogService.listByAppIdAndRoomName(appId, roomName, pageNo, pageSize);
        } else {
            throw new DigitalHumanCommonException("Neither roomId or appId is valid.");
        }
    }

    @GetMapping("/room/session/dialog")
    public PageResponse<Dialog> listBySessionId(
            @RequestParam(name = "sessionId") String sessionId,
            @RequestParam(required = false, defaultValue = "1") int pageNo,
            @RequestParam(required = false, defaultValue = "20") int pageSize) {
        if (StringUtils.isBlank(sessionId)) {
            throw new DigitalHumanCommonException("Invalid empty sessionId");
        }
        return dialogService.listBySessionId(sessionId, pageNo, pageSize);
    }


    /**
     * 根据时间戳去查历史记录，做下拉更新根据 < 的情况去处理，用create time有问题，时间近的会乱序
     *
     * @param roomName     房间名称
     * @param startTimeMs  开始时间戳
     * @param sort         排序字段名，可选值为"desc"或"asc"
     * @param pageNo       当前页码，从1开始计数
     * @param pageSize     分页大小
     * @return 符合条件的对话列表分页响应
     */
    @GetMapping("/room/name/starttimems/dialog")
    public PageResponse<Dialog> listByRoomNameAndTimestampLessThan(
            @RequestParam(name = "roomName") String roomName,
            @RequestParam(name = "appId") String appId,
            @RequestParam(required = false) Long startTimeMs,
            @RequestParam(required = false, defaultValue = "desc") String sort,
            @RequestParam(required = false, defaultValue = "1") int pageNo,
            @RequestParam(required = false, defaultValue = "20") int pageSize) {
        if (StringUtils.isBlank(roomName) ) {
            throw new DigitalHumanCommonException("Invalid empty roomName");
        }

        if (StringUtils.isBlank(appId) ) {
            throw new DigitalHumanCommonException("Invalid empty appId");
        }

        if (startTimeMs == null || startTimeMs < 0) {
            // 开始时间默认为当前时间戳
            startTimeMs = System.currentTimeMillis();
        }

        return dialogService.listByRoomNameAndTimestampLessThan(roomName, appId, startTimeMs, sort, pageNo, pageSize);
    }

    @GetMapping(value = "/room/dialog/mark")
    public Response<MarkIdWrapper<Dialog>> listByMarkId(
            @RequestParam(name = "appId") String appId,
            @RequestParam(name = "roomName") String roomName,
            @RequestParam(name = "markId", required = false) String markId,
            @RequestParam(required = false) Long startTimeMs,
            @RequestParam(required = false, defaultValue = "1000") Integer maxKeys) {
        if (maxKeys <= 0) {
            throw new DigitalHumanCommonException("maxKeys must be greater than 0");
        }
        if (startTimeMs == null) {
            // 开始时间默认为当天零点
            startTimeMs = getTodayStartTimeMs();
        }
        return Response.success(dialogService.listByAppIdAndRoomNameAndMarkId(appId, roomName, markId,
                startTimeMs, maxKeys));
    }
}

