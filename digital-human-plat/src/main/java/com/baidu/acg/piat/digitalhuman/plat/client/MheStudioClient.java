package com.baidu.acg.piat.digitalhuman.plat.client;

import com.baidu.acg.piat.digitalhuman.common.entity.CharacterConfigRelatedModule;
import com.baidu.acg.piat.digitalhuman.common.entity.CharacterConfigRequest;
import com.baidu.acg.piat.digitalhuman.common.exception.DigitalHumanCommonException;
import com.baidu.acg.piat.digitalhuman.common.mhe.MheProject;
import com.baidu.acg.piat.digitalhuman.common.mhe.MheVideo;
import com.baidu.acg.piat.digitalhuman.common.model.PageResult;
import com.baidu.acg.piat.digitalhuman.common.model.Response;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import lombok.extern.slf4j.Slf4j;
import okhttp3.ResponseBody;
import retrofit2.Call;
import retrofit2.Retrofit;
import retrofit2.converter.jackson.JacksonConverterFactory;

import java.util.List;

@Slf4j
public class MheStudioClient {

    private final String baseUrl;

    private final MheStudioService mheStudioService;

    public MheStudioClient(String baseUrl) {
        this.baseUrl = baseUrl;
        var retrofit = new Retrofit.Builder()
                .addConverterFactory(
                        JacksonConverterFactory.create(new ObjectMapper().registerModule(new JavaTimeModule())))
                .baseUrl(baseUrl)
                .build();
        this.mheStudioService = retrofit.create(MheStudioService.class);
    }

    public List<CharacterConfigRelatedModule> listByCharacterConfigIds(CharacterConfigRequest request) {
        return callService(mheStudioService.listByCharacterConfigIds(request), "listByCharacterConfigIds");
    }

    public List<MheProject> listAllProjects(String accountId) {
        return callService(mheStudioService.listAllProjects(accountId), "listAllProjects");
    }

    public PageResult<MheVideo> listAllVidesByPage(int pageNo, int pageSize, String accountId) {
        return callService(mheStudioService.listAllVidesByPage(pageNo, pageSize, accountId)
                , "listAllVidesByPage");
    }

    private <T> T callService(Call<Response<T>> call, String methodName) throws DigitalHumanCommonException {
        try {
            var response = call.execute();
            return getResult(response, methodName);
        } catch (DigitalHumanCommonException e) {
            log.warn("Fail to call mhe-studio service methodName={} url={}, ex=",
                    methodName, call.request().url(), e);
            throw e;
        } catch (Exception e) {
            log.error("Fail to call mhe-studio service methodName={} url={}, ex=",
                    methodName, call.request().url(), e);
            throw new DigitalHumanCommonException(buildMessage(methodName, "call mhe-studio service failed"), e);
        }
    }

    private <T> T getResult(retrofit2.Response<Response<T>> response, String method)
            throws DigitalHumanCommonException {
        if (response.isSuccessful()) {
            var body = response.body();
            if (body == null) {
                return null;
            } else if (body.isSuccess()) {
                return body.getResult();
            } else {
                throw new DigitalHumanCommonException(body.getCode(),
                        buildMessage(method, body.getMessage().getGlobal()));
            }
        }

        throw handleResponseUnsuccessful(response, method);
    }

    private <T> DigitalHumanCommonException handleResponseUnsuccessful(retrofit2.Response<T> response, String method) {
        try (ResponseBody errorBody = response.errorBody()) {
            String errorMessage = errorBody == null ? "unknown" : errorBody.string();
            return new DigitalHumanCommonException(response.code(), buildMessage(method, errorMessage));
        } catch (Exception e) {
            return new DigitalHumanCommonException(buildMessage(method, "unknown server error"), e);
        }
    }

    private String buildMessage(String method, String cause) {
        return method + "失败, 原因: " + cause;
    }

}
