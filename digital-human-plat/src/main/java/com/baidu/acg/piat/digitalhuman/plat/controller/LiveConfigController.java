package com.baidu.acg.piat.digitalhuman.plat.controller;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

import com.baidu.acg.piat.digitalhuman.common.live.LiveConfig;
import com.baidu.acg.piat.digitalhuman.common.model.PageResponse;
import com.baidu.acg.piat.digitalhuman.common.model.Response;
import com.baidu.acg.piat.digitalhuman.plat.service.LiveConfigService;

/**
 * 直播设置
 *
 * <AUTHOR>
 * @since 2021/08/05
 */
@Slf4j
@RestController
@RequestMapping({"/api/digitalhuman/v1/liveconfig"})
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class LiveConfigController {
    private final LiveConfigService liveConfigService;

    @PostMapping
    public Response<LiveConfig> create(@Valid @RequestBody LiveConfig config) {
        return Response.success(liveConfigService.create(config));
    }

    @PutMapping("{configId}")
    public Response<LiveConfig> update(@PathVariable("configId") String configId,
                                       @Valid @RequestBody LiveConfig config) {
        config.setConfigId(configId);
        return Response.success(liveConfigService.update(config));
    }

    @DeleteMapping("{configId}")
    public Response<Void> delete(@PathVariable("configId") String configId) {
        liveConfigService.deleteByConfigId(configId);
        return Response.success(null);
    }

    @GetMapping("{configId}")
    public Response<LiveConfig> getByConfigId(@PathVariable("configId") String configId) {
        return Response.success(liveConfigService.findOneByConfigId(configId).orElse(null));
    }

    @GetMapping("user/latest")
    public Response<LiveConfig> getLatestOneByUserId(@RequestParam("userId") String userId) {
        return Response.success(liveConfigService.findLatestByUserId(userId).orElse(null));
    }

    @GetMapping("user")
    public PageResponse<LiveConfig> getAllByUserId(@RequestParam("userId") String userId,
                                                   @RequestParam(required = false, defaultValue = "1") int pageNo,
                                                   @RequestParam(required = false, defaultValue = "20") int pageSize) {
        Page<LiveConfig> pageResponse = liveConfigService.findAllByUserId(userId, PageRequest.of(pageNo - 1, pageSize));
        return PageResponse.success(pageNo, pageSize, pageResponse.getTotalElements(), pageResponse.getContent());
    }

}
