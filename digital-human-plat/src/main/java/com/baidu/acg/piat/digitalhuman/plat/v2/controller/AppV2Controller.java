package com.baidu.acg.piat.digitalhuman.plat.v2.controller;

import com.baidu.acg.piat.digitalhuman.plat.service.AppService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestAttribute;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.baidu.acg.piat.digitalhuman.common.model.Response;
import com.baidu.acg.piat.digitalhuman.common.access.AccessApp;
import com.baidu.acg.piat.digitalhuman.common.utils.AccessSignUtil;

import java.util.HashMap;

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping({"/api/external/digitalhuman/v2/apps"})
public class AppV2Controller {

    private final AppService appService;

    @GetMapping("/{app_id}/tokens")
    public Response retrieveByIdAndEnable(
            @RequestAttribute("accountId") String accountId,
            @PathVariable(name = "app_id") String appId,
            @RequestParam(name = "expire") String expire) {

        AccessApp accessApp = appService.get(appId);
        if (accessApp == null || !accountId.equals(accessApp.getUserId())) {
            return Response.fail("app not found");
        }
        String appToken = AccessSignUtil.sign(accessApp.getAppKey(), accessApp.getAppId(), expire);
        return Response.success(new HashMap<String, String>() {{ put("appToken", appToken); }});
    }

}
