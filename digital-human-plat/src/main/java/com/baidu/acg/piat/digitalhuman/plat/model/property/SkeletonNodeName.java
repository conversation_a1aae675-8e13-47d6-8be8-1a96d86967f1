package com.baidu.acg.piat.digitalhuman.plat.model.property;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

public enum SkeletonNodeName {
    FACIAL_BROW("<PERSON>_<PERSON><PERSON>",
            "<PERSON>_<PERSON>row01_L",
            "<PERSON>_Brow01u_L",
            "J_Brow02_L",
            "J_Brow02u_L",
            "J_Brow03_L",
            "J_Brow03u_L",
            "J_Brow_L"),
    FACIAL_EYE("J_Eye01_L",
            "J_Eye01_s_L",
            "J_Eye02_L",
            "J_Eye02_s_L",
            "J_Eye03_<PERSON>",
            "J_Eye03_s_L",
            "J_Eye04_L",
            "J_Eye04_s_L",
            "J_EyePos_rz_L",
            "J_Eye_L",
            "J_Eye_r_L",
            "J_Eye_ts_L"),
    FACIAL_FACE("J_Mouth_tr",
            "J_MouthBase",
            "J_aomandible_L",
            "J_bomandible_L",
            "J_cheek_<PERSON>",
            "J_cheekbone_<PERSON>",
            "<PERSON>_<PERSON>",
            "J_depress_L",
            "J_double<PERSON>",
            "<PERSON>_face_<PERSON>",
            "J_fatchin_L",
            "J_hairbase",
            "J_headMid",
            "J_headUp",
            "J_head_s"),
    FACI<PERSON>_MOUTH("J_Mouth_L",
            "J_Mouth_d",
            "J_Mouth_d_m",
            "J_Mouth_d_out",
            "J_Mouth_d_out01",
            "J_Mouth_d_out_m",
            "J_Mouth_d_out_m01",
            "J_Mouth_s",
            "J_Mouth_u",
            "J_Mouth_u_m",
            "J_Mouth_u_out",
            "J_Mouth_u_out01",
            "J_Mouth_u_out_m",
            "J_Mouth_u_out_m01"),
    FACIAL_NOSE("J_NoseBase",
            "J_NoseBridge_s",
            "J_NoseBridge_tr",
            "J_NoseDown",
            "J_NoseWing_L",
            "J_Nose_rs",
            "J_Nose_t",
            "J_Nose_tip",
            "J_Nose_trs");

    private Set<String> nodeNames = new HashSet<>();

    private SkeletonNodeName(String... nodeNames){
        this.nodeNames.addAll(List.of(nodeNames));
    }

    public boolean contain(String nodeName) {
        return nodeNames.contains(nodeName);
    }
}
