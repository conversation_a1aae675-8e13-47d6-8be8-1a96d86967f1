package com.baidu.acg.piat.digitalhuman.plat.service;

import javax.transaction.Transactional;

import com.baidu.acg.piat.digitalhuman.common.entity.CharacterConfigRelatedModule;
import com.baidu.acg.piat.digitalhuman.common.model.PageResponse;
import com.baidu.acg.piat.digitalhuman.common.project.Project;
import com.baidu.acg.piat.digitalhuman.common.project.ProjectDelByNameReq;

import java.util.List;

/**
 * Created on 2020/4/21 18:35.
 *
 * <AUTHOR>
 */
public interface ProjectService {

    @Transactional
    Project create(Project project);

    void delete(String projectId);
    void deleteByName(ProjectDelByNameReq delByNameReq);

    Project detail(String projectId);

    /**
     * 查发布的版本
     * @param projectId
     * @return
     */
    Project detailOfPublishVersion(String projectId);

    Project getByUserIdNameAndVersion(String userId, String name, String version, int apiVersion);

    @Transactional
    Project update(String projectId, Project project);

    @Transactional
    Project publish(String projectId, Project project);

    @Transactional
    Project updateByUserIdAndName(String userId, String name, Project project);

    PageResponse<Project> list(String userId, int apiVersion, int pageNo, int pageSize);

    PageResponse<Project> listWithMaxProjectVersion(String userId, int apiVersion, int pageNo, int pageSize,
                                                    String visibleCharacters);

    List<Project> listAllWithMaxProjectVersion(int apiVersion);
    List<CharacterConfigRelatedModule> listByCharacterConfigIds(List<String> configIds, String userId, int apiVersion);

    PageResponse<Project> getListByUserIdNameAndVersion(String userId, String name, String projectVersion,
                                                        int apiVersion, int pageNo, int pageSize,
                                                        String visibleCharacters);
}
