package com.baidu.acg.piat.digitalhuman.plat.config;

import com.baidu.acg.piat.digitalhuman.common.userfigure.StarLightClient;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class StarLightConfigure {


    @Bean
    @ConfigurationProperties(prefix = "digitalhuman.starlight.client.config")
    public StarLightConfig starLightConfig(){
        return new StarLightConfig();
    }


    @Bean
    public StarLightClient userFigureClient(){
        var config = starLightConfig();
        return new StarLightClient(config.getBaseUrl());
    }

    @Data
    public static class StarLightConfig {
        public String baseUrl = "http://localhost:9090";
    }
}
