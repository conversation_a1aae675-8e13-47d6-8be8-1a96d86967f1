// Copyright (C) 2019 Baidu Inc. All rights reserved.
package com.baidu.acg.piat.digitalhuman.plat.exception;

import com.baidu.acg.piat.digitalhuman.common.exception.DigitalHumanCommonException;
import com.baidu.acg.piat.digitalhuman.common.exception.Error;
import com.baidu.acg.piat.digitalhuman.common.model.Response;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingPathVariableException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ResponseBody;

import java.sql.SQLException;
import java.util.List;

/**
 * ExceptionController
 *
 * <AUTHOR>
 * @since 2019-09-19
 */
@Slf4j
@ResponseBody
@ControllerAdvice
public class ExceptionHandler {

    private static final List<Integer> SHOW_ERROR_CODES =
            Lists.newArrayList(Error.CHARACTE_CONFIG_NOT_EXIST.getCode(),
            Error.INTELLIGENT_ANIMOJI_EMOTION_CONFIG_NOT_ENABLE.getCode(), Error.INTELLIGENT_ANIMOJI_EMOTION_CONFIG_INVALID.getCode());

    @org.springframework.web.bind.annotation.ExceptionHandler(value = Exception.class)
    public Response handle(Exception e) {
        // 不影响已有接口的ErrorCode
        if (e instanceof DigitalHumanCommonException && SHOW_ERROR_CODES.contains(((DigitalHumanCommonException) e).getCode())) {
            DigitalHumanCommonException exception = ((DigitalHumanCommonException) e);
            return Response.fail(((DigitalHumanCommonException) e).getCode(), exception.getMessage());
        }
        if (e instanceof DigitalHumanAccessException
                || e instanceof DigitalHumanCommonException
                || e instanceof MissingPathVariableException
                || e instanceof MissingServletRequestParameterException
                || e instanceof HttpMessageNotReadableException
                || e instanceof HttpRequestMethodNotSupportedException) {
            log.warn("Caught exception: ", e);
            return Response.fail(e.getMessage());
        }

        // 捕获sql相关异常
        if (e.getCause() != null && e.getCause().getCause() != null &&
                e.getCause().getCause() instanceof SQLException) {
            log.error("sql exception: ", e.getCause().getCause());
            return Response.fail(String.format("sql exception, root cause=%s", e.getCause().getCause().getMessage()));
        }

        // 捕获Hibernate Validator异常
        if (e instanceof MethodArgumentNotValidException) {
            MethodArgumentNotValidException exception = (MethodArgumentNotValidException) e;

            return Response.fail(String.format("validator exception, root cause=%s",
                    exception.getBindingResult().getFieldError().getDefaultMessage()));
        }

        log.error("Unexpected exception: ", e);
        return Response.fail(Error.INTERNAL_SERVER_ERROR.getMessage());
    }
}
