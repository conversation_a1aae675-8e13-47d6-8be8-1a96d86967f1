package com.baidu.acg.piat.digitalhuman.plat.controller;

import com.baidu.acg.piat.digitalhuman.common.model.PageResult;
import com.baidu.acg.piat.digitalhuman.common.model.Response;
import com.baidu.acg.piat.digitalhuman.common.statistic.CharacterPreferencesData;
import com.baidu.acg.piat.digitalhuman.common.statistic.CharacterResourceResponse;
import com.baidu.acg.piat.digitalhuman.common.statistic.CharacterUsageTrendData;
import com.baidu.acg.piat.digitalhuman.common.statistic.ProductionStatisticData;
import com.baidu.acg.piat.digitalhuman.common.statistic.SessionStatisticPageResult;
import com.baidu.acg.piat.digitalhuman.common.statistic.StatisticResponse;
import com.baidu.acg.piat.digitalhuman.common.common.PlatCode;
import com.baidu.acg.piat.digitalhuman.plat.service.StatisticService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.concurrent.ExecutionException;

@Slf4j
@RestController
@RequestMapping("/api/digitalhuman/v1/statistic")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class StatisticController {

    private final StatisticService statisticService;

    @GetMapping("/activeSession")
    public Response<StatisticResponse> activeSession(@RequestParam("appId") String appId,
                                                     @RequestParam("startInMs") long startInMs,
                                                     @RequestParam("endInMs") long endInMs) {
        return Response.success(statisticService.getActiveSession(appId, startInMs, endInMs));
    }

    @GetMapping("/totalSession")
    public Response<StatisticResponse> totalSession(@RequestParam("appId") String appId,
                                                    @RequestParam("startInMs") long startInMs,
                                                    @RequestParam("endInMs") long endInMs) {
        return Response.success(statisticService.getTotalSession(appId, startInMs, endInMs));
    }

    @GetMapping("/avgSessionTime")
    public Response<StatisticResponse> avgSessionTime(@RequestParam("appId") String appId,
                                                      @RequestParam("startInMs") long startInMs,
                                                      @RequestParam("endInMs") long endInMs) {
        return Response.success(statisticService.getAvgSessionDuration(appId, startInMs, endInMs));
    }

    @GetMapping("/totalDialog")
    public Response<StatisticResponse> totalDialog(@RequestParam("appId") String appId,
                                                   @RequestParam("startInMs") long startInMs,
                                                   @RequestParam("endInMs") long endInMs) {
        return Response.success(statisticService.getTotalDialog(appId, startInMs, endInMs));
    }

    @GetMapping("/sessionTrend")
    public Response<StatisticResponse> sessionTrend(@RequestParam("appId") String appId,
                                                    @RequestParam("startInMs") long startInMs,
                                                    @RequestParam("endInMs") long endInMs,
                                                    @RequestParam(value = "intervalInSeconds",
                                                            defaultValue = "3600") long intervalInSeconds) {
        return Response.success(statisticService.getSessionTrend(appId, startInMs, endInMs, intervalInSeconds));
    }

    @GetMapping("/characterResource")
    public Response<CharacterResourceResponse> characterResource(@RequestParam("platCode") PlatCode platCode,
                                                                 @RequestParam("accountId") String accountId,
                                                                 @RequestParam("roleLevel") int roleLevel,
                                                                 @RequestParam("accountVisibleCharacters")
                                                                 String accountVisibleCharacters) {
        return Response.success(statisticService.characterResource(platCode, accountId
                , accountVisibleCharacters, roleLevel));
    }

    @GetMapping("/characterUsageTrend")
    public Response<List<CharacterUsageTrendData>> characterUsageTrend(@RequestParam("platCode") PlatCode platCode,
                                                                       @RequestParam("accountId") String accountId,
                                                                       @RequestParam("roleLevel") int roleLevel,
                                                                       @RequestParam("accountVisibleCharacters")
                                                                       String accountVisibleCharacters,
                                                                       @RequestParam("startInMs") long startInMs,
                                                                       @RequestParam("endInMs") long endInMs,
                                                                       @RequestParam("intervalInSeconds")
                                                                       long intervalInSeconds) {
        return Response.success(statisticService.characterUsageTrend(platCode, accountId
                , roleLevel, accountVisibleCharacters, startInMs, endInMs, intervalInSeconds));
    }

    @GetMapping("/characterPreferences")
    public Response<List<CharacterPreferencesData>> characterPreferences(@RequestParam("platCode") PlatCode platCode,
                                                                         @RequestParam("accountId") String accountId,
                                                                         @RequestParam("roleLevel") int roleLevel,
                                                                         @RequestParam("accountVisibleCharacters")
                                                                         String accountVisibleCharacters,
                                                                         @RequestParam("accountMenus")
                                                                         List<String> accountMenus)
            throws ExecutionException {
        return Response.success(statisticService.characterPreferences(platCode, accountId
                , roleLevel, accountVisibleCharacters, accountMenus));
    }


    @GetMapping("/sessionService")
    public Response<SessionStatisticPageResult> sessionService(@RequestParam("platCode") PlatCode platCode,
                                                                     @RequestParam("accountId") String accountId,
                                                                     @RequestParam("roleLevel") int roleLevel,
                                                                     @RequestParam(value = "pageNo", defaultValue = "1")
                                                                     int pageNo,
                                                                     @RequestParam(value = "pageSize"
                                                                             , defaultValue = "20")
                                                                     int pageSize,
                                                                     @RequestParam("startInDate") int startInDate,
                                                                     @RequestParam("endInDate") int endInDate,
                                                                     @RequestParam(value = "orderBy"
                                                                             , defaultValue = "sessionTotalCount")
                                                                     String orderBy,
                                                                     @RequestParam(value = "order"
                                                                             , defaultValue = "desc")
                                                                     String order) {
        return Response.success(statisticService.sessionService(platCode, accountId
                , roleLevel, pageNo, pageSize, startInDate, endInDate, orderBy, order));
    }


    @GetMapping("/production")
    public Response<PageResult<ProductionStatisticData>> production(@RequestParam("platCode") PlatCode platCode,
                                                                    @RequestParam("accountId") String accountId,
                                                                    @RequestParam("roleLevel") int roleLevel,
                                                                    @RequestParam(value = "pageNo", defaultValue = "1")
                                                                    int pageNo,
                                                                    @RequestParam(value = "pageSize"
                                                                            , defaultValue = "20")
                                                                    int pageSize,
                                                                    @RequestParam("startInDate") int startInDate,
                                                                    @RequestParam("endInDate") int endInDate) {
        return Response.success(statisticService.production(platCode, accountId
                , roleLevel, pageNo, pageSize, startInDate, endInDate));
    }
}
