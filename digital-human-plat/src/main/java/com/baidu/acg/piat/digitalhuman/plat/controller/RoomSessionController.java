package com.baidu.acg.piat.digitalhuman.plat.controller;

import com.baidu.acg.piat.digitalhuman.common.model.PageResponse;
import com.baidu.acg.piat.digitalhuman.common.model.Response;
import com.baidu.acg.piat.digitalhuman.common.room.session.RoomSession;
import com.baidu.acg.piat.digitalhuman.common.session.SessionStatus;
import com.baidu.acg.piat.digitalhuman.plat.service.RoomSessionService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * Created on 2020/7/23 15:54.
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/api/digitalhuman/v1/room/session")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class RoomSessionController {

    private final RoomSessionService roomSessionService;

    @PostMapping
    public Response<RoomSession> create(@RequestBody RoomSession roomSession) {
        return Response.success(roomSessionService.create(RoomSession.builder()
                .sessionId(ObjectId.get().toHexString())
                .status(SessionStatus.OPEN)
                .appId(roomSession.getAppId())
                .appToken(roomSession.getAppToken())
                .projectId(roomSession.getProjectId())
                .roomId(roomSession.getRoomId())
                .roomName(roomSession.getRoomName())
                .build()));
    }

    @PutMapping("/{sessionId}")
    public Response<RoomSession> closeRoomSession(@PathVariable("sessionId") String sessionId) {
        return Response.success(roomSessionService.updateStatus(RoomSession.builder()
                .sessionId(sessionId)
                .status(SessionStatus.CLOSED)
                .build()));
    }

    @GetMapping("{sessionId}")
    public Response<RoomSession> get(@PathVariable("sessionId") String sessionId) {
        return Response.success(roomSessionService.detail(sessionId));
    }

    @GetMapping
    public PageResponse<RoomSession> listSessionsByRoomId(
            @RequestParam("roomId") String roomId,
            @RequestParam(value = "pageNo", defaultValue = "1") int pageNo,
            @RequestParam(value = "pageSize", defaultValue = "20") int pageSize) {
        return roomSessionService.listByRoomId(roomId, pageNo, pageSize);
    }

}
