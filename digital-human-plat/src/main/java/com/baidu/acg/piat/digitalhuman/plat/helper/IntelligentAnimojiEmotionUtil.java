package com.baidu.acg.piat.digitalhuman.plat.helper;


import com.baidu.acg.piat.digitalhuman.common.character.BaseCharacterInfo;
import com.baidu.acg.piat.digitalhuman.common.character.BehaviorPattern;
import com.baidu.acg.piat.digitalhuman.common.entity.CharacterModel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
public class IntelligentAnimojiEmotionUtil {

    public static BehaviorPattern.IntelligentAnimojiEmotion obtainValidAnimojiAndEmotion(CharacterModel characterModel
            , BehaviorPattern.IntelligentAnimojiEmotion intelligentAnimojiEmotion) {

        Set<String> characterAnimojis = CollectionUtils.emptyIfNull(characterModel.getAnimojiList())
                .stream().map(BaseCharacterInfo::getId).collect(Collectors.toSet());
        Set<String> characterA2aEmotions = CollectionUtils.emptyIfNull(characterModel.getA2aEmotionList())
                .stream().map(BaseCharacterInfo::getId).collect(Collectors.toSet());
        BehaviorPattern.IntelligentAnimojiEmotion newIntelligentAnimojiEmotion = new BehaviorPattern.IntelligentAnimojiEmotion();
        newIntelligentAnimojiEmotion.setAnimojiRules(Lists.newArrayList());
        newIntelligentAnimojiEmotion.setEmotionRules(Lists.newArrayList());
        newIntelligentAnimojiEmotion.setEnable(intelligentAnimojiEmotion.isEnable());
        if (null != intelligentAnimojiEmotion.getEmotionRules()) {
            intelligentAnimojiEmotion.getEmotionRules().forEach(emotionTag -> {
                List<String> emotions = Lists.newArrayList();
                emotionTag.getEmotions().forEach(emotion -> {
                    if (characterA2aEmotions.contains(emotion)) {
                        emotions.add(emotion);
                    }
                });
                if (!emotions.isEmpty()) {
                    BehaviorPattern.EmotionTag newEmotionTag = new BehaviorPattern.EmotionTag();
                    newEmotionTag.setEmotions(emotions);
                    newEmotionTag.setTag(emotionTag.getTag());
                    newIntelligentAnimojiEmotion.getEmotionRules().add(newEmotionTag);
                }
            });
        }

        if (null != intelligentAnimojiEmotion.getAnimojiRules()) {
            intelligentAnimojiEmotion.getAnimojiRules().forEach(animojiTag -> {
                List<String> animojis = Lists.newArrayList();
                animojiTag.getAnimojis().forEach(animoji -> {
                    if (characterAnimojis.contains(animoji)) {
                        animojis.add(animoji);
                    }
                });
                if (!animojis.isEmpty()) {
                    BehaviorPattern.AnimojiTag newAnimojiTag = new BehaviorPattern.AnimojiTag();
                    newAnimojiTag.setAnimojis(animojis);
                    newAnimojiTag.setTag(animojiTag.getTag());
                    newIntelligentAnimojiEmotion.getAnimojiRules().add(newAnimojiTag);
                }
            });
        }
        return newIntelligentAnimojiEmotion;
    }
}