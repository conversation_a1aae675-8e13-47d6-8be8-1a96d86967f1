package com.baidu.acg.piat.digitalhuman.plat.v2.controller;

import com.baidu.acg.dh.user.client.interceptor.RequestContextHolder;
import com.baidu.acg.piat.digitalhuman.common.utils.AuthUtil;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import javax.imageio.ImageIO;

import com.baidu.acg.piat.digitalhuman.common.background.SpecBackgroundRequest;
import com.baidu.acg.piat.digitalhuman.common.entity.CharacterModel;
import com.baidu.acg.piat.digitalhuman.common.model.PageResponse;
import com.baidu.acg.piat.digitalhuman.common.model.Response;
import com.baidu.acg.piat.digitalhuman.plat.service.CharacterService;

import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestAttribute;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping({"/api/external/digitalhuman/plat/v2/characters", "/api/digitalhuman/v2/characters",
        "/api/internal/digitalhuman/plat/v2/characters"})
public class CharacterV2Controller {

    private static final int API_VERSION = 2;

    private static final int SUPER_ADMINISTRATOR_ROLE_LEVEL = 3;

    private final CharacterService characterService;

    /**
     * 默认有效期1个小时
     */
    @Value("${digitalhuman.access.app.valid.time:1}")
    private int appValidTime;

    @PostMapping
    public Response<CharacterModel> create(@RequestBody CharacterModel character) {
        log.info("request create={}", character);
        if (RequestContextHolder.getRoleLevel() < SUPER_ADMINISTRATOR_ROLE_LEVEL) {
            return Response.fail(1001, "非平台管理员无权限访问此接口");
        }

        return Response.success(characterService.create(character, API_VERSION));
    }

    @GetMapping("/{id}")
    public Response<CharacterModel> retrieveById(@PathVariable String id) {
        log.info("request retrieveById={}", id);
        return Response.success(characterService.selectById(id));
    }

    @GetMapping("/type/{type}")
    public Response<CharacterModel> retrieveByType(@PathVariable String type) {
        CharacterModel characterModel = characterService.selectByType(type, API_VERSION);
        // TODO 后续如果多个接口需要做就抽下
        characterModel.setToken(AuthUtil.generateTokenByApp(characterModel.getAppId(),
                characterModel.getAppKey(), appValidTime));
        /* 后面留个内部模块使用,不对外开放
        characterModel.setAppId("");
        characterModel.setAppKey("");
         */
        return Response.success(characterModel);
    }

    /**
     * 专门给前端使用
     *
     * @param type
     * @return
     */
    @GetMapping("/publish/type/{type}")
    public Response<CharacterModel> retrieveByTypePublish(@PathVariable String type,
                                                          @RequestHeader(value = "Language", defaultValue = "") String language) {
        log.info("request retrieveByTypePublish type :{}, language: {}", type, language);
        CharacterModel characterModel = characterService.selectMultiLanguageByType(type, API_VERSION, language);
        // TODO 后续如果多个接口需要做就抽下
        characterModel.setToken(AuthUtil.generateTokenByApp(characterModel.getAppId(),
                characterModel.getAppKey(), appValidTime));
        characterModel.setAppId("");
        characterModel.setAppKey("");
        return Response.success(characterModel);
    }

    @GetMapping("/all")
    public Response<List<CharacterModel>> listAll(@RequestParam(required = false, defaultValue = "false")
                                                          boolean queryForLive,
                                                  @RequestParam(required = false, defaultValue = "true")
                                                          boolean queryForSce) {
        if (RequestContextHolder.getRoleLevel() < SUPER_ADMINISTRATOR_ROLE_LEVEL) {
            return Response.fail(1001, "非平台管理员无权限访问此接口");
        }
        return Response.success(characterService.listAll(queryForLive, queryForSce, API_VERSION));
    }

    /**
     * 分页查询人像类型；
     * 1.只能查询到用户有权限访问到的人像类型
     * 用户在1.0可见的人像通过tags中的sceVisibleCharacters字段控制，json字符串格式，为空则不限制
     * 用户在2.0可见的人像通过tags中的visibleCharacters字段控制，逗号隔开，为 'all' 则不限制
     * 2.queryForLive=true查询来源为直播时，只返回直播可见人像类型，否则返回全部
     * 3.queryForSce=true查询在场景人像列表中暴露的人像
     */
    @GetMapping
    public PageResponse<CharacterModel> list(@RequestAttribute(value = "tags", required = false)
                                                     Map<String, String> tags,
                                             @RequestParam(required = false, defaultValue = "1") int pageNo,
                                             @RequestParam(required = false, defaultValue = "10") int pageSize,
                                             @RequestParam(required = false, defaultValue = "false")
                                                     boolean queryForLive,
                                             @RequestParam(required = false, defaultValue = "false")
                                                     boolean queryForSce) {
        String userVisibleTypes = Optional.ofNullable(tags).orElse(new HashMap<>())
                .getOrDefault("visibleCharacters", "all");
        return characterService.query(pageNo, pageSize, API_VERSION, userVisibleTypes, queryForLive, queryForSce);
    }

    @PutMapping(value = "/type/{type}")
    public Response<CharacterModel> updateByType(@PathVariable(value = "type") String characterType,
                                                 @RequestBody CharacterModel request) {
        return Response.success(characterService.updateByType(characterType, request, API_VERSION));
    }

    @PutMapping(value = "/{id}")
    public Response<CharacterModel> updateById(@PathVariable(value = "id") String id,
                                               @RequestBody CharacterModel request) {
        return Response.success(characterService.updateById(id, request));
    }

    @PutMapping(value = "/tts-default/{apiversion}")
    public Response<Map<String, String>> updateById(@PathVariable(value = "apiversion") int apiVersion,
                                                    @RequestBody HashMap<String, String> ttsMap) {
        return Response.success(characterService.batchUpdateTts(ttsMap, apiVersion));
    }

    @DeleteMapping(value = "/type/{type}")
    public Response<Void> deleteByType(@PathVariable(value = "type") String type) {
        characterService.deleteByType(type);
        return Response.success(null);
    }

    @DeleteMapping(value = "/{id}")
    public Response<Void> deleteById(@PathVariable(value = "id") String id) {
        characterService.deleteById(id);
        return Response.success(null);
    }

    @ResponseBody
    @PutMapping(value = "/{type}/preview", produces = MediaType.IMAGE_JPEG_VALUE)
    public byte[] preview(
            @PathVariable(value = "type") String type,
            @RequestBody SpecBackgroundRequest specBackgroundRequest
    ) throws IOException {
        ByteArrayOutputStream result = new ByteArrayOutputStream();

        var image = characterService.preview(type, specBackgroundRequest.getBackgroundId(), API_VERSION);

        ImageIO.write(image, "jpg", result);
        return result.toByteArray();
    }
}
