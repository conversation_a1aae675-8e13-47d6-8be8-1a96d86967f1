package com.baidu.acg.piat.digitalhuman.plat.model.property;

public enum PropertyType {
    TOP("<character><property><style>${VAL}</style></property></character>"),
    BOTTOM("<character><property><style>${VAL}</style></property></character>"),
    BODY("<character><property><style>${VAL}</style></property></character>"),
    SUIT("<character><property><style>${VAL}</style></property></character>"),
    HAIR("<character><property><style>${VAL}</style></property></character>"),
    SHOES("<character><property><style>${VAL}</style></property></character>"),
    ACCESSORY("<character><property><style>${VAL}</style></property></character>"),
    FACIAL_EYE("<character><property><style>${VAL}</style></property></character>"),
    FACIAL_BROW("<character><property><style>${VAL}</style></property></character>"),
    FACIAL_FACE("<character><property><style>${VAL}</style></property></character>"),
    FACIAL_NOSE("<character><property><style>${VAL}</style></property></character>"),
    FACIAL_MOUTH("<character><property><style>${VAL}</style></property></character>"),
    EXPRESSION("<animoji><id>${VAL}</id></animoji>"),
    ANIMOJI("<animoji><id>${VAL}</id></animoji>"),
    EFFECT("<character><property><style>${VAL1}</style><state>${VAL2}</state></property></character>"),
    SOUND(""),
    TTS(""),
    SIZE("<character><property><style>${VAL}</style></property></character>"),
    BACKGROUND("<display><background><type>image</type><source>http</source>" +
            "<value>${VAL}</value></background></display>"),
    ADDITIONAL("<character><property><style>${VAL}</style></property></character>");
    private String dhDrml;

    PropertyType(String dhDrml) {
        this.dhDrml = dhDrml;
    }

    public String getDhDrml() {
        return dhDrml;
    }
}
