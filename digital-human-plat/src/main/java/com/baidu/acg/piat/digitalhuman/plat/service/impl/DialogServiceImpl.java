package com.baidu.acg.piat.digitalhuman.plat.service.impl;

import com.baidu.acg.piat.digitalhuman.common.exception.DigitalHumanCommonException;
import com.baidu.acg.piat.digitalhuman.common.util.DomUtil;
import com.baidu.acg.piat.digitalhuman.plat.service.SessionStatisticService;
import com.google.common.collect.Lists;
import io.vavr.control.Try;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.bson.types.ObjectId;
import org.codehaus.jettison.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort.Direction;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

import com.baidu.acg.piat.digitalhuman.common.model.MarkIdWrapper;
import com.baidu.acg.piat.digitalhuman.common.model.PageResponse;
import com.baidu.acg.piat.digitalhuman.common.model.PageResult;
import com.baidu.acg.piat.digitalhuman.common.room.Room;
import com.baidu.acg.piat.digitalhuman.common.session.Dialog;
import com.baidu.acg.piat.digitalhuman.plat.dao.DialogRepository;
import com.baidu.acg.piat.digitalhuman.plat.model.room.session.DialogModel;
import com.baidu.acg.piat.digitalhuman.plat.service.DialogService;
import com.baidu.acg.piat.digitalhuman.plat.service.RoomService;


/**
 * The impl of {@link DialogService}.
 *
 * <AUTHOR> Mao (<EMAIL>)
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class DialogServiceImpl implements DialogService {

    private static final int MAX_QUERY_SIZE = 1000;

    private final DialogRepository dialogRepository;

    private final RoomService roomService;

    private final SessionStatisticService sessionStatisticService;

    @Override
    public Dialog create(Dialog one) {
        var model = DialogModel.builder()
                .dialogId(ObjectId.get().toHexString())
                .appId(one.getAppId())
                .sessionId(one.getSessionId())
                .roomId(one.getRoomId())
                .speaker(one.getSpeaker())
                .audioId(one.getAudioId())
                .type(one.getType())
                .content(one.getContent())
                .drmlContent(one.getDrmlContent())
                .timestamp(one.getTimestamp())
                .build();
        model = dialogRepository.save(model);
        sessionStatisticService.statisticDialogCreate(model.getTimestamp(), model.getAppId());
        return model.toDialog();
    }

    @Override
    public PageResponse<Dialog> listBySessionId(String sessionId, int pageNo, int pageSize) {
        PageRequest pageRequest = PageRequest.of(pageNo - 1, pageSize, Direction.DESC, "timestamp");
        var page = dialogRepository.findBySessionIdOrderByCreateTimeDesc(sessionId, pageRequest);
        return PageResponse.<Dialog>builder()
                .page(PageResult.<Dialog>builder()
                        .pageNo(pageNo)
                        .pageSize(pageSize)
                        .totalCount(page.getTotalElements())
                        .result(page.getContent().stream().map(DialogModel::toDialog).collect(Collectors.toList()))
                        .build())
                .build();
    }

    /**
     * 根据房间名称和时间戳小于的对话列表分页查询
     *
     * @param roomName     房间名称
     * @param startTimeMs  开始时间戳，毫秒单位
     * @param sort         排序字段名，可选值为"desc"或"asc"（默认值）
     * @param pageNo       当前页码，从1开始计数
     * @param pageSize     分页大小
     * @return 符合条件的对话列表分页响应
     */
    @Override
    public PageResponse<Dialog> listByRoomNameAndTimestampLessThan(String roomName, String appId, long startTimeMs, String sort, int pageNo, int pageSize) {
        PageRequest pageRequest = PageRequest.of(pageNo - 1, pageSize, Direction.DESC, "timestamp");

        var room = roomService.getByAppIdAndRoomName(appId, roomName);
        if (room.getId() == null) {
            throw new DigitalHumanCommonException("查询不到room信息，appId=" + appId + ",roomName=" + roomName);
        }

        var page =
            "asc".equals(sort)
                    ? dialogRepository.findByRoomIdAndTimestampLessThanOrderByTimestampAsc(room.getId(), startTimeMs, pageRequest)
                    : dialogRepository.findByRoomIdAndTimestampLessThanOrderByTimestampDesc(room.getId(), startTimeMs, pageRequest) ;
        List<Dialog> dialogs =page.getContent().stream().map(DialogModel::toDialog).collect(Collectors.toList());

        dialogs.forEach((key) ->
                key.setWidget(handleWidget(key.getDrmlContent()))
        );

        return PageResponse.<Dialog>builder()
                .page(PageResult.<Dialog>builder()
                        .pageNo(pageNo)
                        .pageSize(pageSize)
                        .totalCount(page.getTotalElements())
                        .result(dialogs)
                        .build())
                .build();
    }

    // 历史信息需要把drmlContent字段里包含的widget信息转成json给到前端，这里处理这个信息，一般widget会被client包裹
    public String handleWidget(String xml) {
        try {
            var drmljson = DomUtil.xml2Json(xml, false);
            JSONObject jsonObject = new JSONObject(drmljson);
            var widgetClient =  jsonObject.optJSONObject("client");
            return widgetClient.toString();

        } catch (Throwable t) {
            log.error("not widget,xml={} ",  xml);
        }
        return "";
    }

    @Override
    public PageResponse<Dialog> listByRoomId(String roomId, int pageNo, int pageSize) {
        PageRequest pageRequest = PageRequest.of(pageNo - 1, pageSize, Direction.DESC, "timestamp");
        var page = dialogRepository.findByRoomIdOrderByCreateTimeDesc(roomId, pageRequest);
        return PageResponse.<Dialog>builder()
                .page(PageResult.<Dialog>builder()
                        .pageNo(pageNo)
                        .pageSize(pageSize)
                        .totalCount(page.getTotalElements())
                        .result(page.getContent().stream().map(DialogModel::toDialog).collect(Collectors.toList()))
                        .build())
                .build();
    }

    @Override
    public PageResponse<Dialog> listByAppIdAndRoomName(String appId, String roomName, int pageNo, int pageSize) {
        List<String> roomIds = Lists.newArrayList();
        if (StringUtils.isNotBlank(roomName)) {
            var room = roomService.getByAppIdAndRoomName(appId, roomName);
            if (room != null) {
                roomIds.add(room.getId());
            }
        } else {
            roomIds = roomService.listByAppId(appId, 1, MAX_QUERY_SIZE).getPage().getResult().stream()
                    .map(Room::getId).collect(Collectors.toList());
        }
        PageRequest pageRequest = PageRequest.of(pageNo - 1, pageSize, Direction.DESC, "timestamp");
        var page = dialogRepository.findByRoomIdIn(roomIds, pageRequest);
        return PageResponse.<Dialog>builder()
                .page(PageResult.<Dialog>builder()
                        .pageNo(pageNo)
                        .pageSize(pageSize)
                        .totalCount(page.getTotalElements())
                        .result(page.getContent().stream().map(DialogModel::toDialog).collect(Collectors.toList()))
                        .build())
                .build();
    }

    @Override
    public MarkIdWrapper<Dialog> listByAppIdAndRoomNameAndMarkId(String appId, String roomName, String markId,
                                                                 Long startTimeMs, Integer maxKeys) {
        var room = roomService.getByAppIdAndRoomName(appId, roomName);
        if (room == null) {
            return MarkIdWrapper.<Dialog>builder().maxKeys(maxKeys).markId(markId).build();
        }
        List<DialogModel> dialogModelList = Lists.newArrayList();
        Long id = Try.of(() -> Long.valueOf(markId)).getOrNull();
        if (id == null) {
            dialogModelList = dialogRepository.findByRoomIdOrderByIdDesc(
                    room.getId(), startTimeMs, maxKeys);
        } else {
            dialogModelList = dialogRepository.findByRoomIdAndIdOrderByIdDesc(
                    room.getId(), startTimeMs, maxKeys, id);
        }

        List<Dialog> dialogs = dialogModelList.stream().sorted().map(DialogModel::toDialog)
                .collect(Collectors.toList());
        return MarkIdWrapper.<Dialog>builder()
                .markId(CollectionUtils.isEmpty(dialogs) ? ""
                        : String.valueOf(dialogModelList.get(dialogModelList.size() - 1).getId()))
                .maxKeys(maxKeys)
                .results(dialogs).build();
    }

    @Override
    public Long countByAppIdAndTimestampBetween(String appId, Long startTime, Long endTime) {
        return dialogRepository.countAllByAppIdAndTimestampBetween(appId, startTime, endTime).orElse(0L);
    }
}
