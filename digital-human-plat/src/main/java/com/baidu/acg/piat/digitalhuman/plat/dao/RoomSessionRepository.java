package com.baidu.acg.piat.digitalhuman.plat.dao;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.data.repository.query.Param;

import java.util.List;
import java.util.Optional;

import com.baidu.acg.piat.digitalhuman.plat.model.room.session.RoomSessionModel;

/**
 * Created on 2020/7/23 15:58.
 *
 * <AUTHOR>
 */
public interface RoomSessionRepository extends PagingAndSortingRepository<RoomSessionModel, Long> {

    Page<RoomSessionModel> findByRoomIdOrderByCreateTimeDesc(String roomId, Pageable pageable);

    RoomSessionModel findBySessionId(String sessionId);

    Optional<Long> countByAppIdAndStatusName(String appId, String statusName);

    List<RoomSessionModel> findByTimestampBetween(Long startTime, Long endTime);


    Optional<Long> countByAppIdAndTimestampBetween(String appId, Long startTime, Long endTime);

    @Query(value = "SELECT AVG(duration) FROM room_session "
            + "where app_id = :appId and time_stamp > :startTime and time_stamp < :endTime", nativeQuery = true)
    Optional<Long> aggregateAvgDurationByAppIdAndTimestampBetween(
            @Param("appId") String appId, @Param("startTime") Long startTime, @Param("endTime") Long endTime);

}
