package com.baidu.acg.piat.digitalhuman.plat.rpc;

import java.io.IOException;
import java.util.concurrent.TimeUnit;
import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;

import com.baidu.acg.piat.digitalhuman.plat.config.PlatformGrpcConfigure;
import com.baidu.acg.piat.digitalhuman.plat.rpc.platform.PlatformRoomSessionGrpcService;
import io.grpc.Server;
import io.grpc.ServerBuilder;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.stereotype.Service;

/**
 * Platform grpc server.
 *
 * <AUTHOR> (<EMAIL>)
 */
@Slf4j
@Service
@ConditionalOnExpression("${digitalhuman.grpc.enabled:true}")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class PlatformGrpcServer {

    private final PlatformGrpcConfigure platformGrpcConfigure;

    private final PlatformRoomSessionGrpcService platformRoomSessionGrpcService;

    private Server server;

    @PostConstruct
    public void init() throws IOException {
        server = ServerBuilder.forPort(platformGrpcConfigure.getServerConfig().getPort())
                .addService(platformRoomSessionGrpcService)
                .build();
        log.info("Digital human platform grpc server initialized at port {}",
                platformGrpcConfigure.getServerConfig().getPort());

        start();
    }

    private void start() throws IOException {
        server.start();
        log.info("Digital human platform grpc server started");
    }

    @PreDestroy
    public void destroy() throws InterruptedException {
        if (server != null) {
            server.shutdown().awaitTermination(platformGrpcConfigure.getServerConfig().getTerminateAwaitSeconds(),
                    TimeUnit.SECONDS);
        }
        log.info("Digital human platform grpc server closed");
    }
}
