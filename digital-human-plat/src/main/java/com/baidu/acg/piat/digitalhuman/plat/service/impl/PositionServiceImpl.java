package com.baidu.acg.piat.digitalhuman.plat.service.impl;

import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

import com.baidu.acg.piat.digitalhuman.common.exception.DigitalHumanCommonException;
import com.baidu.acg.piat.digitalhuman.common.model.PageResponse;
import com.baidu.acg.piat.digitalhuman.common.model.PageResult;
import com.baidu.acg.piat.digitalhuman.common.position.PositionVo;
import com.baidu.acg.piat.digitalhuman.plat.dao.PositionRepository;
import com.baidu.acg.piat.digitalhuman.plat.model.position.PositionModel;
import com.baidu.acg.piat.digitalhuman.plat.service.PositionService;

/**
 * Created on 2021/8/6 11:40 上午
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class PositionServiceImpl implements PositionService {

    private final PositionRepository repository;

    @Override
    public PositionVo create(PositionVo request) {
        log.debug("Start to create position = {}", request);

        PositionModel positionModel = PositionModel.builder()
                .positionId(ObjectId.get().toHexString())  // 根据时间生成16进制的id
                .userId(request.getUserId())
                .name(request.getName())
                .content(request.getContent())
                .width(request.getWidth())
                .height(request.getHeight())
                .build();
        positionModel = repository.save(positionModel);
        return positionModel.toPositionVo();
    }

    @Override
    public void delete(String positionId) {
        log.debug("Start to delete positionId = {}", positionId);

        repository.deleteByPositionId(positionId);
    }

    @Override
    public PositionVo update(String positionId, PositionVo request) {
        log.debug("Start to update positionId = {}", positionId);

        PositionModel positionModel = getPositionModelById(positionId);

        filterEmptyProperties(request, positionModel);

        return repository.save(positionModel).toPositionVo();
    }

    private void filterEmptyProperties(PositionVo request, PositionModel positionModel) {
        if (StringUtils.isNotEmpty(request.getName())) {
            positionModel.setName(request.getName());
        }
        if (StringUtils.isNotEmpty(request.getContent())) {
            positionModel.setContent(request.getContent());
        }
        if (Objects.nonNull(request.getWidth())) {
            positionModel.setWidth(request.getWidth());
        }
        if (Objects.nonNull(request.getHeight())) {
            positionModel.setHeight(request.getHeight());
        }
    }

    private PositionModel getPositionModelById(String positionId) {
        var positionById = repository.findByPositionId(positionId);

        if (positionById.isEmpty()) {
            throw new DigitalHumanCommonException("Position not existed");
        }
        return positionById.get();
    }

    @Override
    public PositionVo detail(String positionId) {
        log.debug("Start to get position detail={}", positionId);
        var positionModelById = getPositionModelById(positionId);
        return positionModelById.toPositionVo();
    }

    @Override
    public PageResponse<PositionVo> listByUserId(String userId, Integer pageNo, Integer pageSize) {
        log.debug("Start to list position, userId={}, pageNo={}, pageSize={}", userId, pageNo, pageSize);

        PageRequest pageRequest = PageRequest.of(pageNo - 1, pageSize);
        var positionModelPage = repository.findByUserId(
                userId, pageRequest);

        List<PositionVo> positionVoList = Lists.newArrayList();
        positionModelPage.forEach(m -> positionVoList.add(m.toPositionVo()));

        return PageResponse.<PositionVo> builder()
                .page(PageResult.<PositionVo> builder()
                        .pageNo(pageNo)
                        .pageSize(pageSize)
                        .totalCount(positionModelPage.getTotalElements())
                        .result(positionVoList).build())
                .build();
    }

}