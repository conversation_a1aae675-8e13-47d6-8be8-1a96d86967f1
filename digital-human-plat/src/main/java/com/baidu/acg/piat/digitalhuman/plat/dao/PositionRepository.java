package com.baidu.acg.piat.digitalhuman.plat.dao;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.repository.PagingAndSortingRepository;

import java.util.Optional;

import javax.transaction.Transactional;

import com.baidu.acg.piat.digitalhuman.plat.model.position.PositionModel;

/**
 * Created on 2021/8/6 11:43 上午
 *
 * <AUTHOR>
 */

public interface PositionRepository extends PagingAndSortingRepository<PositionModel, Long> {
    @Transactional
    void deleteByPositionId(String positionId);

    Optional<PositionModel> findByPositionId(String positionId);

    Page<PositionModel> findByUserId(String userId, Pageable pageable);

}
