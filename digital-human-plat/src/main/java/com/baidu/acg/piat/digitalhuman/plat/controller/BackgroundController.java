package com.baidu.acg.piat.digitalhuman.plat.controller;

import com.baidu.acg.piat.digitalhuman.common.background.BackgroundImage;
import com.baidu.acg.piat.digitalhuman.common.background.BackgroundUploadRequest;
import com.baidu.acg.piat.digitalhuman.common.model.PageResponse;
import com.baidu.acg.piat.digitalhuman.common.model.Response;
import com.baidu.acg.piat.digitalhuman.plat.service.BackgroundService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */

@Slf4j
@RestController
@RequestMapping("/api/digitalhuman/v1/background")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class BackgroundController {

    private final BackgroundService backgroundImageService;

    @GetMapping("{id}")
    public Response<BackgroundImage> get(@PathVariable(value = "id") String id) {
        var result = backgroundImageService.get(id);
        if (result.isEmpty()) {
            return Response.success(null);
        } else {
            return Response.success(result.get());
        }
    }

    @DeleteMapping("{id}")
    public Response<Void> delete(@PathVariable(value = "id") String id) {
        backgroundImageService.delete(id);
        return Response.success(null);
    }

    @PostMapping
    public Response<BackgroundImage> create(@RequestBody BackgroundUploadRequest request) {
        return Response.success(backgroundImageService.create(request));
    }

    @GetMapping
    public PageResponse<BackgroundImage> findByUserId(
            @RequestParam String userId,
            @RequestParam(required = false, defaultValue = "1") int pageNo,
            @RequestParam(required = false, defaultValue = "10") int pageSize) {
        var pageRequest = PageRequest.of(pageNo - 1, Math.min(pageSize, 50));
        var result = backgroundImageService.selectByUserId(userId, pageRequest);
        return PageResponse.success(
                result.getPageable().getPageNumber() + 1,
                result.getPageable().getPageSize(),
                result.getTotalElements(),
                result.getContent());
    }

}
