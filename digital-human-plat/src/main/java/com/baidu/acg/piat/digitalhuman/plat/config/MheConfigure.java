package com.baidu.acg.piat.digitalhuman.plat.config;

import com.baidu.acg.piat.digitalhuman.plat.client.MheStudioClient;
import com.baidu.acg.piat.digitalhuman.plat.v2.configure.CharacterConfigRelatedServiceConfig;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Created on 2021/1/28 14:41.
 *
 * <AUTHOR>
 */
@Configuration
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class MheConfigure {

    private final CharacterConfigRelatedServiceConfig characterConfigRelatedServiceConfig;

    @Bean
    public MheStudioClient mheStudioClient() {
        return new MheStudioClient(characterConfigRelatedServiceConfig.getMheBaseUrl());
    }

}
