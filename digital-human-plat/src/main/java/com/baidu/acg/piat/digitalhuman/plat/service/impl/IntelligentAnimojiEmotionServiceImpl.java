package com.baidu.acg.piat.digitalhuman.plat.service.impl;

import com.baidu.acg.piat.digitalhuman.common.character.BehaviorPattern;
import com.baidu.acg.piat.digitalhuman.common.entity.CharacterConfig;
import com.baidu.acg.piat.digitalhuman.common.exception.DigitalHumanCommonException;
import com.baidu.acg.piat.digitalhuman.common.exception.Error;
import com.baidu.acg.piat.digitalhuman.common.intelligentanimojiemotion.IntelligentAnimojiEmotionRequest;
import com.baidu.acg.piat.digitalhuman.common.intelligentanimojiemotion.IntelligentAnimojiEmotionResponse;
import com.baidu.acg.piat.digitalhuman.common.intelligentanimojiemotion.Text2GestureRequest;
import com.baidu.acg.piat.digitalhuman.common.intelligentanimojiemotion.Text2GestureResponse;
import com.baidu.acg.piat.digitalhuman.common.utils.JsonUtil;
import com.baidu.acg.piat.digitalhuman.plat.client.Text2GestureClient;
import com.baidu.acg.piat.digitalhuman.plat.dao.CharacterRepository;
import com.baidu.acg.piat.digitalhuman.plat.service.IntelligentAnimojiEmotionService;
import com.baidu.acg.piat.digitalhuman.plat.v2.repository.CharacterConfigRepository;
import io.vavr.control.Try;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@Slf4j
@Service
public class IntelligentAnimojiEmotionServiceImpl implements IntelligentAnimojiEmotionService {

    @Autowired
    private CharacterConfigRepository characterConfigRepository;
    @Autowired
    private Text2GestureClient text2GestureClient;
    @Autowired
    private CharacterRepository characterRepository;

    @Override
    public IntelligentAnimojiEmotionResponse text2Drml(IntelligentAnimojiEmotionRequest request) {
        BehaviorPattern behaviorPattern = null;
        if (StringUtils.isNotEmpty(request.getCharacterImage())) {
            var characterModel = characterRepository.findByType(request.getCharacterImage());
            if (characterModel.isEmpty()) {
                log.error("character model not exist, get AnimojiEmotionConfig failed");
                throw new DigitalHumanCommonException(Error.CHARACTER_META_NOT_EXIST.getCode()
                        , Error.CHARACTER_META_NOT_EXIST.getMessage());
            }
            behaviorPattern = Try.of(() -> {
                return BehaviorPattern.fromFlatJson(characterModel.get(0).getAnimojiEmotionConfig());
            }).getOrNull();
        } else {
            Optional<CharacterConfig> characterConfig = characterConfigRepository.findByConfigId(request.getCharacterConfigId());
            if (characterConfig.isEmpty()) {
                throw new DigitalHumanCommonException(Error.CHARACTE_CONFIG_NOT_EXIST.getCode()
                        , Error.CHARACTE_CONFIG_NOT_EXIST.getMessage());
            }
            behaviorPattern = Try.of(() -> {
                return JsonUtil.readValue(characterConfig.get().getBehaviorPatternConfig(), BehaviorPattern.class);
            }).getOrNull();
        }
        log.debug("behaviorPattern ={}", behaviorPattern);
        if (null == behaviorPattern || null == behaviorPattern.getIntelligentAnimojiEmotion()
                || (CollectionUtils.isEmpty(behaviorPattern.getIntelligentAnimojiEmotion().getAnimojiRules())
                && CollectionUtils.isEmpty(behaviorPattern.getIntelligentAnimojiEmotion().getEmotionRules()))) {
            throw new DigitalHumanCommonException(Error.INTELLIGENT_ANIMOJI_EMOTION_CONFIG_INVALID.getCode()
                    , Error.INTELLIGENT_ANIMOJI_EMOTION_CONFIG_INVALID.getMessage());
        }

        if ((!behaviorPattern.getIntelligentAnimojiEmotion().isEnable()) && StringUtils.isEmpty(request.getCharacterImage())) {
            throw new DigitalHumanCommonException(Error.INTELLIGENT_ANIMOJI_EMOTION_CONFIG_NOT_ENABLE.getCode(),
                    Error.INTELLIGENT_ANIMOJI_EMOTION_CONFIG_NOT_ENABLE.getMessage());
        }

        Map<String, List<String>> emotionMap = new HashMap<>();
        Map<String, List<String>> animojisMap = new HashMap<>();
        CollectionUtils.emptyIfNull(behaviorPattern.getIntelligentAnimojiEmotion().getAnimojiRules()).forEach(item -> {
            animojisMap.put(item.getTag(), item.getAnimojis());
        });
        CollectionUtils.emptyIfNull(behaviorPattern.getIntelligentAnimojiEmotion().getEmotionRules()).forEach(item -> {
            emotionMap.put(item.getTag(), item.getEmotions());
        });
        // 请求text2gesture服务
        Text2GestureRequest text2GestureRequest = Text2GestureRequest.builder()
                .text(request.getText()).build();
        Text2GestureResponse text2GestureResponse =
                text2GestureClient.text2Gesture(text2GestureRequest);
        return buildDrmlFromText2Gesture(request,
                text2GestureResponse, animojisMap, emotionMap, true,
                request.isInterruptible());

    }

    public IntelligentAnimojiEmotionResponse buildDrmlFromText2Gesture(
            IntelligentAnimojiEmotionRequest request, Text2GestureResponse text2GestureResponse,
            Map<String, List<String>> animojisMap,
            Map<String, List<String>> emotionsMap, boolean random,
            boolean interruptible) {
        IntelligentAnimojiEmotionResponse response = IntelligentAnimojiEmotionResponse.builder()
                .requestId(request.getRequestId())
                .emotion(Lists.newArrayList()).action(Lists.newArrayList())
                .wordIndexAction(Lists.newArrayList()).wordIndexEmotion(Lists.newArrayList())
                .build();
        // 6个字延迟1s
        int words = 6;
        // 相距多少个字忽略以内
        int ingoreWords = 20;
        String text = request.getText();
        StringBuilder drml = new StringBuilder();

        String tmp = String.format("<speak><interruptible>%s</interruptible><content>%s</content"
                + "></speak>", interruptible, text);
        drml.append(tmp);

        int lastEmotionIndex = -1;
        int emotionCount = 0;
        int textLen = text.length();
        if (text2GestureResponse.getEmotion() != null && emotionsMap != null) {
            for (int i = 0; i < text2GestureResponse.getEmotion().size(); i++) {
                String emotionTag = text2GestureResponse.getEmotion().get(i);

                int emotionIndex = text2GestureResponse.getWordIndexEmotion().get(i);
                // 6个字delay 1s;
                int delay = emotionIndex / words * 1000;
                if (emotionsMap.containsKey(emotionTag) && (emotionIndex - lastEmotionIndex > ingoreWords
                        || lastEmotionIndex == -1)) {
                    if (lastEmotionIndex == emotionIndex) {
                        continue;
                    }
                    if (lastEmotionIndex != -1) {
                        // 动作4s
                        int diffSeconds = (emotionIndex - lastEmotionIndex) / words;
                        if (diffSeconds - 4 <= 0) {
                            delay = 0;
                        } else {
                            delay = (diffSeconds - 4) * 1000;
                        }
                    }
                    lastEmotionIndex = emotionIndex;
                    List<String> emotionList = emotionsMap.get(emotionTag);
                    int index = random ? (int) (Math.random() * emotionList.size()) : 0;
                    tmp = String.format("<a2a_emotion delay=\"%d\" duration=\"2000\"><id>%s</id></a2a_emotion>",
                            delay, emotionList.get(index));
                    emotionCount++;
                    drml.append(tmp);
                    response.getEmotion().add(emotionList.get(index));
                    response.getWordIndexEmotion().add(emotionIndex);
                }
            }
        }

        int lastActionIndex = -1;
        int actionCount = 0;
        if (text2GestureResponse.getAction() != null && animojisMap != null) {
            for (int i = 0; i < text2GestureResponse.getAction().size(); i++) {
                String actionTag = text2GestureResponse.getAction().get(i);
                int actionIndex = text2GestureResponse.getWordIndexAction().get(i);
                // 6个字delay 1s;
                int delay = actionIndex / words * 1000;
                // 太靠近后面不做动作, 大概2s;
                if (textLen - actionIndex < 10) {
                    continue;
                }
                if (animojisMap.containsKey(actionTag) && (actionIndex - lastActionIndex > ingoreWords
                        || lastActionIndex == -1)) {

                    // 防止两个位置插入一个动作
                    if (lastActionIndex == actionIndex) {
                        continue;
                    }
                    if (lastActionIndex != -1) {
                        // 假设动作4s
                        int diffSeconds = (actionIndex - lastActionIndex) / words;
                        if (diffSeconds - 4 <= 0) {
                            delay = 0;
                        } else {
                            delay = (diffSeconds - 4) * 1000;
                        }
                    }
                    lastActionIndex = actionIndex;

                    actionCount++;
                    List<String> animojiList = animojisMap.get(actionTag);
                    int index = random ? (int) (Math.random() * animojiList.size()) : 0;
                    tmp = String.format("<animoji delay=\"%d\"><id>%s</id></animoji>",
                            delay, animojiList.get(index));
                    drml.append(tmp);
                    response.getAction().add(animojiList.get(index));
                    response.getWordIndexAction().add(actionIndex);
                }
            }
        }

        if (drml.length() > 0) {
            drml.insert(0, "<fusion>");
            drml.append("</fusion>");
        }

        if (!interruptible) {
            drml.insert(0, "<speak interruptible=\"false\">");
        } else {
            drml.insert(0, "<speak interruptible=\"true\">");
        }
        drml.append("</speak>");

        String drmlStr = drml.toString();

        if (actionCount >= 2) {
            drmlStr = drmlStr.replaceAll("<animoji", "<animojis");
            drmlStr = drmlStr.replaceAll("</animoji>", "</animojis>");
        }

        if (emotionCount >= 2) {
            drmlStr = drmlStr.replaceAll("<a2a_emotion", "<a2a_emotions");
            drmlStr = drmlStr.replaceAll("</a2a_emotion>", "</a2a_emotions>");
        }
        response.setDrml(drmlStr);
        return response;
    }
}
