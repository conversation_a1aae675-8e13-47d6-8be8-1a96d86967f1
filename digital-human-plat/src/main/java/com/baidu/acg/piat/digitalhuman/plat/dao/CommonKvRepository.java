// Copyright (C) 2019 Baidu Inc. All rights reserved.
package com.baidu.acg.piat.digitalhuman.plat.dao;

import org.springframework.data.repository.CrudRepository;

import java.util.List;
import java.util.Optional;

import javax.transaction.Transactional;

import com.baidu.acg.piat.digitalhuman.plat.model.CommonKvModel;

/**
 * CommonKvRepository
 *
 * <AUTHOR>
 * @since 2021/08/09
 */
public interface CommonKvRepository extends CrudRepository<CommonKvModel, Long> {

    Optional<CommonKvModel> findByUserIdAndKey(String userId, String key);

    List<CommonKvModel> findAllByUserId(String userId);

    List<CommonKvModel> findAllByUserIdAndKeyIn(String userId, List<String> keyList);

    @Transactional
    void deleteByUserIdAndKey(String userId, String key);

}
