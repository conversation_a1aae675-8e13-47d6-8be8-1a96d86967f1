package com.baidu.acg.piat.digitalhuman.plat.model.property.impl;

import com.baidu.acg.piat.digitalhuman.common.exception.DigitalHumanCommonException;
import com.baidu.acg.piat.digitalhuman.common.utils.ZipFileUtil;
import com.baidu.acg.piat.digitalhuman.plat.model.property.FileVerification;
import com.baidu.acg.piat.digitalhuman.plat.model.property.PropertyFileResponse;
import com.baidu.acg.piat.digitalhuman.plat.model.property.PropertyType;
import io.vavr.Tuple2;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Enumeration;
import java.util.List;
import java.util.zip.ZipEntry;
import java.util.zip.ZipFile;

@Slf4j
@Component
@RequiredArgsConstructor
public class EffectFileVerification implements FileVerification {
    @Override
    public boolean support(PropertyType type) {
        return type.equals(PropertyType.EFFECT);
    }

    /**
     * 文件夹的名称应当与fx文件的名称相同
     * @param file
     * @param type
     * @return
     * @throws IOException
     */
    @Override
    public Tuple2<PropertyFileResponse, String> verify(MultipartFile file, String type) throws IOException {
        Tuple2<ZipFile, String> zipFileAndPath = ZipFileUtil.multipartFileToZipFile(file);
        String temporaryFilePath = zipFileAndPath._2();
        ZipFile zipFile = zipFileAndPath._1();

        String propertyId = "";
        List<String> nameList = new ArrayList<>();

        for (Enumeration entries = zipFile.entries(); entries.hasMoreElements(); ) {
            ZipEntry entry = (ZipEntry) entries.nextElement();
            String curEntryName = entry.getName();

            if (curEntryName.startsWith("__MACOSX/")) {
                continue;
            }

            if (curEntryName.contains(".DS_Store")) {
                continue;
            }

            if (curEntryName.endsWith("/") && StringUtils.countMatches(curEntryName, '/') == 1) {
                // 当前为Particle目录
                propertyId = curEntryName.split("/")[0];
            } else if (curEntryName.contains("/")){
                nameList.add(curEntryName.substring(curEntryName.indexOf('/') + 1));
            }
        }

        if (StringUtils.isEmpty(propertyId)) {
            log.warn("Fail to find propertyId from zipFile");
            throw new DigitalHumanCommonException("不符合明明规则");
        }

        boolean hasFx = false;
        int numOfTextures = 0;
        for (String name : nameList) {
            if (name.endsWith(".fx") && !name.contains("/") && name.split(".fx")[0].equals(propertyId)) {
                hasFx = true;
            } else if (name.startsWith("textures") && StringUtils.countMatches(name, '/') == 1){
                numOfTextures ++ ;
            }
        }

        if (!hasFx) {
            throw new DigitalHumanCommonException("缺少fx文件，或fx文件不符合命名规范");
        }

        if (numOfTextures < 2) {
            throw new DigitalHumanCommonException("缺少贴图文件，或贴图文件不符合命名规范");
        }

        return new Tuple2<>(PropertyFileResponse.builder().propertyId(propertyId).frameCount(0).build(),
                temporaryFilePath);
    }

}
