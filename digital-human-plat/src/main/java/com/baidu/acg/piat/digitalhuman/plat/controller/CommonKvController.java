package com.baidu.acg.piat.digitalhuman.plat.controller;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import javax.validation.Valid;

import com.baidu.acg.piat.digitalhuman.common.commonkv.CommonKv;
import com.baidu.acg.piat.digitalhuman.common.commonkv.CommonKvQueryVO;
import com.baidu.acg.piat.digitalhuman.common.model.PageResponse;
import com.baidu.acg.piat.digitalhuman.common.model.Response;
import com.baidu.acg.piat.digitalhuman.plat.service.CommonKvService;

/**
 * 通用 kv 对，for FE
 *
 * <AUTHOR>
 * @since 2021/08/05
 */
@Slf4j
@RestController
@RequestMapping("/api/digitalhuman/v1/kv")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class CommonKvController {
    private final CommonKvService commonKvService;

    @PostMapping("create")
    public Response<CommonKv> saveOrUpdate(@Valid @RequestBody CommonKv kv) {
        return Response.success(commonKvService.saveOrUpdate(kv));
    }

    @PostMapping("delete")
    public Response delete(@Valid @RequestBody CommonKv kv) {
        commonKvService.deleteByUserIdAndKey(kv.getUserId(), kv.getKey());
        return Response.success();
    }

    @PostMapping("query")
    public PageResponse<CommonKv> query(@Valid @RequestBody CommonKvQueryVO queryVO) {
        List<CommonKv> kvList;
        if (CollectionUtils.isEmpty(queryVO.getKeyList())) {
            kvList = commonKvService.findAllByUserId(queryVO.getUserId());
        } else {
            kvList = commonKvService.findAllByUserIdAndKeyIn(queryVO.getUserId(), queryVO.getKeyList());
        }
        return PageResponse.success(kvList);
    }

}
