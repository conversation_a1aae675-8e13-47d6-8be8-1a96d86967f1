package com.baidu.acg.piat.digitalhuman.plat.model.project;

import static com.baidu.acg.piat.digitalhuman.common.constans.Constants.FIRST_VERSION_MAGIC_NUMBER;
import static com.baidu.acg.piat.digitalhuman.common.constans.Constants.SECOND_VERSION_MAGIC_NUMBER;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import java.io.IOException;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.UpdateTimestamp;

import javax.persistence.Access;
import javax.persistence.AccessType;
import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.OneToOne;
import javax.persistence.Transient;
import javax.persistence.Version;

import com.baidu.acg.piat.digitalhuman.common.project.AlitaParams;
import com.baidu.acg.piat.digitalhuman.common.project.AsrPartEvent;
import com.baidu.acg.piat.digitalhuman.common.project.BotParams;
import com.baidu.acg.piat.digitalhuman.common.project.Camera;
import com.baidu.acg.piat.digitalhuman.common.project.CharacterParams;
import com.baidu.acg.piat.digitalhuman.common.project.FigureCutParams;
import com.baidu.acg.piat.digitalhuman.common.project.HotWordReplaceReg;
import com.baidu.acg.piat.digitalhuman.common.project.MediaOutput;
import com.baidu.acg.piat.digitalhuman.common.project.PaintChartOnPictureParams;
import com.baidu.acg.piat.digitalhuman.common.project.PaintSubtitleOnPictureParams;
import com.baidu.acg.piat.digitalhuman.common.project.Project;
import com.baidu.acg.piat.digitalhuman.common.project.PrologueParams;
import com.baidu.acg.piat.digitalhuman.common.project.ResolutionParams;
import com.baidu.acg.piat.digitalhuman.common.project.TtsParams;
import com.baidu.acg.piat.digitalhuman.common.project.UserInactiveConfig;
import com.baidu.acg.piat.digitalhuman.common.utils.JsonUtil;

/**
 * Created on 2020/4/21 21:05.
 *
 * <AUTHOR>
 */
@Data
@Slf4j
@Builder
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
@DynamicInsert
@DynamicUpdate
@Entity(name = "project")
public class ProjectModel {

    @Id
    @GeneratedValue(strategy = GenerationType.AUTO, generator = "myid")
    @GenericGenerator(name = "myid",
            strategy = "com.baidu.acg.piat.digitalhuman.plat.config.database.ManulInsertGenerator")
    private Long id;

    private String projectId;

    private String name;

    private String description;

    private String userId;

    private Integer isDefault;

    private String projectVersion;

    private String thumbnailUrl;

    private String characterThumbnail;

    private String preset;

    private String characterConfigId;

    private String characterConfigName;

    private String figureAlias;

    private String backgroundImageId;

    private String backgroundImageUrl;

    private String logoUid;

    private String logoUrl;

    @Transient
    private BotParams botParams;

    @Transient
    private TtsParams ttsParams;

    private String characterImage;

    @Transient
    private Camera camera;

    @Transient
    private ResolutionParams resolutionParams;

    @Transient
    private FigureCutParams figureCutParams;

    @Transient
    private CharacterParams characterParams;

    @Transient
    private PaintChartOnPictureParams paintChartOnPictureParams;

    @Transient
    private PaintSubtitleOnPictureParams paintSubtitleOnPictureParams;

    @Transient
    private MediaOutput mediaOutput;

    @OneToOne(cascade = CascadeType.ALL)
    @JoinColumn(name = "conversation_config_id", referencedColumnName = "id")
    private ConversationConfigModel conversationConfig;

    @Transient
    private HotWordReplaceReg hotWords;

    @Transient
    private UserInactiveConfig userInactive;

    @Transient
    private PrologueParams prologueParams;

    @Transient
    private List<String> hitShieldReply;

    @Transient
    private AlitaParams alitaParams;

    @Transient
    private AsrPartEvent asrPartEvent;

    private String editor;

    private int apiVersion;

    private String type;

    @CreationTimestamp
    @Column(insertable = false, updatable = false)
    private ZonedDateTime createTime;

    @UpdateTimestamp
    @Column(insertable = false, updatable = false)
    private ZonedDateTime updateTime;

    @Version
    private Integer version;

    @Column(name = "bot_params")
    @Access(AccessType.PROPERTY)
    public String getBotParamsString() {
        if (botParams != null) {
            try {
                return JsonUtil.writeValueAsString(botParams);
            } catch (JsonProcessingException e) {
                log.error("Parse bot params to json failed.", e);
            }
        }
        return StringUtils.EMPTY;
    }
    /* 后续如果查询project表字段更新了，这里也需同步更新*/
    public static final String PROJECT_COLUMNS =
                    "id, project_id, name, description, user_id, is_default, " +
                    "project_version, thumbnail_url, character_thumbnail, preset, character_config_id, " +
                    "character_config_name, figure_alias, background_image_id, background_image_url, " +
                    "logo_uid, logo_url, bot_params, tts_params, character_image, camera, " +
                    "resolution_params, figure_cut_params, character_params, paint_chart_on_picture_params, " +
                    "paint_subtitle_on_picture_params, media_output, conversation_config_id, hot_words, " +
                    "user_inactive, prologue_params, hit_shield_reply, alita_params, asr_part_event, " +
                    "editor, api_version, type, create_time, update_time, version";

    public void setBotParamsString(String botParams) {
        if (StringUtils.isNotEmpty(botParams)) {
            try {
                this.botParams = JsonUtil.readValue(botParams, BotParams.class);
                if (null == this.botParams.getBotMode()) {
                    Optional<String> botMode = BotParams.BotTypeEnum.getBotMode(this.botParams.getType());
                    if (botMode.isPresent()) {
                        this.botParams.setBotMode(botMode.get());
                    }
                }
            } catch (IOException e) {
                log.error("Exception when parse bot params from json string, string={}.", botParams, e);
            }
        } else {
            this.botParams = null;
        }
    }

    @Column(name = "tts_params")
    @Access(AccessType.PROPERTY)
    public String getTtsParamsString() {
        if (ttsParams != null) {
            try {
                return JsonUtil.writeValueAsString(ttsParams);
            } catch (JsonProcessingException e) {
                log.error("Parse tts params to json failed.", e);
            }
        }
        return StringUtils.EMPTY;
    }

    public void setTtsParamsString(String ttsParams) {
        if (StringUtils.isNotEmpty(ttsParams)) {
            try {
                this.ttsParams = JsonUtil.readValue(ttsParams, TtsParams.class);
            } catch (IOException e) {
                log.error("Exception when parse tts params from json string, string={}.", ttsParams, e);
            }
        } else {
            this.ttsParams = null;
        }
    }

    @Column(name = "camera")
    @Access(AccessType.PROPERTY)
    public String getCameraString() {
        if (camera != null) {
            try {
                return JsonUtil.writeValueAsString(camera);
            } catch (JsonProcessingException e) {
                log.error("Parse camera to json failed.", e);
            }
        }
        return StringUtils.EMPTY;
    }

    public void setCameraString(String cameraString) {
        if (StringUtils.isNotEmpty(cameraString)) {
            try {
                this.camera = JsonUtil.readValue(cameraString, Camera.class);
            } catch (IOException e) {
                log.error("Exception when parse camera from json string, string={}.", camera, e);
            }
        } else {
            this.camera = null;
        }
    }

    @Column(name = "resolution_params")
    @Access(AccessType.PROPERTY)
    public String getResolutionParamsString() {
        if (resolutionParams != null) {
            try {
                return JsonUtil.writeValueAsString(resolutionParams);
            } catch (JsonProcessingException e) {
                log.error("Parse resolution params to json failed.", e);
            }
        }
        return StringUtils.EMPTY;
    }

    public void setResolutionParamsString(String resolutionParams) {
        if (StringUtils.isNotEmpty(resolutionParams)) {
            try {
                this.resolutionParams = JsonUtil.readValue(resolutionParams, ResolutionParams.class);
            } catch (IOException e) {
                log.error("Exception when parse resolution params from json string, string={}.", resolutionParams, e);
            }
        } else {
            this.resolutionParams = null;
        }
    }

    @Column(name = "figure_cut_params")
    @Access(AccessType.PROPERTY)
    public String getFigureCutParamsString() {
        if (figureCutParams != null) {
            try {
                return JsonUtil.writeValueAsString(figureCutParams);
            } catch (JsonProcessingException e) {
                log.error("Parse figure cut params to json failed.", e);
            }
        }
        return StringUtils.EMPTY;
    }

    public void setFigureCutParamsString(String figureCutParams) {
        if (StringUtils.isNotEmpty(figureCutParams)) {
            try {
                this.figureCutParams = JsonUtil.readValue(figureCutParams, FigureCutParams.class);
            } catch (IOException e) {
                log.error("Exception when parse figure cut params from json string, string={}.", figureCutParams, e);
            }
        } else {
            this.figureCutParams = null;
        }
    }

    @Column(name = "character_params")
    @Access(AccessType.PROPERTY)
    public String getCharacterParamsString() {
        if (characterParams != null) {
            try {
                return JsonUtil.writeValueAsString(characterParams);
            } catch (JsonProcessingException e) {
                log.error("Parse character params to json failed.", e);
            }
        }
        return StringUtils.EMPTY;
    }

    public void setCharacterParamsString(String characterParams) {
        if (StringUtils.isNotEmpty(characterParams)) {
            try {
                this.characterParams = JsonUtil.readValue(characterParams, CharacterParams.class);
            } catch (IOException e) {
                log.error("Exception when parse character params from json string, string={}.", characterParams, e);
            }
        } else {
            this.characterParams = null;
        }
    }

    @Column(name = "paint_chart_on_picture_params")
    @Access(AccessType.PROPERTY)
    public String getPaintChartOnPictureParamsString() {
        if (paintChartOnPictureParams != null) {
            try {
                return JsonUtil.writeValueAsString(paintChartOnPictureParams);
            } catch (JsonProcessingException e) {
                log.error("Parse paint chart on picture params to json failed.", e);
            }
        }
        return StringUtils.EMPTY;
    }

    public void setPaintChartOnPictureParamsString(String paintChartOnPictureParams) {
        if (StringUtils.isNotEmpty(paintChartOnPictureParams)) {
            try {
                this.paintChartOnPictureParams = JsonUtil
                        .readValue(paintChartOnPictureParams, PaintChartOnPictureParams.class);
            } catch (IOException e) {
                log.error("Exception when parse paint chart on picture params from json string, string={}.",
                        paintChartOnPictureParams, e);
            }
        } else {
            this.paintChartOnPictureParams = null;
        }
    }

    @Column(name = "paint_subtitle_on_picture_params")
    @Access(AccessType.PROPERTY)
    public String getPaintSubtitleOnPictureParamsString() {
        if (paintSubtitleOnPictureParams != null) {
            try {
                return JsonUtil.writeValueAsString(paintSubtitleOnPictureParams);
            } catch (JsonProcessingException e) {
                log.error("Parse paint subtitle on picture params to json failed.", e);
            }
        }
        return StringUtils.EMPTY;
    }

    public void setPaintSubtitleOnPictureParamsString(String paintSubtitleOnPictureParams) {
        if (StringUtils.isNotEmpty(paintSubtitleOnPictureParams)) {
            try {
                this.paintSubtitleOnPictureParams = JsonUtil
                        .readValue(paintSubtitleOnPictureParams, PaintSubtitleOnPictureParams.class);
            } catch (IOException e) {
                log.error("Exception when parse paint subtitle on picture params from json string, string={}.",
                        paintSubtitleOnPictureParams, e);
            }
        } else {
            this.paintSubtitleOnPictureParams = null;
        }
    }

    @Column(name = "media_output")
    @Access(AccessType.PROPERTY)
    public String getMediaOutputString() {
        if (mediaOutput != null) {
            try {
                return JsonUtil.writeValueAsString(mediaOutput);
            } catch (JsonProcessingException e) {
                log.error("Parse media output to json failed.", e);
            }
        }
        return StringUtils.EMPTY;
    }

    public void setMediaOutputString(String mediaOutput) {
        if (StringUtils.isNotEmpty(mediaOutput)) {
            try {
                this.mediaOutput = JsonUtil.readValue(mediaOutput, MediaOutput.class);
            } catch (IOException e) {
                log.error("Exception when parse media output from json string, string={}.", mediaOutput, e);
            }
        } else {
            this.mediaOutput = null;
        }
    }

    @Column(name = "hot_words")
    @Access(AccessType.PROPERTY)
    public String getHotWordsString() {
        if (hotWords != null) {
            try {
                return JsonUtil.writeValueAsString(hotWords);
            } catch (JsonProcessingException e) {
                log.error("Parse hot words to json failed.", e);
            }
        }
        return StringUtils.EMPTY;
    }

    public void setHotWordsString(String hotWords) {
        if (StringUtils.isNotEmpty(hotWords)) {
            try {
                this.hotWords = JsonUtil.readValue(hotWords, HotWordReplaceReg.class);
            } catch (IOException e) {
                log.error("Exception when parse hot words from json string, string={}.", hotWords, e);
            }
        } else {
            this.hotWords = null;
        }
    }

    @Column(name = "user_inactive")
    @Access(AccessType.PROPERTY)
    public String getUserInactiveString() {
        if (userInactive != null) {
            try {
                return JsonUtil.writeValueAsString(userInactive);
            } catch (JsonProcessingException e) {
                log.error("Parse user inactive to json failed.", e);
            }
        }
        return StringUtils.EMPTY;
    }

    public void setUserInactiveString(String userInactive) {
        if (StringUtils.isNotEmpty(userInactive)) {
            try {
                this.userInactive = JsonUtil.readValue(userInactive, UserInactiveConfig.class);
            } catch (IOException e) {
                log.error("Exception when parse user inactive from json string, string={}.", userInactive, e);
            }
        } else {
            this.userInactive = null;
        }
    }

    @Column(name = "prologue_params")
    @Access(AccessType.PROPERTY)
    public String getPrologueParamsString() {
        if (prologueParams != null) {
            try {
                return JsonUtil.writeValueAsString(prologueParams);
            } catch (JsonProcessingException e) {
                log.error("Parse prologue params to json failed.", e);
            }
        }
        return StringUtils.EMPTY;
    }

    public void setPrologueParamsString(String prologueParams) {
        if (StringUtils.isNotEmpty(prologueParams)) {
            try {
                this.prologueParams = JsonUtil.readValue(prologueParams, PrologueParams.class);
            } catch (IOException e) {
                log.error("Exception when parse prologue params from json string, string={}.", prologueParams, e);
            }
        } else {
            this.prologueParams = null;
        }
    }

    @Column(name = "hit_shield_reply")
    @Access(AccessType.PROPERTY)
    public String getHitShieldReplyString() {
        if (hitShieldReply != null) {
            try {
                return JsonUtil.writeValueAsString(hitShieldReply);
            } catch (JsonProcessingException e) {
                log.error("Parse hit shield reply to json failed.", e);
            }
        }
        return StringUtils.EMPTY;
    }

    public void setHitShieldReplyString(String hitShieldReply) {
        if (StringUtils.isNotEmpty(hitShieldReply)) {
            try {
                this.hitShieldReply = JsonUtil.readValue(hitShieldReply, new TypeReference<List<String>>() {
                });
            } catch (IOException e) {
                log.error("Exception when parse hit shield reply from json string, string={}.", hitShieldReply, e);
            }
        } else {
            this.hitShieldReply = null;
        }
    }

    @Column(name = "alita_params")
    @Access(AccessType.PROPERTY)
    public String getAlitaParamsString() {
        if (alitaParams != null) {
            try {
                return JsonUtil.writeValueAsString(alitaParams);
            } catch (JsonProcessingException e) {
                log.error("Fail to get alita params", e);
            }
        }
        return StringUtils.EMPTY;
    }

    public void setAlitaParamsString(String alitaParamsString) {
        if (StringUtils.isNotEmpty(alitaParamsString)) {
            try {
                this.alitaParams = JsonUtil.readValue(alitaParamsString, AlitaParams.class);
            } catch (IOException e) {
                log.error("Fail to set alita params", e);
            }
        } else {
            this.alitaParams = null;
        }
    }

    @Column(name = "asr_part_event")
    @Access(AccessType.PROPERTY)
    public String getAsrPartEventString() {
        if (asrPartEvent != null) {
            try {
                return JsonUtil.writeValueAsString(asrPartEvent);
            } catch (JsonProcessingException e) {
                log.error("Fail to get asr part event", e);
            }
        }
        return StringUtils.EMPTY;
    }

    public void setAsrPartEventString(String asrPartEventString) {
        if (StringUtils.isNotEmpty(asrPartEventString)) {
            try {
                this.asrPartEvent = JsonUtil.readValue(asrPartEventString, AsrPartEvent.class);
            } catch (IOException e) {
                log.error("Fail to set asr part event", e);
            }
        } else {
            this.asrPartEvent = null;
        }
    }

    public Project toProjectRequest() {
        return Project.builder()
                .id(projectId)
                .name(name)
                .description(description)
                .isDefault(isDefault == null ? null : (isDefault == 1 ? true : false))
                .userId(userId)
                .projectVersion(fromProjectVersion(projectVersion))
                .thumbnailUrl(thumbnailUrl)
                .characterThumbnail(characterThumbnail)
                .characterImage(characterImage)
                .preset(preset)
                .characterConfigId(characterConfigId)
                .characterConfigName(characterConfigName)
                .figureAlias(figureAlias)
                .backgroundImageId(backgroundImageId)
                .backgroundImageUrl(backgroundImageUrl)
                .logoUid(logoUid)
                .logoUrl(logoUrl)
                .botParams(botParams)
                .ttsParams(ttsParams)
                .camera(camera)
                .resolutionParams(resolutionParams)
                .figureCutParams(figureCutParams)
                .paintChartOnPictureParams(paintChartOnPictureParams)
                .paintSubtitleOnPictureParams(paintSubtitleOnPictureParams)
                .characterParams(characterParams)
                .mediaOutput(mediaOutput)
                .conversationConfig(conversationConfig == null ? null : conversationConfig.toConversationConfig())
                .hotWords(hotWords)
                .userInactive(userInactive)
                .prologueParams(prologueParams)
                .hitShieldReply(hitShieldReply)
                .alitaParams(alitaParams)
                .asrPartEvent(asrPartEvent)
                .apiVersion(apiVersion)
                .createTime(DateTimeFormatter.ISO_OFFSET_DATE_TIME.format(createTime))
                .updateTime(DateTimeFormatter.ISO_OFFSET_DATE_TIME.format(updateTime))
                .editor(editor)
                .type(type)
                .build();
    }

    public ProjectModel copy() {
        return ProjectModel.builder()
                .projectId(ObjectId.get().toHexString())
                .name(name)
                .description(description)
                .isDefault(isDefault)
                .userId(userId)
                .projectVersion(projectVersion)
                .thumbnailUrl(thumbnailUrl)
                .characterThumbnail(characterThumbnail)
                .characterImage(characterImage)
                .preset(preset)
                .characterConfigId(characterConfigId)
                .characterConfigName(characterConfigName)
                .figureAlias(figureAlias)
                .backgroundImageId(backgroundImageId)
                .backgroundImageUrl(backgroundImageUrl)
                .logoUid(logoUid)
                .logoUrl(logoUrl)
                .botParams(botParams)
                .ttsParams(ttsParams)
                .camera(camera)
                .resolutionParams(resolutionParams)
                .figureCutParams(figureCutParams)
                .paintChartOnPictureParams(paintChartOnPictureParams)
                .paintSubtitleOnPictureParams(paintSubtitleOnPictureParams)
                .characterParams(characterParams)
                .mediaOutput(mediaOutput)
                .conversationConfig(conversationConfig)
                .hotWords(hotWords)
                .userInactive(userInactive)
                .prologueParams(prologueParams)
                .hitShieldReply(hitShieldReply)
                .alitaParams(alitaParams)
                .asrPartEvent(asrPartEvent)
                .apiVersion(apiVersion)
                .build();
    }

    public void toProjectModel(Project request) {
        if (StringUtils.isNotEmpty(request.getLogoUid())) {
            this.setLogoUid(request.getLogoUid());
        }
        if (StringUtils.isNotEmpty(request.getLogoUrl())) {
            this.setLogoUrl(request.getLogoUrl());
        }
        if (StringUtils.isNotEmpty(request.getBackgroundImageUrl())) {
            this.setBackgroundImageUrl(request.getBackgroundImageUrl());
        }
        if (request.getBotParams() != null && request.getBotParams().validate()) {
            this.setBotParams(request.getBotParams());
        }
        if (request.getTtsParams() != null) {
            this.setTtsParams(request.getTtsParams());
        }
        if (request.getFigureCutParams() != null) {
            this.setFigureCutParams(request.getFigureCutParams());
        }
        if (request.getCamera() != null) {
            this.setCamera(request.getCamera());
        }
        if (request.getResolutionParams() != null) {
            this.setResolutionParams(request.getResolutionParams());
        }
        if (request.getCharacterParams() != null) {
            this.setCharacterParams(request.getCharacterParams());
        } else {
            // character 置默认值
            this.setCharacterParams(new CharacterParams());
        }
        if (Objects.nonNull(request.getPaintChartOnPictureParams())) {
            this.setPaintChartOnPictureParams(request.getPaintChartOnPictureParams());
        }
        if (Objects.nonNull(request.getPaintSubtitleOnPictureParams())) {
            this.setPaintSubtitleOnPictureParams(request.getPaintSubtitleOnPictureParams());
        }
        if (Objects.nonNull(request.getMediaOutput())) {
            this.setMediaOutput(request.getMediaOutput());
        }
        if (request.getConversationConfig() != null) {
            this.setConversationConfig(ConversationConfigModel.toConversationConfigModel(
                    request.getConversationConfig()));
        }
        if (request.getHotWords() != null) {
            this.setHotWords(request.getHotWords());
        }
        if (request.getUserInactive() != null) {
            this.setUserInactive(request.getUserInactive());
        }
        if (request.getPrologueParams() != null) {
            this.setPrologueParams(request.getPrologueParams());
        }
        if (request.getHitShieldReply() != null) {
            this.setHitShieldReply(request.getHitShieldReply());
        }
        if (request.getAlitaParams() != null) {
            this.setAlitaParams(request.getAlitaParams());
        }
        if (request.getAsrPartEvent() != null) {
            this.setAsrPartEvent(request.getAsrPartEvent());
        }
        if (StringUtils.isNotEmpty(request.getThumbnailUrl())) {
            this.setThumbnailUrl(request.getThumbnailUrl());
        }
        if (StringUtils.isNotEmpty(request.getType())) {
            this.setType(request.getType());
        }
        this.setEditor(request.getEditor());
    }

    public static String fromProjectVersion(String projectVersion) {
        if (StringUtils.isNotEmpty(projectVersion)) {
            var number = Integer.parseInt(projectVersion);
            return number / FIRST_VERSION_MAGIC_NUMBER + "." +
                    (number - FIRST_VERSION_MAGIC_NUMBER) / SECOND_VERSION_MAGIC_NUMBER + "." +
                    (number - FIRST_VERSION_MAGIC_NUMBER) % SECOND_VERSION_MAGIC_NUMBER;
        }
        return StringUtils.EMPTY;
    }

    public static String toProjectVersion(String projectVersion) {
        if (StringUtils.isNotEmpty(projectVersion)) {
            var arr = projectVersion.split("\\.");
            if (arr.length == 3) {
                var number = Integer.parseInt(arr[0]) * FIRST_VERSION_MAGIC_NUMBER
                        + Integer.parseInt(arr[1]) * SECOND_VERSION_MAGIC_NUMBER
                        + Integer.parseInt(arr[2]);
                return String.valueOf(number);
            }
        }
        return StringUtils.EMPTY;
    }

}
