package com.baidu.acg.piat.digitalhuman.plat.aspect;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
@Aspect
public class RequestContentLogAspect {
    /**
     * log请求内容
     *
     * @param joinPoint
     */
    @Before("within(com.baidu.acg.piat.digitalhuman.plat.controller.*) || " +
            "within(com.baidu.acg.piat.digitalhuman.plat.v2.controller.*)")
    public void before(JoinPoint joinPoint) {
        Object[] args = joinPoint.getArgs();
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();

        log.info("{}.{} request content: ({})",
                method.getDeclaringClass(), method.getName(), StringUtils.join(args, ", "));
    }

    /**
     * log返回内容
     *
     * @param joinPoint
     * @param response
     */
    @AfterReturning(value = "within(com.baidu.acg.piat.digitalhuman.plat.controller.*) || " +
            "within(com.baidu.acg.piat.digitalhuman.plat.v2.controller.*)",
            returning = "response")
    public void after(JoinPoint joinPoint, Object response) {
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();

        log.info("{}.{} return: {}",
                method.getDeclaringClass(), method.getName(),
                Objects.isNull(response) ? "null" : response.toString());
    }
}
