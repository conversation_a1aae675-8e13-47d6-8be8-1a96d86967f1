package com.baidu.acg.piat.digitalhuman.plat;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;

import com.baidu.acg.dh.user.client.config.AuthAutoConfiguration;
import com.baidu.acg.piat.digitalhuman.common.advice.GlobalControllerAdvice;


/**
 * Created on 2020/5/27 20:24.
 *
 * <AUTHOR>
 */
@SpringBootApplication(
        scanBasePackages = {
                "com.baidu.acg.piat.digitalhuman.plat",
                "com.baidu.acg.piat.digitalhuman.common.metrics",
                "com.baidu.acg.piat.digitalhuman.common.healthz",
                "com.baidu.acg.piat.digitalhuman.storage"
        },
        scanBasePackageClasses = {
                GlobalControllerAdvice.class,
                AuthAutoConfiguration.class
        })
@EnableJpaRepositories
@EntityScan(basePackages = {
        "com.baidu.acg.piat.digitalhuman.common.entity",
        "com.baidu.acg.piat.digitalhuman.plat.v2.model",
        "com.baidu.acg.piat.digitalhuman.plat.model"})
public class DigitalHumanPlatApplication {

    public static void main(String[] args) {
        SpringApplication.run(DigitalHumanPlatApplication.class, args);
    }

}