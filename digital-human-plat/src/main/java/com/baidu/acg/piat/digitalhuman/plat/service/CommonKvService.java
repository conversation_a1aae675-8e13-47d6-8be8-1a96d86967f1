package com.baidu.acg.piat.digitalhuman.plat.service;

import com.sun.istack.Nullable;

import java.util.List;
import java.util.Optional;

import com.baidu.acg.piat.digitalhuman.common.commonkv.CommonKv;

/**
 * <AUTHOR>
 * @since 2021/08/05
 */
public interface CommonKvService {
    CommonKv saveOrUpdate(CommonKv commonKv);

    Optional<CommonKv> findByUserIdAndKey(String userId, String key);

    List<CommonKv> findAllByUserId(String userId);

    List<CommonKv> findAllByUserIdAndKeyIn(String userId, @Nullable List<String> keyList);

    void deleteByUserIdAndKey(String userId, String key);
}
