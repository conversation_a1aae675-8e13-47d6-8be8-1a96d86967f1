package com.baidu.acg.piat.digitalhuman.plat.service;

import com.baidu.acg.piat.digitalhuman.common.exception.DigitalHumanCommonException;
import com.baidu.acg.piat.digitalhuman.common.statistic.QueryResponse;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import retrofit2.Retrofit;
import retrofit2.converter.jackson.JacksonConverterFactory;

import java.io.IOException;

@Slf4j
public class PrometheusClient {

    private final String baseUrl;

    private final PrometheusService prometheusService;

    private OkHttpClient httpClient;


    public PrometheusClient(String baseUrl) {
        this.baseUrl = baseUrl;
        this.httpClient = new OkHttpClient.Builder().build();
        var retrofit = new Retrofit.Builder()
                .baseUrl(baseUrl)
                .client(httpClient)
                .addConverterFactory(JacksonConverterFactory.create(new ObjectMapper()
                        .setPropertyNamingStrategy(PropertyNamingStrategy.SNAKE_CASE)
                ))
                .build();
        this.prometheusService = retrofit.create(PrometheusService.class);
    }

    public QueryResponse query(String query, long time) {
        log.debug("Prometheus query={}, time={}", query, time);
        try {
            var response = prometheusService.query(query, time).execute();
            if (response.isSuccessful()) {
                return response.body();
            }
            assert response.errorBody() != null;
            throw new DigitalHumanCommonException(response.errorBody().string());
        } catch (IOException e) {
            log.warn("Fail to call prometheus api service", e);
            throw new DigitalHumanCommonException(e.getLocalizedMessage());
        }
    }

    public QueryResponse queryRange(String query, long startTime, long endTime, long step) {
        log.debug("Prometheus queryRange={}, startTime={}, endTime={}, step={}", query, startTime, endTime, step);

        try {
            var response = prometheusService.queryRange(query, startTime, endTime, step).execute();
            if (response.isSuccessful()) {
                return response.body();
            }
            assert response.errorBody() != null;
            throw new DigitalHumanCommonException(response.errorBody().string());
        } catch (IOException e) {
            log.warn("Fail to call prometheus api service", e);
            throw new DigitalHumanCommonException(e.getLocalizedMessage());
        }
    }
}
