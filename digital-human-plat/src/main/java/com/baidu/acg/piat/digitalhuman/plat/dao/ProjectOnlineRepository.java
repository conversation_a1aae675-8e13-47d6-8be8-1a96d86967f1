package com.baidu.acg.piat.digitalhuman.plat.dao;

import java.util.Optional;

import javax.transaction.Transactional;

import org.springframework.data.jpa.repository.JpaRepository;

import com.baidu.acg.piat.digitalhuman.plat.model.project.ProjectOnlineModel;

/**
 * Created on 2020/4/21 21:05.
 *
 * <AUTHOR>
 */
public interface ProjectOnlineRepository extends JpaRepository<ProjectOnlineModel, Long> {

    Optional<ProjectOnlineModel> findByProjectId(String projectId);

    @Transactional
    void deleteByProjectId(String projectId);


}
