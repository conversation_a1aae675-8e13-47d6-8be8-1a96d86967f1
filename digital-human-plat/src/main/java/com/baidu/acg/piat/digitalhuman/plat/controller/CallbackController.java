package com.baidu.acg.piat.digitalhuman.plat.controller;


import com.baidu.acg.piat.digitalhuman.common.callback.CallbackTaskCreateRequest;
import com.baidu.acg.piat.digitalhuman.common.callback.CallbackTaskCreateResponse;
import com.baidu.acg.piat.digitalhuman.common.model.Response;
import com.baidu.acg.piat.digitalhuman.plat.service.CallbackManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


@RestController
@Validated
@RequestMapping("/api/digitalhuman/plat/callback/")
public class CallbackController {
    @Autowired
    private CallbackManager callbackManager;

    @PostMapping("submit")
    public Response<CallbackTaskCreateResponse> submitCallbackTask(@RequestBody CallbackTaskCreateRequest request) {
        return Response.success(callbackManager.submit(request));

    }
}
