package com.baidu.acg.piat.digitalhuman.plat.model.property.impl;

import com.baidu.acg.piat.digitalhuman.common.exception.DigitalHumanCommonException;
import com.baidu.acg.piat.digitalhuman.common.utils.JsonUtil;
import com.baidu.acg.piat.digitalhuman.common.utils.ZipFileUtil;
import com.baidu.acg.piat.digitalhuman.plat.model.property.AnimojiJsonFile;
import com.baidu.acg.piat.digitalhuman.plat.model.property.AnimojiNode;
import com.baidu.acg.piat.digitalhuman.plat.model.property.FileVerification;
import com.baidu.acg.piat.digitalhuman.plat.model.property.PropertyFileResponse;
import com.baidu.acg.piat.digitalhuman.plat.model.property.PropertyType;
import io.vavr.Tuple2;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Enumeration;
import java.util.List;
import java.util.zip.ZipEntry;
import java.util.zip.ZipFile;

@Slf4j
@Component
@RequiredArgsConstructor
@Getter
public class AnimojiFileVerification implements FileVerification {

    @Override
    public boolean support(PropertyType type) {
        return type.equals(PropertyType.ANIMOJI) || type.equals(PropertyType.EXPRESSION);
    }

    /**
     * 对用户上传的动作文件夹名字进行校验
     * 文件夹名称、模型名称（gltf）、json文件名、json里的动作名统一保持命名一致
     * @param file
     * @return
     */
    @Override
    public Tuple2<PropertyFileResponse, String> verify(MultipartFile file, String type) throws IOException {

        Tuple2<ZipFile, String> zipFileAndPath = ZipFileUtil.multipartFileToZipFile(file);
        String temporaryFilePath = zipFileAndPath._2();
        ZipFile zipFile = zipFileAndPath._1();

        String propertyId = "";
        int animojiCount = 0;
        List<String> nameList = new ArrayList<>();
        boolean hasBin = false;
        AnimojiJsonFile animojiJson = null;

        for (Enumeration entries = zipFile.entries(); entries.hasMoreElements(); ) {
            ZipEntry entry = (ZipEntry) entries.nextElement();
            String curEntryName = entry.getName();
            if (curEntryName.startsWith("__MACOSX/")) {
                continue;
            }

            if (curEntryName.endsWith("/")) {
                // 校验文件夹名称
                propertyId = curEntryName.split("/")[0];

            } else if (curEntryName.endsWith(".bin")) {
                hasBin = true;
            } else if (curEntryName.contains("/")){
                nameList.add(curEntryName.split("/")[1]);

                // 关于json文件的处理
                if (curEntryName.endsWith(".json")) {

                    try(InputStream inputStream = zipFile.getInputStream(entry)) {
                        animojiJson = JsonUtil.readValue(inputStream.readAllBytes(), AnimojiJsonFile.class);
                    } catch (IOException e) {
                        throw new RuntimeException(e);
                    }
                }
            }

        }

        // 是否有bin文件
        if (!hasBin) {
            throw new DigitalHumanCommonException("bin文件缺失");
        }

        // 校验gltf
        try {
            verifyGltfFileName(nameList, propertyId);
        } catch (DigitalHumanCommonException e) {
            throw new DigitalHumanCommonException(e.getMessage());
        }

        // 校验json文件
        try {
            animojiCount = verifyAnimojiJson(animojiJson, propertyId);
        } catch (DigitalHumanCommonException e) {
            throw new DigitalHumanCommonException(e.getMessage());
        }

        return new Tuple2<>(PropertyFileResponse.builder().propertyId(propertyId).frameCount(animojiCount).build(),
                temporaryFilePath);

    }

    private int verifyAnimojiJson (AnimojiJsonFile json, String propertyId) {

        boolean res = false;
        if (json != null && json.getNodeList() != null && json.getNodeList().size() == 1) {
            List<AnimojiNode.Animation> animationList = json.getNodeList().get(0).getAnimationList();
            if (animationList != null && animationList.size() == 1) {
                if (propertyId.equals(animationList.get(0).getAnimationName())) {
                    return animationList.get(0).getEndFrame() - animationList.get(0).getStartFrame() + 2;
                }
            }
        }

        throw new DigitalHumanCommonException("json文件中的动作名称与文件夹名字不统一");

    }
}
