package com.baidu.acg.piat.digitalhuman.plat.controller;

import javax.validation.Valid;

import com.baidu.acg.piat.digitalhuman.common.model.PageResponse;
import com.baidu.acg.piat.digitalhuman.common.model.Response;
import com.baidu.acg.piat.digitalhuman.common.room.Room;
import com.baidu.acg.piat.digitalhuman.plat.service.RoomService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * The room controller.
 *
 * <AUTHOR> (ma<PERSON><PERSON><PERSON>@baidu.com)
 */
@Slf4j
@RestController
@RequestMapping("/api/digitalhuman/v1/room")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class RoomController {

    private final RoomService roomService;

    @PostMapping
    public Response<Room> create(@Valid @RequestBody Room room) {
        return Response.success(roomService.create(room));
    }

    @PutMapping("/{id}")
    public Response<Room> update(@PathVariable("id") String id, @Valid @RequestBody Room request) {
        return Response.success(roomService.update(id, request));
    }

    @GetMapping("/{id}")
    public Response<Room> detail(@PathVariable("id") String roomId) {
        return Response.success(roomService.detail(roomId));
    }

    @GetMapping
    public PageResponse<Room> list(@RequestParam String userId,
                                   @RequestParam(required = false) String appId,
                                   @RequestParam(required = false) String roomName,
                                   @RequestParam(required = false, defaultValue = "1") int pageNo,
                                   @RequestParam(required = false, defaultValue = "20") int pageSize) {
        return roomService.list(userId, appId, roomName, pageNo, pageSize);
    }
}
