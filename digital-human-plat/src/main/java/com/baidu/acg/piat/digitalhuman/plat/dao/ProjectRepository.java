package com.baidu.acg.piat.digitalhuman.plat.dao;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.data.repository.query.Param;

import java.util.List;
import java.util.Optional;
import java.util.Set;

import javax.transaction.Transactional;

import com.baidu.acg.piat.digitalhuman.plat.model.project.ProjectModel;


/**
 * Created on 2020/4/21 21:05.
 *
 * <AUTHOR>
 */
public interface ProjectRepository extends JpaRepository<ProjectModel, Long> {

    Optional<ProjectModel> findByProjectId(String projectId);

    Page<ProjectModel> findByUserIdAndNameAndApiVersionAndTypeOrderByCreateTimeDesc(String userId, String name,
                                                                             int apiVersion, String type, Pageable pageable);

    Page<ProjectModel> findByUserIdAndNameContainingAndApiVersionOrderByCreateTimeDesc(String userId, String name,
                                                                             int apiVersion, Pageable pageable);

    @Query(value = "select " + ProjectModel.PROJECT_COLUMNS + " from project where CONCAT(user_id, name, "
            + "project_version) in " +
            "(select CONCAT(user_id, name, max(project_version)) from project where user_id = :userId and " +
            "name like :name and api_version = :apiVersion and is_default = :isDefault and type = 'USER' group by name) " +
            "order by create_time desc", nativeQuery = true, countProjection = "1")
    Page<ProjectModel> findByUserIdAndNameContainingAndApiVersionAndIsDefaultAndMaxProjectVersionAndTypeOrderByCreateTimeDesc(@Param("userId") String userId,
                                                                                                           @Param("name") String name,
                                                                                                           @Param("apiVersion") int apiVersion,
                                                                                                           @Param("isDefault") int isDefault,
                                                                                                           Pageable pageable);

    Page<ProjectModel> findByUserIdAndNameContainingAndApiVersionAndCharacterImageInAndIsDefaultAndTypeOrderByCreateTimeDesc(
            String userId, String name, int apiVersion, List<String> characterImages, int isDefault, String type, Pageable pageable);


    Optional<ProjectModel> findByUserIdAndNameAndProjectVersionAndApiVersionAndType(String userId, String name,
                                                                             String projectVersion, int apiVersion, String type);

    @Query(value = "select " + ProjectModel.PROJECT_COLUMNS + " from project where CONCAT(user_id, name, project_version, api_version) " +
            "in :strs and api_version = :apiVersion and type = 'USER' order by create_time desc", nativeQuery = true, countProjection = "1")
    Page<ProjectModel> findByUserIdAndNameAndProjectVersionAndApiVersionAndType(@Param("strs") Set<String> strs,
                                                                                @Param("apiVersion") int apiVersion,
                                                                                Pageable pageable);


    List<ProjectModel> findByUserIdAndNameAndApiVersionAndType(String userId, String name, int apiVersion, String type);

    @Transactional
    void deleteByProjectId(String projectId);

    @Query(value = "select " + ProjectModel.PROJECT_COLUMNS + " from project where user_id = :userId and " +
            "is_default = :isDefault and type = 'USER' order by create_time desc", nativeQuery = true, countProjection = "1")
    Page<ProjectModel> findByUserIdAndIsDefaultAndTypeOrderByCreateTimeDesc(@Param("userId") String userId,
                                                                     @Param("isDefault") int isDefault,
                                                                     Pageable pageable);

    @Query(value = "select " + ProjectModel.PROJECT_COLUMNS + " from project where user_id = :userId and " +
            "is_default = :isDefault and api_version = :apiVersion and type = 'USER' order by create_time desc", nativeQuery = true, countProjection = "1")
    Page<ProjectModel> findByUserIdAndIsDefaultAndApiVersionAndTypeOrderByCreateTimeDesc(@Param("userId") String userId,
                                                                                  @Param("isDefault") int isDefault,
                                                                                  @Param("apiVersion") int apiVersion,
                                                                                  Pageable pageable);

    @Query(value = "select " + ProjectModel.PROJECT_COLUMNS + " from project where CONCAT(user_id, name, project_version) in " +
            "(select CONCAT(user_id, name, max(project_version)) from project where user_id = :userId and " +
            "is_default = :isDefault and api_version = :apiVersion and type = 'USER' group by name) " +
            "order by create_time desc", nativeQuery = true,countProjection = "1")
    Page<ProjectModel> findByUserIdAndMaxProjectVersionAndType(@Param("userId") String userId,
                                                        @Param("isDefault") int isDefault,
                                                        @Param("apiVersion") int apiVersion,
                                                        Pageable pageable);

    @Query(value = "select " + ProjectModel.PROJECT_COLUMNS + " from project where user_id = :userId and is_default = :isDefault " +
            "and api_version = :apiVersion and type = 'USER'", nativeQuery = true)
    List<ProjectModel> findByUserIdAndIsDefaultAndApiVersionAndType(@Param("userId") String userId,
                                                        @Param("isDefault") int isDefault,
                                                        @Param("apiVersion") int apiVersion);

    @Query(value = "select " + ProjectModel.PROJECT_COLUMNS + " from project where is_default = :isDefault " +
            "and api_version = :apiVersion and type = 'USER'", nativeQuery = true)
    List<ProjectModel> findByIsDefaultAndApiVersionAndType(@Param("isDefault") int isDefault,
                                                    @Param("apiVersion") int apiVersion);


    @Query(value = "select " + ProjectModel.PROJECT_COLUMNS + " from project where CONCAT(user_id, name, project_version) in :strs " +
            "and api_version = :apiVersion and type = 'USER'", nativeQuery = true)
    List<ProjectModel> findByUserIdAndNameAndProjectVersionAndType(@Param("strs") Set<String> strs,
                                                            @Param("apiVersion") int apiVersion);

    @Query(value = "select " + ProjectModel.PROJECT_COLUMNS + " from project where CONCAT(user_id, name, project_version) in :strs " +
            "and api_version = :apiVersion and type = 'USER' order by create_time desc", nativeQuery = true, countProjection = "1")
    Page<ProjectModel> findByUserIdAndNameAndProjectVersionAndType(@Param("strs") Set<String> strs,
                                                            @Param("apiVersion") int apiVersion,
                                                            Pageable pageable);



    @Query(value = "select " + ProjectModel.PROJECT_COLUMNS + " from project where CONCAT(user_id, name, project_version, api_version) in " +
            "(select CONCAT(user_id, name, max(project_version), api_version) from project where user_id = :userId " +
            "and is_default = :isDefault and api_version = :apiVersion and type = 'USER' and character_image in :types group by name) " +
            "order by create_time desc", nativeQuery = true, countProjection = "1")
    Page<ProjectModel> findByUserIdAndApiVersionAndMaxProjectVersionAndType(@Param("userId") String userId,
                                                                     @Param("isDefault") int isDefault,
                                                                     @Param("apiVersion") int apiVersion,
                                                                     @Param("types") List<String> types,
                                                                     Pageable pageable);

    @Query(value = "select " + ProjectModel.PROJECT_COLUMNS + " from project where user_id = :userId and is_default = :isDefault " +
            "and api_version = :apiVersion and type = 'USER' and (character_image = '' or character_image in :types)", nativeQuery = true)
    List<ProjectModel> findByUserIdAndIsDefaultAndApiVersionAndCharacterImageAndType(@Param("userId") String userId,
                                                                     @Param("isDefault") int isDefault,
                                                                     @Param("apiVersion") int apiVersion,
                                                                     @Param("types") List<String> types);

    List<ProjectModel> findByCharacterConfigIdInAndUserIdAndApiVersionAndIsDefaultAndType(List<String> characterConfigId,
                                                                     String userId, int apiVersion, int isDefault, String type);

    Page<ProjectModel> findByUserIdAndNameContainingAndProjectVersionAndIsDefaultAndApiVersionAndTypeOrderByCreateTimeDesc(
            String userId, String name, String projectVersion, int isDefault, int apiVersion, String type, PageRequest pageRequest);

    Page<ProjectModel>
    findByUserIdAndNameContainingAndProjectVersionAndIsDefaultAndApiVersionAndCharacterImageInAndTypeOrderByCreateTimeDesc(
            String userId, String name, String projectVersion, int isDefault, int apiVersion,
            List<String> characterImages, String type, PageRequest pageRequest);
}
