package com.baidu.acg.piat.digitalhuman.plat.controller;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import javax.validation.Valid;

import com.baidu.acg.piat.digitalhuman.common.material.Material;
import com.baidu.acg.piat.digitalhuman.common.model.PageResponse;
import com.baidu.acg.piat.digitalhuman.common.model.Response;
import com.baidu.acg.piat.digitalhuman.plat.service.MaterialService;

/**
 * Created on 2020/4/21 18:31.
 *
 * <AUTHOR>
 */
@Slf4j
@RequestMapping("/api/digitalhuman/v1/material")
@RestController
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class MaterialController {

    private final MaterialService materialService;

    @PostMapping
    public Response<Material> create(@Valid @RequestBody Material material) {
        return Response.success(materialService.create(material));
    }

    @DeleteMapping("{id}")
    public Response<Void> delete(@PathVariable("id") String materialId) {
        materialService.delete(materialId);
        return Response.success(null);
    }

    @PutMapping("{id}")
    public Response<Material> update(@PathVariable("id") String materialId, @Valid @RequestBody Material request) {
        return Response.success(materialService.update(materialId, request));
    }

    @GetMapping("{id}")
    public Response<Material> detail(@PathVariable("id") String materialId) {
        return Response.success(materialService.detail(materialId));
    }

    @GetMapping
    public PageResponse<Material> list(@RequestParam String userId,
                                      @RequestParam(required = false, defaultValue = "") String name,
                                      @RequestParam(required = false, defaultValue = "-1") List<String> type,
                                      @RequestParam(required = false, defaultValue = "-1") List<String> positionId,
                                      @RequestParam(required = false, defaultValue = "1") int pageNo,
                                      @RequestParam(required = false, defaultValue = "20") int pageSize) {

        return materialService.listByUserIdAndNameAndTypeAndPositionId(userId, name, type, positionId,
                pageNo, pageSize);
    }
}
