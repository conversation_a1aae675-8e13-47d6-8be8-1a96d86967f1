package com.baidu.acg.piat.digitalhuman.plat.model.background;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;
import org.hibernate.annotations.UpdateTimestamp;

import java.time.ZonedDateTime;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Version;

import com.baidu.acg.piat.digitalhuman.common.background.BackgroundImage;
import com.baidu.acg.piat.digitalhuman.common.utils.DateTimeUtil;

/**
 * <AUTHOR>
 */
@Data
@Builder
@Accessors(chain = true)
@DynamicInsert
@DynamicUpdate
@Entity(name = "background")
@AllArgsConstructor
@NoArgsConstructor
public class BackgroundModel {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    private String backgroundId;

    private String userId;

    private String name;

    private String description;

    private Integer isLiveBg;

    private String url;

    @CreationTimestamp
    @Column(insertable = false, updatable = false)
    private ZonedDateTime createTime;

    @UpdateTimestamp
    @Column(insertable = false, updatable = false)
    private ZonedDateTime updateTime;

    @Version
    private Integer version;

    public BackgroundImage toBackgroundImage() {
        return BackgroundImage.builder()
                .id(backgroundId)
                .userId(userId)
                .name(name)
                .description(description)
                .imageUrl(url)
                .isLiveBg(isLiveBg == null ? null : (isLiveBg == 1 ? true : false))
                .createTime(createTime != null ? DateTimeUtil.beijingTimeString(createTime) : null)
                .build();
    }
}
