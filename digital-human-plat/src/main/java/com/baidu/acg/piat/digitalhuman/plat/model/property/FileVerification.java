package com.baidu.acg.piat.digitalhuman.plat.model.property;

import com.baidu.acg.piat.digitalhuman.common.exception.DigitalHumanCommonException;
import io.vavr.Tuple2;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;

public interface FileVerification {

    boolean support(PropertyType type);

    /**
     * 对上传的文件进行校验
     * @param file
     * @return propertyFileResponse, temporaryFilePath
     */
    Tuple2<PropertyFileResponse, String> verify(MultipartFile file, String type) throws IOException;

    default void verifyGltfFileName (List<String> fileNameList, String propertyId) {
        String gltfName = propertyId + ".gltf";
        if (!fileNameList.contains(gltfName)) {
            throw new DigitalHumanCommonException("gltf文件名称不符合规范");
        }
    }

}
