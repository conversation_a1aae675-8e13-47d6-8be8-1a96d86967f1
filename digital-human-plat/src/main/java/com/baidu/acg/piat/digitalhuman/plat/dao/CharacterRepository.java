package com.baidu.acg.piat.digitalhuman.plat.dao;

import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.repository.PagingAndSortingRepository;

import java.util.List;
import java.util.Optional;

import javax.transaction.Transactional;

import com.baidu.acg.piat.digitalhuman.common.entity.CharacterModel;

/**
 * <AUTHOR>
 */
public interface CharacterRepository extends PagingAndSortingRepository<CharacterModel, Long> {

    Optional<CharacterModel> findByCharacterId(String characterId);

    @Transactional
    void deleteByCharacterId(String characterId);

    /**
     * 根据类型和apiVersion查找
     *
     * @param type
     * @param apiVersion
     * @return
     */
    Optional<CharacterModel> findByTypeAndApiVersion(String type, int apiVersion);

    Optional<CharacterModel> findByFigureName(String figureName);

    /**
     * 根据类型全量查找
     * @param type
     * @return
     */
    List<CharacterModel> findByType (String type);

    /**
     * 根据类型全量查找
     */
    List<CharacterModel> findByTypeInAndApiVersion(List<String> types, int apiVersion);

    /**
     * 查找所有的记录，并按照创建时间倒序
     *
     * @param pageable
     * @return
     */
    PageImpl<CharacterModel> findByApiVersionOrderByCreateTimeDesc(int apiVersion, Pageable pageable);

    /**
     * 查找所有visibleForLive=true的记录，并按照创建时间倒序
     * @param apiVersion
     * @param visibleForLive
     * @param pageable
     * @return
     */
    PageImpl<CharacterModel> findByApiVersionAndVisibleForLiveOrderByCreateTimeDesc(
            int apiVersion, int visibleForLive, Pageable pageable);

    /**
     * 查找所有visibleForSce=true的记录，并按照创建时间倒序
     * @param apiVersion
     * @param visibleForSce
     * @param pageable
     * @return
     */
    PageImpl<CharacterModel> findByApiVersionAndVisibleForSceOrderByCreateTimeDesc(
            int apiVersion, int visibleForSce, Pageable pageable);

    PageImpl<CharacterModel> findByApiVersionAndVisibleForSceAndVisibleForLiveOrderByCreateTimeDesc(
            int apiVersion, int visibleForSce, int visibleForLive, Pageable pageable);

    PageImpl<CharacterModel> findByApiVersionAndTypeInOrderByCreateTimeDesc(
            int apiVersion, List<String> types, Pageable pageable);

    PageImpl<CharacterModel> findByApiVersionAndTypeInAndVisibleForLiveOrderByCreateTimeDesc(
            int apiVersion, List<String> types, int visibleForLive, Pageable pageable);

    PageImpl<CharacterModel> findByApiVersionAndTypeInAndVisibleForSceOrderByCreateTimeDesc(
            int apiVersion, List<String> types, int visibleForSce, Pageable pageable);

    /**
     * @param type
     */
    @Transactional
    void deleteByType(String type);
}
