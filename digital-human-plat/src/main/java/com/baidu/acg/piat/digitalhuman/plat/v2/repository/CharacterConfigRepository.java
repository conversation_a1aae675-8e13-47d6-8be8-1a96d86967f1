package com.baidu.acg.piat.digitalhuman.plat.v2.repository;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.repository.PagingAndSortingRepository;

import java.util.List;
import java.util.Optional;

import com.baidu.acg.piat.digitalhuman.common.entity.CharacterConfig;

public interface CharacterConfigRepository extends PagingAndSortingRepository<CharacterConfig, String> {

    Optional<CharacterConfig> findByConfigId(String configId);

    Optional<CharacterConfig> findByUserFigureId(Long userFigureId);

    Optional<CharacterConfig> findByUserIdAndName(String userId, String name);

    Page<CharacterConfig> findByUserIdInOrderByUpdateTimeDesc(List<String> userIds, Pageable page);

    Page<CharacterConfig> findByUserIdInAndNameIsLikeOrderByUpdateTimeDesc(List<String> userIds, String name, Pageable page);

    Page<CharacterConfig> findByUserIdInAndTypeOrderByUpdateTimeDesc(List<String> userIds, String type, Pageable page);

    Page<CharacterConfig> findByUserIdInAndCharacterNameOrderByUpdateTimeDesc(List<String> userIds, String characterName,
                                                                            Pageable page);

    Page<CharacterConfig> findByUserIdInAndTypeInOrderByUpdateTimeDesc(List<String> userIds, List<String> types,
                                                                       Pageable page);

    Page<CharacterConfig> findByUserIdInAndCharacterIdOrderByUpdateTimeDesc(List<String> userIds, String characterName,
                                                                            Pageable page);

    Page<CharacterConfig> findByUserIdInAndTypeInAndCharacterIdOrderByUpdateTimeDesc(List<String> userIds,
                                                                                     List<String> types,
                                                                                     String characterId,
                                                                                     Pageable page);

    Page<CharacterConfig> findByUserIdInAndTypeInAndNameIsLikeOrderByUpdateTimeDesc(List<String> userIds,
                                                                                    List<String> types,
                                                                                    String name, Pageable page);

    List<CharacterConfig> findByUserIdAndConfigIdIn(String userId, List<String> configIds);

    List<CharacterConfig> findByConfigIdIn(List<String> configIds);

    List<CharacterConfig> findByCharacterIdAndConfigLike(String characterId, String config);

}
