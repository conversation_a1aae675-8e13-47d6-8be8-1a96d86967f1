package com.baidu.acg.piat.digitalhuman.plat.service.impl;

import com.baidu.acg.dh.user.client.UserClient;
import com.baidu.acg.dh.user.client.UserResult;
import com.baidu.acg.dh.user.client.model.vo.UserQueryResVO;
import com.baidu.acg.dh.user.client.model.vo.UserBatchGetReqVO;
import com.baidu.acg.dh.user.client.model.vo.UserQueryReqVO;
import com.baidu.acg.dh.user.client.model.vo.AccountQueryResVO;
import com.baidu.acg.dh.user.client.model.vo.AccountBatchGetReqVO;
import com.baidu.acg.piat.digitalhuman.common.exception.DigitalHumanCommonException;
import com.baidu.acg.piat.digitalhuman.common.exception.Error;
import com.baidu.acg.piat.digitalhuman.plat.helper.OptModuleHolder;
import com.baidu.acg.piat.digitalhuman.common.model.PageResponse;
import com.baidu.acg.piat.digitalhuman.common.model.TreeNode;
import com.baidu.acg.piat.digitalhuman.common.optlog.OptLog;
import com.baidu.acg.piat.digitalhuman.common.utils.DateTimeUtil;
import com.baidu.acg.piat.digitalhuman.common.utils.IDGenerator;
import com.baidu.acg.piat.digitalhuman.plat.dao.OptLogRepository;
import com.baidu.acg.piat.digitalhuman.plat.model.optlog.OptLogModel;
import com.baidu.acg.piat.digitalhuman.common.common.PlatCode;
import com.baidu.acg.piat.digitalhuman.plat.model.optlog.OptLogListRequest;
import com.baidu.acg.piat.digitalhuman.plat.model.optlog.OptLogView;
import com.baidu.acg.piat.digitalhuman.plat.model.optlog.OptModuleItem;
import com.baidu.acg.piat.digitalhuman.plat.service.OptLogService;
import com.baidu.acg.piat.digitalhuman.plat.service.ProductionStatisticService;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVParser;
import org.apache.commons.csv.CSVPrinter;
import org.apache.commons.csv.CSVRecord;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.exception.ConstraintViolationException;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import javax.annotation.PostConstruct;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Predicate;
import java.io.BufferedWriter;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.FileReader;
import java.io.FileWriter;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.sql.SQLIntegrityConstraintViolationException;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;


@Slf4j
@Service
public class OptLogServiceImpl implements OptLogService {

    private static final String SUPER_ADMIN_FLAG = "SUPER_ADMINISTRATOR";

    private static final Integer MAX_PAGE_SIZE = 999;

    private static final int DEFAULT_V2_API_VERSION = 2;

    @Autowired
    private OptLogRepository optLogRepository;
    @Autowired
    private UserClient userClient;
    @Autowired
    private IDGenerator idGenerator;
    @Autowired
    private ProductionStatisticService productionStatisticService;

    @Value("${digitalhuman.plat.opt-log.query-batch:5000}")
    private int exportPageSize;

    @Value("${digitalhuman.plat.opt-log.csv-size:50000}")
    private int exportCsvSize;

    @Value("${digitalhuman.plat.opt-log.pool-core-size:12}")
    private int coreSize;

    @Value("${digitalhuman.plat.opt-log.subtask-num:12}")
    private int subtaskNum;

    @Value("${digitalhuman.plat.opt-log.max-export-year:1}")
    private int maxExportYear;

    ThreadPoolExecutor threadPoolExecutor;


    @PostConstruct
    public void init() {
        threadPoolExecutor = new ThreadPoolExecutor(
                coreSize,                          // 核心线程池大小
                2 * coreSize,                          // 最大线程池大小
                30,                          // 线程空闲时间
                TimeUnit.SECONDS,            // 线程空闲时间单位
                new LinkedBlockingQueue<>(2 * coreSize), // 任务队列
                new ThreadPoolExecutor.AbortPolicy() // 拒绝策略
        );
    }
    @Override
    public OptLog create(OptLog optLog) {
        OptLogModel target = OptLogModel.builder().build();
        BeanUtils.copyProperties(optLog, target);
        target.setLogId(idGenerator.generate("opt"));
        target.setIsDelete(0);
        try {
            OptLogModel saveRes = optLogRepository.save(target);
            OptLog res = OptLog.builder().build();
            BeanUtils.copyProperties(saveRes, res);
            productionStatisticService.statisticProductionOptLog(optLog);
            return res;
        } catch (DataIntegrityViolationException e) {
            if (e.getCause() instanceof ConstraintViolationException
                    && e.getCause().getCause() instanceof SQLIntegrityConstraintViolationException
                    && e.getCause().getCause().getMessage().contains("Duplicate entry")) {
                throw new DigitalHumanCommonException(Error.DUPLICATED_OPT_LOG);
            }
            throw e;
        }
    }

    @Override
    public PageResponse<OptLog> list(List<String> menus, OptLogListRequest request, String accountId) {

        // 前置判断 如果参数中的 optUserName optModules optTypes没有映射出对应的数据直接返回空
        List<String> optUserIds = processOptUserName(request);
        List<Integer> optModules = processOptModules(menus, request);
        List<Integer> optTypes = processOptTypes(menus, request);
        if ((StringUtils.isNotEmpty(request.getOptUserName()) && optUserIds.isEmpty())
                || (!CollectionUtils.isEmpty(request.getOptModules()) && optModules.isEmpty())
                || (!CollectionUtils.isEmpty(request.getOptTypes()) && optTypes.isEmpty())) {
            return PageResponse.success(request.getPageNo()
                    , request.getPageSize(), 0, Lists.newArrayList());

        }
        log.info("Opt log list optUserIds:{},optModules:{},optTypes:{}", optUserIds, optModules, optTypes);

        Specification<OptLogModel> spec = (root, query, builder) -> {
            List<Predicate> predicates = Lists.newArrayList();

            if (request.getPlatCode().equals(PlatCode.SAAS)) {
                request.setOptAccountIds(Lists.newArrayList(accountId));
            }
            if (!CollectionUtils.isEmpty(request.getOptAccountIds())) {
                CriteriaBuilder.In<String> builderIn = builder.in(root.get("optAccountId"));
                request.getOptAccountIds().forEach(item -> {
                    builderIn.value(item);
                });
                predicates.add(builderIn);
            }


            if (!CollectionUtils.isEmpty(optUserIds)) {
                CriteriaBuilder.In<String> builderIn = builder.in(root.get("optUserId"));
                optUserIds.forEach(item -> {
                    builderIn.value(item);
                });
                predicates.add(builderIn);
            }

            if (!CollectionUtils.isEmpty(optModules)) {
                CriteriaBuilder.In<Integer> builderIn = builder.in(root.get("optModule"));
                optModules.forEach(item -> {
                    builderIn.value(item);
                });
                predicates.add(builderIn);
            }


            if (!CollectionUtils.isEmpty(optTypes)) {
                CriteriaBuilder.In<Integer> builderIn = builder.in(root.get("optType"));
                optTypes.forEach(item -> {
                    builderIn.value(item);
                });
                predicates.add(builderIn);
            }
            if (null != request.getStartTime()) {
                predicates.add(builder.ge(root.get("optTime"), request.getStartTime().getTime()));
            }
            if (null != request.getEndTime()) {
                predicates.add(builder.le(root.get("optTime"), request.getEndTime().getTime()));
            }
            // 未删除
            predicates.add(builder.equal(root.get("isDelete"), 0));
            if (!StringUtils.isEmpty(request.getOptObjectName())) {
                predicates.add(builder.like(root.get("optObjectName"), '%' + request.getOptObjectName() + '%'));
            }

            return builder.and(predicates.toArray(new Predicate[predicates.size()]));
        };
        Page<OptLogModel> res = optLogRepository.findAll(spec, PageRequest.of(request.getPageNo() - 1
                , request.getPageSize(), Sort.by(Sort.Direction.DESC, "optTime")));

        PageResponse<OptLog> pageResponse = PageResponse.success(res.getPageable().getPageNumber() + 1
                , res.getPageable().getPageSize(), res.getTotalElements(), res.getContent().stream().map(item -> {
                    OptLog optLog = OptLog.builder().build();
                    BeanUtils.copyProperties(item, optLog);
                    return optLog;
                }).collect(Collectors.toList()));
        return pageResponse;
    }

    @Override
    public PageResponse<OptLogView> listOptLogView(List<String> accountMenus
            , OptLogListRequest request, String accountId) {
        PageResponse<OptLog> resList = list(accountMenus, request, accountId);
        Set<String> optAccountIds = Sets.newHashSet();
        Set<String> optUserIds = Sets.newHashSet();
        resList.getPage().getResult().forEach(item -> {
            optAccountIds.add(item.getOptAccountId());
            optUserIds.add(item.getOptUserId());
        });
        Map<String, String> userid2NameMap = Maps.newHashMap();
        Map<String, String> accountid2NameMap = Maps.newHashMap();
        if (!optUserIds.isEmpty()) {
            UserResult<List<UserQueryResVO>> res = userClient.internalBatchGetUser(
                    DEFAULT_V2_API_VERSION + ""
                    , UserBatchGetReqVO.builder()
                            .userIds(optUserIds.stream().collect(Collectors.toList()))
                            .build());
            res.getResult().forEach(item -> {
                userid2NameMap.put(item.getUserId(), item.getUsername());
            });
        }

        if (!optAccountIds.isEmpty()) {
            UserResult<List<AccountQueryResVO>> res = userClient.internalBatchGetAccount(
                    DEFAULT_V2_API_VERSION + ""
                    , AccountBatchGetReqVO.builder()
                            .accountIds(optAccountIds.stream().collect(Collectors.toList()))
                            .build());
            res.getResult().forEach(item -> {
                accountid2NameMap.put(item.getAccountId(), item.getName());
            });
        }

        List<OptLogView> result = Lists.newArrayList();
        Map<Integer, String> optModuleNameMap = OptModuleHolder.getOptModuleNameMap();
        Map<Integer, String> optTypeNameMap = OptModuleHolder.getOptTypeNameMap();

        resList.getPage().getResult().forEach(item -> {
            result.add(convertToLogView(item, userid2NameMap, accountid2NameMap
                    , optModuleNameMap, optTypeNameMap));
        });
        PageResponse<OptLogView> pageResponse = PageResponse.success(resList.getPage().getPageNo()
                , resList.getPage().getPageSize(), resList.getPage().getTotalCount()
                , result);
        return pageResponse;
    }

    @Override
    public File exportOptLogByQuery(List<String> accountMenus
            , OptLogListRequest request, String accountId) {

        // 前置判断 如果参数中的 optUserName optModules optTypes没有映射出对应的数据直接返回空
        List<String> optUserIds = processOptUserName(request);
        List<Integer> optModules = processOptModules(accountMenus, request);
        List<Integer> optTypes = processOptTypes(accountMenus, request);
        if ((StringUtils.isNotEmpty(request.getOptUserName()) && optUserIds.isEmpty())
                || (!CollectionUtils.isEmpty(request.getOptModules()) && optModules.isEmpty())
                || (!CollectionUtils.isEmpty(request.getOptTypes()) && optTypes.isEmpty())) {
            try {
                return File.createTempFile("file-zip-", ".zip");
            } catch (IOException e) {
                throw new DigitalHumanCommonException("导出空zip文件失败");
            }
        }
        log.info("Opt log list optUserIds:{},optModules:{},optTypes:{}", optUserIds, optModules, optTypes);

        // 构造查询条件
        if (request.getPlatCode().equals(PlatCode.SAAS)) {
            request.setOptAccountIds(Lists.newArrayList(accountId));
        } else if (CollectionUtils.isEmpty(request.getOptAccountIds())) {
            request.setOptAccountIds(null);
        }
        optModules = CollectionUtils.isEmpty(optModules) ? null : optModules;
        request.setOptModules(optModules);
        final List<Integer> finalOptTypes = CollectionUtils.isEmpty(optTypes) ? null : optTypes;
        final List<String> finalOptUserIds = CollectionUtils.isEmpty(optUserIds) ? null : optUserIds;
        if (StringUtils.isNotEmpty(request.getOptObjectName())) {
            request.setOptObjectName("%" + request.getOptObjectName() + "%");
        } else {
            request.setOptObjectName("%%");
        }
        LocalDateTime now = LocalDateTime.now();
        long endSearchTime = now.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
        LocalDateTime oneYearAgo = now.minusYears(maxExportYear);
        long beginSearchTime = oneYearAgo.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
        if (null != request.getStartTime() && null != request.getEndTime()) {
            beginSearchTime = request.getStartTime().getTime();
            endSearchTime = request.getEndTime().getTime();
        } else {
            // 导出日志没有给时间范围，则判断最早的数据到现在是否超过一年，如果超过则提示错误，不超过则导出近一年数据
            Optional<OptLogModel> firstOptLogModel = request.getOptAccountIds() == null ? optLogRepository.findFirstOptLogModel()
                    : optLogRepository.findFirstOptLogModelByAccountId(request.getOptAccountIds());
            if (firstOptLogModel.isPresent() && firstOptLogModel.get().getOptTime() < beginSearchTime) {
                log.debug("one year timeSample is {}, first record timeSample is {}", beginSearchTime, firstOptLogModel.get().getOptTime());
                throw new DigitalHumanCommonException("最多可导出" + maxExportYear + "年的操作记录，请重新选择操作时间范围");
            }
        }

        // 校验传入的时间范围是否超过最大导出时间
        LocalDateTime endDateTime = LocalDateTime.ofInstant(Instant.ofEpochMilli(endSearchTime), ZoneId.systemDefault());
        LocalDateTime minDateTime = endDateTime.minusYears(maxExportYear);
        long minDateTimesample = minDateTime.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
        log.info("begin export opt log , beginTimesample is {}, endTime sample is {}, min time sample is {}", beginSearchTime, endSearchTime, minDateTimesample);
        if (beginSearchTime < minDateTimesample) {
            throw new DigitalHumanCommonException("最多可导出" + maxExportYear + "年的操作记录，请重新选择操作时间范围");
        }

        // 构造临时文件目录
        String random = idGenerator.generate(9);
        String directoryPath = "/tmp/dh-opt-log/" + accountId + "/" + random;
        log.info("begin create csv files, directory is {}, startTime is {}, endTime is {}", directoryPath, beginSearchTime, endSearchTime);
        if (!Files.exists(Paths.get(directoryPath))) {
            try {
                Files.createDirectories(Paths.get(directoryPath));
            } catch (IOException e) {
                e.printStackTrace();
            }
        }

        // 按照时间拆分任务，并发生成csv子文件
        ArrayList<CompletableFuture> futures = Lists.newArrayList();
        List<String> csvPaths = Lists.newArrayList();
        for (int i = 0; i < subtaskNum; i++) {
            long startTime = beginSearchTime + ((endSearchTime - beginSearchTime) * i / subtaskNum);
            long endTime = beginSearchTime + ((endSearchTime - beginSearchTime) * (i + 1) / subtaskNum);
            String index = String.format("%03d", i);

            CompletableFuture<List<String>> c = CompletableFuture.supplyAsync(() -> {
                return exportCsv(request.getPlatCode().name(), request.getOptAccountIds(), finalOptUserIds, request.getOptModules(),
                        finalOptTypes, request.getOptObjectName(), index, directoryPath, startTime, endTime);
            }, threadPoolExecutor).whenComplete((p, t) -> {
                if (t != null) {
                    log.error("Unexpected error occurs while async produce csv", t);
                }
                if (p != null) {
                    csvPaths.addAll(p);
                }
            });
            futures.add(c);
        }
        CompletableFuture<Void> voidCompletableFuture = CompletableFuture.allOf(futures.toArray(new CompletableFuture[futures.size()]));
        List<Object> collect = futures.stream().map(CompletableFuture::join).collect(Collectors.toList());
        log.debug("async produce csv detail is {}", collect);
        csvPaths.sort((a, b) -> a.compareTo(b));

        // 指定合并后的输出文件路径，并合并csv文件为最终的zip文件
        String packgeDirectory = directoryPath + "/packge";
        if (!Files.exists(Paths.get(packgeDirectory))) {
            try {
                Files.createDirectories(Paths.get(packgeDirectory));
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        File directory = new File(packgeDirectory);
        mergeCSVFiles(csvPaths, packgeDirectory);
        File zipFile = null;
        ZipOutputStream out = null;
        try {
            zipFile = File.createTempFile("file-zip-", ".zip");
            out = new ZipOutputStream(new FileOutputStream(zipFile));
            // 遍历目录中的文件，并添加到ZIP输出流中
            addFilesToZip(directory, out);
            out.close();
        } catch (IOException e) {
            throw new DigitalHumanCommonException("添加压缩文件失败");
        }
        return zipFile;
    }

    /**
     * 将多个CSV文件合并为一个文件
     *
     * @param inputFiles 输入的CSV文件列表
     * @param packgeDirectory 输出文件的目录
     */
    public void mergeCSVFiles(List<String> inputFiles, String packgeDirectory) {
        int fileNo = 0;
        int readCount = 0;
        String outputFile = packgeDirectory + "/" + fileNo + ".csv";
        BufferedWriter writer = null;
        CSVPrinter csvPrinter = null;
        try {
            writer = new BufferedWriter(new FileWriter(outputFile));
            csvPrinter = new CSVPrinter(writer, CSVFormat.DEFAULT);
            writer.write("\ufeff");
            List<String> headers = Lists.newArrayList("操作时间", "操作账户", "操作用户", "操作模块", "操作对象", "操作类型");
            csvPrinter.printRecord(headers);
            for (String inputFile : inputFiles) {
                try (CSVParser csvParser = new CSVParser(new FileReader(inputFile), CSVFormat.DEFAULT)) {
                    List<CSVRecord> records = csvParser.getRecords();
                    for (CSVRecord record : records) {
                        // 将记录写入输出文件
                        csvPrinter.printRecord(record);
                        readCount++;
                        if (readCount >= exportCsvSize) {
                            fileNo++;
                            readCount = 0;
                            writer.close();
                            csvPrinter.close();
                            outputFile = packgeDirectory + "/" + fileNo + ".csv";
                            writer = new BufferedWriter(new FileWriter(outputFile));
                            csvPrinter = new CSVPrinter(writer, CSVFormat.DEFAULT);
                            writer.write("\ufeff");
                            csvPrinter.printRecord(headers);
                        }
                    }
                }
            }
        } catch (IOException e) {
            throw new DigitalHumanCommonException("Merge csv files error!");
        } finally {
            try {
                if (writer != null) {
                    writer.close();
                }
                if (csvPrinter != null) {
                    csvPrinter.close();
                }
            } catch (IOException e) {
                log.error("close csv printer error!");
            }
        }
    }

    /**
     * 将符合条件的数据导出为CSV格式文件
     *
     * @param platCode        平台代码，只支持SAAS和ADMIN
     * @param accountIds      账户ID列表
     * @param userIds          用户ID列表
     * @param optModules       操作模块列表
     * @param optTypes         操作类型列表
     * @param optObjectName    操作对象名称
     * @param index            导出的索引文件名
     * @param directoryPath     导出的目录路径
     * @param startTime         查询开始时间
     * @param endTime           查询结束时间
     * @return                  导出的CSV文件列表
     * @throws DigitalHumanCommonException   当平台代码不支持时抛出异常
     */
    public List<String> exportCsv(String platCode, List<String> accountIds,
                                  List<String> userIds, List<Integer> optModules,
                                  List<Integer> optTypes, String optObjectName,
                                  String index, String directoryPath,
                                  long startTime, long endTime) {
        if (!platCode.equals(PlatCode.SAAS.name()) && !platCode.equals(PlatCode.ADMIN.name())) {
            throw new DigitalHumanCommonException("platCode " + platCode + " is not support");
        }
        log.debug("Begin export csv, accountIds is {}, userIds is {}, optModules is {}, optTypes is {}, optObjectName is {}, startTime is {}, endTime is {}",
                accountIds, userIds, optModules, optTypes, optObjectName, startTime, endTime);
        // 当前已处理的opt-log条数
        long count = 0;
        // opt-log总条数
        long total = getOptLogCount(accountIds, userIds, optModules, optTypes, optObjectName, startTime, endTime);
        log.debug("count in query is {}", total);
        // 每次查询的记录数
        int pageSize = exportPageSize;
        // 一个csv文件的行数
        int csvSize = exportCsvSize;
        if (pageSize > csvSize) {
            pageSize = csvSize;
        }
        if (pageSize < 1 || csvSize < 2) {
            log.error("pageSize is {}，csvSize is {}, pageSize must >=1 and csvSize must >=2 ");
            throw new DigitalHumanCommonException("pageSize不能小于1，csvSize不能小于2");
        }
        // 导出文件数目
        long fileCount = total % csvSize == 0 ? (total / csvSize) : (total / csvSize) + 1;
        // 与list方法中同样的数据映射记录
        Set<String> optAccountIds = Sets.newHashSet();
        Set<String> optUserIds = Sets.newHashSet();
        Map<String, String> userid2NameMap = Maps.newHashMap();
        Map<String, String> accountid2NameMap = Maps.newHashMap();
        Map<Integer, String> optModuleNameMap = OptModuleHolder.getOptModuleNameMap();
        Map<Integer, String> optTypeNameMap = OptModuleHolder.getOptTypeNameMap();
        List<String> outFileList = Lists.newArrayList();
        int pageNo = 0;
        log.debug("Begin insert into tem csv , startTime is {}, endTime is {}, pageSize is {}, accountIds is {}, userIds is {}," +
                " optModules is {}, optTypes is {}, optObjectName is {}", startTime, endTime, pageSize, accountIds,
                userIds, optModules, optTypes, optObjectName);
        for (int f = 1; f <= fileCount; f++) {
            directoryPath = directoryPath + "/" + index;
            if (!Files.exists(Paths.get(directoryPath))) {
                try {
                    Files.createDirectories(Paths.get(directoryPath));
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
            String nameInx = String.format("%03d", f);
            String fileName = directoryPath + "/" + nameInx + ".csv";
            outFileList.add(fileName);
            try (
                    BufferedWriter writer = new BufferedWriter(new FileWriter(fileName));
                    CSVPrinter csvPrinter = new CSVPrinter(writer, CSVFormat.DEFAULT);
            ) {
                while (count < total && count < csvSize * f) {
                    List<OptLogModel> pageModels = null;
                    Page<OptLogModel> optLogPage = getOptLogPage(accountIds, userIds, optModules, optTypes, optObjectName, startTime, endTime, pageNo, pageSize);
                    if (optLogPage.getContent().size() == 0) {
                        log.debug("error occurs and it's startTime is {}, endTime is {}", startTime, endTime);
                        break;
                    }
                    pageModels = optLogPage.getContent();
                    count += pageSize;
                    // 用于控制下次查询的起始范围
                    pageNo += 1;
                    log.debug("begin insert data to csv , f={}, count={}, total={}, file count max ={}"
                            , f, count, total, csvSize * f);
                    updateMap(pageModels, optAccountIds, optUserIds, userid2NameMap, accountid2NameMap);
                    List<List<String>> datas = Lists.newArrayList();
                    pageModels.forEach(item -> {
                        datas.add(moudleConvertToLogData(item, userid2NameMap, accountid2NameMap
                                , optModuleNameMap, optTypeNameMap));
                    });
                    for (List<String> row : datas) {
                        csvPrinter.printRecord(row);
                    }
                    csvPrinter.flush();
                }
            } catch (IOException e) {
                throw new DigitalHumanCommonException("导出失败");
            }
        }
        return outFileList;
    }

    /**
     * 更新映射Map
     *
     * @param pageModels 从数据库查询到的数据
     * @param optAccountIds 账户ID集合
     * @param optUserIds 用户ID集合
     * @param userid2NameMap 用户ID到用户名的映射
     * @param accountid2NameMap 账户ID到账户名的映射
     */
    private void updateMap(List<OptLogModel> pageModels, Set<String> optAccountIds, Set<String> optUserIds,
                           Map<String, String> userid2NameMap, Map<String, String> accountid2NameMap) {
        List<String> apendAccountIds = Lists.newArrayList();
        List<String> apendUserIds = Lists.newArrayList();
        pageModels.forEach(
                item -> {
                    if (!optAccountIds.contains(item.getOptAccountId())) {
                        optAccountIds.add(item.getOptAccountId());
                        apendAccountIds.add(item.getOptAccountId());
                    }
                    if (!optUserIds.contains(item.getOptUserId())) {
                        optUserIds.add(item.getOptUserId());
                        apendUserIds.add(item.getOptUserId());
                    }
                }
        );
        if (!apendUserIds.isEmpty()) {
            UserResult<List<UserQueryResVO>> res = userClient.internalBatchGetUser(
                    DEFAULT_V2_API_VERSION + ""
                    , UserBatchGetReqVO.builder()
                            .userIds(apendUserIds)
                            .build());
            res.getResult().forEach(item -> {
                userid2NameMap.put(item.getUserId(), item.getUsername());
            });
        }
        if (!apendAccountIds.isEmpty()) {
            UserResult<List<AccountQueryResVO>> res = userClient.internalBatchGetAccount(
                    DEFAULT_V2_API_VERSION + ""
                    , AccountBatchGetReqVO.builder()
                            .accountIds(apendAccountIds)
                            .build());
            res.getResult().forEach(item -> {
                accountid2NameMap.put(item.getAccountId(), item.getName());
            });
        }
    }

    private List<String> moudleConvertToLogData(OptLogModel model
            , Map<String, String> userid2NameMap
            , Map<String, String> accountid2NameMap
            , Map<Integer, String> optModuleNameMap
            , Map<Integer, String> optTypeNameMap) {
        List<String> data = Lists.newArrayList();
        data.add(DateTimeUtil.format(model.getOptTime()));
        if (SUPER_ADMIN_FLAG.equals(model.getOptAccountId())) {
            data.add("平台管理");
        } else {
            data.add(accountid2NameMap.getOrDefault(model.getOptAccountId(), model.getOptAccountId()));
        }
        data.add(userid2NameMap.getOrDefault(model.getOptUserId(), model.getOptUserId()));
        data.add(optModuleNameMap.get(model.getOptModule()));
        data.add(model.getOptObjectName());
        data.add(optTypeNameMap.get(model.getOptType()));
        return data;
    }

    private static void addFilesToZip(File file, ZipOutputStream out) throws IOException {
        if (file.isDirectory()) {
            File[] files = file.listFiles();
            if (files != null) {
                for (File childFile : files) {
                    addFilesToZip(childFile, out);
                }
            }
        } else {
            try (FileInputStream fis = new FileInputStream(file)) {
                // 添加文件到ZIP输出流中
                ZipEntry entry = new ZipEntry(file.getName());
                out.putNextEntry(entry);
                byte[] buffer = new byte[1024];
                int len;
                while ((len = fis.read(buffer)) > 0) {
                    out.write(buffer, 0, len);
                }
                out.closeEntry();
            }
        }
    }

    private OptLogView convertToLogView(OptLog optLog
            , Map<String, String> userid2NameMap
            , Map<String, String> accountid2NameMap
            , Map<Integer, String> optModuleNameMap
            , Map<Integer, String> optTypeNameMap) {
        OptLogView res = new OptLogView();
        res.setOptModule(optModuleNameMap.get(optLog.getOptModule()));
        res.setOptType(optTypeNameMap.get(optLog.getOptType()));
        res.setOptObjtName(optLog.getOptObjectName());
        if (SUPER_ADMIN_FLAG.equals(optLog.getOptAccountId())) {
            res.setOptAccountName("平台管理");
        } else {
            res.setOptAccountName(accountid2NameMap.getOrDefault(optLog.getOptAccountId(), optLog.getOptAccountId()));
        }
        res.setOptUserName(userid2NameMap.getOrDefault(optLog.getOptUserId(), optLog.getOptUserId()));
        res.setOptTime(DateTimeUtil.format(optLog.getOptTime()));
        res.setLogId(optLog.getLogId());
        return res;
    }

    @Override
    public List<OptModuleItem> queryOptModule(List<String> menus, PlatCode platCode) {
        if (CollectionUtils.isEmpty(menus)) {
            return Lists.newArrayList();
        }

        // 获取当前有权限操作模块
        List<OptModuleItem> specialedMenuModules = getSpecialedMenuModules(menus, optModuleConfigItem -> {
            OptModuleItem res = OptModuleItem.builder()
                    .value(optModuleConfigItem.getModuleId())
                    .label(optModuleConfigItem.getModuleName()).build();
            res.setChildren(Lists.newArrayList());
            return res;
        }, platCode);

        return specialedMenuModules;
    }


    private <T extends TreeNode> List<T> getSpecialedMenuModules(List<String> userMenus
            , Function<OptModuleHolder.OptModuleConfigItem, T> function, PlatCode platCode) {
        List<T> result = Lists.newArrayList();
        List<OptModuleHolder.OptModuleConfigItem> optModules = OptModuleHolder.getOptModules();
        Map<Integer, OptModuleHolder.OptModuleConfigItem> optModuleMap = OptModuleHolder.getOptModuleMap();

        Map<Integer, T> resultMap = Maps.newHashMap();
        optModules.forEach(item -> {
            item.forEachTopDown(new Consumer<OptModuleHolder.OptModuleConfigItem>() {
                List<Integer> nodesIds = Lists.newArrayList();
                OptModuleHolder.OptModuleConfigItem lastNode;

                @Override
                public void accept(OptModuleHolder.OptModuleConfigItem optModuleConfigItem) {
                    if (null != lastNode) {
                        if (CollectionUtils.isEmpty(lastNode.getChildren())
                                && CollectionUtils.isEmpty(optModuleConfigItem.getChildren())) {
                            nodesIds.remove(nodesIds.size() - 1);
                        } else if (CollectionUtils.isEmpty(lastNode.getChildren())
                                && !CollectionUtils.isEmpty(optModuleConfigItem.getChildren())) {
                            nodesIds.remove(nodesIds.size() - 1);
                            nodesIds.remove(nodesIds.size() - 1);
                        }
                    }

                    nodesIds.add(optModuleConfigItem.getModuleId());
                    // 叶子节点
                    if (CollectionUtils.isEmpty(optModuleConfigItem.getChildren())) {
                        if (!(platCode.equals(PlatCode.SAAS)
                                && optModuleConfigItem.isSuperAdminModule())
                                && (userMenus.contains(optModuleConfigItem.getMenus())
                                || platCode.equals(PlatCode.ADMIN)
                                || StringUtils.isEmpty(optModuleConfigItem.getMenus()))) {
                            for (int i = 0; i < nodesIds.size(); ++i) {
                                int id = nodesIds.get(i);
                                OptModuleHolder.OptModuleConfigItem node = optModuleMap.get(id);
                                T resultItem = resultMap.get(id);
                                if (null == resultItem) {
                                    resultItem = function.apply(node);
                                    resultMap.put(id, resultItem);
                                    if (i - 1 >= 0) {
                                        resultMap.get(nodesIds.get(i - 1)).getChildren().add(resultItem);
                                    }
                                }

                                if (i - 1 < 0) {
                                    if (!result.contains(resultItem)) {
                                        result.add(resultItem);
                                    }
                                }
                            }
                        }
                    }
                    lastNode = optModuleConfigItem;

                }
            });
        });
        return result;
    }

    @Override
    public List<String> queryOptType(List<String> menus, PlatCode platCode) {
        List<OptModuleHolder.OptType> optTypes = getOptType(menus, platCode);
        Set<String> result = Sets.newHashSet();
        optTypes.forEach(item -> {
            result.add(item.getOptTypeName());
        });
        return result.stream().collect(Collectors.toList());
    }

    private List<OptModuleHolder.OptType> getOptType(List<String> menus, PlatCode platCode) {
        List<OptModuleHolder.OptType> optTypes = Lists.newArrayList();
        List<OptModuleHolder.OptModuleConfigItem> specialedMenuModules
                = getSpecialedMenuModules(menus, optModuleConfigItem -> {
            OptModuleHolder.OptModuleConfigItem res = OptModuleHolder.OptModuleConfigItem.builder()
                    .moduleName(optModuleConfigItem.getModuleName())
                    .moduleId(optModuleConfigItem.getModuleId())
                    .menus(optModuleConfigItem.getMenus())
                    .optTypes(optModuleConfigItem.getOptTypes())
                    .build();
            res.setChildren(Lists.newArrayList());
            return res;
        }, platCode);
        specialedMenuModules.forEach(item -> {
            item.forEachTopDown(node -> {
                if (CollectionUtils.isEmpty(node.getChildren())) {
                    optTypes.addAll(node.getOptTypes());
                }
            });
        });
        return optTypes;
    }


    private List<Integer> processOptModules(List<String> menus
            , OptLogListRequest request) {
        if (CollectionUtils.isEmpty(request.getOptModules())) {
            return Lists.newArrayList();
        }
        List<Integer> result = Lists.newArrayList();
        List<OptModuleItem> optModules = queryOptModule(menus, request.getPlatCode());
        optModules.forEach(item -> {
            item.forEachTopDown(optModuleItem -> {
                if (request.getOptModules().contains(optModuleItem.getValue())) {
                    result.add(optModuleItem.getValue());
                }
            });
        });
        return result;
    }

    private List<Integer> processOptTypes(List<String> menus
            , OptLogListRequest request) {
        if (CollectionUtils.isEmpty(request.getOptTypes())) {
            return Lists.newArrayList();
        }
        List<Integer> result = Lists.newArrayList();
        List<OptModuleHolder.OptType> optTypes = getOptType(menus, request.getPlatCode());
        optTypes.forEach(item -> {
            if (request.getOptTypes().contains(item.getOptTypeName())) {
                result.add(item.getOptTypeId());
            }
        });
        return result;
    }

    private List<String> processOptUserName(OptLogListRequest request) {
        if (StringUtils.isEmpty(request.getOptUserName())) {
            return Lists.newArrayList();
        }
        UserQueryReqVO userQueryReqVO = new UserQueryReqVO();
        userQueryReqVO.setPageNo(1);
        userQueryReqVO.setPageSize(MAX_PAGE_SIZE);
        userQueryReqVO.setName(request.getOptUserName());
        userQueryReqVO.setDeleted(true);

        UserResult<com.baidu.acg.dh.user.client.PageResult<UserQueryResVO>> res
                = userClient.internalQueryUser( DEFAULT_V2_API_VERSION + "", userQueryReqVO);

        return res.getResult().getResult().stream()
                .map(UserQueryResVO::getUserId).collect(Collectors.toList());
    }

    public long getOptLogCount(List<String> accountIds, List<String> userIds,
                               List<Integer> optModules, List<Integer> optTypes,
                               String optObjectName, long startTime, long endTime) {
        Specification<OptLogModel> spec = (root, query, builder) -> {
            List<Predicate> predicates = Lists.newArrayList();

            if (!CollectionUtils.isEmpty(accountIds)) {
                CriteriaBuilder.In<String> builderIn = builder.in(root.get("optAccountId"));
                accountIds.forEach(item -> {
                    builderIn.value(item);
                });
                predicates.add(builderIn);
            }
            if (!CollectionUtils.isEmpty(userIds)) {
                CriteriaBuilder.In<String> builderIn = builder.in(root.get("optUserId"));
                userIds.forEach(item -> {
                    builderIn.value(item);
                });
                predicates.add(builderIn);
            }
            if (!CollectionUtils.isEmpty(optModules)) {
                CriteriaBuilder.In<Integer> builderIn = builder.in(root.get("optModule"));
                optModules.forEach(item -> {
                    builderIn.value(item);
                });
                predicates.add(builderIn);
            }
            if (!CollectionUtils.isEmpty(optTypes)) {
                CriteriaBuilder.In<Integer> builderIn = builder.in(root.get("optType"));
                optTypes.forEach(item -> {
                    builderIn.value(item);
                });
                predicates.add(builderIn);
            }
            if (0 != startTime) {
                predicates.add(builder.ge(root.get("optTime"), startTime));
            }
            if (0 != endTime) {
                predicates.add(builder.le(root.get("optTime"), endTime));
            }
            // 未删除
            predicates.add(builder.equal(root.get("isDelete"), 0));
            if (!StringUtils.isEmpty(optObjectName)) {
                predicates.add(builder.like(root.get("optObjectName"), '%' + optObjectName + '%'));
            }

            return builder.and(predicates.toArray(new Predicate[predicates.size()]));
        };

        return optLogRepository.count(spec);
    }

    public Page<OptLogModel> getOptLogPage(List<String> accountIds, List<String> userIds, List<Integer> optModules,
                                           List<Integer> optTypes, String optObjectName, long startTime,
                                           long endTime, Integer pageNo, Integer pageSize) {
        Specification<OptLogModel> spec = (root, query, builder) -> {
            List<Predicate> predicates = Lists.newArrayList();

            if (!CollectionUtils.isEmpty(accountIds)) {
                CriteriaBuilder.In<String> builderIn = builder.in(root.get("optAccountId"));
                accountIds.forEach(item -> {
                    builderIn.value(item);
                });
                predicates.add(builderIn);
            }
            if (!CollectionUtils.isEmpty(userIds)) {
                CriteriaBuilder.In<String> builderIn = builder.in(root.get("optUserId"));
                userIds.forEach(item -> {
                    builderIn.value(item);
                });
                predicates.add(builderIn);
            }
            if (!CollectionUtils.isEmpty(optModules)) {
                CriteriaBuilder.In<Integer> builderIn = builder.in(root.get("optModule"));
                optModules.forEach(item -> {
                    builderIn.value(item);
                });
                predicates.add(builderIn);
            }
            if (!CollectionUtils.isEmpty(optTypes)) {
                CriteriaBuilder.In<Integer> builderIn = builder.in(root.get("optType"));
                optTypes.forEach(item -> {
                    builderIn.value(item);
                });
                predicates.add(builderIn);
            }
            if (0 != startTime) {
                predicates.add(builder.ge(root.get("optTime"), startTime));
            }
            if (0 != endTime) {
                predicates.add(builder.le(root.get("optTime"), endTime));
            }
            // 未删除
            predicates.add(builder.equal(root.get("isDelete"), 0));
            if (!StringUtils.isEmpty(optObjectName)) {
                predicates.add(builder.like(root.get("optObjectName"), '%' + optObjectName + '%'));
            }
            return builder.and(predicates.toArray(new Predicate[predicates.size()]));
        };

        return optLogRepository.findAll(spec, PageRequest.of(pageNo
                , pageSize, Sort.by(Sort.Direction.ASC, "id")));
    }


}
