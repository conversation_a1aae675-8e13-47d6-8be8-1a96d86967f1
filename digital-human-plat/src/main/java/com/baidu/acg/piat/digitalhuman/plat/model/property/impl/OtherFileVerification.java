package com.baidu.acg.piat.digitalhuman.plat.model.property.impl;

import com.baidu.acg.piat.digitalhuman.plat.model.property.FileVerification;
import com.baidu.acg.piat.digitalhuman.plat.model.property.PropertyFileResponse;
import com.baidu.acg.piat.digitalhuman.plat.model.property.PropertyType;
import io.vavr.Tuple2;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.UUID;

@Slf4j
@Component
@RequiredArgsConstructor
@Getter
public class OtherFileVerification implements FileVerification {
    @Override
    public boolean support(PropertyType type) {
        return type.equals(PropertyType.SOUND) || type.equals(PropertyType.BACKGROUND);
    }

    @Override
    public Tuple2<PropertyFileResponse, String> verify(MultipartFile file, String type) throws IOException {
        String originalFilename = file.getOriginalFilename();
        int index = originalFilename.lastIndexOf(".");
        String propertyId = originalFilename.substring(0, index) + "_" + UUID.randomUUID().toString();
        return new Tuple2<>(PropertyFileResponse.builder().propertyId(propertyId).frameCount(0).build(), null);
    }
}
