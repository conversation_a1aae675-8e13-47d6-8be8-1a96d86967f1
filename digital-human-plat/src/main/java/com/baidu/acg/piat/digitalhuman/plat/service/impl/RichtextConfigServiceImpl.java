package com.baidu.acg.piat.digitalhuman.plat.service.impl;

import java.util.List;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicReference;

import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.parsers.ParserConfigurationException;

import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.Node;

import com.baidu.acg.piat.digitalhuman.common.exception.DigitalHumanCommonException;
import com.baidu.acg.piat.digitalhuman.common.model.PageResponse;
import com.baidu.acg.piat.digitalhuman.common.model.PageResult;
import com.baidu.acg.piat.digitalhuman.common.richconfig.DrmlAction;
import com.baidu.acg.piat.digitalhuman.common.richconfig.JsonToXmlRequest;
import com.baidu.acg.piat.digitalhuman.common.richconfig.RichtextConfig;
import com.baidu.acg.piat.digitalhuman.common.util.DomUtil;
import com.baidu.acg.piat.digitalhuman.plat.dao.RichtextConfigRepository;
import com.baidu.acg.piat.digitalhuman.plat.model.common.Constant;
import com.baidu.acg.piat.digitalhuman.plat.model.config.RichtextConfigModel;
import com.baidu.acg.piat.digitalhuman.plat.service.CharacterService;
import com.baidu.acg.piat.digitalhuman.plat.service.ProjectService;
import com.baidu.acg.piat.digitalhuman.plat.service.RichtextConfigService;
import com.google.common.collect.Lists;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * Created on 2020/4/22 21:50.
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class RichtextConfigServiceImpl implements RichtextConfigService {

    private final RichtextConfigRepository repository;

    private final ProjectService projectService;

    private final CharacterService characterService;

    private static final int DEFAULT_API_VERSION = 1;

    @Override
    public RichtextConfig create(String projectId, RichtextConfig request) {
        log.debug("create richtext config, projectId:{}, content:{}", projectId, request.getContent());

        if (StringUtils.isEmpty(request.getProjectId())) {
            throw new DigitalHumanCommonException("ProjectId cannot be empty.");
        }
        if (StringUtils.isEmpty(request.getContent())) {
            throw new DigitalHumanCommonException("Richtext content cannot be empty");
        }
        projectService.detail(projectId);
        RichtextConfigModel configModel = RichtextConfigModel.builder()
                .configId(ObjectId.get().toHexString())
                .projectId(projectId)
                .content(request.getContent())
                .downloadUrl(request.getDownloadUrl())
                .audioUrl(request.getAudioUrl())
                .build();
        configModel = repository.save(configModel);
        return configModel.toRichtextConfig();
    }

    @Override
    public RichtextConfig update(String configId, RichtextConfig request) {
        log.debug("update richtext config, configId:{}, content:{}", configId, request.getContent());
        if (StringUtils.isEmpty(configId)) {
            throw new DigitalHumanCommonException("Richtext configId cannot be empty.");
        }
        var one = repository.findByConfigId(configId);
        if (one.isEmpty()) {
            throw new DigitalHumanCommonException("RichtextConfig not existed.");
        }
        var updated = one.get();
        if (StringUtils.isNotEmpty(request.getContent())) {
            updated.setContent(request.getContent());
        }
        if (StringUtils.isNotEmpty(request.getDownloadUrl())) {
            updated.setDownloadUrl(request.getDownloadUrl());
        }
        if (StringUtils.isNotEmpty(request.getAudioUrl())) {
            updated.setAudioUrl(request.getAudioUrl());
        }
        repository.save(updated);
        return updated.toRichtextConfig();
    }

    @Override
    public RichtextConfig get(String configId) {
        log.debug("get richtext config, configId:{}", configId);
        var optional = repository.findByConfigId(configId);
        if (optional.isEmpty()) {
            throw new DigitalHumanCommonException("RichtextConfig not existed.");
        }
        return optional.get().toRichtextConfig();
    }

    @Override
    public void delete(String configId) {
        log.debug("delete richtext config, configId:{}", configId);
        var optional = repository.findByConfigId(configId);
        if (optional.isEmpty()) {
            throw new DigitalHumanCommonException("RichtextConfig not existed.");
        }
        repository.deleteByConfigId(configId);
    }

    @Override
    public PageResponse<RichtextConfig> list(String projectId, int pageNo, int pageSize) {
        log.debug("list richtext config, projectId:{}, pageNo:{}, pageSize:{}", projectId, pageNo, pageSize);
        PageRequest pageRequest = PageRequest.of(pageNo - 1, pageSize);
        var configs = repository.findByProjectIdOrderByCreateTimeDesc(projectId, pageRequest);
        List<RichtextConfig> configList = Lists.newArrayList();
        configs.forEach(r -> configList.add(r.toRichtextConfig()));
        return PageResponse.<RichtextConfig>builder()
                .page(PageResult.<RichtextConfig>builder()
                        .pageNo(pageNo)
                        .pageSize(pageSize)
                        .totalCount(configs.getTotalElements())
                        .result(configList).build())
                .build();
    }

    @Override
    public RichtextConfig createByName(String userId, String projectName, RichtextConfig request) {
        log.debug("create richtext config, projectName:{}, content:{}", projectName, request.getContent());

        if (StringUtils.isEmpty(request.getProjectName())) {
            throw new DigitalHumanCommonException("ProjectName cannot be empty.");
        }
        if (StringUtils.isEmpty(request.getContent())) {
            throw new DigitalHumanCommonException("Richtext content cannot be empty");
        }
        projectService.getByUserIdNameAndVersion(request.getUserId(), request.getProjectName(),
                null, DEFAULT_API_VERSION);
        RichtextConfigModel configModel = RichtextConfigModel.builder()
                .configId(ObjectId.get().toHexString())
                .projectName(projectName)
                .userId(userId)
                .content(request.getContent())
                .downloadUrl(request.getDownloadUrl())
                .audioUrl(request.getAudioUrl())
                .build();
        configModel = repository.save(configModel);
        return configModel.toRichtextConfig();
    }


    @Override
    public PageResponse<RichtextConfig> listByName(String userId, String projectName, int pageNo, int pageSize) {
        log.debug("list richtext config, userId:{}, projectName:{}, pageNo:{}, pageSize:{}", userId,
                projectName, pageNo, pageSize);
        PageRequest pageRequest = PageRequest.of(pageNo - 1, pageSize);
        var configs = repository.findByUserIdAndProjectNameOrderByCreateTimeDesc(userId, projectName, pageRequest);
        List<RichtextConfig> configList = Lists.newArrayList();
        configs.forEach(r -> configList.add(r.toRichtextConfig()));
        return PageResponse.<RichtextConfig>builder()
                .page(PageResult.<RichtextConfig>builder()
                        .pageNo(pageNo)
                        .pageSize(pageSize)
                        .totalCount(configs.getTotalElements())
                        .result(configList).build())
                .build();
    }

    @Override
    public RichtextConfig json2xml(JsonToXmlRequest request) {
        log.debug("begin to transfer json to xml, characterImage={}, drml.size={}", request.getCharacterImage(),
                request.getDrml().size());
        if (validateDrml(request.getDrml())) {
            throw new DigitalHumanCommonException("Invalid drml, please check your param.");
        }
        var ue4Character = validateCharacter(request.getCharacterImage());
        return RichtextConfig.builder()
                .content(json2xml(ue4Character, request.getInterruptible(), request.getDrml()))
                .build();
    }

    private boolean validateCharacter(String character) {

        var selectByType = characterService.selectByType(character);

        return selectByType != null && StringUtils.equalsIgnoreCase(
                Constant.THREE_DIMENTION, selectByType.get(0).getLabel());
    }

    private boolean validateDrml(List<DrmlAction> drmlActions) {
        return drmlActions.stream().anyMatch(drmlAction -> {
            if (drmlAction.getType() == null || (StringUtils.isEmpty(drmlAction.getSpeak())
                                                         && drmlAction.getAnimoji() == null
                                                         && drmlAction.getFusion() == null)) {
                return false;
            }
            if (drmlAction.getAnimoji() != null && !drmlAction.getAnimoji().validate()) {
                return false;
            }
            return drmlAction.getFusion() != null && !drmlAction.getFusion().validate();
        });
    }

    public static String json2xml(Boolean ue4Character, Boolean interruptible, List<DrmlAction> drml) {
        var sb = new StringBuilder();
        DocumentBuilder documentBuilder = null;
        try {
            documentBuilder = DocumentBuilderFactory.newInstance().newDocumentBuilder();
            var document = documentBuilder.newDocument();
            AtomicReference<Element> parentSpeak = new AtomicReference<>(document.createElement(
                    DrmlAction.Type.speak.name()));
            parentSpeak.get().setAttribute("interruptible", interruptible.toString());
            drml.forEach(d -> {
                switch (d.getType()) {
                    case speak:
                        if (StringUtils.isNotEmpty(d.getStartEmotion()) || StringUtils.isNotEmpty(d.getEndEmotion())) {
                            Optional.ofNullable(d.getStartEmotion()).ifPresent(v -> {
                                parentSpeak.get().setAttribute("startEmotion", v);
                            });
                            Optional.ofNullable(d.getEndEmotion()).ifPresent(v -> {
                                parentSpeak.get().setAttribute("endEmotion", v);
                            });
                            parentSpeak.get().appendChild(document.createTextNode(d.getSpeak()));
                            break;
                        }
                        parentSpeak.get().appendChild(document.createTextNode(d.getSpeak()));
                        break;
                    case client:
                        parentSpeak.get().appendChild(buildElement(document, DrmlAction.Type.client, d.getClient()));
                        break;
                    case character:
                        parentSpeak.get().appendChild(buildElement(document,
                                DrmlAction.Type.character, d.getCharacter()));
                        break;
                    case display:
                        parentSpeak.get().appendChild(buildElement(document, DrmlAction.Type.display, d.getDisplay()));
                        break;
                    case silence:
                        var silence = document.createElement(DrmlAction.Type.silence.name());
                        silence.setAttribute("time", d.getSilence());
                        parentSpeak.get().appendChild(silence);
                        break;
                    case fusion:
                        parentSpeak.get().appendChild(buildFusionElement(document, d, ue4Character, parentSpeak,
                                interruptible));
                        break;
                    case animoji:
                        var animoji = document.createElement(DrmlAction.Type.animoji.name());
                        var id = document.createElement("id");
                        id.appendChild(document.createTextNode(d.getAnimoji().getId()));
                        animoji.appendChild(id);
                        parentSpeak.get().appendChild(animoji);
                        break;
                    default:
                        log.error("Unspported drml action:{}", d.getType());
                }
            });
            document.appendChild(parentSpeak.get());
            sb.append(DomUtil.dumpNode(document));
        } catch (ParserConfigurationException e) {
            log.error("Fail to parse document configuration, ex:", e);
        }
        return sb.toString();
    }

    /**
     * 在指定document中创建一个element，名称是 type.name() ，内容由 json 转换
     *
     * @param document
     * @param type
     * @param json
     * @return
     */
    private static Element buildElement(Document document, DrmlAction.Type type, String json) {
        var element = document.createElement(type.name());
        try {
            DomUtil.jsonToXml(json, document, element, false);
        } catch (Exception e) {
            log.warn("Fail to transfer from json to xml", e);
            throw new DigitalHumanCommonException("Fail to transfer from json to xml");
        }
        return element;
    }

    private static Element buildFusionElement(Document document, DrmlAction drmlAction, Boolean ue4Character,
                                              AtomicReference<Element> parentElement, Boolean interruptible) {
        if (ue4Character) {
            var fusion = document.createElement(DrmlAction.Type.fusion.name());
            if (StringUtils.isNotEmpty(drmlAction.getFusion().getSpeak())) {
                var speak = document.createElement(DrmlAction.Type.speak.name());
                parentElement.get().removeAttribute("interruptible");
                var interrpt = document.createElement("interruptible");
                interrpt.appendChild(document.createTextNode(interruptible.toString()));
                speak.appendChild(interrpt);
                if (StringUtils.isNotEmpty(drmlAction.getFusion().getStartEmotion())
                        || StringUtils.isNotEmpty(drmlAction.getFusion().getEndEmotion())) {
                    Optional.ofNullable(drmlAction.getFusion().getStartEmotion()).ifPresent(v -> {
                        var startEmotion = document.createElement("startEmotion");
                        startEmotion.appendChild(document.createTextNode(v));
                        speak.appendChild(startEmotion);
                    });
                    Optional.ofNullable(drmlAction.getFusion().getEndEmotion()).ifPresent(v -> {
                        var endEmotion = document.createElement("endEmotion");
                        endEmotion.appendChild(document.createTextNode(v));
                        speak.appendChild(endEmotion);
                    });
                }
                var content = document.createElement("content");
                content.appendChild(document.createTextNode(drmlAction.getFusion().getSpeak()));
                speak.appendChild(content);
                fusion.appendChild(speak);
            }
            var animojiName = DrmlAction.Type.animoji.name();
            if (drmlAction.getFusion().getAnimojis().size() > 1) {
                animojiName = DrmlAction.Type.animojis.name();
            }
            String finalAnimojiName = animojiName;
            drmlAction.getFusion().getAnimojis().forEach(a -> {
                var animoji = document.createElement(finalAnimojiName);
                Node id = document.createElement("id");
                id.appendChild(document.createTextNode(a));
                animoji.appendChild(id);
                fusion.appendChild(animoji);
            });
            return fusion;
        } else {
            var animoji = document.createElement(DrmlAction.Type.animoji.name());
            var id = document.createElement("id");
            id.appendChild(document.createTextNode(drmlAction.getFusion().getAnimojis().get(0)));
            animoji.appendChild(id);
            var speak = document.createElement(DrmlAction.Type.speak.name());
            speak.appendChild(document.createTextNode(drmlAction.getFusion().getSpeak()));
            animoji.appendChild(speak);
            return animoji;
        }
    }

}
