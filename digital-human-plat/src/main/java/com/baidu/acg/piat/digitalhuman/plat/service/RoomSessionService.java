package com.baidu.acg.piat.digitalhuman.plat.service;

import com.baidu.acg.piat.digitalhuman.common.model.PageResponse;
import com.baidu.acg.piat.digitalhuman.common.room.session.RoomSession;


/**
 * Created on 2020/7/23 15:54.
 *
 * <AUTHOR>
 */
public interface RoomSessionService {

    RoomSession create(RoomSession one);

    RoomSession updateStatus(RoomSession one);

    RoomSession detail(String sessionId);

    PageResponse<RoomSession> listByRoomId(String roomId, int pageNo, int pageSize);

    long activeSession(String appId);

    long countByAppId(String appId, Long startTime, Long endTime);

    long aggregateAvgDuration(String appId, Long startTime, Long endTime);

}
