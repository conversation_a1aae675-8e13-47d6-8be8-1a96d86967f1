package com.baidu.acg.piat.digitalhuman.plat.helper;

import com.baidu.acg.piat.digitalhuman.common.model.TreeNode;
import com.baidu.acg.piat.digitalhuman.common.utils.JsonUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.tomcat.util.http.fileupload.IOUtils;
import org.springframework.util.CollectionUtils;

import java.io.File;
import java.io.FileInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.function.Consumer;

@Slf4j
public class OptModuleHolder {

    private static List<OptModuleConfigItem> optModules;
    private static Map<Integer, String> optModuleNameMap;
    private static Map<Integer, String> optTypeNameMap;

    static {

        // 加载配置文件
        try (FileInputStream in = new FileInputStream(
                new File("/home/<USER>/digital-human-plat/conf/opt-module.json"))
             ; ByteArrayOutputStream out = new ByteArrayOutputStream()) {
            IOUtils.copy(in, out);
            optModules = JsonUtil.readValueQuietly(out.toString(), new TypeReference<List<OptModuleConfigItem>>() {
            });
        } catch (IOException e) {
            log.error("Load opt modules config IO Exception", e);
            throw new RuntimeException("Load opt modules config failed", e);
        }


        // 初始化 optModuleNameMap,optTypeNameMap
        optModuleNameMap = Maps.newHashMap();
        optTypeNameMap = Maps.newHashMap();
        List<OptModuleHolder.OptModuleConfigItem> optModules = getOptModules();


        Map<Integer, List<Integer>> idChainMap = Maps.newHashMap();
        optModules.forEach(item -> {
            item.forEachTopDown(new Consumer<OptModuleHolder.OptModuleConfigItem>() {
                List<Integer> nodesIds = Lists.newArrayList();
                OptModuleHolder.OptModuleConfigItem lastNode;

                @Override
                public void accept(OptModuleHolder.OptModuleConfigItem optModuleConfigItem) {
                    if (null != lastNode) {
                        if (CollectionUtils.isEmpty(lastNode.getChildren())
                                && CollectionUtils.isEmpty(optModuleConfigItem.getChildren())) {
                            nodesIds.remove(nodesIds.size() - 1);
                        } else if (CollectionUtils.isEmpty(lastNode.getChildren())
                                && !CollectionUtils.isEmpty(optModuleConfigItem.getChildren())) {
                            nodesIds.remove(nodesIds.size() - 1);
                            nodesIds.remove(nodesIds.size() - 1);
                        }
                    }
                    if (!CollectionUtils.isEmpty(optModuleConfigItem.getOptTypes())) {
                        optModuleConfigItem.getOptTypes().forEach(item -> {
                            optTypeNameMap.put(item.getOptTypeId(), item.getOptTypeName());
                        });
                    }

                    nodesIds.add(optModuleConfigItem.getModuleId());
                    idChainMap.putIfAbsent(optModuleConfigItem.getModuleId(), Lists.newArrayList());
                    idChainMap.get(optModuleConfigItem.getModuleId()).addAll(nodesIds);
                    lastNode = optModuleConfigItem;

                }
            });
        });
        Map<Integer, OptModuleHolder.OptModuleConfigItem> optModuleMap = OptModuleHolder.getOptModuleMap();
        idChainMap.forEach((key, vals) -> {
            StringBuilder stringBuilder = new StringBuilder();
            vals.forEach(item -> {
                if (StringUtils.isNotEmpty(stringBuilder.toString())) {
                    stringBuilder.append("/");
                }
                OptModuleConfigItem optModuleConfigItem = optModuleMap.get(item);
                stringBuilder.append(optModuleConfigItem.getModuleName());
            });
            optModuleNameMap.put(key, stringBuilder.toString());

        });

    }

    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    public static class OptModuleConfigItem extends TreeNode<OptModuleConfigItem> {
        private String moduleName;
        private Integer moduleId;
        private String menus;
        private boolean isSuperAdminModule;
        private List<OptType> optTypes;
    }

    @Data
    public static class OptType {
        private Integer optTypeId;
        private String optTypeName;
    }

    public static List<OptModuleConfigItem> getOptModules() {
        return Collections.unmodifiableList(optModules);
    }

    public static Map<Integer, OptModuleConfigItem> getOptModuleMap() {
        Map<Integer, OptModuleConfigItem> result = Maps.newHashMap();
        for (OptModuleConfigItem item : getOptModules()) {
            item.forEachTopDown(optModule -> {
                result.put(optModule.getModuleId(), optModule);
            });
        }
        return result;
    }


    public static Map<Integer, String> getOptModuleNameMap() {
        return Collections.unmodifiableMap(optModuleNameMap);
    }


    public static Map<Integer, String> getOptTypeNameMap() {
        return Collections.unmodifiableMap(optTypeNameMap);
    }


}
