package com.baidu.acg.piat.digitalhuman.plat.config;


import com.baidu.acg.piat.digitalhuman.llmdm.client.LlmDmHttpClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Lazy;

/**
 * @Author: <EMAIL>
 * @Date: 2020/3/19
 */
@Configuration
public class LlmDmConfigure {

    @Value("${digitalhuman.llmdm.config.baseUrl:http://digital-human-llm-dm:8075}")
    private String baseUrl;

    @Bean
    @Lazy
    public LlmDmHttpClient llmDmHttpClient() {
        return new LlmDmHttpClient(baseUrl);
    }

}
