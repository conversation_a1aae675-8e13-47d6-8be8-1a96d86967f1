package com.baidu.acg.piat.digitalhuman.plat.service;

import com.baidu.acg.piat.digitalhuman.common.model.PageResponse;
import com.baidu.acg.piat.digitalhuman.common.optlog.OptLog;
import com.baidu.acg.piat.digitalhuman.common.common.PlatCode;
import com.baidu.acg.piat.digitalhuman.plat.model.optlog.OptLogListRequest;
import com.baidu.acg.piat.digitalhuman.plat.model.optlog.OptLogView;
import com.baidu.acg.piat.digitalhuman.plat.model.optlog.OptModuleItem;

import java.io.File;
import java.util.List;

public interface OptLogService {

    OptLog create(OptLog optLog);

    PageResponse<OptLog> list(List<String> menus, OptLogListRequest request, String accountId);

    PageResponse<OptLogView> listOptLogView(List<String> accountMenus
            , OptLogListRequest request, String accountId);

    File exportOptLogByQuery(List<String> accountMenus
            , OptLogListRequest request, String accountId);

    public List<OptModuleItem> queryOptModule(List<String> menus, PlatCode platCode);

    public List<String> queryOptType(List<String> menus, PlatCode platCode);
}
