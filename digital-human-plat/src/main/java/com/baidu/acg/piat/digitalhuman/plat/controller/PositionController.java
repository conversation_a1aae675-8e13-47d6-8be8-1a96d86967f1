package com.baidu.acg.piat.digitalhuman.plat.controller;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

import com.baidu.acg.piat.digitalhuman.common.model.PageResponse;
import com.baidu.acg.piat.digitalhuman.common.model.Response;
import com.baidu.acg.piat.digitalhuman.common.position.PositionVo;
import com.baidu.acg.piat.digitalhuman.plat.service.PositionService;

/**
 * <AUTHOR>
 */
@Slf4j
@RequestMapping("/api/digitalhuman/v1/position")
@RestController
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class PositionController {

    private final PositionService positionService;

    @PostMapping
    public Response<PositionVo> create(@Valid @RequestBody PositionVo request) {
        return Response.success(positionService.create(request));
    }

    @DeleteMapping("{id}")
    public Response<Void> delete(@PathVariable("id") String positionId) {
        positionService.delete(positionId);
        return Response.success(null);
    }

    @PutMapping("{id}")
    public Response<PositionVo> update(@PathVariable("id") String positionId, @Valid @RequestBody PositionVo request) {
        return Response.success(positionService.update(positionId, request));
    }

    @GetMapping("{id}")
    public Response<PositionVo> detail(@PathVariable("id") String positionId) {
        return Response.success(positionService.detail(positionId));
    }

    @GetMapping
    public PageResponse<PositionVo> list(@RequestParam String userId,
                                         @RequestParam(required = false, defaultValue = "1") int pageNo,
                                         @RequestParam(required = false, defaultValue = "20") int pageSize) {

        return positionService.listByUserId(userId, pageNo, pageSize);
    }
}
