package com.baidu.acg.piat.digitalhuman.plat.service;

import com.baidu.acg.piat.digitalhuman.common.model.PageResponse;
import com.baidu.acg.piat.digitalhuman.common.room.Room;

/**
 * Room service.
 *
 * <AUTHOR> (ma<PERSON><PERSON><PERSON>@baidu.com)
 */
public interface RoomService {

    Room create(Room room);

    void delete(String id);

    Room detail(String id);

    Room getByUserIdAndRoomName(String userId, String roomName);

    Room getByAppIdAndRoomName(String appId, String roomName);

    Room update(String id, Room room);

    PageResponse<Room> list(String userId, String appId, String roomName, int pageNo, int pageSize);

    PageResponse<Room> listByAppId(String appId, int pageNo, int pageSize);

}
