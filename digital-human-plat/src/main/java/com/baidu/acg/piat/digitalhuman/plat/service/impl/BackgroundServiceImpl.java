package com.baidu.acg.piat.digitalhuman.plat.service.impl;

import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.net.URL;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Base64;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;

import javax.xml.bind.DatatypeConverter;

import com.baidu.acg.piat.digitalhuman.common.background.BackgroundImage;
import com.baidu.acg.piat.digitalhuman.common.background.BackgroundUploadRequest;
import com.baidu.acg.piat.digitalhuman.common.exception.DigitalHumanCommonException;
import com.baidu.acg.piat.digitalhuman.common.utils.PathUtil;
import com.baidu.acg.piat.digitalhuman.plat.annotation.RecordPerformance;
import com.baidu.acg.piat.digitalhuman.plat.dao.BackgroundRepository;
import com.baidu.acg.piat.digitalhuman.plat.model.background.BackgroundModel;
import com.baidu.acg.piat.digitalhuman.plat.service.BackgroundService;
import com.baidu.acg.piat.digitalhuman.storage.service.StorageService;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class BackgroundServiceImpl implements BackgroundService {

    @Value("${digitalhuman.plat.upload.fileSuffix:jpg,gif,png,ico,bmp,jpeg}")
    private List<String> fileSuffix;

    private final BackgroundRepository backgroundRepository;

    private final StorageService storageService;

    @Override
    public Optional<BackgroundImage> get(@NonNull String backgroundImageId) {
        var result = backgroundRepository.findByBackgroundId(backgroundImageId);
        if (result.isEmpty()) {
            return Optional.empty();
        } else {
            return Optional.of(result.get().toBackgroundImage());
        }
    }

    @Override
    public PageImpl<BackgroundImage> selectByUserId(@NonNull String userId, @NonNull Pageable pageable) {
        var pageModel = backgroundRepository.findAllByUserIdOrderByCreateTimeDesc(userId, pageable);

        var pageContent = pageModel.getContent().stream()
                .map(BackgroundModel::toBackgroundImage)
                .collect(Collectors.toList());

        return new PageImpl<>(pageContent, pageModel.getPageable(), pageModel.getTotalElements());
    }

    @Override
    public BackgroundImage create(BackgroundUploadRequest request) {
        if (StringUtils.isBlank(request.getUserId())) {
            throw new DigitalHumanCommonException("UserId cannot be empty");
        }

        String format = PathUtil.getFileExtension(request.getName());

        if (StringUtils.isAnyBlank(format) || !fileSuffix.contains(format.toLowerCase())) {
            throw new DigitalHumanCommonException(String.format("非法文件：%s，上传文件仅支持：%s",
                    request.getName(), fileSuffix));
        }
        // 新增name过长校验
        if (StringUtils.isNotBlank(request.getName()) && request.getName().length() > 100) {
            throw new DigitalHumanCommonException(String.format("Image name length exceeds 100, filename: %s",
                    request.getName()));
        }

        if (StringUtils.isBlank(format)) {
            throw new DigitalHumanCommonException(String.format("Image name illegal, filename: %s", request.getName()));
        }

        if (StringUtils.isBlank(request.getImageBase64())) {
            throw new DigitalHumanCommonException("image base64 cannot be empty");
        }
        URL url = null;
        try {
            url = storageService.save(Base64.getDecoder().decode(request.getImageBase64()),
                    newBackgroundFileName(request));
        } catch (StorageService.ResourceException e) {
            log.warn("Fail tp upload background image to storage service.");
            throw new DigitalHumanCommonException("Fail to upload background image.");
        }
        var createdModel = BackgroundModel.builder()
                        .backgroundId(ObjectId.get().toHexString())
                        .userId(request.getUserId())
                        .name(request.getName())
                        .description(request.getDescription())
                        .isLiveBg(request.getIsLiveBg() == null ? 0 : (request.getIsLiveBg() ? 1 : 0))
                        .url(url.toString())
                        .build();

        // 判断该上传图片是否为背景图，如果是背景图就保存，不是那就原样返回，只是复用上传url
        if (!request.getIsOthers()) {
            backgroundRepository.save(createdModel);
        }
        return createdModel.toBackgroundImage();
    }

    /**
     * 删除背景
     *
     * @param backgroundImageId
     */
    @Override
    public void delete(@NonNull String backgroundImageId) {
        backgroundRepository.deleteByBackgroundId(backgroundImageId);
    }

    /**
     * 计算图片内容的md5来做作为新的文件名
     *
     * @param request
     * @return
     * @throws NoSuchAlgorithmException
     */
    @RecordPerformance
    public String newBackgroundFileName(BackgroundUploadRequest request) {

        var bytesOfMessage = request.getImageBase64().getBytes();

        String fileName;
        try {
            var messageDigest = MessageDigest.getInstance("MD5");

            byte[] digest = messageDigest.digest(bytesOfMessage);
            String contentDigest = DatatypeConverter
                    .printHexBinary(digest).toUpperCase();

            String[] fragment = new String[]{
                    contentDigest, String.valueOf(System.currentTimeMillis())
            };
            fileName = StringUtils.join(fragment, "-");
        } catch (NoSuchAlgorithmException e) {
            fileName = UUID.randomUUID().toString();
            log.warn("Fail to use md5 algorithm to generate file name, replace with uuid: {}", fileName, e);
        }

        return fileName + "." + StringUtils.substringAfterLast(request.getName(), ".");
    }

    // endregion private method
}
