package com.baidu.acg.piat.digitalhuman.plat.service;

import com.baidu.acg.piat.digitalhuman.common.model.PageResponse;
import com.baidu.acg.piat.digitalhuman.common.richconfig.JsonToXmlRequest;
import com.baidu.acg.piat.digitalhuman.common.richconfig.RichtextConfig;

/**
 * Created on 2020/4/22 21:49.
 *
 * <AUTHOR>
 */
public interface RichtextConfigService {

    RichtextConfig create(String projectId, RichtextConfig request);

    RichtextConfig update(String configId, RichtextConfig request);

    RichtextConfig get(String configId);

    void delete(String configId);

    PageResponse<RichtextConfig> list(String projectId, int pageNo, int pageSize);

    RichtextConfig createByName(String userId, String projectName, RichtextConfig request);

    PageResponse<RichtextConfig> listByName(String userId, String projectName, int pageNo, int pageSize);

    RichtextConfig json2xml(JsonToXmlRequest request);

}
