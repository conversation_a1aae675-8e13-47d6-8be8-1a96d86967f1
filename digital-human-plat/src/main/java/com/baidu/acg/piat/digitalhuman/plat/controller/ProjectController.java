package com.baidu.acg.piat.digitalhuman.plat.controller;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import com.baidu.acg.piat.digitalhuman.common.model.PageResponse;
import com.baidu.acg.piat.digitalhuman.common.model.Response;
import com.baidu.acg.piat.digitalhuman.common.project.Project;
import com.baidu.acg.piat.digitalhuman.common.project.ProjectDelByNameReq;
import com.baidu.acg.piat.digitalhuman.common.richconfig.JsonToXmlRequest;
import com.baidu.acg.piat.digitalhuman.common.richconfig.RichtextConfig;
import com.baidu.acg.piat.digitalhuman.plat.service.ProjectService;
import com.baidu.acg.piat.digitalhuman.plat.service.RichtextConfigService;

/**
 * Created on 2020/4/21 18:31.
 *
 * <AUTHOR>
 */
@Slf4j
@RequestMapping({"/api/digitalhuman/v1/project"})
@RestController
@RequiredArgsConstructor
public class ProjectController {

    private final ProjectService projectService;

    private final RichtextConfigService configService;

    @PostMapping
    public Response<Project> create(@Valid @RequestBody Project project) {
        return Response.success(projectService.create(project));
    }

    @DeleteMapping("{id}")
    public Response<Void> delete(@PathVariable("id") String projectId) {
        projectService.delete(projectId);
        return Response.success(null);
    }

    @DeleteMapping("/name")
    public Response<Void> delete(@RequestParam("name") String projectName,
                                 @RequestParam("userId") String userId,
                                 @RequestParam(defaultValue = "1") int apiVersion) {
        projectService.deleteByName(ProjectDelByNameReq.builder()
                .userId(userId).projectName(projectName).apiVersion(apiVersion).build());
        return Response.success(null);
    }

    @DeleteMapping
    public Response<Void> delete(@RequestBody ProjectDelByNameReq delByNameReq) {
        projectService.deleteByName(delByNameReq);
        return Response.success(null);
    }

    @PutMapping("/name/{name}")
    public Response<Project> updateByNameAndVersion(@PathVariable("name") String name,
                                                    @Valid @RequestBody Project request) {

        return Response.success(projectService.updateByUserIdAndName(request.getUserId(), name, request));
    }

    @PutMapping("{id}")
    public Response<Project> update(@PathVariable("id") String projectId,
                                    @Valid @RequestBody Project request) {
        return Response.success(projectService.update(projectId, request));
    }

    @PutMapping("/publish/{id}")
    public Response<Project> publish(@PathVariable("id") String projectId,
                                    @Valid @RequestBody Project request) {
        return Response.success(projectService.publish(projectId, request));
    }

    @GetMapping("/publish/{id}")
    public Response<Project> detailOfOnline(@PathVariable("id") String projectId) {
        return Response.success(projectService.detailOfPublishVersion(projectId));
    }

    @GetMapping("{id}")
    public Response<Project> detail(@PathVariable("id") String projectId) {
        return Response.success(projectService.detail(projectId));
    }

    @GetMapping("/name/{name}")
    public Response<Project> getByUserIdNameAndVersion(@PathVariable("name") String name,
                                                       @RequestParam("userId") String userId,
                                                       @RequestParam(required = false, defaultValue = "latest")
                                                               String projectVersion,
                                                       @RequestParam(defaultValue = "1") int apiVersion) {
        return Response.success(projectService.getByUserIdNameAndVersion(userId, name, projectVersion, apiVersion));
    }

    @GetMapping("/name-list/{name}")
    public PageResponse<Project> getListByUserIdNameAndVersion(
            @PathVariable("name") String name, @RequestParam("userId") String userId,
            @RequestParam(required = false, defaultValue = "latest") String projectVersion,
            @RequestParam(defaultValue = "1") int apiVersion,
            @RequestParam(required = false, defaultValue = "1") int pageNo,
            @RequestParam(required = false, defaultValue = "20") int pageSize,
            @RequestParam(required = false, defaultValue = "all") String visibleCharacters) {
        return projectService.getListByUserIdNameAndVersion(userId, name, projectVersion,
                apiVersion, pageNo, pageSize, visibleCharacters);
    }

    @GetMapping
    public PageResponse<Project> list(@RequestParam String userId,
                                      @RequestParam(required = false, defaultValue = "1") int apiVersion,
                                      @RequestParam(required = false, defaultValue = "1") int pageNo,
                                      @RequestParam(required = false, defaultValue = "20") int pageSize) {
        return projectService.list(userId, apiVersion, pageNo, pageSize);
    }

    @GetMapping(params = {"listWithMaxProjectVersion"})
    public PageResponse<Project> listWithMaxProjectVersion(
            @RequestParam String userId,
            @RequestParam(defaultValue = "0") int apiVersion,
            @RequestParam(required = false, defaultValue = "1") int pageNo,
            @RequestParam(required = false, defaultValue = "20") int pageSize,
            @RequestParam(required = false, defaultValue = "all") String visibleCharacters) {
        return projectService.listWithMaxProjectVersion(userId, apiVersion, pageNo, pageSize, visibleCharacters);
    }

    @PostMapping("{id}/config")
    public Response<RichtextConfig> createConfig(@PathVariable("id") String projectId,
                                                 @RequestBody RichtextConfig config) {
        return Response.success(configService.create(projectId, config));
    }

    @PutMapping("config/{configId}")
    public Response<RichtextConfig> updateConfig(@PathVariable String configId,
                                                 @RequestBody RichtextConfig config) {
        return Response.success(configService.update(configId, config));
    }

    @DeleteMapping("config/{configId}")
    public Response<Void> deleteConfig(@PathVariable String configId) {
        configService.delete(configId);
        return Response.success(null);
    }

    @GetMapping("config/{configId}")
    public Response<RichtextConfig> getConfig(@PathVariable String configId) {

        return Response.success(configService.get(configId));
    }

    @GetMapping("{id}/config")
    public PageResponse<RichtextConfig> listConfig(@PathVariable("id") String projectId,
                                                   @RequestParam(required = false, defaultValue = "1") int pageNo,
                                                   @RequestParam(required = false, defaultValue = "20") int pageSize) {

        return configService.list(projectId, pageNo, pageSize);
    }

    @PostMapping("/config")
    public Response<RichtextConfig> createConfigByName(@RequestParam("name") String projectName,
                                                       @RequestParam("userId") String userId,
                                                       @RequestBody RichtextConfig config) {
        return Response.success(configService.createByName(userId, projectName, config));
    }

    @GetMapping("/config")
    public PageResponse<RichtextConfig> listConfigByName(
            @RequestParam("name") String projectName,
            @RequestParam("userId") String userId,
            @RequestParam(required = false, defaultValue = "1") int pageNo,
            @RequestParam(required = false, defaultValue = "20") int pageSize) {
        return configService.listByName(userId, projectName, pageNo, pageSize);
    }

    @PostMapping("/config/json2xml")
    public Response<RichtextConfig> json2xml(@RequestBody JsonToXmlRequest request) {

        return Response.success(configService.json2xml(request));
    }

}
