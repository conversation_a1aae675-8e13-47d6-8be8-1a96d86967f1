package com.baidu.acg.piat.digitalhuman.plat.model.room.session;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;
import org.hibernate.annotations.UpdateTimestamp;

import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;

import javax.persistence.Access;
import javax.persistence.AccessType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Transient;
import javax.persistence.Version;

import com.baidu.acg.piat.digitalhuman.common.room.session.RoomSession;
import com.baidu.acg.piat.digitalhuman.common.session.SessionStatus;

/**
 * Room session model.
 *
 * <AUTHOR> (ma<PERSON><PERSON><PERSON>@baidu.com)
 */
@Data
@Builder
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
@DynamicInsert
@DynamicUpdate
@Entity(name = "room_session")
public class RoomSessionModel {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    private String room_session_id;

    private String sessionId;

    private String roomId;

    private String appId;

    private String appToken;

    private String projectId;

    @Transient
    private SessionStatus status;

    private long duration;

    @Column(name = "time_stamp")
    private long timestamp;

    @CreationTimestamp
    @Column(insertable = false, updatable = false)
    private ZonedDateTime createTime;

    @UpdateTimestamp
    @Column(insertable = false, updatable = false)
    private ZonedDateTime updateTime;

    @Version
    private Integer version;

    @Column(name = "status")
    @Access(AccessType.PROPERTY)
    public String getStatusName() {
        if (status != null) {
            return status.name();
        }
        return StringUtils.EMPTY;
    }

    public void setStatusName(String status) {
        if (StringUtils.isNotEmpty(status)) {
            this.status = SessionStatus.valueOf(status);
        } else {
            this.status = null;
        }
    }

    public RoomSession toRoomSession() {
        return RoomSession.builder()
                .sessionId(sessionId)
                .roomId(roomId)
                .appId(appId)
                .appToken(appToken)
                .projectId(projectId)
                .status(status)
                .createTimeInMs(createTime.toInstant().toEpochMilli())
                .createTime(DateTimeFormatter.ISO_OFFSET_DATE_TIME.format(createTime))
                .updateTime(DateTimeFormatter.ISO_OFFSET_DATE_TIME.format(updateTime))
                .build();
    }
}
