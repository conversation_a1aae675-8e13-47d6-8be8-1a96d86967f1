// Copyright (C) 2019 Baidu Inc. All rights reserved.
package com.baidu.acg.piat.digitalhuman.plat.service;

import com.baidu.acg.piat.digitalhuman.common.access.AccessUser;

/**
 * UserService
 *
 * <AUTHOR>
 * @since 2019-09-19
 */
public interface UserService {

    AccessUser create(AccessUser userRequest);

    AccessUser get(String userId);

    AccessUser getByName(String name);

    AccessUser getByIamUserId(AccessUser accessUser);

    AccessUser update(String userId, AccessUser userRequest);

    void delete(String userId);

    /**
     * 当前版本智能使用userId 和user password 进行登录
     *
     * @param userToValidate
     * @return
     */
    AccessUser validate(AccessUser userToValidate);

    /**
     * 适用user name 和user password 进行登录
     *
     * @param userToValidate
     * @return
     */
    AccessUser validateByName(AccessUser userToValidate);

}
