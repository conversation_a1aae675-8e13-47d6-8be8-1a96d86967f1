package com.baidu.acg.piat.digitalhuman.plat.service.impl;

import com.baidu.acg.dh.user.client.UserClient;
import com.baidu.acg.dh.user.client.model.vo.AccountBatchGetReqVO;
import com.baidu.acg.dh.user.client.model.vo.AccountQueryReq;
import com.baidu.acg.dh.user.client.model.vo.AccountQueryResVO;
import com.baidu.acg.dh.user.client.model.vo.ResourceQuota;
import com.baidu.acg.dh.user.client.model.vo.UserBatchGetReqVO;
import com.baidu.acg.dh.user.client.model.vo.UserQueryResVO;
import com.baidu.acg.piat.digitalhuman.common.access.AccessApp;
import com.baidu.acg.piat.digitalhuman.common.entity.CharacterConfig;
import com.baidu.acg.piat.digitalhuman.common.entity.CharacterModel;
import com.baidu.acg.piat.digitalhuman.common.hypervisor.request.HeartBeatRequest;
import com.baidu.acg.piat.digitalhuman.common.mhe.MheVideo;
import com.baidu.acg.piat.digitalhuman.common.model.PageResponse;
import com.baidu.acg.piat.digitalhuman.common.common.PlatCode;
import com.baidu.acg.piat.digitalhuman.common.model.PageResult;
import com.baidu.acg.piat.digitalhuman.common.project.Project;
import com.baidu.acg.piat.digitalhuman.common.statistic.CharacterPreferencesData;
import com.baidu.acg.piat.digitalhuman.common.statistic.CharacterUsageTrendData;
import com.baidu.acg.piat.digitalhuman.common.statistic.ProductionStatisticData;
import com.baidu.acg.piat.digitalhuman.common.statistic.QueryResponse;
import com.baidu.acg.piat.digitalhuman.common.statistic.QueryResult;
import com.baidu.acg.piat.digitalhuman.common.statistic.CharacterResourceData;
import com.baidu.acg.piat.digitalhuman.common.statistic.SessionStatisticData;
import com.baidu.acg.piat.digitalhuman.common.statistic.SessionStatisticPageResult;
import com.baidu.acg.piat.digitalhuman.common.statistic.StatisticDimension;
import com.baidu.acg.piat.digitalhuman.common.statistic.StatisticResponse;
import com.baidu.acg.piat.digitalhuman.common.statistic.CharacterResourceResponse;
import com.baidu.acg.piat.digitalhuman.common.statistic.AdminCharacterResourceResponse;
import com.baidu.acg.piat.digitalhuman.common.statistic.TenancyCharacterResourceResponse;
import com.baidu.acg.piat.digitalhuman.common.statistic.CharacterResourceViewType;
import com.baidu.acg.piat.digitalhuman.plat.client.MheStudioClient;
import com.baidu.acg.piat.digitalhuman.plat.dao.ProductionStatisticRepository;
import com.baidu.acg.piat.digitalhuman.plat.dao.SessionStatisticRepository;
import com.baidu.acg.piat.digitalhuman.plat.model.common.ProductionStatisticType;
import com.baidu.acg.piat.digitalhuman.plat.model.statistic.SessionStatisticPo;
import com.baidu.acg.piat.digitalhuman.plat.service.AppService;
import com.baidu.acg.piat.digitalhuman.plat.service.DialogService;
import com.baidu.acg.piat.digitalhuman.plat.service.ProjectService;
import com.baidu.acg.piat.digitalhuman.plat.service.PrometheusClient;
import com.baidu.acg.piat.digitalhuman.plat.service.RoomSessionService;
import com.baidu.acg.piat.digitalhuman.plat.service.StatisticService;
import com.baidu.acg.piat.digitalhuman.plat.service.CharacterService;
import com.baidu.acg.piat.digitalhuman.plat.v2.service.CharacterConfigService;
import com.baidu.acg.piat.digitalhuman.resource.pool.client.ResourcePoolClient;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import io.vavr.control.Try;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;

import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Map;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.stream.Collectors;
import org.apache.commons.lang3.reflect.FieldUtils;

import com.baidu.acg.piat.digitalhuman.common.constans.StatisticConstant;

@Slf4j
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class StatisticServiceImpl implements StatisticService {

    private static final String ALL_FLAG = "all";

    private static final String APP_MODULENAME = "应用";
    private static final String SCENE_MODULENAME = "场景";
    private static final String CHARACTERCONFIG_MODULENAME = "人设";
    private static final String MHEPROJECT_MODULENAME = "视频工程文件";
    private static final String MHEVIDEO_MODULENAME = "视频作品";

    private LoadingCache<CharacterPreferencesCacheKey, List<CharacterPreferencesData>> characterPreferencesCache
            = CacheBuilder.newBuilder()
            // 缓存存储最大数量
            .maximumSize(1000)
            // 过期时间5 min
            .expireAfterWrite(5, TimeUnit.MINUTES)
            .build(new CacheLoader<CharacterPreferencesCacheKey, List<CharacterPreferencesData>>() {
                @Override
                public List<CharacterPreferencesData> load(CharacterPreferencesCacheKey key) throws Exception {
                    return queryCharacterPreferencesData(key.getPlatCode(), key.getAccountId()
                            , key.getRoleLevel(), key.getAccountVisibleCharacters()
                            , key.getAccountMenus());
                }
            });

    @Value("${spring.application.name:digital-human-plat}")
    private String applicationName;

    private static final DecimalFormat format2decimal = new DecimalFormat("#.###");

    private final PrometheusClient prometheusClient;

    private final AppService appService;

    private final DialogService dialogService;

    private final RoomSessionService roomSessionService;

    private final CharacterService characterService;

    private final ResourcePoolClient resourcePoolClient;

    private final UserClient userClient;

    private final CharacterConfigService characterConfigService;

    private final ProjectService projectService;

    private final MheStudioClient mheStudioClient;

    private final SessionStatisticRepository sessionStatisticRepository;

    private final ProductionStatisticRepository productionStatisticRepository;

    static {
        format2decimal.setRoundingMode(RoundingMode.UP);
    }

    @Override
    public StatisticResponse getActiveSession(String appId, long startInMs, long endInMs) {
        log.debug("Start to query current sessions, appId={}, startInMs={}, endInMs={}", appId, startInMs, endInMs);
        return StatisticResponse.builder()
                .active(roomSessionService.activeSession(appId))
                .total(appService.get(appId).getResourceQuota().getRoomLimits())
                .build();
    }

    @Override
    public StatisticResponse getTotalSession(String appId, long startInMs, long endInMs) {
        log.debug("Start to query total sessions, appId={}, startInMs={}, endInMs={}", appId, startInMs, endInMs);
        var total = roomSessionService.countByAppId(appId, startInMs, endInMs);
        return StatisticResponse.builder().total(total).build();
    }

    @Override
    public StatisticResponse getAvgSessionDuration(String appId, long startInMs, long endInMs) {
        log.debug("Start to query average session duration, appId={}, startInMs={}, endInMs={}",
                appId, startInMs, endInMs);
        var latency = roomSessionService.aggregateAvgDuration(appId, startInMs, endInMs);
        return StatisticResponse.builder().avg(Double.parseDouble(
                format2decimal.format(latency))).build();
    }

    @Override
    public StatisticResponse getTotalDialog(String appId, long startInMs, long endInMs) {
        log.debug("Start to query total dialogs, appId={}, startInMs={}, endInMs={}", appId, startInMs, endInMs);
        var total = dialogService.countByAppIdAndTimestampBetween(appId, startInMs, endInMs);
        return StatisticResponse.builder().total(total).build();
    }

    @Override
    public StatisticResponse getSessionTrend(String appId, long startInMs, long endInMs, long intervalInSeconds) {
        log.debug("Start to query session trend, appId={}, startInMs={}, endInMs={}, intervalInSeconds={}",
                appId, startInMs, endInMs, intervalInSeconds);

        var queryResp = prometheusClient.queryRange(String.format(
                        StatisticConstant.CURRENT_SESSIONS_QUERY, appId),
                startInMs / 1000, endInMs / 1000, intervalInSeconds);
        log.debug("Prometheus getSessionTrend query response={}", queryResp);

        var statisticResults = new ArrayList<StatisticResponse.HistogramResult>();
        if (queryResp.getData() != null && CollectionUtils.isNotEmpty(queryResp.getData().getResult())) {
            for (var queryResult : queryResp.getData().getResult()) {
                var values = queryResult.getValues();
                for (var value : values) {
                    var result = (String) value.get(1);
                    statisticResults.add(StatisticResponse.HistogramResult.builder()
                            .time((Integer) value.get(0) * 1000L)
                            .total("NaN".equals(result) ? 0.0 : Double.parseDouble(result))
                            .build()
                    );
                }
            }
        }
        return StatisticResponse.builder()
                .results(statisticResults)
                .build();
    }

    private List<CharacterModel> queryAllCharacter(PlatCode platCode
            , String accountId, String accountVisibleCharacters, int roleLevel) {
        long startMis = System.currentTimeMillis();
        // 查询平台所有人像数据
        PageResponse<CharacterModel> characterQueryRes = characterService.query(1, 99999
                , 2, ALL_FLAG, false, true);
        if (null == characterQueryRes.getPage() || CollectionUtils.isEmpty(characterQueryRes.getPage().getResult())) {
            return Lists.newArrayList();
        }
        // 根据登录账号权限过滤人像数据
        List<CharacterModel> characterList = characterQueryRes.getPage().getResult();

        if (!(PlatCode.ADMIN.equals(platCode) && roleLevel == 3)) {
            List<String> visibleCharacters = Arrays.stream(accountVisibleCharacters.split(","))
                    .collect(Collectors.toList());
            characterList = characterList.stream().filter(characterModel -> {
                if (ALL_FLAG.equals(accountVisibleCharacters)
                        || visibleCharacters.contains(characterModel.getType())) {
                    return true;
                }
                return false;
            }).collect(Collectors.toList());
        }
        log.info("queryAllCharacter cost:{}", System.currentTimeMillis() - startMis);
        return characterList;
    }

    @Override
    public CharacterResourceResponse characterResource(PlatCode platCode
            , String accountId, String accountVisibleCharacters, int roleLevel) {
        CharacterResourceResponse result = buildEmptyCharacterResourceRes(platCode, roleLevel);
        AtomicBoolean isSuperAdmin = new AtomicBoolean(true);
        if (!(PlatCode.ADMIN.equals(platCode) && roleLevel == 3)) {
            isSuperAdmin.set(false);
        }
        List<CharacterModel> characterList = queryAllCharacter(platCode
                , accountId, accountVisibleCharacters, roleLevel);
        if (CollectionUtils.isEmpty(characterList)) {
            return result;
        }
        Map<String, List<CharacterModel>> resourceType2CharactersMap = characterList
                .stream().collect(Collectors.groupingBy(item -> {
                    return item.getType().split("-")[0];
                }));
        // 获取账户对应分配路数
        Map<String, Integer> distributedAccountMap = new HashMap<>();

        List<AccountQueryResVO> accountList
                = userClient.internalQueryAccount("2.0", AccountQueryReq.builder()
                .pageNo(1)
                .pageSize(99999)
                .build()).getResult().getResult();
        for (AccountQueryResVO account : accountList) {
            if (!isSuperAdmin.get() && !accountId.equals(account.getAccountId())) {
                continue;
            }
            for (ResourceQuota resourceQuota : account.getResourcesQuota()) {
                Integer count = distributedAccountMap.getOrDefault(resourceQuota.getCharacter(), 0);
                distributedAccountMap.put(resourceQuota.getCharacter(), count + resourceQuota.getLimit());
            }
        }

        // 获取人像对应分配路数
        Map<String, Integer> characterType2RoomLimitsMap = Maps.newHashMap();
        Map<String, String> validAppId2CharacterTypeMap = Maps.newHashMap();
        Map<String, List<String>> characterType2AppIds = Maps.newHashMap();
        List<String> characterTypes = characterList.stream().map(CharacterModel::getType).collect(Collectors.toList());
        List<AccessApp> accessApps = appService.listByCharacterImages(characterTypes, 2);
        accessApps.forEach(app -> {
            if (!isSuperAdmin.get() && !accountId.equals(app.getUserId())) {
                return;
            }
            characterType2RoomLimitsMap.putIfAbsent(app.getCharacterImage(), 0);
            characterType2RoomLimitsMap.put(app.getCharacterImage(), app.getResourceQuota().getRoomLimits()
                    + characterType2RoomLimitsMap.get(app.getCharacterImage()));
            validAppId2CharacterTypeMap.put(app.getAppId(), app.getCharacterImage());
            characterType2AppIds.putIfAbsent(app.getCharacterImage(), Lists.newArrayList());
            characterType2AppIds.get(app.getCharacterImage()).add(app.getAppId());
        });
        // 获取人像实时占用
        Map<String, Integer> realAppId2SessionsMap = Maps.newHashMap();

        Set<String> validAppIds = validAppId2CharacterTypeMap.keySet();
        QueryResponse appCurrentSessionRes = prometheusClient.query(StatisticConstant.CURRENT_SESSIONS_APPID_QUERY
                , System.currentTimeMillis() / 1000);
        if (appCurrentSessionRes.getData() != null
                && CollectionUtils.isNotEmpty(appCurrentSessionRes.getData().getResult())) {
            for (QueryResult item : appCurrentSessionRes.getData().getResult()) {
                String appId = item.getMetric().getAppId();
                if (StringUtils.isEmpty(appId) || item.getValue().size() != 2
                        || !validAppIds.contains(appId)) {
                    continue;
                }
                Try.run(() -> {
                    Integer realSessions = Integer.valueOf((String) item.getValue().get(1));
                    realAppId2SessionsMap.putIfAbsent(appId, 0);
                    realAppId2SessionsMap.put(appId, realAppId2SessionsMap.get(appId) + realSessions);
                });
            }
        }

        // 获取resourceType部署路数
        Map<String, Integer> deployResourceMap = Maps.newHashMap();
        if (isSuperAdmin.get()) {
            Map<String, List<HeartBeatRequest>> resourcesTypeMap = resourcePoolClient
                    .listAllByResourceType(Lists.newArrayList(resourceType2CharactersMap.keySet())).getResources();
            resourcesTypeMap = null == resourcesTypeMap ? Maps.newHashMap() : resourcesTypeMap;
            resourcesTypeMap.forEach((key, val) -> {
                int capacity = 0;
                if (CollectionUtils.isNotEmpty(val)) {
                    for (HeartBeatRequest resourceInstance : val) {
                        capacity += Integer.parseInt(resourceInstance.getLabels()
                                .getOrDefault("capacity", "1"));
                    }
                }
                deployResourceMap.put(key, capacity);
            });
        }

        if (isSuperAdmin.get()) {
            AdminCharacterResourceResponse response = (AdminCharacterResourceResponse) result;
            resourceType2CharactersMap.forEach((key, val) -> {
                AdminCharacterResourceResponse.AdminCharacterResourceData singleData
                        = AdminCharacterResourceResponse.AdminCharacterResourceData
                        .builder().resourceType(key)
                        .deploymentRoutes(deployResourceMap.getOrDefault(key, 0))
                        .character(Lists.newArrayList())
                        .build();
                val.forEach(characterModel -> {
                    singleData.getCharacter().add(buildCharacterResourceData(characterModel
                            , characterType2AppIds, distributedAccountMap
                            , realAppId2SessionsMap, characterType2RoomLimitsMap));
                });
                response.getData().add(singleData);
            });
        } else {
            TenancyCharacterResourceResponse response = (TenancyCharacterResourceResponse) result;
            characterList.forEach(characterModel -> {
                response.getData().add(buildCharacterResourceData(characterModel
                        , characterType2AppIds, distributedAccountMap
                        , realAppId2SessionsMap, characterType2RoomLimitsMap));
            });
        }

        return result;
    }

    @Override
    public List<CharacterUsageTrendData> characterUsageTrend(PlatCode platCode, String accountId
            , int roleLevel, String accountVisibleCharacters
            , long startInMs, long endInMs, long intervalInSeconds) {
        List<CharacterUsageTrendData> result = buildEmptyCharacterUsageTrendRes();
        AtomicBoolean isSuperAdmin = new AtomicBoolean(true);
        if (!(PlatCode.ADMIN.equals(platCode) && roleLevel == 3)) {
            isSuperAdmin.set(false);
        }
        List<CharacterModel> characterList = queryAllCharacter(platCode
                , accountId, accountVisibleCharacters, roleLevel);
        if (CollectionUtils.isEmpty(characterList)) {
            return result;
        }

        Map<String, String> characterTypeNameMap = Maps.newHashMap();
        Map<String, List<String>> resourceTypeNamesMap = Maps.newHashMap();
        characterList.forEach(item -> {
            String resourceType = item.getType().split("-")[0];
            characterTypeNameMap.put(item.getType(), item.getFigureAlias());
            resourceTypeNamesMap.putIfAbsent(resourceType, Lists.newArrayList());
            resourceTypeNamesMap.get(resourceType).add(item.getFigureAlias());
        });
        Map<String, String> resourceTypeNameMap = Maps.newHashMap();
        resourceTypeNamesMap.forEach((key, val) -> {
            StringBuilder stringBuilder = new StringBuilder();
            for (int i = 0; i < val.size(); ++i) {
                stringBuilder.append(val.get(i));
                if (!(i == val.size() - 1)) {
                    stringBuilder.append("/");
                }
            }
            resourceTypeNameMap.put(key, stringBuilder.toString());
        });

        // 因为是需要峰值所以请求prometheus的时候step固定1s
        QueryResponse queryResp = prometheusClient.queryRange(StatisticConstant.CURRENT_SESSIONS_USAGETREND_QUERY
                , startInMs / 1000, (endInMs / 1000) + intervalInSeconds, 1);
        Map<String, List<QueryResult>> appId2QueryResultMap = Maps.newHashMap();

        if (queryResp.getData() != null && CollectionUtils.isNotEmpty(queryResp.getData().getResult())) {
            for (QueryResult queryResult : queryResp.getData().getResult()) {
                QueryResult.MetricResult metric = queryResult.getMetric();
                List<List<Object>> values = queryResult.getValues();
                if (null == metric
                        || StringUtils.isEmpty(metric.getAppId())
                        || StringUtils.isEmpty(metric.getCharacterImage())
                        || StringUtils.isEmpty(metric.getResourceType())
                        || CollectionUtils.isEmpty(values)
                        || !characterTypeNameMap.keySet().contains(metric.getCharacterImage())
                        || !resourceTypeNamesMap.keySet().contains(metric.getResourceType())) {
                    continue;
                }
                appId2QueryResultMap.putIfAbsent(metric.getAppId(), Lists.newArrayList());
                appId2QueryResultMap.get(metric.getAppId()).add(queryResult);
            }
        }
        Map<String, AccessApp> appMap = appService.list(Lists.newArrayList(appId2QueryResultMap.keySet()))
                .stream().filter(item -> {
                    if (!isSuperAdmin.get() && !accountId.equals(item.getUserId())) {
                        return false;
                    }
                    return item.getApiVersion() == 2 && StringUtils.isNotEmpty(item.getCharacterImage());
                }).collect(Collectors.toMap(AccessApp::getAppId, Function.identity(), (v1, v2) -> {
                    return v1;
                }));

        Map<String, List<QueryResult>> characterQueryResultMap = Maps.newHashMap();
        Map<String, List<QueryResult>> resourceTyperQueryResultMap = Maps.newHashMap();
        appId2QueryResultMap.forEach((appId, queryResults) -> {
            queryResults.forEach(queryResult -> {
                if (null == appMap.get(appId)
                        || null == characterTypeNameMap
                        .get(queryResult.getMetric().getCharacterImage())
                        || null == resourceTypeNameMap
                        .get(queryResult.getMetric().getResourceType())) {
                    return;
                }
                characterQueryResultMap.putIfAbsent(queryResult.getMetric().getCharacterImage(), Lists.newArrayList());
                characterQueryResultMap.get(queryResult.getMetric().getCharacterImage()).add(queryResult);
                resourceTyperQueryResultMap.putIfAbsent(queryResult.getMetric().getResourceType()
                        , Lists.newArrayList());
                resourceTyperQueryResultMap.get(queryResult.getMetric().getResourceType()).add(queryResult);
            });
        });

        long currentMis=System.currentTimeMillis();
        // 人像维度统计
        buildCharacterUsageTrendRes(characterTypeNameMap, characterQueryResultMap, result.get(0)
                , startInMs, endInMs, intervalInSeconds, currentMis);

        // 部署资源维度统计
        buildCharacterUsageTrendRes(resourceTypeNameMap, resourceTyperQueryResultMap, result.get(1)
                , startInMs, endInMs, intervalInSeconds, currentMis);
        return result;
    }

    @Override
    public List<CharacterPreferencesData> characterPreferences(PlatCode platCode, String accountId
            , int roleLevel, String accountVisibleCharacters, List<String> accountMenus) throws ExecutionException {
        CharacterPreferencesCacheKey key = new CharacterPreferencesCacheKey
                (platCode, accountId, roleLevel, accountVisibleCharacters, accountMenus);
        return characterPreferencesCache.get(key);
    }

    private List<CharacterPreferencesData> queryCharacterPreferencesData(PlatCode platCode, String accountId
            , int roleLevel, String accountVisibleCharacters, List<String> accountMenus) {
        AtomicBoolean isSuperAdmin = new AtomicBoolean(true);
        if (!(PlatCode.ADMIN.equals(platCode) && roleLevel == 3)) {
            isSuperAdmin.set(false);
        }
        List<CharacterPreferencesData> result = buildEmptyCharacterPreferencesRes(isSuperAdmin.get());
        // 人像
        List<CharacterModel> characterList = queryAllCharacter(platCode
                , accountId, accountVisibleCharacters, roleLevel);
        if (characterList.isEmpty()) {
            return result;
        }
        Map<String, String> characterTypeNameMap = characterList.stream()
                .collect(Collectors.toMap(CharacterModel::getType
                        , CharacterModel::getFigureAlias, (v1, v2) -> {
                            return v1;
                        }));
        log.debug("characterTypeNameMap:{}", characterTypeNameMap);
        Map<String, CharacterConfig> characterConfigMap = getAllCharacterConfigMap(accountId
                , accountVisibleCharacters, characterTypeNameMap, isSuperAdmin);
        log.debug("characterConfigKetSet:{}", characterConfigMap.keySet());

        // 获取非线编视频文件 异步处理
        CountDownLatch mheVideorLatch = new CountDownLatch(1);
        fillCharacterPreferencesMheVideoData(result, characterTypeNameMap
                , characterConfigMap, isSuperAdmin, accountId
                , mheVideorLatch, accountMenus);
        // 非线编工程 异步处理
        CountDownLatch mheProjectLatch = new CountDownLatch(1);
        fillCharacterPreferencesMheProjectData(result, characterTypeNameMap
                , characterConfigMap, isSuperAdmin, accountId
                , mheProjectLatch, accountMenus);
        // 人设
        fillCharacterPreferencesConfigData(result, characterTypeNameMap, characterConfigMap
                , accountMenus, isSuperAdmin);

        // 场景
        List<Project> projectList = listAllSceneProject(isSuperAdmin
                , accountId, accountVisibleCharacters);
        fillCharacterPreferencesSceneData(projectList, result
                , characterTypeNameMap, characterConfigMap
                , isSuperAdmin, accountMenus);

        // 应用
        fillCharacterPreferencesAppData(projectList, result, characterTypeNameMap
                , characterConfigMap, isSuperAdmin, accountId, accountVisibleCharacters
                , accountMenus);
        try {
            long startIms = System.currentTimeMillis();
            mheProjectLatch.await();
            mheVideorLatch.await();
            log.debug("mheProjectLatch/mheVideorLatch await cost:{}", System.currentTimeMillis() - startIms);
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }
        return result;
    }

    @Override
    public SessionStatisticPageResult sessionService(PlatCode platCode, String accountId
            , int roleLevel, int pageNo
            , int pageSize, int startInDate, int endInDate, String orderBy, String order) {
        AtomicBoolean isSuperAdmin = new AtomicBoolean(true);
        if (!(PlatCode.ADMIN.equals(platCode) && roleLevel == 3)) {
            isSuperAdmin.set(false);
        }
        if (isSuperAdmin.get()) {
            List<Map<String, Object>> queryRes = sessionStatisticRepository.findAllGroupByAccountId(startInDate
                    , endInDate);
            return buildPageSessionStatisticRes(pageSize, pageNo, orderBy
                    , order, queryRes, stringObjectMap -> (String) stringObjectMap.get("accountId")
                    , sessionStatisticData -> {
                        Set<String> accountIds = sessionStatisticData.stream()
                                .map(SessionStatisticData::getName).collect(Collectors.toSet());
                        Map<String, AccountQueryResVO> accountMap = userClient.internalBatchGetAccount("2"
                                        , AccountBatchGetReqVO.builder()
                                                .accountIds(Lists.newArrayList(accountIds))
                                                .build()).getResult().stream()
                                .collect(Collectors.toMap(AccountQueryResVO::getAccountId
                                        , Function.identity(), (v1, v2) -> {
                                            return v1;
                                        }));
                        // 修改name
                        sessionStatisticData.forEach(item -> {
                            AccountQueryResVO accountQueryResVO = accountMap.get(item.getName());
                            String name = null == accountQueryResVO ? item.getName() : accountQueryResVO.getName();
                            item.setName(name);
                        });
                    });

        } else {
            List<Map<String, Object>> queryRes = sessionStatisticRepository
                    .findAllGroupByAppId(accountId, startInDate, endInDate);
            return buildPageSessionStatisticRes(pageSize, pageNo, orderBy
                    , order, queryRes, stringObjectMap -> (String) stringObjectMap.get("appId")
                    , sessionStatisticData -> {
                        Set<String> appIds = sessionStatisticData.stream()
                                .map(SessionStatisticData::getName).collect(Collectors.toSet());
                        Map<String, AccessApp> appMap = appService.list(Lists.newArrayList(appIds)).stream()
                                .collect(Collectors.toMap(AccessApp::getAppId, Function.identity(), (v1, v2) -> {
                                    return v1;
                                }));

                        // 兜底
                        Map<String, String> deleteAppIdNameMap = Maps.newHashMap();
                        appIds.stream().filter(item -> {
                            return null == appMap.get(item);
                        }).forEach(id -> {
                            Page<SessionStatisticPo> pageRes =
                                    sessionStatisticRepository.findByAppId(id
                                            , PageRequest.of(0, 1, Sort.by("updateTime")
                                                    .descending()));
                            if (CollectionUtils.isEmpty(pageRes.getContent())) {
                                return;
                            }
                            deleteAppIdNameMap.put(id, pageRes.getContent().get(0).getAppName());
                        });

                        // 修改name
                        sessionStatisticData.forEach(item -> {
                            String appId = item.getName();
                            AccessApp accessApp = appMap.get(appId);
                            String name = null;
                            if (null != accessApp) {
                                name = accessApp.getName();
                            }
                            if (null == name && deleteAppIdNameMap.get(appId) != null) {
                                name = deleteAppIdNameMap.get(appId);
                            }
                            name = null == name ? appId : name;
                            item.setName(name);
                        });
                    });
        }
    }


    private SessionStatisticPageResult buildPageSessionStatisticRes(int pageSize, int pageNo
            , String orderBy, String order, List<Map<String, Object>> dbAllQueryRes
            , Function<Map<String, Object>, String> nameIdentityFunc
            , Consumer<List<SessionStatisticData>> fillNameConsumer) {
        if (dbAllQueryRes.isEmpty()) {
            return SessionStatisticPageResult.builder()
                    .pageNo(pageNo).pageSize(pageSize)
                    .result(Lists.newArrayList())
                    .build();
        }
        int totalCount = dbAllQueryRes.size();
        List<SessionStatisticData> datalist = Lists.newArrayList();
        int sessionCountSummary = 0;
        long sessionTotalDurationSummary = 0;
        int dialogCountSummary = 0;
        for (Map<String, Object> item : dbAllQueryRes) {
            String name = nameIdentityFunc.apply(item);
            long sessionTotalDuration = ((Number) item.get("sessionTotalDuration")).longValue();
            int sessionTotalCount = ((Number) item.get("sessionTotalCount")).intValue();
            int dialogTotalCount = ((Number) item.get("dialogTotalCount")).intValue();
            SessionStatisticData data = SessionStatisticData.builder()
                    .sessionTotalCount(sessionTotalCount)
                    .sessionAvgDuration(sessionTotalCount == 0 ? 0 : sessionTotalDuration / sessionTotalCount)
                    .dialogTotalCount(dialogTotalCount)
                    .name(name)
                    .build();
            datalist.add(data);
            sessionCountSummary += sessionTotalCount;
            dialogCountSummary += dialogTotalCount;
            sessionTotalDurationSummary += sessionTotalDuration;
        }
        long sessionAvgDurationSummary = sessionCountSummary == 0
                ? 0 : sessionTotalDurationSummary / sessionCountSummary;
        // 内存排序，排序没有交给db做是因为本接口需要汇总数据在db内部也是对于获取到的数据进行了全量遍历
        Collections.sort(datalist, (o1, o2) -> Try.of(() -> {
            Comparable v1 = (Comparable) FieldUtils.readField(o1, orderBy, true);
            Comparable v2 = (Comparable) FieldUtils.readField(o2, orderBy, true);
            if (order.toLowerCase().equals("desc")) {
                return v2.compareTo(v1);
            } else {
                return v1.compareTo(v2);
            }
        }).getOrElse(() -> {
            return Integer.valueOf(o2.getSessionTotalCount())
                    .compareTo(o2.getSessionTotalCount());
        }));
        // 分页截取
        int fromIndex = (pageNo - 1) * pageSize;
        int toIndex = fromIndex + pageSize;
        toIndex = toIndex > datalist.size() ? datalist.size() : toIndex;
        List<SessionStatisticData> sessionStatisticData = Lists.newArrayList();
        if (!(fromIndex < 0 || fromIndex >= datalist.size() || fromIndex > toIndex)) {
            sessionStatisticData = datalist.subList(fromIndex, toIndex);
            fillNameConsumer.accept(sessionStatisticData);
        }
        return SessionStatisticPageResult.builder()
                .pageNo(pageNo).pageSize(pageSize)
                .totalCount(totalCount)
                .dialogCountSummary(dialogCountSummary)
                .sessionCountSummary(sessionCountSummary)
                .sessionAvgDurationSummary(sessionAvgDurationSummary)
                .result(sessionStatisticData)
                .build();
    }

    @Override
    public PageResult<ProductionStatisticData> production(PlatCode platCode, String accountId
            , int roleLevel, int pageNo, int pageSize, int startInDate, int endInDate) {
        AtomicBoolean isSuperAdmin = new AtomicBoolean(true);
        if (!(PlatCode.ADMIN.equals(platCode) && roleLevel == 3)) {
            isSuperAdmin.set(false);
        }
        PageRequest pageRequest = PageRequest.of(pageNo - 1, pageSize
                , Sort.by("totalCount").descending());
        if (isSuperAdmin.get()) {
            Long totalCount = productionStatisticRepository.distinctAccountIdCount(startInDate, endInDate);
            List<Map<String, Object>> queryRes = productionStatisticRepository.findGroupByAccountId(startInDate
                    , endInDate, pageRequest);
            if (queryRes.isEmpty()) {
                return PageResult.<ProductionStatisticData>builder()
                        .pageNo(pageNo).pageSize(pageSize).totalCount(totalCount)
                        .result(Lists.newArrayList())
                        .build();
            }
            List<String> accountIds = Lists.newArrayList();
            queryRes.forEach(item -> {
                accountIds.add((String) item.get("accountId"));
            });
            Map<String, AccountQueryResVO> accountMap = userClient.internalBatchGetAccount("2"
                            , AccountBatchGetReqVO.builder()
                                    .accountIds(accountIds)
                                    .build()).getResult().stream()
                    .collect(Collectors.toMap(AccountQueryResVO::getAccountId, Function.identity(), (v1, v2) -> {
                        return v1;
                    }));

            List<ProductionStatisticData> datalist = Lists.newArrayList();
            queryRes.forEach(item -> {
                String accountIdRes = (String) item.get("accountId");
                AccountQueryResVO accountQueryResVO = accountMap.get(accountIdRes);
                String name = null == accountQueryResVO ? accountIdRes : accountQueryResVO.getName();
                ProductionStatisticData data = budilProductionStatisticData(item);
                data.setName(name);
                datalist.add(data);
            });
            return PageResult.<ProductionStatisticData>builder()
                    .pageNo(pageNo).pageSize(pageSize).totalCount(totalCount)
                    .result(datalist)
                    .build();
        } else {
            Long totalCount = productionStatisticRepository.distinctUserIdCount(accountId, startInDate, endInDate);
            List<Map<String, Object>> queryRes = productionStatisticRepository
                    .findGroupByUserId(accountId, startInDate, endInDate, pageRequest);
            if (queryRes.isEmpty()) {
                return PageResult.<ProductionStatisticData>builder()
                        .pageNo(pageNo).pageSize(pageSize).totalCount(totalCount)
                        .result(Lists.newArrayList())
                        .build();
            }
            List<String> userIds = Lists.newArrayList();
            queryRes.forEach(item -> {
                userIds.add((String) item.get("userId"));
            });
            log.debug("userIds:{}", userIds);
            Map<String, UserQueryResVO> userMap = userClient.internalBatchGetUser("2"
                            , UserBatchGetReqVO.builder()
                                    .userIds(userIds)
                                    .build()).getResult().stream()
                    .collect(Collectors.toMap(UserQueryResVO::getUserId, Function.identity(), (v1, v2) -> {
                        return v1;
                    }));
            log.debug("userMap:{}", userMap);

            List<ProductionStatisticData> datalist = Lists.newArrayList();
            queryRes.forEach(item -> {
                String userId = (String) item.get("userId");
                UserQueryResVO userQueryResVO = userMap.get(userId);
                String name = null == userQueryResVO ? userId : userQueryResVO.getUsername();
                ProductionStatisticData data = budilProductionStatisticData(item);
                data.setName(name);
                datalist.add(data);

            });
            return PageResult.<ProductionStatisticData>builder()
                    .pageNo(pageNo).pageSize(pageSize).totalCount(totalCount)
                    .result(datalist)
                    .build();
        }
    }

    private ProductionStatisticData budilProductionStatisticData(Map<String, Object> dbRes) {
        ProductionStatisticData data = ProductionStatisticData.builder()
                .characterConfigIncrement(ProductionStatisticData.IncrementData
                        .builder().name(ProductionStatisticType.CHARACTER_CONFIG.getName())
                        .value(((Number) dbRes.get("characterConfigCount")).intValue())
                        .build())
                .videoIncrement(ProductionStatisticData.IncrementData
                        .builder().name(ProductionStatisticType.MHE_VIDEO.getName())
                        .value(((Number) dbRes.get("mheVideoCount")).intValue())
                        .build())
                .projectIncrement(ProductionStatisticData.IncrementData
                        .builder().name(ProductionStatisticType.MHE_PROJECT.getName())
                        .value(((Number) dbRes.get("mheProjectCount")).intValue())
                        .build())
                .sceneIncrement(ProductionStatisticData.IncrementData
                        .builder().name(ProductionStatisticType.SCENE_PROJECT.getName())
                        .value(((Number) dbRes.get("sceneProjectCount")).intValue())
                        .build())
                .appIncrement(ProductionStatisticData.IncrementData
                        .builder().name(ProductionStatisticType.APP.getName())
                        .value(((Number) dbRes.get("appCount")).intValue())
                        .build())
                .build();
        return data;

    }
    private void fillCharacterPreferencesMheVideoData(List<CharacterPreferencesData> result
            , Map<String, String> characterTypeNameMap, Map<String, CharacterConfig> characterConfigMap
            , AtomicBoolean isSuperAdmin, String accountId, CountDownLatch mheVideorLatch
            ,List<String> accountMenus) {
        CompletableFuture.runAsync(() -> {
            if (!isSuperAdmin.get() && !accountMenus.contains("subOverview")) {
                mheVideorLatch.countDown();
                return;
            }
            Map<String, Integer> mheVideoCharacterImageCountMap = Maps.newHashMap();
            AtomicInteger mheVideoCharacterImageToTalCount = new AtomicInteger(0);
            Map<String, Integer> mheVideoCharacterConfigIdCountMap = Maps.newHashMap();
            AtomicInteger mheVideoCharacterConfigIdTotalCount = new AtomicInteger(0);
            long startMis = System.currentTimeMillis();
            int pageNo = 1;
            int pageSize = 5000;
            while (true) {
                PageResult<MheVideo> mheVideoPageResult = mheStudioClient
                        .listAllVidesByPage(pageNo, pageSize, isSuperAdmin.get() ? null : accountId);
                mheVideoPageResult.getResult().forEach(mheVideo -> {
                    if ((!isSuperAdmin.get()
                            && !accountId.equals(mheVideo.getUserId()))
                            || CollectionUtils.isEmpty(mheVideo.getCharacterConfigIds())) {
                        return;
                    }
                    Sets.newHashSet(mheVideo.getCharacterConfigIds()).forEach(configId -> {
                        CharacterConfig characterConfig = characterConfigMap.get(configId);
                        if (null == characterConfig) {
                            return;
                        }
                        mheVideoCharacterImageToTalCount.incrementAndGet();
                        mheVideoCharacterImageCountMap.putIfAbsent(characterConfig.getType(), 0);
                        mheVideoCharacterImageCountMap.put(characterConfig.getType()
                                , mheVideoCharacterImageCountMap.get(characterConfig.getType()) + 1);


                        mheVideoCharacterConfigIdTotalCount.incrementAndGet();
                        mheVideoCharacterConfigIdCountMap.putIfAbsent(configId, 0);
                        mheVideoCharacterConfigIdCountMap.put(configId
                                , mheVideoCharacterConfigIdCountMap.get(configId) + 1);
                    });
                });
                if (mheVideoPageResult.getResult().size() < pageSize) {
                    break;
                }
                pageNo++;
            }
            log.debug("listAllMheVides cost:{},pageNo:{}", System.currentTimeMillis() - startMis, pageNo);
            fillCharacterPreferencesData(result, mheVideoCharacterImageCountMap
                    , mheVideoCharacterImageToTalCount, mheVideoCharacterConfigIdCountMap
                    , mheVideoCharacterConfigIdTotalCount, isSuperAdmin
                    , MHEVIDEO_MODULENAME, characterTypeNameMap, characterConfigMap);

            mheVideorLatch.countDown();
            log.debug("mheVideoCharacterImageCountMap:{}", mheVideoCharacterImageCountMap);
            log.debug("mheVideoCharacterConfigIdCountMap:{}", mheVideoCharacterConfigIdCountMap);

        }).exceptionally(throwable -> {
            log.error(" Async fillCharacterPreferencesMheVideoData error", throwable);
            mheVideorLatch.countDown();
            return null;
        });
    }


    private void fillCharacterPreferencesData(
            List<CharacterPreferencesData> result
            , Map<String, Integer> characterImageCountMap
            , AtomicInteger characterImageToTalCount
            , Map<String, Integer> characterConfigIdCountMap
            , AtomicInteger characterConfigIdTotalCount
            , AtomicBoolean isSuperAdmin
            , String moduleName
            , Map<String, String> characterTypeNameMap
            , Map<String, CharacterConfig> characterConfigMap) {
        // 人像维度
        CharacterPreferencesData.Data mheProjectCharacterData = CharacterPreferencesData.Data.builder()
                .moduleName(moduleName)
                .detail(Lists.newArrayList())
                .build();
        result.get(0).getData().add(mheProjectCharacterData);
        characterTypeNameMap.forEach((characterType, name) -> {
            Integer valCount = characterImageCountMap.getOrDefault(characterType, 0);
            mheProjectCharacterData.getDetail().add(CharacterPreferencesData.DetailVal.builder()
                    .name(name)
                    .count(valCount)
                    .build());
        });

        // 人设维度
        if (!isSuperAdmin.get()) {
            CharacterPreferencesData.Data mheProjectCharacterConfigData = CharacterPreferencesData.Data.builder()
                    .moduleName(moduleName)
                    .detail(Lists.newArrayList())
                    .build();
            result.get(1).getData().add(mheProjectCharacterConfigData);
            characterConfigMap.forEach((configId, config) -> {
                Integer valCount = characterConfigIdCountMap.getOrDefault(configId, 0);
                mheProjectCharacterConfigData.getDetail().add(CharacterPreferencesData.DetailVal.builder()
                        .name(config.getName())
                        .count(valCount)
                        .build());
            });
        }
    }

    private void fillCharacterPreferencesMheProjectData(List<CharacterPreferencesData> result
            , Map<String, String> characterTypeNameMap
            , Map<String, CharacterConfig> characterConfigMap
            , AtomicBoolean isSuperAdmin, String accountId
            , CountDownLatch mheProjectLatch,List<String> accountMenus) {

        CompletableFuture.runAsync(() -> {
            if (!isSuperAdmin.get() && !accountMenus.contains("subOverview")) {
                mheProjectLatch.countDown();
                return;
            }
            Map<String, Integer> mheProjectCharacterImageCountMap = Maps.newHashMap();
            AtomicInteger mheProjectCharacterImageToTalCount = new AtomicInteger(0);
            Map<String, Integer> mheProjectCharacterConfigIdCountMap = Maps.newHashMap();
            AtomicInteger mheProjectCharacterConfigIdTotalCount = new AtomicInteger(0);
            long startMis = System.currentTimeMillis();
            mheStudioClient.listAllProjects(isSuperAdmin.get() ? null : accountId)
                    .forEach(mheProject -> {
                        if ((!isSuperAdmin.get()
                                && !accountId.equals(mheProject.getUserId()))
                                || CollectionUtils.isEmpty(mheProject.getCharacterConfigIds())) {
                            return;
                        }
                        Sets.newHashSet(mheProject.getCharacterConfigIds()).forEach(configId -> {
                            CharacterConfig characterConfig = characterConfigMap.get(configId);
                            if (null == characterConfig) {
                                return;
                            }
                            mheProjectCharacterImageToTalCount.incrementAndGet();
                            mheProjectCharacterImageCountMap.putIfAbsent(characterConfig.getType(), 0);
                            mheProjectCharacterImageCountMap.put(characterConfig.getType()
                                    , mheProjectCharacterImageCountMap.get(characterConfig.getType()) + 1);


                            mheProjectCharacterConfigIdTotalCount.incrementAndGet();
                            mheProjectCharacterConfigIdCountMap.putIfAbsent(configId, 0);
                            mheProjectCharacterConfigIdCountMap.put(configId
                                    , mheProjectCharacterConfigIdCountMap.get(configId) + 1);
                        });
                    });
            log.debug("listAllMheProjects cost:{}", System.currentTimeMillis() - startMis);
            fillCharacterPreferencesData(result, mheProjectCharacterImageCountMap
                    , mheProjectCharacterImageToTalCount, mheProjectCharacterConfigIdCountMap
                    , mheProjectCharacterConfigIdTotalCount, isSuperAdmin
                    , MHEPROJECT_MODULENAME, characterTypeNameMap, characterConfigMap);
            mheProjectLatch.countDown();
            log.debug("mheProjectCharacterImageCountMap:{}", mheProjectCharacterImageCountMap);
            log.debug("mheProjectCharacterConfigIdCountMap:{}", mheProjectCharacterConfigIdCountMap);
        }).exceptionally(throwable -> {
            log.error(" Async fillCharacterPreferencesMheProjectData error", throwable);
            mheProjectLatch.countDown();
            return null;
        });
    }

    private void fillCharacterPreferencesConfigData(List<CharacterPreferencesData> result
            , Map<String, String> characterTypeNameMap, Map<String, CharacterConfig> characterConfigMap
            , List<String> accountMenus, AtomicBoolean isSuperAdmin) {
        if (!isSuperAdmin.get() && !accountMenus.contains("character")) {
            return;
        }
        Map<String, Integer> configCharacterTypeCountMap = Maps.newHashMap();
        characterConfigMap.values().forEach(config -> {
            String type = config.getType();
            configCharacterTypeCountMap.putIfAbsent(type, 0);
            configCharacterTypeCountMap.put(type
                    , configCharacterTypeCountMap.get(type) + 1);


        });
        CharacterPreferencesData.Data configCharacterData = CharacterPreferencesData.Data.builder()
                .moduleName(CHARACTERCONFIG_MODULENAME)
                .detail(Lists.newArrayList())
                .build();
        result.get(0).getData().add(configCharacterData);
        characterTypeNameMap.forEach((characterType, name) -> {
            Integer valCount = configCharacterTypeCountMap.getOrDefault(characterType, 0);
            configCharacterData.getDetail().add(CharacterPreferencesData.DetailVal.builder()
                    .name(name)
                    .count(valCount)
                    .build());
        });
        log.info("configCharacterTypeCountMap:{}", configCharacterTypeCountMap);
    }

    private Map<String, CharacterConfig> getAllCharacterConfigMap(String accountId
            , String accountVisibleCharacters, Map<String, String> characterTypeNameMap
            , AtomicBoolean isSuperAdmin) {
        long startMis = System.currentTimeMillis();
        List<String> visibleCharacters = Arrays
                .stream(accountVisibleCharacters.split(","))
                .collect(Collectors.toList());
        Map<String, CharacterConfig> characterConfigMap = characterConfigService.listAll()
                .stream().filter(characterConfig -> {
                    if (!characterTypeNameMap.keySet()
                            .contains(characterConfig.getType())) {
                        return false;
                    }
                    if (!isSuperAdmin.get()) {
                        if (!accountId.equals(characterConfig.getUserId())) {
                            return false;
                        }
                        if (ALL_FLAG.equals(accountVisibleCharacters)
                                || visibleCharacters.contains(characterConfig.getType())) {
                            return true;
                        }
                        return false;
                    }
                    return true;
                }).collect(Collectors.toMap(CharacterConfig::getConfigId, Function.identity(), (v1, v2) -> v1));
        log.info("getAllCharacterConfigMap cost:{}", System.currentTimeMillis() - startMis);
        return characterConfigMap;
    }


    private List<Project> listAllSceneProject(AtomicBoolean isSuperAdmin, String accountId
            , String accountVisibleCharacters) {
        long startMis = System.currentTimeMillis();
        List<String> visibleCharacters = Arrays
                .stream(accountVisibleCharacters.split(","))
                .collect(Collectors.toList());
        List<Project> projectList = projectService.listAllWithMaxProjectVersion(2)
                .stream().filter(project -> {
                    if (!isSuperAdmin.get()) {
                        if (!accountId.equals(project.getUserId())) {
                            return false;
                        }
                        if (ALL_FLAG.equals(accountVisibleCharacters)
                                || visibleCharacters.contains(project.getCharacterImage())) {
                            return true;
                        }
                        return false;
                    }
                    return true;
                }).collect(Collectors.toList());
        log.info("listAllSceneProject cost:{}", System.currentTimeMillis() - startMis);
        return projectList;
    }


    private void fillCharacterPreferencesSceneData(
            List<Project> projectList
            , List<CharacterPreferencesData> result
            , Map<String, String> characterTypeNameMap
            , Map<String, CharacterConfig> characterConfigMap
            , AtomicBoolean isSuperAdmin, List<String> accountMenus) {
        if (!isSuperAdmin.get() && !accountMenus.contains("scene")) {
            return;
        }
        Map<String, Integer> projectCharacterImageCountMap = Maps.newHashMap();
        AtomicInteger projectCharacterImageToTalCount = new AtomicInteger(0);
        Map<String, Integer> projectCharacterConfigIdCountMap = Maps.newHashMap();
        AtomicInteger projectCharacterConfigIdTotalCount = new AtomicInteger(0);
        projectList.forEach(project -> {
            String characterImage = project.getCharacterImage();
            if (StringUtils.isNotEmpty(characterImage)) {
                projectCharacterImageToTalCount.incrementAndGet();
                projectCharacterImageCountMap.putIfAbsent(characterImage, 0);
                projectCharacterImageCountMap.put(characterImage
                        , projectCharacterImageCountMap.get(characterImage) + 1);
            }

            String configId = project.getCharacterConfigId();
            if (StringUtils.isNotEmpty(configId)) {
                projectCharacterConfigIdTotalCount.incrementAndGet();
                projectCharacterConfigIdCountMap.putIfAbsent(configId, 0);
                projectCharacterConfigIdCountMap.put(configId
                        , projectCharacterConfigIdCountMap.get(configId) + 1);
            }
        });

        fillCharacterPreferencesData(result, projectCharacterImageCountMap
                , projectCharacterImageToTalCount, projectCharacterConfigIdCountMap
                , projectCharacterConfigIdTotalCount, isSuperAdmin
                , SCENE_MODULENAME, characterTypeNameMap, characterConfigMap);

        log.info("projectCharacterImageCountMap:{}", projectCharacterImageCountMap);
        log.info("projectCharacterConfigIdCountMap:{}", projectCharacterConfigIdCountMap);
    }

    private void fillCharacterPreferencesAppData(
            List<Project> projectList
            , List<CharacterPreferencesData> result
            , Map<String, String> characterTypeNameMap
            , Map<String, CharacterConfig> characterConfigMap
            , AtomicBoolean isSuperAdmin
            , String accountId
            , String accountVisibleCharacters
            , List<String> accountMenus) {
        if (!isSuperAdmin.get() && !accountMenus.contains("newConsole")) {
            return;
        }
        List<String> visibleCharacters = Arrays
                .stream(accountVisibleCharacters.split(","))
                .collect(Collectors.toList());
        long startMis = System.currentTimeMillis();
        List<AccessApp> appList = appService.listAll(2)
                .stream().filter(accessApp -> {
                    if (!isSuperAdmin.get()) {
                        if (!accountId.equals(accessApp.getUserId())) {
                            return false;
                        }
                        if (ALL_FLAG.equals(accountVisibleCharacters)
                                || visibleCharacters.contains(accessApp.getCharacterImage())) {
                            return true;
                        }
                        return false;
                    }
                    return true;
                }).collect(Collectors.toList());
        log.info("listAppAll cost:{}", System.currentTimeMillis() - startMis);
        Map<String, Project> projectMap = projectList.stream()
                .collect(Collectors.toMap(Project::getId, Function.identity(), (v1, v2) -> v1));

        Map<String, Integer> appCharacterImageCountMap = Maps.newHashMap();
        AtomicInteger appCharacterImageToTalCount = new AtomicInteger(0);
        Map<String, Integer> appCharacterConfigIdCountMap = Maps.newHashMap();
        AtomicInteger appCharacterConfigIdTotalCount = new AtomicInteger(0);
        appList.forEach(accessApp -> {
            String characterImage = accessApp.getCharacterImage();
            if (StringUtils.isNotEmpty(characterImage)) {
                appCharacterImageToTalCount.incrementAndGet();
                appCharacterImageCountMap.putIfAbsent(characterImage, 0);
                appCharacterImageCountMap.put(characterImage
                        , appCharacterImageCountMap.get(characterImage) + 1);
            }

            String projectId = accessApp.getProjectId();
            if (StringUtils.isNotEmpty(projectId)
                    && null != projectMap.get(projectId)
                    && StringUtils.isNotEmpty(projectMap.get(projectId).getCharacterConfigId())) {
                String configId = projectMap.get(projectId).getCharacterConfigId();
                appCharacterConfigIdTotalCount.incrementAndGet();
                appCharacterConfigIdCountMap.putIfAbsent(configId, 0);
                appCharacterConfigIdCountMap.put(configId
                        , appCharacterConfigIdCountMap.get(configId) + 1);
            }
        });
        fillCharacterPreferencesData(result, appCharacterImageCountMap
                , appCharacterImageToTalCount, appCharacterConfigIdCountMap
                , appCharacterConfigIdTotalCount, isSuperAdmin
                , APP_MODULENAME, characterTypeNameMap, characterConfigMap);

        log.info("appCharacterImageCountMap:{}", appCharacterImageCountMap);
        log.info("appCharacterConfigIdCountMap:{}", appCharacterConfigIdCountMap);
    }

    private List<CharacterPreferencesData> buildEmptyCharacterPreferencesRes(Boolean isSuperAdmin) {
        CharacterPreferencesData character = CharacterPreferencesData.builder()
                .data(Collections.synchronizedList(Lists.newArrayList()))
                .statisticDimension(StatisticDimension.CHARACTER).build();
        CharacterPreferencesData characterConfig = CharacterPreferencesData.builder()
                .data(Collections.synchronizedList(Lists.newArrayList()))
                .statisticDimension(StatisticDimension
                        .CHARACTER_CONFIG).build();
        if (isSuperAdmin) {
            return Collections.synchronizedList(Lists.newArrayList(character));
        } else {
            return Collections.synchronizedList(Lists.newArrayList(character
                    , characterConfig));
        }
    }


    private void buildCharacterUsageTrendRes(Map<String, String> statisticMap
            , Map<String, List<QueryResult>> queryResultMap
            , CharacterUsageTrendData data, long startInMs
            , long endInMs, long intervalInSeconds, long currentMis) {
        statisticMap.forEach((key, val) -> {
            List<QueryResult> queryResults = queryResultMap.get(key);
            CharacterUsageTrendData.Data usageTrendData = CharacterUsageTrendData.Data.builder()
                    .name(val)
                    .values(Lists.newArrayList())
                    .build();
            data.getData().add(usageTrendData);
            usageTrendData.getValues().addAll(buildEmptyPoints(startInMs, endInMs, intervalInSeconds));
            if (CollectionUtils.isNotEmpty(queryResults)) {
                Map<Long, CharacterUsageTrendData.PointVal> pointValMap = Maps.newTreeMap();
                queryResults.forEach(queryResult -> {
                    queryResult.getValues().forEach(metric -> {
                        Long metricTime = Long.valueOf((Integer) metric.get(0)) * 1000L;
                        String metricValue = (String) metric.get(1);
                        Integer intMetricValue = "NaN".equals(metricValue) ? 0 : Integer.parseInt(metricValue);
                        pointValMap.putIfAbsent(metricTime, CharacterUsageTrendData.PointVal.builder()
                                .time(metricTime)
                                .value(0)
                                .build());
                        pointValMap.get(metricTime).setValue(pointValMap.get(metricTime).getValue() + intMetricValue);
                    });
                });
                Set<Long> pointValMapKetSet = pointValMap.keySet();
                usageTrendData.getValues().forEach(item -> {
                    Integer maxVal = null;
                    // 获取区间最大值
                    for (Long timeKey : pointValMapKetSet) {
                        if (timeKey >= (item.getTime() + (intervalInSeconds * 1000L))) {
                            break;
                        }
                        int pointVal = pointValMap.get(timeKey).getValue();
                        if (timeKey >= item.getTime()) {
                            if (null == maxVal) {
                                maxVal = pointVal;
                            }
                            maxVal = pointVal > maxVal ? pointVal : maxVal;
                        }
                    }

                    if (null == maxVal) {
                        return;
                    }
                    item.setValue(maxVal);
                });
                // 最后处理未来时间点的脏数据(prometheus产生的数据)
                usageTrendData.getValues().forEach(item -> {
                    if (item.getTime() > currentMis) {
                        item.setValue(0);
                    }
                });
            }
        });
    }


    private List<CharacterUsageTrendData.PointVal> buildEmptyPoints(long startInMs
            , long endInMs, long intervalInSeconds) {
        List<CharacterUsageTrendData.PointVal> result = Lists.newArrayList();
        for (long i = (startInMs / 1000L) * 1000L; i <= (endInMs / 1000L) * 1000L; i += (intervalInSeconds * 1000L)) {
            result.add(CharacterUsageTrendData.PointVal.builder()
                    .time(i)
                    .value(0)
                    .build());
        }
        return result;
    }

    private List<CharacterUsageTrendData> buildEmptyCharacterUsageTrendRes() {
        return Lists.newArrayList(CharacterUsageTrendData.builder()
                        .data(Lists.newArrayList())
                        .statisticDimension(StatisticDimension.CHARACTER).build()
                , CharacterUsageTrendData.builder()
                        .data(Lists.newArrayList())
                        .statisticDimension(StatisticDimension
                                .RESOURCE_TYPE).build());
    }

    private CharacterResourceData buildCharacterResourceData(
            CharacterModel characterModel
            , Map<String, List<String>> characterType2AppIds
            , Map<String, Integer> distributedAccountMap
            , Map<String, Integer> realAppId2SessionsMap
            , Map<String, Integer> characterType2RoomLimitsMap) {
        int realtimeUseingRoutes = 0;
        List<String> appIds = characterType2AppIds.getOrDefault(characterModel.getType(), Lists.newArrayList());
        for (String appId : appIds) {
            realtimeUseingRoutes += realAppId2SessionsMap.getOrDefault(appId, 0);
        }
        CharacterResourceData characterResourceData = CharacterResourceData.builder()
                .characterName(characterModel.getFigureAlias())
                .assignedToTenancyRoutes(distributedAccountMap.getOrDefault(characterModel.getType(), 0))
                .assignedToAppRoutes(characterType2RoomLimitsMap.getOrDefault(characterModel.getType(), 0))
                .realtimeUseingRoutes(realtimeUseingRoutes)
                .build();
        return characterResourceData;
    }

    private CharacterResourceResponse buildEmptyCharacterResourceRes(PlatCode platCode, int roleLevel) {
        CharacterResourceResponse response = null;
        if (PlatCode.ADMIN == platCode && roleLevel == 3) {
            response = new AdminCharacterResourceResponse();
            response.setViewType(CharacterResourceViewType.SUPER_ADMINISTRATOR.name());
        } else {
            response = new TenancyCharacterResourceResponse();
            response.setViewType(CharacterResourceViewType.TENANCY_ADMINISTRATOR.name());
        }
        response.setData(Lists.newArrayList());

        return response;
    }

    @Data
    @AllArgsConstructor
    static class CharacterPreferencesCacheKey {
        private PlatCode platCode;
        private String accountId;
        private int roleLevel;
        private String accountVisibleCharacters;
        private List<String> accountMenus;
    }
}
