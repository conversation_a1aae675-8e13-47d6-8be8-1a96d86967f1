// Copyright (C) 2019 Baidu Inc. All rights reserved.
package com.baidu.acg.piat.digitalhuman.plat.service;

import org.springframework.data.domain.Pageable;

import java.util.List;

import com.baidu.acg.piat.digitalhuman.common.access.AccessApp;
import com.baidu.acg.piat.digitalhuman.common.model.PageResponse;

/**
 * AppService
 *
 * <AUTHOR>
 * @since 2019-09-19
 */
public interface AppService {

    AccessApp create(AccessApp appRequest, Boolean needValidate);

    AccessApp get(String appId);

    List<AccessApp> list(List<String> appIds);

    AccessApp update(String appId, AccessApp appRequest);

    void delete(String appId, String uid, int apiVersion);

    void disable(String appId);

    PageResponse<AccessApp> listAll(String userId, String name, String characterTypes, String projectIds,
                                    int apiVersion, Pageable pageable);

    List<AccessApp> listAll(int apiVersion);

    AccessApp findByAppIdAndCharacterImage(String appId, String characterImage);

    /**
     * 根据人像类型获取默认的 App
     *
     * @param characterImage
     * @return
     */
    AccessApp findDefaultByCharacterImage(String characterImage, int apiVersion);

    List<AccessApp> listByCharacterImage(String characterImage, int apiVersion);

    List<AccessApp> listByCharacterImages(List<String> characterImages, int apiVersion);

}
