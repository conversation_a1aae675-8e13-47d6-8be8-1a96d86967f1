package com.baidu.acg.piat.digitalhuman.plat.model.property;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;

@Data
@Builder
@Slf4j
@AllArgsConstructor
@NoArgsConstructor
public class PropertyModelList {

    @Builder.Default
    private List<PropertyModel> baseList = new ArrayList<>();

    @Builder.Default
    private List<PropertyModel> bodyList = new ArrayList<>();

    @Builder.Default
    private List<PropertyModel> animojiList = new ArrayList<>();

    @Builder.Default
    private List<PropertyModel> hairList = new ArrayList<>();

    @Builder.Default
    private List<PropertyModel> shoesList = new ArrayList<>();

    @Builder.Default
    private List<PropertyModel> glassesList = new ArrayList<>();

    @Builder.Default
    private List<PropertyModel> specialeyeList = new ArrayList<>();

    @Builder.Default
    private List<PropertyModel> headwearList = new ArrayList<>();

    @Builder.Default
    private List<PropertyModel> ornamentList = new ArrayList<>();

    @Builder.Default
    private List<PropertyModel> eyeList = new ArrayList<>();

    @Builder.Default
    private List<PropertyModel> browList = new ArrayList<>();

    @Builder.Default
    private List<PropertyModel> faceList = new ArrayList<>();

    @Builder.Default
    private List<PropertyModel> mouthList = new ArrayList<>();

    @Builder.Default
    private List<PropertyModel> noseList = new ArrayList<>();

    public void insert(PropertyModel propertyModel) {
        switch (propertyModel.getType()) {
            case "Base" :
                baseList.add(propertyModel);
                break;

            case "Body" :
                bodyList.add(propertyModel);
                break;

            case "Animoji" :
                animojiList.add(propertyModel);
                break;
            case "Hair" :
                hairList.add(propertyModel);
                break;

            case "Shoes" :
                shoesList.add(propertyModel);
                break;

            case "Glasses" :
                glassesList.add(propertyModel);
                break;

            case "Specialeye" :
                specialeyeList.add(propertyModel);
                break;

            case "Headwear" :
                headwearList.add(propertyModel);
                break;

            case "Ornament" :
                ornamentList.add(propertyModel);
                break;

            case "Eye" :
                eyeList.add(propertyModel);
                break;

            case "Brow" :
                browList.add(propertyModel);
                break;

            case "Face" :
                faceList.add(propertyModel);
                break;

            case "Mouth" :
                mouthList.add(propertyModel);
                break;

            case "Nose" :
                noseList.add(propertyModel);
                break;
            default:
                log.error("The type of property={} is not supported.", propertyModel.getType());
                break;
        }
    }
}
