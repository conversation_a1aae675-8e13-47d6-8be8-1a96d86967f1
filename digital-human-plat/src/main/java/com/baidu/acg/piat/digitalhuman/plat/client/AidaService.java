package com.baidu.acg.piat.digitalhuman.plat.client;

import com.baidu.acg.piat.digitalhuman.common.entity.CharacterConfigRelatedModule;
import com.baidu.acg.piat.digitalhuman.common.entity.CharacterConfigRequest;
import com.baidu.acg.piat.digitalhuman.common.model.Response;
import retrofit2.Call;
import retrofit2.http.Body;
import retrofit2.http.POST;

import java.util.List;

public interface AidaService {
    @POST("/api/workflow/v1/internal/digitalHuman/characterConfigs")
    Call<Response<List<CharacterConfigRelatedModule>>> listByCharacterConfigIds(@Body CharacterConfigRequest request);
}
