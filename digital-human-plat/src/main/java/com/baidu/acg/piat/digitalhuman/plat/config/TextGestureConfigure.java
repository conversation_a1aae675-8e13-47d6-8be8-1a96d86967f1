package com.baidu.acg.piat.digitalhuman.plat.config;

import com.baidu.acg.piat.digitalhuman.plat.client.Text2GestureClient;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Lazy;

@Configuration
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class TextGestureConfigure {

    @Bean
    @ConfigurationProperties(prefix = "digitalhuman.text-gesture.config")
    public TextGestureConfig textGestureConfig() {
        return new TextGestureConfig();
    }

    @Bean
    @Lazy
    public Text2GestureClient text2GestureClient(TextGestureConfig textGestureConfig) {
        return new Text2GestureClient(textGestureConfig);
    }

    @Data
    public static class TextGestureConfig {
        private String baseUrl = "http://acg-text2gesture:8080";

        private long connectTimeout = 5000; // 连接超时5s
        private long readTimeout = 30 * 1000; // 读超时30s
        private long writeTimeout = 30 * 1000; // 写超时30s
        private long callTimeout = 90 * 1000; // 整个连接超时 90s
    }
}
