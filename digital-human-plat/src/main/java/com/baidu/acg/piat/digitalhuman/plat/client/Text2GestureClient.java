package com.baidu.acg.piat.digitalhuman.plat.client;

import com.baidu.acg.piat.digitalhuman.common.exception.DigitalHumanCommonException;
import com.baidu.acg.piat.digitalhuman.common.intelligentanimojiemotion.Text2GestureRequest;
import com.baidu.acg.piat.digitalhuman.common.intelligentanimojiemotion.Text2GestureResponse;
import com.baidu.acg.piat.digitalhuman.plat.config.TextGestureConfigure;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import okhttp3.ResponseBody;
import retrofit2.Call;
import retrofit2.Retrofit;
import retrofit2.converter.jackson.JacksonConverterFactory;

import java.util.concurrent.TimeUnit;

@Slf4j
public class Text2GestureClient {

    private final String baseUrl;

    private final Text2GestureService text2GestureService;

    public Text2GestureClient(TextGestureConfigure.TextGestureConfig textGestureConfig) {
        // 增加超时配置
        log.info("TextGestureConfig info: {}", textGestureConfig);
        OkHttpClient okHttpClient = new OkHttpClient.Builder()
                .connectTimeout(textGestureConfig.getConnectTimeout(), TimeUnit.MILLISECONDS)
                .readTimeout(textGestureConfig.getReadTimeout(), TimeUnit.MILLISECONDS)
                .writeTimeout(textGestureConfig.getWriteTimeout(), TimeUnit.MILLISECONDS)
                .callTimeout(textGestureConfig.getCallTimeout(), TimeUnit.MILLISECONDS)
                .retryOnConnectionFailure(true)
                .build();

        this.baseUrl = textGestureConfig.getBaseUrl();
        var retrofit = new Retrofit.Builder()
                .addConverterFactory(
                        JacksonConverterFactory.create(new ObjectMapper().registerModule(new JavaTimeModule())))
                .baseUrl(baseUrl)
                .client(okHttpClient)
                .build();
        this.text2GestureService = retrofit.create(Text2GestureService.class);
    }

    public Text2GestureResponse text2Gesture(Text2GestureRequest request) {
        return callService(text2GestureService.text2Gesture(request), "text2Gesture");
    }


    private <T> T callService(Call call, String methodName) throws DigitalHumanCommonException {
        try {
            retrofit2.Response<T> response = call.execute();
            return getResult(response, methodName);
        } catch (DigitalHumanCommonException e) {
            log.error("Fail to call Text2Gesture service methodName={} url={}, ex=",
                    methodName, call.request().url(), e);
            throw e;
        } catch (Exception e) {
            log.error("Fail to call Text2Gesture service methodName={} url={}, ex=",
                    methodName, call.request().url(), e);
            throw new DigitalHumanCommonException(buildMessage(methodName, e.getMessage()), e);
        }
    }

    private <T> T getResult(retrofit2.Response<T> response, String method)
            throws DigitalHumanCommonException {
        if (response.isSuccessful()) {
            T body = response.body();
            if (null == body) {
                throw new DigitalHumanCommonException("response body must not be null");
            }
            return body;
        }
        throw handleResponseUnsuccessful(response, method);
    }


    private <T> DigitalHumanCommonException handleResponseUnsuccessful(retrofit2.Response<T> response, String method) {
        try (ResponseBody errorBody = response.errorBody()) {
            String errorMessage = errorBody == null ? "unknown" : errorBody.string();
            return new DigitalHumanCommonException(response.code(), buildMessage(method, errorMessage));
        } catch (Exception e) {
            return new DigitalHumanCommonException(buildMessage(method, "unknown server error"), e);
        }
    }

    private String buildMessage(String method, String cause) {
        return method + "失败, 原因: " + cause;
    }
}