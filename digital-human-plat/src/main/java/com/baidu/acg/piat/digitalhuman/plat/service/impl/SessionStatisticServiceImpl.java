package com.baidu.acg.piat.digitalhuman.plat.service.impl;

import com.baidu.acg.piat.digitalhuman.common.access.AccessApp;
import com.baidu.acg.piat.digitalhuman.plat.dao.SessionStatisticRepository;
import com.baidu.acg.piat.digitalhuman.plat.model.statistic.SessionStatisticPo;
import com.baidu.acg.piat.digitalhuman.plat.service.AppService;
import com.baidu.acg.piat.digitalhuman.plat.service.SessionStatisticService;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import io.vavr.control.Try;
import lombok.Builder;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.MutablePair;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.orm.jpa.JpaProperties;
import org.springframework.orm.jpa.vendor.Database;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;


@Slf4j
@Service
public class SessionStatisticServiceImpl implements SessionStatisticService {


    @Autowired
    private JpaProperties jpaProperties;
    @Autowired
    private AppService appService;
    @Autowired
    private SessionStatisticRepository sessionStatisticRepository;
    private ArrayBlockingQueue<SessionStatisticData> queue = new ArrayBlockingQueue<>(500000);

    ExecutorService sessionStatisticExecutorService =
            new ThreadPoolExecutor(1, 1
                    , 0L, TimeUnit.MILLISECONDS,
                    new LinkedBlockingQueue<Runnable>());

    @PostConstruct
    public void init() {
        sessionStatisticExecutorService.submit(new SessionStatisticDataSummaryTask());
    }


    @Override
    public void statisticDialogCreate(long timestamp, String appId) {
        Try.run(() -> {
            queue.put(SessionStatisticData.builder()
                    .timestamp(timestamp)
                    .appId(appId)
                    .dataType(SessionStatisticDataType.DIALOG)
                    .build());
        }).onFailure(throwable -> {
            log.error("SessionStatisticService statisticDialogCreate error", throwable);
        });

    }

    @Override
    public void statisticRoomSessionFinish(long timestamp, String appId, long duration) {
        Try.run(() -> {
            queue.put(SessionStatisticData.builder()
                    .timestamp(timestamp)
                    .appId(appId)
                    .roomsessionDuration(duration)
                    .dataType(SessionStatisticDataType.ROOM_SESSION)
                    .build());
        }).onFailure(throwable -> {
            log.error("SessionStatisticService statisticRoomSessionFinish error", throwable);
        });
    }


    private class SessionStatisticDataSummaryTask implements Runnable {
        @Override
        public void run() {
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMMdd");
            Date nowDate = new Date();
            Map<Pair<Integer, String>, MutablePair<Integer, Long>> roomSessionMap = Maps.newHashMap();
            Map<Pair<Integer, String>, Integer> dialogSessionMap = Maps.newHashMap();
            Set<Pair<Integer, String>> dateAppIdPairSet = Sets.newHashSet();
            AtomicInteger count = new AtomicInteger(0);
            int maxSize = 2000;
            while (true) {
                Try.run(() -> {
                    long sartMis = System.currentTimeMillis();
                    while (true) {
                        SessionStatisticData pollEle = queue.poll(5L, TimeUnit.SECONDS);
                        if (null != pollEle && StringUtils.isNotEmpty(pollEle.getAppId())) {
                            String appId = pollEle.getAppId();
                            nowDate.setTime(pollEle.getTimestamp());
                            int date = Integer.valueOf(simpleDateFormat.format(nowDate));
                            Pair<Integer, String> pairKey = Pair.of(date, appId);
                            switch (pollEle.getDataType()) {
                                case DIALOG:
                                    dateAppIdPairSet.add(pairKey);
                                    dialogSessionMap.putIfAbsent(pairKey, 0);
                                    dialogSessionMap.put(pairKey, dialogSessionMap.get(pairKey) + 1);
                                    break;
                                case ROOM_SESSION:
                                    dateAppIdPairSet.add(pairKey);
                                    roomSessionMap.putIfAbsent(pairKey, MutablePair.of(0, 0L));
                                    MutablePair<Integer, Long> mutablePair = roomSessionMap.get(pairKey);
                                    mutablePair.setLeft(mutablePair.getLeft() + 1);
                                    mutablePair.setRight(mutablePair.getRight() + pollEle.getRoomsessionDuration());
                                    break;
                                default:
                                    break;
                            }
                            count.incrementAndGet();
                        }
                        if (pollEle == null || count.get() >= maxSize) {
                            processStatisticData(roomSessionMap, dialogSessionMap, dateAppIdPairSet);
                            roomSessionMap.clear();
                            dialogSessionMap.clear();
                            dateAppIdPairSet.clear();
                            count.set(0);
                            log.warn("SessionStatisticDataSummaryTask Single Task Finish Cost:{}" +
                                            ",dialogSessionMap:{},roomSessionMap:{},count:{}"
                                    , System.currentTimeMillis() - sartMis, dialogSessionMap
                                    , roomSessionMap, count.get());
                            break;
                        }
                    }
                }).onFailure(throwable -> {
                    log.error("SessionStatisticDataSummaryTask run error", throwable);
                });
                // icode 缺陷检查
                if (count.get() > maxSize) {
                    break;
                }
            }
        }
    }


    private void processStatisticData(Map<Pair<Integer, String>
            , MutablePair<Integer, Long>> roomSessionMap
            , Map<Pair<Integer, String>, Integer> dialogSessionMap
            , Set<Pair<Integer, String>> dateAppIdPairSet) {
        if (dateAppIdPairSet.isEmpty()) {
            return;
        }
        Set<String> appIds = dateAppIdPairSet.stream().map(Pair::getValue).collect(Collectors.toSet());
        Map<String, AccessApp> appMap = appService.list(Lists.newArrayList(appIds))
                .stream().collect(Collectors.toMap(AccessApp::getAppId, Function.identity(), (v1, v2) -> v1));
        dateAppIdPairSet.forEach(pair -> {
            AccessApp accessApp = appMap.get(pair.getValue());
            if (null == accessApp || StringUtils.isEmpty(accessApp.getName())) {
                return;
            }
            int sessionTotalCount = 0;
            long sessionTotalDuration = 0L;
            int dialogTotalCount = 0;
            MutablePair<Integer, Long> mutablePair = roomSessionMap.get(pair);
            if (null != mutablePair) {
                sessionTotalCount = sessionTotalCount + mutablePair.getLeft();
                sessionTotalDuration = sessionTotalDuration + mutablePair.getRight();
            }
            Integer integer = dialogSessionMap.get(pair);
            if (null != integer) {
                dialogTotalCount = dialogTotalCount + integer;
            }
            SessionStatisticPo po = SessionStatisticPo.builder().appId(pair.getValue())
                    .accountId(accessApp.getUserId())
                    .sessionTotalCount(sessionTotalCount)
                    .sessionTotalDuration(sessionTotalDuration)
                    .appName(accessApp.getName())
                    .dialogTotalCount(dialogTotalCount)
                    .date(pair.getKey())
                    .build();
            if (Database.MYSQL == jpaProperties.getDatabase()) {
                sessionStatisticRepository.upsertByMySQL(po);
            } else if (Database.POSTGRESQL == jpaProperties.getDatabase()) {
                sessionStatisticRepository.upsertByPg(po);
            }
        });

    }

    @Builder
    @Data
    private static class SessionStatisticData {
        private SessionStatisticDataType dataType;
        private String appId;
        private long timestamp;
        private long roomsessionDuration;
    }

    private enum SessionStatisticDataType {
        ROOM_SESSION,
        DIALOG;
    }


}
