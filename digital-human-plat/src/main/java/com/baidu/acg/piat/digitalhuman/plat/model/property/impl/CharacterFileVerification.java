package com.baidu.acg.piat.digitalhuman.plat.model.property.impl;

import com.baidu.acg.piat.digitalhuman.common.exception.DigitalHumanCommonException;
import com.baidu.acg.piat.digitalhuman.common.utils.ZipFileUtil;
import com.baidu.acg.piat.digitalhuman.plat.model.property.FileVerification;
import com.baidu.acg.piat.digitalhuman.plat.model.property.PropertyFileResponse;
import com.baidu.acg.piat.digitalhuman.plat.model.property.PropertyType;
import io.vavr.Tuple2;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Enumeration;
import java.util.List;
import java.util.zip.ZipEntry;
import java.util.zip.ZipFile;

@Slf4j
@Component
@RequiredArgsConstructor
public class CharacterFileVerification implements FileVerification {

    @Override
    public boolean support(PropertyType type) {
        return type.equals(PropertyType.TOP)
                || type.equals(PropertyType.BOTTOM)
                || type.equals(PropertyType.BODY)
                || type.equals(PropertyType.HAIR)
                || type.equals(PropertyType.SHOES)
                || type.equals(PropertyType.ACCESSORY)
                || type.equals(PropertyType.ADDITIONAL);
    }

    /**
     * 对用户上传的配饰文件夹名字进行校验，应当符合"配饰名_out"的命名规范
     * @param file
     * @return
     */
    @Override
    public Tuple2<PropertyFileResponse, String> verify(MultipartFile file, String type) throws IOException {
        Tuple2<ZipFile, String> zipFileAndPath = ZipFileUtil.multipartFileToZipFile(file);
        String temporaryFilePath = zipFileAndPath._2();
        ZipFile zipFile = zipFileAndPath._1();

        String propertyId = "";
        List<String> nameList = new ArrayList<>();
        boolean hasBin = false;

        for (Enumeration entries = zipFile.entries(); entries.hasMoreElements(); ) {
            ZipEntry entry = (ZipEntry) entries.nextElement();
            String curEntryName = entry.getName();
            if (curEntryName.startsWith("__MACOSX/")) {
                continue;
            }

            if (curEntryName.endsWith("/")) {
                // 校验文件夹名称
                if (!curEntryName.endsWith("_out/")) {
                    throw new DigitalHumanCommonException("文件夹名称不符合规范");
                }
                propertyId = curEntryName.split("_out/")[0];

            } else if (curEntryName.endsWith(".bin")) {
                hasBin = true;
            } else if (curEntryName.contains("/")){
                nameList.add(curEntryName.split("/")[1]);
            }

        }

        // 是否有bin文件
        if (!hasBin) {
            throw new DigitalHumanCommonException("bin文件缺失");
        }

        // 校验gltf
        try {
            verifyGltfFileName(nameList, propertyId);
            return new Tuple2<>(PropertyFileResponse.builder().propertyId(propertyId).frameCount(0).build(),
                    temporaryFilePath);
        } catch (DigitalHumanCommonException e) {
            throw new DigitalHumanCommonException(e.getMessage());
        }

    }

}
