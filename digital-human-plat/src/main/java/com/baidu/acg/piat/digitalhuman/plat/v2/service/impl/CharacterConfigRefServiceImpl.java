package com.baidu.acg.piat.digitalhuman.plat.v2.service.impl;

import com.baidu.acg.piat.digitalhuman.common.character.CharacterConfigRef;
import com.baidu.acg.piat.digitalhuman.common.character.CharacterConfigRefDeleteReq;
import com.baidu.acg.piat.digitalhuman.common.character.CharacterConfigRefListReq;
import com.baidu.acg.piat.digitalhuman.common.character.CharacterConfigRefType;
import com.baidu.acg.piat.digitalhuman.common.character.CharacterConfigRefUpsertReq;
import com.baidu.acg.piat.digitalhuman.common.entity.CharacterConfigRelatedModule;
import com.baidu.acg.piat.digitalhuman.common.exception.DigitalHumanCommonException;
import com.baidu.acg.piat.digitalhuman.plat.dao.CharacterConfigRefRepository;
import com.baidu.acg.piat.digitalhuman.plat.model.character.CharacterConfigRefPo;
import com.baidu.acg.piat.digitalhuman.plat.v2.service.CharacterConfigRefService;
import com.baidu.acg.piat.digitalhuman.plat.v2.service.CharacterConfigService;
import io.vavr.control.Try;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
public class CharacterConfigRefServiceImpl implements CharacterConfigRefService {

    @Autowired
    private CharacterConfigRefRepository characterConfigRefRepository;

    @Autowired
    private CharacterConfigService characterConfigService;

    @Override
    public List<CharacterConfigRef> upsert(CharacterConfigRefUpsertReq req) {
        if (CollectionUtils.isEmpty(req.getCharacterConfigIds())) {
            return Lists.newArrayList();
        }
        // 校验枚举
        CharacterConfigRefType type = CharacterConfigRefType.valueOf(req.getType());
        characterConfigRefRepository.deleteByBusinessAssetIdEqual(req.getBusinessAssetId(), req.getType());
        List<CharacterConfigRefPo> entities = Lists.newArrayList();
        // 验证 人设的是否存在
        Try.run(() -> characterConfigService.retrieveByIds(req.getCharacterConfigIds()))
                .onFailure(throwable -> {
                    throw new DigitalHumanCommonException(throwable.getMessage());
                });
        if (null != req.getCharacterConfigIds()) {
            req.getCharacterConfigIds().stream().collect(Collectors.toSet()).forEach(id -> {
                CharacterConfigRefPo po = CharacterConfigRefPo.builder()
                        .type(req.getType())
                        .characterConfigId(id)
                        .businessAssetId(req.getBusinessAssetId())
                        .businessAssetName(req.getBusinessAssetName())
                        .build();
                entities.add(po);

            });
        }
        if (entities.isEmpty()) {
            return Lists.newArrayList();
        }
        characterConfigRefRepository.saveAll(entities);
        return entities.stream().map(CharacterConfigRefPo::toViewObject)
                .collect(Collectors.toList());
    }

    @Override
    public List<CharacterConfigRef> list(CharacterConfigRefListReq req) {
        if (!CollectionUtils.isEmpty(req.getBusinessAssetIds())) {
            return characterConfigRefRepository.findByBusinessAssetIdIn(req.getBusinessAssetIds()).stream()
                    .map(CharacterConfigRefPo::toViewObject).collect(Collectors.toList());
        }
        return Lists.newArrayList();
    }

    @Override
    public List<CharacterConfigRelatedModule> listCharacterConfigRelation(List<String> characterConfigIds) {
        if (CollectionUtils.isEmpty(characterConfigIds)) {
            return Lists.newArrayList();
        }

        List<CharacterConfigRefPo> list = characterConfigRefRepository.findByCharacterConfigIdIn(characterConfigIds);
        return list.stream().map(item -> {
            return CharacterConfigRelatedModule.builder()
                    .configId(item.getCharacterConfigId())
                    .name(item.getBusinessAssetName())
                    .type(item.getType())
                    .id(item.getBusinessAssetId())
                    .build();
        }).collect(Collectors.toList());
    }

    @Override
    public Void delete(CharacterConfigRefDeleteReq req) {

        characterConfigRefRepository.deleteByBusinessAssetIdEqual(req.getBusinessAssetId(), req.getType());
        return null;
    }
}
