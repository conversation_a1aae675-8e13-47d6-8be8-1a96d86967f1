package com.baidu.acg.piat.digitalhuman.plat.dao;

import com.baidu.acg.piat.digitalhuman.plat.model.optlog.OptLogModel;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;
import java.util.Optional;

public interface OptLogRepository extends JpaRepository<OptLogModel, Long>, JpaSpecificationExecutor<OptLogModel> {

    @Query(value = "select " + OptLogModel.OPT_LOG_COLUMNS + " from dh_opt_log where opt_time > :startTime and opt_time <= :endTime and id > :id and is_delete = 0 " +
            "and opt_account_id in :acountIds order by id asc limit :limit", nativeQuery = true)
    List<OptLogModel> findOptLogModelByOptAccountIdInAndOptTimeBetweenAndAndIsDelete(@Param("startTime") long startTime,
                                                                             @Param("endTime") long endTime,
                                                                             @Param("limit") int limit,
                                                                             @Param("acountIds") List<String> acountIds,
                                                                             @Param("id") long id);

    @Query(value = "select " + OptLogModel.OPT_LOG_COLUMNS + " from dh_opt_log where opt_time > :startTime and opt_time <= :endTime and id > :id and is_delete = 0 " +
            "order by id asc limit :limit", nativeQuery = true)
    List<OptLogModel> findOptLogModelByOptTimeBetweenAndAndIsDelete(@Param("startTime") long startTime,
                                                                                     @Param("endTime") long endTime,
                                                                                     @Param("limit") int limit,
                                                                                     @Param("id") long id);

    @Query(value = "select " + OptLogModel.OPT_LOG_COLUMNS +  " from dh_opt_log order by id asc limit 1", nativeQuery = true)
    Optional<OptLogModel> findFirstOptLogModel();

    @Query(value = "select " + OptLogModel.OPT_LOG_COLUMNS +  " from dh_opt_log where opt_account_id in :acountIds order by id asc limit 1", nativeQuery = true)
    Optional<OptLogModel> findFirstOptLogModelByAccountId(@Param("acountIds") List<String> acountIds);

}
