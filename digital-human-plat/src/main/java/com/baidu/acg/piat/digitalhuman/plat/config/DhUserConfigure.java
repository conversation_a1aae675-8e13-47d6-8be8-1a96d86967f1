package com.baidu.acg.piat.digitalhuman.plat.config;

import lombok.Data;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import com.baidu.acg.dh.user.client.UserClient;

/**
 * Created on 2021/1/28 14:41.
 *
 * <AUTHOR>
 */
@Configuration
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class DhUserConfigure {

    @Bean
    @ConfigurationProperties(prefix = "digitalhuman.user.config")
    public Config userConfig() {
        return new Config();
    }

    @Bean
    public UserClient userClient() {
        return new UserClient(userConfig().getBaseUrl());
    }

    @Data
    public static class Config {

        private String baseUrl;

        private int retryTimes = 3;

        private int retrySleepMillis = 100;

    }
}
