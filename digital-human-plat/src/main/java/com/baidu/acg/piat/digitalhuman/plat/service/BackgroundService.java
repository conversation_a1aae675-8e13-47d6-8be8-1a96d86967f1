package com.baidu.acg.piat.digitalhuman.plat.service;

import com.baidu.acg.piat.digitalhuman.common.background.BackgroundImage;
import com.baidu.acg.piat.digitalhuman.common.background.BackgroundUploadRequest;

import lombok.NonNull;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;

import java.util.Optional;

/**
 * <AUTHOR>
 */
public interface BackgroundService {
    /**
     * 获取背景图
     *
     * @param backgroundImageId
     * @return
     */
    Optional<BackgroundImage> get(@NonNull String backgroundImageId);

    /**
     * 传入appId，获取appId下所有的背景图
     *
     * @param pageable
     * @param appId
     * @return
     */
    PageImpl<BackgroundImage> selectByUserId(
            @NonNull String appId,
            Pageable pageable
    );

    /**
     * 上传一张新的背景图片
     *
     * @param request
     * @return
     */
    BackgroundImage create(BackgroundUploadRequest request);

    /**
     * 根据背景图id，删除对应的背景图
     *
     * @param backgroundImageId
     */
    void delete(@NonNull String backgroundImageId);

}
