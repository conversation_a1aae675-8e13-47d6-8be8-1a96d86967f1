package com.baidu.acg.piat.digitalhuman.plat.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import com.baidu.acg.piat.digitalhuman.videopipeline.client.VideoProgressHttpClient;

/**
 *
 * <AUTHOR>
 * @since 2021/10/27
 */
@Configuration
public class VideoPipelineConfigure {
    @Bean
    @ConfigurationProperties(prefix = "digitalhuman.videopipeline")
    public VideoPipelineConfig videoPipelineConfig() {
        return new VideoPipelineConfig();
    }

    @Bean
    public VideoProgressHttpClient videoProgressHttpClient() {
        return new VideoProgressHttpClient(videoPipelineConfig().getBaseUrl());
    }

    @Data
    public static class VideoPipelineConfig {
        private String baseUrl = "http://digital-human-video-pipeline:8080";
    }
}
