package com.baidu.acg.piat.digitalhuman.plat.service.impl;

import com.baidu.acg.piat.digitalhuman.common.optlog.OptContent;
import com.baidu.acg.piat.digitalhuman.common.optlog.OptLog;
import com.baidu.acg.piat.digitalhuman.plat.dao.ProductionStatisticRepository;
import com.baidu.acg.piat.digitalhuman.plat.model.common.ProductionStatisticType;
import com.baidu.acg.piat.digitalhuman.plat.model.statistic.ProductionStatisticPo;
import com.baidu.acg.piat.digitalhuman.plat.service.ProductionStatisticService;
import com.google.common.collect.Maps;
import io.vavr.control.Try;
import lombok.Builder;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.orm.jpa.JpaProperties;
import org.springframework.orm.jpa.vendor.Database;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;


@Slf4j
@Service
public class ProductionStatisticServiceImpl implements ProductionStatisticService {
    private Map<OptContent, ProductionStatisticType> optLongMap = Maps.newHashMap();
    @Autowired
    private JpaProperties jpaProperties;
    @Autowired
    private ProductionStatisticRepository productionStatisticRepository;

    private ArrayBlockingQueue<ProductionStatisticData> queue = new ArrayBlockingQueue<>(500000);

    ExecutorService productionStatisticExecutorService =
            new ThreadPoolExecutor(1, 1
                    , 0L, TimeUnit.MILLISECONDS,
                    new LinkedBlockingQueue<Runnable>());

    @PostConstruct
    public void init() {
        optLongMap.put(OptContent.CHARACTERCONFIG_ADD, ProductionStatisticType.CHARACTER_CONFIG);
        optLongMap.put(OptContent.CHARACTERCONFIG_COPY, ProductionStatisticType.CHARACTER_CONFIG);
        optLongMap.put(OptContent.MHE_PROJECT_ADD, ProductionStatisticType.MHE_PROJECT);
        optLongMap.put(OptContent.MHE_PROJECT_COPY, ProductionStatisticType.MHE_PROJECT);
        optLongMap.put(OptContent.MHE_PROJECT_EXPORT, ProductionStatisticType.MHE_VIDEO);
        optLongMap.put(OptContent.SCENE_ADD, ProductionStatisticType.SCENE_PROJECT);
        optLongMap.put(OptContent.SCENE_COPY, ProductionStatisticType.SCENE_PROJECT);
        optLongMap.put(OptContent.APP_ADD, ProductionStatisticType.APP);
        productionStatisticExecutorService.submit(new ProductionStatisticDataSummaryTask());
    }


    @Override
    public void statisticProductionOptLog(OptLog optLog) {
        Try.run(() -> {
            Optional<OptContent> optContent = OptContent.valueOf(optLog.getOptModule(), optLog.getOptType());
            if (optContent.isEmpty()) {
                log.error("invalid optcontent");
                return;
            }
            ProductionStatisticType productionStatisticType = optLongMap.get(optContent.get());
            if (productionStatisticType == null) {
                return;
            }
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMMdd");
            Date nowDate = new Date();
            nowDate.setTime(optLog.getOptTime());
            ProductionStatisticData data = ProductionStatisticData.builder()
                    .accountId(optLog.getOptAccountId())
                    .userId(optLog.getOptUserId())
                    .type(productionStatisticType.getCode())
                    .date(Integer.valueOf(simpleDateFormat.format(nowDate)))
                    .build();
            queue.put(data);
        }).onFailure(throwable -> {
            log.error("ProductionStatisticService statisticProductionOptLog error", throwable);
        });
    }


    private class ProductionStatisticDataSummaryTask implements Runnable {
        @Override
        public void run() {
            Map<ProductionStatisticData, Integer> statisticSummaryMap = Maps.newHashMap();
            AtomicInteger count = new AtomicInteger(0);
            int maxSize = 2000;
            while (true) {
                Try.run(() -> {
                    long sartMis = System.currentTimeMillis();
                    while (true) {
                        ProductionStatisticData pollEle = queue.poll(5L, TimeUnit.SECONDS);
                        if (null != pollEle) {
                            statisticSummaryMap.putIfAbsent(pollEle, 0);
                            statisticSummaryMap.put(pollEle, statisticSummaryMap.get(pollEle) + 1);
                            count.incrementAndGet();
                        }
                        if (pollEle == null || count.get() >= maxSize) {
                            processStatisticData(statisticSummaryMap);
                            statisticSummaryMap.clear();
                            count.set(0);
                            log.warn("ProductionStatisticDataSummaryTask Single Task Finish Cost:{}" +
                                            ",statisticSummaryMap:{},count:{}"
                                    , System.currentTimeMillis() - sartMis
                                    , statisticSummaryMap, count.get());
                            break;
                        }
                    }
                }).onFailure(throwable -> {
                    log.error("ProductionStatisticDataSummaryTask run error", throwable);
                });
                // icode 缺陷检查
                if (count.get() > maxSize) {
                    break;
                }
            }
        }
    }


    private void processStatisticData(Map<ProductionStatisticData, Integer> statisticSummaryMap) {
        if (statisticSummaryMap.isEmpty()) {
            return;
        }
        statisticSummaryMap.forEach((key, val) -> {
            ProductionStatisticPo po = ProductionStatisticPo.builder()
                    .accountId(key.getAccountId())
                    .userId(key.getUserId())
                    .date(key.getDate())
                    .type(key.getType())
                    .increaseCount(val)
                    .build();
            if (Database.MYSQL == jpaProperties.getDatabase()) {
                productionStatisticRepository.upsertByMySQL(po);
            } else if (Database.POSTGRESQL == jpaProperties.getDatabase()) {
                productionStatisticRepository.upsertByPg(po);
            }
        });
    }

    @Builder
    @Data
    private static class ProductionStatisticData {
        private int date;
        private int type;
        private String accountId;
        private String userId;
    }
}
