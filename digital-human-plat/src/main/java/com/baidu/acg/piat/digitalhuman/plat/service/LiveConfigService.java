package com.baidu.acg.piat.digitalhuman.plat.service;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.Optional;

import com.baidu.acg.piat.digitalhuman.common.live.LiveConfig;

/**
 * <AUTHOR>
 * @since 2021/08/05
 */
public interface LiveConfigService {
    LiveConfig create(LiveConfig liveConfig);

    LiveConfig update(LiveConfig liveConfig);

    Optional<LiveConfig> findOneByConfigId(String configId);

    Optional<LiveConfig> findLatestByUserId(String userId);

    Page<LiveConfig> findAllByUserId(String userId, Pageable pageable);

    void deleteByConfigId(String configId);
}
