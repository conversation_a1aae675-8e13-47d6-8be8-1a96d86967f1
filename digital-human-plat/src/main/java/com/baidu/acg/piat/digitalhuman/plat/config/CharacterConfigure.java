package com.baidu.acg.piat.digitalhuman.plat.config;

import lombok.Data;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.HashMap;
import java.util.Map;

import com.baidu.acg.piat.digitalhuman.common.project.FigureCutParams;


@Configuration
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class CharacterConfigure {

    @Bean
    @ConfigurationProperties(prefix = "digitalhuman.character.config")
    public CharacterConfigure.Config characterConfig() {
        return new CharacterConfigure.Config();
    }

    @Bean
    @ConfigurationProperties(prefix = "digitalhuman.character.figure-cut")
    public Map<String, FigureCutParams> figureCutMap() {
        return new HashMap<>();
    }

    @Data
    public static class Config {

        private int roomLimits = 100;

    }
}
