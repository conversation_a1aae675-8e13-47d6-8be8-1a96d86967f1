package com.baidu.acg.piat.digitalhuman.plat.model.property;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class SkeletonFile {

    @JsonProperty("m_name")
    private String name;

    @JsonProperty("m_frame")
    private int frame;

    @JsonProperty("m_image")
    private String image;

    @JsonProperty("m_nodeName")
    private List<String> nodeName;

    @JsonProperty("m_nodeDatas")
    private List<List<NodeData>> nodeDatas;

    @Builder
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    public static class NodeData {
        double x;
        double y;
        double z;
    }

}
