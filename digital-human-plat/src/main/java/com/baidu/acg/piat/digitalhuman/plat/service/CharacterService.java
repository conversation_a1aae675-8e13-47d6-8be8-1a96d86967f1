package com.baidu.acg.piat.digitalhuman.plat.service;

import com.baidu.acg.piat.digitalhuman.common.entity.CharacterModel;
import com.baidu.acg.piat.digitalhuman.common.model.PageResponse;
import lombok.NonNull;

import java.awt.image.BufferedImage;
import java.util.List;
import java.util.Map;

/**
 * 管理characterMeta
 *
 * <AUTHOR>
 */
public interface CharacterService {
    /**
     * 创建一个meta对象
     *
     * @param request
     * @return
     */
    CharacterModel create(CharacterModel request);

    CharacterModel create(CharacterModel request, int apiVersion);

    List<CharacterModel> listAll(boolean visibleForLive, boolean visibleForSce, int apiVersion);

    /**
     * Query characters pageable
     *
     * @param userVisibleTypes user visible character types, split by ',';
     *                         query all flag : 'all'
     * @param visibleForLive   query all if false
     * @param visibleForSce    query all if false
     */
    PageResponse<CharacterModel> query(int pageNo, int pageSize, int apiVersion, String userVisibleTypes,
                                       boolean visibleForLive, boolean visibleForSce);

    /**
     * 根据ID获取
     *
     * @param characterId
     * @return
     */
    CharacterModel selectById(String characterId);

    /**
     * 根据类型获取
     *
     * @param characterMetaType
     * @return
     */
    List<CharacterModel> selectByType(String characterMetaType);

    List<CharacterModel> selectByTypes(List<String> characterMetaTypes, int apiVersion);

    CharacterModel selectByType(String characterMetaType, int apiVersion);

    CharacterModel selectByFigureName(String figureName);

    /**
     * 查找所有meta对象
     *
     * @param pageNo
     * @param pageSize
     * @return
     */
    PageResponse<CharacterModel> selectAll(int pageNo, int pageSize);

    PageResponse<CharacterModel> selectAll(int pageNo, int pageSize, int apiVersion);

    PageResponse<CharacterModel> selectAll(int pageNo, int pageSize, int apiVersion, boolean visibleForLive);

    /**
     * 查找所有sce可见的meta对象
     *
     * @param pageNo
     * @param pageSize
     * @return
     */
    PageResponse<CharacterModel> selectVisibleForSce(boolean visibleForSce, int pageNo, int pageSize);

    PageResponse<CharacterModel> selectVisibleForSce(boolean visibleForSce, int pageNo, int pageSize, int apiVersion);

    PageResponse<CharacterModel> selectNotVisibleForSce(int pageNo, int pageSize,
                                                        int apiVersion);

    /**
     * 根据id删除
     *
     * @param id
     * @return
     */
    void deleteById(String id);

    /**
     * 根据类型删除
     *
     * @param characterMetaType
     * @return
     */
    void deleteByType(String characterMetaType);

    /**
     * 根据id更新内容
     *
     * @param id
     * @param request
     * @return
     */
    CharacterModel updateById(String id, CharacterModel request);

    /**
     * 根据类型更新内容
     * 忽略request中的Id 和 type
     *
     * @param characterImageType
     * @param request
     * @return
     */
    CharacterModel updateByType(@NonNull String characterImageType, CharacterModel request, int apiVersion);

    Map<String, String> batchUpdateTts(Map<String, String> ttsMap, int apiVersion);

    /**
     * 预览在给定背景和模型下的人像
     *
     * @param characterModelType
     * @param backgroundImageId
     * @return
     */
    BufferedImage preview(@NonNull String characterModelType, @NonNull String backgroundImageId, int apiVersion);

}
