package com.baidu.acg.piat.digitalhuman.plat.model.character;

import com.baidu.acg.piat.digitalhuman.common.character.CharacterConfigRef;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;
import org.springframework.beans.BeanUtils;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import java.time.LocalDateTime;

@Data
@Accessors(chain = true)
@Entity(name = "character_config_ref")
@JsonInclude(Include.NON_NULL)
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class CharacterConfigRefPo {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private long id;

    private String businessAssetId;

    private String type;

    private String businessAssetName;

    private String characterConfigId;

    @CreationTimestamp
    @Column(insertable = false, updatable = false)
    private LocalDateTime createTime = LocalDateTime.now();

    @UpdateTimestamp
    @Column(insertable = false, updatable = false)
    private LocalDateTime updateTime = LocalDateTime.now();

    public CharacterConfigRef toViewObject() {
        CharacterConfigRef ref = new CharacterConfigRef();
        BeanUtils.copyProperties(this, ref);
        return ref;
    }
}
