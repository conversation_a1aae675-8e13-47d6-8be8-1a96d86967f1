package com.baidu.acg.piat.digitalhuman.plat.service.impl;

import com.google.common.annotations.VisibleForTesting;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.Optional;
import java.util.UUID;

import com.baidu.acg.piat.digitalhuman.common.exception.DigitalHumanCommonException;
import com.baidu.acg.piat.digitalhuman.common.live.LiveConfig;
import com.baidu.acg.piat.digitalhuman.plat.dao.LiveConfigRepository;
import com.baidu.acg.piat.digitalhuman.plat.model.LiveConfigModel;
import com.baidu.acg.piat.digitalhuman.plat.service.LiveConfigService;

/**
 * <AUTHOR>
 * @since 2021/08/05
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class LiveConfigServiceImpl implements LiveConfigService {
    private final LiveConfigRepository repository;

    @Override
    public LiveConfig create(LiveConfig liveConfig) {
        liveConfig.setConfigId(UUID.randomUUID().toString());
        LiveConfigModel model = toModel(liveConfig);
        return toLiveConfig(repository.save(model));
    }

    @Override
    public LiveConfig update(LiveConfig liveConfig) {
        if (StringUtils.isBlank(liveConfig.getConfigId())) {
            throw new DigitalHumanCommonException("Update live config found configId is blank.");
        }
        var modelOpt = repository.findOneByConfigId(liveConfig.getConfigId());
        if (!modelOpt.isPresent()) {
            throw new DigitalHumanCommonException(
                    "Update live config found config is not exist, config id=" + liveConfig.getConfigId());
        }
        return toLiveConfig(repository.save(toModel(liveConfig).setId(modelOpt.get().getId())));
    }

    @Override
    public Optional<LiveConfig> findOneByConfigId(String configId) {
        return repository.findOneByConfigId(configId).map(this::toLiveConfig);
    }

    @Override
    public Optional<LiveConfig> findLatestByUserId(String userId) {
        return repository.findTopByUserIdOrderByCreateTimeDesc(userId).map(this::toLiveConfig);
    }

    @Override
    public Page<LiveConfig> findAllByUserId(String userId, Pageable pageable) {
        return repository.findAllByUserIdOrderByCreateTimeDesc(userId, pageable).map(this::toLiveConfig);
    }

    @Override
    public void deleteByConfigId(String configId) {
        repository.deleteByConfigId(configId);
    }

    @VisibleForTesting
    LiveConfig toLiveConfig(LiveConfigModel model) {
        LiveConfig config = new LiveConfig();
        BeanUtils.copyProperties(model, config);
        return config;
    }

    private LiveConfigModel toModel(LiveConfig config) {
        LiveConfigModel model = new LiveConfigModel();
        BeanUtils.copyProperties(config, model);
        return model;
    }
}
