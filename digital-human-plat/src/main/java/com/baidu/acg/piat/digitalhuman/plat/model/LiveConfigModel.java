package com.baidu.acg.piat.digitalhuman.plat.model;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;
import org.hibernate.annotations.UpdateTimestamp;

import java.io.IOException;
import java.time.ZonedDateTime;
import java.util.List;

import javax.persistence.Access;
import javax.persistence.AccessType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Transient;

import com.baidu.acg.piat.digitalhuman.common.live.RenderTypeEnum;
import com.baidu.acg.piat.digitalhuman.common.utils.JsonUtil;

/**
 * 直播配置
 *
 * <AUTHOR>
 * @since 2021/08/09
 */
@Data
@Slf4j
@Builder
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
@DynamicInsert
@DynamicUpdate
@Entity(name = "live_config")
public class LiveConfigModel {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    private String userId;

    private String configId;

    private String characterName;

    private String name;

    private Integer resolutionHeight;

    private Integer resolutionWidth;

    private Integer bitrate;

    private String characterConfigId;

    @Transient
    private List<String> rtmpUrlList;

    private String videoDirectoryId;

    private RenderTypeEnum renderType;

    private String workstationLabel;

    @CreationTimestamp
    @Column(insertable = false, updatable = false)
    private ZonedDateTime createTime;

    @UpdateTimestamp
    @Column(insertable = false, updatable = false)
    private ZonedDateTime updateTime;

    @Column(name = "rtmp_url_list")
    @Access(AccessType.PROPERTY)
    public String getRtmpUrlListString() {
        if (rtmpUrlList != null) {
            try {
                return JsonUtil.writeValueAsString(rtmpUrlList);
            } catch (JsonProcessingException e) {
                log.error("Parse rtmpUrlList to json failed.", e);
            }
        }
        return StringUtils.EMPTY;
    }

    public void setRtmpUrlListString(String rtmpUrlListJson) {
        if (StringUtils.isNotEmpty(rtmpUrlListJson)) {
            try {
                this.rtmpUrlList = JsonUtil.readValue(rtmpUrlListJson, new TypeReference<List<String>>() {
                });
            } catch (IOException e) {
                log.error("Exception when parse rtmpUrlList from json string, string={}.", rtmpUrlListJson, e);
            }
        } else {
            this.rtmpUrlList = null;
        }
    }
}
