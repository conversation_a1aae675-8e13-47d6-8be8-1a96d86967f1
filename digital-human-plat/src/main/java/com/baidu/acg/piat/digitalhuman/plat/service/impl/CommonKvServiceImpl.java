package com.baidu.acg.piat.digitalhuman.plat.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import com.baidu.acg.piat.digitalhuman.common.commonkv.CommonKv;
import com.baidu.acg.piat.digitalhuman.plat.dao.CommonKvRepository;
import com.baidu.acg.piat.digitalhuman.plat.model.CommonKvModel;
import com.baidu.acg.piat.digitalhuman.plat.service.CommonKvService;

/**
 * <AUTHOR>
 * @since 2021/08/05
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class CommonKvServiceImpl implements CommonKvService {
    private final CommonKvRepository repository;

    @Override
    public CommonKv saveOrUpdate(CommonKv commonKv) {
        List<CommonKvModel> exits =
                repository.findAllByUserIdAndKeyIn(commonKv.getUserId(), List.of(commonKv.getKey()));
        CommonKvModel model;
        if (CollectionUtils.isEmpty(exits)) {
            model = CommonKvModel.builder().userId(commonKv.getUserId()).key(commonKv.getKey())
                    .value(commonKv.getValue()).build();
        } else {
            model = exits.get(0);
            model.setValue(commonKv.getValue());
        }
        return toCommonKv(repository.save(model));
    }

    @Override
    public Optional<CommonKv> findByUserIdAndKey(String userId, String key) {
        return repository.findByUserIdAndKey(userId, key).map(this::toCommonKv);
    }

    @Override
    public List<CommonKv> findAllByUserId(String userId) {
        return repository.findAllByUserId(userId).stream().map(this::toCommonKv).collect(Collectors.toList());
    }

    @Override
    public List<CommonKv> findAllByUserIdAndKeyIn(String userId, List<String> keyList) {
        if (CollectionUtils.isEmpty(keyList)) {
            return findAllByUserId(userId);
        } else {
            return repository.findAllByUserIdAndKeyIn(userId, keyList).stream().map(this::toCommonKv)
                    .collect(Collectors.toList());
        }
    }

    @Override
    public void deleteByUserIdAndKey(String userId, String key) {
        repository.deleteByUserIdAndKey(userId, key);
    }

    private CommonKv toCommonKv(CommonKvModel model) {
        return CommonKv.builder().userId(model.getUserId()).key(model.getKey()).value(model.getValue()).build();
    }
}
