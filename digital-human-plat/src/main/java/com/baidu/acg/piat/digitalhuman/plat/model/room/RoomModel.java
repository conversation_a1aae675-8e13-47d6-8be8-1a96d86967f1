package com.baidu.acg.piat.digitalhuman.plat.model.room;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;
import org.hibernate.annotations.UpdateTimestamp;

import java.sql.Date;
import java.time.ZonedDateTime;

import javax.persistence.Access;
import javax.persistence.AccessType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Transient;
import javax.persistence.Version;

import com.baidu.acg.piat.digitalhuman.common.room.Room;
import com.baidu.acg.piat.digitalhuman.common.room.RoomStatus;

/**
 * Room model.
 *
 * <AUTHOR> (<EMAIL>)
 */
@Data
@Builder
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
@DynamicInsert
@DynamicUpdate
@Entity(name = "room")
public class RoomModel {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    private String roomId;

    private String userId;

    private String appId;

    private String roomName;

    @Transient
    private RoomStatus status;

    @CreationTimestamp
    @Column(insertable = false, updatable = false)
    private ZonedDateTime createTime;

    @UpdateTimestamp
    @Column(insertable = false, updatable = false)
    private ZonedDateTime updateTime;

    @Version
    private Integer version;

    @Column(name = "status")
    @Access(AccessType.PROPERTY)
    public String getStatusName() {
        if (status != null) {
            return status.name();
        }
        return StringUtils.EMPTY;
    }

    public void setStatusName(String status) {
        if (StringUtils.isNotEmpty(status)) {
            this.status = RoomStatus.valueOf(status);
        } else {
            this.status = null;
        }
    }

    public Room toRoom() {
        return Room.builder()
                .id(roomId)
                .appId(appId)
                .userId(userId)
                .roomName(roomName)
                .status(status)
                .createTime(Date.from(createTime.toInstant()))
                .updateTime(Date.from(updateTime.toInstant()))
                .build();
    }
}