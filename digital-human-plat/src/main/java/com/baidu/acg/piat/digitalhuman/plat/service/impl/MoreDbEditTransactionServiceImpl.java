// Copyright (C) 2019 Baidu Inc. All rights reserved.
package com.baidu.acg.piat.digitalhuman.plat.service.impl;

import com.baidu.acg.piat.digitalhuman.common.entity.CharacterConfig;
import com.baidu.acg.piat.digitalhuman.plat.dao.AppRepository;
import com.baidu.acg.piat.digitalhuman.plat.dao.ProjectOnlineRepository;
import com.baidu.acg.piat.digitalhuman.plat.dao.ProjectRepository;
import com.baidu.acg.piat.digitalhuman.plat.model.app.AccessAppModel;
import com.baidu.acg.piat.digitalhuman.plat.model.project.ProjectModel;
import com.baidu.acg.piat.digitalhuman.plat.v2.model.AILiveConfig;
import com.baidu.acg.piat.digitalhuman.plat.v2.repository.AILiveConfigRepository;
import com.baidu.acg.piat.digitalhuman.plat.v2.repository.CharacterConfigRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.util.List;


@Slf4j
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class MoreDbEditTransactionServiceImpl {
    private final ProjectOnlineRepository projectOnlineRepository;

    private final AppRepository appRepository;

    private final ProjectRepository repository;

    private final CharacterConfigRepository characterConfigRepository;

    private final AILiveConfigRepository aiLiveConfigRepository;

    private final SlaveDbServiceImpl slaveDbService;

    @Transactional
    public AccessAppModel saveApp(AccessAppModel accessAppModel) {
        AccessAppModel accessAppModelV2 = new AccessAppModel();
        BeanUtils.copyProperties(accessAppModel, accessAppModelV2);
        accessAppModel = appRepository.save(accessAppModel);
        accessAppModelV2.setId(accessAppModel.getId());
        slaveDbService.saveAppSlave(accessAppModelV2);
        return accessAppModel;
    }

    @Transactional
    public void deleteByAppId(AccessAppModel accessAppModel) {
        appRepository.deleteByAppId(accessAppModel.getAppId());
        slaveDbService.deleteByAppIdSlave(accessAppModel.getAppId());
    }

    @Transactional
    public ProjectModel saveProjectModel(ProjectModel projectModel) {
        ProjectModel projectModelV2 = new ProjectModel();
        BeanUtils.copyProperties(projectModel, projectModelV2);
        projectModel = repository.save(projectModel);
        projectModelV2.setId(projectModel.getId());
        slaveDbService.saveProjectSlave(projectModelV2);
        return projectModel;
    }

    @Transactional
    public void deleteByProjectId(ProjectModel projectModel) {
        repository.deleteByProjectId(projectModel.getProjectId());
        slaveDbService.deleteByProjectIdSlave(projectModel.getProjectId());
        projectOnlineRepository.deleteByProjectId(projectModel.getProjectId());
    }

    @Transactional
    public CharacterConfig saveCharacterConfig(CharacterConfig characterConfig) {
        CharacterConfig characterConfigV2 = new CharacterConfig();
        BeanUtils.copyProperties(characterConfig, characterConfigV2);
        characterConfig = characterConfigRepository.save(characterConfig);
        slaveDbService.saveCharacterConfigSlave(characterConfigV2);
        return characterConfig;
    }

    @Transactional
    public void deleteCharacterConfig(CharacterConfig characterConfig) {
        characterConfigRepository.delete(characterConfig);
        slaveDbService.deleteCharacterConfigSlave(characterConfig);
    }

    @Transactional
    public void deleteAllCharacterConfig(List<CharacterConfig> list) {
        characterConfigRepository.deleteAll(list);
        slaveDbService.deleteAllCharacterConfigSlave(list);
    }

    @Transactional
    public void deleteAllAiLiveConfig(List<AILiveConfig> list) {
        aiLiveConfigRepository.deleteAll(list);
        slaveDbService.deleteAllAiLiveConfigSlave(list);
    }
}
