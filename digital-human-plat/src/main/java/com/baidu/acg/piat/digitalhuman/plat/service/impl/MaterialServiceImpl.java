package com.baidu.acg.piat.digitalhuman.plat.service.impl;

import com.baidu.acg.piat.digitalhuman.common.exception.DigitalHumanCommonException;
import com.baidu.acg.piat.digitalhuman.common.material.Material;
import com.baidu.acg.piat.digitalhuman.common.model.PageResponse;
import com.baidu.acg.piat.digitalhuman.common.model.PageResult;
import com.baidu.acg.piat.digitalhuman.plat.dao.MaterialRepository;
import com.baidu.acg.piat.digitalhuman.plat.model.material.MaterialModel;
import com.baidu.acg.piat.digitalhuman.plat.service.MaterialService;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Created on 2021/8/6 11:40 上午
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class MaterialServiceImpl implements MaterialService {

    private final MaterialRepository repository;

    @Override
    public Material create(Material request) {
        log.debug("Start to create material = {}", request);

        MaterialModel materialModel = MaterialModel.builder()
                .materialId(ObjectId.get().toHexString())  // 根据时间生成16进制的id
                .userId(request.getUserId())
                .name(request.getName())
                .type(request.getType())
                .picUrl(request.getPicUrl())
                .content(request.getContent())
                .drml(request.getDrml())
                .positionId(request.getPositionId())
                .build();
        materialModel = repository.save(materialModel);
        return materialModel.toMaterialRequest();
    }

    @Override
    public void delete(String materialId) {
        log.debug("Start to delete materialId = {}", materialId);

        repository.deleteByMaterialId(materialId);
    }

    @Override
    public Material update(String materialId, Material request) {
        log.debug("Start to update materialId = {}", materialId);

        MaterialModel materialModel = getMaterialModelById(materialId);

        filterEmptyProperties(request, materialModel);

        return repository.save(materialModel).toMaterialRequest();
    }

    private void filterEmptyProperties(Material request, MaterialModel materialModel) {
        if (StringUtils.isNotEmpty(request.getName())) {
            materialModel.setName(request.getName());
        }
        if (StringUtils.isNotEmpty(request.getType())) {
            materialModel.setType(request.getType());
        }
        if (StringUtils.isNotEmpty(request.getPicUrl())) {
            materialModel.setPicUrl(request.getPicUrl());
        }
        if (StringUtils.isNotEmpty(request.getContent())) {
            materialModel.setContent(request.getContent());
        }
        if (StringUtils.isNotEmpty(request.getDrml())) {
            materialModel.setDrml(request.getDrml());
        }
        if (StringUtils.isNotEmpty(request.getPositionId())) {
            materialModel.setPositionId(request.getPositionId());
        }
    }

    private MaterialModel getMaterialModelById(String materialId) {
        var materialById = repository.findByMaterialId(materialId);

        if (materialById.isEmpty()) {
            throw new DigitalHumanCommonException("Material not existed");
        }
        return materialById.get();
    }

    @Override
    public Material detail(String materialId) {
        log.debug("Start to get material detail={}", materialId);
        var materialModelById = getMaterialModelById(materialId);
        return materialModelById.toMaterialRequest();
    }

    @Override
    public PageResponse<Material> listByUserIdAndNameAndTypeAndPositionId(String userId, String name, List<String> type,
                                                    List<String> positionId, int pageNo, int pageSize) {
        log.debug("Start to list material, userId={}, name={}, type={}, pageNo={}, pageSize={}",
                userId, name, type, pageNo, pageSize);

        PageRequest pageRequest = PageRequest.of(pageNo - 1, pageSize,
                Sort.by(Sort.Direction.DESC, "createTime"));
        boolean inType = false;
        boolean inPositionId = false;
        if (!type.contains("-1")) {
            inType = true;
        }
        if (!positionId.contains("-1")) {
            inPositionId = true;
        }
        Page<MaterialModel> materialModelPage = null;
        if (inType && inPositionId) {
            materialModelPage = repository.findAllByUserIdAndNameContainingAndTypeInAndPositionIdIn(
                    userId, name, type, positionId, pageRequest);
        } else if (inType) {
            materialModelPage = repository.findAllByUserIdAndNameContainingAndTypeIn(
                    userId, name, type, pageRequest);
        } else if (inPositionId) {
            materialModelPage = repository.findAllByUserIdAndNameContainingAndPositionIdIn(
                    userId, name, positionId, pageRequest);
        } else {
            materialModelPage = repository.findAllByUserIdAndNameContaining(
                    userId, name, pageRequest);
        }


        List<Material> materialList = Lists.newArrayList();
        materialModelPage.forEach(m -> materialList.add(m.toMaterialRequest()));

        return PageResponse.<Material> builder()
                .page(PageResult.<Material> builder()
                        .pageNo(pageNo)
                        .pageSize(pageSize)
                        .totalCount(materialModelPage.getTotalElements())
                        .result(materialList).build())
                .build();
    }
}
