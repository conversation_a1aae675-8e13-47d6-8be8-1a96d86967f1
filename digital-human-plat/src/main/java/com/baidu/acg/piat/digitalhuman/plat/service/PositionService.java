package com.baidu.acg.piat.digitalhuman.plat.service;

import javax.transaction.Transactional;

import com.baidu.acg.piat.digitalhuman.common.model.PageResponse;
import com.baidu.acg.piat.digitalhuman.common.position.PositionVo;

/**
 * Created on 2021/8/6 11:38 上午
 *
 * <AUTHOR>
 */

public interface PositionService {

    @Transactional
    PositionVo create(PositionVo positionVo);

    @Transactional
    void delete(String positionId);

    @Transactional
    PositionVo update(String positionId, PositionVo position);

    PositionVo detail(String positionId);

    PageResponse<PositionVo> listByUserId(String userId, Integer pageNo, Integer pageSize);

}
