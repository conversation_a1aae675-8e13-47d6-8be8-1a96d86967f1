package com.baidu.acg.piat.digitalhuman.plat.service;

import com.baidu.acg.piat.digitalhuman.common.model.MarkIdWrapper;
import com.baidu.acg.piat.digitalhuman.common.model.PageResponse;
import com.baidu.acg.piat.digitalhuman.common.session.Dialog;

/**
 * The dialog service.
 *
 * <AUTHOR> (<EMAIL>)
 */
public interface DialogService {

    Dialog create(Dialog one);

    PageResponse<Dialog> listBySessionId(String sessionId, int pageNo, int pageSize);

    PageResponse<Dialog> listByRoomNameAndTimestampLessThan(String roomName, String appId, long startTimeMs, String sort, int pageNo, int pageSize);

    PageResponse<Dialog> listByRoomId(String roomId, int pageNo, int pageSize);

    PageResponse<Dialog> listByAppIdAndRoomName(String appId, String roomName, int pageNo, int pageSize);

    MarkIdWrapper<Dialog> listByAppIdAndRoomNameAndMarkId(String appId, String roomName, String markId,
                                                          Long startTimeMs, Integer maxKeys);

    Long countByAppIdAndTimestampBetween(String appId, Long startTime, Long endTime);
}
