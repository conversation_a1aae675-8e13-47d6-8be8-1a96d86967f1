package com.baidu.acg.piat.digitalhuman.plat.service;

import java.util.List;

import com.baidu.acg.piat.digitalhuman.common.material.Material;
import com.baidu.acg.piat.digitalhuman.common.model.PageResponse;

import javax.transaction.Transactional;

/**
 * Created on 2021/8/6 11:38 上午
 *
 * <AUTHOR>
 */

public interface MaterialService {

    @Transactional
    Material create(Material material);

    @Transactional
    void delete(String materialId);

    @Transactional
    Material update(String materialId, Material material);

    Material detail(String materialId);

    PageResponse<Material> listByUserIdAndNameAndTypeAndPositionId(String userId, String name, List<String> type,
                                                                   List<String> positionId, int pageNo, int pageSize);
}
