package com.baidu.acg.piat.digitalhuman.plat.config;

import javax.annotation.PostConstruct;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * Platform grpc configure.
 *
 * <AUTHOR> (<EMAIL>)
 */
@Configuration
@ConfigurationProperties(prefix = "digitalhuman.grpc")
public class PlatformGrpcConfigure {

    @Getter
    @Setter
    private PlatformGrpcServerConfig serverConfig = new PlatformGrpcServerConfig();

    @PostConstruct
    public void init() {
        assert serverConfig != null;
    }

    @Setter
    @Getter
    public static class PlatformGrpcServerConfig {

        private int port = 8085;

        private int terminateAwaitSeconds = 3;
    }
}
