package com.baidu.acg.piat.digitalhuman.plat.config.database;


import com.alibaba.druid.pool.DruidDataSource;
import com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceBuilder;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

import javax.sql.DataSource;
import java.util.HashMap;
import java.util.Map;

@Configuration
public class DataSourceConfig {

    @Bean(name = "masterDataSource")
    @ConfigurationProperties(prefix = "spring.datasource.master")
    public DruidDataSource masternDruidProperties() {
        return DruidDataSourceBuilder.create().build();
    }

    @Bean(name = "slaveDataSource")
    @ConfigurationProperties(prefix = "spring.datasource.slave")
    public DruidDataSource slaveDruidProperties() {
        return DruidDataSourceBuilder.create().build();
    }

    @Bean
    @Primary
    public RoutingDataSource dynamicDataSource(@Qualifier(value = "masterDataSource") DataSource masterDataSource,
                                               @Qualifier(value = "slaveDataSource") DataSource slaveDataSource) {
        Map<Object, Object> targetDataSources = new HashMap<>(2);
        targetDataSources.put(DataSourceNames.master, masterDataSource);
        targetDataSources.put(DataSourceNames.slave, slaveDataSource);
        RoutingDataSource routingDataSource = new RoutingDataSource(masterDataSource, targetDataSources);
        routingDataSource.afterPropertiesSet();
        return routingDataSource;
    }
}
