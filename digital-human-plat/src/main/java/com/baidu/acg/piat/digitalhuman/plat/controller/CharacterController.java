package com.baidu.acg.piat.digitalhuman.plat.controller;

import com.baidu.acg.piat.digitalhuman.common.background.SpecBackgroundRequest;
import com.baidu.acg.piat.digitalhuman.common.entity.CharacterModel;
import com.baidu.acg.piat.digitalhuman.common.model.PageResponse;
import com.baidu.acg.piat.digitalhuman.common.model.Response;
import com.baidu.acg.piat.digitalhuman.plat.service.CharacterService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.imageio.ImageIO;
import java.io.ByteArrayOutputStream;
import java.io.IOException;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/digitalhuman/v1/character")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class CharacterController {

    private final CharacterService service;

    private static final int API_VERSION = 1;

    @PostMapping
    public Response<CharacterModel> create(@RequestBody CharacterModel request) {
        return Response.success(service.create(request));
    }

    @GetMapping(value = "{id}")
    public Response<CharacterModel> findById(@PathVariable(value = "id") String characterId) {
        return Response.success(service.selectById(characterId));
    }

    @GetMapping(value = "type/{type}")
    public Response<CharacterModel> findByType(@PathVariable(value = "type") String characterMetaType,
                                               @RequestParam(required = false) Integer apiVersion) {
        if (apiVersion == null || apiVersion == 0) {
            return Response.success(service.selectByType(characterMetaType).get(0));
        }
        return Response.success(service.selectByType(characterMetaType, apiVersion));
    }

    @GetMapping
    public PageResponse<CharacterModel> selectAll(@RequestParam(required = false, defaultValue = "1") int pageNo,
                                                 @RequestParam(required = false, defaultValue = "10") int pageSize) {
        return service.selectAll(pageNo, pageSize);
    }

    @GetMapping("sce")
    public PageResponse<CharacterModel> findCharactersVisibleForSce(
            @RequestParam(required = false, defaultValue = "1") int pageNo,
            @RequestParam(required = false, defaultValue = "1000") int pageSize,
            @RequestParam(required = false, defaultValue = "true") boolean visibleForSce) {
        return service.selectVisibleForSce(visibleForSce, pageNo, pageSize);
    }

    @PutMapping(value = "type/{type}")
    public Response<CharacterModel> updateByType(@PathVariable(value = "type") String characterType,
                                                @RequestBody CharacterModel request) {
        return Response.success(service.updateByType(characterType, request, API_VERSION));
    }

    @PutMapping(value = "{id}")
    public Response<CharacterModel> updateById(@PathVariable(value = "id") String id,
                                              @RequestBody CharacterModel request) {
        return Response.success(service.updateById(id, request));
    }

    @DeleteMapping(value = "type/{type}")
    public Response<Void> deleteByType(@PathVariable(value = "type") String type) {
        service.deleteByType(type);
        return Response.success(null);
    }

    @DeleteMapping(value = "{id}")
    public Response<Void> deleteById(@PathVariable(value = "id") String id) {
        service.deleteById(id);
        return Response.success(null);
    }

    @ResponseBody
    @PutMapping(value = "/{type}/preview", produces = MediaType.IMAGE_JPEG_VALUE)
    public byte[] preview(
            @PathVariable(value = "type") String type,
            @RequestBody SpecBackgroundRequest specBackgroundRequest
    ) throws IOException {
        ByteArrayOutputStream result = new ByteArrayOutputStream();

        var image = service.preview(type, specBackgroundRequest.getBackgroundId(), API_VERSION);

        ImageIO.write(image, "jpg", result);
        return result.toByteArray();
    }

    @GetMapping("/figurename")
    public Response<CharacterModel> findCharacterType(@RequestParam String figureName) {
        return Response.success(service.selectByFigureName(figureName));
    }
}
