// Copyright (C) 2019 Baidu Inc. All rights reserved.
package com.baidu.acg.piat.digitalhuman.plat.controller;

import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.RequestHeader;

import java.util.List;

import com.baidu.acg.piat.digitalhuman.common.access.AccessApp;
import com.baidu.acg.piat.digitalhuman.common.access.BatchIdRequest;
import com.baidu.acg.piat.digitalhuman.common.model.PageResponse;
import com.baidu.acg.piat.digitalhuman.common.model.Response;
import com.baidu.acg.piat.digitalhuman.plat.service.AppService;

/**
 * AppController
 *
 * <AUTHOR> Zhensheng(<EMAIL>)
 * @since 2019-09-18
 */
@RestController
@RequestMapping("/api/digitalhuman/v1/access")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class AppController {

    private final AppService appService;

    /**
     * 创建时需要带有App request userId， 其他时候不需要，我们认为访问到这层接口的都是有权限的。
     *
     * @param appRequest
     * @return
     */
    @PostMapping("/app")
    public Response<AccessApp> create(@RequestBody AccessApp appRequest) {
        return Response.success(appService.create(appRequest, true));
    }

    @DeleteMapping("/app/{appId}")
    public Response<Void> delete(@RequestHeader(value = "uid", required = false) String uid
            , @RequestHeader(value = "apiVersion", required = false, defaultValue = "1") int apiVersion
            , @PathVariable(value = "appId") String appId) {
        appService.delete(appId, uid, apiVersion);
        return Response.success(null);
    }

    @PostMapping("/app/disable/{appId}")
    public Response<Void> disable(@PathVariable(value = "appId") String appId) {
        appService.disable(appId);
        return Response.success(null);
    }

    @PutMapping("/app/{appId}")
    public Response<AccessApp> update(@PathVariable(value = "appId") String appId, @RequestBody AccessApp appRequest) {
        return Response.success(appService.update(appId, appRequest));
    }

    @GetMapping("/app/{appId}")
    public Response<AccessApp> get(@PathVariable(value = "appId") String appId) {
        return Response.success(appService.get(appId));
    }

    @GetMapping("/app")
    public PageResponse<AccessApp> list(@RequestParam String userId,
                                        @RequestParam(required = false, defaultValue = "") String name,
                                        @RequestParam(required = false) String characterTypes,
                                        @RequestParam(required = false) String projectIds,
                                        @RequestParam(defaultValue = "1") int apiVersion,
                                        @RequestParam(required = false, defaultValue = "1") int pageNo,
                                        @RequestParam(required = false, defaultValue = "10") int pageSize) {
        PageRequest pageRequest = PageRequest.of(pageNo - 1, pageSize);
        return appService.listAll(userId, name, characterTypes, projectIds, apiVersion, pageRequest);
    }

    @PostMapping("/app/list")
    public Response<List<AccessApp>> listApp(@RequestBody BatchIdRequest request) {
        return Response.success(appService.list(request.getIds()));
    }

    @GetMapping("/app/default/{characterImage}")
    public Response<AccessApp> getDefaultAppByImage(@PathVariable(value = "characterImage") String characterImage,
                                                    @RequestParam(defaultValue = "1") int apiVersion) {
        return Response.success(appService.findDefaultByCharacterImage(characterImage, apiVersion));
    }
}
