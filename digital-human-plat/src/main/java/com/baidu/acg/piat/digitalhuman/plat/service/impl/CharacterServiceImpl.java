package com.baidu.acg.piat.digitalhuman.plat.service.impl;

import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.net.URL;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;
import java.util.concurrent.atomic.AtomicBoolean;

import javax.imageio.ImageIO;
import javax.transaction.Transactional;

import com.baidu.acg.piat.digitalhuman.common.character.BaseCharacterInfo;
import com.baidu.acg.piat.digitalhuman.common.character.BehaviorPattern;
import com.baidu.acg.piat.digitalhuman.common.utils.JsonUtil;
import com.baidu.acg.piat.digitalhuman.multilingual.MultilingualClient;
import com.baidu.acg.piat.digitalhuman.multilingual.model.MultilingualResult;
import com.baidu.acg.piat.digitalhuman.plat.helper.IntelligentAnimojiEmotionUtil;
import com.baidu.acg.piat.digitalhuman.plat.v2.service.ServiceException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Triple;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.dao.EmptyResultDataAccessException;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;

import com.baidu.acg.piat.digitalhuman.common.access.AccessApp;
import com.baidu.acg.piat.digitalhuman.common.character.CharacterMixer;
import com.baidu.acg.piat.digitalhuman.common.entity.CharacterModel;
import com.baidu.acg.piat.digitalhuman.common.exception.DigitalHumanCommonException;
import com.baidu.acg.piat.digitalhuman.common.model.PageResponse;
import com.baidu.acg.piat.digitalhuman.common.quota.ResourceQuota;
import com.baidu.acg.piat.digitalhuman.common.utils.IDGenerator;
import com.baidu.acg.piat.digitalhuman.plat.config.CharacterConfigure;
import com.baidu.acg.piat.digitalhuman.plat.dao.BackgroundRepository;
import com.baidu.acg.piat.digitalhuman.plat.dao.CharacterRepository;
import com.baidu.acg.piat.digitalhuman.plat.service.AppService;
import com.baidu.acg.piat.digitalhuman.plat.service.CharacterService;
import com.baidu.acg.piat.digitalhuman.storage.service.StorageService;

import io.vavr.control.Try;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class CharacterServiceImpl implements CharacterService {

    private static final int API_VERSION = 1;

    private final CharacterConfigure.Config characterConfig;

    private static final String CHARACTER_IMAGE_FILE_PREFIX = "character/";

    private final CharacterRepository characterRepository;

    private final BackgroundRepository backgroundRepository;

    private final StorageService storageService;

    private final IDGenerator idGenerator;

    private final MultilingualClient multilingualClient;

    @Lazy
    @Autowired
    private AppService appService;

    private static final String IMAGE_FILE_FORMAT_NAME = "jpg";

    private static final String SYSTEM = "System";

    private String materialLanguage = "emotion:";
    private String materialLanguageSubTag = "digital_human_plat_saas.character_meta.emotion.name";
    private String materialLanguageSvrName = "digital-human-plat";

    @Override
    public CharacterModel create(CharacterModel request) {
        return create(request, API_VERSION);
    }

    @Override
    public CharacterModel create(CharacterModel request, int apiVersion) {
        if (StringUtils.isEmpty(request.getType())) {
            throw new DigitalHumanCommonException("Character type should not be null.");
        }

        var optional = characterRepository.findByTypeAndApiVersion(request.getType(), API_VERSION);
        if (optional.isPresent()) {
            throw new DigitalHumanCommonException(String.format("Character meta type:%s has existed",
                    request.getType()));
        }
        processAnimojiEmotionConfig(request);
        if (apiVersion == 2 && StringUtils.isNotBlank(request.getConfigSchema())) {
            // triple结构tts_default,schema,tts_list
            Triple<String, String, List<BaseCharacterInfo>> triple = extractSchema(request.getConfigSchema());
            String defaultTts = triple.getLeft();
            request.setConfigSchema(triple.getMiddle());
            request.setTtsList(triple.getRight());
            // 初始化默认音色
            for (int i = 0; i < request.getTtsList().size(); i++) {
                String ttsId = request.getTtsList().get(i).getId();
                if (ttsId.equals(defaultTts)) {
                    Collections.swap(request.getTtsList(), 0, i);
                    break;
                }
            }
        }
        String characterId = idGenerator.generate("c");
        request.setCharacterId(characterId);
        if (StringUtils.isNotBlank(request.getBackgroundImageUrl())
                && StringUtils.isNotBlank(request.getMaskImageUrl())
                && StringUtils.isNotBlank(request.getFrontImageUrl())) {
            Try.of(() -> uploadThumbnail(request.getBackgroundImageUrl(), request.getMaskImageUrl(),
                            request.getFrontImageUrl()))
                    .onFailure(t -> log.warn("Fail to upload thumbnail."))
                    .onSuccess(url -> request.setThumbnailImageUrl(url.toString()));
        }

        // 生成appId和appKey供sce使用
        var accessApp = appService.create(AccessApp.builder()
                .userId(SYSTEM)
                .name(request.getName() + "-" + ObjectId.get().toHexString())
                .description("Used by sce, please don't change")
                .characterImage(request.getType())
                .resourceQuota(ResourceQuota.builder()
                        .roomLimits(characterConfig.getRoomLimits())
                        .build())
                .build(), false);
        request.setAppId(accessApp.getAppId());
        request.setAppKey(accessApp.getAppKey());
        request.setApiVersion(apiVersion);
        return characterRepository.save(request);
    }

    /**
     * 只能动作表情映射
     *
     * @param request
     * @throws ServiceException
     */
    private void processAnimojiEmotionConfig(CharacterModel request) {
        try {
            String animojiEmotionConfig = request.getAnimojiEmotionConfig();
            if (StringUtils.isBlank(animojiEmotionConfig)) {
                return;
            }
            BehaviorPattern.IntelligentAnimojiEmotion intelligentAnimojiEmotion =
                    JsonUtil.readValue(animojiEmotionConfig, BehaviorPattern.IntelligentAnimojiEmotion.class);
            // 根据人像动作列表,表情列表过滤智能动作表情映射配置中不存在的动作，表情
            request.setAnimojiEmotionConfig(JsonUtil.writeValueAsString(
                    IntelligentAnimojiEmotionUtil.obtainValidAnimojiAndEmotion(request, intelligentAnimojiEmotion)));
        } catch (Exception e) {
            throw new DigitalHumanCommonException("Character animojiEmotionConfig illegal");
        }
    }

    @Override
    public List<CharacterModel> listAll(boolean visibleForLive, boolean visibleForSce, int apiVersion) {
        PageResponse<CharacterModel> pageResponse;
        if (visibleForLive) {
            pageResponse = selectAll(1, 10000, apiVersion, true);
        } else if (visibleForSce) {
            pageResponse = selectNotVisibleForSce(1, 10000, apiVersion);
        } else {
            pageResponse = selectAll(1, 10000, apiVersion);
        }
        pageResponse.getPage().getResult().forEach(model -> {
            // black appId and appKey
            model.setAppId(null);
            model.setAppKey(null);
        });
        return pageResponse.getPage().getResult();
    }

    @Override
    public PageResponse<CharacterModel> query(int pageNo, int pageSize, int apiVersion, String userVisibleTypes,
                                              boolean visibleForLive, boolean visibleForSce) {
        // 当该主账号有所有人像权限的时候，需要区分visibleForSce
        if (CharacterModel.USER_VISIBLE_ALL_FLAG.equals(userVisibleTypes)) {
            if (visibleForLive) {
                return selectAll(pageNo, pageSize, apiVersion, true);
            }
            if (visibleForSce) {
                return selectVisibleForSce(true, pageNo, pageSize, apiVersion);
            }
            // TODO  这个情况是用于什么地方
            return selectAll(pageNo, pageSize, apiVersion);
        }
        List<String> types = Arrays.asList(userVisibleTypes.split(","));
        var pageRequest = PageRequest.of(pageNo - 1, pageSize);
        PageImpl<CharacterModel> result;
        // 当该主账号仅有部分人像权限的时候，不需要区分visibleForSce
        if (visibleForLive) {
            result = characterRepository
                    .findByApiVersionAndTypeInAndVisibleForLiveOrderByCreateTimeDesc(
                            apiVersion, types, 1, pageRequest);
        } else {
            int allSize = pageNo * pageSize;
            PageResponse<CharacterModel> sceAllmodel = selectVisibleForSce(true, 1, allSize, apiVersion);
            pageRequest = PageRequest.of(0, allSize);
            result = characterRepository.findByApiVersionAndTypeInOrderByCreateTimeDesc(
                    apiVersion, types, pageRequest);
            int totalSize = sceAllmodel.getPage().getResult().size() + result.getContent().size();
            List<CharacterModel> pageList = mergePage(sceAllmodel.getPage().getResult(), result.getContent(), pageNo, pageSize);
            return PageResponse.success(pageNo, pageSize, totalSize, pageList);
        }
        return PageResponse.success(pageNo, pageSize, result.getTotalElements(), result.getContent());
    }

    private List<CharacterModel> mergePage(List<CharacterModel> page1, List<CharacterModel> page2, int pageNo, int pageSize) {
        List<CharacterModel> result = new ArrayList<>();
        if ((pageNo - 1) * pageSize >= page1.size() + page2.size()) {
            return result;
        }
        int i = 0;
        int j = 0;
        int count = 0;
        while (i < page1.size() || j < page2.size()) {
            if (i == page1.size()) {
                result.add(page2.get(j));
                j++;
                continue;
            } else if (j == page2.size()) {
                result.add(page1.get(i));
                i++;
                continue;
            }
            if (page1.get(i).getCreateTime().compareTo(page2.get(j).getCreateTime()) < 0) {
                result.add(page2.get(j));
                j++;
            } else {
                result.add(page1.get(i));
                i++;
            }
        }
        log.info("result size is {}, add size is{}", result.size(), page1.size() + page2.size());
        int end = Math.min(result.size(), pageSize * pageNo);
        return result.subList(pageSize * (pageNo - 1), end);
    }

    @Override
    public CharacterModel selectById(String characterId) {
        if (StringUtils.isBlank(characterId)) {
            throw new DigitalHumanCommonException("Character meta id cannot be empty");
        }
        var modelResult = getById(characterId);
        if (modelResult.isEmpty()) {
            throw new DigitalHumanCommonException("Character meta not exist");
        }
        return modelResult.get();
    }

    /**
     * 不区分API_VERSION，全量查找
     *
     * @param characterMetaType
     * @return
     */
    @Override
    public List<CharacterModel> selectByType(String characterMetaType) {
        if (StringUtils.isBlank(characterMetaType)) {
            throw new DigitalHumanCommonException("Character meta type cannot be empty");
        }
        var result = characterRepository.findByType(characterMetaType);
        if (result.isEmpty()) {
            throw new DigitalHumanCommonException("Character meta not existed.");
        }
        return result;
    }

    @Override
    public List<CharacterModel> selectByTypes(List<String> characterMetaTypes, int apiVersion) {
        return characterRepository.findByTypeInAndApiVersion(characterMetaTypes, apiVersion);
    }

    @Override
    public CharacterModel selectMultiLanguageByType(String characterMetaType, int apiVersion, String language) {
        if (StringUtils.isBlank(characterMetaType)) {
            throw new DigitalHumanCommonException("Character meta type cannot be empty");
        }
        var result = characterRepository.findByTypeAndApiVersion(characterMetaType, apiVersion);
        if (result.isEmpty()) {
            throw new DigitalHumanCommonException("Character meta not existed.");
        }
        CharacterModel characterModel = result.get();
        log.info("selectMultiLanguageByType characterModel:{}", characterModel);
        if (StringUtils.isNotBlank(language) && characterModel.getAnimojiList().size() > 0) {
            List<String> keyList = new ArrayList<>();
            for (BaseCharacterInfo animojiModel : characterModel.getAnimojiList()) {
                if (StringUtils.isNotBlank(animojiModel.getName())) {
                    keyList.add(materialLanguage + animojiModel.getName());
                }
            }
            log.info("selectMultiLanguageByType keyList:{}", keyList);
            MultilingualResult languageName =
                    multilingualClient.getMultilangStr(materialLanguageSvrName, materialLanguageSubTag, keyList, language);
            for (BaseCharacterInfo animojiModel : characterModel.getAnimojiList()) {
                if (StringUtils.isNotBlank(animojiModel.getName())) {
                    String key = materialLanguage + animojiModel.getName();
                    if (languageName.getLanguageMap().containsKey(key) &&
                            !StringUtils.equals(languageName.getLanguageMap().get(key), "Service Unavailable, Text error.")) {
                        animojiModel.setName(languageName.getLanguageMap().get(key));
                        log.info("selectMultiLanguageByType key:{}, value:{}", key, languageName.getLanguageMap().get(key));
                    }
                }
            }
        }
        return characterModel;
    }

    @Override
    public CharacterModel selectByType(String characterMetaType, int apiVersion) {
        if (StringUtils.isBlank(characterMetaType)) {
            throw new DigitalHumanCommonException("Character meta type cannot be empty");
        }
        var result = characterRepository.findByTypeAndApiVersion(characterMetaType, apiVersion);
        if (result.isEmpty()) {
            throw new DigitalHumanCommonException("Character meta not existed.");
        }
        return result.get();
    }

    @Override
    public PageResponse<CharacterModel> selectAll(int pageNo, int pageSize) {
        return selectAll(pageNo, pageSize, API_VERSION);
    }

    @Override
    public PageResponse<CharacterModel> selectAll(int pageNo, int pageSize, int apiVersion) {
        var pageRequest = PageRequest.of(pageNo - 1, pageSize);
        var result = characterRepository.findByApiVersionOrderByCreateTimeDesc(apiVersion, pageRequest);
        return PageResponse.success(pageNo, pageSize, result.getTotalElements(), result.getContent());
    }

    @Override
    public CharacterModel selectByFigureName(String figureName) {
        if (StringUtils.isBlank(figureName)) {
            throw new DigitalHumanCommonException("figure name cannot be empty");
        }
        var result = characterRepository.findByFigureName(figureName);
        if (result.isEmpty()) {
            throw new DigitalHumanCommonException("Character meta not existed, acquire character type failed.");
        }
        return result.get();
    }

    @Override
    public PageResponse<CharacterModel> selectAll(int pageNo, int pageSize, int apiVersion, boolean visibleForLive) {
        var pageRequest = PageRequest.of(pageNo - 1, pageSize);
        var result = characterRepository.findByApiVersionAndVisibleForLiveOrderByCreateTimeDesc(
                apiVersion, visibleForLive ? 1 : 0, pageRequest);
        return PageResponse.success(pageNo, pageSize, result.getTotalElements(), result.getContent());
    }

    @Override
    public PageResponse<CharacterModel> selectVisibleForSce(boolean visibleForSce, int pageNo, int pageSize) {
        return selectVisibleForSce(visibleForSce, pageNo, pageSize, API_VERSION);
    }

    @Override
    public PageResponse<CharacterModel> selectVisibleForSce(boolean visibleForSce, int pageNo, int pageSize,
                                                            int apiVersion) {
        var pageRequest = PageRequest.of(pageNo - 1, pageSize);
        var result = characterRepository.findByApiVersionAndVisibleForSceOrderByCreateTimeDesc(
                apiVersion, visibleForSce ? 1 : 0, pageRequest);
        return PageResponse.success(pageNo, pageSize, result.getTotalElements(), result.getContent());
    }

    @Override
    public PageResponse<CharacterModel> selectNotVisibleForSce(int pageNo, int pageSize,
                                                               int apiVersion) {
        var pageRequest = PageRequest.of(pageNo - 1, pageSize);
        var result = characterRepository.findByApiVersionAndVisibleForSceAndVisibleForLiveOrderByCreateTimeDesc(
                apiVersion, 0, 0, pageRequest);
        return PageResponse.success(pageNo, pageSize, result.getTotalElements(), result.getContent());
    }

    @Override
    public void deleteById(String id) {
        var result = getById(id);
        if (result.isEmpty()) {
            throw new DigitalHumanCommonException("Character meta not existed.");
        }
        characterRepository.deleteByCharacterId(id);
    }

    private Optional<CharacterModel> getById(String id) {
        return characterRepository.findByCharacterId(id);
    }

    @Override
    public void deleteByType(String characterMetaType) {
        if (StringUtils.isBlank(characterMetaType)) {
            throw new DigitalHumanCommonException("Character meta type cannot be empty");
        }
        try {
            characterRepository.deleteByType(characterMetaType);
        } catch (EmptyResultDataAccessException exception) {
            log.warn("character meta type is not exist", exception);
            throw new DigitalHumanCommonException(
                    String.format("character meta type: %s is not exist!", characterMetaType)
            );
        }
    }

    @Override
    public CharacterModel updateById(String id, CharacterModel request) {
        if (StringUtils.isBlank(id)) {
            throw new DigitalHumanCommonException("Character meta id cannot be empty");
        }
        var modelResult = getById(id);
        if (modelResult.isEmpty()) {
            throw new DigitalHumanCommonException("Character meta not exist");
        }
        var characterMetaModel = modelResult.get();
        if (characterMetaModel.getApiVersion() == 2 && StringUtils.isNotBlank(request.getConfigSchema())) {
            Triple<String, String, List<BaseCharacterInfo>> triple = extractSchema(request.getConfigSchema());
            String defaultTts = triple.getLeft();
            request.setConfigSchema(triple.getMiddle());
            request.setTtsList(triple.getRight());
            compareTtsAndSet(characterMetaModel, request, defaultTts);
        }
        extractCharacterFromRequest(characterMetaModel, request);
        return characterRepository.save(characterMetaModel);
    }

    @Override
    public CharacterModel updateByType(String characterImageType, CharacterModel request, int apiVersion) {
        if (StringUtils.isBlank(characterImageType)) {
            throw new DigitalHumanCommonException("Character meta type cannot be empty");
        }
        var characterMetaModel = selectByType(characterImageType, apiVersion);
        if (characterMetaModel.getApiVersion() == 2 && StringUtils.isNotBlank(request.getConfigSchema())) {
            Triple<String, String, List<BaseCharacterInfo>> triple = extractSchema(request.getConfigSchema());
            String defaultTts = triple.getLeft();
            request.setConfigSchema(triple.getMiddle());
            request.setTtsList(triple.getRight());
            compareTtsAndSet(characterMetaModel, request, defaultTts);
        }
        extractCharacterFromRequest(characterMetaModel, request);
        return characterRepository.save(characterMetaModel);
    }

    @Transactional
    @Override
    public Map<String, String> batchUpdateTts(Map<String, String> ttsMap, int apiVersion) {
        Map<String, String> successIds = new HashMap<>();

        for (Map.Entry<String, String> entry : ttsMap.entrySet()) {
            String id = entry.getKey();
            String per = entry.getValue();
            var modelResult = getById(id);
            if (modelResult.isEmpty()) {
                log.error("Character meta not exist for id = {}", id);
                continue;
            }
            var characterMetaModel = modelResult.get();
            if (characterMetaModel.getTtsList() == null) {
                log.error("Character tts list not exist for id = {}", id);
                continue;
            }
            if (characterMetaModel.getApiVersion() != apiVersion) {
                log.error("Character id is {}, and it's api is {} not pair", id, characterMetaModel.getApiVersion());
                continue;
            }
            for (int j = 0; j < characterMetaModel.getTtsList().size(); j++) {
                if (characterMetaModel.getTtsList().get(j).getId().equals(per)) {
                    BaseCharacterInfo remove = characterMetaModel.getTtsList().remove(j);
                    characterMetaModel.getTtsList().add(0, remove);
                    characterRepository.save(characterMetaModel);
                    successIds.put(id, characterMetaModel.getTtsList().get(0).getId());
                    break;
                }
            }
        }
        return successIds;
    }

    private CharacterModel extractCharacterFromRequest(CharacterModel characterMetaModel,
                                                       CharacterModel request) {
        AtomicBoolean needReGenerateThumbnail = new AtomicBoolean(false);
        Optional.ofNullable(request.getBackgroundImageUrl()).ifPresent(r -> {
            needReGenerateThumbnail.set(true);
            characterMetaModel.setBackgroundImageUrl(r);
        });
        Optional.ofNullable(request.getMaskImageUrl()).ifPresent(r -> {
            needReGenerateThumbnail.set(true);
            characterMetaModel.setMaskImageUrl(r);
        });
        Optional.ofNullable(request.getFrontImageUrl()).ifPresent(r -> {
            needReGenerateThumbnail.set(true);
            characterMetaModel.setFrontImageUrl(r);
        });
        if (needReGenerateThumbnail.get()) {
            Try.of(() -> uploadThumbnail(request.getBackgroundImageUrl(), request.getMaskImageUrl(),
                            request.getFrontImageUrl()))
                    .onFailure(t -> log.warn("Fail to upload thumbnail."))
                    .onSuccess(url -> characterMetaModel.setThumbnailImageUrl(url.toString()));
        }
        Optional.of(request.getSupportCallback()).ifPresent(characterMetaModel::setSupportCallback);
        Optional.of(request.getSupportRtcDatachannel()).ifPresent(characterMetaModel::setSupportRtcDatachannel);
        Optional.of(request.getVisibleForSce()).ifPresent(characterMetaModel::setVisibleForSce);
        Optional.of(request.getVisibleForLive()).ifPresent(characterMetaModel::setVisibleForLive);
        Optional.ofNullable(request.getName()).ifPresent(characterMetaModel::setName);
        Optional.ofNullable(request.getDescription()).ifPresent(characterMetaModel::setDescription);
        Optional.ofNullable(request.getStyle()).ifPresent(characterMetaModel::setStyle);
        Optional.ofNullable(request.getLabel()).ifPresent(characterMetaModel::setLabel);
        Optional.ofNullable(request.getAppId()).ifPresent(characterMetaModel::setAppId);
        Optional.ofNullable(request.getAppKey()).ifPresent(characterMetaModel::setAppKey);
        Optional.ofNullable(request.getTtsList()).ifPresent(characterMetaModel::setTtsList);
        Optional.ofNullable(request.getCameraList()).ifPresent(characterMetaModel::setCameraList);
        Optional.ofNullable(request.getHairStyleList()).ifPresent(characterMetaModel::setHairStyleList);
        Optional.ofNullable(request.getClothingStyleList()).ifPresent(characterMetaModel::setClothingStyleList);
        Optional.ofNullable(request.getBadgeStyleList()).ifPresent(characterMetaModel::setBadgeStyleList);
        Optional.ofNullable(request.getShoeStyleList()).ifPresent(characterMetaModel::setShoeStyleList);
        Optional.ofNullable(request.getAnimojiList()).ifPresent(characterMetaModel::setAnimojiList);
        Optional.ofNullable(request.getEmotionList()).ifPresent(characterMetaModel::setEmotionList);
        Optional.ofNullable(request.getFacialList()).ifPresent(characterMetaModel::setFacialList);
        Optional.ofNullable(request.getMakeupList()).ifPresent(characterMetaModel::setMakeupList);
        Optional.ofNullable(request.getSceneList()).ifPresent(characterMetaModel::setSceneList);
        Optional.ofNullable(request.getConfigSchema()).ifPresent(characterMetaModel::setConfigSchema);
        Optional.ofNullable(request.getDeployVersion()).ifPresent(characterMetaModel::setDeployVersion);
        Optional.ofNullable(request.getFigureAlias()).ifPresent(characterMetaModel::setFigureAlias);
        Optional.ofNullable(request.getThumbnailImageUrl()).ifPresent(characterMetaModel::setThumbnailImageUrl);
        Optional.ofNullable(request.getAnimojiEmotionConfig()).ifPresent((animojiEmotionConfig) -> {
            characterMetaModel.setAnimojiEmotionConfig(animojiEmotionConfig);
            processAnimojiEmotionConfig(characterMetaModel);
        });
        return characterMetaModel;
    }

    @Override
    public BufferedImage preview(@NonNull String characterModelType,
                                 @NonNull String backgroundImageId, int apiVersion) {
        var background = backgroundRepository.findByBackgroundId(backgroundImageId);
        if (background.isEmpty()) {
            throw new DigitalHumanCommonException(String.format("Background : %s is not exist",
                    backgroundImageId));
        }

        var characterModel = selectByType(characterModelType, apiVersion);

        var mixer = createCharacterMixer(
                background.get().getUrl(),
                characterModel.getMaskImageUrl(),
                characterModel.getFrontImageUrl()
        );
        return mixer.mix();
    }

    // region private

    private URL uploadThumbnail(String backUrl, String maskUrl, String frontUrl)
            throws StorageService.ResourceException {
        BufferedImage thumbnail = createCharacterMixer(backUrl, maskUrl, frontUrl).mix();
        var fileName = CHARACTER_IMAGE_FILE_PREFIX + UUID.randomUUID().toString() + "." + IMAGE_FILE_FORMAT_NAME;
        ByteArrayOutputStream stream = new ByteArrayOutputStream();
        Try.of(() -> {
            ImageIO.write(thumbnail, IMAGE_FILE_FORMAT_NAME, stream);
            return stream;
        }).onFailure(t -> {
            log.warn("Fail to write image.");
            throw new DigitalHumanCommonException("Fail to upload character image");
        });
        return storageService.save(stream.toByteArray(), fileName);
    }

    private CharacterMixer createCharacterMixer(String backUrl, String maskUrl, String frontUrl) {
        try {
            return CharacterMixer.builder()
                    .backImage(ImageIO.read(new URL(backUrl)))
                    .mask(ImageIO.read(new URL(maskUrl)))
                    .frontImage(ImageIO.read(new URL(frontUrl)))
                    .build();
        } catch (IOException exception) {
            throw new DigitalHumanCommonException("Cannot read image content from url");
        }
    }

    /**
     * 从schema中提取tts的默认值，并返回一个三元组，包含tts的默认值、剔除person属性后的schema和tts的所有枚举值列表。
     *
     * @param schema 包含tts信息的schema字符串
     * @return 三元组，包含tts的默认值、剔除person属性后的schema和tts的所有枚举值列表
     * @throws DigitalHumanCommonException 如果提取tts默认值失败或提取tts枚举值列表失败
     */
    private Triple<String, String, List<BaseCharacterInfo>> extractSchema(String schema) {
        ObjectMapper objectMapper = new ObjectMapper();
        try {
            JsonNode originNode = objectMapper.readTree(schema);
            JsonNode rootNode = objectMapper.readTree(schema).path("properties").path("tts").path("properties").path("person");
            String ttsDefault = rootNode.get("default").asText();
            List<BaseCharacterInfo> ttsList = new ArrayList<>();
            if (rootNode.get("enum").size() != rootNode.get("dhFeInputValueNames").size()) {
                throw new DigitalHumanCommonException("extract tts list failed");
            }
            for (int i = 0; i < rootNode.get("enum").size(); i++) {
                ttsList.add(new BaseCharacterInfo(rootNode.get("enum").get(i).asText()
                        , rootNode.get("dhFeInputValueNames").get(i).asText()));
            }
            ((ObjectNode) originNode.get("properties")).remove("tts");
            String newSchema = objectMapper.writerWithDefaultPrettyPrinter().writeValueAsString(originNode);
            return Triple.of(ttsDefault, newSchema, ttsList);
        } catch (Exception e) {
            throw new DigitalHumanCommonException("Extract tts default failed, please check your schema", e);
        }
    }

    /**
     * 比较并设置tts语音参数，如果原有的tts_list的第一个元素（默认音色）在请求的tts_list存在，则保留原有的位置。
     *
     * @param characterMetaModel 字符元模型
     * @param request            请求对象
     * @param defaultTts         默认TTS语音参数
     */
    private void compareTtsAndSet(CharacterModel characterMetaModel, CharacterModel request, String defaultTts) {
        if (request.getTtsList() == null) {
            return;
        }
        if (characterMetaModel.getTtsList() != null && characterMetaModel.getTtsList().size() > 0) {
            String oldDefaultId = characterMetaModel.getTtsList().get(0).getId();
            int defaultInx = -1;
            for (int i = 0; i < request.getTtsList().size(); i++) {
                String ttsId = request.getTtsList().get(i).getId();
                if (ttsId.equals(oldDefaultId)) {
                    Collections.swap(request.getTtsList(), 0, i);
                    return;
                }
                if (ttsId.equals(defaultTts)) {
                    defaultInx = i;
                }
            }
            if (defaultInx != -1) {
                Collections.swap(request.getTtsList(), 0, defaultInx);
                return;
            }
        } else if (defaultTts != null) {
            for (int i = 0; i < request.getTtsList().size(); i++) {
                String ttsId = request.getTtsList().get(i).getId();
                if (ttsId.equals(defaultTts)) {
                    Collections.swap(request.getTtsList(), 0, i);
                    return;
                }
            }
        }
    }
    // endregion
}
