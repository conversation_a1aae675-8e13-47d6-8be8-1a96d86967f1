// Copyright (C) 2021 Baidu Inc. All rights reserved.

package com.baidu.acg.piat.digitalhuman.plat.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import com.baidu.acg.piat.digitalhuman.common.utils.IDGenerator;

/**
 * AppGeneratorConfigure
 *
 * <AUTHOR>
 * @date 2021-01-18
 */
@Configuration
public class IdGeneratorConfigure {

    @Bean
    @ConfigurationProperties(prefix = "id-generator")
    public IDGenerator.IDGeneratorConfig idGeneratorConfig() {
        return new IDGenerator.IDGeneratorConfig();
    }

    @Bean
    public IDGenerator idGenerator() {
        return new IDGenerator(idGeneratorConfig());
    }

}
