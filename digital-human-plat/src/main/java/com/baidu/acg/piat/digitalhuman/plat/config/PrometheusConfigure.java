package com.baidu.acg.piat.digitalhuman.plat.config;

import com.baidu.acg.piat.digitalhuman.plat.service.PrometheusClient;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class PrometheusConfigure {

    @Bean
    @ConfigurationProperties(prefix = "digitalhuman.prometheus.config")
    public PrometheusConfig config() {
        return new PrometheusConfig();
    }

    @Bean
    public PrometheusClient prometheusClient() {
        var config = config();
        return new PrometheusClient(config.getUrl());
    }

    @Data
    public static class PrometheusConfig {
        private String url;
    }
}
