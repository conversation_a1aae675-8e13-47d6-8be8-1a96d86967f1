package com.baidu.acg.piat.digitalhuman.plat.service.impl;

import com.baidu.acg.piat.digitalhuman.common.callback.CallbackResponse;
import com.baidu.acg.piat.digitalhuman.common.callback.CallbackTaskCreateRequest;
import com.baidu.acg.piat.digitalhuman.common.callback.CallbackTaskCreateResponse;
import com.baidu.acg.piat.digitalhuman.common.utils.IDGenerator;
import com.baidu.acg.piat.digitalhuman.common.utils.JsonUtil;
import com.baidu.acg.piat.digitalhuman.plat.service.CallbackManager;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.google.common.collect.Maps;
import io.vavr.control.Try;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Call;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;
import okhttp3.ResponseBody;
import org.redisson.api.RBlockingDeque;
import org.redisson.api.RDelayedQueue;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.Map;
import java.util.concurrent.TimeUnit;


@Slf4j
@Component
public class CallbackManagerImpl implements CallbackManager, ApplicationRunner {

    @Autowired
    private RedissonClient redissonClient;

    private OkHttpClient httpClient;
    @Autowired
    private IDGenerator idGenerator;

    private Map<Integer, Integer> delayedTimeConfieMap= Maps.newHashMap();

    private RBlockingDeque<String> blockingDeque;
    private RDelayedQueue<String> delayedQueue;

    @PostConstruct
    public void init() {
        // 线上有超时的情况，从log来看，就是超时了，拉大超时时长。
        httpClient = new OkHttpClient.Builder()
                .connectTimeout(30, TimeUnit.SECONDS)
                .callTimeout(120, TimeUnit.SECONDS).build();
        // 重试延迟时机 15s/30s/1min/5min
        delayedTimeConfieMap.put(1, 15);
        delayedTimeConfieMap.put(2, 15);
        delayedTimeConfieMap.put(3, 30);
        delayedTimeConfieMap.put(4, 240);
        blockingDeque = redissonClient.getBlockingDeque("dh_callback_delayed_queue");
        delayedQueue = redissonClient.getDelayedQueue(blockingDeque);
    }

    @Override
    public void run(ApplicationArguments args) {
        new Thread(new Runnable() {
            private String msg;

            @Override
            public void run() {
                while (true) {
                    Try.run(() -> {
                        msg = null;
                        msg = blockingDeque.take();
                        log.debug("Take a msg from callbackDelayedQueue,msg:{}", msg);
                        CallbackDelayedQueueMsg callbackDelayedQueueMsg = JsonUtil.readValue(msg, CallbackDelayedQueueMsg.class);
                        processCallbackDelayMsg(callbackDelayedQueueMsg);
                    }).onFailure(throwable -> {
                        log.error("CallbackDelayedQueue consume msg error,msg:{}", msg, throwable);
                    });
                }
            }
        }).start();

    }

    private void processCallbackDelayMsg(CallbackDelayedQueueMsg msg) {
        CallbackResponse callbackResponse = doCallback(msg.getData());
        if (CallbackResponse.Status.SUCCEED.equals(callbackResponse.getStatus())) {
            return;
        }
        if (CallbackResponse.Status.ERROR.equals(callbackResponse.getStatus())
                && msg.getRetryNum() >= 4) {
            log.error("Final failed to do callback request,delayedMsg:{}"
                    , JsonUtil.writeValueAsStringQuietly(msg));
            return;
        }
        msg.setOfferMsgTimestamp(System.currentTimeMillis());
        msg.setMsgId(idGenerator.generate("msg"));
        msg.setRetryNum(msg.getRetryNum() + 1);
        log.debug("Offer a msg to callback delayed queue,msg:{}"
                , JsonUtil.writeValueAsStringQuietly(msg));
        delayedQueue.offer(JsonUtil.writeValueAsStringQuietly(msg)
                , delayedTimeConfieMap.get(msg.getRetryNum()), TimeUnit.SECONDS);
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    private static class CallbackDelayedQueueMsg {
        private CallbackTaskCreateRequest data;
        private int retryNum;
        private long offerMsgTimestamp;
        private String msgId;
    }

    @Override
    public CallbackTaskCreateResponse submit(CallbackTaskCreateRequest request) {
        CallbackResponse callbackResponse = doCallback(request);
        if (callbackResponse.getStatus() == CallbackResponse.Status.ERROR) {
            CallbackDelayedQueueMsg msg = CallbackDelayedQueueMsg.builder()
                    .offerMsgTimestamp(System.currentTimeMillis())
                    .msgId(idGenerator.generate("msg"))
                    .retryNum(1)
                    .data(request)
                    .build();
            log.debug("Offer a msg to callback delayed queue,msg:{}"
                    , JsonUtil.writeValueAsStringQuietly(msg));
            delayedQueue.offer(JsonUtil.writeValueAsStringQuietly(msg)
                    , delayedTimeConfieMap.get(1), TimeUnit.SECONDS);
        }
        return new CallbackTaskCreateResponse();
    }

    private CallbackResponse doCallback(CallbackTaskCreateRequest request) {
        return Try.of(() -> {
            long start = System.currentTimeMillis();
            Request okhttpRequest = new Request.Builder()
                    .url(request.getCallbackUrl())
                    .post(RequestBody.create(MediaType.parse("application/json; charset=utf-8"),
                            JsonUtil.writeValueAsString(request)))
                    .build();
            Call call = httpClient.newCall(okhttpRequest);
            try (Response response = call.execute()) {
                if (!response.isSuccessful()) {
                    log.error("Failed to do callback request, reqeust:{},httpCode:{}", JsonUtil.writeValueAsString(request)
                            , response.code());
                    return CallbackResponse.builder().status(CallbackResponse.Status.ERROR).build();
                }
                ResponseBody body = response.body();
                CallbackResponse r = JsonUtil.readValue(body.bytes(), CallbackResponse.class);
                log.debug("Finished to do callback request, reqeust:{},callbackResponse:{},cost:{}", JsonUtil.writeValueAsString(request)
                        , JsonUtil.writeValueAsString(r), System.currentTimeMillis() - start);
                if (r == null || !CallbackResponse.Status.SUCCEED.equals(r.getStatus())) {
                    log.error("Failed to do callback request, reqeust:{},callbackResponse:{}", JsonUtil.writeValueAsString(request)
                            , JsonUtil.writeValueAsString(r));
                    return CallbackResponse.builder().status(CallbackResponse.Status.ERROR).build();
                }
                return CallbackResponse.builder().status(CallbackResponse.Status.SUCCEED).build();
            } catch (Throwable t) {
                log.error("Failed to do callback request, reqeust:{}", JsonUtil.writeValueAsString(request), t);
            }
            return CallbackResponse.builder().status(CallbackResponse.Status.ERROR).build();
        }).getOrElseGet(throwable -> {
            log.error("Failed to do callback request occurred exception.request:{}"
                    , JsonUtil.writeValueAsStringQuietly(request), throwable);
            return CallbackResponse.builder().status(CallbackResponse.Status.ERROR).build();
        });
    }
}
