package com.baidu.acg.piat.digitalhuman.plat.client;

import com.baidu.acg.piat.digitalhuman.common.entity.CharacterConfigRelatedModule;
import com.baidu.acg.piat.digitalhuman.common.entity.CharacterConfigRequest;
import com.baidu.acg.piat.digitalhuman.common.mhe.MheProject;
import com.baidu.acg.piat.digitalhuman.common.mhe.MheVideo;
import com.baidu.acg.piat.digitalhuman.common.model.PageResult;
import com.baidu.acg.piat.digitalhuman.common.model.Response;
import retrofit2.Call;
import retrofit2.http.GET;
import retrofit2.http.POST;
import retrofit2.http.Body;
import retrofit2.http.Query;

import java.util.List;

public interface MheStudioService {

    @POST("/api/internal/digitalhuman/mhe/v1/studio/projects/by-config")
    Call<Response<List<CharacterConfigRelatedModule>>> listByCharacterConfigIds(@Body CharacterConfigRequest request);


    @GET("/api/internal/digitalhuman/mhe/v1/studio/projects/listAllProjects")
    Call<Response<List<MheProject>>> listAllProjects(@Query("accountId") String accountId);


    @GET("/api/internal/digitalhuman/mhe/v1/studio/videos/listAllByPage")
    Call<Response<PageResult<MheVideo>>> listAllVidesByPage(@Query("pageNo") int pageNo,
                                                            @Query("pageSize") int pageSize,
                                                            @Query("accountId") String accountId);


}
