package com.baidu.acg.piat.digitalhuman.plat.v2.controller;

import com.baidu.acg.piat.digitalhuman.common.intelligentanimojiemotion.IntelligentAnimojiEmotionRequest;
import com.baidu.acg.piat.digitalhuman.common.intelligentanimojiemotion.IntelligentAnimojiEmotionResponse;
import com.baidu.acg.piat.digitalhuman.common.model.Response;
import com.baidu.acg.piat.digitalhuman.common.utils.JsonUtil;
import com.baidu.acg.piat.digitalhuman.plat.service.IntelligentAnimojiEmotionService;
import com.fasterxml.jackson.core.JsonProcessingException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping(value = {"/api/digitalhuman/intelligent"})
public class IntelligentAnimojiEmotionController {

    @Autowired
    private IntelligentAnimojiEmotionService intelligentAnimojiEmotionService;

    @PostMapping("/text2drml")
    public Response<IntelligentAnimojiEmotionResponse> text2Drml(@RequestBody @Validated IntelligentAnimojiEmotionRequest request)
            throws JsonProcessingException {
        IntelligentAnimojiEmotionResponse intelligentAnimojiEmotionResponse = intelligentAnimojiEmotionService.text2Drml(request);
        log.info("Text2drml request :{},res: {}"
                , JsonUtil.writeValueAsString(request), JsonUtil.writeValueAsString(intelligentAnimojiEmotionResponse));
        return Response.success(intelligentAnimojiEmotionResponse);
    }
}
