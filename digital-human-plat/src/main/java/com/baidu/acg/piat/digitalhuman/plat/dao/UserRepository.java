// Copyright (C) 2019 Baidu Inc. All rights reserved.
package com.baidu.acg.piat.digitalhuman.plat.dao;

import org.springframework.data.repository.CrudRepository;

import java.util.Optional;

import javax.transaction.Transactional;

import com.baidu.acg.piat.digitalhuman.plat.model.user.AccessUserModel;

/**
 * UserRepository
 *
 * <AUTHOR>
 * @since 2019-09-19
 */
public interface UserRepository extends CrudRepository<AccessUserModel, Long> {

    Optional<AccessUserModel> findByUserId(String userId);

    Optional<AccessUserModel> findByName(String name);

    Optional<AccessUserModel> findByIamUserId(String iamUserId);

    @Transactional
    void deleteByUserId(String userId);

}