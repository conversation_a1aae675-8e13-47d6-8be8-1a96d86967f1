package com.baidu.acg.piat.digitalhuman.plat.controller;

import com.google.protobuf.InvalidProtocolBufferException;
import com.google.protobuf.util.JsonFormat;
import io.grpc.Channel;
import io.grpc.ManagedChannelBuilder;
import io.grpc.stub.StreamObserver;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Profile;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.LinkedBlockingDeque;

import javax.annotation.PostConstruct;

import com.baidu.acg.piat.digitalhuman.platform.model.EventResponse;
import com.baidu.acg.piat.digitalhuman.platform.model.RoomSessionEvent;
import com.baidu.acg.piat.digitalhuman.platform.service.DigitalHumanPlatformServiceGrpc;
import com.baidu.acg.piat.digitalhuman.platform.service.DigitalHumanPlatformServiceGrpc.DigitalHumanPlatformServiceStub;

@Slf4j
@Profile("dev")
@RestController
@RequestMapping("/api/digitalhuman/v1/plat/mock")
public class MockController {

    @Value("${digitalhuman.grpc.serverConfig.port:8085}")
    private int port;

    private DigitalHumanPlatformServiceStub stub;

    private Map<String, StreamObserver<RoomSessionEvent>> requestStreams = new ConcurrentHashMap<>();

    private Map<String, BlockingQueue<EventResponse>> responseQueues = new ConcurrentHashMap<>();

    @PostConstruct
    public void init() {
        Channel channel = ManagedChannelBuilder.forAddress("localhost", port).usePlaintext().build();
        stub = DigitalHumanPlatformServiceGrpc.newStub(channel);
    }

    @PostMapping("/grpc/sessions/{id}")
    public void openSession(@PathVariable String id) {
        BlockingQueue<EventResponse> responseQueue = new LinkedBlockingDeque<>();
        StreamObserver<RoomSessionEvent> requestStream = stub.onEvent(new StreamObserver<EventResponse>() {
            @Override
            public void onNext(EventResponse value) {
                responseQueue.add(value);
            }

            @Override
            public void onError(Throwable t) {
                log.error("Fail to maintain plat grpc call", t);
                close();
            }

            @Override
            public void onCompleted() {
                log.error("Completed plat grpc call");
                close();
            }

            private void close() {
                responseQueues.remove(id);
                requestStreams.remove(id);
            }
        });
        requestStreams.put(id, requestStream);
        responseQueues.put(id, responseQueue);
    }

    @PostMapping("/grpc/sessions/{id}/chats")
    public void chat(@PathVariable String id, @RequestBody String json) throws InvalidProtocolBufferException {
        StreamObserver<RoomSessionEvent> requestStream = requestStreams.get(id);
        if (requestStream == null) {
            throw new NullPointerException();
        }
        requestStream.onNext(extractEvent(json));
    }

    private RoomSessionEvent extractEvent(String json) throws InvalidProtocolBufferException {
        RoomSessionEvent.Builder builder = RoomSessionEvent.newBuilder();
        JsonFormat.parser().merge(json, builder);
        return builder.build();
    }
}
