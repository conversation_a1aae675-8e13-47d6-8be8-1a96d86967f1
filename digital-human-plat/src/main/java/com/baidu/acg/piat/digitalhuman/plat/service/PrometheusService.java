package com.baidu.acg.piat.digitalhuman.plat.service;

import com.baidu.acg.piat.digitalhuman.common.statistic.QueryResponse;
import retrofit2.Call;
import retrofit2.http.POST;
import retrofit2.http.Query;

public interface PrometheusService {

    @POST("/api/v1/query")
    Call<QueryResponse> query(@Query("query") String query, @Query("time") double time);

    @POST("/api/v1/query_range")
    Call<QueryResponse> queryRange(@Query("query") String query, @Query("start") double start,
                                      @Query("end") double end, @Query("step") long step);

}
