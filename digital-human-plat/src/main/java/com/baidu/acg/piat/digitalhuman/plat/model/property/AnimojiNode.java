package com.baidu.acg.piat.digitalhuman.plat.model.property;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class AnimojiNode {

    private String name;

    private String type;

    private List<Animation> animationList;

    @Data
    @NoArgsConstructor
    public static class Animation {

        String animationName;

        int startFrame;

        int endFrame;

    }
}
