package com.baidu.acg.piat.digitalhuman.plat.model.property.impl;

import com.baidu.acg.piat.digitalhuman.common.exception.DigitalHumanCommonException;
import com.baidu.acg.piat.digitalhuman.common.utils.JsonUtil;
import com.baidu.acg.piat.digitalhuman.plat.model.property.FileVerification;
import com.baidu.acg.piat.digitalhuman.plat.model.property.PropertyFileResponse;
import com.baidu.acg.piat.digitalhuman.plat.model.property.PropertyType;
import com.baidu.acg.piat.digitalhuman.plat.model.property.SkeletonFile;
import com.baidu.acg.piat.digitalhuman.plat.model.property.SkeletonNodeName;
import io.vavr.Tuple2;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;

@Slf4j
@Component
@RequiredArgsConstructor
public class ParameterizedFileVerification implements FileVerification {
    @Override
    public boolean support(PropertyType type) {
        return type.equals(PropertyType.FACIAL_BROW)
                || type.equals(PropertyType.FACIAL_EYE)
                || type.equals(PropertyType.FACIAL_FACE)
                || type.equals(PropertyType.FACIAL_NOSE)
                || type.equals(PropertyType.FACIAL_MOUTH);
    }

    /**
     * 五官类型需要上传json文件，并对其内容进行校验
     * @param file
     * @return
     * @throws IOException
     */
    @Override
    public Tuple2<PropertyFileResponse, String> verify(MultipartFile file, String type) throws IOException {

        if (StringUtils.isEmpty(type)) {
            log.warn("type of property is null");
            throw new DigitalHumanCommonException("类型不能为空");
        }

        String filename = file.getOriginalFilename();
        if (!filename.endsWith(".json")) {
            throw new DigitalHumanCommonException("五官类资产需要上传包括骨骼信息的json文件");
        }

        SkeletonFile facialData = null;
        try {
            facialData = JsonUtil.readValue(file.getBytes(), SkeletonFile.class);
        } catch (Exception e) {
            throw new DigitalHumanCommonException("json文件格式错误");
        }

        String propertyId = facialData.getName();

        List<String> nodeNames = facialData.getNodeName();
        if (nodeNames == null) {
            throw new DigitalHumanCommonException("json文件中缺少nodeName字段");
        }
        if (nodeNames.size() < 1) {
            throw new DigitalHumanCommonException("json文件中的nodeName为空");
        }
        if (facialData.getNodeDatas() == null) {
            throw new DigitalHumanCommonException("json文件中缺少nodeDatas字段");
        }
        SkeletonNodeName skeletonNodeName = SkeletonNodeName.valueOf(type.toUpperCase());

        // 符合ar提供的骨骼命名
        nodeNames.stream().forEach(nodeName -> {
            if (!skeletonNodeName.contain(nodeName)) {
                log.warn("The nodeName is illegal.");
                throw new DigitalHumanCommonException("nodeName不符合命名规范");
            }
        });

        // nodeName和实际提供的nodeData数量一致
        if (nodeNames.size() != facialData.getNodeDatas().size()) {
            log.warn("The size of nodeName and nodeData is different");
            throw new DigitalHumanCommonException("nodeName与nodeData的元素数量不同，无法进行骨骼匹配");
        }

        facialData.getNodeDatas().stream().forEach(nodeData -> {
            if (nodeData.size() != 3) {
                log.warn("The structure of nodeData is wrong");
                throw new DigitalHumanCommonException("nodeData的数据结构有误");
            }
        });

        return new Tuple2<>(PropertyFileResponse.builder()
                .propertyId(propertyId)
                .frameCount(0)
                .propertyJson(JsonUtil.writeValueAsString(facialData)).build(), null);
    }

}
