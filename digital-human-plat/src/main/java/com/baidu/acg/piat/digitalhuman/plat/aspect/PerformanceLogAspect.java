package com.baidu.acg.piat.digitalhuman.plat.aspect;

import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;

/**
 * 记录log的切面
 *
 * <AUTHOR>
 */
@Slf4j
@Aspect
@Component
public class PerformanceLogAspect {
    /**
     * 记录性能
     *
     * @param joinPoint
     * @return
     * @throws Throwable
     */
    @Around(value =
            "@annotation(com.baidu.acg.piat.digitalhuman.plat.annotation.RecordPerformance)"
    )
    public Object recordPerformance(ProceedingJoinPoint joinPoint) throws Throwable {
        long startTime = System.currentTimeMillis();
        var result = joinPoint.proceed();
        long costTime = System.currentTimeMillis() - startTime;

        var targetMethod = ((MethodSignature) joinPoint.getSignature()).getMethod();

        log.info("{}.{} process cost {}ms", targetMethod.getDeclaringClass(), targetMethod.getName(), costTime);
        return result;
    }
}
