package com.baidu.acg.piat.digitalhuman.plat.service.impl;

import com.baidu.acg.piat.digitalhuman.common.common.AddType;
import com.baidu.acg.piat.digitalhuman.common.helper.OptLogHelper;
import com.baidu.acg.piat.digitalhuman.common.llmrole.LLMConfig;
import com.baidu.acg.piat.digitalhuman.common.llmrole.LlmRole;
import com.baidu.acg.piat.digitalhuman.common.model.Response;
import com.baidu.acg.piat.digitalhuman.common.optlog.OptContent;
import com.baidu.acg.piat.digitalhuman.common.optlog.OptLog;
import com.baidu.acg.piat.digitalhuman.common.project.BotParams;
import com.baidu.acg.piat.digitalhuman.common.project.Camera;
import com.baidu.acg.piat.digitalhuman.common.project.CharacterParams;
import com.baidu.acg.piat.digitalhuman.common.project.ConversationConfig;
import com.baidu.acg.piat.digitalhuman.common.project.Credentials;
import com.baidu.acg.piat.digitalhuman.common.project.FigureCutParams;
import com.baidu.acg.piat.digitalhuman.common.project.HotWordReplaceReg;
import com.baidu.acg.piat.digitalhuman.common.project.MediaOutput;
import com.baidu.acg.piat.digitalhuman.common.project.PaintChartOnPictureParams;
import com.baidu.acg.piat.digitalhuman.common.project.PaintSubtitleOnPictureParams;
import com.baidu.acg.piat.digitalhuman.common.project.Project;
import com.baidu.acg.piat.digitalhuman.common.project.PrologueParams;
import com.baidu.acg.piat.digitalhuman.common.project.ResolutionParams;
import com.baidu.acg.piat.digitalhuman.common.project.TtsParams;
import com.baidu.acg.piat.digitalhuman.common.project.UserInactiveConfig;
import com.baidu.acg.piat.digitalhuman.common.project.PresetVO;
import com.baidu.acg.piat.digitalhuman.common.project.ProjectDelByNameReq;
import com.baidu.acg.piat.digitalhuman.llmdm.client.LlmDmHttpClient;
import com.baidu.acg.piat.digitalhuman.plat.dao.ProjectOnlineRepository;
import com.baidu.acg.piat.digitalhuman.plat.model.project.ProjectOnlineModel;
import com.baidu.acg.piat.digitalhuman.plat.service.OptLogService;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import io.vavr.control.Try;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Base64;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import static com.baidu.acg.piat.digitalhuman.common.constans.Constants.DEFAULT_PROJECT_VERSION;
import static com.baidu.acg.piat.digitalhuman.common.constans.Constants.LATEST_PROJECT_VERSION;
import com.baidu.acg.piat.digitalhuman.common.entity.CharacterConfig;
import com.baidu.acg.piat.digitalhuman.common.entity.CharacterConfigRelatedModule;
import com.baidu.acg.piat.digitalhuman.common.entity.CharacterModel;
import com.baidu.acg.piat.digitalhuman.common.exception.DigitalHumanCommonException;
import com.baidu.acg.piat.digitalhuman.common.model.PageResponse;
import com.baidu.acg.piat.digitalhuman.common.model.PageResult;
import com.baidu.acg.piat.digitalhuman.common.utils.JsonUtil;
import com.baidu.acg.piat.digitalhuman.plat.dao.AppRepository;
import com.baidu.acg.piat.digitalhuman.plat.dao.ProjectRepository;
import com.baidu.acg.piat.digitalhuman.plat.model.project.ConversationConfigModel;
import com.baidu.acg.piat.digitalhuman.plat.model.project.ProjectModel;
import com.baidu.acg.piat.digitalhuman.plat.service.BackgroundService;
import com.baidu.acg.piat.digitalhuman.plat.service.CharacterService;
import com.baidu.acg.piat.digitalhuman.plat.service.ProjectService;
import com.baidu.acg.piat.digitalhuman.plat.v2.service.CharacterConfigService;
import com.baidu.acg.piat.digitalhuman.plat.v2.service.ServiceException;
import com.baidu.acg.piat.digitalhuman.storage.service.StorageService;
import com.baidu.acg.piat.digitalhuman.storage.service.StorageService.ResourceException;


/**
 * Created on 2020/4/21 18:36.
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class ProjectServiceImpl implements ProjectService {

    @Value("${digitalhuman.llm.enable:false}")
    private boolean llmEnable;

    private final ProjectRepository repository;

    private final ProjectOnlineRepository projectOnlineRepository;

    private final AppRepository appRepository;

    private final MoreDbEditTransactionServiceImpl moreDbEditTransactionService;

    private final BackgroundService backgroundService;

    private final CharacterService characterService;

    private final CharacterConfigService characterConfigService;

    private final StorageService storageService;

    @Value("${digitalhuman.project.html5Url}")
    private String defaultHtml5Url;

    private static final Integer DEFAULT_V2_API_VERSION = 2;
    /**
     * 临时配置2d人像裁剪
     * key：横竖屏-相机位
     * value：裁剪参数
     */
    private final Map<String, FigureCutParams> figureCutMap;

    private final OptLogService optLogService;

    private final LlmDmHttpClient llmDmHttpClient;

    /**
     * 创建一个新的Project实体。
     *
     * @param request 请求参数
     * @return 保存成功后的Project实体，如果创建失败则抛出DigitalHumanCommonException异常
     */
    @Override
    @Transactional
    public Project create(Project request) {
        log.debug("Start to create project = {}", request);
        // 兼容2.0的人设
        CharacterConfig characterConfig = null;
        if (!StringUtils.isEmpty(request.getCharacterConfigId())) {
            try {
                characterConfig = validateCharacterConfigId(request.getCharacterConfigId());
                Optional.ofNullable(characterConfig)
                        .ifPresent(
                                c -> {
                                    request.setCharacterConfigName(c.getName());
                                    request.setCharacterImage(c.getType());
                                });
            } catch (Exception e) {
                log.warn("Can not find character={}", request.getCharacterConfigId());
            }
        }

        validateRequest(request);
        validateName(request);
        if (!"SYSTEM".equals(request.getType())) {
            validateAndProcessBotParam(request.getBotParams(), null);
        }
        validateCharacter(request.getCharacterImage());
        String type = "USER";
        log.debug("project type is {}", request.getType());
        if ("SYSTEM".equals(request.getType())) {
            type = request.getType();
        }
        var projectOptional =
            repository.findByUserIdAndNameAndProjectVersionAndApiVersionAndType(
                request.getUserId(), request.getName(),
                ProjectModel.toProjectVersion(DEFAULT_PROJECT_VERSION), request.getApiVersion(), type);
        if (projectOptional.isPresent()) {
            throw new DigitalHumanCommonException("该项目已经存在");
        }
        String projectId = ObjectId.get().toHexString();
        ProjectModel projectModel =
            ProjectModel.builder()
                .projectId(projectId)
                .name(request.getName())
                .characterImage(request.getCharacterImage())
                .figureAlias(request.getFigureAlias())
                .description(request.getDescription())
                .preset(request.getPreset())
                .isDefault(request.getIsDefault() ? 1 : 0)
                .userId(request.getUserId())
                .logoUid(request.getLogoUid())
                .logoUrl(request.getLogoUrl())
                .projectVersion(ProjectModel.toProjectVersion(DEFAULT_PROJECT_VERSION))
                .thumbnailUrl(request.getThumbnailUrl())
                .characterThumbnail(
                    uploadCharacterThumbnail(request.getCharacterThumbnail(), request.getUserId(), projectId))
                .apiVersion(request.getApiVersion())
                .type(type)
                .botParams(request.getBotParams())
                .build();
        if (characterConfig != null
            && StringUtils.isNotBlank(characterConfig.getConfigId())
            && StringUtils.isNotBlank(characterConfig.getConfig())) {
            projectModel.setCharacterConfigId(characterConfig.getConfigId());
            projectModel.setCharacterConfigName(characterConfig.getName());
        }
        if (StringUtils.isNotEmpty(request.getBackgroundImageId())) {
            var optional = backgroundService.get(request.getBackgroundImageId());
            if (optional.isPresent()) {
                projectModel.setBackgroundImageId(request.getBackgroundImageId());
                projectModel.setBackgroundImageUrl(optional.get().getImageUrl());
            }
        }
        projectModel.toProjectModel(request);
        fillHtml5Url(request.getPaintChartOnPictureParams());
        projectModel.setPreset(generatePreset(projectModel));
        fillFigureCut(projectModel);
        projectModel = save(projectModel);
        projectCreateOptLog(request);
        return projectModel.toProjectRequest();
    }

    /**
     * ValidateAndProcessBotParam 方法用于对传入的参数进行校验和处理，并根据业务需求调用llm-dm接口新增/更新角色。
     * @param newBotParams 需要被处理的新参数。
     * @param oldBotParams 旧参数，用于判断是否需要删除旧参数对应的llmRole。
     */
    private void validateAndProcessBotParam(BotParams newBotParams, BotParams oldBotParams) {
        log.info("ValidateAndProcessBotParam newBotParams = {}, oldBotParams = {}, llmEnable={}", newBotParams,
                oldBotParams, llmEnable);
        if (null == newBotParams) {
            return;
        }
        Optional<String> botMode = BotParams.BotTypeEnum.getBotMode(newBotParams.getType());
        if (botMode.isEmpty()) {
            throw new DigitalHumanCommonException("botParam type invalid");
        }
        if (null != newBotParams.getBotMode() &&
                !botMode.get().equals(newBotParams.getBotMode())) {
            throw new DigitalHumanCommonException("botParam type and botMode do not match");
        }
        newBotParams.setBotMode(botMode.get());
        // 大模型的bot配置需要调用 llm-dm接口新建/更新一个角色 ，在私有化的时候可能不需要
        if (!llmEnable) {
            return;
        }
        // 删除llmconfig
        if (validateBotParamsLlmRoleId(oldBotParams)
                && !newBotParams.getBotMode().equals(BotParams.BotModeEnum.LLM.name())) {
            llmDmHttpClient.batchDeleteLlmConfig(Lists.newArrayList(oldBotParams.getCredentials().getLlmRoleId()));
            return;
        }
        if (!newBotParams.getBotMode().equals(BotParams.BotModeEnum.LLM.name())) {
            return;
        }
        Credentials credentials = newBotParams.getCredentials();
        if (null == credentials) {
            throw new DigitalHumanCommonException("botParam credentials must  not be null");
        }
        String llmRoleId = null;
        if (validateBotParamsLlmRoleId(oldBotParams)) {
            llmRoleId = oldBotParams.getCredentials().getLlmRoleId();
        }
        LLMConfig llmConfig=new LLMConfig();
        llmConfig.setBotType(newBotParams.getType());
        llmConfig.setUrl(newBotParams.getUrl());

        Map<String, Object> llmConfigBotparams = Maps.newHashMap();
        llmConfig.setParams(llmConfigBotparams);
        if (MapUtils.isNotEmpty(newBotParams.getParams())) {
            newBotParams.getParams().forEach((key, val) -> {
                llmConfigBotparams.put(key, val);
            });
        }

        Map<String,String> llmConfigCredentials=Maps.newHashMap();
        llmConfigCredentials.put("token", credentials.getToken());
        llmConfig.setCredential(llmConfigCredentials);
        log.info("Start call llm-dm to create/update a role, llmConfig={}, roleId={}", llmConfig, llmRoleId);
        if (StringUtils.isBlank(llmRoleId)) { // 新建
            Response<LlmRole> llmConfigRes = llmDmHttpClient.createLlmConfig(LlmRole.builder()
                    .llmConfig(llmConfig).build());
            llmRoleId = llmConfigRes.getResult().getLlmRoleId();
        } else { // update
            llmDmHttpClient.updateLlmConfig(
                    LlmRole.builder()
                            .llmRoleId(llmRoleId)
                            .llmConfig(llmConfig).build());
        }
        credentials.setLlmRoleId(llmRoleId);
    }

    private boolean validateBotParamsLlmRoleId(BotParams botParams) {
        if (null != botParams
                && BotParams.BotModeEnum.LLM.name().equals(botParams.getBotMode())
                && null != botParams.getCredentials()
                && StringUtils.isNotBlank(botParams.getCredentials().getLlmRoleId())) {
            return true;
        }
        return false;
    }

    private void projectCreateOptLog(Project project) {
        if (DEFAULT_V2_API_VERSION.equals(project.getApiVersion())) {
            OptLog optLog;
            if (AddType.COPY.name().equals(project.getAddType())
                    && StringUtils.isNotEmpty(project.getUid())) {
                optLog = OptLogHelper.buildOptLog(OptContent.SCENE_COPY
                        , project.getName(), project.getUserId(), project.getUid());
            } else {
                optLog = OptLogHelper.buildOptLog(OptContent.SCENE_ADD
                        , project.getName(), project.getUserId(), project.getUid());
            }
            optLogService.create(optLog);
        }
    }

    @Override
    public void deleteByName(ProjectDelByNameReq delByNameReq) {
        String userId = delByNameReq.getUserId();
        String projectName = delByNameReq.getProjectName();
        int apiVersion = delByNameReq.getApiVersion();
        String type = "USER";
        log.debug("Start to delete project, userId={}, projectName={}, apiVersion={}", userId, projectName, apiVersion);
        var projectList = repository.findByUserIdAndNameAndApiVersionAndType(userId, projectName, apiVersion, type);
        if (CollectionUtils.isNotEmpty(projectList)) {
            var projectIds = new ArrayList<String>();
            projectList.forEach(project -> {
                projectIds.add(project.getProjectId());
            });

            var list = appRepository.findAllByUserIdAndApiVersionAndProjectIdInOrderByCreateTimeDesc(
                    userId, apiVersion, projectIds, PageRequest.of(0, 1));

            var totalCount = list.getTotalElements();
            if (totalCount > 0) {
                log.debug("project has been used userId={}, projectName={}, apiVersion={}, projectIds={}",
                        userId, projectName, apiVersion, projectIds);
                throw new DigitalHumanCommonException("该项目已被应用绑定请先删除相关应用");
            }
            projectList.forEach(project -> {
                deleteByProjectId(project);
                // delByNameReq.getUid() 非空判断为了兼容老del接口没有uid
                if (DEFAULT_V2_API_VERSION.equals(apiVersion)
                        && StringUtils.isNotEmpty(delByNameReq.getUid())) {
                    optLogService.create(OptLogHelper.buildOptLog(OptContent.SCENE_DELETE
                            , project.getName(), project.getUserId(), delByNameReq.getUid()));
                }

            });
        }
    }

    @Override
    public void delete(String projectId) {
        log.debug("Start to delete projectID={}", projectId);
        var project = getByProjectId(projectId);
        deleteByProjectId(project);
    }


    @Override
    public Project detailOfPublishVersion(String projectId) {
        log.debug("Start to get project publish detail={}", projectId);
        var project = getByProjectIdOfOnline(projectId).toProjectRequest();
        // 人像类型如果存在，则需要填充人像默认的appId和appKey
        if (StringUtils.isNotBlank(project.getCharacterImage())) {
            List<CharacterModel> characterImageList = characterService.selectByType(project.getCharacterImage());
            var appId = characterImageList.get(0).getAppId();
            var appKey = characterImageList.get(0).getAppKey();
            project.setAppId(appId);
            project.setAppKey(appKey);
        }
        return project;
    }

    @Override
    public Project detail(String projectId) {
        log.debug("Start to get project detail={}", projectId);
        var project = getByProjectId(projectId).toProjectRequest();
        // 人像类型如果存在，则需要填充人像默认的appId和appKey
        if (StringUtils.isNotBlank(project.getCharacterImage())) {
            List<CharacterModel> characterImageList = characterService.selectByType(project.getCharacterImage());
            var appId = characterImageList.get(0).getAppId();
            var appKey = characterImageList.get(0).getAppKey();
            project.setAppId(appId);
            project.setAppKey(appKey);
        }
        return project;
    }

    private ProjectOnlineModel getByProjectIdOfOnline(String projectId) {
        var optional = projectOnlineRepository.findByProjectId(projectId);
        if (optional.isEmpty()) {
            throw new DigitalHumanCommonException("项目不存在");
        }
        return optional.get();
    }

    private ProjectModel getByProjectId(String projectId) {
        var optional = repository.findByProjectId(projectId);
        if (optional.isEmpty()) {
            throw new DigitalHumanCommonException("项目不存在");
        }
        return optional.get();
    }

    @Override
    public Project getByUserIdNameAndVersion(String userId, String name, String version, int apiVersion) {
        log.debug("Start to get project by userId={} name={} and version={}", userId, name, version);
        var project = getByVersion(userId, name, version, apiVersion).toProjectRequest();
        // 人像类型如果存在，则需要填充人像默认的appId和appKey
        if (StringUtils.isNotBlank(project.getCharacterImage())) {
            List<CharacterModel> characterImageList = characterService.selectByType(project.getCharacterImage());
            var appId = characterImageList.get(0).getAppId();
            var appKey = characterImageList.get(0).getAppKey();
            project.setAppId(appId);
            project.setAppKey(appKey);
        }
        return project;
    }

    @Override
    public PageResponse<Project> getListByUserIdNameAndVersion(String userId, String name, String version,
                                                               int apiVersion, int pageNo, int pageSize,
                                                               String visibleCharacters) {
        log.debug("Start to get project by userId={} name={} version={} and visibleCharacters={}",
                userId, name, version, visibleCharacters);

        PageRequest pageRequest = PageRequest.of(pageNo - 1, pageSize);
        Page<ProjectModel> page = getByVersion(userId, name, version, apiVersion, visibleCharacters, pageRequest);

        List<Project> projectPageList = Lists.newArrayList();
        page.forEach(projectModel -> {
            Project project = projectModel.toProjectRequest();
            projectPageList.add(project);
        });

        return PageResponse.<Project>builder()
                .page(
                        PageResult.<Project>builder()
                                .pageNo(pageNo)
                                .pageSize(pageSize)
                                .totalCount(page.getTotalElements())
                                .result(projectPageList)
                                .build())
                .build();
    }

    private ProjectModel getLatest(String userId, String name, int apiVersion) {
        String type = "USER";
        var page = repository.findByUserIdAndNameAndApiVersionAndTypeOrderByCreateTimeDesc(userId, name, apiVersion, type, null);
        if (page.isEmpty()) {
            throw new DigitalHumanCommonException("项目不存在");
        }
        return page.getContent().get(0);
    }

    private Page<ProjectModel> getLatest(String userId, String name, int apiVersion, PageRequest pageRequest) {
        name = "%" + name + "%";
        var page = repository.findByUserIdAndNameContainingAndApiVersionAndIsDefaultAndMaxProjectVersionAndTypeOrderByCreateTimeDesc(
                userId, name, apiVersion, 0, pageRequest);
        return page;
    }

    private Page<ProjectModel> getLatest(String userId, String name, int apiVersion,
                                         List<String> types, PageRequest pageRequest) {
        String type = "USER";
        var page = repository.findByUserIdAndNameContainingAndApiVersionAndCharacterImageInAndIsDefaultAndTypeOrderByCreateTimeDesc(
                userId, name, apiVersion, types, 0, type, pageRequest);
        return page;
    }

    private ProjectModel getByVersion(String userId, String name, String version, int apiVersion) {
        if (version == null || LATEST_PROJECT_VERSION.equals(version)) {
            return getLatest(userId, name, apiVersion);
        }
        String type = "USER";
        var optional =
            repository.findByUserIdAndNameAndProjectVersionAndApiVersionAndType(
                userId, name, ProjectModel.toProjectVersion(version), apiVersion, type);
        if (optional.isEmpty()) {
            throw new DigitalHumanCommonException("项目不存在");
        }
        return optional.get();
    }

    private Page<ProjectModel> getByVersion(String userId, String name, String version, int apiVersion,
                                            String visibleCharacters, PageRequest pageRequest) {
        if (version == null || LATEST_PROJECT_VERSION.equals(version)) {
            if (CharacterModel.USER_VISIBLE_ALL_FLAG.equals(visibleCharacters)) {
                return getLatest(userId, name, apiVersion, pageRequest);
            }
            List<String> types = new ArrayList<>(Arrays.asList(visibleCharacters.split(",")));
            types.add(StringUtils.EMPTY);
            return getLatest(userId, name, apiVersion, types, pageRequest);
        }

        String type = "USER";
        if (CharacterModel.USER_VISIBLE_ALL_FLAG.equals(visibleCharacters)) {
            return repository
                    .findByUserIdAndNameContainingAndProjectVersionAndIsDefaultAndApiVersionAndTypeOrderByCreateTimeDesc(
                    userId, name, ProjectModel.toProjectVersion(version), 0, apiVersion, type, pageRequest);
        }
        List<String> types = new ArrayList<>(Arrays.asList(visibleCharacters.split(",")));
        types.add(StringUtils.EMPTY);
        String projectType = "USER";
        return repository.
        findByUserIdAndNameContainingAndProjectVersionAndIsDefaultAndApiVersionAndCharacterImageInAndTypeOrderByCreateTimeDesc(
                userId, name, ProjectModel.toProjectVersion(version), 0, apiVersion, types, projectType, pageRequest);
    }


    @Override
    @Transactional
    public Project publish(String projectId, Project request) {
        // 1. 先把本地保存
        log.debug("Start to publish project={}", request);
        ProjectModel projectModel = repository.findByProjectId(projectId).get();
        ProjectOnlineModel projectOnlineModel = new ProjectOnlineModel();
        BeanUtils.copyProperties(projectModel, projectOnlineModel);
        if (projectOnlineRepository.existsById(projectOnlineModel.getId())) {
            projectOnlineRepository.deleteById(projectOnlineModel.getId());
        }
        projectOnlineRepository.save(projectOnlineModel);
        return projectOnlineModel.toProjectRequest();
    }

    @Override
    @Transactional
    public Project update(String projectId, Project request) {
        log.debug("Start to update project={}", request);
        validateRequest(request);
        var updated = getByProjectId(projectId);
        request.setType(updated.getType());
        String type = "USER";
        if ("SYSTEM".equals(request.getType())) {
            type = "SYSTEM";
        }
        if (StringUtils.isNotEmpty(request.getName()) && !request.getName().equals(updated.getName())) {
            var projectModelList = repository.findByUserIdAndNameAndApiVersionAndType(
                    request.getUserId(), request.getName(), updated.getApiVersion(), type);
            if (!projectModelList.isEmpty()) {
                throw new DigitalHumanCommonException("修改后的名称已存在");
            }
            updated.setName(request.getName());
        }
        filerEmpty(request, updated);
        fillFigureCut(updated);
        Project res = save(updated).toProjectRequest();
        if (DEFAULT_V2_API_VERSION.equals(request.getApiVersion())
                && StringUtils.isNotEmpty(request.getUid())) {
            optLogService.create(OptLogHelper.buildOptLog(OptContent.SCENE_UPDATE
                    , updated.getName(), updated.getUserId(), request.getUid()));
        }
        return res;
    }

    @Override
    @Transactional
    public Project updateByUserIdAndName(String userId, String name, Project request) {
        log.debug("Start to update project by userId={} name={} project={}", userId, name, request);
        validateRequest(request);
        // 2.0 场景界面不区分版本
        if (request.getApiVersion() == 2) {
            CharacterConfig characterConfig = null;
            try {
                characterConfig = validateCharacterConfigId(request.getCharacterConfigId());
                Optional.ofNullable(characterConfig)
                        .ifPresent(
                                c -> {
                                    request.setCharacterConfigName(c.getName());
                                });
            } catch (ServiceException e) {
                throw new DigitalHumanCommonException(e.getMessage());
            }
            var updated = getLatest(userId, name, request.getApiVersion());
            filerEmpty(request, updated);
            fillFigureCut(updated);
            if (StringUtils.isNotEmpty(request.getUid())
                    && null != request.getBotParams()) {
                optLogService.create(OptLogHelper.buildOptLog(OptContent.SCENE_BINDTOKE
                        , name, userId, request.getUid()));
            }
            return save(updated).toProjectRequest();
        }
        // 1.0 人设需要区分版本
        var updated = getByVersion(userId, name, LATEST_PROJECT_VERSION, request.getApiVersion());
        var projectModel = updated.copy();
        filerEmpty(request, projectModel);
        fillFigureCut(projectModel);
        projectModel.setProjectVersion(
            ProjectModel.toProjectVersion(
                ProjectModel.fromProjectVersion(increaseRelease(projectModel.getProjectVersion()))));
        Project res = save(projectModel).toProjectRequest();
        return res;
    }

    private void filerEmpty(Project request, ProjectModel updated) {
        if (StringUtils.isNotEmpty(request.getDescription())) {
            updated.setDescription(request.getDescription());
        }
        if (StringUtils.isNotEmpty(request.getEditor())) {
            updated.setEditor(request.getEditor());
        }
        if (StringUtils.isNotEmpty(request.getCharacterImage())) {
            // validateCharacter(request.getCharacterImage());
            updated.setCharacterImage(request.getCharacterImage());
        }
        if (StringUtils.isNotEmpty(request.getFigureAlias())) {
            updated.setFigureAlias(request.getFigureAlias());
        }
        if (StringUtils.isNotEmpty(request.getPreset())) {
            updated.setPreset(request.getPreset());
        }
        if (StringUtils.isNotEmpty(request.getThumbnailUrl())) {
            updated.setThumbnailUrl(request.getThumbnailUrl());
        }
        if (StringUtils.isNotEmpty(request.getCharacterConfigId())) {
            updated.setCharacterConfigId(request.getCharacterConfigId());
            updated.setCharacterConfigName(request.getCharacterConfigName());
        }
        updateLogo(request, updated);
        updateBackgroundImage(request, updated);
        updateBotParams(request, updated);
        updateCamera(request, updated);
        updateResolutionParams(request, updated);
        updateTTSParams(request, updated);
        updateFigureCutParams(request, updated);
        updatePaintChartOnPictureParams(request, updated);
        updateCharacterParams(request, updated);
        updatePaintSubtitleOnPictureParams(request, updated);
        updateMediaOutput(request, updated);
        updateConversationConfig(request, updated);
        updateHotWords(request, updated);
        updateUserInactive(request, updated);
        updatePrologueParams(request, updated);
        updateHitShieldReply(request, updated);
        updateAlitaParams(request, updated);
        updateAsrPartEvent(request, updated);
        updated.setPreset(generatePreset(updated));
        updateCharacterThumbnail(request, updated);
    }

    @Override
    public PageResponse<Project> list(String userId, int apiVersion, int pageNo, int pageSize) {
        log.debug(
            "Start to list project, userId={}, apiVersion={}, pageNo={}, pageSize={}",
            userId,
            apiVersion,
            pageNo,
            pageSize);
        PageRequest pageRequest = PageRequest.of(pageNo - 1, pageSize);
        Page<ProjectModel> projectModelPage = null;
        if (apiVersion == 0) {
            projectModelPage = repository.findByUserIdAndIsDefaultAndTypeOrderByCreateTimeDesc(userId, 0, pageRequest);
        } else {
            projectModelPage =
                repository.findByUserIdAndIsDefaultAndApiVersionAndTypeOrderByCreateTimeDesc(
                    userId, 0, apiVersion, pageRequest);
        }
        List<Project> projectRequestList = Lists.newArrayList();
        projectModelPage.forEach(
            r -> {
                projectRequestList.add(r.toProjectRequest());
            });
        return PageResponse.<Project>builder()
            .page(
                PageResult.<Project>builder()
                    .pageNo(pageNo)
                    .pageSize(pageSize)
                    .totalCount(projectModelPage.getTotalElements())
                    .result(projectRequestList)
                    .build())
            .build();
    }

    @Override
    public PageResponse<Project> listWithMaxProjectVersion(
        String userId, int apiVersion, int pageNo, int pageSize, String visibleCharacters) {
        log.debug(
            "Start to list project by userId={}, apiVersion={}, pageNo={}, pageSize={}, visibleCharacters={} "
                + "where projectVersion is max",
            userId,
            apiVersion,
            pageNo,
            pageSize,
            visibleCharacters);
        Page<ProjectModel> projectModelPage = null;
        if (CharacterModel.USER_VISIBLE_ALL_FLAG.equals(visibleCharacters)) {
            projectModelPage =
                findByUserIdAndMaxProjectVersion(userId, 0, apiVersion, PageRequest.of(pageNo - 1, pageSize));
        } else {
            List<String> types = Arrays.asList(visibleCharacters.split(","));
            projectModelPage =
                findByUserIdAndApiVersionAndMaxProjectVersion(
                    userId, 0, apiVersion, types, PageRequest.of(pageNo - 1, pageSize));
        }

        List<Project> projectRequestList = Lists.newArrayList();
        projectModelPage.forEach(r -> projectRequestList.add(r.toProjectRequest()));
        return PageResponse.<Project>builder()
            .page(
                PageResult.<Project>builder()
                    .pageNo(pageNo)
                    .pageSize(pageSize)
                    .totalCount(projectModelPage.getTotalElements())
                    .result(projectRequestList)
                    .build())
            .build();
    }

    @Override
    public List<Project> listAllWithMaxProjectVersion(int apiVersion) {
        List<ProjectModel> projectModels =
                repository.findByIsDefaultAndApiVersionAndType(0, apiVersion);
        Map<String, List<ProjectModel>> groupMapList =
                projectModels.stream().collect(Collectors.groupingBy(projectModel ->
                        projectModel.getUserId() + projectModel.getName()));
        Set<String> strs = Sets.newHashSet();
        groupMapList.forEach(
                (k, v) -> {
                    String version =
                            v.stream()
                                    .max(Comparator.comparingInt(t -> Integer.valueOf(t.getProjectVersion())))
                                    .get()
                                    .getProjectVersion();
                    strs.add(k + version);
                });
        return repository.findByUserIdAndNameAndProjectVersionAndType(strs, apiVersion)
                .stream().map(ProjectModel::toProjectRequest).collect(Collectors.toList());
    }

    @Override
    public List<CharacterConfigRelatedModule> listByCharacterConfigIds(List<String> configIds,
                                                                       String userId, int apiVersion) {
        log.debug("Start to list project, configIds={}, userId={}, apiVersion={}",
                configIds, userId, apiVersion);
        String type = "USER";
        var projectList = repository.findByCharacterConfigIdInAndUserIdAndApiVersionAndIsDefaultAndType(
                configIds, userId, apiVersion, 0, type);
        if (CollectionUtils.isNotEmpty(projectList)) {
            return projectList.stream().map(projectModel -> CharacterConfigRelatedModule.builder()
                    .configId(projectModel.getCharacterConfigId())
                    .id(projectModel.getProjectId())
                    .name(projectModel.getName())
                    .build()).collect(Collectors.toList());
        }
        return null;
    }

    private Page<ProjectModel> findByUserIdAndMaxProjectVersion(
        String userId, int isDefault, int apiVersion, PageRequest pageRequest) {
        List<ProjectModel> projectModels =
            repository.findByUserIdAndIsDefaultAndApiVersionAndType(userId, isDefault, apiVersion);
        Map<String, List<ProjectModel>> groupMapList =
            projectModels.stream().collect(Collectors.groupingBy(ProjectModel::getName));
        Set<String> strs = Sets.newHashSet();
        groupMapList.forEach(
            (k, v) -> {
                String version =
                    v.stream()
                        .max(Comparator.comparingInt(t -> Integer.valueOf(t.getProjectVersion())))
                        .get()
                        .getProjectVersion();
                strs.add(userId + k + version);
            });
        return repository.findByUserIdAndNameAndProjectVersionAndType(strs, apiVersion, pageRequest);
    }

    private Page<ProjectModel> findByUserIdAndApiVersionAndMaxProjectVersion(
        String userId, int isDefault, int apiVersion, List<String> types, PageRequest pageRequest) {
        List<ProjectModel> projectModels =
            repository.findByUserIdAndIsDefaultAndApiVersionAndCharacterImageAndType(userId, isDefault, apiVersion, types);
        Map<String, List<ProjectModel>> groupMapList =
            projectModels.stream().collect(Collectors.groupingBy(ProjectModel::getName));
        Set<String> strs = Sets.newHashSet();
        groupMapList.forEach(
            (k, v) -> {
                String version =
                    v.stream()
                        .max(Comparator.comparingInt(t -> Integer.valueOf(t.getProjectVersion())))
                        .get()
                        .getProjectVersion();
                strs.add(userId + k + version + apiVersion);
            });
        return repository.findByUserIdAndNameAndProjectVersionAndApiVersionAndType(strs, apiVersion, pageRequest);
    }

    /**
     * 临时填充2D人像的裁剪参数
     * @param projectModel
     */
    private void fillFigureCut(ProjectModel projectModel) {
        if (!ObjectUtils.allNotNull(projectModel.getResolutionParams(), projectModel.getCamera())
            || !ObjectUtils.allNotNull(
                projectModel.getResolutionParams().getHeight(),
                projectModel.getResolutionParams().getWidth(),
                projectModel.getCamera().getId())) {
            return;
        }

        // 判断横屏还是竖屏
        String screen =
            projectModel.getResolutionParams().getHeight() > projectModel.getResolutionParams().getWidth()
                ? "vertical"
                : "horizontal";
        String key = StringUtils.joinWith("-", screen, projectModel.getCamera().getId());
        projectModel.setFigureCutParams(figureCutMap.getOrDefault(key, projectModel.getFigureCutParams()));
    }

    // region update fields

    private void updateLogo(Project request, ProjectModel updated) {
        if (request.getLogoUid() != null && StringUtils.isEmpty(request.getLogoUid())) {
            updated.setLogoUid("");
            updated.setLogoUrl("");
        } else if (StringUtils.isNotEmpty(request.getLogoUid())) {
            var optional = backgroundService.get(request.getLogoUid());
            if (optional.isPresent()) {
                updated.setLogoUid(request.getLogoUid());
                updated.setLogoUrl(optional.get().getImageUrl());
            }
        }
        if (request.getLogoUrl() != null) {
            updated.setLogoUrl(request.getLogoUrl());
        }
    }

    private void updateBackgroundImage(Project request, ProjectModel updated) {
        if (request.getBackgroundImageId() != null && StringUtils.isEmpty(request.getBackgroundImageId())) {
            updated.setBackgroundImageId("");
            updated.setBackgroundImageUrl("");
        } else if (StringUtils.isNotEmpty(request.getBackgroundImageId())) {
            var optional = backgroundService.get(request.getBackgroundImageId());
            if (optional.isPresent()) {
                updated.setBackgroundImageId(request.getBackgroundImageId());
                updated.setBackgroundImageUrl(optional.get().getImageUrl());
            } else if ("SYSTEM".equals(request.getType())) {
                if (StringUtils.isNotBlank(request.getBackgroundImageId())) {
                    updated.setBackgroundImageId(request.getBackgroundImageId());
                }
            }
        }
        if (request.getBackgroundImageUrl() != null) {
            updated.setBackgroundImageUrl(request.getBackgroundImageUrl());
        }
    }

    /**
     * 更新机器人参数
     *
     * @param request  请求对象
     * @param updated  更新后的项目模型
     */
    private void updateBotParams(Project request, ProjectModel updated) {
        // 去掉validate检查，botparams中的参数可以修改为空字符串
        if (request.getBotParams() == null) {
            return;
        }
        log.info("update bot params, request: {}, updated: {}", request, updated);
        if (!"SYSTEM".equals(request.getType())) {
            validateAndProcessBotParam(request.getBotParams(), updated.getBotParams());
        }
        var botParams = request.getBotParams();
        var botParamsBuilder = BotParams.builder()
                .type(botParams.getType())
                .credentials(botParams.getCredentials())
                .url(botParams.getUrl())
                .params(botParams.getParams())
                .extraParams(botParams.getExtraParams())
                .build();
        updated.setBotParams(botParamsBuilder);
    }

    private void updateCamera(Project request, ProjectModel updated) {
        if (Objects.isNull(request.getCamera())) {
            return;
        }
        if (updated.getCamera() == null) {
            updated.setCamera(new Camera());
        }
        var camera = request.getCamera();
        if (camera.getId() != null) {
            updated.getCamera().setId(camera.getId());
        }
    }

    private void updateResolutionParams(Project request, ProjectModel updated) {
        if (Objects.isNull(request.getResolutionParams())) {
            return;
        }
        if (updated.getResolutionParams() == null) {
            updated.setResolutionParams(new ResolutionParams());
        }
        var resolutionParams = request.getResolutionParams();
        if (updated.getResolutionParams() == null) {
            updated.setResolutionParams(new ResolutionParams());
        }
        if (resolutionParams.getWidth() != null) {
            updated.getResolutionParams().setWidth(resolutionParams.getWidth());
        }
        if (resolutionParams.getHeight() != null) {
            updated.getResolutionParams().setHeight(resolutionParams.getHeight());
        }
        if (resolutionParams.getKbpsBitrate() != null) {
            updated.getResolutionParams().setKbpsBitrate(resolutionParams.getKbpsBitrate());
        }
    }

    private void updateTTSParams(Project request, ProjectModel updated) {
        if (Objects.isNull(request.getTtsParams())) {
            return;
        }
        if (updated.getTtsParams() == null) {
            updated.setTtsParams(new TtsParams());
        }
        var ttsParams = request.getTtsParams();
        if (updated.getTtsParams() == null) {
            updated.setTtsParams(new TtsParams());
        }
        if (ttsParams.getPitch() != null) {
            updated.getTtsParams().setPitch(ttsParams.getPitch());
        }
        if (ttsParams.getSpeed() != null) {
            updated.getTtsParams().setSpeed(ttsParams.getSpeed());
        }
        if (ttsParams.getVolume() != null) {
            updated.getTtsParams().setVolume(ttsParams.getVolume());
        }
        if (ttsParams.getPerson() != null) {
            updated.getTtsParams().setPerson(ttsParams.getPerson());
        }
        if (ttsParams.getTtsId() != null) {
            updated.getTtsParams().setTtsId(ttsParams.getTtsId());
        }
        if (ttsParams.getExtraParams() != null) {
            updated.getTtsParams().setExtraParams(ttsParams.getExtraParams());
        }
    }

    private void updateMediaOutput(Project request, ProjectModel updated) {
        if (Objects.isNull(request.getMediaOutput())) {
            return;
        }
        if (updated.getMediaOutput() == null) {
            updated.setMediaOutput(new MediaOutput());
        }
        var mediaOutput = request.getMediaOutput();
        if (Objects.isNull(updated.getMediaOutput())) {
            updated.setMediaOutput(new MediaOutput());
        }
        if (Objects.nonNull(mediaOutput.getBrtc())) {
            updated.getMediaOutput().setBrtc(mediaOutput.getBrtc());
        }
        if (Objects.nonNull(mediaOutput.getRtmp())) {
            updated.getMediaOutput().setRtmp(mediaOutput.getRtmp());
        }
    }

    private void updatePaintChartOnPictureParams(Project request, ProjectModel updated) {
        if (request.getPaintChartOnPictureParams() == null) {
            return;
        }
        if (updated.getPaintChartOnPictureParams() == null) {
            updated.setPaintChartOnPictureParams(new PaintChartOnPictureParams());
        }
        if (Objects.nonNull(request.getPaintChartOnPictureParams().getPaintChartOnPicture())) {
            updated
                .getPaintChartOnPictureParams()
                .setPaintChartOnPicture(request.getPaintChartOnPictureParams().getPaintChartOnPicture());
        }
        if (Objects.nonNull(request.getPaintChartOnPictureParams().getPaintWidgetOnPicture())) {
            updated
                .getPaintChartOnPictureParams()
                .setPaintWidgetOnPicture(request.getPaintChartOnPictureParams().getPaintWidgetOnPicture());
        }
        if (Objects.nonNull(request.getPaintChartOnPictureParams().getHtml5Url())) {
            updated.getPaintChartOnPictureParams().setHtml5Url(request.getPaintChartOnPictureParams().getHtml5Url());
        }
        if (Objects.nonNull(request.getPaintChartOnPictureParams().getRenderVideoOutsideUe4())) {
            updated
                .getPaintChartOnPictureParams()
                .setRenderVideoOutsideUe4(request.getPaintChartOnPictureParams().getRenderVideoOutsideUe4());
        }
        fillHtml5Url(updated.getPaintChartOnPictureParams());
    }

    private void fillHtml5Url(PaintChartOnPictureParams paintChartOnPictureParams) {
        if (paintChartOnPictureParams != null
            && paintChartOnPictureParams.getPaintWidgetOnPicture()
            && StringUtils.isAllEmpty(paintChartOnPictureParams.getHtml5Url())) {
            paintChartOnPictureParams.setHtml5Url(defaultHtml5Url);
        }
    }

    // 全量更新 characterParams
    private void updateCharacterParams(Project request, ProjectModel updated) {
        if (request.getCharacterParams() == null) {
            return;
        }
        if (updated.getCharacterParams() == null) {
            updated.setCharacterParams(new CharacterParams());
        }
        updated.setCharacterParams(request.getCharacterParams());
    }

    private void updatePaintSubtitleOnPictureParams(Project request, ProjectModel updated) {
        if (Objects.isNull(request.getPaintSubtitleOnPictureParams())) {
            return;
        }
        // update
        var paintSubtitleParams = request.getPaintSubtitleOnPictureParams();
        if (updated.getPaintSubtitleOnPictureParams() == null) {
            updated.setPaintSubtitleOnPictureParams(new PaintSubtitleOnPictureParams());
        }
        if (Objects.nonNull(paintSubtitleParams.getPaintSubtitleOnPicture())) {
            updated
                .getPaintSubtitleOnPictureParams()
                .setPaintSubtitleOnPicture(paintSubtitleParams.getPaintSubtitleOnPicture());
        }
        if (Objects.nonNull(paintSubtitleParams.getPaintQueryOnPicture())) {
            updated
                .getPaintSubtitleOnPictureParams()
                .setPaintQueryOnPicture(paintSubtitleParams.getPaintQueryOnPicture());
        }
        if (Objects.nonNull(paintSubtitleParams.getSubtitleBackgroundColor())) {
            updated
                .getPaintSubtitleOnPictureParams()
                .setSubtitleBackgroundColor(request.getPaintSubtitleOnPictureParams().getSubtitleBackgroundColor());
        }
        if (Objects.nonNull(paintSubtitleParams.getSubtitleSplittable())) {
            updated
                .getPaintSubtitleOnPictureParams()
                .setSubtitleSplittable(request.getPaintSubtitleOnPictureParams().getSubtitleSplittable());
        }
        if (Objects.nonNull(paintSubtitleParams.getSubtitleTTL())) {
            updated
                .getPaintSubtitleOnPictureParams()
                .setSubtitleTTL(request.getPaintSubtitleOnPictureParams().getSubtitleTTL());
        }
        if (Objects.nonNull(paintSubtitleParams.getSubtitleMarginPx())) {
            updated
                .getPaintSubtitleOnPictureParams()
                .setSubtitleMarginPx(request.getPaintSubtitleOnPictureParams().getSubtitleMarginPx());
        }
        if (Objects.nonNull(paintSubtitleParams.getSubtitleFontSize())) {
            updated
                .getPaintSubtitleOnPictureParams()
                .setSubtitleFontSize(request.getPaintSubtitleOnPictureParams().getSubtitleFontSize());
        }
        if (Objects.nonNull(paintSubtitleParams.getSubtitleColor())) {
            updated
                .getPaintSubtitleOnPictureParams()
                .setSubtitleColor(request.getPaintSubtitleOnPictureParams().getSubtitleColor());
        }
        if (Objects.nonNull(paintSubtitleParams.getSubtitleSize())) {
            updated
                .getPaintSubtitleOnPictureParams()
                .setSubtitleSize(request.getPaintSubtitleOnPictureParams().getSubtitleSize());
        }
        if (Objects.nonNull(paintSubtitleParams.getSubtitleBackColor())) {
            updated
                .getPaintSubtitleOnPictureParams()
                .setSubtitleBackColor(request.getPaintSubtitleOnPictureParams().getSubtitleBackColor());
        }
        if (Objects.nonNull(paintSubtitleParams.getSubtitleFont())) {
            updated
                .getPaintSubtitleOnPictureParams()
                .setSubtitleFont(request.getPaintSubtitleOnPictureParams().getSubtitleFont());
        }
        if (Objects.nonNull(paintSubtitleParams.getSubtitleLocationX())) {
            updated
                .getPaintSubtitleOnPictureParams()
                .setSubtitleLocationX(request.getPaintSubtitleOnPictureParams().getSubtitleLocationX());
        }
        if (Objects.nonNull(paintSubtitleParams.getSubtitleLocationY())) {
            updated
                .getPaintSubtitleOnPictureParams()
                .setSubtitleLocationY(request.getPaintSubtitleOnPictureParams().getSubtitleLocationY());
        }
    }

    private void updateFigureCutParams(Project request, ProjectModel updated) {
        if (Objects.isNull(request.getFigureCutParams())) {
            return;
        }
        if (updated.getFigureCutParams() == null) {
            updated.setFigureCutParams(new FigureCutParams());
        }
        var figureCutParams = request.getFigureCutParams();
        if (figureCutParams.getWidthRatio() != null) {
            updated.getFigureCutParams().setWidthRatio(figureCutParams.getWidthRatio());
        }
        if (figureCutParams.getCutWidthPercent() != null) {
            updated.getFigureCutParams().setCutWidthPercent(figureCutParams.getCutWidthPercent());
        }
        if (figureCutParams.getCutHeightPercent() != null) {
            updated.getFigureCutParams().setCutHeightPercent(figureCutParams.getCutHeightPercent());
        }
        if (figureCutParams.getCutXPercent() != null) {
            updated.getFigureCutParams().setCutXPercent(figureCutParams.getCutXPercent());
        }
        if (figureCutParams.getCutXPercent() != null) {
            updated.getFigureCutParams().setCutXPercent(figureCutParams.getCutXPercent());
        }
        if (figureCutParams.getCutYPercent() != null) {
            updated.getFigureCutParams().setCutYPercent(figureCutParams.getCutYPercent());
        }
        if (figureCutParams.getPositionCenterXPercent() != null) {
            updated.getFigureCutParams().setPositionCenterXPercent(figureCutParams.getPositionCenterXPercent());
        }
        if (figureCutParams.getPositionBottomYPercent() != null) {
            updated.getFigureCutParams().setPositionBottomYPercent(figureCutParams.getPositionBottomYPercent());
        }
    }

    // 全量更新 ConversationConfig
    private void updateConversationConfig(Project request, ProjectModel updated) {
        if (request.getConversationConfig() == null) {
            return;
        }
        if (updated.getConversationConfig() == null) {
            updated.setConversationConfig(
                ConversationConfigModel.toConversationConfigModel(request.getConversationConfig()));
        } else {
            updated.getConversationConfig().merge(request.getConversationConfig());
        }
    }

    // 全量更新 hotwords
    private void updateHotWords(Project request, ProjectModel updated) {
        if (request.getHotWords() == null) {
            return;
        }
        if (updated.getHotWords() == null) {
            updated.setHotWords(new HotWordReplaceReg());
        }
        updated.setHotWords(request.getHotWords());
    }

    // 全量更新 userInactive
    private void updateUserInactive(Project request, ProjectModel updated) {
        if (request.getUserInactive() == null) {
            return;
        }
        if (updated.getUserInactive() == null) {
            updated.setUserInactive(new UserInactiveConfig());
        }
        updated.setUserInactive(request.getUserInactive());
    }

    // 全量更新 prologueParams
    private void updatePrologueParams(Project request, ProjectModel updated) {
        if (request.getPrologueParams() == null) {
            return;
        }
        if (updated.getPrologueParams() == null) {
            updated.setPrologueParams(new PrologueParams());
        }
        updated.setPrologueParams(request.getPrologueParams());
    }

    // 全量更新 hitShieldReply
    private void updateHitShieldReply(Project request, ProjectModel updated) {
        if (request.getHitShieldReply() == null) {
            return;
        }
        if (updated.getHitShieldReply() == null) {
            updated.setHitShieldReply(Collections.emptyList());
        }
        updated.setHitShieldReply(request.getHitShieldReply());
    }

    private void updateAlitaParams(Project request, ProjectModel updated) {
        if (request.getAlitaParams() == null) {
            return;
        }
        updated.setAlitaParams(request.getAlitaParams());
    }

    private void updateAsrPartEvent(Project request, ProjectModel updated) {
        if (request.getAsrPartEvent() == null) {
            return;
        }
        updated.setAsrPartEvent(request.getAsrPartEvent());
    }

    private void updateCharacterThumbnail(Project request, ProjectModel updated) {
        if (StringUtils.isBlank(request.getCharacterThumbnail())) {
            return;
        }
        updated.setCharacterThumbnail(
            uploadCharacterThumbnail(request.getCharacterThumbnail(), request.getUserId(), updated.getProjectId()));
    }

    // endregion update fields

    // region private method

    private void validateRequest(Project request) {
        if (StringUtils.isEmpty(request.getUserId())) {
            throw new DigitalHumanCommonException("用户ID不能为空");
        }
        checkConversationConfig(request.getConversationConfig());
        checkUserInactiveConfig(request.getUserInactive());
    }

    private void validateName(Project request) {
        if (StringUtils.isEmpty(request.getName())) {
            throw new DigitalHumanCommonException("项目名不能为空");
        }
    }

    private void validateCharacter(String character) {
        if (StringUtils.isNotBlank(character)) {
            characterService.selectByType(character);
        }
    }

    private CharacterConfig validateCharacterConfigId(String characterConfigId) throws ServiceException {
        if (StringUtils.isNotBlank(characterConfigId)) {
            return characterConfigService.retrieve(characterConfigId);
        }
        return null;
    }

    private void checkConversationConfig(ConversationConfig config) {
        if (config == null) {
            return;
        }
        config.check();
    }

    private void checkUserInactiveConfig(UserInactiveConfig userInactiveConfig) {
        if (userInactiveConfig == null) {
            return;
        }
        userInactiveConfig.check();
    }

    private String generatePreset(ProjectModel request) {
        if (request.getResolutionParams() == null) {
            request.setResolutionParams(new ResolutionParams());
        }
        if (request.getCharacterParams() == null) {
            request.setCharacterParams(new CharacterParams());
        }
        if (request.getPaintChartOnPictureParams() == null) {
            request.setPaintChartOnPictureParams(new PaintChartOnPictureParams());
        }
        PresetVO presetVO =
            PresetVO.builder()
                .character(request.getCharacterParams())
                .display(
                    PresetVO.Display.builder()
                        .bitrate(request.getResolutionParams().getKbpsBitrate())
                        .background(new PresetVO.Background().setValue(request.getBackgroundImageUrl()))
                        .resolution(
                            PresetVO.Resolution.builder()
                                .horizontal(request.getResolutionParams().getWidth())
                                .vertical(request.getResolutionParams().getHeight())
                                .build())
                        .html5(
                            request.getPaintChartOnPictureParams().getPaintWidgetOnPicture()
                                ? new PresetVO.Html5().setUrl(request.getPaintChartOnPictureParams().getHtml5Url())
                                : null)
                        .camera(request.getCamera())
                        .build())
                .build();
        return Try.of(() -> JsonUtil.writeValueAsString(presetVO))
            .onFailure(t -> log.error("Fail to serialize presetVO to json.", t))
            .getOrElse(StringUtils.EMPTY);
    }

    /**
     * Auto increases the last bit of release
     *
     * @param oldRelease
     * @return
     */
    private static String increaseRelease(String oldRelease) {
        var lastBitRelease = oldRelease.split("\\.")[oldRelease.split("\\.").length - 1];
        var lastBit = Integer.parseInt(lastBitRelease) + 1;
        return oldRelease.substring(0, oldRelease.length() - lastBitRelease.length()) + lastBit;
    }

    private String uploadCharacterThumbnail(String thumbnail, String userId, String projectId) {
        if (StringUtils.isBlank(thumbnail)) {
            return "";
        }
        if (thumbnail.contains("character_thumbnail.png")) { // hot fix
            return thumbnail;
        }
        try {
            return storageService
                .save(
                    Base64.getDecoder().decode(thumbnail),
                    String.join("/", userId, projectId, "character_thumbnail.png"))
                .toString();
        } catch (ResourceException e) {
            log.error("Failed to upload character thumbnail, ignore", e);
        }
        return "";
    }

    private ProjectModel save(ProjectModel projectModel) {
        return moreDbEditTransactionService.saveProjectModel(projectModel);
    }

    private void deleteByProjectId(ProjectModel projectModel) {
        moreDbEditTransactionService.deleteByProjectId(projectModel);
    }
}
