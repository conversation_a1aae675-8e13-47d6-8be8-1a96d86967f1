// Copyright (C) 2019 Baidu Inc. All rights reserved.
package com.baidu.acg.piat.digitalhuman.plat.service.impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

import com.baidu.acg.dh.user.client.UserClient;
import com.baidu.acg.dh.user.client.model.vo.UserCreateReqVO;
import com.baidu.acg.dh.user.client.model.vo.UserGetResVO;
import com.baidu.acg.dh.user.client.model.vo.UserUpdateReqVO;
import com.baidu.acg.piat.digitalhuman.common.access.AccessUser;
import com.baidu.acg.piat.digitalhuman.common.utils.JsonUtil;
import com.baidu.acg.piat.digitalhuman.plat.exception.DigitalHumanAccessException;
import com.baidu.acg.piat.digitalhuman.plat.service.UserService;

/**
 * UserServiceImpl
 *
 * <AUTHOR> Zhensheng(<EMAIL>)
 * @since 2019-09-19
 */
@Deprecated
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class UserServiceImpl implements UserService {

    private final UserClient userClient;

    @Override
    public AccessUser create(AccessUser userRequest) {
        log.debug("Accept user create request {} ", userRequest);

        if (userRequest.getUserId() != null) {
            throw new DigitalHumanAccessException("User id must be assigned by server");
        }
        if (StringUtils.isEmpty(userRequest.getName())) {
            throw new DigitalHumanAccessException("User name not present");
        }
        if (StringUtils.isEmpty(userRequest.getPassword())) {
            userRequest.setPassword(generate());
        }
        validateName(userRequest.getName());
        userRequest.setUserId(ObjectId.get().toHexString());
        String tag = "";
        try {
            if (userRequest.getTags() != null) {
                tag = JsonUtil.writeValueAsString(userRequest.getTags());
            }
        } catch (JsonProcessingException e) {
            log.warn("Fail to write user tags as string", e);
            throw new DigitalHumanAccessException("Fail to parse tags map.");
        }
        var userCreateReqVO = UserCreateReqVO.builder()
                .userName(userRequest.getName())
                .password(userRequest.getPassword())
                .description(userRequest.getDescription())
                .tag(tag)
                .build();
        var userResult = userClient.addUser(userCreateReqVO);
        if (!userResult.isSuccess()) {
            throw new DigitalHumanAccessException(userResult.getGlobalMessage());
        }
        return toAccessUser(userResult.getResult());
    }

    private String generate() {
        return UUID.randomUUID().toString();
    }

    @Override
    public AccessUser get(String userId) {
        log.debug("Accept user get request {} ", userId);
        var userGetResVO = getByUserId(userId);
        return toAccessUser(userGetResVO);
    }

    private UserGetResVO getByUserId(String userId) {
        var userResult = userClient.getUserById(userId);
        if (userResult.getResult() == null) {
            throw new DigitalHumanAccessException("User not existed");
        }
        return userResult.getResult();
    }

    @Override
    public AccessUser getByName(String name) {
        log.debug("Accept user get request {} ", name);

        var userResult = userClient.getUserByName(name);
        if (userResult.getResult() == null) {
            throw new DigitalHumanAccessException("User not existed");
        }
        log.debug("Success to get user={} by name", userResult.getResult());
        return toAccessUser(userResult.getResult());
    }

    @Override
    public AccessUser getByIamUserId(AccessUser accessUser) {
        log.debug("Accept user get by iam userId request {} ", accessUser);
        return null;

        // only support one tag query
//        if (StringUtils.isEmpty(accessUser.getIamUserId())) {
//            throw new DigitalHumanAccessException("IAM.userId cannot be empty.");
//        }
//        var optional = userRepository.findByIamUserId(accessUser.getIamUserId());
//        if (optional.isEmpty()) {
//            throw new DigitalHumanAccessException("User not existed");
//        }
//        return optional.get().toAccessUser(false);
    }

    @Override
    public AccessUser update(String userId, AccessUser userRequest) {
        log.debug("Accept user update request {} {}", userId, userRequest);

        getByUserId(userId);

        UserUpdateReqVO updateReqVO = new UserUpdateReqVO();
        if (!StringUtils.isEmpty(userRequest.getName())) {
            updateReqVO.setUserName(userRequest.getName());
        }
        if (!StringUtils.isEmpty(userRequest.getPassword())) {
            updateReqVO.setPassword(userRequest.getPassword());
        }
        if (!StringUtils.isEmpty(userRequest.getDescription())) {
            updateReqVO.setDescription(userRequest.getDescription());
        }
        if (!StringUtils.isEmpty(userRequest.getRole())) {
            updateReqVO.setRole(userRequest.getRole());
        }
        if (!CollectionUtils.isEmpty(userRequest.getTags())) {
            try {
                updateReqVO.setTag(JsonUtil.writeValueAsString(userRequest.getTags()));
            } catch (JsonProcessingException e) {
                log.warn("");
            }
        }
        var userResult = userClient.update(updateReqVO);
        if (!userResult.isSuccess()) {
            throw new DigitalHumanAccessException(userResult.getGlobalMessage());
        }
        return toAccessUser(userResult.getResult());
    }

    @Override
    public void delete(String userId) {
        log.debug("Accept user delete request {} ", userId);

        var user = getByUserId(userId);
        userClient.delete(user.getUserName());
    }


    @Override
    public AccessUser validate(AccessUser userToValidate) {
        log.debug("Accept user validate request {} ", userToValidate);

        if (StringUtils.isEmpty(userToValidate.getUserId())) {
            throw new DigitalHumanAccessException("user id not present");
        }
        if (StringUtils.isEmpty(userToValidate.getPassword())) {
            throw new DigitalHumanAccessException("password not present");
        }
        var user = getByUserId(userToValidate.getUserId());
        var passwordMd5 = DigestUtils.md5Hex(userToValidate.getName() + userToValidate.getPassword());
        if (!passwordMd5.equals(user.getPassword())) {
            throw new DigitalHumanAccessException("user password incorrect");
        }
        return toAccessUser(user);
    }

    @Override
    public AccessUser validateByName(AccessUser userToValidate) {
        log.debug("Accept user name validate request {} ", userToValidate);

        if (StringUtils.isEmpty(userToValidate.getName())) {
            throw new DigitalHumanAccessException("user name not present");
        }
        if (StringUtils.isEmpty(userToValidate.getPassword())) {
            throw new DigitalHumanAccessException("password not present");
        }
        var user = getByName(userToValidate.getName());
        var passwordMd5 = DigestUtils.md5Hex(userToValidate.getName() + userToValidate.getPassword());
        log.debug("passwordMd5={}", passwordMd5);
        if (!passwordMd5.equals(user.getPassword())) {
            throw new DigitalHumanAccessException("user password incorrect");
        }
        return user;
    }

    private void validateName(String name) {
        var userResult = userClient.getUserByName(name);
        if (userResult.getResult() != null) {
            throw new DigitalHumanAccessException("User name existed");
        }
    }

    private AccessUser toAccessUser(UserGetResVO request) {
        Map<String, String> tags = new HashMap<>();
        try {
            if (StringUtils.isNotEmpty(request.getTag())) {
                tags = JsonUtil.readValue(request.getTag(), new TypeReference<Map<String, String>>() {
                });
            }
        } catch (IOException e) {
            log.warn("Fail to read user tag as tagsMap", e);
        }
        return AccessUser.builder()
                .name(request.getUserName())
                .password(request.getPassword())
                .description(request.getDescription())
                .userId(request.getUid())
                .tags(tags)
                .role(request.getRole())
                .createTime(request.getCreateTime().toString())
                .updateTime(request.getUpdateTime().toString())
                .build();
    }
}
