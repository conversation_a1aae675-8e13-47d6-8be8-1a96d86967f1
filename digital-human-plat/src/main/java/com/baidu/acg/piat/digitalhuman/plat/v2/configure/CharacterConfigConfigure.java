package com.baidu.acg.piat.digitalhuman.plat.v2.configure;

import com.baidu.acg.piat.digitalhuman.plat.service.OptLogService;
import com.baidu.acg.piat.digitalhuman.plat.service.ProjectService;
import com.baidu.acg.piat.digitalhuman.plat.service.impl.MoreDbEditTransactionServiceImpl;
import com.baidu.acg.piat.digitalhuman.plat.v2.service.CharacterConfigRefService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import com.baidu.acg.piat.digitalhuman.common.utils.IDGenerator;
import com.baidu.acg.piat.digitalhuman.plat.service.CharacterService;
import com.baidu.acg.piat.digitalhuman.plat.v2.repository.CharacterConfigRepository;
import com.baidu.acg.piat.digitalhuman.plat.v2.service.CharacterConfigService;
import com.baidu.acg.piat.digitalhuman.plat.v2.service.impl.CharacterConfigServiceImpl;
import com.baidu.acg.piat.digitalhuman.storage.service.StorageService;
import org.springframework.context.annotation.Lazy;

@Configuration
@RequiredArgsConstructor
public class CharacterConfigConfigure {

    private final CharacterConfigRepository characterConfigRepository;

    private final MoreDbEditTransactionServiceImpl moreDbEditTransactionService;

    private final IDGenerator idGenerator;

    private final StorageService storageService;

    private final CharacterService characterService;

    private final CharacterConfigRelatedServiceConfig characterConfigRelatedServiceConfig;

    private final OptLogService optLogService;

    @Lazy
    @Autowired
    private CharacterConfigRefService characterConfigRefService;

    @Lazy
    @Autowired
    private ProjectService projectService;

    @Bean
    public CharacterConfigService characterConfigService() {
        CharacterConfigServiceImpl characterConfigService = new CharacterConfigServiceImpl(characterConfigRepository,
                moreDbEditTransactionService, idGenerator, storageService, characterService, projectService,
                characterConfigRelatedServiceConfig, optLogService
                , characterConfigRefService);
        return characterConfigService;
    }

}
