server:
  port: 8084
spring:
  application:
    name: digital-human-plat
  profiles:
    active: production
  jpa:
    database: MYSQL
    show-sql: false
    open-in-view: false
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: **********************************************************************************************************************************************************************************
    username: root
    password: 123456
    maxActive: 100
    validationInterval: 5000
    timeBetweenEvictionRunsMillis: 2000
    maxWait: 15000

digitalhuman:
  prometheus:
    config:
      url: http://localhost:9090
  access:
    app:
      generate-config:
        env-prefix: s
        retry-times: 3
        retry-sleep-millis: 100
  character:
    bos:
      accessKeyId: 931b804661f2462a9ae36eea84357241
      secretAccessKey: ********************************
      bucket: digital-human-material
      retryTimes: 1
      retrySleepMillis: 100
      urlExpireSeconds: 2592000
      host: https://bj.bcebos.com/v1
    image:
      types:
        - name: SYSTEM
          label: 2D
        - name: A2A
          label: 2D
        - name: CHINA_MOBILE_P4
          label: 2D
        - name: SPDB
          label: 2D
        - name: XMOV_CARTOON
          label: 2D
        - name: UE4_RENDER_HORIZONTAL
          label: 3D
          userId: 5e99956446a228000725b452
          appId: 3031a3dc-2f0b-4c27-996c-f06d0bf0cbe4
        - name: UE4_RENDER_VERTICAL
          label: 3D
          userId: 5e99956446a228000725b452
          appId: 2a6991fc-4aab-46f1-81a1-4300e1b88574
  grpc:
    enabled: false
    serverConfig:
      port: 8085
      terminateAwaitSeconds: 3