package com.baidu.acg.piat.digitalhuman.plat.service.impl;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

import java.lang.reflect.Method;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.Collections;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;
import org.assertj.core.util.Lists;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;

import com.baidu.acg.piat.digitalhuman.common.background.BackgroundImage;
import com.baidu.acg.piat.digitalhuman.common.entity.CharacterModel;
import com.baidu.acg.piat.digitalhuman.common.exception.DigitalHumanCommonException;
import com.baidu.acg.piat.digitalhuman.common.project.AsrPartEvent;
import com.baidu.acg.piat.digitalhuman.common.project.BotParams;
import com.baidu.acg.piat.digitalhuman.common.project.Camera;
import com.baidu.acg.piat.digitalhuman.common.project.CharacterParams;
import com.baidu.acg.piat.digitalhuman.common.project.ConversationConfig;
import com.baidu.acg.piat.digitalhuman.common.project.Credentials;
import com.baidu.acg.piat.digitalhuman.common.project.FigureCutParams;
import com.baidu.acg.piat.digitalhuman.common.project.HotWordReplaceReg;
import com.baidu.acg.piat.digitalhuman.common.project.PaintChartOnPictureParams;
import com.baidu.acg.piat.digitalhuman.common.project.PaintSubtitleOnPictureParams;
import com.baidu.acg.piat.digitalhuman.common.project.Project;
import com.baidu.acg.piat.digitalhuman.common.project.PrologueParams;
import com.baidu.acg.piat.digitalhuman.common.project.PullbackConfig;
import com.baidu.acg.piat.digitalhuman.common.project.PullbackMode;
import com.baidu.acg.piat.digitalhuman.common.project.ResolutionParams;
import com.baidu.acg.piat.digitalhuman.common.project.TtsParams;
import com.baidu.acg.piat.digitalhuman.common.project.UserInactiveConfig;
import com.baidu.acg.piat.digitalhuman.plat.dao.ProjectOnlineRepository;
import com.baidu.acg.piat.digitalhuman.plat.dao.ProjectRepository;
import com.baidu.acg.piat.digitalhuman.plat.model.project.ProjectModel;
import com.baidu.acg.piat.digitalhuman.plat.service.BackgroundService;
import com.baidu.acg.piat.digitalhuman.plat.service.CharacterService;
import com.baidu.acg.piat.digitalhuman.plat.v2.service.CharacterConfigService;
import com.baidu.acg.piat.digitalhuman.storage.service.StorageService;

/**
 * ProjectServiceImplTest
 *
 * <AUTHOR>
 * @since 2020/07/30
 */
public class ProjectServiceImplTest {

    @Mock
    private ProjectRepository repository;

    @Mock
    private ProjectOnlineRepository projectOnlineRepository;

    @Mock
    private BackgroundService backgroundService;

    @Mock
    private CharacterService characterService;

    @Mock
    private CharacterConfigService characterConfigService;

    @Mock
    private StorageService storageService;

    @Mock
    private MoreDbEditTransactionServiceImpl moreDbEditTransactionService;

    @InjectMocks
    private ProjectServiceImpl projectService;

    private Project project;

    private ProjectModel projectModel;

    private CharacterModel characterMeta;

    private static final String testProjectId = "projectId";
    private static final String testRelease = "latest";
    private static final String testUserId = "userId";
    private static final String testName = "name";
    private static final ZonedDateTime now = ZonedDateTime.now();
    private static final String testBackgroundId = "backgroundId";
    private static final String testBackgroundUrl =
            "https://bj.bcebos.com/v1/digital-human-material/test/character/yidong/background.jpg";
    private static final String testPreset = "{\"character\":{\"clothing\":{\"style\":0},\"hair\":{\"style\":2},\"offset\": {\"x\":0, \"y\": 0},\"badge\":{\"style\":0},\"nod\":{\"scale\":0,\"speed\":1}},\"display\":{\"resolution\":{\"horizontal\":540,\"vertical\":960},\"background\":{\"type\":\"image\",\"source\":\"http\",\"value\":\"https://bj.bcebos.com/v1/digital-human-material/test/character/yidong/background.jpg\"}}}";


    @BeforeEach
    public void init() {
        MockitoAnnotations.initMocks(this);
        project = Project.builder()
                .isDefault(false)
                .id("projectId")
                .name("name")
                .userId("userId")
                .backgroundImageId("backgroundId")
                .backgroundImageUrl("backgroundImageUrl")
                .characterImage("UE4_RENDER_VERTICAL")
                .botParams(BotParams.builder()
                        .type("NGD")
                        .credentials(Credentials.builder().token("token").build())
                        .build())
                .resolutionParams(ResolutionParams.builder()
                        .width(540)
                        .height(960)
                        .kbpsBitrate(2000L).build())
                .ttsParams(TtsParams.builder().pitch("5").speed("5").volume("5").person("5117").build())
                .figureCutParams(FigureCutParams.builder()
                        .cutHeightPercent("100")
                        .cutWidthPercent("50")
                        .cutXPercent("30")
                        .cutYPercent("100")
                        .positionBottomYPercent("0")
                        .positionCenterXPercent("100")
                        .widthRatio("50")
                        .build())
                .paintChartOnPictureParams(PaintChartOnPictureParams.builder()
                        .paintChartOnPicture(true)
                        .paintWidgetOnPicture(true)
                        .html5Url("html5Url")
                        .build())
                .paintSubtitleOnPictureParams(PaintSubtitleOnPictureParams.builder()
                        .subtitleTTL(-1)
                        .subtitleColor("")
                        .subtitleFontSize(20)
                        .subtitleMarginPx(20)
                        .subtitleBackgroundColor("red")
                        .paintSubtitleOnPicture(true)
                        .subtitleSplittable(true)
                        .build())
                .conversationConfig(ConversationConfig.builder()
                        .pullbackSwitch(true)
                        .pullbackTriggerRound(10)
                        .pullbackConfigList(Collections.singletonList(
                                PullbackConfig.builder()
                                        .percentage(10)
                                        .directGuidePercentage(100)
                                        .enterGuidePercentage(0)
                                        .directGuideSpeeches(Collections.singletonList("您好，欢迎光临！"))
                                        .pullbackMode(PullbackMode.DIRECT_GUIDE.name())
                                .build()))
                        .build())
                .hotWords(HotWordReplaceReg.builder().matches(Map.of("5g", "5G")).build())
                .userInactive(UserInactiveConfig.builder()
                        .speak("请问还有什么可以帮您？")
                        .thresholdSeconds(120)
                        .remindTimes(2).build())
                .prologueParams(PrologueParams.builder()
                        .contents("您好，我是数字人苏苏")
                        .showPrologue(true).build())
                .hitShieldReply(Collections.singletonList("我擦"))
                .asrPartEvent(AsrPartEvent.builder().build())
                .apiVersion(1)
                .build();

        projectModel = ProjectModel.builder()
                .projectId("projectId")
                .name("ceshi")
                .characterImage("UE4_RENDER_VERTICAL")
                .userId("userId")
                .projectVersion("10200")
                .preset(testPreset)
                .asrPartEvent(AsrPartEvent.builder().build())
                .createTime(ZonedDateTime.now())
                .updateTime(ZonedDateTime.now())
                .build();
        characterMeta = CharacterModel.builder()
                .name("UE4_RENDER_VERTICAL")
                .appId("appId")
                .appKey("appKey")
                .label("3D")
                .apiVersion(1)
                .build();
        when(characterService.selectByType(anyString(), anyInt())).thenReturn(characterMeta);
        when(moreDbEditTransactionService.saveProjectModel(any())).thenReturn(projectModel);
    }

    @Test
    public void generatePresetTest() throws Exception {
        ProjectServiceImpl service = new ProjectServiceImpl(null, null, null
                ,null, null, null, null, null, null, null, null);

        Method method = service.getClass().getDeclaredMethod("generatePreset", ProjectModel.class);
        method.setAccessible(true);

        var characterParams = new CharacterParams();
        characterParams.setFacial(new CharacterParams.Facial()
                .setLineament(new CharacterParams.Facial.Lineament().setChinShape(1.0)));

        var paintChartOnPictureParams = new PaintChartOnPictureParams();
        paintChartOnPictureParams.setPaintWidgetOnPicture(true);
        paintChartOnPictureParams.setHtml5Url("html5Url");

        ProjectModel project = ProjectModel.builder()
                .camera(Camera.builder().id(22).build())
                .backgroundImageUrl("aUrl")
                .characterParams(characterParams)
                .paintChartOnPictureParams(paintChartOnPictureParams)
                .build();
        String preset = (String) method.invoke(service, project);

        String expect = "{\"character\":{\"hair\":{\"style\":0},\"clothing\":{\"style\":0},\"offset\":" +
                "{\"x\":0.0,\"y\":0.0,\"z\":1.0,\"timeConsumingMs\":0,\"look_front\":false},\"badge\":" +
                "{\"style\":0},\"shoe\":{\"style\":0},\"facial\":{\"lineament\":{\"face_fat\":0.0,\"chin_shape\":1.0}" +
                ",\"eyes\":{\"size\":0.0,\"distance\":0.0,\"rotation\":0.0,\"almond\":0.0,\"amorous\":0.0," +
                "\"phoenix\":0.0},\"eyebrow\":{\"x\":0.0,\"y\":0.0},\"nose\":{\"len\":0.0,\"broad\":0.0," +
                "\"shape\":0.0,\"wing\":0.0},\"mouth\":{\"size\":0.0,\"pos\":0.0,\"upper_lip\":0.0," +
                "\"lower_lip\":0.0}},\"makeup\":{\"eyebrow\":{\"intensity\":0.0},\"eyes\":{\"shadow_intensity\":0.0," +
                "\"shadow_hua\":0.0},\"lipstick\":{\"intensity\":0.0,\"rough\":0.0}},\"glasses\":{\"id\":0}," +
                "\"ear_trinket\":{\"id\":0}},\"display\":{\"resolution\":{\"horizontal\":540,\"vertical\":960}," +
                "\"background\":{\"type\":\"image\",\"source\":\"http\",\"value\":\"aUrl\"},\"bitrate\":1200," +
                "\"camera\":{\"id\":22},\"html5\":{\"url\":\"html5Url\"}}}";
        Assertions.assertEquals(expect, preset);
    }

    @Test
    void create() {

        project.setUserId(null);
        Throwable throwable = assertThrows(DigitalHumanCommonException.class, () -> projectService.create(project));
        assertEquals(throwable.getLocalizedMessage(), "用户ID不能为空");

        project.setUserId(testUserId);

        when(characterService.selectByType(anyString(), anyInt())).thenReturn(characterMeta);
        var backgroundImage = BackgroundImage.builder()
                .id("id")
                .name("name")
                .imageUrl(testBackgroundUrl)
                .description("description")
                .build();
        when(backgroundService.get(anyString())).thenReturn(Optional.of(backgroundImage));
        when(repository.save(any())).thenReturn(projectModel);
        var result = projectService.create(project);
        compareProjectRequest2ProjectModel(result, projectModel);
    }

    @Test
    void get() {
        when(repository.findByUserIdAndNameAndApiVersionAndTypeOrderByCreateTimeDesc(anyString(), anyString(), anyInt(), any(), any())).thenReturn(Page.empty());
        assertThrows(DigitalHumanCommonException.class, () -> projectService.detail(testProjectId));

        when(characterService.selectByType(anyString())).thenReturn(Collections.singletonList(characterMeta));
        when(repository.findByUserIdAndNameAndApiVersionAndTypeOrderByCreateTimeDesc(anyString(), anyString(), anyInt(), any(), any()))
                .thenReturn(new PageImpl(Lists.newArrayList(projectModel)));
        var project = projectService.getByUserIdNameAndVersion(testUserId, testName, testRelease, 1);

        assert (project != null);
        compareProjectRequest2ProjectModel(project, projectModel);

        when(repository.findByUserIdAndNameAndProjectVersionAndApiVersionAndType(anyString(), anyString(), anyString(), anyInt(), anyString()))
                .thenReturn(Optional.of(projectModel));
        project = projectService.getByUserIdNameAndVersion(testUserId, testName, "1.2.0", 1);
        assert (project != null);
        compareProjectRequest2ProjectModel(project, projectModel);

        when(repository.findByProjectId(anyString())).thenReturn(Optional.of(projectModel));
        project = projectService.detail(testProjectId);
        assert (project != null);
        compareProjectRequest2ProjectModel(project, projectModel);
    }

    /**
     * 查找一个不存在的AppId
     */
    @Test
    void selectByAppIdCheckAppId() {
        var emptyAppId = "empty_app_id";
        when(repository.findByUserIdAndIsDefaultAndTypeOrderByCreateTimeDesc(anyString(), anyInt(), any())).thenReturn(Page.empty());
        when(repository.findByUserIdAndIsDefaultAndApiVersionAndTypeOrderByCreateTimeDesc(
                anyString(), anyInt(), anyInt(), any())).thenReturn(Page.empty());
        var result = projectService.list(emptyAppId, 0, 1, 20);

        assertEquals(0, result.getPage().getTotalCount());
        assertEquals(0, result.getPage().getResult().size());

        when(repository.findByUserIdAndIsDefaultAndApiVersionAndTypeOrderByCreateTimeDesc(anyString(), anyBoolean() == true ? 1 : 0, anyInt(), any())).thenReturn(Page.empty());
        result = projectService.list(emptyAppId, 2, 1, 20);

        assertEquals(0, result.getPage().getTotalCount());
        assertEquals(0, result.getPage().getResult().size());
    }

    @Test
    void update() {
        when(characterService.selectByType(anyString(), anyInt())).thenReturn(characterMeta);
        when(repository.findByUserIdAndNameAndProjectVersionAndApiVersionAndType(anyString(), anyString(), eq("1.1"), anyInt(), anyString()))
                .thenReturn(Optional.of(projectModel));
        when(repository.findByUserIdAndNameAndApiVersionAndTypeOrderByCreateTimeDesc(anyString(), anyString(), anyInt(), any(), any()))
                .thenReturn(new PageImpl(Lists.newArrayList(projectModel)));

        when(repository.save(any())).thenReturn(projectModel);
        var updated = projectService.updateByUserIdAndName(testUserId, testName, project);
        assertEquals(updated.getProjectVersion(), "1.2.0");

        project.setProjectVersion(null);
        updated = projectService.updateByUserIdAndName(testUserId, testName, project);
        assertEquals(updated.getProjectVersion(), "1.2.0");
    }

    @Test
    void updateNotExistPreset() {
        project = Project.builder()
                .id(testBackgroundId)
                .userId(testUserId)
                .name(UUID.randomUUID().toString())
                .characterImage("UE4_RENDER_VERTICAL")
                .createTime(DateTimeFormatter.ISO_OFFSET_DATE_TIME.format(now))
                .updateTime(DateTimeFormatter.ISO_OFFSET_DATE_TIME.format(now))
                .backgroundImageId(testBackgroundId)
                .description("description")
                .build();
        when(repository.findByUserIdAndNameAndApiVersionAndTypeOrderByCreateTimeDesc(anyString(), anyString(), anyInt(), any(), any()))
                .thenReturn(new PageImpl(Lists.newArrayList(projectModel)));
        assertThrows(DigitalHumanCommonException.class,
                () -> projectService.update("not_exist_project_id", project), "Project not existed");
    }

    private void compareProjectRequest2ProjectModel(Project request, ProjectModel model) {
        assertEquals(request.getBackgroundImageUrl(), model.getBackgroundImageUrl());
        assertEquals(request.getUserId(), model.getUserId());
        assertEquals(request.getDescription(), model.getDescription());
        assertEquals(request.getName(), model.getName());
        assertEquals(testPreset, model.getPreset());
    }

    @Test
    void testListWithMaxProjectVersion() {
        when(repository.findByUserIdAndMaxProjectVersionAndType(anyString(), anyInt(), anyInt(), any()))
                .thenReturn(new PageImpl(Lists.newArrayList(projectModel)));
        when(repository.findByUserIdAndNameAndProjectVersionAndType(any(), anyInt(), any()))
                .thenReturn(new PageImpl(Lists.newArrayList(projectModel)));
        when(repository.findByUserIdAndIsDefaultAndApiVersionAndTypeOrderByCreateTimeDesc(
                anyString(), anyInt(), anyInt(), any())).thenReturn(new PageImpl(Lists.newArrayList(projectModel)));
        var pageResponse = projectService.listWithMaxProjectVersion("userId", 0, 1, 20, "all");
        assertEquals(pageResponse.getPage().getResult().size(), 1);

        when(repository.findByUserIdAndApiVersionAndMaxProjectVersionAndType(anyString(), anyBoolean() == true ? 1 : 0, anyInt(), anyList(), any()))
                .thenReturn(new PageImpl(Lists.newArrayList(projectModel)));
        pageResponse = projectService.listWithMaxProjectVersion("userId", 1, 1, 20, "all");
        assertEquals(pageResponse.getPage().getResult().size(), 1);
    }
}
