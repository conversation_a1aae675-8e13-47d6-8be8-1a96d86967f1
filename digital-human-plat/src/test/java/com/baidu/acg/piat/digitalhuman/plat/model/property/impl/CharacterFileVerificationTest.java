package com.baidu.acg.piat.digitalhuman.plat.model.property.impl;

import com.baidu.acg.piat.digitalhuman.common.utils.FFmpegUtil;
import com.baidu.acg.piat.digitalhuman.common.utils.PathUtil;
import com.baidu.acg.piat.digitalhuman.plat.model.property.PropertyFileResponse;
import io.vavr.Tuple2;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.StandardCopyOption;

class CharacterFileVerificationTest {

    @InjectMocks
    private CharacterFileVerification characterFileVerification;

    private String url = "https://digital-human-material.bj.bcebos.com/female_shorts_bodytotal_up_out.zip";

    @BeforeEach
    private void init() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void verify() throws Exception {
        String fileName = "female_shorts_bodytotal_up_out.zip";
        Path tempDirectory = Path.of("/tmp/verify-character");
        File dirFile = new File(tempDirectory.toString());
        if (!dirFile.exists()) {
            dirFile.mkdirs();
        }

        try {
            Path tempFile = Path.of(tempDirectory.toString(), fileName);
            File file = urlToFile(url, tempFile);

            MultipartFile mockedMultipartFile = Mockito.mock(MultipartFile.class);
            Mockito.when(mockedMultipartFile.getOriginalFilename()).thenReturn(fileName);
            Mockito.when(mockedMultipartFile.getInputStream()).thenReturn(new FileInputStream(file));

            Tuple2<PropertyFileResponse, String> character = characterFileVerification.verify(
                    mockedMultipartFile, "Top");
            PropertyFileResponse response = character._1();
            Assertions.assertEquals("female_shorts_bodytotal_up", response.getPropertyId());
        } finally {
            PathUtil.delete(tempDirectory);
        }

    }

    private File urlToFile(String urlStr, Path destinationPath) throws Exception {
        URL url = new URL(urlStr);
        try (InputStream in = url.openStream()) {
            Files.copy(in, destinationPath, StandardCopyOption.REPLACE_EXISTING);
        }
        return destinationPath.toFile();
    }

}