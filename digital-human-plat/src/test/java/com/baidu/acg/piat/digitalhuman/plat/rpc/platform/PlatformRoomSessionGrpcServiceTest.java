package com.baidu.acg.piat.digitalhuman.plat.rpc.platform;

import com.baidu.acg.piat.digitalhuman.common.model.Dialog;
import com.baidu.acg.piat.digitalhuman.common.model.Dialog.ContentType;
import com.baidu.acg.piat.digitalhuman.common.room.Room;
import com.baidu.acg.piat.digitalhuman.common.room.session.RoomSession;
import com.baidu.acg.piat.digitalhuman.common.room.session.RoomSessionStatus;
import com.baidu.acg.piat.digitalhuman.common.session.SessionStatus;
import com.baidu.acg.piat.digitalhuman.plat.service.DialogService;
import com.baidu.acg.piat.digitalhuman.plat.service.RoomService;
import com.baidu.acg.piat.digitalhuman.plat.service.RoomSessionService;
import com.baidu.acg.piat.digitalhuman.platform.model.EventResponse;
import com.baidu.acg.piat.digitalhuman.platform.model.OpenRoomSession;
import com.baidu.acg.piat.digitalhuman.platform.model.RoomSessionEvent;
import com.baidu.acg.piat.digitalhuman.platform.model.RoomSessionEvent.SessionEvent;
import com.google.protobuf.Timestamp;
import io.grpc.stub.StreamObserver;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * The test for {@link com.baidu.acg.piat.digitalhuman.plat.rpc.platform.PlatformRoomSessionGrpcService}.
 *
 * <AUTHOR> Mao (<EMAIL>)
 */
public class PlatformRoomSessionGrpcServiceTest {

    @Mock
    private RoomService roomService;

    @Mock
    private DialogService dialogService;

    @Mock
    private RoomSessionService roomSessionService;

    @InjectMocks
    private PlatformRoomSessionGrpcService platformRoomSessionGrpcService;

    @BeforeEach
    public void setup() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void onEvent() {
        StreamObserver streamObserver = platformRoomSessionGrpcService.onEvent(new StreamObserver<EventResponse>() {
            @Override
            public void onNext(EventResponse eventResponse) {
                // empty for mock
            }

            @Override
            public void onError(Throwable throwable) {
                // empty for mock
            }

            @Override
            public void onCompleted() {
                // empty for mock
            }
        });

        when(roomService.getByUserIdAndRoomName("userId", "roomName")).thenReturn(buildRoom());
        when(roomSessionService.create(any())).thenReturn(buildRoomSession());
        streamObserver.onNext(buildRoomSessionEventOpen());
        streamObserver.onNext(buildRoomSessionEvent(SessionEvent.E_CLOSE));
        streamObserver.onNext(buildRoomSessionEvent(SessionEvent.E_CHARGE));
        streamObserver.onNext(buildRoomSessionEvent(SessionEvent.E_DISCHARGE));
        streamObserver.onNext(buildRoomSessionEvent(SessionEvent.E_REGISTER));
        streamObserver.onNext(buildRoomSessionEvent(SessionEvent.E_UNREGISTER));
        streamObserver.onNext(buildRoomSessionEventChat());

        streamObserver.onError(null);
        streamObserver.onCompleted();
        verify(roomSessionService, times(1)).updateStatus(any());
    }

    private Room buildRoom() {
        return Room.builder()
                .id("roomId")
                .build();
    }

    private RoomSessionEvent buildRoomSessionEventOpen() {
        return RoomSessionEvent.newBuilder()
                .setSessionId("sid")
                .setOpenBody(OpenRoomSession.newBuilder()
                        .setUserId("userId")
                        .setAppId("appId")
                        .setRoomName("roomName")
                        .build())
                .setEvent(SessionEvent.E_OPEN)
                .build();
    }

    private RoomSessionEvent buildRoomSessionEventChat() {
        return RoomSessionEvent.newBuilder()
                .setSessionId("sid")
                .setDialog(Dialog.newBuilder()
                        .setSpeaker("USER")
                        .setType(ContentType.TEXT)
                        .setContent("hi")
                        .setTimestamp(Timestamp.newBuilder().build())
                        .build())
                .setEvent(SessionEvent.E_CHAT)
                .build();
    }

    private RoomSessionEvent buildRoomSessionEvent(SessionEvent sessionEvent) {
        return RoomSessionEvent.newBuilder()
                .setSessionId("sid")
                .setEvent(sessionEvent)
                .build();
    }

    private RoomSession buildRoomSession() {
        return RoomSession.builder()
                .roomName("")
                .appId("aid")
                .sessionId("sid")
                .projectId("pid")
                .status(SessionStatus.OPEN)
                .build();
    }
}
