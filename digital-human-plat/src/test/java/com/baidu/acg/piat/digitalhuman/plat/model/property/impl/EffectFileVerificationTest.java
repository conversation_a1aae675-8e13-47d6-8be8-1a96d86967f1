package com.baidu.acg.piat.digitalhuman.plat.model.property.impl;

import com.baidu.acg.piat.digitalhuman.common.utils.PathUtil;
import com.baidu.acg.piat.digitalhuman.plat.model.property.PropertyFileResponse;
import io.vavr.Tuple2;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.StandardCopyOption;

class EffectFileVerificationTest {

    @InjectMocks
    private EffectFileVerification effectFileVerification;

    private String url = "https://digital-human-material.bj.bcebos.com/particle_snowflake.zip";

    @BeforeEach
    private void init() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 测试特效文件的正确性
     * @throws IOException
     */
    @Test
    public void verify() throws Exception {
        String fileName = "particle_snowflake.zip";
        Path tempDirectory = Path.of("/tmp/verify-effect");
        File dirFile = new File(tempDirectory.toString());
        if (!dirFile.exists()) {
            dirFile.mkdirs();
        }

        try {
            Path tempFile = Path.of(tempDirectory.toString(), fileName);
            File file = urlToFile(url, tempFile);

            MultipartFile mockedMultipartFile = Mockito.mock(MultipartFile.class);
            Mockito.when(mockedMultipartFile.getOriginalFilename()).thenReturn(fileName);
            Mockito.when(mockedMultipartFile.getInputStream()).thenReturn(new FileInputStream(file));
            Tuple2<PropertyFileResponse, String> effect = effectFileVerification.verify(mockedMultipartFile, "Effect");

            PropertyFileResponse response = effect._1();
            Assertions.assertEquals("particle_snowflake", response.getPropertyId());
        } finally {
            PathUtil.delete(tempDirectory);
        }
    }

    private File urlToFile(String urlStr, Path destinationPath) throws Exception {
        URL url = new URL(urlStr);
        try (InputStream in = url.openStream()) {
            Files.copy(in, destinationPath, StandardCopyOption.REPLACE_EXISTING);
        }
        return destinationPath.toFile();
    }

}