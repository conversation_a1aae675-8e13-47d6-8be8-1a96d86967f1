package com.baidu.acg.piat.digitalhuman.plat.model.property.impl;

import com.baidu.acg.piat.digitalhuman.common.utils.PathUtil;
import com.baidu.acg.piat.digitalhuman.plat.model.property.PropertyFileResponse;
import io.vavr.Tuple2;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.StandardCopyOption;

class AnimojiFileVerificationTest {

    @InjectMocks
    private AnimojiFileVerification animojiFileVerification;

    private String animojiUrl = "https://digital-human-material.bj.bcebos.com/chat_skeleton.zip";

    private String expressionUrl = "https://digital-human-material.bj.bcebos.com/chat_blendshape.zip";

    @BeforeEach
    private void init() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 验证动作文件的正确性
     * @throws IOException
     */
    @Test
    public void verifyAnimoji() throws Exception {
        String fileName = "chat_skeleton.zip";
        Path tempDirectory = Path.of("/tmp/verify-animoji");
        File dirFile = new File(tempDirectory.toString());
        if (!dirFile.exists()) {
            dirFile.mkdirs();
        }

        try {
            Path tempFile = Path.of(tempDirectory.toString(), fileName);
            File file = urlToFile(animojiUrl, tempFile);
            MultipartFile mockedMultipartFile = Mockito.mock(MultipartFile.class);
            Mockito.when(mockedMultipartFile.getOriginalFilename()).thenReturn(fileName);
            Mockito.when(mockedMultipartFile.getInputStream()).thenReturn(new FileInputStream(file));
            Tuple2<PropertyFileResponse, String> animoji = animojiFileVerification.verify(
                    mockedMultipartFile, "Animoji");

            PropertyFileResponse response = animoji._1();
            Assertions.assertEquals(194, response.getFrameCount());
            Assertions.assertEquals("chat_skeleton", response.getPropertyId());
        } finally {
            PathUtil.delete(tempDirectory);
        }


    }

    /**
     * 验证表情文件的校验
     * @throws IOException
     */
    @Test
    public void verifyExpression() throws Exception {
        String fileName = "chat_blendshape.zip";
        Path tempDirectory = Path.of("/tmp/verify-expression");
        File dirFile = new File(tempDirectory.toString());
        if (!dirFile.exists()) {
            dirFile.mkdirs();
        }

        try {
            Path tempFile = Path.of(tempDirectory.toString(), fileName);
            File file = urlToFile(expressionUrl, tempFile);

            MultipartFile mockedMultipartFile = Mockito.mock(MultipartFile.class);
            Mockito.when(mockedMultipartFile.getOriginalFilename()).thenReturn(fileName);
            Mockito.when(mockedMultipartFile.getInputStream()).thenReturn(new FileInputStream(file));
            Tuple2<PropertyFileResponse, String> expression = animojiFileVerification.verify(
                    mockedMultipartFile, "Expression");

            PropertyFileResponse response = expression._1();
            Assertions.assertEquals(194, response.getFrameCount());
            Assertions.assertEquals("chat_blendshape", response.getPropertyId());

        } finally {
            PathUtil.delete(tempDirectory);
        }
    }

    private File urlToFile(String urlStr, Path destinationPath) throws Exception {
        URL url = new URL(urlStr);
        try (InputStream in = url.openStream()) {
            Files.copy(in, destinationPath, StandardCopyOption.REPLACE_EXISTING);
        }
        return destinationPath.toFile();
    }
}