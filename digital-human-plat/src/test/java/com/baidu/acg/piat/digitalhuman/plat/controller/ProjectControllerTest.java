package com.baidu.acg.piat.digitalhuman.plat.controller;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import com.baidu.acg.piat.digitalhuman.plat.service.ProjectService;

/**
 * Created on 2021/1/25 11:35.
 *
 * <AUTHOR>
 */
public class ProjectControllerTest {

    @InjectMocks
    private ProjectController projectController;

    @Mock
    private ProjectService projectService;

    @BeforeEach
    public void init() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testDetail() {
        projectController.detail("projectId");
    }
}
