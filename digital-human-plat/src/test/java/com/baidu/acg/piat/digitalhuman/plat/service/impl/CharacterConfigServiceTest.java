package com.baidu.acg.piat.digitalhuman.plat.service.impl;

import com.baidu.acg.piat.digitalhuman.common.entity.CharacterConfig;
import com.baidu.acg.piat.digitalhuman.plat.v2.repository.CharacterConfigRepository;
import com.baidu.acg.piat.digitalhuman.plat.v2.service.CharacterConfigService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;

import java.util.ArrayList;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;

/**
 * <AUTHOR>
 * @since 2023/9/13 13:18
 */
public class CharacterConfigServiceTest {



    @Mock
    private CharacterConfigService characterConfigService;


    @Mock
    private CharacterConfigRepository characterConfigRepository;


    @BeforeEach
    public void init() {
        MockitoAnnotations.initMocks(this);
    }


    @Test
    public void testList1() throws Exception {

        Page<CharacterConfig> page =new PageImpl<CharacterConfig>(new ArrayList<>());
        when(characterConfigRepository.findByUserIdInAndTypeInAndNameIsLikeOrderByUpdateTimeDesc(anyList(), anyList(), anyString(), any())).thenReturn(new PageImpl<CharacterConfig>(new ArrayList<>()));
        when(characterConfigRepository.findByUserIdInAndTypeInAndNameIsLikeOrderByUpdateTimeDesc(any(),any(),any(),any())).thenReturn(page);
        when(characterConfigRepository.findByUserIdInAndTypeOrderByUpdateTimeDesc(any(), any(), any())).thenReturn(page);
        when(characterConfigRepository.findByUserIdInAndCharacterNameOrderByUpdateTimeDesc(
                any(), any(), any())).thenReturn(page);
        when(characterConfigRepository.findByUserIdInAndTypeInOrderByUpdateTimeDesc(any(), any(), any())).thenReturn(page);
        when(characterConfigRepository.findByUserIdInAndTypeOrderByUpdateTimeDesc(any(), any(), any())).thenReturn(page);
        when(characterConfigService.list(anyString(), anyString(), anyInt(), anyInt(), anyString(), anyString(),
                anyString(),any(), anyString(), anyString())).thenReturn(new PageImpl<CharacterConfig>(new ArrayList<>()));

        when(characterConfigService.list(anyString(), anyString(), anyInt(), anyInt(), anyString(), anyString(),
                anyString(),any(), anyString(), anyString())).thenReturn(new PageImpl<CharacterConfig>(new ArrayList<>()));
    }

}
