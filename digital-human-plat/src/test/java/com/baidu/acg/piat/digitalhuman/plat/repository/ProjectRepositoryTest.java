package com.baidu.acg.piat.digitalhuman.plat.repository;

import static org.junit.jupiter.api.Assertions.assertEquals;

import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.orm.jpa.DataJpaTest;
import org.springframework.data.domain.PageRequest;

import java.time.ZonedDateTime;

import com.baidu.acg.piat.digitalhuman.plat.dao.ProjectRepository;
import com.baidu.acg.piat.digitalhuman.plat.model.project.ProjectModel;

/**
 * Created on 2021/4/13 下午5:58.
 *
 * <AUTHOR>
 */
@DataJpaTest
@Slf4j
public class ProjectRepositoryTest {

    @Autowired
    private ProjectRepository projectRepository;


    @Test
    public void testFindByUserIdAndMaxProjectVersion() {
        ProjectModel projectModel1 = ProjectModel.builder()
                .name("name")
                .projectId("projectId1")
                .userId("userId")
                .projectVersion("10009")
                .isDefault(0)
                .createTime(ZonedDateTime.now())
                .apiVersion(1)
                .build();
        projectRepository.save(projectModel1);

        ProjectModel projectModel2 = ProjectModel.builder()
                .name("name")
                .projectId("projectId2")
                .userId("userId")
                .projectVersion("10025")
                .isDefault(0)
                .createTime(ZonedDateTime.now())
                .apiVersion(1)
                .build();
        projectRepository.save(projectModel2);

        ProjectModel projectModel3 = ProjectModel.builder()
                .name("test")
                .projectId("projectId3")
                .userId("userId")
                .projectVersion("10000")
                .isDefault(1)
                .createTime(ZonedDateTime.now())
                .apiVersion(1)
                .build();
        projectRepository.save(projectModel3);

        var result = projectRepository.findByUserIdAndMaxProjectVersionAndType("userId", 0, 1, PageRequest.of(0, 20));
        log.debug("result={}", result);
        assertEquals(result.getTotalElements(), 1);
        assertEquals(result.getContent().get(0).getProjectVersion(), "10025");
        assertEquals(result.getContent().get(0).getProjectId(), "projectId2");
        assertEquals(result.getContent().get(0).getName(), "name");
    }
}
