package com.baidu.acg.piat.digitalhuman.plat.v2.controller;

import com.baidu.acg.piat.digitalhuman.common.entity.CharacterConfig;
import com.baidu.acg.piat.digitalhuman.common.model.PageResponse;
import com.baidu.acg.piat.digitalhuman.plat.v2.service.CharacterConfigService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import java.util.ArrayList;
import java.util.Map;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * <AUTHOR>
 * @since 2023/9/13 11:27
 */
public class CharacterConfigControllerTest {
    @InjectMocks
    private CharacterConfigController characterConfigController;

    @Mock
    private CharacterConfigService characterConfigService;

    Page<CharacterConfig> page;

    CharacterConfig config = new CharacterConfig();

    @BeforeEach
    public void init() {
        MockitoAnnotations.initMocks(this);
        characterConfigController = new CharacterConfigController(characterConfigService);
    }

    @Test
    public void testList() throws Exception {

        when(characterConfigService.list(anyString(), anyString(), anyInt(), anyInt(), anyString(), anyString(),
                anyString(),any(), anyString(), anyString())).thenReturn(new PageImpl<CharacterConfig>(new ArrayList<>()));
        PageResponse<CharacterConfig> response = characterConfigController.list("testAccountId",
                Map.of("visibleCharacters","b"), 1, 10, "type", "name",
                "name","name", "characterId", "true");

    }

}
