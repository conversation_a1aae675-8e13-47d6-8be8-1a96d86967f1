package com.baidu.acg.piat.digitalhuman.plat.config;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.MockitoAnnotations;

/**
 * The test for {@link PlatformGrpcConfigure}.
 *
 * <AUTHOR> (maof<PERSON><PERSON>@baidu.com)
 */
public class PlatformGrpcConfigureTest {

    @InjectMocks
    private PlatformGrpcConfigure platformGrpcConfigure;

    @BeforeEach
    public void setup() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void init() {
        platformGrpcConfigure.init();
    }
}
