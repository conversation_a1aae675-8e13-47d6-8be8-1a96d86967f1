// Copyright (C) 2020 Baidu Inc. All rights reserved.
package com.baidu.acg.piat.digitalhuman.plat.model.room;

import org.apache.commons.lang.StringUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.time.ZonedDateTime;

import com.baidu.acg.piat.digitalhuman.common.room.Room;
import com.baidu.acg.piat.digitalhuman.common.room.RoomStatus;

/**
 * RoomModelTest
 *
 * <AUTHOR> (<EMAIL>)
 */
class RoomModelTest {
    private RoomModel roomModel;

    private Room room;

    @BeforeEach
    public void init() {
        roomModel = RoomModel.builder()
                .createTime(ZonedDateTime.now())
                .updateTime(ZonedDateTime.now())
                .build();
        room = roomModel.toRoom();
    }

    @Test
    void getStatusName() {
        String statusName = roomModel.getStatusName();
        roomModel.setStatusName(statusName);

        Assertions.assertTrue(StringUtils.isEmpty(statusName));
        Assertions.assertNull(roomModel.getStatus());

        roomModel.setStatus(RoomStatus.ENABLED);
        statusName = roomModel.getStatusName();
        roomModel.setStatusName(statusName);

        Assertions.assertTrue(StringUtils.isNotEmpty(statusName));
        Assertions.assertNotNull(roomModel.getStatus());
    }
}