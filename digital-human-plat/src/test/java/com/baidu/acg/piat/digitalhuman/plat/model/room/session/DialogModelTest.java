// Copyright (C) 2020 Baidu Inc. All rights reserved.
package com.baidu.acg.piat.digitalhuman.plat.model.room.session;

import org.apache.commons.lang.StringUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.time.ZonedDateTime;

import com.baidu.acg.piat.digitalhuman.common.session.ContentType;
import com.baidu.acg.piat.digitalhuman.common.session.Dialog;

/**
 * DialogModelTest
 *
 * <AUTHOR> (<EMAIL>)
 */
class DialogModelTest {
    private DialogModel dialogModel;

    private Dialog dialog;

    @BeforeEach
    public void init() {
        dialogModel = DialogModel.builder()
                .createTime(ZonedDateTime.now())
                .updateTime(ZonedDateTime.now())
                .build();
        dialog = dialogModel.toDialog();
    }

    @Test
    void getTypeName() {
        String typeName = dialogModel.getTypeName();
        dialogModel.setTypeName(typeName);

        Assertions.assertTrue(StringUtils.isEmpty(typeName));
        Assertions.assertNull(dialogModel.getType());

        dialogModel.setType(ContentType.TEXT);
        typeName = dialogModel.getTypeName();
        dialogModel.setTypeName(typeName);

        Assertions.assertTrue(StringUtils.isNotEmpty(typeName));
        Assertions.assertNotNull(dialogModel.getType());
    }
}