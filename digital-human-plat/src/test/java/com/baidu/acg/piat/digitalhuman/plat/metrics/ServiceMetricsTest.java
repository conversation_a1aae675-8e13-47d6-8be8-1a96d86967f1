//package com.baidu.acg.piat.digitalhuman.plat.metrics;
//
//import io.prometheus.client.CollectorRegistry;
//import io.prometheus.client.Counter;
//import io.prometheus.client.Gauge;
//import io.prometheus.client.Histogram;
//import lombok.extern.slf4j.Slf4j;
//import org.junit.jupiter.api.BeforeEach;
//import org.junit.jupiter.api.Test;
//import org.mockito.InjectMocks;
//import org.mockito.MockitoAnnotations;
//import org.springframework.test.util.ReflectionTestUtils;
//
//import java.util.Arrays;
//import java.util.Collections;
//
//import static org.junit.jupiter.api.Assertions.assertEquals;
//
//@Slf4j
//public class ServiceMetricsTest {
//
//    @InjectMocks
//    private ServiceMetrics serviceMetrics;
//
//    private Counter totalSession;
//
//    private Counter totalDialog;
//
//    private Gauge activeSession;
//
//    private Histogram sessionLatencySeconds;
//
//    @BeforeEach
//    public void setup() {
//        MockitoAnnotations.initMocks(this);
//        CollectorRegistry.defaultRegistry.clear();
//        totalSession = Counter.build()
//                .namespace("digitalhuman")
//                .subsystem("session")
//                .name("session_total")
//                .labelNames("app_id")
//                .help("The total number of session finished.")
//                .register();
//
//        totalDialog = Counter.build()
//                .namespace("digitalhuman")
//                .subsystem("dialog")
//                .name("dialog_total")
//                .labelNames("app_id", "room_id", "session_id")
//                .help("The total number of dialog finished on the session.")
//                .register();
//
//        activeSession = Gauge.build()
//                .namespace("digitalhuman")
//                .subsystem("session")
//                .name("active_session_total")
//                .labelNames("app_id")
//                .help("The number of active sessions.")
//                .register();
//
//        sessionLatencySeconds = Histogram.build()
//                .namespace("digitalhuman")
//                .subsystem("session")
//                .name("session_latency")
//                .labelNames("app_id", "room_id")
//                .help("The latency of the session.")
//                .register();
//
//        ReflectionTestUtils.setField(serviceMetrics, "totalSession", totalSession);
//        ReflectionTestUtils.setField(serviceMetrics, "totalDialog", totalDialog);
//        ReflectionTestUtils.setField(serviceMetrics, "activeSession", activeSession);
//        ReflectionTestUtils.setField(serviceMetrics, "sessionLatencySeconds", sessionLatencySeconds);
//
//    }
//
//    @Test
//    public void testSessionCounter() {
//        serviceMetrics.recordTotalSession("appId1");
//        var totalSessionCollector = serviceMetrics.getTotalSessionCollector();
//        log.debug("totalSessionCollector={}", totalSessionCollector);
//        assertEquals(totalSessionCollector.get(0).samples.get(0).value, 1.0d);
//        assertEquals(totalSessionCollector.get(0).samples.get(0).labelNames, Collections.singletonList("app_id"));
//        assertEquals(totalSessionCollector.get(0).samples.get(0).labelValues, Collections.singletonList("appId1"));
//        assertEquals(totalSessionCollector.get(0).name, "digitalhuman_session_session_total");
//
//        serviceMetrics.recordTotalSession("appId2");
//        totalSessionCollector = serviceMetrics.getTotalSessionCollector();
//        log.debug("totalSessionCollector={}", totalSessionCollector);
//        assertEquals(totalSessionCollector.get(0).samples.get(1).value, 1.0d);
//        assertEquals(totalSessionCollector.get(0).samples.get(1).labelNames, Collections.singletonList("app_id"));
//        assertEquals(totalSessionCollector.get(0).samples.get(1).labelValues, Collections.singletonList("appId2"));
//
//        serviceMetrics.recordTotalSession("appId2");
//        totalSessionCollector = serviceMetrics.getTotalSessionCollector();
//        log.debug("totalSessionCollector={}", totalSessionCollector);
//        assertEquals(totalSessionCollector.get(0).samples.get(1).value, 2.0d);
//        assertEquals(totalSessionCollector.get(0).samples.get(1).labelNames, Collections.singletonList("app_id"));
//        assertEquals(totalSessionCollector.get(0).samples.get(1).labelValues, Collections.singletonList("appId2"));
//    }
//
//    @Test
//    public void testDialogCounter() {
//        serviceMetrics.recordTotalDialog("appId", "roomId", "sessionId");
//        var totalDialogCollector = serviceMetrics.getTotalDialogCollector();
//        log.debug("totalDialogCollector={}", totalDialogCollector);
//        assertEquals(totalDialogCollector.get(0).samples.get(0).value, 1.0d);
//        assertEquals(totalDialogCollector.get(0).samples.get(0).labelNames, Arrays.asList("app_id", "room_id", "session_id"));
//        assertEquals(totalDialogCollector.get(0).samples.get(0).labelValues, Arrays.asList("appId", "roomId", "sessionId"));
//        assertEquals(totalDialogCollector.get(0).name, "digitalhuman_dialog_dialog_total");
//
//        serviceMetrics.recordTotalDialog("appId2", "roomId2", "sessionId2");
//        totalDialogCollector = serviceMetrics.getTotalDialogCollector();
//        log.debug("totalDialogCollector={}", totalDialogCollector);
//        assertEquals(totalDialogCollector.get(0).samples.get(1).value, 1.0d);
//        assertEquals(totalDialogCollector.get(0).samples.get(1).labelNames, Arrays.asList("app_id", "room_id", "session_id"));
//        assertEquals(totalDialogCollector.get(0).samples.get(1).labelValues, Arrays.asList("appId2", "roomId2", "sessionId2"));
//
//        serviceMetrics.recordTotalDialog("appId2", "roomId2", "sessionId2");
//        totalDialogCollector = serviceMetrics.getTotalDialogCollector();
//        log.debug("totalDialogCollector={}", totalDialogCollector);
//        assertEquals(totalDialogCollector.get(0).samples.get(1).value, 2.0d);
//        assertEquals(totalDialogCollector.get(0).samples.get(1).labelNames, Arrays.asList("app_id", "room_id", "session_id"));
//        assertEquals(totalDialogCollector.get(0).samples.get(1).labelValues, Arrays.asList("appId2", "roomId2", "sessionId2"));
//    }
//
//    @Test
//    public void testSessionGauge() {
//        serviceMetrics.incActiveSession("appId");
//        var activeSessionCollector = serviceMetrics.getActiveSessionCollector();
//        log.debug("activeSessionCollector={}", activeSessionCollector);
//        assertEquals(activeSessionCollector.get(0).samples.get(0).value, 1.0d);
//        assertEquals(activeSessionCollector.get(0).samples.get(0).labelNames, Collections.singletonList("app_id"));
//        assertEquals(activeSessionCollector.get(0).samples.get(0).labelValues, Collections.singletonList("appId"));
//        assertEquals(activeSessionCollector.get(0).name, "digitalhuman_session_active_session_total");
//
//        serviceMetrics.decActiveSession("appId");
//        activeSessionCollector = serviceMetrics.getActiveSessionCollector();
//        log.debug("activeSessionCollector={}", activeSessionCollector);
//        assertEquals(activeSessionCollector.get(0).samples.get(0).value, 0.0d);
//        assertEquals(activeSessionCollector.get(0).samples.get(0).labelNames, Collections.singletonList("app_id"));
//        assertEquals(activeSessionCollector.get(0).samples.get(0).labelValues, Collections.singletonList("appId"));
//    }
//
//    @Test
//    public void testSessionLatency() {
//        serviceMetrics.recordSessionLatencySeconds("appId", "roomId", 1000);
//        var sessionLatencySecondsCollector = serviceMetrics.getSessionLatencySecondsCollector();
//        log.debug("sessionLatencySecondsCollector={}", sessionLatencySecondsCollector);
//        assertEquals(sessionLatencySecondsCollector.get(0).samples.size(), 17.0d);
//        assertEquals(sessionLatencySecondsCollector.get(0).samples.get(16).value, 1000);
//        assertEquals(sessionLatencySecondsCollector.get(0).name, "digitalhuman_session_session_latency");
//
//        serviceMetrics.recordSessionLatencySeconds("appId", "roomId", 2000);
//        sessionLatencySecondsCollector = serviceMetrics.getSessionLatencySecondsCollector();
//        log.debug("sessionLatencySecondsCollector={}", sessionLatencySecondsCollector);
//        assertEquals(sessionLatencySecondsCollector.get(0).samples.size(), 17.0d);
//        assertEquals(sessionLatencySecondsCollector.get(0).samples.get(16).value, 3000);
//    }
//}
