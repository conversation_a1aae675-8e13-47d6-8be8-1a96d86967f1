package com.baidu.acg.piat.digitalhuman.plat.model;

enum MixerCase {
    BACK_ASPECT_RATIO_GREATER("back_aspect_ratio_greater"),
    EQUAL_ASPECT_RATIO("equal_aspect_ratio"),
    FRONT_ASPECT_RATIO_GREATER("front_aspect_ratio_greater");

    private final String bosSubDirectory;

    /**
     * @param bosSubDirectory
     */
    MixerCase(final String bosSubDirectory) {
        this.bosSubDirectory = bosSubDirectory;
    }

    @Override
    public String toString() {
        return bosSubDirectory;
    }
}