// Copyright (C) 2020 Baidu Inc. All rights reserved.
package com.baidu.acg.piat.digitalhuman.plat.model.room.session;

import org.apache.commons.lang.StringUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.time.ZonedDateTime;

import com.baidu.acg.piat.digitalhuman.common.room.session.RoomSession;
import com.baidu.acg.piat.digitalhuman.common.session.SessionStatus;

/**
 * RoomSessionModelTest
 *
 * <AUTHOR> (<EMAIL>)
 */
class RoomSessionModelTest {
    private RoomSessionModel roomSessionModel;

    private RoomSession roomSession;

    @BeforeEach
    public void init() {
        roomSessionModel = RoomSessionModel.builder()
                .createTime(ZonedDateTime.now())
                .updateTime(ZonedDateTime.now())
                .build();
        roomSession = roomSessionModel.toRoomSession();
    }

    @Test
    void getStatusName() {
        String statusName = roomSessionModel.getStatusName();
        roomSessionModel.setStatusName(statusName);

        Assertions.assertTrue(StringUtils.isEmpty(statusName));
        Assertions.assertNull(roomSessionModel.getStatus());

        roomSessionModel.setStatus(SessionStatus.INIT);
        statusName = roomSessionModel.getStatusName();
        roomSessionModel.setStatusName(statusName);

        Assertions.assertTrue(StringUtils.isNotEmpty(statusName));
        Assertions.assertNotNull(roomSessionModel.getStatus());
    }
}