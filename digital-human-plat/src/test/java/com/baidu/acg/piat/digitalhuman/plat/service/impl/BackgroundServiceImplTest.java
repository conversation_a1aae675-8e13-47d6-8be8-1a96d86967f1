package com.baidu.acg.piat.digitalhuman.plat.service.impl;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;

import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.net.URL;
import java.time.ZonedDateTime;
import java.util.Base64;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

import javax.imageio.ImageIO;

import com.baidu.acg.piat.digitalhuman.common.background.BackgroundImage;
import com.baidu.acg.piat.digitalhuman.common.background.BackgroundUploadRequest;
import com.baidu.acg.piat.digitalhuman.common.exception.DigitalHumanCommonException;
import com.baidu.acg.piat.digitalhuman.plat.dao.BackgroundRepository;
import com.baidu.acg.piat.digitalhuman.plat.model.background.BackgroundModel;
import com.baidu.acg.piat.digitalhuman.storage.service.StorageService;

@Slf4j
class BackgroundServiceImplTest {

    @InjectMocks
    private BackgroundServiceImpl service;

    @Mock
    private BackgroundRepository backgroundRepository;

    @Mock
    private StorageService storageService;

    private static final String testBackgroundId = UUID.randomUUID().toString();
    private static final String testUserId = UUID.randomUUID().toString();
    private static final ZonedDateTime now = ZonedDateTime.now();
    private static final String testBackgroundUrl =
            "https://bj.bcebos.com/v1/digital-human-material/test/character/yidong/background.jpg";

    private static BackgroundModel baseBackgroundModel;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        baseBackgroundModel = BackgroundModel.builder()
                .backgroundId(testBackgroundId)
                .userId(testUserId)
                .name(UUID.randomUUID().toString() + ".jpg")
                .createTime(now)
                .updateTime(now)
                .url(testBackgroundUrl)
                .description("description")
                .isLiveBg(1)
                .build();
    }

    @Test
    void get() {
        assertThrows(NullPointerException.class, () -> {
            service.get(null);
        });

        // not null
        when(backgroundRepository.findByBackgroundId(anyString())).thenReturn(Optional.empty());
        var backgroundImage = service.get(testBackgroundId);
        assertTrue(backgroundImage.isEmpty());

        when(backgroundRepository.findByBackgroundId(anyString())).thenReturn(Optional.of(baseBackgroundModel));
        backgroundImage = service.get(testBackgroundId);
        assertTrue(backgroundImage.isPresent());
        compareBackgroundImage2BackgroundModel(backgroundImage.get(), baseBackgroundModel);
    }

    /**
     * 查找一个不存在的AppId
     */
    @Test
    void selectByAppIdCheckAppId() {
        var emptyAppId = "empty_app_id";
        when(backgroundRepository.findAllByUserIdOrderByCreateTimeDesc(anyString(), any()))
                .thenReturn(new PageImpl(Collections.emptyList()));
        var result = service.selectByUserId(emptyAppId, PageRequest.of(1, 20));
        assertEquals(0, result.getTotalElements());
        assertEquals(0, result.getContent().size());
    }

    @Test
    void create() throws IOException, StorageService.ResourceException, NoSuchFieldException, IllegalAccessException {
        var fileSuffix = BackgroundServiceImpl.class.getDeclaredField("fileSuffix");
        fileSuffix.setAccessible(true);
        fileSuffix.set(service, List.of("jpg"));

        var request = BackgroundUploadRequest.builder()
                .description(baseBackgroundModel.getDescription())
                .name(baseBackgroundModel.getName())
                .imageBase64(toBase64(ImageIO.read(new URL(baseBackgroundModel.getUrl())), "jpg"))
                .build();
        assertThrows(DigitalHumanCommonException.class, () -> service.create(request));

        request.setUserId(testUserId);
        request.setName(UUID.randomUUID().toString());
        assertThrows(DigitalHumanCommonException.class, () -> service.create(request));

        request.setName(baseBackgroundModel.getName());
        URL url = new URL("http://127.0.0.1:8080");
        when(storageService.save((byte[]) any(), any())).thenReturn(url);
        when(backgroundRepository.save(any())).thenReturn(baseBackgroundModel);
        var result = service.create(request);
        result.setImageUrl(baseBackgroundModel.getUrl());
        result.setId(baseBackgroundModel.getBackgroundId());
        compareBackgroundImage2BackgroundModel(result, baseBackgroundModel);

        request.setImageBase64(null);
        assertThrows(DigitalHumanCommonException.class, () -> service.create(request));

        request.setIsLiveBg(true);
        assertTrue(request.getIsLiveBg());
    }

    @Test
    void delete() {
        service.delete(baseBackgroundModel.getBackgroundId());
    }

    // region private method

    private void compareBackgroundImage2BackgroundModel(BackgroundImage image, BackgroundModel model) {
        assertEquals(image.getId(), model.getBackgroundId());
        assertEquals(image.getImageUrl(), model.getUrl());
        assertEquals(image.getUserId(), model.getUserId());
        assertEquals(image.getDescription(), model.getDescription());
        assertEquals(image.getName(), model.getName());
    }

    private BackgroundModel copy(BackgroundModel model) {
        var backgroundModel = new BackgroundModel();
        backgroundModel.setId(model.getId());
        backgroundModel.setBackgroundId(model.getBackgroundId());
        backgroundModel.setUserId(model.getUserId());
        backgroundModel.setName(model.getName());
        backgroundModel.setDescription(model.getDescription());
        backgroundModel.setUrl(model.getUrl());
        backgroundModel.setCreateTime(model.getCreateTime());
        backgroundModel.setUpdateTime(model.getUpdateTime());
        return backgroundModel;
    }

    private String toBase64(BufferedImage image, String formatName) throws IOException {
        final ByteArrayOutputStream out = new ByteArrayOutputStream();
        ImageIO.write(image, formatName, out);
        return Base64.getEncoder().encodeToString(out.toByteArray());
    }

    @Test
    public void newBackgroundFileName() {
        BackgroundUploadRequest request =
                BackgroundUploadRequest
                        .builder()
                        .imageBase64(Base64.getEncoder().encodeToString("aa".getBytes()))
                        .name("test.jpg").build();
        Assertions.assertTrue(
                service.newBackgroundFileName(request).matches("139D4D4C22C2B25585B0BDA8DD2591CD-[0-9]{13}.jpg"));
    }

    // endregion private method

}