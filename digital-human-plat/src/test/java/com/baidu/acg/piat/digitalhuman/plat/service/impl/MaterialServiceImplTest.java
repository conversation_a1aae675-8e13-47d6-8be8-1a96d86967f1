package com.baidu.acg.piat.digitalhuman.plat.service.impl;

import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import org.assertj.core.util.Lists;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.data.domain.Page;

import java.time.ZonedDateTime;
import java.util.Optional;

import com.baidu.acg.piat.digitalhuman.common.material.Material;
import com.baidu.acg.piat.digitalhuman.plat.dao.MaterialRepository;
import com.baidu.acg.piat.digitalhuman.plat.model.material.MaterialModel;

/**
 * Created on 2021/8/11 6:26 下午
 *
 * <AUTHOR>
 */

class MaterialServiceImplTest {

    @InjectMocks
    private MaterialServiceImpl materialService;

    @Mock
    private MaterialRepository repository;

    private Material material;

    private MaterialModel materialModel;

    @BeforeEach
    public void init() {
        MockitoAnnotations.initMocks(this);

        material = Material.builder()
                .userId("userid")
                .name("name")
                .type("type")
                .picUrl("picUrl")
                .content("content")
                .drml("drml")
                .build();

        materialModel = MaterialModel.builder()
                .userId("userid")
                .name("name")
                .createTime(ZonedDateTime.now())
                .updateTime(ZonedDateTime.now())
                .build();
    }

    @Test
    void create() {
        when(repository.save(any())).thenReturn(materialModel);
        materialService.create(material);
//        verify(repository, times(1)).save(materialModel);
    }

    @Test
    void delete() {
        materialService.delete(material.getId());
        verify(repository, times(1)).deleteByMaterialId(material.getId());
    }

    @Test
    void update() {
        when(repository.save(any())).thenReturn(materialModel);
        when(repository.findByMaterialId(any())).thenReturn(Optional.of(materialModel));
        materialService.update(material.getId(), material);
        verify(repository, times(1)).save(any());
    }

    @Test
    void detail() {
        when(repository.findByMaterialId(any())).thenReturn(Optional.of(materialModel));
        materialService.detail(material.getId());
        verify(repository, times(1)).findByMaterialId(material.getId());
    }

    @Test
    void list() {
        when(repository.save(any())).thenReturn(materialModel);

        when(repository.findAllByUserIdAndNameContainingAndTypeInAndPositionIdIn(anyString(), anyString(),
                anyList(), anyList(), any())).thenReturn(Page.empty());
        materialService.listByUserIdAndNameAndTypeAndPositionId("userid", "name",
                Lists.newArrayList("type"), Lists.newArrayList("positionId"), 1, 20);
        verify(repository, times(1)).findAllByUserIdAndNameContainingAndTypeInAndPositionIdIn(
                anyString(), anyString(), anyList(), anyList(), any());
    }

}