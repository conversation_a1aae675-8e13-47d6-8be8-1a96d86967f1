package com.baidu.acg.piat.digitalhuman.plat.service.impl;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import io.vavr.API;
import lombok.extern.slf4j.Slf4j;
import org.bson.types.ObjectId;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.Spy;
import org.springframework.data.domain.PageImpl;

import java.io.IOException;
import java.net.MalformedURLException;
import java.net.URL;
import java.time.ZonedDateTime;
import java.util.Collections;
import java.util.Optional;
import java.util.UUID;

import com.baidu.acg.piat.digitalhuman.common.access.AccessApp;
import com.baidu.acg.piat.digitalhuman.common.entity.CharacterModel;
import com.baidu.acg.piat.digitalhuman.common.exception.DigitalHumanCommonException;
import com.baidu.acg.piat.digitalhuman.common.quota.ResourceQuota;
import com.baidu.acg.piat.digitalhuman.common.utils.IDGenerator;
import com.baidu.acg.piat.digitalhuman.common.utils.ReflectUtil;
import com.baidu.acg.piat.digitalhuman.plat.Util;
import com.baidu.acg.piat.digitalhuman.plat.config.CharacterConfigure;
import com.baidu.acg.piat.digitalhuman.plat.dao.BackgroundRepository;
import com.baidu.acg.piat.digitalhuman.plat.dao.CharacterRepository;
import com.baidu.acg.piat.digitalhuman.plat.model.background.BackgroundModel;
import com.baidu.acg.piat.digitalhuman.storage.service.StorageService;
import com.baidu.acg.piat.digitalhuman.plat.service.AppService;

@Slf4j
class CharacterServiceImplTest {
    private static final int API_VERSION = 1;

    @InjectMocks
    private CharacterServiceImpl service;

    @Mock
    private CharacterRepository characterRepository;

    @Mock
    private BackgroundRepository backgroundRepository;

    @Mock
    private StorageService storageService;

    @Mock
    private AppService appService;

    @Mock
    private IDGenerator idGenerator;

    @Spy
    private CharacterConfigure.Config characterConfig;

    /**
     * 内存中的基础模型，（其他model均复制于它）
     */
    private CharacterModel metaModel;

    private BackgroundModel baseBackgroundModel;

    private static final String testCharacterImageType = "yidong";
    private static final String testCharacterName = "testCharacterName";
    private static final String testDescription = "testDescription";
    private static final String testCharacterId = UUID.randomUUID().toString();
    private static final ZonedDateTime now = ZonedDateTime.now();
    private static final String testBackgroundUrl =
            "https://bj.bcebos.com/v1/digital-human-material/test/character/yidong/background.jpg";
    private static final String testMaskImageUrl =
            "https://bj.bcebos.com/v1/digital-human-material/test/character/yidong/mask/00001.jpg";
    private static final String testThumbnailImageUrl =
            "https://bj.bcebos.com/v1/digital-human-material/test/character/yidong/front.jpg";
    private static final String testFrontImageUrl =
            "https://bj.bcebos.com/v1/digital-human-material/test/character/yidong/front.jpg";

    private static final String testBackgroundId = UUID.randomUUID().toString();
    private static final String testAppId = UUID.randomUUID().toString();


    @BeforeEach
    void setup() throws NoSuchFieldException, IllegalAccessException {
        MockitoAnnotations.initMocks(this);
        metaModel = CharacterModel.builder()
                .characterId(testCharacterId)
                .name(testCharacterName)
                .description(testDescription)
                .type(testCharacterImageType)
                .backgroundImageUrl(testBackgroundUrl)
                .maskImageUrl(testMaskImageUrl)
                .thumbnailImageUrl(testThumbnailImageUrl)
                .frontImageUrl(testFrontImageUrl)
                .createTime(now)
                .updateTime(now)
                .build();

        // background
        baseBackgroundModel = BackgroundModel.builder()
                .backgroundId(testBackgroundId)
                .userId(testAppId)
                .name(UUID.randomUUID().toString() + ".jpg")
                .createTime(now)
                .updateTime(now)
                .url(testBackgroundUrl)
                .description("description")
                .build();

        characterConfig = new CharacterConfigure.Config();
        characterConfig.setRoomLimits(100);
        ReflectUtil.setFieldValue(service, "appService", appService);

        when(idGenerator.generate()).thenReturn(testCharacterId);

    }

    @Test
    void create() throws IOException, StorageService.ResourceException {
        var tmpType = testCharacterImageType;

        var newRequest = CharacterModel.builder()
                .type(tmpType)
                .backgroundImageUrl(testBackgroundUrl)
                .description(testDescription)
                .frontImageUrl(testFrontImageUrl)
                .maskImageUrl(testMaskImageUrl)
                .name(testCharacterName)
                .build();
        when(appService.create(any(), anyBoolean()))
                .thenReturn(AccessApp.builder()
                        .userId("System")
                        .name(newRequest.getName() + "-"+ ObjectId.get().toHexString())
                        .description("Used by sce, please don't change")
                        .characterImage(newRequest.getType())
                        .resourceQuota(ResourceQuota.builder().roomLimits(100).build())
                        .build());
        URL url = new URL("http://127.0.0.1:8080");
        when(storageService.save((byte[]) any(), anyString())).thenReturn(url);
        when(characterRepository.save(any())).thenReturn(metaModel);
        var data = service.create(newRequest);

        assertFalse(newRequest.getSupportCallback() == 1);
        assertFalse(newRequest.getSupportRtcDatachannel() == 1);

        assertEquals(testCharacterId, data.getCharacterId());
        Util.assertExist(data.getThumbnailImageUrl());
        Util.assertExist(data.getBackgroundImageUrl());
        Util.assertExist(data.getThumbnailImageUrl());
        compare(data, newRequest);

        when(characterRepository.findByTypeAndApiVersion(anyString(), anyInt())).thenReturn(Optional.of(metaModel));
        Throwable throwable = assertThrows(DigitalHumanCommonException.class, () -> {
            service.create(newRequest);
        });
        assertEquals(throwable.getLocalizedMessage(), String.format("Character meta type:%s has existed", tmpType));
    }

    @Test
    void selectByType() {
        Throwable throwable = assertThrows(DigitalHumanCommonException.class, () -> {
            service.selectByType(null);
        });
        assertEquals(throwable.getLocalizedMessage(), "Character meta type cannot be empty");

        when(characterRepository.findByTypeAndApiVersion(anyString(), anyInt())).thenReturn(Optional.of(metaModel));
        var result = service.selectByType(testCharacterImageType, 1);
        compare(metaModel, result);

        verify(characterRepository, times(1)).findByTypeAndApiVersion(anyString(), anyInt());
    }

    @Test
    void selectAll() {
        when(characterRepository.findByApiVersionOrderByCreateTimeDesc(anyInt(), any()))
                .thenReturn(new PageImpl(Collections.emptyList()));
        var response = service.selectAll(1, 20);
        assertEquals(response.getPage().getTotalCount(), 0);
        assertEquals(response.getPage().getPageNo(), 1);
        assertEquals(response.getPage().getPageSize(), 20);
    }

    @Test
    void selectVisibleForSce() {
        when(characterRepository.findByApiVersionAndVisibleForSceOrderByCreateTimeDesc(anyInt(), anyInt(), any()))
                .thenReturn(new PageImpl(Collections.emptyList()));
        var response = service.selectVisibleForSce(true, 1, 20);
        assertEquals(response.getPage().getTotalCount(), 0);
        assertEquals(response.getPage().getPageNo(), 1);
        assertEquals(response.getPage().getPageSize(), 20);
    }

    @Test
    void deleteByType() {
        service.deleteByType(UUID.randomUUID().toString());
        verify(characterRepository, times(1)).deleteByType(anyString());
    }

    @Test
    void deleteById() {
        Throwable throwable = assertThrows(DigitalHumanCommonException.class, () -> {
            service.deleteById(UUID.randomUUID().toString());
        });
        assertEquals(throwable.getLocalizedMessage(), "Character meta not existed.");

        when(characterRepository.findByCharacterId(anyString())).thenReturn(Optional.of(metaModel));
        service.deleteById(UUID.randomUUID().toString());
        verify(characterRepository, times(2)).findByCharacterId(anyString());
        verify(characterRepository, times(1)).deleteByCharacterId(anyString());
    }

    @Test
    void updateByType() throws MalformedURLException, StorageService.ResourceException {
        var metaRequest = CharacterModel.builder()
                .name(testCharacterName)
                .description(testDescription)
                .build();

        Throwable throwable = assertThrows(DigitalHumanCommonException.class,
                () -> service.updateByType("", metaRequest, API_VERSION));
        assertEquals(throwable.getLocalizedMessage(), "Character meta type cannot be empty");

        when(characterRepository.findByTypeAndApiVersion(anyString(), anyInt())).thenReturn(Optional.ofNullable(null));
        throwable = assertThrows(DigitalHumanCommonException.class,
                () -> service.updateByType(testCharacterId, metaRequest, API_VERSION));
        assertEquals("Character meta not existed.", throwable.getLocalizedMessage());

        // do not change image
        when(characterRepository.findByTypeAndApiVersion(anyString(), anyInt())).thenReturn(Optional.of(metaModel));
        when(characterRepository.save(any())).thenReturn(metaModel);
        var result = service.updateByType(testCharacterImageType, metaRequest, API_VERSION);
        assertEquals(metaRequest.getName(), result.getName());
        assertEquals(metaRequest.getDescription(), result.getDescription());
        assertEquals(result.getThumbnailImageUrl(), metaModel.getThumbnailImageUrl());

        // change image
        URL url = new URL("http://127.0.0.1:8080");
        when(storageService.save((byte[]) any(), anyString())).thenReturn(url);
        metaRequest.setBackgroundImageUrl(testBackgroundUrl);
        service.updateByType(testCharacterImageType, metaRequest, API_VERSION);
    }

    @Test
    void updateById() throws MalformedURLException, StorageService.ResourceException {

        var metaRequest = CharacterModel.builder()
                .name(testCharacterName)
                .description(testDescription)
                .build();

        Throwable throwable = assertThrows(DigitalHumanCommonException.class,
                () -> service.updateById("", metaRequest));
        assertEquals(throwable.getLocalizedMessage(), "Character meta id cannot be empty");

        when(characterRepository.findByCharacterId(anyString())).thenReturn(Optional.ofNullable(null));
        throwable = assertThrows(DigitalHumanCommonException.class,
                () -> service.updateById(testCharacterId, metaRequest));
        assertEquals(throwable.getLocalizedMessage(), "Character meta not exist");

        // do not change image
        when(characterRepository.findByCharacterId(anyString())).thenReturn(Optional.of(metaModel));
        when(characterRepository.save(any())).thenReturn(metaModel);

        var result = service.updateById(testCharacterId, metaRequest);
        assertEquals(metaRequest.getName(), result.getName());
        assertEquals(metaRequest.getDescription(), result.getDescription());
        assertEquals(result.getThumbnailImageUrl(), metaModel.getThumbnailImageUrl());


        // change image
        URL url = new URL("http://127.0.0.1:8080");
        when(storageService.save((byte[]) any(), anyString())).thenReturn(url);
        metaRequest.setBackgroundImageUrl(testBackgroundUrl);
        metaRequest.setFrontImageUrl(testFrontImageUrl);
        metaRequest.setMaskImageUrl(testMaskImageUrl);
        service.updateById(testCharacterId, metaRequest);
//        throwable = assertThrows(DigitalHumanCommonException.class,
//                () -> service.updateById(testCharacterId, metaRequest));
//        assertEquals(throwable.getLocalizedMessage(), "Cannot read image content from url");
    }

    @Test
    void preview() {

        Throwable throwable = assertThrows(DigitalHumanCommonException.class, () ->
                service.preview(testCharacterImageType, testBackgroundId, API_VERSION));
        assertEquals(throwable.getLocalizedMessage(), String.format("Background : %s is not exist",
                testBackgroundId));

        when(backgroundRepository.findByBackgroundId(anyString())).thenReturn(Optional.of(baseBackgroundModel));
        throwable = assertThrows(DigitalHumanCommonException.class, () ->
                service.preview(testCharacterImageType, testBackgroundId, API_VERSION));
        assertEquals("Character meta not existed.", throwable.getLocalizedMessage());

        when(characterRepository.findByTypeAndApiVersion(anyString(), anyInt())).thenReturn(Optional.of(metaModel));
        var image = service.preview(testCharacterImageType, testBackgroundId, API_VERSION);

        assertNotNull(image);
    }

    // region private method

    private void compare(CharacterModel request, CharacterModel request1) {
        assertEquals(request1.getName(), request.getName());
        assertEquals(request1.getType(), request.getType());
        assertEquals(request1.getBackgroundImageUrl(), request.getBackgroundImageUrl());
        assertEquals(request1.getMaskImageUrl(), request.getMaskImageUrl());
        assertEquals(request1.getFrontImageUrl(), request.getFrontImageUrl());
        assertEquals(request1.getDescription(), request.getDescription());
        assertEquals(request1.getSupportCallback(), request.getSupportCallback());
        assertEquals(request1.getSupportRtcDatachannel(), request.getSupportRtcDatachannel());
    }

    private CharacterModel copy(CharacterModel model) {
        return CharacterModel.builder()
                .id(model.getId())
                .type(model.getType())
                .name(model.getName())
                .description(model.getDescription())
                .backgroundImageUrl(model.getBackgroundImageUrl())
                .frontImageUrl(model.getFrontImageUrl())
                .maskImageUrl(model.getMaskImageUrl())
                .thumbnailImageUrl(model.getThumbnailImageUrl())
                .createTime(model.getCreateTime())
                .updateTime(model.getUpdateTime())
                .build();
    }

    // endregion

}