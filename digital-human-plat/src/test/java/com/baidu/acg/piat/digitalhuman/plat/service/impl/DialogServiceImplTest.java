package com.baidu.acg.piat.digitalhuman.plat.service.impl;

import com.baidu.acg.piat.digitalhuman.common.constans.StatisticConstant;
import com.baidu.acg.piat.digitalhuman.common.session.Dialog;
import com.baidu.acg.piat.digitalhuman.plat.dao.DialogRepository;
import com.baidu.acg.piat.digitalhuman.plat.model.room.session.DialogModel;
import com.baidu.acg.piat.digitalhuman.plat.service.RoomService;
import com.baidu.acg.piat.digitalhuman.plat.service.SessionStatisticService;

import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.simple.SimpleMeterRegistry;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort.Direction;
import org.springframework.test.util.ReflectionTestUtils;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

import java.util.Optional;

/**
 * The test for {@link DialogServiceImpl}.
 *
 * <AUTHOR> Mao (<EMAIL>)
 */
public class DialogServiceImplTest {

    @Mock
    private SessionStatisticService sessionStatisticService;

    @Mock
    private DialogRepository dialogRepository;

    @InjectMocks
    private DialogServiceImpl dialogService;

    @Mock
    private RoomService roomService;

    @BeforeEach
    public void setup(){
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void create(){
        var model = buildDialogModel();
        when(dialogRepository.save(any())).thenReturn(model);

        assertEquals(buildDialog(), dialogService.create(buildDialog()));
    }

    @Test
    public void list(){
        when(dialogRepository.findBySessionIdOrderByCreateTimeDesc("sid", PageRequest.of(0,1, Direction.DESC, "timestamp")))
                .thenReturn(Page.empty());

        assertEquals(0, dialogService.listBySessionId("sid", 1,1).getPage().getTotalCount());
    }

    @Test
    public void countByAppIdAndTimestampBetween() {
        var res = dialogService.countByAppIdAndTimestampBetween("aid", 0L,0L);
        assertEquals(0, res.longValue());
    }

    private Dialog buildDialog(){
        return Dialog.builder()
                .roomId("roomId")
                .appId("appId")
                .speaker("speaker")
                .timestamp(10000)
                .sessionId("sid")
                .build();
    }

    private DialogModel buildDialogModel(){
        return DialogModel.builder()
                .roomId("roomId")
                .appId("appId")
                .speaker("speaker")
                .timestamp(10000)
                .sessionId("sid")
                .build();
    }
}
