package com.baidu.acg.piat.digitalhuman.plat.controller;

import com.baidu.acg.piat.digitalhuman.common.model.PageResponse;
import com.baidu.acg.piat.digitalhuman.common.session.Dialog;
import com.baidu.acg.piat.digitalhuman.plat.service.DialogService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import static org.mockito.Mockito.when;

/**
 * The test for {@link DialogController}.
 *
 * <AUTHOR> (<EMAIL>)
 */
public class DialogControllerTest {
    @Mock
    private DialogService dialogService;

    @InjectMocks
    private DialogController dialogController;

    @BeforeEach
    public void init() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void list() {
        when(dialogService.listByRoomId("roomId", 1, 10)).thenReturn(PageResponse.<Dialog>builder().build());
        when(dialogService.listByAppIdAndRoomName("appId", "roomName", 1, 10))
                .thenReturn(PageResponse.<Dialog>builder().build());
        dialogController.listByRoomId("roomId", "appId", "roomName", 1, 10);
        dialogController.listByRoomId("", "appId", "roomName", 1, 10);
        dialogController.listByRoomNameAndTimestampLessThan("test","i-phykdrjg5wxv5", (long) -1, "asc", 1, 10);
        dialogController.listByRoomNameAndTimestampLessThan("test","i-phykdrjg5wxv5", (long) -1, "asc", 1, 10);

    }
}
