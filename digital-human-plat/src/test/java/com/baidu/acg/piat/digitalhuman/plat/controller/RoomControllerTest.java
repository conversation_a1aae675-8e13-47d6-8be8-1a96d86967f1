package com.baidu.acg.piat.digitalhuman.plat.controller;

import com.baidu.acg.piat.digitalhuman.common.model.PageResponse;
import com.baidu.acg.piat.digitalhuman.common.model.PageResult;
import com.baidu.acg.piat.digitalhuman.common.room.Room;
import com.baidu.acg.piat.digitalhuman.plat.service.RoomService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.when;

/**
 * The test for {@link RoomController}.
 *
 * <AUTHOR> (<EMAIL>)
 */
public class RoomControllerTest {

    @Mock
    private RoomService roomService;

    @InjectMocks
    private RoomController roomController;

    @BeforeEach
    public void init() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void create() {
        when(roomService.create(buildRoom())).thenReturn(buildRoom());
        assertEquals(buildRoom(), roomController.create(buildRoom()).getResult());
    }

    @Test
    public void update() {
        when(roomService.update("roomId", buildRoom())).thenReturn(buildRoom());
        assertEquals(buildRoom(), roomController.update("roomId", buildRoom()).getResult());
    }

    @Test
    public void detail() {
        when(roomService.detail("roomId")).thenReturn(buildRoom());
        assertEquals(buildRoom(), roomController.detail("roomId").getResult());
    }

    public void list() {
        when(roomService.list("appId", null, null,1, 1)).thenReturn(PageResponse.<Room>builder()
                .page(PageResult.<Room>builder().build()).build());
        assertEquals(0, roomController.list("appId", null, null, 1, 1).getPage().getTotalCount());
    }

    private Room buildRoom() {
        return Room.builder()
                .id("roomId")
                .build();
    }
}
