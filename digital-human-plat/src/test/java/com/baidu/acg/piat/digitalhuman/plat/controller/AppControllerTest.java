package com.baidu.acg.piat.digitalhuman.plat.controller;

import static org.hamcrest.Matchers.containsString;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.web.servlet.MockMvc;

import com.baidu.acg.piat.digitalhuman.common.access.AccessApp;
import com.baidu.acg.piat.digitalhuman.plat.service.AppService;

/**
 * AppControllerTest
 *
 * <AUTHOR>
 * @since 2020/08/06
 */
@Disabled
@WebMvcTest(AppController.class)
public class AppControllerTest {
    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private AppService appService;

    @Test
    public void getDefaultAppByImageTest() throws Exception {
        String characterImage = "UE4";
        String appId = "appId01";
        AccessApp app = AccessApp.builder().appId(appId).build();
        Mockito.when(appService.findDefaultByCharacterImage(characterImage, 1)).thenReturn(app);
        this.mockMvc.perform(get("/api/digitalhuman/v1/access/app/default/UE4"))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(content().string(containsString(appId)));
    }

}
