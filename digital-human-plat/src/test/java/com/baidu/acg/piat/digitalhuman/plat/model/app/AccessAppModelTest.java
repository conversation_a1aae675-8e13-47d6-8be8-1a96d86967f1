// Copyright (C) 2020 Baidu Inc. All rights reserved.
package com.baidu.acg.piat.digitalhuman.plat.model.app;

import com.google.common.collect.Maps;
import org.apache.commons.lang.StringUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.time.ZonedDateTime;

import com.baidu.acg.piat.digitalhuman.common.access.AccessApp;
import com.baidu.acg.piat.digitalhuman.common.quota.ResourceQuota;

/**
 * AccessAppModelTest
 *
 * <AUTHOR> (<EMAIL>)
 */
class AccessAppModelTest {

    private AccessAppModel accessAppModel;

    private AccessApp accessApp;

    @BeforeEach
    public void init() {
        accessAppModel = AccessAppModel.builder()
                .characterImage("UE5_RENDER")
                .createTime(ZonedDateTime.now())
                .updateTime(ZonedDateTime.now())
                .build();
        accessApp = accessAppModel.toAccessApp();
        accessAppModel = AccessAppModel.fromAccessApp(accessApp);
        accessAppModel.setCreateTime(ZonedDateTime.now());
        accessAppModel.setUpdateTime(ZonedDateTime.now());
    }

    @Test
    void getTagsString() {
        String tagsString = accessAppModel.getTagsString();
        accessAppModel.setTagsString(tagsString);

        Assertions.assertTrue(StringUtils.isEmpty(tagsString));
        Assertions.assertNull(accessAppModel.getTags());

        accessAppModel.setTags(Maps.newHashMap());
        accessAppModel.getTags().put("key", "value");
        tagsString = accessAppModel.getTagsString();
        accessAppModel.setTagsString(tagsString);

        Assertions.assertTrue(StringUtils.isNotEmpty(tagsString));
        Assertions.assertNotNull(accessAppModel.getTags());
    }

    @Test
    void getResourceQuotaString() {
        String resourceQuotaString = StringUtils.EMPTY;
        accessAppModel.setResourceQuotaString(resourceQuotaString);

        Assertions.assertTrue(StringUtils.isEmpty(accessAppModel.getResourceQuotaString()));
        Assertions.assertNull(accessAppModel.getResourceQuota());

        accessAppModel.setResourceQuota(ResourceQuota.builder().build());
        resourceQuotaString = accessAppModel.getResourceQuotaString();
        accessAppModel.setResourceQuotaString(resourceQuotaString);

        Assertions.assertTrue(StringUtils.isNotEmpty(resourceQuotaString));
        Assertions.assertNotNull(accessAppModel.getResourceQuota());
    }

    @Test
    void toAccessApp() {
        accessAppModel.setTags(Maps.newHashMap());
        accessAppModel.getTags().put("key", "value");
        accessAppModel.setResourceQuota(ResourceQuota.builder().build());

        accessApp = accessAppModel.toAccessApp();
        accessAppModel = AccessAppModel.fromAccessApp(accessApp);

        Assertions.assertTrue(StringUtils.isNotEmpty(accessAppModel.getTagsString()));
        Assertions.assertTrue(StringUtils.isNotEmpty(accessAppModel.getResourceQuotaString()));
    }
}