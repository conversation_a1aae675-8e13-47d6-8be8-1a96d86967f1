package com.baidu.acg.piat.digitalhuman.plat.controller;

import com.baidu.acg.piat.digitalhuman.common.background.SpecBackgroundRequest;
import com.baidu.acg.piat.digitalhuman.common.entity.CharacterModel;
import com.baidu.acg.piat.digitalhuman.common.model.PageResponse;
import com.baidu.acg.piat.digitalhuman.plat.service.CharacterService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.awt.image.BufferedImage;
import java.io.IOException;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * Created on 2020/7/28 14:54.
 *
 * <AUTHOR>
 */
public class CharacterControllerTest {

    private static final int API_VERSION = 1;

    @InjectMocks
    private CharacterController controller;

    @Mock
    private CharacterService characterService;

    private CharacterModel characterRequest;

    @BeforeEach
    public void init() {
        MockitoAnnotations.initMocks(this);
        characterRequest = CharacterModel.builder()
                .characterId("id")
                .name("UE4")
                .type("UE4_RENDER_VERTICAL")
                .description("description")
                .backgroundImageUrl("backgroundImageUrl")
                .frontImageUrl("frontImageUrl")
                .maskImageUrl("maskImageUrl")
                .build();
    }

    @Test
    public void create() {
        when(characterService.create(any())).thenReturn(characterRequest);
        controller.create(any());
    }

    @Test
    public void findByType() {
        when(characterService.selectByType(any(), anyInt())).thenReturn(characterRequest);
        controller.findByType("type", 1);
    }

    @Test
    public void selectAll() {
        PageResponse<CharacterModel> pageResponse = PageResponse.empty(20, 0);
        when(characterService.selectAll(anyInt(), anyInt())).thenReturn(pageResponse);
        var result = controller.selectAll(anyInt(), anyInt());
        assertEquals(result.getPage().getTotalCount(), 0);
        assertEquals(result.getPage().getPageSize(), 20);
        assertNull(result.getPage().getResult());

    }

    @Test
    void findCharactersVisibleForSce() {
        PageResponse<CharacterModel> pageResponse = PageResponse.empty(20, 0);
        when(characterService.selectVisibleForSce(anyBoolean(), anyInt(), anyInt())).thenReturn(pageResponse);
        var result = controller.findCharactersVisibleForSce(anyInt(), anyInt(), anyBoolean());
        assertEquals(result.getPage().getTotalCount(), 0);
        assertEquals(result.getPage().getPageSize(), 20);
        assertNull(result.getPage().getResult());
    }

    @Test
    public void update() {
        when(characterService.updateByType(anyString(), any(), anyInt())).thenReturn(characterRequest);
        controller.updateByType("type", new CharacterModel());

        when(characterService.updateById(anyString(), any())).thenReturn(characterRequest);
        controller.updateById(anyString(), any());
    }

    @Test
    public void delete() {
        controller.deleteById(anyString());
        verify(characterService, times(1)).deleteById(anyString());

        controller.deleteByType(anyString());
        verify(characterService, times(1)).deleteByType(anyString());
    }

    @Test
    public void preview() throws IOException {
        when(characterService.preview(anyString(), any(), anyInt())).thenReturn(new BufferedImage(100, 100, 1));
        var specBackgroundRequest = SpecBackgroundRequest.builder().backgroundId("backgroundId").build();
        controller.preview("type", specBackgroundRequest);
        verify(characterService, times(1)).preview(anyString(), any(), anyInt());
    }

}
