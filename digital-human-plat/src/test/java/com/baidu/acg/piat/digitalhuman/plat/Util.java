package com.baidu.acg.piat.digitalhuman.plat;

import java.net.HttpURLConnection;
import java.net.MalformedURLException;
import java.net.URL;

import static org.junit.jupiter.api.Assertions.assertTrue;

/**
 * 单测工具类
 */
public class Util {
    public static boolean exists(URL url) {
        try {
            HttpURLConnection.setFollowRedirects(false);
            // note : you may also need
            //        HttpURLConnection.setInstanceFollowRedirects(false)
            HttpURLConnection con =
                    (HttpURLConnection) url.openConnection();
            con.setRequestMethod("HEAD");
            return (con.getResponseCode() == HttpURLConnection.HTTP_OK);
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    public static boolean exists(String url) throws MalformedURLException {
        return exists(new URL(url));
    }

    public static void assertExist(String url) throws MalformedURLException {
        assertTrue(exists(url));
    }
}
