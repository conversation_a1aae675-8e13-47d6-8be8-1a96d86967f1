// Copyright (C) 2020 Baidu Inc. All rights reserved.
package com.baidu.acg.piat.digitalhuman.plat.model.character;

import org.apache.commons.lang.StringUtils;
import org.assertj.core.util.Lists;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.time.ZonedDateTime;

import com.baidu.acg.piat.digitalhuman.common.character.BaseCharacterInfo;
import com.baidu.acg.piat.digitalhuman.common.entity.CharacterModel;

/**
 * CharacterModelTest
 *
 * <AUTHOR> (<EMAIL>)
 */
class CharacterModelTest {
    private CharacterModel characterModel;

    @BeforeEach
    public void init() {
        characterModel = CharacterModel.builder()
                .createTime(ZonedDateTime.now())
                .updateTime(ZonedDateTime.now())
                .build();
        characterModel.setCreateTime(ZonedDateTime.now());
        characterModel.setUpdateTime(ZonedDateTime.now());
    }

    @Test
    void isSupportCallback() {
        boolean supportCallback = characterModel.getSupportCallback() == 1;
        Assertions.assertFalse(supportCallback);
        characterModel.setSupportCallback(1);
        Assertions.assertTrue(characterModel.getSupportCallback() == 1) ;
    }

    @Test
    void isSupportDataChannel() {
        boolean supportRtcDatachannel = characterModel.getSupportRtcDatachannel() == 1;
        Assertions.assertFalse(supportRtcDatachannel);
        characterModel.setSupportRtcDatachannel(1);
        Assertions.assertTrue(characterModel.getSupportRtcDatachannel() == 1);
    }

    @Test
    void getFacialListString() {
        String facialListString = characterModel.getFacialListString();
        characterModel.setFacialListString(facialListString);

        Assertions.assertTrue(StringUtils.isEmpty(facialListString));
        Assertions.assertNull(characterModel.getFacialList());

        characterModel.setFacialList(Lists.newArrayList(new BaseCharacterInfo("id", "name", "picUrl", 0,
                Lists.newArrayList(new BaseCharacterInfo.Children("id", "type", "name",
                        "start", "end", -1.0, 1.0, 0.0,
                        Lists.newArrayList("data", "data"), true, "picUrl")), null)));
        facialListString = characterModel.getFacialListString();
        characterModel.setFacialListString(facialListString);

        Assertions.assertTrue(StringUtils.isNotEmpty(facialListString));
        Assertions.assertNotNull(characterModel.getFacialList());
    }

    @Test
    void getMakeupListString() {
        String makeupListString = characterModel.getMakeupListString();
        characterModel.setMakeupListString(makeupListString);

        Assertions.assertTrue(StringUtils.isEmpty(makeupListString));
        Assertions.assertNull(characterModel.getMakeupList());

        characterModel.setMakeupList(Lists.newArrayList(new BaseCharacterInfo()));
        makeupListString = characterModel.getMakeupListString();
        characterModel.setMakeupListString(makeupListString);

        Assertions.assertTrue(StringUtils.isNotEmpty(makeupListString));
        Assertions.assertNotNull(characterModel.getMakeupList());
    }

    @Test
    void getCameraString() {
        String cameraListString = characterModel.getCameraListString();
        characterModel.setCameraListString(cameraListString);

        Assertions.assertTrue(StringUtils.isEmpty(cameraListString));
        Assertions.assertNull(characterModel.getCameraList());

        characterModel.setCameraList(Lists.newArrayList(new BaseCharacterInfo()));
        cameraListString = characterModel.getCameraListString();
        characterModel.setHairStyleString(cameraListString);

        Assertions.assertTrue(StringUtils.isNotEmpty(cameraListString));
        Assertions.assertNotNull(characterModel.getCameraList());
    }

    @Test
    void getHairStyleString() {
        String hairStyleString = characterModel.getHairStyleString();
        characterModel.setHairStyleString(hairStyleString);

        Assertions.assertTrue(StringUtils.isEmpty(hairStyleString));
        Assertions.assertNull(characterModel.getHairStyleList());

        characterModel.setHairStyleList(Lists.newArrayList(new BaseCharacterInfo()));
        hairStyleString = characterModel.getHairStyleString();
        characterModel.setHairStyleString(hairStyleString);

        Assertions.assertTrue(StringUtils.isNotEmpty(hairStyleString));
        Assertions.assertNotNull(characterModel.getHairStyleList());
    }

    @Test
    void getClothingStyleString() {
        String clothingStyleString = characterModel.getClothingStyleString();
        characterModel.setClothingStyleString(clothingStyleString);

        Assertions.assertTrue(StringUtils.isEmpty(clothingStyleString));
        Assertions.assertNull(characterModel.getHairStyleList());

        characterModel.setClothingStyleList(Lists.newArrayList(new BaseCharacterInfo()));
        clothingStyleString = characterModel.getClothingStyleString();
        characterModel.setClothingStyleString(clothingStyleString);

        Assertions.assertTrue(StringUtils.isNotEmpty(clothingStyleString));
        Assertions.assertNotNull(characterModel.getClothingStyleList());
    }

    @Test
    void getBadgeStyleString() {
        String badgeStyleString = characterModel.getBadgeStyleString();
        characterModel.setBadgeStyleString(badgeStyleString);

        Assertions.assertTrue(StringUtils.isEmpty(badgeStyleString));
        Assertions.assertNull(characterModel.getBadgeStyleList());

        characterModel.setBadgeStyleList(Lists.newArrayList(new BaseCharacterInfo()));
        badgeStyleString = characterModel.getBadgeStyleString();
        characterModel.setBadgeStyleString(badgeStyleString);

        Assertions.assertTrue(StringUtils.isNotEmpty(badgeStyleString));
        Assertions.assertNotNull(characterModel.getBadgeStyleList());
    }

    @Test
    void getEmotionString() {
        String emotionListString = characterModel.getEmotionListString();
        characterModel.setEmotionListString(emotionListString);

        Assertions.assertTrue(StringUtils.isEmpty(emotionListString));
        Assertions.assertNull(characterModel.getEmotionList());

        characterModel.setEmotionList(Lists.newArrayList(new BaseCharacterInfo()));
        emotionListString = characterModel.getEmotionListString();
        characterModel.setEmotionListString(emotionListString);

        Assertions.assertTrue(StringUtils.isNotEmpty(emotionListString));
        Assertions.assertNotNull(characterModel.getEmotionList());
    }

    @Test
    void getSceneListString() {
        String sceneListString = characterModel.getSceneListString();
        characterModel.setSceneListString(sceneListString);

        Assertions.assertTrue(StringUtils.isEmpty(sceneListString));
        Assertions.assertNull(characterModel.getSceneList());

        characterModel.setSceneList(Lists.newArrayList(new BaseCharacterInfo()));
        sceneListString = characterModel.getSceneListString();
        characterModel.setSceneListString(sceneListString);

        Assertions.assertTrue(StringUtils.isNotEmpty(sceneListString));
        Assertions.assertNotNull(characterModel.getSceneList());
    }
}