// Copyright (C) 2020 Baidu Inc. All rights reserved.
package com.baidu.acg.piat.digitalhuman.plat.model.project;

import org.apache.commons.lang.StringUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.time.ZonedDateTime;

import com.baidu.acg.piat.digitalhuman.common.project.AsrPartEvent;
import com.baidu.acg.piat.digitalhuman.common.project.BotParams;
import com.baidu.acg.piat.digitalhuman.common.project.Camera;
import com.baidu.acg.piat.digitalhuman.common.project.CharacterParams;
import com.baidu.acg.piat.digitalhuman.common.project.FigureCutParams;
import com.baidu.acg.piat.digitalhuman.common.project.MediaOutput;
import com.baidu.acg.piat.digitalhuman.common.project.PaintChartOnPictureParams;
import com.baidu.acg.piat.digitalhuman.common.project.PaintSubtitleOnPictureParams;
import com.baidu.acg.piat.digitalhuman.common.project.Project;
import com.baidu.acg.piat.digitalhuman.common.project.ResolutionParams;
import com.baidu.acg.piat.digitalhuman.common.project.TtsParams;

/**
 * ProjectModelTest
 *
 * <AUTHOR> Junyi (<EMAIL>)
 */
class ProjectModelTest {
    private ProjectModel projectModel;

    private Project project;

    @BeforeEach
    public void init() {
        projectModel = ProjectModel.builder()
                .createTime(ZonedDateTime.now())
                .updateTime(ZonedDateTime.now())
                .build();
        project = projectModel.toProjectRequest();
    }

    @Test
    void getBotParamsString() {
        String jsonString = projectModel.getBotParamsString();
        projectModel.setBotParamsString(jsonString);

        Assertions.assertTrue(StringUtils.isEmpty(jsonString));
        Assertions.assertNull(projectModel.getBotParams());

        projectModel.setBotParams(BotParams.builder().build());
        jsonString = projectModel.getBotParamsString();
        projectModel.setBotParamsString(jsonString);

        Assertions.assertTrue(StringUtils.isNotEmpty(jsonString));
        Assertions.assertNotNull(projectModel.getBotParams());
    }

    @Test
    void getTtsParamsString() {
        String jsonString = projectModel.getTtsParamsString();
        projectModel.setTtsParamsString(jsonString);

        Assertions.assertTrue(StringUtils.isEmpty(jsonString));
        Assertions.assertNull(projectModel.getTtsParams());

        projectModel.setTtsParams(new TtsParams());
        jsonString = projectModel.getTtsParamsString();
        projectModel.setTtsParamsString(jsonString);

        Assertions.assertTrue(StringUtils.isNotEmpty(jsonString));
        Assertions.assertNotNull(projectModel.getTtsParams());
    }

    @Test
    void getCameraString() {
        String jsonString = projectModel.getCameraString();
        projectModel.setCameraString(jsonString);

        Assertions.assertTrue(StringUtils.isEmpty(jsonString));
        Assertions.assertNull(projectModel.getCamera());

        projectModel.setCamera(Camera.builder().build());
        jsonString = projectModel.getCameraString();
        projectModel.setCameraString(jsonString);

        Assertions.assertTrue(StringUtils.isNotEmpty(jsonString));
        Assertions.assertNotNull(projectModel.getCamera());
    }

    @Test
    void getResolutionParamsString() {
        String jsonString = projectModel.getResolutionParamsString();
        projectModel.setResolutionParamsString(jsonString);

        Assertions.assertTrue(StringUtils.isEmpty(jsonString));
        Assertions.assertNull(projectModel.getResolutionParams());

        projectModel.setResolutionParams(ResolutionParams.builder().build());
        jsonString = projectModel.getResolutionParamsString();
        projectModel.setResolutionParamsString(jsonString);

        Assertions.assertTrue(StringUtils.isNotEmpty(jsonString));
        Assertions.assertNotNull(projectModel.getResolutionParams());
    }

    @Test
    void getFigureCutParamsString() {
        String jsonString = projectModel.getFigureCutParamsString();
        projectModel.setFigureCutParamsString(jsonString);

        Assertions.assertTrue(StringUtils.isEmpty(jsonString));
        Assertions.assertNull(projectModel.getFigureCutParams());

        projectModel.setFigureCutParams(new FigureCutParams());
        jsonString = projectModel.getFigureCutParamsString();
        projectModel.setFigureCutParamsString(jsonString);

        Assertions.assertTrue(StringUtils.isNotEmpty(jsonString));
        Assertions.assertNotNull(projectModel.getFigureCutParams());
    }

    @Test
    void getCharacterParamsString() {
        String jsonString = projectModel.getCharacterParamsString();
        projectModel.setCharacterParamsString(jsonString);

        Assertions.assertTrue(StringUtils.isEmpty(jsonString));
        Assertions.assertNull(projectModel.getCharacterParams());

        projectModel.setCharacterParams(new CharacterParams());
        jsonString = projectModel.getCharacterParamsString();
        projectModel.setCharacterParamsString(jsonString);

        Assertions.assertTrue(StringUtils.isNotEmpty(jsonString));
        Assertions.assertNotNull(projectModel.getCharacterParams());
    }

    @Test
    void getPaintChartOnPictureParamsString() {
        String jsonString = projectModel.getPaintChartOnPictureParamsString();
        projectModel.setPaintChartOnPictureParamsString(jsonString);

        Assertions.assertTrue(StringUtils.isEmpty(jsonString));
        Assertions.assertNull(projectModel.getPaintChartOnPictureParams());

        projectModel.setPaintChartOnPictureParams(new PaintChartOnPictureParams());
        jsonString = projectModel.getPaintChartOnPictureParamsString();
        projectModel.setPaintChartOnPictureParamsString(jsonString);

        Assertions.assertTrue(StringUtils.isNotEmpty(jsonString));
        Assertions.assertNotNull(projectModel.getPaintChartOnPictureParams());
    }

    @Test
    void getPaintSubtitleOnPictureParamsString() {
        String jsonString = projectModel.getPaintSubtitleOnPictureParamsString();
        projectModel.setPaintSubtitleOnPictureParamsString(jsonString);

        Assertions.assertTrue(StringUtils.isEmpty(jsonString));
        Assertions.assertNull(projectModel.getPaintSubtitleOnPictureParams());

        projectModel.setPaintSubtitleOnPictureParams(new PaintSubtitleOnPictureParams());
        jsonString = projectModel.getPaintSubtitleOnPictureParamsString();
        projectModel.setPaintSubtitleOnPictureParamsString(jsonString);

        Assertions.assertTrue(StringUtils.isNotEmpty(jsonString));
        Assertions.assertNotNull(projectModel.getPaintSubtitleOnPictureParams());
    }

    @Test
    void getMediaOutputString() {
        String jsonString = projectModel.getMediaOutputString();
        projectModel.setMediaOutputString(jsonString);

        Assertions.assertTrue(StringUtils.isEmpty(jsonString));
        Assertions.assertNull(projectModel.getMediaOutput());

        projectModel.setMediaOutput(new MediaOutput());
        jsonString = projectModel.getMediaOutputString();
        projectModel.setMediaOutputString(jsonString);

        Assertions.assertTrue(StringUtils.isNotEmpty(jsonString));
        Assertions.assertNotNull(projectModel.getMediaOutput());
    }

    @Test
    void getAsrPartEventString() {
        String jsonString = projectModel.getAsrPartEventString();
        projectModel.setAsrPartEventString(jsonString);

        Assertions.assertTrue(StringUtils.isEmpty(jsonString));
        Assertions.assertNull(projectModel.getAsrPartEvent());

        projectModel.setAsrPartEvent(AsrPartEvent.builder().build());
        jsonString = projectModel.getAsrPartEventString();
        projectModel.setAsrPartEventString(jsonString);

        Assertions.assertTrue(StringUtils.isNotEmpty(jsonString));
        Assertions.assertNotNull(projectModel.getAsrPartEvent());
    }

    @Test
    void toProjectRequest() {
        projectModel.setBotParams(BotParams.builder().build());
        projectModel.setTtsParams(new TtsParams());
        projectModel.setResolutionParams(ResolutionParams.builder().build());
        projectModel.setFigureCutParams(new FigureCutParams());
        projectModel.setCharacterParams(new CharacterParams());
        projectModel.setPaintChartOnPictureParams(new PaintChartOnPictureParams());
        projectModel.setPaintSubtitleOnPictureParams(new PaintSubtitleOnPictureParams());
        projectModel.setMediaOutput(new MediaOutput());
        projectModel.setAsrPartEvent(AsrPartEvent.builder().build());

        project = projectModel.toProjectRequest();

        Assertions.assertNotNull(project.getBotParams());
        Assertions.assertNotNull(project.getTtsParams());
        Assertions.assertNotNull(project.getResolutionParams());
        Assertions.assertNotNull(project.getFigureCutParams());
        Assertions.assertNotNull(project.getCharacterParams());
        Assertions.assertNotNull(project.getPaintChartOnPictureParams());
        Assertions.assertNotNull(project.getPaintSubtitleOnPictureParams());
        Assertions.assertNotNull(project.getMediaOutput());
        Assertions.assertNotNull(project.getAsrPartEvent());

        ProjectModel newModel = ProjectModel.builder().build();
        newModel.toProjectModel(project);

        Assertions.assertNotNull(newModel.getAsrPartEvent());
    }
}