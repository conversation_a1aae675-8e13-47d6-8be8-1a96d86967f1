package com.baidu.acg.piat.digitalhuman.plat.controller;

import com.baidu.acg.piat.digitalhuman.common.model.PageResponse;
import com.baidu.acg.piat.digitalhuman.common.model.PageResult;
import com.baidu.acg.piat.digitalhuman.common.room.session.RoomSession;
import com.baidu.acg.piat.digitalhuman.common.room.session.RoomSessionStatus;
import com.baidu.acg.piat.digitalhuman.common.session.SessionStatus;
import com.baidu.acg.piat.digitalhuman.plat.service.RoomSessionService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.Collections;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * Created on 2020/7/27 18:28.
 *
 * <AUTHOR>
 */
public class RoomSessionControllerTest {

    @Mock
    private RoomSessionService roomSessionService;

    @InjectMocks
    private RoomSessionController roomSessionController;

    @BeforeEach
    public void init() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void closeRoomSession() {
        when(roomSessionService.updateStatus(buildRoomSession())).thenReturn(buildRoomSession());
        roomSessionController.closeRoomSession("sid");
    }

    @Test
    public void getRoomSession() {
        when(roomSessionService.detail(anyString())).thenReturn(buildRoomSession());
        roomSessionController.get("sid");
    }

    @Test
    public void listRoomSession() {
        var page = PageResponse.<RoomSession>builder().page(PageResult.<RoomSession>builder()
                .pageNo(1)
                .pageSize(20)
                .totalCount(1)
                .result(Collections.singletonList(buildRoomSession()))
                .build()
        ).build();
        when(roomSessionService.listByRoomId(anyString(), anyInt(), anyInt())).thenReturn(page);
        roomSessionController.listSessionsByRoomId("roomId", 1, 20);
        verify(roomSessionService, times(1)).listByRoomId(anyString(), anyInt(), anyInt());
        assertEquals(page.getPage().getPageNo(), 1);
        assertEquals(page.getPage().getPageSize(), 20);
        assertEquals(page.getPage().getTotalCount(), 1);
        assertEquals(page.getPage().getResult().size(), 1);
        assertEquals(page.getPage().getResult().get(0).getSessionId(), "sid");
        assertEquals(page.getPage().getResult().get(0).getStatus(), SessionStatus.CLOSED);
    }

    private RoomSession buildRoomSession() {
        return RoomSession.builder()
                .sessionId("sid")
                .status(SessionStatus.CLOSED)
                .build();
    }
}
