package com.baidu.acg.piat.digitalhuman.plat.controller;

import com.baidu.acg.piat.digitalhuman.common.material.Material;
import com.baidu.acg.piat.digitalhuman.plat.service.MaterialService;

import com.google.common.collect.Lists;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.verify;

/**
 * Created on 2021/8/11 3:07 下午
 *
 * <AUTHOR>
 */

class MaterialControllerTest {

    @InjectMocks
    private MaterialController materialController;

    @Mock
    private MaterialService materialService;

    private Material material;

    @BeforeEach
    public void setup() {
        MockitoAnnotations.initMocks(this);

        material = Material.builder()
                .userId("userid")
                .name("name")
                .build();
    }

    @Test
    void create() {
        materialController.create(material);
        verify(materialService, Mockito.times(1)).create(material);
    }

    @Test
    void delete() {
        materialController.delete("id");
        verify(materialService, Mockito.times(1)).delete("id");
    }

    @Test
    void update() {
        materialController.update("id", material);
        verify(materialService, Mockito.times(1)).update("id", material);
    }

    @Test
    void detail() {
        materialController.detail("id");
        verify(materialService, Mockito.times(1)).detail("id");
    }

    @Test
    void list() {
        materialController.list("userId", "name", Lists.newArrayList("image"),
                Lists.newArrayList("positionId"), 1, 20);
        verify(materialService, Mockito.times(1))
                .listByUserIdAndNameAndTypeAndPositionId(anyString(), anyString(), anyList(), anyList(),
                        anyInt(), anyInt());
    }
}