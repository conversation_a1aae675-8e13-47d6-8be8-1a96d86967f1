package com.baidu.acg.piat.digitalhuman.plat.model;

import com.baidu.acg.piat.digitalhuman.common.character.CharacterMixer;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.IOException;
import java.net.URL;

import static org.junit.jupiter.api.Assertions.assertTrue;

@Slf4j
class CharacterMixerTest {
    private static String root
            = "https://bj.bcebos.com/v1/digital-human-material/test/charactermanager_test/";

    /**
     * 背景长宽比更大
     */
    @Test
    void mixIfBackAspectRatioGreater() throws IOException {
        var caseName = MixerCase.BACK_ASPECT_RATIO_GREATER;
        var mixer = createMixer(caseName);
        var result = mixer.mix();
        var file = new File(caseName + ".jpg");
        ImageIO.write(result, "jpg", file);
        assertTrue(file.exists());
        file.deleteOnExit();
    }

    /**
     * 前景长宽比更大
     */
    @Test
    void mixIfFrontAspectRatioGreater() throws IOException {
        var caseName = MixerCase.FRONT_ASPECT_RATIO_GREATER;
        var mixer = createMixer(caseName);
        var result = mixer.mix();
        var file = new File(caseName + ".jpg");
        ImageIO.write(result, "jpg", file);
        assertTrue(file.exists());
        file.deleteOnExit();
    }

    /**
     * 前景背景长宽比相等
     */
    @Test
    void mixIfAspectRatioEqual() throws IOException {
        var caseName = MixerCase.EQUAL_ASPECT_RATIO;
        var mixer = createMixer(caseName);
        var result = mixer.mix();
        var file = new File(caseName + ".jpg");
        ImageIO.write(result, "jpg", file);
        assertTrue(file.exists());
        file.deleteOnExit();
    }

    private CharacterMixer createMixer(MixerCase mixerCase) throws IOException {
        return CharacterMixer.builder()
                .frontImage(fromUrl(getFrontImageUrl(mixerCase)))
                .backImage(fromUrl(getBackImageUrl(mixerCase)))
                .mask(fromUrl(getMaskImageUrl(mixerCase)))
                .build();
    }

    private String getFrontImageUrl(MixerCase mixerCase) {
        return root + mixerCase.toString() + "/front.jpg";
    }

    private String getBackImageUrl(MixerCase mixerCase) {
        return root + mixerCase.toString() + "/background.jpg";
    }

    private String getMaskImageUrl(MixerCase mixerCase) {
        return root + mixerCase.toString() + "/mask.jpg";
    }

    private BufferedImage fromUrl(String url) throws IOException {
        return ImageIO.read(new URL(url));
    }

}


