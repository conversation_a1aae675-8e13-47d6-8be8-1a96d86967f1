// Copyright (C) 2020 Baidu Inc. All rights reserved.
package com.baidu.acg.piat.digitalhuman.plat.model.user;

import com.google.common.collect.Maps;
import org.apache.commons.lang.StringUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.time.ZonedDateTime;

import com.baidu.acg.piat.digitalhuman.common.access.AccessUser;

/**
 * AccessUserModelTest
 *
 * <AUTHOR> (<EMAIL>)
 */
class AccessUserModelTest {

    private AccessUserModel accessUserModel;

    private AccessUser accessUser;

    @BeforeEach
    public void init() {
        accessUserModel = AccessUserModel.builder()
                .createTime(ZonedDateTime.now())
                .updateTime(ZonedDateTime.now())
                .build();
        accessUser = accessUserModel.toAccessUser(true);
    }

    @Test
    void getTagsString() {
        String tagsString = accessUserModel.getTagsString();
        accessUserModel.setTagsString(tagsString);

        Assertions.assertTrue(StringUtils.isEmpty(tagsString));
        Assertions.assertNull(accessUserModel.getTags());

        accessUserModel.setTags(Maps.newHashMap());
        accessUserModel.getTags().put("key", "value");
        tagsString = accessUserModel.getTagsString();
        accessUserModel.setTagsString(tagsString);

        Assertions.assertTrue(StringUtils.isNotEmpty(tagsString));
        Assertions.assertNotNull(accessUserModel.getTags());
    }
}