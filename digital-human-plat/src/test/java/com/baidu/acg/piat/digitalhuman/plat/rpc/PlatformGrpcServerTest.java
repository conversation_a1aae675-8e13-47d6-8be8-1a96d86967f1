package com.baidu.acg.piat.digitalhuman.plat.rpc;

import java.io.IOException;

import com.baidu.acg.piat.digitalhuman.plat.config.PlatformGrpcConfigure;
import com.baidu.acg.piat.digitalhuman.plat.rpc.platform.PlatformRoomSessionGrpcService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import static org.mockito.Mockito.when;

/**
 * The test for {@link PlatformGrpcServer}.
 *
 * <AUTHOR> (<EMAIL>)
 */
public class PlatformGrpcServerTest {

    @Mock
    private PlatformGrpcConfigure platformGrpcConfigure;

    @Mock
    private PlatformRoomSessionGrpcService platformRoomSessionGrpcService;

    @InjectMocks
    private PlatformGrpcServer platformGrpcServer;

    @BeforeEach
    private void setup() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void init() throws IOException {
        when(platformGrpcConfigure.getServerConfig()).thenReturn(new PlatformGrpcConfigure.PlatformGrpcServerConfig());
        try {
            platformGrpcServer.init();
        } catch (Exception e) {
            // ignore
        }
    }

    @Test
    public void destroy() throws InterruptedException {
        platformGrpcServer.destroy();
    }
}
