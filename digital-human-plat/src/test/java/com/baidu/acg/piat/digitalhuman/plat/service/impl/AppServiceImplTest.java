package com.baidu.acg.piat.digitalhuman.plat.service.impl;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.test.util.ReflectionTestUtils;

import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

import com.baidu.acg.piat.digitalhuman.common.access.AccessApp;
import com.baidu.acg.piat.digitalhuman.common.access.AccessUser;
import com.baidu.acg.piat.digitalhuman.common.character.CharacterImages;
import com.baidu.acg.piat.digitalhuman.common.entity.CharacterModel;
import com.baidu.acg.piat.digitalhuman.common.project.Project;
import com.baidu.acg.piat.digitalhuman.common.quota.ResourceQuota;
import com.baidu.acg.piat.digitalhuman.common.utils.IDGenerator;
import com.baidu.acg.piat.digitalhuman.plat.dao.AppRepository;
import com.baidu.acg.piat.digitalhuman.plat.exception.DigitalHumanAccessException;
import com.baidu.acg.piat.digitalhuman.plat.model.app.AccessAppModel;
import com.baidu.acg.piat.digitalhuman.plat.service.CharacterService;
import com.baidu.acg.piat.digitalhuman.plat.service.ProjectService;
import com.baidu.acg.piat.digitalhuman.plat.service.UserService;

/**
 * The test for {@link AppServiceImpl}.
 *
 * <AUTHOR> Mao (<EMAIL>)
 */
public class AppServiceImplTest {

    private static final ZonedDateTime NOW = ZonedDateTime.now();

    @Mock
    private UserService userService;

    @Mock
    private AppRepository appRepository;

    @Mock
    private IDGenerator idGenerator;

    @Mock
    private CharacterImages characterImages;

    @Mock
    private ProjectService projectService;

    @Mock
    private CharacterService characterService;

    @Mock
    private MoreDbEditTransactionServiceImpl moreDbEditTransactionService;

    @InjectMocks
    private AppServiceImpl appService;

    private static final String CHARACTER_IMAGE = "UE4_RENDER_VERTICAL";
    private static final String APP_ID = "2a6991fc-4aab-46f1-81a1-4300e1b88574";
    private static final String USER_ID = "5e99956446a228000725b452";
    private static final String USER_NAME = "user";
    private static final String PROJECT_ID = "projectId";

    private static AccessAppModel appModel;

    @BeforeEach
    public void init() {
        MockitoAnnotations.initMocks(this);
        ReflectionTestUtils.setField(appService, "envPrefix", "s");
        ReflectionTestUtils.setField(appService, "retryTimes", 3);
        ReflectionTestUtils.setField(appService, "retrySleepMillis", 100);

        appModel = AccessAppModel.builder()
                .name(USER_NAME)
                .userId(USER_ID)
                .appId(APP_ID)
                .projectId(PROJECT_ID)
                .characterImage(CHARACTER_IMAGE)
                .createTime(ZonedDateTime.now())
                .updateTime(ZonedDateTime.now())
                .build();
        when(moreDbEditTransactionService.saveApp(any())).thenReturn(buildAccessAppModel());
    }

    @Test
    public void create() {
        when(userService.get("userId")).thenReturn(AccessUser.builder().build());
        when(appRepository.findByUserIdAndName("userId", "name", null)).thenReturn(Page.empty());
        when(projectService.getByUserIdNameAndVersion("userId", "name", null, 1)).thenReturn(new Project());
        try {
            appService.create(buildAccessApp(), true);
        } catch (DigitalHumanAccessException e) {
            // ignore
        }

        when(idGenerator.generate(anyString())).thenReturn("i-i12345678912345678");
        when(idGenerator.generate(anyInt())).thenReturn("-id12345678912345678");
        when(appRepository.save(any())).thenReturn(buildAccessAppModel());
        assertEquals(buildAccessApp(), appService.create(buildAccessApp(), true));
    }

    private AccessAppModel buildAccessAppModel() {
        return AccessAppModel.builder()
                .name("name")
                .userId("userId")
                .characterImage("UE4_RENDER_VERTICAL")
                .createTime(NOW)
                .updateTime(NOW)
                .enabled(1)
                .build();
    }

    private AccessApp buildAccessApp() {
        return AccessApp.builder()
                .userId("userId")
                .name("name")
                .resourceQuota(ResourceQuota.builder().build())
                .enabled(true)
                .characterImage("UE4_RENDER_VERTICAL")
                .createTime(DateTimeFormatter.ISO_OFFSET_DATE_TIME.format(NOW))
                .updateTime(DateTimeFormatter.ISO_OFFSET_DATE_TIME.format(NOW))
                .build();
    }

    @Test
    public void update() {
        when(appRepository.findByAppId("appId")).thenReturn(Optional.of(buildAccessAppModel()));
        when(appRepository.save(any())).thenReturn(buildAccessAppModel());

        var result = appService.update("appId",
                AccessApp.builder()
                        .name("name")
                        .userId("userId")
                        .resourceQuota(ResourceQuota.builder()
                                .roomLimits(1)
                                .build())
                        .characterImage("UE_RENDER_VERTICAL")
                        .enabled(true)
                        .build());

        var expected = buildAccessApp();
        assertEquals(expected.getName(), result.getName());
        assertEquals(expected.getUserId(), result.getUserId());

        assertThrows(DigitalHumanAccessException.class, () -> appService.update("appId",
                AccessApp.builder().name("name").enabled(true).userId("test").build()));

        when(appRepository.findByUserIdAndName("userId", "test", null)).thenReturn(
                new PageImpl<>(Collections.singletonList(buildAccessAppModel())));
        assertThrows(DigitalHumanAccessException.class, () -> appService.update("appId",
                AccessApp.builder().name("test").enabled(true).build()));
    }

    @Test
    public void findDefaultByCharacterImageTest() {

        CharacterModel characterRequest = CharacterModel.builder()
                .name(CHARACTER_IMAGE)
                .appId(APP_ID)
                .build();

        when(appRepository.findByAppId(anyString())).thenReturn(Optional.of(appModel));
        when(characterService.selectByType(anyString(), anyInt())).thenReturn(characterRequest);

        AccessApp accessApp = appService.findDefaultByCharacterImage(CHARACTER_IMAGE, 1);
        Assertions.assertNotNull(accessApp);
        assertEquals(APP_ID, accessApp.getAppId());
    }

    @Test
    public void listApps() {

        AccessAppModel app = buildAccessAppModel();
        app.setAppId("appId");
        when(appRepository.findAllByAppIdIn(any())).thenReturn(List.of(app));

        List<AccessApp> result = appService.list(List.of("appId", "appId2"));

        assertEquals(1, result.size());
        assertEquals("appId", result.get(0).getAppId());

    }
}
