package com.baidu.acg.piat.digitalhuman.plat.v2.controller;

import com.baidu.acg.piat.digitalhuman.common.entity.CharacterModel;
import com.baidu.acg.piat.digitalhuman.common.model.PageResponse;
import com.baidu.acg.piat.digitalhuman.plat.service.CharacterService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import java.util.ArrayList;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.eq;

class CharacterV2ControllerTest {
    @InjectMocks
    private CharacterV2Controller characterV2Controller;

    @Mock
    private CharacterService characterService;

    @BeforeEach
    public void init() {
        MockitoAnnotations.initMocks(this);
        characterV2Controller = new CharacterV2Controller(characterService);
    }

    @Test
    void list() {
        Mockito.when(characterService.selectAll(anyInt(), anyInt(), anyInt()))
                .thenReturn(PageResponse.success(
                        1, 10, 10, new ArrayList<CharacterModel>()));
        assertDoesNotThrow(() -> characterV2Controller.list(
                Map.of("visibleCharacters", "all"), 1, 10, false, false));

        Mockito.when(characterService.selectAll(anyInt(), anyInt(), anyInt(), eq(true)))
                .thenReturn(PageResponse.success(
                        1, 10, 10, new ArrayList<CharacterModel>()));
        assertDoesNotThrow(() -> characterV2Controller.list(
                Map.of("visibleCharacters", "all"), 1, 10, true, false));

        Mockito.when(characterService.selectAll(anyInt(), anyInt(), anyInt(), eq(true)))
                .thenReturn(PageResponse.success(
                        1, 10, 10, new ArrayList<CharacterModel>()));
        assertDoesNotThrow(() -> characterV2Controller.list(
                Map.of("visibleCharacters", "all"), 1, 10, false, true));
    }
}