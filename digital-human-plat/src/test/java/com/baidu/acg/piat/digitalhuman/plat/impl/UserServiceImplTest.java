package com.baidu.acg.piat.digitalhuman.plat.impl;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

import org.apache.commons.codec.digest.DigestUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.Date;

import com.baidu.acg.dh.user.client.UserClient;
import com.baidu.acg.dh.user.client.UserResult;
import com.baidu.acg.dh.user.client.model.vo.UserGetResVO;
import com.baidu.acg.piat.digitalhuman.common.access.AccessUser;
import com.baidu.acg.piat.digitalhuman.plat.service.impl.UserServiceImpl;

/**
 * Created on 2021/2/2 17:25.
 *
 * <AUTHOR>
 */
public class UserServiceImplTest {

    @InjectMocks
    private UserServiceImpl userService;

    @Mock
    private UserClient userClient;

    private static AccessUser accessUser;

    private static UserGetResVO userGetResVO;

    @BeforeEach
    public void init() {
        MockitoAnnotations.initMocks(this);

        accessUser = AccessUser.builder()
                .name("name")
                .password("password")
                .description("description")
                .role("role")
                .build();
        userGetResVO = UserGetResVO.builder()
                .uid("userId")
                .userName("name")
                .password(DigestUtils.md5Hex("name" + "password"))
                .description("description")
                .role("role")
                .createTime(new Date())
                .updateTime(new Date())
                .build();
    }

    @Test
    public void testCreate() {
        UserResult<UserGetResVO> userResult = UserResult.<UserGetResVO> builder()
                .success(true)
                .result(userGetResVO)
                .build();
        when(userClient.getUserByName(anyString())).thenReturn(UserResult.<UserGetResVO> builder()
                .success(true).build());
        when(userClient.addUser(any())).thenReturn(userResult);
        var result = userService.create(accessUser);
        compare(accessUser, result);
    }

    @Test
    public void testDelete() {
        UserResult<UserGetResVO> userResult = UserResult.<UserGetResVO> builder()
                .success(true)
                .result(userGetResVO)
                .build();
        when(userClient.getUserById(anyString())).thenReturn(userResult);
        userService.delete("userId");
    }

    @Test
    public void testUpdate() {
        UserResult<UserGetResVO> userResult = UserResult.<UserGetResVO> builder()
                .success(true)
                .result(userGetResVO)
                .build();
        when(userClient.getUserById(anyString())).thenReturn(userResult);
        when(userClient.update(any())).thenReturn(userResult);
        var result = userService.update("userId", accessUser);
        compare(accessUser, result);
    }

    @Test
    public void testValidate() {
        UserResult<UserGetResVO> userResult = UserResult.<UserGetResVO> builder()
                .success(true)
                .result(userGetResVO)
                .build();
        when(userClient.getUserById(anyString())).thenReturn(userResult);
        accessUser.setUserId("userId");
        var result = userService.validate(accessUser);
        compare(accessUser, result);
    }

    @Test
    public void testValidateByName() {
        UserResult<UserGetResVO> userResult = UserResult.<UserGetResVO> builder()
                .success(true)
                .result(userGetResVO)
                .build();
        when(userClient.getUserByName(anyString())).thenReturn(userResult);
        var result = userService.validateByName(accessUser);
        compare(accessUser, result);
    }

    private void compare(AccessUser request, AccessUser result) {
        assertEquals(request.getName(), result.getName());
        assertEquals(DigestUtils.md5Hex(request.getName() + request.getPassword()),
                result.getPassword());
        assertEquals(request.getDescription(), result.getDescription());
        assertEquals(request.getRole(), result.getRole());
    }
}
