package com.baidu.acg.piat.digitalhuman.plat.dao;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.orm.jpa.DataJpaTest;
import org.springframework.data.domain.PageRequest;

import java.util.List;

import com.baidu.acg.piat.digitalhuman.plat.model.LiveConfigModel;

/**
 * <AUTHOR>
 * @since 2021/08/09
 */
@DataJpaTest
class LiveConfigRepositoryTest {
    @Autowired
    private LiveConfigRepository repository;

    private String configId = "config1";
    private String userId = "user1";

    @BeforeEach
    public void init() throws Exception {
        LiveConfigModel model = LiveConfigModel.builder()
                .name("123").userId(userId).configId(configId).rtmpUrlList(List.of("rtmp://123", "rtmp://234")).build();
        repository.save(model);

        Thread.sleep(100); // 确保第二个的 createTime 大于前一个

        LiveConfigModel model2 = LiveConfigModel.builder()
                .name("123").userId(userId).configId("config2").rtmpUrlList(List.of("rtmp://123", "rtmp://234"))
                .build();
        repository.save(model2);
    }

    @Test
    void findAllByUserIdOrderByCreateTimeDesc() {
        var pageResponse = repository.findAllByUserIdOrderByCreateTimeDesc(userId, PageRequest.of(1, 1));
        Assertions.assertEquals(2, pageResponse.getTotalElements());
    }

}