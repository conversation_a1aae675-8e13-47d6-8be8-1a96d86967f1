package com.baidu.acg.piat.digitalhuman.plat.service.impl;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.simple.SimpleMeterRegistry;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.data.domain.Page;
import org.springframework.test.util.ReflectionTestUtils;

import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;

import com.baidu.acg.piat.digitalhuman.common.access.AccessApp;
import com.baidu.acg.piat.digitalhuman.common.constans.StatisticConstant;
import com.baidu.acg.piat.digitalhuman.common.exception.DigitalHumanCommonException;
import com.baidu.acg.piat.digitalhuman.common.room.Room;
import com.baidu.acg.piat.digitalhuman.common.room.RoomStatus;
import com.baidu.acg.piat.digitalhuman.common.room.session.RoomSession;
import com.baidu.acg.piat.digitalhuman.common.session.SessionStatus;
import com.baidu.acg.piat.digitalhuman.plat.dao.RoomSessionRepository;
import com.baidu.acg.piat.digitalhuman.plat.model.room.session.RoomSessionModel;
import com.baidu.acg.piat.digitalhuman.plat.service.AppService;
import com.baidu.acg.piat.digitalhuman.plat.service.RoomService;

/**
 * Created on 2020/7/27 18:29.
 *
 * <AUTHOR>
 */
public class RoomSessionServiceImplTest {
    private static final ZonedDateTime NOW = ZonedDateTime.now();
    private static final String NOW_STR = DateTimeFormatter.ISO_OFFSET_DATE_TIME.format(NOW);

    @Mock
    private RoomService roomService;

    @Mock
    private MeterRegistry meterRegistry;

    @Mock
    private AppService appService;

    @Mock
    private RoomSessionRepository roomSessionRepository;

    @InjectMocks
    private RoomSessionServiceImpl roomSessionService;

    @BeforeEach
    public void setup() {
        MockitoAnnotations.initMocks(this);
        meterRegistry = new SimpleMeterRegistry();
    }

    @Test
    public void create() {
        var room = Room.builder()
                .appId("appId")
                .id("roomId")
                .roomName("roomName")
                .status(RoomStatus.ENABLED)
                .userId("userId")
                .build();
        when(appService.get(anyString())).thenReturn(AccessApp.builder().characterImage("characterImage").build());
        when(roomService.detail(anyString())).thenReturn(room);
        when(roomSessionRepository.save(any())).thenReturn(buildRoomSessionModel());
        assertNotNull(roomSessionService.create(buildRoomSession()));
        when(roomSessionRepository.findBySessionId("sessionId")).thenReturn(buildRoomSessionModel());
        try {
            roomSessionService.create(buildRoomSession());
        } catch (DigitalHumanCommonException exception) {
            // ignore
        }

        var roomSession = buildRoomSession();
        roomSession.setAppId("");
        assertThrows(DigitalHumanCommonException.class, () -> roomSessionService.create(roomSession));

        roomSession.setAppId("aid");
        assertThrows(DigitalHumanCommonException.class, () -> roomSessionService.create(roomSession));
    }

    @Test
    public void updateStatus() {
        when(roomSessionRepository.findBySessionId("sessionId")).thenReturn(buildRoomSessionModel());
        assertEquals(buildRoomSession(), roomSessionService.updateStatus(buildRoomSession()));
        try {
            roomSessionService.updateStatus(RoomSession.builder().build());
        } catch (DigitalHumanCommonException exception) {
            // ignore
        }
    }
    @Test
    public void detail() {
        when(roomSessionRepository.findBySessionId("sessionId")).thenReturn(buildRoomSessionModel());
        assertEquals(buildRoomSession(), roomSessionService.detail("sessionId"));
    }

    @Test
    public void listByRoomId() {
        when(roomSessionRepository.findByRoomIdOrderByCreateTimeDesc(anyString(), any())).thenReturn(Page.empty());
        var pageResponse = roomSessionService.listByRoomId("roomId", 1 ,20);
        assertEquals(0, pageResponse.getPage().getTotalCount());
        assertEquals(1, pageResponse.getPage().getPageNo());
        assertEquals(20, pageResponse.getPage().getPageSize());
        assertEquals(0, pageResponse.getPage().getResult().size());
    }

    @Test
    public void activeSession() {
        var res = roomSessionService.activeSession("appId");
        assertEquals(0, res);
    }

    @Test
    public void countByAppId() {
        var res = roomSessionService.countByAppId("appId", 0L, 0L);
        assertEquals(0, res);
    }

    @Test
    public void aggregateAvgDuration() {
        var res = roomSessionService.aggregateAvgDuration("appId", 0L, 0L);
        assertEquals(0, res);
    }

    private RoomSession buildRoomSession() {
        return RoomSession.builder()
                .sessionId("sessionId")
                .appId("appId")
                .roomId("roomId")
                .status(SessionStatus.OPEN)
                .createTimeInMs(NOW.toInstant().toEpochMilli())
                .createTime(NOW_STR)
                .updateTime(NOW_STR)
                .build();
    }

    private RoomSessionModel buildRoomSessionModel() {
        return RoomSessionModel.builder()
                .sessionId("sessionId")
                .appId("appId")
                .roomId("roomId")
                .status(SessionStatus.OPEN)
                .createTime(NOW)
                .updateTime(NOW)
                .build();
    }

}
