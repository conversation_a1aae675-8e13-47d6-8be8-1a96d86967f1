package com.baidu.acg.piat.digitalhuman.plat.service.impl;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.data.domain.Page;

import com.baidu.acg.piat.digitalhuman.common.entity.CharacterModel;
import com.baidu.acg.piat.digitalhuman.common.exception.DigitalHumanCommonException;
import com.baidu.acg.piat.digitalhuman.common.project.Project;
import com.baidu.acg.piat.digitalhuman.common.richconfig.DrmlAction;
import com.baidu.acg.piat.digitalhuman.common.richconfig.JsonToXmlRequest;
import com.baidu.acg.piat.digitalhuman.common.richconfig.RichtextConfig;
import com.baidu.acg.piat.digitalhuman.plat.dao.RichtextConfigRepository;
import com.baidu.acg.piat.digitalhuman.plat.model.config.RichtextConfigModel;
import com.baidu.acg.piat.digitalhuman.plat.service.CharacterService;
import com.baidu.acg.piat.digitalhuman.plat.service.ProjectService;

import lombok.extern.slf4j.Slf4j;

/**
 * Created on 2020/4/24 14:58.
 *
 * <AUTHOR>
 */
@Slf4j
public class RichtextConfigServiceTest {

    @InjectMocks
    private RichtextConfigServiceImpl service;

    @Mock
    private ProjectService projectService;

    @Mock
    private RichtextConfigRepository configRepository;

    @Mock
    private CharacterService characterService;

    private static final String testProjectId = "projectId";
    private static final String testProjectName = "projectName";
    private static final String testConfigId = "configId";
    private static final String testUserId = "userId";
    private static final ZonedDateTime now = ZonedDateTime.now();
    private static final String testContent = "content";

    private static RichtextConfigModel configModel;

    private RichtextConfig config;

    private Project project;

    private CharacterModel characterMeta;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        configModel = RichtextConfigModel.builder()
                .configId(testConfigId)
                .projectId(testProjectId)
                .content(testContent)
                .createTime(now)
                .updateTime(now)
                .build();

        config = RichtextConfig.builder()
                .projectId(testProjectId)
                .configId(testConfigId)
                .content(testContent)
                .build();

        project = Project.builder()
                .id(testProjectId)
                .name(UUID.randomUUID().toString())
                .characterImage("UE4_RENDER_VERTICAL")
                .createTime(DateTimeFormatter.ISO_OFFSET_DATE_TIME.format(now))
                .updateTime(DateTimeFormatter.ISO_OFFSET_DATE_TIME.format(now))
                .description("description")
                .build();

        characterMeta = CharacterModel.builder()
                .name("UE4_RENDER_VERTICAL")
                .appId("appId")
                .appKey("appKey")
                .label("3D")
                .build();
        when(characterService.selectByType(anyString(), anyInt())).thenReturn(characterMeta);
    }

    @Test
    public void create() {
        config.setProjectId(null);
        Throwable throwable = assertThrows(DigitalHumanCommonException.class,
                () -> service.create(testProjectId, config));
        assertEquals(throwable.getLocalizedMessage(), "ProjectId cannot be empty.");


        config = RichtextConfig.builder()
                .projectId(testProjectId)
                .build();
        throwable = assertThrows(DigitalHumanCommonException.class,
                () -> service.create(testProjectId, config));
        assertEquals(throwable.getLocalizedMessage(), "Richtext content cannot be empty");

        config.setContent(testContent);
        when(configRepository.save(any())).thenReturn(configModel);
        when(projectService.detail(anyString())).thenReturn(project);

        var result = service.create(testProjectId, config);
        compareRichtextConfig2ConfigModel(result, configModel);
    }

    @Test
    public void createByName() {
        config = RichtextConfig.builder()
                .projectName(testProjectName)
                .userId(testUserId)
                .build();
        Throwable throwable = assertThrows(DigitalHumanCommonException.class,
                () -> service.createByName(testUserId, testProjectName, config));
        assertEquals(throwable.getLocalizedMessage(), "Richtext content cannot be empty");

        config.setContent(testContent);
        when(configRepository.save(any())).thenReturn(configModel);
        when(projectService.detail(anyString())).thenReturn(project);

        var result = service.createByName(testUserId, testProjectName, config);
        compareRichtextConfig2ConfigModel(result, configModel);
    }

    @Test
    public void update() {
        Throwable throwable = assertThrows(DigitalHumanCommonException.class,
                () -> service.update("", config));
        assertEquals(throwable.getLocalizedMessage(), "Richtext configId cannot be empty.");

        configModel.setDownloadUrl("downloadUrl");
        when(configRepository.findByConfigId(anyString())).thenReturn(Optional.of(configModel));
        config.setDownloadUrl("downloadUrl");
        var result = service.update(testConfigId, config);
        compareRichtextConfig2ConfigModel(result, configModel);
    }

    @Test
    public void delete() {
        when(configRepository.findByConfigIdAndProjectId(anyString(), anyString()))
                .thenReturn(Optional.empty());
        Throwable throwable = assertThrows(DigitalHumanCommonException.class,
                () -> service.delete(testConfigId));
        assertEquals(throwable.getLocalizedMessage(), "RichtextConfig not existed.");

        when(configRepository.findByConfigId(anyString()))
                .thenReturn(Optional.of(configModel));
        service.delete(testConfigId);
    }

    @Test
    void selectByAppIdCheckAppId() {
        var emptyProjectId = "empty_project_id";
        when(configRepository.findByProjectIdOrderByCreateTimeDesc(anyString(), any())).thenReturn(Page.empty());
        var result = service.list(emptyProjectId, 1, 20);

        assertEquals(0, result.getPage().getTotalCount());
        assertEquals(0, result.getPage().getResult().size());
    }

    @Test
    void testListByName() {
        when(configRepository.findByUserIdAndProjectNameOrderByCreateTimeDesc(anyString(), anyString(), any()))
                .thenReturn(Page.empty());
        var result = service.listByName(testUserId, testProjectName, 1, 20);

        assertEquals(0, result.getPage().getTotalCount());
        assertEquals(0, result.getPage().getResult().size());
    }

    private void compareRichtextConfig2ConfigModel(RichtextConfig richtextConfig, RichtextConfigModel configModel) {
        assertEquals(richtextConfig.getProjectId(), configModel.getProjectId());
        assertEquals(richtextConfig.getContent(), configModel.getContent());
        assertEquals(richtextConfig.getDownloadUrl(), configModel.getDownloadUrl());
    }

    @Test
    void json2xml() {
        List<CharacterModel> result = new ArrayList<>();
        result.add(characterMeta);
        when(characterService.selectByType(anyString())).thenReturn(result);


        // 测试顺序融合speak、emotion、animoji和client的情况
        var content = "<speak interruptible=\"false\" startEmotion=\"sleepy\"><animoji><id>left</id></animoji>说话的内容<client><widget><type>chart</type><chart><type>line</type><title>折线图列子</title><data><x>1542772693147</x><y1>47</y1><y2>36</y2></data><data><x>1542774493147</x><y1>24</y1><y2>75</y2></data></chart></widget></client></speak>";
        List<DrmlAction> drmlActions = new ArrayList<>();
        drmlActions.add(DrmlAction.builder()
                .type(DrmlAction.Type.animoji)
                .animoji(DrmlAction.Animoji.builder().id("left").build())
                .build());
        drmlActions.add(DrmlAction.builder()
                .type(DrmlAction.Type.speak)
                .speak("说话的内容")
                .startEmotion("sleepy")
                .build());
        drmlActions.add(DrmlAction.builder()
                .type(DrmlAction.Type.client)
                .client("{\"widget\":{\"type\":\"chart\",\"chart\":{\"type\":\"line\",\"title\":\"折线图列子\",\"data\":[{\"x\":1542772693147,\"y1\":47,\"y2\":36},{\"x\":1542774493147,\"y1\":24,\"y2\":75}]}}}\n")
                .build());

        var request = JsonToXmlRequest.builder()
                .characterImage("UE4_RENDER_VERTICAL")
                .interruptible(false)
                .drml(drmlActions)
                .build();
        var response = service.json2xml(request);
        assertEquals(response.getContent(), content);

        // 测试顺序融合多个speak、emotion、animoji和client的情况
        content = "<speak endEmotion=\"sleepy\" interruptible=\"false\" startEmotion=\"sleepy\"><animoji><id>left</id></animoji>说话的内容<client><widget><type>chart</type><chart><type>line</type><title>折线图列子</title><data><x>1542772693147</x><y1>47</y1><y2>36</y2></data><data><x>1542774493147</x><y1>24</y1><y2>75</y2></data></chart></widget></client>说话的内容2<animoji><id>right</id></animoji></speak>";
        drmlActions.add(DrmlAction.builder()
                .type(DrmlAction.Type.speak)
                .speak("说话的内容2")
                .endEmotion("sleepy")
                .build());
        drmlActions.add(DrmlAction.builder()
                .type(DrmlAction.Type.animoji)
                .animoji(DrmlAction.Animoji.builder().id("right").build())
                .build());

        request = JsonToXmlRequest.builder()
                .characterImage("UE4_RENDER_VERTICAL")
                .interruptible(false)
                .drml(drmlActions)
                .build();
        response = service.json2xml(request);
        assertEquals(response.getContent(), content);

        // 测试并行融合speak、emotion、animojis
        content = "<speak><fusion><speak><interruptible>true</interruptible><startEmotion>think</startEmotion><endEmotion>smile</endEmotion><content>同时说话和做动作</content></speak><animojis><id>nod</id></animojis><animojis><id>left</id></animojis></fusion></speak>";
        drmlActions.clear();
        drmlActions.add(DrmlAction.builder()
                .type(DrmlAction.Type.fusion)
                .fusion(DrmlAction.Fusion.builder()
                        .speak("同时说话和做动作")
                        .startEmotion("think")
                        .endEmotion("smile")
                        .animojis(Arrays.asList("nod", "left")).build())
                .build());
        request = JsonToXmlRequest.builder()
                .characterImage("UE4_RENDER_VERTICAL")
                .interruptible(true)
                .drml(drmlActions)
                .build();
        response = service.json2xml(request);
        System.out.println(response);
        assertEquals(response.getContent(), content);

        String json = "{\"character\":{\"facial\":{\"face_fat\":0,\"chin_shape\":0,"
                + "\"eyes\":{\"size\":0,\"almond\":0,\"amorous\":0,\"phoenix\":0},"
                + "\"nose\":{\"len\":0,\"broad\":0,\"shape\":0,\"wing\":0},"
                + "\"mouth\":{\"size\":0,\"upper_lip\":0,\"lower_lip\":0}}}}";
        content = "<speak interruptible=\"true\"><character><character><facial><face_fat>0</face_fat><chin_shape>0"
                + "</chin_shape><eyes><size>0</size><almond>0</almond><amorous>0</amorous><phoenix>0</phoenix></eyes"
                + "><nose><len>0</len><broad>0</broad><shape>0</shape><wing>0</wing></nose><mouth><size>0</size"
                + "><upper_lip>0</upper_lip><lower_lip>0</lower_lip></mouth></facial></character></character>"
                + "<silence time=\"20s\"/></speak>";
        drmlActions.clear();
        drmlActions.add(DrmlAction.builder()
                .type(DrmlAction.Type.character)
                .character(json)
                .build());
        drmlActions.add(DrmlAction.builder()
                .type(DrmlAction.Type.silence)
                .silence("20s")
                .build());
        request = JsonToXmlRequest.builder()
                .characterImage("UE4_RENDER_VERTICAL")
                .interruptible(true)
                .drml(drmlActions)
                .build();
        response = service.json2xml(request);
        assertEquals(response.getContent(), content);

        json =
            "{\"display\":{\"resolution\":{\"horizontal\":1920,\"vertical\":1080},\"background\":{\"type\":\"image\","
                + "\"source\":\"http\",\"value\":\"http://10.136.172"
                + ".11:<8086/digital-human/backend-upload-file/2023-07-26/188E0669A3702D56AA6A2DFFF8131423"
                + "-1690363099421.png?AWSAccessKeyId=OYR6AKE3OI5HDERHTCPW&Expires=4843963100&Signature=HTf"
                + "%2FzztSSJ2sWZpB9Z7ie4luZdg%3D\"},\"camera\":{\"id\":27},\"bitrate\":3000}}";
        content =
            "<speak interruptible=\"true\"><display><display><resolution><horizontal>1920</horizontal><vertical>1080"
                + "</vertical></resolution><background><type>image</type><source>http</source><value>http://10.136"
                + ".172.11:<8086/digital-human/backend-upload-file/2023-07-26/188E0669A3702D56AA6A2DFFF8131423"
                + "-1690363099421.png?AWSAccessKeyId=OYR6AKE3OI5HDERHTCPW&Expires=4843963100&Signature=HTf"
                + "%2FzztSSJ2sWZpB9Z7ie4luZdg%3D</value></background><camera><id>27</id></camera><bitrate>3000"
                + "</bitrate></display></display></speak>";
        drmlActions.clear();
        drmlActions.add(DrmlAction.builder()
            .type(DrmlAction.Type.display)
            .display(json)
            .build());
        request = JsonToXmlRequest.builder()
            .characterImage("UE4_RENDER_VERTICAL")
            .interruptible(true)
            .drml(drmlActions)
            .build();
        response = service.json2xml(request);
        assertEquals(response.getContent(), content);
    }
}
