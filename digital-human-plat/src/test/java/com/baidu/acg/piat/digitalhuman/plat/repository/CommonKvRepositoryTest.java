package com.baidu.acg.piat.digitalhuman.plat.repository;

import org.apache.commons.collections4.CollectionUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.orm.jpa.DataJpaTest;
import org.springframework.dao.DataIntegrityViolationException;

import java.util.List;

import com.baidu.acg.piat.digitalhuman.plat.dao.CommonKvRepository;
import com.baidu.acg.piat.digitalhuman.plat.model.CommonKvModel;

/**
 * <AUTHOR>
 * @since 2021/08/05
 */
@DataJpaTest
class CommonKvRepositoryTest {
    @Autowired
    private CommonKvRepository repository;

    private String userId = "user1";

    @BeforeEach
    private void init() {

        CommonKvModel model = CommonKvModel.builder().userId(userId).key("key").value("value").build();
        CommonKvModel model2 = CommonKvModel.builder().userId(userId).key("key2").value("value").build();

        repository.save(model);
        repository.save(model2);
    }

    @Test
    void saveTest() {
        List<CommonKvModel> list = repository.findAllByUserId(userId);
        Assertions.assertTrue(!CollectionUtils.isEmpty(list));
        Assertions.assertEquals(2, list.size());
        Assertions.assertThrows(DataIntegrityViolationException.class,
                () -> repository.save(CommonKvModel.builder().userId(userId).key("key").value("value").build()));
    }

    @Test
    void findAllByUserIdAndKeyIn() {
        List<CommonKvModel> list = repository.findAllByUserIdAndKeyIn(userId, List.of("key"));
        Assertions.assertTrue(!CollectionUtils.isEmpty(list));
        Assertions.assertEquals(1, list.size());
    }
}