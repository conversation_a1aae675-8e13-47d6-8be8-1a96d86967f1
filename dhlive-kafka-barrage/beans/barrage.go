package beans

type JdBarrageJqcReqMessages struct {
	List   []JdBarrageJqcReqMessage `json:"list" binding:"required"`
	Appkey string                   `json:"appKey" binding:"required"`
}

type JdBarrageJqcReqMessage struct {
	MessageId string `json:"messageId"`
	Topic     string `json:"topic"`
	Body      string `json:"body"`
}

type JdBarrageJqcMessage struct {
	Nickname   string `json:"nickname"`
	Time       int64  `json:"time"`
	Type       string `json:"type"`     // 消息类型，1-查看sku；2-添加到购物车；3-点击关注；4-点赞直播间；5-用户进入直播间；6-求讲解；7-提问商品,8-用户弹幕；9-抽奖后自动弹幕；10-预设的弹幕
	AnchorId   string `json:"anchorId"` // 主播ID
	LiveId     int64  `json:"liveId"`
	Content    string `json:"content"`
	SkuId      string `json:"skuId"`
	AnchorType string `json:"anchorType"` // 主播类型 1-商家
}
