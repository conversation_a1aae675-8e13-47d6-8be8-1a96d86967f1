package model

import (
	"acg-ai-go-common/gomysql"
	"acg-ai-go-common/goredis"
	"acg-ai-go-common/logger"
	"acg-ai-go-common/utils"
	"context"
	"encoding/json"
	"fmt"
	"gorm.io/gorm"
	"time"
)

const (
	JDBarrageRedisKey  = "kafka-barrage:jdBarrage"
	JDBarrageIsSaveKey = "third-party:jdBarrage"
)

type JDBarrageData struct {
	ID         uint64         `json:"id" gorm:"column:id;primarykey"`
	DataId     string         `json:"dataId" gorm:"column:dataId"` // uuid
	Nickname   string         `json:"nickname" gorm:"column:nickname"`
	Time       int64          `json:"time" gorm:"column:time"`
	Type       string         `json:"type" gorm:"column:type"`         // 消息类型，1-查看sku；2-添加到购物车；3-点击关注；4-点赞直播间；5-用户进入直播间；6-求讲解；7-提问商品,8-用户弹幕；9-抽奖后自动弹幕；10-预设的弹幕
	AnchorId   string         `json:"anchorId" gorm:"column:anchorId"` // 主播ID
	LiveId     int64          `json:"liveId" gorm:"column:liveId"`
	Content    string         `json:"content" gorm:"column:content"`
	SkuId      string         `json:"skuId" gorm:"column:skuId"`
	AnchorType string         `json:"anchorType" gorm:"column:anchorType"`     // 主播类型 1-商家
	VenderId   string         `json:"venderId" gorm:"column:venderId"`         // 商家ID
	SaveTime   int64          `json:"saveTime" gorm:"column:saveTime"`         // 弹幕保存时间
	CreatedAt  time.Time      `json:"createdAt" gorm:"column:createdAt"`       // 创建时间
	UpdatedAt  time.Time      `json:"updatedAt" gorm:"column:updateAt"`        // 更新时间
	DeletedAt  gorm.DeletedAt `json:"deletedAt" gorm:"column:deletedAt;index"` // 删除时间/标记删除
}

func InitModel() {
	if err := gomysql.DB.AutoMigrate(&JDBarrageData{}); err != nil {
		logger.Log.Errorf("InitModel JDBarrageData error : %s", err)
	}
}

func (or *JDBarrageData) TableName() string {
	return "jd_barrage_data"
}

func (or *JDBarrageData) Insert() error {
	// 创建数据
	return gomysql.DB.Create(&or).Error
}

type JDBarrage struct {
	Id         string `json:"id"` // uuid ，唯一ID
	Nickname   string `json:"nickname"`
	Time       int64  `json:"time"`
	Type       string `json:"type"`     // 消息类型，1-查看sku；2-添加到购物车；3-点击关注；4-点赞直播间；5-用户进入直播间；6-求讲解；7-提问商品,8-用户弹幕；9-抽奖后自动弹幕；10-预设的弹幕
	AnchorId   string `json:"anchorId"` // 主播ID
	LiveId     int64  `json:"liveId"`
	Content    string `json:"content"`
	SkuId      string `json:"skuId"`
	AnchorType string `json:"anchorType"` // 主播类型 1-商家
	VenderId   string `json:"venderId"`   // 商家ID
	SaveTime   int64  `json:"saveTime"`   // 弹幕保存时间
}

func SaveJDBarrage(key string, barrages []*JDBarrage) error {
	data := make([]string, 0)
	for _, v := range barrages {
		if v != nil {
			marshal, err := json.Marshal(barrages)
			if err != nil {
				logger.Log.Errorf("SaveJDBarrage marshal data error , err : %s ,data :%+v", err, v)
				continue
			}
			data = append(data, string(marshal))
		}
	}
	redisKey := fmt.Sprintf("%s:%s:%s", utils.GetNameByRunEnv(), JDBarrageRedisKey, key)
	redisIeSaveKey := fmt.Sprintf("%s:%s:%s", utils.GetNameByRunEnv(), JDBarrageIsSaveKey, key)
	logger.Log.Infof("SaveJDBarrage save redis key : %s", redisKey)
	result, _ := goredis.GetClientV2().Exists(context.Background(), redisIeSaveKey).Result()
	if 0 == result {
		logger.Log.Infof("SaveJDBarrage redis key not exist , key : %s", redisIeSaveKey)
		return nil
	}

	for _, v := range barrages {
		go SaveJDBarrageData(v)
	}
	_, err := goredis.GetClientV2().LPush(context.Background(), redisKey, data).Result()
	return err
}

func SaveJDBarrageData(barrages *JDBarrage) {
	if barrages == nil {
		return
	}
	data := &JDBarrageData{
		DataId:     barrages.Id,
		Nickname:   barrages.Nickname,
		Time:       barrages.Time,
		Type:       barrages.Type,
		AnchorId:   barrages.AnchorId,
		LiveId:     barrages.LiveId,
		Content:    barrages.Content,
		SkuId:      barrages.SkuId,
		AnchorType: barrages.AnchorType,
		VenderId:   barrages.VenderId,
		SaveTime:   barrages.SaveTime,
	}
	err := data.Insert()
	if err != nil {
		logger.Log.Errorf("SaveJDBarrageData insert data error , err : %s ,data :%+v", err, barrages)
	}
}
