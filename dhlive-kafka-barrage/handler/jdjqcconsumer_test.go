package handler

import (
	"acg-ai-go-common/global"
	"acg-ai-go-common/gomysql"
	"acg-ai-go-common/goredis"
	"context"
	"dhlive-kafka-barrage/model"
	"testing"
	"time"
)

func TestA(t *testing.T) {
	global.ServerSetting.RedisSetting = &global.RedisSetting{
		Addr:     "127.0.0.1:6777",
		Username: "default",
	}
	//
	goredis.InitRedisV2()
	strings, err := goredis.GetClientV2().LRange(context.TODO(), "DEV:kafka-barrage:jdBarrage:23055065#16736007", 0, -1).Result()
	if err != nil {
		t.<PERSON>rror(err)
		t.Fatal()
	}
	for _, v := range strings {
		t.Log(v)
	}
	t.Log(strings)

}

func TestMysql(t *testing.T) {
	global.ServerSetting.MysqlSetting = &global.MysqlSetting{
		HostPortSetting: global.HostPortSetting{
			Host: "localhost",
			Port: 3366,
		},
		UserNamePwdSetting: global.UserNamePwdSetting{
			Username: "dhlive_tp_plat",
			Password: "Hi109@123",
		},
		Database:     "dhlive_third_platform_sandbox",
		MaxOpenConns: 1000,
		MaxIdleConns: 100,
	}

	gomysql.InitDB(global.ServerSetting.MysqlSetting)

	t1 := time.Now().UnixMilli()

	t3 := time.Now()
	for i := 0; i <= 10; i++ {
		data := &model.JDBarrageData{
			DataId:     "barrages.Id",
			Nickname:   "barrages.Nickname",
			Time:       time.Now().UnixMilli(),
			Type:       "barrages.Type",
			AnchorId:   "barrages.AnchorId",
			LiveId:     456,
			Content:    "barrages.Content",
			SkuId:      "barrages.SkuId",
			AnchorType: "barrages.AnchorType",
			VenderId:   "barrages.VenderId",
			SaveTime:   time.Now().UnixMilli(),
		}
		err := data.Insert()
		if err != nil {
			t.Log(err)
			t.Fatal()
		}
	}
	gomysql.DB.CreateInBatches()
	t.Log(time.Now().UnixMilli() - t1)
	t.Log(time.Since(t3))
}
