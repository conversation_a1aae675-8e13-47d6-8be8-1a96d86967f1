package handler

import (
	commProto "acg-ai-go-common/beans/proto"
	"acg-ai-go-common/logger"
	"dhlive-kafka-barrage/beans"
	"dhlive-kafka-barrage/model"
	"encoding/json"
	"fmt"
	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"net/http"
	"time"
)

// ConsumerJdJqcData 消费京东JQC数据
func ConsumerJdJqcData(c *gin.Context) {
	req := &beans.JdBarrageJqcReqMessages{}
	if err := c.ShouldBind(&req); err != nil {
		logger.Log.Errorf("ConsumerJdJqcData bind param fail, err:%v", err)
		c.JSO<PERSON>(http.StatusOK, commProto.NewCommRsp(100001, "参数异常"))
		return
	}

	// jd jqc 直播数据，可能会存在同一商家账户的多个直播间，所以需要将数据分开存储
	liveDataMap := make(map[string][]*model.JDBarrage, 0)

	t := time.Now().UnixMilli()
	for _, v := range req.List {
		msg := &beans.JdBarrageJqcMessage{}
		err := json.Unmarshal([]byte(v.Body), msg)
		if err != nil {
			logger.Log.Errorf("ConsumerJdJqcData unmarshal data fail, err:%v", err)
			continue
		}
		newUUID, _ := uuid.NewUUID()
		data := model.JDBarrage{
			Id:         newUUID.String(),
			Nickname:   msg.Nickname,
			Time:       msg.Time,
			Type:       msg.Type,
			AnchorId:   msg.AnchorId,
			LiveId:     msg.LiveId,
			Content:    msg.Content,
			SkuId:      msg.SkuId,
			AnchorType: msg.AnchorType,
			SaveTime:   t,
		}
		logger.Log.Infof("ConsumerJdJqcData bind param succ, data:%v", msg)
		key := fmt.Sprintf("%s:%d", req.Appkey, msg.LiveId)
		if liveDataMap[key] == nil {
			liveDataMap[key] = make([]*model.JDBarrage, 0)
		}
		liveDataMap[key] = append(liveDataMap[key], &data)
	}

	for key, v := range liveDataMap {
		err := model.SaveJDBarrage(key, v)
		if err != nil {
			logger.Log.Errorf("ConsumerJdJqcData save jd barrage data fail, err:%v", err)
		}
	}

	logger.Log.Infof("ConsumerJdJqcData bind param succ param:%v", req)
	c.JSON(http.StatusOK, commProto.NewSuccessRsp(""))
}
