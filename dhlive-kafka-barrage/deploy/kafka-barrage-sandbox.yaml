apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: digital-human
    module: dhlive-kafka-barrage
  name: dhlive-kafka-barrage
  namespace: digital-human-sandbox
spec:
  replicas: 1
  minReadySeconds: 0
  strategy:
    type: RollingUpdate # 策略：滚动更新
    rollingUpdate:
      maxSurge: 25%
      maxUnavailable: 25%
  selector:
    matchLabels:
      app: digital-human
      module: dhlive-kafka-barrage
  template:
    metadata:
      labels:
        app: digital-human
        module: dhlive-kafka-barrage
    spec:
      restartPolicy: Always
      # restartPolicy: OnFailure
      # restartPolicy: Never
      containers:
        - name: kafka-barrage
          image: iregistry.baidu-int.com/abc-robot/dhlive-kafka-barrage:20240423_1713843415867
          imagePullPolicy: Always
          command: ["sh"]
          args: ["/home/<USER>/sbin/start.sh"]
          # imagePullPolicy: IfNotPresent
          # imagePullPolicy: Never
          ports:
            - containerPort: 8080
          resources: # 资源限制
            limits:
              cpu: 250m
              memory: 512Mi
            requests:
              cpu: 250m
              memory: 512Mi
          livenessProbe: # 健康检查：存活探针
            httpGet:
              path: /health
              port: 8080
            initialDelaySeconds: 20
            timeoutSeconds: 5
            periodSeconds: 5
          readinessProbe: # 健康检查：就绪探针
            httpGet:
              path: /health
              port: 8080
            initialDelaySeconds: 5
            timeoutSeconds: 1
            periodSeconds: 5
          # volumeMounts:
          #   - name: nginx-volume
          #     mountPath: "/usr/share/nginx"
      imagePullSecrets:
        - name: regcred
        - name: regcred-ccr
        - name: regcred-eccr
      # volumes:
      #   - name: nginx-volume
      #     flexVolume:
      #       driver: "baidubce/cds"
      #       fsType: "ext4"
      #       options:
      #         volumeID: "{id}" # 填写cds的id，注意：cds必须和pod在同一可用区！！
      nodeSelector:
        beta.kubernetes.io/os: linux
      # tolerations: # 容忍污点
      #   - effect: NoExecute
      #     operator: Exists
      #   - effect: NoSchedule
      #     operator: Exists
      # affinity: # 亲和性
      #   nodeAffinity:
      #     requiredDuringSchedulingIgnoredDuringExecution:
      #       nodeSelectorTerms:
      #         - matchExpressions:
      #             - key: failure-domain.beta.kubernetes.io/zone
      #               operator: In
      #               values:
      #                 - zoneA
      #                 - zoneB
      #                 - zoneC
---
apiVersion: v1
kind: Service
metadata:
  labels:
    app: digital-human
    module: dhlive-kafka-barrage
  name: dhlive-kafka-barrage
  namespace: digital-human-sandbox
spec:
  selector:
    app: digital-human
    module: dhlive-kafka-barrage
  ports:
    - protocol: TCP
      port: 8080
      targetPort: 8080
  type: ClusterIP