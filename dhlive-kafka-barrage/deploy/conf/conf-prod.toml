####################################################### 服务配置-生产环境 #######################################################
server-port = 8080
server-name = "kafka-barrage"
log-file-prefix = "localhost"
log-file-path = "./logs/"
log-show-code-file = true
log-show-code-func = true
# consul配置
[consul-setting]
host = "127.0.0.1"
port = 8500
name = ""

# 健康检查地址
health-url = "/health"
check-timeout = "10s"
check-interval = "10s"
deregister-critical-service-after = "30s"

[mysql-setting]
host = "mysql57-online-v3.rdsmidsh9s934wt.rds.bj.baidubce.com"
port = 3306
database = "dhlive_third_platform_online"
username = "dhlive_tp_plat"
password = "Hi109@123"
maxOpenConns = 1000
maxIdlenConns = 100

[redis-setting]
addr = "redis.hibrtswwszeq.scs.bj.baidubce.com:6379"
username = ""
password = ""

[log-es-setting]
host = "http://*************:8200"
username = "superuser"
password = "Baidu_dh123"
