####################################################### 服务配置-开发环境 #######################################################
server-port = 8080
server-name = "kafka-barrage"
log-file-prefix = "localhost"
log-file-path = "./logs/"
log-show-code-file = true
log-show-code-func = true
# consul配置
[consul-setting]
host = "127.0.0.1"
port = 8500
name = ""
# 健康检查地址
health-url = "/health"
check-timeout = "10s"
check-interval = "10s"
deregister-critical-service-after = "30s"

# mysql配置
[mysql-setting]
host = "localhost"
port = 3366
database = "dhlive_third_platform_sandbox"
username = "dhlive_tp_plat"
password = "Hi109@123"
maxOpenConns = 1000
maxIdlenConns = 10

# redis配置
[redis-setting]
addr = "127.0.0.1:6777"
username = ""
password = ""

# 日志上传的elasticsearch配置
[log-es-setting]
host = "http://*************:8200"
username = ""
password = ""
