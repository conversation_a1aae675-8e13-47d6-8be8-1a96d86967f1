package conf

var (
	LocalConfig = &localConfig{}
)

type localConfig struct {
	MySqlSettings         *MysqlSetting         `toml:"mysql-setting"`
	RedisSettings         *RedisSetting         `toml:"redis-setting"`
	ScheduleSettings      *ScheduleSetting      `toml:"schedule-setting"`
	TimeoutSettings       *TimeoutSetting       `toml:"timeout-settings"`
	CacheSettings         *CacheSetting         `toml:"cache-setting"`
	LimiterSettings       *LimiterSetting       `toml:"limiter-setting"`
	MusicAiSettings       *MusicAiSetting       `toml:"music-ai-setting"`
	ElevenLabsSettings    *ElevenLabsSetting    `toml:"eleven-labs-setting"`
	HeygenSettings        *HeygenSetting        `toml:"heygen-setting"`
	TranslateSettings     *TranslateSetting     `toml:"translate-setting"`
	GeminiSettings        *GeminiSetting        `toml:"gemini-setting"`
	DhEngineSettings      *DhEngineSetting      `toml:"dh-engine-setting"`
	MorpheusSettings      *MorpheusSetting      `toml:"morpheus-setting"`
	VideoPipelineSettings *VideoPipelineSetting `toml:"video-pipeline-setting"`
	SubtitleSettings      *SubtitleSetting      `toml:"subtitle-setting"`
	VoiceTuningSettings   *VoiceTuningSetting   `toml:"voice-tuning-setting"`
	VoiceSettings         *VoiceSetting         `toml:"voice-setting"`
	MinimaxSettings       *MinimaxSetting       `toml:"minimax-setting"`
}

type RedisSetting struct {
	SentinelNodes    string `toml:"sentinelNodes"`    // 哨兵集群节点，ip:port，多个节点使用,分割，eg: "127.0.0.1:26379,127.0.0.1:26380,127.0.0.1:26381"
	SentinelUsername string `toml:"sentinelUsername"` // 哨兵节点用户名，可为""
	SentinelPassword string `toml:"sentinelPassword"` // 哨兵节点密码，可为""
	RouteByLatency   bool   `toml:"routeByLatency"`   // 是否将读取命令路由到从节点
	MasterName       string `toml:"masterName"`       // redis集群主节点名称
	Addr             string `toml:"addr"`             // 单节点地址，格式为ip:port，eg: "127.0.0.1:6379"
	Username         string `toml:"username"`         // redis集群主节点用户名
	Password         string `toml:"password"`         // redis集群主节点密码
	PoolSize         int    `toml:"poolSize"`         // redis连接池大小
	MinIdleConns     int    `toml:"minIdleConns"`     // 最大空闲连接数
	RedisEnv         string `toml:"redisEnv"`         // redis环境，dev/test/prod
}

type HostPortSetting struct {
	Host string `toml:"host"`
	Port int    `toml:"port"`
}

type UserNamePwdSetting struct {
	Username string `toml:"username"`
	Password string `toml:"password"`
}

type MysqlSetting struct {
	HostPortSetting
	UserNamePwdSetting
	Database     string `toml:"database"`
	MaxOpenConns int    `toml:"maxOpenConns"`
	MaxIdleConns int    `toml:"maxIdleConns"`
}

type ScheduleSetting struct {
	MaxRunningSize    int `toml:"maxRunningSize"`    // 主任务并发的最大值
	MaxScheduleSize   int `toml:"maxScheduleSize"`   // 一轮调度允许操作的任务数
	MaxTaskRetryCount int `toml:"maxTaskRetryCount"` // 任务出错重试的最大次数
	HttpRetryCount    int `toml:"httpRetryCount"`    // http 请求的重试次数
}

type TimeoutSetting struct {
	TaskTimeoutHour      int `toml:"taskTimeoutHour"`      // 整条任务的处理超时
	TaskSubTimeoutHour   int `toml:"taskSubTimeoutHour"`   // 每一个异步任务公用的超时
	TaskTrainTimeoutHour int `toml:"taskTrainTimeoutHour"` // 训练人像的超时
	TaskVideoTimeoutHour int `toml:"taskVideoTimeoutHour"` // 人像合成的超时
}

type CacheSetting struct {
	RootPath        string `toml:"rootPath"`
	CleanPeriodDay  int    `toml:"cleanPeriodDay"`
	CleanTimeoutDay int    `toml:"cleanTimeoutDay"`
	UseTrueFileName bool   `toml:"useTrueFileName"`
}

type LimiterSetting struct {
	QuotaTimeoutHour int32 `toml:"quotaTimeoutHour"`
	SetTimeoutHour   int32 `toml:"setTimeoutHour"`
	LockTimeoutSec   int32 `toml:"lockTimeoutSec"`
}

type MusicAiSetting struct {
	ApiKey           string `toml:"apiKey"`
	Workflow         string `toml:"workflow"`
	User             string `toml:"user"`
	ConcurrencyLimit int    `toml:"concurrencyLimit"`
}

type VoiceSetting struct {
	CloneEngine       string  `toml:"cloneEngine"`
	SampleMinDuration float64 `toml:"sampleMinDuration"`
	SampleMaxDuration float64 `toml:"sampleMaxDuration"`
	SampleMaxSize     int64   `toml:"sampleMaxSize"`

	VocalVolume float64 `toml:"vocalVolume"`
	BackVolume  float64 `toml:"backVolume"`
}

type MinimaxSetting struct {
	Host    string `toml:"host"`
	ApiKey  string `toml:"apiKey"`
	GroupId string `toml:"groupId"`

	ConcurrencyLimit int `toml:"concurrencyLimit"`
	DeleteVoiceHour  int `toml:"deleteVoiceHour"`
}

type ElevenLabsSetting struct {
	ApiKey string `toml:"apiKey"`

	AsrUrl             string  `toml:"asrUrl"`
	AsrModelId         string  `toml:"asrModelId"`
	AsrSentenceGap     float64 `toml:"asrSentenceGap"`
	AsrSentenceTime    float64 `toml:"asrSentenceTime"`
	AsrMinSentenceTime float64 `toml:"asrMinSentenceTime"`
	AsrMinSentenceGap  float64 `toml:"asrMinSentenceGap"`

	ConcurrencyLimit int `toml:"concurrencyLimit"`

	DeleteVoiceHour int `toml:"deleteVoiceHour"`
}

type HeygenSetting struct {
	ApiKey                        string `toml:"apiKey"`
	HgTranslateMaxNum             int    `toml:"hgTranslateMaxNum"`             // 调用heygen时允许同时处理的最大任务数
	GetTargetLanguages            string `toml:"getTargetLanguagesUrl"`         // get  https://api.heygen.com/v2/video_translate/target_languages
	GenerateProofreadUrl          string `toml:"generateProofreadUrl"`          // post  https://api.heygen.com/v2/video_translate/proofread
	CheckProofreadUrl             string `toml:"checkProofreadUrl"`             // get   https://api.heygen.com/v2/video_translate/proofread/status/
	GenerateVideoFromProofreadUrl string `toml:"generateVideoFromProofreadUrl"` // post  https://api.heygen.com/v2/video_translate/proofread/<proofread_id>/generate
	CheckTranslateStatusUrl       string `toml:"checkTranslateStatusUrl"`       // get   https://api.heygen.com/v2/video_translate/{video_translate_id}
	VideoTranslateCaptionUrl      string `toml:"videoTranslateCaptionUrl"`      // get   https://api.heygen.com/v2/video_translate/caption
}

type TranslateSetting struct {
	GoogleCredential string `toml:"googleCredential"`
	ConcurrencyLimit int    `toml:"concurrencyLimit"`
}

type GeminiSetting struct {
	GoogleCredential string `toml:"googleCredential"`
	ProjectId        string `toml:"projectId"`
	Location         string `toml:"location"`
	Model            string `toml:"model"`
	ConcurrencyLimit int    `toml:"concurrencyLimit"`
}

type DhEngineSetting struct {
	Host             string `toml:"host"`
	AppId            string `toml:"appId"`
	AppKey           string `toml:"appKey"`
	ConcurrencyLimit int    `toml:"concurrencyLimit"`
}

type MorpheusSetting struct {
	Host             string `toml:"host"`
	ResourceLabel    string `toml:"resourceLabel"`
	SceneLabel       string `toml:"sceneLabel"`
	CallbackHost     string `toml:"callbackHost"`
	ConcurrencyLimit int    `toml:"concurrencyLimit"`
}

type VideoPipelineSetting struct {
	Host             string `toml:"host"`
	AppId            string `toml:"appId"`
	AppKey           string `toml:"appKey"`
	Transparent      bool   `toml:"transparent"`
	CallbackHost     string `toml:"callbackHost"`
	ConcurrencyLimit int    `toml:"concurrencyLimit"`
}

type SubtitleSetting struct {
	MaxChars     int     `toml:"maxChars"`
	MaxLineChars int     `toml:"maxLineChars"`
	MaxDuration  float64 `toml:"maxDuration"`

	WideCharLanguage []string `toml:"wideCharLanguage"`
	MaxWideChars     int      `toml:"maxWideChars"`
	MaxWideLineChars int      `toml:"maxWideLineChars"`
	MaxWideDuration  float64  `toml:"maxWideDuration"`

	MaxLandscapeCharsScale     float64 `toml:"maxLandscapeCharsScale"`
	MaxLandscapeLineCharsScale float64 `toml:"maxLandscapeLineCharsScale"`
	MaxLandscapeDurationScale  float64 `toml:"maxLandscapeDurationScale"`

	UsePunctuation bool `toml:"usePunctuation"`
	MaxLines       int  `toml:"maxLines"`
}

type VoiceTuningSetting struct {
	Threshold    float64 `toml:"threshold"`
	MaxThreshold float64 `toml:"maxThreshold"`
	MaxStep      int     `toml:"maxStep"`
	SpeedWeight  float64 `toml:"speedWeight"`
	Prompt       string  `toml:"prompt"`
	PromptFile   string  `toml:"promptFile"`
}
