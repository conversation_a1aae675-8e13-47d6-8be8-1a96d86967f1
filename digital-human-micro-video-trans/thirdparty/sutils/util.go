package sutils

import (
	"acg-ai-go-common/utils"
	"bytes"
	"crypto/aes"
	"encoding/base64"
	"encoding/csv"
	"encoding/json"
	"net/url"
	"path"
	"strings"
	"time"
)

// StructToJSON 将结构体转换为JSON字符串
func StructToJSON(v interface{}) (string, error) {
	bytes, err := json.Marshal(v)
	if err != nil {
		return "", err
	}
	return string(bytes), nil
}

// JSONToStruct 将JSON字符串转换为结构体
func JSONToStruct(jsonString string, v interface{}) error {
	return json.Unmarshal([]byte(jsonString), v)
}

func URLAddQueryParam(baseURL string, params map[string]string) (string, error) {
	u, err := url.Parse(baseURL)
	if err != nil {
		return "", err
	}

	q := u.Query()
	for k, v := range params {
		q.Set(k, v)
	}
	u.RawQuery = q.Encode()

	return u.String(), nil
}

func GenerateActAndPwd() (string, string) {
	return utils.RandStringRunes(7), utils.RandStringRunes(16)
}

func GenerateCsvBytes(tableHeads []string, data [][]string) (*bytes.Buffer, error) {
	//内容先写入buffer缓存
	buff := new(bytes.Buffer)
	//写入UTF-8 BOM,此处如果不写入就会导致写入的汉字乱码
	buff.WriteString("\xEF\xBB\xBF")
	csvWriter := csv.NewWriter(buff)
	// 写入表头
	if err := csvWriter.Write(tableHeads); err != nil {
		return nil, err
	}
	// 写入数据
	for _, d := range data {
		if err := csvWriter.Write(d); err != nil {
			return nil, err
		}
	}
	csvWriter.Flush()
	return buff, nil
}

// GetNMonthAgoTime 获取startTime N个月前的时间
func GetNMonthAgoTime(startTime time.Time, n int) time.Time {
	return startTime.AddDate(0, -n, 0)
}

func AesKeyGenerate(str string) []byte {
	str += "0000000000000000"
	return []byte(str[:16])
}

func AESEcbDecrypt(data, key []byte) (string, error) {
	data, err := base64.StdEncoding.DecodeString(string(data))
	if err != nil {
		return "", err
	}
	block, err := aes.NewCipher(key)
	if err != nil {
		return "", err
	}
	decrypted := make([]byte, len(data))
	size := block.BlockSize()

	for bs, be := 0, size; bs < len(data); bs, be = bs+size, be+size {
		block.Decrypt(decrypted[bs:be], data[bs:be])
	}

	return string(PKCS7UnPadding(decrypted)), nil
}

func AESEcbEncrypt(data, key []byte) (string, error) {
	block, err := aes.NewCipher(key)
	if err != nil {
		return "", err
	}
	data = PKCS7Padding(data, block.BlockSize())
	decrypted := make([]byte, len(data))
	size := block.BlockSize()

	for bs, be := 0, size; bs < len(data); bs, be = bs+size, be+size {
		block.Encrypt(decrypted[bs:be], data[bs:be])
	}

	return base64.StdEncoding.EncodeToString(decrypted), nil
}

func PKCS7Padding(ciphertext []byte, blocksize int) []byte {
	padding := blocksize - len(ciphertext)%blocksize
	if padding < 0 {
		return []byte("")
	}
	padtext := bytes.Repeat([]byte{byte(padding)}, padding)
	return append(ciphertext, padtext...)
}

func PKCS7UnPadding(origData []byte) []byte {
	length := len(origData)
	unpadding := int(origData[length-1])
	if length-unpadding < 0 {
		return []byte("")
	}
	return origData[:(length - unpadding)]
}

func ExtractFilenameFromURL(urlString string) (string, error) {
	// 去除URL中可能存在的fragment标识符（#后面的部分）
	urlString = strings.Split(urlString, "#")[0]

	parsedURL, err := url.Parse(urlString)
	if err != nil {
		return "", err
	}
	// 获取URL中的path部分
	filename := parsedURL.Path

	return filename, nil
}

// 返回字符串s中前n个字符组成的子串
func SubstringByRuneCount(s string, startIndex, endIdex int) string {
	if startIndex < 0 && startIndex >= endIdex {
		return ""
	}

	runeCount := 0
	var substring string
	for _, runeValue := range s {
		if runeCount < startIndex {
			runeCount++
			continue
		}

		if runeCount > endIdex {
			break
		}

		substring += string(runeValue)
		runeCount++
	}
	return substring
}
func EncodeUrl(fileUrl string) (string, error) {
	// 解析 URL
	parsedURL, err := url.Parse(fileUrl)
	if err != nil {
		return "", err
	}

	// 从 URL 的 Path 获取文件名
	fileName := path.Base(parsedURL.Path)
	// 对文件名进行 URL 编码
	encodedFileName := url.PathEscape(fileName)
	// encodedFileName = strings.Replace(encodedFileName, "+", "%20", -1)
	// 替换原始 URL 中的文件名
	return strings.Replace(fileUrl, fileName, encodedFileName, 1), nil
}
