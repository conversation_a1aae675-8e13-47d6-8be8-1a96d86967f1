package ffmpeg

import (
	"acg-ai-go-common/logger"
	"acg-ai-go-common/utils"
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"os"
	"os/exec"
	"strconv"
	"strings"
	"time"
)

// ExtractAudio 从视频文件中提取音频
func ExtractAudio(ctx context.Context, inputVideo string, outputAudio string, codec string, channel int,
	sampleRate int) error {
	cmd := exec.Command(
		"ffmpeg",
		"-i", inputVideo, // 输入视频
		"-vn",            // 禁用视频流
		"-acodec", codec, // 16-bit PCM
		"-ar", fmt.Sprintf("%d", sampleRate), // 采样率 16kHz
		"-ac", fmt.Sprintf("%d", channel), // 单声道
		"-y", // 覆盖输出
		outputAudio,
	)

	cmdStr := strings.Join(cmd.Args, " ")
	logger.Log.Infof(utils.MMark(ctx)+"ffmpeg extract audio cmd: { %s }", cmdStr)
	tStart := time.Now()

	//cmd.Stdout = os.Stdout
	//cmd.Stderr = os.Stderr

	// 仅捕获异常信息
	var stderr bytes.Buffer
	cmd.Stderr = &stderr

	if err := cmd.Run(); err != nil {
		return fmt.Errorf("ExtractAudio error: %v, detail: %s", err, stderr.String())
	}
	duration := float64(time.Since(tStart).Milliseconds()) / 1000.0
	logger.Log.Infof(utils.MMark(ctx)+"finish ffmpeg with duration: %.3fs", duration)

	return nil
}

// MergeAudioVideo 将音频和视频合成，并可指定音频编码、声道数和采样率
func MergeAudioVideo(ctx context.Context, inputVideo string, inputAudio string, output string, audioCodec string,
	channels int, sampleRate int) error {
	cmd := exec.Command("ffmpeg", "-i", inputVideo, "-i", inputAudio,
		"-map", "0:v:0", "-map", "1:a:0", // 选择第一个视频流 + 独立音频流
		"-c:v", "copy",
		"-c:a", audioCodec,
		"-ac", fmt.Sprintf("%d", channels),
		"-ar", fmt.Sprintf("%d", sampleRate),
		"-y", output)

	cmdStr := strings.Join(cmd.Args, " ")
	logger.Log.Infof(utils.MMark(ctx)+"ffmpeg merge audio cmd: { %s }", cmdStr)
	tStart := time.Now()

	//cmd.Stdout = os.Stdout
	//cmd.Stderr = os.Stderr

	// 仅捕获异常信息
	var stderr bytes.Buffer
	cmd.Stderr = &stderr

	if err := cmd.Run(); err != nil {
		return fmt.Errorf("MergeAudioVideo error: %v, detail: %s", err, stderr.String())
	}

	duration := float64(time.Since(tStart).Milliseconds()) / 1000.0
	logger.Log.Infof(utils.MMark(ctx)+"finish ffmpeg with duration: %.3fs", duration)

	return nil
}

// MixAudioIntoVideo 将音频和视频合成，并混合视频原有音频与新音频
func MixAudioIntoVideo(ctx context.Context, inputVideo string, inputAudio string, output string, audioCodec string,
	channels int, sampleRate int) error {

	cmd := exec.Command(
		"ffmpeg",
		"-i", inputVideo,
		"-i", inputAudio,
		"-filter_complex",
		"[0:a]volume=1.5[a0];[1:a]volume=1.5[a1];[a0][a1]amix=inputs=2:duration=longest:dropout_transition=2:normalize=0[aout]",
		"-map", "0:v", // 使用原视频流
		"-map", "[aout]", // 使用混合音频
		"-c:v", "copy", // 视频不重新编码
		"-c:a", audioCodec, // 指定音频编码器（如 aac）
		"-ac", fmt.Sprintf("%d", channels),
		"-ar", fmt.Sprintf("%d", sampleRate),
		"-y", output,
	)

	cmdStr := strings.Join(cmd.Args, " ")
	logger.Log.Infof(utils.MMark(ctx)+"ffmpeg mix audio cmd: { %s }", cmdStr)
	tStart := time.Now()

	//cmd.Stdout = os.Stdout
	//cmd.Stderr = os.Stderr

	// 仅捕获异常信息
	var stderr bytes.Buffer
	cmd.Stderr = &stderr

	if err := cmd.Run(); err != nil {
		return fmt.Errorf("MixAudioIntoVideo error: %v, detail: %s", err, stderr.String())
	}
	duration := float64(time.Since(tStart).Milliseconds()) / 1000.0
	logger.Log.Infof(utils.MMark(ctx)+"finish ffmpeg with duration: %.3fs", duration)

	return nil
}

// MixAudiosIntoVideo 将多个音频和视频合成，并混合视频原有音频与新音频
func MixAudiosIntoVideo(ctx context.Context, inputVideo string, inputAudios []string, output string, audioCodec string,
	channels int, sampleRate int, vol0, vol1 float64) error {

	args := []string{
		"-i", inputVideo,
	}

	// 添加每个音频文件的输入参数
	for _, audio := range inputAudios {
		args = append(args, "-i", audio)
	}

	var filterParts []string
	audioLabels := []string{}

	// 原视频音频作为第一个输入
	filterParts = append(filterParts, fmt.Sprintf("[0:a]volume=%.1f[a0]", vol0))
	audioLabels = append(audioLabels, "[a0]")

	// 其他音频文件，从索引1开始
	for i := range inputAudios {
		inputIndex := i + 1
		label := fmt.Sprintf("a%d", inputIndex)
		filter := fmt.Sprintf("[%d:a]volume=%.1f[%s]", inputIndex, vol1, label)
		filterParts = append(filterParts, filter)
		audioLabels = append(audioLabels, fmt.Sprintf("[%s]", label))
	}

	// amix 合并音频
	amixFilter := fmt.Sprintf("%samix=inputs=%d:duration=longest:dropout_transition=2:normalize=0[aout]",
		strings.Join(audioLabels, ""), len(audioLabels))
	filterParts = append(filterParts, amixFilter)

	// 构造 filter_complex 参数
	filterComplex := strings.Join(filterParts, ";")

	// 添加 filter_complex 参数
	args = append(args, "-filter_complex", filterComplex)

	// map 视频 + 混合音频
	args = append(args,
		"-map", "0:v", // 使用原视频
		"-map", "[aout]", // 混合音频输出
		"-c:v", "copy", // 视频不重新编码
		"-c:a", audioCodec, // 指定音频编码器
		"-ac", fmt.Sprintf("%d", channels),
		"-ar", fmt.Sprintf("%d", sampleRate),
		"-y", output, // 覆盖输出
	)

	cmd := exec.Command("ffmpeg", args...)

	cmdStr := strings.Join(cmd.Args, " ")
	logger.Log.Infof(utils.MMark(ctx)+"ffmpeg mix audio cmd: { %s }", cmdStr)
	tStart := time.Now()

	//cmd.Stdout = os.Stdout
	//cmd.Stderr = os.Stderr

	// 仅捕获异常信息
	var stderr bytes.Buffer
	cmd.Stderr = &stderr

	if err := cmd.Run(); err != nil {
		return fmt.Errorf("MixAudiosIntoVideo error: %v, detail: %s", err, stderr.String())
	}
	duration := float64(time.Since(tStart).Milliseconds()) / 1000.0
	logger.Log.Infof(utils.MMark(ctx)+"finish ffmpeg with duration: %.3fs", duration)

	return nil
}

// ExtractAudioSegment 提取视频中指定时间段的音频为 16kHz 单声道 WAV 文件
func ExtractAudioSegment(ctx context.Context, inputVideo string, start float64, duration float64, audioCodec string,
	channels int, sampleRate int, outputWav string) error {
	cmd := exec.Command("ffmpeg",
		"-ss", fmt.Sprintf("%.3f", start),
		"-t", fmt.Sprintf("%.3f", duration),
		"-i", inputVideo,
		"-c:a", audioCodec,
		"-ar", fmt.Sprintf("%d", sampleRate),
		"-ac", fmt.Sprintf("%d", channels),
		"-vn",
		"-y",
		outputWav,
	)

	cmdStr := strings.Join(cmd.Args, " ")
	logger.Log.Infof(utils.MMark(ctx)+"ffmpeg extract audio cmd: { %s }", cmdStr)
	tStart := time.Now()

	//cmd.Stdout = os.Stdout
	//cmd.Stderr = os.Stderr

	// 仅捕获异常信息
	var stderr bytes.Buffer
	cmd.Stderr = &stderr

	if err := cmd.Run(); err != nil {
		return fmt.Errorf("ExtractAudioSegment error: %v, detail: %s", err, stderr.String())
	}

	ecDuration := float64(time.Since(tStart).Milliseconds()) / 1000.0
	logger.Log.Infof(utils.MMark(ctx)+"finish ffmpeg with duration: %.3fs", ecDuration)
	return nil
}

// MixAudioFilesResample 统一音频格式后混音，保证采样率和声道一致。
// 最终以较长音频为准混合。
func MixAudioFilesResample(ctx context.Context, inputAudio1, inputAudio2, output string, sampleRate, channels int) error {
	temp1 := "temp_input1.wav"
	temp2 := "temp_input2.wav"

	// Step 1: 预处理 - 统一采样率和声道数
	preprocess := func(input, temp string) error {
		cmd := exec.Command("ffmpeg", "-i", input,
			"-ar", fmt.Sprintf("%d", sampleRate),
			"-ac", fmt.Sprintf("%d", channels),
			"-y", temp)
		cmd.Stdout = os.Stdout
		cmd.Stderr = os.Stderr
		return cmd.Run()
	}

	if err := preprocess(inputAudio1, temp1); err != nil {
		return fmt.Errorf("preprocess input1 failed: %w", err)
	}
	if err := preprocess(inputAudio2, temp2); err != nil {
		return fmt.Errorf("preprocess input2 failed: %w", err)
	}

	// Step 2: 混音 - 以较长音频为准
	cmd := exec.Command("ffmpeg",
		"-i", temp1,
		"-i", temp2,
		"-filter_complex", "[0:a][1:a]amix=inputs=2:duration=longest:dropout_transition=0",
		"-y", output)

	cmdStr := strings.Join(cmd.Args, " ")
	logger.Log.Infof(utils.MMark(ctx)+"ffmpeg mix (resample) cmd: { %s }", cmdStr)
	tStart := time.Now()

	//cmd.Stdout = os.Stdout
	//cmd.Stderr = os.Stderr

	// 仅捕获异常信息
	var stderr bytes.Buffer
	cmd.Stderr = &stderr

	if err := cmd.Run(); err != nil {
		return fmt.Errorf("MixAudioFilesResample error: %v, detail: %s", err, stderr.String())
	}
	duration := float64(time.Since(tStart).Milliseconds()) / 1000.0
	logger.Log.Infof(utils.MMark(ctx)+"finish ffmpeg with duration: %.3fs", duration)

	// Step 3: 清理临时文件
	_ = os.Remove(temp1)
	_ = os.Remove(temp2)

	return nil
}

// MixMultipleAudioFiles 将多个音频文件混音成一个音频文件，自动统一采样率和声道数。
// 支持任意数量输入，以最长音频为准，自动清理中间文件。
func MixMultipleAudioFiles(ctx context.Context, tempPath string, inputs []string, output string, sampleRate,
	channels int) error {
	if len(inputs) < 2 {
		return fmt.Errorf("need at least two input files")
	}

	// Step 1: 对每个音频文件做 resample 统一格式
	tempFiles := make([]string, len(inputs))
	for i, input := range inputs {
		temp := fmt.Sprintf("%s/temp_input_%d.wav", tempPath, i)
		tempFiles[i] = temp

		cmd := exec.Command("ffmpeg", "-i", input,
			"-ar", fmt.Sprintf("%d", sampleRate),
			"-ac", fmt.Sprintf("%d", channels),
			"-y", temp)
		cmd.Stdout = os.Stdout
		cmd.Stderr = os.Stderr

		if err := cmd.Run(); err != nil {
			return fmt.Errorf("preprocess input %d failed: %w", i, err)
		}
	}

	// Step 2: 构建混音命令
	args := []string{}
	filterInputs := []string{}
	for i, temp := range tempFiles {
		args = append(args, "-i", temp)
		filterInputs = append(filterInputs, fmt.Sprintf("[%d:a]", i))
	}
	filter := fmt.Sprintf("%samix=inputs=%d:duration=longest:dropout_transition=0",
		strings.Join(filterInputs, ""), len(tempFiles))

	args = append(args, "-filter_complex", filter, "-y", output)

	cmd := exec.Command("ffmpeg", args...)
	cmdStr := strings.Join(cmd.Args, " ")
	logger.Log.Infof(utils.MMark(ctx)+"ffmpeg mix multiple cmd: { %s }", cmdStr)
	tStart := time.Now()

	//cmd.Stdout = os.Stdout
	//cmd.Stderr = os.Stderr

	// 仅捕获异常信息
	var stderr bytes.Buffer
	cmd.Stderr = &stderr

	if err := cmd.Run(); err != nil {
		return fmt.Errorf("MixMultipleAudioFiles error: %v, detail: %s", err, stderr.String())
	}

	ecDuration := float64(time.Since(tStart).Milliseconds()) / 1000.0
	logger.Log.Infof(utils.MMark(ctx)+"finish ffmpeg with duration: %.3fs", ecDuration)

	// Step 3: 删除临时文件
	for _, temp := range tempFiles {
		_ = os.Remove(temp)
	}

	return nil
}

// HasValidAudio 判断音频是否有效（使用 ffprobe）
func HasValidAudio(ctx context.Context, inputFile string) (bool, error) {
	cmd := exec.Command(
		"ffprobe",
		"-v", "quiet",
		"-print_format", "json",
		"-show_entries", "frame_tags=lavfi.astats.Overall.Max_level",
		"-f", "lavfi",
		fmt.Sprintf("amovie=%s,astats=metadata=1:reset=1", inputFile),
	)

	var out bytes.Buffer
	cmd.Stdout = &out

	cmdStr := strings.Join(cmd.Args, " ")
	logger.Log.Infof(utils.MMark(ctx)+"ffprobe check audio cmd: { %s }", cmdStr)
	tStart := time.Now()

	if err := cmd.Run(); err != nil {
		return false, fmt.Errorf("ffprobe run error: %v", err)
	}
	ecDuration := float64(time.Since(tStart).Milliseconds()) / 1000.0
	logger.Log.Infof(utils.MMark(ctx)+"finish ffmpeg with duration: %.3fs", ecDuration)

	// 解析 JSON 输出
	var result struct {
		Frames []struct {
			Tags map[string]string `json:"tags"`
		} `json:"frames"`
	}

	if err := json.Unmarshal(out.Bytes(), &result); err != nil {
		return false, fmt.Errorf("failed to parse ffprobe json: %v", err)
	}

	// 查找最大 dB 值
	maxDB := -1000.0
	for _, frame := range result.Frames {
		if val, ok := frame.Tags["lavfi.astats.Overall.Max_level"]; ok {
			dbValue, err := strconv.ParseFloat(val, 64)
			if err == nil && dbValue > maxDB {
				maxDB = dbValue
			}
		}
	}

	// 判断是否超过阈值（-60dB）
	if maxDB < -60 {
		return false, nil
	}
	return true, nil
}

func IsPlayableByFfmpeg(ctx context.Context, inputVideo string) (bool, string, error) {
	cmd := exec.Command(
		"ffprobe",
		"-v", "error",
		"-show_streams",
		"-select_streams", "v:0",
		"-of", "json",
		inputVideo,
	)

	var out bytes.Buffer
	cmd.Stdout = &out
	cmd.Stderr = &out

	cmdStr := strings.Join(cmd.Args, " ")
	logger.Log.Infof(utils.MMark(ctx)+"ffprobe check codec json cmd: { %s }", cmdStr)

	if err := cmd.Run(); err != nil {
		return false, "", fmt.Errorf("ffprobe run failed: %v", err)
	}

	var result struct {
		Streams []struct {
			CodecName string `json:"codec_name"`
			CodecType string `json:"codec_type"`
		} `json:"streams"`
	}

	if err := json.Unmarshal(out.Bytes(), &result); err != nil {
		return false, "", fmt.Errorf("json unmarshal failed: %v", err)
	}

	for _, stream := range result.Streams {
		if stream.CodecType == "video" && stream.CodecName != "" {
			return true, stream.CodecName, nil
		}
	}

	return false, "", nil // 没有找到有效视频流
}

func HasValidAudioAndVideo(ctx context.Context, inputFile string) (hasVideo, hasAudio bool, err error) {
	cmd := exec.Command(
		"ffprobe",
		"-v", "error",
		"-show_streams",
		"-of", "json",
		inputFile,
	)

	var out bytes.Buffer
	cmd.Stdout = &out
	cmd.Stderr = &out

	cmdStr := strings.Join(cmd.Args, " ")
	logger.Log.Infof(utils.MMark(ctx)+"ffprobe detect av json: { %s }", cmdStr)
	tStart := time.Now()

	if err := cmd.Run(); err != nil {
		return false, false, fmt.Errorf("ffprobe failed: %v", err)
	}
	ecDuration := float64(time.Since(tStart).Milliseconds()) / 1000.0
	logger.Log.Infof(utils.MMark(ctx)+"finish ffmpeg with duration: %.3fs", ecDuration)

	type ffprobeOutput struct {
		Streams []struct {
			CodecType string `json:"codec_type"`
		} `json:"streams"`
	}

	var result ffprobeOutput
	if err := json.Unmarshal(out.Bytes(), &result); err != nil {
		return false, false, fmt.Errorf("json unmarshal failed: %v", err)
	}

	for _, stream := range result.Streams {
		switch stream.CodecType {
		case "video":
			hasVideo = true
		case "audio":
			hasAudio = true
		}
	}

	return hasVideo, hasAudio, nil
}
