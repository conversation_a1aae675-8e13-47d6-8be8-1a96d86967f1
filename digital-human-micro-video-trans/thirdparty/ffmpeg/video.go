package ffmpeg

import (
	"acg-ai-go-common/logger"
	"acg-ai-go-common/utils"
	"bytes"
	"context"
	"fmt"
	"os/exec"
	"strings"
	"time"
)

// ExtractVideoClip 截取视频的 [start, end) 段保存为一个文件
func ExtractVideoClip(ctx context.Context, input string, output string, framerate int, start, end float32) error {
	if end <= start {
		return fmt.Errorf("invalid time range: end (%d) must be greater than start (%d)", end, start)
	}
	args := []string{
		"-ss", fmt.Sprintf("%.3f", start),
		"-to", fmt.Sprintf("%.3f", end),
		"-i", input,
		"-r", fmt.Sprintf("%d", framerate),
		"-g", fmt.Sprintf("%d", framerate),
		"-keyint_min", fmt.Sprintf("%d", framerate),
		"-sc_threshold", "0",
		"-c:v", "libx264",
		"-preset", "slow",
		"-crf", "20",
		"-y",
		output,
	}

	cmd := exec.Command("ffmpeg", args...)

	cmdStr := strings.Join(cmd.Args, " ")
	logger.Log.Infof(utils.MMark(ctx)+"ffmpeg extract video clip cmd: { %s }", cmdStr)
	tStart := time.Now()

	//cmd.Stdout = os.Stdout
	//cmd.Stderr = os.Stderr

	// 仅捕获异常信息
	var stderr bytes.Buffer
	cmd.Stderr = &stderr

	if err := cmd.Run(); err != nil {
		return fmt.Errorf("ExtractVideoClip error: %v, detail: %s", err, stderr.String())
	}

	duration := float64(time.Since(tStart).Milliseconds()) / 1000.0
	logger.Log.Infof(utils.MMark(ctx)+"finish ffmpeg with duration: %.3fs", duration)

	return nil
}

// ConvertToMP4 将任意格式的视频转为 H.264 编码 MP4 文件
func ConvertToMP4(ctx context.Context, frameRate int, input string, output string) error {
	args := []string{
		"-i", input, // 输入文件
		"-r", fmt.Sprintf("%d", frameRate), // 设置输出帧率为 25fps
		"-c:v", "libx264", // 视频编码器
		"-preset", "slow", // 编码速度与压缩效率的平衡
		"-crf", "20", // 质量控制（范围：0~51，越小质量越高）
		"-pix_fmt", "yuv420p", // 保证兼容性（尤其在浏览器中）
		"-y", // 自动覆盖输出文件
		output,
	}

	cmd := exec.Command("ffmpeg", args...)

	// 打印日志（可选）
	cmdStr := strings.Join(cmd.Args, " ")
	logger.Log.Infof(utils.MMark(ctx)+"ffmpeg convert to mp4 cmd: { %s }", cmdStr)
	tStart := time.Now()

	// 标准输出和错误输出重定向
	//cmd.Stdout = os.Stdout
	//cmd.Stderr = os.Stderr

	// 仅捕获异常信息
	var stderr bytes.Buffer
	cmd.Stderr = &stderr

	if err := cmd.Run(); err != nil {
		return fmt.Errorf("ConvertToMP4 error: %v, detail: %s", err, stderr.String())
	}
	duration := float64(time.Since(tStart).Milliseconds()) / 1000.0
	logger.Log.Infof(utils.MMark(ctx)+"finish ffmpeg with duration: %.3fs", duration)

	return nil
}

// MergeSubtitle 合并字幕和视频
func MergeSubtitle(logCtx context.Context, language string, inputVideo string,
	captionPath string, outputVideo string) error {
	language = strings.ToLower(language)

	fontPath := "fontsdir=/usr/share/fonts/noto-sans/NotoSans-Condensed.ttf"
	if strings.Contains(language, "arabic") {
		fontPath = "fontsdir=/usr/share/fonts/noto-arabic/NotoSansArabic-SemiCondensedThin.ttf"
	} else if strings.Contains(language, "hindi") {
		fontPath = "fontsdir=/usr/share/fonts/noto-hindi/NotoSansDevanagariUI-SemiCondensed.ttf"
	} else if strings.Contains(language, "chinese") {
		fontPath = "fontsdir=/usr/share/fonts/opentype/source-han-sans/SourceHanSansCN-Regular.otf"
	} else if strings.Contains(language, "japanese") {
		fontPath = "fontsdir=/usr/share/fonts/opentype/source-han-sans/SourceHanSansJP-Regular.otf"
	} else if strings.Contains(language, "korean") {
		fontPath = "fontsdir=/usr/share/fonts/opentype/source-han-sans/SourceHanSansKR-Regular.otf"
	}

	cmd := exec.Command("ffmpeg",
		"-i", inputVideo,
		"-vf", fmt.Sprintf("subtitles=%s:%s", captionPath, fontPath),
		"-c:v", "libx264",
		"-crf", "20",
		"-preset", "slow",
		"-c:a", "copy",
		"-y",
		outputVideo,
	)

	// 打印日志（可选）
	cmdStr := strings.Join(cmd.Args, " ")
	logger.Log.Infof(utils.MMark(logCtx)+"ffmpeg merge subtitle cmd: { %s }", cmdStr)
	tStart := time.Now()

	// 标准输出和错误输出重定向
	//cmd.Stdout = os.Stdout
	//cmd.Stderr = os.Stderr

	// 仅捕获异常信息
	var stderr bytes.Buffer
	cmd.Stderr = &stderr

	if err := cmd.Run(); err != nil {
		return fmt.Errorf("MergeSubtitle error: %v, detail: %s", err, stderr.String())
	}
	duration := float64(time.Since(tStart).Milliseconds()) / 1000.0
	logger.Log.Infof(utils.MMark(logCtx)+"finish ffmpeg with duration: %.3fs", duration)
	return nil
}

// MakeThumbnail 生成缩略图
func MakeThumbnail(ctx context.Context, videoPath string, imagePath string) error {
	cmd := exec.Command("ffmpeg", "-i", videoPath, "-ss", "00:00:01", "-vframes", "1", "-y", imagePath)

	// 打印日志（可选）
	cmdStr := strings.Join(cmd.Args, " ")
	logger.Log.Infof(utils.MMark(ctx)+"ffmpeg make thumbnail cmd: { %s }", cmdStr)
	tStart := time.Now()

	// 标准输出和错误输出重定向
	//cmd.Stdout = os.Stdout
	//cmd.Stderr = os.Stderr

	// 仅捕获异常信息
	var stderr bytes.Buffer
	cmd.Stderr = &stderr

	if err := cmd.Run(); err != nil {
		return fmt.Errorf("MakeThumbnail error: %v, detail: %s", err, stderr.String())
	}
	duration := float64(time.Since(tStart).Milliseconds()) / 1000.0
	logger.Log.Infof(utils.MMark(ctx)+"finish ffmpeg with duration: %.3fs", duration)
	return nil
}
