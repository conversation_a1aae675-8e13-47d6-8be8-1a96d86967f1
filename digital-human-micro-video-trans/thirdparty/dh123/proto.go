package dh123

import "time"

type TTSParams struct {
	Person string `json:"person,omitempty"` // 发音人ID
	Speed  string `json:"speed,omitempty"`  // 语速（0-15，默认5）
	Volume string `json:"volume,omitempty"` // 音量（0-15，默认5）
	Pitch  string `json:"pitch,omitempty"`  // 语调（0-15，默认5）
}

type GenerateRequest struct {
	TemplateVideoId string     `json:"templateVideoId,omitempty"` // 视频素材文件 ID（与 templateId 二选一）
	DriveType       string     `json:"driveType,omitempty"`       // TEXT（默认）或 VOICE
	Text            string     `json:"text,omitempty"`            // 文本内容（driveType 为 TEXT 时必填）
	TTSParams       *TTSParams `json:"ttsParams,omitempty"`       // TTS参数（driveType 为 TEXT 时必填）
	InputAudioUrl   string     `json:"inputAudioUrl,omitempty"`   // 音频 URL（driveType 为 VOICE 时必填）
	CallbackUrl     string     `json:"callbackUrl,omitempty"`     // 回调地址
}

type GenerateResponse struct {
	Code    int `json:"code"`
	Message struct {
		Global string `json:"global"`
	} `json:"message"`
	Result struct {
		TaskId string `json:"taskId"`
	} `json:"result"`
	RequestId string `json:"requestId"`
	Success   bool   `json:"success"`
}

type CheckStatusResponse struct {
	Code    int `json:"code"`
	Message struct {
		Global string `json:"global"`
	} `json:"message"`
	Result struct {
		TaskId         string    `json:"taskId"`
		Status         string    `json:"status"`
		FailedCode     int       `json:"failedCode"`
		FailedMessage  string    `json:"failedMessage"`
		VideoUrl       string    `json:"videoUrl"`
		Duration       int       `json:"duration"`
		CreateTime     string    `json:"createTime"`
		UpdateTime     string    `json:"updateTime"`
		StartTrainTime time.Time `json:"startTrainTime"`
	} `json:"result"`
	RequestId string `json:"requestId"`
	Success   bool   `json:"success"`
}

type UploadFileResponse struct {
	Code    int `json:"code"`
	Message struct {
		Global string `json:"global"`
	} `json:"message"`
	Result struct {
		FileID   string `json:"fileId"`
		FileName string `json:"fileName"`
	} `json:"result"`
	RequestId string `json:"requestId"`
	Success   bool   `json:"success"`
}

type TrainSubmitRequest struct {
	Name            string `json:"name"`
	CustomizeType   string `json:"customizeType"`
	Gender          string `json:"gender"`
	KeepBackground  bool   `json:"keepBackground"`
	TemplateVideoId string `json:"templateVideoId"`
	MaskVideoId     string `json:"maskVideoId,omitempty"`
	CallbackUrl     string `json:"callbackUrl,omitempty"`
}

type TrainSubmitResponse struct {
	Code    int `json:"code"`
	Message struct {
		Global string `json:"global"`
	} `json:"message"`
	Result struct {
		FigureId string `json:"figureId"`
	} `json:"result"`
	RequestId string `json:"requestId"`
	Success   bool   `json:"success"`
}

type TrainTaskCheckResponse struct {
	Code    int `json:"code"`
	Message struct {
		Global string `json:"global"`
	} `json:"message"`
	Result struct {
		PageNo     int `json:"pageNo"`
		PageSize   int `json:"pageSize"`
		TotalCount int `json:"totalCount"`
		Result     []struct {
			FigureId         string `json:"figureId"`
			Name             string `json:"name"`
			CustomizeType    string `json:"customizeType"`
			SystemFigure     bool   `json:"systemFigure"`
			Gender           string `json:"gender"`
			KeepBackground   bool   `json:"keepBackground"`
			TemplateVideoUrl string `json:"templateVideoUrl"`
			MaskVideoUrl     string `json:"maskVideoUrl"`
			ResolutionWidth  int    `json:"resolutionWidth"`
			ResolutionHeight int    `json:"resolutionHeight"`
			Status           string `json:"status"`
			TemplateImg      string `json:"templateImg"`
			TaskSubmitTime   string `json:"taskSubmitTime"`
			FailedCode       int    `json:"failedCode"`
			FailedMessage    string `json:"failedMessage"`
		} `json:"result"`
	} `json:"result"`
	RequestId string `json:"requestId"`
	Success   bool   `json:"success"`
}

type FigureVideoSubmitSubtitleParams struct {
	Enabled        bool   `json:"enabled,omitempty"`
	SubtitlePolicy string `json:"subtitlePolicy,omitempty"`
}

type VideoParams struct {
	Height      int  `json:"height,omitempty"`
	Width       int  `json:"width,omitempty"`
	Transparent bool `json:"transparent,omitempty"`
}

type FigureVideoSubmitRequest struct {
	FigureId           string `json:"figureId"`
	Text               string `json:"text,omitempty"`
	DriveType          string `json:"driveType"`
	InputAudioUrl      string `json:"inputAudioUrl"`
	BackgroundImageUrl string `json:"backgroundImageUrl,omitempty"`
	TtsParams          struct {
		Person string `json:"person"`
		Speed  string `json:"speed"`
		Pitch  string `json:"pitch"`
		Volume string `json:"volume"`
	} `json:"ttsParams,omitempty"`
	VideoParams    VideoParams                     `json:"videoParams,omitempty"`
	AutoAnimoji    bool                            `json:"autoAnimoji,omitempty"`
	SubtitleParams FigureVideoSubmitSubtitleParams `json:"subtitleParams,omitempty"`
	CallbackUrl    string                          `json:"callbackUrl,omitempty"`
}

type FigureVideoTaskResponse struct {
	Code    int `json:"code"`
	Message struct {
		Global string `json:"global"`
	} `json:"message"`
	Result struct {
		TaskId          string `json:"taskId"`
		Status          string `json:"status"`
		FailedCode      int    `json:"failedCode"`
		FailedMessage   string `json:"failedMessage"`
		VideoUrl        string `json:"videoUrl"`
		Duration        int    `json:"duration"`
		CreateTime      string `json:"createTime"`
		UpdateTime      string `json:"updateTime"`
		SubtitleFileUrl string `json:"subtitleFileUrl"`
	} `json:"result"`
	RequestId string `json:"requestId"`
	Success   bool   `json:"success"`
}
