package dh123

import (
	"acg-ai-go-common/utils/httputil"
	"bytes"
	"context"
	"crypto/hmac"
	"crypto/sha256"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"io"
	"mime/multipart"
	"net/http"
	"net/url"
	"os"
	"path/filepath"
	"time"
)

type AuthGenerator struct {
	AppID  string
	AppKey string
}

func GenerateAuth(appID, appKey string) string {
	t := time.Now().UTC().Add(24 * time.Hour).Local().Truncate(time.Second)
	timeStr := t.Format(time.RFC3339)

	message := appID + timeStr
	h := hmac.New(sha256.New, []byte(appKey))
	h.Write([]byte(message))
	signature := hex.EncodeToString(h.Sum(nil))

	return appID + "/" + signature + "/" + timeStr
}

func SubmitTask(logCtx context.Context, url, appId, appKey string, inputVideoUrl, inputAudioUrl string,
	callbackUrl string) (*GenerateResponse,
	error) {
	req := &GenerateRequest{
		TemplateVideoId: inputVideoUrl,
		DriveType:       "VOICE",
		InputAudioUrl:   inputAudioUrl,
		CallbackUrl:     callbackUrl,
	}

	body, err := json.Marshal(req)
	if err != nil {
		return nil, fmt.Errorf("marshal failed: %v", err)
	}

	headers := map[string]string{
		"Content-Type":  "application/json",
		"Authorization": GenerateAuth(appId, appKey),
	}

	tarUrl := fmt.Sprintf("%s/api/digitalhuman/open/v1/video/submit/fast", url)
	httpclient := httputil.NewRetryHTTPClient(15*time.Minute, 3)
	respBody, err := httpclient.DoRequest(logCtx, http.MethodPost, tarUrl, headers, bytes.NewReader(body))
	if err != nil {
		return nil, fmt.Errorf("http request failed: %v", err)
	}

	rsp := &GenerateResponse{}
	err = json.Unmarshal(respBody, &rsp)
	if err != nil {
		return nil, fmt.Errorf("unmarshal failed: %v", err)
	}

	if !rsp.Success {
		return nil, fmt.Errorf("server error: %s", string(respBody))
	}

	if len(rsp.Result.TaskId) < 1 {
		return nil, fmt.Errorf("server error: %s", string(respBody))
	}
	return rsp, nil
}

func CheckTask(logCtx context.Context, url, appId, appKey, taskId string) (*CheckStatusResponse,
	error) {
	body := []byte{}
	headers := map[string]string{
		"Content-Type":  "application/json",
		"Authorization": GenerateAuth(appId, appKey),
	}

	tarUrl := fmt.Sprintf("%s/api/digitalhuman/open/v1/video/task?taskId=%s", url, taskId)
	httpclient := httputil.NewRetryHTTPClient(15*time.Minute, 3)
	respBody, err := httpclient.DoRequest(logCtx, http.MethodGet, tarUrl, headers, bytes.NewReader(body))
	if err != nil {
		return nil, fmt.Errorf("http request failed: %v", err)
	}

	rsp := &CheckStatusResponse{}
	err = json.Unmarshal(respBody, &rsp)
	if err != nil {
		return nil, fmt.Errorf("unmarshal failed: %v", err)
	}

	if !rsp.Success {
		return nil, fmt.Errorf("server error: %s", string(respBody))
	}

	if len(rsp.Result.TaskId) < 1 {
		return nil, fmt.Errorf("server error: %s", string(respBody))
	}
	return rsp, nil
}

// UploadFile 上传文件到接口
func UploadFile(logCtx context.Context, host, appId, appKey string, filePath,
	providerType string) (*UploadFileResponse, error) {
	fileName := filepath.Base(filePath)
	escapedFileName := url.QueryEscape(fileName)
	tarUrl := fmt.Sprintf("%s/api/digitalhuman/open/v1/file/upload?providerType=%s&sourceFileName=%s", host,
		providerType, escapedFileName)

	var buf bytes.Buffer
	writer := multipart.NewWriter(&buf)

	file, err := os.Open(filePath)
	if err != nil {
		return nil, fmt.Errorf("open file: %v", err)
	}
	defer file.Close()
	part, err := writer.CreateFormFile("file", fileName)
	if err != nil {
		return nil, fmt.Errorf("create form file: %v", err)
	}
	if _, err := io.Copy(part, file); err != nil {
		return nil, fmt.Errorf("copy file: %v", err)
	}
	if err := writer.Close(); err != nil {
		return nil, fmt.Errorf("close multipart writer: %v", err)
	}

	headers := map[string]string{
		"Authorization": GenerateAuth(appId, appKey),
		"Content-Type":  writer.FormDataContentType(),
	}

	httpclient := httputil.NewRetryHTTPClient(15*time.Minute, 3)
	respBody, err := httpclient.DoRequest(logCtx, http.MethodPost, tarUrl, headers, &buf)
	if err != nil {
		return nil, fmt.Errorf("DoRequest failed: %v", err)
	}

	var resp UploadFileResponse
	if err := json.Unmarshal(respBody, &resp); err != nil {
		return nil, fmt.Errorf("unmarshal response: %v, body: %s", err, string(respBody))
	}

	if !resp.Success || len(resp.Result.FileID) < 1 {
		return nil, fmt.Errorf("request error: %s", string(respBody))
	}

	return &resp, nil
}

func TrainTaskSubmit(logCtx context.Context, host, appId, appKey string, name,
	tempVideoId string) (*TrainSubmitResponse, error) {
	req := TrainSubmitRequest{
		Name:            name,
		CustomizeType:   "LITE_2D_GENERAL",
		Gender:          "MALE",
		KeepBackground:  true,
		TemplateVideoId: tempVideoId,
	}

	body, err := json.Marshal(req)
	if err != nil {
		return nil, fmt.Errorf("marshal failed: %v", err)
	}

	headers := map[string]string{
		"Content-Type":  "application/json",
		"Authorization": GenerateAuth(appId, appKey),
	}

	tarUrl := fmt.Sprintf("%s/api/digitalhuman/open/v1/figure/lite2d/train", host)
	httpclient := httputil.NewRetryHTTPClient(15*time.Minute, 3)
	respBody, err := httpclient.DoRequest(logCtx, http.MethodPost, tarUrl, headers, bytes.NewReader(body))
	if err != nil {
		return nil, fmt.Errorf("http request failed: %v", err)
	}

	var resp TrainSubmitResponse
	if err := json.Unmarshal(respBody, &resp); err != nil {
		return nil, fmt.Errorf("unmarshal response: %v, body: %s", err, string(respBody))
	}

	if !resp.Success || len(resp.Result.FigureId) < 1 {
		return nil, fmt.Errorf("request error: %s", string(respBody))
	}

	return &resp, nil
}

func TrainTaskCheck(logCtx context.Context, host, appId, appKey string, figureId string) (*TrainTaskCheckResponse, error) {
	body := []byte{}
	headers := map[string]string{
		"Content-Type":  "application/json",
		"Authorization": GenerateAuth(appId, appKey),
	}

	tarUrl := fmt.Sprintf("%s/api/digitalhuman/open/v1/figure/lite2d/query?figureId=%s", host, figureId)
	httpclient := httputil.NewRetryHTTPClient(15*time.Minute, 3)
	respBody, err := httpclient.DoRequest(logCtx, http.MethodGet, tarUrl, headers, bytes.NewReader(body))
	if err != nil {
		return nil, fmt.Errorf("http request failed: %v", err)
	}

	var resp TrainTaskCheckResponse
	if err := json.Unmarshal(respBody, &resp); err != nil {
		return nil, fmt.Errorf("unmarshal response: %v, body: %s", err, string(respBody))
	}

	if !resp.Success || resp.Result.TotalCount < 1 || len(resp.Result.Result) < 1 {
		return nil, fmt.Errorf("request error: %s", string(respBody))
	}

	return &resp, nil
}

func FigureVideoSubmit(logCtx context.Context, host, appId, appKey string,
	figureId, audioUrl string) (*FigureVideoTaskResponse,
	error) {
	req := FigureVideoSubmitRequest{
		FigureId:      figureId,
		DriveType:     "VOICE",
		InputAudioUrl: audioUrl,
		SubtitleParams: FigureVideoSubmitSubtitleParams{
			Enabled: true,
		},
		VideoParams: VideoParams{
			Width:  1080,
			Height: 1920,
		},
	}

	body, err := json.Marshal(req)
	if err != nil {
		return nil, fmt.Errorf("marshal failed: %v", err)
	}

	headers := map[string]string{
		"Content-Type":  "application/json",
		"Authorization": GenerateAuth(appId, appKey),
	}

	tarUrl := fmt.Sprintf("%s/api/digitalhuman/open/v1/video/submit", host)
	httpclient := httputil.NewRetryHTTPClient(15*time.Minute, 3)
	respBody, err := httpclient.DoRequest(logCtx, http.MethodPost, tarUrl, headers, bytes.NewReader(body))
	if err != nil {
		return nil, fmt.Errorf("http request failed: %v", err)
	}

	var resp FigureVideoTaskResponse
	if err := json.Unmarshal(respBody, &resp); err != nil {
		return nil, fmt.Errorf("unmarshal response: %v, body: %s", err, string(respBody))
	}

	if !resp.Success || len(resp.Result.TaskId) < 1 {
		return nil, fmt.Errorf("request error: %s", string(respBody))
	}

	return &resp, nil
}

func FigureVideoTaskCheck(logCtx context.Context, host, appId, appKey string,
	taskId string) (*FigureVideoTaskResponse, error) {
	body := []byte{}
	headers := map[string]string{
		"Content-Type":  "application/json",
		"Authorization": GenerateAuth(appId, appKey),
	}

	tarUrl := fmt.Sprintf("%s/api/digitalhuman/open/v1/video/task?taskId=%s", host, taskId)
	httpclient := httputil.NewRetryHTTPClient(15*time.Minute, 3)
	respBody, err := httpclient.DoRequest(logCtx, http.MethodGet, tarUrl, headers, bytes.NewReader(body))
	if err != nil {
		return nil, fmt.Errorf("http request failed: %v", err)
	}

	var resp FigureVideoTaskResponse
	if err := json.Unmarshal(respBody, &resp); err != nil {
		return nil, fmt.Errorf("unmarshal response: %v, body: %s", err, string(respBody))
	}

	if !resp.Success || len(resp.Result.TaskId) < 1 {
		return nil, fmt.Errorf("request error: %s", string(respBody))
	}

	return &resp, nil
}
