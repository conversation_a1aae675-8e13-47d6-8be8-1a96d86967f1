package heygen

import (
	"acg-ai-go-common/utils/httputil"
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"time"
)

// SubmitTask 提交处理任务
func SubmitTask(logCtx context.Context, url string, apikey string, payload *TranslateVideoRequest) (taskId string,
	err error) {
	body, err := json.Marshal(payload)
	if err != nil {
		return "", fmt.Errorf("marshal failed: %v", err)
	}

	headers := map[string]string{
		"x-api-key":    apikey,
		"Content-Type": "application/json",
	}

	httpclient := httputil.NewRetryHTTPClient(15*time.Minute, 3)
	respBody, err := httpclient.DoRequest(logCtx, http.MethodPost, url, headers, bytes.NewReader(body))
	if err != nil {
		return "", fmt.Errorf("http request failed: %v", err)
	}

	rsp := &TranslateVideoResponse{}
	err = json.Unmarshal(respBody, &rsp)
	if err != nil {
		return "", fmt.Errorf("unmarshal failed: %v", err)
	}

	if rsp.Error != nil {
		return "", fmt.Errorf("server error: %s", string(respBody))
	}

	if rsp.Data == nil || len(rsp.Data.VideoTranslateId) < 1 {
		return "", fmt.Errorf("server error: %s", string(respBody))
	}

	return rsp.Data.VideoTranslateId, nil
}

// CheckTask 查询处理任务
func CheckTask(logCtx context.Context, url string, apikey string, taskId string) (rsp *TranslateVideoResponse,
	err error) {
	var body []byte
	headers := map[string]string{
		"x-api-key":    apikey,
		"Content-Type": "application/json",
	}

	tarUrl := fmt.Sprintf("%s/%s", url, taskId)

	httpclient := httputil.NewRetryHTTPClient(15*time.Minute, 3)
	respBody, err := httpclient.DoRequest(logCtx, http.MethodGet, tarUrl, headers, bytes.NewReader(body))
	if err != nil {
		return nil, fmt.Errorf("http request failed: %v", err)
	}

	rsp = &TranslateVideoResponse{}
	err = json.Unmarshal(respBody, &rsp)
	if err != nil {
		return nil, fmt.Errorf("unmarshal failed: %v", err)
	}

	if rsp.Error != nil {
		return nil, fmt.Errorf("server error: %s", string(respBody))
	}

	if rsp.Data == nil || len(rsp.Data.VideoTranslateId) < 1 {
		return nil, fmt.Errorf("server error: %s", string(respBody))
	}

	return rsp, nil
}

// GetCaption 获取字幕
func GetCaption(logCtx context.Context, url string, apikey string, taskId string,
	captionType string) (rsp *TranslateCaptionResponse,
	err error) {
	var body []byte
	headers := map[string]string{
		"x-api-key":    apikey,
		"Content-Type": "application/json",
	}

	tarUrl := fmt.Sprintf("%s/caption?video_translate_id=%s&caption_type=%s", url, taskId, captionType)

	httpclient := httputil.NewRetryHTTPClient(15*time.Minute, 3)
	respBody, err := httpclient.DoRequest(logCtx, http.MethodGet, tarUrl, headers, bytes.NewReader(body))
	if err != nil {
		return nil, fmt.Errorf("http request failed: %v", err)
	}

	rsp = &TranslateCaptionResponse{}
	err = json.Unmarshal(respBody, &rsp)
	if err != nil {
		return nil, fmt.Errorf("unmarshal failed: %v", err)
	}

	if rsp.Error != nil {
		return nil, fmt.Errorf("server error: %s", string(respBody))
	}

	if len(rsp.Data.CaptionUrl) < 1 {
		return nil, fmt.Errorf("server error: %s", string(respBody))
	}

	return rsp, nil
}
