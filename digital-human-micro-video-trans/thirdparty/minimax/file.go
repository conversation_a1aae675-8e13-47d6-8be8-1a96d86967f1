package minimax

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"mime/multipart"
	"net/http"
	"os"
	"path/filepath"
)

// UploadFile 将本地文件上传
func UploadFile(host, groupID, apiKey, filePath string) (*UploadResponse, string, error) {
	// 打开文件
	file, err := os.Open(filePath)
	if err != nil {
		return nil, "", fmt.Errorf("open file: %w", err)
	}
	defer file.Close()

	// 创建 multipart/form-data 内容
	var requestBody bytes.Buffer
	writer := multipart.NewWriter(&requestBody)

	// 添加 purpose 字段
	if err := writer.WriteField("purpose", "voice_clone"); err != nil {
		return nil, "", fmt.Errorf("add purpose: %w", err)
	}

	// 添加 file 字段
	fileName := filepath.Base(filePath)
	part, err := writer.CreateFormFile("file", fileName)
	if err != nil {
		return nil, "", fmt.Errorf("create file: %w", err)
	}

	// 写入文件内容
	if _, err := io.Copy(part, file); err != nil {
		return nil, "", fmt.Errorf("write file: %w", err)
	}

	// 关闭 multipart writer
	if err := writer.Close(); err != nil {
		return nil, "", fmt.Errorf("close writer: %w", err)
	}

	// 构造 HTTP 请求
	tarUrl := fmt.Sprintf("%s/files/upload?GroupId=%s", host, groupID)
	req, err := http.NewRequest("POST", tarUrl, &requestBody)
	if err != nil {
		return nil, "", fmt.Errorf("create post: %w", err)
	}

	// 设置请求头
	req.Header.Set("Content-Type", writer.FormDataContentType())
	req.Header.Set("Authorization", "Bearer "+apiKey)

	// 执行请求
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return nil, "", fmt.Errorf("do request: %w", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, "", fmt.Errorf("read response: %w", err)
	}

	var result UploadResponse
	if err := json.Unmarshal(body, &result); err != nil {
		return nil, string(body), fmt.Errorf("unmarshal JSON: %w", err)
	}

	return &result, string(body), nil
}

// RetrieveFile 检索文件
func RetrieveFile(host, groupID, apiKey string, fileId int64) (*RetrieveResponse, string, error) {
	url := fmt.Sprintf("%s/files/retrieve?GroupId=%s&file_id=%d", host, groupID, fileId)

	// 序列化请求体
	var reqBody []byte
	req, err := http.NewRequest("GET", url, bytes.NewReader(reqBody))
	if err != nil {
		return nil, "", fmt.Errorf("new request: %w", err)
	}

	req.Header.Set("Authorization", "Bearer "+apiKey)
	req.Header.Set("Content-Type", "application/json")

	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return nil, "", fmt.Errorf("do request: %w", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, "", fmt.Errorf("read response: %w", err)
	}

	var result RetrieveResponse
	if err := json.Unmarshal(body, &result); err != nil {
		return nil, string(body), fmt.Errorf("unmarshal: %w", err)
	}

	return &result, string(body), nil
}
