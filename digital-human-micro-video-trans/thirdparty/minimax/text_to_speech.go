package minimax

import (
	"bytes"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"os"
)

func NewSyncSpeechRequest(model, voiceId, text string, speed float64) *SynthesisRequest {
	return &SynthesisRequest{
		Model:          model,
		Text:           text,
		Stream:         false,
		LanguageBoost:  "auto",
		OutputFormat:   "hex",
		SubtitleEnable: true,
		SubtitleType:   "word",
		VoiceSetting: &VoiceSetting{
			VoiceID: voiceId,
			Speed:   speed,
			Vol:     1,
			Pitch:   0,
		},
		AudioSetting: &AudioSetting{
			SampleRate: 16000,
			Channel:    1,
			Format:     "wav",
		},
	}
}

// SynthesizeSpeech 文本转语音接口
func SynthesizeSpeech(host, groupID, apiKey string, textParams *SynthesisRequest) (*SynthesisResponse, string, error) {
	url := fmt.Sprintf("%s/t2a_v2?Groupid=%s", host, groupID)

	jsonData, err := json.Marshal(textParams)
	if err != nil {
		return nil, "", fmt.Errorf("marshal: %v", err)
	}

	req, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonData))
	if err != nil {
		return nil, "", fmt.Errorf("new request: %v", err)
	}

	req.Header.Set("Authorization", "Bearer "+apiKey)
	req.Header.Set("Content-Type", "application/json")

	resp, err := http.DefaultClient.Do(req)
	if err != nil {
		return nil, "", fmt.Errorf("make request: %v", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, "", fmt.Errorf("read reqponse: %v", err)
	}

	var result SynthesisResponse
	if err := json.Unmarshal(body, &result); err != nil {
		return nil, string(body), fmt.Errorf("unmarshal: %w", err)
	}

	return &result, string(body), nil
}

func SaveHexAudioToFile(hexStr, filename string) error {
	audioBytes, err := hex.DecodeString(hexStr)
	if err != nil {
		return fmt.Errorf("hex decode: %v", err)
	}
	return os.WriteFile(filename, audioBytes, 0644)
}

// AsyncSpeechSubmit 文本转语音异步接口
func AsyncSpeechSubmit(host, groupID, apiKey string, textParams *AsyncRequest) (*AsyncResponse, string, error) {
	url := fmt.Sprintf("%s/t2a_async_v2?Groupid=%s", host, groupID)

	jsonData, err := json.Marshal(textParams)
	if err != nil {
		return nil, "", fmt.Errorf("marshal: %v", err)
	}

	req, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonData))
	if err != nil {
		return nil, "", fmt.Errorf("new request: %v", err)
	}

	req.Header.Set("Authorization", "Bearer "+apiKey)
	req.Header.Set("Content-Type", "application/json")

	resp, err := http.DefaultClient.Do(req)
	if err != nil {
		return nil, "", fmt.Errorf("make request: %v", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, "", fmt.Errorf("read reqponse: %v", err)
	}

	var result AsyncResponse
	if err := json.Unmarshal(body, &result); err != nil {
		return nil, string(body), fmt.Errorf("unmarshal: %w", err)
	}

	return &result, string(body), nil
}

// AsyncSpeechQuery 文本转语音异步接口
func AsyncSpeechQuery(host, groupID, apiKey string, taskId int64) (*AsyncTaskQueryResponse, string, error) {
	url := fmt.Sprintf("%s/query/t2a_async_query_v2?Groupid=%s&task_id=%d", host, groupID, taskId)

	var jsonData []byte
	req, err := http.NewRequest("GET", url, bytes.NewBuffer(jsonData))
	if err != nil {
		return nil, "", fmt.Errorf("new request: %v", err)
	}

	req.Header.Set("Authorization", "Bearer "+apiKey)
	req.Header.Set("Content-Type", "application/json")

	resp, err := http.DefaultClient.Do(req)
	if err != nil {
		return nil, "", fmt.Errorf("make request: %v", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, "", fmt.Errorf("read reqponse: %v", err)
	}

	var result AsyncTaskQueryResponse
	if err := json.Unmarshal(body, &result); err != nil {
		return nil, string(body), fmt.Errorf("unmarshal: %w", err)
	}

	return &result, string(body), nil
}
