package minimax

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
)

func DoVoiceClone(
	host, groupID, apiKey string,
	reqData VoiceCloneRequest,
) (*VoiceCloneResponse, string, error) {

	url := fmt.Sprintf("%s/voice_clone?GroupId=%s", host, groupID)

	// 序列化请求体
	reqBody, err := json.Marshal(reqData)
	if err != nil {
		return nil, "", fmt.<PERSON><PERSON>("marshal: %w", err)
	}

	// 构造请求
	req, err := http.NewRequest("POST", url, bytes.NewReader(reqBody))
	if err != nil {
		return nil, "", fmt.Errorf("post: %w", err)
	}

	req.Header.Set("Authorization", "Bearer "+apiKey)
	req.Header.Set("Content-Type", "application/json")

	// 执行请求
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return nil, "", fmt.Errorf("make request: %w", err)
	}
	defer resp.Body.Close()

	// 解析响应
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, "", fmt.Errorf("read response: %w", err)
	}

	var result VoiceCloneResponse
	if err := json.Unmarshal(body, &result); err != nil {
		return nil, string(body), fmt.Errorf("unmarshal: %w", err)
	}

	return &result, string(body), nil
}
