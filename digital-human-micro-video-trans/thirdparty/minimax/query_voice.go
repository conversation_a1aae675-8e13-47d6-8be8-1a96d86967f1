package minimax

import (
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strings"
)

func GetVoiceList(host, apiKey string, voiceType string) (*GetVoiceResponse, string, error) {
	url := fmt.Sprintf("%s/get_voice", host)

	formBody := "voice_type=" + voiceType

	req, err := http.NewRequest("POST", url, strings.NewReader(formBody))
	if err != nil {
		return nil, "", fmt.<PERSON>rrorf("new request: %w", err)
	}

	req.Header.Set("Authorization", "Bearer "+apiKey)
	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")

	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return nil, "", fmt.Errorf("do request: %w", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, "", fmt.Errorf("read response: %w", err)
	}

	var result GetVoiceResponse
	if err := json.Unmarshal(body, &result); err != nil {
		return nil, string(body), fmt.Errorf("unmarshal: %w", err)
	}

	return &result, string(body), nil
}
