package minimax

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
)

func DeleteVoice(host, api<PERSON>ey string, voiceType, voiceId string) (*DeleteVoiceResponse, string, error) {
	url := fmt.Sprintf("%s/delete_voice", host)

	type Request struct {
		VoiceType string `json:"voice_type,omitempty"`
		VoiceId   string `json:"voice_id,omitempty"`
	}

	reqData := Request{
		VoiceType: voiceType,
		VoiceId:   voiceId,
	}

	// 序列化请求体
	reqBody, err := json.Marshal(reqData)
	if err != nil {
		return nil, "", fmt.Errorf("marshal: %w", err)
	}

	req, err := http.NewRequest("POST", url, bytes.NewReader(reqBody))
	if err != nil {
		return nil, "", fmt.Errorf("new request: %w", err)
	}

	req.Header.Set("Authorization", "Bearer "+api<PERSON>ey)
	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")

	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return nil, "", fmt.Errorf("do request: %w", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, "", fmt.Errorf("read response: %w", err)
	}

	var result DeleteVoiceResponse
	if err := json.Unmarshal(body, &result); err != nil {
		return nil, string(body), fmt.Errorf("unmarshal: %w", err)
	}

	return &result, string(body), nil
}
