package minimax

const (
	Host string = "https://api.minimaxi.com/v1"

	ModelSpeechHd02    string = "speech-02-hd"
	ModelSpeechTurbo02 string = "speech-02-turbo"
	ModelSpeechHd01    string = "speech-01-hd"
	ModelSpeechTurbo01 string = "speech-01-turbo"
)

type BaseResp struct {
	StatusCode int    `json:"status_code"`
	StatusMsg  string `json:"status_msg"`
}

type File struct {
	FileId    int64  `json:"file_id"`
	Bytes     int    `json:"bytes"`
	CreatedAt int    `json:"created_at"`
	Filename  string `json:"filename"`
	Purpose   string `json:"purpose"`
}

type UploadResponse struct {
	File     *File     `json:"file"`
	BaseResp *BaseResp `json:"base_resp"`
}

type RetrieveResponse struct {
	FileId      int64     `json:"file_id,omitempty"`
	Bytes       int64     `json:"bytes,omitempty"`
	CreatedAt   int64     `json:"created_at,omitempty"`
	Filename    string    `json:"filename,omitempty"`
	Purpose     string    `json:"purpose,omitempty"`
	DownloadUrl string    `json:"download_url,omitempty"`
	BaseResp    *BaseResp `json:"base_resp"`
}

type ClonePrompt struct {
	PromptAudio int64  `json:"prompt_audio"`
	PromptText  string `json:"prompt_text"`
}

type VoiceCloneRequest struct {
	FileID                  int64        `json:"file_id"`
	VoiceID                 string       `json:"voice_id"`
	ClonePrompt             *ClonePrompt `json:"clone_prompt,omitempty"`
	TextValidation          string       `json:"text_validation,omitempty"`
	Text                    string       `json:"text,omitempty"`
	Model                   string       `json:"model,omitempty"`
	Accuracy                float64      `json:"accuracy,omitempty"`
	NeedNoiseReduction      bool         `json:"need_noise_reduction,omitempty"`
	NeedVolumeNormalization bool         `json:"need_volume_normalization,omitempty"`
}

type VoiceCloneResponse struct {
	InputSensitive     bool      `json:"input_sensitive"`
	InputSensitiveType int       `json:"input_sensitive_type"`
	DemoAudio          string    `json:"demo_audio"`
	BaseResp           *BaseResp `json:"base_resp"`
}

type VoiceItem struct {
	VoiceID     string   `json:"voice_id"`
	VoiceName   string   `json:"voice_name,omitempty"` // 仅 system_voice 返回
	Description []string `json:"description,omitempty"`
	CreatedTime string   `json:"created_time,omitempty"` // cloning/generation 用
}

type MusicVoiceItem struct {
	VoiceID        string   `json:"voice_id"`
	InstrumentalID string   `json:"instrumental_id"`
	Description    []string `json:"description,omitempty"`
	CreatedTime    string   `json:"created_time,omitempty"`
}

type GetVoiceResponse struct {
	SystemVoice     []VoiceItem      `json:"system_voice,omitempty"`
	VoiceCloning    []VoiceItem      `json:"voice_cloning,omitempty"`
	VoiceGeneration []VoiceItem      `json:"voice_generation,omitempty"`
	MusicGeneration []MusicVoiceItem `json:"music_generation,omitempty"`
}

type VoiceSetting struct {
	Speed                float64 `json:"speed,omitempty"`
	Vol                  float64 `json:"vol,omitempty"`
	Pitch                int     `json:"pitch,omitempty"`
	VoiceID              string  `json:"voice_id,omitempty"`
	Emotion              string  `json:"emotion,omitempty"`
	LatexRead            bool    `json:"latex_read"`
	EnglishNormalization bool    `json:"english_normalization"`
}

type AudioSetting struct {
	SampleRate int    `json:"sample_rate,omitempty"`
	Bitrate    int    `json:"bitrate,omitempty"`
	Format     string `json:"format,omitempty"`
	Channel    int    `json:"channel,omitempty"`
}

type TimberWeights struct {
	VoiceID string  `json:"voice_id,omitempty"`
	Weight  float64 `json:"weight,omitempty"`
}

type StreamOptions struct {
	ExcludeAggregatedAudio bool `json:"exclude_aggregated_audio,omitempty"`
}

type PronunciationDict struct {
	Tone []string `json:"tone,omitempty"`
}

type SynthesisRequest struct {
	Model string `json:"model"`
	Text  string `json:"text"`

	TimberWeights     *TimberWeights     `json:"timber_weights,omitempty"`
	VoiceSetting      *VoiceSetting      `json:"voice_setting,omitempty"`
	PronunciationDict *PronunciationDict `json:"pronunciation_dict,omitempty"`
	AudioSetting      *AudioSetting      `json:"audio_setting,omitempty"`
	Stream            bool               `json:"stream,omitempty"`
	StreamOptions     *StreamOptions     `json:"stream_options,omitempty"`
	LanguageBoost     string             `json:"language_boost,omitempty"`
	SubtitleEnable    bool               `json:"subtitle_enable,omitempty"`
	SubtitleType      string             `json:"subtitle_type,omitempty"`
	OutputFormat      string             `json:"output_format,omitempty"`
}

type SynthesisData struct {
	Audio        string `json:"audio,omitempty"`
	SubtitleFile string `json:"subtitle_file,omitempty"`
	Status       int    `json:"status,omitempty"`
}

type ExtraInfo struct {
	AudioLength             int64   `json:"audio_length,omitempty"`
	AudioSampleRate         int64   `json:"audio_sample_rate,omitempty"`
	AudioSize               int64   `json:"audio_size,omitempty"`
	Bitrate                 int64   `json:"bitrate,omitempty"`
	AudioFormat             string  `json:"audio_format,omitempty"`
	AudioChannel            int64   `json:"audio_channel,omitempty"`
	InvisibleCharacterRatio float64 `json:"invisible_character_ratio,omitempty"`
	UsageCharacters         int64   `json:"usage_characters,omitempty"`
}

type SynthesisResponse struct {
	BaseResp  *BaseResp      `json:"base_resp,omitempty"`
	Data      *SynthesisData `json:"data,omitempty"`
	TraceId   string         `json:"trace_id,omitempty"`
	ExtraInfo *ExtraInfo     `json:"extra_info,omitempty"`
}

type AsyncRequest struct {
	Model      string `json:"model,omitempty"`
	Text       string `json:"text,omitempty"`
	TextFileId int64  `json:"text_file_id,omitempty"`

	VoiceSetting      *VoiceSetting      `json:"voice_setting,omitempty"`
	AudioSetting      *AudioSetting      `json:"audio_setting,omitempty"`
	PronunciationDict *PronunciationDict `json:"pronunciation_dict,omitempty"`
	LanguageBoost     string             `json:"language_boost,omitempty"`
}

type AsyncResponse struct {
	TaskId    int64     `json:"task_id,omitempty"`
	FileId    int64     `json:"file_id,omitempty"`
	TaskToken string    `json:"task_token,omitempty"`
	BaseResp  *BaseResp `json:"base_resp,omitempty"`
}

type AsyncTaskQueryRequest struct {
	TaskId int64 `json:"task_id,omitempty"`
}

type AsyncTaskQueryResponse struct {
	TaskId   int64     `json:"task_id,omitempty"`
	FileId   int64     `json:"file_id,omitempty"`
	BaseResp *BaseResp `json:"base_resp,omitempty"`
}

type DeleteVoiceResponse struct {
	VoiceId     string    `json:"voice_id"`
	CreatedTime string    `json:"created_time"`
	BaseResp    *BaseResp `json:"base_resp"`
}

type Word struct {
	Word      string  `json:"word"`
	TimeBegin float64 `json:"time_begin"`
	TimeEnd   float64 `json:"time_end"`
	WordBegin int     `json:"word_begin"`
	WordEnd   int     `json:"word_end"`
}

type Subtitle struct {
	Text             string  `json:"text"`
	TimeBegin        float64 `json:"time_begin"`
	TimeEnd          float64 `json:"time_end"`
	TextBegin        int     `json:"text_begin"`
	TextEnd          int     `json:"text_end"`
	TimestampedWords []*Word `json:"timestamped_words"`
}
