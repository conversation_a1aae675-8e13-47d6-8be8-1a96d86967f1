package elevenlabs

import (
	"context"
	"digital-human-micro-video-trans/thirdparty/elevenlabs/speech2text"
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"testing"
)

func TestSpeech2Text(t *testing.T) {
	logCtx := context.Background()

	apiKey := "***************************************************"

	//format := &speech2text.RequestAdditionalFormat{
	//	Format:                      "srt",
	//	IncludeSpeakers:             true,
	//	IncludeTimestamps:           true,
	//	SegmentOnSilenceLongerThanS: 0.3,
	//	MaxSegmentDurationS:         10,
	//	MaxSegmentChars:             40}
	req := &speech2text.Request{
		ModelId: "scribe_v1",
		File: "/Users/<USER>/Downloads/video-translate/cache/task/vte-1mVU4KxFQDqS9GSm/input" +
			"/in_task_item_vocal.wav",
		Diarize:     true,
		NumSpeakers: 32,
		//AdditionalFormats: []*speech2text.RequestAdditionalFormat{format},
		//Temperature:    1.0,
		TagAudioEvents: false,
	}
	resp, err := speech2text.DoSpeechToText(logCtx, apiKey, req)
	if err != nil {
		fmt.Printf("do speech to text failed: %v\n", err)
		return
	}

	outPath := "./output/triple.json"
	dir := filepath.Dir(outPath)
	if err := os.MkdirAll(dir, os.ModePerm); err != nil {
		fmt.Printf("create out path failed: %v\n", err)
		return
	}

	jsonBytes, err := json.Marshal(resp)
	if err != nil {
		fmt.Printf("marshal failed: %v\n", err)
		return
	}

	err = os.WriteFile(outPath, jsonBytes, 0644)
	if err != nil {
		fmt.Printf("write to out file failed: %v\n", err)
		return
	}
	fmt.Printf("result at: %s\n", outPath)

	speaker := speech2text.SplitResponseBySpeakerAndPause(resp, 0.3, 15, 2, 1, 0)
	fmt.Printf("speaker: %v\n", speaker)

	outPath = "./output/triple_speaker.json"
	dir = filepath.Dir(outPath)
	if err := os.MkdirAll(dir, os.ModePerm); err != nil {
		fmt.Printf("create out path failed: %v\n", err)
		return
	}

	jsonBytes, err = json.Marshal(speaker)
	if err != nil {
		fmt.Printf("marshal failed: %v\n", err)
		return
	}

	err = os.WriteFile(outPath, jsonBytes, 0644)
	if err != nil {
		fmt.Printf("write to out file failed: %v\n", err)
		return
	}
	fmt.Printf("speaker result at: %s\n", outPath)

	normalSpeakers := speech2text.NormalizeSpeakers(speaker, 2)

	outPath = "./output/triple_speaker_normal.json"
	dir = filepath.Dir(outPath)
	if err := os.MkdirAll(dir, os.ModePerm); err != nil {
		fmt.Printf("create out path failed: %v\n", err)
		return
	}

	jsonBytes, err = json.Marshal(normalSpeakers)
	if err != nil {
		fmt.Printf("marshal failed: %v\n", err)
		return
	}

	err = os.WriteFile(outPath, jsonBytes, 0644)
	if err != nil {
		fmt.Printf("write to out file failed: %v\n", err)
		return
	}
	fmt.Printf("speaker normalize result at: %s\n", outPath)
}
