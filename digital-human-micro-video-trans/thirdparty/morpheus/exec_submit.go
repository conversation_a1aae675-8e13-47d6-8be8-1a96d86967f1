package morpheus

import (
	"acg-ai-go-common/utils/httputil"
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"time"
)

func SubmitTask(logCtx context.Context, host, videoUrl, name, taskId, userId string, width, height int, resourceLabel,
	sceneLabel string, callbackUrl string) (int, error) {
	var body []byte

	req := TrainSubmitRequest{
		UserId:           userId,
		VideoUrl:         videoUrl,
		BackgroundRemove: false,
		BackgroundImgUrl: "",
		Name:             name,
		Gender:           "MALE",
		ResolutionWidth:  width,
		ResolutionHeight: height,
		TaskId:           taskId,
		ResourceLabel:    resourceLabel,
		SceneLabel:       sceneLabel,
		CallbackUrl:      callbackUrl,
	}

	body, err := json.Marshal(req)
	if err != nil {
		return 0, fmt.Errorf("marshal failed: %v", err)
	}

	headers := map[string]string{
		"Content-Type": "application/json",
	}

	tarUrl := fmt.Sprintf("%s/api/internal/digitalhuman/starlight/v1/open/figure/train", host)
	httpclient := httputil.NewRetryHTTPClient(15*time.Minute, 3)
	respBody, err := httpclient.DoRequest(logCtx, http.MethodPost, tarUrl, headers, bytes.NewReader(body))
	if err != nil {
		return 0, fmt.Errorf("http request failed: %v", err)
	}

	rsp := &TrainSubmitResponse{}
	err = json.Unmarshal(respBody, &rsp)
	if err != nil {
		return 0, fmt.Errorf("unmarshal failed: %v", err)
	}

	if !rsp.Success || rsp.Result == nil || rsp.Result.Id < 1 {
		return 0, fmt.Errorf("submit error: %s", string(respBody))
	}

	return rsp.Result.Id, nil
}
