package test

import (
	"context"
	"digital-human-micro-video-trans/thirdparty/videopipeline"
	"fmt"
	"testing"
)

const (
	Host        string = "http://127.0.0.1:8080"
	AppId       string = "i-qfumx6dt704bz"
	AppKey      string = "ay08rg5gamt1a87q1c5h"
	UserId      string = "1145e61f-103e-430a-813b-72bd4fee0d42"
	CallbackUrl string = ""
	FigureName  string = "fig-rftqx6vxqm6xqwra"
	InputAudio  string = "https://storage.googleapis.com/xiling_us_central1_bucket/xjp-test/digital-human-console/3faaeb91-0727-45b6-a62f-a867d28907da.wav"
)

func TestSubmit(t *testing.T) {
	task, err := videopipeline.SubmitTask(context.Background(), Host, AppId, AppKey, UserId, 1080, 1920, 85, 326901,
		FigureName, "",
		CallbackUrl, InputAudio, false)
	if err != nil {
		fmt.Printf("submit error: %v\n", err)
		return
	}
	fmt.Printf("submit task: %+v\n", task)
}

func TestQuery(t *testing.T) {
	task, err := videopipeline.QueryTask(context.Background(), Host, "vp-rgajxmhgy2qkmgkd8")
	if err != nil {
		fmt.Printf("query error: %v\n", err)
		return
	}
	fmt.Printf("query task: %+v\n", task)
}
