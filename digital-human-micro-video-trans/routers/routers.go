package routers

import (
	"digital-human-micro-video-trans/handler/controller"
	"github.com/gin-gonic/gin"
)

// Routers 路由列表
func Routers(e *gin.Engine) {
	apiV1 := e.Group("/api/digitalhuman/video/translate/acg/v1")
	{
		apiV1.POST("/submit", controller.TaskHandler{}.HandleAcgSubmitTask)
		apiV1.POST("/query", controller.TaskHandler{}.HandleAcgQueryTask)

	}

	apiCallback := e.Group("/api/digitalhuman/video/translate/acg/v1/callback")
	{
		apiCallback.POST("/videopipeline", controller.TaskHandler{}.HandleVideoPipelineCallback)
		apiCallback.POST("/figurecenter", controller.TaskHandler{}.HandleFigureCenterCallback)

	}
}
