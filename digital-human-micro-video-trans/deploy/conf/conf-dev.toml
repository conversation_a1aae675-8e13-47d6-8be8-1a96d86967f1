server-port = 8080
server-name = "digital-human-micro-video-trans"
log-file-prefix = "localhost"
log-file-path = "./logs/"
log-show-code-file = true
log-show-code-func = true

# consul配置
[consul-setting]
host = "127.0.0.1"
port = 8500
name = ""

# 健康检查地址
health-url = "/health"
check-timeout = "10s"
check-interval = "10s"
deregister-critical-service-after = "30s"

# mysql配置
[mysql-setting]
host = "127.0.0.1"
port = 3380
database = "meta_human_editor_saas"
username = "saas"
password = "!Digitalhuman@baidu123"
maxOpenConns = 1000
maxIdlenConns = 5

# redis配置
[redis-setting]
addr = "127.0.0.1:6777"
username = ""
password = ""
redisEnv = "dev"

# 日志上传的elasticsearch配置
[log-es-setting]
# host = "http://*************:8200"
host = "http://127.0.0.1:9202"
username = ""
password = ""

[storage-setting]
type = "bos"

[storage-setting.bos]
# apikey
ak = "ALTAKxdVkHMs4sp0Tgkpa3zCJP"
# Secret Key
sk = "e4f46ff5d1bf45c5b81dd867d6e3c148"
endpoint = "bj.bcebos.com"
bucket   = "xiling-dh"
host     = "https://xiling-dh.bj.bcebos.com"
cdn-host = "https://xiling-dh.cdn.bcebos.com"
obfuscate = true
retryTimes = 3
retrySleepMillis = 1000

[storage-setting.gcs]
credential = "./deploy/conf/gcs_credentials.json"
bucket = "xiling_us_central1_bucket"
host = "https://storage.googleapis.com"
cdn-host = "https://test.keevx.com"
path = "xjp-test/micro-video-translate"
timestamp = true
obfuscate = true
retryTimes = 3
retrySleepMillis = 1000

[timeout-settings]
taskTimeoutHour = 24
taskSubTimeoutHour = 1
taskTrainTimeoutHour = 6
taskVideoTimeoutHour = 6

[cache-setting]
rootPath = "/Users/<USER>/Downloads/video-translate/cache/task"
cleanPeriodDay = 2
cleanTimeoutDay = 7
useTrueFileName = true

[schedule-setting]
maxRunningSize = 1
maxScheduleSize = 10
maxTaskRetryCount = 1
httpRetryCount = 3

[limiter-setting]
quotaTimeoutHour = 3
setTimeoutHour = 6
lockTimeoutSec = 5

[music-ai-setting]
apiKey = "6e41d274-9033-40d3-9c77-31a58eeed3ad"
workflow = "cinematic-stems"
user = "video-trans-dev"
concurrencyLimit = 2

[eleven-labs-setting]
apiKey = "***************************************************"
asrUrl = "https://api.elevenlabs.io/v1/speech-to-text"
asrModelId = "scribe_v1"
asrSentenceGap = 0.3
asrSentenceTime = 10
asrMinSentenceTime = 2
asrMinSentenceGap = 1
concurrencyLimit = 2
deleteVoiceHour = 24

[translate-setting]
googleCredential = "./deploy/conf/gcs_credentials.json"
concurrencyLimit = 2

[gemini-setting]
googleCredential = "./deploy/conf/gcs_credentials.json"
projectId = "xiling-453607"
location = "europe-west4"
model = "gemini-2.0-flash-001"
concurrencyLimit = 2

[dh-engine-setting]
host = "https://persona.baidu.com:8850"
appId = "i-qicsmyfe2vfkp"
appKey = "10qydimkanjfaa0w6edu"
concurrencyLimit = 2

[morpheus-setting]
host = "http://figure-center:8080"
resourceLabel = "2D_TRAIN"
sceneLabel = "videoTranslate"
concurrencyLimit = 2
callbackHost = "http://digital-human-micro-video-trans:8080"

[video-pipeline-setting]
host = "http://digital-human-video-pipeline:8080"
appId = "i-qfumx6dt704bz"
appKey = "ay08rg5gamt1a87q1c5h"
transparent = true
concurrencyLimit = 2
callbackHost = "http://digital-human-micro-video-trans:8080"

[subtitle-setting]
maxLines = 4
usePunctuation = true

maxChars = 40
maxLineChars = 20
maxDuration = 3.0

maxWideChars = 20
maxWideLineChars = 10
maxWideDuration = 3.0

maxLandscapeCharsScale = 1.5
maxLandscapeLineCharsScale = 1.5
maxLandscapeDurationScale = 1.5

wideCharLanguage = ["chinese", "japanese"]

[voice-tuning-setting]
threshold = 0.5
maxThreshold = 8.0
maxStep = 10
speedWeight = 0.04
promptFile = "./deploy/conf/voice-prompt.txt"

[voice-setting]
cloneEngine = "minimax"
sampleMinDuration = 12
sampleMaxDuration = 300
sampleMaxSize = 10240
vocalVolume = 2.0
backVolume = 1.5

[minimax-setting]
host = "https://api.minimaxi.com/v1"
apiKey = "eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************************************************************************************************************************************************************.uoB9L7SsAqe2gIFOAAU1zgoDNtSsg4N9AHPW7VDOzw6KBYI2IvnCGIr_3d2avgD9EeFjVP7T9ETS5ezyhVqEHKXWoTJKzqr3uRpPMU8K4_5B_ptkhZEUNTyq7OQfmGFRpN1ntDl4YMz-ksG05xdGczr1UmFaTMjp5O3Rb7c3qGWJ-Xuaeahzmvj28Cj8MIMzxO1HHTQVM6P-1v99l96v2R-lVYT-mcyM-V0a5LekgRZLot5lWWHUrtPSFd2MIjjTj1Wdh7xWPhZEWsrwIWgVorrmAzeYB87rabqZqs6RSEii9YIxamJ3DA-4uxxkmvHaCK-GM0md2YckWC8z1-MaIQ"
groupId = "1782658868262748291"
concurrencyLimit = 2
deleteVoiceHour = 24