apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: digital-human-micro-video-trans
  name: digital-human-micro-video-trans
  namespace: dh-v3
spec:
  progressDeadlineSeconds: 600
  replicas: 0
  revisionHistoryLimit: 10
  selector:
    matchLabels:
      app: digital-human-micro-video-trans
  strategy:
    rollingUpdate:
      maxSurge: 25%
      maxUnavailable: 25%
    type: RollingUpdate
  template:
    metadata:
      labels:
        app: digital-human-micro-video-trans
    spec:
      containers:
      - args:
        - /home/<USER>/sbin/start.sh
        command:
        - sh
        env:
        - name: POD_NAMESPACE
          valueFrom:
            fieldRef:
              apiVersion: v1
              fieldPath: metadata.namespace
        image: ccr-246im3mw-pub.cnc.bj.baidubce.com/digitalhuman/digital-human-micro-video-trans:20250717_1752733090668
        imagePullPolicy: IfNotPresent
        livenessProbe:
          failureThreshold: 3
          initialDelaySeconds: 30
          periodSeconds: 10
          successThreshold: 1
          tcpSocket:
            port: 8080
          timeoutSeconds: 1
        name: digital-human-micro-video-trans
        ports:
        - containerPort: 8080
          name: http-0
          protocol: TCP
        readinessProbe:
          failureThreshold: 3
          initialDelaySeconds: 30
          periodSeconds: 10
          successThreshold: 1
          tcpSocket:
            port: 8080
          timeoutSeconds: 1
        resources:
          limits:
            cpu: "8"
            memory: 24Gi
          requests:
            cpu: "8"
            memory: 16Gi
        startupProbe:
          failureThreshold: 3
          initialDelaySeconds: 30
          periodSeconds: 10
          successThreshold: 1
          tcpSocket:
            port: 8080
          timeoutSeconds: 1
        terminationMessagePath: /dev/termination-log
        terminationMessagePolicy: File
        volumeMounts:
        - mountPath: /home/<USER>/conf/conf.toml
          name: conf
          readOnly: true
          subPath: conf.toml
        - mountPath: /home/<USER>/conf/voice-prompt.txt
          name: conf
          readOnly: true
          subPath: voice-prompt.txt
        - mountPath: /home/<USER>/conf/gcs_credentials.json
          name: gcs-credentials
          readOnly: true
          subPath: gcs-credentials.json
        - mountPath: /home/<USER>/video-translate/cache/
          name: mhe-node-pvc
          subPath: video-translate
        - mountPath: /home/<USER>/logs
          name: log
      - env:
        - name: LOG_ES_INDEX
          value: digital-human-micro-video-trans
        - name: LOG_PATH
          value: /home/<USER>/logs/*log
        image: ccr-246im3mw-pub.cnc.bj.baidubce.com/digitalhuman/fluentd-kafka:v0.17.5
        imagePullPolicy: IfNotPresent
        name: fluentd
        ports:
        - containerPort: 80
          name: http-0
          protocol: TCP
        resources:
          limits:
            cpu: 500m
            memory: 512Mi
        terminationMessagePath: /dev/termination-log
        terminationMessagePolicy: File
        volumeMounts:
        - mountPath: /etc/fluent/config.d
          name: fluentd-public-cm
          readOnly: true
        - mountPath: /home/<USER>/logs
          name: log
      dnsPolicy: ClusterFirst
      imagePullSecrets:
      - name: regcred-eccr
      restartPolicy: Always
      schedulerName: default-scheduler
      serviceAccount: default
      serviceAccountName: default
      terminationGracePeriodSeconds: 30
      volumes:
      - configMap:
          defaultMode: 420
          name: digital-human-micro-video-trans
        name: conf
      - name: log
      - configMap:
          defaultMode: 420
          name: fluentd-public-cm
        name: fluentd-public-cm
      - name: mhe-node-pvc
        persistentVolumeClaim:
          claimName: v3-mhe-cache-pvc
      - name: gcs-credentials
        secret:
          defaultMode: 420
          secretName: gcs-credentials

---
apiVersion: v1
data:
  conf.toml: |-
    server-port = 8080
    server-name = "digital-human-micro-video-trans"
    log-file-prefix = "localhost"
    log-file-path = "./logs/"
    log-show-code-file = true
    log-show-code-func = true

    # consul配置
    [consul-setting]
    host = "127.0.0.1"
    port = 8500
    name = ""

    # 健康检查地址
    health-url = "/health"
    check-timeout = "10s"
    check-interval = "10s"
    deregister-critical-service-after = "30s"

    # mysql配置
    [mysql-setting]
    host = "mysql80-saas-test.saas-xl-middleware.com"
    port = 3306
    database = "meta_human_editor_saas"
    username = "saas"
    password = "!Digitalhuman@baidu123"
    maxOpenConns = 1000
    maxIdlenConns = 5

    # redis配置
    [redis-setting]
    addr = "redis6-saas-test.saas-xl-middleware.com:6379"
    username = ""
    password = ""
    redisEnv = "dev"

    # 日志上传的elasticsearch配置
    [log-es-setting]
    host = "http://elasticsearch:9200"
    username = "superuser"
    password = "Baidu_dh123"

    [storage-setting]
    type = "gcs"

    [storage-setting.bos]
    # apikey
    ak = "ALTAKxdVkHMs4sp0Tgkpa3zCJP"
    # Secret Key
    sk = "e4f46ff5d1bf45c5b81dd867d6e3c148"
    endpoint = "bj.bcebos.com"
    bucket   = "xiling-dh"
    host     = "https://xiling-dh.bj.bcebos.com"
    cdn-host = "https://xiling-dh.cdn.bcebos.com"
    obfuscate = true
    retryTimes = 3
    retrySleepMillis = 1000

    [storage-setting.gcs]
    credential = "/home/<USER>/conf/gcs_credentials.json"
    bucket = "xiling_us_central1_bucket"
    host = "https://storage.googleapis.com"
    cdn-host = "https://test.keevx.com"
    path = "xjp-test/micro-video-translate"
    timestamp = false
    obfuscate = false
    retryTimes = 3
    retrySleepMillis = 1000
    
    [timeout-settings]
    taskTimeoutHour = 24
    taskSubTimeoutHour = 1
    taskTrainTimeoutHour = 6
    taskVideoTimeoutHour = 6
    
    [cache-setting]
    rootPath = "/home/<USER>/video-translate/cache/task"
    cleanPeriodDay = 2
    cleanTimeoutDay = 7
    useTrueFileName = true
    
    [schedule-setting]
    maxRunningSize = 1
    maxScheduleSize = 10
    maxTaskRetryCount = 1
    httpRetryCount = 3
    
    [limiter-setting]
    quotaTimeoutHour = 3
    setTimeoutHour = 6
    lockTimeoutSec = 5
    
    [music-ai-setting]
    apiKey = "6e41d274-9033-40d3-9c77-31a58eeed3ad"
    workflow = "cinematic-stems"
    user = "video-trans-dev"
    concurrencyLimit = 2
    
    [eleven-labs-setting]
    apiKey = "***************************************************"
    asrUrl = "https://api.elevenlabs.io/v1/speech-to-text"
    asrModelId = "scribe_v1"
    asrSentenceGap = 0.3
    asrSentenceTime = 10
    asrMinSentenceTime = 2
    asrMinSentenceGap = 1
    concurrencyLimit = 2
    deleteVoiceHour = 24
    
    [translate-setting]
    googleCredential = "/home/<USER>/conf/gcs_credentials.json"
    concurrencyLimit = 2
    
    [gemini-setting]
    googleCredential = "/home/<USER>/conf/gcs_credentials.json"
    projectId = "xiling-453607"
    location = "europe-west4"
    model = "gemini-2.0-flash-001"
    concurrencyLimit = 2
    
    [dh-engine-setting]
    host = "https://persona.baidu.com:8850"
    appId = "i-qicsmyfe2vfkp"
    appKey = "10qydimkanjfaa0w6edu"
    concurrencyLimit = 2
    
    [morpheus-setting]
    host = "http://figure-center:8080"
    resourceLabel = "2D_TRAIN"
    sceneLabel = "videoTranslate"
    concurrencyLimit = 2
    callbackHost = "http://digital-human-micro-video-trans:8080"
    
    [video-pipeline-setting]
    host = "http://digital-human-video-pipeline:8080"
    appId = "i-qfumx6dt704bz"
    appKey = "ay08rg5gamt1a87q1c5h"
    transparent = true
    concurrencyLimit = 2
    callbackHost = "http://digital-human-micro-video-trans:8080"
    
    [subtitle-setting]
    maxLines = 5
    usePunctuation = true
    
    maxChars = 40
    maxLineChars = 20
    maxDuration = 3.0
    
    maxWideChars = 20
    maxWideLineChars = 10
    maxWideDuration = 3.0
    
    maxLandscapeCharsScale = 1.5
    maxLandscapeLineCharsScale = 1.5
    maxLandscapeDurationScale = 1.5
    
    wideCharLanguage = ["chinese", "japanese"]
    
    [voice-tuning-setting]
    threshold = 0.5
    maxThreshold = 8.0
    maxStep = 15
    speedWeight = 0.04
    promptFile = "./conf/voice-prompt.txt"
  
    [voice-setting]
    cloneEngine = "minimax"
    sampleMinDuration = 12
    sampleMaxDuration = 300
    sampleMaxSize = 10240
    vocalVolume = 2.0
    backVolume = 1.5
    
    [minimax-setting]
    host = "https://gcp-api.subsup.net/v1"
    apiKey = "eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************************************************************************************************************************************************************.uoB9L7SsAqe2gIFOAAU1zgoDNtSsg4N9AHPW7VDOzw6KBYI2IvnCGIr_3d2avgD9EeFjVP7T9ETS5ezyhVqEHKXWoTJKzqr3uRpPMU8K4_5B_ptkhZEUNTyq7OQfmGFRpN1ntDl4YMz-ksG05xdGczr1UmFaTMjp5O3Rb7c3qGWJ-Xuaeahzmvj28Cj8MIMzxO1HHTQVM6P-1v99l96v2R-lVYT-mcyM-V0a5LekgRZLot5lWWHUrtPSFd2MIjjTj1Wdh7xWPhZEWsrwIWgVorrmAzeYB87rabqZqs6RSEii9YIxamJ3DA-4uxxkmvHaCK-GM0md2YckWC8z1-MaIQ"
    groupId = "1782658868262748291"
    concurrencyLimit = 2
    deleteVoiceHour = 24

  voice-prompt.txt: |-
    你的任务是根据以下参数微调文本，使其合成后的语音长度尽可能接近目标时长。
    请严格按照以下规则操作，确保调整稳定、渐进、不震荡：
    
    【输入参数】：
    1. text：原始文本
    2. newText：翻译后的文本
    3. currentDuration：翻译后文本对应的音频时长（单位：秒）
    4. targetDuration：期望的音频时长（单位：秒）
    5. targetLang：翻译目标语言
      
      【判断逻辑】：
    - 若 |currentDuration - targetDuration| ≤ 1 秒 → 不做修改，直接返回 newText；
    - 若 currentDuration < targetDuration - 1 秒 → 执行**扩写操作**；
    - 若 currentDuration > targetDuration + 1 秒 → 执行**精简操作**。
      
      【调整幅度限制】（适用于扩写与精简）：
    - 每次修改最多影响不超过 6 个词；
    - 修改后预期的语音时长变化 不得超过 1 秒；
    - 禁止改变语序、拆句或合并句子；
    - 禁止替换动词时态或大规模改写句子结构；
    - 所有调整应尽可能轻微，确保改动幅度与差值成正比。
      
      【扩写规则】（仅当 currentDuration 明显短于 targetDuration 时启用）：
    - 最多插入不超过 5 个词；
    - 可细化细节、补充背景，但不得改变原意；
    - 禁止删除任何内容；
    - 禁止增加超过 1 个句子；
    - 仅允许扩写长度不超过 2 秒。
      
      【精简规则】（仅当 currentDuration 明显长于 targetDuration 时启用）：
    - 删除冗余、不影响主旨的词语；
    - 禁止扩写、引入新内容；
    - 保持语义完整，语法正确；
    - 仅允许压缩长度不超过 2 秒。
      
      【输出要求】：
    - 仅输出调整后的 newText；
    - 输出语言必须为 targetLang；
    - 禁止添加任何说明、标签、注释或结构化格式（如 JSON）；
    - 严格控制改动方向：扩写时禁止删减，精简时禁止扩写；
    - 禁止反复震荡（如扩写后再精简）；
    - 禁止一次性大幅重写原文。
      
    请根据这些规则输出新的 newText，用于合成更接近 targetDuration 的语音，且保持稳定渐进。

kind: ConfigMap
metadata:
  annotations:
    kubesphere.io/creator: admin
  name: digital-human-micro-video-trans
  namespace: dh-v3

---
apiVersion: v1
kind: Service
metadata:
  labels:
    app: digital-human-micro-video-trans
  name: digital-human-micro-video-trans
  namespace: dh-v3
spec:
  ipFamilies:
  - IPv4
  ipFamilyPolicy: SingleStack
  ports:
  - name: http
    port: 8080
  selector:
    app: digital-human-micro-video-trans

---
apiVersion:  networking.k8s.io/v1
kind: Ingress
metadata:
  name: digital-human-micro-video-trans
  namespace: dh-v3
  annotations:
    nginx.ingress.kubernetes.io/proxy-body-size: 1024m  
    nginx.ingress.kubernetes.io/use-regex: "true"
spec:
  ingressClassName: nginx
  rules:
  - http:
      paths:
      - backend:
          service:
            name: digital-human-micro-video-trans
            port:
              number: 8080
        path: /api/digitalhuman/video/translate/acg/v1/*
        pathType: ImplementationSpecific

