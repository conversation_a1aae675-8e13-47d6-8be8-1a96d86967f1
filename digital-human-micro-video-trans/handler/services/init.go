package services

import (
	"acg-ai-go-common/logger"
	"digital-human-micro-video-trans/conf"
	"digital-human-micro-video-trans/handler/services/schedeuler"
	"digital-human-micro-video-trans/thirdparty/sutils/cron"
)

// InitScheduler 初始化调度器
func InitScheduler() error {
	schedeuler.Scheduler = schedeuler.NewTaskScheduler(conf.LocalConfig.ScheduleSettings.MaxRunningSize)

	c := cron.GetCron()
	_, err := c.RegisterCron("@every 5s", schedeuler.Scheduler.RunScheduler)
	if err != nil {
		logger.Log.Fatalf("RegisterCron RunScheduler error: %v\n", err)
		return err
	}

	c.StartCron()
	logger.Log.Infof("RegisterCron TaskScheduler success")

	return nil
}
