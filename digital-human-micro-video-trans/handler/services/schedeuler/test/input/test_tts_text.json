[{"id": "speaker_0", "sentences": [{"text": "健康饮食是现代生活中越来越为关注的话题。随着社会节奏的加快和生活方式的改变，人们对饮食的重视程度不断提高。合理的饮食不仅能够满足身体基本需求，还能预防各种疾病，护盾身体健康和心理健康。", "newText": "건강한 식습관은 현대 사회에서 중요한 관심사입니다. 사회의 빠른 변화로 인해 사람들은 식단에 대한 관심이 높아지고 있습니다. 균형 잡힌 식단은 신체의 기본적인 요구를 충족시킬 뿐만 아니라 질병을 예방하고 신체적, 정신적 건강을 보호합니다.\n", "start": 0.079, "end": 14.1039375, "lastStart": 12.88, "audio": "/home/<USER>/video-translate/cache/task/vte-sHvT2HA6XURjnvis/asr/asr_task_item_0_0.wav", "ttsFile": "/home/<USER>/video-translate/cache/task/vte-sHvT2HA6XURjnvis/tts/tts_task_item_0_0.wav", "ttsUrl": "https://storage.googleapis.com/xiling_us_central1_bucket/xjp-test/micro-video-translate/task/2025-07-10/vte-sHvT2HA6XURjnvis/tts_task_item_0_0.wav", "ttStart": 0.08533333333333333, "ttsEnd": 14.0249375, "scale": 1, "alignment": {"characters": ["ᄀ", "ㅓ", "ᆫ", "ᄀ", "ㅏ", "ᆼ", "ᄒ", "ㅏ", "ᆫ", " ", "ᄉ", "ㅣ", "ᆨ", "ᄉ", "ㅡ", "ᆸ", "ᄀ", "ㅘ", "ᄂ", "ㅡ", "ᆫ", " ", "ᄒ", "ㅕ", "ᆫ", "ᄃ", "ㅐ", " ", "ᄉ", "ㅏ", "ᄒ", "ㅚ", "ᄋ", "ㅔ", "ᄉ", "ㅓ", " ", "ᄌ", "ㅜ", "ᆼ", "ᄋ", "ㅛ", "ᄒ", "ㅏ", "ᆫ", " ", "ᄀ", "ㅘ", "ᆫ", "ᄉ", "ㅣ", "ᆷ", "ᄉ", "ㅏ", "ᄋ", "ㅣ", "ᆸ", "ᄂ", "ㅣ", "ᄃ", "ㅏ", ".", " ", "ᄉ", "ㅏ", "ᄒ", "ㅚ", "ᄋ", "ㅢ", " ", "ᄈ", "ㅏ", "ᄅ", "ㅡ", "ᆫ", " ", "ᄇ", "ㅕ", "ᆫ", "ᄒ", "ㅘ", "ᄅ", "ㅗ", " ", "ᄋ", "ㅣ", "ᆫ", "ᄒ", "ㅐ", " ", "ᄉ", "ㅏ", "ᄅ", "ㅏ", "ᆷ", "ᄃ", "ㅡ", "ᄅ", "ㅡ", "ᆫ", " ", "ᄉ", "ㅣ", "ᆨ", "ᄃ", "ㅏ", "ᄂ", "ㅔ", " ", "ᄃ", "ㅐ", "ᄒ", "ㅏ", "ᆫ", " ", "ᄀ", "ㅘ", "ᆫ", "ᄉ", "ㅣ", "ᄆ", "ㅣ", " ", "ᄂ", "ㅗ", "ᄑ", "ㅏ", "ᄌ", "ㅣ", "ᄀ", "ㅗ", " ", "ᄋ", "ㅣ", "ᆮ", "ᄉ", "ㅡ", "ᆸ", "ᄂ", "ㅣ", "ᄃ", "ㅏ", ".", "ᄀ", "ㅠ", "ᆫ", "ᄒ", "ㅕ", "ᆼ", " ", "ᄌ", "ㅏ", "ᆸ", "ᄒ", "ㅣ", "ᆫ", " ", "ᄉ", "ㅣ", "ᆨ", "ᄃ", "ㅏ", "ᄂ", "ㅡ", "ᆫ", " ", "ᄉ", "ㅣ", "ᆫ", "ᄎ", "ㅔ", "ᄋ", "ㅢ", " ", "ᄀ", "ㅣ", "ᄇ", "ㅗ", "ᆫ", "ᄌ", "ㅓ", "ᄀ", "ㅣ", "ᆫ", " ", "ᄋ", "ㅛ", "ᄀ", "ㅜ", "ᄅ", "ㅡ", "ᆯ", " ", "ᄎ", "ㅜ", "ᆼ", "ᄌ", "ㅗ", "ᆨ", "ᄉ", "ㅣ", "ᄏ", "ㅣ", "ᆯ", " ", "ᄈ", "ㅜ", "ᆫ", "ᄆ", "ㅏ", "ᆫ", " ", "ᄋ", "ㅏ", "ᄂ", "ㅣ", "ᄅ", "ㅏ", " ", "ᄌ", "ㅣ", "ᆯ", "ᄇ", "ㅕ", "ᆼ", "ᄋ", "ㅡ", "ᆯ", " ", "ᄋ", "ㅖ", "ᄇ", "ㅏ", "ᆼ", "ᄒ", "ㅏ", "ᄀ", "ㅗ", " ", "ᄉ", "ㅣ", "ᆫ", "ᄎ", "ㅔ", "ᄌ", "ㅓ", "ᆨ", ",", " ", "ᄌ", "ㅓ", "ᆼ", "ᄉ", "ㅣ", "ᆫ", "ᄌ", "ㅓ", "ᆨ", " ", "ᄀ", "ㅓ", "ᆫ", "ᄀ", "ㅏ", "ᆼ", "ᄋ", "ㅡ", "ᆯ", " ", "ᄇ", "ㅗ", "ᄒ", "ㅗ", "ᄒ", "ㅏ", "ᆸ", "ᄂ", "ㅣ", "ᄃ", "ㅏ", "."], "character_start_times_seconds": [0.08533333333333333, 0.17066666666666666, 0.256, 0.29866666666666664, 0.3413333333333333, 0.384, 0.42666666666666664, 0.4693333333333333, 0.512, 0.5546666666666666, 0.5973333333333333, 0.64, 0.6826666666666666, 0.7253333333333333, 0.768, 0.8106666666666666, 0.8533333333333333, 0.896, 0.9386666666666666, 0.9813333333333333, 1.1093333333333333, 1.152, 1.2373333333333334, 1.28, 1.408, 1.4506666666666665, 1.4933333333333332, 1.536, 1.5786666666666667, 1.6213333333333333, 1.664, 1.7066666666666666, 1.7493333333333332, 1.792, 1.8346666666666667, 1.8773333333333333, 1.92, 1.9626666666666666, 2.005333333333333, 2.048, 2.0906666666666665, 2.1333333333333333, 2.1759999999999997, 2.2186666666666666, 2.2613333333333334, 2.304, 2.3466666666666667, 2.389333333333333, 2.432, 2.474666666666667, 2.517333333333333, 2.56, 2.6026666666666665, 2.6453333333333333, 2.6879999999999997, 2.7306666666666666, 2.7733333333333334, 2.816, 2.8586666666666667, 2.901333333333333, 2.944, 2.9866666666666664, 3.029333333333333, 3.1573333333333333, 3.1999999999999997, 3.2426666666666666, 3.2853333333333334, 3.328, 3.3706666666666667, 3.413333333333333, 3.456, 3.4986666666666664, 3.541333333333333, 3.584, 3.6266666666666665, 3.6693333333333333, 3.7119999999999997, 3.7546666666666666, 3.797333333333333, 3.84, 3.8826666666666667, 3.925333333333333, 3.968, 4.010666666666666, 4.053333333333333, 4.096, 4.1386666666666665, 4.181333333333333, 4.224, 4.266666666666667, 4.309333333333333, 4.351999999999999, 4.394666666666667, 4.437333333333333, 4.479999999999999, 4.522666666666667, 4.565333333333333, 4.608, 4.650666666666667, 4.693333333333333, 4.736, 4.778666666666666, 4.8213333333333335, 4.864, 4.906666666666666, 4.949333333333334, 4.992, 5.034666666666666, 5.077333333333333, 5.12, 5.162666666666666, 5.205333333333333, 5.248, 5.290666666666667, 5.333333333333333, 5.3759999999999994, 5.418666666666667, 5.461333333333333, 5.504, 5.546666666666667, 5.589333333333333, 5.632, 5.674666666666666, 5.717333333333333, 5.76, 5.802666666666666, 5.845333333333334, 5.888, 5.930666666666666, 5.973333333333333, 6.016, 6.058666666666666, 6.101333333333333, 6.144, 6.1866666666666665, 6.229333333333333, 6.271999999999999, 6.314666666666667, 6.357333333333333, 6.3999999999999995, 6.442666666666667, 6.485333333333333, 6.528, 6.776743764172337, 6.8620770975056695, 6.904743764172336, 6.947410430839002, 6.99007709750567, 7.032743764172336, 7.0754104308390025, 7.11807709750567, 7.160743764172336, 7.203410430839003, 7.246077097505669, 7.288743764172336, 7.331410430839003, 7.374077097505669, 7.416743764172336, 7.459410430839003, 7.502077097505669, 7.5447437641723365, 7.587410430839002, 7.630077097505669, 7.672743764172337, 7.715410430839002, 7.758077097505669, 7.971410430839002, 8.01407709750567, 8.14207709750567, 8.184743764172337, 8.227410430839003, 8.27007709750567, 8.312743764172335, 8.355410430839003, 8.39807709750567, 8.440743764172336, 8.483410430839003, 8.52607709750567, 8.568743764172336, 8.611410430839003, 8.65407709750567, 8.696743764172336, 8.739410430839003, 8.782077097505669, 8.824743764172336, 8.867410430839003, 8.910077097505669, 8.952743764172336, 8.995410430839001, 9.038077097505669, 9.080743764172338, 9.123410430839002, 9.166077097505669, 9.208743764172336, 9.251410430839003, 9.294077097505669, 9.336743764172336, 9.379410430839004, 9.42207709750567, 9.464743764172335, 9.507410430839002, 9.55007709750567, 9.592743764172335, 9.635410430839002, 9.67807709750567, 9.720743764172335, 9.763410430839004, 9.80607709750567, 9.848743764172335, 9.891410430839004, 9.93407709750567, 9.976743764172335, 10.019410430839, 10.06207709750567, 10.104743764172335, 10.147410430839, 10.19007709750567, 10.232743764172335, 10.275410430839003, 10.31807709750567, 10.360743764172335, 10.403410430839001, 10.44607709750567, 10.488743764172336, 10.531410430839001, 10.57407709750567, 10.616743764172336, 10.659410430839001, 10.702077097505668, 10.744743764172336, 10.787410430839001, 10.830077097505669, 10.872743764172338, 10.915410430839001, 10.958077097505669, 11.000743764172338, 11.043410430839002, 11.086077097505669, 11.171410430839002, 11.427410430839002, 11.47007709750567, 11.640743764172335, 11.683410430839002, 11.72607709750567, 11.768743764172335, 11.85407709750567, 11.896743764172337, 11.98207709750567, 12.024743764172337, 12.152743764172337, 12.195410430839, 12.323410430839001, 12.36607709750567, 12.451410430839003, 12.49407709750567, 12.536743764172336, 12.62207709750567, 12.664743764172336, 12.707410430839001, 12.835410430839001, 12.878077097505669, 12.920743764172338, 12.963410430839001, 13.048743764172338, 13.091410430839002, 13.134077097505669, 13.176743764172338, 13.219410430839002, 13.262077097505669, 13.475410430839004, 13.51807709750567, 13.560743764172335, 13.603410430839002, 13.64607709750567, 13.688743764172335, 13.731410430839002, 13.77407709750567, 13.816743764172337, 13.859410430839002, 13.90207709750567, 13.944743764172337], "character_end_times_seconds": [0.17066666666666666, 0.256, 0.2986666666666667, 0.3413333333333333, 0.384, 0.4266666666666667, 0.4693333333333333, 0.512, 0.5546666666666666, 0.5973333333333334, 0.64, 0.6826666666666666, 0.7253333333333334, 0.768, 0.8106666666666666, 0.8533333333333334, 0.896, 0.9386666666666666, 0.9813333333333333, 1.1093333333333333, 1.152, 1.2373333333333334, 1.28, 1.408, 1.4506666666666668, 1.4933333333333334, 1.536, 1.5786666666666667, 1.6213333333333333, 1.664, 1.7066666666666668, 1.7493333333333334, 1.792, 1.8346666666666667, 1.8773333333333333, 1.92, 1.9626666666666666, 2.005333333333333, 2.048, 2.0906666666666665, 2.1333333333333333, 2.176, 2.2186666666666666, 2.2613333333333334, 2.304, 2.3466666666666667, 2.3893333333333335, 2.432, 2.474666666666667, 2.517333333333333, 2.56, 2.6026666666666665, 2.6453333333333333, 2.688, 2.7306666666666666, 2.7733333333333334, 2.816, 2.8586666666666667, 2.9013333333333335, 2.944, 2.986666666666667, 3.029333333333333, 3.1573333333333333, 3.2, 3.2426666666666666, 3.2853333333333334, 3.328, 3.3706666666666667, 3.4133333333333336, 3.456, 3.498666666666667, 3.541333333333333, 3.584, 3.6266666666666665, 3.6693333333333333, 3.712, 3.7546666666666666, 3.7973333333333334, 3.84, 3.8826666666666667, 3.925333333333333, 3.968, 4.010666666666666, 4.053333333333334, 4.096, 4.1386666666666665, 4.181333333333333, 4.224, 4.266666666666667, 4.309333333333333, 4.352, 4.394666666666667, 4.437333333333333, 4.48, 4.522666666666667, 4.565333333333333, 4.608, 4.650666666666667, 4.693333333333333, 4.736, 4.778666666666667, 4.8213333333333335, 4.864, 4.906666666666666, 4.949333333333334, 4.992, 5.034666666666666, 5.077333333333334, 5.12, 5.162666666666666, 5.205333333333333, 5.248, 5.290666666666667, 5.333333333333333, 5.376, 5.418666666666667, 5.461333333333333, 5.504, 5.546666666666667, 5.589333333333333, 5.632, 5.674666666666667, 5.717333333333333, 5.76, 5.802666666666667, 5.845333333333334, 5.888, 5.930666666666666, 5.973333333333334, 6.016, 6.058666666666666, 6.101333333333334, 6.144, 6.1866666666666665, 6.229333333333333, 6.272, 6.314666666666667, 6.357333333333333, 6.4, 6.442666666666667, 6.485333333333333, 6.528, 6.570666666666667, 6.8620770975056695, 6.904743764172336, 6.947410430839002, 6.99007709750567, 7.032743764172336, 7.0754104308390025, 7.11807709750567, 7.160743764172336, 7.203410430839003, 7.246077097505669, 7.288743764172336, 7.331410430839003, 7.374077097505669, 7.416743764172336, 7.459410430839003, 7.502077097505669, 7.5447437641723365, 7.587410430839002, 7.630077097505669, 7.672743764172337, 7.715410430839002, 7.758077097505669, 7.971410430839002, 8.01407709750567, 8.14207709750567, 8.184743764172337, 8.227410430839003, 8.27007709750567, 8.312743764172335, 8.355410430839003, 8.39807709750567, 8.440743764172336, 8.483410430839003, 8.52607709750567, 8.568743764172336, 8.611410430839003, 8.65407709750567, 8.696743764172336, 8.739410430839003, 8.782077097505669, 8.824743764172336, 8.867410430839003, 8.910077097505669, 8.952743764172336, 8.995410430839003, 9.038077097505669, 9.080743764172338, 9.123410430839002, 9.166077097505669, 9.208743764172336, 9.251410430839003, 9.294077097505669, 9.336743764172336, 9.379410430839004, 9.42207709750567, 9.464743764172335, 9.507410430839004, 9.55007709750567, 9.592743764172337, 9.635410430839002, 9.67807709750567, 9.720743764172335, 9.763410430839004, 9.80607709750567, 9.848743764172335, 9.891410430839004, 9.93407709750567, 9.976743764172335, 10.019410430839004, 10.06207709750567, 10.104743764172337, 10.147410430839, 10.19007709750567, 10.232743764172335, 10.275410430839003, 10.31807709750567, 10.360743764172335, 10.403410430839003, 10.44607709750567, 10.488743764172336, 10.531410430839001, 10.57407709750567, 10.616743764172336, 10.659410430839003, 10.702077097505668, 10.744743764172336, 10.787410430839001, 10.830077097505669, 10.872743764172338, 10.915410430839001, 10.958077097505669, 11.000743764172338, 11.043410430839002, 11.086077097505669, 11.171410430839002, 11.427410430839002, 11.47007709750567, 11.640743764172335, 11.683410430839004, 11.72607709750567, 11.768743764172335, 11.85407709750567, 11.896743764172337, 11.98207709750567, 12.024743764172337, 12.152743764172337, 12.195410430839, 12.323410430839001, 12.36607709750567, 12.451410430839003, 12.49407709750567, 12.536743764172336, 12.62207709750567, 12.664743764172336, 12.707410430839003, 12.835410430839001, 12.878077097505669, 12.920743764172338, 12.963410430839001, 13.048743764172338, 13.091410430839002, 13.134077097505669, 13.176743764172338, 13.219410430839002, 13.262077097505669, 13.475410430839004, 13.51807709750567, 13.560743764172335, 13.603410430839004, 13.64607709750567, 13.688743764172335, 13.731410430839004, 13.77407709750567, 13.816743764172337, 13.859410430839002, 13.90207709750567, 13.944743764172337, 13.987410430839002]}}, {"text": "本文将从健康饮食的定义、原则、食物种类、饮食习惯的影响，以及现代社会中如何保持健康等等方面进行详细的阐释，希望对大家更好地理解和实践健康饮食方式。", "newText": "본 글에서는 건강한 식습관의 정의, 원칙, 음식 종류, 식습관이 미치는 영향, 그리고 현대 사회에서 건강을 유지하는 방법 등을 자세히 설명하여 건강한 식습관을 더 잘 이해하고 실천하는 데 도움이 되고자 합니다.\n", "start": 14.1039375, "end": 25.702375, "lastStart": 25.079, "audio": "/home/<USER>/video-translate/cache/task/vte-sHvT2HA6XURjnvis/asr/asr_task_item_0_1.wav", "ttsFile": "/home/<USER>/video-translate/cache/task/vte-sHvT2HA6XURjnvis/tts/tts_task_item_0_1.wav", "ttsUrl": "https://storage.googleapis.com/xiling_us_central1_bucket/xjp-test/micro-video-translate/task/2025-07-10/vte-sHvT2HA6XURjnvis/tts_task_item_0_1.wav", "ttStart": 0.17066666666666666, "ttsEnd": 11.5984375, "scale": 1, "alignment": {"characters": ["ᄇ", "ㅗ", "ᆫ", " ", "ᄀ", "ㅡ", "ᄅ", "ㅔ", "ᄉ", "ㅓ", "ᄂ", "ㅡ", "ᆫ", " ", "ᄀ", "ㅓ", "ᆫ", "ᄀ", "ㅏ", "ᆼ", "ᄒ", "ㅏ", "ᆫ", " ", "ᄉ", "ㅣ", "ᆨ", "ᄉ", "ㅡ", "ᆸ", "ᄀ", "ㅘ", "ᄂ", "ㅢ", " ", "ᄌ", "ㅓ", "ᆼ", "ᄋ", "ㅢ", ",", " ", "ᄋ", "ㅝ", "ᆫ", "ᄎ", "ㅣ", "ᆨ", ",", " ", "ᄋ", "ㅡ", "ᆷ", "ᄉ", "ㅣ", "ᆨ", " ", "ᄌ", "ㅗ", "ᆼ", "ᄅ", "ㅠ", ",", " ", "ᄉ", "ㅣ", "ᆨ", "ᄉ", "ㅡ", "ᆸ", "ᄀ", "ㅘ", "ᄂ", "ㅣ", " ", "ᄆ", "ㅣ", "ᄎ", "ㅣ", "ᄂ", "ㅡ", "ᆫ", " ", "ᄋ", "ㅕ", "ᆼ", "ᄒ", "ㅑ", "ᆼ", ",", "ᄀ", "ㅡ", "ᄅ", "ㅣ", "ᄀ", "ㅗ", " ", "ᄒ", "ㅕ", "ᆫ", "ᄃ", "ㅐ", " ", "ᄉ", "ㅏ", "ᄒ", "ㅚ", "ᄋ", "ㅔ", "ᄉ", "ㅓ", " ", "ᄀ", "ㅓ", "ᆫ", "ᄀ", "ㅏ", "ᆼ", "ᄋ", "ㅡ", "ᆯ", " ", "ᄋ", "ㅠ", "ᄌ", "ㅣ", "ᄒ", "ㅏ", "ᄂ", "ㅡ", "ᆫ", " ", "ᄇ", "ㅏ", "ᆼ", "ᄇ", "ㅓ", "ᆸ", " ", "ᄃ", "ㅡ", "ᆼ", "ᄋ", "ㅡ", "ᆯ", " ", "ᄌ", "ㅏ", "ᄉ", "ㅔ", "ᄒ", "ㅣ", " ", "ᄉ", "ㅓ", "ᆯ", "ᄆ", "ㅕ", "ᆼ", "ᄒ", "ㅏ", "ᄋ", "ㅕ", " ", "ᄀ", "ㅓ", "ᆫ", "ᄀ", "ㅏ", "ᆼ", "ᄒ", "ㅏ", "ᆫ", " ", "ᄉ", "ㅣ", "ᆨ", "ᄉ", "ㅡ", "ᆸ", "ᄀ", "ㅘ", "ᄂ", "ㅡ", "ᆯ", " ", "ᄃ", "ㅓ", " ", "ᄌ", "ㅏ", "ᆯ", " ", "ᄋ", "ㅣ", "ᄒ", "ㅐ", "ᄒ", "ㅏ", "ᄀ", "ㅗ", " ", "ᄉ", "ㅣ", "ᆯ", "ᄎ", "ㅓ", "ᆫ", "ᄒ", "ㅏ", "ᄂ", "ㅡ", "ᆫ", " ", "ᄃ", "ㅔ", " ", "ᄃ", "ㅗ", "ᄋ", "ㅜ", "ᄆ", "ㅣ", " ", "ᄃ", "ㅚ", "ᄀ", "ㅗ", "ᄌ", "ㅏ", " ", "ᄒ", "ㅏ", "ᆸ", "ᄂ", "ㅣ", "ᄃ", "ㅏ", "."], "character_start_times_seconds": [0.17066666666666666, 0.21333333333333332, 0.256, 0.29866666666666664, 0.3413333333333333, 0.384, 0.42666666666666664, 0.4693333333333333, 0.512, 0.5546666666666666, 0.5973333333333333, 0.64, 0.6826666666666666, 0.7253333333333333, 0.8533333333333333, 0.896, 0.9386666666666666, 0.9813333333333333, 1.024, 1.0666666666666667, 1.1093333333333333, 1.152, 1.1946666666666665, 1.2373333333333334, 1.28, 1.3226666666666667, 1.3653333333333333, 1.408, 1.4933333333333332, 1.536, 1.5786666666666667, 1.664, 1.7066666666666666, 1.7493333333333332, 1.792, 1.8346666666666667, 1.8773333333333333, 1.9626666666666666, 2.005333333333333, 2.048, 2.0906666666666665, 2.1333333333333333, 2.1759999999999997, 2.2186666666666666, 2.2613333333333334, 2.304, 2.3466666666666667, 2.389333333333333, 2.432, 2.474666666666667, 2.517333333333333, 2.56, 2.6026666666666665, 2.6453333333333333, 2.6879999999999997, 2.7306666666666666, 2.7733333333333334, 2.816, 2.8586666666666667, 2.901333333333333, 2.944, 2.9866666666666664, 3.029333333333333, 3.072, 3.1146666666666665, 3.1573333333333333, 3.1999999999999997, 3.2426666666666666, 3.2853333333333334, 3.328, 3.3706666666666667, 3.413333333333333, 3.456, 3.4986666666666664, 3.541333333333333, 3.584, 3.6266666666666665, 3.6693333333333333, 3.7119999999999997, 3.7546666666666666, 3.797333333333333, 3.84, 3.8826666666666667, 3.925333333333333, 3.968, 4.010666666666666, 4.053333333333333, 4.096, 4.1386666666666665, 4.181333333333333, 4.435301587301588, 4.477968253968254, 4.605968253968254, 4.648634920634921, 4.691301587301587, 4.733968253968254, 4.819301587301587, 4.947301587301587, 5.117968253968255, 5.245968253968255, 5.28863492063492, 5.331301587301588, 5.4166349206349205, 5.459301587301588, 5.501968253968254, 5.544634920634921, 5.587301587301588, 5.629968253968254, 5.672634920634921, 5.715301587301587, 5.757968253968254, 5.800634920634921, 5.843301587301587, 5.8859682539682545, 5.928634920634921, 5.971301587301587, 6.013968253968255, 6.05663492063492, 6.0993015873015874, 6.141968253968255, 6.18463492063492, 6.227301587301588, 6.269968253968255, 6.31263492063492, 6.355301587301588, 6.397968253968254, 6.4406349206349205, 6.483301587301587, 6.525968253968254, 6.568634920634921, 6.611301587301588, 6.653968253968254, 6.696634920634921, 6.739301587301587, 6.781968253968254, 6.824634920634921, 6.867301587301587, 6.9099682539682545, 6.95263492063492, 6.9953015873015865, 7.037968253968255, 7.08063492063492, 7.1233015873015875, 7.165968253968255, 7.20863492063492, 7.251301587301588, 7.293968253968254, 7.33663492063492, 7.379301587301588, 7.421968253968254, 7.4646349206349205, 7.507301587301587, 7.549968253968254, 7.592634920634921, 7.635301587301587, 7.677968253968254, 7.720634920634921, 7.763301587301587, 7.8059682539682544, 7.848634920634921, 7.891301587301587, 7.933968253968255, 7.97663492063492, 8.019301587301587, 8.061968253968255, 8.10463492063492, 8.147301587301587, 8.189968253968255, 8.23263492063492, 8.275301587301586, 8.317968253968255, 8.36063492063492, 8.403301587301586, 8.445968253968255, 8.48863492063492, 8.531301587301586, 8.573968253968255, 8.61663492063492, 8.659301587301586, 8.701968253968255, 8.74463492063492, 8.787301587301586, 8.829968253968254, 8.87263492063492, 8.915301587301586, 8.957968253968254, 9.000634920634921, 9.043301587301587, 9.085968253968254, 9.128634920634921, 9.171301587301587, 9.213968253968254, 9.256634920634923, 9.299301587301587, 9.341968253968254, 9.384634920634921, 9.427301587301587, 9.469968253968254, 9.51263492063492, 9.555301587301587, 9.597968253968254, 9.64063492063492, 9.683301587301587, 9.725968253968254, 9.76863492063492, 9.811301587301587, 9.853968253968254, 9.89663492063492, 9.939301587301587, 9.981968253968255, 10.02463492063492, 10.067301587301587, 10.109968253968255, 10.152634920634922, 10.195301587301586, 10.237968253968255, 10.323301587301586, 10.53663492063492, 10.579301587301586, 10.621968253968255, 10.66463492063492, 10.707301587301586, 10.749968253968255, 10.79263492063492, 10.835301587301586, 10.877968253968254, 10.920634920634921, 10.963301587301586, 11.005968253968254, 11.091301587301587, 11.176634920634923, 11.219301587301587, 11.261968253968254, 11.304634920634921, 11.347301587301587, 11.389968253968254, 11.432634920634921, 11.475301587301587, 11.517968253968254], "character_end_times_seconds": [0.21333333333333335, 0.256, 0.2986666666666667, 0.3413333333333333, 0.384, 0.4266666666666667, 0.4693333333333333, 0.512, 0.5546666666666666, 0.5973333333333334, 0.64, 0.6826666666666666, 0.7253333333333334, 0.8533333333333334, 0.896, 0.9386666666666666, 0.9813333333333333, 1.024, 1.0666666666666667, 1.1093333333333333, 1.152, 1.1946666666666668, 1.2373333333333334, 1.28, 1.3226666666666667, 1.3653333333333333, 1.408, 1.4933333333333334, 1.536, 1.5786666666666667, 1.664, 1.7066666666666668, 1.7493333333333334, 1.792, 1.8346666666666667, 1.8773333333333333, 1.9626666666666666, 2.005333333333333, 2.048, 2.0906666666666665, 2.1333333333333333, 2.176, 2.2186666666666666, 2.2613333333333334, 2.304, 2.3466666666666667, 2.3893333333333335, 2.432, 2.474666666666667, 2.517333333333333, 2.56, 2.6026666666666665, 2.6453333333333333, 2.688, 2.7306666666666666, 2.7733333333333334, 2.816, 2.8586666666666667, 2.9013333333333335, 2.944, 2.986666666666667, 3.029333333333333, 3.072, 3.1146666666666665, 3.1573333333333333, 3.2, 3.2426666666666666, 3.2853333333333334, 3.328, 3.3706666666666667, 3.4133333333333336, 3.456, 3.498666666666667, 3.541333333333333, 3.584, 3.6266666666666665, 3.6693333333333333, 3.712, 3.7546666666666666, 3.7973333333333334, 3.84, 3.8826666666666667, 3.925333333333333, 3.968, 4.010666666666666, 4.053333333333334, 4.096, 4.1386666666666665, 4.181333333333333, 4.224, 4.477968253968254, 4.605968253968254, 4.648634920634921, 4.691301587301587, 4.733968253968254, 4.819301587301587, 4.947301587301587, 5.117968253968255, 5.245968253968255, 5.28863492063492, 5.331301587301588, 5.4166349206349205, 5.459301587301588, 5.501968253968254, 5.544634920634921, 5.587301587301588, 5.629968253968254, 5.672634920634921, 5.715301587301587, 5.757968253968254, 5.800634920634921, 5.843301587301587, 5.8859682539682545, 5.928634920634921, 5.971301587301587, 6.013968253968255, 6.05663492063492, 6.0993015873015874, 6.141968253968255, 6.18463492063492, 6.227301587301588, 6.269968253968255, 6.31263492063492, 6.355301587301588, 6.397968253968254, 6.4406349206349205, 6.483301587301588, 6.525968253968254, 6.568634920634921, 6.611301587301588, 6.653968253968254, 6.696634920634921, 6.739301587301587, 6.781968253968254, 6.824634920634921, 6.867301587301587, 6.9099682539682545, 6.95263492063492, 6.995301587301587, 7.037968253968255, 7.08063492063492, 7.1233015873015875, 7.165968253968255, 7.20863492063492, 7.251301587301588, 7.293968253968254, 7.33663492063492, 7.379301587301588, 7.421968253968254, 7.4646349206349205, 7.507301587301588, 7.549968253968254, 7.592634920634921, 7.635301587301587, 7.677968253968254, 7.720634920634921, 7.763301587301587, 7.8059682539682544, 7.848634920634921, 7.891301587301587, 7.933968253968255, 7.97663492063492, 8.019301587301587, 8.061968253968255, 8.10463492063492, 8.147301587301587, 8.189968253968255, 8.23263492063492, 8.275301587301586, 8.317968253968255, 8.36063492063492, 8.403301587301586, 8.445968253968255, 8.48863492063492, 8.531301587301586, 8.573968253968255, 8.61663492063492, 8.659301587301586, 8.701968253968255, 8.74463492063492, 8.787301587301586, 8.829968253968254, 8.87263492063492, 8.915301587301586, 8.957968253968254, 9.000634920634921, 9.043301587301587, 9.085968253968254, 9.128634920634921, 9.171301587301587, 9.213968253968254, 9.256634920634923, 9.299301587301587, 9.341968253968254, 9.384634920634923, 9.427301587301587, 9.469968253968254, 9.51263492063492, 9.555301587301587, 9.597968253968254, 9.64063492063492, 9.683301587301587, 9.725968253968254, 9.76863492063492, 9.811301587301587, 9.853968253968254, 9.89663492063492, 9.939301587301587, 9.981968253968255, 10.02463492063492, 10.067301587301587, 10.109968253968255, 10.152634920634922, 10.195301587301586, 10.237968253968255, 10.323301587301586, 10.53663492063492, 10.579301587301586, 10.621968253968255, 10.66463492063492, 10.707301587301586, 10.749968253968255, 10.79263492063492, 10.835301587301586, 10.877968253968254, 10.920634920634921, 10.963301587301586, 11.005968253968254, 11.091301587301587, 11.176634920634923, 11.219301587301587, 11.261968253968254, 11.304634920634923, 11.347301587301587, 11.389968253968254, 11.432634920634923, 11.475301587301587, 11.517968253968254, 11.56063492063492]}}, {"text": "首先，健康饮食是指通过合理搭配各类食物，这把钥匙控制体重、调节饱腹感、防止过食等，同样满足人体生理需求和维持健康状态的需要。健康饮食的核心在于平衡及不同营养素之间的合理比例，如蛋白质、脂肪、碳水化合物、维生素和矿物质等。只有保持营养均衡，才能保证身体各项功能的正常运行。", "newText": "건강한 식단은 다양한 음식을 섭취하여 체중을 조절하고 포만감을 유지하며 과식을 방지하는 데 필수적입니다. 또한, 건강한 식단의 핵심은 다양한 영양소의 균형과 적절한 비율을 유지하는 데 있습니다. 영양 균형을 유지해야 신체 기능이 원활하게 작동합니다.\n", "start": 25.702375, "end": 41.782250000000005, "lastStart": 43.18, "audio": "/home/<USER>/video-translate/cache/task/vte-sHvT2HA6XURjnvis/asr/asr_task_item_0_2.wav", "ttsFile": "/home/<USER>/video-translate/cache/task/vte-sHvT2HA6XURjnvis/tts/tts_task_item_0_2.wav", "ttsUrl": "https://storage.googleapis.com/xiling_us_central1_bucket/xjp-test/micro-video-translate/task/2025-07-10/vte-sHvT2HA6XURjnvis/tts_task_item_0_2.wav", "ttStart": 0.08533333333333333, "ttsEnd": 16.079875, "scale": 1, "alignment": {"characters": ["ᄀ", "ㅓ", "ᆫ", "ᄀ", "ㅏ", "ᆼ", "ᄒ", "ㅏ", "ᆫ", " ", "ᄉ", "ㅣ", "ᆨ", "ᄃ", "ㅏ", "ᄂ", "ㅡ", "ᆫ", " ", "ᄃ", "ㅏ", "ᄋ", "ㅑ", "ᆼ", "ᄒ", "ㅏ", "ᆫ", " ", "ᄋ", "ㅡ", "ᆷ", "ᄉ", "ㅣ", "ᄀ", "ㅡ", "ᆯ", " ", "ᄉ", "ㅓ", "ᆸ", "ᄎ", "ㅟ", "ᄒ", "ㅏ", "ᄋ", "ㅕ", " ", "ᄎ", "ㅔ", "ᄌ", "ㅜ", "ᆼ", "ᄋ", "ㅡ", "ᆯ", " ", "ᄌ", "ㅗ", "ᄌ", "ㅓ", "ᆯ", "ᄒ", "ㅏ", "ᄀ", "ㅗ", " ", "ᄑ", "ㅗ", "ᄆ", "ㅏ", "ᆫ", "ᄀ", "ㅏ", "ᄆ", "ㅡ", "ᆯ", " ", "ᄋ", "ㅠ", "ᄌ", "ㅣ", "ᄒ", "ㅏ", "ᄆ", "ㅕ", " ", "ᄀ", "ㅘ", "ᄉ", "ㅣ", "ᄀ", "ㅡ", "ᆯ", " ", "ᄇ", "ㅏ", "ᆼ", "ᄌ", "ㅣ", "ᄒ", "ㅏ", "ᄂ", "ㅡ", "ᆫ", " ", "ᄃ", "ㅔ", " ", "ᄑ", "ㅣ", "ᆯ", "ᄉ", "ㅜ", "ᄌ", "ㅓ", "ᄀ", "ㅣ", "ᆸ", "ᄂ", "ㅣ", "ᄃ", "ㅏ", ".", "ᄄ", "ㅗ", "ᄒ", "ㅏ", "ᆫ", ",", " ", "ᄀ", "ㅓ", "ᆫ", "ᄀ", "ㅏ", "ᆼ", "ᄒ", "ㅏ", "ᆫ", " ", "ᄉ", "ㅣ", "ᆨ", "ᄃ", "ㅏ", "ᄂ", "ㅢ", " ", "ᄒ", "ㅐ", "ᆨ", "ᄉ", "ㅣ", "ᄆ", "ㅡ", "ᆫ", " ", "ᄃ", "ㅏ", "ᄋ", "ㅑ", "ᆼ", "ᄒ", "ㅏ", "ᆫ", " ", "ᄋ", "ㅕ", "ᆼ", "ᄋ", "ㅑ", "ᆼ", "ᄉ", "ㅗ", "ᄋ", "ㅢ", " ", "ᄀ", "ㅠ", "ᆫ", "ᄒ", "ㅕ", "ᆼ", "ᄀ", "ㅘ", " ", "ᄌ", "ㅓ", "ᆨ", "ᄌ", "ㅓ", "ᆯ", "ᄒ", "ㅏ", "ᆫ", " ", "ᄇ", "ㅣ", "ᄋ", "ㅠ", "ᄅ", "ㅡ", "ᆯ", " ", "ᄋ", "ㅠ", "ᄌ", "ㅣ", "ᄒ", "ㅏ", "ᄂ", "ㅡ", "ᆫ", " ", "ᄃ", "ㅔ", " ", "ᄋ", "ㅣ", "ᆮ", "ᄉ", "ㅡ", "ᆸ", "ᄂ", "ㅣ", "ᄃ", "ㅏ", ".", "ᄋ", "ㅕ", "ᆼ", "ᄋ", "ㅑ", "ᆼ", " ", "ᄀ", "ㅠ", "ᆫ", "ᄒ", "ㅕ", "ᆼ", "ᄋ", "ㅡ", "ᆯ", " ", "ᄋ", "ㅠ", "ᄌ", "ㅣ", "ᄒ", "ㅐ", "ᄋ", "ㅑ", " ", "ᄉ", "ㅣ", "ᆫ", "ᄎ", "ㅔ", " ", "ᄀ", "ㅣ", "ᄂ", "ㅡ", "ᆼ", "ᄋ", "ㅣ", " ", "ᄋ", "ㅝ", "ᆫ", "ᄒ", "ㅘ", "ᆯ", "ᄒ", "ㅏ", "ᄀ", "ㅔ", " ", "ᄌ", "ㅏ", "ᆨ", "ᄃ", "ㅗ", "ᆼ", "ᄒ", "ㅏ", "ᆸ", "ᄂ", "ㅣ", "ᄃ", "ㅏ", "."], "character_start_times_seconds": [0.08533333333333333, 0.17066666666666666, 0.256, 0.29866666666666664, 0.3413333333333333, 0.384, 0.42666666666666664, 0.4693333333333333, 0.512, 0.5546666666666666, 0.5973333333333333, 0.64, 0.6826666666666666, 0.7253333333333333, 0.768, 0.8106666666666666, 0.8533333333333333, 0.9813333333333333, 1.024, 1.2373333333333334, 1.28, 1.4506666666666665, 1.4933333333333332, 1.536, 1.5786666666666667, 1.6213333333333333, 1.664, 1.7066666666666666, 1.7493333333333332, 1.792, 1.8346666666666667, 1.8773333333333333, 1.92, 1.9626666666666666, 2.005333333333333, 2.048, 2.0906666666666665, 2.1333333333333333, 2.1759999999999997, 2.2186666666666666, 2.2613333333333334, 2.304, 2.3466666666666667, 2.389333333333333, 2.432, 2.474666666666667, 2.56, 2.6453333333333333, 2.7733333333333334, 2.816, 2.8586666666666667, 2.901333333333333, 2.944, 2.9866666666666664, 3.029333333333333, 3.072, 3.1146666666666665, 3.1573333333333333, 3.1999999999999997, 3.2426666666666666, 3.2853333333333334, 3.328, 3.3706666666666667, 3.413333333333333, 3.456, 3.541333333333333, 3.7546666666666666, 3.8826666666666667, 3.968, 4.053333333333333, 4.1386666666666665, 4.181333333333333, 4.224, 4.266666666666667, 4.309333333333333, 4.351999999999999, 4.394666666666667, 4.437333333333333, 4.479999999999999, 4.522666666666667, 4.565333333333333, 4.608, 4.650666666666667, 4.693333333333333, 4.778666666666666, 4.864, 5.077333333333333, 5.162666666666666, 5.333333333333333, 5.3759999999999994, 5.418666666666667, 5.461333333333333, 5.504, 5.546666666666667, 5.589333333333333, 5.632, 5.674666666666666, 5.717333333333333, 5.76, 5.802666666666666, 5.845333333333334, 5.888, 5.930666666666666, 5.973333333333333, 6.016, 6.058666666666666, 6.101333333333333, 6.144, 6.1866666666666665, 6.229333333333333, 6.271999999999999, 6.314666666666667, 6.357333333333333, 6.3999999999999995, 6.442666666666667, 6.485333333333333, 6.528, 6.570666666666667, 6.613333333333333, 6.656, 6.698666666666666, 6.741333333333333, 6.784, 6.912, 6.997333333333334, 7.082666666666667, 7.168, 7.210666666666667, 7.296, 7.338666666666667, 7.552, 7.594666666666667, 7.765333333333334, 7.850666666666667, 7.936, 7.978666666666667, 8.021333333333335, 8.064, 8.106666666666667, 8.149333333333335, 8.192, 8.234666666666667, 8.277333333333335, 8.32, 8.405333333333333, 8.448, 8.490666666666668, 8.533333333333333, 8.576, 8.618666666666668, 8.661333333333333, 8.704, 8.746666666666668, 8.789333333333333, 8.832, 8.874666666666668, 8.917333333333334, 9.216, 9.258666666666668, 9.386666666666668, 9.429333333333334, 9.514666666666667, 9.557333333333334, 9.6, 9.642666666666669, 9.685333333333334, 9.728, 9.770666666666667, 9.813333333333334, 9.856, 9.898666666666667, 9.984, 10.026666666666666, 10.069333333333335, 10.197333333333335, 10.24, 10.282666666666668, 10.325333333333335, 10.368, 10.410666666666668, 10.453333333333333, 10.496, 10.538666666666666, 10.581333333333333, 10.624, 10.709333333333333, 10.794666666666668, 10.837333333333333, 10.88, 10.922666666666668, 10.965333333333332, 11.008, 11.050666666666668, 11.093333333333334, 11.136, 11.178666666666667, 11.221333333333334, 11.264, 11.306666666666667, 11.349333333333334, 11.392, 11.434666666666669, 11.477333333333334, 11.52, 11.562666666666669, 11.605333333333332, 11.648, 11.690666666666669, 11.733333333333333, 11.776, 11.818666666666667, 11.861333333333333, 11.904, 11.946666666666667, 11.989333333333333, 12.032, 12.074666666666667, 12.117333333333335, 12.16, 12.202666666666666, 12.245333333333335, 12.288, 12.330666666666668, 12.373333333333333, 12.416, 12.458666666666668, 12.501333333333331, 12.544, 12.794485260770974, 12.83715192743764, 12.922485260770975, 12.96515192743764, 13.00781859410431, 13.050485260770975, 13.09315192743764, 13.13581859410431, 13.178485260770975, 13.22115192743764, 13.263818594104308, 13.306485260770975, 13.34915192743764, 13.391818594104308, 13.434485260770975, 13.47715192743764, 13.519818594104308, 13.562485260770975, 13.60515192743764, 13.647818594104308, 13.775818594104308, 13.861151927437641, 13.903818594104308, 13.946485260770974, 13.989151927437641, 14.031818594104308, 14.202485260770974, 14.245151927437641, 14.373151927437641, 14.415818594104309, 14.458485260770974, 14.50115192743764, 14.543818594104309, 14.586485260770974, 14.62915192743764, 14.671818594104309, 14.75715192743764, 14.799818594104309, 14.842485260770975, 14.88515192743764, 14.970485260770975, 15.01315192743764, 15.05581859410431, 15.098485260770975, 15.14115192743764, 15.183818594104308, 15.226485260770975, 15.26915192743764, 15.311818594104308, 15.354485260770975, 15.39715192743764, 15.439818594104308, 15.482485260770975, 15.52515192743764, 15.567818594104308, 15.610485260770975, 15.65315192743764, 15.695818594104308, 15.738485260770975, 15.78115192743764, 15.823818594104308, 15.866485260770974, 15.909151927437641, 15.951818594104308, 15.994485260770974], "character_end_times_seconds": [0.17066666666666666, 0.256, 0.2986666666666667, 0.3413333333333333, 0.384, 0.4266666666666667, 0.4693333333333333, 0.512, 0.5546666666666666, 0.5973333333333334, 0.64, 0.6826666666666666, 0.7253333333333334, 0.768, 0.8106666666666666, 0.8533333333333334, 0.9813333333333333, 1.024, 1.2373333333333334, 1.28, 1.4506666666666668, 1.4933333333333334, 1.536, 1.5786666666666667, 1.6213333333333333, 1.664, 1.7066666666666668, 1.7493333333333334, 1.792, 1.8346666666666667, 1.8773333333333333, 1.92, 1.9626666666666666, 2.005333333333333, 2.048, 2.0906666666666665, 2.1333333333333333, 2.176, 2.2186666666666666, 2.2613333333333334, 2.304, 2.3466666666666667, 2.3893333333333335, 2.432, 2.474666666666667, 2.56, 2.6453333333333333, 2.7733333333333334, 2.816, 2.8586666666666667, 2.9013333333333335, 2.944, 2.986666666666667, 3.029333333333333, 3.072, 3.1146666666666665, 3.1573333333333333, 3.2, 3.2426666666666666, 3.2853333333333334, 3.328, 3.3706666666666667, 3.4133333333333336, 3.456, 3.541333333333333, 3.7546666666666666, 3.8826666666666667, 3.968, 4.053333333333334, 4.1386666666666665, 4.181333333333333, 4.224, 4.266666666666667, 4.309333333333333, 4.352, 4.394666666666667, 4.437333333333333, 4.48, 4.522666666666667, 4.565333333333333, 4.608, 4.650666666666667, 4.693333333333333, 4.778666666666667, 4.864, 5.077333333333334, 5.162666666666666, 5.333333333333333, 5.376, 5.418666666666667, 5.461333333333333, 5.504, 5.546666666666667, 5.589333333333333, 5.632, 5.674666666666667, 5.717333333333333, 5.76, 5.802666666666667, 5.845333333333334, 5.888, 5.930666666666666, 5.973333333333334, 6.016, 6.058666666666666, 6.101333333333334, 6.144, 6.1866666666666665, 6.229333333333333, 6.272, 6.314666666666667, 6.357333333333333, 6.4, 6.442666666666667, 6.485333333333333, 6.528, 6.570666666666667, 6.613333333333333, 6.656, 6.698666666666667, 6.741333333333333, 6.784, 6.826666666666667, 6.997333333333334, 7.082666666666667, 7.168, 7.210666666666667, 7.296, 7.338666666666667, 7.552, 7.594666666666667, 7.765333333333334, 7.850666666666667, 7.936, 7.978666666666667, 8.021333333333335, 8.064, 8.106666666666667, 8.149333333333335, 8.192, 8.234666666666667, 8.277333333333335, 8.32, 8.405333333333333, 8.448, 8.490666666666668, 8.533333333333333, 8.576, 8.618666666666668, 8.661333333333333, 8.704, 8.746666666666668, 8.789333333333333, 8.832, 8.874666666666668, 8.917333333333334, 9.216, 9.258666666666668, 9.386666666666668, 9.429333333333334, 9.514666666666669, 9.557333333333334, 9.6, 9.642666666666669, 9.685333333333334, 9.728, 9.770666666666667, 9.813333333333334, 9.856, 9.898666666666667, 9.984, 10.026666666666667, 10.069333333333335, 10.197333333333335, 10.24, 10.282666666666668, 10.325333333333335, 10.368, 10.410666666666668, 10.453333333333333, 10.496, 10.538666666666668, 10.581333333333333, 10.624, 10.709333333333333, 10.794666666666668, 10.837333333333333, 10.88, 10.922666666666668, 10.965333333333332, 11.008, 11.050666666666668, 11.093333333333334, 11.136, 11.178666666666668, 11.221333333333334, 11.264, 11.306666666666668, 11.349333333333334, 11.392, 11.434666666666669, 11.477333333333334, 11.52, 11.562666666666669, 11.605333333333334, 11.648, 11.690666666666669, 11.733333333333333, 11.776, 11.818666666666667, 11.861333333333333, 11.904, 11.946666666666667, 11.989333333333333, 12.032, 12.074666666666667, 12.117333333333335, 12.16, 12.202666666666667, 12.245333333333335, 12.288, 12.330666666666668, 12.373333333333333, 12.416, 12.458666666666668, 12.501333333333333, 12.544, 12.586666666666668, 12.83715192743764, 12.922485260770975, 12.96515192743764, 13.00781859410431, 13.050485260770975, 13.09315192743764, 13.13581859410431, 13.178485260770975, 13.22115192743764, 13.263818594104308, 13.306485260770975, 13.34915192743764, 13.391818594104308, 13.434485260770975, 13.47715192743764, 13.519818594104308, 13.562485260770975, 13.60515192743764, 13.647818594104308, 13.775818594104308, 13.861151927437641, 13.903818594104308, 13.946485260770974, 13.989151927437641, 14.031818594104308, 14.202485260770974, 14.245151927437641, 14.373151927437641, 14.415818594104309, 14.458485260770974, 14.50115192743764, 14.543818594104309, 14.586485260770974, 14.62915192743764, 14.671818594104309, 14.75715192743764, 14.799818594104309, 14.842485260770975, 14.88515192743764, 14.970485260770975, 15.01315192743764, 15.05581859410431, 15.098485260770975, 15.14115192743764, 15.183818594104308, 15.226485260770975, 15.26915192743764, 15.311818594104308, 15.354485260770975, 15.39715192743764, 15.439818594104308, 15.482485260770975, 15.52515192743764, 15.567818594104308, 15.610485260770975, 15.65315192743764, 15.695818594104308, 15.738485260770975, 15.78115192743764, 15.823818594104308, 15.866485260770974, 15.909151927437641, 15.951818594104308, 15.994485260770974, 16.03715192743764]}}], "lang": "Korean", "duration": 43.101, "audioNum": 3}]