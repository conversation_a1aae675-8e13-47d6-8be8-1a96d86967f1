package controller

import (
	"acg-ai-go-common/gomysql"
	"acg-ai-go-common/logger"
	"acg-ai-go-common/utils"
	"context"
	"digital-human-micro-video-trans/beans/enums"
	"digital-human-micro-video-trans/beans/model"
	"digital-human-micro-video-trans/beans/proto"
	"digital-human-micro-video-trans/conf"
	"digital-human-micro-video-trans/thirdparty/sutils/jsonutils"
	"fmt"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	"net/http"
	"strings"
	"time"
)

type TaskHandler struct {
}

// HandleAcgSubmitTask 提交任务
func (p TaskHandler) HandleAcgSubmitTask(c *gin.Context) {
	reqId := utils.RandStringRunes(16)
	logCtx := context.WithValue(context.TODO(), utils.CtxKeyLogID, reqId)

	resp := proto.SubmitTaskResponse{
		ReqId: reqId,
	}

	req := proto.SubmitTaskRequest{}
	if err := c.Should<PERSON>(&req); err != nil {
		logger.Log.<PERSON>(utils.MMark(logCtx)+"HandleSubmitTask BindJSON fail, err:%v", err)
		c.JSON(http.StatusOK, proto.NewCommonResponse(100001, "params invalid", resp))
		return
	}

	// 基础参数校验
	if len(req.VideoUrl) < 1 || len(req.TargetLangs) < 1 {
		logger.Log.Errorf(utils.MMark(logCtx) + "HandleSubmitTask check fail, params invalid")
		c.JSON(http.StatusOK, proto.NewCommonResponse(100002, "videoUrl or targetLangs absent", resp))
		return
	}

	err := gomysql.DB.Transaction(func(tx *gorm.DB) error {
		for _, lang := range req.TargetLangs {
			config := proto.SubmitConfig{
				TargetLangs:           []string{lang},
				SpeakerNum:            req.SpeakerNum,
				RemoveBackgroundAudio: req.RemoveBackgroundAudio,
				EnableCaptions:        req.EnableCaptions,
				EnableDynamicDuration: req.EnableDynamicDuration,
				TranslateAudioOnly:    req.TranslateAudioOnly,
			}
			jsConfig, err := jsonutils.StructToJsonMap(config)
			if err != nil {
				return fmt.Errorf("json to map failed: %v", err)
			}

			fileName := strings.ReplaceAll(req.FileName, " ", "")

			item := model.VideoTranslateTask{
				TaskId:        "vte-" + utils.RandStringRunes(16),
				UserID:        req.UserId,
				LastUpdateBy:  req.UserName,
				Name:          fileName,
				InputUrl:      req.VideoUrl,
				CallbackUrl:   req.CallbackUrl,
				Status:        enums.Submit,
				Config:        jsConfig,
				Retry:         1,
				RetryAttempts: conf.LocalConfig.ScheduleSettings.MaxTaskRetryCount,
				RetryType:     enums.RetryTypeFailedParts,
				CreatedAt:     time.Now(),
				UpdatedAt:     time.Now(),
			}

			err = item.Create(tx)
			if err != nil {
				return fmt.Errorf("update SQL failed: %v", err)
			}

			resp.TaskIds = append(resp.TaskIds, item.TaskId)
			logger.Log.Infof(utils.MMark(logCtx)+"add task: %s with language: %s", item.TaskId, lang)
		}
		return nil
	})

	if err != nil {
		resp.TaskIds = []string{}

		c.JSON(http.StatusOK, proto.NewCommonResponse(100003, "server internal error", resp))
		logger.Log.Errorf(utils.MMark(logCtx)+"HandleSubmitTask fail: %v", err)
		return
	}

	c.JSON(http.StatusOK, proto.NewCommonResponse(0, "success", resp))
	logger.Log.Infof(utils.MMark(logCtx) + "HandleSubmitTask success")
	return
}
