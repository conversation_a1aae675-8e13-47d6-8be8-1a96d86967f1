package controller

import (
	"acg-ai-go-common/logger"
	"acg-ai-go-common/utils"
	"context"
	"digital-human-micro-video-trans/beans/proto"
	"github.com/gin-gonic/gin"
	"net/http"
)

// HandleVideoPipelineCallback 提交任务
func (p TaskHandler) HandleVideoPipelineCallback(c *gin.Context) {
	reqId := utils.RandStringRunes(16)
	logCtx := context.WithValue(context.TODO(), utils.CtxKeyLogID, reqId)

	c.JSON(http.StatusOK, proto.NewCommonResponse(0, "success", nil))
	logger.Log.Infof(utils.MMark(logCtx) + "HandleVideoPipelineCallback success")
	return
}
