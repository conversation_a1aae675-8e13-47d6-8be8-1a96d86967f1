package controller

import (
	"acg-ai-go-common/gomysql"
	"acg-ai-go-common/logger"
	"acg-ai-go-common/utils"
	"context"
	"digital-human-micro-video-trans/beans/enums"
	"digital-human-micro-video-trans/beans/model"
	"digital-human-micro-video-trans/beans/proto"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	"net/http"
)

// HandleAcgQueryTask 查询任务
func (p TaskHandler) HandleAcgQueryTask(c *gin.Context) {
	reqID := utils.RandStringRunes(16)
	logCtx := context.WithValue(context.TODO(), utils.CtxKeyLogID, reqID)

	resp := proto.QueryResponseData{
		PageNo:     1,
		PageSize:   0,
		TotalCount: 0,
		Result:     []proto.VideoTaskItem{},
	}

	req := proto.TaskQueryRequest{}
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"HandleAcgQueryTask BindJSON fail, err:%v", err)
		c.JSON(http.StatusOK, proto.NewCommonResponse(100001, "params invalid", resp))
		return
	}

	err := gomysql.DB.Transaction(func(tx *gorm.DB) error {
		list, total, err := (&model.VideoTranslateTask{}).FilterTasks(tx, req.UserId, req.Ids, req.PageNo,
			req.PageSize)
		if err != nil {
			return err
		}

		if total > 0 {
			resp.TotalCount = int(total)
		}

		resp.PageNo = req.PageNo
		resp.PageSize = req.PageSize

		for _, item := range list {
			task := proto.VideoTaskItem{
				TaskId:           item.TaskId,
				Name:             item.Name,
				InputUrl:         item.InputUrl,
				FigureVideoUrl:   item.VideoUrl,
				CaptionUrl:       item.CaptionUrl,
				VideoUrl:         item.DownloadUrl,
				Duration:         item.Duration,
				Thumbnail:        item.Thumbnail,
				ResolutionWidth:  item.ResolutionWidth,
				ResolutionHeight: item.ResolutionHeight,
				CreatedAt:        item.CreatedAt,
				UpdatedAt:        item.UpdatedAt,
			}

			if item.Status == enums.Failed && item.Retry == 0 {
				task.Status = string(enums.Failed)
				task.Message = item.Message
			} else if item.Status == enums.Submit {
				task.Status = string(enums.Submit)
			} else if item.Status == enums.Success {
				task.Status = string(enums.Success)
			} else {
				task.Status = "RUNNING"
			}

			resp.Result = append(resp.Result, task)
		}
		return nil
	})

	if err != nil {
		c.JSON(http.StatusOK, proto.NewCommonResponse(100003, "server internal error", resp))
		logger.Log.Errorf(utils.MMark(logCtx)+"HandleAcgQueryTask fail: %v", err)
		return
	}

	c.JSON(http.StatusOK, proto.NewCommonResponse(0, "success", resp))
	logger.Log.Infof(utils.MMark(logCtx)+"HandleAcgQueryTask success: %+v", resp)
	return
}
