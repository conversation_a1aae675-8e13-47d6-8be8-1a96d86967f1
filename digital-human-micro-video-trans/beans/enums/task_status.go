package enums

type TaskStatus string
type TaskSubStatus string

const (
	Submit        TaskStatus = "SUBMIT"
	PreProcessing TaskStatus = "PRE_PROCESS"
	VoiceClone    TaskStatus = "VOICE_CLONE"
	VideoTransfer TaskStatus = "VIDEO_TRANSLATE"
	PostProcess   TaskStatus = "POST_PROCESS"
	Success       TaskStatus = "SUCCESS"
	Failed        TaskStatus = "FAILED"
)

const (
	None TaskSubStatus = ""

	Isolation      TaskSubStatus = "ISOLATION"
	IsolationWait  TaskSubStatus = "ISOLATION_WAIT"
	ASR            TaskSubStatus = "ASR"
	ASRIntegration TaskSubStatus = "ASR_INTEGRATION"
	Translate      TaskSubStatus = "TRANSLATE"

	VoiceCloneSubmit      TaskSubStatus = "VOICE_CLONE_SUBMIT"
	VoiceCloneWait        TaskSubStatus = "VOICE_CLONE_WAIT"
	VoiceCloneTts         TaskSubStatus = "VOICE_CLONE_TTS"
	VoiceCloneIntegration TaskSubStatus = "VOICE_CLONE_INTEGRATION"
	VoiceCloneSubtitle    TaskSubStatus = "VOICE_CLONE_SUBTITLE"

	VideoFigureTrainUpload TaskSubStatus = "VIDEO_FIGURE_TRAIN_UPLOAD"
	VideoFigureTrainSubmit TaskSubStatus = "VIDEO_FIGURE_TRAIN_SUBMIT"
	VideoFigureTrainWait   TaskSubStatus = "VIDEO_FIGURE_TRAIN_WAIT"
	VideoTransferSubmit    TaskSubStatus = "VIDEO_TRANSFER_SUBMIT"
	VideoTransferWait      TaskSubStatus = "VIDEO_TRANSFER_WAIT"

	PostProcessWait TaskSubStatus = "POST_PROCESS_WAIT"

	FINISH TaskSubStatus = "FINISH"
)
