package model

import (
	"digital-human-micro-video-trans/beans/enums"
	"digital-human-micro-video-trans/thirdparty/sutils/jsonutils"
	"gorm.io/gorm"
	"time"
)

type VideoTranslateTask struct {
	ID           int64  `json:"id" gorm:"primarykey"`
	TaskId       string `json:"taskId" gorm:"column:taskId;not null;type:varchar(50);index"`
	Name         string `json:"name" gorm:"column:name;type:varchar(128)"`
	UserID       string `json:"userId" gorm:"column:userId;type:varchar(100);index"`
	LastUpdateBy string `json:"lastUpdateBy" gorm:"column:lastUpdateBy;type:varchar(100)"`

	InputUrl         string            `json:"inputUrl" gorm:"column:inputUrl;type:varchar(1024)"`
	VideoUrl         string            `json:"videoUrl" gorm:"column:videoUrl;type:varchar(1024)"`
	CaptionUrl       string            `json:"captionUrl" gorm:"column:captionUrl;type:varchar(1024)"`
	DownloadUrl      string            `json:"downloadUrl" gorm:"column:downloadUrl;type:varchar(1024)"`
	Thumbnail        string            `json:"thumbnail" gorm:"column:thumbnail;type:varchar(512)"`
	Config           jsonutils.JSONMap `json:"config" gorm:"column:config;type:text"`
	CallbackUrl      string            `json:"callbackUrl" gorm:"column:callbackUrl;type:varchar(1024)"`
	ResolutionWidth  int               `json:"resolutionWidth" gorm:"column:resolutionWidth;type:int"`
	ResolutionHeight int               `json:"resolutionHeight" gorm:"column:resolutionHeight;type:int"`
	Duration         int               `json:"duration" gorm:"column:duration;type:int"`

	Status    enums.TaskStatus    `json:"status" gorm:"column:status;type:varchar(64);index"`
	SubStatus enums.TaskSubStatus `json:"subStatus" gorm:"column:subStatus;type:varchar(64);index"`
	Message   string              `json:"message" gorm:"column:message;type:text"`
	Progress  jsonutils.JSONMap   `json:"progress" gorm:"column:progress;type:text"` // 处理过程的任务记录

	VoiceDeleted  int             `json:"voiceDeleted" gorm:"column:voiceDeleted;type:int"`   // 音色是否已经删除
	Retry         int             `json:"retry" gorm:"column:retry;type:int"`                 // 是否可以重试
	RetryAttempts int             `json:"retryAttempts" gorm:"column:retryAttempts;type:int"` // 重试的机会，递减
	RetryType     enums.RetryType `json:"retryType" gorm:"column:retryType;type:int"`         // 重试的模式

	StartTime *time.Time `json:"startTime" gorm:"column:startTime"` // 开始时间
	EndTime   *time.Time `json:"endTime" gorm:"column:endTime"`     // 结束时间
	CostTime  int        `json:"costTime" gorm:"column:costTime"`   // 花费时间

	CreatedAt time.Time      `json:"createdAt" gorm:"column:createdAt"` // 创建时间
	UpdatedAt time.Time      `json:"updatedAt" gorm:"column:updatedAt"` // 更新时间
	DeletedAt gorm.DeletedAt `json:"-" gorm:"column:deletedAt;index"`   // 删除时间/标记删除
}

func (p *VideoTranslateTask) TableName() string {
	return "micro_video_translate_task"
}

func (p *VideoTranslateTask) Create(conn *gorm.DB) error {
	err := conn.Create(p).Error
	return err
}

func (p *VideoTranslateTask) Update(conn *gorm.DB) error {
	err := conn.Save(p).Error
	return err
}

func (p *VideoTranslateTask) GetTasksWithTaskId(db *gorm.DB, taskId string) (*VideoTranslateTask, error) {
	var task VideoTranslateTask
	err := db.Where("taskId = ?", taskId).First(&task).Error
	if err != nil {
		return nil, err
	}
	return &task, nil
}

func (p *VideoTranslateTask) GetTasksWithStatus(db *gorm.DB, status enums.TaskStatus) ([]*VideoTranslateTask, error) {
	var tasks []*VideoTranslateTask
	err := db.Where("status = ?", string(status)).Order("createdAt ASC").Find(&tasks).Error
	if err != nil {
		return nil, err
	}
	return tasks, nil
}

func (p *VideoTranslateTask) GetTasksWithoutStatuses(db *gorm.DB, excludedStatuses []enums.TaskStatus) ([]*VideoTranslateTask, error) {
	var tasks []*VideoTranslateTask

	var excluded []string
	for _, s := range excludedStatuses {
		excluded = append(excluded, string(s))
	}

	err := db.
		Where("status NOT IN (?)", excluded).
		Order("createdAt ASC").
		Find(&tasks).Error

	if err != nil {
		return nil, err
	}
	return tasks, nil
}

func (p *VideoTranslateTask) GetTasksWithSubStatus(db *gorm.DB, status enums.TaskSubStatus) ([]*VideoTranslateTask,
	error) {
	var tasks []*VideoTranslateTask
	err := db.Where("subStatus = ?", string(status)).Order("createdAt ASC").Find(&tasks).Error
	if err != nil {
		return nil, err
	}
	return tasks, nil
}

func (p *VideoTranslateTask) GetTasksWithAllStatus(db *gorm.DB, status enums.TaskStatus, subStatus enums.
	TaskSubStatus) ([]*VideoTranslateTask,
	error) {
	var tasks []*VideoTranslateTask
	err := db.Where("status = ? AND subStatus = ?", string(status), string(subStatus)).Order("createdAt ASC").Find(&tasks).Error
	if err != nil {
		return nil, err
	}
	return tasks, nil
}

func (p *VideoTranslateTask) GetTasksWithStatusAndRetry(db *gorm.DB,
	status enums.TaskStatus) ([]*VideoTranslateTask, error) {
	var tasks []*VideoTranslateTask
	err := db.Where("status = ? AND retry = ?", string(status), 1).Order("createdAt ASC").Find(&tasks).Error
	if err != nil {
		return nil, err
	}
	return tasks, nil
}

func (p *VideoTranslateTask) GetTasksWithStatusAndVoice(db *gorm.DB,
	status enums.TaskStatus, deleted int) ([]*VideoTranslateTask, error) {
	var tasks []*VideoTranslateTask
	err := db.Where("status = ? AND voiceDeleted = ?", string(status), deleted).Order("createdAt ASC").Find(&tasks).Error
	if err != nil {
		return nil, err
	}
	return tasks, nil
}

func (p *VideoTranslateTask) FilterTasks(
	db *gorm.DB,
	userId string,
	taskIds []string,
	pageNo int,
	pageSize int,
) (tasks []*VideoTranslateTask, total int64, err error) {
	tasks = []*VideoTranslateTask{}

	if len(userId) == 0 && len(taskIds) == 0 {
		return tasks, 0, nil // 无条件，返回空结果
	}

	query := db.Model(&VideoTranslateTask{})

	if userId != "" && len(taskIds) > 0 {
		query = query.Where("userId = ? AND taskId IN ?", userId, taskIds)
	} else if userId != "" {
		query = query.Where("userId = ?", userId)
	} else if len(taskIds) > 0 {
		query = query.Where("taskId IN ?", taskIds)
	}

	// 获取总数
	err = query.Count(&total).Error
	if err != nil {
		return nil, 0, err
	}

	// 分页参数保护
	if pageNo < 1 {
		pageNo = 1
	}
	if pageSize <= 0 {
		pageSize = 10
	}

	// 查询当前页数据
	offset := (pageNo - 1) * pageSize
	err = query.Order("createdAt DESC").
		Offset(offset).
		Limit(pageSize).
		Find(&tasks).Error
	if err != nil {
		return nil, 0, err
	}

	return tasks, total, nil
}

func (p *VideoTranslateTask) DeleteById(db *gorm.DB, id int64) error {
	result := db.Delete(&VideoTranslateTask{}, id)
	if result.Error != nil {
		return result.Error
	}
	return nil
}

func (p *VideoTranslateTask) DeleteByTaskId(db *gorm.DB, taskId string) error {
	result := db.Where("taskId = ?", taskId).Delete(&VideoTranslateTask{})
	if result.Error != nil {
		return result.Error
	}
	return nil
}
