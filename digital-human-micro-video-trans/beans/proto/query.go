package proto

import (
	"time"
)

type TaskQueryRequest struct {
	UserId   string   `json:"userId"`
	Ids      []string `json:"ids"`
	PageNo   int      `json:"pageNo" binding:"required"`
	PageSize int      `json:"pageSize" binding:"required"`
}

type VideoTaskItem struct {
	TaskId string `json:"taskId"`
	Name   string `json:"name"`

	InputUrl         string `json:"inputUrl" `
	FigureVideoUrl   string `json:"figureVideoUrl"`
	VideoUrl         string `json:"videoUrl"`
	CaptionUrl       string `json:"captionUrl"`
	Thumbnail        string `json:"thumbnail"`
	Duration         int    `json:"duration"`
	ResolutionWidth  int    `json:"resolutionWidth"`
	ResolutionHeight int    `json:"resolutionHeight"`

	Status  string `json:"status"`
	Message string `json:"message"`

	CreatedAt time.Time `json:"createdAt"`
	UpdatedAt time.Time `json:"updatedAt"`
}

type QueryResponseData struct {
	PageNo     int             `json:"pageNo"`
	PageSize   int             `json:"pageSize"`
	TotalCount int             `json:"totalCount"`
	Result     []VideoTaskItem `json:"result"`
}
