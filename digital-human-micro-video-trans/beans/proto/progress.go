package proto

import (
	"digital-human-micro-video-trans/beans/enums"
	"time"
)

func NewProgress() *Progress {
	return &Progress{}
}

type Progress struct {
	Isolation     IsolationDetail     `json:"isolation,omitempty"`
	Asr           AsrDetail           `json:"asr,omitempty"`
	Translate     TranslateDetail     `json:"translate,omitempty"`
	Voice         []VoiceCloneDetail  `json:"voice,omitempty"`
	VideoTransfer VideoTransferDetail `json:"videoTransfer,omitempty"`
}

type IsolationDetail struct {
	InputUrl  string `json:"inputUrl,omitempty"`
	JobId     string `json:"jobId,omitempty"`
	JobVocal  string `json:"-"`
	JobBgm    string `json:"-"`
	JobEffect string `json:"-"`
	Vocal     string `json:"vocal,omitempty"`
	Bgm       string `json:"bgm,omitempty"`
	Effects   string `json:"effects,omitempty"`

	Status  enums.ProgressStatus `json:"status,omitempty"`
	Message string               `json:"message,omitempty"`

	CreateAt time.Time `json:"createAt,omitempty"`
	UpdateAt time.Time `json:"updateAt,omitempty"`
	Duration int       `json:"duration"`
}

type AsrDetail struct {
	AsrResultUrl   string    `json:"asrResultUrl,omitempty"`
	AsrSentenceUrl string    `json:"asrSentenceUrl,omitempty"`
	NumSpeaker     int       `json:"numSpeaker,omitempty"`
	MaxSpeaker     []string  `json:"maxSpeaker,omitempty"`
	CreateAt       time.Time `json:"createAt,omitempty"`
	UpdateAt       time.Time `json:"updateAt,omitempty"`
	Duration       int       `json:"duration"`
}

type TranslateDetail struct {
	ResultUrl string    `json:"resultUrl,omitempty"`
	CreateAt  time.Time `json:"createAt,omitempty"`
	UpdateAt  time.Time `json:"updateAt,omitempty"`
	Duration  int       `json:"duration"`
}

type VoiceCloneDetail struct {
	Sample   string               `json:"sample"`
	Engine   string               `json:"engine"`
	FileId   string               `json:"fileId"`
	VoiceId  string               `json:"voiceId"`
	VoiceUrl string               `json:"voiceUrl"`
	Status   enums.ProgressStatus `json:"status"`
	Deleted  bool                 `json:"deleted"`
	CreateAt time.Time            `json:"createAt,omitempty"`
	UpdateAt time.Time            `json:"updateAt,omitempty"`
	Duration int                  `json:"duration"`
}

type FigureTrainDetail struct {
	TemplateUrl string               `json:"templateUrl"`
	FigureId    int                  `json:"figureId"`
	Name        string               `json:"name"`
	FigureName  string               `json:"figureName"` // 这个字段是人像包的名称，合成是需要的
	Status      enums.ProgressStatus `json:"status"`
	Message     string               `json:"message,omitempty"`

	ResourceLabel string `json:"resourceLabel,omitempty"`
	SceneLabel    string `json:"sceneLabel,omitempty"`
	CallbackUrl   string `json:"callbackUrl,omitempty"`

	CreateAt time.Time `json:"createAt,omitempty"`
	UpdateAt time.Time `json:"updateAt,omitempty"`
	Duration int       `json:"duration"`

	Skip int `json:"skip"`
}

type FigureVideoDetail struct {
	VideoId    string `json:"videoId"`
	FigureName string `json:"figureName"`
	Name       string `json:"name"`

	Status  enums.ProgressStatus `json:"status"`
	Message string               `json:"message,omitempty"`

	Transparent bool   `json:"transparent"`
	AudioUrl    string `json:"audioUrl"`
	VideoUrl    string `json:"videoUrl"`
	CaptionUrl  string `json:"captionUrl"`

	CallbackUrl string `json:"callbackUrl,omitempty"`

	CreateAt time.Time `json:"createAt,omitempty"`
	UpdateAt time.Time `json:"updateAt,omitempty"`
	Duration int       `json:"duration"`
}

type VideoTransferDetail struct {
	Figure FigureTrainDetail `json:"figure"`
	Video  FigureVideoDetail `json:"video"`

	CaptionUrl string `json:"captionUrl"`
	TtsTextUrl string `json:"ttsTextUrl"`

	CreateAt time.Time `json:"createAt,omitempty"`
	UpdateAt time.Time `json:"updateAt,omitempty"`
	Duration int       `json:"duration"`
}
