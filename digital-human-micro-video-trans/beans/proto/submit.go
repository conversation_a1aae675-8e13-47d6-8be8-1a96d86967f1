package proto

type SubmitConfig struct {
	TargetLangs           []string `json:"targetLangs"`
	SpeakerNum            int      `json:"speakerNum"`
	TranslateAudioOnly    bool     `json:"translateAudioOnly"`    // 是否仅翻译音频
	EnableDynamicDuration bool     `json:"enableDynamicDuration"` // 是否动态调整音频时长
	RemoveBackgroundAudio bool     `json:"removeBackgroundAudio"`
	EnableCaptions        bool     `json:"enableCaptions"`
}

type User struct {
	AccountId   string `json:"accountId"`
	UserId      string `json:"userId"`
	UserName    string `json:"userName"`
	UserChannel string `json:"userChannel"`
}

type SubmitTaskRequest struct {
	FileName    string `json:"fileName"`
	VideoUrl    string `json:"videoUrl"`
	CallbackUrl string `json:"callbackUrl"`
	SubmitConfig
	User
}

type SubmitTaskResponse struct {
	ReqId   string   `json:"reqId"`
	TaskIds []string `json:"taskIds"`
}

type TranslateVideoListRequest struct {
	AccountId string `json:"accountId" binding:"required"`
	Name      string `json:"name"`
	PageNo    int    `json:"pageNo"`
	PageSize  int    `json:"pageSize"`
}

type TranslateVideoListResponse struct {
	PageNo     int                      `json:"pageNo"`
	PageSize   int                      `json:"pageSize"`
	TotalCount int                      `json:"totalCount"`
	Result     []TranslateVideoListData `json:"result"`
}

type TranslateVideoListData struct {
	ID           int64  `json:"id" gorm:"primaryKey;autoIncrement;column:id"`
	VideoID      string `json:"videoId" gorm:"column:video_id;type:varchar(64);comment:视频id"`
	Name         string `json:"name" gorm:"column:name;type:varchar(100);comment:视频名称"`
	UserID       string `json:"userID" gorm:"column:user_id;type:varchar(100);index;comment:userId"`
	LastUpdateBy string `json:"lastUpdateBy" gorm:"column:last_update_by;type:varchar(100)"`
	VideoUrl     string `json:"videoUrl" gorm:"column:video_url;type:varchar(512);comment:视频url"`
	Thumbnail    string `json:"thumbnail" gorm:"column:thumbnail;type:varchar(512);comment:缩略图url地址"`
	Duration     int64  `json:"duration" gorm:"column:duration;comment:视频时长,单位毫秒"`
	Status       string `json:"status" gorm:"column:status;type:varchar(100);index"` // 视频状态，对用户展示
	Language     string `json:"language"`
	Width        int    `json:"width"`
	Height       int    `json:"height"`
	FailedReason string `json:"failedReason"`
	CreateTime   string `json:"createTime"`
	UpdateTime   string `json:"updateTime"`
}

type TranslateDelRequest struct {
	AccountId string   `json:"accountId" binding:"required"`
	Ids       []string `json:"ids" binding:"required"`
}
