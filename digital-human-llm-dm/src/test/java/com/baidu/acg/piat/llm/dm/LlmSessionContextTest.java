package com.baidu.acg.piat.llm.dm;

import java.util.Set;
import java.util.stream.Collectors;

import javax.annotation.PostConstruct;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.autoconfigure.orm.jpa.DataJpaTest;

import com.baidu.acg.piat.llm.dm.constants.LLMConstants;
import com.baidu.acg.piat.llm.dm.model.Query;
import com.baidu.acg.piat.llm.dm.model.SessionContext;

import lombok.extern.slf4j.Slf4j;

/**
 * Created on 2021/4/13 下午5:58.
 *
 * <AUTHOR>
 */
@DataJpaTest
@Slf4j
public class LlmSessionContextTest {
    @Test
    public void testProduceAndConsumeMsg() {
        String llmSplit = ". ,， 。 ； ！ ! ? ？";
        Set<Character> llmSplitEndSet = llmSplit.chars().mapToObj(c -> (char) c).collect(Collectors.toSet());
        int minSplitSentenceLen = 15;
        SessionContext sessionContext = new SessionContext();
        Query query = new Query();
        query.setRequestId("1");

        String msg = sessionContext.produceAndTryConsumeResponse(query, "FAQ", "你好哈<speak>",
                llmSplitEndSet, minSplitSentenceLen);
        Assertions.assertEquals("你好哈<speak>", msg);

        msg = sessionContext.produceAndTryConsumeResponse(query, LLMConstants.SOURCE_LLM, "你好哈",
                llmSplitEndSet, minSplitSentenceLen);
        Assertions.assertEquals(null, msg);

        msg = sessionContext.produceAndTryConsumeResponse(query, LLMConstants.SOURCE_LLM, ",我很开心的是不是",
                llmSplitEndSet, minSplitSentenceLen);
        Assertions.assertEquals(null, msg);

        msg = sessionContext.produceAndTryConsumeResponse(query, LLMConstants.SOURCE_LLM,
                ",我管你开心不开心是是是是呜呜呜呜呜呜，我很开心的是不是",
                llmSplitEndSet, minSplitSentenceLen);
        Assertions.assertEquals("你好哈,我很开心的是不是,我管你开心不开心是是是是呜呜呜呜呜呜，", msg);

        msg = sessionContext.produceAndTryConsumeResponse(query, LLMConstants.SOURCE_LLM,
                "是你的哈哈哈哈哈哈哈哈哈哈哈哈哈就是煎熬。",
                llmSplitEndSet, minSplitSentenceLen);
        Assertions.assertEquals("我很开心的是不是是你的哈哈哈哈哈哈哈哈哈哈哈哈哈就是煎熬。", msg);

        msg = sessionContext.produceAndTryConsumeResponse(query, LLMConstants.SOURCE_LLM, "好的宝贝",
                llmSplitEndSet, minSplitSentenceLen);
        Assertions.assertEquals(null, msg);
        msg = sessionContext.consumeResponse(query, LLMConstants.SOURCE_LLM, true, llmSplitEndSet, minSplitSentenceLen);
        Assertions.assertEquals("好的宝贝", msg);
        sessionContext.produceAndTryConsumeResponse(query, LLMConstants.SOURCE_LLM, "海淀公园位于北京西北,",
                llmSplitEndSet, minSplitSentenceLen);
        sessionContext.produceAndTryConsumeResponse(query, LLMConstants.SOURCE_LLM, "四环万泉河立交桥的西",
                llmSplitEndSet, minSplitSentenceLen);

    }
}
