package com.baidu.acg.piat.llm.dm;

import java.util.Date;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.orm.jpa.DataJpaTest;

import com.baidu.acg.piat.llm.dm.dao.LlmDmConfigRepository;
import com.baidu.acg.piat.llm.dm.model.db.LlmDmConfigModel;

import lombok.extern.slf4j.Slf4j;

/**
 * Created on 2021/4/13 下午5:58.
 *
 * <AUTHOR>
 */
@DataJpaTest
@Slf4j
// @AutoConfigureTestDatabase(replace = AutoConfigureTestDatabase.Replace.NONE) 使用mysql
public class LlmDmConfigRepositoryTest {

    @Autowired
    private LlmDmConfigRepository llmDmConfigRepository;


    @Test
    public void testAddAndFind() {
        LlmDmConfigModel llmDmConfigModel = LlmDmConfigModel.builder()
                .name("name")
                .token("token")
                .createTime(new Date())
                .updateTime(new Date())
                .build();
        llmDmConfigRepository.save(llmDmConfigModel);
        LlmDmConfigModel llmDmConfigModel2 = llmDmConfigRepository.findByToken("token").get();
        Assertions.assertEquals("token", llmDmConfigModel2.getToken());
    }
}
