package com.baidu.acg.piat.llm.dm;

import java.io.IOException;
import java.util.Set;
import java.util.stream.Collectors;

import com.baidu.acg.piat.digitalhuman.common.llmdm.ToolConfig;
import com.baidu.acg.piat.digitalhuman.common.llmrole.LLMConfig;
import com.baidu.acg.piat.llm.dm.model.faq.FaqResponse;
import com.baidu.acg.piat.llm.dm.utils.DrmlUtil;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.autoconfigure.orm.jpa.DataJpaTest;

import com.baidu.acg.piat.digitalhuman.common.utils.JsonUtil;
import com.baidu.acg.piat.llm.dm.constants.LLMConstants;
import com.baidu.acg.piat.llm.dm.model.Query;
import com.baidu.acg.piat.llm.dm.model.SessionContext;
import com.baidu.acg.piat.llm.dm.model.keyue.ChatBotResultDto;
import com.baidu.acg.piat.llm.dm.model.keyue.KeYueChunk;

import lombok.extern.slf4j.Slf4j;

/**
 * Created on 2021/4/13 下午5:58.
 *
 * <AUTHOR>
 */
@Slf4j
public class KeYueTest {
    @Test
    public void keYue() {
        String str = "{\n"
                + "    \"sessionId\": \"dc8015a7-34f5-4155-ae23-3d7146a9250f\",\n"
                + "    \"queryId\": \"f606842f-583f-4567-a68b-cf87810dec81\",\n"
                + "    \"replyStatus\": 200,\n"
                + "    \"answer\": [\n"
                + "        {\n"
                + "            \"chunkId\": 0,\n"
                + "            \"status\": \"running\",\n"
                + "            \"topicId\": \"\",\n"
                + "            \"blockId\": \"\",\n"
                + "            \"nodeId\": \"\",\n"
                + "            \"reply\": {\n"
                + "                \"type\": 1,\n"
                + "                \"text\": \"1]^。\",\n"
                + "                \"textList\": null,\n"
                + "                \"clarifyGuide\": null,\n"
                + "                \"replySource\": \"DOC_QA\",\n"
                + "                \"showDocumentSource\": true,\n"
                + "                \"documents\": [\n"
                + "                    {\n"
                + "                        \"document_id\": \"23b56464-5580-4262-b7cf-9c16c5cbc82a\",\n"
                + "                        \"document_name\": \"免申即享-申请国家安全应急产业示范基地补助指南.docx\",\n"
                + "                        \"data\": [\n"
                + "                            {\n"
                + "                                \"segment_id\": \"63334c68-cb27-4450-ae81-f7d27a69ad88\",\n"
                + "                                \"segment_content\": \"（一）《广州市黄埔区广州开发区广州高新区支持智能应急救援产业发展办法》（穗埔应急规字〔2021〕8号，以下简称《办法》）（二）《广州市黄埔区广州开发区广州高新区支持智能应急救援产业发展办法实施细则》（穗埔应急规字〔2023〕1号，以下简称《细则》）在黄埔区、广州开发区及其受托管理和下辖园区范围内从事生产经营活动，有健全财务制度、具有>独立法人资格、实行独立核算且在《办法》有效期限内被认定为国家、省级安全应急产业示范基地的产业园。说明：（一）“国家安全应急产业示范基地”是指由工业和信息化部、国家发展改革委、科学技术部联合命名的国家安全应急产业综合类（或专业类）示范基地，详见《国家安全应急产业示范基地管理办法（试行）》（工信部联安全〔2021〕48号）。&nbsp; （二）“省级安全应急产业示范基地”是指由省级工信、科技或应急部门认定的示范基地。被认定为国家、省级安全应>急产业示范基地的产业园，分别给予运营管理单位200万元、100万元一次性补助。广州开发区政策研究室“政策兑现”窗口联系电话：82114062 广州市黄埔区应急管理局联系电话：82113385,82378364 该事项属于免申即享事项，企业无需主动>申请，无需提供申请材料，只需按以下流程确认资金申领意愿即可。\",\n"
                + "                                \"sentence_id\": null,\n"
                + "                                \"sentence_content\": null,\n"
                + "                                \"score\": \"1.0\",\n"
                + "                                \"segment_coord\": \"[{\\\"pageno\\\": 0, \\\"box\\\": [117, 88, 360, 22]}, {\\\"pageno\\\": 0, \\\"box\\\": [255, 113, 84, 15]}, {\\\"pageno\\\": 0, \\\"box\\\": [255, 222, 84, 15]}, {\\\"pageno\\\": 0, \\\"box\\\": [255, 404, 84, 15]}, {\\\"pageno\\\": 0, \\\"box\\\": [206, 458, 182, 15]}, {\\\"pageno\\\": 0, \\\"box\\\": [206, 495, 182, 15]}, {\\\"pageno\\\": 0, \\\"box\\\": [255, 531, 84, 15]}, {\\\"pageno\\\": 0, \\\"box\\\": [90, 132, 409, 87]}, {\\\"pageno\\\": 0, \\\"box\\\": [90, 241, 409, 160]}, {\\\"pageno\\\": 0, \\\"box\\\": [90, 422, 402, 33]}, {\\\"pageno\\\": 0, \\\"box\\\": [114, 477, 382, 15]}, {\\\"pageno\\\": 0, \\\"box\\\": [114, 513, 363, 16]}, {\\\"pageno\\\": 0, \\\"box\\\": [90, 549, 402, 33]}]\",\n"
                + "                                \"data\": [\n"
                + "                                    {\n"
                + "                                        \"sentence_id\": \"3\",\n"
                + "                                        \"sentence_content\": \"广州开发区政策研究室“政策兑现”窗口联系电话：82114062 广州市>黄埔区应急管理局联系电话：82113385,82378364 该事项属于免申即享事项，企业无需主动申请，无需提供申请材料，只需按以下流程确认资金申领意愿即可。\",\n"
                + "                                        \"score\": \"1.0\"\n"
                + "                                    }\n"
                + "                                ]\n"
                + "                            }\n"
                + "                        ]\n"
                + "                    }\n"
                + "                ],\n"
                + "                \"faqSearch\": null\n"
                + "            }\n"
                + "        }\n"
                + "    ],\n"
                + "    \"variables\": {\n"
                + "        \"date\": \"2024-03-25\",\n"
                + "        \"last_intent\": \"\",\n"
                + "        \"week\": \"1\",\n"
                + "        \"last_task_prompt\": \"你好，我是您的智能助手，请问有什么可以帮您？\",\n"
                + "        \"time\": \"11:12:43\",\n"
                + "        \"last_response\": \"你好，我是您的智能助手，请问有什么可以帮您？\",\n"
                + "        \"last_user_response\": \"黄浦区应急管理局联系电话\",\n"
                + "        \"LLM_rewrite_query\": \"黄浦区应急管理局联系电话\"\n"
                + "    },\n"
                + "    \"endTime\": null,\n"
                + "    \"recommendList\": [\n"
                + "\n"
                + "    ],\n"
                + "    \"rewriteQuery\": \"黄浦区应急管理局联系电话\",\n"
                + "    \"intent\": null\n"
                + "}";
        try {
            ChatBotResultDto chatBotResultDto = JsonUtil.readValue(str, ChatBotResultDto.class);
            log.info(chatBotResultDto.getAnswer().get(0).getReply().getText());
            Assertions.assertEquals("f606842f-583f-4567-a68b-cf87810dec81", chatBotResultDto.getQueryId());
            log.info(chatBotResultDto.getSessionId());
        } catch (Exception e) {
            log.info("error:{}", e);
            System.out.println(e.getStackTrace().toString());
        }

    }

    @Test
    void testJson() throws IOException {
        String json = "    {\n" +
                "        \"toolType\": \"FAQ_INSERT\",\n" +
                "        \"url\": \"http://ecloud-suctom-faq:8080/custom/faq/save\",\n" +
                "        \"minScore\": 0.8,\n" +
                "        \"params\": {\n" +
                "            \"appId\":\"digital-human-talk\",\n" +
                "            \"openEmbedding\":true\n" +
                "        }\n" +
                "    }";
        ToolConfig toolConfig = JsonUtil.readValue(json, ToolConfig.class);
        System.out.println(toolConfig);

        String json2 = "   {\n" +
                "      \"toolType\": \"FAQ\",\n" +
                "      \"url\": \"http://ecloud-suctom-faq:8080/custom/faq/search\",\n" +
                "      \"minScore\": 0.8,\n" +
                "      \"params\": {\n" +
                "        \"appId\":\"digital-human-talk\",\n" +
                "        \"query\":\"今天天气怎么样\",\n" +
                "        \"hitThreshold\":80,\n" +
                "        \"clarificationThreshold\":80,\n" +
                "        \"openEmbedding\":true\n" +
                "      }\n" +
                "    }";
        toolConfig = JsonUtil.readValue(json2, ToolConfig.class);
        Assertions.assertEquals("FAQ", toolConfig.getToolType().name());

        String json3 = "{\n" +
                "  \"botType\": \"QIAN_FAN\",\n" +
                "  \"url\": \"https://aip.baidubce.com/rpc/2.0/ai_custom/v1/wenxinworkshop/chat/completions_pro\",\n" +
                "  \"timeout\": 20,\n" +
                "  \"interrupted\": false,\n" +
                "  \"interruptedBeforeQuery\": true,\n" +
                "  \"credential\": {\n" +
                "    \"ak\": \"sU43qT5j0CGPyFjHafENvEPq\",\n" +
                "    \"sk\": \"xQh8qEdOjOaQMdQ0yKhrbhaBX7D6X7It\"\n" +
                "  },\n" +
                "  \"preActions\": [\n" +
                "    {\n" +
                "      \"toolType\": \"FAQ\",\n" +
                "      \"url\": \"http://ecloud-suctom-faq:8080/custom/faq/search\",\n" +
                "      \"minScore\": 0.8,\n" +
                "      \"params\": {\n" +
                "        \"appId\":\"digital-human-talk\",\n" +
                "        \"query\":\"今天天气怎么样\",\n" +
                "        \"hitThreshold\":80,\n" +
                "        \"clarificationThreshold\":80,\n" +
                "        \"openEmbedding\":true\n" +
                "      }\n" +
                "    }\n" +
                "  ],\n" +
                "  \"postActions\": [\n" +
                "    {\n" +
                "        \"toolType\": \"FAQ_INSERT\",\n" +
                "        \"url\": \"http://ecloud-suctom-faq:8080/custom/faq/save\",\n" +
                "        \"minScore\": 0.8,\n" +
                "        \"params\": {\n" +
                "            \"appId\":\"digital-human-talk\",\n" +
                "            \"openEmbedding\":true\n" +
                "        }\n" +
                "    }\n" +
                "  ],\n" +
                "  \"recommendConfig\": {\n" +
                "    \"guide\": {\n" +
                "      \"text\": \"现在有⼀份百度云智⼤会专享福利请您查收\",\n" +
                "      \"linkUrl\": \"http://www.baidu.com?k1=v1&k2=v2\",\n" +
                "      \"imgUrl\": \"http://www.baidu.com/img.png\"\n" +
                "    },\n" +
                "    \"sentences\": [\"曦灵数字人可以用在哪些场景？\", \"曦灵文生3D数字人价格是多少？\", \"推荐3\"],\n" +
                "    \"num\": 2\n" +
                "  }\n" +
                "}";
        LLMConfig llmConfig = JsonUtil.readValue(json3, LLMConfig.class);
        System.out.println(llmConfig);

        LLMConfig.RecommendConfig recommendConfig = llmConfig.getRecommendConfig();
        System.out.println(DrmlUtil.buildClientWidget(recommendConfig));

        String str = "{\n" +
                "    \"code\": 0,\n" +
                "    \"msg\": \"success\",\n" +
                "    \"data\": {\n" +
                "        \"question_info\": [\n" +
                "            {\n" +
                "                \"appId\": \"digital-human-talk\",\n" +
                "                \"qid\": \"f9c2ef98-a5fc-4a07-a154-01da26a521b3\",\n" +
                "                \"firstClass\": \"\",\n" +
                "                \"secondClass\": \"\",\n" +
                "                \"general\": false,\n" +
                "                \"keywordGroups\": null,\n" +
                "                \"questions\": [\n" +
                "                    \"明天天气怎么样\"\n" +
                "                ],\n" +
                "                \"pinYin\": [\n" +
                "                    \"ming tian tian qi zen me yang\"\n" +
                "                ],\n" +
                "                \"answers\": [\n" +
                "                    {\n" +
                "                        \"type\": \"TEXT\",\n" +
                "                        \"tags\": null,\n" +
                "                        \"speak\": [\n" +
                "                            {\n" +
                "                                \"answer\": \"明天很好\"\n" +
                "                            }\n" +
                "                        ],\n" +
                "                        \"expression\": \"\",\n" +
                "                        \"kv\": null,\n" +
                "                        \"text\": {\n" +
                "                            \"text\": \"\",\n" +
                "                            \"url\": \"\"\n" +
                "                        },\n" +
                "                        \"videos\": null,\n" +
                "                        \"videosDisplayFullScreen\": false,\n" +
                "                        \"images\": null,\n" +
                "                        \"imagesDisplayFullScreen\": false,\n" +
                "                        \"htmlUrl\": \"\",\n" +
                "                        \"html5DisplayFullScreen\": false,\n" +
                "                        \"recordings\": {\n" +
                "                            \"audioUrl\": \"\"\n" +
                "                        },\n" +
                "                        \"action\": {\n" +
                "                            \"groupId\": \"\",\n" +
                "                            \"groupName\": \"\"\n" +
                "                        },\n" +
                "                        \"httpRequest\": {\n" +
                "                            \"url\": \"\",\n" +
                "                            \"httpMethod\": \"\",\n" +
                "                            \"body\": \"\"\n" +
                "                        },\n" +
                "                        \"notInterrupted\": false,\n" +
                "                        \"richText\": {\n" +
                "                            \"content\": \"\",\n" +
                "                            \"displayFullScreen\": false\n" +
                "                        }\n" +
                "                    }\n" +
                "                ],\n" +
                "                \"createTime\": \"2023-02-01 20:12:40\",\n" +
                "                \"logId\": \"\",\n" +
                "                \"openEmbedding\": true,\n" +
                "                \"callbackUrl\": \"\"\n" +
                "            }\n" +
                "        ],\n" +
                "        \"cosine_score\": 1,\n" +
                "        \"confidence\": 1\n" +
                "    }\n" +
                "}";
        FaqResponse faqResponse = JsonUtil.readValue(str, FaqResponse.class);
        System.out.println(faqResponse);
    }
}
