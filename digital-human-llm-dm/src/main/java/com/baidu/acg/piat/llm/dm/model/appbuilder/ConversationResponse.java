package com.baidu.acg.piat.llm.dm.model.appbuilder;

import com.fasterxml.jackson.annotation.JsonProperty;

public class ConversationResponse {
    private String requestId;

    @JsonProperty(value = "conversation_id")
    private String conversationId;

    public String getRequestId() {
        return requestId;
    }

    public void setRequestId(String requestId) {
        this.requestId = requestId;
    }

    public String getConversationId() {
        return conversationId;
    }

    public void setConversationId(String conversationId) {
        this.conversationId = conversationId;
    }

    @Override
    public String toString() {
        return "ConversationResponse{" +
                "requestId='" + requestId + '\'' +
                ", conversationId='" + conversationId + '\'' +
                '}';
    }
}
