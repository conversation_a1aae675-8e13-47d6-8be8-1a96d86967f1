// Copyright (C) 2019 Baidu Inc. All rights reserved.
package com.baidu.acg.piat.llm.dm.configure;

import lombok.Data;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import com.baidu.acg.piat.digitalhuman.plat.client.PlatformClient;

/**
 * AccessConfigure
 *
 * <AUTHOR>
 * @since 2019-11-28
 */
@Configuration
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class PlatConfigure {

    @Bean
    @ConfigurationProperties(prefix = "digitalhuman.plat.config")
    public Config platConfig() {
        return new Config();
    }

    @Bean
    public PlatformClient platformClient() {
        var config = platConfig();
        return new PlatformClient(config.getBaseUrl());
    }

    @Data
    public static class Config {

        private String baseUrl = "http://digital-human-plat:8080";

        private int pollIntervalMillis = 5000;

        private int notExistedTTL = 5;

        private int retryTimes = 3;

        private int retrySleepMillis = 100;

    }


}
