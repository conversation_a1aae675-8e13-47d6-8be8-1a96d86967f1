package com.baidu.acg.piat.llm.dm.service.impl;

import com.baidu.acg.piat.digitalhuman.common.access.AccessApp;
import com.baidu.acg.piat.digitalhuman.common.exception.DigitalHumanCommonException;
import com.baidu.acg.piat.digitalhuman.common.llmrole.KnowledgeBase;
import com.baidu.acg.piat.digitalhuman.common.llmrole.LlmRole;
import com.baidu.acg.piat.digitalhuman.common.model.PageResponse;
import com.baidu.acg.piat.digitalhuman.common.project.BotParams;
import com.baidu.acg.piat.digitalhuman.common.project.CharacterParams;
import com.baidu.acg.piat.digitalhuman.common.project.Credentials;
import com.baidu.acg.piat.digitalhuman.common.project.Project;
import com.baidu.acg.piat.digitalhuman.common.project.PrologueParams;
import com.baidu.acg.piat.digitalhuman.common.project.TtsParams;
import com.baidu.acg.piat.digitalhuman.common.quota.ResourceQuota;
import com.baidu.acg.piat.digitalhuman.common.utils.DateTimeUtil;
import com.baidu.acg.piat.digitalhuman.common.utils.IDGenerator;
import com.baidu.acg.piat.digitalhuman.plat.client.PlatformClient;
import com.baidu.acg.piat.llm.dm.configure.CommonConfig;
import com.baidu.acg.piat.llm.dm.constants.LLMConstants;
import com.baidu.acg.piat.llm.dm.constants.ModeType;
import com.baidu.acg.piat.llm.dm.constants.StatusType;
import com.baidu.acg.piat.llm.dm.dao.LlmRoleOnlineRepository;
import com.baidu.acg.piat.llm.dm.dao.LlmRoleRepository;
import com.baidu.acg.piat.llm.dm.dao.LlmRoleTemplateRepository;
import com.baidu.acg.piat.llm.dm.model.CustomizedConfig;
import com.baidu.acg.piat.llm.dm.model.LlmRoleModel;
import com.baidu.acg.piat.llm.dm.model.LlmRoleOnlineModel;
import com.baidu.acg.piat.llm.dm.model.LlmRoleTemplateModel;
import com.baidu.acg.piat.llm.dm.service.KnowledgeBaseService;
import com.baidu.acg.piat.llm.dm.service.LlmRoleService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;

import static com.baidu.acg.piat.digitalhuman.common.llmrole.Constant.LLM_ROLE_TYPE;
import static com.baidu.acg.piat.digitalhuman.common.llmrole.Constant.LLM_TEMPLATE_TYPE;

/**
 * <AUTHOR> Xiaoyu
 * @since 2023/11/23 14:17
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class LlmRoleServiceImpl implements LlmRoleService {

    private final KnowledgeBaseService knowledgeBaseService;

    private final LlmRoleRepository llmRoleRepository;

    private final LlmRoleOnlineRepository llmRoleOnlineRepository;

    private final LlmRoleTemplateRepository llmRoleTemplateRepository;

    private final PlatformClient platformClient;

    private final CommonConfig commonConfig;

    private IDGenerator idGenerator = new IDGenerator(new IDGenerator.IDGeneratorConfig());

    /**
     * 创建LLM角色
     *
     * @param role           角色信息，包含角色ID、角色名称等等
     * @param projectRequest 项目请求，包括项目名称、用户ID等信息
     * @param roleType       模板类型（LLM_TEMPLATE_TYPE：模版、OTHER_TYPE：普通）
     * @return 返回创建好的LLM角色信息
     */
    @Override
    public LlmRole createLlmRole(LlmRole role, Project projectRequest, int roleType) {
        log.debug("start create llm scene ,an its account id is{}, name is {}, project is {},uid={}",
                role.getAccountId(), role.getName(), projectRequest, role.getUid());
        if (StringUtils.isBlank(role.getName())) {
            throw new DigitalHumanCommonException("名称不能为空");
        }
        // 如果是创建模版，同步模板名称，非模版则忽略模版名称
        if (roleType == LLM_TEMPLATE_TYPE) {
            role.setAccountId("System");
            projectRequest.setUserId("System");
            role.setTemplateName(role.getName());
        }
        List<LlmRoleModel> exist = llmRoleRepository.findByAccountIdAndNameAndDel(role.getAccountId(), role.getName());
        if (exist.size() > 0) {
            throw new DigitalHumanCommonException("已存在同名交互");
        }
        String llmRoleId = idGenerator.generate("llm");
        // 每一个role对应的场景的botParams的token为roleId
        if (projectRequest.getBotParams() == null) {
            BotParams botParams = BotParams.builder()
                    .credentials(Credentials.builder().llmRoleId(llmRoleId).build()).build();
            projectRequest.setBotParams(botParams);
        } else if (projectRequest.getBotParams().getCredentials() == null) {
            projectRequest.getBotParams().setCredentials(Credentials.builder()
                    .llmRoleId(llmRoleId).build());
        } else if (projectRequest.getBotParams().getCredentials() != null) {
            projectRequest.getBotParams().getCredentials().setLlmRoleId(llmRoleId);
        }
        if (ModeType.ADVANCED.getId() == role.getModeType()) {
            projectRequest.getBotParams().setType(BotParams.BotTypeEnum.KE_YUE.getBotType());
        } else {
            projectRequest.getBotParams().setType(BotParams.BotTypeEnum.COPILOT.getBotType());
        }
        if (projectRequest.getPaintSubtitleOnPictureParams() != null) {
            projectRequest.getPaintSubtitleOnPictureParams().setSubtitleSplittable(false);
        }
        projectRequest.setType("SYSTEM");
        projectRequest.setName(role.getName());
        projectRequest.setPrologueParams(PrologueParams
                .builder()
                .showPrologue(true)
                .type(PrologueParams.PrologueType.query)
                .contents(LLMConstants.START_QUERY)
                .build());
        log.debug("start create project, and its request is {}", projectRequest);
        Project project = platformClient.createProject(projectRequest);
        log.debug("after create project {}", project);
        LlmRoleModel llmRoleModel = LlmRoleModel.builder()
                .llmRoleId(llmRoleId)
                .templateId(role.getTemplateId())
                .llmRoleTemplateId(role.getLlmRoleTemplateId())
                .screenType(role.getScreenType())
                .roleType(roleType)
                .name(role.getName())
                .accountId(role.getAccountId())
                .uid(role.getUid())
                .editor(role.getEditor())
                .templateName(role.getTemplateName())
                .bufferSentencesConfigJson(role.getBufferSentencesConfigJson())
                .skillList(role.getSkillList())
                .knowledge(role.getKnowledge())
                .openingStatement(role.getOpeningStatement())
                .roleDefine(role.getRoleDefine())
                .status(StatusType.OPNE.getStatus())
                .llmConfig(role.getLlmConfig())
                .templateIconUrl(role.getTemplateIconUrl())
                .characterConfigId(role.getCharacterConfigId())
                .projectId(project.getId()).build();
        LlmRoleModel save = llmRoleRepository.save(llmRoleModel);
        return save.toLlmRole();
    }

    /**
     * 根据模板创建LLM角色
     *
     * @param llmRoleTemplateId 模板ID
     * @return 新的LLM角色对象
     */
    @Override
    public LlmRole createLlmRoleByTemplate(String llmRoleTemplateId, LlmRole llmRole) {
        Optional<LlmRoleTemplateModel> llmRoleTemplateModelOptional =
                llmRoleTemplateRepository.findByLlmRoleTemplateId(llmRoleTemplateId);
        String llmRoleId = null;
        if (!llmRoleTemplateModelOptional.isPresent()) {
            throw new DigitalHumanCommonException("模板不存在");
        }
        LlmRoleTemplateModel llmRoleTemplateModel = llmRoleTemplateModelOptional.get();
        llmRoleId = llmRoleTemplateModel.getLlmRoleId();
        LlmRoleModel newLlmRoleModel = new LlmRoleModel();
        llmRoleRepository.findByLlmRoleId(llmRoleId).ifPresent(llmRoleModel -> {
            log.debug("raw role is {}", llmRoleModel);
            BeanUtils.copyProperties(llmRoleModel, newLlmRoleModel);
        });
        String name = llmRoleTemplateModel.getTemplateName() + DateTimeUtil.getNowTimeString();
        if (llmRole != null && StringUtils.isNotEmpty(llmRole.getName())) {
            // 前端
            name = llmRole.getName();
        }
        String newLlmRoleId = idGenerator.generate("llm");
        // copy project, 根据原始的project创建新的project
        Project project = platformClient.getProjectById(newLlmRoleModel.getProjectId());
        project.setName(name + UUID.randomUUID());
        project.setUid(llmRole.getUid());
        project.setId(null);
        if (llmRoleTemplateModel.getCustomizedConfig() != null
                && llmRoleTemplateModel.getCustomizedConfig().getX() != null) {
            CustomizedConfig customizedConfig = llmRoleTemplateModel.getCustomizedConfig();
            CharacterParams characterParams = new CharacterParams();
            CharacterParams.Offset offset = new CharacterParams.Offset();
            offset.setX(customizedConfig.getX());
            offset.setY(customizedConfig.getY());
            offset.setZ(customizedConfig.getZ());
            characterParams.setOffset(offset);
            project.setCharacterParams(characterParams);
        }
        project.getBotParams().getCredentials().setLlmRoleId(newLlmRoleId);
        log.debug("start create project, and its request is {}", project);
        project = platformClient.createProject(project);
        log.debug("after create project {}", project);
        newLlmRoleModel.setLlmRoleTemplateId(llmRoleTemplateId);
        newLlmRoleModel.setScreenType(llmRoleTemplateModel.getScreenType());
        newLlmRoleModel.setId(null);
        newLlmRoleModel.setUid(llmRole.getUid());
        newLlmRoleModel.setLlmRoleId(newLlmRoleId);
        newLlmRoleModel.setName(name);
        newLlmRoleModel.setAccountId(llmRole.getAccountId());
        newLlmRoleModel.setEditor(llmRole.getEditor());
        newLlmRoleModel.setProjectId(project.getId());
        newLlmRoleModel.setCharacterConfigId(project.getCharacterConfigId());
        LlmRoleModel save = llmRoleRepository.save(newLlmRoleModel);
        log.debug("create llm role {} by templateId {}", save, llmRoleTemplateId);
        return llmRoleRepository.save(save).toLlmRole();
    }

    /**
     * 查看LLM角色详情
     *
     * @param accountId 账号id
     * @param llmRoleId LLM角色id
     * @return 返回LLM角色对象
     */
    @Override
    public LlmRole detailLlmRole(String accountId, String llmRoleId) {
        Optional<LlmRoleModel> roleModel = llmRoleRepository.findByLlmRoleId(llmRoleId);
        if (roleModel.isEmpty()) {
            throw new DigitalHumanCommonException("该角色不存在");
        }
        if (!accountId.equals(roleModel.get().getAccountId())) {
            throw new DigitalHumanCommonException("该角色不属于当前账户");
        }
        boolean needUpdate = false;
        if (roleModel.get().getKnowledge() != null) {
            List<KnowledgeBase> personal = roleModel.get().getKnowledge().getPersonal();
            List<KnowledgeBase> preset = roleModel.get().getKnowledge().getPreset();
            personal = personal == null ? new ArrayList<>() : personal;
            preset = preset == null ? new ArrayList<>() : preset;
            List<String> knowledgeIds =
                    personal.stream().map(KnowledgeBase::getKnowledgeBaseId).collect(Collectors.toList());
            knowledgeIds.addAll(preset.stream().map(KnowledgeBase::getKnowledgeBaseId).collect(Collectors.toList()));
            Map<String, Boolean> isDeleteMap = new HashMap<>();
            knowledgeBaseService.batchGetKnowledgeBaseById(knowledgeIds).forEach(knowledgeBase -> {
                isDeleteMap.put(knowledgeBase.getKnowledgeBaseId(), knowledgeBase.isDelete());
            });
            log.debug("delete map is {}", isDeleteMap);
            for (int i = personal.size() - 1; i >= 0; i--) {
                if (isDeleteMap.get(personal.get(i).getKnowledgeBaseId())) {
                    needUpdate = true;
                    personal.remove(i);
                }
            }
            for (int i = preset.size() - 1; i >= 0; i--) {
                if (isDeleteMap.get(preset.get(i).getKnowledgeBaseId())) {
                    needUpdate = true;
                    preset.remove(i);
                }
            }
            List<String> knowledgeBaseIds = roleModel.get().getKnowledge().getPersonal().stream()
                    .map(KnowledgeBase::getKnowledgeBaseId).collect(Collectors.toList());
            if (knowledgeBaseIds != null && knowledgeBaseIds.size() > 0) {
                Map<String, Pair<Boolean, Integer>> knowledgeBaseStatus =
                        knowledgeBaseService.batchGetKnowledgeBaseStatus(knowledgeBaseIds);

                for (KnowledgeBase knowledgeBase : roleModel.get().getKnowledge().getPersonal()) {

                    if (knowledgeBaseStatus.get(knowledgeBase.getKnowledgeBaseId()).getFirst()) {
                        knowledgeBase.setStatus(1);
                    } else {
                        knowledgeBase.setStatus(0);
                    }
                }
            }
        }
        if (needUpdate) {
            llmRoleRepository.save(roleModel.get());
        }

        LlmRole llmRole = roleModel.get().toLlmRole();
        llmRoleOnlineRepository.findByLlmRoleId(llmRoleId).ifPresent(llmRoleOnlineModel -> {
            llmRole.setPublishUrl(buildPublishUrl(llmRoleOnlineModel.getAppId(), llmRoleOnlineModel.getAppKey(),
                    llmRole.getProjectId(), llmRoleOnlineModel.getScreenType(), llmRoleOnlineModel.getTtsParams(),
                    llmRoleOnlineModel.getCharacterConfigId()));
        });
        return llmRole;
    }

    @Override
    public LlmRole copyLlmRole(String accoutId, String llmRoleId, String rename) {

        List<LlmRoleModel> exist = llmRoleRepository.findByAccountIdAndNameAndDel(accoutId, rename);
        if (exist.size() > 0) {
            throw new DigitalHumanCommonException("已存在同名交互");
        }
        Optional<LlmRoleModel> roleModel = llmRoleRepository.findByLlmRoleId(llmRoleId);
        if (roleModel.isEmpty()) {
            throw new DigitalHumanCommonException("该角色不存在");
        }
        if (!accoutId.equals(roleModel.get().getAccountId())) {
            throw new DigitalHumanCommonException("该角色不属于当前账户");
        }
        LlmRoleModel llmRoleModel = roleModel.get();

        llmRoleModel.setName(rename);

        Project project = platformClient.getProjectById(llmRoleModel.getProjectId());

        project.setName(rename);
        return createLlmRole(roleModel.get().toLlmRole(), project, LLM_ROLE_TYPE);

    }

    /**
     * 更新交互信息
     *
     * @param llmRoleId      交互ID
     * @param roleRequest    修改后的交互请求信息（不为空）
     * @param projectRequest 修改后的项目请求信息（根据角色类型不同而变化）
     * @param roleType       角色类型，1：模版，2：角色
     * @return 返回更新后的交互信息
     */
    @Override
    public LlmRole updateLlmRole(String llmRoleId, LlmRole roleRequest, Project projectRequest, int roleType) {
        LlmRoleModel updated = null;
        Optional<LlmRoleModel> role = llmRoleRepository.findByLlmRoleId(llmRoleId);
        if (role.isEmpty()) {
            throw new DigitalHumanCommonException("该交互不存在");
        }
        if (!role.get().getAccountId().equals(roleRequest.getAccountId())) {
            throw new DigitalHumanCommonException("无权限更新");
        }
        if (roleType == LLM_TEMPLATE_TYPE) {
            roleRequest.setAccountId("System");
            projectRequest.setUserId("System");
        }
        updated = role.get();
        if (updated.getRoleType() != roleType) {
            throw new DigitalHumanCommonException("无权限更新");
        }
        updated.setTemplateId(roleRequest.getTemplateId());
        updated.setLlmRoleTemplateId(roleRequest.getLlmRoleTemplateId());
        updated.setScreenType(roleRequest.getScreenType());
        updated.setStatus(roleRequest.getStatus());
        updated.setCharacterConfigId(projectRequest.getCharacterConfigId());
        if (roleRequest.getModeType() == null
                || roleRequest.getModeType() == ModeType.NORMAL.getId()) {
            updated.setModeType(ModeType.NORMAL.getId());
        } else {
            updated.setModeType(roleRequest.getModeType());
        }
        if (StringUtils.isNotBlank(roleRequest.getName()) && !roleRequest.getName().equals(updated.getName())) {
            List<LlmRoleModel> exist =
                    llmRoleRepository.findByAccountIdAndNameAndDel(roleRequest.getAccountId(), roleRequest.getName());
            if (exist.size() > 0) {
                throw new DigitalHumanCommonException("已存在同名交互");
            }
            projectRequest.setName(roleRequest.getName());
        }
        // 如果更新交互的数据来源于模版，还需要将模版里的BotParam同步到交互对应的场景中
        if (roleType == LLM_ROLE_TYPE && roleRequest.getRoleType() == LLM_TEMPLATE_TYPE) {
            Project template = platformClient.getProjectById(roleRequest.getProjectId());
            template.getBotParams().getCredentials().setLlmRoleId(llmRoleId);
            projectRequest.setBotParams(template.getBotParams());
        }
        if (projectRequest == null || StringUtils.isEmpty(projectRequest.getId())) {
            log.debug("Update llm role and project is null, only update status");
            llmRoleRepository.findByLlmRoleId(llmRoleId).ifPresent(llmRoleModel -> {
                llmRoleModel.setStatus(roleRequest.getStatus());
                log.debug("Update llm role and llmRoleModel is status={}", roleRequest.getStatus());
                llmRoleRepository.save(llmRoleModel);
            });
            llmRoleOnlineRepository.findByLlmRoleId(llmRoleId).ifPresent(llmRoleOnlineModel -> {
                llmRoleOnlineModel.setStatus(roleRequest.getStatus());
                log.debug("Update llm role and llmRoleOnlineModel is status={}", roleRequest.getStatus());
                llmRoleOnlineRepository.save(llmRoleOnlineModel);
            });
            return updated.toLlmRole();
        }
        // 高级模式用keyue
        if (ModeType.ADVANCED.getId() == updated.getModeType()) {
            projectRequest.getBotParams().setType(BotParams.BotTypeEnum.KE_YUE.getBotType());
        } else {
            projectRequest.getBotParams().setType(BotParams.BotTypeEnum.COPILOT.getBotType());
        }
        projectRequest.setId(updated.getProjectId());
        projectRequest.setType("SYSTEM");
        projectRequest.getBotParams().getCredentials().setLlmRoleId(llmRoleId);
        projectRequest.setPrologueParams(PrologueParams
                .builder()
                .showPrologue(true)
                .type(PrologueParams.PrologueType.query)
                .contents(LLMConstants.START_QUERY)
                .build());
        log.debug("update llm role and before project is {}", projectRequest);
        Project project = platformClient.updateProject(projectRequest.getId(), projectRequest);
        log.debug("update llm role and updated project is {}", project);
        if (roleRequest != null) {
            mergeRoleModel(updated, roleRequest);
            if (ModeType.ADVANCED.getId() == updated.getModeType()) {
                updated.setTemplateId("");
                updated.setKnowledge(null);
                updated.setRoleDefine("");
                updated.setOpeningStatement(null);
            }
            log.info("Start to update llm role={}", updated);
            if (projectRequest.getTtsParams() != null) {
                updated.setTtsParams(projectRequest.getTtsParams());
            }
            log.info("Start to update llm role={}", updated);
            llmRoleRepository.save(updated);
        }
        return updated.toLlmRole();
    }

    /**
     * 发布交互信息
     *
     * @param llmRoleId      交互ID
     * @param roleRequest    修改后的交互请求信息（不为空）
     * @param projectRequest 修改后的项目请求信息（根据角色类型不同而变化）
     * @param roleType       角色类型，1：模版，2：角色
     * @return 返回发布后的URL
     */
    @Override
    public String publishLlmRole(String llmRoleId, LlmRole roleRequest, Project projectRequest, int roleType) {
        // 通过代理解决事务失效问题
        LlmRole llmRole = updateLlmRole(llmRoleId, roleRequest, projectRequest, roleType);
        LlmRoleModel llmRoleModelOffline = llmRoleRepository.findByLlmRoleId(llmRoleId).get();
        Optional<LlmRoleOnlineModel> llmRoleOnlineModelOptional = llmRoleOnlineRepository.findByLlmRoleId(llmRoleId);
        LlmRoleOnlineModel llmRoleOnlineModel = llmRoleOnlineModelOptional.orElseGet(LlmRoleOnlineModel::new);
        BeanUtils.copyProperties(llmRole, llmRoleOnlineModel);
        if (llmRoleModelOffline.getTtsParams() != null) {
            llmRoleOnlineModel.setTtsParams(llmRoleModelOffline.getTtsParams());
        }
        llmRoleOnlineModel.setStatus("open");
        LlmRoleOnlineModel save = llmRoleOnlineRepository.save(llmRoleOnlineModel);
        log.debug("publish llm role and published role is {}", llmRoleOnlineModel);
        Project project = platformClient.getProjectById(llmRole.getProjectId());
        platformClient.publishProject(project.getId(), project);
        AccessApp accessApp;
        String publishUrl;
        if (StringUtils.isEmpty(llmRoleOnlineModel.getAppId())) {
            // 创建appId, appKey;
            accessApp = AccessApp.builder()
                    .userId(llmRole.getAccountId())
                    .name(llmRole.getName() + UUID.randomUUID())
                    .description("大模型交互")
                    .characterImage(project.getCharacterImage())
                    .resourceQuota(ResourceQuota.builder()
                            .roomLimits(5)
                            .build()).build();
            if (projectRequest.getCharacterConfigId() != null && projectRequest.getCharacterConfigId().startsWith(
                    "people")) {
                accessApp.setCharacterImage("2D_LITE_VIS");
            }
            accessApp = platformClient.createApp(accessApp);
            save.setAppId(accessApp.getAppId());
            save.setAppKey(accessApp.getAppKey());
            publishUrl = buildPublishUrl(accessApp.getAppId(), accessApp.getAppKey(), project.getId(),
                    llmRoleOnlineModel.getScreenType(), llmRoleOnlineModel.getTtsParams(),
                    llmRoleOnlineModel.getCharacterConfigId());
            llmRoleModelOffline.setAppId(accessApp.getAppId());
            llmRoleModelOffline.setAppKey(accessApp.getAppKey());
            llmRoleOnlineRepository.save(save);
        } else {
            accessApp = platformClient.getApp(llmRoleOnlineModel.getAppId());
            publishUrl = buildPublishUrl(accessApp.getAppId(), accessApp.getAppKey(), project.getId(),
                    llmRoleOnlineModel.getScreenType(), llmRoleOnlineModel.getTtsParams(),
                    llmRoleOnlineModel.getCharacterConfigId());
            llmRoleModelOffline.setAppId(accessApp.getAppId());
            llmRoleModelOffline.setAppKey(accessApp.getAppKey());
            accessApp.setCharacterImage(project.getCharacterImage());
            accessApp.setAppKey(null);
            // TODO 垃圾代码
            if (projectRequest.getCharacterConfigId() != null && projectRequest.getCharacterConfigId().startsWith(
                    "people")) {
                accessApp.setCharacterImage("2D_LITE_VIS");
            }
            platformClient.updateApp(accessApp.getAppId(), accessApp);
        }
        llmRoleRepository.save(llmRoleModelOffline);
        return publishUrl;
    }

    private String buildPublishUrl(String appId, String appKey, String projectId, String screenType,
                                   TtsParams ttsParams, String characterConfigId) {
        StringBuffer sb = new StringBuffer();
        String url =
                commonConfig.getPublishUrl() + "?appId=" + appId + "&appKey=" + appKey + "&projectId=" + projectId +
                        "&configVersion=publish&chatMode=isStream&usingAudio=userMedia&pickAudioMode=pressButton" +
                        "&directLogin=1&mode=crop";
        if ("vertical".equals(screenType)) {
            url = url.replace("/cloud/react", "/cloud/vertical");
            url = url.replace("/cloud/web", "/cloud/vertical");
        }
        sb.append(url);
        if (ttsParams != null) {
            sb.append("&ttsSpeed=" + ttsParams.getSpeed());
            sb.append("&ttsPitch=" + ttsParams.getPitch());
        }
        if (StringUtils.isNotEmpty(characterConfigId) && characterConfigId.startsWith("people")) {
            sb.append("&figureName=" + characterConfigId);
        }
        return sb.toString();
    }

    @Override
    public void batchDeleteRole(String accountId, List<String> llmRoleIds, int roleType) {
        List<LlmRoleModel> llmRoles = llmRoleRepository.findByLlmRoleIdIn(llmRoleIds);
        if (llmRoles.size() != llmRoleIds.size()) {
            throw new DigitalHumanCommonException("删除失败，不存在该角色");
        }

        llmRoles.forEach(role -> {
            if (role.getRoleType() != roleType || (!role.getAccountId().equals(accountId)
                                                           && roleType == LLM_ROLE_TYPE)) {
                throw new DigitalHumanCommonException("删除失败，该交互不属于当前账户");
            }
            if (roleType == LLM_ROLE_TYPE || roleType == LLM_TEMPLATE_TYPE) {
                try {
                    log.debug("start dele project id is  {}", role.getProjectId());
                    platformClient.deleteProject(role.getProjectId());
                    if (!StringUtils.isEmpty(role.getAppId())) {
                        try {
                            platformClient.deleteApp(accountId, 2, role.getAppId());
                        } catch (Exception e) {
                            log.warn("Delete app error", e);
                        }
                    }
                    llmRoleOnlineRepository.findByLlmRoleId(role.getLlmRoleId()).ifPresent(llmRoleOnlineModel -> {
                        llmRoleOnlineModel.setDel(true);
                        llmRoleOnlineRepository.save(llmRoleOnlineModel);
                        if (!StringUtils.isEmpty(llmRoleOnlineModel.getAppId())) {
                            try {
                                platformClient.deleteApp(accountId, 2, llmRoleOnlineModel.getAppId());
                            } catch (Exception e) {
                                log.warn("Delete app error", e);
                            }
                        }
                    });
                } catch (Exception e) {
                    throw new DigitalHumanCommonException("删除交互绑定的对应场景失败", e);
                }
            }
            role.setDel(true);
        });
        llmRoleRepository.saveAll(llmRoles);
    }

    public void mergeRoleModel(LlmRoleModel model, LlmRole request) {
        if (StringUtils.isNotBlank(request.getName())) {
            model.setName(request.getName());
        }
        if (request.getLlmConfig() != null) {
            model.setLlmConfig(request.getLlmConfig());
        }
        if (StringUtils.isNotBlank(request.getRoleDefine())) {
            model.setRoleDefine(request.getRoleDefine());
        }
        if (request.getSkillList() != null && request.getSkillList().size() > 0) {
            model.setSkillList(request.getSkillList());
        }
        if (request.getKnowledge() != null) {
            model.setKnowledge(request.getKnowledge());
        }
        if (request.getOpeningStatement() != null) {
            model.setOpeningStatement(request.getOpeningStatement());
        }
        if (request.getBufferSentencesConfigJson() != null) {
            model.setBufferSentencesConfigJson(request.getBufferSentencesConfigJson());
        }
        if (StringUtils.isNotBlank(request.getEditor())) {
            model.setEditor(request.getEditor());
        }
        if (StringUtils.isNotBlank(request.getTemplateName())) {
            model.setTemplateName(request.getTemplateName());
        }
        if (StringUtils.isNotBlank(request.getTemplateIconUrl())) {
            model.setTemplateIconUrl(request.getTemplateIconUrl());
        }
        model.setTemplateId(request.getTemplateId());
        if (request.getModeType() != null) {
            model.setModeType(request.getModeType());
        }
        if (StringUtils.isNotBlank(request.getLlmRoleTemplateId())) {
            model.setLlmRoleTemplateId(request.getLlmRoleTemplateId());
        }
        if (StringUtils.isNotBlank(request.getScreenType())) {
            model.setScreenType(request.getScreenType());
        }
        if (StringUtils.isNotBlank(request.getStatus())) {
            model.setStatus(request.getStatus());
        }

    }

    /**
     * 根据角色类型、账户ID和名称列表查询角色信息列表
     *
     * @param accountId 账户ID
     * @param roleType  角色类型，取值为LLM_TEMPLATE_TYPE（模板角色）或LLM_ROLE_TYPE（实时角色）
     * @param pageNo    当前页码（从1开始计数）
     * @param pageSize  每页显示的条目数量
     * @param name      模板角色名或实时角色名的前缀，用于过滤结果
     * @return 返回分页后的响应对象，包含角色信息列表
     */
    public PageResponse<LlmRole> listByRoleType(String accountId, int roleType, int pageNo, int pageSize, String name) {
        log.debug("start list accountId id is {}, role is {}, name is {} ", accountId, roleType, name);
        PageRequest pageRequest = PageRequest.of(pageNo - 1, pageSize);
        Page<LlmRoleModel> page = null;
        PageResponse<LlmRole> pageResponse = null;
        if (roleType == LLM_TEMPLATE_TYPE) {
            page = llmRoleRepository.findByRoleTypeAndNameContainingAndDelOrderByCreateTimeDesc(roleType, name, false,
                    pageRequest);
            pageResponse = PageResponse.success(pageNo, pageSize, page.getTotalElements(),
                    page.getContent().stream().map(LlmRoleModel::toTemplateRole).collect(Collectors.toList()));
        } else if (roleType == LLM_ROLE_TYPE) {
            page = llmRoleRepository.findByAccountIdAndRoleTypeAndNameContainingAndDelOrderByCreateTimeDesc(accountId,
                    roleType, name, false, pageRequest);
            pageResponse = PageResponse.success(pageNo, pageSize, page.getTotalElements(),
                    page.getContent().stream().map(llmRoleModel -> {
                        LlmRole llmRole = llmRoleModel.toLlmRole();
                        llmRoleOnlineRepository.findByLlmRoleId(llmRole.getLlmRoleId())
                                .ifPresent(llmRoleOnlineModel -> {
                                    llmRole.setPublishUrl(buildPublishUrl(llmRoleOnlineModel.getAppId(),
                                            llmRoleOnlineModel.getAppKey(),
                                            llmRole.getProjectId(),
                                            llmRole.getScreenType(), llmRoleOnlineModel.getTtsParams(),
                                            llmRoleOnlineModel.getCharacterConfigId()));
                                });
                        return llmRole;
                    }).collect(Collectors.toList()));
        }

        return pageResponse;
    }

    @Override
    public LlmRole createLlmConfig(LlmRole role, int roleType) {
        role.setName("LLM-CONFIG");
        role.setAccountId("Config");
        String llmRoleId = idGenerator.generate("llm");
        LlmRoleModel llmRoleModel = LlmRoleModel.builder()
                .llmRoleId(llmRoleId)
                .llmRoleTemplateId(role.getLlmRoleTemplateId())
                .screenType(role.getScreenType())
                .templateId(role.getTemplateId())
                .roleType(roleType)
                .name(role.getName())
                .accountId(role.getAccountId())
                .editor(role.getEditor())
                .templateName(role.getTemplateName())
                .bufferSentencesConfigJson(role.getBufferSentencesConfigJson())
                .skillList(role.getSkillList())
                .knowledge(role.getKnowledge())
                .openingStatement(role.getOpeningStatement())
                .roleDefine(role.getRoleDefine())
                .llmConfig(role.getLlmConfig())
                .status(StatusType.OPNE.getStatus())
                .templateIconUrl(role.getTemplateIconUrl()).build();
        LlmRoleModel save = llmRoleRepository.save(llmRoleModel);
        return save.toLlmRole();
    }

    @Override
    public LlmRole detailLlmConfig(String llmRoleId) {
        Optional<LlmRoleModel> roleModel = llmRoleRepository.findByLlmRoleId(llmRoleId);
        if (roleModel.isEmpty()) {
            throw new DigitalHumanCommonException("该设置不存在");
        }
        return roleModel.get().toLlmRole();
    }

    @Override
    public LlmRole updateLlmConfig(String llmRoleId, LlmRole roleRequest, int roleType) {
        LlmRoleModel updated = null;
        Optional<LlmRoleModel> role = llmRoleRepository.findByLlmRoleId(llmRoleId);
        if (role.isEmpty()) {
            throw new DigitalHumanCommonException("该设置不存在");
        }
        updated = role.get();
        if (updated.getRoleType() != roleType) {
            throw new DigitalHumanCommonException("无权限更新");
        }
        if (roleRequest != null) {
            mergeRoleModel(updated, roleRequest);
            llmRoleRepository.save(updated);
        }
        return updated.toLlmRole();
    }

}