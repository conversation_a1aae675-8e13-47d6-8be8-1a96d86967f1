// Copyright (C) 2019 Baidu Inc. All rights reserved.
package com.baidu.acg.piat.llm.dm.service.impl;

import java.nio.charset.StandardCharsets;
import java.time.Duration;
import java.util.HashMap;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.web.reactive.function.client.WebClient;

import com.baidu.acg.piat.digitalhuman.common.llmrole.LLMConfig;
import com.baidu.acg.piat.digitalhuman.common.project.BotParams;
import com.baidu.acg.piat.digitalhuman.common.utils.JsonUtil;
import com.baidu.acg.piat.llm.dm.model.PendingTask;
import com.baidu.acg.piat.llm.dm.model.Query;
import com.baidu.acg.piat.llm.dm.model.SessionContext;
import com.baidu.acg.piat.llm.dm.model.copilot.DifyRequest;
import com.baidu.acg.piat.llm.dm.model.copilot.DifyResponseBody;
import com.baidu.acg.piat.llm.dm.model.copilot.DifyResult;
import com.baidu.acg.piat.llm.dm.service.BotProviderService;

import io.vavr.control.Try;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.Disposable;
import reactor.core.publisher.Flux;

@RequiredArgsConstructor
@Slf4j
public class BotServiceCopilotStreamImpl implements BotProviderService {

    WebClient webClient = WebClient.builder().build();

    public static final String BOT_TYPE = BotParams.BotTypeEnum.COPILOT.getBotType();

    public static final String COPILOT_TOKEN_PREFIX = "Bearer ";

    /**
     * 只用于http接口测试
     *
     * @param sessionContext
     * @param query
     * @param llmConfig
     * @return
     */
    public Flux<String> queryDifyWithReturn(SessionContext sessionContext, Query query, LLMConfig llmConfig) {
        // TODO
        DifyRequest difyRequest = new DifyRequest();
        difyRequest.setConversationId("");
        difyRequest.setResponseMode(llmConfig.getParams().getOrDefault("response_mode",
                "streaming").toString());
        difyRequest.setQuery(query.getText());
        difyRequest.setUser(sessionContext.getSessionId());
        Map<String, Object> inputParams = new HashMap<>();
        if (llmConfig.getParams() != null) {
            inputParams.putAll(llmConfig.getParams());
        }

        if (query.getContext() != null) {
            inputParams.putAll(query.getContext());
        }
        inputParams.remove("response_mode");

        difyRequest.setInputs(inputParams);
        log.info("Request dify:{}", difyRequest);
        try {
            Flux<String> responseFlux = webClient.post()
                    .uri(llmConfig.getUrl()) // 你的端点
                    .headers(header -> header.addAll(getHeaders(llmConfig)))
                    .bodyValue(difyRequest)
                    .acceptCharset(StandardCharsets.UTF_8)
                    .accept(MediaType.APPLICATION_NDJSON)
                    .retrieve()
                    .bodyToFlux(String.class);
            responseFlux.onErrorResume(e -> {
                log.error("Dify error 2:{}", e.getMessage());
                return Flux.empty();
            }).subscribe(response -> {
                log.info("Dify Response:{}", response);
            });
        return responseFlux;
        } catch (Exception e) {
            log.error("Dify error 1:{}", e.getMessage());
            return Flux.empty();
        }
    }

    public Flux<String>  chat(Query query, LLMConfig llmConfig) {
        DifyRequest difyRequest = new DifyRequest();
        difyRequest.setConversationId("");
        difyRequest.setQuery(query.getText());
        if (llmConfig.getParams() == null) {
            llmConfig.setParams(new HashMap<>());
        }
        difyRequest.setResponseMode(llmConfig.getParams().getOrDefault("response_mode",
                "streaming").toString());

        Map<String, Object> inputParams = new HashMap<>();
        if (llmConfig.getParams() != null) {
            inputParams.putAll(llmConfig.getParams());
        }

        if (query.getContext() != null) {
            inputParams.putAll(query.getContext());
        }

        difyRequest.setInputs(inputParams);
        if (query.getCopilotActionList() != null) {
            difyRequest.setActions(query.getCopilotActionList());
            difyRequest.setPreAction(query.getCopilotActionList());
        }
        difyRequest.setUser("0");
        String url = llmConfig.getUrl();
        try {
            log.info("Request dify url:{}, body:{}, token={}", url, JsonUtil.writeValueAsString(difyRequest),
                    llmConfig.getCredential().get("token"));
        } catch (Exception e) {

        }
        Flux<String> responseFlux = webClient.post()
                .uri(url) // 你的端点
                .headers(header -> header.addAll(getHeaders(llmConfig)))
                .bodyValue(difyRequest)
                .acceptCharset(StandardCharsets.UTF_8)
                .accept(MediaType.APPLICATION_NDJSON)
                .retrieve()
                .bodyToFlux(String.class); // 你的返回类型
        log.debug("Copilot response is {}", responseFlux.toString());
        return responseFlux;
    }

    /**
     * 查询数据。
     *
     * @param sessionContext 会话上下文
     * @param query 查询条件
     */
    @Override
    public void query(SessionContext sessionContext, Query query) {
        LLMConfig llmConfig = sessionContext.getLlmConfig();
        // TODO
        DifyRequest difyRequest = new DifyRequest();
        difyRequest.setConversationId("");
        difyRequest.setQuery(query.getText());
        if (!StringUtils.isEmpty(sessionContext.getCopilotConversationId())) {
            difyRequest.setConversationId(sessionContext.getCopilotConversationId());
        }
        if (llmConfig.getParams() == null) {
            llmConfig.setParams(new HashMap<>());
        }
        difyRequest.setResponseMode(llmConfig.getParams().getOrDefault("response_mode",
                "streaming").toString());
        difyRequest.setUser(sessionContext.getSessionId());

        Map<String, Object> inputParams = new HashMap<>();
        if (llmConfig.getParams() != null) {
            inputParams.putAll(llmConfig.getParams());
        }

        if (query.getContext() != null) {
            inputParams.putAll(query.getContext());
        }

        difyRequest.setInputs(inputParams);
        if (query.getCopilotActionList() != null) {
            difyRequest.setActions(query.getCopilotActionList());
            difyRequest.setPreAction(query.getCopilotActionList());
        }
        String url = llmConfig.getUrl();
        try {
            log.info("Request dify url:{}, body:{}, token={}", url, JsonUtil.writeValueAsString(difyRequest),
                    llmConfig.getCredential().get("token"));
        } catch (Exception e) {

        }
        Flux<String> responseFlux = webClient.post()
                .uri(url) // 你的端点
                .headers(header -> header.addAll(getHeaders(llmConfig)))
                .bodyValue(difyRequest)
                .acceptCharset(StandardCharsets.UTF_8)
                .accept(MediaType.APPLICATION_NDJSON)
                .retrieve()
                .bodyToFlux(String.class); // 你的返回类型

        handleResponse(responseFlux, sessionContext, query);
    }

    @Override
    public void reset(SessionContext sessionContext) {
        sessionContext.setCopilotConversationId(StringUtils.EMPTY);
    }
    private void handleResponse(Flux<String> responseFlux, SessionContext sessionContext, Query query) {
        Disposable disposable = responseFlux
                .timeout(Duration.ofSeconds(sessionContext.getLlmConfig().getTimeout()))
                .doOnCancel(() -> {
                    log.debug("Cancelled, query={}", query);
                    sessionContext.getLlmStreamObserver().onCancel(query);
                }).doOnError(e -> {
                    // 打印错误
                    log.error("Handle response sessionId={}, requestId={}, Error", sessionContext.getSessionId(),
                            query.getRequestId(), e);
                    sessionContext.getLlmStreamObserver().onError(e, query);

                }).doOnComplete(() -> {
                    sessionContext.getLlmStreamObserver().onCompleted(query);
                    // 打印结束
                    log.info("Hanlde response sessionId={}, requestId={} Complete", sessionContext.getSessionId(),
                            query.getRequestId());
                }).subscribe(response -> {
                    // 打印每个接收到的chunk
                    log.info("Received chunk: {}", response);
                    // 请求copilot的中控，DTE
                    if (sessionContext.getLlmRoleModel().getLlmConfig().getUrl().endsWith("/v1/api/planner/integrated")) {
                        Try.of(() -> JsonUtil.readValue(response, DifyResult.class))
                                .onFailure(e -> log.error("Parse response error:{}", e.getMessage()))
                                .onSuccess(r -> {
                                    if (r != null && r.getResult() != null
                                            && !StringUtils.isEmpty(r.getResult().getConversationId())) {
                                        sessionContext.setCopilotConversationId(r.getResult().getConversationId());
                                    }
                                    if (r != null && r.getResult() != null && !StringUtils.isEmpty(
                                            r.getResult().getAnswer())) {
                                        sessionContext.getLlmStreamObserver().onNext(r.getResult().getAnswer(),
                                                "largeModel", query);
                                    }
                                });
                    } else {
                        // 直接请求copilot的单体应用
                        Try.of(() -> JsonUtil.readValue(response, DifyResponseBody.class))
                                .onFailure(e -> log.error("Parse response error:{}", e.getMessage()))
                                .onSuccess(r -> {
                                    if (r != null && !StringUtils.isEmpty(r.getConversationId())) {
                                        sessionContext.setCopilotConversationId(r.getConversationId());
                                    }
                                    if (r != null && !StringUtils.isEmpty(r.getAnswer())) {
                                        sessionContext.getLlmStreamObserver().onNext(r.getAnswer(),
                                                "largeModel", query);
                                    }
                                });
                    }

                });

        sessionContext.getPendingQueryMap().put(query.getRequestId(),
                PendingTask.builder().queryIndex(query.getQueryIndex()).disposable(disposable).build());

    }

    /**
     * @return
     */
    public void interrupt(SessionContext sessionContext, String taskId) {
        if (sessionContext.getPendingQueryMap().isEmpty()) {
            return;
        }
        // 清理一下过期的taskId；
        sessionContext.getInterruptedMap().entrySet().stream().filter(e -> {
            return System.currentTimeMillis() - e.getValue() > 1000 * 60 * 5;
        }).forEach(e -> {
            log.info("Interrupt timeout interrupted taskId={}", e.getKey());
            sessionContext.getInterruptedMap().remove(e.getKey());
        });
        // 批量删除
        if (StringUtils.isEmpty(taskId)) {
            log.info("Interrupt all={}", sessionContext.getPendingQueryMap().keySet());
            sessionContext.getPendingQueryMap().forEach((k, v) -> {
                v.getDisposable().dispose();
            });
            int i = 0;
            // 判断是否真的中断, 一次性给所有的pending发中断指令
            while (sessionContext.getPendingQueryMap().keySet().stream().anyMatch(k -> {
                log.info("Interrupt add interrupted sessionId={}, taskId={}", sessionContext.getSessionId(), k);
                sessionContext.getInterruptedMap().put(k, System.currentTimeMillis());
                return !sessionContext.getPendingQueryMap().get(k).getDisposable().isDisposed();
            }) && i < 3) {
                try {
                    Thread.sleep(100);
                } catch (InterruptedException e) {
                    log.error("Interrupt error", e);
                }
                i++;
            }
            sessionContext.getPendingQueryMap().keySet().forEach(k -> {
                clear(k, sessionContext);
            });
            return;
        }
        PendingTask pendingTask = sessionContext.getPendingQueryMap().get(taskId);
        if (pendingTask != null) {
            log.info("Interrupt sessionId={}, taskId={}", sessionContext.getSessionId(), taskId);
            // 会有延迟不一定立马中断
            pendingTask.getDisposable().dispose();
            sessionContext.getInterruptedMap().put(taskId, System.currentTimeMillis());
            int i = 0;
            // 判断是否真的中断
            while (!pendingTask.getDisposable().isDisposed() && i < 3) {
                try {
                    Thread.sleep(100);
                } catch (InterruptedException e) {
                    log.error("Interrupt error", e);
                }
                i++;
            }
            clear(taskId, sessionContext);
        }
    }

    public void clear(String taskId, SessionContext sessionContext) {
        log.debug("Clear task id={}", taskId);
        if (StringUtils.isEmpty(taskId)) {
            return;
        }
        sessionContext.getPendingQueryMap().remove(taskId);
        sessionContext.getPendingResponseSeqMap().remove(taskId);
        sessionContext.getPendingResponseMap().remove(taskId);
    }

    private HttpHeaders getHeaders(LLMConfig llmConfig) {
        HttpHeaders headers = new HttpHeaders();
        headers.add("Authorization", COPILOT_TOKEN_PREFIX + llmConfig.getCredential().get("token"));
        headers.add("Content-Type", "application/json;charset=UTF-8");
        return headers;
    }

    @Override
    public String getBotType() {
        return BOT_TYPE;
    }
}
