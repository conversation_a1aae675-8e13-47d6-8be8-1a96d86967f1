package com.baidu.acg.piat.llm.dm.model.copilot;

import java.util.List;
import java.util.Map;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CopilotAction {
    /**
     *   {
     *             "name":"search_db",
     *             "inputs":[
     *                 "query"
     *             ],
     *             "arguments":{
     *                 "top_k":2,
     *                 "dataset_id":"035472ab-7620-44e4-a0b4-b219dc87a125"
     *             },
     *             "outputs":[
     *                 "context"
     *             ]
     *         }
     */
    private String name;

    private List<String> inputs;

    private Map<String, Object> arguments;

    private List<String> outputs;

    public CopilotAction(String datasetId) {
        this.name = "search_db";
        this.inputs = List.of("query");
        this.arguments = Map.of("top_k", 3, "dataset_id", datasetId);
        this.outputs = List.of("context");
    }

    public CopilotAction(String datasetId, String topK) {
        this.name = "search_db";
        this.inputs = List.of("query");
        this.arguments = Map.of("top_k", topK, "dataset_id", datasetId);
        this.outputs = List.of("context");
    }
}
