package com.baidu.acg.piat.llm.dm.controller;

import com.baidu.acg.piat.digitalhuman.common.llmrole.LlmRole;
import com.baidu.acg.piat.digitalhuman.common.llmrole.LlmRoleCopy;
import com.baidu.acg.piat.digitalhuman.common.llmrole.RoleWithProject;
import com.baidu.acg.piat.digitalhuman.common.model.PageResponse;
import com.baidu.acg.piat.digitalhuman.common.model.Response;
import com.baidu.acg.piat.digitalhuman.common.project.Project;

import com.baidu.acg.piat.llm.dm.service.LlmRoleService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import static com.baidu.acg.piat.digitalhuman.common.llmrole.Constant.LLM_CONFIG_TYPE;
import static com.baidu.acg.piat.digitalhuman.common.llmrole.Constant.LLM_ROLE_TYPE;
import static com.baidu.acg.piat.digitalhuman.common.llmrole.Constant.LLM_TEMPLATE_TYPE;

/**
 * <AUTHOR> Xiaoyu
 * @since 2023/11/23 13:47
 */
@Slf4j
@RestController
@RequestMapping("/api/digitalhuman/v1/llm/role")
@RequiredArgsConstructor
public class LlmRoleController {

    private final LlmRoleService llmRoleService;


    @PostMapping
    public Response<LlmRole> createLlmRole(@RequestBody RoleWithProject roleWithProject) {
        log.debug("Start to create llm:{}", roleWithProject);
        LlmRole role = roleWithProject.getRole();
        Project project = roleWithProject.getProject();
        return Response.success(llmRoleService.createLlmRole(role, project, LLM_ROLE_TYPE));
    }

    /**
     * 创建LLM角色实例
     *
     * @param llmRoleTemplateId 模板ID
     * @return 返回LLM角色实例
     */
    @PostMapping("/create/{llmRoleTemplateId}")
    public Response<LlmRole> createLlmRoleByTemplate(@PathVariable(value = "llmRoleTemplateId") String
                                                                 llmRoleTemplateId, @RequestBody LlmRole llmRole) {
        log.debug("Start to create llmRole by templateId:{}", llmRoleTemplateId);
        return Response.success(llmRoleService.createLlmRoleByTemplate(llmRoleTemplateId, llmRole));
    }

    @GetMapping
    public PageResponse<LlmRole> listByRoleType(@RequestParam String accountId, @RequestParam int roleType,
                                                @RequestParam String name, @RequestParam int pageNo, @RequestParam int pageSize) {
        log.debug("Start to list llm roleType:{}", roleType);
        return llmRoleService.listByRoleType(accountId, roleType, pageNo, pageSize, name);
    }

    @PutMapping("/{llmRoleId}")
    public Response<LlmRole> updateLlmRole(@PathVariable(value = "llmRoleId") String llmRoleId,
                                           @RequestBody RoleWithProject roleWithProject) {
        log.debug("Start to update llm:{}", roleWithProject);
        LlmRole role = roleWithProject.getRole();
        if (role.getRoleType() == null) {
            role.setRoleType(LLM_ROLE_TYPE);
        }
        Project project = roleWithProject.getProject();
        return Response.success(llmRoleService.updateLlmRole(llmRoleId, role, project, LLM_ROLE_TYPE));
    }

    /**
     * 发布LLM角色
     *
     * @param llmRoleId LLM角色ID
     * @param roleWithProject 角色与项目信息封装类
     * @return 返回响应结果，包含成功或失败信息
     */
    @PutMapping("/publish/{llmRoleId}")
    public Response<String> publishLlmRole(@PathVariable(value = "llmRoleId") String llmRoleId,
                                           @RequestBody RoleWithProject roleWithProject) {
        log.debug("Start to publish llm:{}", roleWithProject);
        LlmRole role = roleWithProject.getRole();
        Project project = roleWithProject.getProject();
        return Response.success(llmRoleService.publishLlmRole(llmRoleId, role, project, LLM_ROLE_TYPE));
    }

    @PostMapping("/delete")
    public Response<Void> deleteLlmRole(@RequestParam String accountId,
                              @RequestBody List<String> llmRoleIds) {
        log.debug("Start to delete llm:{}", llmRoleIds);
        llmRoleService.batchDeleteRole(accountId, llmRoleIds, LLM_ROLE_TYPE);
        return Response.success(null);
    }

    @GetMapping("/detail")
    public Response<LlmRole> detail(@RequestParam String accountId, @RequestParam String llmRoleId) {
        log.debug("start to detail llm");
        return Response.success(llmRoleService.detailLlmRole(accountId, llmRoleId));
    }

    @PostMapping("/copy")
    public Response<LlmRole> copy(@RequestParam String accountId, @RequestBody LlmRoleCopy copy) {
        log.debug("Start to create llm template copy:{}", copy);
        return Response.success(llmRoleService.copyLlmRole(accountId, copy.getLlmRoleId(), copy.getRename()));
    }


    @PostMapping("/template")
    public Response<LlmRole> createTemplate(@RequestBody RoleWithProject roleWithProject) {
        log.debug("Start to create llm template:{}", roleWithProject);
        LlmRole role = roleWithProject.getRole();
        Project project = roleWithProject.getProject();
        return Response.success(llmRoleService.createLlmRole(role, project, LLM_TEMPLATE_TYPE));
    }

    @PutMapping("/template/{llmRoleId}")
    public Response<LlmRole> updateTemplate(@PathVariable(value = "llmRoleId") String llmRoleId,
                                           @RequestBody RoleWithProject roleWithProject) {
        log.debug("Start to update llm template:{}", roleWithProject);
        LlmRole role = roleWithProject.getRole();
        Project project = roleWithProject.getProject();
        return Response.success(llmRoleService.updateLlmRole(llmRoleId, role, project, LLM_TEMPLATE_TYPE));
    }

    @PostMapping("/template/delete")
    public Response<Void> deleTemplate(@RequestParam String accountId, @RequestBody List<String> llmRoleIds) {
        log.debug("Start to delete llm template:{}", llmRoleIds);
        llmRoleService.batchDeleteRole(accountId, llmRoleIds, LLM_TEMPLATE_TYPE);
        return Response.success(null);
    }

    /**
     * 创建LLM配置
     *
     * @param role 角色信息
     * @return 返回创建结果
     */
    @PostMapping("/config")
    public Response<LlmRole> createLlmConfig(@RequestBody LlmRole role) {
        log.info("Start to create llm config:{}", role);
        return Response.success(llmRoleService.createLlmConfig(role, LLM_CONFIG_TYPE));
    }

    @PutMapping("/config/{llmRoleId}")
    public Response<LlmRole> updateLlmConfig(@PathVariable(value = "llmRoleId") String llmRoleId,
                                           @RequestBody LlmRole role) {
        log.debug("Start to update llm config:{}", role);
        return Response.success(llmRoleService.updateLlmConfig(llmRoleId, role, LLM_CONFIG_TYPE));
    }

    @GetMapping("/config/detail")
    public Response<LlmRole> detailLlmConfig(@RequestParam String llmRoleId) {
        log.debug("start to detail llm config,  llmrole id is:{}", llmRoleId);
        return Response.success(llmRoleService.detailLlmConfig(llmRoleId));
    }

    @PostMapping("/config/delete")
    public Response<Void> deleConfig(@RequestParam String accountId, @RequestBody List<String> llmRoleIds) {
        log.debug("Start to delete llm config:{}", llmRoleIds);
        llmRoleService.batchDeleteRole(accountId, llmRoleIds, LLM_CONFIG_TYPE);
        return Response.success(null);
    }

}