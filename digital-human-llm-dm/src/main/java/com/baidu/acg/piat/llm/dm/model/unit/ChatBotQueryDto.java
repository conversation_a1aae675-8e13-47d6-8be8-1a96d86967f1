package com.baidu.acg.piat.llm.dm.model.unit;

import java.util.List;
import java.util.Map;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 **/
@Data
@ApiModel
public class ChatBotQueryDto {
    @ApiModelProperty(value = "请求id")
    private String queryId;
    @ApiModelProperty(value = "会话id")
    private String sessionId;
    @ApiModelProperty(value = "会话模式（faq，task_based，chitchat，kg，clarify（澄清，需要解析clarify字段），none（澄清失败、系统默认回复等），auto_fill（实体自动填充），system（bot系统事件）、botEvent（bot自定义事件）第三方引擎ID、技能ID)")
    private String source;
    @ApiModelProperty(value = "指定会话模式是否有答案.（多轮会话中，命中顶层节点且节点条件为anything_else，solved为false）")
    private Boolean solved;
    @ApiModelProperty(value = "相似度")
    private Double confidence;
    @ApiModelProperty(value = "回复答案")
    private Answer answer;
    @ApiModelProperty(value = "请求时间，格式:yyyy-MM-dd HH:mm:ss")
    private String queryTime;
    @ApiModelProperty(value = "响应时间，格式:yyyy-MM-dd HH:mm:ss")
    private String answerTime;
    @ApiModelProperty(value = "会话上下文信息")
    private Map<String,Object> context;
    @ApiModelProperty(value = "透传的指令，如打断（interrupted ）")
    private List<String> actions;
    @ApiModelProperty(value = "是否使用webhook")
    private Boolean webhook;
    @ApiModelProperty(value = "多轮是否命中顶层anythingElse节点")
    private Boolean topAnythingElse;
    @ApiModelProperty(value = "多轮是否命中anythingElse节点")
    private Boolean anythingElse;
    @ApiModelProperty(value = "对话框场景状态")
    private String dialogSceneStatus;
}
