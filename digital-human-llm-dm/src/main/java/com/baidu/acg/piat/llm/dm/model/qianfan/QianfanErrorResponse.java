package com.baidu.acg.piat.llm.dm.model.qianfan;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 错误返回
 *
 * <AUTHOR>
 * @date 2023/11/13
 */
@Data
public class QianfanErrorResponse {
    /**
     * 错误码
     */
    @JsonProperty("error_code")
    private Integer errorCode;

    /**
     * 错误描述信息，帮助理解和解决发生的错误
     */
    @JsonProperty("error_msg")
    private String errorMsg;
}
