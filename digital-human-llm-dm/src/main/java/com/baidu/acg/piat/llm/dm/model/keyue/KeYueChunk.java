package com.baidu.acg.piat.llm.dm.model.keyue;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonProperty;

import io.swagger.annotations.ApiModel;
import lombok.Data;

/**
 * "chunkId": 2,
 * "status": "done",
 * "topicId": "63067e968cbdb692c97b063a",
 * "blockId": "gtISYF6lKK_yRUOhgReO8Y0c1F81TWIvSpZH",
 * "nodeId": "lh97QWYdyP14e-5Vgu_Oc1FGcglO_hD4Q9J5",
 * "reply": {
 * "type": 1,
 * "text": "你想好了吗？",
 * "textList": null,
 * "replySource": "TEXT_REPLY",
 * "showDocumentSource": null,
 * "documents": null
 * }
 **/
@Data
@ApiModel
public class KeYueChunk {

    private Integer chunkId;

    private String status;

    private String topicId;

    private String blockId;

    private String nodeId;

    private KeYueReply reply;

    @Data
    public static class KeYueReply {
        private Integer type;
        private String text;
        private String replySource;
        private String showDocumentSource;
        private List<KeYueDocument> documents;
    }

    @Data
    public static class KeYueDocument {
        @JsonProperty("document_id")
        private String documentId;

        @JsonProperty("document_name")
        private String documentName;

        private List<KeYueData> data;
    }

    @Data
    public static class KeYueData {
        @JsonProperty("segment_id")
        private String segmentId;

        @JsonProperty("sentence_id")
        private String sentenceId;

        @JsonProperty("sentence_content")
        private String sentenceContent;

        private String score;

        @JsonProperty("segment_coord")
        private String segmentCoord;
    }

}
