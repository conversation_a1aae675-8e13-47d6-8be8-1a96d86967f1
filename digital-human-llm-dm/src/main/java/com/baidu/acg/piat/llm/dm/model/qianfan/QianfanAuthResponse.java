package com.baidu.acg.piat.llm.dm.model.qianfan;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 鉴权请求
 *
 * <AUTHOR>
 * @date 2023/11/8
 */
@Data
public class QianfanAuthResponse {
    /**
     * 访问凭证
     */
    @JsonProperty("access_token")
    private String accessToken;

    /**
     * 有效期，Access Token的有效期。
     * 说明：单位是秒，有效期30天
     */
    @JsonProperty("expires_in")
    private String expiresIn;

    /**
     * 错误码
     * 说明：响应失败时返回该字段，成功时不返回
     */
    private String error;

    /**
     * 错误描述信息，帮助理解和解决发生的错误
     * 说明：响应失败时返回该字段，成功时不返回
     */
    @JsonProperty("error_description")
    private String errorDescription;
}
