package com.baidu.acg.piat.llm.dm.controller;


import java.util.List;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.baidu.acg.piat.digitalhuman.common.model.Response;
import com.baidu.acg.piat.llm.dm.model.stat.CharacterCountRes;
import com.baidu.acg.piat.llm.dm.model.stat.ConversationCountRes;
import com.baidu.acg.piat.llm.dm.service.LlmStatService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @since 2023/11/23 13:47
 */
@Slf4j
@RestController
@RequestMapping("/api/digitalhuman/v1/llm/stat")
@RequiredArgsConstructor

public class LlmStatController {

    private final LlmStatService llmStatService;

    @GetMapping("/getConversationCount")
    public Response<ConversationCountRes> getConversationCount(@RequestParam String uid) {
        log.debug("Start to get conversation count, uid is {}", uid);
        ConversationCountRes conversationCountRes = llmStatService.getConversationCount(uid);
        log.debug("Conversation count is {}", conversationCountRes.getConversationCount());
        return Response.success(conversationCountRes);
    }

    @GetMapping("/getCharacterCount")
    public Response<List<CharacterCountRes>> getCharacterCount(@RequestParam String uid) {
        log.debug("Start to get character count, uid is {}", uid);
        List<CharacterCountRes> res = llmStatService.getCharacterCount(uid);
        log.debug("Character count res is={}", res);
        return Response.success(res);
    }
}
