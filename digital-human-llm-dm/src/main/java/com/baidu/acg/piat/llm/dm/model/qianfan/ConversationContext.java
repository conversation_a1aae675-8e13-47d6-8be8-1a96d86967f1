package com.baidu.acg.piat.llm.dm.model.qianfan;

import com.baidu.acg.piat.llm.dm.model.LLMEngineCancelable;
import lombok.Data;

import java.time.ZonedDateTime;
import java.util.Collections;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

/**
 * 存储对话上下文
 *
 * <AUTHOR>
 * @date 2023/8/31
 */
@Data
public class ConversationContext {

    /**
     * 会话id
     */
    private String conversationId;

    /**
     * 历史记录
     */
    private List<ChatDetail> dialog = Collections.synchronizedList(new LinkedList<>());

    /**
     * 通过该对象能取消当前会话
     */
    private transient LLMEngineCancelable currentCancelable;

    public enum Speaker {
        USER,
        BOT
    }

    @Data
    public static class ChatDetail {
        /**
         * 说话者
         */
        private Speaker speaker;

        /**
         * 输入内容
         */
        private String content;

        /**
         * 时间
         */
        private ZonedDateTime time;

        /**
         * 额外信息
         */
        private Map<String, Object> extrasInfo;


        public ChatDetail(Speaker speaker, String content, Map<String, Object> extrasInfo) {
            this.speaker = speaker;
            this.content = content;
            this.time = ZonedDateTime.now();
            this.extrasInfo = extrasInfo;
        }

        public ChatDetail(Speaker speaker, String content) {
            this(speaker, content, new HashMap<>(1));
        }
    }

    /**
     * 增加对话内容
     */
    public void addRound(ChatDetail chatDetail) {
        dialog.add(chatDetail);
    }

    /**
     * 取消当前会话
     */
    public void cancelCurrentChat() {
        if (currentCancelable != null) {
            currentCancelable.cancel();
        }
    }

    /**
     * {@inheritDoc}
     * 返回一个字符串，该字符串包含聊天记录的详细信息。
     * 详情请参见{@link Object#toString()}。
     */
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        for (ChatDetail chatDetail : dialog) {
            sb.append(
                    String.format("%s\t%s:%s\n", chatDetail.getTime(), chatDetail.getSpeaker(), chatDetail.getContent()));
        }
        return sb.toString();
    }
}
