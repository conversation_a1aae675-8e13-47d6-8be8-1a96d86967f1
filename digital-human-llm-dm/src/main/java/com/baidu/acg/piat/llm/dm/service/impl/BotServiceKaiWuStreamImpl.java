// Copyright (C) 2019 Baidu Inc. All rights reserved.
package com.baidu.acg.piat.llm.dm.service.impl;

import com.baidu.acg.piat.digitalhuman.common.llmrole.LLMConfig;
import com.baidu.acg.piat.digitalhuman.common.project.BotParams;
import com.baidu.acg.piat.digitalhuman.common.utils.JsonUtil;
import com.baidu.acg.piat.llm.dm.model.PendingTask;
import com.baidu.acg.piat.llm.dm.model.Query;
import com.baidu.acg.piat.llm.dm.model.SessionContext;
import com.baidu.acg.piat.llm.dm.model.kaiwu.KaiwuRequest;
import com.baidu.acg.piat.llm.dm.model.kaiwu.KaiwuResponseBody;
import com.baidu.acg.piat.llm.dm.model.kaiwu.ResponseChoice;
import com.baidu.acg.piat.llm.dm.model.qianfan.ConversationContext;
import com.baidu.acg.piat.llm.dm.model.qianfan.History;
import com.baidu.acg.piat.llm.dm.model.qianfan.Message;
import com.baidu.acg.piat.llm.dm.service.BotProviderService;
import com.baidu.acg.piat.llm.dm.service.LLMConversationSessionContext;
import com.fasterxml.jackson.core.JsonProcessingException;
import io.vavr.control.Try;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.util.CollectionUtils;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.Disposable;
import reactor.core.publisher.Flux;

import java.nio.charset.StandardCharsets;
import java.time.Duration;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@RequiredArgsConstructor
@Slf4j
public class BotServiceKaiWuStreamImpl implements BotProviderService {

    WebClient webClient = WebClient.builder().build();

    public static final String BOT_TYPE = BotParams.BotTypeEnum.KAI_WU.getBotType();

    public static final String PROMPT = "prompt";

    private LLMConversationSessionContext llmConversationSessionContext;

    public BotServiceKaiWuStreamImpl(LLMConversationSessionContext llmConversationSessionContext) {
        this.llmConversationSessionContext = llmConversationSessionContext;
    }


    /**
     * 只用于http接口测试
     *
     * @param sessionContext
     * @param query
     * @param llmConfig
     * @return
     */
    public Flux<String> queryKaiwuWithReturn(SessionContext sessionContext, Query query, LLMConfig llmConfig) {
        // TODO
        KaiwuRequest kaiwuRequest = new KaiwuRequest();
        Message kaiwuMessage = new Message();
        kaiwuMessage.setRole(Message.RoleEnum.user);
        kaiwuMessage.setContent(query.getText());
        kaiwuRequest.setMessages(List.of(kaiwuMessage));
        kaiwuRequest.setQuery(query.getText());
        Map<String, Object> inputParams = new HashMap<>();
        if (llmConfig.getParams() != null) {
            inputParams.putAll(llmConfig.getParams());
        }

        if (query.getContext() != null) {
            inputParams.putAll(query.getContext());
        }

        log.info("Request Kaiwu:{}", kaiwuRequest);
        try {
            Flux<String> responseFlux = webClient.post()
                    .uri(llmConfig.getUrl()) // 你的端点
                    .headers(header -> header.addAll(getHeaders(llmConfig)))
                    .bodyValue(kaiwuRequest)
                    .acceptCharset(StandardCharsets.UTF_8)
                    .accept(MediaType.TEXT_EVENT_STREAM)
                    .retrieve()
                    .bodyToFlux(String.class);
            responseFlux.onErrorResume(e -> {
                log.error("Kaiwu error 2:{}", e.getMessage());
                return Flux.empty();
            }).subscribe(response -> {
                log.info("Kaiwu Response:{}", response);
            });
            return responseFlux;
        } catch (Exception e) {
            log.error("Kaiwu error 1:{}", e.getMessage());
            return Flux.empty();
        }
    }

    /**
     * 查询数据。
     *
     * @param sessionContext 会话上下文
     * @param query          查询条件
     */
    @SneakyThrows
    @Override
    public void query(SessionContext sessionContext, Query query) {
        LLMConfig llmConfig = sessionContext.getLlmConfig();
        Result historyResult = getHistory(sessionContext);

        // 构建开物请求，目前请求的messeges参数对应/completions的接口，请求的query参数对应/tool的接口
        KaiwuRequest kaiwuRequest = buildKaiWuRequest(sessionContext, query, llmConfig, historyResult);
        addHistory(sessionContext, query.getText(), true);

        String url = llmConfig.getUrl();
        log.info("Request KaiWu url:{}, body:{}, token={}", url, JsonUtil.writeValueAsString(kaiwuRequest),
                llmConfig.getCredential().get("token"));

        Flux<String> responseFlux = webClient.post()
                .uri(url) // 你的端点
                .headers(header -> header.addAll(getHeaders(llmConfig)))
                .bodyValue(kaiwuRequest)
                .acceptCharset(StandardCharsets.UTF_8)
                .accept(MediaType.TEXT_EVENT_STREAM)
                .retrieve()
                .bodyToFlux(String.class); // 你的返回类型

        handleResponse(responseFlux, sessionContext, query, historyResult.context);
    }

    /**
     * 构建千帆请求
     */
    private KaiwuRequest buildKaiWuRequest(SessionContext sessionContext, Query query, LLMConfig llmConfig,
                                           Result result) throws JsonProcessingException {
        KaiwuRequest kaiwuRequest = new KaiwuRequest();

        List<Message> history = result.histories.stream().flatMap(h -> {
            String answer = h.getAnswer();
            String q = h.getQuery();
            Message userMsg = new Message(Message.RoleEnum.user, q);
            Message botMsg = new Message(Message.RoleEnum.assistant, answer);
            return Stream.of(userMsg, botMsg);
        }).collect(Collectors.toList());
        String prompt = "";
        if (llmConfig.getParams() != null && llmConfig.getParams().containsKey(PROMPT)) {
            prompt = llmConfig.getParams().get(PROMPT).toString();
        }
        List<Message> requestMessages = new ArrayList<>();
        if (!CollectionUtils.isEmpty(history)) {
            requestMessages.addAll(history);
        }
        requestMessages.add(new Message(Message.RoleEnum.user, prompt + query.getText()));
        kaiwuRequest.setMessages(requestMessages);
        log.debug("kaiwu request:{}", JsonUtil.writeValueAsString(kaiwuRequest));
        // 其他参数

        // 暂时没用到额外的参数
//        Map<String, Object> inputParams = new HashMap<>();
//        if (llmConfig.getParams() != null) {
//            inputParams.putAll(llmConfig.getParams());
//        }
//        if (query.getContext() != null) {
//            inputParams.putAll(query.getContext());
//        }
        kaiwuRequest.setQuery(query.getText());
        kaiwuRequest.setStream(true);
        log.info("KaiWu request={}, sessionId={}", kaiwuRequest, sessionContext.getSessionId());
        return kaiwuRequest;
    }


    @Override
    public void reset(SessionContext sessionContext) {
        sessionContext.setCopilotConversationId(StringUtils.EMPTY);
    }

    @Override
    public void clear(SessionContext sessionContext) {
        if (sessionContext != null && !StringUtils.isEmpty(sessionContext.getDialogId())) {
            llmConversationSessionContext.clearContext(sessionContext.getDialogId());
        }
    }

    private void handleResponse(Flux<String> responseFlux, SessionContext sessionContext, Query query,
                                ConversationContext context) {
        StringBuffer resultText = new StringBuffer();
        Disposable disposable = responseFlux
                .timeout(Duration.ofSeconds(sessionContext.getLlmConfig().getTimeout()))
                .doOnCancel(() -> {
                    log.debug("Cancelled, query={}", query);
                    sessionContext.getLlmStreamObserver().onCancel(query);
                }).doOnError(e -> {
                    // 打印错误
                    log.error("Handle response sessionId={}, requestId={}, Error", sessionContext.getSessionId(),
                            query.getRequestId(), e);
                    sessionContext.getLlmStreamObserver().onError(e, query);

                }).doOnComplete(() -> {
                    sessionContext.getLlmStreamObserver().onCompleted(query);
                    if (resultText.length() > 0) {
                        addHistory(sessionContext, resultText.toString(), false);
                    }
                    // 打印结束
                    log.info("Hanlde response sessionId={}, requestId={} Complete", sessionContext.getSessionId(),
                            query.getRequestId());
                }).subscribe(response -> {
                    // 打印每个接收到的chunk
                    log.info("Received chunk: {}", response);

                    // 直接请求kaiwu的单体应用
                    Try.of(() -> JsonUtil.readValue(response, KaiwuResponseBody.class))
                            .onFailure(e -> log.error("Parse response error:{}", e.getMessage()))
                            .onSuccess(r -> {
                                if (r != null && !StringUtils.isEmpty(r.getConversationId())) {
                                    sessionContext.setCopilotConversationId(r.getConversationId());
                                }
                                // 目前只支持单轮对话
                                if (r != null && !CollectionUtils.isEmpty(r.getChoices())) {
                                    // todo 没有调试过compelete接口，这里根据后面接口协议调整
                                    for (ResponseChoice choice : r.getChoices()) {
                                        if (!StringUtils.isEmpty(choice.getDelta().getContent())) {
                                            resultText.append(choice.getDelta().getContent());
                                            sessionContext.getLlmStreamObserver().onNext(choice.getDelta().getContent(),
                                                    "largeModel", query);
                                        }
                                    }
                                }
                            });
                });

        sessionContext.getPendingQueryMap().put(query.getRequestId(),
                PendingTask.builder().queryIndex(query.getQueryIndex()).disposable(disposable).build());

    }

    /**
     * @return
     */
    public void interrupt(SessionContext sessionContext, String taskId) {
        if (sessionContext.getPendingQueryMap().isEmpty()) {
            return;
        }
        // 清理一下过期的taskId；
        sessionContext.getInterruptedMap().entrySet().stream().filter(e -> {
            return System.currentTimeMillis() - e.getValue() > 1000 * 60 * 5;
        }).forEach(e -> {
            log.info("Interrupt timeout interrupted taskId={}", e.getKey());
            sessionContext.getInterruptedMap().remove(e.getKey());
        });
        // 批量删除
        if (StringUtils.isEmpty(taskId)) {
            log.info("Interrupt all={}", sessionContext.getPendingQueryMap().keySet());
            sessionContext.getPendingQueryMap().forEach((k, v) -> {
                v.getDisposable().dispose();
            });
            int i = 0;
            // 判断是否真的中断, 一次性给所有的pending发中断指令
            while (sessionContext.getPendingQueryMap().keySet().stream().anyMatch(k -> {
                log.info("Interrupt add interrupted sessionId={}, taskId={}", sessionContext.getSessionId(), k);
                sessionContext.getInterruptedMap().put(k, System.currentTimeMillis());
                return !sessionContext.getPendingQueryMap().get(k).getDisposable().isDisposed();
            }) && i < 3) {
                try {
                    Thread.sleep(100);
                } catch (InterruptedException e) {
                    log.error("Interrupt error", e);
                }
                i++;
            }
            sessionContext.getPendingQueryMap().keySet().forEach(k -> {
                clearTask(k, sessionContext);
            });
            return;
        }
        PendingTask pendingTask = sessionContext.getPendingQueryMap().get(taskId);
        if (pendingTask != null) {
            log.info("Interrupt sessionId={}, taskId={}", sessionContext.getSessionId(), taskId);
            // 会有延迟不一定立马中断
            pendingTask.getDisposable().dispose();
            sessionContext.getInterruptedMap().put(taskId, System.currentTimeMillis());
            int i = 0;
            // 判断是否真的中断
            while (!pendingTask.getDisposable().isDisposed() && i < 3) {
                try {
                    Thread.sleep(100);
                } catch (InterruptedException e) {
                    log.error("Interrupt error", e);
                }
                i++;
            }
            clearTask(taskId, sessionContext);
        }
    }


    public void clearTask(String taskId, SessionContext sessionContext) {
        log.debug("Clear task id={}", taskId);
        if (StringUtils.isEmpty(taskId)) {
            return;
        }
        sessionContext.getPendingQueryMap().remove(taskId);
        sessionContext.getPendingResponseSeqMap().remove(taskId);
        sessionContext.getPendingResponseMap().remove(taskId);
    }

    private static class Result {
        public final ConversationContext context;
        public final List<History> histories;

        public Result(ConversationContext context, List<History> histories) {
            this.context = context;
            this.histories = histories;
        }
    }

    public void addHistory(SessionContext sessionContext, String text, boolean isUser) {
        String dialogId = sessionContext.getDialogId();
        if (StringUtils.isEmpty(dialogId)) {
            dialogId = llmConversationSessionContext.generateId();
            sessionContext.setDialogId(dialogId);
        }
        ConversationContext context = llmConversationSessionContext.getContextOrCreate(dialogId);
        // 只保留10轮对话,防止过大
        if (context.getDialog() != null && context.getDialog().size() > 10) {
            context.getDialog().remove(0);
            context.getDialog().remove(1);
        }
        if (isUser) {
            ConversationContext.ChatDetail detail = new ConversationContext.ChatDetail(ConversationContext.Speaker.USER,
                    text);
            context.addRound(detail);
        } else {
            ConversationContext.ChatDetail detail = new ConversationContext.ChatDetail(ConversationContext.Speaker.BOT,
                    text);
            context.addRound(detail);
        }
        log.debug("Add history ={}, sessionId={}, dialogId={}, isUser={}",
                text, sessionContext.getSessionId(), dialogId, isUser);
    }

    private Result getHistory(SessionContext sessionContext) {
        String dialogId = sessionContext.getDialogId();
        if (StringUtils.isEmpty(dialogId)) {
            dialogId = llmConversationSessionContext.generateId();
            sessionContext.setDialogId(dialogId);
        }
        ConversationContext context = llmConversationSessionContext.getContextOrCreate(dialogId);
        List<ConversationContext.ChatDetail> dialog = context.getDialog();
        List<History> histories = new ArrayList<>();
        for (int i = 0; i < dialog.size() - 1; ) {
            History history = new History();
            if (ConversationContext.Speaker.USER.equals(dialog.get(i).getSpeaker())
                    && ConversationContext.Speaker.BOT.equals(dialog.get(i + 1).getSpeaker())) {
                history.setQuery(dialog.get(i).getContent());
                history.setAnswer(dialog.get(i + 1).getContent());
                histories.add(history);
                i = i + 2;
            } else {
                i++;
            }
        }
        log.debug("Get history result={}", histories);
        return new Result(context, histories);
    }


    private HttpHeaders getHeaders(LLMConfig llmConfig) {
        HttpHeaders headers = new HttpHeaders();
        headers.add("app-code", llmConfig.getCredential().get("token"));
        headers.add("Content-Type", "application/json;charset=UTF-8");
        return headers;
    }

    @Override
    public String getBotType() {
        return BOT_TYPE;
    }
}
