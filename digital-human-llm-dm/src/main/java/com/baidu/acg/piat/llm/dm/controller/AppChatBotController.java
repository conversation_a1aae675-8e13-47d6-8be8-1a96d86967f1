package com.baidu.acg.piat.llm.dm.controller;

import java.util.UUID;

import com.baidu.acg.piat.digitalhuman.common.llmrole.LLMConfig;
import com.baidu.acg.piat.llm.dm.client.HttpCommonClient;
import com.baidu.acg.piat.llm.dm.configure.CommonConfig;
import com.baidu.acg.piat.llm.dm.configure.QianfanLLMEngineConfiguration;
import com.baidu.acg.piat.llm.dm.dao.LlmHistoryRepository;
import com.baidu.acg.piat.llm.dm.service.LLMBillingService;
import com.baidu.acg.piat.llm.dm.service.LLMConversationSessionContext;
import com.baidu.acg.piat.llm.dm.service.Text2GestureService;
import com.baidu.acg.piat.llm.dm.service.impl.BotServiceAppBuilderStreamImpl;
import com.baidu.acg.piat.llm.dm.service.impl.BotServiceKaiWuStreamImpl;
import com.baidu.acg.piat.llm.dm.service.impl.BotServiceQianfanStreamImpl;
import com.baidu.acg.piat.llm.dm.service.impl.LLmStreamObserverImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.baidu.acg.piat.llm.dm.model.QueryWithModel;
import com.baidu.acg.piat.llm.dm.model.SessionContext;
import com.baidu.acg.piat.llm.dm.model.unit.ChatBotQueryDto;
import com.baidu.acg.piat.llm.dm.service.BotProviderService;
import com.baidu.acg.piat.llm.dm.service.impl.BotServiceCopilotStreamImpl;
import com.baidu.acg.piat.llm.dm.service.impl.BotServiceUnitStreamImpl;

import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.reactive.function.client.WebClient;

import okhttp3.OkHttpClient;
import reactor.core.publisher.Flux;

import javax.annotation.Resource;

/**
 *
 **/
@RestController
@RequestMapping("/api/v1/digitalhuman/llm/chat")
@RequiredArgsConstructor
@Slf4j
public class AppChatBotController {

    @Resource(name = "qianfanClient")
    private WebClient webClient;

    @Resource
    private QianfanLLMEngineConfiguration.QianfanLLMConfig qianfanLLMConfig;

    @Resource
    private LLMConversationSessionContext llmConversationSessionContext;

    private final HttpCommonClient httpCommonClient;

    private final Text2GestureService text2GestureService;

    private final CommonConfig config;

    private final LLMBillingService llmBillingService;

    private final OkHttpClient okHttpClient;

    private final LlmHistoryRepository llmHistoryRepository;

    @ApiOperation(value = "会话中控", notes = "会话中控接口，传入问题，从机器人那获取答案，作者：罗锦江")
    @PostMapping(value = "/core/query", produces = MediaType.APPLICATION_JSON_VALUE)
    public Flux<ChatBotQueryDto> coreQuery(@RequestBody QueryWithModel param) {
        BotProviderService botProviderService = new BotServiceUnitStreamImpl(config);
        return botProviderService.queryWithReturn(new SessionContext(), param.getQuery(), param.getModel());
    }

    @ApiOperation(value = "会话中控", notes = "会话中控接口，传入问题，从机器人那获取答案，作者：罗锦江")
    @PostMapping(value = "/core/dify/query", produces = MediaType.APPLICATION_JSON_VALUE)
    public String coreDifyQuery(@RequestBody QueryWithModel param) {
        BotProviderService botProviderService = new BotServiceCopilotStreamImpl();
        SessionContext sessionContext = new SessionContext();
        sessionContext.setSessionId(UUID.randomUUID().toString());
        botProviderService.queryDifyWithReturn(sessionContext, param.getQuery(), param.getModel());
        return "ok";
    }

    @ApiOperation(value = "会话中控", notes = "会话中控接口，传入问题，从机器人那获取答案，作者：罗锦江")
    @PostMapping(value = "/core/qianfan/query", produces = MediaType.APPLICATION_JSON_VALUE)
    public String coreQianfanQuery(@RequestBody QueryWithModel param) {
        log.info("CoreQianfanQuery request={}", param);
        BotProviderService botProviderService = new BotServiceQianfanStreamImpl(llmConversationSessionContext,
                webClient, httpCommonClient);
        SessionContext sessionContext = new SessionContext();
        sessionContext.setSessionId(UUID.randomUUID().toString());
        sessionContext.setDialogId("y78e21bd812y7e8w");
        sessionContext.setLlmConfig(new LLMConfig());
        sessionContext.setLlmStreamObserver(new LLmStreamObserverImpl(sessionContext, text2GestureService, config,
                llmBillingService, llmHistoryRepository));
        botProviderService.queryQianfanWithReturn(sessionContext, param.getQuery(), param.getModel());
        return "ok";
    }

    @ApiOperation(value = "会话中控", notes = "会话中控接口，传入问题，从机器人那获取答案，作者：罗锦江")
    @PostMapping(value = "/core/kaiwu/query", produces = MediaType.APPLICATION_JSON_VALUE)
    public String coreKaiwuQuery(@RequestBody QueryWithModel param) {
        log.info("CoreQianfanQuery request={}", param);
        BotProviderService botProviderService = new BotServiceKaiWuStreamImpl(llmConversationSessionContext);
        SessionContext sessionContext = new SessionContext();
        sessionContext.setSessionId(UUID.randomUUID().toString());
        botProviderService.queryKaiwuWithReturn(sessionContext, param.getQuery(), param.getModel());
        return "ok";
    }

    @ApiOperation(value = "会话中控", notes = "会话中控接口，传入问题，从机器人那获取答案，作者：罗锦江")
    @PostMapping(value = "/core/appbuilder/query", produces = MediaType.APPLICATION_JSON_VALUE)
    public Flux<String> appBuilderQuery(@RequestBody QueryWithModel param) {
        log.info("CoreQianfanQuery request={}", param);
        BotProviderService botProviderService = new BotServiceAppBuilderStreamImpl(okHttpClient);
        SessionContext sessionContext = new SessionContext();
        sessionContext.setSessionId(UUID.randomUUID().toString());
        sessionContext.setLlmConfig(param.getModel());
        botProviderService.init(sessionContext);
        return botProviderService.queryAppBuilderWithReturn(sessionContext, param.getQuery(), param.getModel());
    }

}
