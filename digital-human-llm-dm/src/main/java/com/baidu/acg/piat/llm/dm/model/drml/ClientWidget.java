package com.baidu.acg.piat.llm.dm.model.drml;

import com.baidu.acg.piat.digitalhuman.common.llmrole.Guide;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlElementWrapper;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlRootElement;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * {
 *   "client": {
 *     "widget": {
 *       "type": "openingBubble",
 *       "data": {
 *         "guide": {
 *           "text": "现在有⼀份百度云智⼤会专享福利请您查收",
 *           "linkUrl": "http://www.baidu.com?k1=v1&amp;amp;k2=v2",
 *           "imgUrl": "aaaa"
 *         },
 *         "recommendedQuestions": [
 *           {
 *             "text": "曦灵数字人可以用在哪些场景？"
 *           },
 *           {
 *             "text": "曦灵文生3D数字人价格是多少？"
 *           }
 *         ]
 *       }
 *     }
 *   }
 * }
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@JacksonXmlRootElement(localName = "client")
public class ClientWidget {

    Widget widget;

    @lombok.Data
    public static class Widget {
        private String type;
        private Data data;
    }

    @lombok.Data
    public static class Data {
        private Guide guide;
        // 使用 @JacksonXmlElementWrapper 配置列表的序列化行为，禁用根元素包装
        @JacksonXmlElementWrapper(useWrapping = false)
        @JacksonXmlProperty(localName = "recommendedQuestions")
        private List<RecommendedQuestion> recommendedQuestions;
    }

    @lombok.Data
    public static class RecommendedQuestion {
        private String text;
    }


}
