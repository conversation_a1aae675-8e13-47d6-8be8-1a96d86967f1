package com.baidu.acg.piat.llm.dm.dao;

import com.baidu.acg.piat.llm.dm.model.LlmRoleTemplateModel;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.repository.PagingAndSortingRepository;

import java.util.List;
import java.util.Optional;

public interface LlmRoleTemplateRepository extends PagingAndSortingRepository<LlmRoleTemplateModel, Long> {
    Optional<LlmRoleTemplateModel> findByLlmRoleTemplateId(String llmRoleTemplateId);
    List<LlmRoleTemplateModel> findByLlmRoleTemplateIdIn(List<String> templateIds);
    Page<LlmRoleTemplateModel> findByTemplateTypeAndTemplateNameContainingAndScreenTypeAndDelOrderByCreateTimeDesc(
            Integer templateType, String name, String screenType, boolean del, Pageable pageable);
    Page<LlmRoleTemplateModel> findByTemplateTypeAndTemplateNameContainingAndDelOrderByCreateTimeDesc(
            Integer templateType, String name, boolean del, Pageable pageable);
}
