package com.baidu.acg.piat.llm.dm.configure;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/12/26 19:20
 */

@Configuration
@Data
public class KnowledgeBaseConfig {

    @Value("${digitalhuman.llmdm.knowledge.base.url:http://180.76.177.169/v1/api/datasets}")
    private String prefix ;

    @Value("${digitalhuman.llmdm.knowledge.base.token:Bearer app-0UpgInBTkKB6tJ6xjn8PIc6s}")
    private String token;

    @Value("${digitalhuman.llmdm.knowledge.base.bceId:c4a91d4d29dc436ca96e4faaa7abb5c5}")
    private String bceId;

    @Value("${digitalhuman.llmdm.knowledge.base.fileNum:10}")
    private Integer maxFileNum;

    @Value("${digitalhuman.llmdm.knowledge.base.supportFileType:txt,pdf,doc,docx}")
    private String supportFileType;

    @Value("${digitalhuman.llmdm.knowledge.base.packageConfig.targetLength:200}")
    private Integer targetLength;

    @Value("${digitalhuman.llmdm.knowledge.base.packageConfig.overlap:0}")
    private Integer overlap;

    @Value("${digitalhuman.llmdm.knowledge.base.packageConfig.minSentenceCount:3}")
    private Integer minSentenceCount;

    @Value("${digitalhuman.llmdm.knowledge.base.packageConfig.xmindUrl:http://10.180.113.65:8366/xmind/parser}")
    private String xmindUrl;

    @Value("${digitalhuman.llmdm.knowledge.base.packageConfig.separators:\n,。}")
    private List<String> separators;

}
