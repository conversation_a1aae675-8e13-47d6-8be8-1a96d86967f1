package com.baidu.acg.piat.llm.dm.websocket.impl;

import java.util.UUID;

import org.springframework.stereotype.Component;

import com.alibaba.excel.util.StringUtils;
import com.baidu.acg.piat.digitalhuman.common.alita.ActionType;
import com.baidu.acg.piat.digitalhuman.common.llmdm.RefreshBody;
import com.baidu.acg.piat.digitalhuman.common.llmdm.TextRenderBody;
import com.baidu.acg.piat.digitalhuman.common.llmdm.WebsocketLlmRequest;
import com.baidu.acg.piat.digitalhuman.common.llmdm.WebsocketLlmResponse;
import com.baidu.acg.piat.digitalhuman.common.llmrole.StatusType;
import com.baidu.acg.piat.digitalhuman.common.utils.JsonUtil;
import com.baidu.acg.piat.llm.dm.model.SessionContext;
import com.baidu.acg.piat.llm.dm.utils.WebsocketUtil;
import com.baidu.acg.piat.llm.dm.websocket.RequestReactor;

import io.vavr.control.Try;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
@RequiredArgsConstructor
public class RefreshDialogRequestReactor implements RequestReactor {

    @Override
    public void react(SessionContext sessionContext, WebsocketLlmRequest request) {
        log.info("Refresh dialog, sessionId={}, request={}", sessionContext.getSessionId(), request);
        RefreshBody refreshBody = Try.of(() -> JsonUtil.readValue(request.getBody(),
                RefreshBody.class)).getOrNull();
        if (refreshBody == null) {
            log.warn("RefreshBody is null, sessionId={}", sessionContext.getSessionId());
            return;
        }
        sessionContext.setDialogId(refreshBody.getDialogId());
        if (sessionContext.getBotProviderService() == null) {
            sessionContext.getBotProviderService().reset(sessionContext);
        }

        if (sessionContext.getLlmRoleModel().getOpeningStatement() != null
                && sessionContext.getLlmRoleModel().getOpeningStatement().getStatus() == StatusType.OPEN.code()
                && !StringUtils.isEmpty(sessionContext.getLlmRoleModel().getOpeningStatement().getStatement())) {
            WebsocketUtil.send(sessionContext.getWebSocketSession(), WebsocketLlmResponse.builder()
                    .action(ActionType.TEXT_RENDER)
                    .requestId(request.getRequestId())
                    .body(TextRenderBody.toBuild(UUID.randomUUID().toString(),
                            sessionContext.getLlmRoleModel().getOpeningStatement().getStatement(),
                            true, 0, sessionContext.getSessionId(), true))
                    .code(0)
                    .message("")
                    .build());
        }
    }

    @Override
    public boolean support(ActionType actionType) {
        return actionType == ActionType.REFRESH_DIALOG;
    }
}
