package com.baidu.acg.piat.llm.dm.model.copilot;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DifyRequest {
    Map<String, Object> inputs;
    String query;

    @JsonProperty(value = "response_mode")
    String responseMode;

    @JsonProperty(value = "conversation_id")
    String conversationId;

    String user;

    // actions用于DTE的配置，preActions用于单体应用, 两者是一样的
    List<CopilotAction> actions = new ArrayList<>();

    private List<String> toolset = List.of("search_db");

    @JsonProperty(value = "pre_action")
    private List<CopilotAction> preAction;
}
