package com.baidu.acg.piat.llm.dm.controller;

import java.util.List;

import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.baidu.acg.piat.llm.dm.model.chat.LlmQuery;
import com.baidu.acg.piat.llm.dm.model.chat.LlmResult;
import com.baidu.acg.piat.llm.dm.service.ChatService;

import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Flux;

/**
 *
 * 只是测试使用
 **/
@RestController
@RequestMapping("/api/digitalhuman/v1/llm")
@RequiredArgsConstructor
@Slf4j
public class ChatController {

    private final ChatService chatService;
    @ApiOperation(value = "会话中控", notes = "会话中控")
    @PostMapping(value = "/chat", produces = MediaType.APPLICATION_STREAM_JSON_VALUE)
    public Flux<LlmResult> chat(@RequestBody LlmQuery llmQuery) {
        log.info("Chat request body={}", llmQuery);
        return chatService.chat(llmQuery);
    }

    @ApiOperation(value = "会话中控同步调用", notes = "会话中控同步调用")
    @PostMapping(value = "/chat/sync", produces = MediaType.APPLICATION_STREAM_JSON_VALUE)
    public List<LlmResult> chatSync(@RequestBody LlmQuery llmQuery) {
        log.info("Chat request body={}", llmQuery);
        return chatService.chat(llmQuery).collectList().block();
    }

}
