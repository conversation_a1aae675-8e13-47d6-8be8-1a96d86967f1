package com.baidu.acg.piat.llm.dm.model.aida;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class WorkflowResponse {

    private String sessionId;

    private String requestId;

    /**
     * 用户语音的文本
     */
    private String reply;


    /**
     * leaflet想传递一些参数给ngd
     */
    private String context;

}
