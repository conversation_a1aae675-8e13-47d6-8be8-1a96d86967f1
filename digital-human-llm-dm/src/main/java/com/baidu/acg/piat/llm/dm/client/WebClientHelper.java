package com.baidu.acg.piat.llm.dm.client;

import lombok.extern.slf4j.Slf4j;
import org.springframework.web.reactive.function.client.ExchangeFilterFunction;
import reactor.core.publisher.Mono;

import java.util.List;
import java.util.function.Consumer;

/**
 * webclient tools
 *
 * <AUTHOR>
 * @date 2023/11/8
 */
@Slf4j
public class WebClientHelper {
    public static ExchangeFilterFunction logRequest() {
        return ExchangeFilterFunction.ofRequestProcessor(request -> {
            log.debug("webclient request url: {}, headers:{}", request.url(), request.headers());
            return Mono.just(request);
        });
    }

    private static ExchangeFilterFunction logResponse() {
        return ExchangeFilterFunction.ofResponseProcessor(response -> {
            if (response.statusCode().is4xxClientError() || response.statusCode().is5xxServerError()) {
                return response.bodyToMono(String.class)
                               .flatMap(body -> {
                                   log.debug("webclient response code:{}", response.statusCode());
                                   return Mono.just(response);
                               });
            } else {
                return Mono.just(response);
            }
        });
    }

    /**
     * 一些常用的filter
     */
    public static Consumer<List<ExchangeFilterFunction>> logFiltersFunction = exchangeFilterFunctions -> {
        exchangeFilterFunctions.add(logRequest());
        exchangeFilterFunctions.add(logResponse());
    };
}
