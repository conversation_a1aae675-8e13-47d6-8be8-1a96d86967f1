package com.baidu.acg.piat.llm.dm.websocket.impl;

import org.springframework.stereotype.Component;

import com.baidu.acg.piat.digitalhuman.common.alita.ActionType;
import com.baidu.acg.piat.digitalhuman.common.llmdm.InterruptBody;
import com.baidu.acg.piat.digitalhuman.common.llmdm.TextRenderBody;
import com.baidu.acg.piat.digitalhuman.common.llmdm.WebsocketLlmRequest;
import com.baidu.acg.piat.digitalhuman.common.llmdm.WebsocketLlmResponse;
import com.baidu.acg.piat.digitalhuman.common.utils.JsonUtil;
import com.baidu.acg.piat.llm.dm.constants.LLMConstants;
import com.baidu.acg.piat.llm.dm.model.SessionContext;
import com.baidu.acg.piat.llm.dm.utils.WebsocketUtil;
import com.baidu.acg.piat.llm.dm.websocket.RequestReactor;

import io.vavr.control.Try;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
@RequiredArgsConstructor
public class InterruptRequestReactor implements RequestReactor {

    @Override
    public void react(SessionContext sessionContext, WebsocketLlmRequest request) {
        log.info("TextQueryRequestReactor, sessionId={}, request={}", sessionContext.getSessionId(), request);
        if (sessionContext.getBotProviderService() == null) {
            log.warn("BotProviderService is null, sessionId={}", sessionContext.getSessionId());
            // 直接发送结束drml给上游
            return;
        }
        InterruptBody interruptBody = Try.of(() -> JsonUtil.readValue(request.getBody(),
                InterruptBody.class)).getOrNull();
        sessionContext.getBotProviderService().interrupt(sessionContext,
                interruptBody == null ? null : interruptBody.getQueryId());
        WebsocketUtil.send(sessionContext.getWebSocketSession(), WebsocketLlmResponse.builder()
                .action(ActionType.TEXT_RENDER)
                .requestId(request.getRequestId())
                .body(TextRenderBody.toBuild(request.getRequestId(),
                        LLMConstants.INTERRUPT_DRML,
                        true, 0, sessionContext.getSessionId(), true))
                .code(0)
                .message("interrupt")
                .build());

    }

    @Override
    public boolean support(ActionType actionType) {
        return actionType == ActionType.INTERRUPT;
    }
}
