package com.baidu.acg.piat.llm.dm.error;

/**
 * 业务自定义异常类
 *
 * <AUTHOR>
 * @date 2023/11/2
 */
public class LLMAdaptorException extends RuntimeException {
    static final long serialVersionUID = -1021827130243444936L;

    public BizError getBizError() {
        return bizError;
    }

    public String getDesc() {
        return desc;
    }

    private BizError bizError;

    private String desc;

    public LLMAdaptorException(String msg) {
        super(msg);
        this.bizError = BizError.UNKNOWN_INTERNAL_ERROR;
        this.desc = msg;
    }

    public LLMAdaptorException(Throwable t) {
        super(t);
        this.bizError = BizError.UNKNOWN_INTERNAL_ERROR;
        this.desc = t.getLocalizedMessage();
    }

    public LLMAdaptorException(BizError bizError) {
        super(bizError.getMessage());
        this.bizError = bizError;
    }

    public LLMAdaptorException(BizError bizError, String message) {
        super(message);
        this.bizError = bizError;
    }

}
