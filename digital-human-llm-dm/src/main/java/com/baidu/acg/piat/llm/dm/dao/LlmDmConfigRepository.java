// Copyright (C) 2019 Baidu Inc. All rights reserved.
package com.baidu.acg.piat.llm.dm.dao;

import java.util.Optional;

import org.springframework.data.repository.PagingAndSortingRepository;

import com.baidu.acg.piat.llm.dm.model.db.LlmDmConfigModel;

/**
 */
public interface LlmDmConfigRepository extends PagingAndSortingRepository<LlmDmConfigModel, Long> {

    Optional<LlmDmConfigModel> findById(String id);


    Optional<LlmDmConfigModel> findByToken(String token);


}
