package com.baidu.acg.piat.llm.dm.service.impl;

import com.baidu.acg.piat.digitalhuman.common.exception.DigitalHumanCommonException;
import com.baidu.acg.piat.digitalhuman.common.model.PageResponse;
import com.baidu.acg.piat.digitalhuman.common.utils.IDGenerator;
import com.baidu.acg.piat.llm.dm.dao.LlmRoleTemplateRepository;
import com.baidu.acg.piat.llm.dm.model.LlmRoleTemplateModel;
import com.baidu.acg.piat.llm.dm.service.LlmTemplateService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Slf4j
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class LlmTemplateServiceImpl implements LlmTemplateService {
    private final LlmRoleTemplateRepository llmRoleTemplateRepository;
    private IDGenerator idGenerator = new IDGenerator(new IDGenerator.IDGeneratorConfig());

    /**
     * 创建LlmRoleTemplateModel对象
     *
     * @param llmRoleTemplate LlmRoleTemplateModel对象，用于保存创建的模板信息
     * @return 创建的LlmRoleTemplateModel对象
     */
    @Override
    public LlmRoleTemplateModel createLlmTemplate(LlmRoleTemplateModel llmRoleTemplate) {
        LlmRoleTemplateModel template = new LlmRoleTemplateModel();
        String templateId = idGenerator.generate();
        BeanUtils.copyProperties(llmRoleTemplate, template);
        template.setLlmRoleTemplateId(templateId);
        llmRoleTemplateRepository.save(template);
        log.debug("create llm template {} success!", template);
        return template;
    }

    /**
     * 批量删除模板
     *
     * @param llmTemplateIds 模板ID列表
     * @throws DigitalHumanCommonException 如果删除失败，则抛出异常
     */
    @Override
    public void batchDeleteTemplate(List<String> llmTemplateIds) {
        List<LlmRoleTemplateModel> llmTemplates = llmRoleTemplateRepository.findByLlmRoleTemplateIdIn(llmTemplateIds);
        if (llmTemplates.size() != llmTemplateIds.size()) {
            throw new DigitalHumanCommonException("删除失败，不存在该模板");
        }
        llmTemplates.forEach(template -> {
            try {
                log.debug("start delete template id is  {}", template.getLlmRoleTemplateId());
                llmRoleTemplateRepository.delete(template);
            } catch (Exception e) {
                throw new DigitalHumanCommonException("删除模板失败", e);
            }
        });
    }

    /**
     * 更新LlmRoleTemplateModel对象
     *
     * @param llmTemplate LlmRoleTemplateModel对象
     * @return 更新后的LlmRoleTemplateModel对象
     * @throws DigitalHumanCommonException 如果模板不存在
     */
    @Override
    public LlmRoleTemplateModel updateLlmTemplate(LlmRoleTemplateModel llmTemplate) {
        LlmRoleTemplateModel updated;
        String llmTemplateId = llmTemplate.getLlmRoleTemplateId();
        Optional<LlmRoleTemplateModel> template = llmRoleTemplateRepository.findByLlmRoleTemplateId(llmTemplateId);
        if (template.isEmpty()) {
            throw new DigitalHumanCommonException("该模板不存在");
        }
        updated = template.get();
        updated.copy(llmTemplate);
        log.debug("update llm template and updated template is {}", updated);
        llmRoleTemplateRepository.save(updated);
        return updated;
    }


    @Override
    public LlmRoleTemplateModel getLlmTemplate(String llmRoleTemplateId) {
        Optional<LlmRoleTemplateModel> template = llmRoleTemplateRepository
                .findByLlmRoleTemplateId(llmRoleTemplateId);
        if (template.isEmpty()) {
            throw new DigitalHumanCommonException("该模板不存在");
        }
        return template.get();
    }

    /**
     * 根据模板类型、分页参数、模板名称查询模板列表
     *
     * @param templateType 模板类型
     * @param pageNo       页码
     * @param pageSize     每页大小
     * @param templateName 模板名称
     * @return 查询到的模板列表分页响应
     */
    @Override
    public PageResponse<LlmRoleTemplateModel> listByTemplateType(int templateType, String templateName, String screenType, int pageNo, int pageSize) {
        log.debug("start list templateType is {}, templateName is {}, screenType is {}, pageNo is {}, pageSize is {} ", templateType, templateName, screenType, pageNo, pageSize);
        PageRequest pageRequest = PageRequest.of(pageNo - 1, pageSize);
        Page<LlmRoleTemplateModel> page;
        PageResponse<LlmRoleTemplateModel> pageResponse;
        if (StringUtils.isBlank(screenType) || "all".equals(screenType)) {
            page = llmRoleTemplateRepository.findByTemplateTypeAndTemplateNameContainingAndDelOrderByCreateTimeDesc(
                    templateType, templateName, false, pageRequest);
        } else {
            page = llmRoleTemplateRepository.findByTemplateTypeAndTemplateNameContainingAndScreenTypeAndDelOrderByCreateTimeDesc(
                    templateType, templateName, screenType, false, pageRequest);
        }
        pageResponse = PageResponse.success(pageNo, pageSize, page.getTotalElements(), new ArrayList<>(page.getContent()));
        log.debug("list template {} success!", page);
        return pageResponse;
    }
}
