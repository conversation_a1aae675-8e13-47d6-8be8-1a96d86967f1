package com.baidu.acg.piat.llm.dm.model.unit;

import java.util.List;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 *
 **/
@Data
@ApiModel
public class Answer {
    @ApiModelProperty(value = "根据answerContents列表拼接的文本推荐答案，若answerText不存在值: - 解析clarify字段")
    private String answerText;
    @ApiModelProperty(value = "答案列表，元素类型有：1: 纯文本类型，2: link类型，3: 富文本类型，4: 图片类型，6: 录音类型，7: 录音拼变量类型，8: 列表类型")
    private List<AnswerContents> answerContents;
    @ApiModelProperty(value = "推荐答案列表，元素类型：8: 列表类型")
    private List<String> recommendContents;

    @ApiModelProperty(value = "澄清答案")
    private Clarify clarify;
    @ApiModelProperty(value = "澄清类型，意图澄清：INTENT_CLARIFY\n" +
            "问答澄清：FAQ_CLARIFY\n" +
            "意图问答澄清：INTENT_FAQ_CLARIFY\n" +
            "faq高置信度澄清：FAQ_HIGH_CLARIFY\n" +
            "问答核心词澄清：FAQ_CORE_CLARIFY\n" +
            "意图模版澄清：TEMPLATE_INTENT_CLARIFY\n" +
            "问答模版澄清：TEMPLATE_FAQ_CLARIFY\n" +
            "意图问答模版澄清：TEMPLATE_INTENT_FAQ_CLARIFY\n" +
            "KG澄清：KG_CLARIFY\n" +
            "实体澄清：ENTITY_CLARIFY\n" +
            "第三方引擎澄清：THIRD_CLARIFY\n" +
            "技能澄清：SKILL_CLARIFY")
    private String clarifyType;
}
