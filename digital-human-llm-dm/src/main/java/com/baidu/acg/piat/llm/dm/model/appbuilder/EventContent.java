package com.baidu.acg.piat.llm.dm.model.appbuilder;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.Map;

public class EventContent {
    @JsonProperty(value = "event_code")
    private String eventCode;

    @JsonProperty(value = "event_message")
    private String enentMessage;

    @JsonProperty(value = "event_type")
    private String eventType;

    @JsonProperty(value = "event_id")
    private String eventId;

    @JsonProperty(value = "event_status")
    private String eventStatus;

    @JsonProperty(value = "content_type")
    private String contentType;
    private Map<String, Object> outputs;

    public String getEventCode() {
        return eventCode;
    }

    public void setEventCode(String eventCode) {
        this.eventCode = eventCode;
    }

    public String getEnentMessage() {
        return enentMessage;
    }

    public void setEnentMessage(String enentMessage) {
        this.enentMessage = enentMessage;
    }

    public String getEventType() {
        return eventType;
    }

    public void setEventType(String eventType) {
        this.eventType = eventType;
    }

    public String getEventId() {
        return eventId;
    }

    public void setEventId(String eventId) {
        this.eventId = eventId;
    }

    public String getEventStatus() {
        return eventStatus;
    }

    public void setEventStatus(String eventStatus) {
        this.eventStatus = eventStatus;
    }

    public String getContentType() {
        return contentType;
    }

    public void setContentType(String contentType) {
        this.contentType = contentType;
    }

    public Map<String, Object> getOutputs() {
        return outputs;
    }

    public void setOutputs(Map<String, Object> outputs) {
        this.outputs = outputs;
    }

    @Override
    public String toString() {
        return "EventContent{" +
                "eventCode='" + eventCode + '\'' +
                ", enentMessage='" + enentMessage + '\'' +
                ", eventType='" + eventType + '\'' +
                ", eventId='" + eventId + '\'' +
                ", eventStatus='" + eventStatus + '\'' +
                ", contentType='" + contentType + '\'' +
                ", outputs=" + outputs +
                '}';
    }
}
