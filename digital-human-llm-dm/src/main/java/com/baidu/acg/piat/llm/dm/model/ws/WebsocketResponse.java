package com.baidu.acg.piat.llm.dm.model.ws;

import java.util.UUID;

import javax.annotation.Nullable;

import com.fasterxml.jackson.databind.node.ObjectNode;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import com.baidu.acg.piat.digitalhuman.common.alita.ActionType;


/**
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WebsocketResponse {
    private String requestId;

    private int code;

    private String message;

    private ActionType action;

    private Object body = null;

    @Override
    public String toString() {
        String body = this.body != null ? this.body.toString() : "";
        return String.format("{'requestId': %s, 'action':%s, 'body':%s}",
                requestId, action, body);
    }


    public WebsocketResponse(WebsocketRequest requestMessage){
        this(requestMessage, null);
    }

    public WebsocketResponse(WebsocketRequest requestMessage, ObjectNode body){
        this.requestId = requestMessage.getRequestId();
        this.action = requestMessage.getAction();
        this.body = body;
    }

    public static WebsocketResponse success(WebsocketRequest request) {
        return success(request, null);
    }

    public static WebsocketResponse success(WebsocketRequest request, Object body) {
        return WebsocketResponse.builder().requestId(request.getRequestId())
                .action(request.getAction())
                .message("ok")
                .body(body)
                .build();
    }

    public static WebsocketResponse success(String requestId, ActionType action, @Nullable String body) {
        return WebsocketResponse.builder().requestId(requestId)
                .action(action)
                .message("ok")
                .body(body)
                .build();
    }


    public static WebsocketResponse fail(WebsocketRequest request, int code, String message) {
        return WebsocketResponse.builder().requestId(request.getRequestId())
                .action(request.getAction())
                .message(message)
                .build();
    }

    public static WebsocketResponse fail(WebsocketRequest request, Error error) {
        return WebsocketResponse.builder().requestId(request.getRequestId())
                .action(request.getAction())
                .message(error.getMessage())
                .build();
    }

    public static WebsocketResponse fail(ActionType action, int code, String message) {
        return WebsocketResponse.builder().requestId(UUID.randomUUID().toString())
                .action(action)
                .code(code)
                .message(message)
                .build();
    }

    public static WebsocketResponse fail(String requestId, ActionType action, int code, String message) {
        return WebsocketResponse.builder().requestId(requestId)
                .action(action)
                .code(code)
                .message(message)
                .build();
    }

}
