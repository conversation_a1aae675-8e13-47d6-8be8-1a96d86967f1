package com.baidu.acg.piat.llm.dm.service.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

import javax.annotation.PreDestroy;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.web.socket.WebSocketSession;
import org.springframework.web.socket.handler.ConcurrentWebSocketSessionDecorator;

import com.baidu.acg.piat.llm.dm.configure.WebsocketConfigure;
import com.baidu.acg.piat.llm.dm.model.SessionContext;
import com.baidu.acg.piat.llm.dm.service.SessionManager;

import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class SessionManagerImpl implements SessionManager {

    private volatile Map<String, SessionContext> sessionMap = new ConcurrentHashMap<>();

    @Lazy
    @Setter
    @Autowired
    private WebsocketConfigure.WebsocketConfig websocketConfig;

    @PreDestroy
    public void destroy() {
        log.info("Shutdown gracefully , clear sessionMap={} ", sessionMap.values());
        sessionMap.values().forEach(this::clearSession);
    }

    @Override
    public void addSession(WebSocketSession session) {
        var sessionContext = new SessionContext().setWebSocketSession(
                new ConcurrentWebSocketSessionDecorator(session,
                        websocketConfig.getSendTimeLimitSeconds(), websocketConfig.getBufferSizeLimit()));
        sessionContext.setWsId(session.getId());
        this.sessionMap.put(session.getId(), sessionContext);
    }

    @Override
    public SessionContext getSessionContext(String websocketSessionId) {
        return this.sessionMap.get(websocketSessionId);
    }

    @Override
    public SessionContext removeSession(String websocketSessionId) {
        return this.sessionMap.remove(websocketSessionId);
    }

    @Override
    public List<SessionContext> sessions() {
        return new ArrayList<>(sessionMap.values());
    }

    @Override
    public List<SessionContext> findSessionContext(String appId) {
        return this.sessionMap.values().stream().filter(s -> appId.equals(s.getAppId()))
                .collect(Collectors.toList());
    }

    @Override
    @Scheduled(fixedRateString = "${digitalhuman.llmdm.session.scan.fixRate:10000}")
    public void scanInactiveSessions() {
        long expireTimeMillis = System.currentTimeMillis() - websocketConfig.getKeepAliveMillis();
        log.debug("Start to scan inactive session");
        sessionMap.values().forEach(sessionContext -> {
            if (sessionContext.getNeedHeartbeat().get()
                    && sessionContext.getHeartbeatActiveTimeMillis() < expireTimeMillis) {
                log.debug("session map size={}, current sessionId={}",
                        sessionMap.values().size(), sessionContext.getSessionId());
                try {
                    clearSession(sessionContext);
                    sessionContext.getWebSocketSession().close();
                } catch (Exception e) {
                    log.error("Fail to expire the session={}", sessionContext.getSessionId());
                }
            }
        });
    }

    public void clearSession(SessionContext sessionContext) {
        log.debug("Start to handle disconnect of websocketId={}, sessionId={}, appId={}",
                sessionContext.getWebSocketSession().getId(), sessionContext.getSessionId(), sessionContext.getAppId());
        try {/* ensure the release of session */
            if (sessionContext != null) {
                if (sessionContext.getBotProviderService() != null) {
                    sessionContext.getBotProviderService().clear(sessionContext);
                    sessionContext.setBotProviderService(null);
                }
                sessionContext.setLlmStreamObserver(null);
                removeSession(sessionContext.getWsId());
            }
        } catch (Exception e) {
            log.warn("Fail to remove the sessionId={}, still doing the release task", sessionContext, e);
            return;
        }
        log.debug("Websocket {} session={} on disconnect", sessionContext.getWebSocketSession().getId(),
                sessionContext.getSessionId());
    }

}
