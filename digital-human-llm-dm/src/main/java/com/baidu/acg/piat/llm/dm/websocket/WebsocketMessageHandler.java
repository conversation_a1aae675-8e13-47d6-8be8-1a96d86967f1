package com.baidu.acg.piat.llm.dm.websocket;

import java.io.IOException;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.LinkedBlockingDeque;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;

import javax.annotation.PostConstruct;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.socket.BinaryMessage;
import org.springframework.web.socket.CloseStatus;
import org.springframework.web.socket.TextMessage;
import org.springframework.web.socket.WebSocketSession;
import org.springframework.web.socket.handler.AbstractWebSocketHandler;

import com.baidu.acg.piat.digitalhuman.common.alita.ActionType;
import com.baidu.acg.piat.digitalhuman.common.exception.DigitalHumanCommonException;
import com.baidu.acg.piat.digitalhuman.common.exception.Error;
import com.baidu.acg.piat.digitalhuman.common.llmdm.BaseRequestBody;
import com.baidu.acg.piat.digitalhuman.common.llmdm.WebsocketLlmRequest;
import com.baidu.acg.piat.digitalhuman.common.llmdm.WebsocketLlmResponse;
import com.baidu.acg.piat.digitalhuman.common.session.SessionStatus;
import com.baidu.acg.piat.digitalhuman.common.utils.JsonUtil;
import com.baidu.acg.piat.llm.dm.model.SessionContext;
import com.baidu.acg.piat.llm.dm.service.SessionManager;
import com.baidu.acg.piat.llm.dm.utils.WebsocketUtil;
import com.google.common.util.concurrent.ThreadFactoryBuilder;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * WebsocketMessageHandler
 */

@Slf4j
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class WebsocketMessageHandler extends AbstractWebSocketHandler {

    private final SessionManager sessionManager;

    private final List<RequestReactor> requestReactors;

    @Value("${digitalhuman.llmdm.core.pool.size:20}")
    private int corePoolSize;

    ThreadPoolExecutor executor;

    @PostConstruct
    public void init() {
        executor = new ThreadPoolExecutor(
                corePoolSize,
                corePoolSize * 4,
                1000,
                TimeUnit.MILLISECONDS,
                new LinkedBlockingDeque<>(corePoolSize * 2),
                new ThreadFactoryBuilder().setNameFormat("digital-human-alita-pool-%d").build(),
                new ThreadPoolExecutor.CallerRunsPolicy());
    }

    @Override
    public void afterConnectionEstablished(WebSocketSession session) {
        log.debug("websocket {} established.", session.getId());
        this.sessionManager.addSession(session);
    }

    @Override
    public void handleTextMessage(WebSocketSession session, TextMessage message) {
        SessionContext sessionContext = sessionManager.getSessionContext(session.getId());
        if (sessionContext == null) {
            log.warn("Websocket {} not found in sessionMap", session.getId());
            return;
        }

        Consumer<WebsocketLlmResponse> consumer = response -> WebsocketUtil.send(session, response);

        WebsocketLlmRequest request;
        try {
            request = JsonUtil.readValue(message.getPayload(), WebsocketLlmRequest.class);
            log.debug("Websocket {} on websocket message={} ", session.getId(), request);
        } catch (Exception e) {
            log.warn("Fail to read message as json {}", message.getPayload());
            consumer.accept(WebsocketLlmResponse.fail(ActionType.UNKNOWN, -1, "fail to parse the request"));
            return;
        }

        if (ActionType.HEART_BEAT.equals(request.getAction())) {
            log.info("Response success heartbeat of websocketSessionId={}, sessionId={}",
                    session.getId(), sessionContext.getSessionId());
            sessionContext.setHeartbeatActiveTimeMillis(System.currentTimeMillis());
            BaseRequestBody requestBody = new BaseRequestBody();
            requestBody.setSessionId(sessionContext.getSessionId());
            consumer.accept(WebsocketLlmResponse.success(request, requestBody));
            return;
        }

        if (ActionType.SESSION_OPEN_STATUS.equals(request.getAction())) {
            log.info("Response sesson open status wsId={}, sessionId={}",
                    session.getId(), sessionContext.getSessionId());
            sessionContext.setStatus(SessionStatus.OPEN);
            return;
        }

        CompletableFuture.runAsync(() -> {
            sessionContext.updateRequestActiveStatus();
            sessionContext.updateRequestActiveStatus();
            Optional<RequestReactor> reactorOption =
                    requestReactors.stream().filter(r -> r.support(request.getAction())).findAny();
            if (reactorOption.isEmpty()) {
                log.warn("Session {} action {} not supported", session.getId(), request.getAction());
                throw new DigitalHumanCommonException(Error.ACTION_NOT_SUPPORTED.getCode(),
                        Error.ACTION_NOT_SUPPORTED.getMessage());
            }
            reactorOption.get().react(sessionContext, request);
        }, executor).exceptionally(t -> {
            log.warn("Fail to handle the request ={} ", request, t);
            WebsocketLlmResponse response;
            Throwable cause = t.getCause();
            if (cause instanceof DigitalHumanCommonException) {
                response = WebsocketLlmResponse.fail(request,
                        ((DigitalHumanCommonException) cause).getCode(),
                        ((DigitalHumanCommonException) cause).getMessage());
            } else {
                response = WebsocketLlmResponse.fail(request,
                        -1, request.getAction().getAlias() + "处理失败");
            }
            consumer.accept(response);
            if (request.getAction() == ActionType.CONNECT) {
                try {
                    sessionContext.getWebSocketSession().close();
                } catch (IOException e1) {
                    log.error("Fail to close the websocket {}", sessionContext.getWebSocketSession().getId(), e1);
                }
            }
            return null;
        });

    }

    @Override
    protected void handleBinaryMessage(WebSocketSession session, BinaryMessage message) {
        log.debug("Websocket {} on binary message={} ", session.getId(), message);
    }

    @Override
    public void afterConnectionClosed(WebSocketSession session, CloseStatus status) {
        log.debug("Websocket {} connection close {}", session.getId(), status);
        SessionContext context = sessionManager.getSessionContext(session.getId());
        if (context != null) {
            sessionManager.clearSession(context);
        }
    }
}
