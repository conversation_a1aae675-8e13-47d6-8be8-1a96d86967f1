package com.baidu.acg.piat.llm.dm.model;

import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;

import org.springframework.web.socket.WebSocketSession;

import com.baidu.acg.piat.digitalhuman.common.llmrole.BufferSentencesConfigJson;
import com.baidu.acg.piat.digitalhuman.common.llmrole.LLMConfig;
import com.baidu.acg.piat.digitalhuman.common.session.SessionStatus;
import com.baidu.acg.piat.llm.dm.constants.LLMConstants;
import com.baidu.acg.piat.llm.dm.model.text2gesture.AnimojisTagConfigJson;
import com.baidu.acg.piat.llm.dm.service.BotProviderService;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;

@Builder
@Data
@Accessors(chain = true)
@Slf4j
@NoArgsConstructor
@AllArgsConstructor
public class SessionContext {

    private String sessionId;

    private String wsId;

    private String chargeToken;

    private LlmRoleModel llmRoleModel;

    private String unitSessionId;

    private String copilotConversationId;

    private String dialogId;

    private String appId;

    private String characterImage;

    private String accountId;

    private String uid;

    private String configVersion;

    private BotProviderService botProviderService;


    private LLMConfig llmConfig;

    private AnimojisTagConfigJson animojisTagConfigJson;

    private BufferSentencesConfigJson bufferSentencesConfigJson;

    @Builder.Default
    private SessionStatus status = SessionStatus.INIT;

    /**
     * 记录websocket session
     */
    private WebSocketSession webSocketSession;

    private AtomicBoolean needHeartbeat = new AtomicBoolean(true);

    private long heartbeatActiveTimeMillis = System.currentTimeMillis();

    private long requestActiveTimeMillis = System.currentTimeMillis();

    private long responseActiveTimeMillis = System.currentTimeMillis();

    private AtomicInteger userInactiveRemindCnt = new AtomicInteger(0);

    /**
     * 用于记录消息index，自增，多线程安全
     */
    private AtomicInteger queryIndex = new AtomicInteger(0);

    @Builder.Default
    private Lock lock = new ReentrantLock(true);

    Map<String, PendingTask> pendingQueryMap = new ConcurrentHashMap<>();

    Map<String, Integer> pendingResponseSeqMap = new ConcurrentHashMap<>();

    Set<String> freezeSet = ConcurrentHashMap.newKeySet();

    Set<String> interruptedByUser = ConcurrentHashMap.newKeySet();

    // 用来存已经被打断的任务
    Map<String, Long> interruptedMap = new ConcurrentHashMap<>();

    Set<String> pendingHasResponseSet = ConcurrentHashMap.newKeySet();

    private Map<String, StringBuffer> pendingResponseMap = new ConcurrentHashMap<>();

    private LlmStreamObserver<String> llmStreamObserver;

    /**
     * 渠道
     */
    private String channel = "";


    /**
     * 正常的请求也会更新心跳事件
     * <p>
     * 用户侧发起的请求，会重置提醒次数
     */
    public void updateRequestActiveStatus() {
        this.setRequestActiveTimeMillis(System.currentTimeMillis());
        this.setHeartbeatActiveTimeMillis(System.currentTimeMillis());
        this.getUserInactiveRemindCnt().set(0);
    }

    /**
     * append响应，然后试图去消费
     *
     * @param query
     * @param source
     * @param data
     * @return
     */
    public String produceAndTryConsumeResponse(Query query, String source, String data, Set<Character> llmSplitEndSet
            , int minSplitSentenceLen) {
        lock.lock();
        try {
            if (!pendingResponseMap.containsKey(query.getRequestId())) {
                pendingResponseMap.put(query.getRequestId(), new StringBuffer());
            }
            pendingResponseMap.get(query.getRequestId()).append(data);
            return consumeResponse(query, source, false, llmSplitEndSet, minSplitSentenceLen);
        } finally {
            lock.unlock();
        }
    }

    /**
     * 消费信息
     *
     * @param query
     * @param source
     * @param isEnd
     * @return
     */
    public String consumeResponse(Query query, String source, boolean isEnd, Set<Character> llmSplitEndSet
            , int minSplitSentenceLen) {
        lock.lock();
        try {
            if (!pendingResponseMap.containsKey(query.getRequestId())) {
                return null;
            }
            StringBuffer sb = pendingResponseMap.get(query.getRequestId());
            // 如果答案不是来自大模型
            if (isEnd || !source.equals(LLMConstants.SOURCE_LLM)) {
                String response = sb.toString();
                sb.delete(0, sb.length());
                return response;
            } else {
                // 字数大于一定的长度才开始消费
                if (sb.length() > minSplitSentenceLen) {
                    int l = sb.length();
                    // 如果句子直接超过一定的长度，且最后一个字符是分隔符，则直接返回
                    if (llmSplitEndSet.contains(sb.charAt(l - 1))) {
                        String response = sb.substring(0, l);
                        sb.delete(0, l);
                        return response;
                    } else {
                        // 如果句子的不是以符号结尾，则从后往前找，找到第一个分隔符，然后返回
                        for (int i = minSplitSentenceLen; i < sb.length(); i++) {
                            if (llmSplitEndSet.contains(sb.charAt(i))) {
                                String response = sb.substring(0, i + 1);
                                sb.delete(0, i + 1);
                                return response;
                            }
                        }
                    }
                }
            }
            return null;
        } finally {
            lock.unlock();
        }
    }

    public void clearPendingResponse(Query query) {
        lock.lock();
        try {
            pendingResponseMap.remove(query.getRequestId());
            pendingResponseSeqMap.remove(query.getRequestId());
        } finally {
            lock.unlock();
        }
    }

}
