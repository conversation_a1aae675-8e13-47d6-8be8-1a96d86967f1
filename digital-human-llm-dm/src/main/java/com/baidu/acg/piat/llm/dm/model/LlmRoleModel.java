package com.baidu.acg.piat.llm.dm.model;

import java.io.IOException;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import javax.persistence.Access;
import javax.persistence.AccessType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Transient;

import com.baidu.acg.piat.digitalhuman.common.llmrole.BufferSentencesConfigJson;
import com.baidu.acg.piat.digitalhuman.common.llmrole.BufferSentencesConfigJson;
import com.baidu.acg.piat.digitalhuman.common.llmrole.Knowledge;
import com.baidu.acg.piat.digitalhuman.common.llmrole.LLMConfig;
import com.baidu.acg.piat.digitalhuman.common.llmrole.LLMConfig;
import com.baidu.acg.piat.digitalhuman.common.llmrole.LlmRole;
import com.baidu.acg.piat.digitalhuman.common.llmrole.OpeningStatement;
import com.baidu.acg.piat.digitalhuman.common.llmrole.Skill;
import com.baidu.acg.piat.digitalhuman.common.project.TtsParams;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;
import org.hibernate.annotations.UpdateTimestamp;

import com.baidu.acg.piat.digitalhuman.common.utils.JsonUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> Xiaoyu
 * @since 2023/11/22 16:31
 */
@Data
@Slf4j
@Builder
@Accessors(chain = true)
@DynamicInsert
@DynamicUpdate
@Entity(name = "llm_role")
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class LlmRoleModel {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    private String llmRoleId;

    private String characterConfigId;

    /**
     * 角色模板
     */
    private String llmRoleTemplateId;

    private String screenType;

    /**
     * 大模型的模板
     */
    private String templateId;

    private String appId;

    private String appKey;


    private String accountId;

    private String uid;

    private String projectId;

    @Builder.Default
    private String status = "open";

    @Transient
    private String publishUrl;

    private String name;

    private String editor;

    private Integer roleType;

    private int modeType;

    private String templateName;

    private String templateIconUrl;

    @Transient
    private LLMConfig llmConfig;
    private String roleDefine;
    @Transient
    private List<Skill> skillList;
    @Transient
    private Knowledge knowledge;
    @Transient
    private OpeningStatement openingStatement;
    @Transient
    private BufferSentencesConfigJson bufferSentencesConfigJson;


    @Transient
    private TtsParams ttsParams;

    @Builder.Default
    private boolean del = false;

    @CreationTimestamp
    @Column(insertable = false, updatable = false)
    private ZonedDateTime createTime;

    @UpdateTimestamp
    @Column(insertable = false, updatable = false)
    private ZonedDateTime updateTime;

    @Column(name = "tts_params")
    @Access(AccessType.PROPERTY)
    public String getTtsParamsString() {
        if (ttsParams != null) {
            try {
                return JsonUtil.writeValueAsString(ttsParams);
            } catch (JsonProcessingException e) {
                log.error("Parse tts params to json failed.", e);
            }
        }
        return StringUtils.EMPTY;
    }

    public void setTtsParamsString(String ttsParams) {
        if (StringUtils.isNotEmpty(ttsParams)) {
            try {
                this.ttsParams = JsonUtil.readValue(ttsParams, TtsParams.class);
            } catch (IOException e) {
                log.error("Exception when parse tts params from json string, string={}.", ttsParams, e);
            }
        } else {
            this.ttsParams = null;
        }
    }

    @Column(name = "llm_config")
    @Access(AccessType.PROPERTY)
    public String getLlmConfigJsonString() {
        if (llmConfig != null) {
            try {
                return JsonUtil.writeValueAsString(llmConfig);
            } catch (JsonProcessingException e) {
                log.error("Parse tags to json failed.", e);
            }
        }
        return StringUtils.EMPTY;
    }

    public void setLlmConfigJsonString(String llmConfigJson) {
        if (StringUtils.isNotEmpty(llmConfigJson)) {
            try {
                this.llmConfig = JsonUtil.readValue(llmConfigJson, LLMConfig.class);
            } catch (IOException e) {
                log.error("Exception when parse tags from json string, string={}.", llmConfigJson, e);
            }
        } else {
            this.llmConfig = null;
        }
    }

    @Column(name = "skill_list")
    @Access(AccessType.PROPERTY)
    public String getSkillListString() {
        if (skillList != null && skillList.size() > 0) {
            try {
                return JsonUtil.writeValueAsString(skillList);
            } catch (JsonProcessingException e) {
                log.error("Parse tags to json failed.", e);
            }
        }
        return StringUtils.EMPTY;
    }

    public void setSkillListString(String skillListString) {
        if (StringUtils.isNotEmpty(skillListString)) {
            try {
                this.skillList = JsonUtil.readValue(skillListString, new TypeReference<List<Skill>>() {
                });
            } catch (IOException e) {
                log.error("Exception when parse tags from json string, string={}.", skillListString, e);
            }
        } else {
            this.skillList = null;
        }
    }

    @Column(name = "knowledge")
    @Access(AccessType.PROPERTY)
    public String getKnowledgeString() {
        if (knowledge != null) {
            try {
                return JsonUtil.writeValueAsString(knowledge);
            } catch (JsonProcessingException e) {
                log.error("Parse tags to json failed.", e);
            }
        }
        return StringUtils.EMPTY;
    }

    public void setKnowledgeString(String knowledgeString) {
        if (StringUtils.isNotEmpty(knowledgeString)) {
            try {
                this.knowledge = JsonUtil.readValue(knowledgeString, Knowledge.class);
            } catch (IOException e) {
                log.error("Exception when parse tags from json string, string={}.", knowledgeString, e);
            }
        } else {
            this.knowledge = null;
        }
    }

    @Column(name = "opening_statement")
    @Access(AccessType.PROPERTY)
    public String getOpeningStatementString() {
        if (openingStatement != null) {
            try {
                return JsonUtil.writeValueAsString(openingStatement);
            } catch (JsonProcessingException e) {
                log.error("Parse resource quota to json failed.", e);
            }
        }
        return StringUtils.EMPTY;
    }

    public void setOpeningStatementString(String str) {
        if (StringUtils.isNotEmpty(str)) {
            try {
                this.openingStatement = JsonUtil.readValue(str, OpeningStatement.class);
            } catch (IOException e) {
                log.error("Exception when parse buffer config json str from json string, string={}.", str,
                        e);
            }
        } else {
            this.openingStatement = null;
        }
    }

    @Column(name = "buffer_sentences_config_json")
    @Access(AccessType.PROPERTY)
    public String getBufferSentencesConfigJsonString() {
        if (bufferSentencesConfigJson != null) {
            try {
                return JsonUtil.writeValueAsString(bufferSentencesConfigJson);
            } catch (JsonProcessingException e) {
                log.error("Parse resource quota to json failed.", e);
            }
        }
        return StringUtils.EMPTY;
    }

    public void setBufferSentencesConfigJsonString(String str) {
        if (StringUtils.isNotEmpty(str)) {
            try {
                this.bufferSentencesConfigJson = JsonUtil.readValue(str, BufferSentencesConfigJson.class);
            } catch (IOException e) {
                log.error("Exception when parse buffer config json str from json string, string={}.", str,
                        e);
            }
        } else {
            this.bufferSentencesConfigJson = null;
        }
    }

    public LlmRole toLlmRole() {
        return LlmRole.builder()
                .llmRoleId(llmRoleId)
                .accountId(accountId)
                .uid(uid)
                .projectId(projectId)
                .llmRoleTemplateId(llmRoleTemplateId)
                .characterConfigId(characterConfigId)
                .screenType(screenType)
                .templateId(templateId)
                .name(name)
                .editor(editor)
                .roleType(roleType)
                .modeType(modeType)
                .llmConfig(llmConfig)
                .status(status)
                .roleDefine(roleDefine)
                .skillList(skillList)
                .knowledge(knowledge)
                .openingStatement(openingStatement)
                .bufferSentencesConfigJson(bufferSentencesConfigJson)
                .templateName(templateName)
                .templateIconUrl(templateIconUrl)
                .createTime(DateTimeFormatter.ISO_OFFSET_DATE_TIME.format(createTime))
                .updateTime(DateTimeFormatter.ISO_OFFSET_DATE_TIME.format(updateTime))
                .editor(editor)
                .build();
    }

    public LlmRole toTemplateRole() {
        return LlmRole.builder()
                .llmRoleId(llmRoleId)
                .accountId(accountId)
                .uid(uid)
                .characterConfigId(characterConfigId)
                .projectId(projectId)
                .editor(editor)
                .roleType(roleType)
                .llmConfig(llmConfig)
                .status(status)
                .publishUrl("https://digitalhuman.baidu.com/cloud/react?appId=***&appKey=***&projectId=" + projectId + "&configVersion=publish")
                .roleDefine(roleDefine)
                .skillList(skillList)
                .knowledge(knowledge)
                .openingStatement(openingStatement)
                .bufferSentencesConfigJson(bufferSentencesConfigJson)
                .templateName(templateName)
                .templateIconUrl(templateIconUrl)
                .createTime(DateTimeFormatter.ISO_OFFSET_DATE_TIME.format(createTime))
                .updateTime(DateTimeFormatter.ISO_OFFSET_DATE_TIME.format(updateTime))
                .editor(editor)
                .build();
    }

    public void toLlmRoleModel(LlmRole request) {
        if (StringUtils.isNotEmpty(request.getLlmRoleId())) {
            this.setLlmRoleId(request.getLlmRoleId());
        }
        if (StringUtils.isNotEmpty(request.getAccountId())) {
            this.setAccountId(request.getAccountId());
        }
        if (StringUtils.isNotEmpty(request.getUid())) {
            this.setUid(request.getUid());
        }
        if (StringUtils.isNotEmpty(request.getProjectId())) {
            this.setProjectId(request.getProjectId());
        }
        if (StringUtils.isNotEmpty(request.getName())) {
            this.setName(request.getName());
        }
        if (request.getRoleType() != null) {
            this.setRoleType(request.getRoleType());
        }
        if (request.getModeType() != null) {
            this.setModeType(request.getModeType());
        }
        if (request.getLlmConfig() != null) {
            this.setLlmConfig(request.getLlmConfig());
        }
        if (StringUtils.isNotEmpty(request.getRoleDefine())) {
            this.setRoleDefine(request.getRoleDefine());
        }
        if (request.getSkillList() != null) {
            this.setSkillList(request.getSkillList());
        }
        if (request.getKnowledge() != null) {
            this.setKnowledge(request.getKnowledge());
        }
        if (request.getOpeningStatement() != null) {
            this.setOpeningStatement(request.getOpeningStatement());
        }
        if (request.getBufferSentencesConfigJson() != null) {
            this.setBufferSentencesConfigJson(request.getBufferSentencesConfigJson());
        }
        if (StringUtils.isNotEmpty(request.getTemplateName())) {
            this.setTemplateName(request.getTemplateName());
        }
        if (StringUtils.isNotEmpty(request.getTemplateIconUrl())) {
            this.setTemplateIconUrl(request.getTemplateIconUrl());
        }
        if (StringUtils.isNotEmpty(request.getCharacterConfigId())) {
            this.setCharacterConfigId(request.getCharacterConfigId());
        }
    }

}
