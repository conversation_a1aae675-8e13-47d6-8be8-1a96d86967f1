package com.baidu.acg.piat.llm.dm.model.qianfan;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class QianfanResponseBody {

    @JsonProperty(value = "message_id")
    String messageId;

    List<Object> references;

    String answer;

    @JsonProperty(value = "conversation_id")
    String conversationId;

    Map<String, Object> extras;

}
