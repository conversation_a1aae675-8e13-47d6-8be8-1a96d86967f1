package com.baidu.acg.piat.llm.dm.model.chat;

import java.util.Map;
import java.util.UUID;

import com.baidu.acg.piat.digitalhuman.common.llmrole.LLMConfig;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 */
@Getter
@Setter
@ToString
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class LlmQuery {

    @Builder.Default
    String requestId = UUID.randomUUID().toString();

    @Builder.Default
    String sessionId = UUID.randomUUID().toString();

    String query;

    boolean stream = false;

    String llmConfigId;

    private Map<String, Object> params;

    LLMConfig model;

}
