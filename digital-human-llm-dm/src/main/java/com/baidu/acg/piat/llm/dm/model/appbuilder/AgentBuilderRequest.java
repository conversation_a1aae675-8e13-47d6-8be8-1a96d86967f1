package com.baidu.acg.piat.llm.dm.model.appbuilder;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AgentBuilderRequest {
    @JsonProperty(value = "app_id")
    private String appId;

    Map<String, Object> inputs;

    String query;

    @JsonProperty(value = "conversation_id")
    String conversationId;

    String user;

    @JsonProperty(value = "file_ids")
    List<String> fileIds = new ArrayList<>();

    private boolean stream = true;

}
