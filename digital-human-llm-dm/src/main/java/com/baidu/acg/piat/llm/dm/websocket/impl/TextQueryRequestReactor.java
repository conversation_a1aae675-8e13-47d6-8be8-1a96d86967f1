package com.baidu.acg.piat.llm.dm.websocket.impl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

import com.baidu.acg.piat.digitalhuman.common.llmrole.LLMConfig;
import com.baidu.acg.piat.llm.dm.constants.ResponseType;
import com.baidu.acg.piat.llm.dm.dao.LlmHistoryRepository;
import com.baidu.acg.piat.llm.dm.model.LlmHistoryModel;
import com.baidu.acg.piat.llm.dm.model.drml.ClientWidget;
import com.baidu.acg.piat.llm.dm.service.FaqService;
import com.baidu.acg.piat.llm.dm.utils.DrmlUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.concurrent.BasicThreadFactory;
import org.checkerframework.checker.units.qual.C;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import com.baidu.acg.piat.digitalhuman.common.alita.ActionType;
import com.baidu.acg.piat.digitalhuman.common.constans.Constants;
import com.baidu.acg.piat.digitalhuman.common.exception.DigitalHumanCommonException;
import com.baidu.acg.piat.digitalhuman.common.llmdm.SourceType;
import com.baidu.acg.piat.digitalhuman.common.llmdm.TextRenderBody;
import com.baidu.acg.piat.digitalhuman.common.llmdm.TextRequestBody;
import com.baidu.acg.piat.digitalhuman.common.llmdm.ToolConfig;
import com.baidu.acg.piat.digitalhuman.common.llmdm.ToolType;
import com.baidu.acg.piat.digitalhuman.common.llmdm.WebsocketLlmRequest;
import com.baidu.acg.piat.digitalhuman.common.llmdm.WebsocketLlmResponse;
import com.baidu.acg.piat.digitalhuman.common.llmrole.Knowledge;
import com.baidu.acg.piat.digitalhuman.common.llmrole.Skill;
import com.baidu.acg.piat.digitalhuman.common.llmrole.StatusType;
import com.baidu.acg.piat.digitalhuman.common.project.BotParams;
import com.baidu.acg.piat.digitalhuman.common.session.SessionStatus;
import com.baidu.acg.piat.digitalhuman.common.utils.JsonUtil;
import com.baidu.acg.piat.llm.dm.configure.CommonConfig;
import com.baidu.acg.piat.llm.dm.constants.LLMConstants;
import com.baidu.acg.piat.llm.dm.dao.LlmRoleRepository;
import com.baidu.acg.piat.llm.dm.error.BizError;
import com.baidu.acg.piat.llm.dm.model.LlmRoleModel;
import com.baidu.acg.piat.llm.dm.model.Query;
import com.baidu.acg.piat.llm.dm.model.SessionContext;
import com.baidu.acg.piat.llm.dm.model.aida.WorkflowRequest;
import com.baidu.acg.piat.llm.dm.model.aida.WorkflowResponse;
import com.baidu.acg.piat.llm.dm.model.copilot.CopilotAction;
import com.baidu.acg.piat.llm.dm.service.LLMBillingService;
import com.baidu.acg.piat.llm.dm.service.Text2GestureService;
import com.baidu.acg.piat.llm.dm.utils.WebsocketUtil;
import com.baidu.acg.piat.llm.dm.websocket.RequestReactor;

import io.vavr.control.Try;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
@RequiredArgsConstructor
public class TextQueryRequestReactor implements RequestReactor {

    private final ScheduledExecutorService scheduler = new ScheduledThreadPoolExecutor(1,
            new BasicThreadFactory.
                    Builder().namingPattern("llm-%d").daemon(true).build());

    private final Text2GestureService text2GestureService;

    private final CommonConfig commonConfig;

    private final LLMBillingService llmBillingService;

    private final LlmRoleRepository llmRoleRepository;

    private final FaqService faqService;

    private final LlmHistoryRepository llmHistoryRepository;


    /**
     * {@inheritDoc}
     *
     * @param sessionContext 会话上下文对象
     * @param request        websocket 请求参数
     */
    @Override
    public void react(SessionContext sessionContext, WebsocketLlmRequest request) {
        log.info("TextQueryRequestReactor, sessionId={}, request={}", sessionContext.getSessionId(), request);
        if (sessionContext == null || sessionContext.getStatus() != SessionStatus.OPEN) {
            // 对于还没有连接好的请求直接发送query过来直接丢掉
            log.warn("Session is not open, sessionId={}, request={}", sessionContext.getSessionId(), request);
            return;
        }

        // TODO 后续用redis实现，不用每次请求都过db
        LlmRoleModel llmDmConfigModel =
                llmRoleRepository.findByLlmRoleId(sessionContext.getLlmRoleModel().getLlmRoleId())
                .orElseThrow(() -> new DigitalHumanCommonException("Token is not exist"));
        if (!com.baidu.acg.piat.llm.dm.constants.StatusType.OPNE.getStatus().equals(llmDmConfigModel.getStatus())) {
            log.info("LlmDmConfigModel is not open, llmDmConfigModel={}", llmDmConfigModel);
            throw new DigitalHumanCommonException(BizError.LLM_STOP.getCode(), BizError.LLM_STOP.getMessage());
        }
        // 处理requestId
        final String requestId = !StringUtils.isEmpty(request.getRequestId()) ? request.getRequestId() :
                UUID.randomUUID().toString();
        request.setRequestId(requestId);
        int queryIndex = sessionContext.getQueryIndex().get();
        TextRequestBody textRequestBody = Try.of(() -> JsonUtil.readValue(request.getBody(),
                TextRequestBody.class)).getOrNull();
        if (sessionContext.getBotProviderService() == null || request.getRequestId() == null
                || textRequestBody == null) {
            log.warn("BotProviderService or requestId is null, sessionId={}", sessionContext.getSessionId());
            // 直接发送结束drml给上游
            WebsocketUtil.send(sessionContext.getWebSocketSession(), WebsocketLlmResponse.builder()
                    .action(ActionType.TEXT_RENDER)
                    .requestId(request.getRequestId())
                    .body(TextRenderBody.toBuild(requestId,
                            LLMConstants.REPLY_COMPLETE_DRML,
                            true, 0, sessionContext.getSessionId(), true))
                    .code(0)
                    .message("Request is invalid")
                    .build());
            return;
        }
        if (!StringUtils.isEmpty(textRequestBody.getSessionId())) {
            sessionContext.setSessionId(textRequestBody.getSessionId());
        }

        // 前置处理，请求画布的内容
        if (textRequestBody.getContext() != null && textRequestBody.getContext().get("actionId") != null) {
            try {
                WorkflowRequest workflowRequest = new WorkflowRequest();
                workflowRequest.setRequestId(request.getRequestId());
                workflowRequest.setText(textRequestBody.getText());
                workflowRequest.setSessionId(textRequestBody.getContext().get("aida_session_id").toString());
                workflowRequest.setContext(JsonUtil.writeValueAsString(textRequestBody.getContext()));
                WorkflowResponse workflowResponse = text2GestureService.requestAida(commonConfig.getAidaUrl(),
                        workflowRequest);
                log.info("WlChat in llmdm, sessionId={}, request={}, response={}",
                        sessionContext.getSessionId(), workflowRequest, workflowResponse);
                if (workflowResponse != null) {
                    WebsocketUtil.send(sessionContext.getWebSocketSession(), WebsocketLlmResponse.builder()
                            .action(ActionType.TEXT_RENDER)
                            .requestId(request.getRequestId())
                            .body(TextRenderBody.toBuild(request.getRequestId(),
                                    workflowResponse.getReply(),
                                    true, 0, sessionContext.getSessionId(), false))
                            .code(0)
                            .message("ok")
                            .build());
                }
                return;
            } catch (Exception e) {
                log.warn("WlChat in llmdm error, sessionId={}, request={}",
                        sessionContext.getSessionId(), request, e);
                return;
            }
        }

        // 收到前端的开始话术的时候，触发开场白, 前端来把控这个时间. 如果是NGD 把digitalhuman.greeting发给NGD
        if (LLMConstants.START_QUERY.equals(textRequestBody.getText())) {
            if (sessionContext.getBotProviderService().getBotType().equals(BotParams.BotTypeEnum.UNIT_LLM.getBotType())
                    || sessionContext.getBotProviderService().getBotType()
                    .equals(BotParams.BotTypeEnum.KE_YUE.getBotType())) {
                log.debug("Start query, sessionId={}", sessionContext.getSessionId());
            } else {
                if (sessionContext.getLlmRoleModel().getOpeningStatement() != null
                        && sessionContext.getLlmRoleModel().getOpeningStatement().getStatus() == StatusType.OPEN.code()
                        && !StringUtils.isEmpty(
                        sessionContext.getLlmRoleModel().getOpeningStatement().getStatement())) {
                    if (sessionContext.getLlmConfig().getRecommendConfig() == null ) {
                        WebsocketUtil.send(sessionContext.getWebSocketSession(), WebsocketLlmResponse.builder()
                                .action(ActionType.TEXT_RENDER)
                                .requestId(request.getRequestId())
                                .body(TextRenderBody.toBuild(requestId,
                                        sessionContext.getLlmRoleModel().getOpeningStatement().getStatement(),
                                        true, 0, sessionContext.getSessionId(), true))
                                .code(0)
                                .message("")
                                .build());
                    } else {
                        LLMConfig.RecommendConfig recommendConfig = sessionContext.getLlmConfig().getRecommendConfig();
                        if (recommendConfig.getNum() > 0) {
                            String clientWidget = DrmlUtil.buildClientWidget(recommendConfig);
                            String xml = sessionContext.getLlmRoleModel().getOpeningStatement().getStatement()
                                    + clientWidget;
                            log.info("Start query, sessionId={}, res clientWidget={}", sessionContext.getSessionId(),
                                    xml);
                            WebsocketUtil.send(sessionContext.getWebSocketSession(), WebsocketLlmResponse.builder()
                                    .action(ActionType.TEXT_RENDER)
                                    .requestId(request.getRequestId())
                                    .body(TextRenderBody.toBuild(requestId,
                                            xml,
                                            true, 0, sessionContext.getSessionId(), true))
                                    .code(0)
                                    .message("")
                                    .build());
                        } else {
                            WebsocketUtil.send(sessionContext.getWebSocketSession(), WebsocketLlmResponse.builder()
                                    .action(ActionType.TEXT_RENDER)
                                    .requestId(request.getRequestId())
                                    .body(TextRenderBody.toBuild(requestId,
                                            sessionContext.getLlmRoleModel().getOpeningStatement().getStatement(),
                                            true, 0, sessionContext.getSessionId(), true))
                                    .code(0)
                                    .message("")
                                    .build());
                        }

                    }
                }
                return;
            }
        }

        if (sessionContext.getLlmConfig() != null && sessionContext.getLlmConfig().isInterruptedBeforeQuery()) {
            log.info("Interrupted before query, sessionId={}", sessionContext.getSessionId());
            sessionContext.getBotProviderService().interrupt(sessionContext, null);
            WebsocketUtil.send(sessionContext.getWebSocketSession(), WebsocketLlmResponse.builder()
                    .action(ActionType.TEXT_RENDER)
                    .requestId(request.getRequestId())
                    .body(TextRenderBody.toBuild(requestId,
                            LLMConstants.INTERRUPT_DRML,
                            true, 0, sessionContext.getSessionId(), true))
                    .code(0)
                    .message("interrupt")
                    .build());

        }

        long startTime = System.currentTimeMillis();
        // 发送缓冲话术
        if (sessionContext.getBufferSentencesConfigJson() != null
                && sessionContext.getBufferSentencesConfigJson().getStatus() == StatusType.OPEN.code()
                && sessionContext.getBufferSentencesConfigJson().getTime() > 0) {
            scheduler.schedule(new Runnable() {
                @Override
                public void run() {
                    // 发送缓冲话术
                    if (sessionContext.getResponseActiveTimeMillis() > startTime
                            && sessionContext.getResponseActiveTimeMillis() - startTime
                            < sessionContext.getBufferSentencesConfigJson().getTime()) {
                        log.info("Buffer sentence not trigger sessionId={}, requestId={}",
                                sessionContext.getSessionId(), request.getRequestId());
                        return;
                    }
                    if (!sessionContext.getPendingQueryMap().containsKey(request.getRequestId())) {
                        log.info("Buffer sentence has bean clear sessionId={}, requestId={}",
                                sessionContext.getSessionId(), request.getRequestId());
                        return;
                    }
                    int l = sessionContext.getBufferSentencesConfigJson().getSentences().size();
                    int index = (int) (Math.random() * l);
                    String sentence = sessionContext.getBufferSentencesConfigJson().getSentences().get(index);
                    WebsocketUtil.send(sessionContext.getWebSocketSession(), WebsocketLlmResponse.builder()
                            .action(ActionType.TEXT_RENDER)
                            .requestId(request.getRequestId())
                            .body(TextRenderBody.toBuild(requestId,
                                    sentence, true, 0, sessionContext.getSessionId(), true))
                            .code(0)
                            .message("Buffer sentence")
                            .build());
                }
            }, sessionContext.getBufferSentencesConfigJson().getTime(), TimeUnit.MILLISECONDS);
        }
        Map<String, Object> allContexts = new HashMap<>();
        if (textRequestBody.getContext() != null) {
            allContexts.putAll(textRequestBody.getContext());
        }
        if (sessionContext.getLlmRoleModel() != null) {
            if (!CollectionUtils.isEmpty(sessionContext.getLlmRoleModel().getSkillList())) {
                allContexts.put("role_desc", sessionContext.getLlmRoleModel().getRoleDefine());
                for (Skill skill : sessionContext.getLlmRoleModel().getSkillList()) {
                    if (skill.getStatus() == StatusType.OPEN.code()) {
                        allContexts.put(skill.getSkillEngName(), skill.getContent());
                    }
                }
            }
        }

        if (sessionContext.getLlmConfig().getParams() != null && sessionContext.getLlmConfig()
                .getParams().containsKey(LLMConstants.USER_STATEMENT)) {
            // TODO 后面优化
            allContexts.put("user_statement", String.format(commonConfig.getLlmUserStatementTemplate(),
                    allContexts.getOrDefault("role_desc", ""),
                    allContexts.getOrDefault("chat_desc", ""),
                    allContexts.getOrDefault("refuse_desc", "")));
        }
        Query query = new Query();
        query.setQueryIndex(queryIndex);
        query.setText(textRequestBody.getText());
        query.setRequestId(requestId);
        query.setContext(allContexts);

        if (sessionContext.getLlmRoleModel().getKnowledge() != null
                && sessionContext.getLlmRoleModel().getKnowledge().getStatus() == StatusType.OPEN.code()) {
            Knowledge knowledge = sessionContext.getLlmRoleModel().getKnowledge();
            List<CopilotAction> copilotActions = new ArrayList<>();
            if (!CollectionUtils.isEmpty(knowledge.getPreset())) {
                knowledge.getPreset().stream().forEach(knowledgeBase -> {
                    CopilotAction copilotAction = new CopilotAction(knowledgeBase.getKnowledgeBaseId());
                    copilotActions.add(copilotAction);
                });
            }
            if (!CollectionUtils.isEmpty(knowledge.getPersonal())) {
                knowledge.getPersonal().stream().forEach(knowledgeBase -> {
                    CopilotAction copilotAction = new CopilotAction(knowledgeBase.getKnowledgeBaseId());
                    copilotActions.add(copilotAction);
                });
            }
            query.setCopilotActionList(copilotActions);
        }
        if (sessionContext.getLlmConfig().getPreActions() != null) {
            for (ToolConfig toolConfig : sessionContext.getLlmConfig().getPreActions()) {
                if (toolConfig.getToolType() == ToolType.BS_CART_NORMALIZE) {
                    if (query.getContext() == null) {
                        query.setContext(new HashMap<>());
                    }
                    if (!query.getContext().containsKey("cart")) {
                        query.getContext().put("cart", new ArrayList<>());
                    }
                    if (query.getContext() != null && query.getContext().containsKey("cart")) {
                        if (query.getContext().get("cart") != null) {
                            Map<String, Object> cartInfos = new HashMap<>();
                            cartInfos.put("cart", query.getContext().get("cart"));
                            cartInfos.put("cartItems", query.getContext().getOrDefault("cartItems", null));
                            cartInfos.put("query", query.getText());
                            if (cartInfos != null && !StringUtils.isEmpty((String) cartInfos
                                    .getOrDefault("query", ""))) {
                                log.info("Rewrite query, sessionId={}, query={}", sessionContext.getSessionId(),
                                        cartInfos.get("query"));
                                query.setText((String) cartInfos.get("query"));
                            }
                            cartInfos = text2GestureService.bsCartNormalize(cartInfos,
                                    toolConfig.getUrl(),
                                    query.getRequestId(),
                                    sessionContext);
                            query.getContext().putAll(cartInfos);
                        }
                    }
                } else if (toolConfig.getToolType() == ToolType.FAQ) {
                    String text = faqService.faqSearch(requestId, query.getText(), toolConfig);
                    // 如果FAQ能够回答就直接用faq
                    if (!StringUtils.isEmpty(text)) {
                        log.info("FaqSearch, sessionId={}, requestId={}, answer text={}",
                                sessionContext.getSessionId(), request, text);

                        text = text.replaceAll("&", "")
                                .replaceAll("<", "")
                                .replaceAll(">", "")
                                .replaceAll("\"", "")
                                .replaceAll("^", "")
                                .replaceAll("'", "");

                        LlmHistoryModel llmHistoryModel = new LlmHistoryModel();
                        llmHistoryModel.setLlmRoleId(sessionContext.getLlmRoleModel().getLlmRoleId());
                        llmHistoryModel.setQuery(query.getText());
                        llmHistoryModel.setChannel(sessionContext.getChannel());
                        llmHistoryModel.setAnswerType(ResponseType.FAQ.toString());
                        llmHistoryModel.setAnswer(text);
                        llmHistoryModel.setRequestId(query.getRequestId());
                        llmHistoryModel.setUid(sessionContext.getUid());
                        llmHistoryModel.setAppId(sessionContext.getAppId());
                        llmHistoryModel.setCharacterImage(sessionContext.getCharacterImage());
                        log.debug("Save llmHistoryModel:{}", llmHistoryModel);
                        try {
                            llmHistoryRepository.save(llmHistoryModel);
                        } catch (Exception e) {
                            log.error("Save llmHistoryModel error", e);
                        }

                        WebsocketUtil.send(sessionContext.getWebSocketSession(), WebsocketLlmResponse.builder()
                                .action(ActionType.TEXT_RENDER)
                                .requestId(request.getRequestId())
                                .body(TextRenderBody.toBuild(requestId,
                                        text, true, 0, sessionContext.getSessionId(), true))
                                .code(0)
                                .message("Buffer sentence")
                                .build());
                        return;
                    }
                }
            }
        }
        // 区别unit
        if (commonConfig.isBillingEnable() && !LLMConstants.START_QUERY.equals(textRequestBody.getText())) {
            boolean freezeQuota = llmBillingService.freezeQuota(sessionContext.getAccountId(),
                    sessionContext.getUid(), sessionContext.getWebSocketSession().getId(),
                    requestId, commonConfig.getBillingCost());
            if (!freezeQuota) {
                log.info("FreezeQuota failed, sessionId={}, requestId={}", sessionContext.getSessionId(), requestId);
                // 如果灵豆不足，分发布返回不同的分支
                if (Constants.PUBLISH_VERSION.equals(sessionContext.getConfigVersion())) {
                    throw new DigitalHumanCommonException(BizError.LLM_STOP.getCode(), BizError.LLM_STOP.getMessage());
                } else {
                    throw new DigitalHumanCommonException(BizError.BILLING_COIN_NOT_ENOUGH.getCode(),
                            BizError.BILLING_COIN_NOT_ENOUGH.getMessage());
                }
            }
            sessionContext.getFreezeSet().add(requestId);

        }

        sessionContext.getBotProviderService().query(sessionContext, query);
    }

    @Override
    public boolean support(ActionType actionType) {
        return actionType == ActionType.TEXT_QUERY;
    }

}
