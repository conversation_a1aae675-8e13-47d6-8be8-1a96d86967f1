package com.baidu.acg.piat.llm.dm.model.faq;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/53G-5UwZAd/NVu1R32grf/kX6KmOc4yVPYLf
 * {
 * "code": 0,
 * "msg": "success",
 * "data": {
 * "question_info": [
 * {
 * "appId": "digital-human-talk",
 * "qid": "1d048c81-8b34-453b-b10c-1314ae3f4caf",
 * "firstClass": "",
 * "secondClass": "",
 * "general": false,
 * "keywordGroups": [
 * ""
 * ],
 * "questions": [
 * "今天天气怎么样"
 * ],
 * "pinYin": [
 * "jin tian tian qi zen me yang"
 * ],
 * "answers": [
 * {
 * "type": "TEXT",
 * "tags": null,
 * "speak": [
 * {
 * "answer": "还行1"
 * }
 * ],
 * "expression": "",
 * "kv": null,
 * "text": {
 * "text": "",
 * "url": ""
 * },
 * "videos": null,
 * "videosDisplayFullScreen": false,
 * "images": null,
 * "imagesDisplayFullScreen": false,
 * "htmlUrl": "",
 * "html5DisplayFullScreen": false,
 * "recordings": {
 * "audioUrl": ""
 * },
 * "action": {
 * "groupId": "",
 * "groupName": ""
 * },
 * "httpRequest": {
 * "url": "",
 * "httpMethod": "",
 * "body": ""
 * },
 * "notInterrupted": false,
 * "richText": {
 * "content": "",
 * "displayFullScreen": false
 * }
 * }
 * ],
 * "createTime": "2023-02-01 20:12:40",
 * "logId": "",
 * "openEmbedding": true,
 * "callbackUrl": ""
 * }
 * ],
 * "cosine_score": 0.9,
 * "confidence": 1
 * }
 * }
 * copilot自动生成
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class FaqResponse {
    private int code;
    private String msg;
    private Data data;

    @lombok.Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Data {
        @JsonProperty(value = "question_info")
        private QuestionInfo[] questionInfo;

        @JsonProperty(value = "cosine_score")
        private double cosineScore;
        private int confidence;
    }

    @lombok.Data
    public static class QuestionInfo {
        private String appId;
        private String qid;
        private String firstClass;
        private String secondClass;
        private boolean general;
        private String[] keywordGroups;
        private String[] questions;
        private String[] pinYin;
        private Answer[] answers;
        private String createTime;
        private String logId;
        private boolean openEmbedding;
        private String callbackUrl;
    }

    @lombok.Data
    public static class Answer {
        private String type;
        private String[] tags;
        private Speak[] speak;
        private String expression;
        private KV kv;
        private Text text;
        private Video[] videos;
        private boolean videosDisplayFullScreen;
        private Image[] images;
        private boolean imagesDisplayFullScreen;
        private String htmlUrl;
        private boolean html5DisplayFullScreen;
        private Recordings recordings;
        private Action action;
        private HttpRequest httpRequest;
        private boolean notInterrupted;
        private RichText richText;
    }

    @lombok.Data
    public static class Speak {
        private String answer;
    }

    @lombok.Data
    public static class KV {
    }

    @lombok.Data
    public static class Text {
        private String text;
        private String url;
    }

    @lombok.Data
    public static class Video {
    }

    @lombok.Data
    public static class Image {
    }

    @lombok.Data
    public static class Recordings {
        private String audioUrl;
    }

    @lombok.Data
    public static class Action {
        private String groupId;
        private String groupName;
    }

    @lombok.Data
    public static class HttpRequest {
        private String url;
        private String httpMethod;
        private String body;
    }

    @lombok.Data
    public static class RichText {
        private String content;
        private boolean displayFullScreen;
    }
}
