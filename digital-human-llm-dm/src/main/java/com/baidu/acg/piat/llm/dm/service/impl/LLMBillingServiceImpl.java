package com.baidu.acg.piat.llm.dm.service.impl;

import java.util.List;

import org.springframework.stereotype.Service;

import com.baidu.acg.dh.user.client.AccountQuotaClient;
import com.baidu.acg.dh.user.client.model.vo.AccountQuotaPackageSummaryRes;
import com.baidu.acg.dh.user.client.model.vo.ConfirmedCostQuotaReq;
import com.baidu.acg.dh.user.client.model.vo.ConfirmedCostQuotaRes;
import com.baidu.acg.dh.user.client.model.vo.FreezeQuotaReq;
import com.baidu.acg.dh.user.client.model.vo.FreezeQuotaRes;
import com.baidu.acg.dh.user.client.model.vo.UnFreezeQuotaReq;
import com.baidu.acg.dh.user.client.model.vo.UnFreezeQuotaRes;
import com.baidu.acg.piat.llm.dm.service.LLMBillingService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
@RequiredArgsConstructor
public class LLMBillingServiceImpl implements LLMBillingService {

    private static final String COIN = "LLM_DIALOG_AMOUNT";

    private final AccountQuotaClient accountQuotaClient;

    @Override
    public boolean freezeQuota(String accountId, String userId,
                               String sessionId, String requestId, int amount) {
        try {
            FreezeQuotaReq freezeQuotaReq = new FreezeQuotaReq();
            freezeQuotaReq.setQuotaFreezeAmount(amount);
            freezeQuotaReq.setQuotaType(COIN);
            freezeQuotaReq.setAccountId(accountId);
            freezeQuotaReq.setUserId(userId);
            freezeQuotaReq.setAssetId(sessionId + "_" + requestId);
            log.info("FreezeQuota freezeQuotaReq: {}", freezeQuotaReq);
            FreezeQuotaRes freezeQuotaRes = accountQuotaClient.freezeQuota(freezeQuotaReq);
            log.info("FreezeQuota freezeQuotaRes: {}", freezeQuotaRes);
            return true;
        } catch (Exception e) {
            log.warn("FreezeQuota error: {}", e);
            return false;
        }
    }


    @Override
    public boolean confirmedCost(String accountId, String sessionId, String requestId, int amount) {
        try {
            ConfirmedCostQuotaReq confirmedCostQuotaReq = new ConfirmedCostQuotaReq();
            confirmedCostQuotaReq.setQuotaConfirmedCostAmount(amount);
            confirmedCostQuotaReq.setQuotaType(COIN);
            confirmedCostQuotaReq.setAccountId(accountId);
            confirmedCostQuotaReq.setAssetId(sessionId + "_" + requestId);
            log.info("ConfirmedCost confirmedCostQuotaReq: {}", confirmedCostQuotaReq);
            ConfirmedCostQuotaRes confirmedCostQuotaRes = accountQuotaClient.confirmedCost(confirmedCostQuotaReq);
            log.info("ConfirmedCost confirmedCostQuotaRes: {}", confirmedCostQuotaRes);
            return true;
        } catch (Exception e) {
            // 如果确认失败，需要人工干预
            log.error("ConfirmedCost error: {}", e);
            return false;
        }
    }

    @Override
    public boolean unfreezeQuota(String accountId, String sessionId, String requestId, int amount) {
        try {
            UnFreezeQuotaReq unfreezeQuotaReq = new UnFreezeQuotaReq();
            unfreezeQuotaReq.setQuotaUnFreezeAmount(amount);
            unfreezeQuotaReq.setQuotaType(COIN);
            unfreezeQuotaReq.setAccountId(accountId);
            unfreezeQuotaReq.setAssetId(sessionId + "_" + requestId);
            log.info("UnfreezeQuota unfreezeQuotaReq: {}", unfreezeQuotaReq);
            UnFreezeQuotaRes unFreezeQuotaRes = accountQuotaClient.unFreezeQuota(unfreezeQuotaReq);
            log.info("UnfreezeQuota unFreezeQuotaRes: {}", unFreezeQuotaRes);
            return true;
        } catch (Exception e) {
            log.warn("UnfreezeQuota error: {}", e);
            return false;
        }
    }

    @Override
    public long summary(String accountId) {
        log.info("Summary request accountId: {}", accountId);
        AccountQuotaPackageSummaryRes accountQuotaPackageSummaryRes = accountQuotaClient.summary(accountId, COIN);
        log.info("AccountQuotaPackageSummaryRes: {}", accountQuotaPackageSummaryRes);
        if (accountQuotaPackageSummaryRes != null) {
            List<AccountQuotaPackageSummaryRes.QuotaData> quotaData = accountQuotaPackageSummaryRes.getQuotaData();
            if (quotaData != null && !quotaData.isEmpty()) {
                for (AccountQuotaPackageSummaryRes.QuotaData data : quotaData) {
                    if (COIN.equals(data.getQuotaType())) {
                        return data.getQuotaTotalbalance();
                    }
                }
            }
        }
        return 0;
    }
}
