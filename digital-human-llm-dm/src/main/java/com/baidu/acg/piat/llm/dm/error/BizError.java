package com.baidu.acg.piat.llm.dm.error;

import lombok.Getter;

/**
 * 错误码定义
 *
 * <AUTHOR>
 * @date 2023/11/2
 */
public enum BizError {
    SUCCESS(0, "ok"),
    UNKNOWN_INTERNAL_ERROR(-1, "unknown internal error"),
    UNKNOWN_RESPONSE_MODE(1, "unknown response mode"),
    REQUEST_VALIDATE_ERROR(2, "request validating error"),
    NO_LLM_ENGINE_SELECT(3, "no suitable llm engine"),
    LLM_ENGINE_ERROR(4, "llm engine error"),
    LLM_ENGINE_AUTH_ERROR(5, "llm engine auth error"),

    BILLING_COIN_NOT_ENOUGH(7001, "灵豆不足"),
    LLM_STOP(7002, "服务已经停止");

    @Getter
    private final int code;
    @Getter
    private final String message;

    BizError(int code, String message) {
        this.code = code;
        this.message = message;
    }
}
