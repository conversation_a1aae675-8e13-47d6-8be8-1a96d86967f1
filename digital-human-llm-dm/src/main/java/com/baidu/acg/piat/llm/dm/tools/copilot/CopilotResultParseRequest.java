package com.baidu.acg.piat.llm.dm.tools.copilot;

import java.util.Map;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class CopilotResultParseRequest {

    private String requestId;

    private String sessionId;

    private String originText;


    private Map<String, String> normalization;

    private Map<String, Object> context;

}
