// Copyright (C) 2019 Baidu Inc. All rights reserved.
package com.baidu.acg.piat.llm.dm.service.impl;

import static com.baidu.acg.piat.llm.dm.constants.LLMConstants.START_QUERY;

import java.nio.charset.StandardCharsets;
import java.time.Duration;

import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.util.StringUtils;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.reactive.function.client.WebClientResponseException;

import com.baidu.acg.piat.digitalhuman.common.llmrole.LLMConfig;
import com.baidu.acg.piat.digitalhuman.common.project.BotParams;
import com.baidu.acg.piat.digitalhuman.common.utils.JsonUtil;
import com.baidu.acg.piat.llm.dm.configure.CommonConfig;
import com.baidu.acg.piat.llm.dm.model.PendingTask;
import com.baidu.acg.piat.llm.dm.model.Query;
import com.baidu.acg.piat.llm.dm.model.SessionContext;
import com.baidu.acg.piat.llm.dm.model.keyue.ChatBotResultDto;
import com.baidu.acg.piat.llm.dm.model.unit.ChatBotQueryParam;
import com.baidu.acg.piat.llm.dm.service.BotProviderService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.Disposable;
import reactor.core.publisher.Flux;

/**
 * 是UNIT的新版本, 客悦平台
 */
@RequiredArgsConstructor
@Slf4j
public class BotServiceKeYueStreamImpl implements BotProviderService {

    WebClient webClient = WebClient.builder().build();

    public static final String BOT_TYPE = BotParams.BotTypeEnum.KE_YUE.getBotType();

    private final CommonConfig config;

    @Override
    public void query(SessionContext sessionContext, Query query) {
        LLMConfig llmConfig = sessionContext.getLlmConfig();
        log.info("LLmConfig={}", llmConfig);
        if (query.getText().equals(START_QUERY)) {
            query.setText("");
        }
        ChatBotQueryParam param = new ChatBotQueryParam();
        if (!StringUtils.isEmpty(sessionContext.getUnitSessionId())) {
            param.setSessionId(sessionContext.getUnitSessionId());
        } else {
            param.setSessionId("");
        }
        param.setQueryText(query.getText());
        param.setContext(query.getContext());
        String url = llmConfig.getUrl();
        if (StringUtils.isEmpty(url)) {
            url = config.getKeYueUrl();
        }
        try {
            log.info("Request keyue url:{}, body:{}", url, JsonUtil.writeValueAsString(param));
        } catch (Exception e) {

        }
        Flux<String> responseFlux = webClient.post()
                .uri(url) // 你的端点
                .headers(header -> header.addAll(getHeaders(llmConfig)))
                .bodyValue(param)
                .acceptCharset(StandardCharsets.UTF_8)
                .accept(MediaType.TEXT_EVENT_STREAM)
                .retrieve()
                .bodyToFlux(String.class); // 你的返回类型
        handleResponse(responseFlux, sessionContext, query);
    }

    private void handleResponse(Flux<String> responseFlux, SessionContext sessionContext, Query query) {
        Disposable disposable = responseFlux
                .timeout(Duration.ofSeconds(sessionContext.getLlmConfig().getTimeout()))
                .doOnCancel(() -> {
                    log.debug("Cancelled, query={}", query);
                    sessionContext.getLlmStreamObserver().onCancel(query);
                }).doOnError(e -> {
                    if (e instanceof WebClientResponseException) {
                        WebClientResponseException exception = (WebClientResponseException) e;
                        String errorBody = exception.getResponseBodyAsString();
                        log.info("Error response body:{}", errorBody);
                        // 这里可以根据errorBody进一步处理错误
                    }
                    // 打印错误
                    log.error("Handle response sessionId={}, requestId={}, Error",
                            sessionContext.getSessionId(),
                            query.getRequestId(), e);
                    sessionContext.getLlmStreamObserver().onError(e, query);

                }).doOnComplete(() -> {
                    sessionContext.getLlmStreamObserver().onCompleted(query);
                    // 打印结束
                    log.info("Hanlde response sessionId={}, requestId={} Complete", sessionContext.getSessionId(),
                            query.getRequestId());
                }).subscribe(r -> {
                    // 打印每个接收到的chunk
                    log.info("Received keyue chunk: {}", r);
                    try {
                        ChatBotResultDto response = JsonUtil.readValue(r, ChatBotResultDto.class);
                        if (response.getAnswer() != null && response
                                .getAnswer().get(0).getReply() != null && !StringUtils.isEmpty(
                                response.getAnswer().get(0).getReply().getText())) {
                            sessionContext.setUnitSessionId(response.getSessionId());
                            String text = response.getAnswer().get(0).getReply().getText();
                            if (text.endsWith("]")) {
                                text = text + "^";
                            }
                            text = text.replaceAll("\\*", "");
                            log.debug("Received keyue answer: {}", text);
                            sessionContext.getLlmStreamObserver().onNext(text, "largeModel", query);
                        }
                    } catch (Exception e) {
                        log.error("Parse response error", e);
                    }
                });

        sessionContext.getPendingQueryMap().put(query.getRequestId(),
                PendingTask.builder().queryIndex(query.getQueryIndex()).disposable(disposable).build());
        log.info("Insert requestId={} to pendingQueryMap={}, wsId={}, query={}", query.getRequestId(),
                sessionContext.getWebSocketSession().getId(), query);

    }

    /**
     * @param 如果没有taskID, 会中断所有pending请求
     * @return
     */
    public void interrupt(SessionContext sessionContext, String taskId) {
        if (sessionContext.getPendingQueryMap().isEmpty()) {
            return;
        }
        // 批量删除
        if (StringUtils.isEmpty(taskId)) {
            log.info("Interrupt all={}", sessionContext.getPendingQueryMap().keySet());
            sessionContext.getPendingQueryMap().forEach((k, v) -> {
                v.getDisposable().dispose();
            });
            int i = 0;
            // 判断是否真的中断, 一次性给所有的pending发中断指令
            while (sessionContext.getPendingQueryMap().keySet().stream().anyMatch(k -> {
                return !sessionContext.getPendingQueryMap().get(k).getDisposable().isDisposed();
            }) && i < 3) {
                try {
                    Thread.sleep(100);
                } catch (InterruptedException e) {
                    log.error("Interrupt error", e);
                }
                i++;
            }
            sessionContext.getPendingQueryMap().keySet().forEach(k -> {
                clear(k, sessionContext);
            });
            return;
        }
        PendingTask pendingTask = sessionContext.getPendingQueryMap().get(taskId);
        if (pendingTask != null) {
            log.info("Interrupt task id={}", taskId);
            // 会有延迟不一定立马中断
            pendingTask.getDisposable().dispose();
            int i = 0;
            // 判断是否真的中断
            while (!pendingTask.getDisposable().isDisposed() && i < 3) {
                try {
                    Thread.sleep(100);
                } catch (InterruptedException e) {
                    log.error("Interrupt error", e);
                }
                i++;
            }
            clear(taskId, sessionContext);
        }
    }

    public void clear(String taskId, SessionContext sessionContext) {
        log.debug("Clear task id={}", taskId);
        if (StringUtils.isEmpty(taskId)) {
            return;
        }
        sessionContext.getPendingQueryMap().remove(taskId);
        sessionContext.getPendingResponseSeqMap().remove(taskId);
        sessionContext.getPendingResponseMap().remove(taskId);
    }

    private HttpHeaders getHeaders(LLMConfig llmConfig) {
        HttpHeaders headers = new HttpHeaders();
        headers.add("Token", llmConfig.getCredential().get("token"));
        headers.add("Content-Type", "application/json;charset=UTF-8");
        return headers;
    }

    @Override
    public String getBotType() {
        return BOT_TYPE;
    }
}
