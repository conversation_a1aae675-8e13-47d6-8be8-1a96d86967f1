package com.baidu.acg.piat.llm.dm.configure;

import com.baidu.acg.piat.llm.dm.client.WebClientHelper;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.reactive.function.client.WebClient;

import java.util.Map;

/**
 * 配置类
 *
 * <AUTHOR>
 * @date 2023/11/8
 */
@Configuration
@ConditionalOnProperty(prefix = "adaptor.llm", name = "mode", havingValue = "qianfan", matchIfMissing = false)
@Slf4j
public class QianfanLLMEngineConfiguration {

    @ConfigurationProperties(prefix = "adaptor.engine.qianfan")
    @Bean
    public QianfanLLMConfig qianfanLLMConfig() {
        return new QianfanLLMConfig();
    }

    @Bean("qianfanClient")
    public WebClient webClient() {
        QianfanLLMConfig qianfanConfig = qianfanLLMConfig();
        WebClient.Builder builder =
                WebClient.builder().baseUrl(qianfanConfig.baseUrl).filters(WebClientHelper.logFiltersFunction);
        return builder.build();
    }

    @Data
    public static class QianfanLLMConfig {
        private String baseUrl;
        private String authUrl;
        private long tokenExpireInMin = 60;
        private String ak;
        private String sk;
        private String model;
        private Map<String, String> engineUriMap;
        private LLMConfig llmConfig = new LLMConfig();
    }

    @Data
    public static class LLMConfig {
        private Float temperature = 0.95f;
        private Float topP = 0.8f;
        private Float penaltyScore = 1.0f;
    }
}
