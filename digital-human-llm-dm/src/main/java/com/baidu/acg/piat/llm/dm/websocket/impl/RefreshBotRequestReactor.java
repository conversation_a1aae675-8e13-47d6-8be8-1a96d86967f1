package com.baidu.acg.piat.llm.dm.websocket.impl;

import java.util.HashMap;
import java.util.Optional;
import java.util.UUID;

import com.baidu.acg.piat.llm.dm.client.HttpCommonClient;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.client.WebClient;

import com.alibaba.excel.util.StringUtils;
import com.baidu.acg.piat.digitalhuman.common.alita.ActionType;
import com.baidu.acg.piat.digitalhuman.common.exception.DigitalHumanCommonException;
import com.baidu.acg.piat.digitalhuman.common.llmdm.WebsocketLlmRequest;
import com.baidu.acg.piat.digitalhuman.common.llmdm.WebsocketLlmResponse;
import com.baidu.acg.piat.digitalhuman.common.llmrole.LLMConfig;
import com.baidu.acg.piat.digitalhuman.common.project.BotParams;
import com.baidu.acg.piat.digitalhuman.common.session.SessionStatus;
import com.baidu.acg.piat.llm.dm.configure.CommonConfig;
import com.baidu.acg.piat.llm.dm.constants.LLMConstants;
import com.baidu.acg.piat.llm.dm.dao.LlmRoleRepository;
import com.baidu.acg.piat.llm.dm.model.LlmRoleModel;
import com.baidu.acg.piat.llm.dm.model.Query;
import com.baidu.acg.piat.llm.dm.model.SessionContext;
import com.baidu.acg.piat.llm.dm.service.LLMConversationSessionContext;
import com.baidu.acg.piat.llm.dm.utils.SessionUtil;
import com.baidu.acg.piat.llm.dm.utils.WebsocketUtil;
import com.baidu.acg.piat.llm.dm.websocket.RequestReactor;

import io.vavr.control.Try;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
@RequiredArgsConstructor
public class RefreshBotRequestReactor implements RequestReactor {

    private final LlmRoleRepository llmRoleRepository;

    private final CommonConfig config;
    private final LLMConversationSessionContext llmConversationSessionContext;
    private final WebClient webClient;
    private final HttpCommonClient httpCommonClient;

    @Override
    public void react(SessionContext sessionContext, WebsocketLlmRequest request) {
        log.info("TextQueryRequestReactor, sessionId={}, request={}", sessionContext.getSessionId(), request);
        if (sessionContext == null || sessionContext.getStatus() != SessionStatus.OPEN) {
            // 对于还没有连接好的请求直接发送query过来直接丢掉
            log.warn("Session is not open, session={}, request={}", sessionContext, request);
            return;
        }

        LlmRoleModel llmDmConfigModel = llmRoleRepository.findByLlmRoleId(sessionContext
                        .getLlmRoleModel().getLlmRoleId())
                .orElseThrow(() -> new DigitalHumanCommonException("大模型配置不存在"));
        sessionContext.setLlmRoleModel(llmDmConfigModel);
        if (!StringUtils.isEmpty(llmDmConfigModel.getTemplateId())) {
            Try.run(() -> {
                Optional.ofNullable(llmRoleRepository.findByLlmRoleId(llmDmConfigModel
                                .getTemplateId()).orElseGet(null))
                        .map(templateModel -> {
                            log.debug("Get templateModel={}", templateModel);
                            // TODO 使用模板里的大模型配置
                            llmDmConfigModel.setLlmConfig(templateModel.getLlmConfig());
                            return null;
                        });
            }).onFailure(e -> {
                log.debug("TemplateId is not exist, templateId={}", llmDmConfigModel.getTemplateId());
            });
        }

        log.info("LLmDmConfigModel={}", llmDmConfigModel);
        LLMConfig llmConfig = llmDmConfigModel.getLlmConfig();
        sessionContext.setCopilotConversationId("");
        sessionContext.setUnitSessionId("");
        sessionContext.setLlmConfig(llmConfig);
        SessionUtil.iniBotProviderService(sessionContext, llmDmConfigModel, request,
                config, webClient, llmConversationSessionContext, httpCommonClient);
        if (sessionContext.getBotProviderService().getBotType()
                .equals(BotParams.BotTypeEnum.KE_YUE.getBotType())) {
            Query query = new Query();
            int queryIndex = sessionContext.getQueryIndex().get();
            query.setQueryIndex(queryIndex);
            query.setText(LLMConstants.START_QUERY);
            query.setRequestId(UUID.randomUUID().toString());
            query.setContext(new HashMap<>());
            sessionContext.getBotProviderService().query(sessionContext, query);
        }
        log.info("Refresh bot success, sessionId={}", sessionContext.getSessionId());
        WebsocketUtil.send(sessionContext.getWebSocketSession(),
                WebsocketLlmResponse.success(request, llmConfig));

    }

    @Override
    public boolean support(ActionType actionType) {
        return actionType == ActionType.REFRESH_BOT;
    }
}
