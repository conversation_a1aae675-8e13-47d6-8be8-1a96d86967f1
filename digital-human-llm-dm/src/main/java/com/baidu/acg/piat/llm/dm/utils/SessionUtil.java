package com.baidu.acg.piat.llm.dm.utils;

import com.baidu.acg.piat.llm.dm.client.HttpCommonClient;
import org.springframework.web.reactive.function.client.WebClient;

import com.baidu.acg.piat.digitalhuman.common.exception.DigitalHumanCommonException;
import com.baidu.acg.piat.digitalhuman.common.llmdm.WebsocketLlmRequest;
import com.baidu.acg.piat.digitalhuman.common.llmrole.LLMConfig;
import com.baidu.acg.piat.digitalhuman.common.project.BotParams;
import com.baidu.acg.piat.llm.dm.configure.CommonConfig;
import com.baidu.acg.piat.llm.dm.model.LlmRoleModel;
import com.baidu.acg.piat.llm.dm.model.SessionContext;
import com.baidu.acg.piat.llm.dm.service.LLMConversationSessionContext;
import com.baidu.acg.piat.llm.dm.service.impl.BotServiceCopilotStreamImpl;
import com.baidu.acg.piat.llm.dm.service.impl.BotServiceKaiWuStreamImpl;
import com.baidu.acg.piat.llm.dm.service.impl.BotServiceKeYueStreamImpl;
import com.baidu.acg.piat.llm.dm.service.impl.BotServiceQianfanStreamImpl;
import com.baidu.acg.piat.llm.dm.service.impl.BotServiceUnitStreamImpl;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class SessionUtil {
    public static void iniBotProviderService(SessionContext sessionContext,
                                             LlmRoleModel llmDmConfigModel, WebsocketLlmRequest request,
                                             CommonConfig config, WebClient webClient,
                                             LLMConversationSessionContext llmConversationSessionContext,
                                             HttpCommonClient httpCommonClient) {
        log.info("Init LLmDmConfigModel={}", llmDmConfigModel);
        LLMConfig llmConfig = llmDmConfigModel.getLlmConfig();
        if (llmConfig.getBotType().equals(BotParams.BotTypeEnum.UNIT_LLM.getBotType())) {
            log.debug("INIT NGD bot={}", request);
            sessionContext.setBotProviderService(new BotServiceUnitStreamImpl(config));
        } else if (llmDmConfigModel.getLlmConfig().getBotType()
                .equals(BotParams.BotTypeEnum.COPILOT.getBotType())) {
            log.debug("INIT COPILOT BOT:{}", request);
            sessionContext.setBotProviderService(new BotServiceCopilotStreamImpl());
        } else if (llmDmConfigModel.getLlmConfig().getBotType()
                .equals(BotParams.BotTypeEnum.QIAN_FAN.getBotType())) {
            log.debug("INIT QIANFAN BOT:{}", request);
            sessionContext.setBotProviderService(
                    new BotServiceQianfanStreamImpl(llmConversationSessionContext, webClient, httpCommonClient));
        } else if (llmDmConfigModel.getLlmConfig().getBotType()
                .equals(BotParams.BotTypeEnum.KAI_WU.getBotType())) {
            log.debug("INIT KAIWU BOT:{}", request);
            sessionContext.setBotProviderService(new BotServiceKaiWuStreamImpl(llmConversationSessionContext));
        } else if (llmDmConfigModel.getLlmConfig().getBotType()
                .equals(BotParams.BotTypeEnum.KE_YUE.getBotType())) {
            log.debug("INIT KE_YUE BOT:{}", request);
            sessionContext.setBotProviderService(new BotServiceKeYueStreamImpl(config));
        } else {
            log.error("BotType is not exist, botType={}", llmDmConfigModel.getLlmConfig().getBotType());
            throw new DigitalHumanCommonException("BotType is not exist");
        }
        sessionContext.setLlmConfig(llmConfig);
        sessionContext.setBufferSentencesConfigJson(llmDmConfigModel.getBufferSentencesConfigJson());
    }
}
