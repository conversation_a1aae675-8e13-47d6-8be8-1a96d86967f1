// Copyright (C) 2019 Baidu Inc. All rights reserved.
package com.baidu.acg.piat.llm.dm.service.impl;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.time.Duration;
import java.util.HashMap;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.web.reactive.function.client.WebClient;

import com.baidu.acg.piat.digitalhuman.common.llmrole.LLMConfig;
import com.baidu.acg.piat.digitalhuman.common.project.BotParams;
import com.baidu.acg.piat.digitalhuman.common.utils.JsonUtil;
import com.baidu.acg.piat.digitalhuman.common.utils.JsonUtils;
import com.baidu.acg.piat.llm.dm.configure.AppBuilderConfig;
import com.baidu.acg.piat.llm.dm.model.PendingTask;
import com.baidu.acg.piat.llm.dm.model.Query;
import com.baidu.acg.piat.llm.dm.model.SessionContext;
import com.baidu.acg.piat.llm.dm.model.appbuilder.AgentBuilderRequest;
import com.baidu.acg.piat.llm.dm.model.appbuilder.AgentBuilderResponse;
import com.baidu.acg.piat.llm.dm.model.appbuilder.ConversationResponse;
import com.baidu.acg.piat.llm.dm.service.BotProviderService;

import io.vavr.control.Try;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;
import reactor.core.Disposable;
import reactor.core.publisher.Flux;

@RequiredArgsConstructor
@Slf4j
public class BotServiceAppBuilderStreamImpl implements BotProviderService {

    WebClient webClient = WebClient.builder().build();

    public static final String BOT_TYPE = BotParams.BotTypeEnum.APP_BUILDER.getBotType();

    public static final String COPILOT_TOKEN_PREFIX = "Bearer ";

    OkHttpClient okHttpClient;

    private static final String CONTENT_TYPE = "content-type";

    private static final String CONTENT_TYPE_VALUE = "application/json; charset=utf-8";

    private static final okhttp3.MediaType MEDIA_TYPE = okhttp3.MediaType.parse("application/json; charset=utf-8");

    public BotServiceAppBuilderStreamImpl(OkHttpClient okHttpClient) {
        this.okHttpClient = okHttpClient;
    }

    public void init(SessionContext sessionContext) {
        String id = createConversation(sessionContext.getLlmConfig());
        sessionContext.setCopilotConversationId(id);
    }

    /**
     * 创建会话
     *
     * @return 返回会话ID
     * @throws IOException 当请求失败时抛出IOException
     */
    public String createConversation(LLMConfig llmConfig) {
        if (llmConfig.getParams() == null || !llmConfig.getParams().containsKey("appId")) {
            throw new RuntimeException("Param 'appId' is required!");
        }
        String appId = llmConfig.getParams().get("appId").toString();
        String url = AppBuilderConfig.APPBUILDER_DEFAULT_GATEWAY + AppBuilderConfig.CREATE_CONVERSATION_URL;
        Map<String, String> requestBody = new HashMap<>();
        requestBody.put("app_id", appId);

        try {
            String jsonBody = JsonUtils.toJsonString(requestBody);
            // 创建请求
            Request request = new Request.Builder().url(url)
                    .addHeader("Authorization", COPILOT_TOKEN_PREFIX
                            + llmConfig.getCredential().get("token").toString())
                    .addHeader(CONTENT_TYPE, CONTENT_TYPE_VALUE)
                    .post(RequestBody.create(MEDIA_TYPE,
                            jsonBody)).build();
            log.info("Request AppBuilder url={}, req body={}", url, jsonBody);
            Response response = okHttpClient.newCall(request).execute();
            if (response.isSuccessful()) {
                String responseBody = response.body().string();
                log.info("Request appbuilder conversation response body={}", responseBody);
                ConversationResponse conversationResponse = Try.of(() -> JsonUtil.readValue(responseBody,
                        ConversationResponse.class)).getOrNull();
                return conversationResponse.getConversationId();
            } else {
                log.info("Response create conversation={}", response);
                return null;
            }
        } catch (Exception e) {
            log.error("Failed to create conversation response err", e);
            return null;
        }
    }

    /**
     * 只用于http接口测试
     *
     * @param sessionContext
     * @param query
     * @param llmConfig
     * @return
     */
    public Flux<String> queryAppBuilderWithReturn(SessionContext sessionContext, Query query, LLMConfig llmConfig) {
        // TODO
        String url = AppBuilderConfig.APPBUILDER_DEFAULT_GATEWAY + AppBuilderConfig.AGENTBUILDER_RUN_URL;
        if (llmConfig.getParams() == null || !llmConfig.getParams().containsKey("appId")) {
            throw new RuntimeException("Param 'appId' is required!");
        }
        AgentBuilderRequest agentBuilderRequest = new AgentBuilderRequest();
        agentBuilderRequest.setAppId(llmConfig.getParams().get("appId").toString());
        if (llmConfig.getParams().containsKey("stream")) {
            agentBuilderRequest.setStream(Boolean.valueOf(llmConfig.getParams().get("stream").toString()));
        }
        agentBuilderRequest.setQuery(query.getText());
        agentBuilderRequest.setConversationId(sessionContext.getCopilotConversationId());
        if (query.getContext() != null && query.getContext().containsKey("fileIds")) {
            try {
                agentBuilderRequest.setFileIds(JsonUtil.strToList(JsonUtil.writeValueAsString(query.getContext().get(
                        "fileIds")), String.class));
            } catch (IOException e) {
                log.error("Parse fileIds error", e);
            }
        }

        try {
            log.info("Request appbuilder  url:{}, body:{}, token={}", url,
                    JsonUtil.writeValueAsString(agentBuilderRequest),
                    llmConfig.getCredential().get("token"));
        } catch (Exception e) {

        }
        try {
            Flux<String> responseFlux = webClient.post()
                    .uri(url) // 你的端点
                    .headers(header -> header.addAll(getHeaders(llmConfig)))
                    .bodyValue(agentBuilderRequest)
                    .acceptCharset(StandardCharsets.UTF_8)
                    .accept(MediaType.APPLICATION_NDJSON)
                    .retrieve()
                    .bodyToFlux(String.class);
            responseFlux.onErrorResume(e -> {
                log.error("Dify error 2:{}", e);
                return Flux.empty();
            }).subscribe(response -> {
                log.info("Dify Response:{}", response);
            });
            return responseFlux;
        } catch (Exception e) {
            log.error("Dify error 1:{}", e.getMessage());
            return Flux.empty();
        }
    }

    /**
     * 查询数据。
     *
     * @param sessionContext 会话上下文
     * @param query          查询条件
     */
    @Override
    public void query(SessionContext sessionContext, Query query) {
        LLMConfig llmConfig = sessionContext.getLlmConfig();
        String url = AppBuilderConfig.APPBUILDER_GATEWAY_URL + AppBuilderConfig.AGENTBUILDER_RUN_URL;
        if (llmConfig.getParams() == null || !llmConfig.getParams().containsKey("appId")) {
            throw new RuntimeException("Param 'appId' is required!");
        }
        AgentBuilderRequest agentBuilderRequest = new AgentBuilderRequest();
        agentBuilderRequest.setAppId(llmConfig.getParams().get("appId").toString());
        if (llmConfig.getParams().containsKey("stream")) {
            agentBuilderRequest.setStream(Boolean.valueOf(llmConfig.getParams().get("stream").toString()));
        }
        agentBuilderRequest.setQuery(query.getText());
        agentBuilderRequest.setConversationId(sessionContext.getCopilotConversationId());
        if (query.getContext() != null && query.getContext().containsKey("fileIds")) {
            try {
                agentBuilderRequest.setFileIds(JsonUtil.strToList(JsonUtil.writeValueAsString(query.getContext().get(
                        "fileIds")), String.class));
            } catch (IOException e) {
                log.error("Parse fileIds error", e);
            }
        }

        try {
            log.info("Request appbuilder  url:{}, body:{}, token={}", url,
                    JsonUtil.writeValueAsString(agentBuilderRequest),
                    llmConfig.getCredential().get("token"));
        } catch (Exception e) {

        }
        Flux<String> responseFlux = webClient.post()
                .uri(url) // 你的端点
                .headers(header -> header.addAll(getHeaders(llmConfig)))
                .bodyValue(agentBuilderRequest)
                .acceptCharset(StandardCharsets.UTF_8)
                .accept(MediaType.APPLICATION_NDJSON)
                .retrieve()
                .bodyToFlux(String.class); // 你的返回类型

        handleResponse(responseFlux, sessionContext, query);
    }

    @Override
    public void reset(SessionContext sessionContext) {
        sessionContext.setCopilotConversationId(StringUtils.EMPTY);
    }

    private void handleResponse(Flux<String> responseFlux, SessionContext sessionContext, Query query) {
        Disposable disposable = responseFlux
                .timeout(Duration.ofSeconds(sessionContext.getLlmConfig().getTimeout()))
                .doOnCancel(() -> {
                    log.debug("Cancelled, query={}", query);
                    sessionContext.getLlmStreamObserver().onCancel(query);
                }).doOnError(e -> {
                    // 打印错误
                    log.error("Handle response sessionId={}, requestId={}, Error", sessionContext.getSessionId(),
                            query.getRequestId(), e);
                    sessionContext.getLlmStreamObserver().onError(e, query);

                }).doOnComplete(() -> {
                    sessionContext.getLlmStreamObserver().onCompleted(query);
                    // 打印结束
                    log.info("Hanlde response sessionId={}, requestId={} Complete", sessionContext.getSessionId(),
                            query.getRequestId());
                }).subscribe(response -> {
                    // 打印每个接收到的chunk
                    log.info("Received chunk: {}", response);
                    // 请求copilot的中控，DTE
                    // 直接请求copilot的单体应用
                    Try.of(() -> JsonUtil.readValue(response, AgentBuilderResponse.class))
                            .onFailure(e -> log.error("Parse response error:{}", e.getMessage()))
                            .onSuccess(r -> {
                                if (r != null && !StringUtils.isEmpty(r.getAnswer())) {
                                    sessionContext.getLlmStreamObserver().onNext(r.getAnswer(),
                                            "largeModel", query);
                                }
                            });

                });

        sessionContext.getPendingQueryMap().put(query.getRequestId(),
                PendingTask.builder().queryIndex(query.getQueryIndex()).disposable(disposable).build());

    }

    /**
     * @return
     */
    public void interrupt(SessionContext sessionContext, String taskId) {
        if (sessionContext.getPendingQueryMap().isEmpty()) {
            return;
        }
        // 清理一下过期的taskId；
        sessionContext.getInterruptedMap().entrySet().stream().filter(e -> {
            return System.currentTimeMillis() - e.getValue() > 1000 * 60 * 5;
        }).forEach(e -> {
            log.info("Interrupt timeout interrupted taskId={}", e.getKey());
            sessionContext.getInterruptedMap().remove(e.getKey());
        });
        // 批量删除
        if (StringUtils.isEmpty(taskId)) {
            log.info("Interrupt all={}", sessionContext.getPendingQueryMap().keySet());
            sessionContext.getPendingQueryMap().forEach((k, v) -> {
                v.getDisposable().dispose();
            });
            int i = 0;
            // 判断是否真的中断, 一次性给所有的pending发中断指令
            while (sessionContext.getPendingQueryMap().keySet().stream().anyMatch(k -> {
                log.info("Interrupt add interrupted sessionId={}, taskId={}", sessionContext.getSessionId(), k);
                sessionContext.getInterruptedMap().put(k, System.currentTimeMillis());
                return !sessionContext.getPendingQueryMap().get(k).getDisposable().isDisposed();
            }) && i < 3) {
                try {
                    Thread.sleep(100);
                } catch (InterruptedException e) {
                    log.error("Interrupt error", e);
                }
                i++;
            }
            sessionContext.getPendingQueryMap().keySet().forEach(k -> {
                clear(k, sessionContext);
            });
            return;
        }
        PendingTask pendingTask = sessionContext.getPendingQueryMap().get(taskId);
        if (pendingTask != null) {
            log.info("Interrupt sessionId={}, taskId={}", sessionContext.getSessionId(), taskId);
            // 会有延迟不一定立马中断
            pendingTask.getDisposable().dispose();
            sessionContext.getInterruptedMap().put(taskId, System.currentTimeMillis());
            int i = 0;
            // 判断是否真的中断
            while (!pendingTask.getDisposable().isDisposed() && i < 3) {
                try {
                    Thread.sleep(100);
                } catch (InterruptedException e) {
                    log.error("Interrupt error", e);
                }
                i++;
            }
            clear(taskId, sessionContext);
        }
    }

    public void clear(String taskId, SessionContext sessionContext) {
        log.debug("Clear task id={}", taskId);
        if (StringUtils.isEmpty(taskId)) {
            return;
        }
        sessionContext.getPendingQueryMap().remove(taskId);
        sessionContext.getPendingResponseSeqMap().remove(taskId);
        sessionContext.getPendingResponseMap().remove(taskId);
    }

    private HttpHeaders getHeaders(LLMConfig llmConfig) {
        HttpHeaders headers = new HttpHeaders();
        headers.add("Authorization", COPILOT_TOKEN_PREFIX + llmConfig.getCredential().get("token"));
        headers.add("Content-Type", "application/json;charset=UTF-8");
        return headers;
    }

    @Override
    public String getBotType() {
        return BOT_TYPE;
    }
}
