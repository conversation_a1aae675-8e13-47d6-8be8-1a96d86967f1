package com.baidu.acg.piat.llm.dm.model.unit;

import java.util.List;
import java.util.Map;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 **/
@Data
@ApiModel
public class ChatBotQueryParam {
    /**
     * 请求文本,必填
     */
    @ApiModelProperty(value = "请求文本即提问内容,必填",required = true)
    private String queryText;
    /**
     * 对话模式:
     * 1. task_based:多轮
     * 2. kg:知识图谱
     * 3. faq:问答
     * 4. chitchat:闲聊
     * 5. 空或无该字段为智能对话
     */
    @ApiModelProperty(value = "对话模式:task_based:多轮\n" +
            "kg:知识图谱\n" +
            "faq:问答\n" +
            "chitchat:闲聊\n" +
            "空或无该字段为智能对话")
    private List<String> sources;
    /**
     * 会话id。
     * 可不传值，由会话机器人生成；
     * 若传值，使用业务方传入的sessionId，用户传入的sessionId必须保证唯一，如UUID.
     */
    @ApiModelProperty(value = "会话id，值从会话开始接口获取",required = true)
    private String sessionId;
    /**
     * 渠道名称，保持整个会话周期一致
     */
    @ApiModelProperty(value = "渠道名称，可为空")
    private String channel;

    /**
     * 上下文信息。与当前的会话session中的上下文进行合并，若存在key冲突，以传入的key为准
     */
    @ApiModelProperty(value = "上下文信息，可为空")
    private Map<String, Object> context;
    /**
     * 是否收集会话数据，用于用户标注。默认为false
     */
    @ApiModelProperty(value = " 是否收集会话数据，用于用户标注。默认为false",hidden = true)
    private Boolean collect;
    /**
     * 系统指令：
     * silent:静默
     * interrupt:打断
     */
    @ApiModelProperty(value = "系统指令：\n" +
            "silent:静默\n" +
            "interrupt:打断",hidden = true)
    private String vad;

    /**
     * 调用方回传参数，格式为json，数据不做处理用来后期分析使用； 示例：调用会话时传入{ "uid",123 } 会话日志中ext将会存储该信息；用于后期分析
     */
    @ApiModelProperty(value = "调用方回传参数，格式为json，数据不做处理用来后期分析使用，可为空")
    private Map<String, Object> ext;
}
