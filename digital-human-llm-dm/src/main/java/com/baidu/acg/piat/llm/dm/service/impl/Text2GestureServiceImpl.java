package com.baidu.acg.piat.llm.dm.service.impl;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;

import org.springframework.stereotype.Service;

import com.baidu.acg.piat.llm.dm.client.HttpCommonClient;
import com.baidu.acg.piat.llm.dm.configure.CommonConfig;
import com.baidu.acg.piat.llm.dm.model.SessionContext;
import com.baidu.acg.piat.llm.dm.model.aida.WorkflowRequest;
import com.baidu.acg.piat.llm.dm.model.aida.WorkflowResponse;
import com.baidu.acg.piat.llm.dm.model.text2gesture.AnimojiRule;
import com.baidu.acg.piat.llm.dm.model.text2gesture.EmotionRule;
import com.baidu.acg.piat.llm.dm.model.text2gesture.Text2GestureRequest;
import com.baidu.acg.piat.llm.dm.model.text2gesture.Text2GestureResponse;
import com.baidu.acg.piat.llm.dm.service.Text2GestureService;
import com.baidu.acg.piat.llm.dm.tools.copilot.CopilotResultParseRequest;
import com.baidu.acg.piat.llm.dm.tools.copilot.CopilotResultParseResponse;
import com.baidu.acg.piat.llm.dm.utils.DrmlUtil;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * TODO 名字要改动，暂时不改, 普通的http client
 */
@RequiredArgsConstructor
@Slf4j
@Service
public class Text2GestureServiceImpl implements Text2GestureService {

    private final HttpCommonClient httpCommonClient;

    private final CommonConfig config;

    @Override
    public WorkflowResponse requestAida(String url, WorkflowRequest workflowRequest) {
        return httpCommonClient.requestAida(url, workflowRequest);
    }

    @Override
    public String text2Gesture(String pureText, SessionContext sessionContext) {
        log.info("Text2Gesure pureText={}", pureText);
        if (sessionContext.getAnimojisTagConfigJson() == null) {
            return pureText;
        }
        if (pureText.startsWith("<speak")) {
            return pureText;
        }
        Map<String, List<String>> emotionMap = new HashMap<>();
        if (sessionContext.getAnimojisTagConfigJson().getEmotionRules() != null) {
            emotionMap = sessionContext.getAnimojisTagConfigJson().getEmotionRules().stream()
                    .collect(Collectors.toMap(EmotionRule::getTag,
                            EmotionRule::getEmotions));
        }
        Map<String, List<String>> animojisMap = new HashMap<>();
        if (sessionContext.getAnimojisTagConfigJson().getAnimojiRules() != null) {
            animojisMap = sessionContext.getAnimojisTagConfigJson()
                    .getAnimojiRules().stream().collect(Collectors.toMap(AnimojiRule::getTag,
                            AnimojiRule::getAnimojis));
        }

        if (!pureText.contains("</animoji") && !pureText.contains("</a2a_emotion")) {
            // TODO 请求智能标签接口
            Text2GestureRequest text2GestureRequest = Text2GestureRequest.builder()
                    .text(pureText).build();
            Text2GestureResponse text2GestureResponse =
                    httpCommonClient.text2gesture(config.getText2GestureUrl(),
                            text2GestureRequest);
            String drml = DrmlUtil.buildDrmlFromText2Gesture(pureText, text2GestureResponse,
                    animojisMap, emotionMap, true, sessionContext.getLlmConfig().isInterrupted());
            log.info("Text2Gesure drml={}", drml);
            return drml;
        }
        return pureText;
    }

    @Override
    public String copilotResultParse(String pureText, String url, String requestId,
                                     SessionContext sessionContext) {
        CopilotResultParseRequest copilotResultParseRequest = CopilotResultParseRequest.builder()
                .requestId(requestId != null ? requestId : UUID.randomUUID().toString())
                .sessionId(sessionContext.getSessionId())
                .originText(pureText)
                .normalization(null)
                .context(null)
                .build();
        CopilotResultParseResponse copilotResultParseResponse = httpCommonClient
                .copilotResultParse(url, copilotResultParseRequest);
        if (copilotResultParseResponse != null) {
            return copilotResultParseResponse.getResult();
        } else {
            return pureText;
        }
    }

    @Override
    public Map<String, Object> bsCartNormalize(Map<String, Object> cartInfos, String url, String requestId,
                                               SessionContext sessionContext) {
        cartInfos.put("requestId", requestId != null ? requestId : UUID.randomUUID().toString());
        return httpCommonClient.bsCartNormalize(url, cartInfos);
    }

}
