package com.baidu.acg.piat.llm.dm.controller;

import java.util.Date;

import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.baidu.acg.piat.llm.dm.dao.LlmDmConfigRepository;
import com.baidu.acg.piat.llm.dm.model.db.LlmDmConfigModel;

import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;

/**
 * @author: v_luojinj
 * @since: 2023/6/5
 **/
@RestController
@RequestMapping("/api/v1/digitalhuman/llm/config")
@RequiredArgsConstructor
public class LlmConfigController {

    private final LlmDmConfigRepository llmDmConfigRepository;

    @ApiOperation(value = "会话中控", notes = "会话中控接口，传入问题，从机器人那获取答案，作者：罗锦江")
    @PostMapping(value = "/save", produces = MediaType.APPLICATION_JSON_VALUE)
    public String save(@RequestBody LlmDmConfigModel llmDmConfigModel) {
        llmDmConfigModel.setCreateTime(new Date());
        llmDmConfigModel.setUpdateTime(new Date());
        llmDmConfigRepository.save(llmDmConfigModel);
        return "success";
    }

}
