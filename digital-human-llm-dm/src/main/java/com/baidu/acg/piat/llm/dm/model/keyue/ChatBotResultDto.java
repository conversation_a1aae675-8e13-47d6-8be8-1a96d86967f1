package com.baidu.acg.piat.llm.dm.model.keyue;

import java.util.List;
import java.util.Map;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 **/
@Data
@ApiModel
public class ChatBotResultDto {
    @ApiModelProperty(value = "请求id")
    private String queryId;

    @ApiModelProperty(value = "会话id")
    private String sessionId;

    private List<KeYueChunk> answer;

    @ApiModelProperty(value = "会话上下文信息")
    private Map<String,Object> variables;
}
