package com.baidu.acg.piat.llm.dm.model.aida;

import javax.validation.constraints.NotEmpty;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class WorkflowRequest {

    @NotEmpty(message = "sessionId不能为空")
    private String sessionId;

    /**
     * 用户语音的文本
     */
    private String text;

    /**
     * leaflet想传递一些参数给ngd
     */
    private String context;

    private String requestId;
}
