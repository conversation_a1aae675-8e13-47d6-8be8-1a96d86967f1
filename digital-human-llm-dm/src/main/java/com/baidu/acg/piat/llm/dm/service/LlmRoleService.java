package com.baidu.acg.piat.llm.dm.service;

import com.baidu.acg.piat.digitalhuman.common.llmrole.LlmRole;
import com.baidu.acg.piat.digitalhuman.common.model.PageResponse;

import com.baidu.acg.piat.digitalhuman.common.project.Project;

import javax.transaction.Transactional;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/11/23 14:05
 */
public interface LlmRoleService {

    @Transactional
    LlmRole createLlmRole(LlmRole role, Project projectRequest, int roleType);

    @Transactional
    LlmRole createLlmRoleByTemplate(String llmRoleTemplateId, LlmRole llmRole);

    @Transactional
    void batchDeleteRole(String accountId, List<String> llmRoleIds, int roleType);

    @Transactional
    LlmRole updateLlmRole(String llmRoleId, LlmRole roleRequest, Project projectRequest, int roleType);

    @Transactional
    String publishLlmRole(String llmRoleId, LlmRole roleRequest, Project projectRequest, int roleType);

    PageResponse<LlmRole> listByRoleType(String accoutId, int roleType, int pageNo, int pageSize, String name);

    LlmRole detailLlmRole(String accoutId, String llmRoleId);

    @Transactional
    LlmRole copyLlmRole(String accoutId, String llmRoleId, String rename);

    LlmRole createLlmConfig(LlmRole role, int roleType);

    LlmRole detailLlmConfig(String llmRoleId);

    LlmRole updateLlmConfig(String llmRoleId, LlmRole roleRequest, int roleType);
}
