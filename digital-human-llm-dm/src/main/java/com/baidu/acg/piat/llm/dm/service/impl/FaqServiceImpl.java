package com.baidu.acg.piat.llm.dm.service.impl;

import com.baidu.acg.piat.digitalhuman.common.llmdm.ToolConfig;
import com.baidu.acg.piat.digitalhuman.common.utils.JsonUtil;
import com.baidu.acg.piat.llm.dm.client.HttpCommonClient;
import com.baidu.acg.piat.llm.dm.model.faq.FaqRequest;
import com.baidu.acg.piat.llm.dm.model.faq.FaqResponse;
import com.baidu.acg.piat.llm.dm.service.FaqService;
import com.baidubce.util.JsonUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@RequiredArgsConstructor
@Slf4j
@Service
public class FaqServiceImpl implements FaqService {

    private final HttpCommonClient httpCommonClient;

    @Override
    public String faqSearch(String requestId, String query, ToolConfig toolConfig) {
        try {
            FaqRequest faqRequest = JsonUtil.readValue(JsonUtils.toJsonString(toolConfig.getParams()), FaqRequest.class);
            faqRequest.setLogId(requestId);
            faqRequest.setQuery(query);
            FaqResponse faqResponse = httpCommonClient.faqSearch(toolConfig.getUrl(), faqRequest);
            if (faqResponse == null) {
                return null;
            }
            double faqScore = faqResponse.getData().getCosineScore();
            if (faqScore < toolConfig.getMinScore()) {
                return null;
            }
            return faqResponse.getData().getQuestionInfo()[0].getAnswers()[0].getSpeak()[0].getAnswer();
        } catch (Exception e) {
            log.warn("FaqSearch requestId={} error", requestId, e);
            return null;
        }

    }
}
