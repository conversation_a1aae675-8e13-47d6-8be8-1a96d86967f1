package com.baidu.acg.piat.llm.dm.model.text2gesture;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class AnimojiRule {

    /**
     * 标签
     */
    private String tag;

    /**
     * 动作id
     */
    private List<String> animojis;
}