// Copyright (C) 2019 Baidu Inc. All rights reserved.
package com.baidu.acg.piat.llm.dm.service.impl;

import java.nio.charset.StandardCharsets;
import java.time.Duration;

import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.util.StringUtils;
import org.springframework.web.reactive.function.client.WebClient;

import com.baidu.acg.piat.digitalhuman.common.llmrole.LLMConfig;
import com.baidu.acg.piat.digitalhuman.common.project.BotParams;
import com.baidu.acg.piat.llm.dm.configure.CommonConfig;
import com.baidu.acg.piat.llm.dm.model.PendingTask;
import com.baidu.acg.piat.llm.dm.model.Query;
import com.baidu.acg.piat.llm.dm.model.SessionContext;
import com.baidu.acg.piat.llm.dm.model.unit.ChatBotQueryDto;
import com.baidu.acg.piat.llm.dm.model.unit.ChatBotQueryParam;
import com.baidu.acg.piat.llm.dm.service.BotProviderService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.Disposable;
import reactor.core.publisher.Flux;

@RequiredArgsConstructor
@Slf4j
public class BotServiceUnitStreamImpl implements BotProviderService {

    WebClient webClient = WebClient.builder().build();

    public static final String BOT_TYPE = BotParams.BotTypeEnum.UNIT_LLM.getBotType();;

    public static final String NGD_TOKEN_PREFIX = "NGD ";

    private final CommonConfig config;

    /**
     * 只用于http接口测试
     *
     * @param sessionContext
     * @param query
     * @param llmConfig
     * @return
     */
    @Override
    public Flux<ChatBotQueryDto> queryWithReturn(SessionContext sessionContext, Query query, LLMConfig llmConfig) {
        ChatBotQueryParam param = new ChatBotQueryParam();
        // 请求unit，如果dialogId为空，则使用sessionId
        param.setSessionId(!StringUtils.isEmpty(sessionContext.getDialogId()) ?
                sessionContext.getDialogId() : query.getSessionId());
        param.setQueryText(query.getText());
        param.setContext(query.getContext());
        String url = llmConfig.getUrl();
        Flux<ChatBotQueryDto> responseFlux = webClient.post()
                .uri(url) // 你的端点
                .headers(header -> header.addAll(getHeaders(llmConfig)))
                .bodyValue(param)
                .acceptCharset(StandardCharsets.UTF_8)
                .accept(MediaType.APPLICATION_NDJSON)
                .retrieve()
                .bodyToFlux(ChatBotQueryDto.class); // 你的返回类型
        responseFlux.subscribe(chatBotQueryDto -> {
            log.info("chatBotQueryDto:{}", chatBotQueryDto);
        });
        return responseFlux;
    }

    @Override
    public void reset(SessionContext sessionContext) {
        sessionContext.setUnitSessionId(org.apache.commons.lang3.StringUtils.EMPTY);
    }

    @Override
    public void query(SessionContext sessionContext, Query query) {
        LLMConfig llmConfig = sessionContext.getLlmConfig();
        ChatBotQueryParam param = new ChatBotQueryParam();
        if (!StringUtils.isEmpty(sessionContext.getUnitSessionId())) {
            param.setSessionId(sessionContext.getUnitSessionId());
        } else {
            param.setSessionId("");
        }
        param.setQueryText(query.getText());
        param.setContext(query.getContext());
        String url = llmConfig.getUrl();
        if (StringUtils.isEmpty(url)) {
            url = config.getUnitUrl();
        }
        log.info("Request unit url:{}, body:{}", url, param);
        Flux<ChatBotQueryDto> responseFlux = webClient.post()
                .uri(url) // 你的端点
                .headers(header -> header.addAll(getHeaders(llmConfig)))
                .bodyValue(param)
                .acceptCharset(StandardCharsets.UTF_8)
                .accept(MediaType.APPLICATION_NDJSON)
                .retrieve()
                .bodyToFlux(ChatBotQueryDto.class); // 你的返回类型
        handleResponse(responseFlux, sessionContext, query);
    }

    private void handleResponse(Flux<ChatBotQueryDto> responseFlux, SessionContext sessionContext, Query query) {
        Disposable disposable = responseFlux
                .timeout(Duration.ofSeconds(sessionContext.getLlmConfig().getTimeout()))
                .doOnCancel(() -> {
                    log.debug("Cancelled, query={}", query);
                    sessionContext.getLlmStreamObserver().onCancel(query);
                }).doOnError(e -> {
                    // 打印错误
                    log.error("Handle response sessionId={}, requestId={}, Error", sessionContext.getSessionId(),
                            query.getRequestId(), e);
                    sessionContext.getLlmStreamObserver().onError(e, query);

                }).doOnComplete(() -> {
                    sessionContext.getLlmStreamObserver().onCompleted(query);
                    // 打印结束
                    log.info("Hanlde response sessionId={}, requestId={} Complete", sessionContext.getSessionId(),
                            query.getRequestId());
                }).subscribe(response -> {
                    // 打印每个接收到的chunk
                    log.info("Received chunk: {}", response);
                    if (response.getSource() != null && !StringUtils.isEmpty(response
                            .getAnswer().getAnswerText())) {
                        sessionContext.setUnitSessionId(response.getSessionId());
                        String text = response.getAnswer().getAnswerText();
                        // 处理unit^[1]^, 防止unit放回]结尾
                        if (text.endsWith("]")) {
                            text = text + "^";
                        }
                        text = text.replaceAll("\\*", "");
                        log.debug("Received answer: {}", text);
                        sessionContext.getLlmStreamObserver().onNext(text, "largeModel", query);
                    }
                });

        sessionContext.getPendingQueryMap().put(query.getRequestId(),
                PendingTask.builder().queryIndex(query.getQueryIndex()).disposable(disposable).build());

    }

    /**
     * @param 如果没有taskID, 会中断所有pending请求
     * @return
     */
    public void interrupt(SessionContext sessionContext, String taskId) {
        if (sessionContext.getPendingQueryMap().isEmpty()) {
            return;
        }
        // 批量删除
        if (StringUtils.isEmpty(taskId)) {
            log.info("Interrupt all={}", sessionContext.getPendingQueryMap().keySet());
            sessionContext.getPendingQueryMap().forEach((k, v) -> {
                v.getDisposable().dispose();
            });
            int i = 0;
            // 判断是否真的中断, 一次性给所有的pending发中断指令
            while (sessionContext.getPendingQueryMap().keySet().stream().anyMatch(k -> {
                return !sessionContext.getPendingQueryMap().get(k).getDisposable().isDisposed();
            }) && i < 3) {
                try {
                    Thread.sleep(100);
                } catch (InterruptedException e) {
                    log.error("Interrupt error", e);
                }
                i++;
            }
            sessionContext.getPendingQueryMap().keySet().forEach(k -> {
                clear(k, sessionContext);
            });
            return;
        }
        PendingTask pendingTask = sessionContext.getPendingQueryMap().get(taskId);
        if (pendingTask != null) {
            log.info("Interrupt task id={}", taskId);
            // 会有延迟不一定立马中断
            pendingTask.getDisposable().dispose();
            int i = 0;
            // 判断是否真的中断
            while (!pendingTask.getDisposable().isDisposed() && i < 3) {
                try {
                    Thread.sleep(100);
                } catch (InterruptedException e) {
                    log.error("Interrupt error", e);
                }
                i++;
            }
            clear(taskId, sessionContext);
        }
    }

    public void clear(String taskId, SessionContext sessionContext) {
        log.debug("Clear task id={}", taskId);
        if (StringUtils.isEmpty(taskId)) {
            return;
        }
        sessionContext.getPendingQueryMap().remove(taskId);
        sessionContext.getPendingResponseSeqMap().remove(taskId);
        sessionContext.getPendingResponseMap().remove(taskId);
    }

    private HttpHeaders getHeaders(LLMConfig llmConfig) {
        HttpHeaders headers = new HttpHeaders();
        headers.add("Authorization", NGD_TOKEN_PREFIX + llmConfig.getCredential().get("token"));
        headers.add("Content-Type", "application/json;charset=UTF-8");
        return headers;
    }

    @Override
    public String getBotType() {
        return BOT_TYPE;
    }
}
