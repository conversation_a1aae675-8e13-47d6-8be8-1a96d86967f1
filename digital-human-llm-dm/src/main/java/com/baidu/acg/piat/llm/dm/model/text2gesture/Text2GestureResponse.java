package com.baidu.acg.piat.llm.dm.model.text2gesture;

import java.util.ArrayList;
import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class Text2GestureResponse {
    private String pureText;
    // 动作标签
    private List<String> action = new ArrayList<>();
    // 动作标签对应的词索引
    private List<Integer> wordIndexAction = new ArrayList<>();
    // 情绪标签
    private List<String> emotion = new ArrayList<>();
    // 情绪标签对应的词索引
    private List<Integer> wordIndexEmotion = new ArrayList<>();

}
