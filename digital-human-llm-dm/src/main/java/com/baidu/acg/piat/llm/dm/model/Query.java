package com.baidu.acg.piat.llm.dm.model;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;

import com.baidu.acg.piat.llm.dm.model.copilot.CopilotAction;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 */
@Getter
@Setter
@ToString
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class Query {

    @Builder.Default
    protected String requestId = UUID.randomUUID().toString();

    protected String sessionId;

    private boolean stream = true;

    private int queryIndex;

    protected String text;

    private Map<String, Object> context;

    private List<CopilotAction> copilotActionList;

    public void addContext(String key, Object value) {
        if (Objects.isNull(context)) {
            context = new HashMap<>(0);
        }

        context.put(key, value);
    }

}
