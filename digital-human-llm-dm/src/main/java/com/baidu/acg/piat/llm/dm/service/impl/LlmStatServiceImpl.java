// Copyright (C) 2019 Baidu Inc. All rights reserved.
package com.baidu.acg.piat.llm.dm.service.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.baidu.acg.piat.digitalhuman.common.entity.CharacterModel;
import com.baidu.acg.piat.digitalhuman.plat.client.PlatformClient;
import com.baidu.acg.piat.llm.dm.dao.LlmHistoryRepository;
import com.baidu.acg.piat.llm.dm.model.stat.CharacterCountRes;
import com.baidu.acg.piat.llm.dm.model.stat.CharacterImageCount;
import com.baidu.acg.piat.llm.dm.model.stat.ConversationCountRes;
import com.baidu.acg.piat.llm.dm.service.LlmStatService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class LlmStatServiceImpl implements LlmStatService {

    private final LlmHistoryRepository llmHistoryRepository;

    public final PlatformClient platformClient;

    @Override
    public ConversationCountRes getConversationCount(String uid) {
        Integer num = llmHistoryRepository.countByUserId(uid);
        return ConversationCountRes.builder().conversationCount(num).build();
    }

    @Override
    public List<CharacterCountRes> getCharacterCount(String uid) {
        List<Map<String, Object>> characterImageMapCounts = llmHistoryRepository.findTop3CharacterImage(uid);
        if (CollectionUtils.isEmpty(characterImageMapCounts)) {
            return new ArrayList<>();
        }

        List<CharacterImageCount> characterImageCounts = characterImageMapCounts.stream().map(map -> {
            return CharacterImageCount.builder()
                    .characterImage((String) map.get("characterImage"))
                    .count(((Number) map.get("count")).intValue()).build();
        }).collect(Collectors.toList());
        log.debug("Get characterImageCounts: {}", characterImageCounts);
        List<CharacterCountRes> characterCountRes = new ArrayList<>();
        for (CharacterImageCount characterImageCount : characterImageCounts) {
            try {
                CharacterModel characterModel =
                        platformClient.findCharacterByType(characterImageCount.getCharacterImage(),
                                2);
                characterCountRes.add(CharacterCountRes.builder()
                        .characterId(characterModel.getCharacterId())
                        .characterImageUrl(characterModel.getThumbnailImageUrl())
                        .performanceStage("conversation").build());
            } catch (Exception e) {
                // 防止有一两个常用的人像删除了，找不到
                log.debug("Get character image={}, err={}",
                        characterImageCount.getCharacterImage(), e.getMessage());
            }
        }
        return characterCountRes;
    }

}
