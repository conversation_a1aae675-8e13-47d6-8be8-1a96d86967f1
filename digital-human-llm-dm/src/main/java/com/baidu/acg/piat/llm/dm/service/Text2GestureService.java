package com.baidu.acg.piat.llm.dm.service;

import java.util.Map;

import com.baidu.acg.piat.llm.dm.model.SessionContext;
import com.baidu.acg.piat.llm.dm.model.aida.WorkflowRequest;
import com.baidu.acg.piat.llm.dm.model.aida.WorkflowResponse;

public interface Text2GestureService {

    WorkflowResponse requestAida(String url, WorkflowRequest workflowRequest);

    String text2Gesture(String pureText, SessionContext sessionContext);

    String copilotResultParse(String pureText, String url, String requestId, SessionContext sessionContext);

    Map<String, Object> bsCartNormalize(Map<String, Object> params, String url, String requestId,
                                        SessionContext sessionContext);

}
