package com.baidu.acg.piat.llm.dm.model;

import com.baidu.acg.piat.digitalhuman.common.llmrole.KnowledgeBase;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;
import org.hibernate.annotations.UpdateTimestamp;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;

/**
 * <AUTHOR>
 * @since 2023/11/22 16:31
 */
@Data
@Slf4j
@Builder
@Accessors(chain = true)
@DynamicInsert
@DynamicUpdate
@Entity(name = "llm_knowledge_base")
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class KnowledgeBaseModel {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    private String knowledgeBaseId;

    private String name;

    private String accountId;

    private String editor;

    private Integer type;

    private Integer fileCount;

    private String explain;

    private Integer status;

    @Builder.Default
    private boolean isDelete = false;

    @CreationTimestamp
    @Column(insertable = false, updatable = false)
    private ZonedDateTime createTime;

    @UpdateTimestamp
    @Column(insertable = false, updatable = false)
    private ZonedDateTime updateTime;

    public KnowledgeBase toKnowledgeBase() {
        return KnowledgeBase.builder()
                .knowledgeBaseId(knowledgeBaseId)
                .name(name)
                .accountId(accountId)
                .editor(editor)
                .type(type)
                .fileCount(fileCount)
                .explain(explain)
                .status(status)
                .isDelete(isDelete)
                .createTime(DateTimeFormatter.ISO_OFFSET_DATE_TIME.format(createTime))
                .updateTime(DateTimeFormatter.ISO_OFFSET_DATE_TIME.format(updateTime))
                .build();
    }

    public void toKnowledgeBaseModel(KnowledgeBase request) {
        if (StringUtils.isNotEmpty(request.getKnowledgeBaseId())) {
            this.setKnowledgeBaseId(request.getKnowledgeBaseId());
        }
        if (StringUtils.isNotEmpty(request.getAccountId())) {
            this.setAccountId(request.getAccountId());
        }
        if (StringUtils.isNotEmpty(request.getEditor())) {
            this.setEditor(request.getEditor());
        }
        if (StringUtils.isNotEmpty(request.getName())) {
            this.setName(request.getName());
        }
        if (request.getType() != null) {
            this.setType(request.getType());
        }
        if (request.getFileCount() != null) {
            this.setFileCount(request.getFileCount());
        }
        if (StringUtils.isNotEmpty(request.getExplain())) {
            this.setExplain(request.getExplain());
        }
        if (request.getStatus() != null) {
            this.setStatus(request.getStatus());
        }
        this.setDelete(request.isDelete());
    }

}
