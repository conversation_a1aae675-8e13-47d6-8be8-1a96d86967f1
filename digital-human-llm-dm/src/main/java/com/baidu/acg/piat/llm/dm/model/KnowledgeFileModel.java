package com.baidu.acg.piat.llm.dm.model;

import com.baidu.acg.piat.digitalhuman.common.llmrole.KnowledgeFile;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;


/**
 * <AUTHOR>
 * @since 2023/11/22 16:31
 */
@Data
@Slf4j
@Builder
@Accessors(chain = true)
@DynamicInsert
@DynamicUpdate
@Entity(name = "llm_knowledge_file")
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class KnowledgeFileModel {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    // copilot平台区分了file和document，file为上传的文件，document为最终关联后的文件。平台的KnowledgeFile的fileId指最终关联后的documentId。
    private String fileId;

    private String name;

    private String status;

    private String url;

    private String knowledgeBaseId;

    @CreationTimestamp
    @Column(insertable = false, updatable = false)
    private ZonedDateTime createTime;

    public KnowledgeFile toKnowledgeFile() {
        return KnowledgeFile.builder()
                .fileId(fileId)
                .name(name)
                .status(status)
                .url(url)
                .createTime(DateTimeFormatter.ISO_OFFSET_DATE_TIME.format(createTime))
                .build();
    }

}
