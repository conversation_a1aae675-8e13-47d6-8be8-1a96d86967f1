package com.baidu.acg.piat.llm.dm.service.impl;

import com.baidu.acg.piat.llm.dm.constants.ResponseType;
import com.baidu.acg.piat.llm.dm.dao.LlmHistoryRepository;
import com.baidu.acg.piat.llm.dm.model.LlmHistoryModel;
import com.baidu.acg.piat.llm.dm.model.qianfan.ConversationContext;

import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import com.baidu.acg.piat.digitalhuman.common.alita.ActionType;
import com.baidu.acg.piat.digitalhuman.common.llmdm.TextRenderBody;
import com.baidu.acg.piat.digitalhuman.common.llmdm.ToolConfig;
import com.baidu.acg.piat.digitalhuman.common.llmdm.ToolType;
import com.baidu.acg.piat.digitalhuman.common.llmdm.WebsocketLlmResponse;
import com.baidu.acg.piat.llm.dm.configure.CommonConfig;
import com.baidu.acg.piat.llm.dm.constants.LLMConstants;
import com.baidu.acg.piat.llm.dm.model.LlmStreamObserver;
import com.baidu.acg.piat.llm.dm.model.Query;
import com.baidu.acg.piat.llm.dm.model.SessionContext;
import com.baidu.acg.piat.llm.dm.service.LLMBillingService;
import com.baidu.acg.piat.llm.dm.service.Text2GestureService;
import com.baidu.acg.piat.llm.dm.utils.WebsocketUtil;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class LLmStreamObserverImpl implements LlmStreamObserver<String> {

    private SessionContext sessionContext;

    private Text2GestureService text2GestureService;

    private CommonConfig commonConfig;

    private LLMBillingService llmBillingService;

    public static final String REGEX = "\\^\\[\\d+\\](\\[\\d+\\])*\\^";

    private LlmHistoryRepository llmHistoryRepository;

    public LLmStreamObserverImpl(SessionContext sessionContext, Text2GestureService text2GestureService,
                                 CommonConfig commonConfig, LLMBillingService llmBillingService,
                                 LlmHistoryRepository llmHistoryRepository) {
        this.sessionContext = sessionContext;
        this.text2GestureService = text2GestureService;
        this.commonConfig = commonConfig;
        this.llmBillingService = llmBillingService;
        this.llmHistoryRepository = llmHistoryRepository;
    }

    @Override
    public void onNext(String text, String source, Query query) {
        sessionContext.setResponseActiveTimeMillis(System.currentTimeMillis());
        // 尝试去消费
        String msg = sessionContext.produceAndTryConsumeResponse(query, source, text, commonConfig.getLlmSplitEndSet()
                , commonConfig.getMinSplitSentenceLen());
        log.info("Consumer msg: {}, id:{}", msg, query.getQueryIndex());
        if (StringUtils.hasText(msg)) {
            consumeMsg(msg, query);
        }
    }

    /**
     * 将消息文本解析并发送给WebSocket Session。
     *
     * @param text  消息文本。
     * @param query 请求信息。
     */
    private void consumeMsg(String text, Query query) {
        if (sessionContext.getInterruptedMap().containsKey(query.getRequestId())) {
            log.info("Response has interrupted:{}, query:{}", query.getRequestId(), query);
            return;
        }
        int seq = sessionContext.getPendingResponseSeqMap().getOrDefault(query.getRequestId(), -1) + 1;
        sessionContext.getPendingResponseSeqMap().put(query.getRequestId(), seq);
        String drml = text;
        // 测试使用
        drml = drml.replaceAll("&", "")
                .replaceAll("<", "")
                .replaceAll(">", "")
                .replaceAll("\"", "")
                .replaceAll("'", "");
        drml = drml.replaceAll(REGEX, "");
        drml = drml.replaceAll("^", "");
        /* 测试的开关，测试代码后面删除TODO chenke08
        if (sessionContext.getLlmConfig() != null && !CollectionUtils.isEmpty(sessionContext.getLlmConfig()
                .getParams())) {
            if (sessionContext.getLlmConfig().getParams().containsKey(LLMConstants.BUILD_DRML)) {
                if (drml != null && !drml.startsWith("<speak")) {
                    drml = drml.replace("&", "")
                            .replace("<", "")
                            .replace(">", "")
                            .replace("\"", "")
                            .replace("'", "");
                    drml = "<speak interruptible=\"false\">" +  drml + "</speak>";
                }
            }
        }*/

        if (sessionContext.getLlmConfig() != null && !CollectionUtils.isEmpty(sessionContext.getLlmConfig()
                .getPostActions())) {
            for (ToolConfig toolConfig : sessionContext.getLlmConfig().getPostActions()) {
                if (toolConfig.getToolType() == ToolType.COPILOT_RESULT_PARSE) {
                    drml = text2GestureService.copilotResultParse(text, toolConfig.getUrl(), query.getRequestId(),
                            sessionContext);
                }
            }
        }
        // 标记大模型返回过答案
        sessionContext.getPendingHasResponseSet().add(query.getRequestId());
        if (commonConfig.isBillingEnable() && sessionContext.getFreezeSet().contains(query.getRequestId())) {

            llmBillingService.confirmedCost(sessionContext.getAccountId(), sessionContext.getWebSocketSession().getId(),
                    query.getRequestId(), commonConfig.getBillingCost());
            LlmHistoryModel llmHistoryModel = new LlmHistoryModel();
            if (!StringUtils.isEmpty(sessionContext.getCharacterImage())
                    && !StringUtils.isEmpty(sessionContext.getUid())) {
                llmHistoryModel.setLlmRoleId(sessionContext.getLlmRoleModel().getLlmRoleId());
                llmHistoryModel.setQuery(query.getText());
                llmHistoryModel.setChannel(sessionContext.getChannel());
                llmHistoryModel.setAnswerType(ResponseType.LLM.toString());
                llmHistoryModel.setAnswer(drml);
                llmHistoryModel.setRequestId(query.getRequestId());
                llmHistoryModel.setUid(sessionContext.getUid());
                llmHistoryModel.setAppId(sessionContext.getAppId());
                llmHistoryModel.setCharacterImage(sessionContext.getCharacterImage());
                log.debug("Save llmHistoryModel:{}", llmHistoryModel);
                llmHistoryRepository.save(llmHistoryModel);
            }
            refreshAccount(sessionContext, query);
            refreshAccount(sessionContext, query);
            sessionContext.getFreezeSet().remove(query.getRequestId());
        }
        WebsocketUtil.send(sessionContext.getWebSocketSession(), WebsocketLlmResponse.builder()
                .action(ActionType.TEXT_RENDER)
                .requestId(query.getRequestId())
                .body(TextRenderBody.toBuild(query.getRequestId(),
                        drml, false, seq, sessionContext.getSessionId(),
                        sessionContext.getLlmConfig().isInterrupted()))
                .code(0)
                .message("ok")
                .build());
    }

    private void refreshAccount(SessionContext sessionContext, Query query) {
        WebsocketUtil.send(sessionContext.getWebSocketSession(), WebsocketLlmResponse.builder()
                .action(ActionType.REFRESH_ACCOUNT)
                .requestId(query.getRequestId())
                .body(null)
                .code(0)
                .message("")
                .build());
    }

    @Override
    public void onCancel(Query query) {
        sessionContext.setResponseActiveTimeMillis(System.currentTimeMillis());
        // 直接发送结束drml给上游
        if (!sessionContext.getPendingHasResponseSet().contains(query.getRequestId())) {
            if (commonConfig.isBillingEnable() && sessionContext.getFreezeSet().contains(query.getRequestId())) {
                llmBillingService.unfreezeQuota(sessionContext.getAccountId(),
                        sessionContext.getWebSocketSession().getId(),
                        query.getRequestId(), commonConfig.getBillingCost());
                refreshAccount(sessionContext, query);
            }
            // 取消的任务不返回错误
            // sendLLmUnknowAnswerComplete(sessionContext, query);
        }
        // 直接发送结束drml给上游
        sendComplete(sessionContext, query, "cancel");
    }

    @Override
    public void onError(Throwable t, Query query) {
        sessionContext.setResponseActiveTimeMillis(System.currentTimeMillis());
        // 直接发送结束drml给上游
        if (!sessionContext.getPendingHasResponseSet().contains(query.getRequestId())) {
            if (commonConfig.isBillingEnable() && sessionContext.getFreezeSet().contains(query.getRequestId())) {
                llmBillingService.unfreezeQuota(sessionContext.getAccountId(),
                        sessionContext.getWebSocketSession().getId(),
                        query.getRequestId(), commonConfig.getBillingCost());
                refreshAccount(sessionContext, query);
            }
            sendLLmUnknowAnswerComplete(sessionContext, query);
        }
        sendComplete(sessionContext, query, "error");
    }

    @Override
    public void onCompleted(Query query) {
        sessionContext.setResponseActiveTimeMillis(System.currentTimeMillis());
        String msg = sessionContext.consumeResponse(query, "", true, commonConfig.getLlmSplitEndSet()
                , commonConfig.getMinSplitSentenceLen());
        if (StringUtils.hasText(msg)) {
            consumeMsg(msg, query);
        }
        if (!sessionContext.getPendingHasResponseSet().contains(query.getRequestId())) {
            if (commonConfig.isBillingEnable() && sessionContext.getFreezeSet().contains(query.getRequestId())) {
                llmBillingService.unfreezeQuota(sessionContext.getAccountId(),
                        sessionContext.getWebSocketSession().getId(),
                        query.getRequestId(), commonConfig.getBillingCost());
                refreshAccount(sessionContext, query);
            }
            sendLLmUnknowAnswerComplete(sessionContext, query);
        }
        sendComplete(sessionContext, query, "complete");
    }

    @Override
    public void onCompleted(Query query, ConversationContext context) {
        sessionContext.setResponseActiveTimeMillis(System.currentTimeMillis());
        String msg = sessionContext.consumeResponse(query, "", true, commonConfig.getLlmSplitEndSet()
                , commonConfig.getMinSplitSentenceLen());
        if (StringUtils.hasText(msg)) {
            consumeMsg(msg, query);
        }
        if (!sessionContext.getPendingHasResponseSet().contains(query.getRequestId())) {
            if (commonConfig.isBillingEnable() && sessionContext.getFreezeSet().contains(query.getRequestId())) {
                llmBillingService.unfreezeQuota(sessionContext.getAccountId(),
                        sessionContext.getWebSocketSession().getId(),
                        query.getRequestId(), commonConfig.getBillingCost());
            }
            sendLLmUnknowAnswerComplete(sessionContext, query);
        }
        sendComplete(sessionContext, query, "complete");
    }

    private void sendLLmUnknowAnswerComplete(SessionContext sessionContext, Query query) {
        // 直接发送结束drml给上游
        int seq = sessionContext.getPendingResponseSeqMap().getOrDefault(query.getRequestId(),
                -1) + 1;
        sessionContext.getPendingResponseSeqMap().put(query.getRequestId(), seq);
        WebsocketUtil.send(sessionContext.getWebSocketSession(), WebsocketLlmResponse.builder()
                .action(ActionType.TEXT_RENDER)
                .requestId(query.getRequestId())
                .body(TextRenderBody.toBuild(query.getRequestId(),
                        commonConfig.getLlmNoAnswer(),
                        false, seq, sessionContext.getSessionId(), true))
                .code(0)
                .message("")
                .build());
    }

    private void sendComplete(SessionContext sessionContext, Query query, String msg) {
        // 直接发送结束drml给上游
        int seq = sessionContext.getPendingResponseSeqMap().getOrDefault(query.getRequestId(),
                -1) + 1;
        WebsocketUtil.send(sessionContext.getWebSocketSession(), WebsocketLlmResponse.builder()
                .action(ActionType.TEXT_RENDER)
                .requestId(query.getRequestId())
                .body(TextRenderBody.toBuild(query.getRequestId(),
                        LLMConstants.REPLY_COMPLETE_DRML,
                        true, seq, sessionContext.getSessionId(), true))
                .code(0)
                .message(msg)
                .build());
        sessionContext.clearPendingResponse(query);
        sessionContext.getPendingHasResponseSet().remove(query.getRequestId());
    }
}
