package com.baidu.acg.piat.llm.dm.model.faq;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * {
 *     "appId":"digital-human-talk",
 *     "query":"天气",
 *     "hitThreshold":80,
 *     "clarificationThreshold":80,
 *     "logId":"h2cna4m87te7waj0",
 *     "openEmbedding":true
 * }
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class FaqRequest {
    private String appId;
    private String query;
    private int hitThreshold;
    private int clarificationThreshold;
    private String logId;
    private boolean openEmbedding = true;
}

