package com.baidu.acg.piat.llm.dm.model.text2gesture;

import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Builder
@Data
@Slf4j
@NoArgsConstructor
@AllArgsConstructor
public class AnimojisTagConfigJson {
    /**
     * 情绪引擎，第一期先做关键词匹配动作
     */
    private List<EmotionRule> emotionRules;

    private List<AnimojiRule> animojiRules;
}
