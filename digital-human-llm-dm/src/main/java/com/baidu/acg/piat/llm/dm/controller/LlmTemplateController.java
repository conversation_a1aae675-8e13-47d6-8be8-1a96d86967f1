package com.baidu.acg.piat.llm.dm.controller;

import com.baidu.acg.piat.digitalhuman.common.model.PageResponse;
import com.baidu.acg.piat.digitalhuman.common.model.Response;
import com.baidu.acg.piat.llm.dm.model.LlmRoleTemplateModel;
import com.baidu.acg.piat.llm.dm.service.LlmTemplateService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Slf4j
@RestController
@RequestMapping("/api/digitalhuman/v1/llm/template")
@RequiredArgsConstructor
public class LlmTemplateController {
    private final LlmTemplateService llmTemplateService;

    /**
     * 创建Llm模板
     *
     * @param template Llm角色模板模型类
     * @return 返回Response对象，包含返回的数据类型为LlmRoleTemplateModel
     */
    @PostMapping
    public Response<LlmRoleTemplateModel> createLlmTemplate(@RequestBody LlmRoleTemplateModel template) {
        log.debug("Start to create llm template:{}", template);
        return Response.success(llmTemplateService.createLlmTemplate(template));
    }

    /**
     * 通过模板类型分页查询llm模板列表
     *
     * @param templateType 模板类型
     * @param templateName 模板名称
     * @param pageNo       分页页码
     * @param pageSize     每页大小
     * @return 角色模板分页响应对象
     */
    @GetMapping
    public PageResponse<LlmRoleTemplateModel> listByTemplateType(@RequestParam(required = false, defaultValue = "1") int templateType,
                                                                 @RequestParam(required = false, defaultValue = "") String templateName,
                                                                 @RequestParam String screenType, @RequestParam int pageNo, @RequestParam int pageSize) {
        log.debug("Start to list llm templateType:{}", templateType);
        return llmTemplateService.listByTemplateType(templateType, templateName, screenType, pageNo, pageSize);
    }


    /**
     * 通过模板类型分页查询llm模板列表
     *
     * @param templateType 模板类型
     * @param templateName 模板名称
     * @param pageNo       分页页码
     * @param pageSize     每页大小
     * @return 角色模板分页响应对象
     */
    @GetMapping( "/detail")
    public Response<LlmRoleTemplateModel> getTemplate(@RequestParam String llmRoleTemplateId) {
        log.debug("Start to get llm templateType id:{}", llmRoleTemplateId);
        return Response.success(llmTemplateService.getLlmTemplate(llmRoleTemplateId));
    }

    /**
     * 更新LLM模板
     *
     * @param llmTemplate 需要更新的LLM模板对象
     * @return 返回更新后的LLM模板对象及其响应信息
     */
    @PutMapping()
    public Response<LlmRoleTemplateModel> updateLlmTemplate(@RequestBody LlmRoleTemplateModel llmTemplate) {
        log.debug("Start to update llm template:{}", llmTemplate);
        return Response.success(llmTemplateService.updateLlmTemplate(llmTemplate));
    }

    /**
     * 删除LLM模板
     *
     * @param llmTemplateIds 要删除的LLM模板ID列表
     * @return 成功返回Response对象，无论删除是否成功都返回空值，失败返回相应错误信息
     */
    @PostMapping("/delete")
    public Response<Void> deleteLlmTemplate(@RequestBody List<String> llmTemplateIds) {
        log.debug("Start to delete llm template:{}", llmTemplateIds);
        llmTemplateService.batchDeleteTemplate(llmTemplateIds);
        return Response.success(null);
    }
}
