package com.baidu.acg.piat.llm.dm.service;

import java.util.List;

import org.springframework.web.socket.WebSocketSession;

import com.baidu.acg.piat.llm.dm.model.SessionContext;

public interface SessionManager {

    void addSession(WebSocketSession session);

    /**
     * if not present return null
     *
     * @param websocketSessionId
     * @return
     */
    SessionContext getSessionContext(String websocketSessionId);

    SessionContext removeSession(String websocketSessionId);

    void clearSession(SessionContext sessionContext);

    List<SessionContext> sessions();

    List<SessionContext> findSessionContext(String appId);

    /**
     * Will be called by scheduler.
     */
    void scanInactiveSessions();

}
