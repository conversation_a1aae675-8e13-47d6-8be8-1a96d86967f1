package com.baidu.acg.piat.llm.dm.service.impl;

import com.baidu.acg.piat.llm.dm.error.LLMAdaptorException;
import com.baidu.acg.piat.llm.dm.model.qianfan.ConversationContext;
import com.baidu.acg.piat.llm.dm.service.LLMConversationSessionContext;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.time.Duration;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutionException;

/**
 * 存储所有llm上下文
 *
 * <AUTHOR>
 * @date 2023/11/6
 */
@Service
@Slf4j
public class LLMConversationContextInMemory implements LLMConversationSessionContext {

    public Map<String, ConversationContext> contextMap = new ConcurrentHashMap<>();

    /**
     * 默认存储最长的内存时间
     */
    @Value("${adaptor.llm.conversationStoreSeconds:1800}")
    private long conversationStoreSeconds;

    private LoadingCache<String, ConversationContext> inMemoryContextMap;

    @PostConstruct
    public void initCache() {
        inMemoryContextMap = CacheBuilder.newBuilder().expireAfterAccess(Duration.ofSeconds(conversationStoreSeconds))
                .removalListener(removalNotification -> log.info("conversation id :{} remove",
                        removalNotification.getKey())).recordStats()
                .build(new CacheLoader<>() {
                    @Override
                    public ConversationContext load(String s) {
                        ConversationContext conversationContext = new ConversationContext();
                        conversationContext.setConversationId(s);
                        return conversationContext;
                    }
                });
    }

    @Override
    public ConversationContext getContextOrCreate(String conversationId) {
        try {
            return inMemoryContextMap.get(conversationId);
        } catch (ExecutionException e) {
            throw new LLMAdaptorException(e);
        }
    }

    @Override
    public void clearContext(String conversationId) {
        contextMap.remove(conversationId);
    }
}
