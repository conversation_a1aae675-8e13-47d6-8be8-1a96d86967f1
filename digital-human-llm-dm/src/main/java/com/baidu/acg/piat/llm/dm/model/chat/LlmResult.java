package com.baidu.acg.piat.llm.dm.model.chat;

import java.util.Map;
import java.util.UUID;

import com.baidu.acg.piat.digitalhuman.common.model.BaseResponse;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 */
@Getter
@Setter
@ToString
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class LlmResult {

    @Builder.Default
    String requestId = UUID.randomUUID().toString();

    String result;

    private int code;

    private BaseResponse.Message message = new BaseResponse.Message();

    private Map<String, Object> extras;

}
