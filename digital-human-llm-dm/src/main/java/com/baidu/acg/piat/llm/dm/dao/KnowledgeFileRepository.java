package com.baidu.acg.piat.llm.dm.dao;

import com.baidu.acg.piat.llm.dm.model.KnowledgeFileModel;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.repository.PagingAndSortingRepository;

import java.util.Optional;

/**
 * <AUTHOR>
 * @since 2023/11/23 14:23
 */
public interface KnowledgeFileRepository extends PagingAndSortingRepository<KnowledgeFileModel, Long> {

    Optional<KnowledgeFileModel> findByFileId(String id);

    Page<KnowledgeFileModel> findByKnowledgeBaseIdAndNameContainingOrderByCreateTimeDesc(String knowledgeBaseId, String name, Pageable pageable);

}
