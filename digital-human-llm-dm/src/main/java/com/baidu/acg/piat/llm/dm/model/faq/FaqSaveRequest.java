package com.baidu.acg.piat.llm.dm.model.faq;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * {
 *     "appId": "digital-human-talk",
 *     "openEmbedding":true,
 *     "questions": [
 *         "明天天气怎么样"
 *     ],
 *     "answers": [
 *         {
 *             "type": "TEXT",
 *             "speak": [
 *                 {
 *                     "answer": "明天很好"
 *                 }
 *             ]
 *
 *         }
 *     ],
 *     "createTime": "2023-02-01 20:12:40"
 * }
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class FaqSaveRequest {
    private String appId;
    private boolean openEmbedding = true;
    private List<String> questions;
    private List<Answer> answers;
    private String createTime;

    @Data
    @Builder
    public static class Answer {
        private String type = "TEXT";
        private List<Speak> speak;

    }

    @Builder
    @Data
    public static class Speak {
        private String answer;
    }
}
