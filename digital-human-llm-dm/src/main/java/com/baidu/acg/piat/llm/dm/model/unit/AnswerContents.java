package com.baidu.acg.piat.llm.dm.model.unit;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 **/
@Data
@ApiModel
public class AnswerContents {
    @ApiModelProperty(value = "回复内容类型 1:纯文本")
    private Integer type;
    @ApiModelProperty(value = "回复内容")
    private String text;
    @ApiModelProperty(value = "原始query实体值")
    private String original;
}
