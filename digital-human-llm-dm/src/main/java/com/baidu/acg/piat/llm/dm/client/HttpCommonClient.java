package com.baidu.acg.piat.llm.dm.client;

import java.io.IOException;
import java.util.Map;
import javax.annotation.Resource;

import com.baidu.acg.piat.digitalhuman.common.exception.DigitalHumanCommonException;
import com.baidu.acg.piat.digitalhuman.common.llmrole.CreateKnowledge;
import com.baidu.acg.piat.llm.dm.model.faq.FaqRequest;
import com.baidu.acg.piat.llm.dm.model.faq.FaqResponse;
import com.baidu.acg.piat.llm.dm.model.faq.FaqSaveRequest;
import okhttp3.MediaType;
import okhttp3.MultipartBody;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;
import org.apache.commons.collections4.MapUtils;
import org.springframework.context.annotation.Configuration;

import com.baidu.acg.piat.digitalhuman.common.utils.JsonUtil;
import com.baidu.acg.piat.llm.dm.model.aida.WorkflowRequest;
import com.baidu.acg.piat.llm.dm.model.aida.WorkflowResponse;
import com.baidu.acg.piat.llm.dm.model.text2gesture.Text2GestureRequest;
import com.baidu.acg.piat.llm.dm.model.text2gesture.Text2GestureResponse;
import com.baidu.acg.piat.llm.dm.tools.copilot.CopilotResultParseRequest;
import com.baidu.acg.piat.llm.dm.tools.copilot.CopilotResultParseResponse;

import io.vavr.control.Try;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.multipart.MultipartFile;

@Slf4j
@Configuration
public class HttpCommonClient {

    private static final String CONTENT_TYPE = "content-type";

    private static final String CONTENT_TYPE_VALUE = "application/json; charset=utf-8";

    private static final MediaType MEDIA_TYPE = MediaType.parse("application/json; charset=utf-8");

    @Resource
    OkHttpClient httpClient;

    /**
     * 智能动作引擎
     *
     * @param url
     * @param text2GestureRequest
     * @return
     */
    public WorkflowResponse requestAida(String url, WorkflowRequest workflowRequest) {
        String requestParamsStr = Try.of(() -> JsonUtil.writeValueAsString(workflowRequest)).getOrNull();
        if (requestParamsStr == null) {
            log.warn("Failed to Aida request params is to json ={}", requestParamsStr);
            return null;
        }

        Request request =
            new Request.Builder()
                .url(url)
                .addHeader(CONTENT_TYPE, CONTENT_TYPE_VALUE)
                .post(RequestBody.create(MEDIA_TYPE, requestParamsStr))
                .build();
        log.info("Request aida url={}, req body={}", url, requestParamsStr);
        try {
            Response response = httpClient.newCall(request).execute();
            if (response.isSuccessful()) {
                String responseBody = response.body().string();
                log.info("Request aida response body={}", responseBody);
                WorkflowResponse workflowResponse =
                    Try.of(() -> JsonUtil.readValue(responseBody, WorkflowResponse.class)).getOrNull();
                log.debug("Response aida = {}", workflowResponse);
                return workflowResponse;
            } else {
                return null;
            }
        } catch (Exception e) {
            log.error("Failed to Text2Gesture response err", e);
            return null;
        }
    }

    /**
     * 智能动作引擎
     *
     * @param url
     * @param text2GestureRequest
     * @return
     */
    public Text2GestureResponse text2gesture(String url, Text2GestureRequest text2GestureRequest) {
        String requestParamsStr = Try.of(() -> JsonUtil.writeValueAsString(text2GestureRequest)).getOrNull();
        if (requestParamsStr == null) {
            log.warn("Failed to Text2Gesture request params is to json ={}", text2GestureRequest);
            return null;
        }

        Request request =
            new Request.Builder()
                .url(url)
                .addHeader(CONTENT_TYPE, CONTENT_TYPE_VALUE)
                .post(RequestBody.create(MEDIA_TYPE, requestParamsStr))
                .build();
        log.info("Request text2Gesture url={}, req body={}", url, requestParamsStr);
        try {
            Response response = httpClient.newCall(request).execute();
            if (response.isSuccessful()) {
                String responseBody = response.body().string();
                log.info("Request Text2Gesture response body={}", responseBody);
                Text2GestureResponse text2GestureResponse =
                    Try.of(() -> JsonUtil.readValue(responseBody, Text2GestureResponse.class)).getOrNull();
                log.debug("Response Text2GestureResponse = {}", text2GestureResponse);
                return text2GestureResponse;
            } else {
                return null;
            }
        } catch (Exception e) {
            log.error("Failed to Text2Gesture response err", e);
            return null;
        }
    }

    public void insertFaq(String url, FaqSaveRequest faqSaveRequest) {
        String requestParamsStr = Try.of(() -> JsonUtil.writeValueAsString(faqSaveRequest)).getOrNull();
        if (requestParamsStr == null) {
            log.warn("Failed to FaqSave request params is to json ={}", faqSaveRequest);
            return;
        }

        Request request =
            new Request.Builder()
                .url(url)
                .addHeader(CONTENT_TYPE, CONTENT_TYPE_VALUE)
                .post(RequestBody.create(MEDIA_TYPE, requestParamsStr))
                .build();
        log.info("Request FaqSave url={}, req body={}", url, requestParamsStr);
        try {
            Response response = httpClient.newCall(request).execute();
            if (response.isSuccessful()) {
                String responseBody = response.body().string();
                log.info("Request FaqSave response body={}", responseBody);
            }
        } catch (Exception e) {
            log.error("Failed to FaqSave response err", e);
        }
    }

    public FaqResponse faqSearch(String url, FaqRequest faqRequest) {
        String requestParamsStr = Try.of(() -> JsonUtil.writeValueAsString(faqRequest)).getOrNull();
        if (requestParamsStr == null) {
            log.warn("Failed to FaqSearch request params is to json ={}", faqRequest);
            return null;
        }

        Request request =
            new Request.Builder()
                .url(url)
                .addHeader(CONTENT_TYPE, CONTENT_TYPE_VALUE)
                .post(RequestBody.create(MEDIA_TYPE, requestParamsStr))
                .build();
        log.info("Request FaqSearch url={}, req body={}", url, requestParamsStr);
        try {
            Response response = httpClient.newCall(request).execute();
            if (response.isSuccessful()) {
                String responseBody = response.body().string();
                log.info("Request FaqSearch response body={}", responseBody);
                FaqResponse faqResponse = Try.of(() -> JsonUtil.readValue(responseBody, FaqResponse.class)).getOrNull();
                log.debug("Response FaqSearch = {}", faqResponse);
                return faqResponse;
            } else {
                return null;
            }
        } catch (Exception e) {
            log.error("Failed to FaqSearch response err", e);
            return null;
        }
    }

    public CopilotResultParseResponse copilotResultParse(
        String url, CopilotResultParseRequest copilotResultParseRequest) {
        String requestParamsStr = Try.of(() -> JsonUtil.writeValueAsString(copilotResultParseRequest)).getOrNull();
        if (requestParamsStr == null) {
            log.warn("Failed to Text2Gesture request params is to json ={}", copilotResultParseRequest);
            return null;
        }

        Request request =
            new Request.Builder()
                .url(url)
                .addHeader(CONTENT_TYPE, CONTENT_TYPE_VALUE)
                .post(RequestBody.create(MEDIA_TYPE, requestParamsStr))
                .build();
        log.info("Request copilot response parse url={}, req body={}", url, requestParamsStr);
        try {
            Response response = httpClient.newCall(request).execute();
            if (response.isSuccessful()) {
                String responseBody = response.body().string();
                log.info("Request copilot parse response body={}", responseBody);
                CopilotResultParseResponse copilotResultParseResponse =
                    Try.of(() -> JsonUtil.readValue(responseBody, CopilotResultParseResponse.class)).getOrNull();
                log.debug("Response copilot parse = {}", copilotResultParseResponse);
                return copilotResultParseResponse;
            } else {
                return null;
            }
        } catch (Exception e) {
            log.error("Failed to Text2Gesture response err", e);
            return null;
        }
    }

    public Map<String, Object> bsCartNormalize(String url, Map<String, Object> params) {
        String requestParamsStr = Try.of(() -> JsonUtil.writeValueAsString(params)).getOrNull();
        if (requestParamsStr == null) {
            log.warn("Failed to Bs card normalize request params is to json ={}", params);
            return null;
        }
        Request request =
            new Request.Builder()
                .url(url)
                .addHeader(CONTENT_TYPE, CONTENT_TYPE_VALUE)
                .post(RequestBody.create(MEDIA_TYPE, requestParamsStr))
                .build();
        log.info("Request BsCartNormalize response parse url={}, req body={}", url, requestParamsStr);
        try {
            Response response = httpClient.newCall(request).execute();
            if (response.isSuccessful()) {
                String responseBody = response.body().string();
                log.info("Request BsCartNormalize response body={}", responseBody);
                Map<String, Object> result = Try.of(() -> JsonUtil.readValue(responseBody, Map.class)).getOrNull();
                log.debug("Response BsCartNormalize = {}", result);
                return result;
            } else {
                return null;
            }
        } catch (Exception e) {
            log.error("Failed to Text2Gesture response err", e);
            return null;
        }
    }

    public Map<String, Object> createDatabase(String url, CreateKnowledge createKnowledge, String token, String bceId) {
        String requestParamsStr = Try.of(() -> JsonUtil.writeValueAsString(createKnowledge)).getOrNull();
        Request request =
            new Request.Builder()
                .url(url)
                .addHeader(CONTENT_TYPE, CONTENT_TYPE_VALUE)
                .addHeader("Authorization", token)
                .addHeader("X-Bce-Account-id", bceId)
                .post(RequestBody.create(MEDIA_TYPE, requestParamsStr))
                .build();
        log.info("Request create database response parse url={}, req body={}", url, requestParamsStr);
        try {
            Response response = httpClient.newCall(request).execute();
            if (response.isSuccessful()) {
                String responseBody = response.body().string();
                log.info("Request create database response body={}", responseBody);
                Map<String, Object> result = Try.of(() -> JsonUtil.readValue(responseBody, Map.class)).getOrNull();
                log.debug("Response create database = {}", result);
                if ((Integer) result.get("code") != 0) {
                    throw new DigitalHumanCommonException("大模型平台删除知识库失败:" + (String) result.get("message"));
                }
                return result;
            } else {
                throw new DigitalHumanCommonException("fail to call copilot");
            }
        } catch (DigitalHumanCommonException e) {
            throw e;
        } catch (Exception e) {
            log.error("Failed to create database", e);
            throw new DigitalHumanCommonException("服务异常:", e);
        }
    }

    public Map<String, Object> deleteDatabase(String url, Map<String, String> params, String token, String bceId) {
        String requestParamsStr = Try.of(() -> JsonUtil.writeValueAsString(params)).getOrNull();
        if (requestParamsStr == null) {
            log.warn("Failed to delete database, request params is to json ={}", params);
            throw new DigitalHumanCommonException("输入参数不应该为空");
        }
        Request request =
            new Request.Builder()
                .url(url)
                .addHeader(CONTENT_TYPE, CONTENT_TYPE_VALUE)
                .addHeader("X-Bce-Account-id", bceId)
                .addHeader("Authorization", token)
                .post(RequestBody.create(MEDIA_TYPE, requestParamsStr))
                .build();
        log.info("Request delete database response parse url={}, req body={}", url, requestParamsStr);
        try {
            Response response = httpClient.newCall(request).execute();
            if (response.isSuccessful()) {
                String responseBody = response.body().string();
                Map<String, Object> result = Try.of(() -> JsonUtil.readValue(responseBody, Map.class)).getOrNull();
                log.debug("Response delete database = {}", result);
                if ((Integer) result.get("code") != 0) {
                    throw new DigitalHumanCommonException("大模型平台删除知识库失败:" + (String) result.get("message"));
                }
                return result;
            } else {
                throw new DigitalHumanCommonException("fail to call copilot");
            }
        } catch (DigitalHumanCommonException e) {
            throw e;
        } catch (Exception e) {
            log.error("Failed to delete database", e);
            throw new DigitalHumanCommonException("服务异常:", e);
        }
    }

    public Map<String, Object> detailDatabase(String url, Map<String, String> params, String token, String bceId) {
        String requestParamsStr = Try.of(() -> JsonUtil.writeValueAsString(params)).getOrNull();
        if (MapUtils.isEmpty(params)) {
            log.warn("Failed to detail database, request params is to json ={}", params);
            throw new DigitalHumanCommonException("输入参数不应该为空");
        }
        Request request =
            new Request.Builder()
                .url(url)
                .addHeader(CONTENT_TYPE, CONTENT_TYPE_VALUE)
                .addHeader("Authorization", token)
                .addHeader("X-Bce-Account-id", bceId)
                .post(RequestBody.create(MEDIA_TYPE, requestParamsStr))
                .build();
        log.info("Request detail database response parse url={}, req body={}", url, requestParamsStr);
        try {
            Response response = httpClient.newCall(request).execute();
            if (response.isSuccessful()) {
                String responseBody = response.body().string();
                log.info("Request detail database response body={}", responseBody);
                Map<String, Object> result = Try.of(() -> JsonUtil.readValue(responseBody, Map.class)).getOrNull();
                log.debug("Response detail database = {}", result);
                if ((Integer) result.get("code") != 0) {
                    throw new DigitalHumanCommonException("大模型平台删除知识库失败:" + (String) result.get("message"));
                }
                return result;
            } else {
                throw new DigitalHumanCommonException("fail to call copilot");
            }
        } catch (DigitalHumanCommonException e) {
            throw e;
        } catch (Exception e) {
            log.error("Failed to create database", e);
            throw new DigitalHumanCommonException("服务异常:", e);
        }
    }

    public String listFiles(String url, Map<String, String> params, String token, String bceId) {
        String requestParamsStr = Try.of(() -> JsonUtil.writeValueAsString(params)).getOrNull();
        if (MapUtils.isEmpty(params)) {
            log.warn("Failed to create database, request params is to json ={}", params);
            throw new DigitalHumanCommonException("输入参数不应该为空");
        }

        Request request =
            new Request.Builder()
                .url(url)
                .addHeader(CONTENT_TYPE, CONTENT_TYPE_VALUE)
                .addHeader("X-Bce-Account-id", bceId)
                .addHeader("Authorization", token)
                .post(RequestBody.create(MEDIA_TYPE, requestParamsStr))
                .build();
        log.info("Request create database response parse url={}, req body={}", url, requestParamsStr);
        try {
            Response response = httpClient.newCall(request).execute();
            if (response.isSuccessful()) {
                String responseBody = response.body().string();
                log.info("Request BsCartNormalize response body={}", responseBody);
                if (JsonUtil.om.readTree(responseBody).path("code").asInt() != 0) {
                    throw new DigitalHumanCommonException(JsonUtil.om.readTree(responseBody).path("message").asText());
                }
                return responseBody;
            } else {
                throw new DigitalHumanCommonException("fail to call copilot");
            }
        } catch (DigitalHumanCommonException e) {
            throw e;
        } catch (Exception e) {
            log.error("Failed to Text2Gesture response err", e);
            throw new DigitalHumanCommonException("服务异常:", e);
        }
    }

    public Map<String, Object> uploadFile(String url, String token, String bceId, String fileName, MultipartFile file) {
        try {

            RequestBody requestBody =
                new MultipartBody.Builder()
                    .setType(MultipartBody.FORM)
                    .addFormDataPart(
                        "file",
                        fileName,
                        RequestBody.create(MediaType.parse("application/octet-stream"), file.getBytes()))
                    .build();

            Request request =
                new Request.Builder()
                    .addHeader("Authorization", token)
                    .addHeader("X-Bce-Account-id", bceId)
                    .url(url)
                    .post(requestBody)
                    .build();
            // 发送请求
            Response response = httpClient.newCall(request).execute();
            if (response.isSuccessful()) {
                String responseBody = response.body().string();
                log.info("Request upload file response body={}", responseBody);
                Map<String, Object> result = Try.of(() -> JsonUtil.readValue(responseBody, Map.class)).getOrNull();
                log.debug("Response upload file = {}", result);
                if ((Integer) result.get("code") != 0) {
                    throw new DigitalHumanCommonException("大模型平台删除知识库失败:" + (String) result.get("message"));
                }
                return result;
            } else {
                throw new DigitalHumanCommonException("can not post to copilot");
            }
        } catch (IOException e) {
            throw new DigitalHumanCommonException("can not upload file", e);
        }
    }

    public String batchAssociateFile(String url, Map<String, Object> params, String token, String bceId) {
        String requestParamsStr = Try.of(() -> JsonUtil.writeValueAsString(params)).getOrNull();
        if (MapUtils.isEmpty(params)) {
            log.warn("Failed to batch association file, request params is to json ={}", params);
            throw new DigitalHumanCommonException("输入参数不应该为空");
        }

        Request request =
            new Request.Builder()
                .url(url)
                .addHeader(CONTENT_TYPE, CONTENT_TYPE_VALUE)
                .addHeader("Authorization", token)
                .addHeader("X-Bce-Account-id", bceId)
                .post(RequestBody.create(MEDIA_TYPE, requestParamsStr))
                .build();
        log.info("Request batch association file response parse url={}, req body={}", url, requestParamsStr);
        try {
            Response response = httpClient.newCall(request).execute();
            if (response.isSuccessful()) {
                String responseBody = response.body().string();
                log.info("Request batch association file response body={}", responseBody);
                if (JsonUtil.om.readTree(responseBody).path("code").asInt() != 0) {
                    throw new DigitalHumanCommonException(JsonUtil.om.readTree(responseBody).path("message").asText());
                }
                return responseBody;
            } else {
                throw new DigitalHumanCommonException("fail to call copilot");
            }
        } catch (DigitalHumanCommonException e) {
            throw e;
        } catch (Exception e) {
            log.error("Failed to batch association file response err", e);
            throw new DigitalHumanCommonException("服务异常:", e);
        }
    }

    public Map<String, Object> associationFile(String url, Map<String, String> params, String token, String bceId) {
        String requestParamsStr = Try.of(() -> JsonUtil.writeValueAsString(params)).getOrNull();
        if (MapUtils.isEmpty(params)) {
            log.warn("Failed to association file, request params is to json ={}", params);
            throw new DigitalHumanCommonException("输入参数不应该为空");
        }
        Request request =
            new Request.Builder()
                .url(url)
                .addHeader(CONTENT_TYPE, CONTENT_TYPE_VALUE)
                .addHeader("Authorization", token)
                .addHeader("X-Bce-Account-id", bceId)
                .post(RequestBody.create(MEDIA_TYPE, requestParamsStr))
                .build();
        log.info("Request association file response parse url={}, req body={}", url, requestParamsStr);
        try {
            Response response = httpClient.newCall(request).execute();
            if (response.isSuccessful()) {
                String responseBody = response.body().string();
                log.info("Request association file response body={}", responseBody);
                Map<String, Object> result = Try.of(() -> JsonUtil.readValue(responseBody, Map.class)).getOrNull();
                log.debug("Response association file = {}", result);
                if ((Integer) result.get("code") != 0) {
                    throw new DigitalHumanCommonException("大模型平台删除知识库失败:" + (String) result.get("message"));
                }
                return result;
            } else {
                throw new DigitalHumanCommonException("fail to call copilot");
            }
        } catch (DigitalHumanCommonException e) {
            throw e;
        } catch (Exception e) {
            log.error("Failed to association file", e);
            throw new DigitalHumanCommonException("服务异常:", e);
        }
    }

    public Map<String, Object> batchDeleteFile(String url, Map<String, Object> params, String token, String bceId) {
        String requestParamsStr = Try.of(() -> JsonUtil.writeValueAsString(params)).getOrNull();
        if (MapUtils.isEmpty(params)) {
            log.warn("Failed to batch delete file, request params is to json ={}", params);
            throw new DigitalHumanCommonException("输入参数不应该为空");
        }
        Request request =
            new Request.Builder()
                .url(url)
                .addHeader(CONTENT_TYPE, CONTENT_TYPE_VALUE)
                .addHeader("X-Bce-Account-id", bceId)
                .addHeader("Authorization", token)
                .post(RequestBody.create(MEDIA_TYPE, requestParamsStr))
                .build();
        log.info("Request batch delete file response parse url={}, req body={}", url, requestParamsStr);
        try {
            Response response = httpClient.newCall(request).execute();
            if (response.isSuccessful()) {
                String responseBody = response.body().string();
                log.info("Request batch delete file response body={}", responseBody);
                Map<String, Object> result = Try.of(() -> JsonUtil.readValue(responseBody, Map.class)).getOrNull();
                log.debug("Response batch delete file = {}", result);
                if ((Integer) result.get("code") != 0) {
                    throw new DigitalHumanCommonException("大模型平台删除知识库失败:" + (String) result.get("message"));
                }
                return result;
            } else {
                throw new DigitalHumanCommonException("fail to call copilot");
            }
        } catch (DigitalHumanCommonException e) {
            throw e;
        } catch (Exception e) {
            log.error("Failed to batch delete file", e);
            throw new DigitalHumanCommonException("服务异常:", e);
        }
    }

    public String batchListFiles(String url, Map<String, Object> params, String token, String bceId) {
        String requestParamsStr = Try.of(() -> JsonUtil.writeValueAsString(params)).getOrNull();
        if (MapUtils.isEmpty(params)) {
            log.warn("Failed to batch list file, request params is to json ={}", params);
            throw new DigitalHumanCommonException("输入参数不应该为空");
        }

        Request request =
            new Request.Builder()
                .url(url)
                .addHeader(CONTENT_TYPE, CONTENT_TYPE_VALUE)
                .addHeader("Authorization", token)
                .addHeader("X-Bce-Account-id", bceId)
                .post(RequestBody.create(MEDIA_TYPE, requestParamsStr))
                .build();
        log.info("Request batch list file response parse url={}, req body={}", url, requestParamsStr);
        try {
            Response response = httpClient.newCall(request).execute();
            if (response.isSuccessful()) {
                String responseBody = response.body().string();
                log.info("Requestbatch list file response body={}", responseBody);
                if (JsonUtil.om.readTree(responseBody).path("code").asInt() != 0) {
                    throw new DigitalHumanCommonException(JsonUtil.om.readTree(responseBody).path("message").asText());
                }
                return responseBody;
            } else {
                throw new DigitalHumanCommonException("fail to call copilot");
            }
        } catch (DigitalHumanCommonException e) {
            throw e;
        } catch (Exception e) {
            log.error("Failed to batch list file response err", e);
            throw new DigitalHumanCommonException("服务异常:", e);
        }
    }
}
