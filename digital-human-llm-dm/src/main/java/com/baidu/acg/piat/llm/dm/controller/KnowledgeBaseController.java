package com.baidu.acg.piat.llm.dm.controller;

import com.baidu.acg.piat.digitalhuman.common.llmrole.KnowledgeBase;
import com.baidu.acg.piat.digitalhuman.common.llmrole.KnowledgeFile;
import com.baidu.acg.piat.digitalhuman.common.model.PageResponse;
import com.baidu.acg.piat.digitalhuman.common.model.Response;
import com.baidu.acg.piat.llm.dm.service.KnowledgeBaseService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;


/**
 * <AUTHOR>
 * @since 2023/11/23 13:47
 */
@Slf4j
@RestController
@RequestMapping("/api/digitalhuman/v1/llm/knowledge")
@RequiredArgsConstructor
public class KnowledgeBaseController {

    private final KnowledgeBaseService knowledgeBaseService;

    @PostMapping
    public Response<KnowledgeBase> createKnowledgeBase(@RequestBody KnowledgeBase request) {
        log.debug("start to create knowledgeBase:{}", request);
        return Response.success(knowledgeBaseService.createKnowledgeBase(request));
    }

    @GetMapping
    public PageResponse<KnowledgeBase> listByType(@RequestParam String accountId, @RequestParam int type,
                                                @RequestParam String name, @RequestParam int pageNo, @RequestParam int pageSize) {
        log.debug("start to list knowledgeBase name is {}, accountId is {}, type is {}", name, accountId, type);
        return knowledgeBaseService.listByType(accountId, type, pageNo, pageSize, name);
    }

    @PostMapping("/delete")
    public Response<Void> deleteKnowledgeBase(@RequestParam String accountId,
                              @RequestBody List<String> ids) {
        log.debug("start to dele knowledgeBase, accountId is {}, ids is {}", accountId, ids);
        knowledgeBaseService.batchDeleteKnowledgeBase(accountId, ids);
        return Response.success(null);
    }

    @GetMapping("/detail")
    public Response<KnowledgeBase> detail(@RequestParam String accountId, @RequestParam String knowledgeBaseId) {
        log.debug("start to detail knowledgeBase, accountId is {}, knowledgeBaseId is {}", accountId, knowledgeBaseId);
        return Response.success(knowledgeBaseService.detailKnowledgeBase(accountId, knowledgeBaseId));
    }

    @PostMapping("/file/create")
    public Response<Map<String, String>> uploadFile(@RequestParam("file") MultipartFile file, @RequestParam("fileName") String fileName) {
        log.debug("start to upload file,  files is {}, file name is {}", file.getName(), fileName);
        return Response.success(knowledgeBaseService.uploadFile(fileName, file));
    }

    @PostMapping("/file/upload")
    public Response<KnowledgeFile> importFile(@RequestParam String accountId, @RequestParam String knowledgeBaseId,
                                               @RequestBody List<KnowledgeFile> files) {
        log.debug("start to import file, accountId is {}, knowledgeBaseId is {} , files is {}", accountId, knowledgeBaseId, files);
        return Response.success(knowledgeBaseService.uploadFileToKnowledgeBase(accountId, knowledgeBaseId, files));
    }


    @PostMapping("/file/delete")
    public Response<Void> deleteFile(@RequestParam String accountId, @RequestParam String knowledgeBaseId,
                                     @RequestBody List<String> fileIds) {
        log.debug("start to delete file, accountId is {}, knowledgeBaseId is {} , fileIds is {}", accountId, knowledgeBaseId, fileIds);
        knowledgeBaseService.deleteFileInKnowledgeBase(accountId, knowledgeBaseId, fileIds);
        return Response.success(null);
    }

    @GetMapping("/file/list")
    public PageResponse<KnowledgeFile> listFile(@RequestParam String accountId, @RequestParam String knowledgeBaseId,
                                                @RequestParam String name, @RequestParam int pageNo, @RequestParam int pageSize) {
        log.debug("start to list file, accountId is {}, knowledgeBaseId is {}, name is {}", accountId, knowledgeBaseId, name);
        return knowledgeBaseService.listFileInKnowledgeBase(accountId, knowledgeBaseId, name, pageNo, pageSize);

    }


}