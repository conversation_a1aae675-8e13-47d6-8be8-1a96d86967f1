package com.baidu.acg.piat.llm.dm.service;

import com.baidu.acg.piat.digitalhuman.common.model.PageResponse;
import com.baidu.acg.piat.llm.dm.model.LlmRoleTemplateModel;

import javax.transaction.Transactional;
import java.util.List;

public interface LlmTemplateService {

    @Transactional
    LlmRoleTemplateModel createLlmTemplate(LlmRoleTemplateModel llmRoleTemplate);

    @Transactional
    void batchDeleteTemplate(List<String> llmTemplateIds);

    @Transactional
    LlmRoleTemplateModel updateLlmTemplate(LlmRoleTemplateModel llmTemplate);

    LlmRoleTemplateModel getLlmTemplate(String llmRoleTemplateId);

    PageResponse<LlmRoleTemplateModel> listByTemplateType(int templateType, String templateName, String screenType, int pageNo, int pageSize);
}
