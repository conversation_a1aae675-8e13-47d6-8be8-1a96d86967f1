// Copyright (C) 2019 Baidu Inc. All rights reserved.
package com.baidu.acg.piat.llm.dm.service;

import com.baidu.acg.piat.digitalhuman.common.llmrole.LLMConfig;
import com.baidu.acg.piat.llm.dm.model.Query;
import com.baidu.acg.piat.llm.dm.model.SessionContext;
import com.baidu.acg.piat.llm.dm.model.qianfan.LLMEngineResponse;
import com.baidu.acg.piat.llm.dm.model.unit.ChatBotQueryDto;

import reactor.core.publisher.Flux;

public interface BotProviderService {

    /**
     * reset the context, change the bot context session id
     */
    default void reset(SessionContext sessionContext) {
    }

    default void clear(SessionContext sessionContext) {}

    default void init(SessionContext sessionContext) {}

    void query(SessionContext sessionContext, Query query);

    void interrupt(SessionContext sessionContext, String taskId);

    default Flux<String> chat(Query query, LLMConfig llmConfig) {
        return null;
    }

    /**
     * 只用于http测试, 用于ngd的stream流式接口测试
     * @param sessionContext
     * @param query
     * @param llmConfig
     * @return
     */
    default Flux<ChatBotQueryDto> queryWithReturn(SessionContext sessionContext, Query query, LLMConfig llmConfig) {
        return null;
    }

    default Flux<String> queryDifyWithReturn(SessionContext sessionContext, Query query, LLMConfig llmConfig) {
        return null;
    }

    default Flux<LLMEngineResponse> queryQianfanWithReturn(SessionContext sessionContext, Query query, LLMConfig llmConfig) {
        return null;
    }

    default Flux<String> queryKaiwuWithReturn(SessionContext sessionContext, Query query, LLMConfig llmConfig) {
        return null;
    }

    default Flux<String> queryAppBuilderWithReturn(SessionContext sessionContext, Query query, LLMConfig llmConfig) {
        return null;
    }

    String getBotType();
}
