package com.baidu.acg.piat.llm.dm.configure;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.socket.config.annotation.EnableWebSocket;
import org.springframework.web.socket.config.annotation.WebSocketConfigurer;
import org.springframework.web.socket.config.annotation.WebSocketHandlerRegistry;
import org.springframework.web.socket.server.standard.ServletServerContainerFactoryBean;

import com.baidu.acg.piat.llm.dm.websocket.WebsocketMessageHandler;

import lombok.Data;
import lombok.RequiredArgsConstructor;

/**
 * WebsocketConfigure
 *
 */
@Configuration
@EnableWebSocket
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class WebsocketConfigure  implements WebSocketConfigurer {

    private final WebsocketMessageHandler websocketMessageHandler;


    @Bean
    @ConfigurationProperties(prefix = "digitalhuman.llmdm.websocket.config")
    public WebsocketConfig websocketConfig() {
        return new WebsocketConfig();
    }

    @Override
    public void registerWebSocketHandlers(WebSocketHandlerRegistry webSocketHandlerRegistry) {
        var config = websocketConfig();
        webSocketHandlerRegistry.addHandler(websocketMessageHandler, config.endpoint)
                .setAllowedOrigins(config.getAllowedOrigins());
    }

    @Bean
    public ServletServerContainerFactoryBean createWebSocketContainer() {
        var config = websocketConfig();
        ServletServerContainerFactoryBean container = new ServletServerContainerFactoryBean();
        container.setMaxBinaryMessageBufferSize(config.getMaxBinaryBufferSize());
        container.setMaxTextMessageBufferSize(config.getMaxTextBufferSize());
        return container;
    }

    @Data
    public static class WebsocketConfig {

        private String endpoint = "/digital-human-llm-dm";

        private long keepAliveMillis = 1000 * 30;

        private int maxBinaryBufferSize = 1024 * 1024;

        private int maxTextBufferSize = 1024 * 1024;

        private String[] allowedOrigins = new String[] {"*"};

        private int sendTimeLimitSeconds = 36000;

        private int bufferSizeLimit = 10000;
    }
}
