package com.baidu.acg.piat.llm.dm.dao;

import com.baidu.acg.piat.llm.dm.model.LlmRoleModel;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.PagingAndSortingRepository;

import java.util.List;
import java.util.Optional;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.repository.query.Param;

/**
 * <AUTHOR>
 * @since 2023/11/23 14:23
 */
public interface LlmRoleRepository extends PagingAndSortingRepository<LlmRoleModel, Long> {
    Page<LlmRoleModel> findByRoleTypeAndNameContainingAndDelOrderByCreateTimeDesc(Integer roleType, String name, boolean del, Pageable pageable);

    Page<LlmRoleModel> findByAccountIdAndRoleTypeAndNameContainingAndDelOrderByCreateTimeDesc(String accountId, Integer roleType, String name, boolean del, Pageable pageable);

    Optional<LlmRoleModel> findByLlmRoleId(String roleId);

    List<LlmRoleModel> findByLlmRoleIdIn(List<String> roleIds);

    @Query(value = "select * from llm_role where account_id =:accountId and BINARY name=:name and del=0", nativeQuery = true)
    List<LlmRoleModel> findByAccountIdAndNameAndDel(@Param("accountId") String accountId, @Param("name") String name);

}
