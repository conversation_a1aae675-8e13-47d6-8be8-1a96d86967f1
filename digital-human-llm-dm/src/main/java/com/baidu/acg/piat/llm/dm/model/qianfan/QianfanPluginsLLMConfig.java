package com.baidu.acg.piat.llm.dm.model.qianfan;

import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Data;

/**
 * chat请求
 *
 * <AUTHOR>
 * @date 2023/11/8
 */
@Data
public class QianfanPluginsLLMConfig {

    /**
     * 通过对已生成的token增加惩罚，减少重复生成的现象。说明：
     * （1）值越大表示惩罚越大
     * （2）默认1.0，取值范围：[1.0, 2.0]
     */
    @JsonProperty("penalty_score")
    private double penaltyScore = 1.0;

    @JsonProperty("top_p")
    private double topP = 1.0;

    /**
     * 说明：
     * （1）较高的数值会使输出更加随机，而较低的数值会使其更加集中和确定
     * （2）默认0.95，范围 (0, 1.0]，不能为0
     * （3）建议该参数和top_p只设置1个
     * （4）建议top_p和temperature不要同时更改
     */
    private double temperature = 1.0;

}

