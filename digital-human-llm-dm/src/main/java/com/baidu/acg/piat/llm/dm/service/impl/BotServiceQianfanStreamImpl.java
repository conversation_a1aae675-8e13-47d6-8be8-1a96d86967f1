// Copyright (C) 2019 Baidu Inc. All rights reserved.
package com.baidu.acg.piat.llm.dm.service.impl;

import com.baidu.acg.piat.digitalhuman.common.exception.DigitalHumanCommonException;
import com.baidu.acg.piat.digitalhuman.common.llmrole.LLMConfig;
import com.baidu.acg.piat.digitalhuman.common.project.BotParams;
import com.baidu.acg.piat.digitalhuman.common.utils.DateUtil;
import com.baidu.acg.piat.digitalhuman.common.utils.JsonUtil;
import com.baidu.acg.piat.llm.dm.client.HttpCommonClient;
import com.baidu.acg.piat.llm.dm.error.BizError;
import com.baidu.acg.piat.llm.dm.error.LLMAdaptorException;
import com.baidu.acg.piat.llm.dm.model.PendingTask;
import com.baidu.acg.piat.llm.dm.model.Query;
import com.baidu.acg.piat.llm.dm.model.SessionContext;
import com.baidu.acg.piat.llm.dm.model.faq.FaqSaveRequest;
import com.baidu.acg.piat.llm.dm.model.qianfan.ConversationContext;
import com.baidu.acg.piat.llm.dm.model.qianfan.History;
import com.baidu.acg.piat.llm.dm.model.qianfan.LLMEngineResponse;
import com.baidu.acg.piat.llm.dm.model.qianfan.Message;
import com.baidu.acg.piat.llm.dm.model.qianfan.QianfanChatRequest;
import com.baidu.acg.piat.llm.dm.model.qianfan.QianfanChatResponse;
import com.baidu.acg.piat.llm.dm.model.qianfan.QianfanErrorResponse;
import com.baidu.acg.piat.llm.dm.model.qianfan.QianfanAuthResponse;
import com.baidu.acg.piat.llm.dm.model.qianfan.QianfanPluginsLLMConfig;
import com.baidu.acg.piat.llm.dm.service.BotProviderService;
import com.baidu.acg.piat.llm.dm.service.LLMConversationSessionContext;
import com.fasterxml.jackson.core.JsonProcessingException;

import io.vavr.control.Try;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.lang3.StringUtils;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.codec.ServerSentEvent;
import org.springframework.util.CollectionUtils;
import org.springframework.web.reactive.function.BodyExtractors;
import org.springframework.web.reactive.function.BodyInserters;
import org.springframework.web.reactive.function.client.ClientResponse;
import org.springframework.web.reactive.function.client.WebClient;

import reactor.core.Disposable;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.io.IOException;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.time.Duration;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@RequiredArgsConstructor
@Slf4j
public class BotServiceQianfanStreamImpl implements BotProviderService {

    private WebClient webClient;

    public static final String BOT_TYPE = BotParams.BotTypeEnum.QIAN_FAN.getBotType();
    ;

    public static final String PROMPT = "prompt";

    public static final String PLUGINS = "plugins";

    public static final String QIANFAN_TOKEN_PREFIX = "Bearer ";

    private LLMConversationSessionContext llmConversationSessionContext;

    private HttpCommonClient httpCommonClient;

    public BotServiceQianfanStreamImpl(LLMConversationSessionContext llmConversationSessionContext,
                                       WebClient webClient, HttpCommonClient httpCommonClient) {
        this.llmConversationSessionContext = llmConversationSessionContext;
        this.webClient = webClient;
        this.httpCommonClient = httpCommonClient;
    }

    public Flux<String> chat(Query query, LLMConfig llmConfig) {
        SessionContext sessionContext = new SessionContext();
        sessionContext.setSessionId(query.getSessionId());
        sessionContext.setDialogId(query.getSessionId());
        Result historyResult = getHistory(sessionContext);
        // 构建千帆请求
        QianfanChatRequest qianfanChatRequest = null;
        try {
            qianfanChatRequest = buildQianfanRequest(sessionContext, query, llmConfig, historyResult);
            addHistory(sessionContext, query.getText(), true);
            log.info("Request Qianfan body:{}, token={}", JsonUtil.writeValueAsString(qianfanChatRequest),
                    llmConfig.getCredential().get("token"));
        } catch (JsonProcessingException e) {
            log.warn("Failed to serialize qianfan request", e);
            throw new DigitalHumanCommonException(e.getMessage());
        }

        // 发送请求
        Flux<String> responseFlux = webClient.post().uri(uri -> uri.path(selectEngineUri(llmConfig))
                        .queryParam("access_token", fetchToken(llmConfig)).build()) // 你的端点
                .contentType(MediaType.APPLICATION_JSON).acceptCharset(StandardCharsets.UTF_8)
                .body(BodyInserters.fromValue(qianfanChatRequest)).exchangeToFlux(response -> {
                    if (!response.statusCode().is2xxSuccessful()) {
                        log.error("qianfan response code is {}", response.rawStatusCode());
                        return response.createException().flatMapMany(Flux::error);
                    }
                    // 不同的content-type需要走不同的处理逻辑
                    Optional<MediaType> mediaType = response.headers().contentType();
                    if (mediaType.isEmpty()) {
                        return Flux.error(() -> new LLMAdaptorException(BizError.LLM_ENGINE_ERROR));
                    }
                    if (MediaType.TEXT_EVENT_STREAM.isCompatibleWith(mediaType.get())) {
                        return handleSse(response);
                    } else {
                        return handleJson(response);
                    }
                }).map(x -> {
                    try {
                        return JsonUtil.writeValueAsString(x);
                    } catch (JsonProcessingException e) {
                        log.error("Json parse error", e);
                        throw new DigitalHumanCommonException(e.getMessage());
                    }
                });
        return responseFlux;
    }

    /**
     * 只用于http接口测试
     */
    @SneakyThrows
    public Flux<LLMEngineResponse> queryQianfanWithReturn(SessionContext sessionContext,
                                                          Query query, LLMConfig llmConfig) {
        // 构建历史记录
        Result result = getHistory(sessionContext);
        // 构建千帆请求
        QianfanChatRequest qianfanChatRequest = buildQianfanRequest(sessionContext, query, llmConfig, result);
        // 发送请求
        try {
            Flux<LLMEngineResponse> responseFlux = webClient.post().uri(uri -> uri.path(selectEngineUri(llmConfig))
                            .queryParam("access_token", fetchToken(llmConfig)).build()) // 你的端点
                    .contentType(MediaType.APPLICATION_JSON).acceptCharset(StandardCharsets.UTF_8)
                    .body(BodyInserters.fromValue(qianfanChatRequest)).exchangeToFlux(response -> {
                        if (!response.statusCode().is2xxSuccessful()) {
                            log.error("qianfan response code is {}", response.rawStatusCode());
                            return response.createException().flatMapMany(Flux::error);
                        }
                        // 不同的content-type需要走不同的处理逻辑
                        Optional<MediaType> mediaType = response.headers().contentType();
                        if (mediaType.isEmpty()) {
                            return Flux.error(() -> new LLMAdaptorException(BizError.LLM_ENGINE_ERROR));
                        }
                        if (MediaType.TEXT_EVENT_STREAM.isCompatibleWith(mediaType.get())) {
                            return handleSse(response);
                        } else {
                            return handleJson(response);
                        }
                    }).map(this::convertResponse);
            handleResponse(responseFlux, sessionContext, query, result.context);
            responseFlux.onErrorResume(e -> {
                log.error("Qianfan error 2:{}", e.getMessage());
                return Flux.empty();
            }).subscribe(response -> log.info("Qianfan Response:{}", response));
            return responseFlux;
        } catch (Exception e) {
            log.error("Qianfan error 1:{}", e);
            return Flux.empty();
        }
    }

    @SneakyThrows
    @Override
    public void query(SessionContext sessionContext, Query query) {
        LLMConfig llmConfig = sessionContext.getLlmConfig();
        Result historyResult = getHistory(sessionContext);
        // 构建千帆请求
        QianfanChatRequest qianfanChatRequest = buildQianfanRequest(sessionContext, query, llmConfig, historyResult);
        addHistory(sessionContext, query.getText(), true);

        log.info("Request Qianfan body:{}, token={}", JsonUtil.writeValueAsString(qianfanChatRequest),
                llmConfig.getCredential().get("token"));

        // 发送请求
        Flux<LLMEngineResponse> responseFlux = webClient.post().uri(uri -> uri.path(selectEngineUri(llmConfig))
                        .queryParam("access_token", fetchToken(llmConfig)).build()) // 你的端点
                .contentType(MediaType.APPLICATION_JSON).acceptCharset(StandardCharsets.UTF_8)
                .body(BodyInserters.fromValue(qianfanChatRequest)).exchangeToFlux(response -> {
                    if (!response.statusCode().is2xxSuccessful()) {
                        log.error("qianfan response code is {}", response.rawStatusCode());
                        return response.createException().flatMapMany(Flux::error);
                    }
                    // 不同的content-type需要走不同的处理逻辑
                    Optional<MediaType> mediaType = response.headers().contentType();
                    if (mediaType.isEmpty()) {
                        return Flux.error(() -> new LLMAdaptorException(BizError.LLM_ENGINE_ERROR));
                    }
                    if (MediaType.TEXT_EVENT_STREAM.isCompatibleWith(mediaType.get())) {
                        return handleSse(response);
                    } else {
                        return handleJson(response);
                    }
                }).map(this::convertResponse);
        handleResponse(responseFlux, sessionContext, query, historyResult.context);
    }

    /**
     * 构建千帆请求
     */
    private QianfanChatRequest buildQianfanRequest(SessionContext sessionContext, Query query, LLMConfig llmConfig,
                                                   Result result) throws JsonProcessingException {
        QianfanChatRequest qianfanChatRequest = new QianfanChatRequest();
        qianfanChatRequest.setLlm(new QianfanPluginsLLMConfig());
        if (llmConfig.getParams() != null) {
            if (llmConfig.getParams().containsKey("penalty_score")) {
                Double penalty = Double.valueOf((String) llmConfig.getParams().get("penalty_score"));
                qianfanChatRequest.setPenaltyScore(penalty);
                qianfanChatRequest.getLlm().setPenaltyScore(penalty);
            }
            if (llmConfig.getParams().containsKey("top_p")) {
                Double topP = Double.valueOf((String) llmConfig.getParams().get("top_p"));
                qianfanChatRequest.setTopP(topP);
                qianfanChatRequest.getLlm().setTopP(topP);
            }
            if (llmConfig.getParams().containsKey("temperature")) {
                Double temperature = Double.valueOf((String) llmConfig.getParams().get("temperature"));
                qianfanChatRequest.setTemperature(temperature);
                qianfanChatRequest.getLlm().setTemperature(temperature);
            }
            if (llmConfig.getParams().containsKey("system")) {
                qianfanChatRequest.setSystem((String) llmConfig.getParams().get("system"));
            }
        }
        qianfanChatRequest.setStream(query.isStream());
        qianfanChatRequest.setUserId(sessionContext.getSessionId());
        List<Message> history = result.histories.stream().flatMap(h -> {
            String answer = h.getAnswer();
            String q = h.getQuery();
            Message userMsg = new Message(Message.RoleEnum.user, q);
            Message botMsg = new Message(Message.RoleEnum.assistant, answer);
            return Stream.of(userMsg, botMsg);
        }).collect(Collectors.toList());
        String prompt = "";
        if (llmConfig.getParams() != null && llmConfig.getParams().containsKey(PROMPT)) {
            prompt = llmConfig.getParams().get(PROMPT).toString();
        }
        List<Message> requestMessages = new ArrayList<>();
        if (!CollectionUtils.isEmpty(history)) {
            requestMessages.addAll(history);
        }
        requestMessages.add(new Message(Message.RoleEnum.user, prompt + query.getText()));
        qianfanChatRequest.setMessages(requestMessages);
        log.debug("qianfan request:{}", JsonUtil.writeValueAsString(qianfanChatRequest));
        // 其他参数
        Map<String, Object> inputParams = new HashMap<>();
        if (llmConfig.getParams() != null) {
            inputParams.putAll(llmConfig.getParams());
        }
        if (query.getContext() != null) {
            inputParams.putAll(query.getContext());
        }
        inputParams.remove("response_mode");
        qianfanChatRequest.setInputs(inputParams);
        qianfanChatRequest.setInputVariables(inputParams);
        qianfanChatRequest.setPlugins(new ArrayList<>());
        if (llmConfig.getParams() != null && llmConfig.getParams().containsKey(PLUGINS)) {
            Try.run(() ->
                    qianfanChatRequest.setPlugins(
                            JsonUtil.toArray(llmConfig.getParams().get(PLUGINS).toString(), String.class))
            ).onFailure(e -> {
                qianfanChatRequest.setPlugins(new ArrayList<>());
                log.warn("Parse plugins err={}", e.getMessage());
            });
        }
        qianfanChatRequest.setHistory(history);
        qianfanChatRequest.setQuery(query.getText());
        log.info("Qianfan request={}, sessionId={}", qianfanChatRequest, sessionContext.getSessionId());
        return qianfanChatRequest;
    }

    public void addHistory(SessionContext sessionContext, String text, boolean isUser) {
        String dialogId = sessionContext.getDialogId();
        if (StringUtils.isEmpty(dialogId)) {
            dialogId = llmConversationSessionContext.generateId();
            sessionContext.setDialogId(dialogId);
        }
        ConversationContext context = llmConversationSessionContext.getContextOrCreate(dialogId);
        // 只保留10轮对话,防止过大
        if (context.getDialog() != null && context.getDialog().size() > 10) {
            context.getDialog().remove(0);
            context.getDialog().remove(1);
        }
        if (isUser) {
            ConversationContext.ChatDetail detail = new ConversationContext.ChatDetail(ConversationContext.Speaker.USER,
                    text);
            context.addRound(detail);
        } else {
            ConversationContext.ChatDetail detail = new ConversationContext.ChatDetail(ConversationContext.Speaker.BOT,
                    text);
            context.addRound(detail);
        }
        log.debug("Add history ={}, sessionId={}, dialogId={}, isUser={}",
                text, sessionContext.getSessionId(), dialogId, isUser);
    }

    private Result getHistory(SessionContext sessionContext) {
        String dialogId = sessionContext.getDialogId();
        if (StringUtils.isEmpty(dialogId)) {
            dialogId = llmConversationSessionContext.generateId();
            sessionContext.setDialogId(dialogId);
        }
        ConversationContext context = llmConversationSessionContext.getContextOrCreate(dialogId);
        List<ConversationContext.ChatDetail> dialog = context.getDialog();
        List<History> histories = new ArrayList<>();
        for (int i = 0; i < dialog.size() - 1; ) {
            History history = new History();
            if (ConversationContext.Speaker.USER.equals(dialog.get(i).getSpeaker())
                    && ConversationContext.Speaker.BOT.equals(dialog.get(i + 1).getSpeaker())) {
                history.setQuery(dialog.get(i).getContent());
                history.setAnswer(dialog.get(i + 1).getContent());
                histories.add(history);
                i = i + 2;
            } else {
                i++;
            }
        }
        log.debug("Get history result={}", histories);
        return new Result(context, histories);
    }

    private static class Result {
        public final ConversationContext context;
        public final List<History> histories;

        public Result(ConversationContext context, List<History> histories) {
            this.context = context;
            this.histories = histories;
        }
    }

    public Flux<QianfanChatResponse> handleSse(ClientResponse response) {
        return response.body(BodyExtractors.toFlux(new ParameterizedTypeReference<ServerSentEvent<String>>() {
                })).filter(sse -> Objects.nonNull(sse.data())).flatMap(serverSentEvent -> {
                    String data = serverSentEvent.data();
                    try {
                        return Flux.just(JsonUtil.readValue(data, QianfanChatResponse.class));
                    } catch (IOException e) {
                        log.error("parse qianfan response error, data={}", data, e);
                        return Flux.error(() -> new LLMAdaptorException(BizError.UNKNOWN_INTERNAL_ERROR));
                    }
                }).doOnNext(qianfanChatResponse -> log.debug("qianfan response:{}", qianfanChatResponse))
                .takeUntil(QianfanChatResponse::getEnd)
                .filter(qianfanChatResponse -> org.springframework.util.StringUtils
                        .hasText(qianfanChatResponse.getResult()));
    }

    private Flux<QianfanChatResponse> handleJson(ClientResponse response) {
        return response.body(BodyExtractors.toFlux(QianfanChatResponse.class));
    }

    /**
     * 获取对应模型的地址
     */
    private String selectEngineUri(LLMConfig llmConfig) {
        if (llmConfig.getUrl().startsWith("http")) {
            try {
                String urlString = llmConfig.getUrl();
                // 将字符串转换为URL对象
                URL url = new URL(urlString);
                // 将URL对象转换为URI对象
                return url.getPath();
            } catch (Exception e) {
                return llmConfig.getUrl();
            }
        }
        return llmConfig.getUrl();
    }

    public LLMEngineResponse convertResponse(QianfanChatResponse response) {
        LLMEngineResponse llmEngineResponse = new LLMEngineResponse();
        llmEngineResponse.setMessageId(response.getId());
        llmEngineResponse.setResult(response.getResult());
        return llmEngineResponse;
    }

    /**
     * 从配置中获取令牌，如果存在令牌则返回，否则从请求令牌接口获取并缓存到内存
     *
     * @param llmConfig LLM配置对象
     * @return 令牌字符串
     */
    public String fetchToken(LLMConfig llmConfig) {
        if (llmConfig.getCredential().containsKey("token")) {
            return llmConfig.getCredential().get("token");
        } else {
            return requestToken(llmConfig).map(QianfanAuthResponse::getAccessToken).onErrorStop()
                    .cache(Duration.ofMinutes(60L)).block();
        }
    }

    /**
     * 请求token
     *
     * @return Mono<QianfanAuthResponse> 返回token响应
     */
    private Mono<QianfanAuthResponse> requestToken(LLMConfig llmConfig) {
        return webClient.get().uri(
                        uriBuilder -> uriBuilder.path(llmConfig.getCredential()
                                        .getOrDefault("auth_url", "oauth/2.0/token"))
                                .queryParam("grant_type",
                                        "client_credentials")
                                .queryParam("client_id", llmConfig.getCredential()
                                        .getOrDefault("ak", "sU43qT5j0CGPyFjHafENvEPq"))
                                .queryParam("client_secret", llmConfig.getCredential()
                                        .getOrDefault("sk", "xQh8qEdOjOaQMdQ0yKhrbhaBX7D6X7It"))
                                .build())
                .acceptCharset(StandardCharsets.UTF_8).accept(MediaType.APPLICATION_JSON)
                // 查看是否http响应成功
                .exchangeToMono(response -> {
                    if (response.statusCode().is2xxSuccessful()) {
                        return response.bodyToMono(QianfanAuthResponse.class);
                    } else {
                        return response.createException().flatMap(Mono::error);
                    }
                }).flatMap(qianfanAuthResponse -> {
                    if (!org.springframework.util.StringUtils.hasText(qianfanAuthResponse.getError())) {
                        return Mono.just(qianfanAuthResponse);
                    } else {
                        // 返回有errorCode
                        log.error("request for qianfan auth error, code={}, errMsg={}", qianfanAuthResponse.getError(),
                                qianfanAuthResponse.getErrorDescription());
                        return Mono.error(() -> new LLMAdaptorException(BizError.LLM_ENGINE_AUTH_ERROR));
                    }
                });
    }

    @Override
    public void reset(SessionContext sessionContext) {
        sessionContext.setCopilotConversationId(StringUtils.EMPTY);
    }

    @Override
    public void clear(SessionContext sessionContext) {
        if (sessionContext != null && !StringUtils.isEmpty(sessionContext.getDialogId())) {
            llmConversationSessionContext.clearContext(sessionContext.getDialogId());
        }
    }

    /**
     * 处理响应
     *
     * @param responseFlux   响应流
     * @param sessionContext 会话上下文
     * @param query          查询
     * @param context        对话上下文
     */
    private void handleResponse(Flux<LLMEngineResponse> responseFlux, SessionContext sessionContext, Query query,
                                ConversationContext context) {
        StringBuffer resultText = new StringBuffer();
        Disposable disposable = responseFlux.timeout(Duration.ofSeconds(sessionContext.getLlmConfig().getTimeout()))
                .doOnCancel(() -> {
                    log.debug("Cancelled, query={}", query);
                    sessionContext.getLlmStreamObserver().onCancel(query);
                }).doOnError(e -> {
                    // 打印错误
                    log.error("Handle response sessionId={}, requestId={}, Error", sessionContext.getSessionId(),
                            query.getRequestId(), e);
                    sessionContext.getLlmStreamObserver().onError(e, query);

                }).doOnComplete(() -> {
                    sessionContext.getLlmStreamObserver().onCompleted(query, context);
                    if (resultText.length() > 0) {
                        if (sessionContext.getLlmConfig().getPostActions() != null) {
                            sessionContext.getLlmConfig().getPostActions().forEach(toolConfig -> {
                                if (toolConfig.getToolType() != null) {
                                    switch (toolConfig.getToolType()) {
                                        case FAQ_INSERT:
                                            FaqSaveRequest faqSaveRequest = new FaqSaveRequest();
                                            faqSaveRequest.setAppId(toolConfig.getParams().get("appId").toString());
                                            faqSaveRequest.setCreateTime(DateUtil.fmt(new Date()));
                                            faqSaveRequest.setQuestions(List.of(query.getText()));
                                            faqSaveRequest.setAnswers(List.of(FaqSaveRequest.Answer.builder()
                                                    .type("TEXT").speak(List.of(FaqSaveRequest.Speak.builder()
                                                            .answer(resultText.toString()).build())).build()));
                                            httpCommonClient.insertFaq(toolConfig.getUrl(), faqSaveRequest);
                                            break;
                                        default:
                                            break;
                                    }
                                }
                            });
                        }
                        addHistory(sessionContext, resultText.toString(), false);
                    }
                    // 打印结束
                    log.info("Handle response sessionId={}, requestId={} Complete", sessionContext.getSessionId(),
                            query.getRequestId());
                }).subscribe(response -> {
                    // 打印每个接收到的chunk
                    log.info("Received chunk: {}", response);
                    if (response != null && response.getResult() != null
                            && !StringUtils.isEmpty(response.getResult())) {
                        resultText.append(response.getResult());
                        sessionContext.getLlmStreamObserver().onNext(response.getResult(), "largeModel", query);
                    }
                });
        sessionContext.getPendingQueryMap().put(query.getRequestId(), PendingTask.builder()
                .queryIndex(query.getQueryIndex()).disposable(disposable).build());
    }

    public void interrupt(SessionContext sessionContext, String taskId) {
        if (sessionContext.getPendingQueryMap().isEmpty()) {
            return;
        }
        // 批量删除
        if (StringUtils.isEmpty(taskId)) {
            log.info("Interrupt all={}", sessionContext.getPendingQueryMap().keySet());
            sessionContext.getPendingQueryMap().forEach((k, v) -> {
                v.getDisposable().dispose();
            });
            int i = 0;
            // 判断是否真的中断, 一次性给所有的pending发中断指令
            while (sessionContext.getPendingQueryMap().keySet().stream().anyMatch(k -> {
                return !sessionContext.getPendingQueryMap().get(k).getDisposable().isDisposed();
            }) && i < 3) {
                try {
                    Thread.sleep(100);
                } catch (InterruptedException e) {
                    log.error("Interrupt error", e);
                }
                i++;
            }
            sessionContext.getPendingQueryMap().keySet().forEach(k -> {
                clear(k, sessionContext);
            });
            return;
        }
        PendingTask pendingTask = sessionContext.getPendingQueryMap().get(taskId);
        if (pendingTask != null) {
            log.info("Interrupt task id={}", taskId);
            // 会有延迟不一定立马中断
            pendingTask.getDisposable().dispose();
            int i = 0;
            // 判断是否真的中断
            while (!pendingTask.getDisposable().isDisposed() && i < 3) {
                try {
                    Thread.sleep(100);
                } catch (InterruptedException e) {
                    log.error("Interrupt error", e);
                }
                i++;
            }
            clear(taskId, sessionContext);
        }
    }

    public void clear(String taskId, SessionContext sessionContext) {
        log.debug("Clear task id={}", taskId);
        if (StringUtils.isEmpty(taskId)) {
            return;
        }
        sessionContext.getPendingQueryMap().remove(taskId);
        sessionContext.getPendingResponseSeqMap().remove(taskId);
        sessionContext.getPendingResponseMap().remove(taskId);
    }

    private HttpHeaders getHeaders(LLMConfig llmConfig) {
        HttpHeaders headers = new HttpHeaders();
        headers.add("Authorization", QIANFAN_TOKEN_PREFIX
                + llmConfig.getCredential().get("token"));
        headers.add("Content-Type", "application/json;charset=UTF-8");
        return headers;
    }

    @Override
    public String getBotType() {
        return BOT_TYPE;
    }
}
