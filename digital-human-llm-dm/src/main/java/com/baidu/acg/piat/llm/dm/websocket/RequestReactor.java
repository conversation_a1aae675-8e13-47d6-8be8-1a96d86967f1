package com.baidu.acg.piat.llm.dm.websocket;

import com.baidu.acg.piat.digitalhuman.common.alita.ActionType;
import com.baidu.acg.piat.digitalhuman.common.llmdm.WebsocketLlmRequest;
import com.baidu.acg.piat.llm.dm.model.SessionContext;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;

public interface RequestReactor {

    ObjectMapper objectMapper = new ObjectMapper()
        .enable(JsonParser.Feature.IGNORE_UNDEFINED)
        .configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)
        .setPropertyNamingStrategy(PropertyNamingStrategy.LOWER_CAMEL_CASE);
    void react(SessionContext sessionContext, WebsocketLlmRequest request);

    boolean support(ActionType actionType);
}
