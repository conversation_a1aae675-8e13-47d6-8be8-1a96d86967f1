// Copyright (C) 2019 Baidu Inc. All rights reserved.
package com.baidu.acg.piat.llm.dm.configure;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import com.baidu.acg.dh.user.client.AccountQuotaClient;

import lombok.Data;
import lombok.RequiredArgsConstructor;

@Configuration
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class DhUserConfigure {

    @Bean
    @ConfigurationProperties(prefix = "digitalhuman.dh-user.config")
    public DhConfig dhUserConfig() {
        return new DhConfig();
    }

    @Bean
    public AccountQuotaClient accountQuotaClient(DhConfig config) {
        return new AccountQuotaClient(config.getBaseUrl());
    }

    @Data
    public static class DhConfig {
        private String baseUrl = "http://dh-user:80";
    }


}
