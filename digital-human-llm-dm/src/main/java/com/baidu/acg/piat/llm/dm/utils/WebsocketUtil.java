package com.baidu.acg.piat.llm.dm.utils;

import java.io.IOException;

import org.springframework.stereotype.Service;
import org.springframework.web.socket.TextMessage;
import org.springframework.web.socket.WebSocketSession;

import com.baidu.acg.piat.digitalhuman.common.llmdm.WebsocketLlmResponse;
import com.baidu.acg.piat.digitalhuman.common.utils.JsonUtil;

import lombok.extern.slf4j.Slf4j;

/**
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class WebsocketUtil {

    public static void send(WebSocketSession session, WebsocketLlmResponse websocketResponse) {
        if (session.isOpen()) {
            log.debug("Send websocket message={} to {}", websocketResponse, session.getId());
        } else {
            log.warn("Websocket {} is closed, message={}", session.getId(), websocketResponse);
        }
        try {
            if (session.isOpen()) {
                session.sendMessage(new TextMessage(JsonUtil.writeValueAsString(websocketResponse)));
            }
        } catch (IOException e) {
            log.error("Unexpected write response websocket as json failed, response ={}, " +
                    "websocket={}", websocketResponse, session.getId());
        }
    }
}
