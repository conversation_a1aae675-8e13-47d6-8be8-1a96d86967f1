// Copyright (C) 2019 Baidu Inc. All rights reserved.
package com.baidu.acg.piat.llm.dm.model.db;

import java.io.IOException;
import java.util.Date;

import javax.persistence.Access;
import javax.persistence.AccessType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Transient;

import org.apache.commons.lang3.StringUtils;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;
import org.hibernate.annotations.GenericGenerator;

import com.baidu.acg.piat.digitalhuman.common.llmrole.BufferSentencesConfigJson;
import com.baidu.acg.piat.digitalhuman.common.llmrole.LLMConfig;
import com.baidu.acg.piat.llm.dm.model.text2gesture.AnimojisTagConfigJson;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;

@Data
@Slf4j
@Builder
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
@DynamicInsert
@DynamicUpdate
@Entity(name = "llm_dm_config")
public class LlmDmConfigModel {

    private static final ObjectMapper MAPPER = new ObjectMapper();

    @Id
    @GeneratedValue(strategy = GenerationType.AUTO, generator = "myid")
    @GenericGenerator(name = "myid",
            strategy = "com.baidu.acg.piat.llm.dm.utils.ManulInsertGenerator")
    private Long id;

    private String botId;

    private String name;

    private String token;


    @Transient
    private AnimojisTagConfigJson animojisTagConfigJson;

    @Transient
    private LLMConfig llmConfigJson;

    @Transient
    private BufferSentencesConfigJson bufferSentencesConfigJson;

    private boolean del;

    private Date createTime;

    private Date updateTime;


    @Column(name = "llm_config_json")
    @Access(AccessType.PROPERTY)
    public String getLlmConfigJsonString() {
        if (llmConfigJson != null) {
            try {
                return MAPPER.writeValueAsString(llmConfigJson);
            } catch (JsonProcessingException e) {
                log.error("Parse tags to json failed.", e);
            }
        }
        return StringUtils.EMPTY;
    }

    public void setLlmConfigJsonString(String llmConfigJson) {
        if (StringUtils.isNotEmpty(llmConfigJson)) {
            try {
                this.llmConfigJson = MAPPER.readValue(llmConfigJson, LLMConfig.class);
            } catch (IOException e) {
                log.error("Exception when parse tags from json string, string={}.", llmConfigJson, e);
            }
        } else {
            this.llmConfigJson = null;
        }
    }

    @Column(name = "animojis_tags_config_json")
    @Access(AccessType.PROPERTY)
    public String getAnimojisConfigJsonString() {
        if (animojisTagConfigJson != null) {
            try {
                return MAPPER.writeValueAsString(animojisTagConfigJson);
            } catch (JsonProcessingException e) {
                log.error("Parse resource quota to json failed.", e);
            }
        }
        return StringUtils.EMPTY;
    }

    public void setAnimojisConfigJsonString(String str) {
        if (StringUtils.isNotEmpty(str)) {
            try {
                this.animojisTagConfigJson = MAPPER.readValue(str, AnimojisTagConfigJson.class);
            } catch (IOException e) {
                log.error("Exception when parse animojis config json str from json string, string={}.", str,
                        e);
            }
        } else {
            this.animojisTagConfigJson = null;
        }
    }

    @Column(name = "buffer_sentences_config_json")
    @Access(AccessType.PROPERTY)
    public String getBsConfigJsonString() {
        if (bufferSentencesConfigJson != null) {
            try {
                return MAPPER.writeValueAsString(bufferSentencesConfigJson);
            } catch (JsonProcessingException e) {
                log.error("Parse resource quota to json failed.", e);
            }
        }
        return StringUtils.EMPTY;
    }

    public void setBsConfigJsonString(String str) {
        if (StringUtils.isNotEmpty(str)) {
            try {
                this.bufferSentencesConfigJson = MAPPER.readValue(str, BufferSentencesConfigJson.class);
            } catch (IOException e) {
                log.error("Exception when parse buffer config json str from json string, string={}.", str,
                        e);
            }
        } else {
            this.bufferSentencesConfigJson = null;
        }
    }

}
