package com.baidu.acg.piat.llm.dm.utils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import com.baidu.acg.piat.digitalhuman.common.llmrole.LLMConfig;
import com.baidu.acg.piat.llm.dm.model.drml.ClientWidget;
import com.fasterxml.jackson.dataformat.xml.XmlMapper;
import org.springframework.util.StringUtils;

import com.baidu.acg.piat.llm.dm.model.text2gesture.Text2GestureResponse;

import lombok.extern.slf4j.Slf4j;

import static com.fasterxml.jackson.databind.SerializationFeature.WRAP_ROOT_VALUE;

@Slf4j
public class DrmlUtil {

    private static final String VAR_PATTERN = "(?<=(\\{%)).*?(?=(\\%}))";

    public static List<String> getRandomElements(List<String> list, int n) {
        if (list.size() < n) {
            throw new IllegalArgumentException("List size is smaller than the number of elements requested.");
        }
        // 打乱列表顺序
        List<String> shuffledList = new ArrayList<>(list);
        Collections.shuffle(shuffledList);

        // 返回前 n 个元素
        return shuffledList.subList(0, n);
    }

    public static String buildClientWidget(LLMConfig.RecommendConfig recommendConfig) {
        ClientWidget clientWidget = new ClientWidget();
        ClientWidget.Widget widget = new ClientWidget.Widget();
        clientWidget.setWidget(widget);
        widget.setType("openingBubble");
        ClientWidget.Data data = new ClientWidget.Data();
        widget.setData(data);
        data.setGuide(recommendConfig.getGuide());
        List<String> recs = DrmlUtil.getRandomElements(recommendConfig.getSentences(),
                recommendConfig.getNum());
        List<ClientWidget.RecommendedQuestion> questions = new ArrayList<>();
        log.info("recs: {}", recs);
        recs.forEach(rec -> {
            ClientWidget.RecommendedQuestion question = new ClientWidget.RecommendedQuestion();
            question.setText(rec);
            questions.add(question);
        });
        data.setRecommendedQuestions(questions);
        log.info("ClientWidget: {}", clientWidget);
        // 创建 XmlMapper 来处理 XML
        XmlMapper xmlMapper = new XmlMapper();

        try {
            xmlMapper.configure(WRAP_ROOT_VALUE, false);
            // 将 JsonNode 转换为 XML 字符串
            return xmlMapper.writeValueAsString(clientWidget);
        } catch (Exception e) {
            log.error("Failed to convert ClientWidget to XML: {}", e.getMessage());
            return "";
        }

    }

    public static String buildDrmlFromTextWithAnimojisAndEmotions(String text, String split,
                                                                  Map<String, List<String>> animojisMap,
                                                                  Map<String, List<String>> emotionsMap) {

        if (StringUtils.isEmpty(text)) {
            return null;
        }
        StringBuffer result = new StringBuffer();
        String[] tokens = text.split("[" + split + "]");
        StringBuffer lastToken = new StringBuffer();
        for (int i = 0; i < tokens.length; i++) {
            String token = tokens[i];
            Pattern pattern = Pattern.compile("\\[(.*?)\\]");
            Matcher matcher = pattern.matcher(token);
            int animojisIndex = -1;
            String animoji = "";
            while (matcher.find()) {
                String extracted = matcher.group(1);
                if (animojisMap.containsKey(extracted)) {
                    String tmp = "[" + extracted + "]";
                    animojisIndex = token.indexOf(tmp);
                    if (animojisIndex > -1) {
                        animoji = tmp;
                        break;
                    }
                }
            }
            pattern = Pattern.compile("\\<(.*?)\\>");
            matcher = pattern.matcher(token);
            int emotionIndex = -1;
            String emotion = "";
            while (matcher.find()) {
                String extracted = matcher.group(1);
                if (emotionsMap.containsKey(extracted)) {
                    String tmp = "<" + extracted + ">";
                    emotionIndex = token.indexOf(tmp);
                    if (emotionIndex > -1) {
                        emotion = tmp;
                        break;
                    }
                }
            }

            if (animojisIndex > -1) {
                token = token.replace(animoji, "");
            }

            if (emotionIndex > -1) {
                token = token.replace(emotion, "");
            }

            // 过滤特殊字符串
            token = token.replace(" ", "");
            token = token.replace("\n", "");
            token = token.replace("<", "");
            token = token.replace(">", "");
            StringBuffer sb = new StringBuffer();
            if (!StringUtils.isEmpty(token)) {

                String tmp = String.format("<speak><interruptible>false</interruptible><content>%s</content"
                        + "></speak>", token);
                sb.append(tmp);
            }

            if (!StringUtils.isEmpty(token) && animojisIndex >= 0) {
                String tmp = String.format("<animoji><id>%s</id></animoji>", animojisMap.get(animoji.substring(1,
                        animoji.length() - 1)).get(0));
                sb.append(tmp);
            }

            if (!StringUtils.isEmpty(token) && emotionIndex > 0) {
                String tmp = String.format("<a2a_emotion duration=\"30\"><id>%s</id></a2a_emotion>",
                        emotionsMap.get(emotion.substring(1, emotion.length() - 1)).get(0));
                sb.append(tmp);
            }

            if (sb.length() > 0) {
                sb.insert(0, "<fusion>");
                sb.append("</fusion>");
                result.append(sb);
            }
        }
        result.insert(0, "<speak>");
        result.append("</speak>");
        return result.toString();
    }



    public static String buildDrmlFromText2Gesture(String text, Text2GestureResponse response,
                                                   Map<String, List<String>> animojisMap,
                                                   Map<String, List<String>> emotionsMap, boolean random,
                                                   boolean interruptible) {
        // 6个字延迟1s
        int words = 6;
        // 相距多少个字忽略以内
        int ingoreWords = 20;
        if (response == null) {
            return text;
        }
        StringBuffer result = new StringBuffer();
        if (StringUtils.isEmpty(text)) {
            return null;
        }

        String tmp = String.format("<speak><interruptible>%s</interruptible><content>%s</content"
                + "></speak>", interruptible, text);
        result.append(tmp);

        int lastEmotionIndex = -1;
        int emotionCount = 0;
        if (response.getEmotion() != null && emotionsMap != null) {
            for (int i = 0; i < response.getEmotion().size(); i++) {
                String emotionTag = response.getEmotion().get(i);
                int emotionIndex = response.getWordIndexEmotion().get(i);
                // 6个字delay 1s;
                int delay = emotionIndex / words * 1000;
                if (emotionsMap.containsKey(emotionTag) && (emotionIndex - lastEmotionIndex > ingoreWords
                                                                    || lastEmotionIndex == -1)) {
                    if (lastEmotionIndex == emotionIndex) {
                        continue;
                    }
                    if (lastEmotionIndex != -1) {
                        // 动作4s
                        int diffSeconds = (emotionIndex - lastEmotionIndex) / words;
                        if (diffSeconds - 4 <= 0) {
                            delay = 0;
                        } else {
                            delay = (diffSeconds - 4) * 1000;
                        }
                    }
                    lastEmotionIndex = emotionIndex;
                    List<String> emotionList = emotionsMap.get(emotionTag);
                    int index = random ? (int) (Math.random() * emotionList.size()) : 0;
                    tmp = String.format("<a2a_emotion delay=\"%d\" duration=\"30\"><id>%s</id></a2a_emotion>",
                            delay, emotionList.get(index));
                    emotionCount++;
                    result.append(tmp);
                }
            }
        }

        int lastActionIndex = -1;
        int actionCount = 0;
        if (response.getAction() != null && animojisMap != null) {
            for (int i = 0; i < response.getAction().size(); i++) {
                String actionTag = response.getAction().get(i);
                int actionIndex = response.getWordIndexAction().get(i);
                // 6个字delay 1s;
                int delay = actionIndex / words * 1000;
                if (animojisMap.containsKey(actionTag) && (actionIndex - lastActionIndex > ingoreWords
                                                                   || lastActionIndex == -1)) {
                    // 防止两个位置插入一个动作
                    if (lastActionIndex == actionIndex) {
                        continue;
                    }
                    if (lastActionIndex != -1) {
                        // 动作4s
                        int diffSeconds = (actionIndex - lastActionIndex) / words;
                        if (diffSeconds - 4 <= 0) {
                            delay = 0;
                        } else {
                            delay = (diffSeconds - 4) * 1000;
                        }
                    }
                    lastActionIndex = actionIndex;

                    actionCount++;
                    List<String> animojiList = animojisMap.get(actionTag);
                    int index = random ? (int) (Math.random() * animojiList.size()) : 0;
                    tmp = String.format("<animoji delay=\"%d\"><id>%s</id></animoji>",
                            delay, animojiList.get(index));
                    result.append(tmp);
                }
            }
        }

        if (result.length() > 0) {
            result.insert(0, "<fusion>");
            result.append("</fusion>");
        }
        if (!interruptible) {
            result.insert(0, "<speak interruptible=\"false\">");
        } else {
            result.insert(0, "<speak interruptible=\"true\">");
        }
        result.append("</speak>");

        String drml = result.toString();

        if (actionCount >= 2) {
            drml = drml.replaceAll("<animoji", "<animojis");
            drml = drml.replaceAll("</animoji>", "</animojis>");
        }

        if (emotionCount >= 2) {
            drml = drml.replaceAll("<a2a_emotion", "<a2a_emotions");
            drml = drml.replaceAll("</a2a_emotion>", "</a2a_emotions>");
        }

        return drml;
    }
}