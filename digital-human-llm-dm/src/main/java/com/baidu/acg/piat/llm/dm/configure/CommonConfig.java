package com.baidu.acg.piat.llm.dm.configure;

import java.util.Set;
import java.util.stream.Collectors;

import javax.annotation.PostConstruct;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;


import lombok.Data;
import lombok.extern.slf4j.Slf4j;

@Configuration
@Data
@Slf4j
public class CommonConfig {

    @Value("${digitalhuman.llmdm.text2gesture.url:http://acg-text2gesture:8080/api/text2gesture}")
    private String text2GestureUrl;

    @Value("${digitalhuman.llmdm.copilotResultParse.url:http://acg-text2gesture:8080/api/text2gesture}")
    private String copilotResultParseUrl;

    @Value("${digitalhuman.llmdm.unit.url:https://api-ngd.baidu.com/core/v4/stream/query}")
    private String unitUrl;

    @Value("${digitalhuman.llmdm.keyue.url:https://keyue.cloud.baidu.com/online/core/v5/stream/query}")
    private String keYueUrl;

    @Value("${digitalhuman.llmdm.aida.url:https://dh-aida-dm/api/digitalhuman/chatbot/adaptor/v1/query}")
    private String aidaUrl;

    @Value("${digitalhuman.llmdm.publish.url:https://digitalhuman.baidu.com/cloud/react}")
    private String publishUrl;

    @Value("${digitalhuman.llmdm.billing.enabled:true}")
    private boolean billingEnable;

    @Value("${digitalhuman.llmdm.billing.cost:5}")
    private int billingCost;

    @Value("${digitalhuman.llmdm.split.end:. ,， 。 ； ！ ! ? ？}")
    private String llmSplit;

    @Value("${digitalhuman.llmdm.no.answer:<speak interruptible=\"false\">我还不知道怎么回答你呢，可以换个问题试试</speak>}")
    private String llmNoAnswer;

    @Value("${digitalhuman.llmdm.userStatement.template:%s，在对话中，你不需要再次声明你的角色，而是直接以专业的身份回答问题。"
            + "当用户开始对话时，你可以直接回答：“你好，请问有什么我可以帮助你的吗？”对于与用户的每轮对话，"
            + "你都需要严格按照以下标准进行回复：\n"
            + "1.当用户此轮的问题与%s有关, 请按照你的人设进行回复。\n"
            + "2.当用户此轮的问题与%s有关，出于你的立场和原则，你只需明确而礼貌地拒绝回答。请避免提供额外信息或解释，"
            + "保持回复的简洁和专业。\n"
            + ".每轮对话不超过100字。}")
    private String llmUserStatementTemplate;

    @Value("${digitalhuman.llmdm.split.minlen:15}")
    private int minSplitSentenceLen;


    private Set<Character> llmSplitEndSet;

    @PostConstruct
    public void loadConfig() {
        try {
            llmSplitEndSet = llmSplit.chars().mapToObj(c -> (char) c).collect(Collectors.toSet());
        } catch (Exception e) {
            log.error("load err:{}", e);
        }
    }

}