package com.baidu.acg.piat.llm.dm.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;
import org.hibernate.annotations.UpdateTimestamp;

import javax.persistence.Access;
import javax.persistence.AccessType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Transient;
import java.io.IOException;
import java.time.ZonedDateTime;

@Data
@Slf4j
@Builder
@Accessors(chain = true)
@DynamicInsert
@DynamicUpdate
@Entity(name = "llm_role_template")
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class LlmRoleTemplateModel {

    private static final ObjectMapper MAPPER = new ObjectMapper();

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    private String llmRoleTemplateId;

    private String templateName;

    private Integer templateType;

    private String templateIconUrl;

    private String exampleVideoUrl;

    // 横版horizontal/竖版vertical
    private String screenType;

    @Transient
    private CustomizedConfig customizedConfig;

    private String llmRoleId;

    @Builder.Default
    private boolean del = false;

    @CreationTimestamp
    @Column(insertable = false, updatable = false)
    private ZonedDateTime createTime;

    @UpdateTimestamp
    @Column(insertable = false, updatable = false)
    private ZonedDateTime updateTime;

    /**
     * 获取自定义配置的JSON字符串。
     *
     * @return 自定义配置的JSON字符串，如果没有自定义配置则返回空字符串""。
     */
    @Column(name = "customized_config")
    @Access(AccessType.PROPERTY)
    public String getCustomizedConfigString() {
        if (customizedConfig != null) {
            try {
                return MAPPER.writeValueAsString(customizedConfig);
            } catch (JsonProcessingException e) {
                log.error("Parse resource quota to json failed.", e);
            }
        }
        return StringUtils.EMPTY;
    }

    /**
     * 解析自定义配置的json字符串并将结果存储在customizedConfig字段中。
     *
     * @param str 要解析的json字符串。
     */
    public void setCustomizedConfigString(String str) {
        if (StringUtils.isNotEmpty(str)) {
            try {
                this.customizedConfig = MAPPER.readValue(str, CustomizedConfig.class);
            } catch (IOException e) {
                log.error("Exception when parse customized config json str from json string, string={}.", str, e);
            }
        } else {
            this.customizedConfig = null;
        }
    }

    public void copy(LlmRoleTemplateModel updated) {
        if (StringUtils.isNotEmpty(updated.getLlmRoleId())) {
            this.setLlmRoleId(updated.getLlmRoleId());
        }
        if (StringUtils.isNotEmpty(updated.getLlmRoleTemplateId())) {
            this.setLlmRoleTemplateId(updated.getLlmRoleTemplateId());
        }
        if (StringUtils.isNotEmpty(updated.getScreenType())) {
            this.setScreenType(updated.screenType);
        }
        if (StringUtils.isNotEmpty(updated.getTemplateName())) {
            this.setTemplateName(updated.getTemplateName());
        }
        if (StringUtils.isNotEmpty(updated.getScreenType())) {
            this.setScreenType(updated.getScreenType());
        }
        if (StringUtils.isNotEmpty(updated.getTemplateIconUrl())) {
            this.setTemplateIconUrl(updated.getTemplateIconUrl());
        }
        if (StringUtils.isNotEmpty(updated.getCustomizedConfigString())) {
            this.setCustomizedConfigString(updated.getCustomizedConfigString());
        }
        if (updated.getTemplateType() != null) {
            this.setTemplateType(updated.getTemplateType());
        }
        if (updated.getExampleVideoUrl() != null) {
            this.setExampleVideoUrl(updated.getExampleVideoUrl());
        }
    }
}
