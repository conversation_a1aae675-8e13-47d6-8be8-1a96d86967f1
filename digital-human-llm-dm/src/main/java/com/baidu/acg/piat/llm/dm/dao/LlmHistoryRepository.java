package com.baidu.acg.piat.llm.dm.dao;

import java.util.List;
import java.util.Map;

import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.data.repository.query.Param;

import com.baidu.acg.piat.llm.dm.model.LlmHistoryModel;
import com.baidu.acg.piat.llm.dm.model.LlmRoleModel;

/**
 * 历史
 */
public interface LlmHistoryRepository extends PagingAndSortingRepository<LlmHistoryModel, Long> {

    List<LlmRoleModel> findByLlmRoleId(String roleId);


    @Query(value = "select count(uid)  from  llm_history" +
            " where uid=:uid "
            , nativeQuery = true)
    Integer countByUserId(@Param("uid") String uid);



    // TODO 按character_image分组统计最高出现的3个character_image, 垃圾代码，数据量大会很慢，后期如果数据量大，需要优化
    @Query(value = "select character_image as characterImage, count(character_image) as count from llm_history where "
            + "uid=:uid group "
            + "by character_image order by count desc limit 3"
            , nativeQuery = true)
    List<Map<String, Object>> findTop3CharacterImage(@Param("uid") String uid);

}
