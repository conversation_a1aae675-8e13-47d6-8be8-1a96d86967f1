package com.baidu.acg.piat.llm.dm.service;

import com.baidu.acg.piat.llm.dm.model.qianfan.ConversationContext;

import java.util.UUID;

/**
 * 管理会话session
 *
 * <AUTHOR>
 * @date 2023/8/31
 */
public interface LLMConversationSessionContext {

    /**
     * 通过conversationId获取当前会话上下文
     * 如果 conversationId为空，就生成一个
     * 实现的时候请注意线程安全
     *
     * @param conversationId
     * @return
     */
    ConversationContext getContextOrCreate(String conversationId);

    /**
     * 清楚当前会话上下文
     *
     * @param conversationId
     */
    void clearContext(String conversationId);

    /**
     * 生成id
     *
     * @return
     */
    default String generateId() {
        return UUID.randomUUID().toString();
    }

}
