package com.baidu.acg.piat.llm.dm.dao;

import com.baidu.acg.piat.llm.dm.model.KnowledgeBaseModel;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.data.repository.query.Param;

import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR> @since 2023/11/23 14:23
 */
public interface KnowledgeBaseRepository extends PagingAndSortingRepository<KnowledgeBaseModel, Long> {

    Page<KnowledgeBaseModel> findByAccountIdAndTypeAndNameContainingAndIsDeleteOrderByCreateTimeDesc
            (String accountId, Integer type, String name, boolean isDelete, Pageable pageable);

    Optional<KnowledgeBaseModel> findByKnowledgeBaseId(String id);

    @Query(value = "select * from llm_knowledge_base where account_id =:accountId and BINARY name=:name " +
            "and type =:type and is_delete =:isDelete", nativeQuery = true)
    List<KnowledgeBaseModel> findByAccountIdAndNameAndTypeAndIsDelete(@Param("accountId") String accountId, @Param("name") String name,
                                                                      @Param("type") Integer type, @Param("isDelete") boolean isDelete);

    List<KnowledgeBaseModel> findByKnowledgeBaseIdIn(List<String> knowledgeBaseIds);

}
