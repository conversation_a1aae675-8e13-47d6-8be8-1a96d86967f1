package com.baidu.acg.piat.llm.dm.model.stat;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> ke
 */
@Data
@Slf4j
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class CharacterCountRes {

    private String characterId;

    private String characterImageUrl;

    private String performanceStage = "conversation";
}