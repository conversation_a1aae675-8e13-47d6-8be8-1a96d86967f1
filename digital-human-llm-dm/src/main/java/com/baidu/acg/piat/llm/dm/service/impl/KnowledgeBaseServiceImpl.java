package com.baidu.acg.piat.llm.dm.service.impl;

import com.baidu.acg.piat.digitalhuman.common.exception.DigitalHumanCommonException;
import com.baidu.acg.piat.digitalhuman.common.llmrole.CreateKnowledge;
import com.baidu.acg.piat.digitalhuman.common.llmrole.KnowledgeBase;
import com.baidu.acg.piat.digitalhuman.common.llmrole.KnowledgeFile;
import com.baidu.acg.piat.digitalhuman.common.model.PageResponse;
import com.baidu.acg.piat.digitalhuman.common.utils.JsonUtil;
import com.baidu.acg.piat.digitalhuman.storage.service.StorageService;
import com.baidu.acg.piat.llm.dm.client.HttpCommonClient;
import com.baidu.acg.piat.llm.dm.configure.CopilotEsConfigure;
import com.baidu.acg.piat.llm.dm.configure.KnowledgeBaseConfig;
import com.baidu.acg.piat.llm.dm.dao.KnowledgeBaseRepository;
import com.baidu.acg.piat.llm.dm.dao.KnowledgeFileRepository;
import com.baidu.acg.piat.llm.dm.model.KnowledgeBaseModel;
import com.baidu.acg.piat.llm.dm.model.KnowledgeFileModel;
import com.baidu.acg.piat.llm.dm.service.KnowledgeBaseService;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.io.IOException;
import java.net.URL;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Xiaoyu
 * @since 2023/11/23 14:17
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class KnowledgeBaseServiceImpl implements KnowledgeBaseService {

    private final KnowledgeBaseRepository knowledgeBaseRepository;

    private final KnowledgeFileRepository knowledgeFileRepository;

    private final HttpCommonClient httpCommonClient;

    private final KnowledgeBaseConfig knowledgeBaseConfig;

    private final StorageService storageService;

    private String createKnowledgeBaseUrl;

    private String deleteKnowledgeBaseUrl;

    private String listFileUrl;

    private String detailKnowledgeBaseUrl;

    private String uploadFileUrl;

    private String associateFileUrl;

    private String batchAssociateFileUrl;

    private String batchDeleteFileUrl;

    private String batchListFileUrl;

    private String token;

    private String bceId;

    private String[] supportFileTypes;

    private Map<String, Object> packageConfig;

    private final CopilotEsConfigure.Config config;

    @PostConstruct
    public void init() {

        createKnowledgeBaseUrl = knowledgeBaseConfig.getPrefix() + "/create";
        deleteKnowledgeBaseUrl = knowledgeBaseConfig.getPrefix() + "/delete";
        listFileUrl = knowledgeBaseConfig.getPrefix() + "/documents/list";
        detailKnowledgeBaseUrl = knowledgeBaseConfig.getPrefix() + "/detail";
        uploadFileUrl = knowledgeBaseConfig.getPrefix() + "/files/upload";
        associateFileUrl = knowledgeBaseConfig.getPrefix() + "/documents";
        batchAssociateFileUrl = knowledgeBaseConfig.getPrefix() + "/documents_list";
        batchDeleteFileUrl = knowledgeBaseConfig.getPrefix() + "/document/deletes";
        batchListFileUrl = knowledgeBaseConfig.getPrefix() + "/documents/batch/list";
        token = knowledgeBaseConfig.getToken();
        bceId = knowledgeBaseConfig.getBceId();
        supportFileTypes = knowledgeBaseConfig.getSupportFileType().split(",");
        packageConfig = Map.of(
                "target_length", knowledgeBaseConfig.getTargetLength(),
                "overlap", knowledgeBaseConfig.getOverlap(),
                "min_sentence_count", knowledgeBaseConfig.getMinSentenceCount(),
                "xmind_url", knowledgeBaseConfig.getXmindUrl(),
                "separators", knowledgeBaseConfig.getSeparators());
        log.debug("llm platform url is {}, token is {}, support file type is {}, packageConfig is {}",
                knowledgeBaseConfig.getPrefix(), token, supportFileTypes, packageConfig);
    }

    @Override
    public KnowledgeBase createKnowledgeBase(KnowledgeBase request) {

        List<KnowledgeBaseModel> exist = knowledgeBaseRepository.findByAccountIdAndNameAndTypeAndIsDelete(
                request.getAccountId(), request.getName(), request.getType(), false);
        if (exist != null && exist.size() != 0) {
            throw new DigitalHumanCommonException("该知识库已存在");
        }

        CreateKnowledge createKnowledge = new CreateKnowledge();
        createKnowledge.setName(request.getEditor() + "_" + request.getName());
        createKnowledge.setResourceType(config.getResourceType());
        CreateKnowledge.ResourceInfo resourceInfo = new CreateKnowledge.ResourceInfo();
        resourceInfo.setBesAdminName(config.getBesAdminName());
        resourceInfo.setBesEndpoint(config.getBesEndpoint());
        resourceInfo.setBesPassword(config.getBesPassword());
        createKnowledge.setResourceInfo(resourceInfo);
        Map<String, Object> result = httpCommonClient.createDatabase(createKnowledgeBaseUrl, createKnowledge, token, bceId);
        log.debug("Response in llm model platform when create knowleadgeBase is {}", result);
        String id = ((LinkedHashMap<String, String>) result.get("result")).get("id");
        try {
            request.setKnowledgeBaseId(id);
            KnowledgeBaseModel model = new KnowledgeBaseModel();
            model.toKnowledgeBaseModel(request);
            log.debug("Create knowledgeBase is: {}", model);
            knowledgeBaseRepository.save(model);
        } catch (Exception e) {
            httpCommonClient.deleteDatabase(deleteKnowledgeBaseUrl, Map.of("dataset_id", id), token, bceId);
            throw new DigitalHumanCommonException("sql创建知识库失败", e);
        }
        return request;
    }

    @Override
    public void batchDeleteKnowledgeBase(String accountId, List<String> ids) {

        String url = deleteKnowledgeBaseUrl;
        for (String id : ids) {
            Optional<KnowledgeBaseModel> delete = knowledgeBaseRepository.findByKnowledgeBaseId(id);
            if (!delete.isPresent()) {
                throw new DigitalHumanCommonException("该知识库不存在");
            }
            if (!accountId.equals(delete.get().getAccountId())) {
                throw new DigitalHumanCommonException("无权限删除该知识库");
            }
            KnowledgeBaseModel model = delete.get().setDelete(true);
            knowledgeBaseRepository.save(model);
            try {
                Map<String, Object> result = httpCommonClient.deleteDatabase(url, Map.of("dataset_id", id), token, bceId);
                log.debug("Response in copilot when delete knowleadgeBase is {}, its knowleadgeBaseId is {}", result, id);
            } catch (Exception e) {
                model = delete.get().setDelete(false);
                knowledgeBaseRepository.save(model);
                log.debug("Copilot delete knowledgeBase false, model is: {}", model);
                throw new DigitalHumanCommonException("copilot删除知识库失败", e);
            }
        }
        return;
    }

    @Override
    public PageResponse<KnowledgeBase> listByType(String accoutId, int type, int pageNo, int pageSize, String name) {

        PageRequest pageRequest = PageRequest.of(pageNo - 1, pageSize);
        Page<KnowledgeBaseModel> page = knowledgeBaseRepository.findByAccountIdAndTypeAndNameContainingAndIsDeleteOrderByCreateTimeDesc(accoutId, type, name, false, pageRequest);

        List<String> knowledgeBaseIds = page.getContent().stream()
                .map(KnowledgeBaseModel::getKnowledgeBaseId).collect(Collectors.toList());
        if (knowledgeBaseIds == null || knowledgeBaseIds.size() == 0) {
            return PageResponse.success(pageNo, pageSize, 0, new ArrayList<KnowledgeBase>());
        }
        Map<String, Pair<Boolean, Integer>> statusMap = batchGetKnowledgeBaseStatus(knowledgeBaseIds);

        log.debug("List knowledgeBase, and its status map is {}", statusMap);
        for (KnowledgeBaseModel model : page.getContent()) {

            if (statusMap.get(model.getKnowledgeBaseId()).getFirst()) {
                model.setStatus(1);
            } else {
                model.setStatus(0);
            }
            model.setFileCount(statusMap.get(model.getKnowledgeBaseId()).getSecond());
        }
        return PageResponse.success(pageNo, pageSize, page.getTotalElements(),
                page.getContent().stream().map(KnowledgeBaseModel::toKnowledgeBase).collect(Collectors.toList()));
    }


    @Override
    public KnowledgeBase detailKnowledgeBase(String accoutId, String knowledgeBaseId) {
        Optional<KnowledgeBaseModel> detail = knowledgeBaseRepository.findByKnowledgeBaseId(knowledgeBaseId);
        if (detail.isPresent()) {
            KnowledgeBaseModel model = detail.get();
            if (!accoutId.equals(model.getAccountId())) {
                throw new DigitalHumanCommonException("无权限获取该知识库详情");
            } else {
                // todo set status
                return model.toKnowledgeBase();
            }
        } else {
            throw new DigitalHumanCommonException("不存在改知识库");
        }
    }

    @Override
    public Map<String, String> uploadFile(String fileName, MultipartFile file) {
        fileName = validFileName(fileName);
        log.debug("After valid file name, and file name is{}", fileName);
        Map<String, String> output = new HashMap<>();
        try {
            Map<String, Object> result = httpCommonClient.uploadFile(uploadFileUrl, token, bceId, fileName, file);
            String id = ((LinkedHashMap<String, String>) result.get("result")).get("id");
            output.put("fileId", id);
            URL save = storageService.save(file.getBytes(), "file_" + System.currentTimeMillis() + "_" + fileName);
            log.info("upload text file url is = {}", save);
            output.put("url", save.toString());
        } catch (StorageService.ResourceException e) {
            throw new DigitalHumanCommonException("can not gennerate url", e);
        } catch (IOException e) {
            throw new DigitalHumanCommonException("can prosess file", e);
        }
        return output;

    }

    @Override
    public KnowledgeFile uploadFileToKnowledgeBase(String accountId, String knowledgeBaseId, List<KnowledgeFile> files) {

        files.forEach(this::validFile);
        Optional<KnowledgeBaseModel> knowledgeBase = knowledgeBaseRepository.findByKnowledgeBaseId(knowledgeBaseId);
        if (!knowledgeBase.isPresent()) {
            throw new DigitalHumanCommonException("该知识库不存在");
        }
        if (!accountId.equals(knowledgeBase.get().getAccountId())) {
            throw new DigitalHumanCommonException("无权限访问该知识库");
        }
        long existFileNum = knowledgeFileRepository.findByKnowledgeBaseIdAndNameContainingOrderByCreateTimeDesc(
                knowledgeBaseId, "", PageRequest.of(0, 1)).getTotalElements();
        if (existFileNum + files.size() > knowledgeBaseConfig.getMaxFileNum()) {
            throw new DigitalHumanCommonException("目前知识库最大绑定文件数目为" + knowledgeBaseConfig.getMaxFileNum());
        }
        Map<String, Object> request = new HashMap<>();
        request.put("dataset_id", knowledgeBaseId);
        request.put("file_ids", files.stream().map(KnowledgeFile::getFileId).collect(Collectors.toList()));
        request.put("package_name", "SimpleSplitOnlyPackage");
        request.put("package_config", packageConfig);
        String responseBody = httpCommonClient.batchAssociateFile(batchAssociateFileUrl, request, token, bceId);

        log.debug("Response in copilot when upload file to knowledgeBase is {}", responseBody);
        // copilot平台区分了file和document，file为上传的文件，document为最终关联后的文件。平台的KnowledgeFile的fileId指最终关联后的documentId
        List<String> documentIds = new ArrayList<>();
        try {
            JsonNode rootNode = null;
            rootNode = JsonUtil.om.readTree(responseBody).path("result").path("document_ids");
            for (int i = 0; i < rootNode.size(); i++) {
                documentIds.add(rootNode.get(i).asText());
            }
        } catch (JsonProcessingException e) {
            throw new DigitalHumanCommonException("解析绑定文档信息失败");
        }
        List<KnowledgeFileModel> models = new ArrayList<>();
        for (int i = 0; i < files.size(); i++) {
            KnowledgeFile file = files.get(i);
            String documentId = documentIds.get(i);
            KnowledgeFileModel model = KnowledgeFileModel.builder()
                    .name(file.getName())
                    .knowledgeBaseId(knowledgeBaseId)
                    .fileId(documentId)
                    .status("waiting")
                    .url(file.getUrl()).build();
            models.add(model);
        }
        log.debug("Upload files to knowledge base, files is :{}", models);
        try {
            knowledgeFileRepository.saveAll(models);
        } catch (Exception e) {
            httpCommonClient.batchDeleteFile(batchDeleteFileUrl, Map.of("dataset_id", knowledgeBaseId, "document_ids", documentIds), token, bceId);
            throw new DigitalHumanCommonException("sql更新知识库数据失败");
        }
        knowledgeBaseRepository.save(knowledgeBase.get().setFileCount(knowledgeBase.get().getFileCount() + files.size()));
        return null;
    }

    @Override
    public void deleteFileInKnowledgeBase(String accountId, String knowledgeBaseId, List<String> fileIds) {

        Optional<KnowledgeBaseModel> knowledgeBase = knowledgeBaseRepository.findByKnowledgeBaseId(knowledgeBaseId);
        if (!knowledgeBase.isPresent()) {
            throw new DigitalHumanCommonException("该知识库不存在");
        }
        if (!accountId.equals(knowledgeBase.get().getAccountId())) {
            throw new DigitalHumanCommonException("无权限访问该知识库");
        }
        for (String fileId : fileIds) {
            Optional<KnowledgeFileModel> delete = knowledgeFileRepository.findByFileId(fileId);
            if (delete.isPresent()) {
                if (knowledgeBaseId.equals(delete.get().getKnowledgeBaseId())) {
                    knowledgeFileRepository.delete(delete.get());
                } else {
                    throw new DigitalHumanCommonException("该文件不在当前知识库中");
                }
            } else {
                throw new DigitalHumanCommonException("该文件不存在");
            }
        }
        Map<String, Object> response = httpCommonClient.batchDeleteFile(batchDeleteFileUrl, Map.of("dataset_id", knowledgeBaseId, "document_ids", fileIds), token, bceId);
        log.debug("Response in copilot when dele knowleadgeBase file is {}", response);
        knowledgeBaseRepository.save(knowledgeBase.get().setFileCount(knowledgeBase.get().getFileCount() - fileIds.size()));
    }

    @Override
    public PageResponse<KnowledgeFile> listFileInKnowledgeBase(String accountId, String knowledgeBaseId, String name, int pageNo, int pageSize) {

        Optional<KnowledgeBaseModel> knowledgeBase = knowledgeBaseRepository.findByKnowledgeBaseId(knowledgeBaseId);
        if (!knowledgeBase.isPresent()) {
            throw new DigitalHumanCommonException("该知识库不存在");
        }
        if (!accountId.equals(knowledgeBase.get().getAccountId())) {
            throw new DigitalHumanCommonException("无权限访问该知识库");
        }
        PageRequest pageRequest = PageRequest.of(pageNo - 1, pageSize);
        Page<KnowledgeFileModel> page = knowledgeFileRepository.findByKnowledgeBaseIdAndNameContainingOrderByCreateTimeDesc(knowledgeBaseId, name, pageRequest);
        Map<String, String> fileStatusMap = getFileStatus(knowledgeBaseId);
        log.debug("Files status in knowledgeBase is :{}", fileStatusMap);
        for (KnowledgeFileModel file : page.getContent()) {
            String status = fileStatusMap.get(file.getFileId());
            file.setStatus(status);
        }
        return PageResponse.success(pageNo, pageSize, page.getTotalElements(),
                page.getContent().stream().map(KnowledgeFileModel::toKnowledgeFile).collect(Collectors.toList()));
    }

    @Override
    public Map<String, Pair<Boolean, Integer>> batchGetKnowledgeBaseStatus(List<String> knowledgeBaseIds) {
        String responseBody = httpCommonClient.batchListFiles(batchListFileUrl, Map.of("dataset_ids", knowledgeBaseIds), token, bceId);
        log.debug("Response in copilot when batch list knowleadgeBase is {}", responseBody);
        // todo
        JsonNode rootNode = null;
        Map<String, Pair<Boolean, Integer>> map = new HashMap<>();
        try {
            rootNode = JsonUtil.om.readTree(responseBody).path("result").path("data");
            int size = rootNode.size();
            for (int i = 0; i < size; i++) {
                String knowledgeBaseId = rootNode.get(i).path("dataset_id").asText();
                JsonNode fileNode = rootNode.get(i).path("documents");
                int fileSize = fileNode.size();
                boolean isPrepared = true;
                for (int j = 0; j < fileSize; j++) {
                    String status = fileNode.get(j).get("indexing_status").asText();
                    if (!"completed".equals(status) && !"error".equals(status)) {
                        isPrepared = false;
                        break;
                    }
                }
                map.put(knowledgeBaseId, Pair.of(isPrepared, fileSize));
            }
        } catch (JsonProcessingException e) {
            throw new DigitalHumanCommonException("解析文件状态失败");
        }
        return map;
    }

    @Override
    public List<KnowledgeBase> batchGetKnowledgeBaseById(List<String> knowledgeBaseIds) {

        return knowledgeBaseRepository.findByKnowledgeBaseIdIn(knowledgeBaseIds)
                .stream().map(KnowledgeBaseModel::toKnowledgeBase).collect(Collectors.toList());
    }

    public Map<String, String> getFileStatus(String knowledgeBaseId) {
        String responseBody = httpCommonClient.listFiles(listFileUrl, Map.of("dataset_id", knowledgeBaseId), token, bceId);
        log.debug("Response in copilot when list knowleadgeBase is {}", responseBody);
        // todo
        JsonNode rootNode = null;
        try {
            rootNode = JsonUtil.om.readTree(responseBody).path("result").path("data");
            int size = rootNode.size();
            Map<String, String> fileStatusMap = new HashMap<>();
            for (int i = 0; i < size; i++) {
                String status = rootNode.get(i).get("indexing_status").asText();
                if ("completed".equals(status)) {
                    fileStatusMap.put(rootNode.get(i).get("id").asText(), "completed");
                } else if ("error".equals(status)) {
                    fileStatusMap.put(rootNode.get(i).get("id").asText(), "error");
                } else if ("waiting".equals(status)) {
                    fileStatusMap.put(rootNode.get(i).get("id").asText(), "waiting");
                } else if ("parsing".equals(status) || "cleaning".equals(status) || "splitting".equals(status) || "indexing".equals(status)) {
                    fileStatusMap.put(rootNode.get(i).get("id").asText(), "studying");
                }
            }
            return fileStatusMap;
        } catch (JsonProcessingException e) {
            throw new DigitalHumanCommonException("解析文件状态失败");
        }
    }

    public void validFile(KnowledgeFile file) {
        if (StringUtils.isBlank(file.getFileId())) {
            throw new DigitalHumanCommonException("上传文件id不合法，请检查");
        }
        if (StringUtils.isBlank(file.getUrl())) {
            throw new DigitalHumanCommonException("上传文件url不合法，请检查");
        }
        if (StringUtils.isBlank(file.getName())) {
            throw new DigitalHumanCommonException("上传文件name不合法，请检查");
        }
        file.setName(validFileName(file.getName()));
    }

    public String validFileName(String fileName) {
        try {
            int index = fileName.lastIndexOf('.');
            String type = fileName.substring(index + 1).toLowerCase();
            for (String supportFileType : supportFileTypes) {
                if (supportFileType.equals(type)) {
                    return fileName.substring(0, index) + "." + supportFileType;
                }
            }
        } catch (Exception e) {
            throw new DigitalHumanCommonException("不支持的文件类型" + fileName, e);
        }
        throw new DigitalHumanCommonException("不支持的文件类型" + fileName);
    }

}