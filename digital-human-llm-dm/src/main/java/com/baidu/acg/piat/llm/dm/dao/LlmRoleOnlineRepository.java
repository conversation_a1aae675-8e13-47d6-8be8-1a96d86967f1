package com.baidu.acg.piat.llm.dm.dao;

import com.baidu.acg.piat.llm.dm.model.LlmRoleOnlineModel;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.data.repository.query.Param;

import java.util.List;
import java.util.Optional;

public interface LlmRoleOnlineRepository extends PagingAndSortingRepository<LlmRoleOnlineModel, Long> {
    Page<LlmRoleOnlineModel> findByRoleTypeAndNameContainingAndDelOrderByCreateTimeDesc(Integer roleType, String name, boolean del, Pageable pageable);

    Page<LlmRoleOnlineModel> findByAccountIdAndRoleTypeAndNameContainingAndDelOrderByCreateTimeDesc(String accountId, Integer roleType,
                                                                                                    String name, boolean del, Pageable pageable);

    Optional<LlmRoleOnlineModel> findByLlmRoleId(String roleId);

    List<LlmRoleOnlineModel> findByLlmRoleIdIn(List<String> roleIds);

    @Query(value = "select * from llm_role where account_id =:accountId and BINARY name=:name and del=0", nativeQuery = true)
    List<LlmRoleOnlineModel> findByAccountIdAndNameAndDel(@Param("accountId") String accountId, @Param("name") String name);

}
