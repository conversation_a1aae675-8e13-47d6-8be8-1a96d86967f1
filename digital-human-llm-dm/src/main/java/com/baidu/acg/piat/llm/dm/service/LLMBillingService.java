package com.baidu.acg.piat.llm.dm.service;

public interface LLMBillingService {
    /**
     * Freeze quota.冻结amount数量的灵豆
     * @param accountId
     * @param userId
     * @param sessionId
     * @param requestId
     * @param amount
     * @return
     */
    boolean freezeQuota(String accountId,  String userId,
                        String sessionId, String requestId, int amount);

    /**
     * Confirmed cost.确认消费amount数量的灵豆
     * @param accountId
     * @param sessionId
     * @param requestId
     * @param amount
     * @return
     */
    boolean confirmedCost(String accountId, String sessionId, String requestId, int amount);

    /**
     * Unfreeze quota.解冻amount数量的灵豆
     * @param accountId
     * @param sessionId
     * @param requestId
     * @param amount
     * @return
     */
    boolean unfreezeQuota(String accountId, String sessionId, String requestId, int amount);


    long summary(String accountId);
}

