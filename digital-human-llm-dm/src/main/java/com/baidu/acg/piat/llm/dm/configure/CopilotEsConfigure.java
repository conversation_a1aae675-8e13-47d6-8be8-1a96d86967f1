// Copyright (C) 2019 Baidu Inc. All rights reserved.
package com.baidu.acg.piat.llm.dm.configure;

import lombok.Data;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * AccessConfigure
 *
 * <AUTHOR>
 * @since 2019-11-28
 */
@Configuration
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class CopilotEsConfigure {

    @Bean
    @ConfigurationProperties(prefix = "digitalhuman.copilot.config")
    public Config copilotEsConfig() {
        return new Config();
    }


    @Data
    public static class Config {

        private String resourceType = "bes";

        private String besEndpoint = "http://100.66.165.157:8200";

        private String besAdminName = "superuser";

        private String besPassword = "dh@2025!";

    }


}
