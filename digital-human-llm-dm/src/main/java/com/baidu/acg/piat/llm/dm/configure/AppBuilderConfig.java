package com.baidu.acg.piat.llm.dm.configure;

public class AppBuilderConfig {
    public static final String APPBUILDER_TOKEN = "APPBUILDER_TOKEN";
    public static final String APPBUILDER_GATEWAY_URL = "GATEWAY_URL";
    public static final String APPBUILDER_REQUEST_ID = "X-Appbuilder-Request-Id";
    public static final String APPBUILDER_DEFAULT_GATEWAY = "https://appbuilder.baidu.com";

    /*
     * HTTP client request timeout, in seconds
     */
    public static final int HTTP_CLIENT_CONNECTION_TIMEOUT = 300;

    /**
     * Create a conversation
     */
    public static final String CREATE_CONVERSATION_URL = "/api/v1/app/conversation";
    /**
     * Upload file
     */
    public static final String UPLOAD_FILE_URL = "/api/v1/app/conversation/file/upload";
    /**
     * Run appbuilder
     */
    public static final String AGENTBUILDER_RUN_URL = "/api/v1/app/conversation/runs";

    /**
     * Run rag
     */
    public static final String RAG_RUN_URL = "/api/v1/ai_engine/agi_platform/v1/instance/integrated";

    /**
     * Create a dataset
     */
    public static final String DATASET_CREATE_URL = "/api/v1/ai_engine/agi_platform/v1/datasets/create";
    /**
     * Add a document
     */
    public static final String DATASET_ADD_FILE_URL = "/api/v1/ai_engine/agi_platform/v1/datasets/documents";
    /**
     * Get a list of documents
     */
    public static final String DATASET_GET_FILE_LIST_URL = "/api/v1/ai_engine/agi_platform/v1/datasets/documents/list_page";
    /**
     * Delete a document
     */
    public static final String DATASET_DELETE_FILE_URL = "/api/v1/ai_engine/agi_platform/v1/datasets/document/delete";
    /**
     * Upload a file
     */
    public static final String DATASET_UPLOAD_FILE_URL = "/api/v1/ai_engine/agi_platform/v1/datasets/files/upload";

    private AppBuilderConfig() {
    }
}
