package com.baidu.acg.piat.llm.dm.service;

import com.baidu.acg.piat.digitalhuman.common.llmrole.KnowledgeBase;
import com.baidu.acg.piat.digitalhuman.common.llmrole.KnowledgeFile;
import com.baidu.acg.piat.digitalhuman.common.model.PageResponse;
import org.springframework.data.util.Pair;
import org.springframework.web.multipart.MultipartFile;

import javax.transaction.Transactional;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2023/11/23 14:05
 */
public interface KnowledgeBaseService {

    @Transactional
    KnowledgeBase createKnowledgeBase(KnowledgeBase request);

    @Transactional
    void batchDeleteKnowledgeBase(String accountId, List<String> ids);

    PageResponse<KnowledgeBase> listByType(String accoutId, int type, int pageNo, int pageSize, String name);

    KnowledgeBase detailKnowledgeBase(String accoutId, String llmRoleId);

    Map<String, String> uploadFile (String fileName, MultipartFile file);

    @Transactional
    KnowledgeFile uploadFileToKnowledgeBase(String accoutId, String knowledgeBaseId, List<KnowledgeFile> files);

    @Transactional
    void deleteFileInKnowledgeBase(String accoutId, String knowledgeBaseId, List<String> fileIds);

    PageResponse<KnowledgeFile> listFileInKnowledgeBase(String accoutId, String knowledgeBaseId, String name, int pageNo, int pageSize);

    Map<String, Pair<Boolean, Integer>> batchGetKnowledgeBaseStatus(List<String> knowledgeBaseIds);

    List<KnowledgeBase> batchGetKnowledgeBaseById(List<String> knowledgeBaseIds);
}
