package com.baidu.acg.piat.llm.dm.websocket.impl;

import com.alibaba.excel.util.StringUtils;
import com.baidu.acg.piat.digitalhuman.common.alita.ActionType;
import com.baidu.acg.piat.digitalhuman.common.constans.Constants;
import com.baidu.acg.piat.digitalhuman.common.exception.DigitalHumanCommonException;
import com.baidu.acg.piat.digitalhuman.common.llmdm.ConnectBody;
import com.baidu.acg.piat.digitalhuman.common.llmdm.WebsocketLlmRequest;
import com.baidu.acg.piat.digitalhuman.common.llmdm.WebsocketLlmResponse;
import com.baidu.acg.piat.digitalhuman.common.llmrole.LLMConfig;
import com.baidu.acg.piat.digitalhuman.common.project.BotParams;
import com.baidu.acg.piat.digitalhuman.common.session.SessionStatus;
import com.baidu.acg.piat.digitalhuman.common.utils.JsonUtil;
import com.baidu.acg.piat.digitalhuman.plat.client.PlatformClient;
import com.baidu.acg.piat.llm.dm.client.HttpCommonClient;
import com.baidu.acg.piat.llm.dm.configure.CommonConfig;
import com.baidu.acg.piat.llm.dm.constants.StatusType;
import com.baidu.acg.piat.llm.dm.dao.LlmHistoryRepository;
import com.baidu.acg.piat.llm.dm.dao.LlmRoleOnlineRepository;
import com.baidu.acg.piat.llm.dm.dao.LlmRoleRepository;
import com.baidu.acg.piat.llm.dm.error.BizError;
import com.baidu.acg.piat.llm.dm.model.LlmRoleModel;
import com.baidu.acg.piat.llm.dm.model.LlmRoleOnlineModel;
import com.baidu.acg.piat.llm.dm.model.SessionContext;
import com.baidu.acg.piat.llm.dm.service.LLMBillingService;
import com.baidu.acg.piat.llm.dm.service.LLMConversationSessionContext;
import com.baidu.acg.piat.llm.dm.service.Text2GestureService;
import com.baidu.acg.piat.llm.dm.service.impl.BotServiceCopilotStreamImpl;
import com.baidu.acg.piat.llm.dm.service.impl.BotServiceKaiWuStreamImpl;
import com.baidu.acg.piat.llm.dm.service.impl.BotServiceKeYueStreamImpl;
import com.baidu.acg.piat.llm.dm.service.impl.BotServiceQianfanStreamImpl;
import com.baidu.acg.piat.llm.dm.service.impl.BotServiceUnitStreamImpl;
import com.baidu.acg.piat.llm.dm.service.impl.LLmStreamObserverImpl;
import com.baidu.acg.piat.llm.dm.utils.SessionUtil;
import com.baidu.acg.piat.llm.dm.utils.WebsocketUtil;
import com.baidu.acg.piat.llm.dm.websocket.RequestReactor;

import io.vavr.control.Try;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.client.WebClient;

import java.util.Optional;

@Slf4j
@Component
@RequiredArgsConstructor
public class ConnectRequestReactor implements RequestReactor {

    private final LlmRoleRepository llmRoleRepository;

    private final LlmRoleOnlineRepository llmRoleOnlineRepository;

    private final Text2GestureService text2GestureService;

    private final LLMBillingService llmBillingService;

    private final LlmHistoryRepository llmHistoryRepository;

    private final PlatformClient platformClient;

    private final CommonConfig config;
    private final LLMConversationSessionContext llmConversationSessionContext;
    private final WebClient webClient;
    private final HttpCommonClient httpCommonClient;

    @Override
    public void react(SessionContext sessionContext, WebsocketLlmRequest request) {
        log.info("TextQueryRequestReactor, sessionId={}, request={}", sessionContext.getSessionId(), request);
        sessionContext.setStatus(SessionStatus.OPENING);
        ConnectBody connectBody = Try.of(() -> JsonUtil.readValue(request.getBody(),
                ConnectBody.class)).getOrNull();
        if (connectBody == null || StringUtils.isEmpty(connectBody.getLlmRoleId())) {
            log.warn("ConnectBody is null, sessionId={}", sessionContext.getSessionId());
            throw new DigitalHumanCommonException("ConnectBody token is null");
        }
        sessionContext.setConfigVersion(connectBody.getConfigVersion());

        LlmRoleModel llmDmConfigModel = llmRoleRepository.findByLlmRoleId(connectBody.getLlmRoleId())
                .orElseThrow(() -> new DigitalHumanCommonException("Token is not exist"));
        // 如果是发布版本，需要加载发布的配置
        if (Constants.PUBLISH_VERSION.equals(connectBody.getConfigVersion())) {
            log.debug("ConnectBody is publish version, load publish version={}", connectBody);
            LlmRoleOnlineModel llmDmConfigOnlineModel =
                    llmRoleOnlineRepository.findByLlmRoleId(connectBody.getLlmRoleId())
                            .orElseThrow(() -> new DigitalHumanCommonException("Token is not exist"));
            BeanUtils.copyProperties(llmDmConfigOnlineModel, llmDmConfigModel);
        }
        if (!StatusType.OPNE.getStatus().equals(llmDmConfigModel.getStatus())) {
            log.info("LlmDmConfigModel is not open, llmDmConfigModel={}", llmDmConfigModel);
            throw new DigitalHumanCommonException(BizError.LLM_STOP.getCode(), BizError.LLM_STOP.getMessage());
        }
        sessionContext.setLlmRoleModel(llmDmConfigModel);
        sessionContext.setAccountId(llmDmConfigModel.getAccountId());
        sessionContext.setUid(llmDmConfigModel.getUid());
        sessionContext.setAppId(llmDmConfigModel.getAppId());
        if (!org.apache.commons.lang3.StringUtils.isEmpty(connectBody.getCharacterImage())) {
            sessionContext.setCharacterImage(connectBody.getCharacterImage());
        }
        if (connectBody.getParameters() != null) {
            sessionContext.setChannel(connectBody.getParameters().getOrDefault("channel", ""));
        }
        if (!StringUtils.isEmpty(llmDmConfigModel.getTemplateId())) {
            Try.run(() -> {
                Optional.ofNullable(llmRoleRepository.findByLlmRoleId(llmDmConfigModel
                                .getTemplateId()).orElseGet(null))
                        .map(templateModel -> {
                            log.debug("Get templateModel={}", templateModel);
                            // TODO 使用模板里的大模型配置
                            llmDmConfigModel.setLlmConfig(templateModel.getLlmConfig());
                            return null;
                        });
            }).onFailure(e -> {
                log.debug("TemplateId is not exist, templateId={}", llmDmConfigModel.getTemplateId());
            });
        }
        SessionUtil.iniBotProviderService(sessionContext, llmDmConfigModel, request,
                config, webClient, llmConversationSessionContext, httpCommonClient);
        LLMConfig llmConfig = llmDmConfigModel.getLlmConfig();
        if (llmConfig.getBotType().equals(BotParams.BotTypeEnum.UNIT_LLM.getBotType())) {
            log.debug("INIT NGD bot={}", request);
            sessionContext.setBotProviderService(new BotServiceUnitStreamImpl(config));
        } else if (llmDmConfigModel.getLlmConfig().getBotType()
                .equals(BotParams.BotTypeEnum.COPILOT.getBotType())) {
            log.debug("INIT COPILOT BOT:{}", request);
            sessionContext.setBotProviderService(new BotServiceCopilotStreamImpl());
        } else if (llmDmConfigModel.getLlmConfig().getBotType()
                .equals(BotParams.BotTypeEnum.QIAN_FAN.getBotType())) {
            log.debug("INIT QIANFAN BOT:{}", request);
            sessionContext.setBotProviderService(
                    new BotServiceQianfanStreamImpl(llmConversationSessionContext, webClient, httpCommonClient));
        } else if (llmDmConfigModel.getLlmConfig().getBotType()
                .equals(BotParams.BotTypeEnum.KAI_WU.getBotType())) {
            log.debug("INIT KAIWU BOT:{}", request);
            sessionContext.setBotProviderService(new BotServiceKaiWuStreamImpl(llmConversationSessionContext));
        } else if (llmDmConfigModel.getLlmConfig().getBotType()
                .equals(BotParams.BotTypeEnum.KE_YUE.getBotType())) {
            log.debug("INIT KE_YUE BOT:{}", request);
            sessionContext.setBotProviderService(new BotServiceKeYueStreamImpl(config));
        } else {
            log.error("BotType is not exist, botType={}", llmDmConfigModel.getLlmConfig().getBotType());
            throw new DigitalHumanCommonException("BotType is not exist");
        }
        sessionContext.setLlmConfig(llmConfig);
        sessionContext.setBufferSentencesConfigJson(llmDmConfigModel.getBufferSentencesConfigJson());

        sessionContext.setLlmStreamObserver(new LLmStreamObserverImpl(sessionContext, text2GestureService,
                config, llmBillingService, llmHistoryRepository));
        sessionContext.setSessionId(connectBody.getSessionId());

        if (config.isBillingEnable()) {
            long accountBalance = llmBillingService.summary(sessionContext.getAccountId());
            if (accountBalance < config.getBillingCost()) {
                log.warn("Account balance is not enough, accountId={}, accountBalance={}, quotaCost={}, wsId={}",
                        sessionContext.getAccountId(), accountBalance, config.getBillingCost(),
                        sessionContext.getWebSocketSession().getId());
                // 如果灵豆不足，分发布返回不同的分支
                if (Constants.PUBLISH_VERSION.equals(connectBody.getConfigVersion())) {
                    throw new DigitalHumanCommonException(BizError.LLM_STOP.getCode(), BizError.LLM_STOP.getMessage());
                } else {
                    throw new DigitalHumanCommonException(BizError.BILLING_COIN_NOT_ENOUGH.getCode(),
                            BizError.BILLING_COIN_NOT_ENOUGH.getMessage());
                }
            }

        }

        log.info("Connect success, sessionId={}", sessionContext.getSessionId());
        WebsocketUtil.send(sessionContext.getWebSocketSession(),
                WebsocketLlmResponse.success(request, llmConfig));

    }

    @Override
    public boolean support(ActionType actionType) {
        return actionType == ActionType.CONNECT;
    }
}
