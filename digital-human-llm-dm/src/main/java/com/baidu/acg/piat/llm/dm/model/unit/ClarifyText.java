package com.baidu.acg.piat.llm.dm.model.unit;

import java.util.List;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 *
 * 澄清
 **/
@ApiModel(description = "文字回复")
@Data
public class ClarifyText {
    /**
     * 澄清标题
     */
    @ApiModelProperty(value = "澄清标题")
    private String title;

    /**
     * 澄清问题列表
     */
    @ApiModelProperty(value = "澄清问题列表")
    private List<String> list;
}
