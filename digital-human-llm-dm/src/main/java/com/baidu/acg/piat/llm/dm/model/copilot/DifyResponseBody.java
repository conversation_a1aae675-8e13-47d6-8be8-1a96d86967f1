package com.baidu.acg.piat.llm.dm.model.copilot;

import java.util.List;
import java.util.Map;

import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DifyResponseBody {

    @JsonProperty(value = "message_id")
    String messageId;

    List<Object> references;

    String answer;

    @JsonProperty(value = "conversation_id")
    String conversationId;

    Map<String, Object> extras;


    String event;

    @JsonProperty(value = "task_id")
    String taskId;

    @JsonProperty(value = "id")
    String id;

    long createdAt;


}
