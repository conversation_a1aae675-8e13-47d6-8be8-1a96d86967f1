// Copyright (C) 2019 Baidu Inc. All rights reserved.
package com.baidu.acg.piat.llm.dm.service.impl;

import com.baidu.acg.piat.llm.dm.client.HttpCommonClient;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;

import com.baidu.acg.piat.digitalhuman.common.exception.DigitalHumanCommonException;
import com.baidu.acg.piat.digitalhuman.common.llmrole.LLMConfig;
import com.baidu.acg.piat.digitalhuman.common.model.BaseResponse;
import com.baidu.acg.piat.digitalhuman.common.project.BotParams;
import com.baidu.acg.piat.digitalhuman.common.utils.JsonUtil;
import com.baidu.acg.piat.llm.dm.configure.CommonConfig;
import com.baidu.acg.piat.llm.dm.dao.LlmRoleRepository;
import com.baidu.acg.piat.llm.dm.model.LlmRoleModel;
import com.baidu.acg.piat.llm.dm.model.Query;
import com.baidu.acg.piat.llm.dm.model.chat.LlmQuery;
import com.baidu.acg.piat.llm.dm.model.chat.LlmResult;
import com.baidu.acg.piat.llm.dm.model.copilot.DifyResponseBody;
import com.baidu.acg.piat.llm.dm.model.qianfan.QianfanChatResponse;
import com.baidu.acg.piat.llm.dm.service.BotProviderService;
import com.baidu.acg.piat.llm.dm.service.ChatService;
import com.baidu.acg.piat.llm.dm.service.LLMConversationSessionContext;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Flux;

@Slf4j
@Service
@RequiredArgsConstructor
public class ChatServiceImpl implements ChatService {

    private final LlmRoleRepository llmRoleRepository;

    private final CommonConfig config;

    private final LLMConversationSessionContext llmConversationSessionContext;

    private final WebClient webClient;

    private final HttpCommonClient httpCommonClient;

    public Flux<LlmResult> chat(LlmQuery llmQuery) {
        LLMConfig llmConfig = llmQuery.getModel();
        if (!StringUtils.isEmpty(llmQuery.getLlmConfigId())) {
            LlmRoleModel llmDmConfigModel = llmRoleRepository.findByLlmRoleId(llmQuery.getLlmConfigId())
                    .orElseThrow(() -> new DigitalHumanCommonException("llmConfigId not found"));
            llmConfig = llmDmConfigModel.getLlmConfig();
        }
        if (llmConfig == null) {
            throw new DigitalHumanCommonException("大模型配置不存在");
        }
        String botType = llmConfig.getBotType();
        BotProviderService botProviderService = null;
        if (llmConfig.getBotType().equals("UNIT_LLM")) {
            botProviderService = new BotServiceUnitStreamImpl(config);
        } else if (llmConfig.getBotType().equals("COPILOT")) {
            botProviderService = new BotServiceCopilotStreamImpl();
        } else if (llmConfig.getBotType().equals("QIAN_FAN")) {
            botProviderService = new BotServiceQianfanStreamImpl(llmConversationSessionContext, webClient,
                    httpCommonClient);
        } else if (llmConfig.getBotType().equals("KAI_WU")) {
            botProviderService = new BotServiceKaiWuStreamImpl(llmConversationSessionContext);
        } else {
            throw new DigitalHumanCommonException("大模型类型不存在");
        }
        Query query = Query.builder()
                .text(llmQuery.getQuery())
                .sessionId(llmQuery.getSessionId())
                .stream(llmQuery.isStream())
                .context(llmQuery.getParams())
                .requestId(llmQuery.getRequestId()).build();
        return botProviderService.chat(query, llmConfig).map(s -> {
            if (BotParams.BotTypeEnum.QIAN_FAN.getBotType().equals(botType)) {
                try {
                    log.debug("QianfanChatResponse: {}, id={}", s, query.getRequestId());
                    QianfanChatResponse qianfanChatResponse = JsonUtil.readValue(s, QianfanChatResponse.class);
                    LlmResult llmResult = new LlmResult();
                    llmResult.setResult(qianfanChatResponse.getResult());
                    llmResult.setRequestId(llmQuery.getRequestId());
                    if (!StringUtils.isEmpty(qianfanChatResponse.getErrorMsg())) {
                        llmResult.setMessage(new BaseResponse.Message(qianfanChatResponse.getErrorMsg()));
                        llmResult.setCode(qianfanChatResponse.getErrorCode());
                    }
                    return llmResult;
                } catch (Exception e) {
                    log.error("QianfanChatResponse parse error", e);
                    return null;
                }
            } else if ("COPILOT".equals(botType)) {
                try {
                    log.debug("CopilotChatResponse: {}, id={}", s, query.getRequestId());
                    DifyResponseBody difyResponseBody = JsonUtil.readValue(s, DifyResponseBody.class);
                    LlmResult llmResult = new LlmResult();
                    llmResult.setResult(difyResponseBody.getAnswer());
                    llmResult.setRequestId(llmQuery.getRequestId());

                    return llmResult;
                } catch (Exception e) {
                    log.error("CopilotChatResponse parse error", e);
                    return null;
                }
            } else {
                return null;
            }
        });
    }

}
