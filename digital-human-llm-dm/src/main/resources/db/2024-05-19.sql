CREATE TABLE `llm_history`
(
    `id`               bigint(20) unsigned                     NOT NULL AUTO_INCREMENT COMMENT '自增主键',
    `uid`           varchar(64) COLLATE utf8mb4_unicode_ci  NOT NULL DEFAULT '' COMMENT '用户id',
    `app_id`           varchar(128) COLLATE utf8mb4_unicode_ci  NOT NULL DEFAULT '' COMMENT '应用id',
    `character_image`           varchar(128) COLLATE utf8mb4_unicode_ci  NOT NULL DEFAULT '' COMMENT '人像id',
    `llm_role_id`           varchar(64) COLLATE utf8mb4_unicode_ci  NOT NULL DEFAULT '' COMMENT '交互id',
    `request_id`           varchar(128) COLLATE utf8mb4_unicode_ci  NOT NULL DEFAULT '' COMMENT '请求id',
    `query`           varchar(1024) COLLATE utf8mb4_unicode_ci  NOT NULL DEFAULT '' COMMENT '请求query',
    `answer`           varchar(2056) COLLATE utf8mb4_unicode_ci  NOT NULL DEFAULT '' COMMENT '回复的内容，流式只存第一句',
    `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `uid` (`uid`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT ='大模型交互记录';

ALTER TABLE llm_history ADD COLUMN `channel` VARCHAR(32) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '渠道';
ALTER TABLE llm_history ADD COLUMN `answer_type` VARCHAR(32) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '回答类型';
