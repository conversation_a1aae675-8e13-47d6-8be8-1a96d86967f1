CREATE TABLE `llm_role_template` (
                                     `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
                                     `llm_role_template_id` varchar(128) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '角色模板id',
                                     `template_name` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '模板名称',
                                     `template_type` tinyint(3) DEFAULT '2' COMMENT '模板类型A类或者B类',
                                     `template_icon_url` varchar(256) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
                                     `customized_config` varchar(512) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '模版特设值',
                                     `llm_role_id` varchar(512) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '关联的角色id',
                                     `del` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除',
                                     `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                     `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                     PRIMARY KEY (`id`),
                                     UNIQUE KEY `uk_llm_role_id` (`llm_role_template_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1379 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='大模型角色模板表';
ALTER TABLE llm_role_online
    ADD COLUMN `status` VARCHAR(128) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '交互状态：启用或停止';

ALTER TABLE llm_role
    ADD COLUMN `status` VARCHAR(128) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '交互状态：启用或停止';
ALTER TABLE llm_role_template
    ADD COLUMN `screen_type` VARCHAR(128) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '横版horizontal或竖版vertical';

ALTER TABLE llm_role
    ADD COLUMN `app_id` VARCHAR(128) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'appId';
ALTER TABLE llm_role
    ADD COLUMN `app_key` VARCHAR(128) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'appKey';

CREATE TABLE IF NOT EXISTS `llm_role_online` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
    `llm_role_id` varchar(128) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '角色id',
    `name` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '角色名称',
    `account_id` varchar(128) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '账户id',
    `project_id` varchar(128) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '关联的场景id',
    `editor` varchar(256) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '编辑人',
    `role_type` tinyint(3) DEFAULT '2' COMMENT '角色类型',
    `template_id` varchar(128) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
    `template_name` varchar(128) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
    `template_icon_url` varchar(256) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
    `app_id` varchar(128) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
    `app_key` varchar(128) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
    `llm_config` varchar(512) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '大模型的配置内容',
    `role_define` varchar(1024) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '角色的设定',
    `skill_list` mediumtext COLLATE utf8mb4_unicode_ci COMMENT '角色技能',
    `knowledge` mediumtext COLLATE utf8mb4_unicode_ci COMMENT '角色知识',
    `opening_statement` varchar(768) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '开场白',
    `buffer_sentences_config_json` mediumtext COLLATE utf8mb4_unicode_ci COMMENT '缓冲话术',
    `del` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除',
    `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `status` varchar(128) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '交互状态：启用或停止',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_llm_role_id` (`llm_role_id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='大模型角色表生产表online';
COMMIT;

CREATE TABLE IF NOT EXISTS `project_online` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
    `project_id` varchar(100) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT 'project id',
    `name` varchar(100) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT 'name',
    `description` varchar(200) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT 'description',
    `tanant_id` varchar(100) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT 'tanant id',
    `user_id` varchar(100) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT 'user id',
    `is_default` tinyint(4) NOT NULL DEFAULT '0' COMMENT 'is default',
    `is_system` tinyint(4) NOT NULL DEFAULT '0' COMMENT 'is system project',
    `project_version` varchar(40) COLLATE utf8mb4_bin NOT NULL DEFAULT '1.0.0' COMMENT 'project version',
    `thumbnail_url` varchar(1024) COLLATE utf8mb4_bin DEFAULT '',
    `pic_url` varchar(1024) COLLATE utf8mb4_bin NOT NULL DEFAULT '',
    `preset` varchar(1024) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT 'preset',
    `character_config_id` varchar(256) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT 'character_config_id',
    `character_config_name` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT 'character_config_name',
    `character_config` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT 'character_config',
    `background_image_id` varchar(100) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT 'background image id',
    `background_image_url` varchar(1024) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT 'background image url',
    `logo_uid` varchar(100) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT 'logo uid',
    `logo_url` varchar(1024) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT 'logo url',
    `bot_params` varchar(1400) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT 'bot params',
    `tts_params` varchar(256) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT 'tts params',
    `character_image` varchar(100) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT 'character image',
    `figure_alias` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT 'figure_alias',
    `camera` varchar(256) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT 'camera params',
    `resolution_params` varchar(256) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT 'resolution params',
    `figure_cut_params` varchar(1024) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT 'figure cut params',
    `character_params` varchar(1024) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT 'character params',
    `paint_chart_on_picture_params` varchar(512) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT 'paint chart on picture params',
    `paint_subtitle_on_picture_params` varchar(512) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT 'paint subtitle on picture params',
    `media_output` varchar(256) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT 'media output',
    `conversation_config_id` bigint(20) DEFAULT NULL COMMENT 'conversation config id',
    `hot_words` mediumtext COLLATE utf8mb4_bin NOT NULL COMMENT 'hot words',
    `user_inactive` varchar(256) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT 'user inactive',
    `prologue_params` varchar(512) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT 'prologue params',
    `hit_shield_reply` varchar(1024) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT 'hit shield reply',
    `alita_params` varchar(256) COLLATE utf8mb4_bin DEFAULT NULL COMMENT 'alita_params',
    `asr_part_event` varchar(256) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT 'asr_part_event',
    `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'create time',
    `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'last update time',
    `version` int(11) NOT NULL COMMENT 'for optimistic locking',
    `editor` varchar(128) COLLATE utf8mb4_bin DEFAULT '' COMMENT '最后编辑人',
    `character_thumbnail` varchar(512) COLLATE utf8mb4_bin NOT NULL DEFAULT '',
    `api_version` tinyint(4) NOT NULL DEFAULT '1' COMMENT 'api version',
    `type` varchar(8) COLLATE utf8mb4_bin NOT NULL DEFAULT 'USER' COMMENT '是否是大模型交互对应场景',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_user_id_name_type_project_version_api_version` (`user_id`,`name`,`type`,`project_version`,`api_version`) USING BTREE,
    KEY `idx_project_id` (`project_id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='project online table';


ALTER TABLE llm_role ADD COLUMN `mode_type` tinyint(3) DEFAULT '0' COMMENT '模式类型：0-普通，1-高级';
ALTER TABLE llm_role_online ADD COLUMN `mode_type` tinyint(3) DEFAULT '0' COMMENT '模式类型：0-普通，1-高级';


ALTER TABLE llm_role ADD COLUMN `llm_role_template_id` VARCHAR(128) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '角色模板id';
ALTER TABLE llm_role_online ADD COLUMN `llm_role_template_id` VARCHAR(128) COLLATE utf8mb4_unicode_ci DEFAULT NULL
    COMMENT '角色模板id';

ALTER TABLE llm_role ADD COLUMN `screen_type` VARCHAR(128) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '横版horizontal或竖版vertical';
ALTER TABLE llm_role_online ADD COLUMN `screen_type` VARCHAR(128) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT
    '横版horizontal或竖版vertical';

ALTER TABLE llm_role ADD COLUMN `tts_params` varchar(256) DEFAULT NULL COMMENT 'tts';
ALTER TABLE llm_role_online ADD COLUMN `tts_params` varchar(256) DEFAULT NULL COMMENT 'tts';

ALTER TABLE llm_role ADD COLUMN `uid` varchar(128) DEFAULT NULL COMMENT '用户id';
ALTER TABLE llm_role_online ADD COLUMN `uid` varchar(128) DEFAULT NULL COMMENT '用户id';

ALTER TABLE llm_role ADD COLUMN `character_config_id` varchar(128) DEFAULT NULL COMMENT '人设id';
ALTER TABLE llm_role_online ADD COLUMN `character_config_id` varchar(128) DEFAULT NULL COMMENT '人设id';
