CREATE TABLE `llm_dm_config`
(
    `id`               bigint(20) unsigned                     NOT NULL AUTO_INCREMENT COMMENT '自增主键',
    `bot_id`           varchar(64) COLLATE utf8mb4_unicode_ci  NOT NULL DEFAULT '' COMMENT 'bot的uuid标识',
    `name`           varchar(128) COLLATE utf8mb4_unicode_ci  NOT NULL DEFAULT '' COMMENT '名字',
    `token`           varchar(64) COLLATE utf8mb4_unicode_ci  NOT NULL DEFAULT '' COMMENT 'bot的uuid标识',
    `llm_config_json`    mediumtext COLLATE utf8mb4_unicode_ci  NOT NULL COMMENT 'llm 配置内容',
    `animojis_tags_config_json`    mediumtext COLLATE utf8mb4_unicode_ci  NOT NULL COMMENT '动作映射json内容',
    `buffer_sentences_config_json`    mediumtext COLLATE utf8mb4_unicode_ci  NOT NULL COMMENT '缓冲话术',
    `del`              tinyint(1)                              NOT NULL DEFAULT '0' COMMENT '删除 1是， 0 否',
    `create_time`      datetime                                NOT NULL COMMENT '创建时间',
    `update_time`      datetime                                NOT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `ix_bot_id` (`bot_id`),
    UNIQUE KEY `ix_token` (`token`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT ='大模型配置表';

