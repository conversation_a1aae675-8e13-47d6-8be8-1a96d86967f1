server:
  port: 8075
spring:
  application:
    name: digital-human-llm-dm
  profiles:
    active: dev
  jpa:
    database: MYSQL
    show-sql: false
    open-in-view: false
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ****************************************************************************************************************************************************************************************************************************
    username: dhsandbox
    password: '!Digitalhuman@baidu123'
    maxActive: 100
    validationInterval: 5000
    timeBetweenEvictionRunsMillis: 2000
    maxWait: 15000

logging:
  level:
    root: INFO
    com.baidu.acg.piat: DEBUG

digitalhuman:
  llmdm:
    split:
      end: . ,， 。 ； ！ ! ? ？)]}）】
  dh-user:
    config:
      baseUrl: http://localhost:8080
  auth:
    enabled: true
    paths: /api/digitalhuman/plat/v2/**,/api/digitalhuman/v2/**,/api/digitalhuman/optlog/**,/api/external/digitalhuman/v2/apps/**
    exclude-paths: /api/digitalhuman/v1/**,/api/digitalhuman/v2/property/id/**
    url:
      base: http://localhost:8080
      login: https://login.bcetest.baidu.com/
    bce:
      enabled: true
      endpoint: http://*************:37777/v4
      accessKey: ALTAK70YrgfU8s69809DorhoqO
      secretKey: ab78e068490c4f6b88253a349438c953
  storage:
    bos:
      accessKeyId: fb1e15123edc43afa2d7b34f78177f55
      secretAccessKey: 16a4e80552a04d939a4c9d5b014427d5
      bucket: digital-human-material
      endpoint: https://digital-human-material.bj.bcebos.com
      retryTimes: 1
      retrySleepMillis: 100
      urlExpireSeconds: -1

adaptor:
  llm:
    mode: qianfan
    conversationStoreSeconds: 1800
  engine:
    qianfan:
      baseUrl: https://aip.baidubce.com
      authUrl: oauth/2.0/token
      model: ernie
      engineUriMap:
        eb35: rpc/2.0/ai_custom/v1/wenxinworkshop/chat/completions
        eb4: rpc/2.0/ai_custom/v1/wenxinworkshop/chat/completions_pro
        ebturbo: rpc/2.0/ai_custom/v1/wenxinworkshop/chat/eb-instant
        ernie: rpc/2.0/ai_custom/v1/wenxinworkshop/chat/ai_apaas
      ak: sU43qT5j0CGPyFjHafENvEPq
      sk: xQh8qEdOjOaQMdQ0yKhrbhaBX7D6X7It