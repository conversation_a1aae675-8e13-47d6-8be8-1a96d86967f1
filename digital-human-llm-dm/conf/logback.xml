<?xml version="1.0" encoding="UTF-8"?>
<configuration debug="false">
    <property name="LOG.HOME" value="../log/"/>

    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <Pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg %n</Pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>


    <appender name="CHAT_LOG_APPENDER_V1" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG.HOME}/chat_v1.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <FileNamePattern>${LOG.HOME}/chat_v1.%d{yyyy-MM-dd}.log</FileNamePattern>
            <!-- keep x days' worth of history -->
            <maxHistory>120</maxHistory>
        </rollingPolicy>
        <encoder>
            <Pattern>%msg%n</Pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>
    <appender name="DEBUG_LOG_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>DEBUG</level>
        </filter>
        <encoder>
            <charset>UTF-8</charset>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} %5p ${PID:- } [%t] --- %-40.40logger{39} : llm-dm llm-dm-%X{x-request-id} [%X{currentUser}][%F:%M:%L] %m%n}
            </pattern>
        </encoder>
        <file>${LOG.HOME}/debug/llm-dm.debug.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>
                ${LOG.HOME}/debug/llm-dm.error.log-%d{yyyy-MM-dd}/%d{yyyy-MM-dd}.%i.debug.log
            </fileNamePattern>
            <maxHistory>30</maxHistory>
            <timeBasedFileNamingAndTriggeringPolicy
                    class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <maxFileSize>30MB</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy>
        </rollingPolicy>
    </appender>
    <appender name="INFO_LOG_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>INFO</level>
        </filter>
        <encoder>
            <charset>UTF-8</charset>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} %5p ${PID:- } [%t] --- %-40.40logger{39} : llm-dm llm-dm-%X{x-request-id} [%X{currentUser}][%F:%M:%L] %m%n}
            </pattern>
        </encoder>
        <file>${LOG.HOME}/info/llm-dm.info.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>
                ${LOG.HOME}/info/llm-dm.info.log-%d{yyyy-MM-dd}/%d{yyyy-MM-dd}.%i.info.log
            </fileNamePattern>
            <maxHistory>30</maxHistory>
            <timeBasedFileNamingAndTriggeringPolicy
                    class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <maxFileSize>30MB</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy>
        </rollingPolicy>
    </appender>
    <appender name="ERROR_LOG_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>ERROR</level>
        </filter>
        <encoder>
            <charset>UTF-8</charset>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} %5p ${PID:- } [%t] --- %-40.40logger{39} : llm-dm llm-dm-%X{x-request-id} [%X{currentUser}][%F:%M:%L] %m%n}
            </pattern>
        </encoder>
        <file>${LOG.HOME}/error/llm-dm.error.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>
                ${LOG.HOME}/error/llm-dm.error.log-%d{yyyy-MM-dd}/%d{yyyy-MM-dd}.%i.error.log
            </fileNamePattern>
            <maxHistory>30</maxHistory>
            <timeBasedFileNamingAndTriggeringPolicy
                    class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <maxFileSize>50MB</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy>
        </rollingPolicy>
    </appender>

    <logger name="CHAT_LOG_V1" additivity="false">
        <level value="info"/>
        <appender-ref ref="STDOUT"/>
        <appender-ref ref="CHAT_LOG_APPENDER_V1"/>
    </logger>

    <logger name="org.springframework.kafka" level="off" />
    <logger name="org.apache.kafka" level="OFF"/>

    <root level="DEBUG">
        <appender-ref ref="STDOUT"/>
        <appender-ref ref="ERROR_LOG_FILE"/>
        <appender-ref ref="INFO_LOG_FILE"/>
        <appender-ref ref="DEBUG_LOG_FILE"/>
    </root>
</configuration>