# !/bin/sh

script=$0
if [[ ${script:0:1} == "/" ]]; then
    bin=`dirname $script`
else
    bin=`pwd`/`dirname $script`
fi

PROFILE=$PROFILE
echo ${PROFILE}

root=${bin}/..
app=digital-human-llm-dm

APPLICATION=${bin}/${app}.jar
SPRING_CONFIG_FILE=${root}/conf/application.yaml
LOGBACK_FILE_PATH=${root}/conf/logback.xml
MAX_MEMORY=4096M

java -Dspring.config.location=$SPRING_CONFIG_FILE -Dlogging.config=$LOGBACK_FILE_PATH -Dfile.encoding=UTF-8 -Xmx$MAX_MEMORY -XX:+UseConcMarkSweepGC -XX:+PrintGCDetails -Xloggc:gc.log -jar ${APPLICATION}
