package main

import (
	"acg-ai-go-common/global"
	"acg-ai-go-common/logger"
	"fmt"
	"log"
	"os"
	"os/signal"
	"sync"
	"syscall"
	"time"

	"github.com/BurntSushi/toml"
)

var exitChan = make(chan struct{})
var wg sync.WaitGroup
var removeFileList = NewSafeMap()

func loopDownloadFile() {
	defer wg.Done()
	f := FileListHttp{
		downloadFileList: []string{"https://text2figure.cdn.bcebos.com/1D12CFFB4D5B4D3FD03F35882B30D4AC.bin", "https://text2figure.cdn.bcebos.com/6BD6D8924FA046C94C28F68B0C2CE0E7.bin"},
		downloadPath:     "c://figurebin//",
		tmpDownloadPath:  ".//tmp//",
	}
	for {
		select {
		case <-exitChan:
			logger.Log.Infoln("loopDownloadFile received exit signal. Exiting...")
			return
		default:
			ok, err := f.GetFileList(LocalConfig.VisSetting.ActiveURL)

			if err != nil {
				time.Sleep(1 * time.Second)
				continue
			}

			if !ok {
				time.Sleep(1 * time.Second)
				continue
			}

			// 先删除多余文件
			f.RemoveRedundantFile()

			// 在下载文件
			f.DownLoadFile()
			time.Sleep(1 * time.Second)
		}
	}
}
func loopMonitorDisk() {
	// 等待下载文件完成和删除无用文件后在启动监控磁盘空间
	time.Sleep(2 * time.Minute)
	defer wg.Done()
	starttime := time.Now()
	DiskMonitor("c://figurebin")
	for {
		select {
		case <-exitChan:
			logger.Log.Infoln("loopMonitorDisk received exit signal. Exiting...")
			return
		default:
			endtime := time.Now()

			if endtime.Sub(starttime) > 2*time.Minute {
				starttime = time.Now()
				DiskMonitor("c://figurebin")
				LogMonitor(ConfFilePath)
			}
			time.Sleep(1 * time.Second)
		}
	}
}

const ConfFilePath = "./conf/conf.toml"

// go build -ldflags -H=windowsgui
func main() {
	fmt.Println("dhlive-resource-cache is running...")
	if _, err := toml.DecodeFile(ConfFilePath, global.ServerSetting); err != nil {
		log.Panicf("本地个性化配置加载失败: {%v}", err)
	}

	if _, err := toml.DecodeFile(ConfFilePath, LocalConfig); err != nil {
		log.Panicf("本地个性化配置加载失败: {%v}", err)
	}

	// 创建日志路径
	createFolderIfNotExists("./logs/")
	//初始化服务名称，供logger对象使用
	logger.SetLogger()
	logger.Log.Infoln("dhlive-resource-cache log init success")
	wg.Add(2)
	// 开启循环
	go loopDownloadFile()
	go loopMonitorDisk()
	// go LogMonitor("./logs/")
	// 创建一个接收退出信号的通道
	osexitChan := make(chan os.Signal, 1)
	signal.Notify(osexitChan, syscall.SIGINT, syscall.SIGTERM)
	// 等待接收退出信号
	<-osexitChan
	// 收到退出信号后执行清理工作，然后退出程序
	logger.Log.Infof("Received exit signal. Performing cleanup and exiting...")

	close(exitChan)
	wg.Wait()
	logger.Log.Infof("exit")
	// 退出程序
	os.Exit(0)
}
