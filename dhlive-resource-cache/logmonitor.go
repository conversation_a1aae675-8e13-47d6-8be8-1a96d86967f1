package main

import (
	"acg-ai-go-common/logger"
	"os"
	"path/filepath"
)

func LogMonitor(dir string) error {
	// 遍历文件夹
	err := filepath.Walk(dir, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}
		// 只处理文件，不处理目录
		if !info.IsDir() {
			logsize := info.Size()
			if logsize/(1024*1024) > 30 {
				err := os.Remove(path)
				if err != nil {
					return err
				}
				logger.Log.Infof("remove log file %s", path)
			}
		}
		return nil
	})

	if err != nil {
		return err
	}

	return nil
}
