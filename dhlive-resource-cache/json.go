package main

type DataItem struct {
	ID           int    `json:"id"`
	RequestID    string `json:"requestId"`
	StarLightID  int    `json:"starLightId"`
	UserID       string `json:"userId"`
	FigureName   string `json:"figureName"`
	Name         string `json:"name"`
	PreviewImg   string `json:"previewImg"`
	LoadUrl      string `json:"loadUrl"`
	VideoUrl     string `json:"videoUrl"`
	Status       string `json:"status"`
	TrainLoadUrl string `json:"trainLoadUrl"`
	TrainStatus  string `json:"trainStatus"`
	CreatedAt    string `json:"createdAt"`
	UpdatedAt    string `json:"updatedAt"`
}

type Response struct {
	Code int        `json:"code"`
	Msg  string     `json:"msg"`
	Data []DataItem `json:"data"`
}
