package main

import (
	"acg-ai-go-common/logger"
	"fmt"
	"os"
	"path/filepath"
	"sort"
	"syscall"
	"time"

	"github.com/BurntSushi/toml"
)

// FileWithTime 结构体用于存储文件信息和访问时间
type FileWithTime struct {
	Path       string
	AccessTime time.Time
}

// ByAccessTime 实现 sort.Interface 接口，用于按访问时间排序
type ByAccessTime []FileWithTime

func (a ByAccessTime) Len() int           { return len(a) }
func (a ByAccessTime) Swap(i, j int)      { a[i], a[j] = a[j], a[i] }
func (a ByAccessTime) Less(i, j int) bool { return a[i].AccessTime.Before(a[j].AccessTime) }

// getAccessTime 获取文件的访问时间
func getAccessTime(path string) (time.Time, error) {
	finfo, err := os.Stat(path)
	if err != nil {
		return time.Time{}, err
	}
	stat, ok := finfo.Sys().(*syscall.Win32FileAttributeData)
	if !ok {
		return time.Time{}, fmt.Errorf("not a valid Win32 file info: %v", finfo.Sys())
	}

	// Convert the access time from FILETIME to time.Time
	accessTime := time.Unix(0, (stat.LastAccessTime.Nanoseconds()))
	return accessTime, nil
}

func DiskMonitor(dir string) error {
	_, free, err := GetDiskUsage(dir)
	if err != nil {
		return err
	}

	freegb := free / (1024 * 1024 * 1024)
	if freegb > LocalConfig.DiskMaxLimit {
		return nil
	}

	var files []FileWithTime
	// 遍历文件夹
	err = filepath.Walk(dir, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}
		// 只处理文件，不处理目录
		if !info.IsDir() {
			accessTime, err := getAccessTime(path)
			if err != nil {
				return err
			}

			files = append(files, FileWithTime{
				Path:       path,
				AccessTime: accessTime,
			})
		}
		return nil
	})

	if err != nil {
		return err
	}

	removeFileList.DeleteByTime(6 * 60 * 60)
	// 根据访问时间倒序排序
	sort.Sort(ByAccessTime(files))

	if _, err := toml.DecodeFile(ConfFilePath, LocalConfig); err != nil {
		return err
	}

	// 删除文件
	for _, file := range files {
		_, free, err := GetDiskUsage(dir)
		if err != nil {
			return err
		}
		freegb := free / (1024 * 1024 * 1024)
		if freegb < LocalConfig.DiskMaxLimit {
			err := os.Remove(file.Path)
			if err != nil {
				fmt.Println("Error deleting file:", err)
				continue
			}
			removeFileList.Set(file.Path, time.Now().Unix())
		} else {
			break
		}

		logger.Log.Infof("DeleteFile delete file:%s", file.Path)
	}

	return nil
}
