####################################################### 服务配置-测试环境 #######################################################
server-port = 8112
server-name = "dhlive-resource-cache"
log-file-prefix = "localhost"
log-file-path = "./logs/"
log-show-code-file = true
log-show-code-func = true

# 单位GB
disk-max-limit = 5
# consul配置
[consul-setting]
host = "127.0.0.1"
port = 8500
name = ""
# 健康检查地址
health-url = "/health"
check-timeout = "10s"
check-interval = "10s"
deregister-critical-service-after = "30s"

# 日志上传的elasticsearch配置
[log-es-setting]
host = "http://*************:8200"
username = "robot"
password = "Hi109.3"

[vis-setting]
registerUrl = "http://*************:8010/api/terminal/v1/authorization/register"

# 测试url
# activeUrl = "https://persona.baidu.com:8050/text2figure/manager/api/v1/figure/all" 

# 线上url
activeUrl = "https://xl.baidu.com/text2figure/manager/api/v1/figure/all"