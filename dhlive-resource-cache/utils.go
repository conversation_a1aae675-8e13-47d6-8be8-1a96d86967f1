package main

import (
	"fmt"
	"io/ioutil"
	"os"

	"github.com/shirou/gopsutil/disk"
)

func fileExists(filePath string) bool {
	_, err := os.Stat(filePath)
	return !os.IsNotExist(err)
}

func getFileSize(filePath string) (int64, error) {
	fileInfo, err := os.Stat(filePath)
	if err != nil {
		return 0, err
	}
	return fileInfo.Size(), nil
}

func createFolderIfNotExists(folderPath string) error {
	if _, err := os.Stat(folderPath); os.IsNotExist(err) {
		err := os.MkdirAll(folderPath, 0755)
		return err
	}
	return nil
}

func moveFile(sourcePath, destinationPath string) error {
	err := os.Rename(sourcePath, destinationPath)
	if err != nil {
		return fmt.Errorf("failed to move file: %w", err)
	}
	return nil
}

func deleteFile(filePath string) error {
	err := os.Remove(filePath)
	if err != nil {
		return fmt.Errorf("failed to delete file: %w", err)
	}
	return nil
}

func getFileNamesInFolder(folderPath string) (map[string]struct{}, error) {
	files, err := ioutil.ReadDir(folderPath)
	if err != nil {
		return nil, err
	}

	var fileNames = make(map[string]struct{})
	for _, file := range files {
		if !file.IsDir() {
			fileNames[file.Name()] = struct{}{}
		}
	}

	return fileNames, nil
}

// GetDiskUsage 获取指定路径的磁盘使用情况
func GetDiskUsage(path string) (total, free uint64, err error) {
	var usageStat *disk.UsageStat
	usageStat, err = disk.Usage(path)
	if err != nil {
		return 0, 0, err
	}

	return usageStat.Total, usageStat.Free, err
}
