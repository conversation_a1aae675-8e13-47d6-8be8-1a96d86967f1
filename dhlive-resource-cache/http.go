package main

import (
	"acg-ai-go-common/logger"
	"encoding/json"
	"errors"
	"io"
	"net/http"
	"os"
	"strings"
	"time"
)

type FileListHttp struct {
	downloadFileList []string // 下载文件列表
	downloadPath     string   // 下载路径
	tmpDownloadPath  string   // 临时下载路径
}

func (f *FileListHttp) initDownloadFileList() {
	f.downloadFileList = make([]string, 0)
	f.downloadFileList = append(f.downloadFileList, "https://text2figure.cdn.bcebos.com/figure/F_Face143.bin")
	f.downloadFileList = append(f.downloadFileList, "https://text2figure.cdn.bcebos.com/figure/F_Face130.bin")
	f.downloadFileList = append(f.downloadFileList, "https://text2figure.cdn.bcebos.com/figure/F_Face151.bin")
	f.downloadFileList = append(f.downloadFileList, "https://text2figure.cdn.bcebos.com/figure/F_Face141.bin")
	f.downloadFileList = append(f.downloadFileList, "https://text2figure.cdn.bcebos.com/figure/F_Face134.bin")
	f.downloadFileList = append(f.downloadFileList, "https://text2figure.cdn.bcebos.com/figure/F_Face131.bin")
}

func (f *FileListHttp) GetFileList(fileListUrl string) (bool, error) {
	f.initDownloadFileList()
	// 发起HTTP请求获取文件列表
	reqBody := strings.NewReader("") // 创建一个空的请求体

	req, err := http.NewRequest("POST", fileListUrl, reqBody)
	if err != nil {
		logger.Log.Errorf("GetFileList creating request failed: %v", err)
		return false, errors.New("GetFileList creating request failed. err:" + err.Error())
	}

	// 添加所需的 header
	req.Header.Set("Content-Type", "application/json; charset=utf-8")
	req.Header.Set("x-instance-name", "ws11LocalTest")
	resp, err := http.DefaultClient.Do(req) // 使用自定义的请求进行 HTTP 请求
	if err != nil {
		logger.Log.Errorf("GetFileList http requesting failed: %v", err)
		return false, errors.New("GetFileList http requesting failed. error:" + err.Error())
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		// logger.Log.Errorf("GetFileList http response status code: %d", resp.StatusCode)
		return false, errors.New("GetFileList http response status code:" + string(resp.StatusCode))
	}

	// // 读取文件列表
	jsonStr, err := io.ReadAll(resp.Body)
	if err != nil {
		// logger.Log.Errorf("GetFileList Error reading file list:%v", err)
		return false, errors.New("GetFileList Error reading file list:" + err.Error())
	}

	var response Response
	err = json.Unmarshal([]byte(jsonStr), &response)
	if err != nil {
		// logger.Log.Errorf("GetFileList json Unmarshal file list response: %s err: %v", jsonStr, err)
		return false, errors.New("GetFileList Error reading file list:" + err.Error())
	}

	// 解析文件列表并下载文件到tmp文件夹
	for _, item := range response.Data {
		if len(item.LoadUrl) > 0 {
			f.downloadFileList = append(f.downloadFileList, item.LoadUrl)
		}

		if item.LoadUrl != item.TrainLoadUrl && len(item.TrainLoadUrl) > 0 {
			f.downloadFileList = append(f.downloadFileList, item.TrainLoadUrl)
		}
	}

	// logger.Log.Infof("GetFileList succeed filelist number:%d", len(f.downloadFileList))
	return true, nil
}

func (f *FileListHttp) DownLoadFile() {
	// 先创建下载临时文件夹
	createFolderIfNotExists(f.tmpDownloadPath)
	// 创建copy文件夹
	createFolderIfNotExists(f.downloadPath)

	// total := len(f.downloadFileList)
	for _, fileUrl := range f.downloadFileList {
		fp := strings.Split(fileUrl, "/")
		fname := fp[len(fp)-1]

		filePath := f.tmpDownloadPath + fname
		destPath := f.downloadPath + fname

		// 判断存在
		_, ok := removeFileList.Get(destPath)
		if ok {
			removeFileList.Set(destPath, time.Now().Unix())
			continue
		}
		// 判断文件是否存在
		exists := fileExists(destPath)
		if exists {
			fsize, _ := getFileSize(destPath)

			if fsize > 0 {
				continue
			}
		}

		// 不存在或者文件大小为0
		// 下载文件到tmp路径下
		resp, err := http.Get(fileUrl)
		if err != nil {
			logger.Log.Errorf("DownLoadFile http Get fail, file url:%s err:%v", fileUrl, err)
			continue
		}
		defer resp.Body.Close()

		out, err := os.Create(filePath)
		if err != nil {
			logger.Log.Errorf("DownLoadFile create file path fail,filePath:%s err:%v", filePath, err)
			continue
		}
		defer out.Close()

		_, err = io.Copy(out, resp.Body)
		if err != nil {
			logger.Log.Errorf("DownLoadFile download fail,file url:%s err:%v", fileUrl, err)
			continue
		}
		out.Close()
		// 删除临时文件
		defer os.Remove(filePath)

		f.MoveFile(fname)
		// logger.Log.Infof("DownLoadFile download succeed,index: %d / %d file url:%s", i, total, fileUrl)
	}
}

func (f *FileListHttp) MoveFile(filename string) (bool, error) {
	filePath := f.tmpDownloadPath + filename
	destPath := f.downloadPath + filename
	exists := fileExists(filePath)
	if !exists {
		logger.Log.Errorf("DownLoadFileCopy fileExists is null,filePath:%s", filePath)
		return false, nil
	}

	fsize, _ := getFileSize(filePath)

	if fsize <= 0 {
		logger.Log.Errorf("DownLoadFileCopy getFileSize is 0,filePath:%s", filePath)
		return false, nil
	}

	exists = fileExists(destPath)
	if exists {
		deleteFile(destPath)
	}

	err := moveFile(filePath, destPath)
	if err != nil {
		logger.Log.Errorf("DownLoadFileCopy move File fail,filePath:%s err:%v", filePath, err)
		return false, err
	}

	// logger.Log.Infof("DownLoadFileCopy move succeed,file:%s", destPath)

	return true, nil
}

func (f *FileListHttp) RemoveRedundantFile() {
	filemap, err := getFileNamesInFolder(f.downloadPath)
	if err != nil {
		logger.Log.Errorf("RemoveRedundantFile getFileNamesInFolder err:%v", err)
		return
	}

	downfilemap := make(map[string]struct{})
	for _, fileUrl := range f.downloadFileList {
		fp := strings.Split(fileUrl, "/")
		fname := fp[len(fp)-1]
		downfilemap[fname] = struct{}{}
	}

	for filename, _ := range filemap {
		_, ok := downfilemap[filename]
		if ok {
			continue
		}
		os.Remove(f.downloadPath + filename)
	}
}
