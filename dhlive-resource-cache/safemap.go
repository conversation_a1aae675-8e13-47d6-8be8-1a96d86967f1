package main

import (
	"sync"
	"time"
)

type SafeMap struct {
	m map[string]interface{}
	l sync.RWMutex
}

func NewSafeMap() *SafeMap {
	return &SafeMap{m: make(map[string]interface{})}
}

func (s *SafeMap) Get(key string) (interface{}, bool) {
	s.l.RLock()
	defer s.l.RUnlock()
	v, ok := s.m[key]
	return v, ok
}

func (s *SafeMap) Set(key string, value interface{}) {
	s.l.Lock()
	defer s.l.Unlock()
	s.m[key] = value
}

func (s *SafeMap) Delete(key string) {
	s.l.Lock()
	defer s.l.Unlock()
	delete(s.m, key)
}

func (s *SafeMap) DeleteByTime(interval int64) {
	s.l.Lock()
	defer s.l.Unlock()
	nowtime := time.Now().Unix()
	for k, v := range s.m {
		if (nowtime - int64(v.(int))) > interval { // 删除过期数据
			delete(s.m, k)
		}
	}
}
