package main

const (
	BosAk       = "ALTAKxlhy9EA7cNHOJyhKeLyV9"
	BosSk       = "32916cee049a41fc8224d9717cad6271"
	BosEndpoint = "bj.bcebos.com"
	BosBucket   = "abcrobot-software"
)

var (
	LocalConfig = &localConfig{}
)

type localConfig struct {
	DiskMaxLimit uint64      `toml:"disk-max-limit"`
	VisSetting   *visSetting `toml:"vis-setting"`
}

// visSetting Vis相关配置
type visSetting struct {
	RegisterURL string `toml:"registerUrl"` // 项目注册修改地址
	ActiveURL   string `toml:"activeUrl"`   // 2D sdk license激活修改地址
}
