Global:
    tool: build_submitter
Default:
    profile: [change]
Profiles:
    - profile:
      name: change
      env: DECK_CENTOS6U3_K3
      command: sh -x ./build/ci_build.sh test
      cache:
        enable: false
      release: false
    - profile:
      name: branch
      env: DECK_CENTOS6U3_K3
      command: sh -x ./build/ci_build.sh package
      release: true
      cache:
        enable: false
    - profile:
      name: master
      env: DECK_CENTOS6U3_K3
      command: sh -x ./build/ci_build.sh verify
      cache:
        enable: false
      release: true
    - profile:
      name: mvn_deploy
      env: DECK_CENTOS6U3_K3
      command: sh ./build/ci_deploy.sh
      cache:
        enable: false
      release: false
    - profile:
      name: reinforce
      env: DECK_CENTOS6U3_K3
      command: sh -x ./build/ci_build.sh verify private
      cache:
        enable: false
      release: true
    - profile:
      name: aarch64
      env: AIS-ARM-FT2000-KYLIN-V10
      command: sh -x ./build/ci_build.sh package
      release: true