// Copyright (C) 2019 Baidu Inc. All rights reserved.
package com.baidu.acg.piat.digitalhuman.common.queue;

import com.baidu.acg.piat.digitalhuman.common.model.Response;
import com.baidu.acg.piat.digitalhuman.common.queue.model.AcquireResult;
import com.baidu.acg.piat.digitalhuman.common.queue.model.StrategyRequest;
import java.util.List;
import retrofit2.Call;
import retrofit2.http.Body;
import retrofit2.http.POST;

/**
 * QueueService
 *
 * <AUTHOR>
 * @since 2019-07-29
 */
public interface QueueStrategyService {

    @POST("/api/digitalhuman/queue/acquire")
    Call<Response<List<AcquireResult>>> acquire(@Body StrategyRequest request);


    @POST("/api/digitalhuman/queue/status")
    Call<Response<List<AcquireResult>>> status(@Body StrategyRequest request);

    @POST("/api/digitalhuman/queue/cancel")
    Call<Response<Void>> cancel(@Body StrategyRequest request);

    @POST("/api/digitalhuman/queue/poll")
    Call<Response<List<AcquireResult>>> poll(@Body StrategyRequest request);
}
