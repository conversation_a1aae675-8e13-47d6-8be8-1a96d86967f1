package com.baidu.acg.piat.digitalhuman.common.llmrole;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class CreateKnowledge {
    String name;

    @JsonProperty("resource_type")
    private String resourceType;


    @JsonProperty("resource_info")
    private ResourceInfo resourceInfo;

    @Data
    public static class ResourceInfo {
        @JsonProperty("bes_endpoint")
        private String besEndpoint = "http://100.66.165.157:8200";

        @JsonProperty("bes_admin_name")
        private String besAdminName = "superuser";

        @JsonProperty("bes_password")
        private String besPassword = "dh@2025!";
    }
}
