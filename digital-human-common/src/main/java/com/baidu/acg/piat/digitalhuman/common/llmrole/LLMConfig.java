package com.baidu.acg.piat.digitalhuman.common.llmrole;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

import java.util.List;
import java.util.Map;

import com.baidu.acg.piat.digitalhuman.common.llmdm.ToolConfig;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;

/**
 * <AUTHOR> @since 2023/11/23 14:59
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class LLMConfig {
    private String botType;
    private String botId;
    private String name;
    private String url;

    public int getTimeout() {
        if (timeout < 2) {
            return 20;
        } else {
            return timeout;
        }
    }

    /**
     * 默认20s没返回就取消
     */
    private int timeout = 20;
    private boolean interrupted = false;
    private boolean interruptedBeforeQuery = true;
    private Map<String, String> credential;
    private Map<String, Object> params;

    private List<ToolConfig> postActions;
    private List<ToolConfig> preActions;
    private RecommendConfig recommendConfig;

    @Data
    public static class RecommendConfig {
        // widget drml;
        /*
                "guide": {
          "text": "现在有⼀份百度云智⼤会专享福利请您查收",
          "linkUrl": "http://www.baidu.com?k1=v1&amp;amp;k2=v2",
          "imgUrl": "aaaa"
        }
         */
        private Guide guide;

        private List<String> sentences;

        // 推荐几个问题
        private int num = 2;

    }

}