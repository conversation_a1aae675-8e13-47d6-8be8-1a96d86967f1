// Copyright (C) 2019 Baidu Inc. All rights reserved.
package com.baidu.acg.piat.digitalhuman.common.access;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import com.baidu.acg.piat.digitalhuman.common.quota.ResourceQuota;

import com.baidu.acg.piat.digitalhuman.common.quota.ResourceQuota;
import org.apache.commons.lang3.StringUtils;

/**
 * AccessApp
 *
 * <AUTHOR>
 * @since 2019-09-18
 */
@Data
@Builder
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class AccessApp {

    private String appId;
    private String appKey;

    private String projectId;

    private String projectName;

    private String projectVersion;

    /**
     * owner userId , immutable
     */
    private String userId;

    /**
     * mutable
     */
    private String name;

    /**
     * mutable
     */
    private String description;

    /**
     * mutable
     */
    private Map<String, String> tags;

    /**
     * mutable
     */
    private ResourceQuota resourceQuota;

    /**
     * mutable
     */
    private String characterImage;

    /**
     * mutable
     */
    @Deprecated
    private Integer maxIdleInSecond;

    private String editor;

    private String createTime;

    private String updateTime;

    private boolean enabled = true;

    private int apiVersion;

    // 登录操作账户id
    private String uid;

    public boolean resourceQuotaValid() {
        if (resourceQuota == null) {
            return false;
        } else {
            return resourceQuota.valid();
        }
    }

    public static AccessApp buildCreateRequest(String userId, String name, String description,
                                               Map<String, String> tags,
                                               ResourceQuota resourceQuota) {
        return AccessApp.builder()
                .userId(userId)
                .name(name)
                .description(description)
                .tags(tags).resourceQuota(resourceQuota)
                .build();
    }

}
