// Copyright (C) 2021 Baidu Inc. All rights reserved.

package com.baidu.acg.piat.digitalhuman.common.console.video;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * VideoFailRequest
 *
 * <AUTHOR>
 * @date 2021-01-13
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class VideoFailRequest {

    @NotBlank(message = "videoId cannot be blank")
    private String id;

    @Builder.Default
    private String sessionId = "";

    /**
     * 不能重试， 则直接置失败
     */
    @NotNull(message = "canRetry cannot be null")
    @Builder.Default
    private Boolean canRetry = true;

    /**
     * 调度失败，不增加调度次数
     */
    @NotNull(message = "canRetry cannot be null")
    @Builder.Default
    private Boolean scheduleFailed = false;

    @Builder.Default
    private Integer errorCode = -1;

    private String errorMsg = "";

    private String showErrMsg;
}
