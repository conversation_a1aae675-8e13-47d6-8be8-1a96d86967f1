package com.baidu.acg.piat.digitalhuman.common.prime;

import lombok.Synchronized;

import java.util.Optional;

/**
 * 只允许一次赋值
 *
 * <AUTHOR>
 */
public class SingleAssignGuard<T> {
    private T value = null;

    /**
     * 通过这个构造函数创建的数据不允许修改
     *
     * @param initValue init value
     */
    public SingleAssignGuard(T initValue) {
        this.value = initValue;
    }

    public SingleAssignGuard() {
    }

    /**
     * @param value expected value
     * @return true set success; false set failed;
     */
    @Synchronized
    public boolean set(T value) {
        if (this.value == null) {
            this.value = value;
            return true;
        }
        return false;
    }

    /**
     * <pre>
     *     应该要进行深拷贝，避免获取到引用之后对这个值得修改。但没有类型信息
     *     泛型深拷贝参考：https://www.baeldung.com/java-deep-copy
     *     这里没有这么实现，为保证逻辑正确，外面不能用返回引用修改数据
     * </pre>
     *
     * @return optional value
     */
    @Synchronized
    public Optional<T> get() {
        return Optional.ofNullable(this.value);
    }

    @Synchronized
    public T getOrDefault(T defaultValue) {
        return this.value == null ? defaultValue : value;
    }
}
