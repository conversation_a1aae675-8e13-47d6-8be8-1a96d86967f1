package com.baidu.acg.piat.digitalhuman.common.resource;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.net.InetAddress;
import java.net.UnknownHostException;
import java.util.Map;

import javax.annotation.PostConstruct;

import com.baidu.acg.piat.digitalhuman.common.config.ResourceInstanceConfig;
import com.baidu.acg.piat.digitalhuman.common.cloud.Selectors;

/**
 * Describe information of rtc resource instance, including instance ip address and service ports.
 *
 * <AUTHOR>
 */
@Data
@Slf4j
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ResourceInstance {

    // not only as identifier but also api calling host
    private String host;

    private String ip;

    private String resourceType;

    private Map<String, Integer> ports;

    private Selectors.Labels labels;

    public ResourceInstance(ResourceInstanceConfig config) {
        this.ip = config.getIp();
        this.resourceType = config.getResourceType();
        this.ports = config.getPorts();
        this.labels = config.getLabels();
    }

    @PostConstruct
    public void init() throws UnknownHostException, ResourceInstanceInitializeException {
        ip = StringUtils.isBlank(ip) ? InetAddress.getLocalHost().getHostAddress() : ip;
        checkPort("host");
        host = "http://" + ip + ":" + ports.get("host");

        log.info("Initialized resource instance, host={} ports={}", host, ports);
    }

    private void checkPort(String portName) throws ResourceInstanceInitializeException {
        if (!ports.containsKey(portName)) {
            throw new ResourceInstanceInitializeException("instance should config "
                    + portName + " port in resource instance ports");
        }
    }
}
