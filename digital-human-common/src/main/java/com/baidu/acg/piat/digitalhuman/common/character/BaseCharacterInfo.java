package com.baidu.acg.piat.digitalhuman.common.character;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(value = JsonInclude.Include.NON_NULL)
public class BaseCharacterInfo {

    private String id;

    private String name;

    private String picUrl;

    private long duration;

    private List<Children> children;

    private Map<Integer, String> cameraBasedPics;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public static class Children {

        private String id;
        private String type;
        private String name;

        // 滑动条配置
        private String start;
        private String end;
        private Double startVal;
        private Double endVal;
        private Double initVal;

        // 单选项配置
        private List<String> data;
        private Boolean canCustomize = false;
        private String picUrl;
    }

    public BaseCharacterInfo(String id, String name) {
        this.id = id;
        this.name = name;
    }
}
