package com.baidu.acg.piat.digitalhuman.common.llmdm;

import java.util.Date;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 *
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TextRenderBody extends BaseRequestBody {

    String msgId;

    int seq;

    private boolean completed;

    private String text;

    private boolean interruptible;

    public static TextRenderBody toBuild(String requestId, String text, boolean completed,
                                         int seq, String sessionId, boolean interruptible) {
        TextRenderBody textRenderBody = TextRenderBody
                .builder()
                .text(text)
                .seq(seq)
                .completed(completed)
                .interruptible(interruptible)
                .msgId(requestId + "_" + seq)
                .build();
        textRenderBody.setSessionId(sessionId);
        textRenderBody.setSt(new Date());
        return textRenderBody;
    }
}
