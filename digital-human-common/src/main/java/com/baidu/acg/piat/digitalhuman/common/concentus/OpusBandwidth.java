/* Copyright (c) 2007-2008 CSIRO
   Copyright (c) 2007-2011 Xiph.Org Foundation
   Originally written by <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>,
   <PERSON>, and the Opus open-source contributors
   Ported to Java by <PERSON>

   Redistribution and use in source and binary forms, with or without
   modification, are permitted provided that the following conditions
   are met:

   - Redistributions of source code must retain the above copyright
   notice, this list of conditions and the following disclaimer.

   - Redistributions in binary form must reproduce the above copyright
   notice, this list of conditions and the following disclaimer in the
   documentation and/or other materials provided with the distribution.

   - Neither the name of Internet Society, IETF or IETF Trust, nor the
   names of specific contributors, may be used to endorse or promote
   products derived from this software without specific prior written
   permission.

   THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
   ``AS IS'' AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
   LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
   A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER
   OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL,
   EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO,
   PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR
   PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF
   LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING
   NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
   SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 */
package com.baidu.acg.piat.digitalhuman.common.concentus;

public enum OpusBandwidth {
    OPUS_BANDWIDTH_UNKNOWN,
    OPUS_BANDWIDTH_AUTO,
    OPUS_BANDWIDTH_NARROWBAND,
    OPUS_BANDWIDTH_MEDIUMBAND,
    OPUS_BANDWIDTH_WIDEBAND,
    OPUS_BANDWIDTH_SUPERWIDEBAND,
    OPUS_BANDWIDTH_FULLBAND
}

// Helpers to port over uses of OpusBandwidth as an integer
class OpusBandwidthHelpers {

    static int GetOrdinal(OpusBandwidth bw) {
        switch (bw) {
            case OPUS_BANDWIDTH_NARROWBAND:
                return 1;
            case OPUS_BANDWIDTH_MEDIUMBAND:
                return 2;
            case OPUS_BANDWIDTH_WIDEBAND:
                return 3;
            case OPUS_BANDWIDTH_SUPERWIDEBAND:
                return 4;
            case OPUS_BANDWIDTH_FULLBAND:
                return 5;
        }

        return -1;
    }

    static OpusBandwidth GetBandwidth(int ordinal) {
        switch (ordinal) {
            case 1:
                return OpusBandwidth.OPUS_BANDWIDTH_NARROWBAND;
            case 2:
                return OpusBandwidth.OPUS_BANDWIDTH_MEDIUMBAND;
            case 3:
                return OpusBandwidth.OPUS_BANDWIDTH_WIDEBAND;
            case 4:
                return OpusBandwidth.OPUS_BANDWIDTH_SUPERWIDEBAND;
            case 5:
                return OpusBandwidth.OPUS_BANDWIDTH_FULLBAND;
        }

        return OpusBandwidth.OPUS_BANDWIDTH_AUTO;
    }

    static OpusBandwidth MIN(OpusBandwidth a, OpusBandwidth b) {
        if (GetOrdinal(a) < GetOrdinal(b)) {
            return a;
        }
        return b;
    }

    static OpusBandwidth MAX(OpusBandwidth a, OpusBandwidth b) {
        if (GetOrdinal(a) > GetOrdinal(b)) {
            return a;
        }
        return b;
    }

    static OpusBandwidth SUBTRACT(OpusBandwidth a, int b) {
        return GetBandwidth(GetOrdinal(a) - b);
    }
}
