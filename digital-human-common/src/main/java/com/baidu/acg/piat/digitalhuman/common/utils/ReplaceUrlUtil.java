package com.baidu.acg.piat.digitalhuman.common.utils;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

@Slf4j
public class ReplaceUrlUtil {
    /**
     * @param sourceUrl          需要加载资源路径原始url
     * @param replaceSourceRegex 替换处理匹配正则表达式
     * @param replacementTarget  替换处理目标字符串
     * @return
     */
    public static String replaceSourceUrlForIntranet(String sourceUrl, String replaceSourceRegex
            , String replacementTarget) {
        if (StringUtils.isBlank(sourceUrl)) {
            return sourceUrl;
        }
        if (StringUtils.isNotBlank(replaceSourceRegex)
                && StringUtils.isNotBlank(replacementTarget)) {
            log.info("Replace replaceSourceRegex={} to replacementTarget={} for url={}"
                    , replaceSourceRegex, replacementTarget, sourceUrl);
            return sourceUrl.replaceAll(replaceSourceRegex, replacementTarget);
        }
        return sourceUrl;
    }
}
