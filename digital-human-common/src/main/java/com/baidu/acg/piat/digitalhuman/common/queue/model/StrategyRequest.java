// Copyright (C) 2019 Baidu Inc. All rights reserved.
package com.baidu.acg.piat.digitalhuman.common.queue.model;

import com.baidu.acg.piat.digitalhuman.common.auth.model.UserInfo;
import java.util.List;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * QueryInfo
 *
 * <AUTHOR>
 * @since 2019-07-24
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class StrategyRequest {
    @Builder.Default
    private String requestId = UUID.randomUUID().toString();
    private List<UserInfo> userInfos;
}
