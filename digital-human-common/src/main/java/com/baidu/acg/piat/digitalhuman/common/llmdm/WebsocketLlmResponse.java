package com.baidu.acg.piat.digitalhuman.common.llmdm;

import java.util.UUID;

import javax.annotation.Nullable;

import com.baidu.acg.piat.digitalhuman.common.alita.ActionType;
import com.fasterxml.jackson.databind.node.ObjectNode;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


/**
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WebsocketLlmResponse {
    private String requestId;

    private int code;

    private String message;

    private ActionType action;

    private Object body = null;

    @Override
    public String toString() {
        String body = this.body != null ? this.body.toString() : "";
        return String.format("{'requestId': %s, 'action':%s, 'body':%s}",
                requestId, action, body);
    }


    public WebsocketLlmResponse(WebsocketLlmRequest requestMessage){
        this(requestMessage, null);
    }

    public WebsocketLlmResponse(WebsocketLlmRequest requestMessage, ObjectNode body){
        this.requestId = requestMessage.getRequestId();
        this.action = requestMessage.getAction();
        this.body = body;
    }

    public static WebsocketLlmResponse success(WebsocketLlmRequest request) {
        return success(request, null);
    }

    public static WebsocketLlmResponse success(WebsocketLlmRequest request, Object body) {
        return WebsocketLlmResponse.builder().requestId(request.getRequestId())
                .action(request.getAction())
                .message("ok")
                .body(body)
                .build();
    }

    public static WebsocketLlmResponse success(String requestId, ActionType action, @Nullable String body) {
        return WebsocketLlmResponse.builder().requestId(requestId)
                .action(action)
                .message("ok")
                .body(body)
                .build();
    }


    public static WebsocketLlmResponse fail(WebsocketLlmRequest request, int code, String message) {
        return WebsocketLlmResponse.builder().requestId(request.getRequestId())
                .code(code)
                .action(request.getAction())
                .message(message)
                .build();
    }

    public static WebsocketLlmResponse fail(WebsocketLlmRequest request, Error error) {
        return WebsocketLlmResponse.builder().requestId(request.getRequestId())
                .action(request.getAction())
                .message(error.getMessage())
                .build();
    }

    public static WebsocketLlmResponse fail(ActionType action, int code, String message) {
        return WebsocketLlmResponse.builder().requestId(UUID.randomUUID().toString())
                .action(action)
                .code(code)
                .message(message)
                .build();
    }

    public static WebsocketLlmResponse fail(String requestId, ActionType action, int code, String message) {
        return WebsocketLlmResponse.builder().requestId(requestId)
                .action(action)
                .code(code)
                .message(message)
                .build();
    }

}
