package com.baidu.acg.piat.digitalhuman.common.utils;

import lombok.extern.slf4j.Slf4j;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.TimeZone;

/**
 * 日期工具类
 */
@Slf4j
public final class DateUtil {

    public static final TimeZone UTC = TimeZone.getTimeZone("UTC");
    public static final SimpleDateFormat FORMAT_UTC = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss'Z'");

    static {
        FORMAT_UTC.setTimeZone(UTC);
    }

    public static String fmt(long dateInMs) {
        return dateInMs <= 0 ? "0" : FORMAT_UTC.format(new Date(dateInMs));
    }

    public static String fmt(Date date) {
        if (date == null) {
            return "0";
        }
        return FORMAT_UTC.format(date);
    }


    /**
     * 当前时间（UTC）
     *
     * @return 当前时间（UTC）
     */
    public static Calendar nowInUtc() {
        return Calendar.getInstance(UTC);
    }

    public static Calendar addHourUtc(Date date, int m) {
        Calendar cal = Calendar.getInstance(TimeZone.getTimeZone("UTC"));
        cal.setTime(date);
        cal.add(Calendar.HOUR, m);
        return cal;
    }

}
