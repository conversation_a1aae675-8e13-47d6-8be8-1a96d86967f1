package com.baidu.acg.piat.digitalhuman.common.entity;

import java.io.IOException;
import java.time.ZonedDateTime;
import java.util.List;

import javax.persistence.Access;
import javax.persistence.AccessType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Transient;
import javax.persistence.Version;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;
import org.hibernate.annotations.UpdateTimestamp;

import com.baidu.acg.piat.digitalhuman.common.character.BaseCharacterInfo;
import com.baidu.acg.piat.digitalhuman.common.utils.JsonUtil;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;
import org.hibernate.annotations.UpdateTimestamp;

import javax.persistence.Access;
import javax.persistence.AccessType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Transient;
import javax.persistence.Version;
import java.io.IOException;
import java.time.ZonedDateTime;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Slf4j
@Builder
@Accessors(chain = true)
@DynamicInsert
@DynamicUpdate
@Entity(name = "character_meta")
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class CharacterModel {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    private String characterId;

    private String type;

    private String name;

    private String figureAlias;

    private String figureName;

    private String description;

    private String style;

    /**
     * 用户上传的基础形象文件的url
     */
    private String resourceUrl;

    private String md5;

    /**
     * ar基础包的url
     */
    private String baseArUrl;

    private String baseArMd5;

    @Builder.Default
    private int supportCallback = 0;

    @Builder.Default
    private int supportRtcDatachannel = 0;

    @Builder.Default
    private int visibleForSce = 0;

    /**
     * 对于直播模块可见
     */
    @Builder.Default
    private int visibleForLive = 0;

    /**
     * 是否支持人像面向前方
     */
    @Builder.Default
    private int enableLookFront = 0;

    /*
     * 用于热部署判断是否需要自动更新
     */
    @Builder.Default
    @JsonIgnoreProperties(ignoreUnknown = true)
    private int deployVersion = -1;

    /**
     * 是否支持vis动捕
     */
    private int visibleForVisMocap = 0;

    private String label;

    /**
     * System default appId and appKey, only for sce
     */
    private String appId;

    private String appKey;

    private int apiVersion;

    private String configSchema;

    // used to help frontend distinguish different entry page
    private Mode configMode;

    /**
     * 智能动作表情配置
     */
    private String animojiEmotionConfig;

    @Transient
    private List<BaseCharacterInfo> ttsList;

    @Transient
    private List<BaseCharacterInfo> cameraList;

    @Transient
    private List<BaseCharacterInfo> hairStyleList;

    @Transient
    private List<BaseCharacterInfo> clothingStyleList;

    @Transient
    private List<BaseCharacterInfo> badgeStyleList;

    @Transient
    private List<BaseCharacterInfo> shoeStyleList;

    @Transient
    private List<BaseCharacterInfo> animojiList;

    @Transient
    private List<BaseCharacterInfo> emotionList;

    @Transient
    private List<BaseCharacterInfo> a2aEmotionList;

    @Transient
    private List<BaseCharacterInfo> facialList;

    @Transient
    private List<BaseCharacterInfo> makeupList;

    @Transient
    private List<BaseCharacterInfo> sceneList;

    /**
     * 默认前景图
     */
    private String frontImageUrl;

    /**
     * 默认mask
     */
    private String maskImageUrl;

    /**
     * 默认背景图
     */
    private String backgroundImageUrl;

    private String thumbnailImageUrl;

    @JsonIgnore
    @CreationTimestamp
    @Column(insertable = false, updatable = false)
    private ZonedDateTime createTime;

    @JsonIgnore
    @UpdateTimestamp
    @Column(insertable = false, updatable = false)
    private ZonedDateTime updateTime;

    @Version
    private Integer version;

    @Transient
    private String token;

    @Column(name = "tts_list")
    @Access(AccessType.PROPERTY)
    public String getTtsListString() {
        if (!CollectionUtils.isEmpty(ttsList)) {
            try {
                return JsonUtil.writeValueAsString(ttsList);
            } catch (JsonProcessingException e) {
                log.error("Parse tts list to json failed.", e);
            }
        }
        return StringUtils.EMPTY;
    }

    public void setTtsListString(String ttsListString) {
        if (StringUtils.isNotEmpty(ttsListString)) {
            try {
                this.ttsList = JsonUtil.readValue(ttsListString, new TypeReference<List<BaseCharacterInfo>>() {
                });
            } catch (IOException e) {
                log.error("Exception when parse tts list from json string, string={}.", ttsListString, e);
            }
        } else {
            this.ttsList = null;
        }
    }

    @Column(name = "camera_list")
    @Access(AccessType.PROPERTY)
    public String getCameraListString() {
        if (!CollectionUtils.isEmpty(cameraList)) {
            try {
                return JsonUtil.writeValueAsString(cameraList);
            } catch (JsonProcessingException e) {
                log.error("Parse hair style list to json failed.", e);
            }
        }
        return StringUtils.EMPTY;
    }

    public void setCameraListString(String cameraListString) {
        if (StringUtils.isNotEmpty(cameraListString)) {
            try {
                this.cameraList = JsonUtil.readValue(cameraListString, new TypeReference<List<BaseCharacterInfo>>() {
                });
            } catch (IOException e) {
                log.error("Exception when parse camera list from json string, string={}.", cameraListString, e);
            }
        } else {
            this.cameraList = null;
        }
    }

    @Column(name = "scene_list")
    @Access(AccessType.PROPERTY)
    public String getSceneListString() {
        if (!CollectionUtils.isEmpty(sceneList)) {
            try {
                return JsonUtil.writeValueAsString(sceneList);
            } catch (JsonProcessingException e) {
                log.error("Parse scene list to json failed.", e);
            }
        }
        return StringUtils.EMPTY;
    }

    public void setSceneListString(String sceneListString) {
        if (StringUtils.isNotEmpty(sceneListString)) {
            try {
                this.sceneList = JsonUtil.readValue(sceneListString, new TypeReference<List<BaseCharacterInfo>>() {
                });
            } catch (IOException e) {
                log.error("Exception when parse scene list from json string, string={}.", sceneListString, e);
            }
        } else {
            this.sceneList = null;
        }
    }

    @Column(name = "facial_list")
    @Access(AccessType.PROPERTY)
    public String getFacialListString() {
        if (!CollectionUtils.isEmpty(facialList)) {
            try {
                return JsonUtil.writeValueAsString(facialList);
            } catch (JsonProcessingException e) {
                log.error("Parse facial list to json failed.", e);
            }
        }
        return StringUtils.EMPTY;
    }

    public void setFacialListString(String facialListString) {
        if (StringUtils.isNotEmpty(facialListString)) {
            try {
                this.facialList = JsonUtil.readValue(facialListString, new TypeReference<List<BaseCharacterInfo>>() {
                });
            } catch (IOException e) {
                log.error("Exception when parse facial list from json string, string={}.", facialListString, e);
            }
        } else {
            this.facialList = null;
        }
    }

    @Column(name = "makeup_list")
    @Access(AccessType.PROPERTY)
    public String getMakeupListString() {
        if (!CollectionUtils.isEmpty(makeupList)) {
            try {
                return JsonUtil.writeValueAsString(makeupList);
            } catch (JsonProcessingException e) {
                log.error("Parse makeup style list to json failed.", e);
            }
        }
        return StringUtils.EMPTY;
    }

    public void setMakeupListString(String makeupListString) {
        if (StringUtils.isNotEmpty(makeupListString)) {
            try {
                this.makeupList = JsonUtil.readValue(makeupListString, new TypeReference<List<BaseCharacterInfo>>() {
                });
            } catch (IOException e) {
                log.error("Exception when parse makeup list from json string, string={}.", makeupListString, e);
            }
        } else {
            this.makeupList = null;
        }
    }

    @Column(name = "hair_style_list")
    @Access(AccessType.PROPERTY)
    public String getHairStyleString() {
        if (!CollectionUtils.isEmpty(hairStyleList)) {
            try {
                return JsonUtil.writeValueAsString(hairStyleList);
            } catch (JsonProcessingException e) {
                log.error("Parse hair style list to json failed.", e);
            }
        }
        return StringUtils.EMPTY;
    }

    public void setHairStyleString(String hairStyle) {
        if (StringUtils.isNotEmpty(hairStyle)) {
            try {
                this.hairStyleList = JsonUtil.readValue(hairStyle, new TypeReference<List<BaseCharacterInfo>>() {
                });
            } catch (IOException e) {
                log.error("Exception when parse hair style list from json string, string={}.", hairStyle, e);
            }
        } else {
            this.hairStyleList = null;
        }
    }

    @Column(name = "clothing_style_list")
    @Access(AccessType.PROPERTY)
    public String getClothingStyleString() {
        if (!CollectionUtils.isEmpty(clothingStyleList)) {
            try {
                return JsonUtil.writeValueAsString(clothingStyleList);
            } catch (JsonProcessingException e) {
                log.error("Parse clothing style list to json failed.", e);
            }
        }
        return StringUtils.EMPTY;
    }

    public void setClothingStyleString(String clothingStyle) {
        if (StringUtils.isNotEmpty(clothingStyle)) {
            try {
                this.clothingStyleList = JsonUtil.readValue(clothingStyle,
                        new TypeReference<List<BaseCharacterInfo>>() {
                        });
            } catch (IOException e) {
                log.error("Exception when parse clothing style from json string, string={}.", clothingStyle, e);
            }
        } else {
            this.clothingStyleList = null;
        }
    }

    @Column(name = "badge_style_list")
    @Access(AccessType.PROPERTY)
    public String getBadgeStyleString() {
        if (!CollectionUtils.isEmpty(badgeStyleList)) {
            try {
                return JsonUtil.writeValueAsString(badgeStyleList);
            } catch (JsonProcessingException e) {
                log.error("Parse badge style list to json failed.", e);
            }
        }
        return StringUtils.EMPTY;
    }

    public void setBadgeStyleString(String badgeStyle) {
        if (StringUtils.isNotEmpty(badgeStyle)) {
            try {
                this.badgeStyleList = JsonUtil.readValue(badgeStyle, new TypeReference<List<BaseCharacterInfo>>() {
                });
            } catch (IOException e) {
                log.error("Exception when parse badge style list from json string, string={}.", badgeStyle, e);
            }
        } else {
            this.badgeStyleList = null;
        }
    }

    @Column(name = "shoe_style_list")
    @Access(AccessType.PROPERTY)
    public String getShoeStyleString() {
        if (!CollectionUtils.isEmpty(shoeStyleList)) {
            try {
                return JsonUtil.writeValueAsString(shoeStyleList);
            } catch (JsonProcessingException e) {
                log.error("Parse shoe style list to json failed.", e);
            }
        }
        return StringUtils.EMPTY;
    }

    public void setShoeStyleString(String shoeStyle) {
        if (StringUtils.isNotEmpty(shoeStyle)) {
            try {
                this.shoeStyleList = JsonUtil.readValue(shoeStyle, new TypeReference<List<BaseCharacterInfo>>() {
                });
            } catch (IOException e) {
                log.error("Exception when parse shoe style list from json string, string={}.", shoeStyle, e);
            }
        } else {
            this.shoeStyleList = null;
        }
    }

    @Column(name = "animoji_list")
    @Access(AccessType.PROPERTY)
    public String getAnimojiListString() {
        if (!CollectionUtils.isEmpty(animojiList)) {
            try {
                return JsonUtil.writeValueAsString(animojiList);
            } catch (JsonProcessingException e) {
                log.error("Parse animoji list to json failed.", e);
            }
        }
        return StringUtils.EMPTY;
    }

    public void setAnimojiListString(String animojiList) {
        if (StringUtils.isNotEmpty(animojiList)) {
            try {
                this.animojiList = JsonUtil.readValue(animojiList, new TypeReference<List<BaseCharacterInfo>>() {
                });
            } catch (IOException e) {
                log.error("Exception when parse animoji list from json string, string={}.", animojiList, e);
            }
        } else {
            this.animojiList = null;
        }
    }

    @Column(name = "emotion_list")
    @Access(AccessType.PROPERTY)
    public String getEmotionListString() {
        if (!CollectionUtils.isEmpty(emotionList)) {
            try {
                return JsonUtil.writeValueAsString(emotionList);
            } catch (JsonProcessingException e) {
                log.error("Parse emotion list to json failed.", e);
            }
        }
        return StringUtils.EMPTY;
    }

    public void setEmotionListString(String emotionListString) {
        if (StringUtils.isNotEmpty(emotionListString)) {
            try {
                this.emotionList = JsonUtil.readValue(emotionListString,
                        new TypeReference<List<BaseCharacterInfo>>() {
                        });
            } catch (IOException e) {
                log.error("Exception when parse emotion list from json string, string={}.", emotionList, e);
            }
        } else {
            this.emotionList = null;
        }
    }


    public void setA2aEmotionListString(String a2aEmotionListString) {
        if (StringUtils.isNotEmpty(a2aEmotionListString)) {
            try {
                this.a2aEmotionList = JsonUtil.readValue(a2aEmotionListString,
                        new TypeReference<List<BaseCharacterInfo>>() {
                        });
            } catch (IOException e) {
                log.error("Exception when parse a2a emotion list from json string, string={}.", a2aEmotionList, e);
            }
        } else {
            this.a2aEmotionList = null;
        }
    }


    @Column(name = "a2a_emotion_list")
    @Access(AccessType.PROPERTY)
    public String getA2aEmotionListString() {
        if (!CollectionUtils.isEmpty(a2aEmotionList)) {
            try {
                return JsonUtil.writeValueAsString(a2aEmotionList);
            } catch (JsonProcessingException e) {
                log.error("Parse a2a emotion list to json failed.", e);
            }
        }
        return StringUtils.EMPTY;
    }

    public enum Mode {
        CHARACTER_CONFIG, CHARACTER_FACTORY
    }

    public static final String USER_VISIBLE_ALL_FLAG = "all";
}
