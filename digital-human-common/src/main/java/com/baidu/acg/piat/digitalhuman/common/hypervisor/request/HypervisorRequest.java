// Copyright (C) 2021 Baidu Inc. All rights reserved.
package com.baidu.acg.piat.digitalhuman.common.hypervisor.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.NonNull;

import com.baidu.acg.piat.digitalhuman.common.hypervisor.WebRtcProcessArguments;

/**
 * Hypervisor request.
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class HypervisorRequest {

    @NonNull
    private String owner;

    /**
     * in edge rendering scenario, acquiring request needs this information to acquire the specified render-edge
     * stream on render-edge-server which may maintain multiple render-edge streams.
     * See also {@link com.baidu.acg.piat.digitalhuman.common.resource.Constants#RESOURCE_ID_KEY_IN_LABELS}
     */
    private String target;

    private WebRtcProcessArguments args;
}
