package com.baidu.acg.piat.digitalhuman.common.intelligentanimojiemotion;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;


@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class IntelligentAnimojiEmotionRequest {

    private String characterImage;

    private String characterConfigId;
    @NotNull
    @NotEmpty
    private String text;

    private boolean interruptible = true;
    @NotNull
    private String requestId;
}
