package com.baidu.acg.piat.digitalhuman.common.console.video;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@JsonIgnoreProperties(ignoreUnknown = true)
public class VideoSucceedRequest {

    private String sessionId;

    private String suffix;

    private Boolean combined = true; // Combined AV
}
