package com.baidu.acg.piat.digitalhuman.common.userfigure;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class UserFigureModel {

    private int id;

    private String userId;

    private String source;

    private String name;

    private String status;

    private String resourceLabel;

    private String templateImg;

    private String templateVideo;

    private String figureName;

    private String submitTime;

    private int consumeTime;

    private String info;

    private boolean isNew;

    private String lastUsedTime;

    private boolean collected;

    private boolean recentlyUsed;

    private boolean systemProvided;

    private String type;

    private int resolutionWidth;

    private int resolutionHeight;

    private String videoUrl;

    private String effectsThumbnail;

    private String effects;

    private String figureResult;

    private int isDelete;
}