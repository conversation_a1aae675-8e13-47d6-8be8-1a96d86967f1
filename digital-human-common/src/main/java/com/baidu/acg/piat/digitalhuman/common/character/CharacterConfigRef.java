package com.baidu.acg.piat.digitalhuman.common.character;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class CharacterConfigRef {

    private long id;

    private String businessAssetId;

    private String businessAssetName;

    private String type;

    private String characterConfigId;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;
}
