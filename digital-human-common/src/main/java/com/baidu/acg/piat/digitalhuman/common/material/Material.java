package com.baidu.acg.piat.digitalhuman.common.material;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;

/**
 * Created on 2021/8/6 11:20 上午
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@JsonIgnoreProperties(ignoreUnknown = true)
public class Material {

    private String id;

    private String userId;

    private String positionId;

    @NotBlank(message = "Material name cannot be empty")
    private String name;

    private String type;

    private String picUrl;

    private String content;

    private String drml;

    private String createTime;

    private String updateTime;

}
