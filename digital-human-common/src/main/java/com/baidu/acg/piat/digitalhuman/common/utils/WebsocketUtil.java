package com.baidu.acg.piat.digitalhuman.common.utils;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.socket.TextMessage;
import org.springframework.web.socket.WebSocketSession;

import java.io.IOException;

import com.baidu.acg.piat.digitalhuman.common.alita.websocket.WebsocketResponse;


/**
 * Created on 2020/6/12 14:12.
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class WebsocketUtil {

    public static void send(WebSocketSession session, WebsocketResponse websocketResponse) {
        log.debug("Send websocket message={} to {}", websocketResponse, session.getId());
        try {
            if (session.isOpen()) {
                session.sendMessage(new TextMessage(JsonUtil.writeValueAsString(websocketResponse)));
            }
        } catch (IOException e) {
            log.error("Unexpected write response websocket as json failed, response ={}, " +
                    "websocket={}", websocketResponse, session.getId());
        }
    }
}
