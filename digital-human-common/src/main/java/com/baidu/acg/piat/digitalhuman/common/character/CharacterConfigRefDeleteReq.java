package com.baidu.acg.piat.digitalhuman.common.character;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class CharacterConfigRefDeleteReq {
    private String businessAssetId;
    private String businessAssetName;
    private String type;
}
