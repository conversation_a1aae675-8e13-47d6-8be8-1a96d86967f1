package com.baidu.acg.piat.digitalhuman.common.alita.mocap;

import com.baidu.acg.piat.digitalhuman.common.util.DomUtil;
import com.baidu.acg.piat.digitalhuman.common.utils.JsonUtil;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.google.common.base.Strings;
import io.vavr.control.Try;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.w3c.dom.Document;
import org.w3c.dom.NodeList;

import java.util.Optional;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MocapCharge {

    private String appId;

    private String sessionId;

    private String staffId;

    private Action action;

    /**
     * 记录iphone 所连接的alita节点所在地址信息
     */
    private Service service;

    public enum Action {
        OPEN, CLOSE
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Service {

        private String ip;

        private int port;
    }

    @JsonIgnore
    public boolean isValid() {
        if (action == null) {
            return false;
        } else if (action == Action.OPEN) {
            return true;
        }
        return StringUtils.isNoneBlank(appId, sessionId, staffId) && ObjectUtils.allNotNull(service)
                && service.getIp() != null;
    }

    /**
     * 解析 动捕托管 drml
     *
     * @param xml
     * @return
     */
    public static Optional<MocapCharge> fromXml(String xml) {
        if (Strings.isNullOrEmpty(xml) || !xml.contains("mocapCharge")) {
            return Optional.empty();
        }
        Document document = DomUtil.getDocument(xml);
        NodeList mocapChargeNodeList = document.getElementsByTagName("mocapCharge");
        if (mocapChargeNodeList.getLength() == 0) {
            return Optional.empty();
        } else {
            return Try.of(() -> JsonUtil.readValue(DomUtil.xmlNode2Json(mocapChargeNodeList.item(0)),
                    MocapCharge.class)
            ).toJavaOptional();
        }
    }
}
