package com.baidu.acg.piat.digitalhuman.common.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;
import org.hibernate.validator.constraints.Range;

@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class PageRequest {

    @Range(min = 1, message = "Page no cannot less then 1!")
    private Integer pageNo = 1;

    @Range(min = 1, message = "Page size cannot less then 1!")
    private Integer pageSize = 20;

}
