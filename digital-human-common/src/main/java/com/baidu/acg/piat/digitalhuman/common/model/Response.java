// Copyright (C) 2019 Baidu Inc. All rights reserved.
package com.baidu.acg.piat.digitalhuman.common.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import com.baidu.acg.piat.digitalhuman.common.exception.Error;

/**
 * Response
 *
 * <AUTHOR>
 * @since 2019-07-11
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Response<T> extends BaseResponse {
    private T result;

    public static <T> Response<T> success(T result) {
        return new Response<>(result);
    }

    public static <T> Response<T> success() {
        return new Response<>(null);
    }

    public static <T> Response<T> fail(Error error) {
        return fail(error.getCode(), error.getMessage());
    }

    public static <T> Response<T> fail(int code, String message) {
        Response<T> response = new Response<T>();
        response.setCode(code);
        response.setSuccess(false);
        response.setMessage(new Message(message));
        return response;
    }

    public static <T> Response<T> fail(String message) {
        return fail(Error.INTERNAL_SERVER_ERROR.getCode(), message);
    }

}
