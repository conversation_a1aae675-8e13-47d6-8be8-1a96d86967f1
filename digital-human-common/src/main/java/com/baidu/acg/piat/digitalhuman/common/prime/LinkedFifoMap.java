package com.baidu.acg.piat.digitalhuman.common.prime;

import java.util.LinkedHashMap;
import java.util.Map;

public class LinkedFifoMap<K, V> extends LinkedHashMap<K, V> {
    private final int capacity;

    public LinkedFifoMap(int capacity) {
        super(capacity);
        this.capacity = capacity;
    }

    @Override
    protected boolean removeEldestEntry(Map.Entry entry) {
        return size() > this.capacity;
    }
}
