package com.baidu.acg.piat.digitalhuman.common.session;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;

/**
 * Simple session context.
 *
 * <AUTHOR> (maof<PERSON><PERSON>@baidu.com)
 */
@Builder
@Data
@Accessors(chain = true)
@Slf4j
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class SimpleSessionContext {

    private String appId;

    private String roomName;

    private String sessionId;

    private String leafletWsSessionId; // leaflet web socket session id.

    private ChargeStatus chargeStatus;

    private SessionStatus status;

    private String chargeToken;

    private String host;

    private Integer port;

    private Long createTime;
}
