package com.baidu.acg.piat.digitalhuman.common.hypervisor;

import com.baidu.acg.piat.digitalhuman.common.webrtc.WebRtcSession;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.NonNull;

/**
 * Web rtc process arguments.
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WebRtcProcessArguments {

    @NonNull
    private String rtcServerUrl;

    @NonNull
    private String appId;

    @NonNull
    private String roomName;

    @NonNull
    private String userId;

    @NonNull
    private String userToken;

    private List<String> feedIds;

    private String ue4Url;

    private String internalRtcPlatformUrl;

    public static WebRtcProcessArguments getAgentArguments(WebRtcSession session) {
        return WebRtcProcessArguments.builder()
                .rtcServerUrl(session.getInternalRtcServerUrl())
                .appId(session.getAppId())
                .userId(session.getAgentId())
                .userToken(session.getAgentToken())
                .feedIds(List.of(session.getClientId()))
                .roomName(session.getRoomId())
                .internalRtcPlatformUrl(session.getInternalRtcPlatformUrl())
                .build();
    }

    public static WebRtcProcessArguments getVis2dProxyArguments(WebRtcSession session) {
        return WebRtcProcessArguments.builder()
                .rtcServerUrl(session.getInternalRtcServerUrl())
                .appId(session.getAppId())
                .userId(session.getAgentId())
                .userToken(session.getAgentToken())
                .feedIds(List.of())
                .roomName(session.getRoomId())
                .internalRtcPlatformUrl(session.getInternalRtcPlatformUrl())
                .build();
    }

    public static WebRtcProcessArguments getProcesServerArguments(WebRtcSession session, String ue4Url) {
        return WebRtcProcessArguments.builder()
                .rtcServerUrl(session.getInternalRtcServerUrl())
                .appId(session.getAppId())
                .roomName(session.getRoomId())
                .userId(session.getProxyServerId())
                .userToken(session.getProxyServerToken())
                .feedIds(List.of())
                .ue4Url(ue4Url)
                .internalRtcPlatformUrl(session.getInternalRtcPlatformUrl())
                .build();
    }

    public static WebRtcProcessArguments getClientArguments(WebRtcSession session) {
        return WebRtcProcessArguments.builder()
                .rtcServerUrl(session.getExternalRtcServerUrl())
                .appId(session.getAppId())
                .roomName(session.getRoomId())
                .userId(session.getClientId())
                .userToken(session.getClientToken())
                .feedIds(List.of(session.getProxyServerId()))
                .internalRtcPlatformUrl(session.getInternalRtcPlatformUrl())
                .build();
    }
}
