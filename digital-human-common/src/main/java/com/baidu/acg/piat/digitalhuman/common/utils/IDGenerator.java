// Copyright (C) 2021 Baidu Inc. All rights reserved.

package com.baidu.acg.piat.digitalhuman.common.utils;

import lombok.Data;
import lombok.RequiredArgsConstructor;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.Random;

@RequiredArgsConstructor
public class IDGenerator {

    private Random random = new Random(new Date().getTime());
    private static final int maxLength = 20;
    private static final int maxPrefixLength = 3;

    private final IDGeneratorConfig config;

    @Data
    public static class IDGeneratorConfig {

        private Integer defaultLength = 20;

        private Integer minLength = 17;

        private String defaultPrefix = "df";

    }

    public String generate() {
        return generate(config.getDefaultPrefix(), config.getDefaultLength());
    }



    public String generate(String prefix) {
        return generate(prefix, config.getDefaultLength());
    }

    /**
     * 生成不超过20字节长度的ID,遵循公有云ID生成规范
     * “ID必须是一个长度不超过20位的定长字符串且只允许包含小写字母/数字/连字号(-)。
     * ID的开头必须是三位以上的小写字母跟一个连字号（-），要求能够有效标识ID所代表的对象。”
     * 在此规范基础上，使用了4个字符隐含时间.
     * 那么当prefix.length() = 3, total length = 20时，id中有12位用来做随机串,
     * 满足规范“随机字符串长度应足够长，一个原则是随机生成的冲突率小于1亿分之一。
     * 也就是说，所有随机字符串的总数（34^Length)需要超过系统中对象数量上限*1亿。
     * 一般情况下，我们可以简单认为12位随机字符串是够用的。”
     * <p>
     * 另外,考虑ID中4个字符隐含时间对应1个小时,那么我们随机字符串的总数(34^Length)需要超过
     * 系统中1个小时内新增对象上限 * 1亿,
     * 一般情况下,我们也可以认为在下述算法中,9位随机字符串也是够用的.
     * <p>
     * 0 1 2 | 3 | 4  |  5  | 6 | 7  |8 9 . . .  19|
     * prefix| - |year|month|day|hour|random string|
     *
     * @param prefix prefix
     * @param length id length
     * @return String
     */
    public String generate(String prefix, int length) {
        if (prefix.length() > maxPrefixLength) {
            throw new RuntimeException("id prefix too long");
        }
        if (length < config.getMinLength() || length > maxLength) {
            throw new RuntimeException("invalid id length");
        }
        StringBuilder stringBuilder = new StringBuilder(length);
        stringBuilder.append(prefix);
        stringBuilder.append('-');
        LocalDateTime now = LocalDateTime.now();
        stringBuilder.append(getCharNotOL((char) (now.getYear() - 2010 + 'a')));
        stringBuilder.append(getCharNotOL((char) (now.getMonth().ordinal() + 'a')));
        stringBuilder.append(getCharNotOL((char) (now.getDayOfMonth() > 24
                ? now.getDayOfMonth() - 25 + '0' : now.getDayOfMonth() + 'a' - 1)));
        stringBuilder.append(getCharNotOL((char) (now.getHour() + 'a')));
        return this.getLastString(stringBuilder, length - stringBuilder.length()).toString();
    }

    public String generate(int length) {
        return getLastString(new StringBuilder(), length).toString();
    }

    private char getCharNotOL(char origin) {
        if (!(origin >= 'a' && origin <= 'x') && !(origin >= '0' && origin <= '9')) {
            throw new RuntimeException("unsupported char" + origin);
        }
        if (origin >= 'l') {
            origin = (char) (origin + 1);
        }
        if (origin >= 'o') {
            origin = (char) (origin + 1);
        }
        return origin;
    }

    private StringBuilder getLastString(StringBuilder stringBuilder, int last) {
        while (true) {
            int v = random.nextInt(36);
            char c = (char) (v < 26 ? v + 'a' : v - 26 + '0');
            if (c == 'o' || c == 'l') {
                continue;
            }
            stringBuilder.append(c);
            if (--last == 0) {
                break;
            }
        }
        return stringBuilder;
    }

//    public static void main(String[] args) {
//        IDGenerator idGenerator = new IDGenerator();
//        idGenerator.defaultLength = 20;
//        idGenerator.minLength = 17;
//        for (int i = 0; i < 7; ++i) {
//            System.out.println(idGenerator.generate("").substring(1));
//        }
//    }
}
