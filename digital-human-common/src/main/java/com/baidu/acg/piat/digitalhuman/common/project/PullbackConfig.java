// Copyright (C) 2020 Baidu Inc. All rights reserved.
package com.baidu.acg.piat.digitalhuman.common.project;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;

import javax.validation.constraints.Min;

import com.baidu.acg.piat.digitalhuman.common.exception.DigitalHumanCommonException;

/**
 * PullbackConfig
 *
 * <AUTHOR> (<EMAIL>)
 */
@Data
@Slf4j
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class PullbackConfig {

    @NonNull
    @Builder.Default
    @Min(value = 0, message = "percentage cannot less then 0")
    private Integer percentage = 0;

    @NonNull
    @Builder.Default
    private String pullbackMode = PullbackMode.DIRECT_GUIDE.name();

    private String targetSkill ;

    @Builder.Default
    private List<String> enterSpeeches = new ArrayList<>(0);

    @Builder.Default
    private List<String> directGuideSpeeches = new ArrayList<>(0);

    @Builder.Default
    private List<String> enterGuideSpeeches = new ArrayList<>(0);

    @Builder.Default
    private List<String> relateSkills = new ArrayList<>(0);

    @Min(value = 0, message = "direct guide percentage cannot less then 0")
    @Builder.Default
    private Integer directGuidePercentage = 0;

    @Min(value = 0, message = "direct guide percentage cannot less then 0")
    @Builder.Default
    private Integer enterGuidePercentage = 0;

    public void check() {
        if (directGuidePercentage + enterGuidePercentage > 100) {
            throw new DigitalHumanCommonException("direct guide percentage + enter guide percentage cannot" +
                    " bigger then 100");
        }

        PullbackMode mode;
        try {
            mode = PullbackMode.valueOf(pullbackMode);
        } catch (Exception e) {
            throw new DigitalHumanCommonException("pullback model is illegal");
        }

        switch (mode) {
            case DIRECT_GUIDE:
                checkDirectGuide();
                break;
            case ENTER_GUIDE:
                checkEnterGuide();
                break;
            case PERCENTAGE_GUIDE:
                checkPercentageGuide();
                break;
            default:
                throw new DigitalHumanCommonException(String.format("pullback mode = %s unsupported",
                        pullbackMode));
        }
    }

    private void checkDirectGuide() {
        if (directGuideSpeeches.isEmpty()) {
            throw new DigitalHumanCommonException(String.format("direct guide speech cannot empty when " +
                    "mode = %s", pullbackMode));
        }

        directGuidePercentage = 100;
        enterGuidePercentage = 0;
    }

    private void checkEnterGuide() {
        if (enterGuideSpeeches.isEmpty()) {
            throw new DigitalHumanCommonException(String.format("enter guide speech cannot empty when " +
                    "mode = %s", pullbackMode));
        }

        if (enterSpeeches.isEmpty()) {
            throw new DigitalHumanCommonException(String.format("enter speech cannot empty when " +
                    "mode = %s", pullbackMode));
        }

        directGuidePercentage = 0;
        enterGuidePercentage = 100;
    }

    private void checkPercentageGuide() {

        if (directGuideSpeeches.isEmpty() && directGuidePercentage > 0) {
            throw new DigitalHumanCommonException("direct guide speech cannot be empty when directGuidePercentage > 0");
        }

        if ((enterGuideSpeeches.isEmpty() || enterSpeeches.isEmpty()) && enterGuidePercentage > 0) {
            throw new DigitalHumanCommonException("enter guide speech or enter speeches cannot be empty when "
                    + "directGuidePercentage > 0");
        }

        if (directGuidePercentage <= 0 && enterGuidePercentage <= 0) {
            throw new DigitalHumanCommonException(String.format("percentages cannot all be zero "
                    + "when mode = %s", pullbackMode));
        }

        directGuidePercentage = (int) (directGuidePercentage * 100.0 / (directGuidePercentage + enterGuidePercentage));
        enterGuidePercentage = 100 - directGuidePercentage;
    }
}
