// Copyright (C) 2019 Baidu Inc. All rights reserved.
package com.baidu.acg.piat.digitalhuman.common.webrtc;

import java.time.Instant;
import java.util.Random;
import org.apache.commons.codec.digest.HmacAlgorithms;
import org.apache.commons.codec.digest.HmacUtils;

/**
 * WetSessionTokenUtil
 *
 * <AUTHOR>
 * @since 2019-08-05
 */
public class WebRtcTokenUtil {

    private static final Random RANDOM = new Random();

    public static String getToken(String webRtcVersion, long tokenExpireMillis, String appId, String appKey,
                                  String roomName, String userId) {
        Instant ts = Instant.now();
        Instant expire = ts.plusMillis(tokenExpireMillis);
        String tsSecond = complement(ts.getEpochSecond() + "", 10);
        String expireSecond = complement(expire.getEpochSecond() + "", 10);
        String randString = complement(Integer.toHexString(RANDOM.nextInt()), 8);
        String signature = new HmacUtils(HmacAlgorithms.HMAC_SHA_1, appKey)
                .hmacHex("ACS" + appId + tsSecond + randString
                        + roomName + userId + expireSecond);
        return webRtcVersion + signature + tsSecond + randString + expireSecond;
    }

    private static String complement(String data, int length) {
        if (data.length() == length) {
            return data;
        } else if (data.length() > length) {
            return data.substring(0, length);
        }
        var sb = new StringBuilder();
        for (var i = 0; i < length - data.length(); i++) {
            sb.append(0);
        }
        sb.append(data);
        return sb.toString();
    }

}
