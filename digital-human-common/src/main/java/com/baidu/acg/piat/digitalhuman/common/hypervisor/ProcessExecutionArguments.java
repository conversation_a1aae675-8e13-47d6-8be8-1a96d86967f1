package com.baidu.acg.piat.digitalhuman.common.hypervisor;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProcessExecutionArguments {

    private String rtcServerUrl;

    private String appId;

    private String roomId;

    private String agentId;

    private String agentToken;

    private List<String> agentFeedIds;

    private String proxyServerId;

    private String proxyServerToken;

    private List<String> proxyServerFeedIds;

    private String clientId;

    private String clientToken;

    private String ue4;
}
