// Copyright (C) 2019 Baidu Inc. All rights reserved.
package com.baidu.acg.piat.digitalhuman.common.model;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * PageResponse
 *
 * <AUTHOR>
 * @since 2019-07-16
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class PageResponse<T> extends BaseResponse {
    private PageResult<T> page;

    public static <T> PageResponse<T> fail(String errorMessage) {
        PageResponse<T> result = new PageResponse<>();
        result.setMessage(new Message(errorMessage));
        result.setSuccess(false);
        return result;
    }

    /**
     * @param pageNo   input 0 , output 1
     * @param pageSize
     * @param total
     * @param contents
     * @param <T>
     * @return
     */
    public static <T> PageResponse<T> success(int pageNo, int pageSize, long total, List<T> contents) {
        PageResponse<T> result = new PageResponse<>();
        result.setPage(PageResult.<T>builder()
                .pageNo(pageNo)
                .pageSize(pageSize)
                .totalCount(total)
                .result(contents)
                .build());
        return result;
    }

    public static <T> PageResponse<T> success(List<T> contents) {
        PageResponse<T> result = new PageResponse<>();
        result.setPage(PageResult.<T>builder()
                .pageNo(1)
                .pageSize(contents.size())
                .totalCount(contents.size())
                .result(contents)
                .build());
        return result;
    }


    public static <T> PageResponse<T> empty(int pageSize, int total) {
        PageResponse<T> result = new PageResponse<>();
        result.setPage(PageResult.<T>builder()
                .pageNo(1)
                .pageSize(pageSize)
                .totalCount(total)
                .build());
        return result;
    }
}
