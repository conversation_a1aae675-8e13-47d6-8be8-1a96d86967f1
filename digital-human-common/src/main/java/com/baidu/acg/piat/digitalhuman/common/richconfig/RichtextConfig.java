package com.baidu.acg.piat.digitalhuman.common.richconfig;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Created on 2020/4/22 21:47.
 *
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class RichtextConfig {
    @Deprecated
    private String projectId;
    private String projectName;
    private String userId;
    private String configId;
    private String content;
    private String downloadUrl;
    private String audioUrl;
    private String createTime;
    private String updateTime;
}
