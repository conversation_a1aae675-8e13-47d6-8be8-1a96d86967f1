package com.baidu.acg.piat.digitalhuman.common.statistic;

import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


/**
 * 该类新加字段需要关注新加字段是否支持导出
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class SessionStatisticData {
    @ExcelProperty("${name}")
    private String name;
    @ExcelProperty("${sessionTotalCount}")
    private int sessionTotalCount;
    @ExcelProperty("${sessionAvgDuration}")
    private long sessionAvgDuration;
    @ExcelProperty("${dialogTotalCount}")
    private int dialogTotalCount;
}
