// Copyright (C) 2021 Baidu Inc. All rights reserved.

package com.baidu.acg.piat.digitalhuman.common.utils;

import com.baidu.acg.piat.digitalhuman.common.util.DomUtil;
import com.google.common.base.Strings;
import io.vavr.control.Try;
import org.w3c.dom.Document;
import org.w3c.dom.NodeList;

import java.util.Optional;

/**
 * CommandParserUtil
 *
 * <AUTHOR> (<EMAIL>)
 */
public class CommandParserUtil {

    /**
     *  parse command object from xml
     *
     * @param xml String with command info
     * @param tag String about specific command, such as actionRecog, rtmpControl
     * @param clazz Class of command
     * @return Optional of command
     */
    public static <T> Optional<T> fromXml(String xml, String tag, Class<T> clazz) {
        if (Strings.isNullOrEmpty(xml) || !xml.contains(tag)) {
            return Optional.empty();
        }
        Document document = DomUtil.getDocument(xml);
        if (document == null) {
            return Optional.empty();
        }
        NodeList nodeList = document.getElementsByTagName(tag);
        if (nodeList.getLength() == 0) {
            return Optional.empty();
        } else {
            return Try.of(() -> JsonUtil.readValue(DomUtil.xmlNode2Json(nodeList.item(0)), clazz)
            ).toJavaOptional();
        }
    }
}
