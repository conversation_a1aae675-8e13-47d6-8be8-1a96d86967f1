package com.baidu.acg.piat.digitalhuman.common.audio;

import lombok.Data;

import java.nio.ByteBuffer;
import java.nio.ByteOrder;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@Data
public class Wave extends Audio {

    // riff chunk
    private int riffId;

    private int riffSize;

    private int riffType;

    // format chunk
    private int formatId;

    private int formatSize;

    private short audioFormat;

    private short numChannels;

    private int sampleRate;

    private int byteRate;

    private short blockAlign;

    private short bitsPerSample;

    // data chunk
    private int dataId;

    private int dataSize;

    private byte[] data;

    private long duration;

    public Wave(byte[] bytes) {
        super(bytes);

        var byteBuffer = ByteBuffer.wrap(bytes);

        // riff chunk
        riffId = byteBuffer.order(ByteOrder.BIG_ENDIAN).getInt(0x00);
        riffSize = byteBuffer.order(ByteOrder.LITTLE_ENDIAN).getInt(0x04);
        riffType = byteBuffer.order(ByteOrder.BIG_ENDIAN).getInt(0x08);

        // format chunk
        formatId = byteBuffer.order(ByteOrder.BIG_ENDIAN).getInt(0x0C);
        formatSize = byteBuffer.order(ByteOrder.LITTLE_ENDIAN).getInt(0x10);
        audioFormat = byteBuffer.order(ByteOrder.LITTLE_ENDIAN).getShort(0x14);
        numChannels = byteBuffer.order(ByteOrder.LITTLE_ENDIAN).getShort(0x16);
        sampleRate = byteBuffer.order(ByteOrder.LITTLE_ENDIAN).getInt(0x18);
        byteRate = byteBuffer.order(ByteOrder.LITTLE_ENDIAN).getInt(0x1C);
        blockAlign = byteBuffer.order(ByteOrder.LITTLE_ENDIAN).getShort(0x20);
        bitsPerSample = byteBuffer.order(ByteOrder.LITTLE_ENDIAN).getShort(0x22);

        // data chunk
        dataId = byteBuffer.order(ByteOrder.BIG_ENDIAN).getInt(0x24);
        dataSize = byteBuffer.order(ByteOrder.LITTLE_ENDIAN).getInt(0x28);
        data = Arrays.copyOfRange(bytes, 0x2C, bytes.length);
        duration = dataSize * 1000 / byteRate;

        validate();
    }

    private void validate() {
        // TODO: more verification
        if (byteRate <= 0) {
            byteRate = numChannels * sampleRate * bitsPerSample / 8;
        }

        if (byteRate <= 0) {
            throw new IllegalArgumentException("Can not initialize wav audio: byte rate must be greater than 0");
        }
    }

    @Override
    public List<Audio> slice(int intervalMillis, boolean padding) {
        if (intervalMillis <= 0) {
            throw new IllegalArgumentException("slice interval must be greater than 0");
        }
        if (intervalMillis * byteRate % 1000 != 0) {
            throw new IllegalArgumentException("slice interval multiply byte rate can not be divided by 1000");
        }

        var length = byteRate * intervalMillis / 1000;
        var audios = new ArrayList<Audio>(dataSize / length + dataSize % length == 0 ? 0 : 1);

        for (int i = 0; i * length < dataSize; i++) {

            // segment audio data size
            var size = padding ? length : Math.min(length, dataSize - i * length);

            // segment audio bytes
            var bytes = new byte[0x2C + size];

            // copy head
            System.arraycopy(getBytes(), 0, bytes, 0, 0x2C);

            // copy segment audio data
            System.arraycopy(data, i * length, bytes, 0x2C, Math.min(length, dataSize - i * length));

            // modify segment audio data size
            System.arraycopy(ByteBuffer.allocate(4).order(ByteOrder.LITTLE_ENDIAN).putInt(size).array(), 0,
                    bytes, 0x28, 4);

            // modify segment audio riff size
            System.arraycopy(ByteBuffer.allocate(4).order(ByteOrder.LITTLE_ENDIAN).putInt(size + 0x24).array(), 0,
                    bytes, 0x04, 4);

            audios.add(new Wave(bytes));
        }
        return audios;
    }

    @Override
    public List<Audio> sliceMonoIntoStereo(int interval) {
        return null;
    }
}
