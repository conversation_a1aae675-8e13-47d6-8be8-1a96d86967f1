package com.baidu.acg.piat.digitalhuman.common.llmrole;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

@Builder
@Data
@Slf4j
@NoArgsConstructor
@AllArgsConstructor
public class CustomizedConfig {
    // 相机位
    private List<String> cameras;
    private Double x;
    private Double y;
    private Double z;
}
