package com.baidu.acg.piat.digitalhuman.common.project;

import com.baidu.acg.piat.digitalhuman.common.util.JsonUtil;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

import com.baidu.acg.piat.digitalhuman.common.constans.RenderOpenParameters;

/**
 * Created on 2020/4/21 18:37.
 *
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class Project {

    private String id;

    private String name;

    private String description;

    private String userName;

    private String userId;

    private String appId;

    private String appKey;

    // 适配2.0人设characterConfig
    private String characterConfigId;

    private String characterConfigName;

    private String characterImage;

    private String figureAlias;

    private String preset;

    private Boolean isDefault = false;

    private String projectVersion;

    private String thumbnailUrl;

    // character thumbnail captured on character management frontend
    private String characterThumbnail;

    private String backgroundImageId;

    private String backgroundImageUrl;

    private String logoUid;

    private String logoUrl;

    private BotParams botParams;

    private TtsParams ttsParams;

    private Camera camera;

    private Scene scene;

    private ResolutionParams resolutionParams;

    private FigureCutParams figureCutParams;

    private CharacterParams characterParams;

    private PaintChartOnPictureParams paintChartOnPictureParams;

    private PaintSubtitleOnPictureParams paintSubtitleOnPictureParams;

    private MediaOutput mediaOutput;

    private ConversationConfig conversationConfig;

    private HotWordReplaceReg hotWords;

    private UserInactiveConfig userInactive;

    private PrologueParams prologueParams;

    private List<String> hitShieldReply;

    private AlitaParams alitaParams;

    private AsrPartEvent asrPartEvent;

    private String editor;

    private int apiVersion;

    private String createTime;

    private String updateTime;

    // 记录当登录账户的id
    private String uid;
    // 用于区别新建时是单独新建还是复制
    private String addType;

    private String type;

    public Map<String, String> toRenderParams() {
        Map<String, String> renderParameters = new HashMap<>();
        Optional.ofNullable(preset).ifPresent(r -> renderParameters.put(RenderOpenParameters.preset.name(), r));

        Optional.ofNullable(backgroundImageUrl).ifPresent(r -> renderParameters.put(
                RenderOpenParameters.background_image_url.name(), r));
        if (resolutionParams != null && resolutionParams.getWidth() != null
                && resolutionParams.getHeight() != null) {
            Optional.of(resolutionParams.getWidth()).ifPresent(r -> renderParameters.put(
                    RenderOpenParameters.resolution_width.name(), String.valueOf(r)));
            Optional.of(resolutionParams.getHeight()).ifPresent(r -> renderParameters.put(
                    RenderOpenParameters.resolution_height.name(), String.valueOf(r)));
            Optional.ofNullable(resolutionParams.getKbpsBitrate()).ifPresent(r -> renderParameters.put(
                    RenderOpenParameters.x264_param_rc_i_bitrate.name(), String.valueOf(r)));
        }

        // 这四个参数基本用不到，所以如果未定义，则赋默认值
        renderParameters.put(RenderOpenParameters.figureCutXPercent.name(), "0");
        renderParameters.put(RenderOpenParameters.figureCutYPercent.name(), "0");
        renderParameters.put(RenderOpenParameters.figureCutWidthPercent.name(), "100");
        renderParameters.put(RenderOpenParameters.figureCutHeightPercent.name(), "100");

        if (figureCutParams != null) {
            Optional.ofNullable(figureCutParams.getCutXPercent()).ifPresentOrElse(r -> renderParameters.put(
                    RenderOpenParameters.figureCutXPercent.name(), r),
                    () -> renderParameters.put(RenderOpenParameters.figureCutXPercent.name(), "0"));
            Optional.ofNullable(figureCutParams.getCutYPercent()).ifPresentOrElse(r -> renderParameters.put(
                    RenderOpenParameters.figureCutYPercent.name(), r),
                    () -> renderParameters.put(RenderOpenParameters.figureCutYPercent.name(), "0"));
            Optional.ofNullable(figureCutParams.getCutWidthPercent()).ifPresentOrElse(r -> renderParameters.put(
                    RenderOpenParameters.figureCutWidthPercent.name(), r),
                    () -> renderParameters.put(RenderOpenParameters.figureCutWidthPercent.name(), "100"));
            Optional.ofNullable(figureCutParams.getCutHeightPercent()).ifPresentOrElse(r -> renderParameters.put(
                    RenderOpenParameters.figureCutHeightPercent.name(), r),
                    () -> renderParameters.put(RenderOpenParameters.figureCutHeightPercent.name(), "100"));

            Optional.ofNullable(figureCutParams.getPositionCenterXPercent()).ifPresent(r -> renderParameters.put(
                    RenderOpenParameters.figurePositionCenterXPercent.name(), r));
            Optional.ofNullable(figureCutParams.getPositionBottomYPercent()).ifPresent(r -> renderParameters.put(
                    RenderOpenParameters.figurePositionBottomYPercent.name(), r));
            Optional.ofNullable(figureCutParams.getWidthRatio()).ifPresent(r -> renderParameters.put(
                    RenderOpenParameters.figureWidthRatio.name(), r));
        }
        if (ttsParams != null) {
            Optional.ofNullable(ttsParams.getSpeed()).ifPresent(speed -> renderParameters.put(
                    RenderOpenParameters.ttsSpeed.name(), speed));
            Optional.ofNullable(ttsParams.getPitch()).ifPresent(pitch -> renderParameters.put(
                    RenderOpenParameters.ttsPitch.name(), pitch));
            Optional.ofNullable(ttsParams.getVolume()).ifPresent(volume -> renderParameters.put(
                    RenderOpenParameters.ttsVolume.name(), volume));
            Optional.ofNullable(ttsParams.getPerson()).ifPresent(person -> renderParameters.put(
                    RenderOpenParameters.ttsPerson.name(), person));
            Optional.ofNullable(ttsParams.getExtraParams()).ifPresent(extraParams -> renderParameters.put(
                    RenderOpenParameters.ttsExtraParams.name(), JsonUtil.writeValueAsStringNoThrow(extraParams)));
        }
        if (Objects.nonNull(paintChartOnPictureParams) && paintChartOnPictureParams.getPaintWidgetOnPicture()) {
            renderParameters.put(RenderOpenParameters.paintWidgetOnPicture.name(), "true");
            Optional.ofNullable(paintChartOnPictureParams.getHtml5Url()).ifPresent(r ->
                    renderParameters.put(RenderOpenParameters.h5Embedded.name(), "true"));
            Optional.ofNullable(paintChartOnPictureParams.getRenderVideoOutsideUe4()).ifPresent(r ->
                    renderParameters.put(RenderOpenParameters.renderVideoOutsideUe4.name(), String.valueOf(r)));
        }
        if (Objects.nonNull(paintSubtitleOnPictureParams)) {
            Optional.ofNullable(paintSubtitleOnPictureParams.getPaintSubtitleOnPicture()).ifPresent(r ->
                    renderParameters.put(RenderOpenParameters.paintSubtitleOnPicture.name(), String.valueOf(r)));
            Optional.ofNullable(paintSubtitleOnPictureParams.getPaintQueryOnPicture()).ifPresent(r ->
                    renderParameters.put(RenderOpenParameters.paintQueryOnPicture.name(), String.valueOf(r)));
            Optional.ofNullable(paintSubtitleOnPictureParams.getSubtitleBackgroundColor()).ifPresent(r ->
                    renderParameters.put(RenderOpenParameters.subtitleBackgroundColor.name(), r));
            Optional.ofNullable(paintSubtitleOnPictureParams.getSubtitleFontSize()).ifPresent(r ->
                    renderParameters.put(RenderOpenParameters.subtitleFontSize.name(), String.valueOf(r)));
            Optional.ofNullable(paintSubtitleOnPictureParams.getSubtitleSplittable()).ifPresent(r ->
                    renderParameters.put(RenderOpenParameters.subtitleSplittable.name(), String.valueOf(r)));
            Optional.ofNullable(paintSubtitleOnPictureParams.getSubtitleTTL()).ifPresent(r ->
                    renderParameters.put(RenderOpenParameters.subtitleTTL.name(), String.valueOf(r)));
            Optional.ofNullable(paintSubtitleOnPictureParams.getSubtitleMarginPx()).ifPresent(r ->
                    renderParameters.put(RenderOpenParameters.subtitleMarginPx.name(), String.valueOf(r)));

            // 3D字幕配置参数
            Optional.ofNullable(paintSubtitleOnPictureParams.getSubtitleColor()).ifPresent(r ->
                    renderParameters.put(RenderOpenParameters.subtitleColor.name(), r));
            Optional.ofNullable(paintSubtitleOnPictureParams.getSubtitleSize()).ifPresent(r ->
                    renderParameters.put(RenderOpenParameters.subtitleSize.name(), r));
            Optional.ofNullable(paintSubtitleOnPictureParams.getSubtitleBackColor()).ifPresent(r ->
                    renderParameters.put(RenderOpenParameters.subtitleBackColor.name(), r));
            Optional.ofNullable(paintSubtitleOnPictureParams.getSubtitleFont()).ifPresent(r ->
                    renderParameters.put(RenderOpenParameters.subtitleFont.name(), r));
            Optional.ofNullable(paintSubtitleOnPictureParams.getSubtitleLocationX()).ifPresent(r ->
                    renderParameters.put(RenderOpenParameters.subtitleLocationX.name(), String.valueOf(r)));
            Optional.ofNullable(paintSubtitleOnPictureParams.getSubtitleLocationY()).ifPresent(r ->
                    renderParameters.put(RenderOpenParameters.subtitleLocationY.name(), String.valueOf(r)));
        }
        if (Objects.nonNull(asrPartEvent)) {
            Optional.ofNullable(asrPartEvent.getAsrPartEventTriggerNumber()).ifPresent(n ->
                    renderParameters.put(RenderOpenParameters.asrPartEventTriggerNumber.name(), String.valueOf(n)));
            Optional.ofNullable(asrPartEvent.getAsrPartEventTriggerText()).ifPresent(s ->
                    renderParameters.put(RenderOpenParameters.asrPartEventTriggerText.name(), s));
        }

        if (Objects.nonNull(camera)) {
            Optional.ofNullable(camera.getId())
                .ifPresent(id -> renderParameters.put(RenderOpenParameters.cameraId.name(), String.valueOf(id)));
        }

        if (Objects.nonNull(scene)) {
            Optional.ofNullable(scene.getId())
                .ifPresent(id -> renderParameters.put(RenderOpenParameters.sceneId.name(), String.valueOf(id)));
        }
        return renderParameters;
    }

}
