package com.baidu.acg.piat.digitalhuman.common.llmrole;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 知识库具体实体
 *
 * <AUTHOR>
 * @since 2023/12/1 10:48
 */
@Data
@Builder
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class KnowledgeBase {

    private String knowledgeBaseId;

    private String name;

    private String accountId;

    private String editor;

    private Integer type;

    private Integer fileCount;

    private String explain;

    private Integer status;

    private boolean isDelete;

    private String createTime;

    private String updateTime;
}