package com.baidu.acg.piat.digitalhuman.common.alita.websocket;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.UUID;

import javax.annotation.Nullable;

import com.baidu.acg.piat.digitalhuman.common.alita.ActionType;
import com.baidu.acg.piat.digitalhuman.common.exception.Error;

/**
 * Created on 2020/6/12 14:22.
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WebsocketResponse {
    private String requestId;

    private String action;

    private int code;

    private String message;

    private String body;

    public static WebsocketResponse success(WebsocketRequest request) {
        return success(request, null);
    }

    public static WebsocketResponse success(WebsocketRequest request, String body) {
        return WebsocketResponse.builder().requestId(request.getRequestId())
                .action(request.getAction().name())
                .code(0)
                .message("ok")
                .body(body)
                .build();
    }

    public static WebsocketResponse success(String requestId, ActionType action, @Nullable String body) {
        return WebsocketResponse.builder().requestId(requestId)
                .action(action.name())
                .code(0)
                .message("ok")
                .body(body)
                .build();
    }

    public static WebsocketResponse success(String requestId, String action, @Nullable String body) {
        return WebsocketResponse.builder().requestId(requestId)
                .action(action)
                .code(0)
                .message("ok")
                .body(body)
                .build();
    }


    public static WebsocketResponse fail(WebsocketRequest request, int code, String message) {
        return WebsocketResponse.builder().requestId(request.getRequestId())
                .action(request.getAction().name())
                .code(code)
                .message(message)
                .build();
    }

    public static WebsocketResponse fail(WebsocketRequest request, Error error) {
        return WebsocketResponse.builder().requestId(request.getRequestId())
                .action(request.getAction().name())
                .code(error.getCode())
                .message(error.getMessage())
                .build();
    }

    public static WebsocketResponse fail(ActionType action, int code, String message) {
        return WebsocketResponse.builder().requestId(UUID.randomUUID().toString())
                .action(action.name())
                .code(code)
                .message(message)
                .build();
    }

    public static WebsocketResponse fail(String requestId, String action, int code, String message) {
        return WebsocketResponse.builder().requestId(requestId)
                .action(action)
                .code(code)
                .message(message)
                .build();
    }

}
