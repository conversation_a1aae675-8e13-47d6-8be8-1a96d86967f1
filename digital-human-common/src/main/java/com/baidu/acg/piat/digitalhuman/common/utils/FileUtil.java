package com.baidu.acg.piat.digitalhuman.common.utils;

import lombok.extern.slf4j.Slf4j;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.io.FileOutputStream;
import java.io.FileInputStream;

@Slf4j
public class FileUtil {
    public static final String WAV_FORMAT = ".wav";
    public static final String MP3_FORMAT = ".mp3";

    /**
     * 保存MultipartFile对象
     * @param file
     * @param toFilePath
     * @return
     */
    public static String saveMultipartFile(MultipartFile file, String toFilePath){
        File toFile = null;
        if ("".equals(file) || file.getSize() <= 0) {
            return null;
        } else {
            toFile = new File(toFilePath);
            String absolutePath = null;
            try {
                absolutePath = toFile.getCanonicalPath();
                String dirPath = absolutePath.substring(0, absolutePath.lastIndexOf(File.separator));
                File dir = new File(dirPath);
                if (!dir.exists()) {
                    dir.mkdirs();
                }
                InputStream ins = file.getInputStream();
                inputStreamToFile(ins, toFile);
                ins.close();
            } catch (IOException e) {
                log.error("Fail to saveMultipartFile e:{}", e);
            }
            return absolutePath;
        }
    }

    /**
     * 文件存储
     * @param ins
     * @param file
     */
    private static void inputStreamToFile(InputStream ins, File file) {
        try {
            OutputStream os = new FileOutputStream(file);
            int bytesRead = 0;
            byte[] buffer = new byte[1024];
            while ((bytesRead = ins.read(buffer)) != -1) {
                os.write(buffer, 0, bytesRead);
            }
            os.close();
            ins.close();
        } catch (Exception e) {
            log.error("Fail to inputStreamToFile e:{}", e);
        }
    }

    /**
     * 获取文件二进制数据
     * @param file
     * @return
     */
    public static byte[] fileToByteData(File file){
        FileInputStream fileInputStream = null;
        byte[] byteData = null;
        try {
            byteData = new byte[(int) file.length()];
            fileInputStream = new FileInputStream(file);
            fileInputStream.read(byteData);
        } catch (IOException e) {
            log.error("Fail to fileToByteData e:{}", e);
        } finally {
            if (fileInputStream != null) {
                try {
                    fileInputStream.close();
                } catch (IOException e) {
                    log.error("Fail to closeFileInputStream e:{}", e);
                }
            }
        }
        return byteData;
    }

    /**
     * 获取文件名
     * @param file
     * @return
     */
    public static String getFileName(MultipartFile file){
        String originalFilename = file.getOriginalFilename();
        return originalFilename.substring(0, originalFilename.lastIndexOf("."));
    }

    /**
     * 获取文件后缀（带.）
     * @param file
     * @return
     */
    public static String getFileFormat(MultipartFile file){
        String originalFilename = file.getOriginalFilename();
        return originalFilename.substring(originalFilename.lastIndexOf(".")).toLowerCase();
    }
}
