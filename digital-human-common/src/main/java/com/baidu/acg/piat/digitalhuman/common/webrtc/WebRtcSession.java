package com.baidu.acg.piat.digitalhuman.common.webrtc;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.NonNull;
import lombok.Setter;

/**
 * Web rtc session.
 *
 * <AUTHOR>
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WebRtcSession {

    @NonNull
    private String internalRtcServerUrl;

    @NonNull
    private String externalRtcServerUrl;

    @NonNull
    private String appId;

    @NonNull
    private String roomId;

    @NonNull
    private String clientId;

    @NonNull
    private String clientToken;

    @NonNull
    private String proxyServerId;

    @NonNull
    private String proxyServerToken;

    @NonNull
    private String agentId;

    @NonNull
    private String agentToken;

    private String internalRtcPlatformUrl;

    private String sessionId;

}
