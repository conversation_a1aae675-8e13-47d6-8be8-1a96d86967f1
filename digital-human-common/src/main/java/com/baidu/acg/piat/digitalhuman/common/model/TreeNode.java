package com.baidu.acg.piat.digitalhuman.common.model;

import lombok.Getter;
import lombok.Setter;

import java.util.List;
import java.util.function.Consumer;


@Setter
@Getter
public abstract class TreeNode<T extends TreeNode> {

    protected List<T> children;


    /**
     * 自底向上遍历所有下级节点和本节点。
     */
    public void forEachBottomUp(Consumer<T> func) {
        if (children != null) {
            children.forEach(child -> child.forEachBottomUp(func));
        }
        func.accept((T) this);
    }

    /**
     * 自顶向下遍历所有下级节点和本节点。
     */
    public void forEachTopDown(Consumer<T> func) {
        func.accept((T) this);
        if (children != null) {
            children.forEach(child -> child.forEachTopDown(func));
        }
    }
}
