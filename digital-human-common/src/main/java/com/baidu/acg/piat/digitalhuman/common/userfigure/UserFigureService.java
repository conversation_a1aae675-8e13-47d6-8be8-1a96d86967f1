package com.baidu.acg.piat.digitalhuman.common.userfigure;

import com.baidu.acg.piat.digitalhuman.common.model.Response;
import com.fasterxml.jackson.databind.JsonNode;
import retrofit2.Call;
import retrofit2.http.GET;
import retrofit2.http.Path;
import retrofit2.http.Query;

public interface UserFigureService {
    @GET("/api/internal/digitalhuman/starlight/v1/figures/queryByName")
    Call<JsonNode> queryUserFigure(@Query(value = "figureName") String figureName, @Query(value = "userId") String userId);

    @GET("/api/internal/digitalhuman/starlight/v1/figures/{id}")
    Call<Response<UserFigureModel>> get(@Path(value = "id") Long id);
}
