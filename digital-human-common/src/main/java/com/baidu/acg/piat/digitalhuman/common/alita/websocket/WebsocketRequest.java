package com.baidu.acg.piat.digitalhuman.common.alita.websocket;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import com.baidu.acg.piat.digitalhuman.common.alita.ActionType;


/**
 * Created on 2020/6/12 14:21.
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WebsocketRequest {

    private String requestId;

    private ActionType action;

    private String body;

    @Override
    public String toString() {
        String body = this.body;
        return String.format("{'requestId': %s, 'action':%s, 'body':%s}",
                requestId, action, body);
    }
}
