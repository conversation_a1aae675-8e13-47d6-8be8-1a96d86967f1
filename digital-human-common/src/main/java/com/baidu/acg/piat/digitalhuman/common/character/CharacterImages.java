package com.baidu.acg.piat.digitalhuman.common.character;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Created on 2020/4/26 18:49.
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CharacterImages {

    private List<Image> types;

    @Data
    public static class Image {
        private String name;
        private String label;
        private String userId;
        private String appId;
        private String appKey;
    }

    public List<String> getCharacterImageNameList() {
        return this.types.stream().map(Image::getName).collect(Collectors.toList());
    }

    public Map<String, Image> getCharacterImageMap() {
        return this.types.stream().collect(Collectors.toMap(Image::getName, image->image));
    }

}
