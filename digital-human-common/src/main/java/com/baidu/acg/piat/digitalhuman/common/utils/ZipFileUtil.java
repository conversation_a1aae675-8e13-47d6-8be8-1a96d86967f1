package com.baidu.acg.piat.digitalhuman.common.utils;

import com.baidu.acg.piat.digitalhuman.common.exception.DigitalHumanCommonException;
import io.vavr.Tuple2;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.zip.ZipEntry;
import java.util.zip.ZipFile;
import java.util.zip.ZipOutputStream;

@Slf4j
public class ZipFileUtil {

    /**
     * 压缩指定的文件和目录到ZIP文件
     *
     * @param sourceDirPath 源目录路径
     * @param zipFilePath   ZIP文件输出路径
     * @throws IOException 如果发生I/O错误
     */
    public static void pack(String sourceDirPath, String zipFilePath) throws IOException {
        Path zipPath = Files.createFile(Paths.get(zipFilePath));
        try (ZipOutputStream zs = new ZipOutputStream(Files.newOutputStream(zipPath))) {
            Path pp = Paths.get(sourceDirPath);
            Files.walk(pp)
                    .filter(path -> !Files.isDirectory(path))
                    .forEach(path -> {
                        ZipEntry zipEntry = new ZipEntry(pp.relativize(path).toString());
                        try {
                            zs.putNextEntry(zipEntry);
                            Files.copy(path, zs);
                            zs.closeEntry();
                        } catch (IOException e) {
                            log.error("Error while adding file to zip: " + e.getMessage());
                        }
                    });
        }
    }

    /**
     *将multipartFile转换为zipFile
     * @param multipartFile
     * @return
     */
    public static Tuple2<ZipFile, String> multipartFileToZipFile (MultipartFile multipartFile) {

        String zipName = multipartFile.getOriginalFilename();
        if (!zipName.endsWith(".zip")) {
            throw new DigitalHumanCommonException("文件类型不属于zip压缩文件");
        }

        try {
            File file = File.createTempFile(zipName.split(".zip")[0] + "tmp", ".zip");
            String filePath = file.getAbsolutePath();
            FileUtils.copyInputStreamToFile(multipartFile.getInputStream(), file);
            ZipFile zip = new ZipFile(file);
            return new Tuple2<>(zip, filePath);
        } catch (IOException e) {
            throw new DigitalHumanCommonException("文件转换失败");
        }

    }

    /**
     * 删除解析zip文件时临时生成的文件
     * @param filePath
     */
    public static void deleteTemporaryFile(String filePath) {
        try {
            if (StringUtils.isEmpty(filePath)) {
                return;
            }

            File file = new File(filePath);
            if (file.delete()) {
                return;
            } else {
                throw new DigitalHumanCommonException("Fail to delete temporary file=" + filePath);
            }
        } catch (Exception e) {
            throw new DigitalHumanCommonException("Fail to delete temporary file=" + filePath);
        }
    }
}
