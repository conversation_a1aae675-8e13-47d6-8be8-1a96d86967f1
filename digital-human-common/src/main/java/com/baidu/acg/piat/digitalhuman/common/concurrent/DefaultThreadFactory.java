package com.baidu.acg.piat.digitalhuman.common.concurrent;

import lombok.RequiredArgsConstructor;

import java.util.concurrent.ThreadFactory;
import java.util.concurrent.atomic.AtomicInteger;

@RequiredArgsConstructor
public class DefaultThreadFactory implements ThreadFactory {

    private final String threadPrefix;

    private final AtomicInteger threadNumber = new AtomicInteger(0);

    @Override
    public Thread newThread(Runnable r) {
        String name = threadPrefix + "-" + threadNumber.getAndIncrement();
        return new Thread(r, name);
    }
}
