package com.baidu.acg.piat.digitalhuman.common.healthz;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.baidu.acg.piat.digitalhuman.common.hypervisor.response.HealthResponse;


@RestController
@RequestMapping("/healthz")
public class CommonHealthzController {
    @GetMapping
    public HealthResponse get() {
        return HealthResponse.succeed();
    }
}