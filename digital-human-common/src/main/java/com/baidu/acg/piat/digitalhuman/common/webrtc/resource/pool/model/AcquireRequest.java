package com.baidu.acg.piat.digitalhuman.common.webrtc.resource.pool.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.util.Map;

import com.baidu.acg.piat.digitalhuman.common.cloud.Selectors;

/**
 * Web rtc resource request.
 *
 * <AUTHOR>
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class AcquireRequest {

    private String resourceType;

    private String owner;

    private Map<String, Object> customizedItems;

    private Selectors selectors = new Selectors();
}