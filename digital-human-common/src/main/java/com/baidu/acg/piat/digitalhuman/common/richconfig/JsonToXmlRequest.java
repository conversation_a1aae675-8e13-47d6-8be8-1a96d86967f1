package com.baidu.acg.piat.digitalhuman.common.richconfig;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * Created on 2020/4/27 19:28.
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class JsonToXmlRequest {

    private String characterImage;

    private Boolean interruptible = false;

    private List<DrmlAction> drml;

}
