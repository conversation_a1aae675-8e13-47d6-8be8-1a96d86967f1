package com.baidu.acg.piat.digitalhuman.common.audio;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Objects;

/**
 * 音频参数
 *
 * <AUTHOR>
 * @since 2021/08/02
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AudioFormat {
    @Builder.Default
    private int numChannels = 1;
    @Builder.Default
    private int sampleRate = 16000;
    @Builder.Default
    private int sampleBits = 16;

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null || getClass() != obj.getClass()) {
            return false;
        }
        AudioFormat other = (AudioFormat) obj;
        return numChannels == other.numChannels &&
                sampleRate == other.sampleRate &&
                sampleBits == other.sampleBits;
    }

    @Override
    public int hashCode() {
        return Objects.hash(numChannels, sampleRate, sampleBits);
    }
}
