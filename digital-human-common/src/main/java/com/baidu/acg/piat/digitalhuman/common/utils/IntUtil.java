package com.baidu.acg.piat.digitalhuman.common.utils;

/**
 * <AUTHOR>
 */
public class IntUtil {
    /**
     * @param bytes
     * @return
     * @throws RuntimeException
     */
    public static int fromByteArray(byte[] bytes) throws RuntimeException {
        if (bytes.length < 4) {
            throw new RuntimeException(String.format("array too small: %s < %s", bytes.length, 4));
        }
        return fromBytes(bytes[0], bytes[1], bytes[2], bytes[3]);
    }

    /**
     * @param b1
     * @param b2
     * @param b3
     * @param b4
     * @return
     */
    public static int fromBytes(byte b1, byte b2, byte b3, byte b4) {
        return b1 << 24 | (b2 & 255) << 16 | (b3 & 255) << 8 | b4 & 255;
    }

    /**
     * @param value
     * @return
     */
    public static byte[] toByteArray(int value) {
        return new byte[]{(byte) (value >> 24), (byte) (value >> 16), (byte) (value >> 8), (byte) value};
    }
}
