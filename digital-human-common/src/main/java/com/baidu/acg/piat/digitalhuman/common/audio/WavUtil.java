// Copyright (C) 2019 Baidu Inc. All rights reserved.
package com.baidu.acg.piat.digitalhuman.common.audio;

import lombok.extern.slf4j.Slf4j;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.nio.ByteBuffer;
import java.nio.ByteOrder;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;

import javax.sound.sampled.AudioFormat;
import javax.sound.sampled.AudioFormat.Encoding;
import javax.sound.sampled.AudioInputStream;
import javax.sound.sampled.AudioSystem;
import javax.sound.sampled.UnsupportedAudioFileException;

/**
 * WavUtil
 *
 * <AUTHOR>
 * @since 2019-10-08
 */
@Slf4j
public class WavUtil {

    /**
     * header template
     */
    private static final byte[] WAV_HEAD_TEMP = new byte[] {82, 73, 70, 70, 110, 118, 0, 0, 87, 65, 86, 69,
            102, 109, 116, 32, 16, 0, 0, 0, 1, 0, 1, 0, -128, 62, 0, 0, 0, 125, 0, 0, 2, 0, 16,
            0, 100, 97, 116, 97, 0, 0, 0, 0};

    /**
     * ASCII RIFF
     */
    private static final int RIFF_ID = 1380533830;
    /**
     * ASCII WAVE
     */
    private static final int RIFF_TYPE = 1463899717;
    /**
     * ASCII fmt (注意有空格)
     */
    private static final int FORMAT_ID = 1718449184;

    public static byte[] normalize(byte[] audio, short numChannels, int sampleRate, short sampleBits) {
        if (validateHeader(audio)) {
            return audio;
        }
        log.debug("accept abnormal audio attach header ");
        return attachHeader(audio, numChannels, sampleRate, sampleBits);
    }

    /**
     * read header length of wav
     *
     * @return
     */
    public static int wavHeaderLength() {
        return WAV_HEAD_TEMP.length;
    }

    public static boolean validateHeader(byte[] audio) {
        // todo 校验采样率等其他信息
        byte[] header = new byte[0x2C];
        if (audio.length < 0x2C) {
            return false;
        }
        System.arraycopy(audio, 0, header, 0, 0x2C);
        var byteBuffer = ByteBuffer.wrap(header);
        // riff chunk
        var riffId = byteBuffer.order(ByteOrder.BIG_ENDIAN).getInt(0x00);
        var riffType = byteBuffer.order(ByteOrder.BIG_ENDIAN).getInt(0x08);
        // format chunk
        var formatId = byteBuffer.order(ByteOrder.BIG_ENDIAN).getInt(0x0C);
        return riffId == RIFF_ID && riffType == RIFF_TYPE && formatId == FORMAT_ID;
    }

    public static byte[] attachHeader(byte[] audio, short numChannels, int sampleRate, short sampleBits) {

        var bytes = new byte[0x2C + audio.length];
        var byteRate = numChannels * sampleRate * sampleBits / 8;

        // copy head template
        System.arraycopy(WAV_HEAD_TEMP, 0, bytes, 0, 44);
        // set riff size
        System.arraycopy(ByteBuffer.allocate(4).order(ByteOrder.LITTLE_ENDIAN).putInt(bytes.length - 8).array(),
                0, bytes, 0x04, 4);
        // set channel number
        System.arraycopy(ByteBuffer.allocate(2).order(ByteOrder.LITTLE_ENDIAN)
                .putShort(numChannels).array(), 0, bytes, 0x16, 2);
        // set sample rate
        System.arraycopy(ByteBuffer.allocate(4).order(ByteOrder.LITTLE_ENDIAN)
                .putInt(sampleRate).array(), 0, bytes, 0x18, 4);
        // set sample bits
        System.arraycopy(ByteBuffer.allocate(2).order(ByteOrder.LITTLE_ENDIAN)
                .putShort(sampleBits).array(), 0, bytes, 0x22, 2);
        // set byte rate
        System.arraycopy(ByteBuffer.allocate(4).order(ByteOrder.LITTLE_ENDIAN)
                .putInt(byteRate).array(), 0, bytes, 0x1c, 4);
        // set data size
        System.arraycopy(ByteBuffer.allocate(4).order(ByteOrder.LITTLE_ENDIAN)
                .putInt(audio.length).array(), 0, bytes, 0x28, 4);

        // copy segment audio data
        System.arraycopy(audio, 0, bytes, 0x2C, audio.length);
        return bytes;
    }

    public static List<byte[]> cutWav(byte[] wavBytes,
                                      int intervalMillis, int sampleRate, int sampleBits) {
        return cutWav(wavBytes, intervalMillis, sampleRate, sampleBits, 44, true);
    }

    public static List<byte[]> cutWav(byte[] wavBytes, int intervalMillis, int sampleRate,
                                      int sampleBits, int headerSize, boolean attachHeader) {
        if (wavBytes == null) {
            return new ArrayList<>();
        }
        var byteRate = sampleRate * sampleBits / 8;
        var normalSize = intervalMillis * byteRate / 1000;
        int left = wavBytes.length - headerSize;
        int index = headerSize;
        List<byte[]> result = new ArrayList<>();
        while (left > 0) {

            var byteSize = Math.min(left, normalSize);

            byte[] bytes = new byte[byteSize];
            System.arraycopy(wavBytes, index, bytes, 0, byteSize);
            if (attachHeader) {
                result.add(attachHeader(bytes, (short) 1, sampleRate, (short) sampleBits));
            } else {
                result.add(bytes);
            }

            left -= byteSize;
            index += byteSize;
        }
        return result;
    }

    public static List<byte[]> cutPcm(byte[] pcmBytes, int intervalMillis, int sampleRate, int sampleBits) {
        return cutWav(pcmBytes, intervalMillis, sampleRate, sampleBits, 0, false);
    }

    public static long millisEstimate(long lengthWithoutHeader, int sampleRate, int sampleBits, int numChannels) {
        return lengthWithoutHeader * 1000 * 8 / sampleBits / numChannels / sampleRate;
    }

    /**
     * @return audio length without header
     */
    public static long lengthEstimate(long millis, int sampleRate, int sampleBits, int numChannels) {
        return millis * sampleBits * sampleRate * numChannels / 1000 / 8;
    }

    public static byte[] fromPCM(int sampleRate, short sampleBits, short channels, byte[] pcm) {
        var bytes = new byte[44 + pcm.length];

        System.arraycopy(WAV_HEAD_TEMP, 0, bytes, 0, 44);

        // channels
        System.arraycopy(ByteBuffer.allocate(2).order(ByteOrder.LITTLE_ENDIAN).putShort(channels).array(),
                0, bytes, 0x16, 2);

        // sample rate
        System.arraycopy(ByteBuffer.allocate(4).order(ByteOrder.LITTLE_ENDIAN).putInt(sampleRate).array(),
                0, bytes, 0x18, 4);

        // sample bits
        System.arraycopy(ByteBuffer.allocate(2).order(ByteOrder.LITTLE_ENDIAN).putShort(sampleBits).array(),
                0, bytes, 0x22, 2);

        // byte rate
        System.arraycopy(
                ByteBuffer.allocate(4).order(ByteOrder.LITTLE_ENDIAN).putInt(sampleBits / 8 * sampleRate).array(),
                0, bytes, 0x1c, 4);

        // data size
        System.arraycopy(ByteBuffer.allocate(4).order(ByteOrder.LITTLE_ENDIAN).putInt(pcm.length).array(), 0, bytes,
                0x28, 4);

        // data
        System.arraycopy(pcm, 0, bytes, 44, pcm.length);

        return bytes;
    }

    public static byte[] transform2Stereo(byte[] wav) throws IOException, UnsupportedAudioFileException {
        var start = Instant.now().toEpochMilli();
        var audioInputStream = AudioSystem.getAudioInputStream(new ByteArrayInputStream(wav));
        javax.sound.sampled.AudioFormat sourceFormat = audioInputStream.getFormat();

        javax.sound.sampled.AudioFormat targetFormat = new AudioFormat(Encoding.PCM_SIGNED,
                sourceFormat.getSampleRate(),
                sourceFormat.getSampleSizeInBits(),
                sourceFormat.getChannels() * 2,
                sourceFormat.getFrameSize() * 2,
                sourceFormat.getSampleRate(),
                sourceFormat.isBigEndian());

        AudioInputStream inputStream = AudioSystem.getAudioInputStream(targetFormat, audioInputStream);
        var res = inputStream.readAllBytes();
        return res;
    }
}
