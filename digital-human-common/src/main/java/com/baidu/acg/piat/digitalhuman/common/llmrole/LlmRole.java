package com.baidu.acg.piat.digitalhuman.common.llmrole;

import com.baidu.acg.piat.digitalhuman.common.project.TtsParams;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import java.util.List;

/**
 * 对应交互的角色
 *
 * <AUTHOR>
 * @since 2023/11/22 16:31
 */
@Data
@Slf4j
@Builder
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class LlmRole {

    private String llmRoleId;

    private String accountId;

    private String characterConfigId;

    private String uid;

    private String projectId;

    private String status;

    private String publishUrl;

    private String llmRoleTemplateId;

    private String screenType;


    /**
     * 记录根据哪个模板的创建id
     */
    private String templateId;

    private String name;

    private String editor;

    private Integer roleType;

    private Integer modeType;

    private LLMConfig llmConfig;

    private String roleDefine;

    private List<Skill> skillList;

    private Knowledge knowledge;

    private OpeningStatement openingStatement;

    private BufferSentencesConfigJson bufferSentencesConfigJson;

    private String templateName;

    private String templateIconUrl;

    private String createTime;

    private String updateTime;

    private TtsParams ttsParams;

}