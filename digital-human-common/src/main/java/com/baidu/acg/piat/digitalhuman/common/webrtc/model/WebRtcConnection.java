package com.baidu.acg.piat.digitalhuman.common.webrtc.model;

import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.NonNull;

/**
 * Web rtc connection.
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WebRtcConnection {

    @NonNull
    private String url;

    @NonNull
    private String appId;

    @NonNull
    private String roomId;

    @NonNull
    private String userId;

    @NonNull
    private String token;

    @NonNull
    private List<String> pullStreamUserIds;

    private String webSocketSessionId;
}
