// Copyright (C) 2022 Baidu Inc. All rights reserved.

package com.baidu.acg.piat.digitalhuman.common.project;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * AsrPartEvent
 *
 * <AUTHOR> (<EMAIL>)
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class AsrPartEvent {

    /**
     * -1为不出发打断，大于等于0，则识别到多少个用户的字后打断播报
     */
    @Builder.Default
    private Integer asrPartEventTriggerNumber = -1;

    /**
     * 触发打断事件的指令，默认为 <silence time='1s'></silence>, 以兼容2d。
     * 3d 可配置为<interrupt></interrupt>
     */
    @Builder.Default
    private String asrPartEventTriggerText = "<silence time='1s'></silence>";
}
