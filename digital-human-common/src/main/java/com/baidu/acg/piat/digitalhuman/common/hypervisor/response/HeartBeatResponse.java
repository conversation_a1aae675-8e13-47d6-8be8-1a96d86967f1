// Copyright (C) 2021 Baidu Inc. All rights reserved.
package com.baidu.acg.piat.digitalhuman.common.hypervisor.response;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Heart beat response.
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class HeartBeatResponse {

    private int code;

    private String message;

    public static HeartBeatResponse succeed() {
        return new HeartBeatResponse(0, "ok");
    }

    public static HeartBeatResponse fail(String errMsg) {
        return new HeartBeatResponse(1, errMsg);
    }

    public static HeartBeatResponse fail(int code, String errMsg) {
        return new HeartBeatResponse(code, errMsg);
    }

}
