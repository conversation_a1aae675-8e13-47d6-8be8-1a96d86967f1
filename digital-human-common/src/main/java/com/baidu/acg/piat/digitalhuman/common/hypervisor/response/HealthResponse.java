// Copyright (C) 2021 Baidu Inc. All rights reserved.
package com.baidu.acg.piat.digitalhuman.common.hypervisor.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Health response.
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class HealthResponse {

    private int code;

    private String message;

    public static HealthResponse succeed() {
        return new HealthResponse(0, "ok");
    }

    public static HealthResponse fail(String errMsg) {
        return new HealthResponse(1, errMsg);
    }
}
