package com.baidu.acg.piat.digitalhuman.common.alita.arkit;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2021/11/11 8:44 下午
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FaceCaptureCalibrationStep {

    private String step;

    private int order;

    private List<Map<String, Double>> values;
}
