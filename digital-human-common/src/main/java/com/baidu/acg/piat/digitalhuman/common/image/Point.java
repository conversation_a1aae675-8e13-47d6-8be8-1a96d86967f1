package com.baidu.acg.piat.digitalhuman.common.image;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class Point {
    private int x;
    private int y;

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class FloatPoint {
        private float x;
        private float y;
    }
}


