package com.baidu.acg.piat.digitalhuman.common.utils;

import io.grpc.stub.StreamObserver;

import java.util.function.Function;

public class StreamObserverConverter {
    public static <I, O> StreamObserver<I> convert(StreamObserver<O> input, Function<I, O> converter) {
        return new StreamObserver<I>() {
            @Override
            public void onNext(I i) {
                O res = converter.apply(i);
                input.onNext(res);
            }

            @Override
            public void onError(Throwable throwable) {
                input.onError(throwable);
            }

            @Override
            public void onCompleted() {
                input.onCompleted();
            }
        };
    }
}
