package com.baidu.acg.piat.digitalhuman.common.richtext;

import java.io.IOException;
import java.util.List;
import lombok.Data;

import com.baidu.acg.piat.digitalhuman.common.util.DomUtil;
import com.baidu.acg.piat.digitalhuman.common.utils.JsonUtil;
@Data
public class FusionTag {

    private SpeakTag speak;

    private AnimojiTag animoji;

    private List<AnimojiTag> animojis;

    public static FusionTag fromXml(String xml) throws IOException {
        return JsonUtil.readValue(DomUtil.xml2Json(xml, false), FusionTag.class);
    }
}
