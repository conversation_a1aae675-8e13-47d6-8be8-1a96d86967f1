// Copyright (C) 2020 Baidu Inc. All rights reserved.

package com.baidu.acg.piat.digitalhuman.common.console.video;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * VideoStatus
 * <p>
 * state-machine
 * 正常成功的流： INIT -> PREPARE_RENDER  -> RENDERING ->  PROCESSING -> SUCCESS
 * 任以阶段发生错误进入FAILED状态    --->  ERROR
 * RENDERING过程中收到异常的流状态进入COMPLETED状态，预留的状态  RENDERING  ->  COMPLETED
 *
 * <AUTHOR>
 * @since 2020-12-10
 */
@Getter
@AllArgsConstructor
public enum ProgressStatus {

    /**
     * 任务被提交
     */
    SUBMIT(true, false),

    /**
     * 任务被调度到
     */
    SCHEDULED(false, false),

    /**
     * handling init status, use{@link #SCHEDULED} instead
     */
    @Deprecated
    INIT(false, false),

    /**
     * 数字人会话已经连接，但是x264还未开启的时候。
     */
    PREPARE_RENDER(false, false),

    /**
     * 接受x264流
     */
    RENDERING(false, false),

    /**
     * 视频合成中
     */
    PROCESSING(false, false),

    /**
     * 失败了，但是这种失败，可以通过重试，有可能恢复，
     */
    FAILED(true, false),

    /**
     * 失败了且属于无法重试的错误，例如文本错误等。
     */
    ERROR(false, true),

    /**
     *
     */
    SUCCEED(false, true),

    /**
     * 任务被CANCELE
     */
    CANCELED(false, true),

    ;

    private boolean needScheduled;

    private boolean terminated;

}
