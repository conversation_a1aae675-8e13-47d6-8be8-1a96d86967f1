// Copyright (C) 2020 Baidu Inc. All rights reserved.
package com.baidu.acg.piat.digitalhuman.common.project;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

import javax.validation.Valid;
import javax.validation.constraints.Min;

import com.baidu.acg.piat.digitalhuman.common.exception.DigitalHumanCommonException;

/**
 * ConversationConfig
 *
 * <AUTHOR> (<EMAIL>)
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class ConversationConfig {

    @Builder.Default
    private boolean pullbackSwitch = false;

    @Builder.Default
    @Min(value = 1, message = "pull back trigger round cannot less then 1")
    private Integer pullbackTriggerRound = 3;

    @Valid
    @Builder.Default
    private List<PullbackConfig> pullbackConfigList = new ArrayList<>(0);

    public void check() {
        if (pullbackSwitch && pullbackConfigList.isEmpty()) {
            throw new DigitalHumanCommonException("PullbackConfigList cannot be empty if pullback switch on");
        }

        for (PullbackConfig pullbackConfig : pullbackConfigList) {
            pullbackConfig.check();
        }
    }

}
