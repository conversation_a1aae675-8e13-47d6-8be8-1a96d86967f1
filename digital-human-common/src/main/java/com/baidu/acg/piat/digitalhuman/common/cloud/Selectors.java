package com.baidu.acg.piat.digitalhuman.common.cloud;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;
import java.util.stream.Collectors;

import javax.annotation.Nullable;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class Selectors {

    @Nullable
    private List<Selector> filters = new ArrayList<>();

    @Nullable
    private List<Selector> comparators = new ArrayList<>();


    public static Selectors convertSelectors(com.baidu.acg.digitalhuman.cloud.grpc.Selectors selectors) {
        Selectors output = new Selectors();
        if (selectors == null) {
            return output;
        }
        for (var selector : selectors.getFiltersList()) {
            output.add(convertSelector(selector));
        }
        for (var selector : selectors.getComparatorsList()) {
            output.add(convertSelector(selector));
        }
        return output;
    }

    public static Selectors.Selector convertSelector(com.baidu.acg.digitalhuman.cloud.grpc.Selector selector) {
        Selectors.Selector output = new Selectors.Selector();
        output.setOperator(Operator.valueOf(selector.getOperator().name()));
        output.setData(selector.getData());
        output.setLabel(selector.getLabel());
        return output;
    }


    public com.baidu.acg.digitalhuman.cloud.grpc.Selectors to() {
        return com.baidu.acg.digitalhuman.cloud.grpc.Selectors.newBuilder()
                .addAllFilters(filters.stream().map(Selector::to).collect(Collectors.toList()))
                .addAllComparators(comparators.stream().map(Selector::to).collect(Collectors.toList()))
                .build();
    }


    public void add(Selector selector) {
        switch (selector.operator) {
            case MATCH:
            case GREATER:
            case LESS:
            case CONTAIN:
                filters.add(selector);
                break;
            case MIN:
            case MAX:
                filters.add(new Selector(Operator.CONTAIN, selector.getLabel(), ""));
                comparators.add(selector);
                break;
            default:
                throw new RuntimeException("Unsupported selector");
        }
    }

    private boolean filter(Labels labels) {
        // all selector should be matched
        for (Selector selector : filters) {
            if (!selector.filter(labels)) {
                return false;
            }
        }
        return true;
    }

    private List<String> applyComparators(Map<String, Labels> instanceLabels) {
        for (Selector selector : comparators) {
            instanceLabels = selector.bestMatch(instanceLabels);
        }
        return new ArrayList<>(instanceLabels.keySet());
    }

    public List<String> bestMatch(Map<String, Labels> instanceLabels) {
        Map<String, Labels> instanceCopy = new TreeMap<>(instanceLabels);
        instanceCopy.entrySet().removeIf(entry -> !filter(entry.getValue()));
        return applyComparators(instanceCopy);
    }


    public boolean isEmpty() {
        return filters.isEmpty() && comparators.isEmpty();
    }

    public enum Operator {
        CONTAIN, MATCH, MAX, MIN, GREATER, LESS
    }

    @NoArgsConstructor
    public static class Labels extends HashMap<String, String> {
        public Labels(Map<String, String> labelMap) {
            super(labelMap);
        }
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Selector {
        private Operator operator;
        private String label;
        private String data;

        public com.baidu.acg.digitalhuman.cloud.grpc.Selector to() {
            return com.baidu.acg.digitalhuman.cloud.grpc.Selector.newBuilder()
                    .setData(data)
                    .setLabel(label)
                    .setOperator(com.baidu.acg.digitalhuman.cloud.grpc.Selector.Operator.valueOf(operator.name()))
                    .build();
        }


        public boolean filter(Labels labels) {
            // 对于单一的筛选条件， 多个label， 只要有一个满足， 表示正确
            for (var entry : labels.entrySet()) {
                if (filter(entry.getKey(), entry.getValue())) {
                    return true;
                }
            }
            return false;
        }

        public boolean filter(String label, String value) {
            if (!this.label.equals(label)) {
                return false;
            }
            switch (operator) {
                case CONTAIN:
                    // ignore value
                    return true;
                case MATCH:
                    return this.data.equals(value);
                case GREATER:
                    return value.compareTo(data) > 0;
                case LESS:
                    return value.compareTo(data) < 0;
                default:
                    return false;
            }
        }

        public Map<String, Labels> bestMatch(Map<String, Labels> instanceLabels) {
            if (instanceLabels.isEmpty()) {
                return Collections.emptyMap();
            } else if (instanceLabels.size() == 1) {
                return instanceLabels;
            }

            // label value to id map
            TreeMap<String, List<String>> valueToIdMap = new TreeMap<>();
            instanceLabels.forEach((id, labels) -> {
                String labelValue = labels.get(label);
                if (labelValue == null) {
                    return;
                }
                valueToIdMap.computeIfAbsent(labelValue, k -> new ArrayList<>()).add(id);
            });
            if (valueToIdMap.isEmpty()) {
                return Collections.emptyMap();
            }
            List<String> leftIds = null;
            if (operator == Operator.MAX) {
                leftIds = valueToIdMap.lastEntry().getValue();
            } else if (operator == Operator.MIN) {
                leftIds = valueToIdMap.firstEntry().getValue();
            } else {
                throw new RuntimeException("Unsupported operator to compare");
            }

            ArrayList<String> removeIds = new ArrayList<>(instanceLabels.keySet());
            removeIds.removeAll(leftIds);
            for (String id : removeIds) {
                instanceLabels.remove(id);
            }
            return instanceLabels;
        }
    }
}
