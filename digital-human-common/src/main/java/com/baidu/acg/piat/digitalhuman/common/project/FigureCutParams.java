// Copyright (C) 2020 Baidu Inc. All rights reserved.

package com.baidu.acg.piat.digitalhuman.common.project;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * FigureCutParams
 *
 * <AUTHOR>
 * @since 2020-03-30
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class FigureCutParams {

    private String cutXPercent;

    private String cutYPercent;

    private String cutWidthPercent;

    private String cutHeightPercent;

    private String widthRatio;

    private String positionCenterXPercent;

    private String positionBottomYPercent;
}
