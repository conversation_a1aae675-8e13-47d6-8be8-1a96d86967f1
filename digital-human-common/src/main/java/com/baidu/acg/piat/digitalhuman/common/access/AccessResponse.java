// Copyright (C) 2019 Baidu Inc. All rights reserved.
package com.baidu.acg.piat.digitalhuman.common.access;

import com.baidu.acg.piat.digitalhuman.common.model.BaseResponse;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * AccessResponse
 *
 * <AUTHOR>
 * @since 2019-09-20
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AccessResponse<T> extends BaseResponse {
    private T result;

    public static <T> AccessResponse<T> success(T result) {
        return new AccessResponse<>(result);
    }

    public static <T> AccessResponse<T> fail(int code, String message) {
        AccessResponse<T> response = new AccessResponse<T>();
        response.setCode(code);
        response.setSuccess(false);
        response.setMessage(new Message(message));
        return response;
    }

    public static <T> AccessResponse<T> fail(String message) {
        return fail(0, message);
    }
}
