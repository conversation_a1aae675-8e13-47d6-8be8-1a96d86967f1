package com.baidu.acg.piat.digitalhuman.common.exception;

public  enum VisKouboError {
    BAD_REQUEST(400, Error.BAD_REQUEST.getCode(), Error.BAD_REQUEST.getMessage()),
    MISSING_REQUIRED_PARAMETERS(110001, Error.MISSING_REQUIRED_PARAMETERS.getCode(), Error.MISSING_REQUIRED_PARAMETERS.getMessage()),
    IMAGE_VALIDATE_FAILED(110002, Error.IMAGE_VALIDATE_FAILED.getCode(), Error.IMAGE_VALIDATE_FAILED.getMessage()),
    POSITION_VALIDATE_FAILED(110003, Error.POSITION_VALIDATE_FAILED.getCode(), Error.POSITION_VALIDATE_FAILED.getMessage()),
    PARAMETER_VALUE_ERROR(110004, Error.PARAMETER_VALUE_ERROR.getCode(), Error.PARAMETER_VALUE_ERROR.getMessage()),
    TTS_VALIDATE_FAILED(110005, Error.TTS_VALIDATE_FAILED.getCode(), Error.TTS_VALIDATE_FAILED.getMessage()),
    SERVER_ERROR(500, Error.SERVER_ERROR.getCode(), Error.SERVER_ERROR.getMessage()),
    ADD_TASK_ERROR(210001, Error.ADD_TASK_ERROR.getCode(), Error.ADD_TASK_ERROR.getMessage()),
    TASK_ID_ERROR(210003, Error.TASK_ID_ERROR.getCode(), Error.TASK_ID_ERROR.getMessage()),
    DOWNLOAD_VIDEO_ERROR(210006, Error.DOWNLOAD_VIDEO_ERROR.getCode(), Error.DOWNLOAD_VIDEO_ERROR.getMessage()),
    CALLBACK_ERROR(210007, Error.CALLBACK_ERROR.getCode(), Error.CALLBACK_ERROR.getMessage()),
    VIDEO_TASK_EXPIRED(1125, Error.VIDEO_TASK_EXPIRED.getCode(), Error.VIDEO_TASK_EXPIRED.getMessage()),

    FAILED_TO_CREATE_VIS_TASK(1108, Error.FAILED_TO_CREATE_VIS_TASK.getCode(), Error.FAILED_TO_CREATE_VIS_TASK.getMessage()),
    FAILED_TO_QUERY_VIS_TASK(1109, Error.FAILED_TO_QUERY_VIS_TASK.getCode(), Error.FAILED_TO_QUERY_VIS_TASK.getMessage()),
    FAILED_TO_GET_VIS_VIDEO(1110, Error.FAILED_TO_GET_VIS_VIDEO.getCode(), Error.FAILED_TO_GET_VIS_VIDEO.getMessage()),
    FAILED_TO_UPLOAD_VIS_VIDEO(1111, Error.FAILED_TO_UPLOAD_VIS_VIDEO.getCode(), Error.FAILED_TO_UPLOAD_VIS_VIDEO.getMessage()),
    VIS_VIDEO_TIMEOUT(1112, Error.VIS_VIDEO_TIMEOUT.getCode(), Error.VIS_VIDEO_TIMEOUT.getMessage()),
    FAILED_TO_UPDATE_VIDEO_PROGRESS(1113, Error.FAILED_TO_UPDATE_VIDEO_PROGRESS.getCode(), Error.FAILED_TO_UPDATE_VIDEO_PROGRESS.getMessage());


    private int srcCode;
    private int dstCode;
    private String message;


    private VisKouboError(int srcCode, int dstCode, String message) {
        this.srcCode = srcCode;
        this.dstCode = dstCode;
        this.message = message;
    }

    public int getSrcCode() {
        return this.srcCode;
    }

    public int getDstCode() {
        return this.dstCode;
    }

    public String getMessage() {
        return this.message;
    }

}

