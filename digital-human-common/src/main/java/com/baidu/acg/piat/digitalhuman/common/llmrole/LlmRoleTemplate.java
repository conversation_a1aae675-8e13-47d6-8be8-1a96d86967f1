package com.baidu.acg.piat.digitalhuman.common.llmrole;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;

/**
 * 对应交互的模板
 */
@Data
@Slf4j
@Builder
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class LlmRoleTemplate {

    private String llmRoleTemplateId;

    private String templateName;

    private Integer templateType;

    private String templateIconUrl;

    private String exampleVideoUrl;

    // 横版horizontal/竖版vertical
    private String screenType;

    private CustomizedConfig customizedConfig;

    private String llmRoleId;

    private boolean del = false;

    private String createTime;

    private String updateTime;
}
