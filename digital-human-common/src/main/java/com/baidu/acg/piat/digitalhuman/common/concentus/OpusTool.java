package com.baidu.acg.piat.digitalhuman.common.concentus;

public class OpusTool {
    private OpusEncoder encoder;
    private OpusDecoder decoder;
    private int sampleRate;
    private int channels;
    private int frameTimeMs;
    private int compressRatio;
    private int bitDep;
    private int frameSize;

    public OpusTool(int sampleRate, int channels, int frameTimeMs, int bitDep) throws OpusException {
        this.sampleRate = sampleRate;
        this.channels = channels;
        this.frameTimeMs = frameTimeMs;
//        this.compressRatio = compressRatio;
        this.bitDep = bitDep;
        frameSize = sampleRate / 1000 * frameTimeMs;
//        int bitrate = sampleRate * bitDep * channels / compressRatio;
        int bitrate = 96000;
        encoder = new OpusEncoder(sampleRate, channels, OpusApplication.OPUS_APPLICATION_AUDIO);
        encoder.setBitrate(bitrate);
//            encoder.setBandwidth(OpusBandwidth.OPUS_BANDWIDTH_AUTO);
//            encoder.setLSBDepth(16);
//            encoder.setUseVBR(false);
//            encoder.setUseConstrainedVBR(false);
//            encoder.setUseInbandFEC(false);
//            encoder.setForceChannels(OpusConstants.OPUS_AUTO);
        encoder.setSignalType(OpusSignal.OPUS_SIGNAL_VOICE);
        encoder.setComplexity(10);
        decoder = new OpusDecoder(sampleRate, channels);
    }

    public short[] BytesToShorts(byte[] input, int offset, int length) {
        short[] processedValues = new short[length / 2];
        for (int c = 0; c < processedValues.length; c++) {
            short a = (short) (((int) input[(c * 2) + offset]) & 0xFF);
            short b = (short) (((int) input[(c * 2) + 1 + offset]) << 8);
            processedValues[c] = (short) (a | b);
        }

        return processedValues;
    }

    public byte[] encode(byte[] pcmData) throws OpusException {
        byte[] dataPacket = new byte[frameSize * (bitDep / 8)];
        int bytesEncoded = encoder.encode(pcmData, 0, frameSize, dataPacket, 0, dataPacket.length);
        byte[] packet = new byte[bytesEncoded];
        System.arraycopy(dataPacket, 0, packet, 0, bytesEncoded);
        return packet;
    }
}
