package com.baidu.acg.piat.digitalhuman.common.callback;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class CallbackTaskCreateRequest {
    private CallbackTaskType type;
    private String callbackUrl;
    private Object callbackData;
}
