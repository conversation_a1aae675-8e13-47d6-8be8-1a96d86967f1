// Copyright (C) 2020 Baidu Inc. All rights reserved.

package com.baidu.acg.piat.digitalhuman.common.console.video;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * VideoUpdateRequest
 *
 * <AUTHOR>
 * @since 2020-12-10
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class VideoUpdateRequest {

    private String id;

    private String appId;

    /**
     * optional
     */
    private String sessionId;

    /**
     * optional, 用于更新失败错误的情况。
     */
    private ProgressStatus status;

    private String downloadUrl;

    private String audioUrl;

    /**
     * optional
     */
    private int code = 0;

    /**
     * optional
     */
    private String message;

    private String showMessage;

    private String subtitleFileUrl;

    public static VideoUpdateRequest scheduledStatus(String videoId,
                                                     String appId) {
        return VideoUpdateRequest.builder().id(videoId)
                .appId(appId)
                .status(ProgressStatus.SCHEDULED)
                .build();
    }

    public static VideoUpdateRequest prepareSessionId(String videoId,
                                                      String appId,
                                                      String sessionId) {
        return VideoUpdateRequest.builder().id(videoId)
                .sessionId(sessionId)
                .appId(appId)
                .status(ProgressStatus.PREPARE_RENDER)
                .build();
    }

    public static VideoUpdateRequest error(String videoId
            , String appId, int code, String message
            , String showMessage) {
        return VideoUpdateRequest.builder().id(videoId)
                .appId(appId)
                .status(ProgressStatus.ERROR)
                .code(code)
                .message(message)
                .showMessage(showMessage)
                .build();
    }

}
