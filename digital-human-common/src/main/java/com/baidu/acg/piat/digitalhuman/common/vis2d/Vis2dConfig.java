// Copyright (C) 2019 Baidu Inc. All rights reserved.
package com.baidu.acg.piat.digitalhuman.common.vis2d;

import lombok.Data;

/**
 * Vis2dConfig
 *
 * <AUTHOR>
 * @since 2019-08-23
 */
@Data
public class Vis2dConfig {

    private String baseUrl;

    private long connectTimeoutMillis = 30_000L;
    private long readTimeoutMillis = 300_000L;
    private long writeTimeoutMillis = 300_000L;
    private long callTimeoutMillis = 300_000L;
    private int maxConnection = 100;
    private long connectionKeepAliveMillis = 300_000L;

    private boolean printParamsEnabled = false;

}
