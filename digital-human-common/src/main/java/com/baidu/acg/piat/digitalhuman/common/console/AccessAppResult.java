// Copyright (C) 2019 Baidu Inc. All rights reserved.
package com.baidu.acg.piat.digitalhuman.common.console;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Author: <EMAIL>
 * @Date: 2020/3/20
 */

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AccessAppResult {

    private String appId;

    private String appKey;

    /**
     * owner userId , immutable
     */
    private String userId;

    /**
     * mutable
     */
    private String name;

    /**
     * mutable
     */
    private String description;

    /**
     * mutable
     */
    private String characterImage;

    private String createTime;

    private String updateTime;

    private boolean enabled = false;

}
