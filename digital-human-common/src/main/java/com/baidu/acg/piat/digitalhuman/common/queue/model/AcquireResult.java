// Copyright (C) 2019 Baidu Inc. All rights reserved.
package com.baidu.acg.piat.digitalhuman.common.queue.model;

import com.baidu.acg.piat.digitalhuman.common.webrtc.resource.pool.model.AcquireResponse;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * AcquireResult
 *
 * <AUTHOR>
 * @since 2019-07-24
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class AcquireResult {
    private String userId;
    private String userToken;
    private AcquireStatus status;
    private AcquireResponse result;
    private QueueDetail queue;
}
