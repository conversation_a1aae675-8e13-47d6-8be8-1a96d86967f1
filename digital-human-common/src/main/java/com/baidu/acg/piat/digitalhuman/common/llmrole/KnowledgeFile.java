package com.baidu.acg.piat.digitalhuman.common.llmrole;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;


/**
 * 知识库具体实体
 *
 * <AUTHOR>
 * @since 2023/12/1 10:48
 */
@Data
@Builder
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class KnowledgeFile {

    // copilot平台区分了file和document，file为上传的文件，document为最终关联后的文件。平台的KnowledgeFile的fileId指最终关联后的documentId。
    private String fileId;

    private String name;

    private String status;

    private String url;

    private String createTime;

}