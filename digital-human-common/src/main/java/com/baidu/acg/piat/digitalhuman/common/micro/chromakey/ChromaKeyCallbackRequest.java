package com.baidu.acg.piat.digitalhuman.common.micro.chromakey;

import com.baidu.acg.piat.digitalhuman.common.console.video.ProgressStatus;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ChromaKeyCallbackRequest {

    @NotBlank(message = "videoId cannot be empty")
    private String videoId;

    @NotBlank(message = "status cannot be empty")
    private ProgressStatus status;

    private String downloadUrl;

    private String description;
}
