// Copyright (C) 2019 Baidu Inc. All rights reserved.
package com.baidu.acg.piat.digitalhuman.common.session;

import java.time.ZonedDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Participater
 *
 * <AUTHOR>
 * @since 2019-07-12
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Endpoint {

    private String id;

    private String host;

    private String ip;

    private Type type;

    private boolean joined;

    private ZonedDateTime updateTime;

    public enum Type {
        PROXY_SERVER, AGENT, CLIENT
    }
}
