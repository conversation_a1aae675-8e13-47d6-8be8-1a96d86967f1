// Copyright (C) 2021 Baidu Inc. All rights reserved.

package com.baidu.acg.piat.digitalhuman.common.console.video;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * Text2VideoScheduleRequest
 *
 * <AUTHOR>
 * @date 2021-01-12
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class VideoScheduleRequest extends Text2VideoParams {

    @NotBlank(message = "videoId cannot be blank")
    private String videoId;

    @NotEmpty(message = "text cannot be blank")
    private String text;

    public VideoScheduleRequest(Text2VideoParams text2VideoParams, String videoId, String text) {
        super(text2VideoParams);
        this.videoId = videoId;
        this.text = text;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ComplexVideoScheduleRequest extends Text2VideoParams {

        @NotBlank(message = "videoId cannot be blank")
        private String videoId;

        @NotEmpty(message = "texts cannot be blank")
        private List<String> texts;

        public ComplexVideoScheduleRequest(Text2VideoParams text2VideoParams, String videoId, List<String> texts) {
            super(text2VideoParams);
            this.videoId = videoId;
            this.texts = texts;
        }

        public ComplexVideoScheduleRequest(Text2VideoParams text2VideoParams, String videoId, String text) {
            super(text2VideoParams);
            this.videoId = videoId;
            this.texts = List.of(text.split(Text2VideoRequest.ComplexText2VideoRequest.CONTINUE_SPLIT));
        }

        public static ComplexVideoScheduleRequest convert(VideoScheduleRequest simpleRequest) {
            return new ComplexVideoScheduleRequest(simpleRequest, simpleRequest.getVideoId(), simpleRequest.getText());
        }
    }

}
