package com.baidu.acg.piat.digitalhuman.common.statistic;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CharacterResourceData {
    private String characterName;
    private int assignedToTenancyRoutes;
    private int assignedToAppRoutes;
    private int realtimeUseingRoutes;
}
