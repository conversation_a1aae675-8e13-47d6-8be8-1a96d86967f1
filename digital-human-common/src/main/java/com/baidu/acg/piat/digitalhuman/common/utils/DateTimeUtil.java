// Copyright (C) 2019 Baidu Inc. All rights reserved.
package com.baidu.acg.piat.digitalhuman.common.utils;

import com.baidu.acg.piat.digitalhuman.common.exception.DigitalHumanCommonException;

import java.text.SimpleDateFormat;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Calendar;
import java.util.Date;

import lombok.extern.slf4j.Slf4j;

/**
 * datetime formatter util
 *
 * <AUTHOR>
 * create on 2019-03-21
 */
@Slf4j
public class DateTimeUtil {

    public static DateTimeFormatter formatter = DateTimeFormatter.ISO_OFFSET_DATE_TIME;

    public static String PATTERN = "yyyy-MM-dd HH:mm:ss";

    public static String BEIJING_ZONE = "Asia/Shanghai";

    public static String format(ZonedDateTime dateTime) {
        if (dateTime == null) {
            return null;
        }
        return dateTime.format(formatter);
    }

    public static String format(long mills) {
        Date date = new Date(mills);
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat(PATTERN);
        return simpleDateFormat.format(date);
    }

    public static String format(ZonedDateTime dateTime, String pattern, String zoneId) {
        DateTimeFormatter bjFormatter = DateTimeFormatter.ofPattern(pattern).withZone(ZoneId.of(zoneId));
        return bjFormatter.format(dateTime);
    }

    public static String beijingTimeString(ZonedDateTime dateTime) {
        return format(dateTime, PATTERN, BEIJING_ZONE);
    }

    public static String getNowTimeString() {
        ZonedDateTime now = ZonedDateTime.now();
        return beijingTimeString(now);
    }

    public static void validateTimeRange(ZonedDateTime beginDateTime, ZonedDateTime endDateTime) {
        if (beginDateTime != null && endDateTime != null) {
            if (beginDateTime.toInstant().compareTo(endDateTime.toInstant()) > 0) {
                throw new DigitalHumanCommonException("Invalid time range");
            }
        }
    }

    public static ZonedDateTime parse(String dataTimeStr) {
        try {
            DateTimeFormatter bjFormatter = DateTimeFormatter.ofPattern(PATTERN).withZone(ZoneId.of(BEIJING_ZONE));
            return ZonedDateTime.parse(dataTimeStr, bjFormatter);
        } catch (Exception e) {
            throw new DigitalHumanCommonException("fail to parse date time [" + dataTimeStr + "]", e);
        }
    }

    public static long getTodayStartTimeMs() {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTime().getTime();
    }
}
