package com.baidu.acg.piat.digitalhuman.common.constans;

public class StatisticConstant {

    /**
     * 该指标为了满足客户取到对应时间区间峰值的需求,所以本指标的采集间隔是1s
     */

    public static final String CURRENT_SESSIONS_NAME = "digitalhuman_current_sessions";
    public static final String TOTAL_SESSIONS_NAME = "digitalhuman_total_sessions";
    public static final String TOTAL_DIALOGS_NAME = "digitalhuman_total_dialogs";
    public static final String SESSION_LATENCY_NAME = "digitalhuman_session_latency";
    public static final long SESSION_LATENCY_NAME_MAXVALUE = 24 * 3600 * 30 * 1000;
    public static final String SESSION_CONNECT_NAME = "digitalhuman_session_connect";
    public static final long SESSION_CONNECT_NAME_MAXVALUE = 200 * 1000;
    public static final String SESSION_CONNECT_FAILED_NAME = "digitalhuman_session_connect_failed";
    public static final String SESSION_TEXTRENDER_START_NAME = "digitalhuman_session_textrender_start";
    public static final String SESSION_TEXTRENDER_FAILED_NAME = "digitalhuman_session_textrender_failed";
    public static final long SESSION_TEXTRENDER_START_MAXVALUE = 10 * 1000;
    public static final String DIGITALHUMAN_CHATBOT_CALL_NAME = "digitalhuman_chatbot_call";
    public static final long DIGITALHUMAN_CHATBOT_CALL_MAXVALUE = 100 * 1000;
    public static final String DIGITALHUMAN_CHATBOT_CALL_FAILED_NAME = "digitalhuman_chatbot_call_failed";

    public static final String DIGITALHUMAN_ASR_SYNC_CALL_NAME = "digitalhuman_asr_sync_call";
    public static final long DIGITALHUMAN_ASR_CALL_MAXVALUE = 100 * 1000;
    public static final String DIGITALHUMAN_ASR_SYNC_CALL_FAILED_NAME = "digitalhuman_asr_sync_call_failed";


    public static final String DIGITALHUMAN_ASR_ASYNC_INTERRUPT_DUR_NAME = "digitalhuman_asr_async_interrupt";
    public static final String DIGITALHUMAN_ASR_ASYNC_CALL_FAILED_NAME = "digitalhuman_asr_async_call_failed";

    public static final String DIGITALHUMAN_ASR_INTERRUPT_TEXTRENDER_START_NAME =
            "digitalhuman_asr_interrupt_textrender_start";


    public static final String CHARACTER_RESOURCE_COUNT = "digitalhuman_character_resource_total_count";
    public static final String CHARACTER_RESOURCE_BUSY = "digitalhuman_character_resource_busy_count";


    public static final String LEAFLET_WS_RESPONSE_CODE_NAME = "digitalhuman_leaflet_ws_response_code";

    public static final String TOTAL_SESSIONS_QUERY = "digitalhuman_total_sessions_total{appId=\"%s\"}";
    public static final String CURRENT_SESSIONS_QUERY = "sum(digitalhuman_current_sessions{appId=\"%s\"})";
    public static final String SESSION_LATENCY_QUERY = "digitalhuman_session_latency_seconds_sum{appId=\"%s\"}";
    public static final String SESSION_TREND_QUERY = "digitalhuman_current_sessions{appId=\"%s\"}";
    public static final String CURRENT_SESSIONS_APPID_QUERY = "sum(digitalhuman_current_sessions)by(appId)";
    public static final String CURRENT_SESSIONS_USAGETREND_QUERY = "sum(digitalhuman_current_sessions)" +
            "by(appId,characterImage,resourceType)";


    public static final long DEFAULT_INTERVAL_IN_SECONDS = 60;

}
