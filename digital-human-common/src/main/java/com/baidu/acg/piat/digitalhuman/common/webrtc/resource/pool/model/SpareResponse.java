// Copyright (C) 2019 Baidu Inc. All rights reserved.
package com.baidu.acg.piat.digitalhuman.common.webrtc.resource.pool.model;

import com.baidu.acg.piat.digitalhuman.common.resource.AppResourceVO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

import com.baidu.acg.piat.digitalhuman.common.hypervisor.request.HeartBeatRequest;
import com.baidu.acg.piat.digitalhuman.common.thirdparty.model.ThirdPartyServiceResponse;

/**
 * SpareReponse
 *
 * <AUTHOR>
 * @since 2019-08-05
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SpareResponse extends ThirdPartyServiceResponse {

    private Map<String, List<HeartBeatRequest>> resources;

    private List<AppResourceVO> appResourceList;

    public static SpareResponse succeed(Map<String, List<HeartBeatRequest>> resources) {
        SpareResponse response = new SpareResponse();
        response.setResources(resources);
        response.setCode(0);
        response.setMessage("ok");
        return response;
    }

    public static SpareResponse succeed(List<AppResourceVO> appResourceList) {
        SpareResponse response = new SpareResponse();
        response.setAppResourceList(appResourceList);
        response.setCode(0);
        response.setMessage("ok");
        return response;
    }

    public static SpareResponse fail(String message) {
        return fail(1, message);
    }

    public static SpareResponse fail(int code, String message) {
        SpareResponse response = new SpareResponse();
        response.setCode(code);
        response.setMessage(message);
        return response;
    }
}
