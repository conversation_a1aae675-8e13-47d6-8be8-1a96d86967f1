package com.baidu.acg.piat.digitalhuman.common.utils;

import java.awt.Image;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.Base64;
import java.util.Optional;

import org.apache.commons.lang3.StringUtils;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 */
@Slf4j
public class ImageUtil {
    /**
     * 调整图片大小
     * @param image
     * @param width
     * @param height
     * @return
     */
    public static BufferedImage resizeImage(BufferedImage image, int width, int height) {
        BufferedImage resizedImage = new BufferedImage(width, height, image.getType());

        Image originalImage = image.getScaledInstance(width, height, Image.SCALE_DEFAULT);

        var g = resizedImage.createGraphics();
        g.drawImage(originalImage, 0, 0, width, height, null);
        g.dispose();

        return resizedImage;
    }

    /**
     * 图片URL转Base64编码
     *
     * @param imgUrl
     * @return
     */
    public static Optional<String>  imageUrlToBase64(String imgUrl) {
        if (StringUtils.isBlank(imgUrl)) {
            return Optional.empty();
        }
        URL url = null;
        InputStream is = null;
        ByteArrayOutputStream outStream = null;
        HttpURLConnection httpUrl = null;

        try {
            url = new URL(imgUrl);
            httpUrl = (HttpURLConnection) url.openConnection();
            httpUrl.connect();
            httpUrl.getInputStream();

            is = httpUrl.getInputStream();
            outStream = new ByteArrayOutputStream();

            byte[] buffer = new byte[1024];
            int len = 0;
            while ((len = is.read(buffer)) != -1) {
                outStream.write(buffer, 0, len);
            }
            Base64.Encoder base64Encoder = Base64.getEncoder();
            return Optional.of(base64Encoder.encodeToString(outStream.toByteArray()));
        } catch (Exception e) {
            log.error("imageUrlToBase64 error,imgUrl:{}", imgUrl, e);
        } finally {
            try {
                if (is != null) {
                    is.close();
                }
                if (outStream != null) {
                    outStream.close();
                }
                if (httpUrl != null) {
                    httpUrl.disconnect();
                }
            } catch (Exception e) {
                log.error("imageUrlToBase64 error,imgUrl:{}", imgUrl, e);
            }
        }
        return Optional.empty();
    }
}
