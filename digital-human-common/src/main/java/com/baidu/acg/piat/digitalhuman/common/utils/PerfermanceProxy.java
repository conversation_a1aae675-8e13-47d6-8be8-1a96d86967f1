// Copyright (C) 2020 Baidu Inc. All rights reserved.
package com.baidu.acg.piat.digitalhuman.common.utils;

import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.InvocationHandler;
import java.lang.reflect.Method;
import java.lang.reflect.Proxy;

/**
 * PerfermanceProxy
 *
 * <AUTHOR>
 * @since 2020-01-07
 */
@Slf4j
public class PerfermanceProxy {


    @SuppressWarnings("unchecked")
    public static <T> T newProxy(Class<T> clazz, String description) {
        return (T) Proxy.newProxyInstance(PerfermanceProxy.class.getClassLoader(), new Class[]{clazz},
                invocationHandler(description));
    }


    public static InvocationHandler invocationHandler(String descrption) {
        return new InvocationHandler() {
            @Override
            public Object invoke(Object proxy, Method method, Object[] args) throws Throwable {
                long startTs = System.currentTimeMillis();
                try {
                    return method.invoke(proxy, args);
                } finally {
                    log.debug(descrption, System.currentTimeMillis() - startTs);
                }
            }
        };
    }
}
