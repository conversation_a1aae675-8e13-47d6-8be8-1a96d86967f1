package com.baidu.acg.piat.digitalhuman.common.statistic;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class SessionStatisticPageResult {
    private int pageNo;

    private int pageSize;

    private long totalCount;

    private int sessionCountSummary;

    private long sessionAvgDurationSummary;

    private int dialogCountSummary;

    private List<SessionStatisticData> result;
}
