// Copyright (C) 2019 Baidu Inc. All rights reserved.
package com.baidu.acg.piat.digitalhuman.common.queue;

import com.baidu.acg.piat.digitalhuman.common.webrtc.model.WebRtcConnection;
import java.util.List;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * ResourceRequest
 *
 * <AUTHOR>
 * @since 2019-07-29
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ResourceResponse {

    @Builder.Default
    private int code = 0;
    @Builder.Default
    private String message = "ok";
    private String sessionId;
    private ResourceInfo resource;
    private RtcInfo arguments;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ResourceInfo {
        private String ip;
        private Map<String, Integer> ports;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RtcInfo {
        private String rtcServerUrl;
        private String appId;
        private String roomId;
        private String agentId;
        private String proxyServerId;
        private String clientId;
        private String clientToken;

        public WebRtcConnection toWebRtcConnection(String websocketId) {
            return WebRtcConnection.builder()
                    .url(rtcServerUrl)
                    .appId(appId)
                    .roomId(roomId)
                    .userId(clientId)
                    .token(clientToken)
                    .pullStreamUserIds(List.of(proxyServerId))
                    .webSocketSessionId(websocketId)
                    .build();
        }

        public static RtcInfo fromWebRtcConnection(WebRtcConnection webRtcConnection) {
            return RtcInfo.builder().rtcServerUrl(webRtcConnection.getUrl())
                    .appId(webRtcConnection.getAppId())
                    .roomId(webRtcConnection.getRoomId())
                    .agentId("agentId")
                    .proxyServerId(webRtcConnection.getPullStreamUserIds().get(0))
                    .clientId(webRtcConnection.getUserId())
                    .clientToken(webRtcConnection.getToken())
                    .build();
        }

    }


}
