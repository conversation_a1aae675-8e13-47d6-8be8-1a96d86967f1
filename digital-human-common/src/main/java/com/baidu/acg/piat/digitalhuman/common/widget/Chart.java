// Copyright (C) 2020 Baidu Inc. All rights reserved.

package com.baidu.acg.piat.digitalhuman.common.widget;

import java.util.List;
import java.util.Map;
import lombok.Data;

/**
 * Chart
 *
 * <AUTHOR>
 * @since 2019-12-18
 */
@Data
public class Chart {

    private String type;

    private String title;

    private List<Map<String, Object>> data;

    private Map<String, String> keyMap;

    private Map<String, String> labelMap;

    private Map<String, String> titleMap;

    private Format formatX;

    private Direction vertical;

    private Map<String, Object> options;

    public enum Direction {
        vertical
    }

    public enum Format {

        text, date,
    }

    public enum Type {
        line, bar, pie,
    }

}
