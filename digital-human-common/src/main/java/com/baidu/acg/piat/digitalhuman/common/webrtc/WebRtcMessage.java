package com.baidu.acg.piat.digitalhuman.common.webrtc;

import com.baidu.acg.piat.digitalhuman.common.auth.model.AuthInfo;
import com.baidu.acg.piat.digitalhuman.common.queue.ResourceResponse;
import com.baidu.acg.piat.digitalhuman.common.queue.model.QueueDetail;
import com.baidu.acg.piat.digitalhuman.common.resource.ResourceInstance;
import com.baidu.acg.piat.digitalhuman.common.webrtc.model.WebRtcConnection;
import java.util.Map;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.codehaus.commons.nullanalysis.NotNull;

/**
 * Web rtc message.
 *
 * <AUTHOR>
 */
@Data
@Builder
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
public class WebRtcMessage {

    @Builder.Default
    private String requestId = UUID.randomUUID().toString();

    @NotNull
    private WebRtcMessageType type;

    @Builder.Default
    private Boolean success = true;

    private String message;

    private AuthInfo auth;

    private QueueDetail queue;

    private String sessionId;

    private ResourceInstance resource;

    private WebRtcConnection connection;

    private WebRtcSession arguments;

    private Map<String, Object> customizedItems;

    public static WebRtcMessage from(ResourceResponse resourceResponse, String websocketId) {
        return WebRtcMessage.builder().type(WebRtcMessageType.CONNECT)
                .connection(resourceResponse.getArguments().toWebRtcConnection(websocketId))
                .build();
    }

    public static WebRtcMessage fail(WebRtcMessageType type, String message) {
        return WebRtcMessage.builder().type(type).success(false).message(message).build();
    }
}
