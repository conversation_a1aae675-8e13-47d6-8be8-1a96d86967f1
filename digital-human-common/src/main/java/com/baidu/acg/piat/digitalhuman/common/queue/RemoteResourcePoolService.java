// Copyright (C) 2019 Baidu Inc. All rights reserved.
package com.baidu.acg.piat.digitalhuman.common.queue;

import com.baidu.acg.piat.digitalhuman.common.model.Response;
import com.baidu.acg.piat.digitalhuman.common.queue.model.RoomInfo;
import java.util.List;
import retrofit2.Call;
import retrofit2.http.Body;
import retrofit2.http.POST;

/**
 * RemoteResourcePoolService
 *
 * <AUTHOR>
 * @since 2019-07-24
 */
public interface RemoteResourcePoolService {

    @POST("/digitalhuman/resource/pool/acquireResult/list")
    Call<Response<List<RoomInfo>>> listAllRoom();

    @POST("/digitalhuman/resource/pool/acquireResult/acquire")
    Call<ResourceResponse> acquire(@Body ResourceRequest request);

}
