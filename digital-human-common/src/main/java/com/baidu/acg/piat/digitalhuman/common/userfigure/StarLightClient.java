package com.baidu.acg.piat.digitalhuman.common.userfigure;

import com.baidu.acg.piat.digitalhuman.common.exception.DigitalHumanCommonException;
import com.baidu.acg.piat.digitalhuman.common.model.Response;
import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.fasterxml.jackson.databind.module.SimpleModule;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import lombok.extern.slf4j.Slf4j;
import okhttp3.ResponseBody;
import retrofit2.Call;
import retrofit2.Retrofit;
import retrofit2.converter.jackson.JacksonConverterFactory;

import java.io.IOException;
import java.util.Objects;

@Slf4j
public class StarLightClient {

    private final String baseUrl;

    private final UserFigureService userFigureService;

    public StarLightClient(String baseUrl) {
        this.baseUrl = baseUrl;
        var retrofit = new Retrofit.Builder()
                .addConverterFactory(
                        JacksonConverterFactory.create(new ObjectMapper()
                                .registerModule(new JavaTimeModule())
                                .registerModule(new SimpleModule().addSerializer(String.class, new EmptyStringSerializer()))
                        ))
                .baseUrl(baseUrl)
                .build();

        userFigureService = retrofit.create(UserFigureService.class);
    }

    public String
    queryUserFigure(String figureName, String userId) throws DigitalHumanCommonException, IOException {
        return Objects.requireNonNull(userFigureService.queryUserFigure(figureName, userId).execute().body()).get("result").asText();
    }

    public UserFigureModel getById(Long id) throws DigitalHumanCommonException{
        Call<Response<UserFigureModel>> call = userFigureService.get(id);
        return callService(call, "get");
    }

    private <T> T callService(Call<Response<T>> call, String methodName) throws DigitalHumanCommonException {
        try {
            var response = call.execute();
            return getResult(response, methodName);
        } catch (DigitalHumanCommonException e) {
            log.warn("Fail to call platform service methodName={} url={}, ex=",
                    methodName, call.request().url(), e);
            throw e;
        } catch (Exception e) {
            log.error("Fail to call platform service methodName={} url={}, ex=",
                    methodName, call.request().url(), e);
            throw new DigitalHumanCommonException(buildMessage(methodName, "call service failed"), e);
        }
    }

    private String buildMessage(String method, String cause) {
        return method + "失败, 原因: " + cause;
    }

    private <T> RuntimeException handleResponseUnsuccessful(retrofit2.Response<T> response, String method) {
        try (ResponseBody errorBody = response.errorBody()) {
            String errorMessage = errorBody == null ? "unknown" : errorBody.string();
            return new RuntimeException(buildMessage(method, errorMessage));
        } catch (Exception e) {
            return new RuntimeException(buildMessage(method, "unknown server error"), e);
        }
    }

    private <T> T getResult(retrofit2.Response<Response<T>> response, String method)
            throws DigitalHumanCommonException {
        if (response.isSuccessful()) {
            var body = response.body();
            if (body == null) {
                return null;
            } else if (body.isSuccess()) {
                return body.getResult();
            } else {
                throw new DigitalHumanCommonException(body.getCode(),
                        buildMessage(method, body.getMessage().getGlobal()));
            }
        }

        throw handleResponseUnsuccessful(response, method);
    }

    public static class EmptyStringSerializer extends JsonSerializer<String> {
        @Override
        public void serialize(String s, JsonGenerator jsonGenerator, SerializerProvider serializerProvider)
                throws IOException {
            if (s != null && s.isEmpty()) {
                jsonGenerator.writeString("");
            } else {
                jsonGenerator.writeString(s);
            }
        }
    }
}
