package com.baidu.acg.piat.digitalhuman.common.material;

import com.baidu.acg.piat.digitalhuman.common.model.PageRequest;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.List;

/**
 * Created on 2021/8/6 11:08 上午
 *
 * <AUTHOR>
 */
@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
public class MaterialListRequest extends PageRequest {

    private String userId;

    private String name;

    private List<String> type;

    private List<String> positionId;
}
