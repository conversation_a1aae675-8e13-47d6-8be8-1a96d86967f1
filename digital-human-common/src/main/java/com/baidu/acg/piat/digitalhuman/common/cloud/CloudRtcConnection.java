// Copyright (C) 2019 Baidu Inc. All rights reserved.
package com.baidu.acg.piat.digitalhuman.common.cloud;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import com.baidu.acg.digitalhuman.cloud.grpc.RtcConnection;

/**
 * CloudRtcConnncetion
 *
 * <AUTHOR>
 * @since 2019-08-29
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy.class)
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CloudRtcConnection {
    private String rtcServerUrl;
    private String appId;
    private String roomName;
    private String clientId;
    private String clientToken;
    private String feedId;

    public static CloudRtcConnection from(RtcConnection rtcConnection) {
        return builder()
                .rtcServerUrl(rtcConnection.getRtcServerUrl())
                .appId(rtcConnection.getAppId())
                .roomName(rtcConnection.getRoomName())
                .clientId(rtcConnection.getClientId())
                .clientToken(rtcConnection.getClientToken())
                .feedId(rtcConnection.getFeedId())
                .build();
    }
}
