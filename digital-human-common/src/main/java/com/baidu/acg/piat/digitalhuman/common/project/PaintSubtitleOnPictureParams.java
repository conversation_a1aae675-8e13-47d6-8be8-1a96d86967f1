package com.baidu.acg.piat.digitalhuman.common.project;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class PaintSubtitleOnPictureParams {
    private Boolean paintSubtitleOnPicture = false;

    private Boolean subtitleSplittable = true;

    // todo: 2d不支持该字段，后续开发支持
    private Boolean paintQueryOnPicture = false;

    @Deprecated
    private Integer subtitleFontSize;

    // 字幕显示时长
    private Integer subtitleTTL;

    @Deprecated
    private String subtitleBackgroundColor;

    // 字幕距底边距离
    private Integer subtitleMarginPx;

    private String subtitleColor;

    private String subtitleSize;

    private String subtitleBackColor;

    private String subtitleFont;

    // 字幕位置
    private Integer subtitleLocationX;

    private Integer subtitleLocationY;
}
