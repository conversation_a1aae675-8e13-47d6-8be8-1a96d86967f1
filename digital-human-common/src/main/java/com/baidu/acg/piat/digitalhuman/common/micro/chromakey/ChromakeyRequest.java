package com.baidu.acg.piat.digitalhuman.common.micro.chromakey;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.annotation.Nullable;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class ChromakeyRequest {
    @NotBlank(message = "videoId cannot be blank")
    private String videoId;

    @NotBlank(message = "videoUrl cannot be blank")
    private String videoUrl;

    @NotBlank(message = "callbackUrl cannot be blank")
    private String callbackUrl;

    @NotEmpty(message = "config cannot be blank")
    private ChromakeyReqConfig config;

    @Nullable
    private ChromakeyReqAdditional additional;
}

