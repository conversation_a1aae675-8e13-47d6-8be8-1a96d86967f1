package com.baidu.acg.piat.digitalhuman.common.helper;

import com.baidu.acg.piat.digitalhuman.common.optlog.OptContent;
import com.baidu.acg.piat.digitalhuman.common.optlog.OptLog;

public class OptLogHelper {

    public static OptLog buildOptLog(OptContent optContent
            , String optObjectName, String optAccountId, String optUserId) {
        OptLog optLog = OptLog.builder()
                .optTime(System.currentTimeMillis())
                .optType(optContent.getOptType())
                .optModule(optContent.getOptModule())
                .optAccountId(optAccountId)
                .optUserId(optUserId)
                .optObjectName(optObjectName)
                .build();
        return optLog;
    }
}
