// Copyright (C) 2020 Baidu Inc. All rights reserved.

package com.baidu.acg.piat.digitalhuman.common.utils;

import java.lang.reflect.Field;
import java.lang.reflect.Method;

/**
 * ReflectUtil
 *
 * <AUTHOR>
 * @since 2020-11-23
 */
public class ReflectUtil {

    public static void setFieldValue(Object object, String fieldName, Object value)
            throws NoSuchFieldException, IllegalAccessException {
        Field field = object.getClass().getDeclaredField(fieldName);
        field.setAccessible(true);

        field.set(object, value);
    }

    public static Object getFieldValue(Object object, String fieldName)
            throws NoSuchFieldException, IllegalAccessException {
        Field field = object.getClass().getDeclaredField(fieldName);
        field.setAccessible(true);

        return field.get(object);
    }

    public static Method getMethod(Object object, String methodName, Class<?>... parameterTypes)
            throws NoSuchMethodException {
        Method method = object.getClass().getDeclaredMethod(methodName, parameterTypes);
        method.setAccessible(true);

        return method;
    }
}
