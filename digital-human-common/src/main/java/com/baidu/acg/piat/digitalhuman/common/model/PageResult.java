// Copyright (C) 2019 Baidu Inc. All rights reserved.
package com.baidu.acg.piat.digitalhuman.common.model;

import java.io.Serializable;
import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * PageResult
 *
 * <AUTHOR>
 * @since 2019-07-16
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PageResult<T> implements Serializable {

    int pageNo;

    int pageSize;

    long totalCount;

    List<T> result;

}
