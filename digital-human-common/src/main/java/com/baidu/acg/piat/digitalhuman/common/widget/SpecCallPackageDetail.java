// Copyright (C) 2020 Baidu Inc. All rights reserved.

package com.baidu.acg.piat.digitalhuman.common.widget;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * SpecCallPackageDetail
 * <p>
 * 移动定制，话费套餐明细
 *
 * <AUTHOR>
 * @since 2019-12-31
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SpecCallPackageDetail {

    private String title;

    private String vipDiscount;

    private String price;

    private String unit;

    private String detail;

    private List<Content> contents;

    private static class Content {
        private String subtitle;

        private String content;
    }
}
