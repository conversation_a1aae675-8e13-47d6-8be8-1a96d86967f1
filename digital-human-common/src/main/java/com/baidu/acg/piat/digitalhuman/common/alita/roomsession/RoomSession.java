package com.baidu.acg.piat.digitalhuman.common.alita.roomsession;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.Map;

import com.baidu.acg.piat.digitalhuman.alita.model.RtcConnection;

/**
 * Created on 2020/6/15 21:37.
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class RoomSession {

    private String appId;
    private String roomId;
    private String roomName;
    private String sessionId;
    private String leafletWsSessionId; // leaflet web socket session id.
    private int status;
    private AgentAddress address;
    private RTCConnection connection;
    private Date createTime;
    private String webSocketId;
    private String chargeToken;
    private Date chargeAlertTime;

    private String dialogId;
    private Map<String, String> connectParameters;

    private Boolean enableRtcPublish;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AgentAddress {
        private String host;
        private int port;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RTCConnection {
        /**
         * external server url
         */
        private String serverUrl;
        private String appId;
        private String roomName;
        private String clientId;
        private String clientToken;
        private String feedId; // 用户推流使用的id, 拉流用
        private String chargeId; // 客服的推流id
        private String chargeToken; // 客服的推流token

        private String internalServerUrl;

        public static RTCConnection toRTCConnection(RtcConnection connection) {
            return RTCConnection.builder()
                    .appId(connection.getAppId())
                    .roomName(connection.getRoomName())
                    .serverUrl(connection.getServerUrl())
                    .clientId(connection.getClientId())
                    .clientToken(connection.getClientToken())
                    .feedId(connection.getFeedId())
                    .chargeId(connection.getChargeId())
                    .chargeToken(connection.getChargeToken())
                    .internalServerUrl(connection.getInternalServerUrl())
                    .build();
        }

        public RtcConnection from() {
            return RtcConnection.newBuilder()
                    .setAppId(this.appId)
                    .setRoomName(this.roomName)
                    .setServerUrl(this.serverUrl)
                    .setClientId(this.clientId)
                    .setClientToken(this.clientToken)
                    .setFeedId(this.feedId)
                    .setChargeId(this.chargeId)
                    .setChargeToken(this.chargeToken)
                    .build();
        }
    }

    public static RoomSession copy(RoomSession one) {
        return RoomSession.builder()
                .appId(one.getAppId())
                .roomId(one.getRoomId())
                .roomName(one.getRoomName())
                .sessionId(one.getSessionId())
                .leafletWsSessionId(one.getLeafletWsSessionId())
                .status(one.getStatus())
                .address(one.getAddress() == null ? null :
                        AgentAddress.builder()
                                .host(one.getAddress().getHost())
                                .port(one.getAddress().getPort())
                                .build())
                .connection(one.getConnection() == null ? null :
                        RTCConnection.builder()
                                .appId(one.getConnection().getAppId())
                                .roomName(one.getConnection().getRoomName())
                                .serverUrl(one.getConnection().getServerUrl())
                                .clientId(one.getConnection().getClientId())
                                .clientToken(one.getConnection().getClientToken())
                                .feedId(one.getConnection().getFeedId())
                                .chargeId(one.getConnection().getChargeId())
                                .chargeToken(one.getConnection().getChargeToken())
                                .build())
                .createTime(one.getCreateTime())
                .webSocketId(one.getWebSocketId())
                .chargeToken(one.getChargeToken())
                .chargeAlertTime(one.getChargeAlertTime())
                .build();
    }
}
