package com.baidu.acg.piat.digitalhuman.common.project;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import com.baidu.acg.piat.digitalhuman.common.project.Camera;
import com.baidu.acg.piat.digitalhuman.common.project.CharacterParams;

/**
 * 对应 project preset 字符串的 vo
 *
 * <AUTHOR>
 * @since 2020/07/30
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class PresetVO {
    // character 参数太多了，暂时使用 web 接口 vo
    private CharacterParams character;
    private Display display;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Display {
        private Resolution resolution;
        private Background background;
        private long bitrate;
        private Camera camera;
        private Html5 html5;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Resolution {
        private Integer horizontal;
        private Integer vertical;
    }

    @Data
    @Accessors(chain = true)
    public static class Background {
        private String type = "image";
        private String source = "http";
        private String value = "";
    }

    @Data
    @Accessors(chain = true)
    public static class Html5 {
        private String url;
    }
}
