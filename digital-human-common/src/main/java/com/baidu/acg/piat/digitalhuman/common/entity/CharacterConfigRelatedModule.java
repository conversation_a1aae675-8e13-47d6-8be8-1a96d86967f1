package com.baidu.acg.piat.digitalhuman.common.entity;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class CharacterConfigRelatedModule{

    private String configName;

    private String configId;

    private String moduleName;

    private String id;

    private String name;

    private String type;

    public CharacterConfigRelatedModule(CharacterConfigRelatedModule module) {
        this.configName = module.getConfigName();
        this.configId = module.getConfigId();
        this.moduleName = module.getModuleName();
        this.id = module.getId();
        this.name = module.getName();
    }
}
