package com.baidu.acg.piat.digitalhuman.common.callback;


import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class CallbackResponse {
    private Status status;

    public static enum Status {
        ERROR,
        SUCCEED;
    }
}
