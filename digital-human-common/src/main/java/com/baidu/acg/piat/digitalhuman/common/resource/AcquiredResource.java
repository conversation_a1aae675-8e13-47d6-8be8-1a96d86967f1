package com.baidu.acg.piat.digitalhuman.common.resource;

import com.baidu.acg.piat.digitalhuman.common.webrtc.WebRtcSession;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AcquiredResource {

    private String sessionId;

    private ResourceInstance resource;

    private WebRtcSession webRtcSession;

    @Data
    public static class AcquiredResourceWrapper {
        private AcquiredResource resource;
    }
}
