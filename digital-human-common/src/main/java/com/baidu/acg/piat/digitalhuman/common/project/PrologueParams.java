package com.baidu.acg.piat.digitalhuman.common.project;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

@Data
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class PrologueParams {

    private static final PrologueType DEFAULT_TYPE = PrologueType.render;

    private boolean showPrologue = false;

    /**
     * use {@link PrologueType#render} default
     */
    private PrologueType type = PrologueType.render;

    private String contents;

    public enum PrologueType {
        query, render
    }

    public PrologueType getType() {
        if (type == null) {
            return DEFAULT_TYPE;
        }
        return type;
    }

}
