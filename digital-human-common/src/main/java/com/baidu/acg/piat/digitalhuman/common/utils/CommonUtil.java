package com.baidu.acg.piat.digitalhuman.common.utils;

public class CommonUtil {

    public static String concatSpeak(String text) {
        if (!text.startsWith("<speak")) {
            text = "<speak>" + text + "</speak>";
        }
        return text;
    }

    public static String concatSpeakWithInterruptible(String text, boolean interruptible) {
        // 不以speak开头的情况
        if (!text.startsWith("<speak")) {
            text = "<speak interruptible='" + interruptible + "'>" + text + "</speak>";
        } else if (text.startsWith("<speak>")) {
            // 以speak开头，但是没有interruptible的情况
            text = text.replace("<speak>", "<speak interruptible='" + interruptible + "'>");
        }
        return text;
    }
}
