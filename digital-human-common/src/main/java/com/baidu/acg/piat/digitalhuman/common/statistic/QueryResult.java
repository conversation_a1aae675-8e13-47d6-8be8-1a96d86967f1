package com.baidu.acg.piat.digitalhuman.common.statistic;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class QueryResult {

    private MetricResult metric;

    private List<Object> value;

    private List<List<Object>> values;

    @Data
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class MetricResult {
        @JsonProperty("__name__")
        private String name;
        @JsonProperty("appId")
        private String appId;
        private String application;
        private String instance;
        private String job;
        private String pod;
        @JsonProperty("resourceType")
        private String resourceType;
        @JsonProperty("characterImage")
        private String characterImage;
    }

}
