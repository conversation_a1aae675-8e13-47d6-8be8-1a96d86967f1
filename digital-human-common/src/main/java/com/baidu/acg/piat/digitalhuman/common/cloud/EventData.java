// Copyright (C) 2020 Baidu Inc. All rights reserved.
package com.baidu.acg.piat.digitalhuman.common.cloud;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * EventMessage
 *
 * <AUTHOR>
 * @since 2020-02-26
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EventData {

    private String type;

    private String content;

    public enum EventType {

        /* reserved*/
        UNKNOWN,

        ACK,

        RENDER_START,

        RENDER_ERROR,

        /* 渲染进行中，如果RENDER_COMPLETE会很晚才返回的指令，需要注意持续发送该事件，避免上游注册的回调被清理*/
        RENDER_CONTINUE,

        RENDER_COMPLETED,

        RENDER_INTERRUPTED,

        REPLY_START,

        REPLY_COMPLETE,

        DOWN_SUBTITLE,

        DOWN_CLIENT,

        /* 最后一个事件，后续不再有关于这个消息的事件*/
        FINISHED,

        VOICE_CHANGER_ERROR,

        COMMAND_SUCCESS,

        COMMAND_FAILED,

        SUBTITLE_CONFIG_ERROR,

        SUBTITLE_CONFIG
    }
}
