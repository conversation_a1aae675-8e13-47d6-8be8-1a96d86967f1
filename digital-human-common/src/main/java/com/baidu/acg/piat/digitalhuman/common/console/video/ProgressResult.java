// Copyright (C) 2020 Baidu Inc. All rights reserved.

package com.baidu.acg.piat.digitalhuman.common.console.video;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * ProgressResult
 *
 * <AUTHOR>
 * @since 2020-02-10
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ProgressResult {

    private String videoId;

    private String text;

    private ProgressStatus status;

    private String downloadUrl;

    private String audioUrl;

    private Integer errorCode;

    private String failureCause;

    private String showMessage;

    private Integer scheduleTimes;

    private String submitTime;

    private String lastScheduleStartTime;

    private String lastScheduleFinishTime;

    private String videoGenerateFinishTime;

    private Long videoGenerateCostMillis;

    public boolean judgeFinishStatus() {
        // 这里需要后后端的状态为保持一致
        return status.isTerminated();

    }

    private String thumbnail;

    private Integer resolutionWidth;

    private Integer resolutionHeight;

    private String videoName;

    private Long videoDuration;

    private String subtitleFileUrl;
}
