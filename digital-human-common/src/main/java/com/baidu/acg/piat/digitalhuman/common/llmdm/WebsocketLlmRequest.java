package com.baidu.acg.piat.digitalhuman.common.llmdm;


import com.baidu.acg.piat.digitalhuman.common.alita.ActionType;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 *
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WebsocketLlmRequest {

    private String requestId;


    private ActionType action;

    private SourceType sourceType;


    private Object body = null;

    @Override
    public String toString() {
        String body = this.body != null ? this.body.toString() : "";
        return String.format("{'requestId': %s, 'action':%s, 'body':%s}",
                requestId, action, body);
    }
}
