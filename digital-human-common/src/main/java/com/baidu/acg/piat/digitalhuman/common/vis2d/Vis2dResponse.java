// Copyright (C) 2019 Baidu Inc. All rights reserved.
package com.baidu.acg.piat.digitalhuman.common.vis2d;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Vis2dResponse
 *
 * <AUTHOR>
 * @since 2019-08-20
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy.class)
public class Vis2dResponse {
    private int errNo;
    private String errMsg;
    private String format;
    private String result;

    public static Vis2dResponse fail(String errMsg) {
        return new Vis2dResponse(1, errMsg, null, null);
    }

    public static Vis2dResponse success() {
        return new Vis2dResponse(0, "ok", null, null);
    }
}
