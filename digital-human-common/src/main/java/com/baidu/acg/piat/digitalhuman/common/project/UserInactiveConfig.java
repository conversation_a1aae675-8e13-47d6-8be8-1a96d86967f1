// Copyright (C) 2020 Baidu Inc. All rights reserved.
package com.baidu.acg.piat.digitalhuman.common.project;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.google.common.collect.Maps;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.util.Map;

import com.baidu.acg.piat.digitalhuman.common.exception.DigitalHumanCommonException;

/**
 * SpeakerAwayConfig
 *
 * <AUTHOR>
 * @since 2020-03-05
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class UserInactiveConfig {
    /**
     * 是否开启长时间无交互关闭会话
     */
    private boolean enableAutoDisconnect;

    /**
     * 超过该时间无交互则断连
     */
    private int autoDisconnectSeconds;

    /**
     * 自动断连前多少秒发送预警消息，小于等于0则不发送
     */
    private int autoDisconnectPreAlertSeconds;

    private int inactiveDisconnectSec = -1;

    private int preAlertSec = -1;

    private int thresholdSeconds;

    private String speak;

    /**
     * 提醒的次数，-1 为一直提醒；
     */
    private int remindTimes = 1;

    /**
     * 触发提醒的形式，render为数字人播报，query为触发对话机器人问答。
     */
    @Builder.Default
    private String type = "render";

    /**
     * 如为query类型，可指定问询所需传递的参数
     */
    @Builder.Default
    private Map<String, Object> params = Maps.newHashMap();

    public void check() {
        if ((thresholdSeconds > 0 && StringUtils.isEmpty(speak)) ||
                (enableAutoDisconnect && autoDisconnectSeconds <= 0)) {
            throw new DigitalHumanCommonException("invalid user inactive config");
        }
    }
}
