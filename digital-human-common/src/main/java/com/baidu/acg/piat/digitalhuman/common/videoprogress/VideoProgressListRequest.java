package com.baidu.acg.piat.digitalhuman.common.videoprogress;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

import com.baidu.acg.piat.digitalhuman.common.model.PageRequest;

/**
 * Created on 2021/8/6 11:08 上午
 *
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class VideoProgressListRequest extends PageRequest {

    private String name;

    private String projectName;

    private List<List<String>> status;

    private String orderBy;

    private String order;

    private String videoId;

}