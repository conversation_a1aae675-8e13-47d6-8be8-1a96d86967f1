package com.baidu.acg.piat.digitalhuman.common.llmrole;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 角色的对应知识库
 *
 * <AUTHOR>
 * @since 2023/11/23 15:17
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class Knowledge {

    Integer disabled; // 知识问答是否可用，1：不可用，0：可用
    String content; // 知识问答内容
    Integer status; // 知识问答状态，1：打开，0：关闭
    List<KnowledgeBase> preset;  // 预置的知识库，不允许增删
    List<KnowledgeBase> personal; // 个人知识库，允许增删
}