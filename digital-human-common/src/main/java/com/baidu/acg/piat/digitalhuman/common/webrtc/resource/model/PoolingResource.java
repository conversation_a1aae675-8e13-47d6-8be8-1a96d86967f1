package com.baidu.acg.piat.digitalhuman.common.webrtc.resource.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Set;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PoolingResource<T> {

    private String resourceType;

    private Set<T> spareSet;

    private List<T> spareList;
}
