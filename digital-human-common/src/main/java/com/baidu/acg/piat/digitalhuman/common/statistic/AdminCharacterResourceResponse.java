package com.baidu.acg.piat.digitalhuman.common.statistic;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class AdminCharacterResourceResponse extends CharacterResourceResponse<AdminCharacterResourceResponse
        .AdminCharacterResourceData>{


    @JsonIgnoreProperties(ignoreUnknown = true)
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class AdminCharacterResourceData{
        private String resourceType;
        private int deploymentRoutes;
        private List<CharacterResourceData> character;
    }
}
