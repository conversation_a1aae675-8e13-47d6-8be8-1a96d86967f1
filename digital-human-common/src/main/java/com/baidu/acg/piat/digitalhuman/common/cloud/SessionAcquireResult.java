// Copyright (C) 2019 Baidu Inc. All rights reserved.
package com.baidu.acg.piat.digitalhuman.common.cloud;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.baidu.acg.digitalhuman.cloud.grpc.ErrorInfo;
import com.baidu.acg.digitalhuman.cloud.grpc.OpenResponse;
import com.baidu.acg.digitalhuman.cloud.grpc.RtcConnection;
import com.baidu.acg.piat.digitalhuman.common.session.SessionStatus;

/**
 * SessionAcquireResponse
 *
 * <AUTHOR>
 * @since 2019-08-29
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy.class)
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SessionAcquireResult {

    private String sessionId;

    private String sessionToken;

    private SessionStatus status;

    private CloudRtcConnection rtcConnection;

    private String character;

    private Map<String, String> parameters = new HashMap<>();

    private String createTime;

    private String updateTime;

    private Extra extra;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Extra {
        private List<CloudRtcConnection> extraRtcConnections;
    }

    public OpenResponse toOpenResponse() {
        CloudRtcConnection rtc = getRtcConnection();

        List<RtcConnection> extraRtcs = new ArrayList<>();
        getExtra().getExtraRtcConnections().forEach(r -> {
            extraRtcs.add(RtcConnection.newBuilder()
                    .setClientId(r.getClientId())
                    .setAppId(r.getAppId())
                    .setClientToken(r.getClientToken())
                    .setFeedId(r.getFeedId())
                    .setRoomName(r.getRoomName())
                    .setRtcServerUrl(r.getRtcServerUrl())
                    .build());
        });
        OpenResponse.Builder builder = OpenResponse.newBuilder()
                .setSessionId(getSessionId())
                .setSessionToken(getSessionToken())
                .setCharacter(getCharacter())
                .setExtra(com.baidu.acg.digitalhuman.cloud.grpc.Extra.newBuilder()
                        .addAllExtraRtcConnections(extraRtcs)
                        .build())
                .setErrorInfo(ErrorInfo.newBuilder().setCode(0).build());
        if (rtc != null) {
            builder.setRtcConnection(RtcConnection.newBuilder()
                    .setClientId(rtc.getClientId())
                    .setAppId(rtc.getAppId())
                    .setClientToken(rtc.getClientToken())
                    .setFeedId(rtc.getFeedId())
                    .setRoomName(rtc.getRoomName())
                    .setRtcServerUrl(rtc.getRtcServerUrl())
                    .build());
        }
        return builder.build();
    }
}
