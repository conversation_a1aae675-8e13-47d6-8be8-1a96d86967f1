package com.baidu.acg.piat.digitalhuman.common.character;

import com.baidu.acg.piat.digitalhuman.common.utils.IntUtil;
import lombok.Builder;
import lombok.Getter;
import org.springframework.data.geo.Point;

import java.awt.Image;
import java.awt.image.BufferedImage;

/**
 * <AUTHOR>
 */
@Getter
@Builder
public class CharacterMixer {
    /**
     * 前景图
     */
    private BufferedImage frontImage;
    /**
     * mask：表示前景图的透明度，纯白为全透明
     */
    private BufferedImage mask;
    /**
     * 背景图
     */
    private BufferedImage backImage;

    /**
     * 1. 对齐大小
     * 2. 混合背景
     *
     * @return
     * @throws UnsupportedOperationException
     */
    public BufferedImage mix() throws UnsupportedOperationException {
        resize();

        return mixBackground();
    }

    // region private methods

    /**
     * 调整图片大小
     */
    private void resize() {
        if (frontImage.getWidth() == mask.getWidth()
                && frontImage.getHeight() == mask.getHeight()
                && backImage.getWidth() == mask.getWidth()
                && backImage.getHeight() == mask.getHeight()) {
            return;
        }

        var backAspectRatio = (float) backImage.getWidth() / backImage.getHeight();
        var frontAspectRatio = (float) frontImage.getWidth() / frontImage.getHeight();

        if (Math.abs(backAspectRatio - frontAspectRatio) < 0) {
            resizeWhenEqualAspectRatio();
        } else if (backAspectRatio > frontAspectRatio) {
            resizeIfBackAspectRatioGreater();
        } else {
            resizeIfFrontAspectRatioGreater();
        }
    }

    /**
     * 当背景图和前景图比例相等的时候
     * <p>
     * 扩大或缩小背景图片
     */
    private void resizeWhenEqualAspectRatio() {
        backImage = resizeAccordingSize(backImage, frontImage.getWidth(), frontImage.getHeight());
    }

    /**
     * 当背景图的width / height比前景图更大的情况下
     * <p>
     * 用0填充mask和front，mask=0，表示该元素透明
     */
    private void resizeIfBackAspectRatioGreater() {
        var tmpFront = resizeAccordingHeight(frontImage, backImage.getHeight());
        frontImage = fillZeroInUnCovered(tmpFront, backImage, new Point(
                (double) (backImage.getWidth() - tmpFront.getWidth()) / 2,
                0
        ));


        var tmpMask = resizeAccordingHeight(mask, backImage.getHeight());
        mask = fillZeroInUnCovered(tmpMask, backImage, new Point(
                (double) (backImage.getWidth() - tmpFront.getWidth()) / 2,
                0
        ));
    }

    /**
     * 当前景图的width / height比背景图的更大的情况下
     * <p>
     * 用0填充mask和front，mask=0，表示该元素透明
     */
    private void resizeIfFrontAspectRatioGreater() {
        var tmpFront = resizeAccordingWidth(frontImage, backImage.getWidth());
        frontImage = fillZeroInUnCovered(tmpFront, backImage,
                new Point(
                        0, backImage.getHeight() - tmpFront.getHeight()));

        var tmpMask = resizeAccordingWidth(mask, backImage.getWidth());
        mask = fillZeroInUnCovered(tmpMask, backImage,
                new Point(
                        0, backImage.getHeight() - tmpMask.getHeight()));
    }

    /**
     * 给mask或者front在其覆盖不到的地方填充0
     *
     * @param beFilled
     * @param norm
     * @param point
     * @return
     */
    private BufferedImage fillZeroInUnCovered(BufferedImage beFilled, BufferedImage norm, Point point) {
        BufferedImage result = new BufferedImage(
                norm.getWidth(), norm.getHeight(), beFilled.getType()
        );

        var g = result.createGraphics();
        g.drawImage(beFilled,
                (int) point.getX(),
                (int) point.getY(),
                beFilled.getWidth(), beFilled.getHeight(), null);
        g.dispose();
        return result;
    }

    /**
     * 根据指定的长宽缩放目标大小
     *
     * @param original
     * @param width
     * @param height
     * @return
     */
    private BufferedImage resizeAccordingSize(BufferedImage original, int width, int height) {
        BufferedImage out = new BufferedImage(width, height, original.getType());

        Image image = original.getScaledInstance(width, height, Image.SCALE_DEFAULT);

        var g = out.createGraphics();
        g.drawImage(image, 0, 0, width, height, null);
        g.dispose();

        return out;
    }

    /**
     * 根据高度来缩放
     *
     * @param original   原始图
     * @param specHeight 指定的目标高度
     * @return
     */
    private BufferedImage resizeAccordingHeight(BufferedImage original, int specHeight) {
        if (original.getHeight() == specHeight) {
            return original;
        }
        int specWidth = (int) (((float) original.getWidth() / original.getHeight()) * specHeight);

        return resizeAccordingSize(original, specWidth, specHeight);
    }

    /**
     * 根据高度来缩放
     *
     * @param original  原始图
     * @param specWidth 指定的目标高度
     * @return
     */
    private BufferedImage resizeAccordingWidth(BufferedImage original, int specWidth) {
        if (original.getWidth() == specWidth) {
            return original;
        }
        int specHeight = (int) (((float) original.getHeight() / original.getWidth()) * specWidth);

        return resizeAccordingSize(original, specWidth, specHeight);
    }

    /**
     * 将背景图格式化成没有透明度的rgb图片
     * <p>
     * 1. 提取mask中的灰度作为透明度
     * 2. 针对前景图和背景图每一个通道都计算一下权重（mask本质就是前景透明度所占比重）
     * 3. 输出
     */
    private BufferedImage mixBackground() {
        BufferedImage out = new BufferedImage(
                frontImage.getWidth(),
                frontImage.getHeight(),
                BufferedImage.TYPE_3BYTE_BGR
        );

        for (int i = 0; i < out.getWidth(); i++) {
            for (int j = 0; j < out.getHeight(); j++) {
                out.setRGB(i, j, calInEachPixel(i, j));
            }
        }

        return out;
    }

    /**
     * @param x
     * @param y
     * @return
     */
    private int calInEachPixel(int x, int y) {
        var maskPixel = mask.getRGB(x, y);
        byte maskAlphaChannel = (byte) (maskPixel);
        int frontPixel = frontImage.getRGB(x, y);
        int backPixel = backImage.getRGB(x, y);

        if (Byte.toUnsignedInt(maskAlphaChannel) == (Byte.MAX_VALUE - Byte.MIN_VALUE)) {
            return frontPixel;
        } else if (Byte.toUnsignedInt(maskAlphaChannel) == 0) {
            return backPixel;
        }

        byte[] frontBytes = IntUtil.toByteArray(frontPixel);
        byte[] backBytes = IntUtil.toByteArray(backPixel);
        byte[] newByte = new byte[4];

        newByte[0] = (byte) Byte.toUnsignedInt((byte) (Byte.MAX_VALUE - Byte.MIN_VALUE));
        newByte[1] = calInEachByte(frontBytes[1], backBytes[1], maskAlphaChannel);
        newByte[2] = calInEachByte(frontBytes[2], backBytes[2], maskAlphaChannel);
        newByte[3] = calInEachByte(frontBytes[3], backBytes[3], maskAlphaChannel);

        return IntUtil.fromByteArray(newByte);
    }

    /**
     * @param front
     * @param back
     * @param weight
     * @return
     */
    private byte calInEachByte(byte front, byte back, byte weight) {
        float frontWeight = (float) ((float) (Byte.toUnsignedInt(weight)) / 255.0);
        float backWeight = 1 - frontWeight;

        return (byte) (
                (Byte.toUnsignedInt(front)) * frontWeight
                        + (Byte.toUnsignedInt(back)) * backWeight);
    }
    // endregion
}