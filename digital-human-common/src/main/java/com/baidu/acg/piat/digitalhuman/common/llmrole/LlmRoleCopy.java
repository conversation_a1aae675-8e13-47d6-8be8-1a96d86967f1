package com.baidu.acg.piat.digitalhuman.common.llmrole;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 复制交互时的请求体
 *
 * <AUTHOR>
 * @since 2023/11/30 17:49
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class LlmRoleCopy {

    private String llmRoleId;

    private String name;

    private String rename;
}