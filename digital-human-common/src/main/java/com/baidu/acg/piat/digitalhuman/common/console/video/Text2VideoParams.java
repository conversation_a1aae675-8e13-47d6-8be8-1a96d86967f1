// Copyright (C) 2021 Baidu Inc. All rights reserved.

package com.baidu.acg.piat.digitalhuman.common.console.video;

import java.util.Map;

import com.baidu.acg.piat.digitalhuman.common.cloud.Selectors;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.springframework.lang.Nullable;

import com.baidu.acg.piat.digitalhuman.common.project.FigureCutParams;
import com.baidu.acg.piat.digitalhuman.common.project.TtsParams;

/**
 * Text2VideoParams
 *
 * <AUTHOR>
 * @date 2021-01-12
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class Text2VideoParams {

    private String appId;

    private String appKey;

    private String backgroundImageUrl;

    /**
     * resolution
     */
    private Integer resolutionWidth;

    private Integer resolutionHeight;

    private String projectId;

    @Nullable
    private TtsParams ttsParams;

    @Nullable
    private FigureCutParams figureCutParams;

    private String preset;

    private RetryPolicy retryPolicy = RetryPolicy.defaultPolicy;

    /**
     * 支持传入临时渲染参数，推荐调试时使用或需紧急生效时兜底
     */
    private Map<String, String> renderParams;

    private Selectors selectors;

    private String callbackUrl;

    private String characterConfigId;

    private String characterConfig;

    private String videoName;

    private Long videoDuration;

    // 字幕相关参数
    private SubtitleParams subtitleParams;

    public Text2VideoParams(Text2VideoParams copy) {
        this(copy.getAppId(), copy.getAppKey(), copy.getBackgroundImageUrl(),
                copy.getResolutionWidth(), copy.getResolutionHeight(),
                copy.getProjectId(), copy.getTtsParams(), copy.getFigureCutParams(), copy.getPreset(),
                copy.getRetryPolicy(), copy.getRenderParams(), copy.getSelectors(), copy.callbackUrl,
                copy.characterConfigId, copy.characterConfig, copy.videoName, copy.videoDuration, copy.subtitleParams);
    }

    public void copy(Text2VideoParams copy) {
        if (copy == null) {
            return;
        }
        this.appId = copy.getAppId();
        this.appKey = copy.getAppKey();
        this.backgroundImageUrl = copy.getBackgroundImageUrl();
        this.resolutionWidth = copy.getResolutionWidth();
        this.resolutionHeight = copy.getResolutionHeight();
        this.projectId = copy.getProjectId();
        this.ttsParams = copy.getTtsParams();
        this.figureCutParams = copy.getFigureCutParams();
        this.preset = copy.getPreset();
        this.retryPolicy = copy.getRetryPolicy();
        this.renderParams = copy.getRenderParams();
        this.selectors = copy.getSelectors();
        this.callbackUrl = copy.getCallbackUrl();
        this.characterConfigId = copy.characterConfigId;
        this.characterConfig = copy.characterConfig;
        this.videoName = copy.videoName;
        this.videoDuration = copy.videoDuration;
        this.subtitleParams = copy.subtitleParams;
    }
}
