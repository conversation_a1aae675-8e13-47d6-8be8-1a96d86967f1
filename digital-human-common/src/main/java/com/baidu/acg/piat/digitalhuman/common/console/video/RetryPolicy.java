// Copyright (C) 2021 Baidu Inc. All rights reserved.

package com.baidu.acg.piat.digitalhuman.common.console.video;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Objects;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * RetryPolicy
 *
 * <AUTHOR>
 * @date 2021-01-12
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class RetryPolicy {

    public static final RetryPolicy defaultPolicy = new RetryPolicy(true, RetryPolicyType.fixed, 3, 60 * 24L, 30_000L);

    @NotNull(message = "retryEnabled flag cannot be null")
    private Boolean retryEnabled = true;

    @NotNull(message = "type cannot be null")
    private RetryPolicyType type = RetryPolicyType.fixed;

    private Integer maxRetryTimes = 3;

    /**
     * 超时时间， 超过一定的时间后，不再调度该任务。
     */
    @Min(value = 1L, message = "expireInSeconds cannot be smaller than 1")
    @NotNull(message = "expireInSeconds cannot be null ")
    private Long expireInSeconds = 60 * 60 * 24L;

    @Min(value = 10000, message = "retryIntervalMillis cannot be smaller than 10000 ")
    @NotNull(message = "retryIntervalMillis cannot be null ")
    private Long retryIntervalMillis = 30_000L;

    public enum RetryPolicyType {

        /**
         * retryIntervalMillis ， retryIntervalMillis ，retryIntervalMillis ..
         */
        fixed,

        /**
         * retryIntervalMillis, 2 * retryIntervalMillis ,  4* retryIntervalMillis , 8 * retryIntervalMillis ...
         */
        exponential,
    }

    public int getMaxRetryTimes() {
        if (Objects.isNull(maxRetryTimes)) {
            return defaultPolicy.getMaxRetryTimes();
        }
        return maxRetryTimes;
    }

}
