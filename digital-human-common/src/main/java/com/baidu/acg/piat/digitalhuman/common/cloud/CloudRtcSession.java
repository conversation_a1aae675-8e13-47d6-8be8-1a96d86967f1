// Copyright (C) 2019 Baidu Inc. All rights reserved.
package com.baidu.acg.piat.digitalhuman.common.cloud;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * CloudRtcSession
 *
 * <AUTHOR>
 * @since 2019-08-29
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CloudRtcSession {

    @Deprecated
    private String roomPushUrl;

    /**
     * 实际这个rtc连接信息是发放给数字人的
     */
    private CloudRtcConnection clientConnection;

    /**
     * 本意是为了存放已经使用的rtc 用户id，实际看来没啥用，准备后续移除。
     */
    @Deprecated
    private List<String> rtcUserIds;

}
