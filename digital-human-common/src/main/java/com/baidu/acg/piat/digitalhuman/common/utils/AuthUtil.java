package com.baidu.acg.piat.digitalhuman.common.utils;

import com.baidu.acg.piat.digitalhuman.common.exception.DigitalHumanCommonException;
import io.vavr.control.Try;

import java.util.Date;

public class AuthUtil {
    public static String generateTokenByApp(String appId, String appKey, int duration) {
        if (duration <= 0) {
            duration = 1;
        }
        if (duration > 30) {
            throw new DigitalHumanCommonException("duration should be less than 30 days");
        }

        String expiredTime = DateUtil.fmt(DateUtil.addHourUtc(new Date(), duration).getTime());

        return Try.of(() ->
                        appId + "/" + SignUtil.hmacSha256(appKey, appId + expiredTime) + "/" + expiredTime)
                .getOrElse("");
    }
}
