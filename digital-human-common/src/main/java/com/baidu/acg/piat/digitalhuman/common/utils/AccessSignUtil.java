// Copyright (C) 2019 Baidu Inc. All rights reserved.
package com.baidu.acg.piat.digitalhuman.common.utils;

import org.apache.commons.codec.digest.HmacAlgorithms;
import org.apache.commons.codec.digest.HmacUtils;

import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;

/**
 * AccessSignUtil
 *
 * <AUTHOR>
 * @since 2019-09-18
 */
public class AccessSignUtil {

    private static final String AUTHORIZATION_MAGIC_PREFIX = "BDH ";

    public static String sign(String appKey, String appId, String expireTime) {
        var hmac = new HmacUtils(HmacAlgorithms.HMAC_SHA_256, appKey);
        return hmac.hmacHex(appId + expireTime);
    }

    /**
     * 默认生成一个小时超时时间的的authorization
     *
     * @param appKey
     * @param appId
     *
     * @return BDH
     */
    public static String authorization(String appKey, String appId) {
        String expireTime = ZonedDateTime.now().plusHours(1).format(DateTimeFormatter.ISO_OFFSET_DATE_TIME);
        return authorization(appKey, appId, expireTime);

    }

    public static String authorization(String appKey, String appId, String expireTime) {
        return AUTHORIZATION_MAGIC_PREFIX + String.join("/", appId, sign(appKey, appId, expireTime), expireTime);
    }


    public static void main(String[] args) {
        String appKey="uuryq32awn14yxt55ra1";
        String appId="p-pf5kxrt5hzd0t";
        String authorization = AccessSignUtil.authorization(appKey, appId);
        System.out.println(authorization);
    }
}
