package com.baidu.acg.piat.digitalhuman.common.session;

import com.baidu.acg.piat.digitalhuman.common.resource.AcquiredResource;
import com.baidu.acg.piat.digitalhuman.common.resource.ResourceInstance;
import com.baidu.acg.piat.digitalhuman.common.webrtc.WebRtcSession;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class SessionResponse {

    private int code;

    private String message;

    private String sessionId;

    private ResourceInstance resource;

    private WebRtcSession webRtcSession;

    public static SessionResponse succeed(String sessionId) {
        return new SessionResponse(0, "ok", sessionId, null, null);
    }

    public static SessionResponse succeed(AcquiredResource resource) {
        return new SessionResponse(0, "ok", resource.getSessionId(), resource.getResource(),
                resource.getWebRtcSession());
    }

    public static SessionResponse fail(String errMsg) {
        return new SessionResponse(1, errMsg, null, null, null);
    }
}
