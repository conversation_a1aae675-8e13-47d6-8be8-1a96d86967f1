// Copyright (C) 2019 Baidu Inc. All rights reserved.
package com.baidu.acg.piat.digitalhuman.common.session;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * DialogRequest
 *
 * <AUTHOR>
 * @since 2019-07-15
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SessionQuery {
    private String id;
    private String sessionId;
    private String startTime;
    private String endTime;
    private SessionStatus status;
    private int pageNo = 1;
    private int pageSize = 20;

    public String getSessionId() {
        return sessionId != null ? sessionId : id;
    }

}
