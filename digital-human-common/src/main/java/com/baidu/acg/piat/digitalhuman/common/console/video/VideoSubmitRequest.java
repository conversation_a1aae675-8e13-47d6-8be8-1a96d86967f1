// Copyright (C) 2020 Baidu Inc. All rights reserved.

package com.baidu.acg.piat.digitalhuman.common.console.video;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.util.List;
import java.util.stream.Collectors;

/**
 * VideoSubmitRequest
 * 视频合成请求的全量信息，能够完整的进行视频合成的离线重试工作。
 *
 * <AUTHOR>
 * @since 2020-12-10
 */
@Getter
@Setter
@ToString(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
public class VideoSubmitRequest extends Text2VideoParams {

    private List<String> texts;

    private String userId;

    public List<Text2VideoRequest> toBatchText2VideoRequests() {
        return texts.stream().map(text ->
                new Text2VideoRequest(this, text)).collect(Collectors.toList());
    }

    public VideoSubmitRequest(Text2VideoParams copy, List<String> texts) {
        super(copy);
        this.texts = texts;
    }

    public VideoSubmitRequest(Text2VideoParams copy, List<String> texts, String userId) {
        super(copy);
        this.texts = texts;
        this.userId = userId;
    }
}
