package com.baidu.acg.piat.digitalhuman.common.optlog;

import java.util.Optional;

public enum OptContent {
    CHARACTERCONFIG_ADD(1, 1001),
    CHARACTERCONFIG_UPDATE(1, 1002),
    CHARACTERCONFIG_DELETE(1, 1003),
    CHARACTERCONFIG_COPY(1, 1004),
    MHE_PROJECT_ADD(4, 4001),
    MHE_PROJECT_UPDATE(4, 4002),
    MHE_PROJECT_DELETE(4, 4003),
    MHE_PROJECT_COPY(4, 4004),
    MHE_PROJECT_EXPORT(4, 4005),
    MHE_WORKS_DOWNLOADAUDIO(5, 5001),
    MHE_WORKS_DOWNLOADVIDEO(5, 5002),
    MHE_WORKS_DELETE(5, 5003),
    MHE_WORKS_MOVE(5, 5004),
    MHE_WORKS_EDIT(5, 5005),
    MHE_MATERIAL_IMAGE_ADD(7, 7001),
    MH<PERSON>_MATERIAL_IMAGE_DOWNLOAD(7, 7002),
    MHE_MATERIAL_IMAGE_DELETE(7, 7003),
    MHE_MATERIAL_VIDEO_ADD(8, 8001),
    MHE_MATERIAL_VIDEO_DOWNLOAD(8, 8002),
    MHE_MATERIAL_VIDEO_DELETE(8, 8003),
    MHE_MATERIAL_AUDIO_ADD(9, 9001),
    MHE_MATERIAL_AUDIO_DOWNLOAD(9, 9002),
    MHE_MATERIAL_AUDIO_DELETE(9, 9003),
    SCENE_ADD(10, 10001),
    SCENE_UPDATE(10, 10002),
    SCENE_DELETE(10, 10003),
    SCENE_COPY(10, 10004),
    SCENE_BINDTOKE(10, 10005),
    APP_ADD(12, 12001),
    APP_UPDATE(12, 12002),
    APP_DELETE(12, 12003),
    USER_ADD(14, 14001),
    USER_UPDATE(14, 14002),
    USER_DELETE(14, 14003),
    USER_RESET_PASSWORD(14, 14004),
    ROLE_ADD(15, 15001),
    ROLE_UPDATE(15, 15002),
    ROLE_DELETE(15, 15003),
    ROLE_ENABLE(15, 15004),
    ROLE_DISABLE(15, 15005),
    TENANT_ADD(17, 17001),
    TENANT_UPDATE(17, 17002),
    TENANT_DELETE(17, 17003),
    TENANT_ADD_USER(17, 17004),
    TENANT_REMOVE_USER(17, 17005),
    TENANT_ADD_ADMINISTRATOR(17, 17006),
    TENANT_REMOVE_ADMINISTRATOR(17, 17007),
    PLATFORM_ADMINISTRATOR_ADD_USER(18, 18001),
    PLATFORM_ADMINISTRATOR_UPDATE_USER(18, 18002),
    PLATFORM_ADMINISTRATOR_REMOVE_USER(18, 18003),
    PLATFORM_ADMINISTRATOR_RESET_USER_PASSWORD(18, 18004),
    PLATFORM_ADMINISTRATOR_ADD_PLATFORM_ADMINISTRATOR(18, 18005),
    PLATFORM_ADMINISTRATOR_REMOVE_PLATFORM_ADMINISTRATOR(18, 18006),
    LOGIN(19, 19001);


    private int optModule;
    private int optType;

    OptContent(int optModule, int optType) {
        this.optModule = optModule;
        this.optType = optType;
    }

    public int getOptModule() {
        return optModule;
    }

    public int getOptType() {
        return optType;
    }


    public static Optional<OptContent> valueOf(int optModule, int optType) {
        for (OptContent item : values()) {
            if (item.getOptModule() == optModule && item.getOptType() == optType) {
                return Optional.of(item);
            }
        }
        return Optional.empty();
    }
}
