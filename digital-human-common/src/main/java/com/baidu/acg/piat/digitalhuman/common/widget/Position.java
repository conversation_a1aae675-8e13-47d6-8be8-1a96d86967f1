// Copyright (C) 2021 Baidu Inc. All rights reserved.

package com.baidu.acg.piat.digitalhuman.common.widget;

import com.baidu.acg.piat.digitalhuman.common.image.ImageSize;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.Optional;

/**
 * Position
 * <p>
 * 参考：http://agroup.baidu.com/abc_allinone/md/article/2285344#widget-%E4%BD%8D%E7%BD%AE%E4%BF%A1%E6%81%AF
 *
 * <AUTHOR> (<EMAIL>)
 */
@Slf4j
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class Position {
    /**
     * example:0.2
     */
    private Double aspectRatio;

    /**
     * example:20%
     */
    @JsonProperty("xCenterOffset")
    private Double xCenterOffset;

    /**
     * example:30%
     */
    @JsonProperty("yCenterOffset")
    private Double yCenterOffset;

    /**
     * example:200
     */
    private Integer width;

    /**
     * example:0.1
     */
    private Double widthRatio;

    public Double getYCenterOffset() {
        return yCenterOffset == null ? 0 : yCenterOffset;
    }

    public Double getXCenterOffset() {
        return xCenterOffset == null ? 0 : xCenterOffset;
    }

    public double getWidthRatio(double defaultWidthRatio) {
        if (getWidthRatio() == null || getWidthRatio() <= 0) {
            return defaultWidthRatio;
        }
        return getWidthRatio();
    }

    public int getWidth(int resolutionWidth, double defaultWidthRatio) {
        if (getWidth() != null && getWidth() > 0) {
            return getWidth();
        }

        double valuableWidthRatio = defaultWidthRatio;
        if (getWidthRatio() != null && getWidthRatio() > 0) {
            valuableWidthRatio = getWidthRatio();
        } else {
            log.warn("Illegal width ration, use default value={} to cal width", defaultWidthRatio);
        }
        return Double.valueOf(resolutionWidth * valuableWidthRatio).intValue();
    }

    public Optional<Integer> getHeight(int width) {
        if (getAspectRatio() != null && getAspectRatio() > 0) {
            return Optional.of(Double.valueOf((1 / getAspectRatio()) * width).intValue());
        } else {
            log.warn("Illegal aspect ratio={}", getAspectRatio());
            return Optional.empty();
        }
    }

    /**
     * support default aspect ratio
     */
    public int getHeight(int width, double defaultAspectRatio) {
        double valuableAspectRatio = defaultAspectRatio;
        if (getAspectRatio() != null && getAspectRatio() > 0) {
            valuableAspectRatio = getAspectRatio();
        } else {
            log.warn("Illegal aspect ratio, use default value={} to cal height", defaultAspectRatio);
        }

        return Double.valueOf((1 / valuableAspectRatio) * width).intValue();
    }

    public int getHeight(int resolutionWidth, double defaultWidthRatio, double defaultAspectRatio) {
        int targetWidth = getWidth(resolutionWidth, defaultWidthRatio);
        return getHeight(targetWidth, defaultAspectRatio);
    }

    public ImageSize getTargetSize(int resolutionWidth, double defaultWidthRatio, double defaultAspectRatio) {
        int targetWidth = getWidth(resolutionWidth, defaultWidthRatio);
        return new ImageSize(targetWidth, getHeight(targetWidth, defaultAspectRatio));
    }
}
