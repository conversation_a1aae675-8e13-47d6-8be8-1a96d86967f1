// Copyright (C) 2019 Baidu Inc. All rights reserved.
package com.baidu.acg.piat.digitalhuman.common.vis2d;

import com.baidu.acg.piat.digitalhuman.common.exception.DigitalHumanCommonException;
import com.baidu.acg.piat.digitalhuman.common.vis2d.Vis2dRequestParams.Action;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.ConnectionPool;
import okhttp3.OkHttpClient;
import retrofit2.Retrofit;
import retrofit2.converter.gson.GsonConverterFactory;

import java.util.Base64;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;

import javax.annotation.PostConstruct;

/**
 * Vis2dClientIml
 *
 * <AUTHOR>
 * @since 2019-08-20
 */
@Slf4j
@RequiredArgsConstructor
public class Vis2dClientImpl implements Vis2dClient {

    private final Vis2dConfig config;

    private final Map<String, Vis2dService> vis2dServices = new HashMap<>();

    private AtomicLong logIdCounter = new AtomicLong(0);

    @PostConstruct
    public void init() {
        log.info("vis 2d client initialized: config = {}", config);
    }

    @Override
    public Vis2dResponse drive(String vis2dUrl, Vis2dRequestParams request) {
        var service = getVis2dService(vis2dUrl);
        logIdCounter.compareAndSet(Long.MAX_VALUE, 0);
        var action = request.getAction();
        try {
            if (action != Vis2dRequestParams.Action.wav_to_rtc && action != Action.wav_to_video &&
                    action != Action.user_online && action != Action.user_offline) {
                throw new DigitalHumanCommonException("unsupported vis2d action ");
            }
            Vis2dRequest visRequest = request.buildRequestBody(logIdCounter.getAndIncrement());
            if (config.isPrintParamsEnabled()) {
                log.debug("Vis2d Input Params={}",
                        new String(Base64.getDecoder().decode(visRequest.getData())));
            }
            return service.drive(visRequest).execute().body();
        } catch (Throwable t) {
            log.error("Fail to drive vis2d , url = {} , action = {} ", vis2dUrl,
                    request.getAction(), t);
            if (t instanceof DigitalHumanCommonException) {
                throw (DigitalHumanCommonException) t;
            } else {
                throw new DigitalHumanCommonException("fail to drive vis2d: " + t.getMessage());
            }
        }
    }


    private Vis2dService getVis2dService(String vis2dUrl) {
        var service = vis2dServices.get(vis2dUrl);
        if (service == null) {
            synchronized (vis2dServices) {
                service = vis2dServices.get(vis2dUrl);
                if (service == null) {
                    vis2dServices.put(vis2dUrl, service = buildVis2dService(vis2dUrl));
                }
            }
        }
        return service;
    }

    private Vis2dService buildVis2dService(String vis2dUrl) {
        Retrofit retrofit = new Retrofit.Builder()
                .baseUrl(vis2dUrl)
                .client(new OkHttpClient.Builder()
                        .connectionPool(new ConnectionPool(config.getMaxConnection(),
                                config.getConnectionKeepAliveMillis(), TimeUnit.MILLISECONDS))
                        .connectTimeout(config.getConnectTimeoutMillis(), TimeUnit.MILLISECONDS)
                        .readTimeout(config.getReadTimeoutMillis(), TimeUnit.MILLISECONDS)
                        .writeTimeout(config.getWriteTimeoutMillis(), TimeUnit.MILLISECONDS)
                        .callTimeout(config.getCallTimeoutMillis(), TimeUnit.MILLISECONDS)
                        .build())
                .addConverterFactory(GsonConverterFactory.create())
                .build();
        return retrofit.create(Vis2dService.class);
    }
}