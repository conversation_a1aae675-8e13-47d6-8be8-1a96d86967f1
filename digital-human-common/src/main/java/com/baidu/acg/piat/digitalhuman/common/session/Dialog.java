// Copyright (C) 2019 Baidu Inc. All rights reserved.
package com.baidu.acg.piat.digitalhuman.common.session;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

/**
 * Dialog
 *
 * <AUTHOR>
 * @since 2019-07-11
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class Dialog {

    private String id;

    private String appId;

    private String sessionId;

    private String roomId;

    private String speaker;

    private String audioId;

    @NotNull
    private ContentType type;

    private String content;

    private String drmlContent;

    private String widget;

    @NotNull
    private long timestamp;

    public static DialogBuilder builder() {
        return new DialogBuilder();
    }

    public String getId() {
        return this.id;
    }

    public String getAppId() {
        return this.appId;
    }

    public String getSessionId() {
        return this.sessionId;
    }

    public String getRoomId() {
        return this.roomId;
    }

    public String getSpeaker() {
        return this.speaker;
    }

    public String getAudioId() {
        return this.audioId;
    }

    @NotNull
    public ContentType getType() {
        return this.type;
    }

    public String getContent() {
        return this.content;
    }

    public String getDrmlContent() {
        return this.drmlContent;
    }

    public String getWidget() {
        return this.widget;
    }

    @NotNull
    public long getTimestamp() {
        return this.timestamp;
    }

    public static class DialogBuilder {
        private String id;
        private String appId;
        private String sessionId;
        private String roomId;
        private String speaker;
        private String audioId;
        @NotNull
        private ContentType type;
        @NotNull
        private String content;
        private String drmlContent;
        private String widget;
        private long timestamp;

        DialogBuilder() {

        }

        public DialogBuilder id(String id) {
            this.id = id;
            return this;
        }

        public DialogBuilder appId(String appId) {
            this.appId = appId;
            return this;
        }

        public DialogBuilder sessionId(String sessionId) {
            this.sessionId = sessionId;
            return this;
        }

        public DialogBuilder roomId(String roomId) {
            this.roomId = roomId;
            return this;
        }

        public DialogBuilder speaker(String speaker) {
            this.speaker = speaker;
            return this;
        }

        public DialogBuilder audioId(String audioId) {
            this.audioId = audioId;
            return this;
        }

        public DialogBuilder type(@NotNull ContentType type) {
            this.type = type;
            return this;
        }

        public DialogBuilder content(String content) {
            this.content = content;
            return this;
        }

        public DialogBuilder drmlContent(String drmlContent) {
            this.drmlContent = drmlContent;
            return this;
        }

        public DialogBuilder widget(String widget) {
            this.widget = widget;
            return this;
        }

        public DialogBuilder timestamp(@NotNull long timestamp) {
            this.timestamp = timestamp;
            return this;
        }

        public Dialog build() {
            return new Dialog(id, appId, sessionId, roomId, speaker, audioId, type, content, drmlContent,widget, timestamp);
        }

        public String toString() {
            return "Dialog.DialogBuilder(id=" + this.id + "appId=" + this.appId + ", sessionId=" + this.sessionId
                    + ", roomId=" + this.roomId + ", speaker=" + this.speaker + ", audioId=" + this.audioId
                    + ", type=" + this.type + ", content=" + this.content + ", timestamp=" + this.timestamp + ")";
        }
    }
}
