package com.baidu.acg.piat.digitalhuman.common.llmrole;

import com.baidu.acg.piat.digitalhuman.common.project.Project;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;

/**
 * 创建和更新交互时的请求体
 *
 * <AUTHOR>
 * @since 2023/11/27 11:28
 */

@Data
@Slf4j
@Builder
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class RoleWithProject {
    LlmRole role;
    Project project;
}