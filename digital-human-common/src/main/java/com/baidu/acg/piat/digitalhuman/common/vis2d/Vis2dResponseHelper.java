// Copyright (C) 2019 Baidu Inc. All rights reserved.
package com.baidu.acg.piat.digitalhuman.common.vis2d;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.io.IOException;
import java.util.Base64;

/**
 * Vis2dResponseHelper
 *
 * <AUTHOR>
 * @since 2019-08-27
 */
public class Vis2dResponseHelper {

    private static ObjectMapper objectMapper = new ObjectMapper();

    public static <T> T decodeResult(Vis2dResponse response, TypeReference<T> typeReference) throws IOException {
        if (response == null) {
            throw new NullPointerException("vis2dResponse null");
        }
        return objectMapper.readValue(new String(Base64.getDecoder().decode(response.getResult())),
                typeReference);
    }
}
