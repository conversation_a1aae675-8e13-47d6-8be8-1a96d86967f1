// Copyright (C) 2019 Baidu Inc. All rights reserved.
package com.baidu.acg.piat.digitalhuman.common.constans;

/**
 * RenderOpenParameters
 *
 * <AUTHOR>
 * @since 2019-12-22
 */
public enum RenderOpenParameters {

    /**
     * 分辨率高，值为数字的字符串
     */
    resolution_height,
    /**
     * 分辨率宽，值为数字的字符串
     */
    resolution_width,
    /**
     * 人像位置偏移
     */
    characterOffset,
    /**
     * 设备类型信号
     */
    client_device,
    /**
     * 输出目标，缺省为brtc
     */
    output_target,
    /**
     * 背景图片参数，值为背景字节流的base64编码,
     * NOT supported, 目前会话的配置不支持直接放图片，后续应该也不会支持，
     */
    background_image_base64file,

    /**
     *
     */
    background_image_type,
    /**
     *
     */
    background_image_url,

    /**
     * x264编码参数
     */
    x264_param_rc_i_bitrate,

    x264_param_rc_i_vbv_buffer_size,

    x264_param_rc_f_ip_factor,

    x264_param_apply_profile,

    x264_param_i_level_idc,

    /**
     * 人物裁剪参数
     */
    figureCutXPercent,

    figureCutYPercent,

    figureCutWidthPercent,

    figureCutHeightPercent,

    figureWidthRatio,

    figurePositionCenterXPercent,

    figurePositionBottomYPercent,

    /**
     * tts 发言人
     */
    ttsPerson,
    /**
     * tts语速
     */
    ttsSpeed,

    /**
     * tts语调
     */
    ttsPitch,

    /**
     * tts音调
     */
    ttsVolume,

    /**
     * 工行tts音色
     */
    ttsPid,

    ttsExtraParams,

    /**
     * 是否在画面中输出字幕
     */
    paintSubtitleOnPicture,

    /**
     * 是否在画面中输出query
     */
    paintQueryOnPicture,

    /**
     * 是否在画面中输出图表
     * todo: move later
     */
    @Deprecated
    paintChartOnPicture,

    /**
     * 是否使用ue4内嵌视频
     */
    renderVideoOutsideUe4,

    /**
     * 是否在画面中输出widget
     */
    paintWidgetOnPicture,

    /**
     * 是否将字幕分段展示
     */
    subtitleSplittable,

    /**
     * 字幕配置
     */
    subtitleFont,

    /**
     * 字幕大小
     */
    subtitleFontSize,

    /**
     * 字幕的存活时间
     */
    subtitleTTL,

    /**
     * 背景图颜色
     */
    subtitleBackgroundColor,

    /**
     * 字体颜色
     */
    subtitleColor,

    /**
     *
     */
    subtitleMarginPx,

    /**
     * 字体大小
     */
    subtitleSize,

    /**
     * 字幕背景颜色
     */
    subtitleBackColor,

    /**
     * 字幕位置x
     */
    subtitleLocationX,

    /**
     * 字幕位置y
     */
    subtitleLocationY,

    rtcConnectionCnt,

    /**
     * bool 是否输出到brtc
     */
    mediaOutput2Brtc,

    /**
     * bool 是否输出到rtmp
     */
    mediaOutput2Rtmp,

    /**
     * string rtmp输出地址
     */
    rtmpUrl,

    h5Embedded,

    preset,

    extraInfo,

    /**
     * asr 识别结果触发打断事件
     * -1为永不触发；若asr识别的字数大于该值，即使当前尚未识别完毕，仍主动打断，让数字人停止播报
     */
    asrPartEventTriggerNumber,

    /**
     * asr 识别结果触发打断事件的指令
     * 默认为 <silence time='1s'></silence>, 以兼容2d。
     * 3d 可配置为<interrupt></interrupt>
     */
    asrPartEventTriggerText,

    // ue4
    cameraId, sceneId,

    characterConfig,

    transparent,

    // video generation mode
    video_mode,

    // video fps
    video_fps,

    character_type,

    /**
     * 通知上游仍在渲染中的间隔
     */
    longRenderAckIntervalSeconds,

    /**
     * 图层
     */
    elements,

    /**
     * 音频单/双通道
     */
    audioChannels,
    /**
     * 采样率
     */
    audioSampleRate,
    /**
     * 深度采样率
     */
    audioSampleBits,

    /**
     * 对应人像的figure_name
     */
    figureName,

    /**
     * TTS音频输出给上游
     */
    ttsAudioInfoToUpStreamEnable,

    videoMode
}
