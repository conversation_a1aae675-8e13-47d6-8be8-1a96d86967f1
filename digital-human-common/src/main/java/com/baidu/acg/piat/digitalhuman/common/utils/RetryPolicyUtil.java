// Copyright (C) 2021 Baidu Inc. All rights reserved.

package com.baidu.acg.piat.digitalhuman.common.utils;

import com.baidu.acg.piat.digitalhuman.common.console.video.RetryPolicy;
import com.baidu.acg.piat.digitalhuman.common.exception.DigitalHumanCommonException;

/**
 * RetryPolicyUtil
 *
 * <AUTHOR>
 * @date 2021-01-15
 */
public class RetryPolicyUtil {

    public static boolean needRetry(int scheduleTimes, RetryPolicy retryPolicy) {
        if (scheduleTimes == 0) {
            return true;
        }
        if (retryPolicy != null && (retryPolicy.getRetryEnabled() && (scheduleTimes - 1 < retryPolicy
                .getMaxRetryTimes()))) {
            return true;
        }
        return false;
    }

    public static boolean canRetryAtThisTime(
            int scheduleTimes, RetryPolicy retryPolicy, long lastScheduleFinishTime,
            long currentTime) {
        int retryTimes = scheduleTimes - 1;

        if (retryTimes == -1) {
            return true;
        }

        long sleepMillis;
        switch (retryPolicy.getType()) {
            case fixed: {
                sleepMillis = retryPolicy.getRetryIntervalMillis();
                break;
            }
            case exponential: {
                sleepMillis = retryTimes * (int) Math.pow(2, retryTimes) * retryPolicy.getRetryIntervalMillis();
                break;

            }
            default: {
                throw new DigitalHumanCommonException("unknown retry policy type");
            }

        }
        long nextScheduleTime = lastScheduleFinishTime + sleepMillis;

        if (currentTime < nextScheduleTime) {
            return false;
        } else {
            return true;
        }
    }

}
