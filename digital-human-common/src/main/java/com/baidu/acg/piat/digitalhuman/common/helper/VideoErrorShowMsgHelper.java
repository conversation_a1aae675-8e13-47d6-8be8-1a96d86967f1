package com.baidu.acg.piat.digitalhuman.common.helper;

import com.baidu.acg.piat.digitalhuman.common.utils.JsonUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.tomcat.util.http.fileupload.IOUtils;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;

@Slf4j
@Getter
@Setter
public class VideoErrorShowMsgHelper {
    private static volatile VideoErrorShowMsgHelper singleton;

    private ErrorMsgConf errorMsgConf;

    private VideoErrorShowMsgHelper() {

    }

    public static VideoErrorShowMsgHelper getSingleton(String appHomePath) {
        if (singleton == null) {
            synchronized (VideoErrorShowMsgHelper.class) {
                if (singleton == null) {
                    singleton = new VideoErrorShowMsgHelper();
                    // 加载配置文件
                    try (FileInputStream in = new FileInputStream(
                            new File(appHomePath + "/conf/error-msg-conf.json"))
                         ; ByteArrayOutputStream out = new ByteArrayOutputStream()) {
                        IOUtils.copy(in, out);
                        ErrorMsgConf errorMsgConf = JsonUtil.readValueQuietly(out.toString(), new TypeReference<ErrorMsgConf>() {
                        });
                        singleton.setErrorMsgConf(errorMsgConf);
                    } catch (IOException e) {
                        log.error("Load video error msg conf IO Exception", e);
                        throw new RuntimeException("Load video error msg conf failed", e);
                    }
                }
            }
        }
        return singleton;
    }

    @Data
    public static class ErrorMsgConf {
        private String unknownErrorMsg;

        private String videoGenerationErrorMsg;

        private String mrqGenerationErrorMsg;

        private String mrqVideoAckErrorMsg;

        private String mrqGenerationTimeOutErrorMsg;

        private String renderErrorMsg;

        private String renderingStartupErrorMsg;

        private String dhVideoDownloadFailedErrorMsg;

        private String editorSynthesisFailedErrorMsg;
    }
}
