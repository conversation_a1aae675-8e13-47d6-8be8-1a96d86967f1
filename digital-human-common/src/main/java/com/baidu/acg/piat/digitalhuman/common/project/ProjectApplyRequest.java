package com.baidu.acg.piat.digitalhuman.common.project;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.util.List;

/**
 * Created on 2021/1/13 16:08.
 *
 * <AUTHOR>
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ProjectApplyRequest {

    private String projectId;

    private String projectVersion;

    private List<String> appIds;
}
