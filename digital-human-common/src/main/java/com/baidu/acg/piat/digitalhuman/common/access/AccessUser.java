// Copyright (C) 2019 Baidu Inc. All rights reserved.
package com.baidu.acg.piat.digitalhuman.common.access;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * AccessUser
 *
 * <AUTHOR>
 * @since 2019-09-18
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class AccessUser {

    public static final String SCE_VISIBLE_CHARACTERS = "sceVisibleCharacters";

    private String userId;

    /**
     * mutable
     */
    private String name;

    /**
     * password will only present when created
     * <p>
     * cannot change in this version {@date 2019年09月19日21:04:44}
     */
    private String password;

    /**
     * mutable
     */
    private String description;

    private String role;

    /**
     * mutable
     */
    private Map<String, String> tags;

    private String iamUserId;

    private String createTime;

    private String updateTime;


    public static AccessUser ofUpdateRequest(String name, String password, String description,
                                             Map<String, String> tags) {
        return AccessUser.builder().name(name)
                .password(password)
                .description(description)
                .tags(tags).build();
    }

    public static AccessUser ofValidateRequest(String userId, String password) {
        return AccessUser.builder().userId(userId).password(password).build();
    }


}
