// Copyright (C) 2020 Baidu Inc. All rights reserved.

package com.baidu.acg.piat.digitalhuman.common.console.video;

import com.baidu.acg.piat.digitalhuman.common.project.TtsParams;
import com.baidu.acg.piat.digitalhuman.common.util.JsonUtil;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * TextRequest
 *
 * <AUTHOR>
 * @since 2019-12-06
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Slf4j
public class Text2VideoRequest extends Text2VideoParams {

    private String text;

    public Text2VideoRequest(Text2VideoParams copy, String text) {
        super(copy);
        this.text = text;
    }

    public Text2VideoParams toSubmitParams() {
        Text2VideoParams text2VideoParams = new Text2VideoParams(this);

        // 临时解决裂变视频tts传参不一致问题
        // todo: 后续统一优化
        if (text2VideoParams.getTtsParams() != null &&
                (text2VideoParams.getTtsParams().getPerson() == null ||
                        text2VideoParams.getTtsParams().getPerson().isEmpty())) {
            try {
                String characterConfigStr = text2VideoParams.getCharacterConfig();
                CharacterConfig characterConfig = JsonUtil.readValue(characterConfigStr, CharacterConfig.class);
                text2VideoParams.setTtsParams(characterConfig.getTts());
            } catch (Exception e) {
                // ignore
                log.error("copy tts params from character config to ttsParams failed", e);
            }
        }
        return text2VideoParams;
    }

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class CharacterConfig {
        private TtsParams tts;
    }

    public VideoSubmitRequest toVideoSubmitRequest(String appId, String appKey) {

        VideoSubmitRequest result = new VideoSubmitRequest();
        result.copy(this);
        result.setAppId(appId);
        result.setAppKey(appKey);
        result.setTexts(List.of(this.getText()));
        return result;
    }

    public VideoScheduleRequest toVideoScheduleRequest(String appId, String appKey, String videoId) {
        VideoScheduleRequest result = new VideoScheduleRequest();
        result.copy(this);
        result.setText(this.getText());
        result.setAppId(appId);
        result.setAppKey(appKey);
        result.setVideoId(videoId);
        return result;
    }

    @Data
    @EqualsAndHashCode(callSuper = true)
    @AllArgsConstructor
    @NoArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class ComplexText2VideoRequest extends Text2VideoParams {
        public static final String CONTINUE_SPLIT = "<br/>";

        private List<String> continueTexts;

        public ComplexText2VideoRequest(Text2VideoRequest copy) {
            super(copy);
            this.continueTexts = List.of(copy.getText());
        }

        public ComplexText2VideoRequest(Text2VideoParams copy, List<String> continueTexts) {
            super(copy);
            this.continueTexts = continueTexts;
        }

        /**
         * submit text is joined text. like
         * ["123", "123"]
         * convert to
         * 123<br/>123
         *
         * @param appId
         * @param appKey
         * @return
         */
        public VideoSubmitRequest toVideoSubmitRequest(String appId, String appKey) {
            VideoSubmitRequest result = new VideoSubmitRequest();
            result.copy(this);
            result.setAppId(appId);
            result.setAppKey(appKey);
            result.setTexts(List.of(String.join(CONTINUE_SPLIT, continueTexts)));
            return result;
        }

        public VideoScheduleRequest.ComplexVideoScheduleRequest toVideoScheduleRequest(
                String appId, String appKey, String videoId) {
            VideoScheduleRequest.ComplexVideoScheduleRequest result =
                    new VideoScheduleRequest.ComplexVideoScheduleRequest();
            result.copy(this);
            result.setTexts(continueTexts);
            result.setAppId(appId);
            result.setAppKey(appKey);
            result.setVideoId(videoId);
            return result;
        }

        public Text2VideoParams toSubmitParams() {
            return new Text2VideoParams(this);
        }
    }
}
