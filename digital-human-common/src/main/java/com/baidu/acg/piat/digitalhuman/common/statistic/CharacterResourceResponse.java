package com.baidu.acg.piat.digitalhuman.common.statistic;




import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import lombok.Data;

import java.util.List;

@Data
@JsonTypeInfo(use = JsonTypeInfo.Id.NAME, property = "viewType", include = JsonTypeInfo.As.EXISTING_PROPERTY)
@JsonSubTypes({@JsonSubTypes.Type(value = AdminCharacterResourceResponse.class, name = "SUPER_ADMINISTRATOR")
        , @JsonSubTypes.Type(value = TenancyCharacterResourceResponse.class, name = "TENANCY_ADMINISTRATOR")})
public abstract class CharacterResourceResponse<T> {
    private String viewType;
    private List<T> data;
}
