package com.baidu.acg.piat.digitalhuman.common.optlog;


import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class OptLog {
    private Long id;

    private String logId;
    @NotBlank
    private String optAccountId;
    @NotBlank
    private String optUserId;
    @NotBlank
    private String optObjectName;
    @Min(1)
    private Integer optModule;
    @Min(1)
    private Integer optType;

    private Integer isDelete;
    @Min(1)
    private Long optTime;
}
