package com.baidu.acg.piat.digitalhuman.common.richconfig;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

/**
 * Created on 2020/4/27 20:02.
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class DrmlAction {

    private Type type;

    private Animoji animoji;

    private String speak;

    private String startEmotion;

    private String endEmotion;

    private Fusion fusion;

    private String client;

    private String character;

    private String display;

    private String silence;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Animoji {
        private String id;

        public boolean validate() {
            return StringUtils.isNotEmpty(id);
        }
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Fusion {
        private String speak;
        private String startEmotion;
        private String endEmotion;
        private List<String> animojis;

        public boolean validate() {
            return StringUtils.isNotEmpty(speak) && CollectionUtils.isNotEmpty(animojis);
        }
    }

    public enum Type {
        speak, animoji, animojis, client, fusion, character, display, silence
    }
}
