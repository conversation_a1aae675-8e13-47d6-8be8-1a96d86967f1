package com.baidu.acg.piat.digitalhuman.common.character;


import com.baidu.acg.piat.digitalhuman.common.utils.JsonUtil;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.io.IOException;

@Data
@Slf4j
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class BehaviorPattern {

    private IntelligentAnimojiEmotion intelligentAnimojiEmotion;

    public static BehaviorPattern fromFlatJson(String json) throws IOException {
        try {
            FlatJson flatJson = JsonUtil.readValue(json, FlatJson.class);
            IntelligentAnimojiEmotion intelligentAnimojiEmotion = new IntelligentAnimojiEmotion();
            intelligentAnimojiEmotion.setEnable(flatJson.isEnable());
            intelligentAnimojiEmotion.setAnimojiRules(flatJson.getAnimojiRules());
            intelligentAnimojiEmotion.setEmotionRules(flatJson.getEmotionRules());
            return new BehaviorPattern(intelligentAnimojiEmotion);
        } catch (IOException e) {
            log.debug("Error parsing AnimojiEmotionConfig JSON: " + json, e);
            throw new IOException("Error parsing AnimojiEmotionConfig JSON: " + json, e);
        }
    }

    @Data
    private static class FlatJson {
        private boolean enable;
        private List<AnimojiTag> animojiRules;
        private List<EmotionTag> emotionRules;
    }

    @Data
    public static class IntelligentAnimojiEmotion {

        private boolean enable;

        private List<AnimojiTag> animojiRules;

        private List<EmotionTag> emotionRules;
    }

    @Data
    public static class AnimojiTag {
        private String tag;
        private List<String> animojis;
    }

    @Data
    public static class EmotionTag {
        private String tag;
        private List<String> emotions;
    }
}
