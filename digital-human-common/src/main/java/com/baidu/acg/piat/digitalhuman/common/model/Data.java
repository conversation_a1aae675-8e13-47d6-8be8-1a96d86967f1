// Copyright (C) 2019 Baidu Inc. All rights reserved.
package com.baidu.acg.piat.digitalhuman.common.model;

import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.NoArgsConstructor;

/**
 * Data
 *
 * <AUTHOR>
 * @since 2019-07-15
 */

@Builder
@lombok.Data
@NoArgsConstructor
@AllArgsConstructor
public class Data {

    @Builder.Default
    private String id = UUID.randomUUID().toString();

    private String type;

    private String data;
}
