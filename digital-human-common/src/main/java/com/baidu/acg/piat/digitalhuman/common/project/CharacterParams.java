package com.baidu.acg.piat.digitalhuman.common.project;

import java.util.List;
import java.util.Map;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * Created on 2020/6/29 14:48.
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CharacterParams {
    private Hair hair = new Hair();
    private Clothing clothing = new Clothing();
    private Offset offset = new Offset();
    private Badge badge = new Badge();
    private Shoe shoe = new Shoe();
    private Facial facial = new Facial();
    private Makeup makeup = new Makeup();
    @JsonProperty("ear_trinket")
    private EarTrinket earTrinket = new EarTrinket();
    private Glasses glasses = new Glasses();
    @JsonProperty("action_state")
    private ActionState actionState;

    @Data
    @Accessors(chain = true)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Hair {
        // 0: 内卷短发, 1: 外卷短发, 2: 马尾
        private Integer style = 0;
    }

    @Data
    @Accessors(chain = true)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Clothing {
        // 0: 西服, 1: 衬衫
        private Integer style = 0;

        private Map<String, String> color;

        /**
         * 上下装自由穿搭服装
         */
        private List<Clothing> clothes;
    }

    @Data
    @Accessors(chain = true)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Offset {
        private Double x = 0.0;

        private Double y = 0.0;

        private Double z = 1.0;

        /**
         * the time taken up for moving, e.g. 100ms
         */
        private Integer timeConsumingMs = 0;

        @JsonProperty("look_front")
        private Boolean lookFront = false;
    }

    @Data
    @Accessors(chain = true)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Badge {
        // 0: 无, 1: 百度, 2: 百度+文字, 3: 智能云, 4: 智能云+文字, 5: 央视网
        private Integer style = 0;
    }

    @Data
    @Accessors(chain = true)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Shoe {
        private Integer style = 0;
    }

    @Data
    @Accessors(chain = true)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Facial {
        private Lineament lineament = new Lineament();

        private Eye eyes = new Eye();

        private Eyebrow eyebrow = new Eyebrow();

        private Nose nose = new Nose();

        private Mouth mouth = new Mouth();


        @Data
        @Accessors(chain = true)
        @JsonInclude(JsonInclude.Include.NON_NULL)
        @JsonIgnoreProperties(ignoreUnknown = true)
        public static class Eyebrow {
            // 眼眉间距
            private Double x = 0.0;

            // 眼眉上下
            private Double y = 0.0;
        }

        @Data
        @Accessors(chain = true)
        @JsonInclude(JsonInclude.Include.NON_NULL)
        @JsonIgnoreProperties(ignoreUnknown = true)
        public static class Lineament {
            // 脸部胖瘦，[-1,1]，-1为最瘦，1为最胖，默认：0
            @JsonProperty("face_fat")
            private Double faceFat = 0.0;

            // 下巴形状，[-1,1]，-1为最圆，1为最方，默认：0
            @JsonProperty("chin_shape")
            private Double chinShape = 0.0;
        }

        @Data
        @Accessors(chain = true)
        @JsonInclude(JsonInclude.Include.NON_NULL)
        @JsonIgnoreProperties(ignoreUnknown = true)
        public static class Eye {
            // 眼睛大小，[-1,1]，-1为最小，1为最大，默认：0
            private Double size = 0.0;

            // 眼睛距离；范围为[-1,1]，-1表示最小，1表示最大
            private Double distance = 0.0;

            // 外眼角角度；范围为[-1,1]，-1表示外眼角向下，1表示外眼角向上
            private Double rotation = 0.0;

            // 杏花眼 [0,1]，默认：0
            private Double almond = 0.0;

            // 桃花眼，[0,1]，默认：0
            private Double amorous = 0.0;

            // 丹凤眼，[0,1]，默认：0
            private Double phoenix = 0.0;
        }

        @Data
        @Accessors(chain = true)
        @JsonInclude(JsonInclude.Include.NON_NULL)
        @JsonIgnoreProperties(ignoreUnknown = true)
        public static class Nose {
            // 鼻子长度，[-1,1]，-1为最短，1为最长，默认：0
            private Double len = 0.0;

            // 鼻子宽度，[-1,1]，-1为最窄，1为最宽，默认：0
            private Double broad = 0.0;

            // 鼻子形状，[-1,1]，-1为圆鼻头，1为鹰钩鼻，默认：0
            private Double shape = 0.0;

            // 鼻翼偏移，[-1,1]，-1为上移，1为下移，默认：0
            private Double wing = 0.0;
        }

        @Data
        @Accessors(chain = true)
        @JsonInclude(JsonInclude.Include.NON_NULL)
        @JsonIgnoreProperties(ignoreUnknown = true)
        public static class Mouth {
            // 嘴巴大小，[-1,1]，-1为最小，1为最大，默认：0
            private Double size = 0.0;

            // 嘴巴偏移;范围为[-1,1]，-1表示嘴巴上移最大，1表示嘴巴下移最大
            private Double pos = 0.0;

            // 上嘴唇厚度，[-1,1]，-1为最薄，1为最厚，默认：0
            @JsonProperty("upper_lip")
            private Double upperLip = 0.0;

            // 下嘴唇厚度，[-1,1]，-1为最薄，1为最厚，默认：0
            @JsonProperty("lower_lip")
            private Double lowerLip = 0.0;
        }

    }

    @Data
    @Accessors(chain = true)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Makeup {

        private Eyebrow eyebrow = new Eyebrow(); // 眼眉

        private Eyes eyes = new Eyes(); // 眼妆

        private Lipstick lipstick = new Lipstick(); // 唇妆

        @Data
        @Accessors(chain = true)
        @JsonInclude(JsonInclude.Include.NON_NULL)
        @JsonIgnoreProperties(ignoreUnknown = true)
        public static class Eyebrow {

            // 眼眉浓密
            private Double intensity = 0.0;
        }

        @Data
        @Accessors(chain = true)
        @JsonInclude(JsonInclude.Include.NON_NULL)
        @JsonIgnoreProperties(ignoreUnknown = true)
        public static class Eyes {

            // 眼影深度
            @JsonProperty("shadow_intensity")
            private Double shadowIntensity = 0.0;

            // 眼影色
            @JsonProperty("shadow_hua")
            private Double shadowHua = 0.0;

            // 眼线色
            @JsonProperty("line_color")
            private String lineColor;

            // 卧蚕色
            @JsonProperty("lid_down_color")
            private String lidDownColor;
        }

        @Data
        @Accessors(chain = true)
        @JsonInclude(JsonInclude.Include.NON_NULL)
        @JsonIgnoreProperties(ignoreUnknown = true)
        public static class Lipstick {

            // 口红厚度
            private Double intensity = 0.0;

            // 口红雾面、釉面
            private Double rough = 0.0;

            // 口红色
            private String color;
        }
    }


    @Data
    @Accessors(chain = true)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class EarTrinket {
        private Integer id = 0;
    }

    @Data
    @Accessors(chain = true)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Glasses {
        private Integer id = 0;
    }

    public enum ActionState {
        wobble, motionless
    }

}
