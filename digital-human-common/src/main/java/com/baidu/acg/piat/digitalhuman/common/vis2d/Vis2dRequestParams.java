// Copyright (C) 2019 Baidu Inc. All rights reserved.
package com.baidu.acg.piat.digitalhuman.common.vis2d;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.NonNull;
import org.apache.commons.lang3.StringUtils;

import java.util.Base64;

/**
 * Vis2dRequest
 *
 * <AUTHOR>
 * @since 2019-08-20
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy.class)
public class Vis2dRequestParams {

    @NonNull
    @Builder.Default
    private String objectType = "mimic";

    @NonNull
    private Action action = Action.wav_to_video;

    /**
     * 会话id
     */
    private String sessionId;

    /**
     * 发送给下游的地址
     */
    private String sentTo;

    private String source;

    /**
     * target roomNumber or rtc service
     */
    @Deprecated
    private String roomNumber;
    /**
     * base64 content
     */
    private String content;

    /**
     * base64 wav
     */
    private String wav;

    private String initRequest;

    private String channelData;

    @Builder.Default
    private int bodyAction = 0;

    @Builder.Default
    private boolean predicateEnabled = false;

    private int beginIndex = 0;

    private int wavLength = 0;


    public Vis2dRequest buildWav2RtcRequestBody(long logId) {
        assert action == Action.wav_to_rtc;
        String data = String.join("&",
                "object_type=" + getObjectType(),
                "action=" + getAction(),
                "session_id=" + getSessionId(),
                "sent_to=" + getSentTo(),
                "wav=" + getWav());
        return Vis2dRequest.builder().logid(logId)
                .data(Base64.getEncoder().encodeToString(data.getBytes())).build();
    }

    public Vis2dRequest buildUserOnlineRequestBody(long logId) {
        assert action == Action.user_online;
        String data = String.join("&",
                "object_type=" + getObjectType(),
                "action=" + getAction(),
                "session_id=" + getSessionId(),
                "sent_to=" + getSentTo());
        return Vis2dRequest.builder().logid(logId)
                .data(Base64.getEncoder().encodeToString(data.getBytes())).build();
    }

    public Vis2dRequest buildUserOfflineRequestBody(long logId) {
        assert action == Action.user_offline;
        String data = String.join("&",
                "object_type=" + getObjectType(),
                "action=" + getAction(),
                "session_id=" + getSessionId(),
                "sent_to=" + getSentTo());
        return Vis2dRequest.builder().logid(logId)
                .data(Base64.getEncoder().encodeToString(data.getBytes())).build();
    }


    public Vis2dRequest buildWav2VideoRequestBody(long logId) {
        assert action == Action.wav_to_video;
        String data = String.join("&",
                "object_type=" + getObjectType(),
                "action=" + getAction(),
                "wav=" + getWav());
        return Vis2dRequest.builder().logid(logId)
                .data(Base64.getEncoder().encodeToString(data.getBytes())).build();
    }

    public Vis2dRequest buildRequestBody(long logId) {
        StringBuilder sb = new StringBuilder("action=" + action.name());
        if (!StringUtils.isEmpty(objectType)) {
            sb.append("&object_type=").append(objectType);
        }
        if (!StringUtils.isEmpty(source)) {
            sb.append("&source=").append(source);
        }
        if (!StringUtils.isEmpty(sessionId)) {
            sb.append("&session_id=").append(sessionId);
        }
        if (!StringUtils.isEmpty(sentTo)) {
            sb.append("&sent_to=").append(sentTo);
        }
        if (!StringUtils.isEmpty(content)) {
            sb.append("&content=").append(content);
        }
        if (!StringUtils.isEmpty(wav)) {
            sb.append("&wav=").append(wav);
        }
        if (!StringUtils.isEmpty(initRequest)) {
            sb.append("&init_request=").append(Base64.getEncoder().encodeToString(initRequest.getBytes()));
        }
        if (!StringUtils.isEmpty(channelData)) {
            sb.append("&channel_data=").append(Base64.getEncoder().encodeToString(channelData.getBytes()));
        }
        if (predicateEnabled) {
            sb.append("&begin_index=").append(beginIndex).append("&wav_length=").append(wavLength);
        }
        sb.append("&body_action=").append(bodyAction);
        return Vis2dRequest.builder().logid(logId)
                .data(Base64.getEncoder().encodeToString(sb.toString().getBytes())).build();
    }

    public enum Action {
        /**
         * 台词到嘴部关键点
         */
        txt_to_link,
        /**
         * 台词到虚拟人物视频
         */
        txt,
        /**
         * 带有问答的，答案台词到嘴部关键点
         */
        qa_to_lmk,
        /**
         * 带有问答的，答案台词到人物视频
         */
        qa,
        /**
         *
         */
        service_check,
        /**
         * 音频输入，本地音频文件输出
         */
        wav_to_video,

        /**
         * 音频输入，rtc输出
         */
        wav_to_rtc,

        user_online,

        user_offline,

    }
}
