package com.baidu.acg.piat.digitalhuman.common.cloud;

import com.baidu.acg.piat.digitalhuman.common.session.SessionStatus;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.HashMap;
import java.util.Map;

/**
 * @Author: <EMAIL>
 * @Date: 2020/3/20
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CloudSessionResult {

    private String sessionId;

    private String sessionToken;

    private SessionStatus status;

    private RtcConnection rtcConnection;

    private String character;

    private Map<String, String> parameters = new HashMap<>();

    private SessionAcquireResult.Extra extra;

    private String createTime;

    private String updateTime;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RtcConnection {
        private String rtcServerUrl;
        private String appId;
        private String roomName;
        private String clientId;
        private String clientToken;
        private String feedId;

        public static RtcConnection convert(CloudRtcConnection connection) {
            return RtcConnection.builder()
                    .appId(connection.getAppId())
                    .clientId(connection.getClientId())
                    .clientToken(connection.getClientToken())
                    .feedId(connection.getFeedId())
                    .roomName(connection.getRoomName())
                    .rtcServerUrl(connection.getRtcServerUrl())
                    .build();
        }
    }

}
