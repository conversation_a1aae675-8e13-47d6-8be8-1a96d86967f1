package com.baidu.acg.piat.digitalhuman.common.metrics;

import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.config.MeterFilter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.actuate.autoconfigure.metrics.MeterRegistryCustomizer;
import org.springframework.boot.autoconfigure.condition.ConditionalOnWebApplication;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Slf4j
@Configuration
@ConditionalOnWebApplication
public class MetricsConfig {

    @Value("${spring.application.name:unknown}")
    private String applicationName;

    @Bean
    MeterRegistryCustomizer<MeterRegistry> meterRegistryCustomizer() {
        if ("unknown".equals(applicationName)) {
            log.error("spring.application.name NOT set, use unknown instead");
        }

        return registry -> registry.config()
                .commonTags("application", applicationName)
                .meterFilter(MeterFilter.deny(id -> {
                    String uri = id.getTag("uri");
                    return uri != null && uri.startsWith("/actuator");
                }))
                .meterFilter(MeterFilter.deny(id -> {
                    String uri = id.getTag("uri");
                    return uri != null && uri.contains("favicon");
                }));
    }
}
