package com.baidu.acg.piat.digitalhuman.common.config;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

import com.baidu.acg.piat.digitalhuman.common.cloud.Selectors;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ResourceInstanceConfig {

    private String ip;

    private String resourceType;

    private Map<String, Integer> ports;

    private Selectors.Labels labels;
}
