package com.baidu.acg.piat.digitalhuman.common.webrtc.resource.pool.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import com.baidu.acg.piat.digitalhuman.common.resource.AcquiredResource;
import com.baidu.acg.piat.digitalhuman.common.resource.ResourceInstance;
import com.baidu.acg.piat.digitalhuman.common.thirdparty.model.ThirdPartyServiceResponse;

/**
 * Web rtc resource response.
 *
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AcquireResponse extends ThirdPartyServiceResponse {

    private ResourceInstance resourceInstance;

    public static AcquireResponse succeed(AcquiredResource resource) {
        var response = AcquireResponse.builder()
                .resourceInstance(resource.getResource())
                .build();
        response.setCode(0);
        response.setMessage("ok");
        return response;
    }

    public static AcquireResponse fail(int code, String message) {
        var response = new AcquireResponse();
        response.setCode(code);
        response.setMessage(message);
        return response;
    }

    public static AcquireResponse fail(String message) {
        return fail(1, message);
    }

    @Data
    public static class Wrapper {
        private AcquireResponse response;
    }

}