package com.baidu.acg.piat.digitalhuman.common.thirdparty.service.factory;

import retrofit2.Retrofit;
import retrofit2.converter.gson.GsonConverterFactory;

/**
 * Retrofit based third-party service factory.
 *
 * <AUTHOR>
 */
public class ThirdPartyServiceFactory {

    public static <T> T create(String baseUrl, Class<T> clazz) {
        Retrofit retrofit = new Retrofit.Builder()
                .baseUrl(baseUrl)
                .addConverterFactory(GsonConverterFactory.create())
                .build();

        return retrofit.create(clazz);
    }
}
