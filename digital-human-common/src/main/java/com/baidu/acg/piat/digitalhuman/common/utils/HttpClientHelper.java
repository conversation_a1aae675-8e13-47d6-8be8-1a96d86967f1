// Copyright (C) 2021 Baidu Inc. All rights reserved.

package com.baidu.acg.piat.digitalhuman.common.utils;

import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import okhttp3.ResponseBody;
import retrofit2.Call;
import retrofit2.Retrofit;
import retrofit2.converter.jackson.JacksonConverterFactory;

import java.io.IOException;

import com.baidu.acg.piat.digitalhuman.common.exception.DigitalHumanCommonException;

/**
 * @Author: <EMAIL>
 * @Date: 2020/3/19
 */
@Slf4j
public abstract class HttpClientHelper {

    public <T> T init(String baseUrl, OkHttpClient okHttpClient, Class<T> clazz) {
        var retrofit = new Retrofit.Builder()
                .addConverterFactory(JacksonConverterFactory.create(new ObjectMapper()))
                .client(okHttpClient)
                .baseUrl(baseUrl)
                .build();
        log.info("baseUrl = {}", baseUrl);
        return retrofit.create(clazz);
    }

    public <T> T init(String baseUrl, Class<T> clazz) {
        var retrofit = new Retrofit.Builder()
                .addConverterFactory(JacksonConverterFactory.create(new ObjectMapper()))
                .baseUrl(baseUrl)
                .build();
        log.info("baseUrl = {}", baseUrl);
        return retrofit.create(clazz);
    }


    public <T> T call(Call<T> call, String method) {
        try {
            var response = call.execute();
            if (!response.isSuccessful()) {
                try (ResponseBody errorBody = response.errorBody()) {
                    String errorMessage = errorBody == null ? "unknown" : errorBody.string();
                    DigitalHumanCommonException ex = JsonUtil.readValue(errorMessage,
                            DigitalHumanCommonException.class);
                    if (ex != null) {
                        errorMessage = ex.getMessage();
                    }
                    throw new DigitalHumanCommonException(response.code(), buildErrorMsg(errorMessage, method));
                } catch (DigitalHumanCommonException e) {
                    log.warn("Fail to call service {} , ex:{}", call.request().url(), e);
                    throw e;
                } catch (Exception e) {
                    log.error("meet error ", e);
                    throw new DigitalHumanCommonException(response.code(), "unknown server error");
                }
            }
            return response.body();

        } catch (IOException e) {
            throw new DigitalHumanCommonException(
                    String.format("Fail to call http server, cause: %s", e.getMessage()), e);
        }
    }

    public String buildErrorMsg(String cause, String method) {
        return "Fail to " + method + ", cause: " + cause;
    }


}
