// Copyright (C) 2021 Baidu Inc. All rights reserved.
package com.baidu.acg.piat.digitalhuman.common.hypervisor.response;

import com.baidu.acg.piat.digitalhuman.common.hypervisor.request.HeartBeatRequest;
import com.baidu.acg.piat.digitalhuman.common.resource.ResourceInstance;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * Hypervisor response.
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class HypervisorResponse {

    private int code;

    private String message;

    private ResourceInstance resource;

    private com.baidu.acg.piat.digitalhuman.common.hypervisor.request.HeartBeatRequest heartBeatRequest;

    public static HypervisorResponse succeed() {
        return succeed(null);
    }

    public static HypervisorResponse succeed(ResourceInstance resourceInstance) {
        return new HypervisorResponse(0, "ok", resourceInstance, null);
    }

    public static HypervisorResponse succeed(ResourceInstance resourceInstance, HeartBeatRequest heartBeatRequest) {
        return new HypervisorResponse(0, "ok", resourceInstance, heartBeatRequest);
    }

    public static HypervisorResponse fail(String errMsg) {
        return new HypervisorResponse(1, errMsg, null, null);
    }

    @Setter
    @Getter
    public static class HypervisorResultWrapper {
        private HypervisorResponse hypervisorResponse;
    }
}
