// Copyright (C) 2021 Baidu Inc. All rights reserved.

package com.baidu.acg.piat.digitalhuman.common.console.video;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.List;

import javax.validation.constraints.NotEmpty;

/**
 * Text2VideoBatchRequest
 *
 * <AUTHOR>
 * @date 2021-01-12
 */
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
public class Text2VideoBatchRequest extends Text2VideoParams {

    @NotEmpty(message = "texts cannot be empty")
    private List<String> texts;

    public VideoSubmitRequest toVideoSubmitRequest(String appId, String appKey) {

        VideoSubmitRequest result = new VideoSubmitRequest(this, texts);
        result.setAppId(appId);
        result.setAppKey(appKey);
        return result;
    }

}
