// Copyright (C) 2019 Baidu Inc. All rights reserved.
package com.baidu.acg.piat.digitalhuman.common.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Response
 *
 * <AUTHOR>
 * @since 2019-07-11
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class BaseResponse {
    private int code = 0;
    private boolean success = true;
    private Message message = new Message();

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Message {
        private String global = "success";

        @Override
        public String toString() {
            return global;
        }
    }
}
