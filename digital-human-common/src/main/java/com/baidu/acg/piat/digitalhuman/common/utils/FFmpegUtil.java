package com.baidu.acg.piat.digitalhuman.common.utils;

import java.io.File;
import java.io.IOException;
import java.nio.file.Path;

import java.util.concurrent.TimeUnit;
import lombok.extern.slf4j.Slf4j;

import com.baidu.acg.piat.digitalhuman.common.exception.DigitalHumanCommonException;

/**
 * FFmpegUtil
 *
 * <AUTHOR> (<EMAIL>)
 */

@Slf4j
public class FFmpegUtil {

    private static final String COMPUTE_FORMAT = "ffmpeg -y -i %s -vn -ar %d -ac %d %s";

    private static final String COMPUTE_PCM_FORMAT = "ffmpeg -y -i %s -vn -ar %d -ac %d -acodec pcm_s%dle -f s%dle %s";

    private static final String MP3_COVER_WAV_TTS16K = "ffmpeg -i %s -map_metadata -1 -acodec pcm_s16le -b:a 16k -ar " +
            "16000 -ac 1 -fflags +bitexact -flags:v +bitexact -flags:a +bitexact %s";

    /**
     * compute pcm audio from video or other audio format, and convert audio to specific params.
     * ffmpeg -i {src} -vn -ar {rate} -ac {channel} -acodec pcm_s16dle -f s16le {out}
     *
     * @param srcVideo       Path or Url string of video
     * @param output         String of output file path
     * @param sampleRate     int usually set to 16000
     * @param sampleBits     int usually set to 16
     * @param channels       int usually set to 1
     * @param timeoutSeconds int time out in seconds
     */
    public static void computePcm(String srcVideo, String output, int sampleRate, int sampleBits,
                                  int channels, int timeoutSeconds) throws IOException {
        String command = String.format(COMPUTE_PCM_FORMAT, srcVideo,
                sampleRate, channels, sampleBits, sampleBits, output);
    }

    public static void extractAudio(String srcVideo, String output, int sampleRate, int sampleBits,
                                    int channels, int timeoutSeconds) throws IOException {

        // 创建输出文件所需目录
        Path pathParent = Path.of(output).getParent();
        File dir = new File(pathParent.toString());
        if (!dir.exists()) {
            dir.mkdirs();
        }

        String command = String.format(COMPUTE_FORMAT, srcVideo,
                sampleRate, channels, output);
        execCommand(command, timeoutSeconds);
    }

    /**
     * execute ffmpeg command
     *
     * @param command        command
     * @param timeoutSeconds int time out in seconds
     */
    private static void execCommand(String command, int timeoutSeconds) throws IOException {
        log.info("command: {}", command);

        Process process;
        process = Runtime.getRuntime().exec(command);
        log.info("process info={}", process.info());

        var result = false;
        try {
            result = process.waitFor(timeoutSeconds, TimeUnit.SECONDS);
        } catch (InterruptedException e) {
            throw new DigitalHumanCommonException(String.format("command: %s was interrupted. detail:%s",
                    command, e.getMessage()));
        }

        if (!result) {
            var errorMessage = String.format("command:%s timeout.", command);
            throw new DigitalHumanCommonException(errorMessage);
        } else if (process.exitValue() != 0) {
            var errorMessage = StringUtil.fromInputStream(process.getErrorStream());
            throw new DigitalHumanCommonException(process.exitValue(), errorMessage);
        }
    }

    public static void mp3ToWavTts16k(String srcAudio, String output, int timeoutSeconds) throws IOException {
        // 创建输出文件所需目录
        Path pathParent = Path.of(output).getParent();
        File dir = new File(pathParent.toString());
        if (!dir.exists()) {
            dir.mkdirs();
        }
        String command = String.format(MP3_COVER_WAV_TTS16K, srcAudio, output);
        execCommand(command, timeoutSeconds);
    }
}