package com.baidu.acg.piat.digitalhuman.common.commonkv;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;

/**
 * 通用 kv 对，for FE
 *
 * <AUTHOR>
 * @since 2021/08/05
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CommonKv {

    private String userId;

    @NotBlank(message = "key cannot be blank.")
    private String key;

    private String value;
}
