package com.baidu.acg.piat.digitalhuman.common.richtext;

import java.io.IOException;
import lombok.Data;

import com.baidu.acg.piat.digitalhuman.common.util.DomUtil;
import com.baidu.acg.piat.digitalhuman.common.utils.JsonUtil;

@Data
public class AnimojiTag {

    private String id;

    private String speak;


    public static AnimojiTag fromXml(String xml) throws IOException {
        return JsonUtil.readValue(DomUtil.xml2Json(xml, false), AnimojiTag.class);
    }
}
