// Copyright (C) 2020 Baidu Inc. All rights reserved.

package com.baidu.acg.piat.digitalhuman.common.widget;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Widget
 *
 * <AUTHOR>
 * @since 2019-12-18
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class Widget {

    private WidgetContent widget;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class WidgetContent {
        /**
         * widget类型
         */
        private String type;

        /**
         * 版本号
         */
        private String version;

        /**
         * 存活时间
         */
        private Double ttl;

        private Position position;

        private String text;

        private MutilChoiceQuestion mcq;

        private MultiChoiceAnswer mcqAnswer;

        private Password password;

        private String passwordAnswer;

        private Video video;

        private Image image;

        private Chart chart;

        private EChart echart;

        private MultiImage multiImage;

        private Graphic graphic;

        private Form form;

        private Table table;

        private ScreenShift screenShift;

        private SpecCallPackage specCallPackage;

        private SpecCallPackageDetail specCallPackageDetail;

        private SpecCallPackageUsage specCallPackageUsage;

        private SpecCallBill specCallBill;
    }
}
