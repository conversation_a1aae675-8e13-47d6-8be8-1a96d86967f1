package com.baidu.acg.piat.digitalhuman.common.entity;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Transient;

import com.baidu.acg.piat.digitalhuman.common.entity.CharacterModel.Mode;
import org.hibernate.annotations.UpdateTimestamp;

@Data
@Accessors(chain = true)
@Entity(name = "character_config")
@JsonInclude(Include.NON_NULL)
public class CharacterConfig {

    @Id
    private String configId;

    private String name;

    private String type;

    private String characterId;

    private String characterName;

    private String description;

    private String userId;

    private String config;

    private Mode configMode;

    private String thumbnail;

    // same as character
    private String label;

    private LocalDateTime createTime;

    @UpdateTimestamp
    @Column(name = "update_time")
    private LocalDateTime updateTime;

    @Transient
    private String addType;

    private String behaviorPatternConfig;

    // 关联starlight模块的user_figure表id
    private Long userFigureId;
}
