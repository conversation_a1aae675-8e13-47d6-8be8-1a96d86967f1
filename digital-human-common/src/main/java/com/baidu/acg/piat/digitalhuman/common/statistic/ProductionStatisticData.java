package com.baidu.acg.piat.digitalhuman.common.statistic;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ProductionStatisticData {
    private String name;
    private IncrementData videoIncrement;
    private IncrementData characterConfigIncrement;
    private IncrementData projectIncrement;
    private IncrementData appIncrement;
    private IncrementData sceneIncrement;


    @JsonIgnoreProperties(ignoreUnknown = true)
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class IncrementData {
        private String name;
        private int value;
    }
}
