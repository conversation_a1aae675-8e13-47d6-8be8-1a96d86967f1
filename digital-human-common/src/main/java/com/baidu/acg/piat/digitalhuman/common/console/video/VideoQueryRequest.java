// Copyright (C) 2022 Baidu Inc. All rights reserved.

package com.baidu.acg.piat.digitalhuman.common.console.video;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Range;

/**
 * VideoQueryRequest
 *
 * 查询视频记录的请求，请求参数为筛选项，用于筛选出符合预期的视频
 *
 * <AUTHOR> (<EMAIL>)
 */

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class VideoQueryRequest {
    private String videoId;

    private String sessionId;

    private String appId;

    private String userId;

    private String userName;

    private String projectName;

    private String characterConfigId;

    @Range(min = 1, message = "Page no cannot less then 1!")
    private Integer pageNo = 1;
    @Range(min = 1, message = "Page size cannot less then 1!")
    private Integer pageSize = 20;
}
