// Copyright (C) 2021 Baidu Inc. All rights reserved.

package com.baidu.acg.piat.digitalhuman.common.widget;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * WidgetObject
 *
 * <AUTHOR> (<EMAIL>)
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class WidgetObject {

    private WidgetContent widget;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class WidgetContent {
        /**
         * widget id
         */
        private String id;

        /**
         * widget类型
         */
        private String type;

        /**
         * target类型
         */
        private String target;

        /**
         * 版本号
         */
        private String version;

        /**
         * 存活时间
         */
        private Double ttl;

        private Position position;

        private String text;

        private Object mcq;

        private Object mcqAnswer;

        private Object password;

        private String passwordAnswer;

        private Object video;

        private Image image;

        private Object chart;

        private Object echart;

        private Object videoTitle;

        private Object multiImage;

        private Object graphic;

        private Object form;

        private Object table;

        private Object screenShift;

        private Object specCallPackage;

        private Object specCallPackageDetail;

        private Object specCallPackageUsage;

        private Object specCallBill;

        private Object productRecommendation;

        private Object financialMaturity;

        private Object iframe;
    }
}
