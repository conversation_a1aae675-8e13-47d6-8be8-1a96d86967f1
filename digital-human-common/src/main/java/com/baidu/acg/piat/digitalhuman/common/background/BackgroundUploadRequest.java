package com.baidu.acg.piat.digitalhuman.common.background;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class BackgroundUploadRequest {

    private String userId;

    private String name;

    private String imageBase64;

    private String description;

    private Boolean isLiveBg;

    @Builder.Default
    private Boolean isOthers = false;
}
