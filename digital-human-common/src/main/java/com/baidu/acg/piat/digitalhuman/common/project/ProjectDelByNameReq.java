package com.baidu.acg.piat.digitalhuman.common.project;


import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class ProjectDelByNameReq {
    private String userId;
    private String projectName;
    @Builder.Default
    private int apiVersion = 1;
    // 登录操作账户id
    private String uid;
}
