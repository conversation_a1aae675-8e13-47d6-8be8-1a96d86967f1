package com.baidu.acg.piat.digitalhuman.common.utils;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.JsonArray;
import com.google.gson.JsonParser;

import java.io.IOException;
import java.util.Map;
import java.util.Optional;
import lombok.extern.slf4j.Slf4j;
import java.util.ArrayList;
import java.util.List;

/**
 * Created on 2020/4/26 13:43.
 *
 * <AUTHOR>
 */
@Slf4j
public class JsonUtil {

    public static final ObjectMapper om =
            new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);

    private static final ObjectMapper omNoNull =
            new ObjectMapper().setSerializationInclusion(JsonInclude.Include.NON_NULL);

    public static <T> T readValue(String json, Class<T> clazz) throws IOException {
        return om.readValue(json, clazz);
    }

    public static <T> T readValue(byte[] json, Class<T> clazz) throws IOException {
        return om.readValue(json, clazz);
    }

    public static <T> T readValue(String json, TypeReference<T> typeReference) throws IOException {
        return om.readValue(json, typeReference);
    }

    public static <T> T readValueQuietly(String json, Class<T> clazz) {
        try {
            return om.readValue(json, clazz);
        } catch (IOException e) {
            e.printStackTrace();
        }
        return null;
    }

    public static <T> T readValueQuietly(String json, TypeReference<T> typeReference) {
        try {
            return om.readValue(json, typeReference);
        } catch (IOException e) {
            e.printStackTrace();
        }
        return null;
    }

    public static <T> Optional<T> readValue(JsonNode jsonNode, Class<T> clazz) {
        try {
            return Optional.of(om.convertValue(jsonNode, clazz));
        } catch (IllegalArgumentException e) {
            log.warn("Fail to convert jsonNode={} to class={}", jsonNode, clazz.getName(), e);
            return Optional.empty();
        }
    }

    public static <T> T readValue(Object object, Class<T> clazz) throws IOException {
        return om.readValue(JsonUtil.writeValueAsString(object), clazz);
    }

    public static <K, V> Optional<Map<K, V>> readMapValue(String json, Class<K> key, Class<V> value) {
        try {
            return Optional.of(om.readValue(json, om.getTypeFactory().constructParametricType(Map.class, key, value)));
        } catch (IOException e) {
            log.warn("Fail to convert json={} to map", json, e);
            return Optional.empty();
        }
    }

    public static JsonNode readJsonNode(String json) throws IOException {
        return om.readTree(json);
    }

    public static <T> Optional<T> convertType(Object t, Class<T> clazz) {
        return Optional.ofNullable(om.convertValue(t, clazz));
    }

    public static String writeValueAsString(Object object) throws JsonProcessingException {
        return om.writeValueAsString(object);
    }

    public static <T> List<T> toArray(String str, Class<T> tClass) {
        if (null == str || str.isEmpty()) {
            return null;
        }
        try {
            ObjectMapper mapper = new ObjectMapper();
            mapper.configure(DeserializationFeature.ACCEPT_SINGLE_VALUE_AS_ARRAY, true)
                    .configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
            return mapper.readValue(str, mapper.getTypeFactory().constructCollectionType(List.class, tClass));
        } catch (JsonProcessingException e) {
            log.warn("Parse string {} to {} array error", str, tClass.getSimpleName());
            return null;
        }
    }

    public static String writeValueAsStringQuietly(Object object) {
        String json = null;
        try {
            json = om.writeValueAsString(object);
        } catch (JsonProcessingException e) {
            e.printStackTrace();
        }
        return json;
    }

    public static String writeValueAsStringNoThrow(Object object) {
        try {
            return om.writeValueAsString(object);
        } catch (JsonProcessingException e) {
            return null;
        }
    }

    public static byte[] writeValueAsBytes(Object object) throws JsonProcessingException {
        return om.writeValueAsBytes(object);
    }

    public static String writeValueAsStringIgnoreNull(Object object) throws JsonProcessingException {
        return omNoNull.writeValueAsString(object);
    }

    public static <T> List<T> strToList(String str, Class<T> clazz) throws IOException {
        JsonParser jsonParser = new JsonParser();
        JsonArray jsonArray = jsonParser.parse(str).getAsJsonArray();
        List<T> list = new ArrayList<>();
        for (int i = 0; i < jsonArray.size(); i++) {
            T t = readValue(jsonArray.get(i).getAsString(), clazz);
            list.add(t);
        }
        return list;
    }

}
