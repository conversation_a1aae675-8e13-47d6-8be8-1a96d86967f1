package com.baidu.acg.piat.digitalhuman.common.commonkv;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.annotation.Nullable;
import java.util.List;

/**
 * 通用 kv 对查询 vo
 *
 * <AUTHOR>
 * @since 2021/08/05
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CommonKvQueryVO {

    private String userId;

    // 为空则查询 user 下全部 kv
    @Nullable
    private List<String> keyList;
}
