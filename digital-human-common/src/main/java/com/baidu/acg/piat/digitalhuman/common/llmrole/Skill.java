package com.baidu.acg.piat.digitalhuman.common.llmrole;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class Skill {
    String name;  // 技能名称
    String explain;  // 技能说明
    Integer status;      // 技能状态 1表式开启，0表示
    String content;     // 技能内容
    int type;  // 技能类型
    /**
     * 英文名称, 非空
     */
    String skillEngName;
}
