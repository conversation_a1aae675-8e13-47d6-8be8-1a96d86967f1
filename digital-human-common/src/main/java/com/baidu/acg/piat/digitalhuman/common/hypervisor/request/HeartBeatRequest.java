// Copyright (C) 2021 Baidu Inc. All rights reserved.
package com.baidu.acg.piat.digitalhuman.common.hypervisor.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import com.baidu.acg.piat.digitalhuman.common.cloud.Selectors;
import com.baidu.acg.piat.digitalhuman.common.hypervisor.Status;

/**
 * Heart beat request.
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class HeartBeatRequest {

    private String host;

    private Status status;

    private String resourceType;

    private Selectors.Labels labels = new Selectors.Labels();
}
