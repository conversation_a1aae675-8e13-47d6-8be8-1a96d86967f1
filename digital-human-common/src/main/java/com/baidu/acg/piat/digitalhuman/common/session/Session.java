// Copyright (C) 2019 Baidu Inc. All rights reserved.
package com.baidu.acg.piat.digitalhuman.common.session;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.NonNull;

import java.time.ZonedDateTime;
import java.util.List;

import com.baidu.acg.piat.digitalhuman.common.resource.ResourceInstance;
import com.baidu.acg.piat.digitalhuman.common.session.Endpoint.Type;
import com.baidu.acg.piat.digitalhuman.common.webrtc.WebRtcSession;

/**
 * Session
 *
 * <AUTHOR>
 * @since 2019-07-12
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Session {

    private String id;

    private String businessId;

    private String appId;

    private String roomId;

    @NonNull
    private String internalRtcServerUrl;

    @NonNull
    private String externalRtcServerUrl;

    private SessionStatus status;

    private String ue4;

    private List<String> extArgs;

    private List<Endpoint> endpoints;

    private WebRtcSession webRtcConnection;

    private ZonedDateTime createTime;

    private ZonedDateTime updateTime;

    public static Session construct(WebRtcSession webRtcSession, String businessId,
                                    ResourceInstance agent, ResourceInstance proxyServer,
                                    String... args) {

        var now = ZonedDateTime.now();
        return Session.builder()
                .businessId(businessId)
                .appId(webRtcSession.getAppId())
                .roomId(webRtcSession.getRoomId())
                .internalRtcServerUrl(webRtcSession.getInternalRtcServerUrl())
                .externalRtcServerUrl(webRtcSession.getExternalRtcServerUrl())
                .status(SessionStatus.OPEN)
                .extArgs(List.of(args))
                .endpoints(List.of(
                        Endpoint.builder()
                                .id(webRtcSession.getAgentId())
                                .host(agent.getHost())
                                .ip(agent.getIp())
                                .updateTime(now)
                                .type(Type.AGENT)
                                .build(),
                        Endpoint.builder()
                                .id(webRtcSession.getProxyServerId())
                                .host(proxyServer.getHost())
                                .ip(proxyServer.getIp())
                                .updateTime(now)
                                // TODO 暂时将vis2dproxy写成这个参数。
                                .type(Type.PROXY_SERVER)
                                .build(),
                        Endpoint.builder()
                                .id(webRtcSession.getClientId())
                                .updateTime(now)
                                .type(Type.CLIENT)
                                .build()))
                .createTime(now)
                .updateTime(now)
                .build();
    }

}
