package com.baidu.acg.piat.digitalhuman.common.utils;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.stream.Collectors;

public class PathUtil {
    /**
     * 获取文件扩展名
     * ps: 不兼容：tar.gz扩展
     *
     * @return
     */
    public static String getFileExtension(String filename) {
        int lastPointIndex = filename.lastIndexOf('.');
        if (lastPointIndex == -1) {
            return "";
        }

        return filename.substring(filename.lastIndexOf('.') + 1);
    }

    /**
     * 获取父目录
     *
     * @param path
     * @return
     */
    public static Path parent(Path path) {
        return Paths.get(path.toAbsolutePath().toFile().getParent());
    }

    /**
     * 删除文件或目录
     * 如果是目录则递归删除目录下所有内容
     * ps: 不支持link类型的目录
     *
     * @param path 要删除的文件
     * @return
     * @throws IOException
     */
    public static boolean delete(Path path) throws IOException {
        if (Files.isDirectory(path)) {
            return deleteDirectory(path);
        } else if (Files.isRegularFile(path)) {
            return deleteFile(path);
        }

        throw new IOException(String.format("cannot handle path:%s", path.toString()));
    }

    /**
     * 删除文件
     *
     * @param path
     * @return
     * @throws IOException
     */
    private static boolean deleteFile(Path path) throws IOException {
        if (!Files.exists(path)) {
            return true;
        }
        return Files.deleteIfExists(path);
    }

    /**
     * 迭代的清空目录
     *
     * @return
     */
    private static boolean deleteDirectory(Path path) throws IOException {
        if (!Files.exists(path)) {
            return true;
        }

        var children = Files.list(path).collect(Collectors.toList());

        boolean success = true;
        for (Path child : children) {
            try {
                boolean stepResult;
                if (Files.isDirectory(child)) {
                    stepResult = deleteDirectory(child);
                } else {
                    stepResult = deleteFile(child);
                }

                if (!stepResult) {
                    success = false;
                }
            } catch (IOException e) {
                success = false;
            }
        }

        if (success) {
            success = Files.deleteIfExists(path);
        }
        return success;
    }
}
