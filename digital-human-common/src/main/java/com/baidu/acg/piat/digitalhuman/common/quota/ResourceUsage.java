// Copyright (C) 2019 Baidu Inc. All rights reserved.
package com.baidu.acg.piat.digitalhuman.common.quota;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Optional;

/**
 * ResourceUsage
 *
 * <AUTHOR>
 * @since 2019-09-20
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ResourceUsage {

    @Builder.Default
    private Integer roomUsage = 0;

    public void add(ResourceUsage resourceUsage) {
        if (resourceUsage == null) {
            return;
        }
        this.roomUsage = Optional.ofNullable(roomUsage).orElse(0)
                + Optional.ofNullable(resourceUsage.getRoomUsage()).orElse(0);
    }

    public int compare(ResourceQuota resourceQuota) {
        // -1 means infinite
        if (resourceQuota.getRoomLimits() == -1) {
            return -1;
        }
        return Optional.of(roomUsage).orElse(0)
                - Optional.of(resourceQuota.getRoomLimits()).orElse(0);
    }
}
