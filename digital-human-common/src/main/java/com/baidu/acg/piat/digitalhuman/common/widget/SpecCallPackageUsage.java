// Copyright (C) 2020 Baidu Inc. All rights reserved.

package com.baidu.acg.piat.digitalhuman.common.widget;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * SpecCallPackageUsage
 *
 * <AUTHOR>
 * @since 2019-12-31
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SpecCallPackageUsage {

    private String title;

    private List<UsageDetail> usages;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class UsageDetail {

        private String title;

        private String totalLeft;

        private List<UsageDetailItem> items;

    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class UsageDetailItem {

        private String subtitle;

        private String left;

        private String total;

        private String used;

        private Boolean enabled = true;

    }

}
