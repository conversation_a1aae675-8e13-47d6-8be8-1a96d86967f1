package com.baidu.acg.piat.digitalhuman.common.llmdm;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 *
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ConnectBody extends BaseRequestBody {

    private String llmRoleId;

    private String configVersion;

    // 传人像类型过去，预览的时候统计用
    private String characterImage;

    private Map<String, String> parameters;

}
