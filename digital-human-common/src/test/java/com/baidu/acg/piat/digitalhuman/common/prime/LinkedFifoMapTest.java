package com.baidu.acg.piat.digitalhuman.common.prime;

import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

class LinkedFifoMapTest {
    @Test
    void test_put() {
        LinkedFifoMap<Integer, Integer> linkedFifoMap = new LinkedFifoMap<>(10);
        linkedFifoMap.put(1, 1);
        Integer res = linkedFifoMap.get(1);
        assertEquals(res, 1);
    }

    @Test
    void test_put_sameKey() {
        LinkedFifoMap<Integer, Integer> linkedFifoMap = new LinkedFifoMap<>(10);
        linkedFifoMap.put(1, 1);
        linkedFifoMap.put(1, 2);
        Integer res = linkedFifoMap.get(1);
        assertEquals(res, 2);
    }

    @Test
    void test_put_OutOfCapacity() {
        LinkedFifoMap<Integer, Integer> linkedFifoMap = new LinkedFifoMap<>(1);
        linkedFifoMap.put(1, 1);
        linkedFifoMap.put(2, 2);
        Integer res = linkedFifoMap.get(1);
        assertNull(res);
    }
}