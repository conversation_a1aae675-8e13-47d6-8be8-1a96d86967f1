package model

import (
	"acg-ai-go-common/gomysql"
	"acg-ai-go-common/logger"
	"acg-ai-go-common/multilanguage"
	"acg-ai-go-common/utils"
	"context"
	cons "ecloud-large-model/constant"
	"errors"
	"strings"
	"time"

	"gorm.io/gorm"
)

type PromptTemplate struct {
	ID        int64          `json:"id" gorm:"column:id;primarykey;autoIncrement"` // 模版ID`
	User      string         `json:"user" gorm:"column:user;type:varchar(100)"`    // 模版归属者
	Name      string         `json:"name" gorm:"column:name;type:varchar(100);"`   // Prompt模版名称,需要保证全局唯一,作为调用时的参数
	Content   string         `json:"content" gorm:"column:content"`                // 模版内容
	CreatedAt time.Time      `json:"createdAt" gorm:"column:createdAt"`            // 创建时间
	UpdatedAt time.Time      `json:"updatedAt" gorm:"column:updatedAt"`            // 更新时间
	DeletedAt gorm.DeletedAt `json:"-" gorm:"column:deletedAt;index"`              // 删除时间
}

func (pt *PromptTemplate) TableName() string {
	return "ellm_prompt_template_v1"
}

func (pt *PromptTemplate) Insert() error {
	// 创建数据
	return gomysql.DB.Create(&pt).Error
}

func (pt *PromptTemplate) InsertBatch(results []*PromptTemplate) error {
	// 创建数据
	return gomysql.DB.CreateInBatches(&results, len(results)).Error
}

func (pt *PromptTemplate) SaveOrUpdate() error {
	// 创建或更新数据
	return gomysql.DB.Save(&pt).Error
}

func (pt *PromptTemplate) Update() error {
	if pt.ID == 0 {
		return errors.New("ID不能等于0")
	}
	// 更新数据
	return gomysql.DB.Where("id = ?", pt.ID).Updates(&pt).Error
}

func (pt *PromptTemplate) FindByID(id int64, language string) error {
	err := gomysql.DB.Where("id = ?", id).Find(&pt).Error
	if err == nil || err == gorm.ErrRecordNotFound {
		pt.Content = HandMultilingual(pt.Content, language)
		return nil
	}

	return err
}

func (pt *PromptTemplate) FindByIds(ids []int64, language string) ([]*PromptTemplate, error) {
	var pts []*PromptTemplate
	err := gomysql.DB.Where("id in ?", ids).Find(&pts).Error

	for i := 0; pts != nil && i < len(pts); i++ {
		pts[i].Content = HandMultilingual(pts[i].Content, language)
	}

	return pts, err
}

func (pt *PromptTemplate) FindByName(name string, language string) error {
	err := gomysql.DB.Where("name = ?", name).Find(&pt).Error
	if err == nil || err == gorm.ErrRecordNotFound {
		pt.Content = HandMultilingual(pt.Content, language)
		return nil
	}

	return err
}

func (pt *PromptTemplate) Search(user, name string,
	pageNum, pageSize int64, language string) (int64, []*PromptTemplate, error) {
	var count int64
	var pts []*PromptTemplate
	conn := gomysql.DB.Model(&PromptTemplate{})
	if len(user) > 0 {
		conn.Where("user = ?", user)
	}
	if len(name) > 0 {
		conn.Where("name like ?", "%"+name+"%")
	}
	skip, limit := utils.ConvertPageNumSize2Skip(pageNum, pageSize)
	if err := conn.Count(&count).Order("updatedAt desc").
		Limit(int(limit)).Offset(int(skip)).Find(&pts).Error; err != nil {
		return 0, nil, err
	}

	for i := 0; pts != nil && i < len(pts); i++ {
		pts[i].Content = HandMultilingual(pts[i].Content, language)
	}

	return count, pts, nil
}

func (pt *PromptTemplate) DeleteByID(id int64) error {
	return gomysql.DB.
		Where("id = ?", id).
		Delete(&pt).Error
}

// 处理多语言
func HandMultilingual(templateContent string, language string) string {
	if language == "" || !strings.HasPrefix(templateContent, cons.MultiPrefix) {
		return templateContent
	}

	// 通过key 获取多语言文本
	logCtx := context.WithValue(context.TODO(), utils.CtxKeyLogID, utils.RandStringRunes(16))
	logger.Log.Infof(utils.MMark(logCtx)+"templateContent:%v language:%v", templateContent, language)
	mapKeys, _ := multilanguage.GetMultilangStr(logCtx, cons.MultiSvrName, cons.MultiSubTag, []string{templateContent}, language)
	logger.Log.Infof(utils.MMark(logCtx)+"GetMultilangStr mapKeys: %+v\n", mapKeys)
	return mapKeys[templateContent]
}
