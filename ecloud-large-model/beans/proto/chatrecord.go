package proto

import (
	commProto "acg-ai-go-common/beans/proto"
	"ecloud-large-model/beans/enum"
)

type ChatRecord struct {
	ID         string `json:"id"`
	User       string `json:"user"`
	Query      string `json:"query"`
	Answer     string `json:"answer"`
	SessionId  string `json:"sessionId"`
	CreateTime int64  `json:"createTime"`
	UpdateTime int64  `json:"updateTime"`
}

type GetChatUserReq struct {
	commProto.CommSkipPage
	Keyword string `json:"keyword" form:"keyword"`
}

type GetChatRecordReq struct {
	commProto.CommSkipPage
	User string `json:"user" form:"user"`
}

type GetChatRecordReqV2 struct {
	commProto.CommSkipPage
	User      string              `json:"user" form:"user"`
	SessionId string              `json:"sessionId" form:"sessionId"`
	Sn        string              `json:"sn" form:"sn"`
	MarkType  enum.RecordMarkType `json:"markType" form:"markType"`
}

type UpdateChatAnswer struct {
	QueryId string `json:"queryId" binding:"required"`
	Answer  string `json:"answer" binding:"required"`
}

type AddOrUpdateChatRecordReq struct {
	QueryId   string `json:"queryId"`
	Answer    string `json:"answer"`
	User      string `json:"user"`
	Content   string `json:"content"`
	SessionID string `json:"sessionId"`
}

type SNMarkChatRecordReq struct {
	User         string              `json:"user" binding:"required"`
	SN           string              `json:"sn" binding:"required"`
	QuestionType enum.RecordMarkType `json:"questionType" binding:"required"`
	Mark         bool                `json:"mark"`
}
