package proto

import (
	commProto "acg-ai-go-common/beans/proto"
)

type CommPageRsp struct {
	Count    int64       `json:"count"`
	List     interface{} `json:"list"`
	PageNum  int64       `json:"pageNum"`
	PageSize int64       `json:"pageSize"`
}

type AddPromptTemplateReq struct {
	User    string `json:"user"`
	Name    string `json:"name" binding:"required"` // Prompt模版名称
	Content string `json:"content" binding:"required"`
}

type DeletePromptTemplateReq struct {
	ID int64 `json:"id" form:"id" binding:"required"`
}

type UpdatePromptTemplateReq struct {
	ID      int64  `json:"id" binding:"required"`
	User    string `json:"user"`
	Name    string `json:"name" binding:"required"` // Prompt模版名称
	Content string `json:"content" binding:"required"`
}

type SearchPromptTemplateReq struct {
	commProto.CommSkipPage
	User string `json:"user" form:"user"`
	Name string `json:"name" form:"name"` // Prompt模版名称
}

type AIAnswerReq struct {
	Stream      bool   `json:"stream"`
	User        string `json:"user"`
	Content     string `json:"content" binding:"required"`
	LLMModel    string `json:"llmModel"`
	SessionID   string `json:"sessionId"`
	Sn          string `json:"sn"`          // Asr的SN
	CopilotUser string `json:"copilotUser"` // Asr/TTS的App Key
}

type AIAnswerResult struct {
	QueryID    string `json:"queryId"`
	User       string `json:"user"`
	Think      string `json:"think"`
	Content    string `json:"content"`
	SessionID  string `json:"sessionId"`
	IsEnd      bool   `json:"isEnd"`
	IsRisk     bool   `json:"isRisk,omitempty"`
	SentenceID int    `json:"sentenceId"`
	Timestamp  int64  `json:"timestamp,omitempty"`
}

type ReqMessage struct {
	QueryID string `json:"queryId"`
	Role    string `json:"role"`
	Content string `json:"content"`
}

// Usage 对话Tokens信息统计
type Usage struct {
	PromptTokens     int `json:"prompt_tokens"`     // 问题tokens数
	CompletionTokens int `json:"completion_tokens"` // 回答tokens数
	TotalTokens      int `json:"total_tokens"`      // tokens总数
}

type AIPromptTemplateAnswerReq struct {
	DraftID  string            `json:"draftId"`
	Stream   bool              `json:"stream"`
	User     string            `json:"user"`
	PtName   string            `json:"ptName" binding:"required"`
	Input    map[string]string `json:"input" binding:"required"`
	LLMModel string            `json:"llmModel"`
}

type AIPromptTemplateAnswerV3Req struct {
	DraftID   string            `json:"draftId"`
	Stream    bool              `json:"stream"`
	User      string            `json:"user"`
	PtName    string            `json:"ptName" binding:"required"`
	Input     map[string]string `json:"input" binding:"required"`
	LLMModel  string            `json:"llmModel"`
	SessionID string            `json:"sessionId"`
}
