package enum

type RecordMarkType string

const (
	RecordMarkNone RecordMarkType = ""
	RecordMarkAll  RecordMarkType = "all"
	RecordMarkOr   RecordMarkType = "or"
	RecordMarkAsr  RecordMarkType = "asr"
	RecordMarkNlp  RecordMarkType = "nlp"
)

const (
	RoleSystem    = "system"
	RoleUser      = "user"
	RoleAssistant = "assistant"
)

const (
	Ernie3_5_128K             = "Ernie-3.5-128k"
	Ernie3_5_8K               = "Ernie-3.5-8k"
	Ernie4_0_8K               = "Ernie-4.0-8k"
	Deepseek_r1               = "deepseek-r1"
	ChatGLM2_6B               = "ChatGLM2-6b"
	Mix                       = "Mix"
	ErnieEmbedding            = "Embedding-V1"
	Gemini_2_0_flast_001      = "gemini-2.0-flash-001"
	Gemini_2_0_flast_lite_001 = "gemini-2.0-flash-lite-001"
)

const (
	StatusInit    = "INIT"
	StatusRunning = "RUNNING"
	StatusSucceed = "SUCCEED"
	StatusFailed  = "FAILED"
	StatusStopped = "STOPPED"
)

type FeedbackType string

const (
	TypeNone    FeedbackType = "NONE"
	TypeLike    FeedbackType = "LIKE"
	TypeDislike FeedbackType = "DISLIKE"
)

type ContentType string

const (
	Translate = "Translation"
	Writing   = "Writing"
)
