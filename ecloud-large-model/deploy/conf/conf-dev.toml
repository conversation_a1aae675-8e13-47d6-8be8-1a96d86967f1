####################################################### 服务配置-开发环境 #######################################################
server-port = 8114
server-name = "ecloud-large-model"
log-file-prefix = "localhost"
log-file-path = "./logs/"
# consul配置
[consul-setting]
host = "127.0.0.1"
port = 8500
name = ""
# 健康检查地址
health-url = "/health"
check-timeout = "10s"
check-interval = "10s"
deregister-critical-service-after = "30s"

# mysql配置
[mysql-setting]
host = "localhost"
port = 3306
database = "dhlive_third_platform"
username = "root"
password = "123456"
maxOpenConns = 1000
maxIdlenConns = 10

# redis配置
[redis-setting]
addr = "127.0.0.1:6379"
username = ""
password = ""

# 日志上传的elasticsearch配置
[log-es-setting]
host = "http://127.0.0.1:9200"
username = ""
password = ""

[dh-user-setting]
baseUrl = "http://dh-user:80"

[ernie-setting]
qianfan-app-types = ["Ernie-3.5-8k", "Ernie-3.5-128k", "Ernie-4.0-8k", "deepseek-r1", "deepseek-v3"]
ernie3-5-8kUrl = "https://aip.baidubce.com/rpc/2.0/ai_custom/v1/wenxinworkshop/chat/completions"
ernie4-0-8kUrl = "https://aip.baidubce.com/rpc/2.0/ai_custom/v1/wenxinworkshop/chat/completions_pro"
ernie3-5-128kUrl = "https://aip.baidubce.com/rpc/2.0/ai_custom/v1/wenxinworkshop/chat/ernie-3.5-128k"


[chatGLM-setting]
url = "http://*************:8000/v1/chat/completions"

[chatGEMINI-setting]
projectId = "xiling-453607"
location = "europe-west4"
oauthFail = "./deploy/conf/xiling-453607-5f236fdbee17.json"