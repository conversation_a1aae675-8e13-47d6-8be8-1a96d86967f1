package handler

import (
	"acg-ai-go-common/logger"
	"context"
	"ecloud-large-model/beans/enum"
	"ecloud-large-model/beans/model"
	"ecloud-large-model/beans/proto"
	"fmt"
	"github.com/baidubce/bce-qianfan-sdk/go/qianfan"
	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/redis/go-redis/v9"
	"net/http"
	"strings"
	"time"
)

// GetErniePtAnswerV3 prompt模板千帆应用对话-不支持上下文（使用千帆SDK ChatV2方式）(deepseek-r1思考单独字段存放)
func GetErniePtAnswerV3(c *gin.Context) {
	req := &proto.AIPromptTemplateAnswerV3Req{}
	if err := c.ShouldBindJSON(req); err != nil {
		logger.CtxLog(c).Errorf("GetErniePtAnswerV3 bind param fail, err: [%v]", err)
		c.AbortWithStatusJSON(http.StatusOK, proto.NewCommRsp(100001, ParamErrorMsg))
		return
	}
	accountId, _ := c.Get("AccountId")
	logger.CtxLog(c).Infof("accountId:%v", accountId)
	req.User = accountId.(string)
	pt := model.PromptTemplate{}
	lan := GetLanguage(c)
	// 查找prompt模板
	if err := pt.FindByName(req.PtName, lan); err != nil {
		logger.CtxLog(c).Errorf("GetErniePtAnswerV3 pt FindByName fail, err: [%v]", err)
		c.AbortWithStatusJSON(http.StatusOK, proto.NewCommRsp(530001, ServerErrorMsg))
		return
	} else if pt.ID == 0 {
		c.AbortWithStatusJSON(http.StatusOK, proto.NewCommRsp(100002, "["+req.PtName+"]的模版不存在"))
		return
	}
	// 组装Prompt
	prompt := pt.Content
	for k, v := range req.Input {
		prompt = strings.ReplaceAll(prompt, "{{"+k+"}}", v)
	}
	messages := []qianfan.ChatCompletionV2Message{
		{
			Role:    enum.RoleUser,
			Content: prompt,
		},
	}
	// 记录用户请求数据
	if len(req.SessionID) == 0 {
		req.SessionID = uuid.NewString()
	}
	record, err := createErnieChatRecord(req.User, req.DraftID, prompt, req.LLMModel,
		req.PtName, "", req.SessionID, req.Stream)
	if err != nil {
		c.JSON(http.StatusOK, proto.NewCommRsp(530002, ServerErrorMsg))
		return
	}
	// 组装千帆SDK请求参数
	chatErnieReq := &qianfan.ChatCompletionV2Request{
		BaseRequestBody: qianfan.BaseRequestBody{
			Extra: map[string]interface{}{
				"stream": req.Stream,
			},
		},
		Model:        strings.ToLower(req.LLMModel),
		Messages:     messages,
		User:         req.User,
		PenaltyScore: 1.2,
		Temperature:  0.8,
		TopP:         0.8,
	}
	chatReq := &proto.AIAnswerReq{
		Stream:    req.Stream,
		User:      req.User,
		Content:   prompt,
		LLMModel:  req.LLMModel,
		SessionID: req.SessionID,
	}

	// 发送请求获取答案/结果
	switch req.LLMModel {
	case enum.Gemini_2_0_flast_001, enum.Gemini_2_0_flast_lite_001:
		sendGeminiAnswer(c, record.ID, chatReq, prompt, req.LLMModel)
	default:
		sendErnieQianfanAnswerV3(c, record.ID, chatReq, chatErnieReq)
	}

}

func sendErnieQianfanAnswerV3(c *gin.Context, queryId string, req *proto.AIAnswerReq,
	ernieChatReq *qianfan.ChatCompletionV2Request) {
	var chatRecord *model.ErnieChatRecord
	var err error
	if err = chatRecord.UpdateStatusAndErrMsgById(queryId, enum.StatusRunning, enum.StatusRunning); err != nil {
		logger.CtxLog(c).Errorf("sendErnieQianfanAnswerV3 UpdateStatusAndErrMsgById fail, queryId:%v, status:%v, err: %v",
			queryId, enum.StatusRunning, err)
		c.AbortWithStatusJSON(http.StatusOK, proto.NewCommRsp(530004, ServerErrorMsg))
		return
	}
	defer func() {
		var status = enum.StatusSucceed
		var errMsg = enum.StatusSucceed
		if rec := recover(); rec != nil {
			errMsg = fmt.Sprintf("panic: %v", rec)
			status = enum.StatusStopped
			logger.CtxLog(c).Errorf(errMsg)
		}
		if err != nil {
			status = enum.StatusFailed
			errMsg = err.Error()
		}
		_ = chatRecord.UpdateStatusAndErrMsgById(queryId, status, errMsg)
	}()
	// 负载均衡账号，获取Token（实现多账号扩容RPM/TPM）
	app, rpmLimit, err := loadBalanceErnieApp(c, req.LLMModel)
	if err != nil {
		logger.CtxLog(c).Errorf("sendErnieQianfanAnswerV3 loadBalanceErnieApp fail, is rpm limit:%v, err:%v", rpmLimit, err)
		if rpmLimit {
			c.AbortWithStatusJSON(http.StatusOK, proto.NewCommDataRsp(
				530005, "服务繁忙", err.Error()))
			return
		} else if err == redis.Nil {
			app = &model.ErnieApp{
				APIKey: "bce-v3/ALTAK-4Hqmh07HdZMfNeDaFNSm6/93efbe96ee3c7af29b344a9aeb96526900bee247",
			}
			logger.CtxLog(c).Warnf("sendErnieQianfanAnswerV3 loadBalanceErnieApp use default app")
		} else {
			c.AbortWithStatusJSON(http.StatusOK, proto.NewCommDataRsp(
				530006, ServerErrorMsg, err.Error()))
			return
		}
	}
	// 提交请求文心千帆对话
	err = postErnieChatV3(c, queryId, req, app, ernieChatReq)
}

func postErnieChatV3(c *gin.Context, queryId string, req *proto.AIAnswerReq, app *model.ErnieApp,
	ernieChatReq *qianfan.ChatCompletionV2Request) error {
	startTime := time.Now()
	var isRisk = false              // 是否含有风险内容
	var riskIndex = -1              // 风险内容所在的索引位置
	var answerThink = "<think>\n\n" // 最终答案的思考过程
	var answerContent = ""          // 最终答案内容
	var firstCost = "0"             // 首包响应耗时
	var totalCost = "0"             // 总耗时
	var sentenceId = 0              // 句子ID/响应结果索引
	var err error
	defer func() {
		totalCost = fmt.Sprintf("%v", time.Since(startTime))
		// 触发对话风控,riskIndex = -1时表示当前内容
		if isRisk {
			logger.CtxLog(c).Warnf("sendErnieQianfanAnswerV3 存在风险内容, user:%v, queryId:%v, riskIndex: %v",
				req.User, queryId, riskIndex)
		}
		// 更新对话记录
		if err := (&model.ErnieChatRecord{}).UpdateByQueryId(queryId, isRisk, answerContent, firstCost, totalCost); err != nil {
			logger.CtxLog(c).Errorf("sendErnieQianfanAnswerV3 UpdateByQueryId fail, queryId:%v, err: %v", queryId, err)
		}
		logger.CtxLog(c).Infof("postErnieChatV3 stream answer end, cost:%v", totalCost)
	}()
	qianfan.GetConfig().BearerToken = app.APIKey
	chatV2 := qianfan.NewChatCompletionV2(
		qianfan.WithLLMRetryCount(3),          // 自动重试次数
		qianfan.WithModel(ernieChatReq.Model), // 调用的模型
	)
	if req.Stream {
		var resp *qianfan.ChatCompletionV2ResponseStream
		// 流式返回
		resp, err = chatV2.Stream(context.TODO(), ernieChatReq)
		if err != nil {
			logger.CtxLog(c).Errorf("postErnieChatV3 qianfan Stream fail, err:%v", err)
			c.AbortWithStatusJSON(http.StatusOK, proto.NewCommDataRsp(531001, ServerInnerErrorMsg, err.Error()))
			return err
		}
		for !resp.IsEnd {
			r := ChatCompletionV3Response{}
			err = resp.Recv(&r)
			if err != nil {
				logger.CtxLog(c).Errorf("postErnieChatV3 Recv fail, sentenceId:%v, err:%v", sentenceId, err)
				c.AbortWithStatusJSON(http.StatusOK, proto.NewCommDataRsp(531002, ServerInnerErrorMsg, err.Error()))
				return err
			}
			if len(r.Choices) > 0 {
				isRisk = r.Choices[0].Flag > 0
				riskIndex = r.Choices[0].BanRound
				var curContent string
				var curThink string
				curThink = r.Choices[0].Delta.ReasoningContent
				curContent = r.Choices[0].Delta.Content
				if len(curThink) == 0 && len(curContent) == 0 {
					continue
				}
				c.SSEvent("", &proto.AIAnswerResult{
					QueryID:    queryId,
					User:       req.User,
					Think:      curThink,
					Content:    curContent,
					SessionID:  req.SessionID,
					IsEnd:      false,
					IsRisk:     isRisk,
					SentenceID: sentenceId,
					Timestamp:  time.Now().UnixMilli(),
				})
				logger.CtxLog(c).Infof("postErnieChatV3 stream answer success, sentenceId:%v, result:%v",
					sentenceId, curContent)
				c.Writer.Flush()
				answerThink += curThink
				answerContent += curContent
				sentenceId++
			}
		}
		answerContent = answerThink + "</think>\n\n" + answerContent
		// 由于千帆SDK有时不会响应结束标识直接断流结束，因此在断流后自行向客户端发送结束标识
		c.SSEvent("", &proto.AIAnswerResult{
			QueryID:    queryId,
			User:       req.User,
			Content:    "",
			SessionID:  req.SessionID,
			IsEnd:      true,
			IsRisk:     isRisk,
			SentenceID: sentenceId,
			Timestamp:  time.Now().UnixMilli(),
		})
		c.Writer.Flush()
	} else {
		var resp *qianfan.ChatCompletionV2Response
		resp, err = chatV2.Do(c, ernieChatReq)
		if err != nil {
			logger.CtxLog(c).Errorf("postErnieChatV3 chatV2.Do qianfanAPI fail, err:%v, req:%v", err, ernieChatReq)
			c.AbortWithStatusJSON(http.StatusOK, proto.NewCommDataRsp(531002, ServerInnerErrorMsg, err.Error()))
			return err
		} else if resp.Error != nil {
			logger.CtxLog(c).Errorf("postErnieChatV3 call ErnieAPI fail, resp:%v", resp)
			c.AbortWithStatusJSON(http.StatusOK, proto.NewCommDataRsp(531004, ServerInnerErrorMsg, resp))
			return err
		}
		isRisk = resp.Choices[0].Flag > 0
		riskIndex = resp.Choices[0].BanRound
		answerContent = resp.Choices[0].Message.Content
		// 返回结果
		c.JSON(http.StatusOK, proto.NewSuccessRsp(&proto.AIAnswerResult{
			QueryID:   queryId,
			User:      req.User,
			Content:   answerContent,
			SessionID: req.SessionID,
			IsEnd:     true,
			IsRisk:    isRisk,
			Timestamp: time.Now().UnixMilli(),
		}))
		logger.CtxLog(c).Infof("sendYiYanAnswer blocking answer success, result:%v", answerContent)
	}
	return err
}

type ChatCompletionV3Response struct {
	qianfan.ChatCompletionV2Response
	Choices []ChatCompletionV3Choice `mapstructure:"choices"` // 生成结果
}

type ChatCompletionV3Choice struct {
	Index        int                             `mapstructure:"index"`         // 生成结果索引
	Message      qianfan.ChatCompletionV2Message `mapstructure:"message"`       // 生成结果
	Delta        ChatCompletionV3Delta           `mapstructure:"delta"`         // 生成结果
	FinishReason string                          `mapstructure:"finish_reason"` // 生成结果的分数
	Flag         int                             `mapstructure:"flag"`          // 生成结果的标志
	BanRound     int                             `mapstructure:"ban_round"`     // 生成结果
}

type ChatCompletionV3Delta struct {
	Content          string             `mapstructure:"content"`              // 生成结果
	ReasoningContent string             `json:"reasoning_content"`            // 推理过程内容
	ToolCalls        []qianfan.ToolCall `mapstructure:"tool_calls,omitempty"` // 函数调用
}
