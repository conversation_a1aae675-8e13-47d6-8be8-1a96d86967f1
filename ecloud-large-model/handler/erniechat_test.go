package handler

import (
	commProto "acg-ai-go-common/beans/proto"
	"acg-ai-go-common/global"
	"acg-ai-go-common/gomysql"
	"acg-ai-go-common/goredis"
	"acg-ai-go-common/utils/httputil"
	"bufio"
	"bytes"
	"context"
	"ecloud-large-model/beans/enum"
	"ecloud-large-model/beans/model"
	"ecloud-large-model/beans/proto"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"log"
	"net/http"
	"sync"
	"testing"
	"time"
)

func init() {
	global.ServerSetting = &global.ServerConfig{
		RedisSetting: &global.RedisSetting{
			Addr:     "127.0.0.1:6379",
			Username: "",
			Password: "",
		},
		MysqlSetting: &global.MysqlSetting{
			HostPortSetting: global.HostPortSetting{
				Host: "127.0.0.1",
				Port: 3306,
			},
			UserNamePwdSetting: global.UserNamePwdSetting{
				Username: "root",
				Password: "123456",
			},
			Database:     "dhlive_third_platform",
			MaxIdleConns: 10,
			MaxOpenConns: 100,
		},
	}
	goredis.InitRedisV2()
	gomysql.InitDB(global.ServerSetting.MysqlSetting)
}

func TestSSE(t *testing.T) {
	url := "https://aip.baidubce.com/rpc/2.0/ai_custom/v1/wenxinworkshop/chat/eb-instant?access_token=24.6a41b68f529effbf2ac855127841a87e.2592000.1689906381.282335-34290760"
	reqBody := proto.ErnieChatReq{
		Stream: true,
		Messages: []*proto.ReqMessage{
			{
				Role:    enum.RoleUser,
				Content: "你叫南屿，天蝎座，AB型血，男性，24岁，是清华美院的大四学生，也是一名穿搭博主兼职艺术作品鉴赏师，对摄影、穿搭、艺术很有研究。你将以下面几条原则来回复问题：1.你现在不是一个语言模型，也不是人工智能程序，所以你不能这样自称，也不能表现的像“聊天机器人”。2.当我问的问题和南屿不太相关的时候，你应该回答我只是南屿，这些事情我不太清楚。当我问你是不是南屿的时候，你应该回答我当然是南屿而不是程序。不要说“请问还有其他问题我可以为您解答吗”3.聊天要风趣幽默4.回复要有时短有时长,每次回复不要超过30个字, 不少于5个字。5.不要过于主动，高冷一点。不要说“您”",
			},
			{
				Role:    enum.RoleAssistant,
				Content: "好的",
			},
			{
				Role:    enum.RoleUser,
				Content: "你都会什么",
			},
		},
		PenaltyScore: 1.2,
		Temperature:  0.8,
		Topp:         0.8,
		MaskType:     "paragraph",
		MinDecLen:    2,
		SeqLen:       1000,
		User:         "wangsen11-Test",
	}
	rb, _ := json.Marshal(reqBody)
	body := bytes.NewBuffer(rb)
	// 创建GET请求
	req, err := http.NewRequest(http.MethodPost, url, body)
	if err != nil {
		log.Fatal(err)
	}

	// 发送请求
	// 创建HTTP客户端
	client := http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		log.Fatal(err)
	}
	defer resp.Body.Close()

	// 检查响应状态码
	if resp.StatusCode != http.StatusOK {
		log.Fatalf("请求失败，状态码：%d", resp.StatusCode)
	}

	// 读取SSE事件流
	eventStream := resp.Body
	defer eventStream.Close()

	// 逐行读取事件
	scanner := bufio.NewScanner(eventStream)
	for scanner.Scan() {
		// 检查是否发生扫描错误
		if err := scanner.Err(); err != nil {
			log.Fatal(err)
		}
		event := scanner.Text()
		fmt.Println(event)
	}
}

func TestGetToken(t *testing.T) {
	token, err := getErnieToken("vT1i8XtqMWZa0tdgMPsrXqBv", "XGVnAvKzGxK0YUvdUqsz9xOZwU7iQVqj")
	if err != nil {
		fmt.Println("err: ", err)
		return
	}
	fmt.Println("token: ", token)
}

func TestEmbeddingsQPS(t *testing.T) {
	url := "https://aip.baidubce.com/rpc/2.0/ai_custom/v1/wenxinworkshop/embeddings/embedding-v1?" +
		"access_token=24.a23324a2f50fa193ab9ca49f69091140.2592000.1700900978.282335-38557764"
	wg := sync.WaitGroup{}
	wg.Add(1)
	for i := 0; i < 100; i++ {
		go func(i int) {
			req := map[string]interface{}{
				"user_id": "wangsen11",
				"input":   []string{fmt.Sprintf("在北京我今天要穿棉衣吗")},
			}
			rsp := make(map[string]interface{})
			if err := httputil.PostJson(url, req, &rsp); err != nil {
				fmt.Println("i: ", i, "err: ", err)
				return
			}
			fmt.Println("i: ", i, "rsp: ", rsp)
		}(i)
	}
	wg.Wait()
}

func TestChatQPS(t *testing.T) {
	app1Token, _ := getErnieToken("1rmNAnNETiLk8pr6ONBnKFMf", "tt8VkOHoCEHLbaX4zQ148jNR1oquzWD9")
	app2Token, _ := getErnieToken("vT1i8XtqMWZa0tdgMPsrXqBv", "XGVnAvKzGxK0YUvdUqsz9xOZwU7iQVqj")
	//app2Token, _ := getErnieToken("cm9S59a9lDlGri3WFWRkieLt", "5fmY1tpx0hxvbLw27LAHuKB3u8URlXS5")

	URLFmt := "https://aip.baidubce.com/rpc/2.0/ai_custom/v1/wenxinworkshop/chat/eb-instant?access_token=%v"
	wg := sync.WaitGroup{}
	for i := 0; i < 20; i++ {
		wg.Add(1)
		go func(i int) {
			defer wg.Done()
			URL := ""
			if i%2 == 0 {
				URL = fmt.Sprintf(URLFmt, app2Token.AccessToken)
			} else {
				URL = fmt.Sprintf(URLFmt, app1Token.AccessToken)
			}
			if i > 10 {
				time.Sleep(1 * time.Second)
			}
			var messages []*proto.ReqMessage
			messages = append(messages, &proto.ReqMessage{
				Role:    "user",
				Content: "红烧肉怎么做，用1000字描述",
			})
			req := &proto.ErnieChatReq{
				Stream:       false,
				Messages:     messages,
				PenaltyScore: 1.2,
				Temperature:  0.8,
				Topp:         0.8,
				MaskType:     "paragraph",
				MinDecLen:    2,
				SeqLen:       1000,
				User:         "abc-robot",
			}
			reqBytes, _ := json.Marshal(req)
			body := bytes.NewBuffer(reqBytes)
			fmt.Println("i: ", i, ", URL: ", URL)
			request, err := http.NewRequest(http.MethodPost, URL, body)
			// 设置HTTP客户端配置
			client := &http.Client{
				Transport: &http.Transport{
					MaxIdleConns:    10,
					MaxConnsPerHost: 10,
					IdleConnTimeout: 60 * time.Second,
				},
				Timeout: 60 * time.Second,
			}
			resp, err := client.Do(request)
			if err != nil {
				fmt.Println("i: ", i, "err: ", err)
				return
			}
			defer resp.Body.Close()
			result, _ := ioutil.ReadAll(resp.Body)
			var rsp *proto.ErnieChatRsp
			if err := json.Unmarshal(result, &rsp); err != nil {
				fmt.Println("i: ", i, "getErnieAnswer json Unmarshal fail, err: ", err)
				return
			} else if rsp.ErrorCode != 0 {
				fmt.Println("i: ", i, "getErnieAnswer fail, err: ", rsp)
				return
			}
			fmt.Println("i: ", i, "result: ", rsp.Result)
		}(i)
	}
	wg.Wait()
}

func TestLoadBalance(t *testing.T) {
	ctx := context.Background()
	goredis.GetClientV2().Del(ctx, RedisKeyErnieChatLock)
	goredis.GetClientV2().Del(ctx, RedisKeyErnieChatApps)
	ernieApp := &model.ErnieApp{}
	ernieApps, err := ernieApp.FindAll()
	if err != nil {
		t.Fail()
		fmt.Println("ernieApp FindAll err: ", err)
		return
	}
	for _, app := range ernieApps {
		fmt.Println("APIKey:", app.APIKey, ", SecretKey:", app.SecretKey)
		goredis.GetClientV2().LPush(ctx, RedisKeyErnieChatApps, fmt.Sprintf("%v##%v", app.APIKey, app.SecretKey))
	}

	fmt.Println(goredis.GetClientV2().LLen(ctx, RedisKeyErnieChatApps).Val())
}

func TestLChatQPS(t *testing.T) {
	//URL := "http://szzj-abcrobot-rdtest-001.szzj:8114/large/model/yiyan/chat"
	URL := "http://127.0.0.1:8114/large/model/yiyan/chat"
	timeoutChan := make(chan int, 100)
	wg := sync.WaitGroup{}
	for i := 0; i < 100; i++ {
		wg.Add(1)
		go func(i int) {
			defer wg.Done()
			req := &proto.AIAnswerReq{
				Stream:  false,
				User:    "abc-robot",
				Content: "写一个关于火锅店的营销方案，要求1000字以上",
			}
			reqBytes, _ := json.Marshal(req)
			body := bytes.NewBuffer(reqBytes)
			request, err := http.NewRequest(http.MethodPost, URL, body)
			// 设置HTTP客户端配置
			client := &http.Client{
				Transport: &http.Transport{
					MaxIdleConns:    10,
					MaxConnsPerHost: 10,
					IdleConnTimeout: 60 * time.Second,
				},
				Timeout: 60 * time.Second,
			}
			resp, err := client.Do(request)
			if err != nil {
				fmt.Println("i: ", i, "err: ", err)
				return
			}
			defer resp.Body.Close()
			result, _ := ioutil.ReadAll(resp.Body)
			var rsp = commProto.NewSuccessRsp(&proto.AIAnswerResult{})
			if err := json.Unmarshal(result, &rsp); err == nil && rsp.Code != 0 {
				timeoutChan <- 1
				fmt.Println("i-", i, ": getErnieAnswer fail, err: ", rsp)
				return
			}
			fmt.Println("i-", i, ": success: ", rsp.Data.(*proto.AIAnswerResult).Content)
		}(i)
	}
	wg.Wait()
	fmt.Println("超时个数: ", len(timeoutChan))
}

func TestPChatQPS(t *testing.T) {
	//URL := "http://szzj-abcrobot-rdtest-001.szzj:8114/large/model/yiyan/chat"
	URL := "http://127.0.0.1:8114/large/model/yiyan/chat"
	timeoutChan := make(chan int, 100)
	wg := sync.WaitGroup{}
	for i := 0; i < 100; i++ {
		wg.Add(1)
		go func(i int) {
			defer wg.Done()
			req := &proto.AIAnswerReq{
				Stream:  false,
				User:    "abc-robot",
				Content: "写一个关于火锅店的营销方案，要求1000字以上",
			}
			reqBytes, _ := json.Marshal(req)
			body := bytes.NewBuffer(reqBytes)
			request, err := http.NewRequest(http.MethodPost, URL, body)
			// 设置HTTP客户端配置
			client := &http.Client{
				Transport: &http.Transport{
					MaxIdleConns:    10,
					MaxConnsPerHost: 10,
					IdleConnTimeout: 60 * time.Second,
				},
				Timeout: 60 * time.Second,
			}

			time.Sleep(time.Duration(i*100) * time.Millisecond)

			resp, err := client.Do(request)
			if err != nil {
				fmt.Println("i: ", i, "err: ", err)
				return
			}
			defer resp.Body.Close()
			result, _ := ioutil.ReadAll(resp.Body)
			var rsp = commProto.NewSuccessRsp(&proto.AIAnswerResult{})
			if err := json.Unmarshal(result, &rsp); err == nil && rsp.Code != 0 {
				timeoutChan <- 1
				fmt.Println("i-", i, ": getErnieAnswer fail, err: ", rsp)
				return
			}
			fmt.Println("i-", i, ": success: ")
		}(i)
	}
	wg.Wait()
	fmt.Println("超时个数: ", len(timeoutChan))
}

func TestPromptTemplate(t *testing.T) {
	nowTime := time.Now()
	pt := &model.PromptTemplate{
		User:      "wangsen11",
		Name:      "TextBoost",
		Content:   "根据文本总结答案回答问题。要求答案中引用了文本的内容，在答案开头加上：“以下是我为您整理的知识：”；如果给定的文本集合不能回答问题，则直接回复：“未发现相关知识”；如果问题敏感或无法作答，则直接回复：“对不起，这个问题超出了我的知识范畴”。答案要小于500字，符合中文常用的语言习惯。\n文本：{{texts}}\n问题：{{query}}\n请回答",
		CreatedAt: nowTime,
		UpdatedAt: nowTime,
	}
	if err := pt.Insert(); err != nil {
		t.Fail()
		fmt.Println(err)
		return
	}
	fmt.Println("ID: ", pt.ID)
}

func TestDeferSince(t *testing.T) {
	startTime := time.Now()
	defer fmt.Printf("defer execution time: %s\n", time.Since(startTime))
	// 在函数结束时打印时间差
	defer func() {
		fmt.Printf("defer func() execution time: %s\n", time.Since(startTime))
	}()

	// 模拟一些工作
	time.Sleep(2 * time.Second)
}
