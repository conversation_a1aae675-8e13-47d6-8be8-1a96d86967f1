package handler

import (
	"acg-ai-go-common/goredis"
	"acg-ai-go-common/logger"
	"acg-ai-go-common/utils"
	"context"
	"ecloud-large-model/beans/enum"
	"ecloud-large-model/beans/model"
	"ecloud-large-model/beans/proto"
	config "ecloud-large-model/conf"
	"errors"
	"fmt"
	"github.com/gin-gonic/gin"
	"github.com/redis/go-redis/v9"
	"google.golang.org/api/iterator"
	"strings"

	"cloud.google.com/go/vertexai/genai"
	"google.golang.org/api/option"
	"net/http"
	"time"
)

var (
	incrScript = redis.NewScript(`
		local key = KEYS[1]
		local max = tonumber(ARGV[1])
		local expire = tonumber(ARGV[2])

		local current = redis.call('INCR', key)
		if current > max then
			redis.call('DECR', key)
			return 0
		else
			redis.call('EXPIRE', key, expire)
			return 1
		end
	`)

	decrScript = redis.NewScript(`
		local key = KEYS[1]
		local result = redis.call('DECR', key)
		if result <= 0 then
			redis.call('DEL', key)
		end
		return result
	`)
)

const (
	GeminiMaxNumErrorMsg = "You’re submitting requests too quickly. Please try again later."
)

func sendGeminiAnswer(c *gin.Context, queryId string, req *proto.AIAnswerReq, prompt, llmModel string) {
	chatRecord := &model.ErnieChatRecord{}
	var err error
	if err = chatRecord.UpdateStatusAndErrMsgById(queryId, enum.StatusRunning, enum.StatusRunning); err != nil {
		logger.CtxLog(c).Errorf("GetErnieAnswer UpdateStatusAndErrMsgById fail, queryId:%v, status:%v, err: %v",
			queryId, enum.StatusRunning, err)
		c.AbortWithStatusJSON(http.StatusOK, proto.NewCommRsp(530004, ServerErrorMsg))
		return
	}
	defer func() {
		var status = enum.StatusSucceed
		var errMsg = enum.StatusSucceed
		if rec := recover(); rec != nil {
			errMsg = fmt.Sprintf("panic: %v", rec)
			status = enum.StatusStopped
			logger.CtxLog(c).Errorf(errMsg)
		}
		if err != nil {
			status = enum.StatusFailed
			errMsg = err.Error()
		}
		_ = chatRecord.UpdateStatusAndErrMsgById(queryId, status, errMsg)
	}()

	isRisk, answerContent, firstCost, totalCost, err := postGeminiChat(c, queryId, req, prompt, llmModel)
	go func() {
		// 更新对话记录风险
		if isRisk {
			if err = chatRecord.UpdateByQueryId(queryId, isRisk, answerContent, firstCost, totalCost); err != nil {
				logger.CtxLog(c).Errorf("GetErnieAnswer UpdateByQueryId fail, queryId:%v, err: %v", queryId, err)
				return
			}
		}
		// 更新对话记录答案
		if err = chatRecord.UpdateByQueryId(queryId, isRisk, answerContent, firstCost, totalCost); err != nil {
			logger.CtxLog(c).Errorf("GetErnieAnswer UpdateByQueryId fail, queryId:%v, err: %v", queryId, err)
		}
	}()
}

func postGeminiChat(c *gin.Context, queryId string, req *proto.AIAnswerReq, promptText, llmModel string) (
	bool, string, string, string, error) {
	startTime := time.Now()
	var err error
	var answerContent = ""
	var firstCost = "0"
	var totalCost = "0"
	sentenceId := 0
	ctx := context.Background()
	client, err := genai.NewClient(ctx, config.LocalConfig.ChatGEMINISetting.ProjectId,
		config.LocalConfig.ChatGEMINISetting.Location,
		option.WithCredentialsFile(config.LocalConfig.ChatGEMINISetting.OauthFail))
	if err != nil {
		logger.CtxLog(c).Errorf("postGeminiChat NewRequest fail, err:%v", err)
		c.AbortWithStatusJSON(http.StatusOK, proto.NewCommDataRsp(531001, ServerInnerErrorMsg, err.Error()))
		return false, answerContent, firstCost, totalCost, err
	}
	defer func(client *genai.Client) {
		err = client.Close()
		if err != nil {
			logger.CtxLog(c).Errorf("postGeminiChat close client fail, err:%v", err)
		}
	}(client)
	key := fmt.Sprintf("%s:%s:%s_%s", utils.GetNameByRunEnv(), RedisKeyGeminiUserMaxNum, llmModel, req.User)
	allowed, err := incrScript.Run(ctx, goredis.GetClientV2(), []string{key}, 5, 30).Int()
	if err != nil {
		logger.CtxLog(c).Errorf("postGeminiChat NewRequest fail, err:%v", err)
		c.AbortWithStatusJSON(http.StatusOK, proto.NewCommDataRsp(531001, ServerInnerErrorMsg, err.Error()))
		return false, answerContent, firstCost, totalCost, err
	}
	if allowed == 0 {
		logger.CtxLog(c).Errorf("postGeminiChat GeminiMaxNumErrorMsg")
		c.AbortWithStatusJSON(http.StatusOK, proto.NewCommDataRsp(531001, GeminiMaxNumErrorMsg, GeminiMaxNumErrorMsg))
		return false, answerContent, firstCost, totalCost, err
	}

	defer func() {
		_, err := decrScript.Run(ctx, goredis.GetClientV2(), []string{key}).Int()
		if err != nil {
			fmt.Printf("postGeminiChat Warning: decrement failed for %s: %v\n", key, err)
		}
	}()

	gemini := client.GenerativeModel(req.LLMModel)
	prompt := genai.Text(promptText)

	if !req.Stream {
		content := &genai.GenerateContentResponse{}
		content, err = gemini.GenerateContent(ctx, prompt)
		if err != nil {
			logger.CtxLog(c).Errorf("postGeminiChat GenerateContent fail, err:%v", err)
			c.AbortWithStatusJSON(http.StatusOK, proto.NewCommDataRsp(531001, ServerInnerErrorMsg, err.Error()))
			return false, answerContent, firstCost, totalCost, err
		}
		if content.PromptFeedback != nil {
			logger.CtxLog(c).Errorf("postGeminiChat PromptFeedback isRisk:true , msg : %+v", *content.PromptFeedback)
			answerContent = content.PromptFeedback.BlockReasonMessage
			c.JSON(http.StatusOK, proto.NewSuccessRsp(&proto.AIAnswerResult{
				QueryID:   queryId,
				User:      req.User,
				Content:   answerContent,
				SessionID: req.SessionID,
				IsRisk:    true,
				IsEnd:     true,
				Timestamp: time.Now().UnixMilli(),
			}))
			return true, answerContent, firstCost, totalCost, err
		}
		answerContent = responseString(content)
		c.JSON(http.StatusOK, proto.NewSuccessRsp(&proto.AIAnswerResult{
			QueryID:   queryId,
			User:      req.User,
			Content:   answerContent,
			SessionID: req.SessionID,
			IsEnd:     true,
			Timestamp: time.Now().UnixMilli(),
		}))
		return true, answerContent, firstCost, totalCost, err
	}

	stream := gemini.GenerateContentStream(ctx, prompt)
	retryNum := 0
	for {
		var resp = &genai.GenerateContentResponse{}
		resp, err = stream.Next()
		if errors.Is(err, iterator.Done) {
			totalCost = fmt.Sprintf("%v", time.Since(startTime).Milliseconds())
			break
		}
		if err != nil {
			logger.CtxLog(c).Errorf("postGeminiChat stream fail, err:%v , retryNum: %v", err, retryNum)
			if err.Error() == "Resource exhausted, please try again later." && retryNum <= 3 {
				retryNum++
				time.Sleep(time.Second * time.Duration(retryNum))
				stream = gemini.GenerateContentStream(ctx, prompt)
				continue
			}
			c.AbortWithStatusJSON(http.StatusOK, proto.NewCommDataRsp(531001, ServerInnerErrorMsg, err.Error()))
			return false, answerContent, firstCost, totalCost, err
		}
		if resp.PromptFeedback != nil {
			logger.CtxLog(c).Errorf("postGeminiChat PromptFeedback isRisk:true , msg : %+v", *resp.PromptFeedback)
			c.SSEvent("", &proto.AIAnswerResult{
				QueryID:    queryId,
				User:       req.User,
				Content:    "",
				SessionID:  req.SessionID,
				IsEnd:      false,
				IsRisk:     true,
				SentenceID: sentenceId,
				Timestamp:  time.Now().UnixMilli(),
			})
			c.AbortWithStatusJSON(http.StatusOK, proto.NewCommDataRsp(531001, ServerInnerErrorMsg, err.Error()))
			answerContent = resp.PromptFeedback.BlockReasonMessage
			return true, answerContent, firstCost, totalCost, err
		}
		if sentenceId == 0 {
			firstCost = fmt.Sprintf("%v", time.Since(startTime).Milliseconds())
		}
		for _, cand := range resp.Candidates {
			content := contentString(cand.Content)
			c.SSEvent("", &proto.AIAnswerResult{
				QueryID:    queryId,
				User:       req.User,
				Content:    content,
				SessionID:  req.SessionID,
				IsEnd:      false,
				SentenceID: sentenceId,
				Timestamp:  time.Now().UnixMilli(),
			})
			sentenceId++
			answerContent += content
			//if cand.Content
		}
	}
	c.SSEvent("", &proto.AIAnswerResult{
		QueryID:    queryId,
		User:       req.User,
		Content:    "",
		SessionID:  req.SessionID,
		IsEnd:      true,
		SentenceID: sentenceId,
		Timestamp:  time.Now().UnixMilli(),
	})
	return false, answerContent, firstCost, totalCost, nil
}

func responseString(resp *genai.GenerateContentResponse) string {
	var b strings.Builder
	for i, cand := range resp.Candidates {
		if len(resp.Candidates) > 1 {
			fmt.Fprintf(&b, "%d:", i+1)
		}
		if cand.Content != nil {
			b.WriteString(contentString(cand.Content))
		}
	}
	return b.String()
}

func contentString(c *genai.Content) string {
	var b strings.Builder
	for i, part := range c.Parts {
		if i > 0 {
			fmt.Fprintf(&b, ";")
		}
		fmt.Fprintf(&b, "%v", part)
	}
	return strings.ReplaceAll(b.String(), "\n", " ")
}
