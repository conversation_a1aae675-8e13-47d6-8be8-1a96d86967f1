package handler

import (
	commProto "acg-ai-go-common/beans/proto"
	"acg-ai-go-common/goredis"
	"acg-ai-go-common/logger"
	"acg-ai-go-common/utils"
	"ecloud-large-model/beans/model"
	"ecloud-large-model/beans/proto"
	"fmt"
	"github.com/gin-gonic/gin"
	"net/http"
	"time"
)

func GetFeedback(c *gin.Context) {
	// 解析校验参数
	req := &proto.GetFeedbackReq{}
	if err := c.ShouldBindQuery(req); err != nil {
		logger.Log.Errorf("GetFeedback bind param fail, err: [%v]", err)
		c.AbortWithStatusJSON(http.StatusOK, commProto.NewCommDataRsp(100001, "参数错误", err.Error()))
		return
	}
	// 查询数据
	feedback := &model.Feedback{}
	count, fbs, err := feedback.Search(req.UserID, req.ContentID,
		req.ContentType, req.FbType, req.PageNum, req.PageSize)
	if err != nil {
		logger.Log.E<PERSON>rf("GetFeedback FindByUserIdAndQueryId fail, err: [%v]", err)
		c.AbortWithStatusJSON(http.StatusOK, commProto.NewCommDataRsp(530001, ServerErrorMsg, err.Error()))
		return
	}
	// 返回结果
	c.JSON(http.StatusOK, commProto.NewSuccessRsp(commProto.CommPageRsp{
		Count: count,
		List:  fbs,
	}))
}

const (
	SaveFeedbackRateLimitKeyFmt = "saveFeedbackRateLimit:%s" // SaveFeedbackRateLimitKeyFmt SaveFeedback handler rate limit key
	SaveFeedbackRateLimitKeyExp = 1 * time.Second            // SaveFeedbackRateLimitKeyFmt SaveFeedback handler rate limit key exp
)

func SaveFeedback(c *gin.Context) {
	// 解析校验参数
	req := &proto.SaveFeedbackReq{}
	if err := c.ShouldBindJSON(req); err != nil {
		logger.Log.Errorf("SaveFeedback bind param fail, err: [%v]", err)
		c.AbortWithStatusJSON(http.StatusOK, proto.NewCommRsp(100001, "参数错误"))
		return
	}

	accountId, _ := c.Get("AccountId")
	logger.CtxLog(c).Infof("accountId:%v", accountId)
	// 接口访问速度控制 1个账号1s内只能访问一次
	rateLimitKey := utils.GetNameByRunEnv() + ":" + fmt.Sprintf(SaveFeedbackRateLimitKeyFmt, accountId)
	if ok, err := goredis.GetClientV2().SetNX(c, rateLimitKey, 1, SaveFeedbackRateLimitKeyExp).Result(); err != nil {
		logger.Log.Errorf("SaveFeedback SetNX fail, key: [%s], err: [%v]", rateLimitKey, err)
		c.AbortWithStatusJSON(http.StatusOK, proto.NewCommRsp(530001, ServerErrorMsg))
		return
	} else if !ok {
		logger.Log.Warnf("SaveFeedback handler rate limit, key: [%s]", rateLimitKey)
		c.AbortWithStatusJSON(http.StatusOK, proto.NewCommRsp(330001, "操作太频繁了~"))
		return
	}
	feedback := &model.Feedback{}
	if err := feedback.FindByContentIdAndType(accountId.(string), req.ContentID, req.ContentType); err != nil {
		logger.Log.Errorf("SaveFeedback FindByContentIdAndType fail, err: [%v]", err)
		c.AbortWithStatusJSON(http.StatusOK, proto.NewCommRsp(530002, ServerErrorMsg))
		return
	}
	if feedback.ID > 0 {
		feedback.DraftID = req.DraftID
		feedback.FbType = req.FbType
		feedback.Reason = &req.Reason
		if err := feedback.Update(); err != nil {
			logger.Log.Errorf("SaveFeedback Update fail, err: [%v]", err)
			c.AbortWithStatusJSON(http.StatusOK, proto.NewCommRsp(530003, ServerErrorMsg))
			return
		}
	} else {
		// 记录数据
		feedback = &model.Feedback{
			DraftID:     req.DraftID,
			ContentID:   req.ContentID,
			ContentType: req.ContentType,
			UserID:      accountId.(string),
			FbType:      req.FbType,
			Reason:      &req.Reason,
			CreatedAt:   time.Now(),
			UpdatedAt:   time.Now(),
		}
		if err := feedback.Insert(); err != nil {
			logger.Log.Errorf("SaveFeedback Insert fail, err: [%v]", err)
			c.AbortWithStatusJSON(http.StatusOK, proto.NewCommRsp(530004, ServerErrorMsg))
			return
		}
	}
	// 返回成功
	c.JSON(http.StatusOK, proto.NewSuccessRsp(nil))
}
