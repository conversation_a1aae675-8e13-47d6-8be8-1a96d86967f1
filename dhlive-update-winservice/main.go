package main

import (
	"acg-ai-go-common/global"
	"acg-ai-go-common/logger"
	"dhlive-update-winservice/internal/service"
	"dhlive-update-winservice/pkg/conf"
	"dhlive-update-winservice/pkg/utils"
	"fmt"
	"log"
	"os"
	"os/signal"
	"path"
	"sync"
	"syscall"
	"time"

	"github.com/BurntSushi/toml"
	rotatelogs "github.com/lestrrat-go/file-rotatelogs"
)

var exitChan = make(chan struct{})
var wg sync.WaitGroup

func loopFunction() {
	defer wg.Done()
	starttime := time.Now()
	starttimeupload := time.Now()
	service.SoftUpdate()
	service.UploadLocalConfig()
	for {
		select {
		case <-exitChan:
			logger.Log.Infoln("loopFunction received exit signal. Exiting...")
			return
		default:
			if service.IsSelfUpdate {
				logger.Log.Info("update goroutine exit...")
				// 退出程序
				os.Exit(0)
				return
			}
			endtime := time.Now()

			if endtime.Sub(starttime) > 90*time.Second {
				starttime = time.Now()
				service.SoftUpdate()
			}

			if endtime.Sub(starttimeupload) > 2*time.Minute {
				starttimeupload = time.Now()
				service.UploadLocalConfig()
			}

			time.Sleep(1 * time.Second)
		}
	}
}

const ConfFilePath = "./conf/conf.toml"

// go build -ldflags -H=windowsgui
func main() {
	fmt.Println("dhlive-update-winservice is running...")
	if _, err := toml.DecodeFile(ConfFilePath, global.ServerSetting); err != nil {
		log.Panicf("local config load err : {%v}", err)
	}

	if _, err := toml.DecodeFile(ConfFilePath, conf.LocalConfig); err != nil {
		log.Panicf("local config load err: {%v}", err)
	}

	// 创建日志路径
	utils.CreateFolderIfNotExists("./logs/")
	//初始化服务名称，供logger对象使用
	logger.SetLogger()
	logger.Log.Infoln("dhlive-update-winservice is running...")
	//初始化服务名称，供logger对象使用
	logger.SetLogger()
	logFilePath := "./logs/"
	if len(global.ServerSetting.LogFilePath) > 0 {
		logFilePath = global.ServerSetting.LogFilePath
	}
	logFilePrefix := global.ServerSetting.ServerName
	if len(global.ServerSetting.LogFilePrefix) > 0 {
		logFilePrefix = global.ServerSetting.LogFilePrefix
	}

	// 日志文件
	fileName := path.Join(logFilePath, logFilePrefix)
	// 设置 rotate logs
	logWriter, err := rotatelogs.New(
		// 分割后的文件名称
		fileName+".%Y%m%d.log",
		// 生成软链，指向最新日志文件
		rotatelogs.WithLinkName(fmt.Sprintf("%s_latest.log", fileName)),
		// 设置最大保存时间(7天)
		rotatelogs.WithMaxAge(24*time.Hour),
		// 设置日志切割时间间隔(1天)
		rotatelogs.WithRotationTime(24*time.Hour),
	)

	if err != nil {
		logger.Log.Panicf("logWriter err: %v", err)
	}
	logger.Log.SetOutput(logWriter)

	wg.Add(1)
	// 开启循环
	go loopFunction()

	// 创建一个接收退出信号的通道
	osexitChan := make(chan os.Signal, 1)
	signal.Notify(osexitChan, syscall.SIGINT, syscall.SIGTERM)
	// 等待接收退出信号
	<-osexitChan
	// 收到退出信号后执行清理工作，然后退出程序
	logger.Log.Infof("Received exit signal. Performing cleanup and exiting...")

	close(exitChan)
	wg.Wait()
	logger.Log.Infof("exit")
	// 退出程序
	os.Exit(0)
}
