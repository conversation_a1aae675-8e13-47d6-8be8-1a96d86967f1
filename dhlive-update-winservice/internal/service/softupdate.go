package service

import (
	"acg-ai-go-common/global"
	"acg-ai-go-common/logger"
	"dhlive-update-winservice/api"
	"dhlive-update-winservice/pkg/conf"
	"dhlive-update-winservice/pkg/download"
	"dhlive-update-winservice/pkg/utils"
	"encoding/json"
	"errors"
	"fmt"
	"os"
	"strings"
	"sync"
	"time"

	"github.com/go-ini/ini"
)

const (
	SoftUpdateConfigUrlFmt       = "%s/api/digitalhuman/dhlive/update/service/v1/software/config/windows?machineName=%s"
	WinClientVersionReportUrlFmt = "%s/api/digitalhuman/dhlive/update/service/v1/software/config/windows"
)

var (
	mapConfig = sync.Map{}

	IsSelfUpdate = false
	SoftDir      = "c:\\update-cache"
)

// 开启定时器，定时更新
func SoftUpdate() {
	softconf, err := getSoftUdpateConfig()
	if err != nil {
		logger.Log.Errorf("获取软件更新配置失败,err:%v", err)
		return
	}

	for i, config := range softconf {
		if config.SoftFlag == 2 {
			// 判断是否需要更新
			version, err := loadConfig(config.SoftConfigPath, config.SoftConfigVersionName)
			if err != nil {
				logger.Log.Errorf("soft name: %s read version config failed", config.SoftName)
				continue
			}
			versionflag := utils.CompareVersion(config.SoftVersion, version)
			if versionflag > 0 {
				logger.Log.Infof("soft name:%s need update", config.SoftName)
			} else if versionflag == 0 {
				logger.Log.Infof("soft name: %s not neet update", config.SoftName)
				continue
			} else if versionflag < 0 {
				logger.Log.Infof("soft name:%s The version is too early to upgrade", config.SoftName)
				continue
			}
		} else if config.SoftFlag == 3 {
			continue
		} else if config.SoftFlag == 1 || config.SoftFlag == 4 {
			// 判断是否需要更新
			version, err := loadConfig(config.SoftConfigPath, config.SoftConfigVersionName)
			if err != nil {
				version = "0.0.0"
			}
			versionflag := utils.CompareVersion(config.SoftVersion, version)
			if versionflag == 0 {
				logger.Log.Infof("soft name: %s not need update", config.SoftName)
				continue
			} else if versionflag < 0 {
				logger.Log.Infof("soft name:%s The version is too early to upgrade", config.SoftName)
				continue
			}
		}

		// 判断是否需要更新
		if conf, ok := mapConfig.Load(config.SoftName + string(config.MachineType)); ok {
			softconf := conf.(api.SoftConfig)
			if softconf.SoftVersion == config.SoftVersion {
				logger.Log.Infof("soft name: %s is newest,not update", config.SoftName)
				continue
			}
		}

		index := strings.LastIndex(config.SoftInstallPath, "/") // 获取最后一个\的位置
		total := len(config.SoftInstallPath)
		if index == total-1 {
			config.SoftInstallPath = config.SoftInstallPath[0:index]
		}

		// 软件自升级
		if global.ServerSetting.ServerName == config.SoftName {
			err := selfSoftUpdate(config.SoftName, config.SoftUrl, config.SoftInstallPath, config.SoftMd5, config.SoftRunCmd, config.SoftCloseCmd)
			if err != nil {
				logger.Log.Errorf("soft name: %s update failed err:%v", config.SoftName, err)
				return
			}
			logger.Log.Infof("soft name:%s update succeed, Wait for the scheduled task to start", config.SoftName)
			IsSelfUpdate = true
			return
		}

		// 升级软件
		err := signalSoftUpdate(i, len(softconf), config)
		if err != nil {
			logger.Log.Errorf("soft name:%s update failed:%v", config.SoftName, err)
		} else {
			logger.Log.Infof("soft name:%s update succeed", config.SoftName)
			mapConfig.Store(config.SoftName+string(config.MachineType), config)
		}
	}
}

func getSoftUdpateConfig() ([]api.SoftConfig, error) {
	res := api.WinClientGetSoftConfigResponse{}
	var softconf = []api.SoftConfig{}
	osname, err := os.Hostname()
	if err != nil {
		return softconf, err
	}

	resp, err := utils.GetRequest(fmt.Sprintf(SoftUpdateConfigUrlFmt, conf.LocalConfig.UpdateConfig.Baseurl, osname))
	if err != nil {
		return softconf, err
	}

	logger.Log.Infof("get update config: %s", resp)
	err = json.Unmarshal([]byte(resp), &res)
	if err != nil {
		return softconf, err
	}

	return res.Result.SoftConfigs, err
}

func signalSoftUpdate(i, total int, config api.SoftConfig) (err error) {
	logger.Log.Infof("%s begin to update, index: %d/%d", config.SoftName, i, total)
	isrun, err := utils.IsProcessRunning(config.SoftExeName)
	if err != nil {
		logger.Log.Errorf("%s IsProcessRunning err: %v", config.SoftName, err)
		return err
	}

	if isrun {
		// 关闭软件
		err = softClose(config.SoftName+"_close", config.SoftCloseCmd)
		if err != nil {
			logger.Log.Errorf("%s close failed: %v", config.SoftName, err)
			return err
		}

		// 如果返回错误需要重启软件
		defer func() {
			if err != nil {
				// 执行启动命令
				runcmd := "cd /d " + config.SoftInstallPath + " && " + config.SoftRunCmd
				err = softStart(config.SoftName+"_start", runcmd)
				if err != nil {
					logger.Log.Errorf("%s start failed: %v", config.SoftName, err)
				}
			}
		}()
	}

	if config.SoftFlag == 4 {
		if ok := utils.Exists(config.SoftInstallPath); ok {
			// 执行卸载命令
			err = softUnInstall(config.SoftName+"_uninstall", config.SoftUninstallCmd)
			if err != nil {
				logger.Log.Errorf("%s uninstall failed: %v", config.SoftName, err)
				return err
			}
		}
	}

	// 软件安装
	err = softInstall(config.SoftName, config.SoftUrl, config.SoftInstallPath, config.SoftMd5)
	if err != nil {
		logger.Log.Errorf("%s update failed: %v", config.SoftName, err)
		return err
	}

	runcmd := "cd /d " + config.SoftInstallPath + " && " + config.SoftRunCmd
	// 执行启动命令
	err = softStart(config.SoftName+"_start", runcmd)
	if err != nil {
		logger.Log.Errorf("%s start failed: %v", config.SoftName, err)
		return err
	}

	logger.Log.Infof("%s update success", config.SoftName)
	return nil
}

func selfSoftUpdate(softName, softUrl, softInstallPath, softMd5, softRunCmd, softCloseCmd string) error {
	// 下载文件
	newFolderName, err := selfSoftInstall(softName, softUrl, softInstallPath, softMd5)
	if err != nil {
		logger.Log.Errorf("%s selfSoftInstall failed: %v", softName, err)
		return err
	}

	path := strings.Split(softInstallPath, "/") // 获取文件夹名称
	newpath := strings.Join(path[:len(path)-1], "\\")
	newsoftInstallPath := strings.Join(path, "\\")
	newsoftInstallPath = strings.Replace(newsoftInstallPath, "\\\\", "\\", 1)
	timeprx := time.Now().Format("2006-01-02_15-04-05")

	softInstallPath = strings.Replace(softInstallPath, "//", "\\\\", 1)
	softInstallPath = strings.Replace(softInstallPath, "/", "\\", -1)
	newFolderName = strings.Replace(newFolderName, "//", "\\\\", 1)
	newFolderName = strings.Replace(newFolderName, "/", "\\", -1)

	cmd := softCloseCmd + "\n"
	cmd += "cd /d " + newpath + "\n"
	cmd += fmt.Sprintf("IF EXIST \"%s\" ( %s ) \n", softInstallPath, "move  "+softInstallPath+" "+softInstallPath+timeprx)
	cmd += "rd /s /q C:\\dhlive-update-winservice \n"
	cmd += "move  " + newFolderName + " " + softInstallPath + "\n"
	cmd += "cd /d " + newsoftInstallPath + "\n"
	cmd += softRunCmd + "\n"

	// 执行启动命令
	cmdpath, err := createBatFile(cmd, softName, SoftDir)
	if err != nil {
		logger.Log.Errorf("%s create bat file failed: %v", softName, err)
		return err
	}
	err = utils.DeleteTask("dhlive-update-service_update_task")
	if err != nil {
		logger.Log.Warnf("%s DeleteTask failed: %v", softName, err)
		// return err
	}
	// 开启定时任务
	err = utils.CreateScheduledTask("dhlive-update-service_update_task", cmdpath, time.Now().Add(2*time.Minute))
	if err != nil {
		logger.Log.Errorf("%s CreateScheduledTask failed: %v", softName, err)
		return err
	}

	return nil
}

func selfSoftInstall(softName, softUrl, softInstallPath, softMd5 string) (newFolderName string, err error) {
	// 下载文件
	err = utils.CreateFolderIfNotExists(SoftDir)
	if err != nil {
		logger.Log.Errorf("%s CreateFolderIfNotExists failed: %v", softName, err)
		return "", err
	}
	// 获取下载文件路径
	filename, err := utils.GetUrlFileName(softUrl)
	if err != nil {
		logger.Log.Errorf("%s GetUrlFileName failed: %v", softName, err)
		return "", err
	}
	tmpfilepath := strings.Join([]string{SoftDir, filename}, "\\")

	err = download.DownloadFile(softUrl, tmpfilepath)
	if err != nil {
		logger.Log.Errorf("%s DownloadFile failed: %v", softName, err)
		return "", err
	}

	// 删除临时文件
	if tmpfilepath != SoftDir {
		defer os.RemoveAll(tmpfilepath)
	}

	// 校验md5
	md5, err := utils.CalculateMD5(tmpfilepath)
	if err != nil {
		logger.Log.Errorf("%s CalculateMD5 failed: %v", softName, err)
		return "", err
	}

	if md5 != softMd5 {
		logger.Log.Errorf("%s md5 check failed md5: %s", softName, md5)
		return "", errors.New("md5 check failed")
	}
	// 将旧版本软件文件夹重新命名
	index := strings.LastIndex(softInstallPath, "/") // 获取最后一个\的位置
	newFolderName = softInstallPath + "_new"
	total := len(softInstallPath)
	if index == total-1 {
		newFolderName = softInstallPath[0:index] + "_new"
	}

	// 删除旧版本软件文件夹
	os.Remove(newFolderName)

	// 解压文件
	err = utils.Unzip(tmpfilepath, newFolderName)
	if err != nil {
		logger.Log.Errorf("%s Unzip failed: %v", softName, err)
		return "", err
	}
	return newFolderName, nil
}

func softInstall(softName, softUrl, softInstallPath, softMd5 string) (err error) {
	// 下载文件
	err = utils.CreateFolderIfNotExists(SoftDir)
	if err != nil {
		logger.Log.Errorf("%s CreateFolderIfNotExists failed: %v", softName, err)
		return err
	}

	// 获取下载文件路径
	filename, err := utils.GetUrlFileName(softUrl)
	if err != nil {
		logger.Log.Errorf("%s GetUrlFileName failed: %v", softName, err)
		return err
	}
	tmpfilepath := strings.Join([]string{SoftDir, filename}, "\\")

	err = download.DownloadFile(softUrl, tmpfilepath)
	if err != nil {
		logger.Log.Errorf("%s DownloadFile failed: %v", softName, err)
		return err
	}

	// 删除临时文件
	if tmpfilepath != SoftDir {
		defer os.Remove(tmpfilepath)
	}

	// 校验md5
	md5, err := utils.CalculateMD5(tmpfilepath)
	if err != nil {
		logger.Log.Errorf("%s CalculateMD5 failed: %v", softName, err)
		return err
	}

	if md5 != softMd5 {
		logger.Log.Errorf("%s md5 check failed md5: %s", softName, md5)
		return errors.New("md5 check failed")
	}

	if utils.Exists(softInstallPath) {
		// 将旧版本软件文件夹重新命名
		timesuff := time.Now().Format("2006-01-02_15-04-05")
		index := strings.LastIndex(softInstallPath, "/") // 获取最后一个\的位置
		newFolderName := softInstallPath + "_" + timesuff
		total := len(softInstallPath)
		if index == total-1 {
			newFolderName = softInstallPath[0:index] + "_" + timesuff
		}

		err = os.Rename(softInstallPath, newFolderName)
		if err != nil {
			logger.Log.Errorf("%s rename %s failed: %v", softInstallPath, newFolderName, err)
			err = os.RemoveAll(softInstallPath)
			if err != nil {
				logger.Log.Errorf("%s remove %s failed: %v", softInstallPath, newFolderName, err)
				return err
			}
		} else {
			defer func() {
				if err != nil {
					err = os.Rename(newFolderName, softInstallPath)
					if err != nil {
						logger.Log.Errorf("%s rename %s failed: %v", newFolderName, softInstallPath, err)
					}
				}
			}()
		}

	}

	// 解压文件
	err = utils.Unzip(tmpfilepath, softInstallPath)
	if err != nil {
		// 删除解压的文件夹
		os.RemoveAll(softInstallPath)
		logger.Log.Errorf("%s Unzip failed: %v", softName, err)
		return err
	}

	return nil
}

func createBatFile(softRunCmd, softName, batdir string) (string, error) {
	err := utils.CreateFolderIfNotExists(batdir)
	if err != nil {
		logger.Log.Errorf("%s CreateFolderIfNotExists failed: %v", softName, err)
		return "", err
	}
	timesuff := time.Now().Unix()
	batpath := fmt.Sprintf("%s\\%s-%d.bat", batdir, softName, timesuff)
	err = utils.CreateBatFile(softRunCmd, batpath)
	if err != nil {
		logger.Log.Errorf("%s create bat file failed: %v", softName, err)
		return batpath, err
	}
	return batpath, nil
}

func softUnInstall(softName, softUninstallCmd string) error {
	// 执行卸载命令
	uninstallbatpath, err := createBatFile(softUninstallCmd, softName, SoftDir)
	if err != nil {
		logger.Log.Errorf("%s create bat file failed: %v", softName, err)
		return err
	}
	defer os.Remove(uninstallbatpath)

	_, err = utils.ExecuteBatFile(uninstallbatpath)
	if err != nil {
		logger.Log.Errorf("%s execute bat file failed: %v", softName, err)
		return err
	}

	// logger.Log.Infof("%s execute unintall bat file output: %v", softName, string(output))
	return nil
}

func softStart(softName, softRunCmd string) error {
	// 执行启动命令
	runbatpath, err := createBatFile(softRunCmd, softName, SoftDir)
	if err != nil {
		logger.Log.Errorf("%s create bat file failed: %v", softName, err)
		return err
	}
	// defer os.Remove(runbatpath)

	err = utils.StartScriptInBackground(runbatpath)
	if err != nil {
		logger.Log.Errorf("%s execute bat file failed: %v", softName, err)
		return err
	}

	// logger.Log.Infof("%s execute start run bat file", softName)
	return nil
}

func softClose(softName, softCloseCmd string) error {
	// 执行关闭命令
	clostbatpath, err := createBatFile(softCloseCmd, softName, SoftDir)
	if err != nil {
		logger.Log.Errorf("%s create bat file failed: %v", softName, err)
		return err
	}
	defer os.Remove(clostbatpath)

	_, err = utils.ExecuteBatFile(clostbatpath)
	if err != nil {
		logger.Log.Errorf("%s execute bat file failed: %v", softName, err)
		return err
	}
	// logger.Log.Infof("%s execute close bat file output: %v", softName, string(output))
	return nil
}

func loadConfig(configPath, softConfigVersionName string) (string, error) {
	// 加载INI文件
	cfg, err := ini.Load(configPath)
	if err != nil {
		return "", err
	}

	// 访问特定的section和key
	section, err := cfg.GetSection("Software")
	if err != nil {

		return "", err
	}

	// 获取key的值
	softConfigPath := section.Key(softConfigVersionName).String()
	return softConfigPath, nil
}

func UploadLocalConfig() {
	//isupload := false
	var requst api.WinClientVersionReportRequest
	// 获取本机名称
	// 获取本机主机名
	hostname, err := os.Hostname()
	if err != nil {
		logger.Log.Errorf("获取主机名错误: %v", err)
		return
	}

	if len(hostname) <= 0 {
		logger.Log.Errorf("获取主机名失败")
		return
	}

	logger.Log.Infof("主机名: %v", hostname)
	// 获取本机IP
	ips, err := utils.GetIPAddresses()
	if err != nil {
		logger.Log.Errorf("获取IP地址错误: %v", err)
		return
	}

	requst.MachineName = hostname
	requst.MachineIps = ips

	mapConfig.Range(func(key, value interface{}) bool {
		sconf := value.(api.SoftConfig)
		// 判断是否需要更新
		version, err := loadConfig(sconf.SoftConfigPath, sconf.SoftConfigVersionName)
		if err != nil {
			logger.Log.Errorf("soft name: %s read version config failed", sconf.SoftName)
			return true
		}

		if sconf.SoftVersion != version {
			//isupload = true
			sconf.SoftVersion = version
		}

		requst.SoftConfigs = append(requst.SoftConfigs, sconf)
		return true // 继续遍历
	})

	// 判断是否需要更新配置
	// if !isupload {
	// 	return
	// }

	// 发送请求
	if err = winClientVersionReport(&requst); err != nil {
		logger.Log.Errorf("send request failed: %v", err)
	}
}

func winClientVersionReport(requst *api.WinClientVersionReportRequest) error {
	response, err := utils.PostRequest(fmt.Sprintf(WinClientVersionReportUrlFmt, conf.LocalConfig.UpdateConfig.Baseurl), requst)
	if err != nil {
		return err
	}
	logger.Log.Infof("response: %v", string(response))
	return nil
}
