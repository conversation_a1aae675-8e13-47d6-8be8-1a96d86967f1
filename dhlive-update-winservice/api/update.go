package api

type MachineType string

const (
	PpeMachine    MachineType = "PPE"
	OnlineMachine MachineType = "ONLINE"
)

type SoftConfig struct {
	SoftName              string      `json:"softName" gorm:"column:softName;not null"`                  // 软件名称
	SoftExeName           string      `json:"softExeName" gorm:"column:softExeName;not null"`            // 软件执行名称
	MachineName           string      `json:"machineName" gorm:"column:machineName"`                     // 机器名称
	SoftVersion           string      `json:"softVersion" gorm:"column:softVersion;not null"`            // 软件版本
	MachineType           MachineType `json:"machineType" gorm:"column:machineType"`                     // 机器类型
	SoftUrl               string      `json:"softUrl" gorm:"column:softUrl"`                             // 软件下载地址
	SoftInstallPath       string      `json:"softInstallPath" gorm:"column:softInstallPath"`             // 软件安装路径
	SoftMd5               string      `json:"softMd5" gorm:"column:softMd5"`                             // 软件md5值
	SoftRunCmd            string      `json:"softRunCmd" gorm:"column:softRunCmd"`                       // 软件启动命令
	SoftCloseCmd          string      `json:"softCloseCmd" gorm:"column:softCloseCmd"`                   // 软件关闭命令
	SoftUninstallCmd      string      `json:"softUninstallCmd" gorm:"column:softUninstallCmd"`           // 注意字段名修正：软件卸载命令
	SoftFlag              int64       `json:"softFlag" gorm:"column:softFlag;not null"`                  // 1:强制升级 2:非强制升级 3:不升级 4.强制升级并卸载
	SoftConfigPath        string      `json:"softConfigPath" gorm:"column:softConfigPath"`               // 软件配置文件路径
	SoftConfigVersionName string      `json:"softConfigVersionName" gorm:"column:softConfigVersionName"` // 软件配置文件版本名称
}

type WinClientVersionReportRequest struct {
	MachineName string       `json:"machineName"`
	MachineIps  []string     `json:"machineIps"`
	SoftConfigs []SoftConfig `json:"softConfigs"`
}

type WinClientVersionReportResponse struct {
	LogId string `json:"logId"`
}

type WinClientGetSoftConfigResponse struct {
	Code    int                `json:"code"`
	Message string             `json:"message"`
	Result  SoftConfigResponse `json:"result"`
}

type SoftConfigResponse struct {
	LogId       string       `json:"logId"`
	SoftConfigs []SoftConfig `json:"softConfigs"`
}
