package download

import (
	"fmt"
	"io"
	"net/http"
	"os"
)

// DownloadFile 下载指定 URL 的文件并保存到指定路径
func DownloadFile(url string, filepath string) error {
	// 发起 GET 请求
	response, err := http.Get(url)
	if err != nil {
		return fmt.Errorf("failed to download file: %w", err)
	}
	defer response.Body.Close()

	// 检查响应状态码
	if response.StatusCode != http.StatusOK {
		return fmt.Errorf("failed to download file: server responded with %v", response.Status)
	}

	// 创建目标文件
	out, err := os.Create(filepath)
	if err != nil {
		return fmt.Errorf("failed to create file: %w", err)
	}
	defer out.Close()

	// 将响应体的内容写入目标文件
	if _, err := io.Copy(out, response.Body); err != nil {
		return fmt.Errorf("failed to save file: %w", err)
	}

	return nil
}
