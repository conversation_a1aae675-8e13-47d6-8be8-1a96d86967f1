package utils

import (
	"bytes"
	"crypto/md5"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"io"
	"io/ioutil"
	"net"
	"net/http"
	"os"
	"os/exec"
	"strconv"
	"strings"
	"time"
)

func GetUrlFileName(url string) (string, error) {
	return url[strings.LastIndex(url, "/")+1:], nil
}

// CalculateMD5 计算指定文件的 MD5 哈希值
func CalculateMD5(filePath string) (string, error) {
	// 打开指定的文件
	file, err := os.Open(filePath)
	if err != nil {
		return "", fmt.Errorf("failed to open file: %w", err)
	}
	defer file.Close()

	// 创建 MD5 哈希计算的实例
	hasher := md5.New()

	// 将文件内容写入哈希计算器
	if _, err := io.Copy(hasher, file); err != nil {
		return "", fmt.Errorf("failed to copy file content: %w", err)
	}

	// 计算哈希值并转换为十六进制字符串
	hash := hasher.Sum(nil)
	return hex.EncodeToString(hash), nil
}

// StartProgram 启动指定程序
func StartProgram(program string) error {
	cmd := exec.Command(program)
	return cmd.Start()
}

// CloseProgram 关闭指定程序
func CloseProgram(programName string) error {
	// 使用 taskkill 命令关闭程序
	cmd := exec.Command("taskkill", "/IM", programName, "/F")
	return cmd.Run()
}

// executeBatFile 执行给定路径的 .bat 文件并返回输出或错误
func ExecuteBatFile(batFilePath string) (string, error) {
	// 通过 exec.Command 来执行 .bat 文件
	cmd := exec.Command("cmd.exe", "/C", batFilePath)

	// 运行命令，并获取输出
	output, err := cmd.CombinedOutput()
	if err != nil {
		return "", fmt.Errorf("error executing bat file: %w", err)
	}

	return string(output), nil
}

// StartScriptInBackground 启动指定的脚本文件，不会阻塞当前程序
func StartScriptInBackground(scriptPath string) error {
	cmd := exec.Command("cmd.exe", "/C", scriptPath)
	// 启动脚本，不会阻塞
	err := cmd.Start()
	if err != nil {
		return fmt.Errorf("error starting the script: %w", err)
	}

	return nil
}

// createBatFile 创建一个包含指定内容的 .bat 文件，并返回它的路径
func CreateBatFile(content, path string) error {
	os.Remove(path)
	// 创建并写入文件
	err := ioutil.WriteFile(path, []byte(content), 0755) // 设置文件权限为可执行
	if err != nil {
		return fmt.Errorf("failed to write to bat file: %w", err)
	}

	return nil
}

// CompareVersion 比较两个版本号字符串
// 返回 0 表示相等，大于 0 表示 v1 > v2，小于 0 表示 v1 < v2
func CompareVersion(v1, v2 string) int {
	// 使用strings.Split将版本号字符串按点号分割为切片
	ver1 := strings.Split(v1, ".")
	ver2 := strings.Split(v2, ".")

	// 初始化两个索引，用于遍历两个版本号的切片
	i, j := 0, 0

	// 循环遍历，直到至少一个版本号遍历完
	for i < len(ver1) || j < len(ver2) {
		var num1, num2 int
		var err error

		// 处理v1
		if i < len(ver1) {
			num1, err = strconv.Atoi(ver1[i])
			if err != nil {
				// 如果转换失败，则视为较小的版本号
				return -1
			}
			i++
		} else {
			num1 = 0 // 如果v1遍历完，则视为0
		}

		// 处理v2
		if j < len(ver2) {
			num2, err = strconv.Atoi(ver2[j])
			if err != nil {
				// 如果转换失败，则视为较大的版本号
				return 1
			}
			j++
		} else {
			num2 = 0 // 如果v2遍历完，则视为0
		}

		// 比较当前数字大小
		if num1 != num2 {
			return num1 - num2
		}
	}

	// 如果所有数字都相等，则版本号相等
	return 0
}

// createFolderIfNotExists 创建一个文件夹，如果它不存在的话
func CreateFolderIfNotExists(folderPath string) error {
	// 使用Stat函数检查文件夹是否存在
	// 如果不存在，返回的错误类型将是*os.PathError
	info, err := os.Stat(folderPath)

	// 如果返回nil错误，说明文件夹存在
	if err == nil {
		// 检查info是否是一个目录
		if !info.IsDir() {
			// 如果不是目录，返回错误
			return fmt.Errorf("path exists but it's not a directory: %s", folderPath)
		}
		// 如果是目录，则无需做任何事情，返回nil
		return nil
	}

	// 如果错误类型是*os.PathError，并且错误描述是"no such file or directory"，
	// 那么我们就需要创建这个文件夹
	if os.IsNotExist(err) {
		// 使用MkdirAll创建文件夹及其所有必要的父文件夹
		// MkdirAll的权限参数设置为0755（所有者可以读写执行，组用户和其他用户可读可执行）
		err = os.MkdirAll(folderPath, 0755)
		if err != nil {
			// 如果创建失败，返回错误
			return err
		}
		// 如果创建成功，返回nil
		return nil
	}

	// 如果错误不是因为文件夹不存在，返回原始错误
	return err
}

// CreateScheduledTask 创建一个 Windows 定时任务
func CreateScheduledTask(taskName, scriptPath string, runTime time.Time) error {
	// 格式化运行时间为 HH:mm 格式
	runTimeStr := runTime.Format("15:04")

	// 创建命令
	cmd := exec.Command("schtasks", "/create", "/tn", taskName, "/tr", scriptPath, "/sc", "once", "/st", runTimeStr)

	// 执行命令并获取输出
	output, err := cmd.CombinedOutput()
	if err != nil {
		return fmt.Errorf("failed to create scheduled task: %s, output: %s", err, output)
	}
	return nil
}
func DeleteTask(taskName string) error {
	// 构建删除定时任务的命令
	cmd := exec.Command("schtasks", "/delete", "/tn", taskName, "/f")

	// 执行命令并获取输出
	output, err := cmd.CombinedOutput()
	if err != nil {
		// 如果执行出错，返回错误信息
		return fmt.Errorf("failed to delete task: %v, output: %s", err, output)
	}

	// 如果没有错误，输出成功信息
	fmt.Println("Task deleted successfully.")
	return nil
}
func IsProcessRunning(processName string) (bool, error) {
	// 使用tasklist命令查询所有进程
	cmd := exec.Command("tasklist")
	var out bytes.Buffer
	cmd.Stdout = &out
	err := cmd.Run()
	if err != nil {
		return false, err
	}

	// 检查输出中是否包含指定的进程名
	output := out.String()
	// logger.Log.Infof("tasklist output: %s", output)
	if strings.Contains(output, processName) {
		return true, nil
	}

	return false, nil
}

func Exists(path string) bool {
	_, err := os.Stat(path)
	if err != nil {
		if os.IsNotExist(err) {
			return false
		}
		return false
	}
	return true
}

// GetIPAddresses 获取本机的所有非回环 IP 地址
func GetIPAddresses() ([]string, error) {
	var ipAddresses []string

	// 获取本机所有网络接口信息
	interfaces, err := net.Interfaces()
	if err != nil {
		return nil, fmt.Errorf("get net interfaces error: %w", err)
	}

	// 遍历网络接口以获取 IP 地址
	for _, iface := range interfaces {
		addrs, err := iface.Addrs()
		if err != nil {
			return nil, fmt.Errorf("get interface addresses error: %w", err)
		}
		for _, addr := range addrs {
			// 检查地址类型是否为 IP 地址
			if ipnet, ok := addr.(*net.IPNet); ok && !ipnet.IP.IsLoopback() {
				ipAddresses = append(ipAddresses, ipnet.IP.String())
			}
		}
	}

	return ipAddresses, nil
}

// PostRequest 封装发送 POST 请求的函数
func PostRequest(url string, payload interface{}) (string, error) {
	// 将 payload 转换为 JSON 格式
	jsonData, err := json.Marshal(payload)
	if err != nil {
		return "", fmt.Errorf("JSON encode error: %w", err)
	}

	// 创建新的 POST 请求
	resp, err := http.Post(url, "application/json", bytes.NewBuffer(jsonData))
	if err != nil {
		return "", fmt.Errorf("send POST request error: %w", err)
	}
	defer resp.Body.Close()

	// 读取响应内容
	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return "", fmt.Errorf("read response body error: %w", err)
	}

	// 检查响应状态码
	if resp.StatusCode != http.StatusOK {
		return "", fmt.Errorf("response status code: %d, body: %s", resp.StatusCode, body)
	}

	return string(body), nil
}

// GetRequest 发送 GET 请求并解析返回的 body
func GetRequest(url string) (string, error) {
	// 发送 GET 请求
	resp, err := http.Get(url)
	if err != nil {
		return "", fmt.Errorf("failed to send GET request: %w", err)
	}
	defer resp.Body.Close() // 确保在函数结束时关闭响应体

	// 检查响应状态码
	if resp.StatusCode != http.StatusOK {
		return "", fmt.Errorf("unexpected status code: %d", resp.StatusCode)
	}

	// 读取响应体
	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return "", fmt.Errorf("failed to read response body: %w", err)
	}

	return string(body), nil
}
