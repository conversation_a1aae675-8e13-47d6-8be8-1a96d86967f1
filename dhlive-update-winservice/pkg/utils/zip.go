package utils

import (
	"archive/zip"
	"fmt"
	"io"
	"os"
	"path/filepath"
)

// Unzip 解压 ZIP 文件到指定路径
func Unzip(zipFilePath string, dest string) error {
	// 打开 ZIP 文件
	r, err := zip.OpenReader(zipFilePath)
	if err != nil {
		return fmt.Errorf("failed to open zip file: %w", err)
	}
	defer r.Close()

	// 创建解压的目标文件夹
	if err := os.MkdirAll(dest, os.ModePerm); err != nil {
		return fmt.Errorf("failed to create directory: %w", err)
	}

	// 解压每个文件
	for _, f := range r.File {
		// 构建每个文件的完整路径
		destFilePath := filepath.Join(dest, f.Name)

		// 创建目录
		if f.FileInfo().IsDir() {
			if err := os.MkdirAll(destFilePath, os.ModePerm); err != nil {
				return fmt.Errorf("failed to create directory: %w", err)
			}
			continue
		}

		// 创建文件
		if err := os.MkdirAll(filepath.Dir(destFilePath), os.ModePerm); err != nil {
			return fmt.Errorf("failed to create directory: %w", err)
		}
		outFile, err := os.Create(destFilePath)
		if err != nil {
			return fmt.Errorf("failed to create file: %w", err)
		}
		defer outFile.Close()

		// 打开 ZIP 中的文件
		rc, err := f.Open()
		if err != nil {
			return fmt.Errorf("failed to open zip file entry: %w", err)
		}
		defer rc.Close()

		// 将 ZIP 文件中的内容写入创建的文件
		if _, err := io.Copy(outFile, rc); err != nil {
			return fmt.Errorf("failed to extract file: %w", err)
		}
	}

	return nil
}
