@echo off
setlocal

set WORK_SPACE=%~dp0
set OUT_PUT=..\output\dhlive-update-winservice
set binName=dhlive-update-winservice.exe

:: 处理参数
:parse_args
if "%~1"=="" goto end_args
if "%~1"=="-t" (
    set moduleBType=%~2
    shift
)
shift
goto parse_args
:end_args

echo WORK_SPACE: %WORK_SPACE%
echo bin name: %binName%

:: 删除输出目录
if exist "%OUT_PUT%" rmdir /s /q "%OUT_PUT%"

set MODULE_OUT_PUT=%OUT_PUT%\%binName%
mkdir "%OUT_PUT%"
mkdir "%OUT_PUT%\logs"
mkdir "%OUT_PUT%\conf"

:: 复制发布目录
xcopy /e /i "%WORK_SPACE%conf" "%OUT_PUT%\conf"

:: 配置 Go 环境变量
go env -w GONOPROXY=**.baidu.com**
go env -w GONOSUMDB=*
go env -w GOPROXY=https://goproxy.baidu-int.com
go env -w GOPRIVATE=**.baidu.com

:: 编译为 Windows 应用程序
echo %binName% build start ...
if "%moduleBType%"=="server" (
    set CGO_ENABLED=0
    set GOOS=windows
    set GOARCH=amd64
    go build -o %MODULE_OUT_PUT%   -ldflags -H=windowsgui
) else (
    go build -o %MODULE_OUT_PUT%   -ldflags -H=windowsgui
)

if ERRORLEVEL 1 (
    echo %binName% build error
    exit /b 1
)

echo %binName% build end
dir /a "%OUT_PUT%"

if exist "%OUT_PUT%.zip" del /q "%OUT_PUT%.zip"
:: 压缩输出目录
powershell -Command "Compress-Archive -Path '%OUT_PUT%\*' -DestinationPath '%OUT_PUT%.zip'"

endlocal
