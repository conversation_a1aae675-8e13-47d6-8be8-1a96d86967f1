#!/bin/sh
set -x

# mv static resources for cloud demonstration
#cloud_resources=digital-human-cloud-demonstration/src/main/resources/static
#mkdir -p ${cloud_resources}
#cp -rf fe_source/* ${cloud_resources}
#mkdir -p ${cloud_resources}/error
#cp ${cloud_resources}/index.html ${cloud_resources}/error/404.html


# mv static resource for console service
## build
export PATH=$NODEJS_BIN_LATEST:$YARN_BIN_LATEST:$PATH

echo "node: $(node -v)"
echo "npm: $(npm -v)"
# 设置编译机群上的npm变量
npm config set registry http://registry.npm.baidu-int.com
npm config set puppeteer_skip_chromium_download true

cd fe_cloud_admin

echo $(pwd)

# 如果NODE_ENV为production, npm5以上版本就不会安装devDependencies.
NODE_ENV=development npm install
# 为生产环境构建加NODE_ENV=production.
NODE_ENV=production npm run dll
NODE_ENV=production npm run build
cd ..

## copy
console_resources=digital-human-console/src/main/resources/static
mkdir -p ${console_resources}
cp -rf fe_cloud_admin/dist/* ${console_resources}

mkdir -p ${console_resources}/error
cp ${console_resources}/index.html ${console_resources}/error/404.html

