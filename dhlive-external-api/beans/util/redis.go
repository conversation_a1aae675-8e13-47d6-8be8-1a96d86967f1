package util

import (
	"acg-ai-go-common/global"
	"context"
	"fmt"
	"log"
	"time"

	redis "github.com/redis/go-redis/v9"
)

func MonitorRedisConnection(rdb *redis.Client) {
	for {
		_, err := rdb.Ping(context.Background()).Result()
		if err != nil {
			log.Println("Redis connection lost, attempting to reconnect...")
			// 尝试重新连接 Redis
			if err := reconnect(rdb); err != nil {
				log.Printf("Failed to reconnect to Redis: %v", err)
				time.Sleep(5 * time.Second) // 当连接失败时，每隔 5 秒检查一次连接状态
				continue
			}
		}
		time.Sleep(10 * time.Second) // 每隔 10 秒检查一次连接状态
	}
}

func reconnect(rdb *redis.Client) error {
	// 尝试重新连接 Redis
	err := rdb.Close()
	if err != nil {
		log.Printf("Error closing previous Redis connection: %v", err)
		// 忽略关闭错误，继续尝试重连
	}
	// 重新创建 Redis 客户端
	rdb = redis.NewClient(&redis.Options{
		Addr:     global.ServerSetting.RedisSetting.Addr,     // 你的redis服务地址
		Username: global.ServerSetting.RedisSetting.Username, // 用户名
		Password: global.ServerSetting.RedisSetting.Password, // 如果没有设置密码则为空字符串
		DB:       0,                                          // 默认数据库设置为0
	})

	// 检查连接是否成功
	pong, err := rdb.Ping(context.Background()).Result()
	if err != nil {
		return fmt.Errorf("could not reconnect to Redis: %v", err)
	}
	log.Printf("Reconnected to Redis: %v", pong)
	return nil
}
