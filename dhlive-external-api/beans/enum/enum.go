package enum

// UeInstanceStatus UE实例状态
type UeInstanceStatus string

const (
	UeInstanceInit    UeInstanceStatus = "INIT"
	UeInstanceIdle    UeInstanceStatus = "IDLE"
	UeInstanceUsed    UeInstanceStatus = "USED"
	UeInstanceOffline UeInstanceStatus = "OFFLINE"
)

// UeRtcStatus UE实例状态
type UeRtcStatus string

const (
	UeRtcNone    UeRtcStatus = "NONE"
	UeRtcInit    UeRtcStatus = "INIT"
	UeRtcFailed  UeRtcStatus = "FAILED"
	UeRtcSucceed UeRtcStatus = "SUCCEED"
	UeRtcFinish  UeRtcStatus = "FINISH"
)

// WsMsgAction Websocket消息动作事件
type WsMsgAction string

const (
	ActionHeartBeat         WsMsgAction = "HEART_BEAT"         // 心跳
	ActionConnInfo          WsMsgAction = "CONN_INFO"          // 连接信息
	ActionFigureInfo        WsMsgAction = "FIGURE_INFO"        // 当前人像信息
	ActionBRtcInfo          WsMsgAction = "BRTC_INFO"          // BRtc信息
	ActionUeEvent           WsMsgAction = "UE_EVENT"           // UE指令
	ActionEventRsp          WsMsgAction = "EVENT_RSP"          // UE指令响应
	ActionText2Figure       WsMsgAction = "TEXT2FIGURE"        // 文生人像
	ActionText2FigureStop   WsMsgAction = "TEXT2FIGURE_STOP"   // 停止文生人像
	ActionText2FigureResult WsMsgAction = "TEXT2FIGURE_RESULT" // 文生人像结果
	ActionAuthInvalid       WsMsgAction = "AUTH_INVALID"       // 授权过期通知
	ActionErrorInfo         WsMsgAction = "ERROR_INFO"         // 错误信息
	ActionClose             WsMsgAction = "CLOSE"              // 关闭连接
)

type TaskStatus string // TaskStatus READY/RUNNING/STOP/SUCCEED/FAILED

const (
	TaskReady         TaskStatus = "READY"
	TaskRunning       TaskStatus = "RUNNING"
	TaskStop          TaskStatus = "STOP"
	TaskSucceed       TaskStatus = "SUCCEED"
	TaskFailed        TaskStatus = "FAILED"
	TaskParsing       TaskStatus = "PARSING"
	TaskCensor        TaskStatus = "CENSOR"
	TaskCensorSucceed TaskStatus = "CENSOR_SUCCEED"
	TaskCensorFailed  TaskStatus = "CENSOR_FAILED"
)

type FigureTrainStatus string

const (
	FigureTrainSubmit  = "submit"
	FigureTrainRunning = "running"
	FigureTrainSucceed = "success"
	FigureTrainFailed  = "failed"
)

type MaterialType string

const (
	MaterialImage MaterialType = "IMAGE"
	MaterialAudio MaterialType = "AUDIO"
	MaterialVideo MaterialType = "VIDEO"
)

type CampaignBenefitType string

const (
	BenefitTypeNone      CampaignBenefitType = "NONE"
	BenitTypeLogin       CampaignBenefitType = "LOGIN_BENEFIT"        // 登录权益
	BenitTypeVideoCreate CampaignBenefitType = "VIDEO_CREATE_BENEFIT" // 视频创作权益
	BenitTypeFirstBuy    CampaignBenefitType = "FIRST_BUY_BENEFIT"    // 首购权益
)

type CampaignBenefitStatus string // 活动权益下发状态

const (
	BenefitStatusPending CampaignBenefitStatus = "PENDING" // 待下发
	BenefitStatusSuccess CampaignBenefitStatus = "SUCCESS" // 成功
	BenefitStatusFailed  CampaignBenefitStatus = "FAILED"  // 失败
)
