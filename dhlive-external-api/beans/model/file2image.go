package model

import (
	"dhlive-external-api/beans/enum"
	"time"

	"gorm.io/gorm"
)

// FileInfo 对应数据库中的文件信息表
type File2Image struct {
	ID                   int64           `json:"id" gorm:"primarykey"`
	AccountId            string          `json:"accountId" gorm:"column:accountId;not null;type:varchar(50);index"` // 用户ID
	TaskId               string          `json:"taskId" gorm:"column:taskId;not null;type:varchar(50);index"`       // 任务ID
	FileUrl              string          `json:"fileUrl" gorm:"column:fileUrl;not null;type:varchar(1024)"`         // 文件bos服务对应的url
	Md5                  string          `json:"md5" gorm:"column:md5;not null;type:varchar(50);index"`             // 文件md5
	FileFormatSupported  bool            `json:"fileFormatSupported" gorm:"column:fileFormatSupported;not null"`    // 文件格式是否符合要求
	FileSizeWithinLimit  bool            `json:"fileSizeWithinLimit" gorm:"column:fileSizeWithinLimit;not null"`    // 文件大小是否符合要求
	PageCountWithinLimit bool            `json:"pageCountWithinLimit" gorm:"column:pageCountWithinLimit;not null"`  // 页数是否符合要求
	PageCount            int             `json:"pageCount" gorm:"column:pageCount;not null"`                        // 图片总个数
	ImageInfos           string          `json:"imageInfos" gorm:"column:imageInfos;type:MEDIUMTEXT;not null"`      // json串，存放多个image的url地址
	Status               enum.TaskStatus `json:"status" gorm:"column:status;not null;type:varchar(20);index"`       // 业务状态
	CensorTaskId         string          `json:"censorTaskId" gorm:"column:censorTaskId;type:varchar(50);index"`    // 审核任务ID
	Message              string          `json:"message" gorm:"column:message;type:MEDIUMTEXT"`                     // 错误信息
	CreatedAt            time.Time       `json:"createdAt" gorm:"column:createdAt;autoCreateTime"`                  // 创建时间
	UpdatedAt            time.Time       `json:"updatedAt" gorm:"column:updatedAt;autoUpdateTime"`                  // 更新时间
	DeletedAt            gorm.DeletedAt  `json:"-" gorm:"column:deletedAt;index"`                                   // 删除时间/标记删除
}

// TableName 指定表名
func (File2Image) TableName() string {
	return "exa_file_2_image" // 假设数据库表名为file_infos
}

// CreateFile2Image 创建
func (f *File2Image) CreateFile2Image(db *gorm.DB) error {
	result := db.Create(f)
	if result.Error != nil {
		return result.Error
	}
	return nil
}

// GetFile2Image 根据ID查询
func (f *File2Image) GetFile2Image(db *gorm.DB, id int64) (*File2Image, error) {
	var rf File2Image
	if err := db.First(&rf, id).Error; err != nil {
		return nil, err
	}
	return &rf, nil
}

// GetFile2ImageFromMd5 根据 md5 查询
func (f *File2Image) GetFile2ImageFromMd5(db *gorm.DB, md5 string) (*File2Image, error) {
	var rf File2Image
	if err := db.Where("md5 = ?", md5).First(&rf).Error; err != nil {
		return nil, err
	}
	return &rf, nil
}

// GetFile2ImageFromTaskId 根据 taskid 查询
func (f *File2Image) GetFile2ImageFromTaskId(db *gorm.DB, taskid string) (*File2Image, error) {
	var rf File2Image
	if err := db.Where("taskId = ?", taskid).First(&rf).Error; err != nil {
		return nil, err
	}
	return &rf, nil
}

// UpdateFile2Image 更新
func (f *File2Image) UpdateFile2Image(db *gorm.DB) error {
	result := db.Save(f)
	if result.Error != nil {
		return result.Error
	}
	return nil
}

// HardDeleteFile2Image 执行硬删除，即从数据库中永久移除记录
func (item *File2Image) HardDeleteFile2Image(db *gorm.DB, id int64) error {
	result := db.Unscoped().Delete(&File2Image{}, id)
	if result.Error != nil {
		return result.Error
	}
	return nil
}

func (item *File2Image) GetTasksWithStatus(db *gorm.DB, status enum.TaskStatus) ([]*File2Image, error) {
	var tasks []*File2Image
	err := db.Where("status = ?", string(status)).
		Order("createdAt ASC").Find(&tasks).Error
	if err != nil {
		return nil, err
	}
	return tasks, nil
}
