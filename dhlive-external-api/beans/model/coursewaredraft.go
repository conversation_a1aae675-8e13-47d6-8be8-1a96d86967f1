package model

import (
	"acg-ai-go-common/logger"
	"dhlive-external-api/beans/proto"
	config "dhlive-external-api/conf"
	"errors"
	"time"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

type CoursewareDraftEntity struct {
	ID                int64     `json:"id" gorm:"primaryKey;autoIncrement;column:id"`
	DraftID           string    `json:"draftID" gorm:"column:draft_id;type:varchar(64);index"`
	Name              string    `json:"name" gorm:"column:name;type:varchar(100)"`
	TemplateID        string    `json:"templateID" gorm:"column:template_id;type:varchar(100)"`
	UserID            string    `json:"userID" gorm:"column:user_id;type:varchar(100);index"`
	LastUpdateBy      string    `json:"lastUpdateBy" gorm:"column:last_update_by;type:varchar(100)"`
	Duration          int64     `json:"duration" gorm:"column:duration"`
	IsDelete          int       `json:"isDelete" gorm:"column:is_delete"`
	AspectWidth       int       `json:"aspectWidth" gorm:"column:aspect_width"`
	AspectHeight      int       `json:"aspectHeight" gorm:"column:aspect_height"`
	Tracks            string    `json:"tracks" gorm:"column:tracks;type:mediumtext"`
	ThumbnailUrl      string    `json:"thumbnailUrl" gorm:"column:thumbnail_url;type:varchar(512)"`
	TranslateStatus   string    `json:"translateStatus" gorm:"column:translate_status;type:varchar(100);index"` // 翻译状态
	TranslateCount    int       `json:"translateCount" gorm:"column:translate_count;type:varchar(10)"`          // 翻译次数
	VideoID           string    `json:"videoID" gorm:"column:video_id;type:varchar(100)"`
	TranslateLan      string    `json:"translateLan" gorm:"column:translate_lan;type:varchar(50)"` // 翻译的目标语种
	TransFailedReason string    `json:"transFailedReason" gorm:"column:translate_failed_reason;type:mediumtext"`
	TransTimbre       string    `json:"transTimbre" gorm:"column:translate_timbre;type:mediumtext"` // 视频翻译的目标音色信息
	CreateTime        time.Time `json:"createTime" gorm:"column:create_time"`
	UpdateTime        time.Time `json:"updateTime" gorm:"column:update_time"`
}

func (CoursewareDraftEntity) TableName() string {
	return "courseware_draft"
}
func (d *CoursewareDraftEntity) Create(db *gorm.DB) error {
	result := db.Create(d)
	if result.Error != nil {
		return result.Error
	}
	return nil
}

func (d *CoursewareDraftEntity) GetItemByDraftID(db *gorm.DB, draftID string) (CoursewareDraftEntity, error) {
	var draftItem CoursewareDraftEntity
	if err := db.Where("draft_id = ?", draftID).First(&draftItem).Error; err != nil {
		return draftItem, err
	}
	return draftItem, nil
}

// 获取超时的任务对状态进行处理
func (d *CoursewareDraftEntity) GetRunningAndOutTimeItemsForUpdate(db *gorm.DB) ([]CoursewareDraftEntity, error) {
	var draftItems []CoursewareDraftEntity
	threshold := time.Now().Add(-time.Duration(config.LocalConfig.VideoTranslate.TransOutTime) * time.Second)
	logger.Log.Infof("CoursewareDraftEntity GetRunningAndOutTimeItemsForUpdate thresholdTime:%+v", threshold)

	err := db.Transaction(func(tx *gorm.DB) error {
		// 使用 SELECT ... FOR UPDATE 加锁查询
		if err := tx.Clauses(clause.Locking{Strength: "UPDATE"}).Where("translate_status = ? AND create_time < ?", proto.TransStatusRunning, threshold).Find(&draftItems).Error; err != nil {
			return err
		}
		logger.Log.Infof("CoursewareDraftEntity GetRunningAndOutTimeItemsForUpdate draftItems.size:%d", len(draftItems))
		for _, item := range draftItems {
			transStatus := proto.TransStatusWaiting
			if item.TranslateCount >= config.LocalConfig.VideoTranslate.MaxRetryCount {
				transStatus = proto.TransStatusFailed
			}
			logger.Log.Infof("CoursewareDraftEntity GetRunningAndOutTimeItemsForUpdate draftId:%s,transStatus:%s", item.DraftID, transStatus)
			if err := tx.Model(&item).Update("translate_status", transStatus).Error; err != nil {
				return err
			}
		}
		return nil
	})
	return draftItems, err
}

func (d *CoursewareDraftEntity) UpdateFieldsByDraftID(db *gorm.DB, draftId string, fields map[string]interface{}) error {
	result := db.Model(&CoursewareDraftEntity{}).Where("draft_id = ?", draftId).Updates(fields)
	if result.Error != nil {
		return result.Error
	}
	// 未查询到对应的数据
	if result.RowsAffected == 0 {
		logger.Log.Errorf("CoursewareDraftEntity UpdateFieldsByDraftID draftId:%s,result.RowsAffected == 0", draftId)
		return errors.New("no matching record found")
	}
	return nil
}

func (d *CoursewareDraftEntity) UpdateItemsByDraftIDs(db *gorm.DB, draftIds []string) error {
	result := db.Model(&CoursewareDraftEntity{}).Where("draft_id IN ?", draftIds).Updates(d)
	if result.Error != nil {
		return result.Error
	}
	return nil
}

func (d *CoursewareDraftEntity) GetTranslateRunningCount(db *gorm.DB) (int64, error) {
	var count int64
	// 执行带条件的Count查询
	err := db.Model(&CoursewareDraftEntity{}).
		Where("translate_status = ?", proto.TransStatusRunning).
		Count(&count).Error

	if err != nil {
		return 0, err // 返回错误信息
	}
	return count, nil
}

// 获取一定数量的数据
func (d *CoursewareDraftEntity) GetLimitedCountItemByStatus(db *gorm.DB, transStatus string, limitCount int) ([]CoursewareDraftEntity, error) {
	logger.Log.Infof("GetLimitedCountItemByStatus transStatus:%s, limitCount:%d", transStatus, limitCount)
	var entitys []CoursewareDraftEntity
	err := db.Model(&CoursewareDraftEntity{}).Where("translate_status = ?", transStatus).Order("create_time ASC").Limit(limitCount).Find(&entitys).Error
	if err != nil {
		logger.Log.Errorf("GetLimitedCountItemByStatus error:%+v", err)
		return entitys, err
	}
	return entitys, nil
}
