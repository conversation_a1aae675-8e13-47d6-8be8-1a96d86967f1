package model

import (
	"acg-ai-go-common/gomysql"
	"acg-ai-go-common/logger"
	"acg-ai-go-common/utils"
	"context"
	"fmt"
	"gorm.io/gorm"
	"strconv"
	"strings"
	"time"
)

type Questionnaire struct {
	ID                int64          `json:"id" gorm:"column:id;primaryKey;autoIncrement"` // 主键ID，自增
	UserId            string         `json:"userId" gorm:"column:userId;type:varchar(100);comment:用户ID;index"`
	QuestionnaireList string         `json:"questionnaireList" gorm:"column:questionnaireList;comment:问卷数据"`
	Operator          string         `json:"operator" gorm:"column:operator;type:varchar(100);comment:操作人;"`
	Status            string         `json:"status" gorm:"column:status;type:varchar(100);comment:处理状态，UNTREATED:未处理 PROCESSING:处理中 RESOLVED:已解决 CLOSED:已关闭;index"`
	CreatedAt         time.Time      `json:"createdAt" gorm:"column:createdAt"`
	UpdatedAt         time.Time      `json:"updatedAt" gorm:"column:updatedAt"`
	DeletedAt         gorm.DeletedAt `json:"-" gorm:"column:deletedAt"`
}

func (item *Questionnaire) TableName() string { return "exa_questionnaire" }

func (item *Questionnaire) Insert(db *gorm.DB, logCtx context.Context) (int, error) {
	logger.Log.Infof(utils.MMark(logCtx) + "Questionnaire Insert")
	result := db.Create(item)
	if result.Error != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"Questionnaire Insert error=%v", result.Error)
		return 0, result.Error
	}
	logger.Log.Infof(utils.MMark(logCtx)+"Questionnaire Insert scuuess", utils.MMark(logCtx))
	return int(result.RowsAffected), nil
}
func (item *Questionnaire) SearchList(db *gorm.DB, logCtx context.Context, pageNo, pageSize int64, createAt string) (int64, []*Questionnaire, error) {
	conn := db.Model(item)
	if len(item.UserId) > 0 {
		conn.Where("userId = ?", item.UserId)
	}
	if len(item.Status) > 0 {
		conn.Where("status = ?", item.Status)
	}
	if len(createAt) > 0 {
		timestamps := strings.Split(createAt, ",")
		if len(timestamps) == 2 {
			start, err1 := strconv.ParseInt(timestamps[0], 10, 64)
			end, err2 := strconv.ParseInt(timestamps[1], 10, 64)
			if err1 != nil || err2 != nil {
				return 0, nil, fmt.Errorf("无效的时间戳范围")
			}
			conn.Where("createdAt > ? AND createdAt < ?", time.Unix(start, 0), time.Unix(end, 0))
		} else {
			return 0, nil, fmt.Errorf("时间戳格式无效")
		}
	}
	conn = conn.Order("createdAt DESC")
	var total int64
	if err := conn.Count(&total).Error; err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"Questionnaire count error=%v", err)
		return 0, nil, err
	}
	skip, limit := utils.ConvertPageNumSize2Skip(pageNo, pageSize)
	var consults []*Questionnaire
	if err := conn.Offset(int(skip)).Limit(int(limit)).Find(&consults).Error; err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"Questionnaire Insert error=%v", err)
		return 0, nil, err
	}
	logger.Log.Infof(utils.MMark(logCtx)+"Questionnaire SearchList scuuess", utils.MMark(logCtx))
	return total, consults, nil
}
func (item *Questionnaire) Update(db *gorm.DB, logCtx context.Context) (int64, error) {
	result := db.Model(&Questionnaire{}).Where("id = ?", item.ID).Updates(Questionnaire{
		Status:   item.Status,
		Operator: item.Operator,
	})
	if result.Error != nil {
		return 0, result.Error
	}
	logger.Log.Infof(utils.MMark(logCtx) + "Questionnaire Update scuuess")
	return result.RowsAffected, nil
}

func (item *Questionnaire) UpdateByOperator(db *gorm.DB, logCtx context.Context) error {
	result := db.Model(&Questionnaire{}).Where("id = ?", item.ID).Updates(&Questionnaire{
		Operator: item.Operator,
	})
	if result.Error != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"UpdateByOperator error=%v", result.Error)
		return result.Error
	}
	logger.Log.Infof(utils.MMark(logCtx) + "UpdateByOperator  scuuess")
	return nil
}
func (item *Questionnaire) DeleteByID() error {
	return gomysql.DB.
		Where("id = ?", item.ID).
		Delete(&item).Error
}
