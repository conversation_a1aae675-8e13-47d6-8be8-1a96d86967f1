package model

import (
	"gorm.io/gorm"
	"time"
)

type TranslateTextFeedback struct {
	ID        int64          `json:"id" gorm:"column:id;primaryKey;autoIncrement"`                // 主键ID，自增
	AccountID string         `json:"accountId" gorm:"column:accountId;not null;type:varchar(50)"` // 用户ID
	SrcText   string         `json:"srcText" gorm:"column:srcText"`                               // 源文本内容
	DstText   string         `json:"dstText" gorm:"column:dstText"`                               // 目标文本内容
	FromLan   string         `json:"fromLan" gorm:"column:fromLan"`                               // 源语种
	ToLan     string         `json:"toLan" gorm:"column:toLan"`                                  // 目标语种
	Md5       string         `json:"md5" gorm:"column:md5;index;type:varchar(50)"`                // 翻译数据的MD5值
	CreatedAt time.Time      `json:"createdAt" gorm:"column:createdAt"`                           // 创建时间
	UpdatedAt time.Time      `json:"updatedAt" gorm:"column:updatedAt"`                           // 更新时间
	DeletedAt gorm.DeletedAt `json:"-" gorm:"column:deletedAt;index"`                             // 删除时间/标记删除
}

// TableName 获取表名
func (item *TranslateTextFeedback) TableName() string {
	return "exa_translate_text_feedback"
}

func (item *TranslateTextFeedback) CreateItem(db *gorm.DB) error {
	result := db.Create(item)
	if result.Error != nil {
		return result.Error
	}
	return nil
}

func (item *TranslateTextFeedback) GetItemByMd5(db *gorm.DB, md5 string) (*TranslateTextFeedback, error) {
	var rt TranslateTextFeedback
	if err := db.Where("md5 = ?", md5).First(&rt).Error; err != nil {
		return nil, err
	}
	return &rt, nil
}

func (item *TranslateTextFeedback) UpdateItem(db *gorm.DB) error {
	result := db.Save(item)
	if result.Error != nil {
		return result.Error
	}
	return nil
}

// 执行硬删除，即从数据库中永久移除记录
func (item *TranslateTextFeedback) HardDeleteItem(db *gorm.DB, id int64) error {
	result := db.Unscoped().Delete(&TranslateTextFeedback{}, id)
	if result.Error != nil {
		return result.Error
	}
	return nil
}
