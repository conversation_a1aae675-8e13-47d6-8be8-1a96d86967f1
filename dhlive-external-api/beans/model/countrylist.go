package model

import (
	"acg-ai-go-common/gomysql"
	"acg-ai-go-common/logger"
	"acg-ai-go-common/utils"
	"context"
	"gorm.io/gorm"
	"strings"
	"time"
)

type Country struct {
	ID                     int64          `json:"id" gorm:"column:id;primaryKey;autoIncrement"` // 主键ID，自增
	Name                   string         `json:"name" gorm:"column:name;type:varchar(100);comment:国家名称;index"`
	Alpha2                 string         `json:"alpha2" gorm:"column:alpha2;type:varchar(32);comment:国家字母代码，2位;"`
	Alpha3                 string         `json:"alpha3" gorm:"column:alpha3;type:varchar(32);comment:国家字母代码，3位;index"`
	CountryCode            string         `json:"countryCode" gorm:"column:countryCode;type:varchar(100);comment:国家代码;index"`
	IsoStandard            string         `json:"isoStandard" gorm:"column:isoStandard;type:varchar(100);comment:地理分区;"`
	Region                 string         `json:"region" gorm:"column:region;type:varchar(100);comment:区域;"`
	SubRegion              string         `json:"SubRegion" gorm:"column:SubRegion;type:varchar(100);comment:次区域;"`
	IntermediateRegion     string         `json:"intermediateRegion" gorm:"column:intermediateRegion;type:varchar(100);comment:中间区域;"`
	RegionCode             string         `json:"regionCode" gorm:"column:regionCode;type:varchar(100);comment:区域代码;"`
	SubRegionCode          string         `json:"subRegionCode" gorm:"column:subRegionCode;type:varchar(100);comment:子区域代码;"`
	IntermediateRegionCode string         `json:"intermediateRegionCode" gorm:"column:intermediateRegionCode;type:varchar(100);comment:中间区域代码;"`
	Sort                   int            `json:"sort" gorm:"column:sort"`
	Flag                   string         `json:"flag" gorm:"column:flag;type:varchar(100);comment:国旗图标，预留;"`
	Operator               string         `json:"operator" gorm:"column:operator;type:varchar(100);comment:操作人;"`
	CreatedAt              time.Time      `json:"createdAt" gorm:"column:createdAt"`
	UpdatedAt              time.Time      `json:"updatedAt" gorm:"column:updatedAt"`
	DeletedAt              gorm.DeletedAt `json:"-" gorm:"column:deletedAt"`
}

func (item *Country) TableName() string { return "exa_country_list" }

func (item *Country) BatchInsert(db *gorm.DB, logCtx context.Context, countries []*Country) (int, error) {
	if len(countries) == 0 {
		logger.Log.Errorf(utils.MMark(logCtx)+"BatchInsert  countries=%v", len(countries))
		return 0, nil
	}
	err := db.Create(countries).Error
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"BatchInsert create  err=%v", err)
		return 0, err
	}
	logger.Log.Infof(utils.MMark(logCtx)+"BatchInsert success: size=%d", len(countries))
	return len(countries), nil
}
func (item *Country) Insert(db *gorm.DB, logCtx context.Context) (int64, error) {
	logger.Log.Infof(utils.MMark(logCtx) + "Insert Country")
	result := db.Create(item)
	if result.Error != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"Insert error=%v", result.Error)
		return 0, result.Error
	}
	logger.Log.Infof(utils.MMark(logCtx) + "Insert scuuess")
	return result.RowsAffected, nil
}
func (item *Country) Search(db *gorm.DB, logCtx context.Context) ([]Country, error) {
	logger.Log.Infof(utils.MMark(logCtx) + "Search")
	qurey := db.Model(item)
	if item.Name != "" {
		qurey = qurey.Where("LOWER(name) LIKE ?", strings.ToLower(item.Name)+"%")
	}
	if item.CountryCode != "" {
		qurey = qurey.Where("countryCode = ?", item.CountryCode)
	}
	if item.Alpha3 != "" {
		qurey = qurey.Where("alpha3 LIKE ?", "%"+item.Alpha3+"%")
	}
	var result []Country
	if err := qurey.Debug().Find(&result).Error; err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"Search error=%v", err)
		return nil, err
	}
	logger.Log.Infof(utils.MMark(logCtx) + "Search scuuess")
	return result, nil
}
func (item *Country) SearchList(db *gorm.DB, logCtx context.Context, pageNo, pageSize int64) (int64, []*Country, error) {
	logger.Log.Infof(utils.MMark(logCtx) + "SearchList")
	qurey := db.Model(item)
	if item.Name != "" {
		qurey = qurey.Where("LOWER(name) LIKE ?", strings.ToLower(item.Name)+"%")
	}
	if item.CountryCode != "" {
		qurey = qurey.Where("countryCode = ?", item.CountryCode)
	}
	if item.Alpha3 != "" {
		qurey = qurey.Where("alpha3 LIKE ?", "%"+item.Alpha3+"%")
	}
	qurey = qurey.Order("sort DESC")
	var total int64
	if err := qurey.Count(&total).Error; err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"SearchList count error=%v", err)
		return 0, nil, err
	}
	skip, limit := utils.ConvertPageNumSize2Skip(pageNo, pageSize)
	var consults []*Country
	if err := qurey.Offset(int(skip)).Limit(int(limit)).Find(&consults).Error; err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"SearchList find error=%v", err)
		return 0, nil, err
	}
	logger.Log.Infof(utils.MMark(logCtx) + "SearchList scuuess")
	return total, consults, nil
}
func (item *Country) Update(db *gorm.DB, logCtx context.Context) (int64, error) {
	result := db.Model(&Country{}).Where("id = ?", item.ID).Updates(item)
	if result.Error != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"SearchList find error=%v", result.Error)
		return 0, result.Error
	}
	logger.Log.Infof(utils.MMark(logCtx) + "Update scuuess")
	return result.RowsAffected, nil
}

func (item *Country) UpdateByOperator(db *gorm.DB, logCtx context.Context) error {
	result := db.Model(&Country{}).Where("id = ?", item.ID).Updates(&Country{
		Operator: item.Operator,
	})
	if result.Error != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"UpdateByOperator error=%v", result.Error)
		return result.Error
	}
	logger.Log.Infof(utils.MMark(logCtx) + "UpdateByOperator scuuess")
	return nil
}
func (item *Country) DeleteByID() error {
	return gomysql.DB.
		Where("id = ?", item.ID).
		Delete(&item).Error
}
