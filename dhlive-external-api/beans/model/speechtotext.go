package model

import (
	"time"

	"gorm.io/gorm"
)

type SpeechToTextStatus string

const (
	SpeechToTextStatusSuccess SpeechToTextStatus = "SUCCESS"
	SpeechToTextStatusFailed  SpeechToTextStatus = "FAILED"
)

type SpeechToText struct {
	ID        int64              `json:"id" gorm:"primaryKey;autoIncrement;"`
	UrlMd5    string             `json:"urlMd5" gorm:"column:urlMd5;type:varchar(255);not null;index"`
	FileMd5   string             `json:"fileMd5" gorm:"column:fileMd5;type:varchar(255);not null;index"`
	AudioURL  string             `json:"audioURL" gorm:"column:audioURL;not null;type:varchar(1024)"`    // 音频URL
	Content   string             `json:"content" gorm:"column:content;type:mediumtext;not null"`         // 音频转文本内容
	Status    SpeechToTextStatus `json:"status" gorm:"column:status;type:varchar(255);not null;index"`   // 状态 SUCCESS/FAILED
	ErrorMsg  string             `json:"errorMsg" gorm:"column:errorMsg;default:null;type:varchar(255)"` // 错误信息
	CreatedAt time.Time          `json:"createdAt" gorm:"column:createdAt;autoCreateTime"`               // 创建时间
	UpdatedAt time.Time          `json:"updatedAt" gorm:"column:updatedAt;autoUpdateTime"`               // 更新时间
	DeletedAt gorm.DeletedAt     `json:"-" gorm:"column:deletedAt;index"`                                // 删除时间/标记删除
}

func (s *SpeechToText) TableName() string {
	return "exa_speach_to_text"
}

// CreateAnimationTemplate 创建动画模板
func (s *SpeechToText) CreateSpeechToText(db *gorm.DB) error {
	result := db.Create(s)
	if result.Error != nil {
		return result.Error
	}
	return nil
}

// UpdateAnimationTemplate 更新动画模板
func (s *SpeechToText) UpdateSpeechToText(db *gorm.DB) error {
	result := db.Save(s)
	if result.Error != nil {
		return result.Error
	}
	return nil
}

func (s *SpeechToText) ListSpeechToText(db *gorm.DB, page int, pagesize int) ([]*SpeechToText, error) {
	var tasks []*SpeechToText
	// 计算偏移量
	offset := (page - 1) * pagesize
	err := db.Order("id ASC").Limit(pagesize).Offset(offset).Find(&tasks).Error
	if err != nil {
		return nil, err
	}
	return tasks, nil
}

func (s *SpeechToText) GetSpeechToText(db *gorm.DB, urlMd5 string, status SpeechToTextStatus) (SpeechToText, error) {
	var tasks SpeechToText
	err := db.Where("urlMd5 = ?", urlMd5).Where("status = ?", status).First(&tasks).Error
	if err != nil {
		return tasks, err
	}
	return tasks, nil
}

func (s *SpeechToText) GetSpeechToTextByFileMd5(db *gorm.DB, fileMd5 string, status SpeechToTextStatus) (SpeechToText, error) {
	var tasks SpeechToText
	err := db.Where("fileMd5 = ?", fileMd5).Where("status = ?", status).First(&tasks).Error
	if err != nil {
		return tasks, err
	}
	return tasks, nil
}
