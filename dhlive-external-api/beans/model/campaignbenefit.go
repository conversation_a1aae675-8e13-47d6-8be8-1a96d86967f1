package model

import (
	"dhlive-external-api/beans/enum"
	"time"

	"gorm.io/gorm"
)

type CampaignBenefit struct {
	ID             uint                       `json:"id"              gorm:"column:id;primaryKey;autoIncrement"`                                                                  // 数据库ID
	CampaignType   string                     `json:"campaignType"    gorm:"column:campaignType;not null;type:varchar(50);index:idx_campaign_benefit_invitee_user,unique;index"`  // 活动类型，普通索引
	BenefitType    enum.CampaignBenefitType   `json:"benefitType"     gorm:"column:benefitType;not null;type:varchar(50);index:idx_campaign_benefit_invitee_user,unique;index"`   // 权益类型，普通索引
	InviteeUserID  string                     `json:"inviteeUserId"   gorm:"column:inviteeUserId;not null;type:varchar(50);index:idx_campaign_benefit_invitee_user,unique;index"` // 被邀请者用户ID，活动类型+被邀请者用户ID+权益类型组合唯一索引
	InviteUserID   string                     `json:"inviteUserId"    gorm:"column:inviteUserId;not null;type:varchar(50);index:idx_invite_user"`                                 // 邀请者用户ID，普通索引
	InviteCode     string                     `json:"inviteCode"      gorm:"column:inviteCode;not null;type:varchar(50);index:idx_invite_code"`                                   // 邀邀请码，普通索引
	BenefitContent int                        `json:"benefitContent"  gorm:"column:benefitContent;not null;type:int"`                                                             // 下发权益数值 单位秒
	GrantStatus    enum.CampaignBenefitStatus `json:"grantStatus"     gorm:"column:grantStatus;not null;type:varchar(20);index:idx_grant_status"`                                 // 发放状态，普通索引
	FailureReason  string                     `json:"failureReason"   gorm:"column:failureReason;type:text"`                                                                      // 失败原因
	UpdatedAt      time.Time                  `json:"updatedAt"       gorm:"column:updatedAt;autoUpdateTime"`                                                                     // 更新时间
	CreatedAt      time.Time                  `json:"createdAt"       gorm:"column:createdAt;autoCreateTime"`                                                                     // 创建时间
	DeletedAt      gorm.DeletedAt             `json:"deletedAt"       gorm:"column:deletedAt;index"`                                                                              // 软删除时间
}

// TableName 表名
func (CampaignBenefit) TableName() string {
	return "exa_campaign_benefit_grants"
}

// 创建权益发放记录
func (c *CampaignBenefit) CreateCampaignBenefit(db *gorm.DB) error {
	return db.Create(c).Error
}

// 更新权益发放记录
func (c *CampaignBenefit) UpdateCampaignBenefit(db *gorm.DB) error {
	return db.Model(CampaignBenefit{}).Where("id = ?", c.ID).Updates(c).Error
}

// 查询权益发放记录
func (c *CampaignBenefit) GetCampaignBenefitByInvitee(db *gorm.DB, campaignType string, benefitType enum.CampaignBenefitType, inviteeUserID string) (*CampaignBenefit, error) {
	var camp CampaignBenefit
	err := db.Where("campaignType = ? AND benefitType = ? AND inviteeUserId = ?", campaignType, benefitType, inviteeUserID).First(&camp).Error
	return &camp, err
}
