package model

import (
	"acg-ai-go-common/gomysql"
	"acg-ai-go-common/logger"
	"acg-ai-go-common/utils"
	"context"
	"fmt"
	"gorm.io/gorm"
	"strconv"
	"strings"
	"time"
)

type WorkPreFeedback struct {
	ID           int64          `json:"id" gorm:"column:id;primaryKey;autoIncrement"` // 主键ID，自增
	Type         string         `json:"type" gorm:"column:type;type:varchar(100);comment:所属模块;index"`
	UserId       string         `json:"user_id" gorm:"column:user_id;type:varchar(100);comment:用户ID;index"`
	FeedbackList string         `json:"feedback_list" gorm:"column:feedback_list;comment:反馈信息"`
	Operator     string         `json:"operator" gorm:"column:operator;type:varchar(100);comment:操作人;"`
	Status       string         `json:"status" gorm:"column:status;type:varchar(100);comment:处理状态，UNTREATED:未处理 PROCESSING:处理中 RESOLVED:已解决 CLOSED:已关闭;index"`
	CreatedAt    time.Time      `json:"createdAt" gorm:"column:createdAt"`
	UpdatedAt    time.Time      `json:"updatedAt" gorm:"column:updatedAt"`
	DeletedAt    gorm.DeletedAt `json:"-" gorm:"column:deletedAt"`
}

func (item *WorkPreFeedback) TableName() string { return "exa_work_pre_feedback" }

func (item *WorkPreFeedback) Insert(db *gorm.DB, logCtx context.Context) (int, error) {
	logger.Log.Infof(utils.MMark(logCtx) + "WorkPreFeedback Insert")
	result := db.Create(item)
	if result.Error != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"WorkPreFeedback Insert error=%v", result.Error)
		return 0, result.Error
	}
	logger.Log.Infof(utils.MMark(logCtx)+"WorkPreFeedback Insert scuuess", utils.MMark(logCtx))
	return int(result.RowsAffected), nil
}
func (item *WorkPreFeedback) SearchList(db *gorm.DB, logCtx context.Context, pageNo, pageSize int64, createAt string) (int64, []*WorkPreFeedback, error) {
	conn := db.Model(item)
	if len(item.UserId) > 0 {
		conn.Where("userId = ?", item.UserId)
	}
	if len(item.Status) > 0 {
		conn.Where("status = ?", item.Status)
	}
	if len(item.Type) > 0 {
		conn.Where("type = ?", item.Type)
	}
	if len(createAt) > 0 {
		timestamps := strings.Split(createAt, ",")
		if len(timestamps) == 2 {
			start, err1 := strconv.ParseInt(timestamps[0], 10, 64)
			end, err2 := strconv.ParseInt(timestamps[1], 10, 64)
			if err1 != nil || err2 != nil {
				return 0, nil, fmt.Errorf("无效的时间戳范围")
			}
			conn.Where("createdAt > ? AND createdAt < ?", time.Unix(start, 0), time.Unix(end, 0))
		} else {
			return 0, nil, fmt.Errorf("时间戳格式无效")
		}
	}
	conn = conn.Order("createdAt DESC")
	var total int64
	if err := conn.Count(&total).Error; err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"WorkPreFeedback count error=%v", err)
		return 0, nil, err
	}
	skip, limit := utils.ConvertPageNumSize2Skip(pageNo, pageSize)
	var consults []*WorkPreFeedback
	if err := conn.Offset(int(skip)).Limit(int(limit)).Find(&consults).Error; err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"WorkPreFeedback Insert error=%v", err)
		return 0, nil, err
	}
	logger.Log.Infof(utils.MMark(logCtx)+"WorkPreFeedback SearchList scuuess", utils.MMark(logCtx))
	return total, consults, nil
}
func (item *WorkPreFeedback) Update(db *gorm.DB, logCtx context.Context) (int64, error) {
	result := db.Model(&WorkPreFeedback{}).Where("id = ?", item.ID).Updates(WorkPreFeedback{
		Status:   item.Status,
		Operator: item.Operator,
	})
	if result.Error != nil {
		return 0, result.Error
	}
	logger.Log.Infof(utils.MMark(logCtx) + "WorkPreFeedback Update scuuess")
	return result.RowsAffected, nil
}
func (item *WorkPreFeedback) BatchUpdateWorkPreFeedbackByIDs(db *gorm.DB, logCtx context.Context, ids []int64, status string, operator string) (int64, error) {
	// 使用单个UPDATE语句批量更新
	result := db.Model(&WorkPreFeedback{}).
		Where("id IN (?)", ids).
		Updates(map[string]interface{}{
			"status":   status,
			"operator": operator,
		})

	if result.Error != nil {
		return 0, result.Error
	}
	logger.Log.Infof(utils.MMark(logCtx) + "WorkPreFeedback BatchUpdateWorkPreFeedbackByIDs scuuess")
	return result.RowsAffected, nil
}

func (item *WorkPreFeedback) UpdateByOperator(db *gorm.DB, logCtx context.Context) error {
	result := db.Model(&WorkPreFeedback{}).Where("id = ?", item.ID).Updates(&WorkPreFeedback{
		Operator: item.Operator,
	})
	if result.Error != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"WorkPreFeedback UpdateByOperator error=%v", result.Error)
		return result.Error
	}
	logger.Log.Infof(utils.MMark(logCtx) + "WorkPreFeedback UpdateByOperator  scuuess")
	return nil
}
func (item *WorkPreFeedback) BatchUpdateByOperator(db *gorm.DB, logCtx context.Context, ids []int64, operator string) (int64, error) {
	if len(ids) == 0 {
		return 0, nil
	}

	result := db.Model(&WorkPreFeedback{}).
		Where("id IN (?)", ids).
		Updates(map[string]interface{}{
			"operator": operator,
		})

	if result.Error != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"BatchUpdateByOperator error=%v", result.Error)
		return 0, result.Error
	}

	logger.Log.Infof(utils.MMark(logCtx)+"BatchUpdateByOperator success, affected rows=%d", result.RowsAffected)
	return result.RowsAffected, nil
}
func (item *WorkPreFeedback) DeleteByID() error {
	return gomysql.DB.
		Where("id = ?", item.ID).
		Delete(&item).Error
}
func (item *WorkPreFeedback) BatchDeleteByIDs(db *gorm.DB, ids []int64) (int64, error) {
	if len(ids) == 0 {
		return 0, nil
	}

	result := db.Where("id IN (?)", ids).Delete(&WorkPreFeedback{})
	if result.Error != nil {
		return 0, result.Error
	}
	return result.RowsAffected, nil
}
