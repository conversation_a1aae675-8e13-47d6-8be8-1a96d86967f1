package model

import (
	"gorm.io/gorm"
	"time"
)

const (
	FbTypeLike    = "LIKE"
	FbTypeDISLIKE = "DISLIKE"
)

type TranslateTextItem struct {
	ID        int64          `json:"id" gorm:"column:id;primaryKey;autoIncrement"`   // 主键ID，自增
	TransId   string         `json:"transId" gorm:"column:transId;type:varchar(50)"`                  // 该词翻译的唯一ID
	SrcText   string         `json:"srcText" gorm:"column:srcText"`                  // 源文本内容
	DstText   string         `json:"dstText" gorm:"column:dstText"`                  // 目标文本内容
	FromLan   string         `json:"fromLan" gorm:"column:fromLan;type:varchar(50)"` // 源语种
	ToLan     string         `json:"toLan" gorm:"column:toLan;type:varchar(50)"`     // 目标语种
	Md5       string         `json:"md5" gorm:"column:md5;index;type:varchar(50)"`   // 翻译数据的MD5值
	UseCount  int64          `json:"useCount" gorm:"column:useCount;type:int(10)"`   // 使用次数 计数
	CreatedAt time.Time      `json:"createdAt" gorm:"column:createdAt"`              // 创建时间
	UpdatedAt time.Time      `json:"updatedAt" gorm:"column:updatedAt"`              // 更新时间
	DeletedAt gorm.DeletedAt `json:"-" gorm:"column:deletedAt;index"`
}

// TableName 获取表名
func (item *TranslateTextItem) TableName() string {
	return "exa_translate_text"
}

func (item *TranslateTextItem) CreateItem(db *gorm.DB) error {
	result := db.Create(item)
	if result.Error != nil {
		return result.Error
	}
	return nil
}

func (item *TranslateTextItem) GetItemByMd5(db *gorm.DB, md5 string) (*TranslateTextItem, error) {
	var transItem TranslateTextItem
	if err := db.Where("md5 = ?", md5).First(&transItem).Error; err != nil {
		return nil, err
	}
	// 将 count 字段的值加 1
	transItem.UseCount++
	// 更新数据库中的 UseCount 字段
	if err := db.Save(&transItem).Error; err != nil {
		return nil, err // 如果更新失败，返回错误
	}
	return &transItem, nil
}

func (item *TranslateTextItem) UpdateItem(db *gorm.DB) error {
	result := db.Save(item)
	if result.Error != nil {
		return result.Error
	}
	return nil
}

// 执行硬删除，即从数据库中永久移除记录
func (item *TranslateTextItem) HardDeleteItem(db *gorm.DB, id int64) error {
	result := db.Unscoped().Delete(&TranslateTextItem{}, id)
	if result.Error != nil {
		return result.Error
	}
	return nil
}
