package model

import (
	"time"

	"gorm.io/gorm"
)

type CampaignInviteCode struct {
	ID           uint           `json:"id"           gorm:"column:id;primaryKey;autoIncrement"`
	CampaignType string         `json:"campaignType" gorm:"column:campaignType;type:varchar(50);not null;;index;index:idx_campaign_user,unique"`
	UserID       string         `json:"userId"       gorm:"column:userId;type:varchar(50);not null;index;index:idx_campaign_user,unique"`
	InviteCode   string         `json:"inviteCode"   gorm:"column:inviteCode;type:varchar(50);not null;uniqueIndex"`
	UpdatedAt    time.Time      `json:"updatedAt"    gorm:"column:updatedAt;autoUpdateTime"`
	CreatedAt    time.Time      `json:"createdAt"    gorm:"column:createdAt;autoCreateTime"`
	DeletedAt    gorm.DeletedAt `json:"deletedAt"    gorm:"column:deletedAt;index"`
}

// 表名
func (CampaignInviteCode) TableName() string {
	return "exa_campaign_invite_codes"
}

// 创建活动邀请码
func (c *CampaignInviteCode) CreateCampaignInviteCode(db *gorm.DB) error {
	return db.Create(c).Error
}

// 更新活动邀请码
func (c *CampaignInviteCode) UpdateCampaignInviteCode(db *gorm.DB) error {
	return db.Model(c).Save(c).Error
}

// 删除活动邀请码
func (c *CampaignInviteCode) DeleteCampaignInviteCode(db *gorm.DB) error {
	return db.Delete(c).Error
}

// 通过活动类型和用户ID查询活动邀请码信息
func (c *CampaignInviteCode) GetCampaignInviteCode(db *gorm.DB, campaignType, userId string) (*CampaignInviteCode, error) {
	var campaignInviteCode CampaignInviteCode
	err := db.Model(c).Where("campaignType = ? ", campaignType).Where("userId = ?", userId).First(&campaignInviteCode).Error
	return &campaignInviteCode, err
}

// 通过邀请码查询活动邀请码信息
func (c *CampaignInviteCode) GetCampaignInviteCodeByInviteCode(db *gorm.DB, inviteCode string) (*CampaignInviteCode, error) {
	var campaignInviteCode CampaignInviteCode
	err := db.Model(c).Where("inviteCode = ?", inviteCode).First(&campaignInviteCode).Error
	return &campaignInviteCode, err
}
