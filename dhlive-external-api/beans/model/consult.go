package model

import (
	"acg-ai-go-common/logger"
	"acg-ai-go-common/utils"
	"context"
	"fmt"
	"gorm.io/gorm"
	"strconv"
	"strings"
	"time"
)

type Consult struct {
	ID     int64  `json:"id" gorm:"column:id;primaryKey;autoIncrement"` // 主键ID，自增
	UserId string `json:"userId" gorm:"column:userId;type:varchar(100);comment:用户ID;index"`
	//FirstName     string         `json:"firstName" gorm:"column:firstName;type:varchar(100);comment:名字;index"`
	//LastName      string         `json:"lastName" gorm:"column:lastName;type:varchar(100);comment:姓氏;index"`
	//Country       string         `json:"country" gorm:"column:country;type:varchar(100);comment:国家;index"`
	//CompanyName   string         `json:"companyName" gorm:"column:companyName;type:varchar(512);comment:公司;index"`
	//ContactEmail  string         `json:"contactEmail" gorm:"column:contactEmail;type:varchar(100);comment:联系邮箱;"`
	//MainReason    string         `json:"mainReason" gorm:"column:mainReason;type:varchar(512);comment:只要原因;index"`
	//MirameaIUsage string         `json:"mirameaIUsage" gorm:"column:mirameaIUsage;type:varchar(512);comment:使用方式;index"`
	//MoreCase      string         `json:"moreCase" gorm:"column:moreCase;comment:更多;"`
	ConsultList string         `json:"consultList" gorm:"column:consultList;comment:咨询列表数据"`
	Status      string         `json:"status" gorm:"column:status;type:varchar(100);comment:咨询状态，UNTREATED:未处理 PROCESSING:处理中 RESOLVED:已解决 CLOSED:已关闭;index"`
	Operator    string         `json:"operator" gorm:"column:operator;type:varchar(100);comment:操作人;"`
	Reason      string         `json:"-" gorm:"column:reason;comment:操作说明;"`
	CreatedAt   time.Time      `json:"createdAt" gorm:"column:createdAt"`
	UpdatedAt   time.Time      `json:"updatedAt" gorm:"column:updatedAt"`
	DeletedAt   gorm.DeletedAt `json:"-" gorm:"column:deletedAt"`
}

func (item *Consult) TableName() string { return "exa_enterprise_custom_consult" }

func (item *Consult) Insert(db *gorm.DB, logCtx context.Context) (int64, error) {
	logger.Log.Infof(utils.MMark(logCtx) + "Insert Consult")
	result := db.Create(item)
	if result.Error != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"Insert error=%v", result.Error)
		return 0, result.Error
	}
	logger.Log.Infof(utils.MMark(logCtx) + "Insert scuuess")
	return result.RowsAffected, nil
}
func (item *Consult) SearchList(db *gorm.DB, logCtx context.Context, pageNo, pageSize int64, createAt string) (int64, []*Consult, error) {
	logger.Log.Infof(utils.MMark(logCtx) + "SearchList")
	conn := db.Model(item)
	if len(item.UserId) > 0 {
		conn.Where("userId = ?", item.UserId)
	}
	//if len(item.FirstName) > 0 {
	//	conn.Where("firstName LIKE ?", "%"+item.FirstName+"%")
	//}
	//if len(item.LastName) > 0 {
	//	conn.Where("lastName LIKE ?", "%"+item.LastName+"%")
	//}
	//if len(item.Country) > 0 {
	//	conn.Where("country LIKE ?", "%"+item.Country+"%")
	//}
	//if len(item.CompanyName) > 0 {
	//	conn.Where("companyName LIKE ?", "%"+item.CompanyName+"%")
	//}
	//if len(item.MainReason) > 0 {
	//	conn.Where("mainReason LIKE ?", "%"+item.MainReason+"%")
	//}
	//if len(item.MirameaIUsage) > 0 {
	//	conn.Where("mirameAiUsage LIKE ?", "%"+item.MirameaIUsage+"%")
	//}
	if len(item.Status) > 0 {
		conn.Where("status = ?", item.Status)
	}
	if len(createAt) > 0 {
		timestamps := strings.Split(createAt, ",")
		if len(timestamps) == 2 {
			start, err1 := strconv.ParseInt(timestamps[0], 10, 64)
			end, err2 := strconv.ParseInt(timestamps[1], 10, 64)
			if err1 != nil || err2 != nil {
				return 0, nil, fmt.Errorf("无效的时间戳范围")
			}
			conn.Where("createdAt > ? AND createdAt < ?", time.Unix(start, 0), time.Unix(end, 0))
		} else {
			logger.Log.Errorf(utils.MMark(logCtx) + "SearchList 时间戳格式无效")
			return 0, nil, fmt.Errorf("时间戳格式无效")
		}
	}
	conn = conn.Order("createdAt DESC")
	var total int64
	if err := conn.Count(&total).Error; err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"SearchList Count error=%v", err)
		return 0, nil, err
	}
	skip, limit := utils.ConvertPageNumSize2Skip(pageNo, pageSize)
	var consults []*Consult
	if err := conn.Offset(int(skip)).Limit(int(limit)).Find(&consults).Error; err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"SearchList find error=%v", err)
		return 0, nil, err
	}
	logger.Log.Infof(utils.MMark(logCtx) + "SearchList scuuess")
	return total, consults, nil
}
func (item *Consult) Update(db *gorm.DB, logCtx context.Context) (int64, error) {
	logger.Log.Infof(utils.MMark(logCtx) + "Update")
	result := db.Model(&Consult{}).Where("id = ?", item.ID).Updates(Consult{
		Status:   item.Status,
		Operator: item.Operator,
		Reason:   item.Reason,
	})
	if result.Error != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"Update  error=%v", result.Error)
		return 0, result.Error
	}
	logger.Log.Infof(utils.MMark(logCtx) + "Update scuuess")
	return result.RowsAffected, nil
}
func (item *Consult) Delete(db *gorm.DB, logCtx context.Context) (int64, error) {
	logger.Log.Infof(utils.MMark(logCtx) + "Delete ")
	// 1. 查询记录是否存在
	if err := db.Where("id = ?", item.ID).First(item).Error; err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"Delete 记录不存在 error=%v", err)
		return 0, err
	}
	// 2. 更新字段（记录操作人、原因、处理时间）
	item.Operator = item.Operator
	item.Reason = item.Reason
	// 3. 更新记录的其他字段
	if err := db.Save(item).Error; err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"Delete 更新记录失败 error=%v", err)
		return 0, err
	}
	// 4. 执行软删除（GORM 会自动设置 DeletedAt）
	if err := db.Delete(item).Error; err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"Delete 删除失败 error=%v", err)
		return 0, err
	}
	logger.Log.Infof(utils.MMark(logCtx) + "Delete scuuess")
	return item.ID, nil
}
