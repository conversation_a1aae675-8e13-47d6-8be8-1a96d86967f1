package model

import (
	"acg-ai-go-common/logger"
	"dhlive-external-api/beans/enum"
	"strconv"
	"time"

	"gorm.io/gorm"
)

// RiskControlType 定义风险控制条目的类型
type RiskControlType string

const (
	RiskControlTypeText       RiskControlType = "TEXT"
	RiskControlTypeImage      RiskControlType = "IMAGE"
	RiskControlTypeLongVideo  RiskControlType = "LONG_VIDEO"
	RiskControlTypeLongAudio  RiskControlType = "LONG_AUDIO"
	RiskControlTypeShortVideo RiskControlType = "SHORT_VIDEO"
	RiskControlTypeShortAudio RiskControlType = "SHORT_AUDIO"
)

// RiskControlItem 表示通用的风险控制条目信息
type RiskControlItem struct {
	ID                int64           `json:"id" gorm:"column:id;primaryKey;autoIncrement"`                      // 主键ID，自增
	AccountID         string          `json:"accountId" gorm:"column:accountId;not null;type:varchar(50)"`       // 用户ID
	LogId             string          `json:"logId" gorm:"column:logId;type:varchar(50)"`                        // 风控日志ID
	TaskId            string          `json:"taskId" gorm:"column:taskId;type:varchar(50);index"`                // 风控任务ID
	Text              string          `json:"text" gorm:"column:text"`                                           // 文本内容
	FileUrl           string          `json:"fileUrl" gorm:"column:fileUrl;type:varchar(1024)"`                  // 音频或视频的URL
	CompressionUrl    string          `json:"compressionUrl" gorm:"column:compressionUrl;type:varchar(1024)"`    // 音频或视频压缩后的URL
	AudioType         string          `json:"audioType" gorm:"column:audioType;type:varchar(20)"`                // 音频类型
	AudioRate         string          `json:"audioRate" gorm:"column:audioRate;type:varchar(20)"`                // 音频采样率
	Type              RiskControlType `json:"type" gorm:"column:type;type:varchar(20)"`                          // 风险控制条目的类型
	Md5               string          `json:"md5" gorm:"column:md5;index;type:varchar(50)"`                      // 文本或音频视频文件的MD5值
	UrlMd5            string          `json:"urlMd5" gorm:"column:urlMd5;index;type:varchar(50)"`                // 文本或音频视频文件的MD5值
	ConclusionType    int64           `json:"conclusionType" gorm:"column:conclusionType;type:varchar(20)"`      // 审核结果，可取值：1 合规，2 不合规，3 疑似， 4 审核失败
	CensorResult      string          `json:"censorResult" gorm:"column:censorResult;type:MEDIUMTEXT"`           // 风控结果
	SubmitResult      string          `json:"submitResult" gorm:"column:submitResult;type:varchar(1024)"`        // 提交结果
	AudioCensorResult string          `json:"audioCensorResult" gorm:"column:audioCensorResult;type:MEDIUMTEXT"` // 音频风控结果 此字段只用于短视频提取音频的审核结果。
	Status            enum.TaskStatus `json:"status" gorm:"column:status;type:varchar(20);index"`                // 状态，用于前端展示  READY/RUNNING/STOP/SUCCEED/FAILED
	Duration          int64           `json:"duration" gorm:"column:duration"`                                   // 任务处理的耗时时间 单位：毫秒
	RetryNumber       int64           `json:"retryNumber" gorm:"column:retryNumber"`                             // 任务重试次数
	CreatedAt         time.Time       `json:"createdAt" gorm:"column:createdAt"`                                 // 创建时间
	UpdatedAt         time.Time       `json:"updatedAt" gorm:"column:updatedAt"`                                 // 更新时间
	DeletedAt         gorm.DeletedAt  `json:"-" gorm:"column:deletedAt;index"`                                   // 删除时间/标记删除
}

// TableName 获取表名
func (item *RiskControlItem) TableName() string {
	return "exa_risk_control"
}

// CreateRiskControlItem 创建风险控制条目
func (item *RiskControlItem) CreateRiskControlItem(db *gorm.DB) error {
	result := db.Create(item)
	if result.Error != nil {
		return result.Error
	}
	return nil
}

// GetRiskControlItem 根据ID查询风险控制条目
func (item *RiskControlItem) GetRiskControlItem(db *gorm.DB, id int64) (*RiskControlItem, error) {
	var rcitem RiskControlItem
	if err := db.First(&rcitem, id).Error; err != nil {
		return nil, err
	}
	return &rcitem, nil
}

// UpdateRiskControlItem 更新风险控制条目
func (item *RiskControlItem) UpdateRiskControlItem(db *gorm.DB) error {
	result := db.Save(item)
	if result.Error != nil {
		return result.Error
	}
	return nil
}

// HardDeleteRiskControlItem 执行硬删除，即从数据库中永久移除记录
func (item *RiskControlItem) HardDeleteRiskControlItem(db *gorm.DB, id int64) error {
	result := db.Unscoped().Delete(&RiskControlItem{}, id)
	if result.Error != nil {
		return result.Error
	}
	return nil
}

func (item *RiskControlItem) GetRiskControlItemFromMd5(db *gorm.DB, md5 string) (*RiskControlItem, error) {
	var rt RiskControlItem
	if err := db.Where("md5 = ?", md5).First(&rt).Error; err != nil {
		return nil, err
	}
	return &rt, nil
}

func (item *RiskControlItem) GetRiskControlItemFromUrl(db *gorm.DB, fileUrl string) (*RiskControlItem, error) {
	var rt RiskControlItem
	if err := db.Where("fileUrl = ?", fileUrl).First(&rt).Error; err != nil {
		return nil, err
	}
	return &rt, nil
}

func (item *RiskControlItem) GetRiskControlItemFromUrlMd5(db *gorm.DB, urlmd5 string) (*RiskControlItem, error) {
	var rt RiskControlItem
	if err := db.Where("urlMd5 = ?", urlmd5).First(&rt).Error; err != nil {
		return nil, err
	}
	return &rt, nil
}
func (item *RiskControlItem) GetRiskControlItemFromTaskId(db *gorm.DB, taskid string) (*RiskControlItem, error) {
	var rt RiskControlItem
	if err := db.Where("taskId = ?", taskid).First(&rt).Error; err != nil {
		return nil, err
	}
	return &rt, nil
}

func (item *RiskControlItem) GetTasksWithStatus(db *gorm.DB, status enum.TaskStatus, taskType RiskControlType) ([]*RiskControlItem, error) {
	var tasks []*RiskControlItem
	err := db.Where("status = ?", string(status)).Where("type = ?", string(taskType)).
		Order("createdAt ASC").Find(&tasks).Error
	if err != nil {
		return nil, err
	}
	return tasks, nil
}

func (item *RiskControlItem) GetRiskControlItemList(db *gorm.DB, accountID, taskId string, conclusionType int64, pageNo int, pageSize int, startTime, endtime string) ([]*RiskControlItem, error) {
	var tasks []*RiskControlItem
	tx := db.Model(&RiskControlItem{})
	if len(accountID) > 0 {
		tx = tx.Where("accountId = ?", accountID)
		if tx.Error != nil {
			return nil, tx.Error
		}
	}

	if len(taskId) > 0 {
		tx = tx.Where("taskId = ?", taskId)
		if tx.Error != nil {
			return nil, tx.Error
		}
	}

	if conclusionType > 0 {
		tx = tx.Where("conclusionType = ?", conclusionType)
		if tx.Error != nil {
			return nil, tx.Error
		}
	}

	if len(startTime) > 0 && len(endtime) > 0 {
		nstartTime, err := strconv.ParseInt(startTime, 10, 64)
		if err != nil {
			return nil, err
		}
		nendtime, err := strconv.ParseInt(endtime, 10, 64)
		if err != nil {
			return nil, err
		}
		// 将时间戳转换为 time.Time 对象
		startTimer := time.Unix(nstartTime, 0).UTC() // 转换为 UTC 时间
		endTimer := time.Unix(nendtime, 0).UTC()     // 转换为 UTC 时间
		// 手动加上 8 小时（北京时间为 UTC+8）
		startTimerBeijing := startTimer.Add(8 * time.Hour)
		endTimerBeijing := endTimer.Add(8 * time.Hour)
		logger.Log.Infof("startTimerBeijing: %s, endTimerBeijing: %s", startTimerBeijing.Format("2006-01-02 15:04:05"), endTimerBeijing.Format("2006-01-02 15:04:05"))
		tx = tx.Where("createdAt BETWEEN ? AND ?", startTimerBeijing.Format("2006-01-02 15:04:05"), endTimerBeijing.Format("2006-01-02 15:04:05"))
		if tx.Error != nil {
			return nil, err
		}
	}

	err := tx.Offset((pageNo - 1) * pageSize).Limit(pageSize).
		Order("id ASC").Find(&tasks).Error
	if err != nil {
		return nil, err
	}
	return tasks, nil
}

func (item *RiskControlItem) GetRiskControlItemCount(db *gorm.DB, accountID, taskId string, conclusionType int64, pageNo int, pageSize int, startTime, endtime string) (int64, error) {
	var count int64

	tx := db.Model(&RiskControlItem{})
	if len(accountID) > 0 {
		tx = tx.Where("accountId = ?", accountID)
		if tx.Error != nil {
			return 0, tx.Error
		}
	}

	if len(taskId) > 0 {
		tx = tx.Where("taskId = ?", taskId)
		if tx.Error != nil {
			return 0, tx.Error
		}
	}

	if conclusionType > 0 {
		tx = tx.Where("conclusionType = ?", conclusionType)
		if tx.Error != nil {
			return 0, tx.Error
		}
	}

	if len(startTime) > 0 && len(endtime) > 0 {
		nstartTime, err := strconv.ParseInt(startTime, 10, 64)
		if err != nil {
			return 0, err
		}
		nendtime, err := strconv.ParseInt(endtime, 10, 64)
		if err != nil {
			return 0, err
		}
		// 将时间戳转换为 time.Time 对象
		startTimer := time.Unix(nstartTime, 0).UTC() // 转换为 UTC 时间
		endTimer := time.Unix(nendtime, 0).UTC()     // 转换为 UTC 时间
		// 手动加上 8 小时（北京时间为 UTC+8）
		startTimerBeijing := startTimer.Add(8 * time.Hour)
		endTimerBeijing := endTimer.Add(8 * time.Hour)
		tx = tx.Where("createdAt BETWEEN ? AND ?", startTimerBeijing.Format("2006-01-02 15:04:05"), endTimerBeijing.Format("2006-01-02 15:04:05"))
		if tx.Error != nil {
			return 0, err
		}
	}

	err := tx.Count(&count).Error
	if err != nil {
		return 0, err
	}

	return count, nil
}
