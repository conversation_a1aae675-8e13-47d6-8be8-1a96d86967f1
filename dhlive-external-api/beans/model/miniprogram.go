package model

import (
	"time"

	"gorm.io/gorm"
)

type MiniprogramInfo struct {
	ID        int64          `json:"id" gorm:"primarykey"`
	OpenId    string         `json:"openID" gorm:"column:openID;type:varchar(50)"`
	UniqueId  string         `json:"uniqueID"  gorm:"column:uniqueID;type:varchar(50)"`
	AccountID string         `json:"accountID" gorm:"column:accountID;type:varchar(50)"`
	CreatedAt time.Time      `json:"createdAt" gorm:"column:createdAt"` // 创建时间
	UpdatedAt time.Time      `json:"updatedAt" gorm:"column:updatedAt"` // 更新时间
	DeletedAt gorm.DeletedAt `json:"-" gorm:"column:deletedAt;index"`
}

// TableName 指定表名
func (MiniprogramInfo) TableName() string {
	return "exa_miniprogram_info"
}

// CreateRiskControlItem 创建风险控制条目
func (info *MiniprogramInfo) CreateMiniprogramInfo(db *gorm.DB) error {
	result := db.Create(info)
	if result.Error != nil {
		return result.Error
	}
	return nil
}

// UpdateMiniprogramInfo 更新
func (info *MiniprogramInfo) UpdateMiniprogramInfo(db *gorm.DB) error {
	result := db.Save(info)
	if result.Error != nil {
		return result.Error
	}
	return nil
}

// GetMiniprogramInfoFromOpenID
func (info *MiniprogramInfo) GetMiniprogramInfoFromOpenID(db *gorm.DB, openID string) (*MiniprogramInfo, error) {
	var minfo MiniprogramInfo
	if err := db.Where("openID = ?", openID).First(&minfo).Error; err != nil {
		return nil, err
	}
	return &minfo, nil
}

// GetMiniprogramInfoFromUniqueId
func (info *MiniprogramInfo) GetMiniprogramInfoFromUniqueId(db *gorm.DB, uniqueID string) (*MiniprogramInfo, error) {
	var minfo MiniprogramInfo
	if err := db.Where("uniqueID = ?", uniqueID).First(&minfo).Error; err != nil {
		return nil, err
	}
	return &minfo, nil
}

type RecordVideoTaskV1 struct {
	ID           uint64         `json:"id" gorm:"column:id;primarykey;autoIncrement"`  // 数据ID
	UserID       string         `json:"userId" gorm:"column:userId;index"`             // 用户ID
	EditID       uint64         `json:"editId" gorm:"column:editId;index"`             // 关联figure_v1表ID
	FigureID     uint64         `json:"figureId" gorm:"column:figureId;index"`         // 关联star-light人像表ID
	TaskType     string         `json:"taskType" gorm:"column:taskType"`               // 任务类型
	FigureTaskID uint64         `json:"figureTaskId" gorm:"column:figureTaskId;index"` // 关联figure_task_v1表ID
	LoadURL      string         `json:"loadUrl"  gorm:"column:loadUrl"`                // 人像加载地址
	UeName       string         `json:"ueName" gorm:"column:ueName"`                   // 执行任务的UE实例
	UeEvent      string         `json:"ueEvent" gorm:"column:ueEvent"`                 // 发送的UE指令
	Status       string         `json:"status" gorm:"column:status"`                   // 任务状态 READY/RUNNING/STOP/SUCCEED/FAILED
	Result       string         `json:"result" gorm:"column:result"`                   // UE响应结果
	VideoURL     string         `json:"videoUrl" gorm:"column:videoUrl"`               // 视频地址
	StartTime    int64          `json:"startTime" gorm:"column:startTime"`             // 开始时间
	EndTime      int64          `json:"endTime" gorm:"column:endTime"`                 // 结束时间
	CreatedAt    time.Time      `json:"createdAt" gorm:"column:createdAt"`             // 创建时间
	UpdatedAt    time.Time      `json:"updatedAt" gorm:"column:updatedAt"`             // 更新时间
	DeletedAt    gorm.DeletedAt `json:"-" gorm:"column:deletedAt;index"`               // 删除时间/标记删除
}

// TableName 指定表名
func (RecordVideoTaskV1) TableName() string {
	return "record_video_task_v1"
}

// GeteRecordVideoTaskV1FromUserID
func (r *RecordVideoTaskV1) GeteRecordVideoTaskV1FromUserID(db *gorm.DB, userId string) ([]*RecordVideoTaskV1, error) {
	var r1 []*RecordVideoTaskV1
	if err := db.Where("userId = ?", userId).Find(&r1).Error; err != nil {
		return nil, err
	}
	return r1, nil
}

func (r *RecordVideoTaskV1) GeteRecordVideoTaskV1FromUserIDAndTime(db *gorm.DB, userId string, startTime, endTime int64) (RecordVideoTaskV1, error) {
	var r1 RecordVideoTaskV1
	if err := db.Where("userId = ?", userId).Where("startTime = ?", startTime).Where("endTime = ?", endTime).First(&r1).Error; err != nil {
		return r1, err
	}
	return r1, nil
}

// UpdateRecordVideoTaskV1 更新
func (r *RecordVideoTaskV1) UpdateRecordVideoTaskV1(db *gorm.DB) error {
	result := db.Save(r)
	if result.Error != nil {
		return result.Error
	}
	return nil
}

// CreateRecordVideoTaskV1 创建
func (r *RecordVideoTaskV1) CreateRecordVideoTaskV1(db *gorm.DB) error {
	result := db.Create(r)
	if result.Error != nil {
		return result.Error
	}
	return nil
}

type FigureV1 struct {
	ID           uint64         `json:"id" gorm:"column:id;primarykey;autoIncrement"`
	RequestID    string         `json:"requestId" gorm:"column:requestId;index"`                   // 请求ID，标识哪次请求创建
	StarLightID  uint64         `json:"starLightId" gorm:"column:starLightId;index"`               // 人像ID（由2D API返回）
	UserID       string         `json:"userId" gorm:"column:userId;index"`                         // 人像所属用户
	Username     string         `json:"username" gorm:"column:username;type:varchar(100)"`         // 人像所属用户名称
	FigureName   string         `json:"figureName" gorm:"column:figureName;type:varchar(100)"`     // 人像名称（由3转2人像训练完成）
	Name         string         `json:"name" gorm:"column:name;type:varchar(100);index"`           // 人像名称
	PreviewImg   string         `json:"previewImg" gorm:"column:previewImg;type:varchar(100)"`     // 人像预览图
	LoadUrl      string         `json:"loadUrl" gorm:"column:loadUrl;type:varchar(100)"`           // 最后编辑的人像加载地址
	VideoUrl     string         `json:"videoUrl" gorm:"column:videoUrl;type:varchar(100)"`         // 人像视频地址
	Status       string         `json:"status" gorm:"column:status;type:varchar(100)"`             // 人像状态
	TrainLoadUrl string         `json:"trainLoadUrl" gorm:"column:trainLoadUrl;type:varchar(100)"` // 已训练的人像加载地址
	TrainStatus  string         `json:"trainStatus" gorm:"column:trainStatus;type:varchar(100)"`   // 人像3转2训练状态
	Source       string         `json:"source" gorm:"column:source;type:varchar(100)"`             // 人像来源, Applet/PC，默认为PC
	WxOpenID     string         `json:"wxOpenId" gorm:"column:wxOpenId;type:varchar(100)"`         // 微信OpenID
	WxUnionID    string         `json:"wxUnionId" gorm:"column:wxUnionId;type:varchar(100)"`       // 微信UnionID
	ShowVideoUrl string         `json:"showVideoUrl" gorm:"column:showVideoUrl;type:varchar(100)"` // 人像展示视频地址
	CreatedAt    time.Time      `json:"createdAt" gorm:"column:createdAt"`                         // 创建时间
	UpdatedAt    time.Time      `json:"updatedAt" gorm:"column:updatedAt"`                         // 更新时间
	DeletedAt    gorm.DeletedAt `json:"-" gorm:"column:deletedAt;index"`                           // 删除时间/标记删除
}

func (FigureV1) TableName() string {
	return "figure_v1"
}

// CreateFigureV1 创建
func (f *FigureV1) CreateFigureV1(db *gorm.DB) error {
	result := db.Create(f)
	if result.Error != nil {
		return result.Error
	}
	return nil
}

func (f *FigureV1) GetFigureV1FromUserID(db *gorm.DB, userId string) ([]*FigureV1, error) {
	var r1 []*FigureV1
	if err := db.Where("userId = ?", userId).Find(&r1).Error; err != nil {
		return nil, err
	}
	return r1, nil
}

func (f *FigureV1) GetFigureV1FromUserIDAndName(db *gorm.DB, userId, name string) (FigureV1, error) {
	var f1 FigureV1
	if err := db.Where("userId = ?", userId).Where("name = ?", name).First(&f1).Error; err != nil {
		return f1, err
	}
	return f1, nil
}

func (f *FigureV1) GetFigureV1FromUserIDAndSource(db *gorm.DB, userId, source string) (FigureV1, error) {
	var f1 FigureV1
	if err := db.Where("userId = ?", userId).Where("source = ?", source).First(&f1).Error; err != nil {
		return f1, err
	}
	return f1, nil
}

func (f *FigureV1) GetFigureV1FromUserIDAndStarLightID(db *gorm.DB, userId string, starLightId int64) (FigureV1, error) {
	var f1 FigureV1
	if err := db.Where("userId = ?", userId).Where("starLightId = ?", starLightId).First(&f1).Error; err != nil {
		return f1, err
	}
	return f1, nil
}

// UpdateRecordVideoTaskV1 更新
func (r *FigureV1) UpdateFigureV1(db *gorm.DB) error {
	result := db.Save(r)
	if result.Error != nil {
		return result.Error
	}
	return nil
}

type FigureTaskV1 struct {
	ID            uint64         `json:"id" gorm:"column:id;primarykey;autoIncrement"` // 数据ID
	ChannelID     uint64         `json:"channelId" gorm:"column:channelId;index"`      // 通道ID
	RequestID     string         `json:"requestId" gorm:"column:requestId;index"`      // 请求ID
	ParentID      uint64         `json:"parentId" gorm:"column:parentId;index"`        // 父任务ID
	UserID        string         `json:"userId" gorm:"column:userId;index"`            // 用户ID
	Operator      string         `json:"operator" gorm:"column:operator"`              // 操作人
	EditID        uint64         `json:"editId" gorm:"column:editId;index"`            // 人像编辑ID=>对应figure_v1表ID
	FigureID      uint64         `json:"figureId" gorm:"column:figureId;index"`        // 人像ID=>对应figure_v1表starLightId
	Name          string         `json:"name"  gorm:"column:name"`                     // 任务名称
	TaskType      string         `json:"taskType" gorm:"column:taskType"`              // 任务类型
	Query         string         `json:"query" gorm:"column:query"`                    // 话术
	Result        string         `json:"result" gorm:"column:result"`                  // 结果
	ParseResult   string         `json:"parseResult" gorm:"column:parseResult"`        // 解析后的结果
	Reason        string         `json:"reason" gorm:"column:reason"`                  // 解析后的原因
	UeEvent       string         `json:"ueEvent" gorm:"column:ueEvent"`                // 解析后的UE指令
	Status        string         `json:"status" gorm:"column:status"`                  // 任务状态 READY/RUNNING/STOP/SUCCEED/FAILED
	FinalStatus   string         `json:"finalStatus" gorm:"column:finalStatus"`        // 整体任务状态 READY/RUNNING/STOP/SUCCEED/FAILED
	Prompt        string         `json:"prompt" gorm:"column:prompt"`                  // 提示词
	StartTime     int64          `json:"startTime" gorm:"column:startTime"`            // 开始时间
	EndTime       int64          `json:"endTime" gorm:"column:endTime"`                // 结束时间
	FigureLoadUrl string         `json:"figureLoadUrl" gorm:"column:figureLoadUrl"`    // 人像加载地址
	Version       uint           `gorm:"column:version;default:1"`                     // 版本号
	CreatedAt     time.Time      `json:"createdAt" gorm:"column:createdAt"`            // 创建时间
	UpdatedAt     time.Time      `json:"updatedAt" gorm:"column:updatedAt"`            // 更新时间
	DeletedAt     gorm.DeletedAt `json:"-" gorm:"column:deletedAt;index"`              // 删除时间/标记删除
}

func (FigureTaskV1) TableName() string {
	return "figure_task_v1"
}

// CreateFigureV1 创建
func (f *FigureTaskV1) CreateFigureTaskV1(db *gorm.DB) error {
	result := db.Create(f)
	if result.Error != nil {
		return result.Error
	}
	return nil
}

func (f *FigureTaskV1) GetFigureTaskV1FromUserID(db *gorm.DB, userId string) ([]*FigureTaskV1, error) {
	var r1 []*FigureTaskV1
	if err := db.Where("userId = ?", userId).Find(&r1).Error; err != nil {
		return nil, err
	}
	return r1, nil
}

func (f *FigureTaskV1) GetFigureTaskV1FromUserIDAndTime(db *gorm.DB, userId string, startTime, endTime int64, name string) (FigureTaskV1, error) {
	var f1 FigureTaskV1
	if err := db.Where("userId = ?", userId).Where("startTime = ?", startTime).Where("endTime = ?", endTime).Where("name = ?", name).First(&f1).Error; err != nil {
		return f1, err
	}
	return f1, nil
}

func (r *FigureTaskV1) UpdateFigureTaskV1(db *gorm.DB) error {
	result := db.Save(r)
	if result.Error != nil {
		return result.Error
	}
	return nil
}

type UserFigure struct {
	ID               int64     `json:"id" gorm:"primaryKey"`
	UserID           string    `json:"user_id" gorm:"column:user_id"`
	Source           string    `json:"source" gorm:"column:source"`
	ResourceLabel    string    `json:"resource_label" gorm:"column:resource_label"`
	SceneLabel       string    `json:"scene_label" gorm:"column:scene_label"`
	Name             string    `json:"name" gorm:"column:name"`
	VideoURL         string    `json:"video_url" gorm:"column:video_url"`
	VideoURL2        string    `json:"video_url2" gorm:"column:video_url2"`
	MaskVideoURL     string    `json:"mask_video_url" gorm:"column:mask_video_url"`
	TemplateImg      string    `json:"template_img" gorm:"column:template_img"`
	TemplateVideo    string    `json:"template_video" gorm:"column:template_video"`
	Status           string    `json:"status" gorm:"column:status"`
	TaskID           string    `json:"task_id" gorm:"column:task_id"`
	FigureName       string    `json:"figure_name" gorm:"column:figure_name"`
	ResultMessage    string    `json:"result_message" gorm:"column:result_message"`
	FigureResult     string    `json:"figure_result" gorm:"column:figure_result"`
	IsDelete         int64     `json:"is_delete" gorm:"column:is_delete"`
	SystemProvided   uint8     `json:"system_provided" gorm:"column:system_provided"`
	Type             uint8     `json:"type" gorm:"column:type"`
	Gender           int64     `json:"gender" gorm:"column:gender"`
	ResolutionWidth  int64     `json:"resolution_width" gorm:"column:resolution_width"`
	ResolutionHeight int64     `json:"resolution_height" gorm:"column:resolution_height"`
	LastUsedTime     time.Time `json:"last_used_time" gorm:"column:last_used_time"`
	CreateTime       time.Time `json:"create_time" gorm:"column:create_time"`
	UpdateTime       time.Time `json:"update_time" gorm:"column:update_time"`
	EffectsThumbnail string    `json:"effects_thumbnail" gorm:"column:effects_thumbnail"`
	Effects          string    `json:"effects" gorm:"column:effects"`
	QuotaRecordID    int64     `json:"quota_record_id" gorm:"column:quota_record_id"`
	MatchTTS         string    `json:"match_tts" gorm:"column:match_tts"`
	PictureURL       string    `json:"picture_url" gorm:"column:picture_url"`
	Thumbnail        string    `json:"thumbnail" gorm:"column:thumbnail"`
}

func (UserFigure) TableName() string {
	return "user_figure"
}

func (u *UserFigure) CreateUserFigure(db *gorm.DB) error {
	result := db.Create(u)
	if result.Error != nil {
		return result.Error
	}
	return nil
}

func (u *UserFigure) GetUserFigureByUserID(db *gorm.DB, userID string) ([]*UserFigure, error) {
	var u1 []*UserFigure
	if err := db.Where("user_id = ?", userID).Find(&u1).Error; err != nil {
		return nil, err
	}
	return u1, nil
}

func (u *UserFigure) GetUserFigureByUserIDAndName(db *gorm.DB, userID, name string) (UserFigure, error) {
	var u1 UserFigure
	if err := db.Where("user_id = ?", userID).Where("name = ?", name).First(&u1).Error; err != nil {
		return u1, err
	}
	return u1, nil
}

// type UserFigureCopy struct {
// 	ID               int64          `json:"id" gorm:"primaryKey"`
// 	UserFigureID     int64          `json:"userFigureId" gorm:"column:userFigureId"`
// 	UserFigureCopyID int64          `json:"userFigureCopyId" gorm:"column:userFigureCopyId"`
// 	LogID            string         `json:"logID" gorm:"column:logID"`
// 	CreatedAt        time.Time      `json:"createdAt" gorm:"column:createdAt"`
// 	UpdatedAt        time.Time      `json:"updatedAt" gorm:"column:updatedAt"`
// 	DeletedAt        gorm.DeletedAt `json:"-" gorm:"column:deletedAt"`
// }

// func (UserFigureCopy) TableName() string {
// 	return "exa_user_figure_copy"
// }

// func (u *UserFigureCopy) CreateUserFigureCopy(db *gorm.DB) error {
// 	result := db.Create(u)
// 	if result.Error != nil {
// 		return result.Error
// 	}
// 	return nil
// }

// func (u *UserFigureCopy) GetUserFigureByUserID(db *gorm.DB, userFigureId int64) (UserFigureCopy, error) {
// 	var u1 UserFigureCopy
// 	if err := db.Where("userFigureId = ?", userFigureId).First(&u1).Error; err != nil {
// 		return u1, err
// 	}
// 	return u1, nil
// }
