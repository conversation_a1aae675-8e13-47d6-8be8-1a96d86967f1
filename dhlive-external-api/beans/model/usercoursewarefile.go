package model

import (
	"acg-ai-go-common/gomysql"
	"dhlive-external-api/beans/enum"
	"dhlive-external-api/beans/proto"
	"encoding/json"
	"time"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

type UserCoursewareFile struct {
	ID               int64           `json:"id" gorm:"primarykey"`
	UserID           string          `json:"userId" gorm:"column:userId;type:varchar(50);index"`
	FileName         string          `json:"fileName" gorm:"column:fileName;index;type:varchar(512)"`
	TaskId           string          `json:"taskId" gorm:"column:taskId;type:varchar(50)"`                           // 任务ID
	CoursewareTaskId string          `json:"coursewareTaskId" gorm:"column:coursewareTaskId;type:varchar(50);index"` // 课件任务ID
	ImageInfos       string          `json:"ImageInfos" gorm:"column:ImageInfos;type:MEDIUMTEXT"`                    // 课件信息
	Status           enum.TaskStatus `json:"status" gorm:"column:status;type:varchar(20)"`
	CreatedAt        time.Time       `json:"-" gorm:"column:createdAt;autoCreateTime"` // 创建时间
	UpdatedAt        time.Time       `json:"-" gorm:"column:updatedAt;autoUpdateTime"` // 更新时间
	DeletedAt        gorm.DeletedAt  `json:"-" gorm:"column:deletedAt;index"`          // 删除时间/标记删除
}

func (UserCoursewareFile) TableName() string {
	return "exa_user_courseware"
}

func (u *UserCoursewareFile) Create(db *gorm.DB) error {
	return db.Create(&u).Error
}

func (u *UserCoursewareFile) Update(db *gorm.DB) error {
	return db.Save(&u).Error
}

func (u *UserCoursewareFile) GetUserCoursewareFileList(db *gorm.DB, userId string) ([]*UserCoursewareFile, error) {
	var user []*UserCoursewareFile
	err := db.Where("userId = ?", userId).Where("status = ?", enum.TaskCensorSucceed).Where("deletedAt IS NULL").Order("id DESC").Find(&user).Error
	return user, err
}

func (u *UserCoursewareFile) GetUserCoursewareFileByStatusList(db *gorm.DB, status enum.TaskStatus) ([]*UserCoursewareFile, error) {
	var user []*UserCoursewareFile
	err := db.Where("status = ?", status).Where("deletedAt IS NULL").Order("id DESC").Find(&user).Error
	return user, err
}

func (u *UserCoursewareFile) GetUserCoursewareFile(db *gorm.DB, userId string, coursewareTaskId string) (*UserCoursewareFile, error) {
	var user UserCoursewareFile
	err := db.Where("userId = ?", userId).Where("coursewareTaskId = ?", coursewareTaskId).Where("deletedAt IS NULL").First(&user).Error
	return &user, err
}

func (u *UserCoursewareFile) GetUserCoursewareFileByForUpdate(db *gorm.DB, userId string, coursewareTaskId string) (*UserCoursewareFile, error) {
	var user UserCoursewareFile
	err := db.Clauses(clause.Locking{
		Strength: "UPDATE", // 强制使用 FOR UPDATE
	}).Where("userId = ?", userId).Where("coursewareTaskId = ?", coursewareTaskId).Where("deletedAt IS NULL").First(&user).Error
	return &user, err
}

func (u *UserCoursewareFile) GetUserCoursewareFileByFileName(db *gorm.DB, userId string, fileName string) (*UserCoursewareFile, error) {
	var user UserCoursewareFile
	err := db.Where("userId = ?", userId).Where("fileName = ?", fileName).Where("deletedAt IS NULL").First(&user).Error
	return &user, err
}

func (u *UserCoursewareFile) SetUserCoursewareFile(db *gorm.DB, usetId string, req *proto.DeleteUserCoursewareFileRequest) error {
	// 启动事务 查询用户课件列表并更新数据
	return db.Transaction(func(tx *gorm.DB) error {
		// 查询数据 (你可以根据需要查询特定的记录)
		var userCoursewareFile UserCoursewareFile
		if err := tx.Where("userId = ?", usetId).Where("coursewareTaskId = ?", req.FileId).Where("status = ?", enum.TaskCensorSucceed).Where("deletedAt IS NULL").First(&userCoursewareFile).Error; err != nil {
			// 如果查询失败，回滚事务并返回错误
			return err
		}
		// 提取用户课件列表
		imageInfo := proto.File2ImageInfoList{}
		newimageInfo := proto.File2ImageInfoList{
			ImageInfos: make([]proto.File2ImageInfo, len(imageInfo.ImageInfos)),
		}

		err := json.Unmarshal([]byte(userCoursewareFile.ImageInfos), &imageInfo)
		if err != nil {
			return err
		}

		if req.IsDeleteAll {
			userCoursewareFile.ImageInfos = ""
			// 保存更新的记录
			if err = u.DeleteUserCoursewareFile(gomysql.DB, usetId, req.FileId); err != nil {
				// 如果保存失败，回滚事务并返回错误
				return err
			}
			return nil
		}

		for _, image := range imageInfo.ImageInfos {
			isFind := false
			for _, index := range req.IndexList {
				if image.Index == index {
					isFind = true
				}
			}
			if !isFind {
				newimageInfo.ImageInfos = append(newimageInfo.ImageInfos, image)
			}
		}

		if len(newimageInfo.ImageInfos) == 0 {
			userCoursewareFile.ImageInfos = ""
			return u.DeleteUserCoursewareFile(gomysql.DB, usetId, req.FileId)
		}

		strinfo, err := json.Marshal(newimageInfo)
		if err != nil {
			return err
		}

		userCoursewareFile.ImageInfos = string(strinfo)
		// 保存更新的记录
		if err := tx.Save(&userCoursewareFile).Error; err != nil {
			// 如果保存失败，回滚事务并返回错误
			return err
		}

		// 如果没有错误，事务会自动提交
		return nil
	})
}

func (u *UserCoursewareFile) DeleteUserCoursewareFile(db *gorm.DB, userId string, taskId string) error {
	result := db.Where("userId = ?", userId).Where("coursewareTaskId = ?", taskId).Unscoped().Delete(&UserCoursewareFile{})
	if result.Error != nil {
		return result.Error
	}
	return nil
}
