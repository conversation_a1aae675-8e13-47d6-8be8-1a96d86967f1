package model

import (
	"time"

	"gorm.io/gorm"
)

// FileInfo 对应数据库中的文件信息表
type File2ImageDraftId struct {
	ID        int64          `json:"id" gorm:"primarykey"`
	DraftId   string         `json:"draftId" gorm:"column:draftId;not null;type:varchar(50)"`     // 草稿ID
	TaskId    string         `json:"taskId" gorm:"column:taskId;not null;type:varchar(50)"`       // 任务ID
	AccountID string         `json:"accountId" gorm:"column:accountId;not null;type:varchar(50)"` // 用户ID
	CreatedAt time.Time      `json:"createdAt" gorm:"column:createdAt"`                           // 创建时间
	UpdatedAt time.Time      `json:"updatedAt" gorm:"column:updatedAt"`                           // 更新时间
	DeletedAt gorm.DeletedAt `json:"-" gorm:"column:deletedAt;index"`                             // 删除时间/标记删除
}

// TableName 指定表名
func (faid *File2ImageDraftId) TableName() string {
	return "exa_file_2_image_draft_id"
}

func (faid *File2ImageDraftId) CreateFile2ImageDraftId(db *gorm.DB) error {
	result := db.Create(faid)
	if result.Error != nil {
		return result.Error
	}
	return nil
}

func (faid *File2ImageDraftId) GetFile2ImageDraftId(db *gorm.DB, id int64) (*File2ImageDraftId, error) {
	var rf File2ImageDraftId
	if err := db.First(&rf, id).Error; err != nil {
		return nil, err
	}
	return &rf, nil
}

// GetFile2ImageDraftIdFromTaskId 根据 taskid 查询
func (faid *File2ImageDraftId) GetFile2ImageDraftIdFromDraftId(db *gorm.DB, draftId, taskid, accountId string) (*File2ImageDraftId, error) {
	var rf File2ImageDraftId
	if err := db.Where("draftId = ?", draftId).Where("taskId = ?", taskid).Where("accountId = ?", accountId).First(&rf).Error; err != nil {
		return nil, err
	}
	return &rf, nil
}

// UpdateFile2ImageDraftId 更新
func (faid *File2ImageDraftId) UpdateFile2ImageDraftId(db *gorm.DB) error {
	result := db.Save(faid)
	if result.Error != nil {
		return result.Error
	}
	return nil
}

// HardDeleteRiskControlItem 执行硬删除，即从数据库中永久移除记录
func (faid *File2ImageDraftId) HardDeleteFile2ImageDraftId(db *gorm.DB, id int64) error {
	result := db.Unscoped().Delete(&File2ImageDraftId{}, id)
	if result.Error != nil {
		return result.Error
	}
	return nil
}
