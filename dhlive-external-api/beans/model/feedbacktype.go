package model

import (
	"acg-ai-go-common/logger"
	"acg-ai-go-common/utils"
	"context"
	"gorm.io/gorm"
	"time"
)

type FeedbackType struct {
	ID        int64     `json:"-" gorm:"column:id;primaryKey;autoIncrement"`          // 主键ID，自增
	Text      string    `json:"text" gorm:"column:text;not null;type:varchar(50);"`   // 反馈类型
	Value     string    `json:"value" gorm:"column:value;not null;type:varchar(50);"` // 反馈类型的value值
	External  string    `json:"external" gorm:"column:external"`                      // 其他类型
	Sort      int       `json:"sort" gorm:"column:sort"`                              // 排序，区间1-2000，数字越高越靠前
	CreatedAt time.Time `json:"-" gorm:"column:createdAt"`                            // 创建时间
	UpdatedAt time.Time `json:"-" gorm:"column:updatedAt"`                            // 更新时间
}

func (item *FeedbackType) TableName() string { return "exa_feedback_type" }

func (item *FeedbackType) InsertType(db *gorm.DB, logCtx context.Context) (int64, error) {
	newFeedbackItem := &FeedbackType{
		Text:     item.Text,
		Value:    item.Value,
		External: item.External,
		Sort:     item.Sort,
	}
	err := db.Create(newFeedbackItem).Error
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"insertType create feedback type error=%v", err)
		return 0, err
	}
	logger.Log.Errorf(utils.MMark(logCtx)+"insertType success feedbackType.Id=%v", newFeedbackItem.ID)
	return newFeedbackItem.ID, nil

}

func (item *FeedbackType) UpdateItem(db *gorm.DB, logCtx context.Context, id int64) (int, error) {
	result := db.Model(&FeedbackType{}).Where("id = ?", id).Updates(&FeedbackType{
		Text:     item.Text,
		Value:    item.Value,
		Sort:     item.Sort,
		External: item.External,
	})
	if result.Error != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"UpdateItem error=%v", result.Error)
		return 0, result.Error
	}
	return int(result.RowsAffected), nil
}
func (item *FeedbackType) DeleteItem(db *gorm.DB, logCtx context.Context, id int) error {
	result := db.Where("id = ?", id).Delete(&FeedbackType{})
	if result.Error != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"DeleteItem failed: id=%d, error=%v", id, result.Error)
		return result.Error
	}
	if result.RowsAffected == 0 {
		logger.Log.Errorf(utils.MMark(logCtx)+"DeleteItem no record found with id=%d", id)
		return gorm.ErrRecordNotFound
	}
	logger.Log.Infof(utils.MMark(logCtx)+"DeleteItem success: id=%d", id)
	return nil
}
func (item *FeedbackType) GetAllFeedbackTypes(db *gorm.DB, logCtx context.Context, text string) ([]FeedbackType, error) {
	logger.Log.Infof(utils.MMark(logCtx) + "GetAllFeedbackTypes")
	// 初始化切片用来保存结果
	var feedbacks []FeedbackType
	// 根据text参数构建查询条件
	var query *gorm.DB = db.Model(&FeedbackType{}) // 创建一个查询构建器
	if text != "" {
		// 假设有一个Text字段用于匹配text参数
		// 注意：这里使用LIKE查询，它通常是大小写敏感的，根据数据库和排序规则可能有所不同
		query = query.Where("text LIKE ?", "%"+text+"%")
	}
	// 添加排序条件：sort字段降序排列
	query = query.Order("sort DESC")
	// 执行查询，并将结果放到切片中
	result := query.Find(&feedbacks)
	// 查询失败直接返回
	if result.Error != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"GetAllFeedbackTypes err%v", result.Error)
		return nil, result.Error
	}
	logger.Log.Infof(utils.MMark(logCtx) + "GetAllFeedbackTypes success")
	return feedbacks, nil
}
