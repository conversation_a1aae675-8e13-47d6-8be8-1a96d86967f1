package model

import (
	"dhlive-external-api/beans/enum"
	"time"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

type Watermark struct {
	ID           int64           `json:"id" gorm:"primaryKey;autoIncrement;"`
	FileId       string          `json:"fileId" gorm:"column:fileId;not null;type:varchar(50)"`                        // 视频源地址
	SourceBucket string          `json:"sourceBucket,omitempty" gorm:"column:sourceBucket;not null;type:varchar(256)"` // 源文件桶名
	SourceKey    string          `json:"sourceKey" gorm:"column:sourceKey;not null;type:varchar(1024)"`                // 源文件 key 值
	TargetBucket string          `json:"targetBucket,omitempty" gorm:"column:targetBucket;not null;type:varchar(256)"` // 目标文件桶名
	TargetKey    string          `json:"targetKey" gorm:"column:targetKey;not null;type:varchar(1024)"`                // 目标文件key值
	CallbackUrl  string          `json:"callbackUrl" gorm:"column:callbackUrl;not null;type:varchar(1024)"`            // 回调url
	SourceUrl    string          `json:"sourceUrl" gorm:"column:sourceUrl;not null;type:varchar(1024)"`                // 源文件url
	TargetUrl    string          `json:"targetUrl" gorm:"column:targetUrl;not null;type:varchar(1024)"`                // 目标文件url
	JobId        string          `json:"jobId" gorm:"column:jobId;not null;type:varchar(50)"`                          // 任务ID
	Status       enum.TaskStatus `json:"status" gorm:"column:status;not null;type:varchar(20);index"`                  // 业务状态
	StartTime    time.Time       `json:"startTime" gorm:"column:start_time;default:NULL"`                              // 任务开始时间
	EndTime      time.Time       `json:"endTime" gorm:"column:end_time;default:NULL"`                                  // 任务结束时间
	CreatedAt    time.Time       `json:"createdAt" gorm:"column:createdAt;autoCreateTime"`                             // 创建时间
	UpdatedAt    time.Time       `json:"updatedAt" gorm:"column:updatedAt;autoUpdateTime"`                             // 更新时间
	DeletedAt    gorm.DeletedAt  `json:"-" gorm:"column:deletedAt;index"`                                              // 删除时间/标记删除
}

// TableName 指定表名
func (w *Watermark) TableName() string {
	return "exa_water_mark" // 假设数据库表名为file_infos
}

func (w *Watermark) CreateWatermark(db *gorm.DB) error {
	result := db.Create(w)
	if result.Error != nil {
		return result.Error
	}
	return nil
}

// GetWatermark 根据ID查询
func (w *Watermark) GetWatermark(db *gorm.DB, id int64) (*Watermark, error) {
	var w1 Watermark
	if err := db.First(&w1, id).Error; err != nil {
		return nil, err
	}
	return &w1, nil
}

// GetWatermarkFromSourceUrl 根据ID查询
func (f *Watermark) GetWatermarkFromFileId(db *gorm.DB, FileId string) (*Watermark, error) {
	var w1 Watermark
	if err := db.Where("fileId = ?", FileId).First(&w1).Error; err != nil {
		return nil, err
	}
	return &w1, nil
}

// GetWatermarkFromSourceUrl 根据ID查询
func (f *Watermark) GetWatermarkFromFileIdAndBosKey(db *gorm.DB, FileId, sourceKey string) (*Watermark, error) {
	var w1 Watermark
	if err := db.Where("fileId = ?", FileId).Where("sourceKey = ?", sourceKey).First(&w1).Error; err != nil {
		return nil, err
	}
	return &w1, nil
}

func (f *Watermark) GetWatermarkFromJobId(db *gorm.DB, jobId string) (*Watermark, error) {
	var w1 Watermark
	if err := db.Where("jobId = ?", jobId).First(&w1).Error; err != nil {
		return nil, err
	}
	return &w1, nil
}

// UpdateWatermark 更新
func (w *Watermark) UpdateWatermark(db *gorm.DB) error {
	result := db.Save(w)
	if result.Error != nil {
		return result.Error
	}
	return nil
}

// HardDeleteWatermark 执行硬删除，即从数据库中永久移除记录
func (w *Watermark) HardDeleteWatermark(db *gorm.DB, id int64) error {
	result := db.Unscoped().Delete(&Watermark{}, id)
	if result.Error != nil {
		return result.Error
	}
	return nil
}
func (w *Watermark) RecordExists(db *gorm.DB, id int, userID string) (*Watermark, bool) {
	var w1 Watermark
	result := db.Where("id = ? AND user_id = ?", id, userID).First(&w1)
	return &w1, result.RowsAffected > 0
}

func (p *Watermark) GetAndUpdateStatus(db *gorm.DB, oldState enum.TaskStatus, newState enum.TaskStatus) ([]*Watermark, error) {
	var tasks []*Watermark
	err := db.Transaction(func(tx *gorm.DB) error {
		// 使用 SELECT ... FOR UPDATE 加锁查询
		if err := tx.Clauses(clause.Locking{Strength: "UPDATE"}).Where("status = ?", string(oldState)).Find(&tasks).Error; err != nil {
			return err
		}

		// 更新查询到的记录的status字段为1
		for _, task := range tasks {
			if err := tx.Model(&task).Update("status", string(newState)).Error; err != nil {
				return err
			}
		}
		return nil
	})
	return tasks, err
}

func (p *Watermark) GetTasksWithStatus(db *gorm.DB, status enum.TaskStatus) ([]*Watermark, error) {
	var tasks []*Watermark
	err := db.Where("status = ?", string(status)).Where("jobId <> ''").
		Order("createdAt ASC").Find(&tasks).Error
	if err != nil {
		return nil, err
	}
	return tasks, nil
}
