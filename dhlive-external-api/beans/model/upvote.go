package model

import (
	"acg-ai-go-common/gomysql"
	"errors"
	"gorm.io/gorm"
	"time"
)

type Upvote struct {
	ID           int64          `json:"id" gorm:"primaryKey;autoIncrement;"`
	UserId       string         `json:"userId" gorm:"column:userId;not null;type:varchar(50)"`
	ResourceType string         `json:"resourceType" gorm:"column:resourceType;not null;type:varchar(50)"`
	ResourceId   string         `json:"resourceId" gorm:"column:resourceId;not null;type:varchar(50)"`
	Operate      string         `json:"operate" gorm:"column:operate;not null;type:varchar(50)"`
	CreatedAt    time.Time      `json:"createdAt" gorm:"column:createdAt;autoCreateTime"` // 创建时间
	UpdatedAt    time.Time      `json:"updatedAt" gorm:"column:updatedAt;autoUpdateTime"` // 更新时间
	DeletedAt    gorm.DeletedAt `json:"-" gorm:"column:deletedAt;index"`                  // 删除时间/标记删除
}

// TableName 指定表名
func (data *Upvote) TableName() string {
	return "upvote" // 假设数据库表名为file_infos
}

func (data *Upvote) Save() error {
	// 创建数据
	return gomysql.DB.Save(&data).Error
}

func (data *Upvote) GetUpvoteByResource(userId, resourceType, resourceId string) error {
	return gomysql.DB.Where("userId = ? AND resourceType = ? AND resourceId = ?",
		userId, resourceType, resourceId).First(&data).Error
}

func (data *Upvote) FindByResourceType(userId, resourceType string, resourceIds []string) (upvotes []*Upvote, err error) {
	err = gomysql.DB.Where("userId = ? and resourceType = ? and resourceId in ?", userId, resourceType, resourceIds).
		Find(&upvotes).Error
	if errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, nil
	}
	return upvotes, err
}
