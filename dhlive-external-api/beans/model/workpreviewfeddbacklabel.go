package model

import (
	"acg-ai-go-common/gomysql"
	"acg-ai-go-common/logger"
	"acg-ai-go-common/utils"
	"context"
	"errors"
	"fmt"
	"gorm.io/gorm"
	"time"
)

type WorkPreTag struct {
	ID          int64          `json:"id" gorm:"column:id;primaryKey;autoIncrement"` // 主键ID，自增
	Type        string         `json:"type" gorm:"column:type;type:varchar(100);comment:所属模块;index"`
	IsAuth      int            `json:"is_auth" gorm:"column:is_auth;comment:是否需要授权(1:不授权，2:授权);"`
	ControlType string         `json:"control_type" gorm:"column:control_type;type:varchar(100);comment:控件类型;index"`
	ControlName string         `json:"control_name" gorm:"column:control_name;type:varchar(100);comment:控件类型"`
	Options     string         `json:"options" gorm:"column:options;comment:选择项;"`
	Required    int            `json:"required" gorm:"column:required;default:2;comment:是否必填(1否2是)"`
	ColSpan     int            `json:"col_span" gorm:"column:col_span;comment:控件宽度"`
	MaxLength   int            `json:"max_length" gorm:"column:max_length;comment:最大可输入字符长度"`
	Sort        int            `json:"sort" gorm:"column:sort"`
	Others      string         `json:"others" gorm:"column:others;comment:附加信息;"`
	Creator     string         `json:"creator" gorm:"column:creator;type:varchar(100);comment:创建人;"`
	Operator    string         `json:"operator" gorm:"column:operator;type:varchar(100);comment:操作人;"`
	CreatedAt   time.Time      `json:"-" gorm:"column:createdAt"`
	UpdatedAt   time.Time      `json:"-" gorm:"column:updatedAt"`
	DeletedAt   gorm.DeletedAt `json:"-" gorm:"column:deletedAt"`
}

func (item *WorkPreTag) TableName() string { return "exa_work_pre_feedback_tag" }

func (item *WorkPreTag) Search(db *gorm.DB, logCtx context.Context) ([]WorkPreTag, error) {
	var results []WorkPreTag
	conn := db.Model(item)
	if item.Type != "" {
		conn = conn.Where("type = ?", item.Type)
	}
	// 添加排序
	conn = conn.Order("sort DESC")

	// 执行查询
	if err := conn.Find(&results).Error; err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"WorkPreTag Search error=%v, conditions: %+v", err, item)
		return nil, fmt.Errorf("查询失败: %w", err)
	}
	logger.Log.Infof(utils.MMark(logCtx) + "WorkPreTag Search scuuess")
	return results, nil
}
func (item *WorkPreTag) SearchList(db *gorm.DB, logCtx context.Context, pageNo, pageSize int64) (int64, []WorkPreTag, error) {
	var results []WorkPreTag
	conn := db.Model(item)
	if item.Type != "" {
		conn = conn.Where("type = ?", item.Type)
	}
	// 添加排序
	conn = conn.Order("sort DESC")
	var total int64
	if err := conn.Count(&total).Error; err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"WorkPreFeedback count error=%v", err)
		return 0, nil, err
	}
	skip, limit := utils.ConvertPageNumSize2Skip(pageNo, pageSize)
	// 执行查询
	if err := conn.Offset(int(skip)).Limit(int(limit)).Find(&results).Error; err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"WorkPreTag Search error=%v, conditions: %+v", err, item)
		return 0, nil, fmt.Errorf("查询失败: %w", err)
	}
	logger.Log.Infof(utils.MMark(logCtx) + "WorkPreTag Search scuuess")
	return total, results, nil
}
func (item *WorkPreTag) QueryByProblemType(db *gorm.DB, logCtx context.Context, problemType string) (*WorkPreTag, error) {
	var result *WorkPreTag
	query := db.Model(&WorkPreTag{}).Where("type = ?", item.Type).Where("control_name = ?", problemType).First(&result)
	if err := query.Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			logger.Log.Errorf(utils.MMark(logCtx) + "WorkPreTag QueryByProblemType ErrRecordNotFound")
			return nil, nil
		}
		logger.Log.Errorf(utils.MMark(logCtx)+"WorkPreTag QueryByProblemType error=%v", err)
		return nil, err
	}
	logger.Log.Infof(utils.MMark(logCtx) + "WorkPreTag QueryByProblemType scuuess")
	return result, nil
}
func (item *WorkPreTag) QueryById(db *gorm.DB, logCtx context.Context, id int64) (*WorkPreTag, error) {
	var result *WorkPreTag
	query := db.Model(&WorkPreTag{}).Where("id = ?", id).First(&result)
	if err := query.Error; err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"WorkPreTag QueryById error=%v", err)
		return nil, err
	}
	logger.Log.Infof(utils.MMark(logCtx) + "WorkPreTag QueryById scuuess")
	return result, nil
}
func (item *WorkPreTag) Insert(db *gorm.DB, logCtx context.Context) (int64, error) {
	result := db.Create(item)
	if result.Error != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"WorkPreTag Insert error=%v", result.Error)
		return 0, result.Error
	}
	logger.Log.Infof(utils.MMark(logCtx) + "WorkPreTag Insert scuuess")
	return result.RowsAffected, nil
}
func (item *WorkPreTag) Update(db *gorm.DB, logCtx context.Context) (int64, error) {
	result := db.Model(&WorkPreTag{}).Where("id = ?", item.ID).Updates(&WorkPreTag{
		Type:        item.Type,
		ControlType: item.ControlType,
		ControlName: item.ControlName,
		IsAuth:      item.IsAuth,
		Options:     item.Options,
		Required:    item.Required,
		ColSpan:     item.ColSpan,
		MaxLength:   item.MaxLength,
		Sort:        item.Sort,
		Operator:    item.Operator,
		Others:      item.Others,
	})
	if result.Error != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"WorkPreTag Update error=%v", result.Error)
		return 0, result.Error
	}
	logger.Log.Infof(utils.MMark(logCtx) + "WorkPreTag Update scuuess")
	return result.RowsAffected, nil
}

func (item *WorkPreTag) UpdateByOperator(db *gorm.DB, logCtx context.Context) error {
	result := db.Model(&WorkPreTag{}).Where("id = ?", item.ID).Updates(&WorkPreTag{
		Operator: item.Operator,
	})
	if result.Error != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"WorkPreTag UpdateByOperator error=%v", result.Error)
		return result.Error
	}
	logger.Log.Infof(utils.MMark(logCtx) + "WorkPreTag UpdateByOperator scuuess")
	return nil
}
func (item *WorkPreTag) DeleteByID() error {
	return gomysql.DB.
		Where("id = ?", item.ID).
		Delete(&item).Error
}

func (item *WorkPreTag) GetAuthStatus(db *gorm.DB, logCtx context.Context) (int, error) {
	var result struct {
		IsAuth int
	}
	// Query the database for the first matching record
	err := db.WithContext(logCtx).
		Model(&WorkPreTag{}).
		Select("is_auth").
		Where("type = ?", item.Type).
		First(&result).Error

	// Handle record not found case
	if errors.Is(err, gorm.ErrRecordNotFound) {
		return 0, nil
	}

	// Handle other potential errors
	if err != nil {
		return 0, fmt.Errorf("database query failed: %w", err)
	}

	// Return the found IsAuth value
	return result.IsAuth, nil
}
