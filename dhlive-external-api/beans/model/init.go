package model

import (
	"acg-ai-go-common/gomysql"
)

func InitMysqlDBTable() error {
	// 自动建表｜自动更新表结构
	if err := gomysql.DB.AutoMigrate(&File2Image{}); err != nil {
		return err
	}

	if err := gomysql.DB.AutoMigrate(&File2ImageAccountId{}); err != nil {
		return err
	}

	if err := gomysql.DB.AutoMigrate(&File2ImageDraftId{}); err != nil {
		return err
	}

	if err := gomysql.DB.AutoMigrate(&Watermark{}); err != nil {
		return err
	}

	if err := gomysql.DB.AutoMigrate(&RiskControlItem{}); err != nil {
		return err
	}

	if err := gomysql.DB.AutoMigrate(&MiniprogramInfo{}); err != nil {
		return err
	}

	if err := gomysql.DB.AutoMigrate(&UserCoursewareFile{}); err != nil {
		return err
	}
	if err := gomysql.DB.AutoMigrate(&TranslateTextItem{}); err != nil {
		return err
	}
	if err := gomysql.DB.AutoMigrate(&FeedbackType{}); err != nil {
		return err
	}
	if err := gomysql.DB.AutoMigrate(&UserFeedbackV1{}); err != nil {
		return err
	}
	if err := gomysql.DB.AutoMigrate(&Country{}); err != nil {
		return err
	}

	if err := gomysql.DB.AutoMigrate(&ConsultLabel{}); err != nil {
		return err
	}
	if err := gomysql.DB.AutoMigrate(&Questionnaire{}); err != nil {
		return err
	}
	if err := gomysql.DB.AutoMigrate(&QuestionnaireTag{}); err != nil {
		return err
	}

	if err := gomysql.DB.AutoMigrate(&CampaignInviteCode{}); err != nil {
		return err
	}

	if err := gomysql.DB.AutoMigrate(&CampaignBenefit{}); err != nil {
		return err
	}

	if err := gomysql.DB.AutoMigrate(&Consult{}); err != nil {
		return err
	}

	if err := gomysql.DB.AutoMigrate(&SpeechToText{}); err != nil {
		return err
	}

	// 点赞点踩
	if err := gomysql.DB.AutoMigrate(&Upvote{}); err != nil {
		return err
	}
	if err := gomysql.DB.AutoMigrate(&WorkPreTag{}); err != nil {
		return err
	}
	if err := gomysql.DB.AutoMigrate(&WorkPreFeedback{}); err != nil {
		return err
	}
	return nil
}

type JSONMap map[string]interface{}
