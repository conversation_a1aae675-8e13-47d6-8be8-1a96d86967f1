package model

import (
	"time"

	"gorm.io/gorm"
)

// FileInfo 对应数据库中的文件信息表
type File2ImageAccountId struct {
	ID        int64          `json:"id" gorm:"primarykey"`
	TaskId    string         `json:"taskId" gorm:"column:taskId;not null;type:varchar(50)"`       // 任务ID
	AccountID string         `json:"accountId" gorm:"column:accountId;not null;type:varchar(50)"` // 用户ID
	Md5       string         `json:"md5" gorm:"column:md5;not null;type:varchar(50)"`             // 文件md5
	LogId     string         `json:"logId" gorm:"column:logID;not null;type:varchar(50)"`         // 日志ID
	CreatedAt time.Time      `json:"createdAt" gorm:"column:createdAt;autoCreateTime"`            // 创建时间
	UpdatedAt time.Time      `json:"updatedAt" gorm:"column:updatedAt;autoUpdateTime"`            // 更新时间
	DeletedAt gorm.DeletedAt `json:"-" gorm:"column:deletedAt;index"`                             // 删除时间/标记删除
}

// TableName 指定表名
func (faid *File2ImageAccountId) TableName() string {
	return "exa_file_2_image_account_id"
}

func (faid *File2ImageAccountId) CreateFile2ImageAccountId(db *gorm.DB) error {
	result := db.Create(faid)
	if result.Error != nil {
		return result.Error
	}
	return nil
}

func (faid *File2ImageAccountId) GetFile2ImageAccountId(db *gorm.DB, id int64) (*File2ImageAccountId, error) {
	var rf File2ImageAccountId
	if err := db.First(&rf, id).Error; err != nil {
		return nil, err
	}
	return &rf, nil
}

// GetFile2ImageAccountIdFromTaskId 根据 taskid 查询
func (faid *File2ImageAccountId) GetFile2ImageAccountIdFromTaskId(db *gorm.DB, taskid string, accountid string) (*File2ImageAccountId, error) {
	var rf File2ImageAccountId
	if err := db.Where("taskId = ?", taskid).Where("accountId = ?", accountid).First(&rf).Error; err != nil {
		return nil, err
	}
	return &rf, nil
}

// UpdateFile2ImageAccountId 更新
func (faid *File2ImageAccountId) UpdateFile2ImageAccountId(db *gorm.DB) error {
	result := db.Save(faid)
	if result.Error != nil {
		return result.Error
	}
	return nil
}

// HardDeleteRiskControlItem 执行硬删除，即从数据库中永久移除记录
func (faid *File2ImageAccountId) HardDeleteFile2ImageAccountId(db *gorm.DB, id int64) error {
	result := db.Unscoped().Delete(&File2ImageAccountId{}, id)
	if result.Error != nil {
		return result.Error
	}
	return nil
}
