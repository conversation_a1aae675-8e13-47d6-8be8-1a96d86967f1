package model

import (
	"acg-ai-go-common/gomysql"
	"acg-ai-go-common/logger"
	"acg-ai-go-common/utils"
	"context"
	"errors"
	"gorm.io/gorm"
	"time"
)

type ConsultLabel struct {
	ID          int64          `json:"id" gorm:"column:id;primaryKey;autoIncrement"` // 主键ID，自增
	Type        string         `json:"type" gorm:"column:type;type:varchar(100);comment:文本框类型;"`
	ProblemType string         `json:"problemType" gorm:"column:problemType;type:varchar(100);comment:文本框key;index"`
	Options     string         `json:"options" gorm:"column:options;comment:选择项;"`
	Required    int            `json:"required" gorm:"column:required;default:2;comment:是否必填(0否1是)"`
	ColSpan     int            `json:"colSpan" gorm:"column:colSpan;comment:控件宽度"`
	MaxLength   int            `json:"maxLength" gorm:"column:maxLength;comment:最大可输入字符长度"`
	Sort        int            `json:"sort" gorm:"column:sort"`
	Creator     string         `json:"creator" gorm:"column:creator;type:varchar(100);comment:创建人;"`
	Operator    string         `json:"operator" gorm:"column:operator;type:varchar(100);comment:操作人;"`
	CreatedAt   time.Time      `json:"-" gorm:"column:createdAt"`
	UpdatedAt   time.Time      `json:"-" gorm:"column:updatedAt"`
	DeletedAt   gorm.DeletedAt `json:"-" gorm:"column:deletedAt"`
}

func (item *ConsultLabel) TableName() string { return "exa_enterprise_custom_consult_label" }

func (item *ConsultLabel) Search(db *gorm.DB, logCtx context.Context) ([]ConsultLabel, error) {
	logger.Log.Infof(utils.MMark(logCtx) + "Search")
	var results []ConsultLabel
	err := db.Model(&ConsultLabel{}).Debug().Order("sort DESC").Find(&results).Error
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"Search error=%v", err)
		return nil, err
	}
	logger.Log.Infof(utils.MMark(logCtx) + "Search Scuuess")
	return results, nil
}
func (item *ConsultLabel) QueryByProblemType(db *gorm.DB, logCtx context.Context, problemType string) (*ConsultLabel, error) {
	logger.Log.Infof(utils.MMark(logCtx) + "QueryByProblemType")
	var result *ConsultLabel
	query := db.Model(&ConsultLabel{}).Where("problemType = ?", problemType).First(&result)
	if err := query.Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			logger.Log.Errorf(utils.MMark(logCtx) + "QueryByProblemType ErrRecordNotFound")
			return nil, nil
		}
		logger.Log.Errorf(utils.MMark(logCtx)+"UpdateByOQueryByProblemTypeperator error=%v", err)
		return nil, err
	}
	logger.Log.Infof(utils.MMark(logCtx) + "QueryByProblemType Scuuess")
	return result, nil
}
func (item *ConsultLabel) QueryById(db *gorm.DB, logCtx context.Context, id int64) (*ConsultLabel, error) {
	logger.Log.Infof(utils.MMark(logCtx) + "QueryById")
	var result *ConsultLabel
	query := db.Model(&ConsultLabel{}).Where("id = ?", id).First(&result)
	if err := query.Error; err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"QueryById error=%v", err)
		return nil, err
	}
	logger.Log.Infof(utils.MMark(logCtx) + "QueryById Scuuess")
	return result, nil
}
func (item *ConsultLabel) Insert(db *gorm.DB, logCtx context.Context) (int64, error) {
	logger.Log.Infof(utils.MMark(logCtx) + "Insert")
	result := db.Create(item)
	if result.Error != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"Insert error=%v", result.Error)
		return 0, result.Error
	}
	logger.Log.Infof(utils.MMark(logCtx) + "Insert Scuuess")
	return result.RowsAffected, nil
}
func (item *ConsultLabel) Update(db *gorm.DB, logCtx context.Context) (int64, error) {
	logger.Log.Infof(utils.MMark(logCtx) + "Update")
	result := db.Model(&ConsultLabel{}).Where("id = ?", item.ID).Updates(&ConsultLabel{
		Type:        item.Type,
		ProblemType: item.ProblemType,
		Options:     item.Options,
		Required:    item.Required,
		ColSpan:     item.ColSpan,
		MaxLength:   item.MaxLength,
		Sort:        item.Sort,
		Operator:    item.Operator,
	})
	if result.Error != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"Update error=%v", result.Error)
		return 0, result.Error
	}
	logger.Log.Infof(utils.MMark(logCtx) + "Update Scuuess")
	return result.RowsAffected, nil
}

func (item *ConsultLabel) UpdateByOperator(db *gorm.DB, logCtx context.Context) error {
	logger.Log.Infof(utils.MMark(logCtx) + "UpdateByOperator")
	result := db.Model(&ConsultLabel{}).Where("id = ?", item.ID).Updates(&ConsultLabel{
		Operator: item.Operator,
	})
	if result.Error != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"UpdateByOperator error=%v", result.Error)
		return result.Error
	}
	logger.Log.Infof(utils.MMark(logCtx) + "UpdateByOperator Scuuess")
	return nil
}
func (item *ConsultLabel) DeleteByID() error {
	return gomysql.DB.
		Where("id = ?", item.ID).
		Delete(&item).Error
}
