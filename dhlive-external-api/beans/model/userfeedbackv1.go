package model

import (
	"acg-ai-go-common/logger"
	"acg-ai-go-common/utils"
	"context"
	"fmt"
	"gorm.io/gorm"
	"strconv"
	"strings"
	"time"
)

type UserFeedbackV1 struct {
	ID          int64          `json:"id" gorm:"column:id;primaryKey;autoIncrement"`                                                                                                                           // 主键ID，自增
	UserId      string         `json:"user_id" gorm:"column:user_id;type:varchar(100);comment:用户ID;"`                                                                                                        // 用户id
	Type        string         `json:"type" gorm:"column:type;type:varchar(512);comment:DH_CLONE: 数字人克隆 SOUND_CLONE: 声音克隆 AI_VIDEO: AI视频 PPT_EXPLAIN: PPT讲解视频 EDITOR: 编辑器使用 OTHER: 其他;"` // 反馈类型
	CurrentPage string         `json:"current_page" gorm:"column:current_page;type:varchar(100);comment:反馈时所在页面;"`                                                                                      // 反馈时所在页面
	Contact     string         `json:"contact" gorm:"column:contact;type:varchar(32);comment:联系方式;"`                                                                                                       // 联系方式
	Describe    string         `json:"describe" gorm:"column:describe;type:varchar(1024);comment:描述信息;"`                                                                                                   // 描述
	UploadArr   string         `json:"upload_arr" gorm:"column:upload_arrcomment:图片地址;"`                                                                                                                   // 图片地址
	Status      string         `json:"status" gorm:"column:status;type:varchar(32);comment:反馈状态，UNTREATED:未处理 PROCESSING:处理中 RESOLVED:已解决 CLOSED:已关闭;"`                                        // 反馈状态
	Operator    string         `json:"operator" gorm:"column:operator;type:varchar(100);comment:操作人;"`                                                                                                      // 操作人
	Reason      string         `json:"reason" gorm:"column:reason;type:varchar(2048);comment:操作说明;"`                                                                                                       // 备注
	ProcessTime time.Time      `json:"process_time" gorm:"column:process_time;comment:处理流程时间;"`                                                                                                          // 处理流程时间
	CreatedAt   time.Time      `json:"create_time" gorm:"column:createdAt"`                                                                                                                                    // 创建时间,驼峰命名是为了兼容gorm
	UpdatedAt   time.Time      `json:"update_time" gorm:"column:updatedAt"`                                                                                                                                    // 更新时间,驼峰命名是为了兼容gorm
	DeletedAt   gorm.DeletedAt `json:"-" gorm:"column:DeletedAt"`                                                                                                                                              // 删除时间,驼峰命名是为了兼容gorm
}
type UserFeedbackQuery struct {
	PageNo      int    `json:"pageNo" form:"page_no"`           // 页码
	PageSize    int    `json:"pageSize" form:"page_size"`       // 每页数量
	UserId      string `json:"userId" form:"user_id"`           // 用户ID
	Type        string `json:"type" form:"type"`                // 类型
	CurrentPage string `json:"currentPage" form:"current_page"` // 所在页面
	Status      string `json:"status" form:"status"`            // 状态
	CreateTime  string `json:"createTime" form:"createdAt"`     // 创建时间
}

func (item *UserFeedbackV1) TableName() string { return "exa_user_feedback_v1" }

func (item *UserFeedbackV1) Insert(db *gorm.DB, logCtx context.Context) (int, error) {
	logger.Log.Infof(utils.MMark(logCtx) + "Insert feesback")
	result := db.Create(item)
	if result.Error != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"Insert feesback error=%v", result.Error)
		return 0, result.Error
	}
	logger.Log.Infof(utils.MMark(logCtx)+"Insert feesback scuuess", utils.MMark(logCtx))
	return int(result.RowsAffected), nil
}

func (item *UserFeedbackV1) GetFeedBackList(db *gorm.DB, logCtx context.Context, req *UserFeedbackQuery) (int64, []UserFeedbackV1, error) {
	logger.Log.Infof(utils.MMark(logCtx)+"GetFeedBackList", utils.MMark(logCtx))
	query := db.Model(item)
	if req.UserId != "" {
		query.Where("user_id = ?", req.UserId)
	}
	if req.Type != "" {
		types := strings.Split(req.Type, ",")
		// 正确的方式：使用Scopes或者直接构建OR条件
		orConditions := make([]string, 0)
		params := make([]interface{}, 0)
		for _, t := range types {
			t = strings.TrimSpace(t)
			orConditions = append(orConditions, "type LIKE ?")
			params = append(params, "%"+t+"%")
		}
		query = query.Where(strings.Join(orConditions, " OR "), params...)
	}
	if req.Status != "" {
		query.Where("status = ?", req.Status)
	}
	if req.CurrentPage != "" {
		query.Where("current_page IN (?)", req.CurrentPage)
	}
	if req.CreateTime != "" {
		// 如果输入字符串包含逗号，拆分为多个时间戳
		timestamps := strings.Split(req.CreateTime, ",")
		if len(timestamps) == 2 {
			start, err1 := strconv.ParseInt(timestamps[0], 10, 64)
			end, err2 := strconv.ParseInt(timestamps[1], 10, 64)
			if err1 != nil || err2 != nil {
				return 0, nil, fmt.Errorf("无效的时间戳范围")
			}
			// 创建时间在给定的时间戳范围内
			query.Where("createdAt > ? AND createdAt < ?", time.Unix(start, 0), time.Unix(end, 0))
		} else {
			return 0, nil, fmt.Errorf("时间戳格式无效")
		}
	}
	query.Order("createdAt desc")
	logger.Log.Infof(utils.MMark(logCtx)+"GetFeedBackList query:%v", query)
	// 获取总数用于分页
	var total int64
	if err := query.Count(&total).Error; err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"GetFeedBackList 统计反馈数量错误:%v", err)
		return 0, nil, err
	}
	// 设置分页参数
	if req.PageNo <= 0 {
		req.PageNo = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 10 // 默认每页10条
	}
	// 执行分页查询
	offset := (req.PageNo - 1) * req.PageSize
	var feedbacks []UserFeedbackV1
	if err := query.Offset(offset).Limit(req.PageSize).Find(&feedbacks).Error; err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"GetFeedBackList 查询反馈列表错误:%v", err)
		return 0, nil, err
	}

	logger.Log.Infof(utils.MMark(logCtx)+"GetFeedBackList scuuess total:%v", total)
	return total, feedbacks, nil
}

func (item *UserFeedbackV1) UpdateStatus(db *gorm.DB, logCtx context.Context) (int, error) {
	logger.Log.Infof(utils.MMark(logCtx) + "UpdateStatus ")
	result := db.Model(&UserFeedbackV1{}).Where("id=?", item.ID).Updates(&UserFeedbackV1{
		Status:      item.Status,
		Operator:    item.Operator,
		Reason:      item.Reason,
		ProcessTime: time.Now(),
	})
	if result.Error != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"UpdateStatus error=%v", result.Error)
		return 0, result.Error
	}
	logger.Log.Infof(utils.MMark(logCtx)+"UpdateStatus success", utils.MMark(logCtx))
	return int(result.RowsAffected), nil
}

func (item *UserFeedbackV1) DeleteItem(db *gorm.DB, logCtx context.Context, id int64, operator string, reason string) (int64, error) {
	logger.Log.Infof(utils.MMark(logCtx)+"DeleteItem ", utils.MMark(logCtx))
	// 1. 查询记录是否存在
	if err := db.Where("id = ?", id).First(item).Error; err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"DeleteItem 记录不存在 error=%v", err)
		return 0, err
	}
	// 2. 更新字段（记录操作人、原因、处理时间）
	item.Operator = operator
	item.Reason = reason
	item.ProcessTime = time.Now() // 记录处理时间
	// 3. 更新记录的其他字段
	if err := db.Save(item).Error; err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"DeleteItem 更新记录失败 error=%v", err)
		return 0, err
	}
	// 4. 执行软删除（GORM 会自动设置 DeletedAt）
	if err := db.Delete(item).Error; err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"DeleteItem 删除失败 error=%v", err)
		return 0, err
	}
	logger.Log.Infof(utils.MMark(logCtx) + "DeleteItem scuuess")
	return item.ID, nil
}
