package proto

type SpeechToTextRequest struct {
	AudioURL []string `json:"audioUrl" bind:"required"`
}

type SpeechToTextResponse struct {
	TotalDuration int       `json:"totalDuration"` // 总时长（毫秒）
	Detail        []Segment `json:"detail"`        // 分段列表
	LogId         string    `json:"logId,omitempty"`
}

type Segment struct {
	Text      string          `json:"text"`      // 整段文本
	Duration  int             `json:"duration"`  // 段持续时间（毫秒）
	StartTime int             `json:"startTime"` // 起始时间（毫秒）
	EndTime   int             `json:"endTime"`   // 结束时间（毫秒）
	Detail    []SegmentDetail `json:"detail"`    // 子级细节（逐字）
}

type SegmentDetail struct {
	Text      string `json:"text"`      // 单字文本
	StartTime int    `json:"startTime"` // 起始时间（毫秒）
	EndTime   int    `json:"endTime"`   // 结束时间（毫秒）
}
