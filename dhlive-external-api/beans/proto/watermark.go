package proto

import "time"

type CreateWaterMarkRequest struct {
	FileId       string `json:"fileId"`
	SourceBucket string `json:"sourceBucket,omitempty"`
	SourceKey    string `json:"sourceKey"`
	TargetBucket string `json:"targetBucket,omitempty"`
	TargetKey    string `json:"targetKey"`
	CallbackUrl  string `json:"callbackUrl"`
}

type WaterMarkResponse struct {
	FileId string `json:"fileId"`
}

type GetWaterMarkRequest struct {
	FileId string `json:"fileId"`
	JobId  string `json:"jobId"`
}
type GetWaterMarkExtractionRequest struct {
	JobId string `json:"jobId"`
}

type WatermarkCallbackResponse struct {
	FileId string `json:"fileId"` // 文件id，输出日志使用
}

// WatermarkCallbackMessage
// https://cloud.baidu.com/doc/MCT/s/7jwvz5gew#%E6%9F%A5%E8%AF%A2%E9%80%9A%E7%9F%A5 接口文档
type WatermarkCallbackMessage struct {
	MessageId        string `json:"messageId"`
	MessageBody      string `json:"messageBody"`
	Notification     string `json:"notification"`
	Server           string `json:"server"`
	SubscriptionName string `json:"subscriptionName"`
	Version          string `json:"version"`
	Signature        string `json:"signature"`
}

type WatermarkCallbackMessageBody struct {
	JobId           string                      `json:"jobId"`
	PipelineName    string                      `json:"pipelineName"`
	JobStatus       string                      `json:"jobStatus"`
	CreateTime      time.Time                   `json:"createTime"` // Consider using time.Time and custom marshaling
	StartTime       time.Time                   `json:"startTime"`  // Consider using time.Time and custom marshaling
	EndTime         time.Time                   `json:"endTime"`    // Consider using time.Time and custom marshaling
	Error           *WatermarkCallbackErrorInfo `json:"error,omitempty"`
	Source          WatermarkCallbackSource     `json:"source"`
	Target          WatermarkCallbackTarget     `json:"target"`
	Output          WatermarkCallbackOutput     `json:"output"`
	SecretKeyUserId string                      `json:"secretKeyUserId"`
	ClusterName     string                      `json:"clusterName"`
}

type WatermarkCallbackErrorInfo struct {
	Code    string `json:"code"`
	Message string `json:"message"`
}

type WatermarkCallbackSource struct {
	SourceKey string                      `json:"sourceKey"`
	Clips     []WatermarkCallbackClipInfo `json:"clips"`
}

type WatermarkCallbackClipInfo struct {
	Bucket                 string `json:"bucket"`
	SourceKey              string `json:"sourceKey"`
	AsMasterClip           bool   `json:"asMasterClip"`
	EnableLogo             bool   `json:"enableLogo"`
	EnableDelogo           bool   `json:"enableDelogo"`
	EnableCrop             bool   `json:"enableCrop"`
	StartTimeInSecond      int    `json:"startTimeInSecond,omitempty"`
	DurationInSecond       int    `json:"durationInSecond,omitempty"`
	StartTimeInMillisecond int    `json:"startTimeInMillisecond,omitempty"`
	DurationInMillisecond  int    `json:"durationInMillisecond,omitempty"`
}

type WatermarkCallbackTarget struct {
	TargetKey  string `json:"targetKey"`
	PresetName string `json:"presetName"`
}

type WatermarkCallbackOutput struct {
	Video WatermarkCallbackVideoInfo `json:"video"`
	Audio WatermarkCallbacAudioInfo  `json:"audio"`
}

type WatermarkCallbackVideoInfo struct {
	DurationInSeconds float64 `json:"durationInSeconds"`
	SizeInKiloByte    float64 `json:"sizeInKiloByte"`
	WidthInPixel      int     `json:"widthInPixel"`
	HeightInPixel     int     `json:"heightInPixel"`
	FrameRate         int     `json:"frameRate"`
	Mp4MoovSize       int     `json:"mp4MoovSize"`
}

type WatermarkCallbacAudioInfo struct {
	SampleRateInHz int `json:"sampleRateInHz"`
	Channels       int `json:"channels"`
}

// 水印结果请求结构体
type JobStatusInfo struct {
	Code       string `json:"code"`       // 错误码
	ErrMsg     string `json:"errMsg"`     // 错误原因
	FileId     string `json:"fileId"`     // 文件标识
	JobId      string `json:"jobId"`      // 任务ID
	JobStatus  string `json:"jobStatus"`  // 任务状态（SUCCESS, FAILED）
	CreateTime string `json:"createTime"` // 任务创建时间
	StartTime  string `json:"startTime"`  // 任务开始时间
	EndTime    string `json:"endTime"`    // 任务结束时间
	FileUrl    string `json:"fileUrl"`    // 水印视频 bos url
	Duration   int    `json:"duration"`   // 任务时长
}

type ErrorInfo struct {
	Code    string `json:"code"`
	Message string `json:"message"`
}
