package proto

import "dhlive-external-api/beans/enum"

type IDReq struct {
	ID uint64 `json:"id" form:"id" binding:"required"` // 数据ID
}

// OptionSearchReq 爱速搭下拉框通用搜索请求结构体
type OptionSearchReq struct {
	Term string `json:"term" form:"term"`
}

// OptionResult 爱速搭下拉框渲染结构体
type OptionResult struct {
	Options []*Option `json:"options"`
}

type Option struct {
	Label string `json:"label"`
	Value string `json:"value"`
}

// WebsocketMessage websocket消息结构
type WebsocketMessage struct {
	Action    enum.WsMsgAction `json:"action"`         // 事件/动作，由该字段判断消息处理逻辑
	RequestId string           `json:"requestId"`      // 本次消息的ID
	Body      interface{}      `json:"body,omitempty"` // 消息额外数据存放的位置
}

type CommResponse struct {
	RequestId string `json:"requestId"` // 本次消息的ID
}

func NewWsMessage(action enum.WsMsgAction, requestId string, body interface{}) *WebsocketMessage {
	return &WebsocketMessage{
		Action:    action,
		RequestId: requestId,
		Body:      body,
	}
}

func NewWsHeartBertMessage(requestId string) *WebsocketMessage {
	return &WebsocketMessage{
		Action:    enum.ActionHeartBeat,
		RequestId: requestId,
	}
}

func NewWsSendHeartBertMessage(requestId string) *WebsocketMessage {
	return &WebsocketMessage{
		Action:    enum.ActionConnInfo,
		RequestId: requestId,
	}
}

func NewWsAuthInvalidMessage(requestId string) *WebsocketMessage {
	return &WebsocketMessage{
		Action:    enum.ActionAuthInvalid,
		RequestId: requestId,
	}
}

func NewWsErrorInfoMessage(requestId string, errInfo interface{}) *WebsocketMessage {
	return &WebsocketMessage{
		Action:    enum.ActionErrorInfo,
		RequestId: requestId,
		Body:      errInfo,
	}
}

func NewWsConnInfoMessage(requestId string, body interface{}) *WebsocketMessage {
	return &WebsocketMessage{
		Action:    enum.ActionBRtcInfo,
		RequestId: requestId,
		Body:      body,
	}
}

func NewWsBRtcMessage(requestId string, body interface{}) *WebsocketMessage {
	return &WebsocketMessage{
		Action:    enum.ActionBRtcInfo,
		RequestId: requestId,
		Body:      body,
	}
}

type RspMessage struct {
	Global   string      `json:"global"`             // 表示通用错误处理。一般会由全局错误处理以Dialog的形式展示
	Redirect string      `json:"redirect,omitempty"` // 重定向地址
	Field    interface{} `json:"field,omitempty"`    // 表示表单字段错误。在表单提交给后端后，如果某些字段未能通过后端校验，则返回对应字段的错误信息。（field 中的key与空间中的name相对应，错误信息就可以展示在空间后面
}

type CommRsp struct {
	Code    int        `json:"code"`
	Success bool       `json:"success"`
	Message RspMessage `json:"message"`
}

type CommDataRsp struct {
	CommRsp
	Result interface{} `json:"result"`
}

type CommDataServiceRsp struct {
	Code    int                       `json:"code"`
	Message CommDataServiceRspMessage `json:"message"`
	Result  interface{}               `json:"result"`
}
type CommDataServiceRspMessage struct {
	Global   string `json:"global"`
	Redirect string `json:"redirect"`
}

func NewCommRsp(code int, msg string) CommRsp {
	return CommRsp{
		Code:    code,
		Success: code == 0,
		Message: RspMessage{
			Global:   msg,
			Redirect: "",
			Field:    nil,
		},
	}
}

func NewCommDataRsp(code int, msg string, data interface{}) CommDataRsp {
	return CommDataRsp{
		CommRsp: NewCommRsp(code, msg),
		Result:  data,
	}
}

func NewSuccessRsp(data interface{}) CommDataRsp {
	return CommDataRsp{
		CommRsp: NewCommRsp(0, "success"),
		Result:  data,
	}
}

func NewCommServiceRsp(code int, msg string, data interface{}) CommDataServiceRsp {
	return CommDataServiceRsp{
		Code:    code,
		Message: CommDataServiceRspMessage{Global: msg, Redirect: ""},
		Result:  data,
	}
}
func NewSuccessServiceRsp(data interface{}) CommDataServiceRsp {
	return CommDataServiceRsp{
		Code:    0,
		Message: CommDataServiceRspMessage{Global: "", Redirect: ""},
		Result:  data,
	}
}
