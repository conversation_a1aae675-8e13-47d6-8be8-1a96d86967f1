package proto

type RiskControlShortAudioRequest struct {
	AudioUrl string `json:"audioUrl"` // 视频URL，字符串数组类型，JSON字段名为"StringArray"
}

type RiskControlShortAudioResponse struct {
	LogID string                       `json:"logId"`          // 请求唯一id
	Data  RiskControlAudioResponseData `json:"data,omitempty"` // 音频信息
}

type RiskControlAudioResponseData struct {
	TaskId                   string                                     `json:"taskId"`                   // 任务id
	Status                   string                                     `json:"status"`                   // 业务状态
	ErrorCode                int64                                      `json:"error_code,omitempty"`     // 错误提示码，失败才返回，成功不返回
	ErrorMsg                 string                                     `json:"error_msg,omitempty"`      // 错误提示信息，失败才返回，成功不返回
	Conclusion               string                                     `json:"conclusion,omitempty"`     // 审核结果
	ConclusionType           int                                        `json:"conclusionType,omitempty"` // 审核结果类型
	ConclusionTypeGroupInfos []RiskControlAudioConclusionTypeGroupInfos `json:"conclusionTypeGroupInfos"` // 审核结果类型描述
}

type RiskControlAudioConclusionTypeGroupInfos struct {
	Text  string   `json:"text"` // 语音识别文本
	Msg   string   `json:"msg"`  // 不合规项描述信息
	Words []string `json:"words"`
}

type RiskControlShortAudioCensorResponse struct {
	LogID          int64                                      `json:"log_id"`         // 请求唯一id，用于问题排查
	ErrorCode      int64                                      `json:"error_code"`     // 审核服务异常错误码
	ErrorMsg       string                                     `json:"error_msg"`      // 审核服务异常错误提示信息
	Conclusion     string                                     `json:"conclusion"`     // 音频任务审核结果描述
	ConclusionType int64                                      `json:"conclusionType"` // 音频任务审核结果值
	Sn             string                                     `json:"sn"`             // 音频数据唯一标识，系统内部产生
	AudioID        string                                     `json:"audioId"`        // 用户侧音频唯一标识
	Account        string                                     `json:"account"`        // 用户侧音频备注
	RawText        []string                                   `json:"rawText"`        // 语音识别文本结果，字符串数组
	Data           []RiskControlLongAudioCensorPullDataDetail `json:"data"`           // 语音识别文本审核结果
	AudioData      []RiskControlAudioData                     `json:"audioData"`      // 音频数据详情
}

// 声纹审核结果，包括声纹检测、音频质量的结果，参见audioData的结构
type RiskControlAudioData struct {
	Msg            string `json:"msg"`            // 不合规项描述信息
	Conclusion     string `json:"conclusion"`     // 音频任务审核结果描述
	ConclusionType int64  `json:"conclusionType"` // 音频任务审核结果值
	Type           int64  `json:"type"`           // 审核主类型
	SubType        int64  `json:"subType"`        // 审核子类型
}
