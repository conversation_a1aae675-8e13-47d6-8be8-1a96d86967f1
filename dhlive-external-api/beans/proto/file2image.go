package proto

import "dhlive-external-api/beans/enum"

type File2ImageRequest struct {
	FileID string `json:"fileid"`
}

type File2ImageSetDraftIdRequest struct {
	FileID  string `json:"fileid"`
	DraftId string `json:"draftId"`
}

type File2ImageRequestV2 struct {
	FileID string `json:"fileId"`
}

type File2ImageResponse struct {
	PageCount        int                 `json:"pageCount"`                  // 图片总个数
	ImageInfos       string              `json:"imageInfos"`                 // json串，存放多个image的url地址
	Status           enum.TaskStatus     `json:"status"`                     // 业务状态
	RequestId        string              `json:"requestId"`                  // 请求id
	CensorResultList []*CensorResultList `json:"censorResultList,omitempty"` // 审核结果列表
}

type CensorResultList struct {
	Index    int      `json:"index"`
	Url      string   `json:"url"`
	Messages []string `json:"messages"`
}

type FileFormatCheckResponse struct {
	FileID                 string `json:"fileid"`
	FileFormatIsSupported  bool   `json:"fileFormatIsSupported"`
	FileSizeIsWithinLimit  bool   `json:"fileSizeIsWithinLimit"`
	PageCountIsWithinLimit bool   `json:"pageCountIsWithinLimit"`
	RequestId              string `json:"requestId"`
}
type FileFormatCheckResponseV2 struct {
	FileID                 string `json:"fileId"`
	FileFormatIsSupported  bool   `json:"fileFormatIsSupported"`
	FileSizeIsWithinLimit  bool   `json:"fileSizeIsWithinLimit"`
	PageCountIsWithinLimit bool   `json:"pageCountIsWithinLimit"`
	RequestId              string `json:"requestId"`
}

// 文件转图片 url list
type File2ImageInfoList struct {
	ImageInfos []File2ImageInfo `json:"imageInfos"`
}

// 文件转图片 url
type File2ImageInfo struct {
	Index  int    `json:"index"`
	Width  int    `json:"width"`
	Height int    `json:"height"`
	Url    string `json:"url"`
	Remark string `json:"remark,omitempty"`
	ErrMsg string `json:"errMsg,omitempty"`
}

type PPTFileInfo struct {
	SlideSize PPTFileSlideInfo `json:"slide_size"`
	Notes     []FileNotes      `json:"notes"`
}

type PPTFileSlideInfo struct {
	Width_inches  float64 `json:"width_inches"`
	Height_inches float64 `json:"height_inches"`
}

type FileNotes struct {
	Index int    `json:"index"` // 图片总个数
	Note  string `json:"note"`  // json串，存放多个image的url地址
}
