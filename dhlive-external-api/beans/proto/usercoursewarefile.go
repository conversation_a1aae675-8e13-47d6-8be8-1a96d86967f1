package proto

type GetUserCoursewareFileListResponse struct {
	LogId     string                          `json:"logId"`
	PageCount int                             `json:"pageCount"`
	FileList  []GetUserCoursewareFileListItem `json:"fileList"`
}

type GetUserCoursewareFileListItem struct {
	Name       string `json:"name"`
	FileId     string `json:"fileId"`
	ImageInfos string `json:"imageInfos"`
}

// type UserCoursewareFileItem struct {
// 	Name       string           `json:"name"`
// 	FileId     string           `json:"fileId"`
// 	ImageInfos []File2ImageInfo `json:"imageInfos"`
// }

type DeleteUserCoursewareFileRequest struct {
	FileId      string `json:"fileId"`
	IndexList   []int  `json:"indexList"`
	IsDeleteAll bool   `json:"isDeleteAll"`
}
