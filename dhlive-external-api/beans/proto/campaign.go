package proto

type GetShareLinkRequst struct {
	BaseUrl      string `json:"baseUrl" form:"baseUrl" binding:"required"`
	CampaignType string `json:"campaignType" form:"campaignType" binding:"required"`
}

type GetShareLinkResponse struct {
	ShareLink string `json:"shareLink"`
	LogID     string `json:"logID"`
}

type BindInviteRequest struct {
	InviteCode string `json:"inviteCode" binding:"required"`
}

type BindInviteResponse struct {
	LogID string `json:"logID"`
}

type AddKafakaQueueRequest struct {
	TopicName string `json:"topicName" binding:"required"`
	Key       string `json:"key" binding:"required"`
	Value     string `json:"value" binding:"required"`
}

type AddKafakaQueueResponse struct {
	LogID string `json:"logID"`
}
