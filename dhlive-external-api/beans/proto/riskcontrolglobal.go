package proto

type Detail struct {
	ErrorCode   int64      `json:"error_code,omitempty"`  // 内层错误提示码，底层服务失败才返回
	ErrorMsg    string     `json:"error_msg,omitempty"`   // 内层错误提示信息
	Type        int64      `json:"type"`                  // 结果具体命中的模型类型
	SubType     int64      `json:"subType"`               // 审核子类型
	Msg         string     `json:"msg"`                   // 不合规项描述信息
	Probability float64    `json:"probability"`           // 不合规项置信度
	DatasetName string     `json:"datasetName,omitempty"` // 命中所属自定义数据集名称
	Stars       []Star     `json:"stars,omitempty"`       // 命中人脸相关信息
	Hits        []Hit      `json:"hits,omitempty"`        // 图文审核命中信息
	Codes       []string   `json:"codes,omitempty"`       // 二维码或条形码识别结果
	Location    []Location `json:"location,omitempty"`    // 命中内容的位置相关信息
}

// 定义人脸相关信息结构体
type Star struct {
	Name        string  `json:"name"`        // 人名
	Probability float64 `json:"probability"` // 人脸相似度
	DatasetName string  `json:"datasetName"` // 人脸所属自定义数据集名称
}

// 定义图文审核命中信息结构体
type Hit struct {
	Words             []string          `json:"words,omitempty"`             // 检文本命中词库的关键词
	Probability       float64           `json:"probability,omitempty"`       // 不合规项置信度
	DatasetName       string            `json:"datasetName,omitempty"`       // 违规项目所属自定义名称
	ModelHitPositions [][]float64       `json:"modelHitPositions,omitempty"` // 送检文本命中模型的详细信息
	WordHitPositions  []WordHitPosition `json:"wordHitPositions,omitempty"`  // 送检文本命中词库的详细信息
	ModelName         string            `json:"modelName,omitempty"`         // 命中自定义模型名称
	Score             float64           `json:"score,omitempty"`             // 命中自定义模型置信度
	ModelId           []int64           `json:"modelId,omitempty"`           // 命中自定义模型ID
	Label             string            `json:"label,omitempty"`             // 命中自定义模型标签名称
}

// WordHitPosition 表示送检文本命中词库的详细信息
type WordHitPosition struct {
	Keyword   string  `json:"keyword"`   // 送检文本命中词库的关键词
	Positions [][]int `json:"positions"` // 关键词在送检原文中的位置：[起始位置, 结束位置]
	Label     string  `json:"label"`     // 关键词命中的细分标签
}

// 定义位置相关信息结构体
type Location struct {
	Top      float64 `json:"top"`      // 命中内容顶部与图片上边界的距离
	Left     float64 `json:"left"`     // 命中内容左侧与图片左边界的距离
	Width    float64 `json:"width"`    // 命中内容的宽度
	Height   float64 `json:"height"`   // 命中内容的高度
	Rotation float64 `json:"rotation"` // 相对于竖直方向的顺时针旋转角度
}

type ConclusionTypeGroupInfo struct {
	Msg             string        `json:"msg"`
	SubTypeInfoList []SubTypeInfo `json:"subTypeInfoList"`
	TypeInfo        TypeInfo      `json:"typeInfo"`
}

type SubTypeInfo struct {
	Timestamp int64  `json:"timestamp"`
	SubType   string `json:"subType"`
}

type TypeInfo struct {
	Type string `json:"type"`
}

// RequestInfo 结构体包含了请求的唯一ID和任务的唯一标识
type RiskControlAsyncSubmitResponse struct {
	LogID    string                               `json:"logId"`    // 请求唯一id
	DataList []RiskControlAsyncSubmitResponseItem `json:"dataList"` // 本次任务的唯一标识，JSON字段名为"taskId"
}

type RiskControlAsyncSubmitResponseItem struct {
	ErrorCode string `json:"errorCode,omitempty"` // 错误提示码，失败才返回，成功不返回
	ErrorMsg  string `json:"errorMsg,omitempty"`  // 错误提示信息，失败才返回，成功不返回
	TaskId    string `json:"taskId"`              // 本次任务的唯一标识，JSON字段名为"taskId"
}
