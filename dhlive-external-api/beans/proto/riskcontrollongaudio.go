package proto

type RiskControlLongAudioSubmitRequest struct {
	AudioUrl []string `json:"audioUrl"`
}

type RiskControlLongAudioPullRequest struct {
	TaskIds []string `json:"taskIds"`
}

type RiskControlLongAudioPullResponse struct {
	LogID    string                                 `json:"logId"`    // 请求唯一id
	DataList []RiskControlLongAudioPullDataListItem `json:"dataList"` // 审核结果列表
}

// DataListItem 表示审核结果列表中的单个项目
type RiskControlLongAudioPullDataListItem struct {
	Status         string                         `json:"status"`                   // 业务状态
	ErrorCode      int64                          `json:"error_code,omitempty"`     // 错误提示码，失败才返回，成功不返回
	ErrorMsg       string                         `json:"error_msg,omitempty"`      // 错误提示信息，失败才返回，成功不返回
	Conclusion     string                         `json:"conclusion,omitempty"`     // 审核结果
	ConclusionType int                            `json:"conclusionType,omitempty"` // 审核结果类型
	Data           []RiskControlLongAudioPullData `json:"data,omitempty"`           // 音频信息
}

type RiskControlLongAudioPullData struct {
	Text  string   `json:"text"` // 语音识别文本
	Msg   string   `json:"msg"`  // 不合规项描述信息
	Words []string `json:"words"`
}

type RiskControlLongAudioCensorSubmitResponse struct {
	ErrorCode int64                                       `json:"error_code,omitempty"` // 错误提示码，失败才返回，成功不返回
	ErrorMsg  string                                      `json:"error_msg,omitempty"`  // 错误提示信息，失败才返回，成功不返回
	LogId     int64                                       `json:"logId"`                // 请求唯一id，用于问题排查
	Msg       string                                      `json:"msg,omitempty"`        // 详细描述结果
	Ret       string                                      `json:"ret"`                  // 响应状态码，可取值：0处理成功，其他为处理失败
	Data      RiskControlLongAudioCensorSubmitDataDetails `json:"data"`                 // 结果详情
}
type RiskControlLongAudioCensorSubmitDataDetails struct {
	TaskId  string `json:"taskId"`  // 长视频的任务taskId
	AudioId int64  `json:"audioId"` // 长视频的音频id
}

type RiskControlLongAudioCensorPullResponse struct {
	LogID          int64                                      `json:"log_id"`         // 请求唯一id，用于问题排查
	ErrorCode      int64                                      `json:"error_code"`     // 审核服务异常错误码
	ErrorMsg       string                                     `json:"error_msg"`      // 审核服务异常错误提示信息
	Conclusion     string                                     `json:"conclusion"`     // 音频任务审核结果描述
	ConclusionType int64                                      `json:"conclusionType"` // 音频任务审核结果值
	AudioID        string                                     `json:"audioId"`        // 用户侧音频唯一标识
	Account        string                                     `json:"account"`        // 用户侧音频备注
	RawText        []string                                   `json:"rawText"`        // 语音识别文本结果，字符串数组
	TaskID         string                                     `json:"taskId"`         // 透传提交任务接口返回参数中的taskId
	Data           []RiskControlLongAudioCensorPullDataDetail `json:"data"`           // 语音识别文本审核结果
	TaskInfo       RiskControlLongAudioCensorPullTaskInfo     `json:"taskInfo"`       // 语音识别文本审核结果的原始JSON
	AudioData      []RiskControlAudioData                     `json:"audioData"`      // 音频数据详情
}

type RiskControlLongAudioCensorPullDataDetail struct {
	Text           string                                          `json:"text"`           // 分段文本结果
	StartTime      int64                                           `json:"startTime"`      // 文本对应音频文件的起始时间戳
	EndTime        int64                                           `json:"endTime"`        // 文本对应音频文件的终止时间戳
	Conclusion     string                                          `json:"conclusion"`     // 文本审核结果
	ConclusionType int                                             `json:"conclusionType"` // 文本审核结果类型
	AuditData      []RiskControlLongAudioCensorPullAuditDataDetail `json:"auditData"`      // 文本审核结果详情
}

type RiskControlLongAudioCensorPullAuditDataDetail struct {
	Type    int64  `json:"type"`    // 审核主类型
	SubType int64  `json:"subType"` // 审核子类型
	Msg     string `json:"msg"`     // 不合规项描述信息
	Hits    []Hit  `json:"hits"`    // 命中信息
}

type RiskControlLongAudioCensorPullTaskInfo struct {
	TaskDuration int64 `json:"taskDuration"` // 单位为秒
}
