package proto

type RiskControlShortVideoSubmitRequest struct {
	VideoURL string `json:"videoURL"` // 视频URL，字符串数组类型，JSON字段名为"StringArray"
}

// Response 表示整个响应的结构体
type RiskControlShortVideoResponse struct {
	LogID string                            `json:"logId"` // 请求唯一id
	Data  RiskControlShortVideoDataListItem `json:"data"`  // 审核结果列表
}

// DataListItem 表示审核结果列表中的单个项目
type RiskControlShortVideoDataListItem struct {
	TaskId                   string                    `json:"taskId"`                             // 任务id
	Status                   string                    `json:"status"`                             // 业务状态
	ErrorCode                int64                     `json:"error_code,omitempty"`               // 错误提示码，失败才返回，成功不返回
	ErrorMsg                 string                    `json:"error_msg,omitempty"`                // 错误提示信息，失败才返回，成功不返回
	Conclusion               string                    `json:"conclusion,omitempty"`               // 审核结果
	ConclusionType           int                       `json:"conclusionType,omitempty"`           // 审核结果类型
	ConclusionTypeGroupInfos []ConclusionTypeGroupInfo `json:"conclusionTypeGroupInfos,omitempty"` // 审核结论汇总
}

type RiskControlShortVideoCensorResponse struct {
	LogId                    int64                     `json:"logId"`                              // 请求唯一id，用于问题排查
	ErrorCode                int64                     `json:"error_code,omitempty"`               // 错误提示码，失败才返回，成功不返回
	ErrorMsg                 string                    `json:"error_msg,omitempty"`                // 错误提示信息，失败才返回，成功不返回
	Conclusion               string                    `json:"conclusion"`                         // 审核结果描述
	ConclusionType           int64                     `json:"conclusionType"`                     // 审核结果类型
	IsHitMd5                 bool                      `json:"isHitMd5"`                           // 是否命中md5，true表示命中，false表示未命中
	Msg                      string                    `json:"msg,omitempty"`                      // 命中MD5提示
	Frames                   []Frame                   `json:"frames"`                             // 帧审核明细
	ConclusionTypeGroupInfos []ConclusionTypeGroupInfo `json:"conclusionTypeGroupInfos,omitempty"` // 审核结论汇总
}
