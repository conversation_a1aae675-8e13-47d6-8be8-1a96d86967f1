package proto

type TranslateTextRequest struct {
	From  string `json:"from"`  // 源语种
	To    string `json:"to"`    // 目标语种
	ReqId string `json:"reqId"` // 请求的唯一id
	TextList []string `json:"textList"` // 文本列表
}

type TranslateTextResponse struct {
	ReqID       string             `json:"reqId"`       // 请求的唯一ID
	TransResult []TransResultModel `json:"transResult"` // 翻译结果
}

type TransResultModel struct {
	TransID   string `json:"transId,omitempty"` //  这一对翻译的唯一标识
	Src       string `json:"src"`               // 源文本
	Dst       string `json:"dst"`               // 目标文本
	From      string `json:"from"`              // 源语种
	To        string `json:"to"`                // 目标语种
	ErrorCode string `json:"errorCode"`
	ErrorMsg  string `json:"errorMsg"`
}

type TransApiRequest struct {
	Q     string `json:"q"`     // 翻译文本
	From  string `json:"from"`  // 源语种
	To    string `json:"to"`    // 目标语种
	AppId string `json:"appid"` // appId
	Salt  string `json:"salt"`  // 随机字符串
	Sign  string `json:"sign"`  // 签名字符串
}

type TransApiResponse struct {
	SeqID       int                `json:"seqId"`        // 顺序ID
	TransResult []TransResultModel `json:"trans_result"` // 翻译结果
	From        string             `json:"from"`         // 源语种
	To          string             `json:"to"`           // 目标语种
	ErrorCode   string             `json:"error_code"`
	ErrorMsg    string             `json:"error_msg"`
}
