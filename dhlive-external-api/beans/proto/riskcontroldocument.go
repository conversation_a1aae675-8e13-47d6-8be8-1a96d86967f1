package proto

type RiskControlDocumentSubmitRequest struct {
	AppID        int64  `json:"appid,omitempty"`        // 应用ID
	StrategyID   int64  `json:"strategyId,omitempty"`   // 策略ID
	NoticeURL    string `json:"noticeUrl,omitempty"`    // 通知地址
	FileName     string `json:"fileName"`               // 文件名称
	FilePassword string `json:"filePassword,omitempty"` // 文件密码
	URL          string `json:"url,omitempty"`          // 文档地址
	FileBase64   string `json:"fileBase64,omitempty"`   // 文档的base64编码
	DetectType   int    `json:"detectType,omitempty"`   // 检测类型
	SubEvents    string `json:"subEvents,omitempty"`    // 订阅事件列表
}

type RiskControlDocumentSubmitResponse struct {
	LogID int64                `json:"logId"` // 请求唯一id，用于问题排查
	Msg   string               `json:"msg"`   // 详细描述结果
	Ret   string               `json:"ret"`   // 响应状态码
	Data  DocumentResponseData `json:"data"`  // 结果详情
}

type DocumentResponseData struct {
	TaskID string `json:"taskId"` // 本次任务的唯一标识
}

type RiskControlDocumentAsyncResult struct {
	TaskID         int64           `json:"taskId"`
	ErrorCode      int             `json:"error_code,omitempty"`
	ErrorMsg       string          `json:"error_msg,omitempty"`
	Conclusion     string          `json:"conclusion,omitempty"`
	ConclusionType int             `json:"conclusionType,omitempty"`
	AuditList      []DocumentAudit `json:"auditList,omitempty"`
}

type DocumentAudit struct {
	Conclusion     string           `json:"conclusion,omitempty"`
	ConclusionType int              `json:"conclusionType,omitempty"`
	PageNum        int              `json:"pageNum"`
	DataURL        string           `json:"dataUrl"`
	DataType       int              `json:"dataType"`
	Data           []DocumentDetail `json:"data,omitempty"`
}

type DocumentDetail struct {
	ErrorCode   int           `json:"error_code,omitempty"`
	ErrorMsg    string        `json:"error_msg,omitempty"`
	Type        int           `json:"type,omitempty"`
	SubType     int           `json:"subType,omitempty"`
	Msg         string        `json:"msg,omitempty"`
	Probability float64       `json:"probability,omitempty"`
	DatasetName string        `json:"datasetName,omitempty"`
	Codes       []string      `json:"codes,omitempty"`
	Hits        []DocumentHit `json:"hits,omitempty"`
}

type DocumentHit struct {
	Probability float64  `json:"probability,omitempty"`
	DatasetName float64  `json:"datasetName,omitempty"` // Note: This should probably be a string, not a float64. Check the actual data type.
	Words       []string `json:"words,omitempty"`
	ModelName   string   `json:"modelName,omitempty"`
	Score       float64  `json:"score,omitempty"`
	ModelID     []string `json:"modelId,omitempty"` // Assuming modelId is a string array based on the provided information. Adjust if necessary.
	Label       string   `json:"label,omitempty"`
}

// 风控文档结果回复结构体
type RiskControlDocumentResponse struct {
	LogID int64  `json:"logId"` // 请求唯一id，用于问题排查
	Msg   string `json:"msg"`   // 详细描述结果
	Ret   string `json:"ret"`   // 响应状态码
}
