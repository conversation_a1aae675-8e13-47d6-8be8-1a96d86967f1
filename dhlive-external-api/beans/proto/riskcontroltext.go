package proto

type RiskControlTextRequest struct {
	Texts []string `json:"texts"` // 待审核文本字符串
}

type RiskControlTextResponse struct {
	LogID    string                `json:"logId"`    // 请求唯一id
	DataList []RiskControlTextItem `json:"dataList"` // 审核列表结果，使用空接口以接受任意类型数据
}

type RiskControlTextItem struct {
	TaskID         string                      `json:"taskId"`              // 请求唯一id
	ErrorCode      int64                       `json:"errorCode,omitempty"` // 错误提示码，失败才返回，成功不返回
	ErrorMsg       string                      `json:"errorMsg,omitempty"`  // 错误提示信息，失败才返回，成功不返回
	Conclusion     string                      `json:"conclusion"`          // 审核结果，可取值描述：合规、不合规、疑似、审核失败
	ConclusionType int64                       `json:"conclusionType"`      // 审核结果类型
	Data           []RiskControlTextItemDetail `json:"data,omitempty"`      // 不合规/疑似/命中白名单项详细信息
}

type RiskControlTextItemDetail struct {
	ErrorCode int64    `json:"errorCode,omitempty"` // 错误提示码，失败才返回，成功不返回
	ErrorMsg  string   `json:"errorMsg,omitempty"`  // 错误提示信息，失败才返回，成功不返回
	Msg       string   `json:"msg"`                 // 不合规项描述信息
	Words     []string `json:"words"`
}

type TextCensorResponse struct {
	LogId          int64                `json:"log_id"`                   // 请求唯一id
	ErrorCode      int64                `json:"error_code,omitempty"`     // 错误提示码
	ErrorMsg       string               `json:"error_msg,omitempty"`      // 错误提示信息
	Conclusion     string               `json:"conclusion,omitempty"`     // 审核结果
	ConclusionType int64                `json:"conclusionType,omitempty"` // 审核结果类型
	Data           []TextCensorDataItem `json:"data,omitempty"`           // 不合规/疑似/命中白名单项详细信息
}

type TextCensorDataItem struct {
	ErrorCode      int64                 `json:"error_code,omitempty"`     // 内层错误提示码
	ErrorMsg       string                `json:"error_msg,omitempty"`      // 内层错误提示信息
	Type           int                   `json:"type"`                     // 审核主类型
	SubType        int                   `json:"subType"`                  // 审核子类型
	Conclusion     string                `json:"conclusion,omitempty"`     // 审核结果
	ConclusionType int64                 `json:"conclusionType,omitempty"` // 审核结果类型
	Msg            string                `json:"msg,omitempty"`            // 不合规项描述信息
	Hits           []TextCensorHitDetail `json:"hits,omitempty"`           // 送检文本违规原因的详细信息
}

type TextCensorHitDetail struct {
	Probability       float64           `json:"probability,omitempty"`       // 不合规项置信度
	DatasetName       string            `json:"datasetName,omitempty"`       // 违规项目所属数据集名称
	Words             []string          `json:"words,omitempty"`             // 送检文本命中词库的关键词
	Details           []string          `json:"details,omitempty"`           // 恶意推广中命中的联系方式细分标签名称
	ModelHitPositions [][]float64       `json:"modelHitPositions,omitempty"` // 送检文本命中模型的详细信息
	WordHitPositions  []WordHitPosition `json:"wordHitPositions,omitempty"`  // 送检文本命中词库的详细信息
}
