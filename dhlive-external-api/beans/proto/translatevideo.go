package proto

const (
	TransStatusWaiting = "WAITING"
	TransStatusRunning = "RUNNING"
	TransStatusSuccess = "SUCCESS"
	TransStatusFailed  = "FAILED"
)

type TranslateVideoRequest struct {
	DraftId   string           `json:"draftId"`       // 草稿id
	TransList []TransListModel `json:"translateList"` // 翻译目标列表信息
}

type TranslateVideoRetryRequest struct {
	DraftId string `json:"draftId"` // 草稿id
}

type TransListModel struct {
	Lan    string `json:"lan"`    // 目标语种code
	Timbre string `json:"timbre"` // 目标音色的配置信息
}

type TranslateVideoCallbackRequest struct {
	Code        int                           `json:"code"`
	Message     MessageModel                  `json:"message"`
	ResultModel TranslateVideoCallResultModel `json:"result"`
}

type TranslateVideoCallResultModel struct {
	DraftId string `json:"draftId"`
	PodId   string `json:"podId"`
	Tracks  string `json:"tracks"`
}

type MessageModel struct {
	Global string `json:"global"`
}

type CopyDraftRequest struct {
	Name     string `json:"name"`
	DraftId  string `json:"draftId"`
	Language string `json:"language"`
}

type CopyDraftResponse struct {
	Code        int                  `json:"code"`
	Message     MessageModel         `json:"message"`
	ResultModel CopyDraftResultModel `json:"result"`
}
type CopyDraftResultModel struct {
	DraftId string `json:"draftId"`
}
