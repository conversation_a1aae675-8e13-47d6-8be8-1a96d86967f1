package routers

import (
	dh_user "dhlive-external-api/handler/dh-user"
	"dhlive-external-api/handler/feedback"
	"dhlive-external-api/handler/figure"
	"dhlive-external-api/handler/speechtotext"
	"dhlive-external-api/handler/translate"

	"github.com/gin-gonic/gin"
)

// Routers 路由列表
func Routers(e *gin.Engine) {
	// document := figure.NewRiskControlDocument()

	apiV1 := e.Group("/api/digitalhuman/external/riskcontrol/v1")
	// ----- 风控 API ----- //
	{
		image := figure.NewRiskControlImage()
		text := figure.NewRiskControlText()
		audio := figure.GetRiskControlLongAudio()
		video := figure.GetRiskControlLongVideo()
		shortVideo := figure.GetRiskControlShortVideo()
		shortAudio := figure.GetRiskControlShortAudio()
		apiV1.POST("/figure/texts", dh_user.DhUser<PERSON>heck, text.HandleRequest)
		apiV1.POST("/figure/images", dh_user.DhUserCheck, image.HandleRequest)
		apiV1.POST("/figure/video", dh_user.DhUserCheck, shortVideo.CensorVideo)
		apiV1.POST("/figure/audio", dh_user.DhUserCheck, shortAudio.CensorAudio)
		apiV1.POST("/figure/audio/async/submit", dh_user.DhUserCheck, audio.SubmitAudioCensor)
		apiV1.POST("/figure/audio/async/pull", dh_user.DhUserCheck, audio.PullAudioCensor)
		apiV1.POST("/figure/videos/async/submit", dh_user.DhUserCheck, video.SubmitVideoCensor)
		apiV1.POST("/figure/videos/async/pull", dh_user.DhUserCheck, video.PullVideoCensorResult)

		apiV1.POST("/internal/figure/texts", text.HandleRequest)
		apiV1.POST("/internal/figure/images", image.HandleRequest)
		apiV1.POST("/internal/figure/video", shortVideo.CensorVideo)
		apiV1.POST("/internal/figure/audio", shortAudio.CensorAudio)

		// apiV1.POST("/figure/document", dh_user.DhUserCheck, document.HandleRequest)
	}

	apiInternalV1 := e.Group("/api/digitalhuman/external/riskcontrol/internal/v1")
	{
		apiInternalV1.POST("/figure/list", figure.GetRiskControlItemList)
		apiInternalV1.POST("/figure/custom", figure.CustomRiskControlItem)
	}

	// faceV1 := e.Group("/api/digitalhuman/external/face/v1")
	// {
	// 	faceV1.POST("/figure/match", dh_user.DhUserCheck)
	// 	faceV1.POST("/figure/stop", dh_user.DhUserCheck)
	// }

	watermarkV1 := e.Group("/watermark/api/v1")
	// ----- 视频水印接口 api ----- //
	{
		watermark := figure.GetVideoWatermark()
		// 水印视频提交
		watermarkV1.POST("/figure/video", watermark.HandleRequest)
		watermarkV1.POST("/figure/video/result", watermark.GetWaterMarkResult)
		// 水印视频完成回调
		// watermarkV1.POST("/figure/video/callback", watermark.WaterMarkCallback)
		// 提取水印
		// watermarkV1.POST("/figure/video/extract/create", watermark.CreateWaterMarkExtraction)
		// watermarkV1.POST("/figure/video/extract/result", watermark.GetWaterMarkExtractionResult)

		watermarkV1.POST("/figure/video/callback/test", watermark.TestCallback)
	}

	file2ImageV1 := e.Group("/api/digitalhuman/file2image/v1")
	{
		file2image := figure.NewFile2Image()

		file2ImageV1.POST("/figure/upload", dh_user.DhUserCheck, file2image.CheckFile)
		file2ImageV1.POST("/figure/parse", dh_user.DhUserCheck, file2image.HandlerRequest)
		file2ImageV1.POST("/figure/parse/result", dh_user.DhUserCheck, file2image.GetParseResult)
		file2ImageV1.POST("/figure/parse/stop", dh_user.DhUserCheck, file2image.StopParseResult)
		file2ImageV1.PUT("/figure/set/draftId", dh_user.DhUserCheck, file2image.SetDraftId)
		// file2ImageV1.POST("/figure/upload", file2image.CheckFile)
		// file2ImageV1.POST("/figure/parse", file2image.HandlerRequest)
		// file2ImageV1.POST("/figure/parse/result", file2image.GetParseResult)
	}

	file2ImageV2 := e.Group("/api/digitalhuman/file2image/v2")
	{
		file2ImageV2.POST("/figure/upload", dh_user.DhUserCheck, figure.UploadCoursewareFile)
		file2ImageV2.POST("/figure/parse/result", dh_user.DhUserCheck, figure.GetParseResultV2)
		file2ImageV2.POST("/figure/courseware/list", dh_user.DhUserCheck, figure.GetUserCoursewareFile)
		file2ImageV2.POST("/figure/courseware/remove", dh_user.DhUserCheck, figure.DeletedAtFile2Image)
		file2ImageV2.POST("/figure/courseware/stop", dh_user.DhUserCheck, figure.StopParseResultV2)
	}

	miniprogramV1 := e.Group("/api/digitalhuman/external/miniprogram/v1")
	{
		miniprogramV1.GET("/figure/id", dh_user.DhUserCheck, figure.CreatMiniProgramID)
		miniprogramV1.POST("/figure/copy", dh_user.DhUserCheck, figure.CopyProperty)
	}

	translateV1 := e.Group("/api/digitalhuman/external/translate/v1")
	{
		translateV1.POST("/text", dh_user.DhUserCheck, translate.TranslateText)
		translateV1.POST("/texttest", translate.TranslateText)
	}

	feedbackV1 := e.Group("/api/digitalhuman/external/feedback/v1")
	{
		// 反馈类型增删改查（暂无 增、删、改需求， 方便后期修改反馈类型）
		feedbackV1.GET("/user-feedback/tags", dh_user.DhUserCheckByPass, feedback.GetFeedBackTypes)
		feedbackV1.POST("/user-feedback/tag", feedback.AddFeedbackType)
		feedbackV1.PUT("/user-feedback/tag", feedback.UpdateFeedbackType)
		feedbackV1.DELETE("/user-feedback/tag", feedback.DeleteFeedbackType)

		// 用户反馈相关接口
		feedbackV1.POST("/user-feedback/list", feedback.QueryUserFeedBack)
		feedbackV1.POST("/user-feedback", dh_user.DhUserCheckByPass, feedback.AddUserFeedBack)
		feedbackV1.PUT("/user-feedback", feedback.UpdateUserFeedBack)
		feedbackV1.POST("/user-feedback/delete", feedback.DeleteUserFeedBack)
	}
	{
		api := e.Group("/api/digitalhuman/external/asr/v1")
		api.POST("/speech2text", dh_user.DhUserCheck, speechtotext.SpeechToText)
	}
}
