package routers

import (
	"acg-ai-go-common/beans/proto2"
	"acg-ai-go-common/goredis"
	"acg-ai-go-common/logger"
	"dhlive-external-api/handler/campaign"
	"dhlive-external-api/handler/consult"
	"dhlive-external-api/handler/country"
	dh_user "dhlive-external-api/handler/dh-user"
	"dhlive-external-api/handler/feedback"
	"dhlive-external-api/handler/figure"
	"dhlive-external-api/handler/speechtotext"
	"dhlive-external-api/handler/translate"
	"net/http"
	"time"

	"github.com/go-redis/redis_rate/v10"

	"github.com/gin-gonic/gin"
)

// Routers 路由列表
func Routers(e *gin.Engine) {
	// document := figure.NewRiskControlDocument()

	apiV1 := e.Group("/api/digitalhuman/external/riskcontrol/v1")
	// ----- 风控 API ----- //
	{
		image := figure.NewRiskControlImage()
		text := figure.NewRiskControlText()
		audio := figure.GetRiskControlLongAudio()
		video := figure.GetRiskControlLongVideo()
		shortVideo := figure.GetRiskControlShortVideo()
		shortAudio := figure.GetRiskControlShortAudio()
		apiV1.POST("/figure/texts", dh_user.DhUserCheck, text.CensorTexts)
		apiV1.POST("/figure/images", dh_user.DhUserCheck, image.CensorImage)
		apiV1.POST("/figure/video", dh_user.DhUserCheck, shortVideo.CensorVideo)
		apiV1.POST("/figure/audio", dh_user.DhUserCheck, shortAudio.CensorAudio)
		apiV1.POST("/figure/audio/async/submit", dh_user.DhUserCheck, audio.SubmitAudioCensor)
		apiV1.POST("/figure/audio/async/pull", dh_user.DhUserCheck, audio.PullAudioCensor)
		apiV1.POST("/figure/videos/async/submit", dh_user.DhUserCheck, video.SubmitVideoCensor)
		apiV1.POST("/figure/videos/async/pull", dh_user.DhUserCheck, video.PullVideoCensorResult)

		apiV1.POST("/internal/figure/texts", text.CensorTexts)
		apiV1.POST("/internal/figure/images", image.CensorImage)
		apiV1.POST("/internal/figure/video", shortVideo.CensorVideo)
		apiV1.POST("/internal/figure/audio", shortAudio.CensorAudio)
	}

	apiInternalV1 := e.Group("/api/digitalhuman/external/riskcontrol/internal/v1")
	{
		apiInternalV1.POST("/figure/list", figure.GetRiskControlItemList)
		apiInternalV1.POST("/figure/custom", figure.CustomRiskControlItem)
	}

	// faceV1 := e.Group("/api/digitalhuman/external/face/v1")
	// {
	// 	faceV1.POST("/figure/match", dh_user.DhUserCheck)
	// 	faceV1.POST("/figure/stop", dh_user.DhUserCheck)
	// }

	watermarkV1 := e.Group("/watermark/api/v1")
	// ----- 视频水印接口 api ----- //
	{
		watermark := figure.GetVideoWatermark()
		// 水印视频提交
		watermarkV1.POST("/figure/video", watermark.HandleRequest)
		watermarkV1.POST("/figure/video/result", watermark.GetWaterMarkResult)
		// 水印视频完成回调
		// watermarkV1.POST("/figure/video/callback", watermark.WaterMarkCallback)
		// 提取水印
		// watermarkV1.POST("/figure/video/extract/create", watermark.CreateWaterMarkExtraction)
		// watermarkV1.POST("/figure/video/extract/result", watermark.GetWaterMarkExtractionResult)

		watermarkV1.POST("/figure/video/callback/test", watermark.TestCallback)
	}

	file2ImageV1 := e.Group("/api/digitalhuman/file2image/v1")
	{
		file2image := figure.NewFile2Image()

		file2ImageV1.POST("/figure/upload", dh_user.DhUserCheck, file2image.CheckFile)
		file2ImageV1.POST("/figure/parse", dh_user.DhUserCheck, file2image.HandlerRequest)
		file2ImageV1.POST("/figure/parse/result", dh_user.DhUserCheck, file2image.GetParseResult)
		file2ImageV1.POST("/figure/parse/stop", dh_user.DhUserCheck, file2image.StopParseResult)
		file2ImageV1.PUT("/figure/set/draftId", dh_user.DhUserCheck, file2image.SetDraftId)
		// file2ImageV1.POST("/figure/upload", file2image.CheckFile)
		// file2ImageV1.POST("/figure/parse", file2image.HandlerRequest)
		// file2ImageV1.POST("/figure/parse/result", file2image.GetParseResult)
	}

	file2ImageV2 := e.Group("/api/digitalhuman/file2image/v2")
	{
		file2ImageV2.POST("/figure/upload", dh_user.DhUserCheck, figure.UploadCoursewareFile)
		file2ImageV2.POST("/figure/parse/result", dh_user.DhUserCheck, figure.GetParseResultV2)
		file2ImageV2.POST("/figure/courseware/list", dh_user.DhUserCheck, figure.GetUserCoursewareFile)
		file2ImageV2.POST("/figure/courseware/remove", dh_user.DhUserCheck, figure.DeletedAtFile2Image)
		file2ImageV2.POST("/figure/courseware/stop", dh_user.DhUserCheck, figure.StopParseResultV2)
	}

	miniprogramV1 := e.Group("/api/digitalhuman/external/miniprogram/v1")
	{
		miniprogramV1.GET("/figure/id", dh_user.DhUserCheck, figure.CreatMiniProgramID)
		miniprogramV1.POST("/figure/copy", dh_user.DhUserCheck, figure.CopyProperty)
	}

	externalTranslateV1 := e.Group("/api/digitalhuman/external/translate/v1")
	{
		// 文本翻译接口
		externalTranslateV1.POST("/text", dh_user.DhUserCheck, translate.TranslateText)
		// 视频翻译接口
		externalTranslateV1.POST("/video", dh_user.DhUserCheck, translate.TranslateVideo)
		// 视频翻译失败后的重试接口
		externalTranslateV1.POST("/video/retry", dh_user.DhUserCheck, translate.TranslateVideoRetry)

	}
	// 视频翻译回调接口，前端thymeleaf翻译结束后回调
	internalTransCallbackV1 := e.Group("/api/digitalhuman/internal/translate/end/v1")
	{
		internalTransCallbackV1.POST("/video", translate.TranslateVideoCallback)
	}
	// 文本翻译服务内部调用接口-前端thymeleaf服务调用
	internalTranslateV1 := e.Group("/api/digitalhuman/internal/translate/v1")
	{
		internalTranslateV1.POST("/text", translate.TranslateText)
	}

	feedbackV1 := e.Group("/api/digitalhuman/external/feedback/v1")
	{
		// 反馈类型增删改查（暂无 增、删、改需求， 方便后期修改反馈类型）
		feedbackV1.GET("/user-feedback/tags", rateLimitMiddleware("getUserFeedbackTags", 100, 1*time.Second), dh_user.DhUserCheckByPass, feedback.GetFeedBackTypes)
		feedbackV1.POST("/user-feedback/tag", rateLimitMiddleware("getUserFeedbackTagsAdd", 100, 1*time.Second), feedback.AddFeedbackType)
		feedbackV1.PUT("/user-feedback/tag", rateLimitMiddleware("getUserFeedbackTagsPut", 100, 1*time.Second), feedback.UpdateFeedbackType)
		feedbackV1.DELETE("/user-feedback/tag", rateLimitMiddleware("getUserFeedbackTagsDel", 100, 1*time.Second), feedback.DeleteFeedbackType)

		// 用户反馈相关接口
		feedbackV1.POST("/user-feedback/list", rateLimitMiddleware("getUserFeedbacks", 100, 1*time.Second), feedback.QueryUserFeedBack)
		feedbackV1.POST("/user-feedback", rateLimitMiddleware("getUserFeedbackAdd", 100, 1*time.Second), dh_user.DhUserCheckByPass, feedback.AddUserFeedBack)
		feedbackV1.PUT("/user-feedback", rateLimitMiddleware("getUserFeedbackPut", 100, 1*time.Second), feedback.UpdateUserFeedBack)
		feedbackV1.PUT("/user-feedback-batch", rateLimitMiddleware("getUserFeedbackBatchPut", 100, 1*time.Second), feedback.UpdateBatchUserFeedBack)
		feedbackV1.POST("/user-feedback/delete", rateLimitMiddleware("getUserFeedbackDel", 100, 1*time.Second), feedback.DeleteUserFeedBack)

		// 点赞点踩
		feedbackV1.POST("/upvote", rateLimitMiddleware("getUserFeedbackUpvote", 100, 1*time.Second), dh_user.DhUserCheckByPass, feedback.UpvoteFeedback)
		feedbackV1.POST("/getUpvote", rateLimitMiddleware("getUserFeedbackGetUpvote", 100, 1*time.Second), dh_user.DhUserCheckByPass, feedback.GetUpvoteFeedback)

		// 调查问卷相关标签
		feedbackV1.GET("/questionnaire/tags", rateLimitMiddleware("getQuestionnaireLabels", 100, 1*time.Second), dh_user.DhUserCheckByPass, feedback.QuestionnaireLabels)
		feedbackV1.POST("/questionnaire/tag", rateLimitMiddleware("getQuestionnaireLabelAdd", 100, 1*time.Second), feedback.QuestionnaireLabelAdd)
		feedbackV1.PUT("/questionnaire/tag", rateLimitMiddleware("getQuestionnaireLabelPut", 100, 1*time.Second), feedback.QuestionnaireLabelUpdate)
		feedbackV1.DELETE("/questionnaire/tag", rateLimitMiddleware("getQuestionnaireLabelDel", 100, 1*time.Second), feedback.QuestionnaireLabelDelete)

		// 问卷调查
		feedbackV1.POST("/questionnaire/list", rateLimitMiddleware("getQuestionnaires", 100, 1*time.Second), feedback.QuestionnaireList)
		feedbackV1.POST("/questionnaire", rateLimitMiddleware("getQuestionnaireAdd", 100, 1*time.Second), dh_user.DhUserCheckByPass, feedback.QuestionnaireAdd)
		feedbackV1.PUT("/questionnaire", rateLimitMiddleware("getQuestionnairePut", 100, 1*time.Second), feedback.QuestionnaireUpdate)
		feedbackV1.DELETE("/questionnaire", rateLimitMiddleware("getQuestionnaireDel", 100, 1*time.Second), feedback.QuestionnaireDelete)

		//点踩反馈标签
		feedbackV1.GET("/workpreview-feedback-tag", rateLimitMiddleware("getWorkPreviewFeedbackLabels", 100, 1*time.Second), feedback.WorkPreviewLabels)
		feedbackV1.POST("/workpreview-feedback-tags", rateLimitMiddleware("getWorkPreviewFeedbackLabels", 100, 1*time.Second), feedback.WorkPreviewList)
		feedbackV1.POST("/workpreview-feedback-tag", rateLimitMiddleware("getWorkPreviewFeedbackLabelAdd", 100, 1*time.Second), feedback.WorkPreLabelAdd)
		feedbackV1.PUT("/workpreview-feedback-tag", rateLimitMiddleware("getWorkPreviewFeedbackLabelPut", 100, 1*time.Second), feedback.WorkPreLabelUpdate)
		feedbackV1.DELETE("/workpreview-feedback-tag", rateLimitMiddleware("getWorkPreviewFeedbackLabelDel", 100, 1*time.Second), feedback.WorkPreLabelDelete)
		//点踩反馈标签
		feedbackV1.POST("/workpreview-feedback-list", rateLimitMiddleware("getWorkPreviewFeedbacks", 100, 1*time.Second), feedback.WorkPreFeedbackList)
		feedbackV1.POST("/workpreview-feedback", rateLimitMiddleware("getWorkPreviewFeedbackAdd", 100, 1*time.Second), dh_user.DhUserCheckByPass, feedback.WorkPreFeedbackAdd)
		feedbackV1.PUT("/workpreview-feedback", rateLimitMiddleware("getWorkPreviewFeedbackPut", 100, 1*time.Second), feedback.WorkPreFeedbackUpdate)
		feedbackV1.POST("/workpreview-feedback-del", rateLimitMiddleware("getWorkPreviewFeedbackDel", 100, 1*time.Second), feedback.WorkPreFeedbackDelete)

	}
	countryV1 := e.Group("/api/digitalhuman/external/country/v1")
	{
		// 国家信息管理
		countryV1.GET("/country/list", rateLimitMiddleware("getCountrys", 100, 1*time.Second), dh_user.DhUserCheckByPass, country.CountryList)
		countryV1.POST("/country", rateLimitMiddleware("getCountryAdd", 100, 1*time.Second), country.CountryAdd)
		countryV1.POST("/country/search", rateLimitMiddleware("getCountrySearch", 100, 1*time.Second), country.CountrySearch)
		countryV1.POST("/country/batch-add", rateLimitMiddleware("getCountryBatchAdd", 100, 1*time.Second), country.CountryBatchAdd)
		countryV1.PUT("/country", rateLimitMiddleware("getCountryPut", 100, 1*time.Second), country.CountryUpdate)
		countryV1.DELETE("/country", rateLimitMiddleware("getCountryDel", 100, 1*time.Second), country.CountryDelete)
	}

	consultV1 := e.Group("/api/digitalhuman/external/consult/v1")
	{
		// 企业定制咨询标签
		consultV1.GET("/consult-label/tags", rateLimitMiddleware("getConsultLabels", 100, 1*time.Second), dh_user.DhUserCheckByPass, consult.ConsultLabels)
		consultV1.POST("/consult-label/tag", rateLimitMiddleware("getConsultLabelAdd", 100, 1*time.Second), consult.ConsultLabelAdd)
		consultV1.PUT("/consult-label/tag", rateLimitMiddleware("getConsultLabelPut", 100, 1*time.Second), consult.ConsultLabelUpdate)
		consultV1.DELETE("/consult-label/tag", rateLimitMiddleware("getConsultLabelDel", 100, 1*time.Second), consult.ConsultLabelDelete)

		// 企业定制咨询
		consultV1.POST("/enterprise-custom-consult/list", rateLimitMiddleware("getConsults", 100, 1*time.Second), consult.ConsultList)
		consultV1.POST("/enterprise-custom-consult/delete", rateLimitMiddleware("getConsultDel", 100, 1*time.Second), consult.ConsultDelete)
		consultV1.POST("/enterprise-custom-consult", rateLimitMiddleware("getConsultAdd", 100, 1*time.Second), consult.ConsultAdd)
		consultV1.PUT("/enterprise-custom-consult", rateLimitMiddleware("getConsultPut", 100, 1*time.Second), consult.ConsultUpdate)
	}

	{
		campaignApiV1 := e.Group("/api/digitalhuman/external/v1/campaign")
		campaignApiV1.GET("/share/link", dh_user.DhUserCheck, campaign.GetShareLink)
		campaignApiV1.POST("/invitee/bind", dh_user.DhUserCheck, campaign.BindInvite)
		campaignInternalApiV1 := e.Group("/api/digitalhuman/external/v1/internal/campaign")
		campaignInternalApiV1.POST("/kafka/send", campaign.AddKafakaQueue)
	}

	{
		api := e.Group("/api/digitalhuman/external/asr/v1")
		api.POST("/speech2text", dh_user.DhUserCheck, speechtotext.SpeechToText)
	}
}
func rateLimitMiddleware(key string, limit int, window time.Duration) gin.HandlerFunc {
	return func(c *gin.Context) {
		logger.CtxLog(c).Infof("%vREQREQUEST, limit: %v, window: %v", key, limit, window)
		// 使用 redis_rate 进行限流
		lit := redis_rate.Limit{
			Rate:   limit,
			Period: window,
			Burst:  limit,
		}
		res, err := goredis.GetLimiter().Allow(c, key, lit)
		if err != nil {
			logger.CtxLog(c).Errorf("redis rate limit fail, key:%v, err: %v", key, err)
			c.JSON(http.StatusInternalServerError, proto2.NewCommRsp(540321, "服务繁忙"))
			c.Abort()
			return
		}

		// 判断是否被限流
		if res.Allowed == 0 {
			logger.CtxLog(c).Warnf("%vREQOVERLIMIT, redis rate limit ok, res: %v", key, res)
			c.JSON(http.StatusOK, proto2.NewCommRsp(-90000, "limit over!"))
			c.Abort()
			return
		}

		// 如果没有被限流，继续处理请求
		c.Next()
	}
}
