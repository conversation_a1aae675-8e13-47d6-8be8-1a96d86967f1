package config

var (
	LocalConfig = &localConfig{}
)

type localConfig struct {
	StarLightMysqlSetting   *MysqlSetting   `toml:"starlight-mysql-setting"`
	Text2FigureMysqlSetting *MysqlSetting   `toml:"text2figure-mysql-setting"`
	MetaHumanEditorSaas     *MysqlSetting   `toml:"metaHuman-editorsaas-mysql-setting"`
	RedisSetting            *RedisSetting   `toml:"redis-setting"`
	DhUserSetting           *DhUserSetting  `toml:"dh-user-setting"`
	RiskControl             *RiskControl    `toml:"risk-control"`
	WatermMark              *WatermMark     `toml:"watermark"`
	BosSetting              *BosSetting     `toml:"bos-setting"`
	TranslateApi            *TranslateApi   `toml:"translate-api"`
	VideoTranslate          *VideoTranslate `toml:"video-translate"`
	KafkaSetting            *KafkaSetting   `toml:"kafka-setting"`
	AsrSetting              *AsrSetting     `toml:"asr-setting"`
}

type RedisSetting struct {
	SentinelNodes    string `toml:"sentinelNodes"`    // 哨兵集群节点，ip:port，多个节点使用,分割，eg: "127.0.0.1:26379,127.0.0.1:26380,127.0.0.1:26381"
	SentinelUsername string `toml:"sentinelUsername"` // 哨兵节点用户名，可为""
	SentinelPassword string `toml:"sentinelPassword"` // 哨兵节点密码，可为""
	RouteByLatency   bool   `toml:"routeByLatency"`   // 是否将读取命令路由到从节点
	MasterName       string `toml:"masterName"`       // redis集群主节点名称
	Addr             string `toml:"addr"`             // 单节点地址，格式为ip:port，eg: "127.0.0.1:6379"
	Username         string `toml:"username"`         // redis集群主节点用户名
	Password         string `toml:"password"`         // redis集群主节点密码
	PoolSize         int    `toml:"poolSize"`         // redis连接池大小
	MinIdleConns     int    `toml:"minIdleConns"`     // 最大空闲连接数
	RedisEnv         string `toml:"redisEnv"`         // redis环境，dev/test/prod
}

type DhUserSetting struct {
	BaseUrl string `toml:"baseUrl"`
}

type RiskControl struct {
	AK                        string   `toml:"ak"`
	SK                        string   `toml:"sk"`
	StrategyId                int      `toml:"strategyId"`
	TextQueueSize             int      `toml:"text-queue-size"`
	MiniTextQpsMax            int      `toml:"mini-text-qps-max"`
	AudioQueueSize            int      `toml:"audio-queue-size"`
	MiniAudioQpsMax           int      `toml:"mini-audio-qps-max"`
	ImageQueueSize            int      `toml:"image-queue-size"`
	ImageQpsMax               int      `toml:"image-qps-max"`
	VideoQueueSize            int      `toml:"video-queue-size"`
	VideoQpsMax               int      `toml:"video-qps-max"`
	MiniCensorUrl             string   `toml:"mini-censor-url"`
	MiniTextDetectionUrl      string   `toml:"mini-text-detection-url"`
	MiniAudioAsrUrl           string   `toml:"mini-audio-asr-url"`
	IsInternational           bool     `toml:"is-international"`
	SensitiveInfoDetectionUrl string   `toml:"sensitive-info-detection-url"`
	SensitiveInfoDetectionAK  string   `toml:"sensitive-info-detection-ak"`
	SensitiveInfoDetectionSK  string   `toml:"sensitive-info-detection-sk"`
	GCPConfidenceMax          float32  `toml:"gcp-confidence-max"`
	GCPCensorTextType         []string `toml:"gcp-censor-text-type"`
	GCPCensorImageType        []string `toml:"gcp-censor-image-type"`
	CensorValidDomains        []string `toml:"censor-valid-domains"`
	IsSwitch                  bool     `toml:"is-switch"` // 是否开启水印
}

type WatermMark struct {
	AK                   string `toml:"ak"`
	SK                   string `toml:"sk"`
	Endpoint             string `toml:"endpoint"`
	PipelineName         string `toml:"pipeline-name"`           // 流水线任务名称
	PresetName           string `toml:"preset-name"`             // 转码模板名称
	PresetNameWebm       string `toml:"preset-name-webm"`        // 转码模板名称
	DigitalWmTextContent string `toml:"digital-wm-text-content"` // 水印文本内容
	DigitalWmId          string `toml:"digital-wm-id"`           // 模版数字水印ID
	InputBucket          string `toml:"input-bucket"`            // 输入桶名
	OutputBucket         string `toml:"out-bucket"`              // 输出桶名
	Notification         string `toml:"notification"`            // 回调地址
	IsSwitch             bool   `toml:"is-switch"`               // 是否开启水印
}

type TranslateApi struct {
	AppID                  string   `toml:"appId"`
	SecurityKey            string   `toml:"securityKey"`
	URL                    string   `toml:"url"`
	TranslateListSizeLimit int      `toml:"translateListSizeLimit"` // 一次提交的翻译列表的最大值
	QpsMax                 int      `toml:"qpsMax"`
	NeedReryErrorCode      []string `toml:"needReryErrorCode"`
	GoogleProjectID        string   `toml:"googleProjectID"`
	WhichTranslate         int      `toml:"whichTranslate"` // 0: 百度翻译 1: Google翻译
	GoogleQpsMax           int      `toml:"googleQpsMax"`
	GoogleCredentialsPath  string   `toml:"googleCredentialsPath"`
	UseDataCache           bool     `toml:"useDataCache"` // 是否使用数据库缓存能力
	LanCodeBaiduToGoogle   string   `toml:"lanCodeBaiduToGoogle"`
	LanCodeGoogleToBaidu   string   `toml:"lanCodeGoogleToBaidu"`
}

type VideoTranslate struct {
	TransMaxNum             int    `toml:"transMaxNum"`   // 下游服务支持的最多任务数
	TransOutTime            int    `toml:"transOutTime"`  // 超时时间 单位秒
	MaxRetryCount           int    `toml:"maxRetryCount"` // 每一个任务最大重试次数
	ThymeleafTransHost      string `toml:"thymeleafTransHost"`
	ThymeleafTransPath      string `toml:"thymeleafTransPath"`
	DraftCopyHost           string `toml:"draftCopyHost"`
	SlideDraftCopyPath      string `toml:"slideDraftCopyPath"`
	CoursewareDraftCopyPath string `toml:"coursewareDraftCopyPath"`
}

type BosSetting struct {
	AK       string `toml:"ak"`
	SK       string `toml:"sk"`
	Endpoint string `toml:"endpoint"`
	Bucket   string `toml:"bucket"`
	Host     string `toml:"host"`
	CDNHost  string `toml:"cdn-host"`
}
type HostPortSetting struct {
	Host string `toml:"host"`
	Port int    `toml:"port"`
}

type UserNamePwdSetting struct {
	Username string `toml:"username"`
	Password string `toml:"password"`
}

type MysqlSetting struct {
	HostPortSetting
	UserNamePwdSetting
	Database     string `toml:"database"`
	MaxOpenConns int    `toml:"maxOpenConns"`
	MaxIdleConns int    `toml:"maxIdleConns"`
}

type KafkaSetting struct {
	BootstrapServers string `toml:"bootstrap-servers"`
	CredentialsPath  string `toml:"credentials-path"`
}

type AsrSetting struct {
	SplitFlags string `toml:"split-flags"`
	AsrHost    string `toml:"asr-host"`
	ApiKey     string `toml:"apikey"`
}
