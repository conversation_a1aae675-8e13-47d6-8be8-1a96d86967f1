apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: digital-human
    module: dhlive-external-api
  name: dhlive-external-api
  namespace: dh-v3
spec:
  replicas: 3
  minReadySeconds: 0
  strategy:
    type: RollingUpdate # 策略：滚动更新
    rollingUpdate:
      maxSurge: 25%
      maxUnavailable: 25%
  selector:
    matchLabels:
      app: digital-human
      module: dhlive-external-api
  template:
    metadata:
      labels:
        app: digital-human
        module: dhlive-external-api
    spec:
      affinity:
        podAntiAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            - labelSelector:
                matchExpressions:
                  - key: app
                    operator: In
                    values:
                      - digital-human
                  - key: module
                    operator: In
                    values:
                      - dhlive-external-api
              topologyKey: kubernetes.io/hostname
      restartPolicy: Always
      tolerations:
        - key: "limit"
          operator: "Equal"
          value: "dh-v3"
          effect: "NoSchedule"
      containers:
        - name: dhlive-external-api
          image: ccr-246im3mw-pub.cnc.bj.baidubce.com/digitalhuman/dhlive-external-api:20241219_1734608345143
          imagePullPolicy: Always
          command: ["sh"]
          env:
            - name: POD_NAMESPACE
              valueFrom:
                fieldRef:
                  fieldPath: metadata.namespace
          args: ["/home/<USER>/sbin/start.sh"]
          # imagePullPolicy: IfNotPresent
          # imagePullPolicy: Never
          ports:
            - containerPort: 8080
          livenessProbe: # 健康检查：存活探针
            httpGet:
              path: /health
              port: 8080
            initialDelaySeconds: 20
            timeoutSeconds: 5
            periodSeconds: 5
          readinessProbe: # 健康检查：就绪探针
            httpGet:
              path: /health
              port: 8080
            initialDelaySeconds: 5
            timeoutSeconds: 1
            periodSeconds: 5
          resources:
            limits:
              cpu: "8"
              memory: 10Gi
            requests:
              cpu: "2"
              memory: 1Gi
          volumeMounts:
            - mountPath: /home/<USER>/conf/conf.toml
              name: config-volume
              subPath: conf.toml
      volumes:
        - configMap:
            defaultMode: 420
            name: dhlive-external-api
          name: config-volume
      imagePullSecrets:
        - name: regcred
        - name: regcred-ccr
        - name: regcred-eccr
---
apiVersion: v1
kind: Service
metadata:
  labels:
    app: digital-human
    module: dhlive-external-api
  name: dhlive-external-api
  namespace: dh-v3
spec:
  selector:
    app: digital-human
    module: dhlive-external-api
  ports:
    - protocol: TCP
      port: 8080
      targetPort: 8080
  type: ClusterIP
---
apiVersion: v1
kind: ConfigMap
metadata:
  labels:
    app: digital-human
    module: dhlive-external-api
  name: dhlive-external-api
  namespace: dh-v3
data:
  conf.toml: |
    ####################################################### 服务配置-生产环境 #######################################################
    server-port = 8080
    server-name = "dhlive-external-api"
    log-file-prefix = "localhost"
    log-file-path = "./logs/"
    log-show-code-file = true
    log-show-code-func = true
    # consul配置
    [consul-setting]
    host = "127.0.0.1"
    port = 8500
    name = ""

    # 健康检查地址
    health-url = "/health"
    check-timeout = "10s"
    check-interval = "10s"
    deregister-critical-service-after = "30s"

    # mysql配置
    [mysql-setting]
    # host = "mysql57-test.rdsmltt6s1aa9rz.rds.bj.baidubce.com"
    host = "*************"
    port = 3306
    database = "dhlive_third_platform_online"
    username = "dhlive_tp_plat"
    password = "Hi109@123"
    maxOpenConns = 100
    maxIdlenConns = 5

    [starlight-mysql-setting]
    host = "*************"
    port = 3306
    database = "digital_human_star_light"
    username = "saas"
    password = "!Digitalhuman@baidu123"
    maxOpenConns = 100
    maxIdlenConns = 5

    [text2figure-mysql-setting]
    host = "*************"
    port = 3306
    database = "text2figure_ppe_v3"
    username = "saas"
    password = "!Digitalhuman@baidu123"
    maxOpenConns = 100
    maxIdlenConns = 5

    # redis配置
    [redis-setting]
    addr = "************:6379"
    username = ""
    password = ""
    redisEnv = "prerelease-dh-v3"

    # 日志上传的elasticsearch配置
    [log-es-setting]
    host = "http://elasticsearch:9200"
    username = ""
    password = ""

    [dh-user-setting]
    baseUrl = "http://dh-user:80"

    # 风控所需配置
    [risk-control]
    # apikey
    ak = "smjmRmlvwMBj0UqNwXoXZRA1"
    # Secret Key
    sk = "pWTkyu16gawlOZFccYpoZdVb4UU3Wev8"
    strategyId = 37737
    [bos-setting]
    # apikey
    ak = "ALTAKxdVkHMs4sp0Tgkpa3zCJP"
    # Secret Key
    sk = "e4f46ff5d1bf45c5b81dd867d6e3c148"
    endpoint = "bj.bcebos.com"
    bucket   = "xiling-dh"
    host     = "https://xiling-dh.bj.bcebos.com"
    cdn-host = "https://xiling-dh.cdn.bcebos.com"

    [translate-api]
    appId = "20241210002224952"
    securityKey = "OS1K9_lfiLL0_12XuWvb"
    url = "https://fanyi-api.baidu.com/api/trans/vip/translate"
    qpsMax = 100
    needReryErrorCode = ["52001", "52002", "54003"]
    translateListSizeLimit = 50
    googleProjectID = "xiling-453607"
    # 0: 百度翻译 1: Google翻译
    whichTranslate = 1
    googleQpsMax = 100 
    googleCredentialsPath = "xiling-453607-5f236fdbee17.json"

    [watermark]
    ak = "ALTAKQjv35LJ4y7uRcZQv1wouK"
    sk = "470218fdae3b4f278eb7be4bd6efb1d6"
    # 流水线任务名称
    pipeline-name = "saas_water_mark_pipeline"
    # 转码模板名称      
    preset-name = "saas_water_master_temp"
    preset-name-webm = "saas_water_master_temp_webm"
    # 水印文本内容    
    digital-wm-text-content = "百度智能云曦灵"
    # 模版数字水印ID
    digital-wm-id = "dwm-qgangakdamw1xuar"
    # 输出桶名  
    input-bucket = "saas-water-mark-input"     
    out-bucket = "saas-water-mark-output"
    endpoint = "media.bj.baidubce.com"
    # notification = "http://**************/watermark/api/v1/figure/video/callback"
    # notification = "http://persona.baidu.com:8050/live2d/dhlive-cgi/dhlive-external-api/watermark/api/v1/figure/video/callback"
---

