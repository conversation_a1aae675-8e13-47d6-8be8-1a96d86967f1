####################################################### 服务配置-测试环境 #######################################################
server-port = 8080
server-name = "dhlive-external-api"
log-file-prefix = "localhost"
log-file-path = "./logs/"
log-show-code-file = true
log-show-code-func = true
# consul配置
[consul-setting]
host = "127.0.0.1"
port = 8500
name = ""
# 健康检查地址
health-url = "/health"
check-timeout = "10s"
check-interval = "10s"
deregister-critical-service-after = "30s"

# mysql配置
[mysql-setting]
# host = "mysql57.rdsm9dvaqfvz285.rds.bj.baidubce.com"
host = "127.0.0.1"
port = 3371
database = "dhlive_third_platform_v3"
username = "dhlive_tp_plat"
password = "Hi109@123"
maxOpenConns = 1000
maxIdlenConns = 5

[starlight-mysql-setting]
host = "127.0.0.1"
port = 3371
database = "star_light_v3"
username = "dh_v3"
password = "!Digitalhuman@baidu123"
maxOpenConns = 100
maxIdlenConns = 5

[text2figure-mysql-setting]
host = "127.0.0.1"
port = 3371
database = "dhlive_third_platform_v3"
username = "dhlive_tp_plat"
password = "Hi109@123"
maxOpenConns = 1000
maxIdlenConns = 5

# redis配置
[redis-setting]
# addr = "************:6379"
addr = "127.0.0.1:6777"
username = ""
password = ""
redisEnv = "dev"

# 日志上传的elasticsearch配置
[log-es-setting]
# host = "http://*************:8200"
host = "http://127.0.0.1:9202"
username = ""
password = ""

# 鉴权
[dh-user-setting]
baseUrl = "http://dh-user:80"

# 风控所需配置
[risk-control]
# apikey
ak = "smjmRmlvwMBj0UqNwXoXZRA1"
# Secret Key
sk = "pWTkyu16gawlOZFccYpoZdVb4UU3Wev8"
strategyId = 37737

[bos-setting]
# apikey
ak = "ALTAKxdVkHMs4sp0Tgkpa3zCJP"
# Secret Key
sk = "e4f46ff5d1bf45c5b81dd867d6e3c148"
endpoint = "bj.bcebos.com"
bucket   = "xiling-dh"
host     = "https://xiling-dh.bj.bcebos.com"
cdn-host = "https://xiling-dh.cdn.bcebos.com"

[watermark]
ak = "ALTAKQjv35LJ4y7uRcZQv1wouK"
sk = "470218fdae3b4f278eb7be4bd6efb1d6"
# 流水线任务名称
pipeline-name = "saas_water_mark_pipeline"
# 转码模板名称      
preset-name = "saas_water_master_temp"
preset-name-webm = "saas_water_master_temp_webm"
# 水印文本内容    
digital-wm-text-content = "百度智能云曦灵"
# 模版数字水印ID
digital-wm-id = "dwm-qgangakdamw1xuar"
# 输出桶名  
input-bucket = "saas-water-mark-input"     
out-bucket = "saas-water-mark-output"
endpoint = "media.bj.baidubce.com"
# notification = "http://**************/watermark/api/v1/figure/video/callback"
# notification = "http://persona.baidu.com:8050/live2d/dhlive-cgi/dhlive-external-api/watermark/api/v1/figure/video/callback"


[translate-api]
appId = "20241210002224952"
securityKey = "OS1K9_lfiLL0_12XuWvb"
url = "https://fanyi-api.baidu.com/api/trans/vip/translate"
qpsMax = 10
needReryErrorCode = ["52001", "52002", "54003"]

[asr-setting]
split-flags = "[;|,|；|，|。|?|？|!|！|.|؛|؟|!]"
asr-host = "https://api.elevenlabs.io/v1/speech-to-text"
apikey = "***************************************************"