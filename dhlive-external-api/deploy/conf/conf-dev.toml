####################################################### 服务配置-测试环境 #######################################################
server-port = 8080
server-name = "dhlive-external-api"
log-file-prefix = "localhost"
log-file-path = "./logs/"
log-show-code-file = true
log-show-code-func = true
# consul配置
[consul-setting]
host = "127.0.0.1"
port = 8500
name = ""
# 健康检查地址
health-url = "/health"
check-timeout = "10s"
check-interval = "10s"
deregister-critical-service-after = "30s"

# mysql配置
[mysql-setting]
# host = "mysql57.rdsm9dvaqfvz285.rds.bj.baidubce.com"
host = "127.0.0.1"
port = 3306
database = "dhlive_third_platform_online"
username = "saas"
password = "!Digitalhuman@baidu123"
maxOpenConns = 1000
maxIdlenConns = 5

[starlight-mysql-setting]
host = "127.0.0.1"
port = 3306
database = "digital_human_star_light"
username = "saas"
password = "!Digitalhuman@baidu123"
maxOpenConns = 100
maxIdlenConns = 5

[text2figure-mysql-setting]
host = "127.0.0.1"
port = 3306
database = "dhlive_third_platform_online"
username = "saas"
password = "!Digitalhuman@baidu123"
maxOpenConns = 1000
maxIdlenConns = 5

[metaHuman-editorsaas-mysql-setting]
host = "127.0.0.1"
port = 3306
database = "meta_human_editor_saas"
username = "saas"
password = "!Digitalhuman@baidu123"
maxOpenConns = 1000
maxIdlenConns = 5


# redis配置
[redis-setting]
# addr = "************:6379"
addr = "127.0.0.1:6379"
username = ""
password = ""
redisEnv = "dev"

# 日志上传的elasticsearch配置
[log-es-setting]
# host = "http://*************:8200"
host = "http://127.0.0.1:9202"
username = ""
password = ""

# 鉴权
[dh-user-setting]
baseUrl = "http://dh-user:80"

# 风控所需配置
[risk-control]
is-switch = false
is-international = true
# apikey
ak = "smjmRmlvwMBj0UqNwXoXZRA1"
# Secret Key
sk = "pWTkyu16gawlOZFccYpoZdVb4UU3Wev8"
strategyId = 37737
text-queue-size =  30
mini-text-qps-max = 10
audio-queue-size =  30
mini-audio-qps-max = 5
image-queue-size =  50
image-qps-max = 10
video-queue-size =  30
video-qps-max = 5
mini-censor-url = "http://*************:8061/v1/detect-multimodal"
mini-text-detection-url = "http://*************:8041/language/classify"
mini-audio-asr-url = "http://*************:8031/asr"
sensitive-info-detection-url = "http://*************:8421/api/v2/privacy_extract"
sensitive-info-detection-ak = "FVYV2N4gxBNz"
sensitive-info-detection-sk = "8ba63256cecb13e6"
gcp-confidence-max = 0.7
gcp-censor-text-type = ["Toxic", "Insult", "Profanity","Derogatory","Sexual","Violent","Firearms & Weapons","Public Safety","Religion & Belief","Illicit Drugs","War & Conflict","Politics","Legal"]
gcp-censor-image-type = ["Adult", "Racy", "Violence", "Spoof", "Medical"]
censor-valid-domains = ["www.keevx.com","www.keevaai.com","test.keevx.com","test.keevaai.com","storage.googleapis.com"]
[watermark]
# 水印开关 false 关闭水印 当水印请求过来直接返回结果即可
is-switch = false
ak = "ALTAKQjv35LJ4y7uRcZQv1wouK"
sk = "470218fdae3b4f278eb7be4bd6efb1d6"
# 流水线任务名称
pipeline-name = "saas_water_mark_pipeline"
# 转码模板名称      
preset-name = "saas_water_master_temp"
preset-name-webm = "saas_water_master_temp_webm"
# 水印文本内容    
digital-wm-text-content = "百度智能云曦灵"
# 模版数字水印ID
digital-wm-id = "dwm-qgangakdamw1xuar"
# 输出桶名  
input-bucket = "saas-water-mark-input"     
out-bucket = "saas-water-mark-output"
endpoint = "media.bj.baidubce.com"
# notification = "http://**************/watermark/api/v1/figure/video/callback"
# notification = "http://persona.baidu.com:8050/live2d/dhlive-cgi/dhlive-external-api/watermark/api/v1/figure/video/callback"


[translate-api]
appId = "20241210002224952"
securityKey = "OS1K9_lfiLL0_12XuWvb"
url = "https://fanyi-api.baidu.com/api/trans/vip/translate"
qpsMax = 100
needReryErrorCode = ["52001", "52002", "54003"]
translateListSizeLimit = 50
googleProjectID = "xiling-453607"
# 0: 百度翻译 1: Google翻译
whichTranslate = 1
googleQpsMax = 100 
googleCredentialsPath = "xiling-453607-5f236fdbee17.json"
useDataCache = true
lanCodeBaiduToGoogle = '{"ara": "ar", "dan": "da", "fra": "fr", "jp": "ja", "kor": "ko", "pot": "pt-BR", "rom": "ro", "spa": "es", "swe": "sv", "ukr": "tr"}'
lanCodeGoogleToBaidu = '{"zh-CN": "zh","zh-TW": "zh","ar": "ara","da": "dan","fr": "fra","fr-CA": "fra","ko": "kor","pt-BR": "pot","pt-PT": "pt","es": "spa","sv": "swe","tr": "ukr"}'

[video-translate]
transMaxNum = 3
transOutTime = 30
maxRetryCount = 2
thymeleafTransHost = '127.0.0.1:8787'
thymeleafTransPath = '/api/digitalhuman/mhen/translate'
draftCopyHost = '127.0.0.1:8080'
slideDraftCopyPath = '/api/digitalhuman/slide/v1/draft/copy'
coursewareDraftCopyPath = '/api/digitalhuman/slide/v1/courseware/draft/copy'

[storage-setting]
type = "gcs"  # bos/gcs

[storage-setting.bos]
# apikey
ak = "ALTAKxdVkHMs4sp0Tgkpa3zCJP"
# Secret Key
sk = "e4f46ff5d1bf45c5b81dd867d6e3c148"
endpoint = "bj.bcebos.com"
bucket   = "xiling-dh"
host     = "https://xiling-dh.bj.bcebos.com"
cdn-host = "https://xiling-dh.cdn.bcebos.com"
retryTimes = 3
retrySleepMillis = 1000

[storage-setting.gcs]
credential = "./deploy/conf/gcs_credentials.json"
bucket = "xiling_asia-southeast1_bucket"
host = "https://storage.googleapis.com"
cdn-host = "https://test.keevx.com"
path = "external-api"
retryTimes = 3
retrySleepMillis = 1000

[kafka-setting]
bootstrap-servers = "bootstrap.saas-kafka.asia-southeast1.managedkafka.xiling-453607.cloud.goog:9092"
credentials-path = "./deploy/conf/gcs_credentials.json"

[asr-setting]
split-flags = "[;|,|；|，|。|?|？|!|！|.|؛|؟|!]"
asr-host = "https://api.elevenlabs.io/v1/speech-to-text"
apikey = "***************************************************"
