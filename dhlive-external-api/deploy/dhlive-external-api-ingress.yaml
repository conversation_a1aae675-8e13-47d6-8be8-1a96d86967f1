apiVersion: extensions/v1beta1
kind: Ingress
metadata:
  name: dhlive-external-api-ingress
  namespace: dh-v3
  annotations:
    nginx.ingress.kubernetes.io/proxy-body-size: 1024m  
    nginx.ingress.kubernetes.io/use-regex: "true"
spec:
  rules:
    - http:
        paths:
          - backend:
              serviceName: dhlive-external-api
              servicePort: 8080
            path: /watermark/api/v1/*      
          - backend:
              serviceName: dhlive-external-api
              servicePort: 8080
            path: /api/digitalhuman/file2image/v1/*
          - backend:
              serviceName: dhlive-external-api
              servicePort: 8080
            path: /api/digitalhuman/file2image/v2/*
          - backend:
              serviceName: dhlive-external-api
              servicePort: 8080
            path: /api/digitalhuman/external/riskcontrol/v1/*     
          - backend:
              serviceName: dhlive-external-api
              servicePort: 8080
            path: /api/digitalhuman/external/miniprogram/v1/*        
          - backend:
              serviceName: dhlive-external-api
              servicePort: 8080
            path: /api/digitalhuman/external/translate/v1/*                    