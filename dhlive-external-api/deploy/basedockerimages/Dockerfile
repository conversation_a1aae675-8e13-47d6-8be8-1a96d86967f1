# Ubuntu 22.04.4 LTS 镜像
FROM iregistry.baidu-int.com/abc-robot/dhlivebase:v20240410-2002

RUN apt update && \
    apt -y install language-pack-en && \
    locale-gen en_US.UTF-8 && update-locale LANG=en_US.UTF-8

# 设置环境变量，确保使用 UTF-8 编码
ENV LANG en_US.UTF-8
ENV LANGUAGE en_US:en
ENV LC_ALL en_US.UTF-8

WORKDIR /home/<USER>

# 添加 LibreOffice 并安装
ADD LibreOffice_24.8.0.3_Linux_x86-64_deb.tar.gz /home/<USER>
# 添加字体并刷新字体缓存
ADD fonts_windows.tar.gz /home/<USER>/fonts_windows

RUN apt update && \
    apt -y install /home/<USER>/LibreOffice_24.8.0.3_Linux_x86-64_deb/DEBS/*.deb  && \
    rm -rf /home/<USER>/LibreOffice_24.8.0.3_Linux_x86-64_deb && \
    mv /usr/local/bin/libreoffice24.8 /usr/local/bin/libreoffice && \
    # 安装 libreoffice24 依赖库
    apt -y install libnss3 libnspr4 libxslt1.1 libxslt-dev libcups2 
RUN apt -y install python3 python3-pip && \
    pip3 install --upgrade pip && \
    # 更新 pip 并安装 python-pptx
    pip3 install python-pptx && \
    # 更新字体
    mv /home/<USER>/fonts_windows /usr/share/fonts && \
    apt -y install  fontconfig && \
    fc-cache -fv
    # 安装ffmpeg
RUN apt -y install ffmpeg mupdf-tools && \
    apt-get clean

RUN apt update && \
    apt -y install libreoffice && \
    ln -s /usr/bin/libreoffice /usr/local/bin/libreoffice7.6 && \
    apt-get clean