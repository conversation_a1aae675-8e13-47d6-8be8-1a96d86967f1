package imageutils

import (
	"fmt"
	"image"
	_ "image/gif"
	"image/jpeg"
	_ "image/png"
	"io"
	"os"
	"path/filepath"

	"github.com/disintegration/imaging"
	"golang.org/x/image/draw"
	"golang.org/x/image/tiff"
	"golang.org/x/image/webp"
)

// GetImageSize returns the width and height of the image in pixels.
func GetImageSize(inputPath string) (image.Image, error) {
	var img image.Image
	var format string

	inputFile, err := os.Open(inputPath)
	if err != nil {
		return img, fmt.Errorf("failed to open image: %v", err)
	}
	defer inputFile.Close()

	switch filePathExtension(inputPath) {
	case ".webp", ".WEBP":
		img, err = webp.Decode(inputFile)
		format = "webp"
	case ".tiff", ".tif", ".TIFF", ".TIF":
		img, err = tiff.Decode(inputFile)
		format = "tiff"
	default:
		img, format, err = image.Decode(inputFile)
	}
	if err != nil {
		return img, fmt.Errorf("failed to decode %s image: %v", format, err)
	}

	return img, nil
}

func ResizeImage(img image.Image, outputPath string, width, height int) error {
	format := filePathExtension(outputPath)

	if format == ".webp" || format == ".WEBP" {
		dst := image.NewRGBA(image.Rect(0, 0, width, height))
		draw.CatmullRom.Scale(dst, dst.Bounds(), img, img.Bounds(), draw.Src, nil)

		// 创建输出文件
		outputFile, err := os.Create(outputPath)
		if err != nil {
			return err
		}
		defer outputFile.Close()

		// 设置JPEG图像质量选项
		options := &jpeg.Options{Quality: 50}
		return jpeg.Encode(outputFile, img, options)
	}

	// 使用imaging库调整图像尺寸
	resizedImage := imaging.Resize(img, width, height, imaging.NearestNeighbor)

	// 保存调整尺寸后的图像
	err := imaging.Save(resizedImage, outputPath)
	if err != nil {
		return fmt.Errorf("failed to save resized image: %w", err)
	}

	return nil
}

// filePathExtension returns the file extension of a given path.
func filePathExtension(path string) string {
	return filepath.Ext(path)
}

// ConvertToJPEG 将给定路径的图片转换为JPEG格式并保存到指定路径
func ConvertToJPEG(inputPath, outputPath string, quality int) error {
	inputFile, err := os.Open(inputPath)
	if err != nil {
		return fmt.Errorf("failed to open input image: %v", err)
	}
	defer inputFile.Close()

	var img image.Image
	var format string
	switch filePathExtension(inputPath) {
	case ".webp", ".WEBP":
		img, err = webp.Decode(inputFile)
		format = "webp"
	case ".tiff", ".tif", ".TIFF", ".TIF":
		img, err = tiff.Decode(inputFile)
		format = "tiff"
	default:
		img, format, err = image.Decode(inputFile)
	}
	if err != nil {
		return fmt.Errorf("failed to decode %s image: %v", format, err)
	}

	// 创建输出文件
	outputFile, err := os.Create(outputPath)
	if err != nil {
		return err
	}
	defer outputFile.Close()

	// 设置JPEG图像质量选项
	options := &jpeg.Options{Quality: quality}

	// 将图像以JPEG格式写入输出文件
	return jpeg.Encode(outputFile, img, options)
}

// IsJPGOrWebP 判断给定的文件路径是否指向一个JPG或WebP格式的文件。
func IsJPGOrWebP(filePath string) (bool, error) {
	// 打开文件
	file, err := os.Open(filePath)
	if err != nil {
		return false, err
	}
	defer file.Close()

	// 读取文件的前几个字节来判断文件格式
	buffer := make([]byte, 12) // JPG和WebP的签名长度不会超过12字节
	n, err := file.Read(buffer)
	if err != nil && err != io.EOF {
		return false, err
	}

	// 检查读取的字节数是否足够进行判断
	if n < 4 {
		return false, nil // 文件太小，无法判断格式
	}

	// JPG文件的签名通常是0xFF, 0xD8开头，紧接着是其他的0xFF和一个不为0x00或0xFF的字节
	if buffer[0] == 0xFF && buffer[1] == 0xD8 && buffer[2] == 0xFF {
		if buffer[3] != 0x00 && buffer[3] != 0xFF {
			return true, nil // 是JPG文件
		}
	}

	// WebP文件的签名是"RIFF"后跟四个字节，其中最后一个是"WEBP"
	if n >= 12 {
		if buffer[0] == 'R' && buffer[1] == 'I' && buffer[2] == 'F' && buffer[3] == 'F' &&
			buffer[8] == 'W' && buffer[9] == 'E' && buffer[10] == 'B' && buffer[11] == 'P' {
			return true, nil // 是WebP文件
		}
	}

	return false, nil // 不是JPG或WebP文件
}

// IsJPEGOrWebPByExtension 根据文件后缀判断文件是否为JPG或WebP格式.
func IsJPEGOrWebPByExtension(filePath string) bool {
	ext := filePathExtension(filePath)
	return ext == ".jpg" || ext == ".jpeg" || ext == ".webp" || ext == ".JPG" || ext == ".JPEG" || ext == ".WEBP"
}
