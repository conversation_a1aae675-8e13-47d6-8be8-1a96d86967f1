package handlerUtils

import (
	"acg-ai-go-common/logger"
	"acg-ai-go-common/utils"
	"bufio"
	"bytes"
	"context"
	"crypto/md5"
	"dhlive-external-api/beans/proto"
	"encoding/base64"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"net/url"
	"os"
	"os/exec"
	"path"
	"path/filepath"
	"regexp"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/google/uuid"
)

// 检查文件格式是否在支持的范围内
func CheckFileFormat(logCtx context.Context, fileFormat string, filename string) bool {
	file, err := os.Open(filename)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"CheckFileFormat Open error:%v\n", err)
		return false
	}
	defer file.Close()
	// 创建一个读取器
	reader := bufio.NewReader(file)

	// 创建一个字节切片来保存读取的数据
	buffer := make([]byte, 512)

	// 读取前512字节
	_, err = reader.Read(buffer)
	if err != nil && err != io.EOF {
		logger.Log.Errorf(utils.MMark(logCtx)+"CheckFileFormat Read error:%v\n", err)
		return false
	}

	contentType := http.DetectContentType(buffer)
	switch fileFormat {
	case ".pptx":
		if contentType == "application/vnd.openxmlformats-officedocument.presentationml.presentation" || contentType == "application/zip" {
			return true
		}

		logger.Log.Errorf(utils.MMark(logCtx)+"CheckFileFormat file type pptx encode is error type:%v\n", contentType)
		return false
	case ".pdf":
		if contentType == "application/pdf" {
			return true
		}

		logger.Log.Errorf(utils.MMark(logCtx)+"CheckFileFormat file type encode is error type:%v\n", contentType)
		return false
	default:
		return false
	}
}

// // 检查文件大小是否在限制内
func CheckFileSize(filePath string, maxSize int64) bool {
	fileInfo, err := os.Stat(filePath)
	if err != nil {
		log.Println("Error getting file info:", err)
		return false
	}

	return fileInfo.Size() <= maxSize
}

// RunPythonScript 执行指定的Python脚本并返回输出或错误
func RunPythonScript(scriptName string, args ...string) (string, error) {
	// 构建命令，这里假设python3已经安装并配置在环境变量中
	cmd := exec.Command("python3", append([]string{scriptName}, args...)...)

	// 创建缓冲区来保存命令的输出和错误
	var stdoutBuf, stderrBuf bytes.Buffer
	cmd.Stdout = &stdoutBuf
	cmd.Stderr = &stderrBuf

	// 运行命令
	err := cmd.Run()

	// 获取命令的输出和错误信息
	stdout := stdoutBuf.String()
	stderr := stderrBuf.String()

	if err != nil {
		// 如果有错误发生，返回错误信息
		return "", fmt.Errorf("error running python script,output: %v err: %v, stderr: %s", stdout, err, stderr)
	}

	// 返回脚本的输出
	return stdout, nil
}

// 获取 PPT备注信息
func GetPPTPNotes(filePath string) (proto.PPTFileInfo, error) {
	notes := proto.PPTFileInfo{}
	stdout, err := RunPythonScript("pythonscript/getPPTNotes.py", filePath)
	if err != nil {
		return notes, err
	}

	// 替换换行符 \n
	noNewlines := strings.ReplaceAll(stdout, "\n", "")
	// 替换空格（包括空格、制表符、换行符等）
	noSpaces := strings.ReplaceAll(noNewlines, " ", "")

	// 解析JSON数据
	err = json.Unmarshal([]byte(noSpaces), &notes)
	if err != nil {
		logger.Log.Errorf("getPPTPNotes %s Unmarshal err:%v\n", noSpaces, err)
		return notes, err
	}
	return notes, nil
}

// 获取 PPT 文件的页数（幻灯片数）
func getPPTPageCount(filePath string) (int, error) {
	stdout, err := RunPythonScript("pythonscript/getPPTPageCount.py", filePath)
	if err != nil {
		return 0, err
	}
	// 替换换行符 \n
	noNewlines := strings.ReplaceAll(stdout, "\n", "")
	// 替换空格（包括空格、制表符、换行符等）
	noSpaces := strings.ReplaceAll(noNewlines, " ", "")

	num, err := strconv.Atoi(noSpaces)
	if err != nil {
		return 0, fmt.Errorf("getPPTPageCount output: %s, err: %v \n ", stdout, err)
	}
	return num, nil
}

// 检查文件页数是否在限制内
func CheckPageCount(logCtx context.Context, fileFormat string, fileName string, maxPages int) (bool, error) {
	switch fileFormat {
	case ".pptx":
		count, err := getPPTPageCount(fileName)
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+"Error getting PDF page count:", err)
			return false, err
		}
		return count <= maxPages, nil
	case ".pdf":
		count, err := GetPDFPageCountFromMupdf(fileName)
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+"CheckPageCount Error getting PDF page count:%v\n", err)
			return false, err
		}
		return count <= maxPages, nil

	default:
		return false, fmt.Errorf("unsupported file format")
	}
}

var PPTtoPDFMutex sync.Mutex

func ConvertPPTtoPDF(logCtx context.Context, inputFilePath string, outputFilePath string) error {
	PPTtoPDFMutex.Lock()
	defer PPTtoPDFMutex.Unlock()

	cmd := exec.Command("libreoffice", "--headless", "--convert-to", "pdf", inputFilePath, "--outdir", outputFilePath)
	var stdoutBuf, stderrBuf bytes.Buffer
	cmd.Stdout = &stdoutBuf
	cmd.Stderr = &stderrBuf
	err := cmd.Run()
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"Error executing command: %v file:%s  Stdout:%s Stderr:%s", err, outputFilePath, stdoutBuf.String(), stderrBuf.String())
		return err
	}

	logger.Log.Infof(utils.MMark(logCtx)+"Converted %s to PDF successfully. Stdout: %s Stderr: %s", outputFilePath, stdoutBuf.String(), stderrBuf.String())
	return nil
}

func ConvertPPTtoPDFByLibreoffice7(logCtx context.Context, inputFilePath string, outputFilePath string) error {
	PPTtoPDFMutex.Lock()
	defer PPTtoPDFMutex.Unlock()

	cmd := exec.Command("libreoffice7.6", "--headless", "--convert-to", "pdf", inputFilePath, "--outdir", outputFilePath)
	var stdoutBuf, stderrBuf bytes.Buffer
	cmd.Stdout = &stdoutBuf
	cmd.Stderr = &stderrBuf
	err := cmd.Run()
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"Error executing command: %v file:%s  Stdout:%s Stderr:%s", err, outputFilePath, stdoutBuf.String(), stderrBuf.String())
		return err
	}

	logger.Log.Infof(utils.MMark(logCtx)+"Converted %s to PDF successfully. Stdout: %s Stderr: %s", outputFilePath, stdoutBuf.String(), stderrBuf.String())
	return nil
}

// MudrawToImage 使用mudraw命令将PDF文件的指定页面转换为图片
// 参数:
// - pdfFile: PDF文件的路径
// - outputFile: 输出图片文件的路径（包括文件名和后缀）
// - pageIndex: 要转换的页面索引（从0开始）
// 返回值:
// - error: 如果发生错误，则返回错误信息；否则返回nil
func MudrawToImage(pdfFile, outputFile string) error {
	// 构造mudraw命令 如果图片过长可能导致前端显示不全，需要调整分辨率
	cmd := exec.Command("mutool", "draw", "-o", outputFile, "-r", "300", pdfFile) // 注意mudraw的页面索引从1开始

	// 创建一个新的缓冲来存储命令的输出
	var out bytes.Buffer
	var stderr bytes.Buffer
	cmd.Stdout = &out
	cmd.Stderr = &stderr
	// 运行命令
	err := cmd.Run()
	if err != nil {
		return fmt.Errorf("执行mudraw命令失败: %v\n命令输出: %s", err, out.String())
	}

	// 检查输出是否有错误（虽然这里可能不是必要的，因为 err 通常会捕获错误）
	if strings.TrimSpace(out.String()) != "" {
		logger.Log.Errorf("Non-empty stdout from MudrawToImage command: %s", out.String())
		return fmt.Errorf("non-empty output from MudrawToImage command: %s", out.String())
	}

	// 如果没有错误，则返回nil
	return nil
}

// MudrawToImageByPage 使用 mutool 将 PDF 指定页面转换为图片
// 参数:
// - pdfFile: PDF文件路径
// - outputFile: 输出图片路径（包括文件名和后缀）
// - pageIndex: 要转换的页面索引（从0开始）
// - dpi: 分辨率（默认为 300 DPI）
func MudrawToImageByPage(pdfFile, outputFile string, pageIndex, dpi int) error {
	// 构造 mutool draw 命令
	cmd := exec.Command("mutool", "draw", "-o", outputFile, "-r", strconv.Itoa(dpi), pdfFile, strconv.Itoa(pageIndex))

	// 捕获输出和错误
	var out bytes.Buffer
	var stderr bytes.Buffer
	cmd.Stdout = &out
	cmd.Stderr = &stderr

	// 执行命令
	err := cmd.Run()
	if err != nil {
		return fmt.Errorf("执行 mutool 失败: %v\n错误输出: %s", err, stderr.String())
	}

	// 检查输出是否有错误（虽然这里可能不是必要的，因为 err 通常会捕获错误）
	if strings.TrimSpace(out.String()) != "" {
		logger.Log.Errorf("Non-empty stdout from MudrawToImage command: %s", out.String())
		return fmt.Errorf("non-empty output from MudrawToImage command: %s", out.String())
	}

	// 命令执行成功
	return nil
}

// GetPDFPageCount 使用mutool命令获取PDF文件的页面数
func GetPDFPageCountFromMupdf(pdfFile string) (int, error) {
	// 构造mutool命令
	cmd := exec.Command("mutool", "info", pdfFile)

	// 执行命令并捕获输出
	var out bytes.Buffer
	cmd.Stdout = &out
	err := cmd.Run()
	if err != nil {
		return 0, fmt.Errorf("执行mutool命令失败: %v", err)
	}

	// 从输出中解析页面数
	output := out.String()
	re := regexp.MustCompile(`Pages:\s+(\d+)`)
	matches := re.FindStringSubmatch(output)
	if len(matches) < 2 {
		return 0, fmt.Errorf("无法从输出中解析页面数")
	}

	// 将页面数字符串转换为整数
	pageCount, err := strconv.Atoi(matches[1])
	if err != nil {
		return 0, fmt.Errorf("无法将页面数字符串转换为整数: %v", err)
	}

	return pageCount, nil
}

// DeleteFile 删除指定名称的文件
func DeleteFile(filename string) error {
	err := os.Remove(filename)
	if err != nil {
		return err
	}
	return nil
}

func FileExists(filename string) (bool, error) {
	_, err := os.Stat(filename)
	if err != nil {
		if os.IsNotExist(err) {
			return false, err
		}
		return false, err
	}
	return true, nil
}

// ReadFileToBytes reads the contents of a file into a byte slice.
func ReadFileToBytes(filename string) ([]byte, error) {
	// 打开文件
	file, err := os.Open(filename)
	if err != nil {
		return nil, fmt.Errorf("failed to open file %s: %w", filename, err)
	}
	defer file.Close()

	// 读取文件内容到字节切片
	data, err := io.ReadAll(file)
	if err != nil {
		return nil, fmt.Errorf("failed to read file %s: %w", filename, err)
	}

	return data, nil
}

// DownloadFile 从给定的URL下载文件并保存到本地路径
func DownloadFile(url string, destPath string) error {
	// 创建一个HTTP请求
	resp, err := http.Get(url)
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	// 检查HTTP响应状态码
	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("server returned non-200 status: %d %s", resp.StatusCode, resp.Status)
	}

	// 创建目标目录（如果不存在）
	dir := filepath.Dir(destPath)
	if err := os.MkdirAll(dir, 0755); err != nil {
		return err
	}

	// 创建文件
	outFile, err := os.Create(destPath)
	if err != nil {
		return err
	}
	defer outFile.Close()

	// 复制响应流到文件
	_, err = io.Copy(outFile, resp.Body)
	return err
}

func GetFileExtensionFromURL(fileURL string) (string, error) {
	// 解析URL
	parsedURL, err := url.Parse(fileURL)
	if err != nil {
		return "", err
	}

	// 从URL中提取路径部分
	filePath := parsedURL.Path

	// 获取文件后缀名，它包含前面的点（.）
	fileExtWithDot := path.Ext(filePath)

	// 去掉后缀名前面的点
	// fileExt := strings.TrimPrefix(fileExtWithDot, ".")

	return fileExtWithDot, nil
}

// GetFileSize 获取指定文件的大小
func GetFileSize(filePath string) (int64, error) {
	// 使用os.Stat获取文件信息
	fileInfo, err := os.Stat(filePath)
	if err != nil {
		return 0, fmt.Errorf("failed to get file size: %w", err)
	}

	// 返回文件大小
	return fileInfo.Size(), nil
}

func FileBase64Encode(filePath string) (string, error) {
	// 读取文件内容
	data, err := ReadFileToBytes(filePath)
	if err != nil {
		return "", fmt.Errorf("failed to read file: %w", err)
	}
	// 将文件内容编码为base64字符串
	encodedData := base64.StdEncoding.EncodeToString(data)
	return encodedData, nil
}

func GetFileBase64EncodeSize(filePath string) (int, error) {
	encodedData, err := FileBase64Encode(filePath)
	if err != nil {
		return 0, fmt.Errorf("failed to encode file: %w", err)
	}
	return len(encodedData), nil
}

// CalculateMD5 takes the path to a file and returns its MD5 hash as a hex string.
func CalculateMD5FromFile(filePath string) (string, error) {
	// 打开文件
	file, err := os.Open(filePath)
	if err != nil {
		return "", err
	}
	defer file.Close()

	// 创建一个新的MD5哈希生成器
	hash := md5.New()

	// Copy the file content to the hash object
	if _, err := io.Copy(hash, file); err != nil {
		return "", err
	}

	// 计算最终的哈希值并返回其十六进制表示
	md5sum := hash.Sum(nil)
	return hex.EncodeToString(md5sum), nil
}

// CalculateMD5 takes a string input and returns the hex representation of its MD5 hash.
func CalculateMD5FromText(text string) string {
	hasher := md5.New()                       // 创建一个新的MD5哈希生成器
	hasher.Write([]byte(text))                // 将文本转换为字节切片并写入哈希生成器
	md5Bytes := hasher.Sum(nil)               // 计算MD5哈希值，返回字节切片
	md5String := hex.EncodeToString(md5Bytes) // 将字节切片转换为十六进制字符串
	return md5String
}

func CalculateMD5FromUrl(url string, outpath string) (string, error) {
	for i := 0; i < 3; i++ {
		if err := DownloadFile(url, outpath); err != nil {
			if i == 2 {
				return "", err
			} else {
				time.Sleep(1 * time.Second)
				continue
			}
		}
	}

	md5, err := CalculateMD5FromFile(outpath)
	if err != nil {
		return "", err
	}

	return md5, nil
}

func SaveDecodeBase64(base64String string, outpath string) error {
	// 解码Base64字符串
	decodedBytes, err := base64.StdEncoding.DecodeString(base64String)
	if err != nil {
		return err
	}

	// 创建目标目录（如果不存在）
	dir := filepath.Dir(outpath)
	if err := os.MkdirAll(dir, 0755); err != nil {
		return err
	}

	// 将解码后的数据写入到文件中
	outFile, err := os.Create(outpath)
	if err != nil {
		return err
	}
	defer outFile.Close()

	// 打开文件以写入数据
	file, err := os.OpenFile(outpath, os.O_CREATE|os.O_WRONLY|os.O_TRUNC, 0644)
	if err != nil {
		return err
	}
	defer file.Close() // 确保在函数结束时关闭文件

	// 将解码后的数据写入到文件中
	_, err = file.Write(decodedBytes)
	if err != nil {
		return err
	}

	// 可选：同步文件到磁盘
	err = file.Sync()
	if err != nil {
		return err
	}

	return nil
}

func CreateFileNameFromUrl(prefix, url string) (string, error) {
	uuid := uuid.New().String()
	format, err := GetFileExtensionFromURL(url)
	if err != nil {
		return "", err
	}
	return prefix + uuid + format, nil
}
