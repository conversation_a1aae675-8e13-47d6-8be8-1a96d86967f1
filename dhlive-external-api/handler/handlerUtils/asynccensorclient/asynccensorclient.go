package asynccensorclient

import (
	"dhlive-external-api/handler/handlerUtils/synccensorclient"
)

const __documentCensorUserDefinedUrl = "https://aip.baidubce.com/rest/2.0/solution/v1/solution/document/v1/submit"
const __longVideoCensorUserDefinedUrl = "https://aip.baidubce.com/rest/2.0/solution/v1/video_censor/v1/video/submit"
const __longVideoPullCensorUserDefinedUrl = "https://aip.baidubce.com/rest/2.0/solution/v1/video_censor/v1/video/pull"
const __longAudioCensorUserDefinedUrl = "https://aip.baidubce.com/rest/2.0/solution/v1/async_voice/submit"
const __longAudioPullCensorUserDefinedUrl = "https://aip.baidubce.com/rest/2.0/solution/v1/async_voice/pull"

type AsyncContentCensorClient struct {
	auth synccensorclient.Auth
}

func NewClient(ak string, sk string) *AsyncContentCensorClient {
	var client AsyncContentCensorClient
	client.auth = synccensorclient.Auth{}
	client.auth.InitAuth(ak, sk)
	return &client
}

func (client *AsyncContentCensorClient) DocumentCensorSubmit(fileName, filePassword, fileBase64, noticeUrl string, options map[string]interface{}) (result string) {
	data := make(map[string]string)
	data["fileName"] = fileName
	data["fileBase64"] = fileBase64
	data["noticeUrl"] = noticeUrl

	if len(filePassword) > 0 {
		data["filePassword"] = filePassword
	}

	for key, val := range options {
		switch val := val.(type) {
		case string:
			data[key] = val
		}
	}

	return synccensorclient.PostUrlForm(__documentCensorUserDefinedUrl, data, &client.auth)
}

func (client *AsyncContentCensorClient) DocumentCensorSubmitFromUrl(fileName, filePassword, url, noticeUrl string, options map[string]interface{}) (result string) {
	data := make(map[string]string)
	data["fileName"] = fileName
	data["url"] = url
	data["noticeUrl"] = noticeUrl

	if len(filePassword) > 0 {
		data["filePassword"] = filePassword
	}

	for key, val := range options {
		switch val := val.(type) {
		case string:
			data[key] = val
		}
	}

	return synccensorclient.PostUrlForm(__documentCensorUserDefinedUrl, data, &client.auth)
}

func (client *AsyncContentCensorClient) LongVideoCensorSubmit(url, noticeUrl string, options map[string]interface{}) string {
	data := make(map[string]string)
	data["url"] = url
	data["noticeUrl"] = noticeUrl

	for key, val := range options {
		switch val := val.(type) {
		case string:
			data[key] = val
		}
	}
	res := synccensorclient.PostUrlForm(__longVideoCensorUserDefinedUrl, data, &client.auth)

	return res
}

func (client *AsyncContentCensorClient) LongVideoCensorPull(taskId string, options map[string]interface{}) string {
	data := make(map[string]string)
	data["taskId"] = taskId

	for key, val := range options {
		switch val := val.(type) {
		case string:
			data[key] = val
		}
	}
	res := synccensorclient.PostUrlForm(__longVideoPullCensorUserDefinedUrl, data, &client.auth)

	return res
}

func (client *AsyncContentCensorClient) LongAudioCensorSubmit(url, noticeUrl, format, rate string, options map[string]interface{}) string {
	data := make(map[string]string)
	data["url"] = url
	data["noticeUrl"] = noticeUrl
	data["fmt"] = format
	data["rate"] = rate

	for key, val := range options {
		switch val := val.(type) {
		case string:
			data[key] = val
		}
	}
	res := synccensorclient.PostUrlForm(__longAudioCensorUserDefinedUrl, data, &client.auth)

	return res
}

func (client *AsyncContentCensorClient) LongAudioCensorPull(taskId string, options map[string]interface{}) string {
	data := make(map[string]string)
	data["taskId"] = taskId

	for key, val := range options {
		switch val := val.(type) {
		case string:
			data[key] = val
		}
	}
	res := synccensorclient.PostUrlForm(__longAudioPullCensorUserDefinedUrl, data, &client.auth)

	return res
}
