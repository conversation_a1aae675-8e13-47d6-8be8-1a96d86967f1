package ffmpegutils

import (
	"acg-ai-go-common/logger"
	"acg-ai-go-common/storage"
	"bufio"
	"bytes"
	"dhlive-external-api/handler/handlerUtils"
	"encoding/json"
	"fmt"
	"os"
	"os/exec"
	"path/filepath"
	"regexp"
	"strconv"
	"strings"
	"time"

	"github.com/google/uuid"
)

// FFprobeInfo 封装了从ffprobe命令中获取的信息
type FFprobeInfo struct {
	CodecName  string
	SampleRate string
	Error      error
}

// VideoInfo 包含视频文件的一些基本信息，如时长
type VideoInfo struct {
	Duration float64 `json:"duration"` // 视频时长（秒）
}

// ExecuteFFprobe 执行ffprobe命令并返回编解码器名称、采样率和错误信息
func ExecuteFFprobe(filePath string) FFprobeInfo {
	// 构建ffprobe命令
	cmd := exec.Command("ffprobe", "-v", "error", "-select_streams", "a:0", "-show_entries", "stream=codec_name,sample_rate", "-of", "default=noprint_wrappers=1:nokey=1", filePath)

	stdoutPipe, err := cmd.StdoutPipe()
	if err != nil {
		return FFprobeInfo{Error: fmt.Errorf("failed to create stdout pipe: %w", err)}
	}

	if err := cmd.Start(); err != nil {
		return FFprobeInfo{Error: fmt.Errorf("failed to start ffprobe: %w", err)}
	}

	scanner := bufio.NewScanner(stdoutPipe)
	var codecName, sampleRate string
	lineCount := 0

	for scanner.Scan() {
		lineCount++
		line := scanner.Text()

		switch lineCount {
		case 1:
			if !isValidCodecName(line) {
				return FFprobeInfo{Error: fmt.Errorf("invalid codec name: %s", line)}
			}
			codecName = strings.ToLower(line)
		case 2:
			if !isValidSampleRate(line) {
				return FFprobeInfo{Error: fmt.Errorf("invalid sample rate: %s", line)}
			}
			sampleRate = strings.ToLower(line)
		default:
			// Unexpected additional output, might indicate a problem or a change in ffprobe's output format
			return FFprobeInfo{Error: fmt.Errorf("unexpected output from ffprobe: %s", line)}
		}
	}

	if err := scanner.Err(); err != nil {
		return FFprobeInfo{Error: fmt.Errorf("error reading ffprobe output: %w", err)}
	}

	if err := cmd.Wait(); err != nil {
		return FFprobeInfo{Error: fmt.Errorf("ffprobe exited with error: %w", err)}
	}

	if lineCount < 2 {
		return FFprobeInfo{Error: fmt.Errorf("insufficient output from ffprobe (got %d lines, expected at least 2)", lineCount)}
	}

	return FFprobeInfo{
		CodecName:  codecName,
		SampleRate: sampleRate,
	}
}

// isValidCodecName checks if the given string is a valid codec name.
// This is a simple example and may need to be adjusted based on actual requirements.
func isValidCodecName(codecName string) bool {
	// Add your validation logic here, for example, checking that the codec name is not empty.
	return codecName != ""
}

// isValidSampleRate checks if the given string is a valid sample rate.
// This is a simple example and may need to be adjusted based on actual requirements.
func isValidSampleRate(sampleRate string) bool {
	// Use a regular expression to validate that the sample rate is a positive integer.
	matched, _ := regexp.MatchString(`^\d+$`, sampleRate)
	return matched
}

func CalculateMd5AndFormatAndRate(url, cachePath string) (FFprobeInfo, string, error) {
	var info FFprobeInfo
	uuid := uuid.New().String()
	format, err := handlerUtils.GetFileExtensionFromURL(url)
	if err != nil {
		return info, "", err
	}

	if cachePath == "" {
		cachePath = "./cache/"
	}

	path := cachePath + uuid + format

	if err = storage.RetryDownloadFile(url, path, 3); err != nil {
		return info, "", err
	}
	defer os.Remove(path)

	md5, err := handlerUtils.CalculateMD5FromFile(path)
	if err != nil {
		return info, "", err
	}

	// ffprobe 解析失败重试三次，防止ffprobe解析时出现异常
	for i := 0; i < 3; i++ {
		if info = ExecuteFFprobe(path); info.Error != nil {
			time.Sleep(100 * time.Millisecond)
			continue
		}
		break
	}

	return info, md5, nil
}

// CompressVideo 使用FFmpeg压缩视频文件，可以选择是否更改输出格式
func CompressVideo(inputFile, outputFile, crf string) error {
	//cmd := exec.Command("ffmpeg", "-i", inputFile, "-vcodec", codec, "-crf", crf, "-preset", "veryfast", "-vf", "scale=1280:720", outputFile)
	cmd := exec.Command("ffmpeg", "-i", inputFile, "-crf", crf, "-preset", "veryfast", "-r", "1", "-an", outputFile)
	output, err := cmd.CombinedOutput()
	if err != nil {
		return fmt.Errorf("error compressing video: %w\n%s", err, output)
	}

	return nil
}

// GetVideoDuration 使用ffprobe获取视频文件的时长
func GetVideoDuration(filePath string) (float64, error) {
	// 构造ffprobe命令
	cmd := exec.Command("ffprobe", "-v", "error", "-show_entries", "format=duration", "-of", "default=noprint_wrappers=1:nokey=1", filePath)

	// 执行命令并捕获输出
	var out bytes.Buffer
	cmd.Stdout = &out
	err := cmd.Run()
	if err != nil {
		return 0, fmt.Errorf("failed to run ffprobe: %w", err)
	}

	// 解析输出以获取时长
	var duration float64
	if err := json.Unmarshal(out.Bytes(), &duration); err != nil {
		// 注意：这里实际上不是直接解析为float64，因为ffprobe的输出是一个简单的数值字符串
		// 所以我们需要先将其转换为字符串，然后再转换为float64
		strDuration := string(bytes.TrimSpace(out.Bytes()))
		duration, err = strconv.ParseFloat(strDuration, 64)
		if err != nil {
			return 0, fmt.Errorf("failed to parse duration from ffprobe output: %w", err)
		}
	}

	return duration, nil
}

// GetAudioDuration 使用ffprobe获取音频文件的时长
func GetAudioDuration(filePath string) (float64, error) {
	// 构建ffprobe命令
	cmd := exec.Command("ffprobe", "-v", "quiet", "-print_format", "json", "-show_format", "-show_streams", filePath)

	// 执行命令并捕获输出
	var out bytes.Buffer
	cmd.Stdout = &out
	err := cmd.Run()
	if err != nil {
		return 0, fmt.Errorf("ffprobe failed: %w", err)
	}

	// 解析ffprobe的输出
	// 这里假设ffprobe的输出是JSON格式，并且我们关注"streams"数组中的第一个元素的"duration"字段
	// 注意：实际使用中，可能需要更复杂的逻辑来处理多个流或多个音频轨道的情况
	// 这里为了简化，我们只处理最常见的场景
	type Stream struct {
		Duration string `json:"duration"`
	}

	type Format struct {
		Streams []Stream `json:"streams"`
	}

	var result Format
	if err := json.Unmarshal(out.Bytes(), &result); err != nil {
		return 0, fmt.Errorf("failed to unmarshal ffprobe output: %w", err)
	}

	if len(result.Streams) == 0 {
		return 0, fmt.Errorf("no streams found in the audio file")
	}

	durationStr := result.Streams[0].Duration

	// 将duration字符串转换为秒
	// ffprobe的duration输出可能是以秒为单位的小数，或者包含时:分:秒的字符串
	// 这里为了简化，我们假设duration总是以秒为单位的小数
	// 如果需要处理更复杂的格式，可能需要使用正则表达式来提取时间部分
	duration, err := strconv.ParseFloat(durationStr, 64)
	if err != nil {
		// 如果转换失败，可能是因为duration不是预期的格式，这里返回一个错误
		return 0, fmt.Errorf("failed to parse duration: %w", err)
	}

	return duration, nil
}

// ExtractAudio 从视频文件中提取音频，返回音频文件路径和可能的错误
func ExtractAudio(videoFilePath string) (string, error) {
	// 确保视频文件存在
	if _, err := os.Stat(videoFilePath); os.IsNotExist(err) {
		return "", fmt.Errorf("video file does not exist: %v", err)
	}

	// 获取视频文件的目录和文件名
	dir, filename := filepath.Split(videoFilePath)
	// 获取文件的扩展名并去掉
	ext := filepath.Ext(filename)
	base := filename[:len(filename)-len(ext)]

	// 定义输出音频文件的路径，默认保存为同名的 MP3 文件
	audioFilePath := filepath.Join(dir, base+".mp3")

	// 创建 FFmpeg 命令来提取音频
	cmd := exec.Command("ffmpeg", "-i", videoFilePath, "-vn", "-acodec", "mp3", audioFilePath)

	// 执行命令并捕获错误
	err := cmd.Run()
	if err != nil {
		return "", fmt.Errorf("error extracting audio: %v", err)
	}

	// 返回生成的音频文件路径
	return audioFilePath, nil
}

func GetAudioChannel(audioPath string) (int, error) {
	cmd := exec.Command("ffprobe", "-v", "error", "-show_entries", "stream=channels", "-of", "json", audioPath)
	var out bytes.Buffer
	var errbuf bytes.Buffer
	cmd.Stdout = &out
	cmd.Stderr = &errbuf
	err := cmd.Run()
	if err != nil {
		return 0, fmt.Errorf("failed to run ffprobe: %w", err)
	}

	if errbuf.String() != "" {
		return 0, fmt.Errorf("ffprobe error: %s", errbuf.String())
	}
	type Streams struct {
		Channels int `json:"channels"`
	}
	type FfprobeStream struct {
		Streams []Streams `json:"streams"`
	}
	fstream := FfprobeStream{}
	if err := json.Unmarshal(out.Bytes(), &fstream); err != nil {
		return 0, fmt.Errorf("failed to parse channel count from ffprobe output: %w", err)
	}
	if len(fstream.Streams) <= 0 {
		return 0, fmt.Errorf("no streams found in the audio file")
	}
	return fstream.Streams[0].Channels, nil
}

func AudioTranscoding(audioPath, dstAudioPath string) error {
	cmd := exec.Command("ffmpeg", "-i", audioPath, "-ac", "1", "-ar", "16000", "-f", "wav", dstAudioPath)

	var out bytes.Buffer
	cmd.Stdout = &out
	err := cmd.Run()
	if err != nil {
		return fmt.Errorf("error transcoding audio: %v", err)
	}

	logger.Log.Infof("audio transcoding success. output: %s", out.String())
	return nil
}

func IsVideoContainsAudio(videopath string) (bool, error) {
	cmd := exec.Command("ffprobe", "-v", "error", "-select_streams", "a", "-show_entries", "stream=index", "-of", "default=noprint_wrappers=1:nokey=1", videopath)

	var out bytes.Buffer
	cmd.Stdout = &out
	err := cmd.Run()
	if err != nil {
		return false, fmt.Errorf("error transcoding audio: %v", err)
	}

	if out.String() == "" {
		return false, nil
	}
	text := out.String()
	reg := regexp.MustCompile(`\s+`)
	temptext := reg.ReplaceAllString(text, "")
	index, err := strconv.Atoi(temptext)
	if err != nil {
		return false, err
	}

	if index < 0 {
		return false, nil
	}

	logger.Log.Infof("audio transcoding success. output: %s", out.String())
	return true, nil
}

// TranscodeVideo 将输入的视频文件转码为 H.264 编码并输出到指定文件
func TranscodeVideo(inputFile, outputFile string) error {
	// 构建 FFmpeg 命令：-i 输入文件 -c:v libx264 -preset fast -crf 22 输出文件
	cmd := exec.Command("ffmpeg", "-i", inputFile, "-c:v", "libx264", "-preset", "fast", "-crf", "22", outputFile)

	// 获取命令的标准输出和标准错误输出
	cmd.Stdout = os.Stdout
	cmd.Stderr = os.Stderr

	// 执行命令并返回执行结果
	if err := cmd.Run(); err != nil {
		return fmt.Errorf("video transcoding failed: %v", err)
	}

	return nil
}
