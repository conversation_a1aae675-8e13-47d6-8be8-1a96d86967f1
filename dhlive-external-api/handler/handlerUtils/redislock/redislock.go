package redislock

import (
	"context"
	"fmt"
	"time"

	redis "github.com/redis/go-redis/v9"
)

type RedisLock struct {
	rdb      *redis.Client
	lockKey  string
	lockVal  string
	duration time.Duration
}

func NewRedisLock(rdb *redis.Client, lockKey string, duration time.Duration) *RedisLock {
	return &RedisLock{
		rdb:      rdb,
		lockKey:  lockKey,
		lockVal:  fmt.Sprintf("%d", time.Now().UnixNano()),
		duration: duration,
	}
}

func (rl *RedisLock) Lock(ctx context.Context) (bool, error) {
	result, err := rl.rdb.SetNX(ctx, rl.lockKey, rl.lockVal, rl.duration).Result()
	if err != nil {
		return false, err
	}
	return result, nil
}

func (rl *RedisLock) Unlock(ctx context.Context) error {
	script := `
		if redis.call("get", KEYS[1]) == ARGV[1] then
			return redis.call("del", KEYS[1])
		else
			return 0
		end
	`
	keys := []string{rl.lockKey}
	args := []interface{}{rl.lockVal}
	_, err := rl.rdb.Eval(ctx, script, keys, args...).Result()
	return err
}
