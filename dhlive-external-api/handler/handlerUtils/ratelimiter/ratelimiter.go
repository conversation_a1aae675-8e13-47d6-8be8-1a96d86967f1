package ratelimiter

import (
	"acg-ai-go-common/utils/redisproxy"
	"context"
	"fmt"
	"log"
	"time"
)

// Allow 判断是否通过限流
func Allow(key string, limit int, expirationTime time.Duration) (bool, error) {
	redisproxy := redisproxy.GetRedisProxy()
	current, err := redisproxy.Rdb.Incr(context.Background(), key).Result()
	if err != nil {
		return false, err
	}

	// 第一次添加限流计数 需要设置过期时间
	// 过期时间到了，计数重制
	if current == 1 {
		redisproxy.Rdb.Expire(context.Background(), key, expirationTime)
	}

	return current <= int64(limit), nil
}

func AllowFromLua(key string, limit int, expirationTime int64) (bool, error) {
	redisproxy := redisproxy.GetRedisProxy()
	// Lua 脚本内容
	script := `
	local count = redis.call('GET', KEYS[1])
	if count then
		count = tonumber(count) + 1
	else
		count = 1
	end
	if count > tonumber(ARGV[1]) then
		return {false, count}
	end
	redis.call('SET', KEYS[1], count)
	redis.call('EXPIRE', KEYS[1], tonumber(ARGV[2]))
	return {true, count}
	`
	keys := []string{key}
	argv := []interface{}{limit, expirationTime}

	// 执行 Lua 脚本
	result, err := redisproxy.Rdb.Eval(context.Background(), script, keys, argv).Result()
	if err != nil {
		log.Fatalf("Failed to execute Lua script: %v", err)
	}

	// 处理 Lua 脚本的返回结果
	// 假设 Lua 脚本返回的是一个数组，第一个元素是布尔值，第二个是计数
	results, ok := result.([]interface{})
	if !ok {
		return false, fmt.Errorf("failed to parse Lua script results: %v", result)
	}

	// 转换结果中的布尔值和计数
	if len(results) >= 2 {
		success, ok := results[0].(int64) // Lua 中的 true/false 会被转换成 1/0
		if !ok {
			return false, fmt.Errorf("failed to parse Lua script results: %v", result)
		}
		successBool := success == 1

		// count, ok := results[1].(int64)
		// if !ok {
		// 	return false, fmt.Errorf("failed to parse Lua script results: %v", result)
		// }

		return successBool, nil
	} else {
		return false, fmt.Errorf("failed to parse Lua script results: %v", result)
	}
}

// DecrementCounterIfGreaterThanZero 减少指定键的计数，如果计数大于0
func DecrementCounter(key string) (int64, error) {
	redisproxy := redisproxy.GetRedisProxy()

	script := `
	local count = redis.call('GET', KEYS[1])
	if count then
		count = tonumber(count)
		if count > 0 then
			count = count - 1
			redis.call('SET', KEYS[1], count)
		end
		return count
	else
		return 0
	end
	`

	// 准备Lua脚本的参数
	keys := []string{key}
	argv := []interface{}{}

	// 执行Lua脚本
	result, err := redisproxy.Rdb.Eval(context.Background(), script, keys, argv).Result()
	if err != nil {
		return 0, fmt.Errorf("failed to execute Lua script: %w", err)
	}

	// 将结果转换为int64类型
	count, ok := result.(int64)
	if !ok {
		return 0, fmt.Errorf("failed to parse Lua script result: %w", err)
	}

	return count, nil
}
