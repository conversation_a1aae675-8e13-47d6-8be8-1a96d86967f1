package campaign

import (
	"acg-ai-go-common/global"
	"acg-ai-go-common/gomysql"
	rdsv9 "acg-ai-go-common/goredis/rds-v9"
	"acg-ai-go-common/logger"
	"acg-ai-go-common/utils"
	"acg-ai-go-common/utils/redisproxy"
	"context"
	"dhlive-external-api/beans/enum"
	"dhlive-external-api/beans/model"
	"dhlive-external-api/beans/proto"
	config "dhlive-external-api/conf"
	dh_user "dhlive-external-api/handler/dh-user"
	"dhlive-external-api/handler/handlerUtils"
	"dhlive-external-api/handler/i18n/respi18n"
	"dhlive-external-api/handler/kafkaproxy"
	"fmt"
	"net/http"
	"sync"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/segmentio/kafka-go"
)

const (
	i18nSubTag = "campaign.err_msg"
)

var (
	kafkaProducerOnce sync.Once
	kafkaProxy        *kafkaproxy.KafkaProxy
)

func InitKafkaProxy() {
	GetKafkaProducer()
	kafkaProxy.StartConsumingWithGracefulShutdown(DealBenefitQueue)
}

func GetKafkaProducer() *kafkaproxy.KafkaProxy {
	kafkaProducerOnce.Do(func() {
		kafkaProxy = kafkaproxy.InitKafka([]string{config.LocalConfig.KafkaSetting.BootstrapServers}, config.LocalConfig.KafkaSetting.CredentialsPath)
		// 初始化消费者
		kafkaProxy.InitConsumers([]string{config.LocalConfig.KafkaSetting.BootstrapServers}, "campagin-group", []string{"user_first_purchase", "user_video_create"})
	})
	return kafkaProxy
}

// 获取活动分享链接
func GetShareLink(c *gin.Context) {
	traceID := c.GetHeader(global.HeaderTraceID)
	targetLanguage := c.GetHeader("Language")

	logCtx := context.WithValue(context.TODO(), utils.CtxKeyLogID, traceID)
	accountID := c.GetString("AccountId")
	if len(accountID) == 0 {
		accountID = "test_account"
	}

	res := proto.GetShareLinkResponse{
		LogID:     utils.GetLogID(logCtx),
		ShareLink: "",
	}

	req := proto.GetShareLinkRequst{}
	if err := c.ShouldBind(&req); err != nil {
		c.JSON(http.StatusOK, proto.NewCommDataRsp(100001, respi18n.TransformResponseLocaleByDefault(logCtx, targetLanguage, "参数异常"), res))
		return
	}

	// TODO: 后期如果用户量大，可以考虑添加 redis 缓存
	// 查找用户是否创建过邀请码
	if ciCode, err := (&model.CampaignInviteCode{}).GetCampaignInviteCode(gomysql.DB, req.CampaignType, accountID); err == nil {
		res.ShareLink = req.BaseUrl + "?inviteCode=" + ciCode.InviteCode
		logger.CtxLog(c).Infof("GetShareLink: %s", res.ShareLink)
		c.JSON(http.StatusOK, proto.NewSuccessRsp(res))
		return
	}

	// 创建邀请码
	inviteCode := uuid.New().String()
	ciCode := &model.CampaignInviteCode{
		CampaignType: req.CampaignType,
		UserID:       accountID,
		InviteCode:   inviteCode,
	}

	if err := ciCode.CreateCampaignInviteCode(gomysql.DB); err != nil {
		logger.CtxLog(c).Errorf("CreateCampaignInviteCode err: %v", err)
		c.JSON(http.StatusOK, proto.NewCommDataRsp(100002, respi18n.TransformResponseLocaleBySubTag(logCtx, i18nSubTag, targetLanguage, "创建邀请码失败"), res))
		return
	}

	res.ShareLink = req.BaseUrl + "?inviteCode=" + inviteCode
	c.JSON(http.StatusOK, proto.NewSuccessRsp(res))
}

// 被邀请人与邀请人绑定
func BindInvite(c *gin.Context) {
	traceID := c.GetHeader(global.HeaderTraceID)
	targetLanguage := c.GetHeader("Language")
	logCtx := context.WithValue(context.TODO(), utils.CtxKeyLogID, traceID)
	accountID := c.GetString("AccountId")
	if len(accountID) == 0 {
		accountID = "test_account1"
	}
	userCreateTime := c.GetTime("UserCreateTime")

	res := proto.BindInviteResponse{
		LogID: utils.GetLogID(logCtx),
	}

	req := proto.BindInviteRequest{}
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusOK, proto.NewCommDataRsp(100001, respi18n.TransformResponseLocaleByDefault(logCtx, targetLanguage, "参数异常"), res))
		return
	}

	// 判断用户是否是新注册用户
	// userCreateTime 在1分钟之内创建的算是新用户
	if time.Since(userCreateTime) > time.Minute {
		logger.CtxLog(c).Errorf("not a new user")
		c.JSON(http.StatusOK, proto.NewCommDataRsp(100002, respi18n.TransformResponseLocaleBySubTag(logCtx, i18nSubTag, targetLanguage, "不是新用户"), res))
		return
	}

	// 使用锁机制，防止同一个用户在短时间内重复绑定
	redisKey := fmt.Sprintf("%s:external:campaign:shareLink:%s", handlerUtils.GetNameByRunEnv(), accountID)
	spanLock := rdsv9.DefaultSpinLock(c, redisproxy.GetRedisProxy().Rdb, redisKey, "")
	if ok, err := spanLock.TryLock(); err != nil {
		logger.CtxLog(c).Errorf("GetShareLink TryLock err: %v", err)
		c.JSON(http.StatusOK, proto.NewCommDataRsp(100003, respi18n.TransformResponseLocaleBySubTag(logCtx, i18nSubTag, targetLanguage, "邀请码绑定失败,请重试"), res))
		return
	} else if !ok {
		c.JSON(http.StatusOK, proto.NewCommDataRsp(100004, "邀请码在绑定中", res))
		return
	}
	defer spanLock.Unlock()

	// 查找数据库中是否存在绑定关系
	ciCode, err := (&model.CampaignInviteCode{}).GetCampaignInviteCodeByInviteCode(gomysql.DB, req.InviteCode)
	if err != nil {
		logger.CtxLog(c).Errorf("GetCampaignInviteCodeByInviteCode err: %v", err)
		c.JSON(http.StatusOK, proto.NewCommDataRsp(100005, respi18n.TransformResponseLocaleBySubTag(logCtx, i18nSubTag, targetLanguage, "邀请码不存在"), res))
		return
	}

	// 判断邀请人是否为自己
	if ciCode.UserID == accountID {
		logger.CtxLog(c).Errorf("don't invite yourself")
		c.JSON(http.StatusOK, proto.NewCommDataRsp(100006, respi18n.TransformResponseLocaleBySubTag(logCtx, i18nSubTag, targetLanguage, "不能邀请自己"), res))
		return
	}

	// 判断是否已经绑定过]
	if _, err := (&model.CampaignBenefit{}).GetCampaignBenefitByInvitee(gomysql.DB, ciCode.CampaignType, enum.BenitTypeLogin, accountID); err == nil {
		logger.CtxLog(c).Errorf("already bind")
		c.JSON(http.StatusOK, proto.NewCommDataRsp(100007, respi18n.TransformResponseLocaleBySubTag(logCtx, i18nSubTag, targetLanguage, "已经绑定过邀请"), res))
		return
	}

	// 创建绑定记录
	camBenefit := &model.CampaignBenefit{
		CampaignType:   ciCode.CampaignType,
		BenefitType:    enum.BenitTypeLogin,
		InviteeUserID:  accountID,
		InviteUserID:   ciCode.UserID,
		InviteCode:     ciCode.InviteCode,
		BenefitContent: 4 * 60, // 4分钟 单位秒
		GrantStatus:    enum.BenefitStatusSuccess,
		FailureReason:  "",
	}

	if err = grantBenefit(logCtx, camBenefit, "注册登录权益"); err != nil {
		logger.CtxLog(c).Errorf("CreateCampaignBenefit err: %v", err)
		c.JSON(http.StatusOK, proto.NewCommDataRsp(100008, respi18n.TransformResponseLocaleBySubTag(logCtx, i18nSubTag, targetLanguage, "绑定邀请失败"), res))
		return
	}

	c.JSON(http.StatusOK, proto.NewSuccessRsp(res))
}

// 作为消费者，获取权益消息队列，处理权益
func DealBenefitQueue(m kafka.Message) {
	logCtx := context.WithValue(context.TODO(), utils.CtxKeyLogID, string(m.Value))
	topic := m.Topic
	// 使用锁机制，防止同一个用户在短时间内重复绑定
	logger.Log.Infof(utils.MMark(logCtx)+" DealBenefitQueue topic: %s,key: %s value: %s", topic, string(m.Key), string(m.Value))
	redisKey := fmt.Sprintf("%s:external:campaign:benefitQueue:%s:%s", handlerUtils.GetNameByRunEnv(), topic, string(m.Value))
	spanLock := rdsv9.DefaultSpinLock(nil, redisproxy.GetRedisProxy().Rdb, redisKey, "")
	if ok, err := spanLock.TryLock(); err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+" TryLock err: %v", err)
		return
	} else if !ok {
		return
	}
	defer spanLock.Unlock()

	// 判断有没有下发过权益
	benefitType := enum.BenefitTypeNone
	switch topic {
	case "user_first_purchase": // 首次购买权益
		benefitType = enum.BenitTypeFirstBuy

	case "user_video_create": // 视频创作权益
		benefitType = enum.BenitTypeVideoCreate
	default:
		logger.Log.Errorf(utils.MMark(logCtx)+" topic not found, topic: %s value: %s", topic, string(m.Value))
		return
	}

	_, err := (&model.CampaignBenefit{}).GetCampaignBenefitByInvitee(gomysql.DB, "INVITE_GIFT", benefitType, string(m.Value))
	if err == nil {
		logger.Log.Infof(utils.MMark(logCtx)+" already grant benefit, topic: %s value: %s", topic, string(m.Value))
		return
	}

	// 查询登录权益下发记录，获取绑定关系
	// 创建绑定记录
	newCamBenefit := &model.CampaignBenefit{}
	name := "其他权益"
	if camBenefit, err := (&model.CampaignBenefit{}).GetCampaignBenefitByInvitee(gomysql.DB, "INVITE_GIFT",
		enum.BenitTypeLogin, string(m.Value)); err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+" GetCampaignBenefitByInvitee err: %v", err)
		return
	} else {
		newCamBenefit = &model.CampaignBenefit{
			CampaignType:  camBenefit.CampaignType,
			InviteeUserID: camBenefit.InviteeUserID,
			InviteUserID:  camBenefit.InviteUserID,
			InviteCode:    camBenefit.InviteCode,
			GrantStatus:   enum.BenefitStatusSuccess,
			FailureReason: "",
		}
		// 区分活动类型，处理不同活动类型的权益下发
		switch topic {
		case "user_first_purchase": // 首次购买权益
			newCamBenefit.BenefitContent = 15 * 60
			newCamBenefit.BenefitType = enum.BenitTypeFirstBuy
			name = "首次购买权益"
		case "user_video_create": // 视频创作权益
			newCamBenefit.BenefitContent = 6 * 60
			newCamBenefit.BenefitType = enum.BenitTypeVideoCreate
			name = "视频创作权益"
		default:
			logger.Log.Errorf(utils.MMark(logCtx)+" topic not found, topic: %s value: %s", topic, string(m.Value))
			return
		}
	}

	// 创建绑定记录
	if err := grantBenefit(logCtx, newCamBenefit, name); err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"CreateCampaignBenefit err: %v", err)
		return
	}
	logger.Log.Infof(utils.MMark(logCtx)+" DealBenefitQueue topic: %s, value: %s success", topic, string(m.Value))
}

// 下发权益
func grantBenefit(logCtx context.Context, camBenefit *model.CampaignBenefit, name string) error {
	camBenefit.GrantStatus = enum.BenefitStatusSuccess
	camBenefit.FailureReason = ""
	// 下发权益
	quotaAddReq := &dh_user.QuotaAddBenefitReq{
		QuotaType:   dh_user.QuotaAddBenefitTypeVideo,
		Name:        name,
		QuotaAmount: camBenefit.BenefitContent, // 4分钟
		Time:        372,                       // 372 天
		TimeUnit:    "DAY",
		StartTime:   time.Now().Format("2006-01-02 15:04:05"),
		AccountID:   camBenefit.InviteUserID, // 邀请人
		ProductType: camBenefit.CampaignType,
	}

	if quoRes, err := dh_user.QuotaAddBenefit(logCtx, quotaAddReq); err != nil {
		camBenefit.GrantStatus = enum.BenefitStatusFailed
		camBenefit.BenefitContent = 0
		camBenefit.FailureReason = err.Error()
	} else if quoRes.Code != 0 || !quoRes.Success {
		camBenefit.GrantStatus = enum.BenefitStatusFailed
		camBenefit.BenefitContent = 0
		camBenefit.FailureReason = quoRes.Message.Global
	}

	if err := camBenefit.CreateCampaignBenefit(gomysql.DB); err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+" CreateCampaignBenefit err: %v", err)
		return err
	}

	return nil
}

func AddKafakaQueue(c *gin.Context) {
	traceID := c.GetHeader(global.HeaderTraceID)
	logCtx := context.WithValue(context.TODO(), utils.CtxKeyLogID, traceID)
	res := &proto.AddKafakaQueueResponse{
		LogID: utils.GetLogID(logCtx),
	}

	req := proto.AddKafakaQueueRequest{}
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusOK, proto.NewCommServiceRsp(100001, "参数错误", res))
		return
	}
	logger.CtxLog(c).Infof("AddKafakaQueue topic: %s, key: %s, value: %s", req.TopicName, req.Key, req.Value)
	kafkaProducer := GetKafkaProducer()
	if err := kafkaProducer.SendMessage(req.TopicName, req.Key, req.Value, nil); err != nil {
		logger.CtxLog(c).Errorf("AddKafakaQueue err: %v", err)
		c.JSON(http.StatusOK, proto.NewCommServiceRsp(100002, "添加kafka队列失败", res))
		return
	}

	c.JSON(http.StatusOK, proto.NewSuccessServiceRsp(res))
}
