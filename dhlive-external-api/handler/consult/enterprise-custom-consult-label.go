package consult

import (
	commProto "acg-ai-go-common/beans/proto"
	"acg-ai-go-common/gomysql"
	"acg-ai-go-common/logger"
	"acg-ai-go-common/utils"
	"context"
	"dhlive-external-api/beans/model"
	"dhlive-external-api/beans/proto"
	"dhlive-external-api/handler/i18n/respi18n"
	"encoding/json"
	"github.com/gin-gonic/gin"
	"net/http"
)

type Labels struct {
	Label string `json:"label"`
}
type LabelAdd struct {
	Type        string   `json:"type"`
	ProblemType string   `json:"problemType"`
	Required    bool     `json:"required"`
	ColSpan     int      `json:"colSpan"`
	MaxLength   int      `json:"maxLength"`
	Sort        int      `json:"sort"`
	Creator     string   `json:"creator"`
	Options     []Labels `json:"options"`
}

type LabelUpdate struct {
	Id          int64    `json:"id"`
	Type        string   `json:"type"`
	ProblemType string   `json:"problemType"`
	Required    bool     `json:"required"`
	ColSpan     int      `json:"colSpan"`
	Operator    string   `json:"operator"`
	MaxLength   int      `json:"maxLength"`
	Sort        int      `json:"sort"`
	Options     []Labels `json:"options"`
}

type LabelHandler struct {
	Id        int64    `json:"id"`
	Type      string   `json:"type"`
	Name      string   `json:"name"`
	Title     string   `json:"title"`
	Required  bool     `json:"required"`
	ColSpan   int      `json:"colSpan"`
	MaxLength int      `json:"maxLength"`
	Sort      int      `json:"sort"`
	Options   []Labels `json:"options"`
}
type LabelDel struct {
	Id       int64  `form:"id" binding:"required"`
	Operator string `form:"operator" binding:"required"`
}

func ConsultLabels(c *gin.Context) {
	targetLanguage := c.GetHeader("Language")
	logCtx := context.WithValue(context.TODO(), utils.CtxKeyLogID, utils.RandStringRunes(18))
	consultLabel := &model.ConsultLabel{}
	labels, err := consultLabel.Search(gomysql.DB, logCtx)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"UpdateUserFeedBack UpdateStatus error=%v", err)
		c.JSON(http.StatusOK, proto.NewCommDataRsp(510001, err.Error(), nil))
		return
	}
	consults := make([]LabelHandler, 0, len(labels))
	for _, t := range labels {
		labe, err := convertToTagHandler(logCtx, t, targetLanguage)
		if err != nil {
			continue
		}
		consults = append(consults, labe)
	}
	c.JSON(http.StatusOK, proto.NewSuccessRsp(gin.H{"tags": consults}))

}
func ConsultLabelAdd(c *gin.Context) {
	logCtx := context.WithValue(context.TODO(), utils.CtxKeyLogID, utils.RandStringRunes(18))
	req := &LabelAdd{}
	if err := c.ShouldBindJSON(req); err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"ConsultLabelAdd 解析请求参数失败 error=%v", err)
		c.JSON(http.StatusOK, commProto.NewCommDataRsp(100001, err.Error(), nil))
		return
	}
	consultLabel := &model.ConsultLabel{}
	label, err := consultLabel.QueryByProblemType(gomysql.DB, logCtx, req.ProblemType)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"ConsultLabelAdd QueryByProblemType error=%v", err)
		c.JSON(http.StatusOK, commProto.NewCommDataRsp(510001, err.Error(), nil))
		return
	}
	var updatedOptions []Labels
	if label != nil {
		consultLabel.ID = label.ID
		if len(label.Options) > 0 {
			err = json.Unmarshal([]byte(label.Options), &updatedOptions)
			if err != nil {
				logger.Log.Errorf(utils.MMark(logCtx)+"ConsultLabelAdd Unmarshal error=%v", err)
				c.JSON(http.StatusOK, commProto.NewCommDataRsp(100002, err.Error(), nil))
				return
			}
		} else {
			updatedOptions = make([]Labels, 0)
		}
	}
	for _, t := range req.Options {
		updatedOptions = append(updatedOptions, Labels{Label: t.Label})
	}
	optionsJson, err := json.Marshal(updatedOptions)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"ConsultLabelAdd Marshal error=%v", err)
		c.JSON(http.StatusOK, commProto.NewCommDataRsp(100002, err.Error(), nil))
		return
	}
	consultLabel.Type = req.Type
	consultLabel.ProblemType = req.ProblemType
	consultLabel.Required = map[bool]int{true: 1, false: 2}[req.Required]
	consultLabel.ColSpan = req.ColSpan
	consultLabel.MaxLength = req.MaxLength
	consultLabel.Sort = req.Sort
	consultLabel.Creator = req.Creator
	consultLabel.Operator = req.Creator
	consultLabel.Options = string(optionsJson)
	if label != nil {
		idx, err := consultLabel.Update(gomysql.DB, logCtx)
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+"ConsultLabelAdd Update error=%v", err)
			c.JSON(http.StatusOK, commProto.NewCommDataRsp(510002, err.Error(), nil))
			return
		}
		logger.Log.Infof(utils.MMark(logCtx)+"ConsultLabelAdd Update success, id=%v", idx)
		c.JSON(http.StatusOK, commProto.NewSuccessRsp(nil))
		return
	}
	idx, err := consultLabel.Insert(gomysql.DB, logCtx)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"ConsultLabelAdd Insert error=%v", err)
		c.JSON(http.StatusOK, commProto.NewCommDataRsp(510003, err.Error(), nil))
		return
	}
	logger.Log.Infof(utils.MMark(logCtx)+"ConsultLabelAdd Insert success, id=%v", idx)
	c.JSON(http.StatusOK, commProto.NewSuccessRsp(nil))

}
func ConsultLabelUpdate(c *gin.Context) {
	logCtx := context.WithValue(context.TODO(), utils.CtxKeyLogID, utils.RandStringRunes(18))
	req := &LabelUpdate{}
	if err := c.ShouldBindJSON(req); err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"ConsultLabelUpdate 解析请求参数失败 error=%v", err)
		c.JSON(http.StatusOK, commProto.NewCommDataRsp(100001, err.Error(), nil))
		return
	}
	optionString := ""
	if len(req.Options) > 0 {
		externalJson, err := json.Marshal(req.Options)
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx) + "ConsultLabelUpdate 参数错误")
			c.JSON(http.StatusOK, commProto.NewCommDataRsp(100001, err.Error(), nil))
			return
		}
		optionString = string(externalJson)
	} else {
		optionString = "[]"
	}

	consultLabel := &model.ConsultLabel{
		ID:          req.Id,
		Type:        req.Type,
		ProblemType: req.ProblemType,
		Required:    map[bool]int{true: 1, false: 2}[req.Required],
		ColSpan:     req.ColSpan,
		MaxLength:   req.MaxLength,
		Sort:        req.Sort,
		Operator:    req.Operator,
		Options:     optionString,
	}
	idx, err := consultLabel.Update(gomysql.DB, logCtx)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+" ConsultLabelUpdate Update error=%v", err)
		c.JSON(http.StatusOK, commProto.NewCommDataRsp(510001, err.Error(), nil))
		return
	}
	logger.Log.Infof(utils.MMark(logCtx)+"ConsultLabelUpdate Update success, idx=%v", idx)
	c.JSON(http.StatusOK, commProto.NewSuccessRsp(nil))
}
func ConsultLabelDelete(c *gin.Context) {
	logCtx := context.WithValue(context.TODO(), utils.CtxKeyLogID, utils.RandStringRunes(18))
	req := &LabelDel{}
	if err := c.ShouldBindQuery(req); err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"ConsultLabelUpdate 解析请求参数失败 error=%v", err)
		c.JSON(http.StatusOK, commProto.NewCommDataRsp(100001, err.Error(), nil))
		return
	}
	consultLabel := &model.ConsultLabel{
		ID:      req.Id,
		Options: req.Operator,
	}
	err := consultLabel.UpdateByOperator(gomysql.DB, logCtx)
	if err != nil {
		logger.CtxLog(c).Errorf("ConsultLabelDelete UpdateByOperator fail,  error=%v", err)
		c.JSON(http.StatusOK, commProto.NewCommRsp(510001, err.Error()))
		return
	}
	err = consultLabel.DeleteByID()
	if err != nil {
		logger.CtxLog(c).Errorf("ConsultLabelDelete DeleteByID fail, error=%v", err)
		c.JSON(http.StatusOK, commProto.NewCommRsp(510002, err.Error()))
		return
	}
	c.JSON(http.StatusOK, commProto.NewSuccessRsp(nil))
}

/*
*
数据格式转换
*/
func convertToTagHandler(logCtx context.Context, t model.ConsultLabel, targetLanguage string) (LabelHandler, error) {
	var labels []Labels
	// 如果字段值不为空进行反序列化
	if len(t.Options) > 0 {
		err := json.Unmarshal([]byte(t.Options), &labels)
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+"convertToTagHandler json.Unmarshal error=%v", err)
			return LabelHandler{}, err
		}
	} else {
		// 字段值为空反馈[] 数组
		labels = make([]Labels, 0)
	}
	// 遍历 labels 并对每个 Label 进行翻译
	translatedLabels := make([]Labels, len(labels))
	for i, label := range labels {
		translatedLabel := respi18n.TransformResponseLocaleBySubTag(logCtx, "enterprise-custom-consult", targetLanguage, label.Label)
		translatedLabels[i] = Labels{
			Label: translatedLabel,
		}
	}
	return LabelHandler{
		Id:        t.ID,
		Type:      t.Type,
		Name:      t.ProblemType,
		Title:     respi18n.TransformResponseLocaleBySubTag(logCtx, "enterprise-custom-consult", targetLanguage, t.ProblemType),
		Required:  t.Required != 2,
		ColSpan:   t.ColSpan,
		MaxLength: t.MaxLength,
		Sort:      t.Sort,
		Options:   translatedLabels,
	}, nil

}
