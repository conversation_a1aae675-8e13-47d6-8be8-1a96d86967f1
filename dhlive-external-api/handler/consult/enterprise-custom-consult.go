package consult

import (
	commProto "acg-ai-go-common/beans/proto"
	"acg-ai-go-common/gomysql"
	"acg-ai-go-common/logger"
	"acg-ai-go-common/utils"
	"context"
	"dhlive-external-api/beans/model"
	"dhlive-external-api/beans/proto"
	"dhlive-external-api/handler/i18n/respi18n"
	"encoding/json"
	"github.com/gin-gonic/gin"
	"net/http"
	"reflect"
	"regexp"
	"strings"
)

type ConsultHandler struct {
	Id       int64  `json:"id"`
	Operator string `json:"operator"`
	Reason   string `json:"reason" `
	Status   string `json:"status"`
}
type InsertDynamicField struct {
	Key      string      `json:"key"`
	Value    interface{} `json:"value"`
	Required bool        `json:"required"`
}
type ConsultInsertDynamic struct {
	Fields []InsertDynamicField `json:"fields" binding:"required"`
}

type ConsultQuery struct {
	PageNo   int64  `json:"pageNo" form:"pageNo"`     // 页码
	PageSize int64  `json:"pageSize" form:"pageSize"` // 每页数量
	UserId   string `json:"userId" form:"userId"`
	//FirstName     string `json:"firstName" form:"firstName"`         // 名字
	//LastName      string `json:"lastName" form:"lastName"`           // 姓
	//Country       string `json:"country" form:"country"`             // 国家
	//CompanyName   string `json:"companyName" form:"companyName"`     // 公司
	//MainReason    string `json:"mainReason" form:"mainReason"`       // 主要原因
	//MirameAiUsage string `json:"mirameAiUsage" form:"mirameAiUsage"` // 使用方式
	Status   string `json:"status" form:"status"`      // 状态
	CreateAt string `json:"createAt" form:"createdAt"` // 创建时间
}

type ConsultAddRepLabel struct {
	Label string `json:"label"`
}
type AddResponse struct {
	Title  string               `json:"title"`
	Labels []ConsultAddRepLabel `json:"labels"`
}

var (
	// 邮箱基本格式校验
	emailRegex = regexp.MustCompile(`^[a-zA-Z0-9][a-zA-Z0-9._+\-]*@[a-zA-Z0-9\-]+\.[a-zA-Z]{2,}(\.[a-zA-Z]{2,})?$`)
	// 禁止的特殊字符
	invalidSpecialChars = regexp.MustCompile(`[^a-zA-Z0-9._+\-@]`)
)

func ConsultList(c *gin.Context) {
	logCtx := context.WithValue(context.TODO(), utils.CtxKeyLogID, utils.RandStringRunes(18))
	req := &ConsultQuery{}
	if err := c.ShouldBindJSON(req); err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"AddUserFeedBack 解析请求参数失败 error=%v", err)
		c.JSON(http.StatusOK, commProto.NewCommRsp(100001, err.Error()))
		return
	}
	consult := &model.Consult{
		UserId: req.UserId,
		Status: req.Status,
	}
	count, consults, err := consult.SearchList(gomysql.DB, logCtx, req.PageNo, req.PageSize, req.CreateAt)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"UpdateUserFeedBack UpdateStatus error=%v", err)
		c.JSON(http.StatusOK, commProto.NewCommRsp(510001, err.Error()))
		return
	}
	c.JSON(http.StatusOK, commProto.NewSuccessRsp(gin.H{"count": count, "list": consults}))
}
func ConsultAdd(c *gin.Context) {
	// 默认未登录
	accountID := ""
	// 获取登录信息
	aid, ok := c.Get("AccountId")
	if ok {
		accountID = aid.(string)
	}
	targetLanguage := c.GetHeader("Language")
	logCtx := context.WithValue(context.TODO(), utils.CtxKeyLogID, utils.RandStringRunes(18))
	req := &ConsultInsertDynamic{}
	if err := c.ShouldBindJSON(req); err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"ConsultAdd 解析请求参数失败 error=%v", err)
		c.JSON(http.StatusOK, proto.NewCommRsp(100001, err.Error()))
		return
	}
	for _, field := range req.Fields {
		if field.Required && isEmptyValue(field.Value) {
			logger.Log.Errorf(utils.MMark(logCtx)+"ConsultAdd field.key: %v is required", field.Key)
			c.JSON(http.StatusOK, proto.NewCommRsp(100002, respi18n.TransformResponseLocaleByDefault(logCtx, targetLanguage, field.Key+" is required")))
			return
		}
		if field.Key == "contactEmail" {
			if str, ok := field.Value.(string); ok {
				contact := strings.TrimSpace(str) // 安全转换 + 去空格
				if !validateEmail(contact) {
					logger.Log.Errorf(utils.MMark(logCtx) + "ConsultAdd Invalid email format")
					c.JSON(http.StatusOK, proto.NewCommRsp(100003,
						respi18n.TransformResponseLocaleBySubTag(logCtx, "enterprise-custom-consult", targetLanguage, "Invalid email format")))
					return
				}
			} else {
				logger.Log.Errorf(utils.MMark(logCtx) + "ConsultAdd Invalid email format")
				c.JSON(http.StatusOK, proto.NewCommRsp(100003,
					respi18n.TransformResponseLocaleBySubTag(logCtx, "enterprise-custom-consult", targetLanguage, "Invalid email format")))
				return
			}
		}
	}
	fieldsJson, err := json.Marshal(req.Fields)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"ConsultAdd fieldsJson json.Marshal error=%v", err)
		c.JSON(http.StatusOK, proto.NewCommRsp(100002, err.Error()))
		return
	}
	consult := &model.Consult{
		UserId:      accountID,
		ConsultList: string(fieldsJson),
		Status:      "UNTREATED",
	}
	idx, err := consult.Insert(gomysql.DB, logCtx)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"ConsultAdd Insert error=%v", err)
		c.JSON(http.StatusOK, commProto.NewCommRsp(510001, err.Error()))
		return
	}
	logger.Log.Infof(utils.MMark(logCtx)+"ConsultAdd Insert success, id=%v", idx)

	var labels []ConsultAddRepLabel
	labels = append(labels, ConsultAddRepLabel{
		Label: respi18n.TransformResponseLocaleBySubTag(logCtx, "enterprise-custom-consult", targetLanguage, "Consult success label1"),
	})
	c.JSON(http.StatusOK, proto.NewSuccessRsp(AddResponse{
		Title:  respi18n.TransformResponseLocaleBySubTag(logCtx, "enterprise-custom-consult", targetLanguage, "Consultation submission"),
		Labels: labels,
	}))

}
func ConsultUpdate(c *gin.Context) {
	logCtx := context.WithValue(context.TODO(), utils.CtxKeyLogID, utils.RandStringRunes(18))
	req := &ConsultHandler{}
	if err := c.ShouldBindJSON(req); err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"ConsultUpdate 解析请求参数失败 error=%v", err)
		c.JSON(http.StatusOK, proto.NewCommRsp(100001, err.Error()))
		return
	}
	consult := &model.Consult{
		ID:       req.Id,
		Status:   req.Status,
		Operator: req.Operator,
		Reason:   req.Reason,
	}
	idx, err := consult.Update(gomysql.DB, logCtx)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"ConsultUpdate Update error=%v", err)
		c.JSON(http.StatusOK, proto.NewCommRsp(510001, err.Error()))
		return
	}
	logger.Log.Infof(utils.MMark(logCtx)+"UpdateUserFeedBack UpdateStatus success, id=%v", idx)
	c.JSON(http.StatusOK, commProto.NewSuccessRsp(nil))
}
func ConsultDelete(c *gin.Context) {
	logCtx := context.WithValue(context.TODO(), utils.CtxKeyLogID, utils.RandStringRunes(18))
	req := &ConsultHandler{}
	if err := c.ShouldBindJSON(req); err != nil {
		logger.Log.Infof(utils.MMark(logCtx)+"ConsultDelete 解析请求参数失败 error=%v", err)
		c.JSON(http.StatusOK, proto.NewCommRsp(100001, err.Error()))
		return
	}
	consult := &model.Consult{
		ID:       req.Id,
		Status:   req.Status,
		Operator: req.Operator,
		Reason:   req.Reason,
	}
	idx, err := consult.Delete(gomysql.DB, logCtx)
	if err != nil {
		logger.Log.Infof(utils.MMark(logCtx)+"ConsultDelete Delete error=%v", err)
		c.JSON(http.StatusOK, proto.NewCommRsp(510001, err.Error()))
		return
	}
	logger.Log.Infof(utils.MMark(logCtx)+"ConsultDelete Delete success, id=%v", idx)
	c.JSON(http.StatusOK, commProto.NewSuccessRsp(nil))
}

func validateEmail(email string) bool {
	// 基本格式校验
	if !emailRegex.MatchString(email) {
		return false
	}

	// 检查是否包含禁止的特殊字符
	if invalidSpecialChars.MatchString(email) {
		return false
	}

	// 分割本地部分和域名
	parts := strings.Split(email, "@")
	if len(parts) != 2 {
		return false
	}
	localPart, domain := parts[0], parts[1]

	// 检查本地部分不以特殊字符开头或结尾
	if len(localPart) == 0 {
		return false
	}
	firstChar := localPart[0]
	lastChar := localPart[len(localPart)-1]

	if isSpecialChar(firstChar) || isSpecialChar(lastChar) {
		return false
	}

	// 检查域名部分
	if strings.HasPrefix(domain, ".") || strings.HasSuffix(domain, ".") {
		return false
	}

	return true
}

// isSpecialChar 判断是否是特殊字符
func isSpecialChar(c byte) bool {
	return c == '.' || c == '_' || c == '+' || c == '-'
}

func isEmptyValue(value interface{}) bool {
	if value == nil {
		return true
	}
	v := reflect.ValueOf(value)
	switch v.Kind() {
	case reflect.String:
		return v.Len() == 0 // 空字符串 ""
	case reflect.Slice, reflect.Array, reflect.Map:
		return v.Len() == 0 // 空切片 [] 或空 map {}
	case reflect.Int, reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64:
		return v.Int() == 0 // 0
	case reflect.Uint, reflect.Uint8, reflect.Uint16, reflect.Uint32, reflect.Uint64:
		return v.Uint() == 0 // 0
	case reflect.Float32, reflect.Float64:
		return v.Float() == 0 // 0.0
	case reflect.Bool:
		return !v.Bool() // false
	case reflect.Ptr, reflect.Interface:
		return v.IsNil() // nil 指针或 interface
	case reflect.Struct:
		return reflect.DeepEqual(value, reflect.Zero(v.Type()).Interface()) // 零值 struct
	default:
		return false // 其他类型默认不为空
	}
}
