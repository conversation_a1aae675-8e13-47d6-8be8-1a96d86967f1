package kafkaproxy

import (
	"acg-ai-go-common/logger"
	"context"
	"crypto/tls"
	"encoding/base64"
	"encoding/json"
	"errors"
	"io/ioutil"
	"log"
	"os"
	"os/signal"
	"sync"
	"syscall"
	"time"

	"github.com/segmentio/kafka-go"
	"github.com/segmentio/kafka-go/sasl/plain"
)

type KafkaProxy struct {
	Producer *kafka.Writer
	Readers  []*kafka.Reader
	wg       sync.WaitGroup
	cancel   context.CancelFunc
	userName string
	password string
}

// 定义结构体用于解析 JSON 中的 client_email 字段
type GoogleCredentials struct {
	ClientEmail string `json:"client_email"`
}

// InitKafka 初始化 Kafka 连接，并返回 KafkaProxy 实例
//
// 参数:
// - brokers: Kafka broker 的地址列表。
// - credentialsPath: 包含 Google 凭证的 JSON 文件路径。
//
// 返回值:
// - KafkaProxy 实例指针。
func InitKafka(brokers []string, credentialsPath string) *KafkaProxy {
	// 读取 JSON 文件内容
	data, err := ioutil.ReadFile(credentialsPath)
	if err != nil {
		log.Fatalf("读取凭证文件失败: %v", err)
	}

	// 解析 JSON 获取 client_email 字段
	var creds GoogleCredentials
	if err := json.Unmarshal(data, &creds); err != nil {
		log.Fatalf("解析 JSON 失败: %v", err)
	}

	logger.Log.Infof("client_email: %s", creds.ClientEmail)

	// Base64 编码整个 JSON 文件内容
	encoded := base64.StdEncoding.EncodeToString(data)
	logger.Log.Infof("Base64 encoded credentials: %s", encoded)
	return NewKafkaProxy(brokers, creds.ClientEmail, encoded)
}

// NewKafkaProxy 创建一个 KafkaProxy 实例，用于处理 Kafka 消息的生产,并启动监听事件 + 优雅退出
//
// 参数:
// - brokers: Kafka broker 的地址列表。
// - userName: Kafka SASL/PLAIN 认证机制的用户名。
// - password: Kafka SASL/PLAIN 认证机制的密码。
//
// 返回值:
// - KafkaProxy 实例指针。
func NewKafkaProxy(brokers []string, userName string, password string) *KafkaProxy {
	// SASL/PLAIN 认证机制，填入你的用户名和密码
	mechanism := plain.Mechanism{
		Username: userName,
		Password: password,
	}

	dialer := &kafka.Dialer{
		Timeout:       10 * time.Second,
		DualStack:     true,
		SASLMechanism: mechanism,
		TLS: &tls.Config{
			InsecureSkipVerify: false, // 根据需要是否验证证书
			// RootCAs:            rootCAs, // 这里设置系统默认根证书池
		},
	}
	writer := kafka.NewWriter(kafka.WriterConfig{
		BatchTimeout: 10 * time.Millisecond,
		Brokers:      brokers,
		Dialer:       dialer,
	})

	proxy := &KafkaProxy{
		Producer: writer,
		userName: userName,
		password: password,
	}

	// 自动优雅退出 producer
	go func() {
		c := make(chan os.Signal, 1)
		signal.Notify(c, syscall.SIGINT, syscall.SIGTERM)
		<-c
		logger.Log.Infoln("🛑 [Producer] Shutting down...")
		writer.Close()
	}()

	return proxy
}

// SendMessage 发送消息（支持 Key 和 Headers）
func (k *KafkaProxy) SendMessage(topic, key, value string, headers map[string]string) error {
	var kafkaHeaders []kafka.Header
	for hk, hv := range headers {
		kafkaHeaders = append(kafkaHeaders, kafka.Header{Key: hk, Value: []byte(hv)})
	}

	msg := kafka.Message{
		Topic:   topic,
		Key:     []byte(key),
		Value:   []byte(value),
		Headers: kafkaHeaders,
		Time:    time.Now(),
	}

	return k.Producer.WriteMessages(context.Background(), msg)
}

// InitConsumers 初始化多个 KafkaConsumer（绑定多个 topic）
func (k *KafkaProxy) InitConsumers(brokers []string, groupID string, topics []string) error {
	mechanism := plain.Mechanism{
		Username: k.userName,
		Password: k.password,
	}
	dialer := &kafka.Dialer{
		Timeout:       10 * time.Second,
		DualStack:     true,
		SASLMechanism: mechanism,
		TLS: &tls.Config{
			InsecureSkipVerify: false, // 根据需要是否验证证书
			// RootCAs: rootCAs, // 这里设置系统默认根证书池
		},
	}
	for _, topic := range topics {
		reader := kafka.NewReader(kafka.ReaderConfig{
			Brokers:        brokers,
			GroupID:        groupID,
			Topic:          topic,
			CommitInterval: 0, // 手动提交 offset
			Dialer:         dialer,
		})

		k.Readers = append(k.Readers, reader)
	}

	return nil
}

// StartConsumingWithGracefulShutdown 启动消费者并优雅退出
func (k *KafkaProxy) StartConsumingWithGracefulShutdown(handler func(msg kafka.Message)) {
	ctx, cancel := context.WithCancel(context.Background())
	k.cancel = cancel

	// 为每个 topic 创建独立的消费 goroutine
	for _, reader := range k.Readers {
		k.wg.Add(1)
		go func(r *kafka.Reader) {
			defer k.wg.Done()
			for {
				msg, err := r.ReadMessage(ctx)
				if err != nil {
					if errors.Is(err, context.Canceled) {
						logger.Log.Infoln("🛑 Consumer context canceled")
						return
					}
					// 如果是非致命错误，等待一段时间再尝试
					time.Sleep(5 * time.Second)
					// 关闭旧 reader 并创建新 reader 重试
					r.Close()
					logger.Log.Errorf("❌ Failed to read message: %v", err)
					newReader := kafka.NewReader(r.Config())
					r = newReader
					continue
				}
				// 处理消息
				handler(msg)

				// 手动提交 offset
				if err := r.CommitMessages(ctx, msg); err != nil {
					logger.Log.Errorf("⚠️ Failed to commit offset: %v\n", err)
				}
			}
		}(reader)
	}

	// 自动优雅退出消费者
	go func() {
		c := make(chan os.Signal, 1)
		signal.Notify(c, syscall.SIGINT, syscall.SIGTERM)
		<-c
		logger.Log.Infoln("🛑 [Consumer] Shutdown signal received")
		cancel()
		time.Sleep(2 * time.Second)
		for _, reader := range k.Readers {
			reader.Close()
		}
		k.wg.Wait() // 等待所有 goroutine 完成
		logger.Log.Infoln("✅ [Consumer] Graceful shutdown complete")
	}()
}
