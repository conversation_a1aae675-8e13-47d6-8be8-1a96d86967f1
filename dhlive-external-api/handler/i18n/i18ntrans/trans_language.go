package i18ntrans

import (
	"acg-ai-go-common/logger"
	"acg-ai-go-common/multilanguage"
	"acg-ai-go-common/utils"
	"context"
	"dhlive-external-api/handler/i18n"
	"fmt"
)

func GetMessageWithLocale(logCtx context.Context, subTag string, lang string, key string, defaultRet string) string {
	res := key
	if len(res) < 1 || len(lang) < 1 {
		logger.Log.Warnf("%sno need for trans", utils.MMark(logCtx))
		return defaultRet
	}

	localeKeyList := []string{key}

	// 去多语言服务查一下语种结果
	nameMap, err := multilanguage.GetMultilangStr(logCtx, i18n.ServiceName, subTag, localeKeyList, lang)
	if err != nil {
		logger.Log.Warnf("%sGetMultilangStr failed: %v", utils.MMark(logCtx), err)
		return defaultRet
	}

	if ret, ok := nameMap[key]; ok {
		if ret == i18n.DefaultErrorValue {
			return defaultRet
		}
		return ret
	}

	logger.Log.Warnf("%svalue for key: %s in lang: %s not found", utils.MMark(logCtx), key, lang)
	return defaultRet
}

func GetTypeMessageWithLocale(logCtx context.Context, subTag string, lang string, retType int,
	defaultRet string) string {
	// 生成一个key
	key := fmt.Sprintf("key_type_%03d", retType)
	return GetMessageWithLocale(logCtx, subTag, lang, key, defaultRet)
}
