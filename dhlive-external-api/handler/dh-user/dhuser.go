package dh_user

import (
	"acg-ai-go-common/logger"
	"acg-ai-go-common/utils"
	"acg-ai-go-common/utils/httputil"
	"bytes"
	"context"
	"dhlive-external-api/beans/proto"
	config "dhlive-external-api/conf"
	"encoding/json"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

const (
	DhUserInfoPath           = "/api/internal/workflow/v2/user/auth/info"              // 获取用户信息
	DhUserQuotaFreeze        = "/api/internal/workflow/v1/account/quota/freezeQuota"   // 冻结
	DhUserQuotaUnFreeze      = "/api/internal/workflow/v1/account/quota/unFreezeQuota" // 解冻
	DhUserQuotaConfirmedCost = "/api/internal/workflow/v1/account/quota/confirmedCost" // 确认消耗
	DhUserQuotaAddBenefit    = "/api/internal/workflow/v1/account/quota/increase"      // 新增权益接口
)

func DhUserCheck(c *gin.Context) {
	reqHeader := map[string]string{
		"Cookie":         c.GetHeader("Cookie"),
		"Host":           c.GetHeader("Host"),
		"Origin":         c.GetHeader("Origin"),
		"Referer":        c.GetHeader("Referer"),
		"weixin-session": c.GetHeader("weixin-session"),
	}
	rsp, err := GetDhUserInfo(c)
	if err != nil {
		logger.CtxLog(c).Errorf("DhUserCheck GetDhUserInfo fail, header:%v, err:%v", reqHeader, err)
		c.AbortWithStatusJSON(http.StatusOK, proto.NewCommRsp(590001, "服务内部异常"))
		return
	} else if rsp == nil || !rsp.Success {
		c.AbortWithStatusJSON(http.StatusOK, rsp)
		return
	}
	logger.CtxLog(c).Infof("DhUserCheck GetDhUserInfo success, info:%v", rsp.Result)
	c.Set("AccountId", rsp.Result.AccountId)
	c.Set("UserId", rsp.Result.Uid)
	c.Set("UserName", rsp.Result.Username)
	c.Set("UserCreateTime", rsp.Result.UserCreateTime)
	c.Next()
}

func DhUserCheckByPass(c *gin.Context) {
	reqHeader := map[string]string{
		"Cookie":         c.GetHeader("Cookie"),
		"Host":           c.GetHeader("Host"),
		"Origin":         c.GetHeader("Origin"),
		"Referer":        c.GetHeader("Referer"),
		"weixin-session": c.GetHeader("weixin-session"),
	}
	logger.CtxLog(c).Infof("DhUserCheckByPass GetDhUserInfo strat, reqHeader:%+v", reqHeader)
	rsp, err := GetDhUserInfo(c)
	if err != nil {
		logger.CtxLog(c).Warnf("DhUserCheckByPass GetDhUserInfo fail, header:%v, err:%v", reqHeader, err)
	} else if rsp != nil && rsp.Success {
		logger.CtxLog(c).Infof("DhUserCheckByPass GetDhUserInfo success, rsp:%+v", rsp)
		c.Set("AccountId", rsp.Result.AccountId)
		c.Set("UserId", rsp.Result.Uid)
		c.Set("UserName", rsp.Result.Username)
		c.Set("UserCreateTime", rsp.Result.UserCreateTime)
	} else {
		logger.CtxLog(c).Warnf("User not login, DhUserCheckByPass GetDhUserInfo fail, rsp:%+v", rsp)
	}
	c.Next()
}

func GetDhUserInfo(c *gin.Context) (*DhUserInfoRsp, error) {
	header := map[string]string{
		"Cookie":         c.GetHeader("Cookie"),
		"Host":           c.GetHeader("Host"),
		"Origin":         c.GetHeader("Origin"),
		"Referer":        c.GetHeader("Referer"),
		"weixin-session": c.GetHeader("weixin-session"),
	}
	logger.CtxLog(c).Infof("GetDhUserInfo header:%v", header)

	logCtx := context.WithValue(context.TODO(), utils.CtxKeyLogID, uuid.New().String())
	url := config.LocalConfig.DhUserSetting.BaseUrl + DhUserInfoPath

	retryClt := httputil.NewRetryHTTPClient(10*time.Second, 3)
	res, err := retryClt.DoRequest(logCtx, http.MethodGet, url, header, nil)
	if err != nil {
		logger.CtxLog(c).Errorf(utils.MMark(logCtx)+"GetDhUserInfo do request fail, url:%s, err:%v", url, err)
		return nil, err
	}
	logger.CtxLog(c).Infof(utils.MMark(logCtx)+"GetDhUserInfo do request success, url:%s, rsp:%s", url, string(res))
	var rsp *DhUserInfoRsp
	err = json.Unmarshal(res, &rsp)
	return rsp, err
}

// QuotaFreeze 配额冻结
func QuotaFreeze(req *QuotaFreezeReq) (*QuotaFreezeRsp, error) {
	url := config.LocalConfig.DhUserSetting.BaseUrl + DhUserQuotaFreeze
	var rsp *QuotaFreezeRsp
	err := httputil.PostJson(url, req, &rsp)
	return rsp, err
}

// QuotaUnFreeze 配额解冻
func QuotaUnFreeze(req *QuotaUnFreezeReq) (*QuotaUnFreezeRsp, error) {
	url := config.LocalConfig.DhUserSetting.BaseUrl + DhUserQuotaUnFreeze
	var rsp *QuotaUnFreezeRsp
	err := httputil.PostJson(url, req, &rsp)
	return rsp, err
}

// QuotaConfirmedCost 配额确认扣除
func QuotaConfirmedCost(req *QuotaConfirmedCostReq) (*QuotaConfirmedCostRsp, error) {
	url := config.LocalConfig.DhUserSetting.BaseUrl + DhUserQuotaConfirmedCost
	var rsp *QuotaConfirmedCostRsp
	err := httputil.PostJson(url, req, &rsp)
	return rsp, err
}

// 增加权益
func QuotaAddBenefit(logCtx context.Context, req *QuotaAddBenefitReq) (*QuotaAddBenefitRsp, error) {
	url := config.LocalConfig.DhUserSetting.BaseUrl + DhUserQuotaAddBenefit
	var rsp *QuotaAddBenefitRsp
	retryClt := httputil.NewRetryHTTPClient(10*time.Second, 3)

	body, err := json.Marshal(req)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"QuotaAddBenefit json marshal fail, req:%+v, err:%v", req, err)
		return nil, err
	}

	header := map[string]string{
		"Content-Type": "application/json",
	}

	if res, err := retryClt.DoRequest(logCtx, http.MethodPost, url, header, bytes.NewBuffer(body)); err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"QuotaAddBenefit do request fail, req:%+v, err:%v", req, err)
		return nil, err
	} else {
		logger.Log.Infof(utils.MMark(logCtx)+"QuotaAddBenefit do request success, req:%+v, rsp:%+v", req, string(res))
		if err = json.Unmarshal(res, &rsp); err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+"QuotaAddBenefit json unmarshal fail, req:%+v, res:%s, err:%v", req, string(res), err)
			return nil, err
		}
	}

	return rsp, nil
}
