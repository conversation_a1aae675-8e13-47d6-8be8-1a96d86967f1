package country

import (
	commProto "acg-ai-go-common/beans/proto"
	"acg-ai-go-common/gomysql"
	"acg-ai-go-common/logger"
	"acg-ai-go-common/utils"
	"context"
	"dhlive-external-api/beans/model"
	"dhlive-external-api/beans/proto"
	"github.com/gin-gonic/gin"
	"net/http"
)

type BatchCountry struct {
	Name                   string `json:"name"`
	Alpha2                 string `json:"alpha-2"`
	Alpha3                 string `json:"alpha-3"`
	CountryCode            string `json:"country-code"`
	Iso                    string `json:"iso_3166-2"`
	Region                 string `json:"region"`
	SubRegion              string `json:"sub-region"`
	IntermediateRegion     string `json:"intermediate-region"`
	RegionCode             string `json:"region-code"`
	SubRegionCode          string `json:"sub-region-code"`
	IntermediateRegionCode string `json:"intermediate-region-code"`
	Flag                   string `json:"flag"`
}
type Country struct {
	ID                     int64  `json:"id"`
	Name                   string `json:"name"`
	Alpha2                 string `json:"alpha2"`
	Alpha3                 string `json:"alpha3"`
	CountryCode            string `json:"countryCode"`
	Iso                    string `json:"isoStandard"`
	Region                 string `json:"region"`
	SubRegion              string `json:"subRegion"`
	IntermediateRegion     string `json:"intermediateRegion"`
	RegionCode             string `json:"regionCode"`
	SubRegionCode          string `json:"subRegionCode"`
	Sort                   int    `json:"sort"`
	IntermediateRegionCode string `json:"intermediateRegionCode"`
	Flag                   string `json:"flag"`
	Operator               string `json:"operato"`
}

type Search struct {
	PageNo           int64  `json:"pageNo"`
	PageSize         int64  `json:"pageSize"`
	CountryName      string `json:"countryName" form:"countryName"`
	CountryCode      string `json:"countryCode" form:"countryCode"`
	CountryAlphaCode string `json:"countryAlphaCode" form:"countryAlphaCode"`
}
type SearchResponse struct {
	Name        string `json:"name"`
	CountryCode string `json:"countryCode"`
	Flag        string `json:"flag"`
}
type BatchAdd struct {
	CountryLabels []BatchCountry ` json:"countryList"`
}

type CountryDel struct {
	Id       int64  `form:"id" binding:"required"`
	Operator string `form:"operator" binding:"required"`
}

func CountryList(c *gin.Context) {
	logCtx := context.WithValue(context.TODO(), utils.CtxKeyLogID, utils.RandStringRunes(18))
	req := &Search{}
	if err := c.ShouldBindQuery(req); err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"CountryList 解析请求参数失败 error=%v", err)
		c.JSON(http.StatusOK, proto.NewCommRsp(100001, err.Error()))
		return
	}
	country := &model.Country{
		Alpha3:      req.CountryAlphaCode,
		Name:        req.CountryName,
		CountryCode: req.CountryCode,
	}
	countrys, err := country.Search(gomysql.DB, logCtx)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"CountryList Search error=%v", err)
		c.JSON(http.StatusOK, proto.NewCommRsp(510001, err.Error()))
		return
	}
	countryList := make([]SearchResponse, 0, len(countrys))
	for _, t := range countrys {
		tag, err := convertToCountryLabelHandler(t)
		if err != nil {
			continue
		}
		countryList = append(countryList, tag)
	}
	logger.Log.Infof(utils.MMark(logCtx) + "CountryList success")
	c.JSON(http.StatusOK, proto.NewSuccessRsp(gin.H{"list": countryList}))
}

func CountrySearch(c *gin.Context) {
	logCtx := context.WithValue(context.TODO(), utils.CtxKeyLogID, utils.RandStringRunes(18))
	req := &Search{}
	if err := c.ShouldBindJSON(req); err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"CountryAdd 解析请求参数失败 error=%v", err)
		c.JSON(http.StatusOK, commProto.NewCommRsp(10001, err.Error()))
		return
	}
	country := &model.Country{
		Name:        req.CountryName,
		CountryCode: req.CountryCode,
		Alpha3:      req.CountryAlphaCode,
	}
	count, consults, err := country.SearchList(gomysql.DB, logCtx, req.PageNo, req.PageSize)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"CountrySearch SearchList error=%v", err)
		c.JSON(http.StatusOK, commProto.NewCommRsp(510001, err.Error()))
		return
	}
	c.JSON(http.StatusOK, commProto.NewSuccessRsp(gin.H{"count": count, "list": consults}))
}

func CountryAdd(c *gin.Context) {
	logCtx := context.WithValue(context.TODO(), utils.CtxKeyLogID, utils.RandStringRunes(18))
	req := &Country{}
	if err := c.ShouldBindJSON(req); err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"CountryAdd 解析请求参数失败 error=%v", err)
		c.JSON(http.StatusOK, commProto.NewCommRsp(100001, err.Error()))
		return
	}
	country := &model.Country{
		Name:                   req.Name,
		Alpha2:                 req.Alpha2,
		Alpha3:                 req.Alpha3,
		CountryCode:            req.CountryCode,
		IsoStandard:            req.Iso + ":" + req.Alpha2,
		Region:                 req.Region,
		SubRegion:              req.SubRegion,
		IntermediateRegion:     req.IntermediateRegion,
		RegionCode:             req.RegionCode,
		SubRegionCode:          req.SubRegionCode,
		IntermediateRegionCode: req.IntermediateRegionCode,
		Sort:                   req.Sort,
		Flag:                   req.Flag,
		Operator:               req.Operator,
	}
	idx, err := country.Insert(gomysql.DB, logCtx)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"UpdateUserFeedBack UpdateStatus error=%v", err)
		c.JSON(http.StatusOK, commProto.NewCommRsp(510001, err.Error()))
		return
	}
	logger.Log.Infof(utils.MMark(logCtx)+"CountryAdd success: idx=%d", idx)
	c.JSON(http.StatusOK, commProto.NewSuccessRsp(nil))
}

func CountryBatchAdd(c *gin.Context) {
	logCtx := context.WithValue(context.TODO(), utils.CtxKeyLogID, utils.RandStringRunes(18))
	req := &BatchAdd{}
	if err := c.ShouldBindJSON(req); err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"AddUserFeedBack 解析请求参数失败 error=%v", err)
		c.JSON(http.StatusOK, proto.NewCommRsp(100001, "参数解析异常"))
		return
	}
	var countries []*model.Country
	for _, item := range req.CountryLabels {
		country := &model.Country{
			Name:                   item.Name,
			Alpha2:                 item.Alpha2,
			Alpha3:                 item.Alpha3,
			CountryCode:            item.CountryCode,
			IsoStandard:            item.Iso,
			Region:                 item.Region,
			SubRegion:              item.SubRegion,
			IntermediateRegion:     item.IntermediateRegion,
			RegionCode:             item.RegionCode,
			SubRegionCode:          item.SubRegionCode,
			IntermediateRegionCode: item.IntermediateRegionCode,
			Flag:                   item.Flag,
		}
		countries = append(countries, country)
	}
	country := &model.Country{}
	size, err := country.BatchInsert(gomysql.DB, logCtx, countries)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"UpdateUserFeedBack UpdateStatus error=%v", err)
		c.JSON(http.StatusOK, commProto.NewCommRsp(510001, err.Error()))
		return
	}
	c.JSON(http.StatusOK, commProto.NewSuccessRsp(nil))
	logger.Log.Infof(utils.MMark(logCtx)+"BatchInsert success: size=%d", size)

}
func CountryUpdate(c *gin.Context) {
	logCtx := context.WithValue(context.TODO(), utils.CtxKeyLogID, utils.RandStringRunes(18))
	req := &Country{}
	if err := c.ShouldBindJSON(req); err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"AddUserFeedBack 解析请求参数失败 error=%v", err)
		c.JSON(http.StatusOK, proto.NewCommRsp(100001, err.Error()))
		return
	}
	country := &model.Country{
		ID:                     req.ID,
		Name:                   req.Name,
		Alpha2:                 req.Alpha2,
		Alpha3:                 req.Alpha3,
		CountryCode:            req.CountryCode,
		IsoStandard:            req.Iso,
		Region:                 req.Region,
		SubRegion:              req.SubRegion,
		IntermediateRegion:     req.IntermediateRegion,
		RegionCode:             req.RegionCode,
		SubRegionCode:          req.SubRegionCode,
		IntermediateRegionCode: req.IntermediateRegionCode,
		Sort:                   req.Sort,
		Flag:                   req.Flag,
		Operator:               req.Operator,
	}
	idx, err := country.Update(gomysql.DB, logCtx)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"UpdateUserFeedBack UpdateStatus error=%v", err)
		c.JSON(http.StatusOK, commProto.NewCommRsp(510001, err.Error()))
		return
	}
	logger.Log.Infof(utils.MMark(logCtx)+"UpdateUserFeedBack UpdateStatus success, id=%v", idx)
	c.JSON(http.StatusOK, commProto.NewSuccessRsp(nil))
}
func CountryDelete(c *gin.Context) {
	logCtx := context.WithValue(context.TODO(), utils.CtxKeyLogID, utils.RandStringRunes(18))
	req := &CountryDel{}
	if err := c.ShouldBindQuery(req); err != nil {
		logger.Log.Infof(utils.MMark(logCtx)+"ConsultDelete 解析请求参数失败 error=%v", err)
		c.JSON(http.StatusOK, proto.NewCommRsp(100001, err.Error()))
		return
	}
	country := &model.Country{
		ID:       req.Id,
		Operator: req.Operator,
	}
	err := country.UpdateByOperator(gomysql.DB, logCtx)
	if err != nil {
		logger.CtxLog(c).Errorf("QuestionnaireDelete UpdateByOperator fail,  error=%v", err)
		c.JSON(http.StatusOK, commProto.NewCommRsp(510001, err.Error()))
		return
	}
	err = country.DeleteByID()
	if err != nil {
		logger.CtxLog(c).Errorf("QuestionnaireDelete DeleteByID fail, error=%v", err)
		c.JSON(http.StatusOK, commProto.NewCommRsp(510002, err.Error()))
		return
	}
	c.JSON(http.StatusOK, commProto.NewSuccessRsp(nil))
}

/*
*
数据格式转换
*/
func convertToCountryLabelHandler(t model.Country) (SearchResponse, error) {
	return SearchResponse{
		Name:        t.Name,
		CountryCode: t.CountryCode,
		Flag:        t.Flag,
	}, nil

}
