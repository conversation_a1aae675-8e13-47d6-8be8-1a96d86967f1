package speechtotext

import (
	"acg-ai-go-common/global"
	"acg-ai-go-common/gomysql"
	"acg-ai-go-common/logger"
	"acg-ai-go-common/utils"
	"acg-ai-go-common/utils/elevenlabs"
	"context"
	"dhlive-external-api/beans/model"
	"dhlive-external-api/beans/proto"
	config "dhlive-external-api/conf"
	"dhlive-external-api/handler/figure"
	"dhlive-external-api/handler/handlerUtils"
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"path"
	"regexp"
	"time"

	rdsv9 "acg-ai-go-common/goredis/rds-v9"
	"acg-ai-go-common/utils/redisproxy"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"gorm.io/gorm"
)

const (
	ModelId string = "scribe_v1"
)

func SpeechToText(c *gin.Context) {
	req := proto.SpeechToTextRequest{}
	res := []*proto.SpeechToTextResponse{}
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.CtxLog(c).Errorf("ShouldBindJSON error: %v", err)
		c.JSON(http.StatusOK, proto.NewCommDataRsp(100001, "内部错误", res))
		return
	}

	for _, url := range req.AudioURL {
		// 语音转文本
		tmpRes, err := speechToText(c, url)
		if err != nil {
			logger.CtxLog(c).Errorf("speechToText error: %v", err)
			c.JSON(http.StatusOK, proto.NewCommDataRsp(100004, "内部错误", res))
			return
		}
		res = append(res, tmpRes)
	}

	c.JSON(http.StatusOK, proto.NewSuccessRsp(res))
}

func formatSpeechToText(c *gin.Context, res *elevenlabs.AsrResponse) *proto.SpeechToTextResponse {
	speechRes := &proto.SpeechToTextResponse{}
	logger.CtxLog(c).Infof("result:language_probability: %f language_code: %s text: %s", res.LanguageProbability, res.LanguageCode, res.Text)

	if len(res.Words) > 0 {
		speechRes.TotalDuration = int(res.Words[len(res.Words)-1].End * 1000)
	}

	text := ""
	starTextTime := -1 // 文本的开始时间 ms
	endTextTime := 0   // 文本的结束时间 ms
	wordDetail := []proto.SegmentDetail{}

	for i, word := range res.Words {
		if word.Type == "audio_event" {
			continue
		}

		if starTextTime == -1 {
			starTextTime = int(word.Start * 1000)
		}

		text += word.Text

		// 判断有没有标点符号
		re := regexp.MustCompile(config.LocalConfig.AsrSetting.SplitFlags)
		segments := re.Split(word.Text, -1) // -1 表示全部切分
		if len(segments) > 1 {
			endTextTime = int(word.End * 1000)
			speechRes.Detail = append(speechRes.Detail, proto.Segment{
				StartTime: starTextTime,
				EndTime:   endTextTime,
				Text:      text,
				Duration:  endTextTime - starTextTime,
				Detail:    wordDetail,
			})
			logger.CtxLog(c).Infof("text: %s start: %d end: %d", text, starTextTime, endTextTime)
			starTextTime = -1
			text = ""
			wordDetail = make([]proto.SegmentDetail, 0)

			continue
		}

		tmpStartTime := int(word.Start * 1000)
		if i > 0 {
			tmpStartTime = int(res.Words[i-1].End * 1000)
		}

		wordDetail = append(wordDetail, proto.SegmentDetail{
			Text:      word.Text,
			StartTime: tmpStartTime,
			EndTime:   int(word.End * 1000),
		})

		if i == len(res.Words)-1 { // 最后一个单词 还不是标点符号就拼接上
			endTextTime = int(word.End * 1000)
			speechRes.Detail = append(speechRes.Detail, proto.Segment{
				StartTime: starTextTime,
				EndTime:   endTextTime,
				Text:      text,
				Duration:  endTextTime - starTextTime,
				Detail:    wordDetail,
			})
		}
	}

	for _, format := range res.AdditionalFormats {
		logger.CtxLog(c).Infof("format: %+v", format)
	}

	if res.Detail != nil {
		logger.CtxLog(c).Infof("detail: %s %s", res.Detail.Message, res.Detail.Status)
	}

	return speechRes
}

func speechToText(c *gin.Context, url string) (*proto.SpeechToTextResponse, error) {
	// 使用redis 锁
	// 使用锁机制，防止同一个用户在短时间内重复绑定
	urlMd5 := handlerUtils.CalculateMD5FromText(url)
	redisKey := fmt.Sprintf("%s:external:speechToText:%s", handlerUtils.GetNameByRunEnv(), urlMd5)

	spanLock := rdsv9.DefaultSpinLock(c, redisproxy.GetRedisProxy().Rdb, redisKey, "")
	if ok, err := spanLock.TryLock(); err != nil {
		return nil, err
	} else if !ok {
		logger.CtxLog(c).Errorf("findSpeechToTextCache file is processing")
		return nil, errors.New("file is processing")
	}
	defer spanLock.Unlock()

	// 查库
	if tmpRes, err := findSpeechToTextCache(c, url); err == nil {
		return tmpRes, nil
	}

	// 下载文件并计算md5
	downpath := figure.DownloadFilePrefix + uuid.NewString() + path.Ext(url)
	defer handlerUtils.DeleteFile(downpath)

	if err := handlerUtils.DownloadFile(url, downpath); err != nil {
		logger.CtxLog(c).Errorf("DownloadFile error: %v", err)
		return nil, err
	}
	fileMd5, err := handlerUtils.CalculateMD5FromFile(downpath)
	if err != nil {
		logger.CtxLog(c).Errorf("CalculateMD5FromFile error: %v", err)
		return nil, err
	}

	if tmpRes, err := findSpeechToTextCacheByFileMd5(c, fileMd5); err == nil {
		return tmpRes, nil
	}

	// asr request
	asrReq := elevenlabs.AsrRequest{
		ModelID: ModelId,
		File:    downpath,
		Diarize: true,
	}

	// 计算耗时
	starTime := time.Now()
	status := model.SpeechToTextStatusSuccess
	errMsg := ""
	logCtx := context.WithValue(context.TODO(), utils.CtxKeyLogID, c.GetHeader(global.HeaderTraceID))
	asrText, err := elevenlabs.DoSpeechToText(logCtx, config.LocalConfig.AsrSetting.AsrHost, config.LocalConfig.AsrSetting.ApiKey, &asrReq)
	if err != nil {
		logger.CtxLog(c).Errorf("asr failed: %v\n", err)
		status = model.SpeechToTextStatusFailed
		errMsg = err.Error()
	}
	endTime := time.Now()
	logger.CtxLog(c).Infof("asr cost: %v\n", endTime.Sub(starTime))

	// 存库
	if err = saveSpeechToTextCache(c, url, fileMd5, status, errMsg, asrText); err != nil {
		logger.CtxLog(c).Errorf("saveSpeechToTextCache error: %v", err)
		return nil, err
	}

	tmpRes := formatSpeechToText(c, asrText)
	return tmpRes, nil
}

func findSpeechToTextCache(c *gin.Context, url string) (*proto.SpeechToTextResponse, error) {
	urlMd5 := handlerUtils.CalculateMD5FromText(url)

	cache, err := (&model.SpeechToText{}).GetSpeechToText(gomysql.DB, urlMd5, model.SpeechToTextStatusSuccess)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, err
		}
		logger.CtxLog(c).Errorf("findSpeechToTextCache error: %v", err)
		return nil, err
	}

	asrRes := &elevenlabs.AsrResponse{}
	if err = json.Unmarshal([]byte(cache.Content), &asrRes); err != nil {
		logger.CtxLog(c).Errorf("json.Unmarshal error: %v", err)
		return nil, err
	}

	res := formatSpeechToText(c, asrRes)
	return res, nil
}

func findSpeechToTextCacheByFileMd5(c *gin.Context, fileMd5 string) (*proto.SpeechToTextResponse, error) {
	cache, err := (&model.SpeechToText{}).GetSpeechToTextByFileMd5(gomysql.DB, fileMd5, model.SpeechToTextStatusSuccess)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, err
		}
		logger.CtxLog(c).Errorf("findSpeechToTextCache error: %v", err)
		return nil, err
	}

	asrRes := &elevenlabs.AsrResponse{}
	if err = json.Unmarshal([]byte(cache.Content), &asrRes); err != nil {
		logger.CtxLog(c).Errorf("json.Unmarshal error: %v", err)
		return nil, err
	}

	res := formatSpeechToText(c, asrRes)
	return res, nil
}

func saveSpeechToTextCache(c *gin.Context, url, fileMd5 string, status model.SpeechToTextStatus, errMsg string, asrRes *elevenlabs.AsrResponse) error {
	asrBate := []byte{}
	var err error
	if asrRes != nil {
		asrBate, err = json.Marshal(asrRes)
		if err != nil {
			logger.CtxLog(c).Errorf("json.Marshal error: %v", err)
			return err
		}
	}

	cache := &model.SpeechToText{
		UrlMd5:    handlerUtils.CalculateMD5FromText(url),
		FileMd5:   fileMd5,
		AudioURL:  url,
		Content:   string(asrBate),
		Status:    status,
		ErrorMsg:  errMsg,
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	if err = cache.CreateSpeechToText(gomysql.DB); err != nil {
		logger.CtxLog(c).Errorf("CreateSpeechToText error: %v", err)
		return err
	}
	return nil
}
