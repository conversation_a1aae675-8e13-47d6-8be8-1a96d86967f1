package minilanguagecensor

func GetCensorLabel(id int64) string {
	switch id {
	case -1:
		return "正常"
	case 0:
		return "正常"
	case 1:
		return "党史和抗战史（正向）"
	case 2:
		return "党政及国家制度（负向）"
	case 3:
		return "歧视偏见"
	case 4:
		return "违反公序良俗"
	case 5:
		return "扭曲国家形象"
	case 6:
		return "关键国家领导人（负向）"
	case 7:
		return "窥探国家机密"
	case 8:
		return "涉1（负向）"
	case 9:
		return "党代材料"
	case 10:
		return "涉1（正向）"
	case 11:
		return "违法意图"
	case 12:
		return "党政及国家制度（正向）"
	case 13:
		return "领土完整（正向）"
	case 14:
		return "敏感政策（正向）"
	case 15:
		return "关键国家领导人（正向）"
	case 16:
		return "政治人物（负向）"
	case 17:
		return "涉政恶意"
	case 18:
		return "英烈楷模（负向）"
	case 19:
		return "政治人物（正向）"
	case 20:
		return "领土完整（负向）"
	case 21:
		return "国际关系（正向）"
	case 22:
		return "敏感政策（负向）"
	case 23:
		return "国际关系（负向）"
	case 24:
		return "敏感民族宗教（负向）"
	case 25:
		return "辱骂"
	case 26:
		return "色情"
	case 27:
		return "党史和抗战史（负向）"
	case 28:
		return "热点舆情"
	case 29:
		return "邪教（正常）"
	case 30:
		return "低俗内容"
	case 31:
		return "邪教（负向）"
	case 32:
		return "两性知识"
	case 33:
		return "自杀自残"
	case 34:
		return "侵犯个人隐私"
	case 35:
		return "敏感民族宗教（正向）"
	case 36:
		return "劣迹艺人"
	case 37:
		return "心理健康"
	case 38:
		return "宣扬封建迷信"
	case 39:
		return "未成年不宜"
	case 40:
		return "血腥暴力"
	case 41:
		return "检测到敏感信息"
	}
	return "未知审核原因"
}
