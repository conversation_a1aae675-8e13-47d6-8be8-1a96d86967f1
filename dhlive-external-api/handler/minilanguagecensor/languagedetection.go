// 语种检测
// 支持检测中文、英文等多语种
package minilanguagecensor

import (
	"acg-ai-go-common/logger"
	"acg-ai-go-common/utils"
	"acg-ai-go-common/utils/httputil"
	"bytes"
	"context"
	config "dhlive-external-api/conf"
	"encoding/json"
	"fmt"
	"io"
	"mime/multipart"
	"os"
	"path/filepath"
	"time"
)

// 文本语种检测接口
func IsMiniLangBytext(logCtx context.Context, text string) (bool, error) {
	// 调用第三方API进行语种检测
	body := map[string]string{
		"query": text,
	}

	// 序列化请求数据
	jsonData, err := json.Marshal(body)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"SendCallbackRequse Marshal request failed: %+v\n", err)
		return false, err
	}
	// 返回语种
	retryHttpCli := httputil.NewRetryHTTPClient(15*time.Second, 3)
	header := map[string]string{
		"Content-Type": "application/json",
	}

	res, err := retryHttpCli.DoRequest(logCtx, "POST", config.LocalConfig.RiskControl.MiniTextDetectionUrl, header, bytes.NewBuffer(jsonData))
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+" textLanguageDetection error: %+v", err)
		return false, err
	}
	logger.Log.Infof(utils.MMark(logCtx)+" textLanguageDetection res: %+v", string(res))
	type Data struct {
		Label string `json:"label"`
	}
	// 定义结构体
	type Response struct {
		Data      Data   `json:"data"`
		ErrorCode int    `json:"error_code"`
		ErrorMsg  string `json:"error_msg"`
	}
	var response Response
	if err = json.Unmarshal(res, &response); err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+" textLanguageDetection error: %+v , res: %+v", err, string(res))
		return false, err
	}
	if response.ErrorCode != 0 {
		logger.Log.Errorf(utils.MMark(logCtx)+" textLanguageDetection error: %+v", response.ErrorMsg)
		return false, fmt.Errorf(response.ErrorMsg)
	}

	if response.Data.Label == "is_han" {
		return false, nil
	}

	return true, nil
}

// // 音频语种检测接口
func IsMiniLangByAudio(logCtx context.Context, filePath string) (bool, error) {
	// 打开要上传的文件
	file, err := os.Open(filePath)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+" Failed to open file: %v", err)
		return false, err
	}
	defer file.Close()

	// 创建一个 buffer 用于构造 multipart/form-data 请求体
	var requestBody bytes.Buffer
	writer := multipart.NewWriter(&requestBody)

	// 创建 form 文件字段
	part, err := writer.CreateFormFile("file", filepath.Base(filePath))
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+" Error creating form file: %v", err)
		return false, err
	}

	// 将文件内容复制到 form 字段中
	_, err = io.Copy(part, file)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+" Error copying file: %v", err)
		return false, err
	}

	// 关闭 multipart 写入器，设置好边界等信息
	err = writer.Close()
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+" Error closing writer: %v", err)
		return false, err
	}

	// 构造 header
	headers := map[string]string{
		"Content-Type": writer.FormDataContentType(), // 非常重要！
	}

	retryHttpCli := httputil.NewRetryHTTPClient(15*time.Second, 3)
	// 发送 POST 请求
	resp, err := retryHttpCli.DoRequest(logCtx, "POST", config.LocalConfig.RiskControl.MiniAudioAsrUrl, headers, &requestBody)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+" audioLanguageDetection error: %+v", err)
		return false, err
	}

	type ASRResponse struct {
		ErrorCode int    `json:"error_code"`
		ErrorMsg  string `json:"error_msg"`
		Text      string `json:"text"`
	}
	var asrRes ASRResponse
	err = json.Unmarshal(resp, &asrRes)
	if err != nil {
		fmt.Println("Error parsing JSON:", err)
		return false, err
	}
	logger.Log.Infof(utils.MMark(logCtx)+" textLanguageDetection res: %+v", asrRes.Text)

	// 判断是否为小语种
	if ok, err := IsMiniLangBytext(logCtx, asrRes.Text); err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+" isMiniLangBytext err:%v", err)
		return false, err
	} else if !ok {
		return false, nil
	}

	return true, nil
}

// // 视频语种检测接口
// func isMiniLangByVideo(video []byte) string {
// 	// 调用第三方API进行语种检测
// 	// 返回语种
// 	return "ja"
// }

// // 图片语种检测接口
// func isMiniLangByImage(image []byte) string {
// 	// 调用第三方API进行语种检测
// 	// 返回语种
// 	return "ko"
// }
