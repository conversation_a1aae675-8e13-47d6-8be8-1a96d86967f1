// 小语种审核接口
package minilanguagecensor

import (
	"acg-ai-go-common/logger"
	"acg-ai-go-common/utils"
	"acg-ai-go-common/utils/httputil"
	"bytes"
	"context"
	config "dhlive-external-api/conf"
	"encoding/json"
	"io"
	"mime/multipart"
	"os"
	"path/filepath"
	"time"
)

type CensorTextMiniLanguageResponse struct {
	Data struct {
		LabelID int64    `json:"41_label_id"`
		Score   float32  `json:"score"`
		Words   []string `json:"words"`
	} `json:"data"`
	ErrorCode int64  `json:"error_code"`
	ErrorMsg  string `json:"error_msg"`
}

// 文本小语种审核
func CensorTextPost(logCtx context.Context, text string) (string, error) {
	// 调用第三方API进行语种检测
	header := map[string]string{
		"Content-Type": "application/json",
	}

	body := map[string]string{
		"query":     text,
		"algorithm": "text_detect",
	}

	// 序列化请求数据
	jsonData, err := json.Marshal(body)
	if err != nil {
		logger.Log.<PERSON>rrorf(utils.MMark(logCtx)+"SendCallbackRequse Marshal request failed: %+v\n", err)
		return "", err
	}

	retryHttpCli := httputil.NewRetryHTTPClient(15*time.Second, 3)
	res, err := retryHttpCli.DoRequest(logCtx, "POST", config.LocalConfig.RiskControl.MiniCensorUrl, header, bytes.NewBuffer(jsonData))
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+" textLanguageDetection error: %+v", err)
		return "", err
	}
	// 不是小语种文本，则进行常规审核
	return string(res), nil

}

// 图片小语种审核
func imageMiniLanguageCensor(imageUrl string) {

}

// 视频小语种审核
func videoMiniLangeuageCensor(videoUrl string) {

}

// 音频小语种审核
func CensorAudioPost(logCtx context.Context, filePath string) (string, error) {
	// 打开要上传的文件
	file, err := os.Open(filePath)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+" Failed to open file: %v", err)
		return "", err
	}
	defer file.Close()

	// 创建一个 buffer 用于构造 multipart/form-data 请求体
	var requestBody bytes.Buffer
	writer := multipart.NewWriter(&requestBody)

	// 写入字符串字段 algorithm=audio_detect
	err = writer.WriteField("algorithm", "audio_detect")
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+" Error writing field: %v", err)
		return "", err
	}

	// 创建 form 文件字段
	part, err := writer.CreateFormFile("file", filepath.Base(filePath))
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+" Error creating form file: %v", err)
		return "", err
	}

	// 将文件内容复制到 form 字段中
	_, err = io.Copy(part, file)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+" Error copying file: %v", err)
		return "", err
	}

	// 关闭 multipart 写入器，设置好边界等信息
	err = writer.Close()
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+" Error closing writer: %v", err)
		return "", err
	}

	// 构造 header
	headers := map[string]string{
		"Content-Type": writer.FormDataContentType(), // 非常重要！
	}

	retryHttpCli := httputil.NewRetryHTTPClient(15*time.Second, 3)
	// 发送 POST 请求
	resp, err := retryHttpCli.DoRequest(logCtx, "POST", config.LocalConfig.RiskControl.MiniCensorUrl, headers, &requestBody)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+" audioLanguageDetection error: %+v", err)
		return "", err
	}
	logger.Log.Infof(utils.MMark(logCtx)+"audioLanguageDetection response: %s", string(resp))
	return string(resp), nil
}
