package minilanguagecensor

import (
	"acg-ai-go-common/logger"
	"acg-ai-go-common/utils"
	"acg-ai-go-common/utils/httputil"
	"bytes"
	"context"
	"crypto/md5"
	config "dhlive-external-api/conf"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"time"
)

type SensitiveInfoDetection struct {
}

// 生成 token
func (s *SensitiveInfoDetection) GenerateToken() string {
	timestamp := fmt.Sprintf("%d", time.Now().Unix())
	raw := timestamp + config.LocalConfig.RiskControl.SensitiveInfoDetectionAK + config.LocalConfig.RiskControl.SensitiveInfoDetectionSK

	hash := md5.Sum([]byte(raw))
	sign := hex.EncodeToString(hash[:])

	return fmt.Sprintf("%s.%s.%s", sign, timestamp, config.LocalConfig.RiskControl.SensitiveInfoDetectionAK)
}

// 构建请求体结构
type SensitiveInfoDetectionRequestBody struct {
	Token    string   `json:"token"`
	Content  []string `json:"content"`
	TaskType string   `json:"task_type"`
}

func (s *SensitiveInfoDetection) SensitiveInfoDetection(logCtx context.Context, body SensitiveInfoDetectionRequestBody) ([]string, error) {
	jsonData, err := json.Marshal(body)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+" json marshal error: %+v", err)
		return nil, err
	}
	// 返回语种
	retryHttpCli := httputil.NewRetryHTTPClient(15*time.Second, 3)
	header := map[string]string{
		"Content-Type": "application/json",
	}

	res, err := retryHttpCli.DoRequest(logCtx, "POST", config.LocalConfig.RiskControl.SensitiveInfoDetectionUrl, header, bytes.NewBuffer(jsonData))
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+" http request error: %+v", err)
		return nil, err
	}
	type Response struct {
		ErrNo int      `json:"ErrNo"`
		Msg   string   `json:"Msg"`
		Data  []string `json:"Data"`
	}

	var result Response
	if err := json.Unmarshal(res, &result); err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+" json unmarshal error: %+v", err)
		return nil, err
	}

	if result.ErrNo != 200 {
		logger.Log.Errorf(utils.MMark(logCtx)+" SensitiveInfoDetection error: %+v", result.Msg)
		return nil, fmt.Errorf("SensitiveInfoDetection error: %v", result.Msg)
	}

	logger.Log.Infof(utils.MMark(logCtx)+" textLanguageDetection response: %+v", string(res))
	return result.Data, nil
}
