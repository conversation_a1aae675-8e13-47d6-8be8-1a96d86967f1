package feedback

import (
	commProto "acg-ai-go-common/beans/proto"
	"acg-ai-go-common/gomysql"
	"acg-ai-go-common/logger"
	"acg-ai-go-common/utils"
	"context"
	"dhlive-external-api/beans/model"
	"dhlive-external-api/beans/proto"
	"dhlive-external-api/handler/i18n/respi18n"
	"encoding/json"
	"github.com/gin-gonic/gin"
	"net/http"
	"strings"
)

type WorkPreLabels struct {
	Label string `json:"label"`
}
type WorkPreLabelInsert struct {
	Type        string          `json:"type" binding:"required"`
	IsAuth      bool            `json:"isAuth"`
	ControlType string          `json:"controlType" binding:"required"`
	ControlName string          `json:"controlName" binding:"required"`
	Required    bool            `json:"required"`
	ColSpan     int             `json:"colSpan" binding:"required"`
	MaxLength   int             `json:"maxLength"`
	Sort        int             `json:"sort" binding:"required"`
	Others      interface{}     `json:"others"`
	Creator     string          `json:"creator" binding:"required"`
	Options     []WorkPreLabels `json:"options"`
}
type WorkPreLabelSearch struct {
	Type string `form:"type" binding:"required"`
}
type WorkPreLabelUp struct {
	Id          int64           `json:"id"`
	Type        string          `json:"type" binding:"required"`
	IsAuth      bool            `json:"isAuth"`
	ControlType string          `json:"controlType" binding:"required"`
	ControlName string          `json:"controlName" binding:"required"`
	Required    bool            `json:"required"`
	ColSpan     int             `json:"colSpan" binding:"required"`
	MaxLength   int             `json:"maxLength"`
	Sort        int             `json:"sort" binding:"required"`
	Others      interface{}     `json:"others"`
	Operator    string          `json:"operator"`
	Options     []WorkPreLabels `json:"options"`
}

type WorkPreLabelDel struct {
	Id       int64  `form:"id" binding:"required"`
	Operator string `form:"operator" binding:"required"`
}
type WorkPreLabelList struct {
	PageNo   int64  `json:"pageNo" form:"pageNo" binding:"required"`     // 页码
	PageSize int64  `json:"pageSize" form:"pageSize" binding:"required"` // 每页数量
	Type     string `json:"type" form:"type"`
}

type WorkPreLabelHandler struct {
	Id        int64           `json:"id"`
	Type      string          `json:"type"`
	Module    string          `json:"module"`
	IsAuth    bool            `json:"isAuth"`
	Name      string          `json:"name"`
	Title     string          `json:"title"`
	Required  bool            `json:"required"`
	ColSpan   int             `json:"colSpan"`
	MaxLength int             `json:"maxLength"`
	Sort      int             `json:"sort"`
	Others    interface{}     `json:"others"`
	Options   []WorkPreLabels `json:"options"`
}

func WorkPreviewLabels(c *gin.Context) {
	// 获取目标语言
	targetLanguage := c.GetHeader("Language")
	logCtx := context.WithValue(context.TODO(), utils.CtxKeyLogID, utils.RandStringRunes(18))
	req := &WorkPreLabelSearch{}
	if err := c.ShouldBindQuery(req); err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"WorkPreviewLabels 解析请求参数失败 error=%v", err)
		c.JSON(http.StatusOK, commProto.NewCommDataRsp(100001, err.Error(), nil))
		return
	}
	workpreTag := &model.WorkPreTag{
		Type: req.Type,
	}
	labels, err := workpreTag.Search(gomysql.DB, logCtx)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"WorkPreviewLabels UpdateStatus error=%v", err)
		c.JSON(http.StatusOK, commProto.NewCommRsp(510001, err.Error()))
		return
	}
	questionnaires := make([]WorkPreLabelHandler, 0, len(labels))
	for _, t := range labels {
		labe, err := convertToWorkPreTagHandler(logCtx, t, targetLanguage)
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+"WorkPreviewLabels UpdateStatus error=%v", err)
			c.JSON(http.StatusOK, commProto.NewCommRsp(10002, err.Error()))
			continue
		}
		questionnaires = append(questionnaires, labe)
	}
	c.JSON(http.StatusOK, proto.NewSuccessRsp(gin.H{"tags": questionnaires}))
}

func WorkPreviewList(c *gin.Context) {
	targetLanguage := c.GetHeader("Language")
	logCtx := context.WithValue(context.TODO(), utils.CtxKeyLogID, utils.RandStringRunes(18))
	req := &WorkPreLabelList{}
	if err := c.ShouldBindJSON(req); err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"WorkPreviewList 解析请求参数失败 error=%v", err)
		c.JSON(http.StatusOK, commProto.NewCommDataRsp(100001, err.Error(), nil))
		return
	}
	workpreTag := &model.WorkPreTag{
		Type: req.Type,
	}
	count, labels, err := workpreTag.SearchList(gomysql.DB, logCtx, req.PageNo, req.PageSize)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"WorkPreviewList UpdateStatus error=%v", err)
		c.JSON(http.StatusOK, commProto.NewCommRsp(510001, err.Error()))
		return
	}
	workpreLabels := make([]WorkPreLabelHandler, 0, len(labels))
	for _, t := range labels {
		label, err := convertToWorkPreTagHandler(logCtx, t, targetLanguage)
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+"WorkPreviewList UpdateStatus error=%v", err)
			c.JSON(http.StatusOK, commProto.NewCommRsp(10002, err.Error()))
			continue
		}
		workpreLabels = append(workpreLabels, label)
	}
	c.JSON(http.StatusOK, commProto.NewSuccessRsp(gin.H{"count": count, "list": workpreLabels}))
}
func WorkPreLabelAdd(c *gin.Context) {
	logCtx := context.WithValue(context.TODO(), utils.CtxKeyLogID, utils.RandStringRunes(18))
	req := &WorkPreLabelInsert{}
	if err := c.ShouldBindJSON(req); err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"WorkPreLabelAdd 解析请求参数失败 error=%v", err)
		c.JSON(http.StatusOK, commProto.NewCommDataRsp(100001, err.Error(), nil))
		return
	}
	workpreTag := &model.WorkPreTag{
		Type: req.Type,
	}
	status, err := workpreTag.GetAuthStatus(gomysql.DB, logCtx)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"WorkPreLabelAdd  GetAuthStatus error=%v", err)
		c.JSON(http.StatusOK, proto.NewCommRsp(510001, err.Error()))
		return
	}

	if status != 0 {
		if map[bool]int{true: 1, false: 2}[req.IsAuth] != status {
			c.JSON(http.StatusOK, commProto.NewCommDataRsp(100001, "isAuth status disagree", nil))
			return
		}
	}
	label, err := workpreTag.QueryByProblemType(gomysql.DB, logCtx, req.ControlName)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"WorkPreLabelAdd QueryByProblemType error=%v", err)
		c.JSON(http.StatusOK, commProto.NewCommDataRsp(510001, err.Error(), nil))
		return
	}
	var updatedOptions []WorkPreLabels
	if label != nil {
		workpreTag.ID = label.ID
		if len(label.Options) > 0 {
			err = json.Unmarshal([]byte(label.Options), &updatedOptions)
			if err != nil {
				logger.Log.Errorf(utils.MMark(logCtx)+"WorkPreLabelAdd Unmarshal error=%v", err)
				c.JSON(http.StatusOK, commProto.NewCommDataRsp(100002, err.Error(), nil))
				return
			}
		} else {
			updatedOptions = make([]WorkPreLabels, 0)
		}
	}
	for _, t := range req.Options {
		updatedOptions = append(updatedOptions, WorkPreLabels{Label: t.Label})
	}
	optionsJson, err := json.Marshal(updatedOptions)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"WorkPreLabelAdd Marshal error=%v", err)
		c.JSON(http.StatusOK, commProto.NewCommDataRsp(100002, err.Error(), nil))
		return
	}
	otherJsonStr := ""
	if req.Others != nil {
		otherJson, err := json.Marshal(req.Others)
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx) + "WorkPreLabelUpdate req.Others json.Marshal")
			c.JSON(http.StatusOK, commProto.NewCommDataRsp(100002, err.Error(), nil))
			return
		}
		otherJsonStr = string(otherJson)
		if otherJsonStr != `""` && otherJsonStr != "{}" && !strings.HasPrefix(otherJsonStr, "{") {
			logger.Log.Errorf(utils.MMark(logCtx)+"WorkPreLabelUpdate req.Others must be a JSON got: %s", otherJsonStr)
			c.JSON(http.StatusBadRequest, commProto.NewCommDataRsp(100002, "others must be a JSON", nil))
			return
		}
	}

	workpreTag.Type = req.Type
	workpreTag.IsAuth = map[bool]int{true: 1, false: 2}[req.IsAuth]
	workpreTag.Required = map[bool]int{true: 1, false: 2}[req.Required]
	workpreTag.ColSpan = req.ColSpan
	workpreTag.MaxLength = req.MaxLength
	workpreTag.ControlType = req.ControlType
	workpreTag.ControlName = req.ControlName
	workpreTag.Sort = req.Sort
	workpreTag.Creator = req.Creator
	workpreTag.Others = otherJsonStr
	workpreTag.Options = string(optionsJson)
	if label != nil {
		idx, err := workpreTag.Update(gomysql.DB, logCtx)
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+"WorkPreLabelAdd Update error=%v", err)
			c.JSON(http.StatusOK, commProto.NewCommDataRsp(510002, err.Error(), nil))
			return
		}
		logger.Log.Infof(utils.MMark(logCtx)+"WorkPreLabelAdd Update success, id=%v", idx)
		c.JSON(http.StatusOK, commProto.NewSuccessRsp(nil))
		return
	}
	idx, err := workpreTag.Insert(gomysql.DB, logCtx)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"WorkPreLabelAdd Insert error=%v", err)
		c.JSON(http.StatusOK, commProto.NewCommDataRsp(510003, err.Error(), nil))
		return
	}
	logger.Log.Infof(utils.MMark(logCtx)+"WorkPreLabelAdd Insert success, id=%v", idx)
	c.JSON(http.StatusOK, commProto.NewSuccessRsp(nil))

}
func WorkPreLabelUpdate(c *gin.Context) {
	logCtx := context.WithValue(context.TODO(), utils.CtxKeyLogID, utils.RandStringRunes(18))
	req := &WorkPreLabelUp{}
	if err := c.ShouldBindJSON(req); err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"WorkPreLabelUpdate 解析请求参数失败 error=%v", err)
		c.JSON(http.StatusOK, commProto.NewCommDataRsp(100001, err.Error(), nil))
		return
	}
	optionString := ""
	if len(req.Options) > 0 {
		externalJson, err := json.Marshal(req.Options)
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx) + "WorkPreLabelUpdate 参数错误")
			c.JSON(http.StatusOK, commProto.NewCommDataRsp(100001, err.Error(), nil))
			return
		}
		optionString = string(externalJson)
	} else {
		optionString = "[]"
	}
	otherJsonStr := ""
	if req.Others != nil {
		otherJson, err := json.Marshal(req.Others)
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx) + "WorkPreLabelUpdate req.Others json.Marshal")
			c.JSON(http.StatusOK, commProto.NewCommDataRsp(100002, err.Error(), nil))
			return
		}
		otherJsonStr = string(otherJson)
		if otherJsonStr != `""` && otherJsonStr != "{}" && !strings.HasPrefix(otherJsonStr, "{") {
			logger.Log.Errorf(utils.MMark(logCtx)+"WorkPreLabelUpdate req.Others must be a JSON got: %s", otherJsonStr)
			c.JSON(http.StatusBadRequest, commProto.NewCommDataRsp(100002, "others must be a JSON", nil))
			return
		}
	}

	workpreTag := &model.WorkPreTag{
		ID:          req.Id,
		Type:        req.Type,
		ControlType: req.ControlType,
		ControlName: req.ControlName,
		IsAuth:      map[bool]int{true: 1, false: 2}[req.IsAuth],
		Required:    map[bool]int{true: 1, false: 2}[req.Required],
		ColSpan:     req.ColSpan,
		MaxLength:   req.MaxLength,
		Sort:        req.Sort,
		Others:      otherJsonStr,
		Operator:    req.Operator,
		Options:     optionString,
	}
	idx, err := workpreTag.Update(gomysql.DB, logCtx)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+" WorkPreLabelUpdate Update error=%v", err)
		c.JSON(http.StatusOK, commProto.NewCommDataRsp(510001, err.Error(), nil))
		return
	}
	logger.Log.Infof(utils.MMark(logCtx)+"WorkPreLabelUpdate Update success, idx=%v", idx)
	c.JSON(http.StatusOK, commProto.NewSuccessRsp(nil))
}
func WorkPreLabelDelete(c *gin.Context) {
	logCtx := context.WithValue(context.TODO(), utils.CtxKeyLogID, utils.RandStringRunes(18))
	req := &WorkPreLabelDel{}
	if err := c.ShouldBindQuery(req); err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"WorkPreLabelUpdate 解析请求参数失败 error=%v", err)
		c.JSON(http.StatusOK, commProto.NewCommDataRsp(100001, err.Error(), nil))
		return
	}
	workpreTag := &model.WorkPreTag{
		ID:      req.Id,
		Options: req.Operator,
	}
	err := workpreTag.UpdateByOperator(gomysql.DB, logCtx)
	if err != nil {
		logger.CtxLog(c).Errorf("WorkPreLabelUpdate UpdateByOperator fail,  error=%v", err)
		c.JSON(http.StatusOK, commProto.NewCommRsp(510001, err.Error()))
		return
	}
	err = workpreTag.DeleteByID()
	if err != nil {
		logger.CtxLog(c).Errorf("WorkPreLabelUpdate DeleteByID fail, error=%v", err)
		c.JSON(http.StatusOK, commProto.NewCommRsp(510002, err.Error()))
		return
	}
	c.JSON(http.StatusOK, commProto.NewSuccessRsp(nil))
}

/*
*
数据格式转换
*/
func convertToWorkPreTagHandler(logCtx context.Context, t model.WorkPreTag, targetLanguage string) (WorkPreLabelHandler, error) {
	var labels []WorkPreLabels
	// 如果字段值不为空进行反序列化
	if len(t.Options) > 0 {
		err := json.Unmarshal([]byte(t.Options), &labels)
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+"convertToWorkPreTagHandler json.Unmarshal error=%v", err)
			return WorkPreLabelHandler{}, err
		}
	} else {
		// 字段值为空反馈[] 数组
		labels = make([]WorkPreLabels, 0)
	}

	// 遍历 labels 并对每个 Label 进行翻译
	translatedLabels := make([]WorkPreLabels, len(labels))
	for i, label := range labels {
		translatedLabel := respi18n.TransformResponseLocaleBySubTag(logCtx, "work-preview-feedback", targetLanguage, label.Label)
		translatedLabels[i] = WorkPreLabels{
			Label: translatedLabel,
		}
	}
	var jsonMap map[string]interface{}
	err := json.Unmarshal([]byte(t.Others), &jsonMap)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"convertToWorkPreTagHandler Others json.Unmarshal error=%v", err)
	}

	return WorkPreLabelHandler{
		Id:        t.ID,
		Module:    t.Type,
		Type:      t.ControlType,
		Name:      t.ControlName,
		Title:     respi18n.TransformResponseLocaleBySubTag(logCtx, "work-preview-feedback", targetLanguage, t.ControlName),
		Required:  t.Required != 2,
		IsAuth:    t.IsAuth != 2,
		ColSpan:   t.ColSpan,
		MaxLength: t.MaxLength,
		Sort:      t.Sort,
		Others:    jsonMap,
		Options:   translatedLabels,
	}, nil

}
