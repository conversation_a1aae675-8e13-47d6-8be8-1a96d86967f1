package feedback

import (
	commProto "acg-ai-go-common/beans/proto"
	"acg-ai-go-common/gomysql"
	"acg-ai-go-common/logger"
	"acg-ai-go-common/utils"
	"context"
	"dhlive-external-api/beans/model"
	"dhlive-external-api/beans/proto"
	"encoding/json"
	"github.com/gin-gonic/gin"
	"net/http"
	"regexp"
	"strings"
	"time"
	"unicode/utf8"
)

type UserFeedbackInsert struct {
	Type        []string `json:"type" form:"type"`
	CurrentPage string   `json:"currentPage" form:"current_page"`
	Describe    string   `json:"describe" form:"describe"`
	Contact     string   `json:"contact" form:"contact"`
	UploadArr   []string `json:"uploadArr" form:"upload_arr"`
}

type Content struct {
	Type       []string `json:"type"`
	Describe   string   `json:"describe"`
	Contact    string   `json:"contact"`
	UploadAddr []string `json:"upload_addr"`
}

type TransformedFeedback struct {
	ID          int64   `json:"id"`
	UserId      string  `json:"user_id"`
	CreateTime  int64   `json:"create_time"`
	CurrentPage string  `json:"currentPage"`
	Status      string  `json:"status"`
	ProcessTime int64   `json:"process_time"`
	Content     Content `json:"conent"`
}
type UserFeedbackUpdate struct {
	ID       int64  `json:"id"`
	Status   string `json:"status"`
	Operator string `json:"operator"`
	Reason   string `json:"reason"`
}
type UserFeedbackDelete struct {
	ID       int64  `json:"id"`
	Operator string `json:"operator"`
	Reason   string `json:"reason"`
}

var (
	// 中国大陆手机号：1开头，11位数字
	cnPhoneRegex = regexp.MustCompile(`^1[3-9]\d{9}$`)
	// 国际电话号码：+开头，1-4位国家代码，随后是7-15位数字
	internationalPhoneRegex = regexp.MustCompile(`^\+\d{1,4}\d{7,15}$`)
	// 邮箱基本格式校验
	emailRegex = regexp.MustCompile(`^[a-zA-Z0-9][a-zA-Z0-9._+\-]*@[a-zA-Z0-9\-]+\.[a-zA-Z]{2,}(\.[a-zA-Z]{2,})?$`)
	// 禁止的特殊字符
	invalidSpecialChars = regexp.MustCompile(`[^a-zA-Z0-9._+\-@]`)
)

func AddUserFeedBack(c *gin.Context) {
	// 默认未登录
	accountID := ""
	// 获取登录信息
	aid, ok := c.Get("AccountId")
	if ok {
		accountID = aid.(string)
	}
	logCtx := context.WithValue(context.TODO(), utils.CtxKeyLogID, utils.RandStringRunes(18))
	req := &UserFeedbackInsert{}
	if err := c.ShouldBindJSON(req); err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"AddUserFeedBack 解析请求参数失败 error=%v", err)
		c.JSON(http.StatusOK, proto.NewCommRsp(100001, "参数解析异常"))
		return
	}
	if len(req.Type) == 0 || len(req.CurrentPage) == 0 || len(req.Describe) == 0 {
		logger.Log.Errorf(utils.MMark(logCtx) + "AddUserFeedBack 解析错误")
		c.JSON(http.StatusOK, proto.NewCommRsp(100002, "参数错误"))
		return
	}

	if utf8.RuneCountInString(req.Describe) > 200 {
		logger.Log.Errorf(utils.MMark(logCtx) + "AddUserFeedBack Contact fail")
		c.JSON(http.StatusOK, proto.NewCommRsp(100003, "描述文字超限"))
		return
	}
	if len(req.UploadArr) > 4 {
		logger.Log.Errorf(utils.MMark(logCtx) + "AddUserFeedBack Contact fail")
		c.JSON(http.StatusOK, proto.NewCommRsp(100003, "素材超限"))
		return
	}
	// 如果传入联系方式则进行校验，否则不进行校验
	contact := strings.TrimSpace(req.Contact)
	if len(contact) > 0 {
		var isValidate bool
		contact = strings.TrimSpace(req.Contact)
		if validatePhone(contact) {
			isValidate = true
		} else if validateEmail(contact) {
			isValidate = true
		}
		if !isValidate {
			logger.Log.Errorf(utils.MMark(logCtx) + "AddUserFeedBack Contact fail")
			c.JSON(http.StatusOK, proto.NewCommRsp(100003, "请输入有效的手机号或邮箱"))
			return
		}
	}

	// type进行序列化
	typeJson, err := json.Marshal(req.Type)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"AddUserFeedBack typeJson error=%v", err)
		c.JSON(http.StatusOK, proto.NewCommRsp(100002, err.Error()))
		return
	}

	uploadArrString := ""
	if len(req.UploadArr) > 0 {
		uploadArrJson, err := json.Marshal(req.UploadArr)
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+"AddUserFeedBack uploadArrJson error=%v", err)
			c.JSON(http.StatusOK, proto.NewCommRsp(100002, err.Error()))
			return
		}
		uploadArrString = string(uploadArrJson)
	}

	userFeedback := &model.UserFeedbackV1{
		UserId:      accountID,
		Status:      "UNTREATED",
		Type:        string(typeJson),
		Contact:     contact,
		UploadArr:   uploadArrString,
		CurrentPage: req.CurrentPage,
		Describe:    req.Describe,
		ProcessTime: time.Now(),
	}
	id, err := userFeedback.Insert(gomysql.DB, logCtx)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"AddUserFeedBack  userFeedback.Insert error=%v", err)
		c.JSON(http.StatusOK, proto.NewCommRsp(100004, err.Error()))
		return
	}
	logger.Log.Infof(utils.MMark(logCtx)+"AddUserFeedBack Insert success, insertID=%v", id)
	c.JSON(http.StatusOK, proto.NewSuccessRsp(nil))
}

func DeleteUserFeedBack(c *gin.Context) {
	logCtx := context.WithValue(context.TODO(), utils.CtxKeyLogID, utils.RandStringRunes(18))
	req := &UserFeedbackDelete{}
	if err := c.ShouldBindJSON(req); err != nil {
		return
	}
	if len(req.Operator) == 0 || len(req.Reason) == 0 {
		logger.Log.Errorf(utils.MMark(logCtx) + "UpdateUserFeedBack 参数错误")
		c.JSON(http.StatusOK, commProto.NewCommRsp(100002, "参数错误"))
		return
	}
	userFeedback := &model.UserFeedbackV1{}
	id, err := userFeedback.DeleteItem(gomysql.DB, logCtx, req.ID, req.Operator, req.Reason)
	if err != nil {
		return
	}
	logger.Log.Infof(utils.MMark(logCtx)+"UpdateUserFeedBack UpdateStatus success, id=%v", id)
	c.JSON(http.StatusOK, commProto.NewSuccessRsp(nil))
}
func UpdateUserFeedBack(c *gin.Context) {
	logCtx := context.WithValue(context.TODO(), utils.CtxKeyLogID, utils.RandStringRunes(18))
	req := &UserFeedbackUpdate{}
	if err := c.ShouldBindJSON(req); err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"UpdateFeedbackType 解析请求参数失败 error=%v", err)
		c.JSON(http.StatusOK, commProto.NewCommRsp(100001, "参数解析异常"))
		return
	}
	if len(req.Operator) == 0 || len(req.Reason) == 0 || len(req.Status) == 0 {
		logger.Log.Errorf(utils.MMark(logCtx) + "UpdateUserFeedBack 参数错误")
		c.JSON(http.StatusOK, commProto.NewCommRsp(100002, "参数错误"))
		return
	}
	userFeedback := &model.UserFeedbackV1{}
	userFeedback.ID = req.ID
	userFeedback.Status = req.Status
	userFeedback.Operator = req.Operator
	userFeedback.Reason = req.Reason
	rows, err := userFeedback.UpdateStatus(gomysql.DB, logCtx)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"UpdateUserFeedBack UpdateStatus error=%v", err)
		c.JSON(http.StatusOK, commProto.NewCommRsp(10003, err.Error()))
		return
	}
	logger.Log.Infof(utils.MMark(logCtx)+"UpdateUserFeedBack UpdateStatus success, rows=%v", rows)
	c.JSON(http.StatusOK, commProto.NewSuccessRsp(nil))

}
func QueryUserFeedBack(c *gin.Context) {
	logCtx := context.WithValue(context.TODO(), utils.CtxKeyLogID, utils.RandStringRunes(18))
	req := &model.UserFeedbackQuery{}
	if err := c.ShouldBindJSON(req); err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"QueryUserFeedBack 解析请求参数失败 error=%v", err)
		c.JSON(http.StatusOK, proto.NewCommRsp(100001, "参数解析异常"))
		return
	}
	if req.PageNo <= 0 || req.PageSize <= 0 {
		logger.Log.Errorf(utils.MMark(logCtx) + "QueryUserFeedBack 解析错误")
		c.JSON(http.StatusOK, proto.NewCommRsp(100002, "参数错误"))
		return
	}
	userFeedback := &model.UserFeedbackV1{}
	total, feedbacks, err := userFeedback.GetFeedBackList(gomysql.DB, logCtx, req)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"QueryUserFeedBack  userFeedback.GetFeedBackList error=%v", err)
		c.JSON(http.StatusOK, proto.NewCommRsp(100003, err.Error()))
		return
	}
	// 格式处理
	var transformed []TransformedFeedback
	for _, fb := range feedbacks {
		transformed = append(transformed, transformFeedback(logCtx, fb))
	}
	if transformed == nil {
		transformed = make([]TransformedFeedback, 0)
	}
	logger.Log.Infof(utils.MMark(logCtx) + "QueryUserFeedBack success")
	c.JSON(http.StatusOK, commProto.NewSuccessRsp(gin.H{"count": total, "list": transformed}))
}

func validateEmail(email string) bool {
	// 基本格式校验
	if !emailRegex.MatchString(email) {
		return false
	}

	// 检查是否包含禁止的特殊字符
	if invalidSpecialChars.MatchString(email) {
		return false
	}

	// 分割本地部分和域名
	parts := strings.Split(email, "@")
	if len(parts) != 2 {
		return false
	}
	localPart, domain := parts[0], parts[1]

	// 检查本地部分不以特殊字符开头或结尾
	if len(localPart) == 0 {
		return false
	}
	firstChar := localPart[0]
	lastChar := localPart[len(localPart)-1]

	if isSpecialChar(firstChar) || isSpecialChar(lastChar) {
		return false
	}

	// 检查域名部分
	if strings.HasPrefix(domain, ".") || strings.HasSuffix(domain, ".") {
		return false
	}

	return true
}

// isSpecialChar 判断是否是特殊字符
func isSpecialChar(c byte) bool {
	return c == '.' || c == '_' || c == '+' || c == '-'
}

func validatePhone(phone string) bool {
	return IsChinesePhone(phone) || IsInternationalPhone(phone)
}

// IsChinesePhone 判断是否是中国大陆手机号
func IsChinesePhone(input string) bool {
	return cnPhoneRegex.MatchString(input)
}

// IsInternationalPhone 判断是否是国际电话号码
func IsInternationalPhone(input string) bool {
	return internationalPhoneRegex.MatchString(input)
}

/*
*
将查询的数据格式进行重组
*/
func transformFeedback(logCtx context.Context, feedback model.UserFeedbackV1) TransformedFeedback {
	logger.Log.Infof(utils.MMark(logCtx) + "transformFeedback")
	var typeResult []string
	var uploadArr []string
	// 对type 进行反序列化
	err := json.Unmarshal([]byte(feedback.Type), &typeResult)
	if err != nil {
		logger.Log.Infof(utils.MMark(logCtx)+"transformFeedback feedback.Type json.Unmarshal error=%v", err)
		return TransformedFeedback{}
	}
	if len(feedback.UploadArr) > 0 {
		err = json.Unmarshal([]byte(feedback.UploadArr), &uploadArr)
		if err != nil {
			logger.Log.Infof(utils.MMark(logCtx)+"transformFeedback feedback.UploadArr json.Unmarshal error=%v", err)
			return TransformedFeedback{}
		}
	}
	logger.Log.Infof(utils.MMark(logCtx) + "transformFeedback scuuess")
	return TransformedFeedback{
		ID:          feedback.ID,
		UserId:      feedback.UserId,
		CreateTime:  feedback.CreatedAt.Unix(),
		CurrentPage: feedback.CurrentPage,
		Status:      feedback.Status,
		ProcessTime: feedback.ProcessTime.Unix(),
		Content: Content{
			Type:       typeResult,
			Describe:   feedback.Describe,
			Contact:    feedback.Contact,
			UploadAddr: nilToEmptySlice(uploadArr),
		},
	}
}

/*
*
将空数据转换为空数组
*/
func nilToEmptySlice(arr []string) []string {
	if arr == nil {
		return []string{}
	}
	return arr
}
