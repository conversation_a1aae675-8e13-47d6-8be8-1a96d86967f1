package feedback

import (
	"acg-ai-go-common/beans/proto2"
	"acg-ai-go-common/logger"
	"acg-ai-go-common/utils"
	"context"
	"dhlive-external-api/beans/model"
	"github.com/gin-gonic/gin"
	"net/http"
)

type UpvoteFeedbackReq struct {
	ResourceType string `json:"resourceType" binding:"required"`
	ResourceId   string `json:"ResourceId" binding:"required"`
	Operate      string `json:"operate"`
}

type GetUpvoteFeedbackReq struct {
	ResourceType string   `json:"resourceType" binding:"required"`
	ResourceIds  []string `json:"resourceIds" binding:"required"`
}

func UpvoteFeedback(c *gin.Context) {
	accountID := ""
	// 获取登录信息
	aid, ok := c.Get("AccountId")
	if ok {
		accountID = aid.(string)
	}
	logCtx := context.WithValue(context.TODO(), utils.CtxKeyLogID, utils.RandStringRunes(18))
	req := &UpvoteFeedbackReq{}
	if err := c.Should<PERSON>indJ<PERSON>(req); err != nil {
		logger.Log.<PERSON>rf(utils.MMark(logCtx)+"UpvoteFeedback 解析请求参数失败 error=%v", err)
		c.JSON(http.StatusOK, proto2.NewCommRsp(100001, "参数解析异常"))
		return
	}

	upvote := model.Upvote{}
	_ = upvote.GetUpvoteByResource(accountID, req.ResourceType, req.ResourceId)
	if upvote.ID == 0 {
		upvote.UserId = accountID
		upvote.ResourceType = req.ResourceType
		upvote.ResourceId = req.ResourceId
		upvote.Operate = req.Operate
		err := upvote.Save()
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+"UpvoteFeedback 保存点赞信息失败 error=%v", err)
		}
		c.JSON(http.StatusOK, proto2.NewSuccessRsp(nil))
		return
	}

	upvote.Operate = req.Operate
	err := upvote.Save()
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"UpvoteFeedback 保存点赞信息失败 error=%v", err)
	}
	c.JSON(http.StatusOK, proto2.NewSuccessRsp(nil))
	return

}

func GetUpvoteFeedback(c *gin.Context) {
	accountID := ""
	// 获取登录信息
	aid, ok := c.Get("AccountId")
	if ok {
		accountID = aid.(string)
	}
	logCtx := context.WithValue(context.TODO(), utils.CtxKeyLogID, utils.RandStringRunes(18))
	req := &GetUpvoteFeedbackReq{}
	if err := c.ShouldBindJSON(req); err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"GetUpvoteFeedback 解析请求参数失败 error=%v", err)
		c.JSON(http.StatusOK, proto2.NewCommRsp(100001, "参数解析异常"))
		return
	}

	upvote := model.Upvote{}
	upvotes, err := upvote.FindByResourceType(accountID, req.ResourceType, req.ResourceIds)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"GetUpvoteFeedback 查询点赞信息失败 error=%v", err)
		c.JSON(http.StatusOK, proto2.NewCommRsp(100001, "查询点赞信息失败"))
		return
	}

	votes := make([]model.Upvote, 0)
	for _, id := range req.ResourceIds {
		for _, up := range upvotes {
			flag := false
			if up.ResourceId == id {
				votes = append(votes, *up)
				flag = true
			}
			if !flag {
				votes = append(votes, model.Upvote{
					ResourceType: req.ResourceType,
					ResourceId:   id,
					Operate:      "",
				})
			}
		}
	}
	resp := make(map[string]interface{}, 0)
	resp["votes"] = votes
	c.JSON(http.StatusOK, proto2.NewSuccessRsp(resp))
	return
}
