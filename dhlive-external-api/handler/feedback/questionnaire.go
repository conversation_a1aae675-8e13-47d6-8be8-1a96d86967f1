package feedback

import (
	commProto "acg-ai-go-common/beans/proto"
	"acg-ai-go-common/gomysql"
	"acg-ai-go-common/logger"
	"acg-ai-go-common/utils"
	"context"
	"dhlive-external-api/beans/model"
	"dhlive-external-api/beans/proto"
	"dhlive-external-api/handler/i18n/respi18n"
	"encoding/json"
	"github.com/gin-gonic/gin"
	"net/http"
	"reflect"
)

type InsertDynamicField struct {
	Key      string      `json:"key"`
	Value    interface{} `json:"value"`
	Required bool        `json:"required"`
}
type QuestionnaireInsertDynamic struct {
	Fields []InsertDynamicField `json:"fields" binding:"required"`
}
type QuestionnaireQuery struct {
	PageNo   int64  `json:"pageNo" form:"pageNo"`     // 页码
	PageSize int64  `json:"pageSize" form:"pageSize"` // 每页数量
	UserId   string `json:"userId" form:"userid"`
	Status   string `json:"status" form:"status"`       // 状态
	CreateAt string `json:"createdAt" form:"createdAt"` // 创建时间
}

type QuestionnaireUp struct {
	Id       int64  `form:"id" binding:"required"`
	Status   string `json:"status" form:"status"`
	Operator string `json:"operator"`
}
type QuestionnaireDel struct {
	Id       int64  `form:"id" binding:"required"`
	Operator string `form:"operator" binding:"required"`
}

func QuestionnaireList(c *gin.Context) {
	logCtx := context.WithValue(context.TODO(), utils.CtxKeyLogID, utils.RandStringRunes(18))
	req := &QuestionnaireQuery{}
	if err := c.ShouldBindJSON(req); err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"QuestionnaireList 解析请求参数失败 error=%v", err)
		c.JSON(http.StatusOK, proto.NewCommRsp(100001, err.Error()))
		return
	}
	questionnaire := &model.Questionnaire{
		UserId: req.UserId,
		Status: req.Status,
	}
	count, consults, err := questionnaire.SearchList(gomysql.DB, logCtx, req.PageNo, req.PageSize, req.CreateAt)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"QuestionnaireList SearchList error=%v", err)
		c.JSON(http.StatusOK, commProto.NewCommRsp(510001, err.Error()))
		return
	}
	c.JSON(http.StatusOK, commProto.NewSuccessRsp(gin.H{"count": count, "list": consults}))
}
func QuestionnaireAdd(c *gin.Context) {
	// 默认未登录
	accountID := ""
	// 获取登录信息
	aid, ok := c.Get("AccountId")
	if ok {
		accountID = aid.(string)
	}
	targetLanguage := c.GetHeader("Language")
	logCtx := context.WithValue(context.TODO(), utils.CtxKeyLogID, utils.RandStringRunes(18))
	req := &QuestionnaireInsertDynamic{}
	if err := c.ShouldBindJSON(req); err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"QuestionnaireAdd 解析请求参数失败 error=%v", err)
		c.JSON(http.StatusOK, proto.NewCommRsp(100001, respi18n.TransformResponseLocaleByDefault(logCtx, targetLanguage, "param exception")))
		return
	}
	for _, field := range req.Fields {
		if field.Required && isEmptyValue(field.Value) {
			logger.Log.Errorf(utils.MMark(logCtx)+"QuestionnaireAdd field.key: %v is required", field.Key)
			c.JSON(http.StatusOK, proto.NewCommRsp(100002, respi18n.TransformResponseLocaleByDefault(logCtx, targetLanguage, field.Key+" is required")))
			return
		}
	}
	fieldsJson, err := json.Marshal(req.Fields)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"QuestionnaireAdd fieldsJson json.Marshal error=%v", err)
		c.JSON(http.StatusOK, proto.NewCommRsp(100002, err.Error()))
		return
	}
	questionnaire := &model.Questionnaire{
		UserId:            accountID,
		QuestionnaireList: string(fieldsJson),
		Status:            "UNTREATED",
	}
	id, err := questionnaire.Insert(gomysql.DB, logCtx)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"QuestionnaireAdd  Insert error=%v", err)
		c.JSON(http.StatusOK, proto.NewCommRsp(510001, err.Error()))
		return
	}
	logger.Log.Infof(utils.MMark(logCtx)+"QuestionnaireAdd Insert success, insertID=%v", id)
	c.JSON(http.StatusOK, proto.NewSuccessRsp(nil))
}
func QuestionnaireUpdate(c *gin.Context) {
	logCtx := context.WithValue(context.TODO(), utils.CtxKeyLogID, utils.RandStringRunes(18))
	req := &QuestionnaireUp{}
	if err := c.ShouldBindJSON(req); err != nil {
		logger.Log.Infof(utils.MMark(logCtx)+"QuestionnaireUpdate 解析请求参数失败 error=%v", err)
		c.JSON(http.StatusOK, proto.NewCommRsp(100001, err.Error()))
		return
	}
	questionnaire := &model.Questionnaire{
		ID:       req.Id,
		Status:   req.Status,
		Operator: req.Operator,
	}
	idx, err := questionnaire.Update(gomysql.DB, logCtx)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"QuestionnaireUpdate Update error=%v", err)
		c.JSON(http.StatusOK, commProto.NewCommRsp(10003, err.Error()))
		return
	}
	logger.Log.Infof(utils.MMark(logCtx)+"QuestionnaireUpdate Update success, id=%v", idx)
	c.JSON(http.StatusOK, commProto.NewSuccessRsp(nil))
}
func QuestionnaireDelete(c *gin.Context) {
	logCtx := context.WithValue(context.TODO(), utils.CtxKeyLogID, utils.RandStringRunes(18))
	req := &QuestionnaireDel{}
	if err := c.ShouldBindQuery(req); err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"QuestionnaireDelete 解析请求参数失败 error=%v", err)
		c.JSON(http.StatusOK, commProto.NewCommDataRsp(100001, err.Error(), nil))
		return
	}
	questionnaire := &model.Questionnaire{
		ID:       req.Id,
		Operator: req.Operator,
	}
	err := questionnaire.UpdateByOperator(gomysql.DB, logCtx)
	if err != nil {
		logger.CtxLog(c).Errorf("QuestionnaireDelete UpdateByOperator fail,  error=%v", err)
		c.JSON(http.StatusOK, commProto.NewCommRsp(510001, err.Error()))
		return
	}
	err = questionnaire.DeleteByID()
	if err != nil {
		logger.CtxLog(c).Errorf("QuestionnaireDelete DeleteByID fail, error=%v", err)
		c.JSON(http.StatusOK, commProto.NewCommRsp(510002, err.Error()))
		return
	}
	c.JSON(http.StatusOK, commProto.NewSuccessRsp(nil))
}

func isEmptyValue(value interface{}) bool {
	if value == nil {
		return true
	}
	v := reflect.ValueOf(value)
	switch v.Kind() {
	case reflect.String:
		return v.Len() == 0 // 空字符串 ""
	case reflect.Slice, reflect.Array, reflect.Map:
		return v.Len() == 0 // 空切片 [] 或空 map {}
	case reflect.Int, reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64:
		return v.Int() == 0 // 0
	case reflect.Uint, reflect.Uint8, reflect.Uint16, reflect.Uint32, reflect.Uint64:
		return v.Uint() == 0 // 0
	case reflect.Float32, reflect.Float64:
		return v.Float() == 0 // 0.0
	case reflect.Bool:
		return !v.Bool() // false
	case reflect.Ptr, reflect.Interface:
		return v.IsNil() // nil 指针或 interface
	case reflect.Struct:
		return reflect.DeepEqual(value, reflect.Zero(v.Type()).Interface()) // 零值 struct
	default:
		return false // 其他类型默认不为空
	}
}
