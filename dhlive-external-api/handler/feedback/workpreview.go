package feedback

import (
	commProto "acg-ai-go-common/beans/proto"
	"acg-ai-go-common/gomysql"
	"acg-ai-go-common/logger"
	"acg-ai-go-common/utils"
	"context"
	"dhlive-external-api/beans/model"
	"dhlive-external-api/beans/proto"
	"dhlive-external-api/handler/i18n/respi18n"
	"encoding/json"
	"github.com/gin-gonic/gin"
	"net/http"
	"reflect"
	"strconv"
	"strings"
)

type WorkPreInsertDynamicField struct {
	Key      string      `json:"key"`
	Value    interface{} `json:"value"`
	Required bool        `json:"required"`
}
type WorkPreInsertDynamic struct {
	Type   string                      `json:"type"`
	Fields []WorkPreInsertDynamicField `json:"fields" binding:"required"`
}
type WorkPreQuery struct {
	PageNo   int64  `json:"pageNo" form:"pageNo"`     // 页码
	PageSize int64  `json:"pageSize" form:"pageSize"` // 每页数量
	Type     string `json:"type" form:"type"`
	UserId   string `json:"userId" form:"userid"`
	Status   string `json:"status" form:"status"`       // 状态
	CreateAt string `json:"createdAt" form:"createdAt"` // 创建时间
}

type WorkPreBatchUp struct {
	Ids      string `json:"ids" binding:"required"`
	Status   string `json:"status" form:"status"`
	Operator string `json:"operator"`
}
type WorkPreDel struct {
	Ids      string `json:"ids" binding:"required"`
	Operator string `json:"operator" binding:"required"`
}

type WorkPreAddRepLabel struct {
	Label string `json:"label"`
}
type AddResponse struct {
	Title  string               `json:"title"`
	Labels []WorkPreAddRepLabel `json:"labels"`
}

func WorkPreFeedbackList(c *gin.Context) {
	logCtx := context.WithValue(context.TODO(), utils.CtxKeyLogID, utils.RandStringRunes(18))
	req := &WorkPreQuery{}
	if err := c.ShouldBindJSON(req); err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"WorkPreFeedbackList 解析请求参数失败 error=%v", err)
		c.JSON(http.StatusOK, proto.NewCommRsp(100001, err.Error()))
		return
	}
	workPreFeedback := &model.WorkPreFeedback{
		UserId: req.UserId,
		Type:   req.Type,
		Status: req.Status,
	}
	count, consults, err := workPreFeedback.SearchList(gomysql.DB, logCtx, req.PageNo, req.PageSize, req.CreateAt)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"WorkPreFeedbackList SearchList error=%v", err)
		c.JSON(http.StatusOK, commProto.NewCommRsp(510001, err.Error()))
		return
	}
	c.JSON(http.StatusOK, commProto.NewSuccessRsp(gin.H{"count": count, "list": consults}))
}
func WorkPreFeedbackAdd(c *gin.Context) {
	logCtx := context.WithValue(context.TODO(), utils.CtxKeyLogID, utils.RandStringRunes(18))
	targetLanguage := c.GetHeader("Language")
	req := &WorkPreInsertDynamic{}
	if err := c.ShouldBindJSON(req); err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"WorkPreFeedbackAdd 解析请求参数失败 error=%v", err)
		c.JSON(http.StatusOK, proto.NewCommRsp(100001, respi18n.TransformResponseLocaleByDefault(logCtx, targetLanguage, "param exception")))
		return
	}
	workPreTag := &model.WorkPreTag{
		Type: req.Type,
	}
	status, err := workPreTag.GetAuthStatus(gomysql.DB, logCtx)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"WorkPreFeedbackAdd  GetAuthStatus error=%v", err)
		c.JSON(http.StatusOK, proto.NewCommRsp(510001, err.Error()))
		return
	}
	// 默认未登录
	accountID := ""
	// 获取登录信息
	aid, ok := c.Get("AccountId")
	if ok {
		accountID = aid.(string)
	}
	if status == 1 && accountID == "" {
		c.JSON(http.StatusOK, proto.NewCommRsp(100001, respi18n.TransformResponseLocaleByDefault(logCtx, targetLanguage, "permission insufficient")))
		return
	}

	for _, field := range req.Fields {
		if field.Required && isWorkPreEmptyValue(field.Value) {
			logger.Log.Errorf(utils.MMark(logCtx)+"WorkPreFeedbackAdd field.key: %v is required", field.Key)
			c.JSON(http.StatusOK, proto.NewCommRsp(100002, respi18n.TransformResponseLocaleByDefault(logCtx, targetLanguage, field.Key+" is required")))
			return
		}
	}
	fieldsJson, err := json.Marshal(req.Fields)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"WorkPreFeedbackAdd fieldsJson json.Marshal error=%v", err)
		c.JSON(http.StatusOK, proto.NewCommRsp(100002, err.Error()))
		return
	}
	workPreFeedback := &model.WorkPreFeedback{
		Type:         req.Type,
		UserId:       accountID,
		FeedbackList: string(fieldsJson),
		Status:       "UNTREATED",
	}
	id, err := workPreFeedback.Insert(gomysql.DB, logCtx)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"WorkPreFeedbackAdd  Insert error=%v", err)
		c.JSON(http.StatusOK, proto.NewCommRsp(510002, err.Error()))
		return
	}
	logger.Log.Infof(utils.MMark(logCtx)+"WorkPreFeedbackAdd Insert success, insertID=%v", id)
	var labels []WorkPreAddRepLabel
	labels = append(labels, WorkPreAddRepLabel{
		//Label: respi18n.TransformResponseLocaleBySubTag(logCtx, "work-preview-feedback", targetLanguage, "Consult success label1"),
	})
	c.JSON(http.StatusOK, proto.NewSuccessRsp(AddResponse{
		Title:  respi18n.TransformResponseLocaleBySubTag(logCtx, "work-preview-feedback", targetLanguage, "Thanks for your feedback!"),
		Labels: labels,
	}))
}
func WorkPreFeedbackUpdate(c *gin.Context) {
	logCtx := context.WithValue(context.TODO(), utils.CtxKeyLogID, utils.RandStringRunes(18))
	req := &WorkPreBatchUp{}
	if err := c.ShouldBindJSON(req); err != nil {
		logger.Log.Infof(utils.MMark(logCtx)+"WorkPreFeedbackBatchUpdate 解析请求参数失败 error=%v", err)
		c.JSON(http.StatusOK, proto.NewCommRsp(100001, err.Error()))
		return
	}
	// 检查ID列表是否为空
	if len(req.Ids) == 0 {
		logger.Log.Infof(utils.MMark(logCtx) + "WorkPreFeedbackBatchUpdate ID列表为空")
		c.JSON(http.StatusOK, commProto.NewCommRsp(100002, "ID列表不能为空"))
		return
	}
	ids, err := stringToint(req.Ids)
	if err != nil {
		logger.Log.Infof(utils.MMark(logCtx) + "WorkPreFeedbackBatchUpdate ID列表为空")
		c.JSON(http.StatusOK, commProto.NewCommRsp(100002, "ID参数异常"))
		return
	}
	workPreFeedback := &model.WorkPreFeedback{}
	idx, err := workPreFeedback.BatchUpdateWorkPreFeedbackByIDs(gomysql.DB, logCtx, ids, req.Status, req.Operator)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"WorkPreFeedbackBatchUpdate BatchUpdateWorkPreFeedbackByIDs error=%v", err)
		c.JSON(http.StatusOK, commProto.NewCommRsp(510003, err.Error()))
		return
	}
	logger.Log.Infof(utils.MMark(logCtx)+"WorkPreFeedbackBatchUpdate BatchUpdateWorkPreFeedbackByIDs success, count=%v", idx)
	c.JSON(http.StatusOK, commProto.NewSuccessRsp(nil))
}
func WorkPreFeedbackDelete(c *gin.Context) {
	logCtx := context.WithValue(context.TODO(), utils.CtxKeyLogID, utils.RandStringRunes(18))
	req := &WorkPreDel{}
	if err := c.ShouldBindJSON(req); err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"WorkPreFeedbackDelete 解析请求参数失败 error=%v", err)
		c.JSON(http.StatusOK, commProto.NewCommDataRsp(100001, err.Error(), nil))
		return
	}
	// 检查ID列表是否为空
	if len(req.Ids) == 0 {
		logger.Log.Errorf(utils.MMark(logCtx) + "WorkPreFeedbackDelete ID列表为空")
		c.JSON(http.StatusOK, commProto.NewCommRsp(100002, "ID列表不能为空"))
		return
	}
	ids, err := stringToint(req.Ids)
	if err != nil {
		logger.Log.Infof(utils.MMark(logCtx) + "WorkPreFeedbackBatchUpdate ID列表为空")
		c.JSON(http.StatusOK, commProto.NewCommRsp(100002, "ID参数异常"))
		return
	}
	workPreFeedback := &model.WorkPreFeedback{}
	updateCount, err := workPreFeedback.BatchUpdateByOperator(gomysql.DB, logCtx, ids, req.Operator)
	if err != nil {
		logger.CtxLog(c).Errorf("WorkPreFeedbackDelete BatchUpdateByOperator fail,  error=%v", err)
		c.JSON(http.StatusOK, commProto.NewCommRsp(510001, err.Error()))
		return
	}
	logger.CtxLog(c).Infof("WorkPreFeedbackDelete BatchUpdateByOperator success,  count=%v", updateCount)
	delCount, err := workPreFeedback.BatchDeleteByIDs(gomysql.DB, ids)
	if err != nil {
		logger.CtxLog(c).Errorf("WorkPreFeedbackDelete DeleteByID fail, error=%v", err)
		c.JSON(http.StatusOK, commProto.NewCommRsp(510002, err.Error()))
		return
	}
	logger.CtxLog(c).Infof("WorkPreFeedbackDelete BatchDeleteByIDs success,  count=%v", delCount)
	c.JSON(http.StatusOK, commProto.NewSuccessRsp(nil))
}

func isWorkPreEmptyValue(value interface{}) bool {
	if value == nil {
		return true
	}
	v := reflect.ValueOf(value)
	switch v.Kind() {
	case reflect.String:
		return v.Len() == 0 // 空字符串 ""
	case reflect.Slice, reflect.Array, reflect.Map:
		return v.Len() == 0 // 空切片 [] 或空 map {}
	case reflect.Int, reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64:
		return v.Int() == 0 // 0
	case reflect.Uint, reflect.Uint8, reflect.Uint16, reflect.Uint32, reflect.Uint64:
		return v.Uint() == 0 // 0
	case reflect.Float32, reflect.Float64:
		return v.Float() == 0 // 0.0
	case reflect.Bool:
		return !v.Bool() // false
	case reflect.Ptr, reflect.Interface:
		return v.IsNil() // nil 指针或 interface
	case reflect.Struct:
		return reflect.DeepEqual(value, reflect.Zero(v.Type()).Interface()) // 零值 struct
	default:
		return false // 其他类型默认不为空
	}
}
func stringToint(id string) ([]int64, error) {
	var ids []int64
	if strings.Contains(id, ",") {
		// 多 ID 情况
		idStrs := strings.Split(id, ",")
		for _, idStr := range idStrs {
			id, err := strconv.ParseInt(strings.TrimSpace(idStr), 10, 64)
			if err != nil {
				logger.Log.Errorf("WorkPreFeedbackDelete ids stringToint , error=%v", err)
				return nil, err
			}
			ids = append(ids, id)
		}
	} else {
		// 单 ID 情况
		id, err := strconv.ParseInt(strings.TrimSpace(id), 10, 64)
		if err != nil {
			logger.Log.Errorf("WorkPreFeedbackDelete ids stringToint , error=%v", err)
			return nil, err
		}
		ids = append(ids, id)
	}
	return ids, nil
}
