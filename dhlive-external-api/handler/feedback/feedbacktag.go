package feedback

import (
	commProto "acg-ai-go-common/beans/proto"
	"acg-ai-go-common/gomysql"
	"acg-ai-go-common/logger"
	"acg-ai-go-common/utils"
	"context"
	"dhlive-external-api/beans/model"
	"dhlive-external-api/beans/proto"
	"encoding/json"
	"github.com/gin-gonic/gin"
	"net/http"
	"strconv"
)

type ExternalItem struct {
	Text  string `json:"text"`
	Value string `json:"value"`
}
type FeedbackTagHandler struct {
	Id       int64          `json:"id"`
	Text     string         ` json:"text"`
	Value    string         ` json:"value"`
	Sort     int            ` json:"sort"`
	External []ExternalItem ` json:"external"`
}

/*
*
获取全部tag
*/
func GetFeedBackTypes(c *gin.Context) {
	logCtx := context.WithValue(context.TODO(), utils.CtxKeyLogID, utils.RandStringRunes(18))
	db := gomysql.DB
	feedbacktype := &model.FeedbackType{}
	types, err := feedbacktype.GetAllFeedbackTypes(db, logCtx, c.<PERSON>("text"))
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"GetFeedBackTypes feedbacktype.GetAllFeedbackTypes error=%v", err)
		c.JSON(http.StatusInternalServerError, proto.NewCommDataRsp(10001, "查询失败", nil))
		return
	}
	// 转换反馈类型为前端需要的格式
	feedbackTagHandler := make([]FeedbackTagHandler, 0, len(types))
	for _, t := range types {
		feedbackTag, err := convertToFeedbackTagHandler(logCtx, t)
		if err != nil {
			// 记录错误日志，但继续处理其他反馈类型
			logger.Log.Errorf(utils.MMark(logCtx)+"GetFeedBackTypes convertToFeedbackTagHandler error=%v", err)
			continue
		}
		feedbackTagHandler = append(feedbackTagHandler, feedbackTag)
	}
	// 返回成功响应
	c.JSON(http.StatusOK, proto.NewCommDataRsp(0, "success", feedbackTagHandler))

}

/*
*
新增tag
*/
func AddFeedbackType(c *gin.Context) {
	logCtx := context.WithValue(context.TODO(), utils.CtxKeyLogID, utils.RandStringRunes(18))
	req := &FeedbackTagHandler{}
	if err := c.ShouldBindJSON(req); err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"AddFeedbackType 解析请求参数失败 error=%v", err)
		c.JSON(http.StatusOK, commProto.NewCommDataRsp(100001, "参数解析异常", req))
		return
	}
	if len(req.Text) == 0 || len(req.Value) == 0 {
		logger.Log.Errorf(utils.MMark(logCtx) + "AddFeedbackType 参数错误")
		c.JSON(http.StatusOK, commProto.NewCommDataRsp(100001, "参数错误", nil))
		return
	}
	externalString := ""
	if len(req.External) > 0 {
		externalJson, err := json.Marshal(req.External)
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx) + "AddFeedbackType 参数错误")
			c.JSON(http.StatusOK, commProto.NewCommDataRsp(100001, err.Error(), nil))
			return
		}
		externalString = string(externalJson)
	}
	feedbacktype := &model.FeedbackType{
		Text:     req.Text,
		Value:    req.Value,
		External: externalString,
		Sort:     req.Sort,
	}
	id, err := feedbacktype.InsertType(gomysql.DB, logCtx)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"AddFeedbackType InsertType error=%v", err)
		c.JSON(http.StatusOK, commProto.NewCommDataRsp(10002, err.Error(), nil))
		return
	}
	logger.Log.Infof(utils.MMark(logCtx)+"AddFeedbackType InsertType success, insertID=%v", id)
	c.JSON(http.StatusOK, commProto.NewSuccessRsp(nil))
}

/*
*
更新tag
*/
func UpdateFeedbackType(c *gin.Context) {
	logCtx := context.WithValue(context.TODO(), utils.CtxKeyLogID, utils.RandStringRunes(18))
	req := &FeedbackTagHandler{}
	if err := c.ShouldBindJSON(req); err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"UpdateFeedbackType 解析请求参数失败 error=%v", err)
		c.JSON(http.StatusOK, commProto.NewCommDataRsp(100001, "参数解析异常", req))
		return
	}
	if len(req.Text) == 0 || len(req.Value) == 0 {
		logger.Log.Errorf(utils.MMark(logCtx) + "UpdateFeedbackType 参数错误")
		c.JSON(http.StatusOK, commProto.NewCommDataRsp(100001, "参数错误", nil))
		return
	}
	externalString := ""
	if len(req.External) > 0 {
		externalJson, err := json.Marshal(req.External)
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx) + "UpdateFeedbackType 参数错误")
			c.JSON(http.StatusOK, commProto.NewCommDataRsp(100001, err.Error(), nil))
			return
		}
		externalString = string(externalJson)
	}
	feedbacktype := &model.FeedbackType{
		Text:     req.Text,
		Value:    req.Value,
		Sort:     req.Sort,
		External: externalString,
	}
	rows, err := feedbacktype.UpdateItem(gomysql.DB, logCtx, req.Id)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+" UpdateFeedbackType UpdateItem error=%v", err)
		c.JSON(http.StatusOK, commProto.NewCommDataRsp(10002, err.Error(), nil))
		return
	}
	logger.Log.Infof(utils.MMark(logCtx)+"UpdateFeedbackType UpdateItem success, rows=%v", err, rows)
	c.JSON(http.StatusOK, commProto.NewSuccessRsp(nil))
}

/*
*
删除指定tag
*/
func DeleteFeedbackType(c *gin.Context) {
	logCtx := context.WithValue(context.TODO(), utils.CtxKeyLogID, utils.RandStringRunes(18))
	idStr := c.Query("id")
	if len(idStr) == 0 {
		logger.Log.Errorf(utils.MMark(logCtx) + "DeleteFeedbackType get id fail")
		c.JSON(http.StatusOK, commProto.NewCommDataRsp(100001, "参数异常", nil))
		return
	}
	id, err := strconv.Atoi(idStr)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"DeleteFeedbackType Atoi error=%v", err)
		c.JSON(http.StatusOK, commProto.NewCommDataRsp(100001, err.Error(), nil))
		return
	}

	feedbacktype := &model.FeedbackType{}
	err = feedbacktype.DeleteItem(gomysql.DB, logCtx, id)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"DeleteFeedbackType DeleteItem error=%v", err)
		c.JSON(http.StatusOK, commProto.NewCommDataRsp(100002, err.Error(), nil))
		return
	}
	logger.Log.Infof(utils.MMark(logCtx)+"DeleteFeedbackType delete success, id=%v", id)
	c.JSON(http.StatusOK, commProto.NewSuccessRsp(nil))
}

/*
*
数据格式转换
*/
func convertToFeedbackTagHandler(logCtx context.Context, t model.FeedbackType) (FeedbackTagHandler, error) {
	var external []ExternalItem
	// 如果字段值不为空进行反序列化
	if len(t.External) > 0 {
		err := json.Unmarshal([]byte(t.External), &external)
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+"convertToFeedbackTagHandler json.Unmarshal error=%v", err)
			return FeedbackTagHandler{}, err
		}
	} else {
		// 字段值为空反馈[] 数组
		external = make([]ExternalItem, 0)
	}
	logger.Log.Errorf(utils.MMark(logCtx) + "convertToFeedbackTagHandler scuuess")
	return FeedbackTagHandler{
		Id:       t.ID,
		Text:     t.Text,
		Value:    t.Value,
		Sort:     t.Sort,
		External: external,
	}, nil

}
