package figure

import (
	"acg-ai-go-common/gomysql"
	"acg-ai-go-common/logger"
	"acg-ai-go-common/utils"
	"acg-ai-go-common/utils/redisproxy"
	"context"
	"dhlive-external-api/beans/enum"
	"dhlive-external-api/beans/model"
	"dhlive-external-api/beans/proto"
	config "dhlive-external-api/conf"
	"dhlive-external-api/handler/handlerUtils"
	"dhlive-external-api/handler/handlerUtils/ffmpegutils"
	"dhlive-external-api/handler/handlerUtils/redislock"
	"dhlive-external-api/handler/handlerUtils/synccensorclient"
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"os"
	"path"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

const (
	RiskControlShortVideoQps    = "riskcontrolshortvideoqps"
	RiskControlShortVideoQpsFmt = "%s:" + RiskControlShortVideoQps + ":xiling-saas-v3"
	RiskControlShortVideoQpsMax = 20 // 最大并发量 50

	RiskControlShortVideoQueue            = "riskcontrolshortvideoqueue"
	RiskControlShortVideoQueueFmt         = "%s:" + RiskControlShortVideoQueue + ":xiling-saas-v3"
	RiskControlShortVideoQueueMaxInterval = 30 // 单位s
	RiskControlShortVideoQueueMax         = 30 // 最大并发量

	RiskControlShortVideoLockFmt          = "%s:" + "riskControlshortvideo" + "%s"
	RiskControlShortVideoLockTimeDuration = 10 * time.Minute

	RiskControlShortVideoTimeSizeMax = 290              // 290秒
	RiskControlShortVideoFileSizeMax = 49 * 1024 * 1024 // 50M
)

var (
	// 实例化一个VideoWatermark
	RiskControlShortVideoInstance *RiskControlShortVideo
	// once 用于确保实例化操作只执行一次
	onceRiskControlShortVideo sync.Once
)

type RiskControlShortVideo struct {
	client *synccensorclient.ContentCensorClient
}

func GetRiskControlShortVideo() *RiskControlShortVideo {
	onceRiskControlShortVideo.Do(func() {
		RiskControlShortVideoInstance = newRiskControlShortVideo()
	})
	return RiskControlShortVideoInstance
}

func newRiskControlShortVideo() *RiskControlShortVideo {
	return &RiskControlShortVideo{
		client: synccensorclient.NewClient(config.LocalConfig.RiskControl.AK, config.LocalConfig.RiskControl.SK),
	}
}

func (r *RiskControlShortVideo) CensorVideo(c *gin.Context) {
	logCtx := context.WithValue(context.TODO(), utils.CtxKeyLogID, utils.RandStringRunes(16))
	accountID := "ceshishortvideo"
	aid, ok := c.Get("AccountId")
	if ok {
		accountID = aid.(string)
	}
	// 解析请求参数
	resSlice := proto.RiskControlShortVideoResponse{
		LogID: utils.GetLogID(logCtx),
		Data:  proto.RiskControlShortVideoDataListItem{},
	}

	req := proto.RiskControlShortVideoSubmitRequest{}
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"CensorVideo ShouldBindQuery fail, err:%v req:%#v", err, req)
		c.JSON(http.StatusOK, proto.NewCommDataRsp(ParamErrorErrCode, ParamErrorErrMsg, resSlice))
		return
	}

	// 参数校验
	if len(req.VideoURL) <= 0 {
		logger.Log.Errorf(utils.MMark(logCtx)+"CensorVideo VideoURL is empty, req:%#v", req)
		c.JSON(http.StatusOK, proto.NewCommDataRsp(ParamEmptyErrCode, ParamEmptyErrMsg, resSlice))
		return
	}

	if !VerifyBosUrl(req.VideoURL) {
		logger.Log.Errorf(utils.MMark(logCtx)+"CensorVideo VideoURL not bos url, req:%#v", req)
		c.JSON(http.StatusOK, proto.NewCommDataRsp(ParamEmptyErrCode, "视频链接不合法", resSlice))
		return
	}

	// url 转换
	if internalAddress, err := BosUrlReplaceInternalAddress(logCtx, req.VideoURL); err != nil {
		// 如果失败了，就直接用原来的url去审核，这里打印日志即可，不影响业务逻辑执行
		logger.Log.Errorf(utils.MMark(logCtx)+"CensorVideo BosUrlReplaceInternalAddress fail, err:%v", err)
	} else {
		// 替换成功，则使用内部地址去审核
		req.VideoURL = internalAddress
	}

	var err error
	resSlice.Data, err = r.startCensorVideoTask(logCtx, accountID, req.VideoURL)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+utils.ServiceInternalEsErrMsg+"CensorVideo censorVideo fail, err:%v req:%#v", err, req)
		c.JSON(http.StatusOK, proto.NewCommDataRsp(100003, "视频审核失败", resSlice))
		return
	}

	if resSlice.Data.ErrorCode != 0 {
		logger.Log.Errorf(utils.MMark(logCtx)+"CensorVideo censor fail, errCode:%d errMsg:%s req:%#v", resSlice.Data.ErrorCode, resSlice.Data.ErrorMsg, req)
		c.JSON(http.StatusOK, proto.NewCommDataRsp(100004, "视频审核失败", resSlice))
		return
	}

	c.JSON(http.StatusOK, proto.NewSuccessRsp(resSlice))
}

func (r *RiskControlShortVideo) startCensorVideoTask(logCtx context.Context, accountID string, videoURL string) (proto.RiskControlShortVideoDataListItem, error) {
	resItem := proto.RiskControlShortVideoDataListItem{}

	// 使用分布式锁锁住任务防止其他任务重复提交到数据库和任务处理
	// 获取锁
	urlmd5 := handlerUtils.CalculateMD5FromText(videoURL)
	redisProxy := redisproxy.GetRedisProxy()
	redisLockKey := fmt.Sprintf(RiskControlShortVideoLockFmt, handlerUtils.GetNameByRunEnv(), urlmd5+"_lock")
	redisLock := redislock.NewRedisLock(redisProxy.Rdb, redisLockKey, RiskControlShortVideoLockTimeDuration)

	ok, err := redisLock.Lock(context.Background())
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+utils.RedisEsErrMsg+"censorVideo Lock key: %s error: %+v\n", redisLockKey, err)
		return resItem, err
	} else if !ok {
		err = acquireCensorLockWithBlock(logCtx, redisLock)
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+" acquireCensorLockWithBlock error: %+v\n", err)
			return resItem, err
		}
	}

	defer redisLock.Unlock(context.Background())

	// 查询数据库
	if rcitem, err := searchRiskControlItemFromUrlMd5(logCtx, urlmd5); err == nil && rcitem.ID > 0 {
		logger.Log.Info(utils.MMark(logCtx) + "censorVideo searchRiskControlItemFromUrlMd5 found success")
		return r.getRiskControlListItem(logCtx, rcitem)
	}

	// 申请队列
	queueKey := fmt.Sprintf(RiskControlShortVideoQueueFmt, handlerUtils.GetNameByRunEnv())
	ok, err = checkQueueAllowance(queueKey, RiskControlShortVideoQueueMax, RiskControlShortVideoQueueMaxInterval)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"censorVideo checkQueueAllowance is err:%v", err)
		return resItem, err
	}

	if !ok {
		return resItem, errors.New("queue is full")
	}

	defer decrementCounter(queueKey)
	// 提交任务
	starttime := time.Now()
	fileuuid := uuid.New().String()
	format, err := handlerUtils.GetFileExtensionFromURL(videoURL)
	if err != nil {
		return resItem, err
	}

	// 下载文件并计算md5
	downpath := DownloadFilePrefix + fileuuid + format
	md5, err := handlerUtils.CalculateMD5FromUrl(videoURL, downpath)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"censorVideo CalculateMD5FromUrl fail,err:%v url:%s", err, videoURL)
		return resItem, err
	}
	defer os.Remove(downpath)

	// 即使不同的url，md5也有可能相同
	// 锁一下文件，防止重复提交
	redisLockKey = fmt.Sprintf(RiskControlShortVideoLockFmt, handlerUtils.GetNameByRunEnv(), md5+"_lock")
	redisLockMd5 := redislock.NewRedisLock(redisProxy.Rdb, redisLockKey, RiskControlShortVideoLockTimeDuration)

	ok, err = redisLockMd5.Lock(context.Background())
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+utils.RedisEsErrMsg+"censorVideo redisLockMd5 Lock key: %s error: %+v\n", redisLockKey, err)
		return resItem, err
	} else if !ok {
		err = acquireCensorLockWithBlock(logCtx, redisLockMd5)
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+" acquireCensorLockWithBlock error: %+v\n", err)
			return resItem, err
		}
	}

	defer redisLockMd5.Unlock(context.Background())

	// 在查询一次文件的md5值，防止重复提交
	rcitem, err := searchRiskControlItemFromMd5(logCtx, md5)
	if err != nil {
		return resItem, err
	}

	if rcitem.ID > 0 {
		logger.Log.Info(utils.MMark(logCtx) + "censorVideo searchRiskControlItemFromMd5 found success")
		if rcitem.UrlMd5 != urlmd5 {
			rcitem.UrlMd5 = urlmd5
			rcitem.UpdatedAt = time.Now()
			if err = rcitem.UpdateRiskControlItem(gomysql.DB); err != nil {
				logger.Log.Errorf(utils.MMark(logCtx)+"searchRiskControlItemFromMd5 fail, err:%v", err)
			}
		}
		return r.getRiskControlListItem(logCtx, rcitem)
	}

	return r.censorVideo(logCtx, accountID, videoURL, downpath, md5, urlmd5, starttime)
}

func (r *RiskControlShortVideo) censorVideo(logCtx context.Context, accountID string, videoURL, downpath, md5, urlmd5 string, starttime time.Time) (proto.RiskControlShortVideoDataListItem, error) {
	resItem := proto.RiskControlShortVideoDataListItem{}
	taskIdPrefix := "rcv-"
	// 获取文件大小
	fsize, err := handlerUtils.GetFileSize(downpath)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"censorVideo GetFileSize err: %v \n", err)
		return resItem, err
	}

	compressionUrl := ""
	outpath := downpath
	// 查看是否需要压缩
	if fsize > RiskControlShortVideoFileSizeMax {
		// 压缩文件
		fileuuid := uuid.New().String()
		outpath = DownloadFilePrefix + fileuuid + ".mp4"
		err = ffmpegutils.CompressVideo(downpath, outpath, "28")
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+"censorVideo  CompressVideo err: %v \n", err)
			return resItem, err
		}
		defer os.Remove(outpath)
		// 上传bos
		compressionUrl, err = RetryUploadBosServiceFromFile(logCtx, FigureRiskControlVideoPath, outpath, ".mp4")
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+"censorVideo UploadBosServiceFromFile err: %v \n", err)
			return resItem, err
		}

	}

	// 判断视频时长大于5分钟需要采用异步方式
	vdurtion, err := ffmpegutils.GetVideoDuration(outpath)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"censorVideo GetVideoDuration err: %v \n", err)
		return resItem, err
	}

	// 获取文件大小
	fsize, err = handlerUtils.GetFileSize(outpath)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"censorVideo GetFileSize err: %v \n", err)
		return resItem, err
	}

	if vdurtion > RiskControlShortVideoTimeSizeMax || fsize > RiskControlShortVideoFileSizeMax { // 异步审核
		taskid := taskIdPrefix + utils.RandStringRunes(16)
		rcitem := &model.RiskControlItem{
			AccountID:      accountID,
			LogId:          utils.GetLogID(logCtx),
			TaskId:         taskid,
			FileUrl:        videoURL,
			CompressionUrl: compressionUrl,
			Type:           model.RiskControlTypeLongVideo,
			Md5:            md5,
			UrlMd5:         urlmd5,
			Status:         enum.TaskReady,
			CreatedAt:      starttime,
			UpdatedAt:      time.Now(),
		}

		return r.ReTryAsyncVideoCensor(logCtx, rcitem)
	}
	// 开启子协程提取音频并审核等待审核结果返回。
	audioCh := make(chan proto.RiskControlShortAudioResponse)

	go extractAudioAndCensor(logCtx, accountID, downpath, audioCh)

	// 申请qps限制
	key := fmt.Sprintf(RiskControlShortVideoQpsFmt, handlerUtils.GetNameByRunEnv())
	for {
		ok, err := checkQpsAllowance(key, RiskControlShortVideoQpsMax)
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+"censorVideo checkQpsAllowance is err:%v", err)
			return resItem, err
		}

		if !ok {
			time.Sleep(10 * time.Millisecond)
			continue
		}

		break
	}

	censorurl := videoURL
	if len(compressionUrl) != 0 {
		censorurl = compressionUrl
	}

	// 审核视频
	filename := strings.Split(videoURL, "/")[len(strings.Split(videoURL, "/"))-1]
	censorRes, res, retryNumber := r.ReTryVideoCensor(logCtx, filename, censorurl, accountID, outpath)

	// audiores 序列化成json字符串
	audiores := <-audioCh
	audiojson, _ := json.Marshal(audiores)
	// 统计耗时
	endtime := time.Now()
	duration := endtime.Sub(starttime).Milliseconds()/1000 + 1

	// 获取任务状态
	taststatus := enum.TaskSucceed
	if censorRes.ErrorCode != 0 || censorRes.ConclusionType == 4 {
		taststatus = enum.TaskFailed
	}

	taskid := taskIdPrefix + utils.RandStringRunes(16)
	// 保存数据库
	rcitem := &model.RiskControlItem{
		AccountID:         accountID,
		LogId:             utils.GetLogID(logCtx),
		TaskId:            taskid,
		Text:              "",
		FileUrl:           videoURL,
		CompressionUrl:    compressionUrl,
		AudioType:         "",
		AudioRate:         "",
		Type:              model.RiskControlTypeShortVideo,
		Md5:               md5,
		UrlMd5:            urlmd5,
		ConclusionType:    censorRes.ConclusionType,
		CensorResult:      res,
		SubmitResult:      "",
		AudioCensorResult: string(audiojson),
		Status:            taststatus,
		Duration:          duration,
		RetryNumber:       int64(retryNumber),
		CreatedAt:         starttime,
		UpdatedAt:         endtime,
	}

	err = rcitem.UpdateRiskControlItem(gomysql.DB)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"censorVideo UpdateRiskControlItem fail, err:%v", err)
		return resItem, err
	}

	resItem = proto.RiskControlShortVideoDataListItem{
		TaskId:                   taskid,
		Status:                   string(taststatus),
		ErrorCode:                censorRes.ErrorCode,
		ErrorMsg:                 censorRes.ErrorMsg,
		Conclusion:               censorRes.Conclusion,
		ConclusionType:           int(censorRes.ConclusionType),
		ConclusionTypeGroupInfos: censorRes.ConclusionTypeGroupInfos,
	}

	if censorRes.ConclusionType == 1 || censorRes.ConclusionType == 3 {
		if audiores.Data.ErrorCode != 0 {
			resItem.ErrorCode = audiores.Data.ErrorCode
			resItem.ErrorMsg = audiores.Data.ErrorMsg
		} else if audiores.Data.ConclusionType == 2 || audiores.Data.ConclusionType == 4 {
			resItem.Conclusion = audiores.Data.Conclusion
			resItem.ConclusionType = audiores.Data.ConclusionType
			for _, v := range audiores.Data.ConclusionTypeGroupInfos {
				info := proto.ConclusionTypeGroupInfo{
					Msg: v.Msg + ";" + v.Text,
				}
				resItem.ConclusionTypeGroupInfos = append(resItem.ConclusionTypeGroupInfos, info)
			}
		}
	}

	return resItem, nil
}

func (r *RiskControlShortVideo) getRiskControlListItem(logCtx context.Context, rcitem *model.RiskControlItem) (proto.RiskControlShortVideoDataListItem, error) {
	resItem := proto.RiskControlShortVideoDataListItem{}
	if rcitem.Status != enum.TaskSucceed && rcitem.Status != enum.TaskFailed {
		for {
			newitem, err := rcitem.GetRiskControlItem(gomysql.DB, rcitem.ID)
			if err != nil {
				return resItem, err
			} else if newitem.Status == enum.TaskSucceed || newitem.Status == enum.TaskFailed {
				rcitem = newitem
				break
			}
			time.Sleep(5 * time.Second)
		}
	}
	// 短视频结果
	if rcitem.Type == model.RiskControlTypeShortVideo {
		censorRes := proto.RiskControlShortVideoCensorResponse{}
		err := json.Unmarshal([]byte(rcitem.CensorResult), &censorRes)
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+"censorVideo Unmarshal fail taskid:%s, res:%s err:%v", rcitem.TaskId, rcitem.CensorResult, err)
			return resItem, err
		}

		resItem = proto.RiskControlShortVideoDataListItem{
			TaskId:                   rcitem.TaskId,
			Status:                   string(rcitem.Status),
			ErrorCode:                censorRes.ErrorCode,
			ErrorMsg:                 censorRes.ErrorMsg,
			Conclusion:               censorRes.Conclusion,
			ConclusionType:           int(censorRes.ConclusionType),
			ConclusionTypeGroupInfos: censorRes.ConclusionTypeGroupInfos,
		}

		// 解析音频结果
		if len(rcitem.AudioCensorResult) != 0 {
			audioCensorRes := proto.RiskControlShortAudioResponse{}
			err = json.Unmarshal([]byte(rcitem.AudioCensorResult), &audioCensorRes)
			if err != nil {
				logger.Log.Errorf(utils.MMark(logCtx)+"censorVideo Unmarshal fail taskid:%s, res:%s err:%v", rcitem.TaskId, rcitem.AudioCensorResult, err)
				return resItem, err
			}
			if censorRes.ConclusionType == 1 || censorRes.ConclusionType == 3 {
				if audioCensorRes.Data.ErrorCode != 0 {
					resItem.ErrorCode = audioCensorRes.Data.ErrorCode
					resItem.ErrorMsg = audioCensorRes.Data.ErrorMsg
				} else if audioCensorRes.Data.ConclusionType == 2 || audioCensorRes.Data.ConclusionType == 4 {
					resItem.Conclusion = audioCensorRes.Data.Conclusion
					resItem.ConclusionType = audioCensorRes.Data.ConclusionType
					for _, v := range audioCensorRes.Data.ConclusionTypeGroupInfos {
						info := proto.ConclusionTypeGroupInfo{
							Msg: v.Msg + ";" + v.Text,
						}
						resItem.ConclusionTypeGroupInfos = append(resItem.ConclusionTypeGroupInfos, info)
					}
				}
			}
		}

		return resItem, nil
	}

	// 长视频结果
	pullres := proto.RiskControlLongVideoCensorPullResponse{}
	err := json.Unmarshal([]byte(rcitem.CensorResult), &pullres)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"censorVideo Unmarshal fail, err:%v", err)
		return resItem, err
	}

	resItem = proto.RiskControlShortVideoDataListItem{
		TaskId:                   rcitem.TaskId,
		Status:                   string(rcitem.Status),
		ErrorCode:                pullres.ErrorCode,
		ErrorMsg:                 pullres.ErrorMsg,
		Conclusion:               pullres.Data.Conclusion,
		ConclusionType:           int(pullres.Data.ConclusionType),
		ConclusionTypeGroupInfos: pullres.Data.ConclusionTypeGroupInfos,
	}

	return resItem, err
}

func (r *RiskControlShortVideo) ReTryVideoCensor(logCtx context.Context, filename, censorurl, accountID, outpath string) (proto.RiskControlShortVideoCensorResponse, string, int) {
	res := ""
	retryNumber := 0
	censorRes := proto.RiskControlShortVideoCensorResponse{}
	options := make(map[string]interface{})
	options["strategyId"] = strconv.Itoa(config.LocalConfig.RiskControl.StrategyId)
	// url 文件名部分需要进行 encodeurl 编码
	encodeurl, err := handlerUtils.EncodeUrl(censorurl)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"EncodeUrl fail, url:%s", censorurl)
		encodeurl = censorurl
	}

	for i := 0; i < 3; i++ {
		censorRes = proto.RiskControlShortVideoCensorResponse{}
		retryNumber += 1
		res = r.client.VideoCensor(filename, encodeurl, accountID, options)

		err := json.Unmarshal([]byte(res), &censorRes)
		if err != nil {
			censorRes.ErrorCode = 200001
			censorRes.ErrorMsg = "res: " + res + " err:" + err.Error()
			logger.Log.Errorf(utils.MMark(logCtx)+"censorVideo Unmarshal fail,res:%s err:%v", res, err)
			time.Sleep(1 * time.Second)
			continue
		}
		if censorRes.ErrorCode == 18 { // qps 超限
			censorRes.ErrorCode = 200002
			censorRes.ErrorMsg = "qps limit"
			logger.Log.Errorf(utils.MMark(logCtx)+"censorVideo ImgCensor qps limit res:%v", res)
			time.Sleep(1 * time.Second)
			continue
		} else if censorRes.ErrorCode == 282877 { //视频格式不支持
			// 将视频进行转码
			tmpCensorUrl, err := transcodeVideo(logCtx, outpath)
			if err != nil {
				censorRes.ErrorCode = 200003
				censorRes.ErrorMsg = "短视频转码失败"
				logger.Log.Errorf(utils.MMark(logCtx)+"transcodeVideo fail, err:%v", err)
				break
			}
			logger.Log.Infof(utils.MMark(logCtx)+"censorVideo VideoCensor fail, need transcode tmpCensorUrl: %s", tmpCensorUrl)
			// url 文件名部分需要进行 encodeurl 编码
			tmpEncodeUrl, err := handlerUtils.EncodeUrl(tmpCensorUrl)
			if err != nil {
				censorRes.ErrorCode = 200004
				censorRes.ErrorMsg = "短视频地址转码失败"
				logger.Log.Errorf(utils.MMark(logCtx)+"EncodeUrl fail, url:%s", tmpEncodeUrl)
			} else {
				encodeurl = tmpEncodeUrl
				logger.Log.Infof(utils.MMark(logCtx) + " transcode success")
			}
			continue
		} else if censorRes.ErrorCode != 0 {
			censorRes.ErrorCode = 200005
			time.Sleep(1 * time.Second)
			continue
		} else if censorRes.ConclusionType == 4 {
			censorRes.ErrorCode = 200006
			logger.Log.Errorf(utils.MMark(logCtx)+"censorVideo VideoCensor fail, res:%s", res)
			time.Sleep(1 * time.Second)
			continue
		}

		break
	}
	return censorRes, res, retryNumber
}

func (r *RiskControlShortVideo) ReTryAsyncVideoCensor(logCtx context.Context, rcitem *model.RiskControlItem) (proto.RiskControlShortVideoDataListItem, error) {
	resItem := proto.RiskControlShortVideoDataListItem{}
	if err := rcitem.UpdateRiskControlItem(gomysql.DB); err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"censorVideo UpdateRiskControlItem fail, err:%v", err)
		return resItem, err
	}

	// 查询异步结果
	rci := model.RiskControlItem{}
	for {
		newrci, err := rci.GetRiskControlItemFromTaskId(gomysql.DB, rcitem.TaskId)
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+"censorVideo GetRiskControlItemFromTaskId fail,err:%v", err)
			return resItem, err
		}

		if newrci.Status == enum.TaskFailed {
			resItem = proto.RiskControlShortVideoDataListItem{
				Status:    string(rcitem.Status),
				ErrorCode: 200001,
				ErrorMsg:  "异步审核失败",
			}
			break
		}

		if len(newrci.CensorResult) > 0 {
			// 长视频结果
			pullres := proto.RiskControlLongVideoCensorPullResponse{}
			err = json.Unmarshal([]byte(newrci.CensorResult), &pullres)
			if err != nil {
				logger.Log.Errorf(utils.MMark(logCtx)+"censorVideo Unmarshal fail, err:%v", err)
				return resItem, err
			}

			resItem = proto.RiskControlShortVideoDataListItem{
				TaskId:                   newrci.TaskId,
				Status:                   string(newrci.Status),
				ErrorCode:                pullres.ErrorCode,
				ErrorMsg:                 pullres.ErrorMsg,
				Conclusion:               pullres.Data.Conclusion,
				ConclusionType:           int(pullres.Data.ConclusionType),
				ConclusionTypeGroupInfos: pullres.Data.ConclusionTypeGroupInfos,
			}
			break
		}

		time.Sleep(5 * time.Second)
	}
	return resItem, nil
}

func extractAudioAndCensor(logCtx context.Context, accountID string, videoPath string, input chan<- proto.RiskControlShortAudioResponse) {
	res := proto.RiskControlShortAudioResponse{
		LogID: utils.GetLogID(logCtx),
	}

	defer func() {
		input <- res
	}()
	ok, err := ffmpegutils.IsVideoContainsAudio(videoPath)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"IsVideoContainsAudio fail,err:%v", err)
		res.Data.ErrorCode = 200001
		res.Data.ErrorMsg = "视频是否包含音频获取失败"
		return
	}

	if !ok {
		res.Data.ErrorMsg = "视频不包含音频文件"
		return
	}

	audiopath, err := ffmpegutils.ExtractAudio(videoPath)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"extractAudio fail,err:%v", err)
		res.Data.ErrorCode = 200002
		res.Data.ErrorMsg = "视频提取音频失败"
		return
	}

	if ok, err := handlerUtils.FileExists(audiopath); err != nil || !ok {
		logger.Log.Errorf(utils.MMark(logCtx)+"extractAudio fail,err:%v", err)
		res.Data.ErrorCode = 200003
		res.Data.ErrorMsg = "视频提取音频失败"
		return
	}
	defer os.Remove(audiopath)

	audiourl, err := RetryUploadBosServiceFromFile(logCtx, FigureRiskControlAudioPath, audiopath, path.Ext(audiopath))
	if err != nil {
		logger.Log.Errorf("RetryUploadBosServiceFromFile fail,err:%v", err)
		res.Data.ErrorCode = 200004
		res.Data.ErrorMsg = "提取的音频文件上传bos失败"
		return
	}

	rcsaClient := GetRiskControlShortAudio()
	res, err = rcsaClient.censorAudio(logCtx, accountID, audiourl)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"censorAudio fail,audiourl:%s err:%v", audiourl, err)
		res.Data.ErrorCode = 200005
		res.Data.ErrorMsg = "提取的音频文件审核失败"
		return
	}

	logger.Log.Infof(utils.MMark(logCtx)+"censorAudio success,audiourl:%s res:%v", audiourl, res)
}

func transcodeVideo(logCtx context.Context, downpath string) (string, error) {
	newFileName := DownloadFilePrefix + uuid.NewString() + ".mp4"
	if err := ffmpegutils.TranscodeVideo(downpath, newFileName); err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"transcodeVideo fail,err:%v", err)
		return "", err
	}

	if ok, err := handlerUtils.FileExists(newFileName); err != nil || !ok {
		logger.Log.Errorf(utils.MMark(logCtx)+"transcodeVideo fail,err:%v", err)
		return "", err
	}

	// 删除文件
	defer os.Remove(newFileName)
	// 上传bos
	compressionUrl, err := RetryUploadBosServiceFromFile(logCtx, FigureRiskControlVideoPath, newFileName, ".mp4")
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+" UploadBosServiceFromFile err: %v \n", err)
		return "", err
	}

	return compressionUrl, nil
}
