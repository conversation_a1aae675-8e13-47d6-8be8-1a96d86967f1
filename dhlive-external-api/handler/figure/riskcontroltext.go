package figure

import (
	"acg-ai-go-common/gomysql"
	"acg-ai-go-common/logger"
	"acg-ai-go-common/utils"
	"acg-ai-go-common/utils/redisproxy"
	"context"
	"dhlive-external-api/beans/enum"
	"dhlive-external-api/beans/model"
	"dhlive-external-api/beans/proto"
	config "dhlive-external-api/conf"
	"dhlive-external-api/handler/handlerUtils"
	"dhlive-external-api/handler/handlerUtils/redislock"
	"encoding/json"
	"fmt"
	"net/http"
	"regexp"
	"strconv"
	"time"

	"github.com/Baidu-AIP/golang-sdk/baseClient"
	"github.com/gin-gonic/gin"
)

const (
	RiskControlTextLockFmt          = "%s:" + "riskControlText" + "%s"
	RiskControlTextLockTimeDuration = 2 * time.Minute
)

type RiskControlText struct {
	auth baseClient.Auth
}

func NewRiskControlText() *RiskControlText {
	auth := baseClient.Auth{}
	auth.InitAuth(config.LocalConfig.RiskControl.AK, config.LocalConfig.RiskControl.SK)
	return &RiskControlText{
		auth: auth,
	}
}

func (f *RiskControlText) HandleRequest(c *gin.Context) {
	logCtx := context.WithValue(context.TODO(), utils.CtxKeyLogID, utils.RandStringRunes(16))
	accountID := "text"
	aid, ok := c.Get("AccountId")
	if ok {
		accountID = aid.(string)
	}

	res := proto.RiskControlTextResponse{
		LogID:    utils.GetLogID(logCtx),
		DataList: []proto.RiskControlTextItem{},
	}

	// 解析请求参数
	req := proto.RiskControlTextRequest{}
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"HandleRequest ShouldBindQuery fail, err:%v req:%#v", err, req)
		c.JSON(http.StatusOK, proto.NewCommDataRsp(ParamErrorErrCode, ParamErrorErrMsg, res))
		return
	}

	for _, text := range req.Texts {
		// 添加到返回数据
		item := proto.RiskControlTextItem{}

		if len(text) == 0 {
			item.Conclusion = "合规"
			item.ConclusionType = 1
			res.DataList = append(res.DataList, item)

			logger.Log.Warnf(utils.MMark(logCtx) + "HandleRequest censorText text is null")
			continue
		}

		if len(text) >= 20000 {
			item.ErrorCode = 100004
			item.ErrorMsg = "审核文本个数超过限制"
			res.DataList = append(res.DataList, item)
			logger.Log.Errorf(utils.MMark(logCtx) + "HandleRequest censorText text limit")
			continue
		}

		reg := regexp.MustCompile(`\s+`)
		temptext := reg.ReplaceAllString(text, "")
		if len(temptext) <= 0 {
			item.Conclusion = "合规"
			item.ConclusionType = 1
			res.DataList = append(res.DataList, item)

			logger.Log.Warnf(utils.MMark(logCtx) + "HandleRequest censorText text is null")
			continue
		}

		response, taskid, err := f.censorText(logCtx, text, accountID)
		if err != nil {
			item.ErrorCode = 10004
			item.ErrorMsg = err.Error()
			res.DataList = append(res.DataList, item)
			logger.Log.Errorf(utils.MMark(logCtx)+utils.ServiceInternalEsErrMsg+"HandleRequest censorText is err:%v", err)
			continue
		}

		item = proto.RiskControlTextItem{
			Taskid:         taskid,
			ErrorCode:      response.ErrorCode,
			ErrorMsg:       response.ErrorMsg,
			Conclusion:     response.Conclusion,
			ConclusionType: response.ConclusionType,
			Data:           []proto.RiskControlTextItemDetail{},
		}
		if response.ConclusionType == 2 {
			for _, data := range response.Data {
				// 命中的关键词map 用于去重
				hitWordMap := make(map[string]struct{})
				itemdata := proto.RiskControlTextItemDetail{
					ErrorCode: data.ErrorCode,
					ErrorMsg:  data.ErrorMsg,
					Msg:       data.Msg,
					Words:     []string{},
				}
				// 跳过合规项
				if data.ConclusionType == 1 {
					continue
				}

				for _, itemHits := range data.Hits {
					for _, word := range itemHits.Words {
						words, ok := IsHitWordWithSave(word, hitWordMap)
						if !ok {
							continue
						}

						itemdata.Words = append(itemdata.Words, words...)
					}

					//如果大模型没有命中关键词 再根据模型命中位置去匹配关键词
					if len(itemHits.Words) > 0 {
						continue
					}

					for _, hitpos := range itemHits.ModelHitPositions {
						if len(hitpos) > 2 {
							word := handlerUtils.SubstringByRuneCount(text, int(hitpos[0]), int(hitpos[1]))
							words, ok := IsHitWordWithSave(word, hitWordMap)
							if !ok {
								continue
							}
							itemdata.Words = append(itemdata.Words, words...)
						}
					}

					for _, wordhitpos := range itemHits.WordHitPositions {
						words, ok := IsHitWordWithSave(wordhitpos.Keyword, hitWordMap)
						if !ok {
							continue
						}
						itemdata.Words = append(itemdata.Words, words...)
						for _, pos := range wordhitpos.Positions {
							if len(pos) == 2 {
								word := handlerUtils.SubstringByRuneCount(text, int(pos[0]), int(pos[1]))
								words, ok := IsHitWordWithSave(word, hitWordMap)
								if !ok {
									continue
								}
								itemdata.Words = append(itemdata.Words, words...)
							}
						}
					}

				}

				item.Data = append(item.Data, itemdata)
			}
		}

		res.DataList = append(res.DataList, item)
	}

	if len(res.DataList) == 1 {
		if res.DataList[0].ErrorCode != 0 {
			c.JSON(http.StatusOK, proto.NewCommDataRsp(int(res.DataList[0].ErrorCode), res.DataList[0].ErrorMsg, res))
			return
		}
	}

	c.JSON(http.StatusOK, proto.NewSuccessRsp(res))
}

func (f *RiskControlText) censorText(logCtx context.Context, text, accountId string) (proto.TextCensorResponse, string, error) {
	var response proto.TextCensorResponse
	md5 := handlerUtils.CalculateMD5FromText(text)

	// 使用分布式锁锁住任务防止其他任务重复提交到数据库和任务处理
	// 获取锁
	redisProxy := redisproxy.GetRedisProxy()
	redisLockKey := fmt.Sprintf(RiskControlTextLockFmt, handlerUtils.GetNameByRunEnv(), md5+"_lock")
	redisLock := redislock.NewRedisLock(redisProxy.Rdb, redisLockKey, RiskControlTextLockTimeDuration)

	ok, err := redisLock.Lock(context.Background())
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+utils.RedisEsErrMsg+"censorText Lock key: %s error: %+v\n", redisLockKey, err)
		return response, "", err
	} else if !ok {
		err = acquireCensorLockWithBlock(logCtx, redisLock)
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+" acquireCensorLockWithBlock error: %+v\n", err)
			return response, "", err
		}
	}

	defer redisLock.Unlock(context.Background())
	// 查找数据库中是否解析过
	r := model.RiskControlItem{}
	riskitem, err := r.GetRiskControlItemFromMd5(gomysql.DB, md5)
	if err == nil {
		// 如果数据库存在则直接获取审核结果并返回
		if riskitem.ID != 0 {
			err = handlerUtils.JSONToStruct(riskitem.CensorResult, &response)
			if err != nil {
				logger.Log.Errorf(utils.MMark(logCtx)+"censorText JSONToStruct is err:%v", err)
				return response, "", err
			}

			logger.Log.Infof(utils.MMark(logCtx)+"censorText GetRiskControlItemFromMd5 is success db logId:%s", riskitem.LogId)
			return response, riskitem.TaskId, nil
		}
	}

	starttime := time.Now()
	retryNumber := int64(0)
	censorRes := ""

	for i := 0; i < 3; i++ {
		response = proto.TextCensorResponse{}
		retryNumber += 1
		// 数据库中不存在则调用接口进行审核
		censorRes = f.textCensor(text, config.LocalConfig.RiskControl.StrategyId)
		err = json.Unmarshal([]byte(censorRes), &response)
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+"censorText TextCensor is err:%v res:%v", err, censorRes)
			response.ErrorCode = 200001
			response.ErrorMsg = "res:" + censorRes + "err: " + err.Error()
			time.Sleep(1 * time.Second)
			continue
		}

		// 错误信息 IAM Certification failed 这个错误码重复验证一下可能就成功了
		if response.ErrorCode == 18 { // qps 超限
			logger.Log.Errorf(utils.MMark(logCtx)+"censorText ImgCensor qps limit res:%v", censorRes)
			response.ErrorCode = 200002
			response.ErrorMsg = "qps limit"
			time.Sleep(1 * time.Second)
			continue
		} else if response.ErrorCode != 0 {
			logger.Log.Errorf(utils.MMark(logCtx)+"censorText TextCensor IAM Certification failed, res:%v", censorRes)
			response.ErrorCode = 200003
			time.Sleep(1 * time.Second)
			continue
		} else if response.ConclusionType == 4 { // 审核失败 重试三次
			logger.Log.Errorf(utils.MMark(logCtx)+"censorText TextCensor censor failed res:%v", censorRes)
			response.ErrorCode = 200004
			time.Sleep(1 * time.Second)
			continue
		}
		break
	}

	taskstatus := enum.TaskSucceed
	if response.ErrorCode != 0 {
		taskstatus = enum.TaskFailed
	}

	endtime := time.Now()
	duration := endtime.Sub(starttime).Milliseconds()/1000 + 1
	taskid := "rct-" + utils.RandStringRunes(16)

	r = model.RiskControlItem{
		AccountID:      accountId,
		LogId:          utils.GetLogID(logCtx),
		TaskId:         taskid,
		Text:           text,
		Type:           model.RiskControlTypeText,
		Md5:            md5,
		ConclusionType: response.ConclusionType,
		CensorResult:   censorRes,
		Status:         taskstatus,
		Duration:       duration,
		RetryNumber:    retryNumber,
		CreatedAt:      starttime,
		UpdatedAt:      endtime,
	}

	err = r.UpdateRiskControlItem(gomysql.DB)

	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"censorText UpdateRiskControlItem is err:%v text:%s", err, text)
		return response, "", err
	}

	return response, taskid, nil
}
func (f *RiskControlText) textCensor(text string, strategyId int) (result string) {
	data := make(map[string]string)
	data["text"] = text
	data["strategyId"] = strconv.Itoa(strategyId)

	return baseClient.PostUrlForm(__textCensorUserDefinedUrl, data, &f.auth)
}
