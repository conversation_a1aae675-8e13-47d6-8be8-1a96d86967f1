package figure

import (
	"acg-ai-go-common/gomysql"
	"acg-ai-go-common/logger"
	"acg-ai-go-common/utils"
	"acg-ai-go-common/utils/redisproxy"
	"context"
	"dhlive-external-api/beans/enum"
	"dhlive-external-api/beans/model"
	"dhlive-external-api/beans/proto"
	config "dhlive-external-api/conf"
	"dhlive-external-api/handler/gcpcensor"
	"dhlive-external-api/handler/handlerUtils"
	"dhlive-external-api/handler/handlerUtils/redislock"
	"dhlive-external-api/handler/i18n/respi18n"
	minicensor "dhlive-external-api/handler/minilanguagecensor"
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"regexp"
	"strconv"
	"time"
	"unicode/utf8"

	"github.com/Baidu-AIP/golang-sdk/baseClient"
	"github.com/gin-gonic/gin"
)

const (
	RiskControlTextLockFmt          = "%s:" + "riskControlText" + "%s"
	RiskControlTextLockTimeDuration = 10 * time.Second

	RiskControlMiniTextQpsFmt   = "%s:riskcontrolminitextqps:xiling-saas-v3"
	RiskControlMiniTextQueueFmt = "%s:riskcontrolminitextqueue:xiling-saas-v3"

	RiskControlRedisKeyMaxInterval = 30 // 单位s
)

type RiskControlText struct {
	auth baseClient.Auth
}

func NewRiskControlText() *RiskControlText {
	auth := baseClient.Auth{}
	auth.InitAuth(config.LocalConfig.RiskControl.AK, config.LocalConfig.RiskControl.SK)
	return &RiskControlText{
		auth: auth,
	}
}

func (f *RiskControlText) CensorTexts(c *gin.Context) {
	logCtx := context.WithValue(context.TODO(), utils.CtxKeyLogID, utils.RandStringRunes(16))
	accountID := "text"
	aid, ok := c.Get("AccountId")
	if ok {
		accountID = aid.(string)
	}

	// 获取目标语言
	targetLanguage := c.GetHeader("Language")
	logCtx = context.WithValue(logCtx, "Language", targetLanguage)
	logger.Log.Infof(utils.MMark(logCtx)+"CensorText langugae in header is: %s", targetLanguage)
	res := &proto.RiskControlTextResponse{
		LogID:    utils.GetLogID(logCtx),
		DataList: []proto.RiskControlTextItem{},
	}

	if !config.LocalConfig.RiskControl.IsSwitch {
		c.JSON(http.StatusOK, proto.NewSuccessRsp(res))
		return
	}

	// 解析请求参数
	req := proto.RiskControlTextRequest{}
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"HandleRequest ShouldBindQuery fail, err:%v req:%#v", err, req)
		c.JSON(http.StatusOK, proto.NewCommDataRsp(ParamErrorErrCode, respi18n.TransformResponseLocaleByDefault(
			logCtx, targetLanguage, ParamErrorErrMsg), res))
		return
	}

	for _, text := range req.Texts {
		// 添加到返回数据
		item := proto.RiskControlTextItem{}

		if len(text) == 0 {
			item.Conclusion = respi18n.TransformResponseLocaleByDefault(logCtx, targetLanguage, "合规")
			item.ConclusionType = 1
			res.DataList = append(res.DataList, item)

			logger.Log.Warnf(utils.MMark(logCtx) + " censorText text is null")
			continue
		}
		// TODO: 文本长度限制需要根据业务调整
		if utf8.RuneCountInString(text) > 6666 {
			item.ErrorCode = 100004
			item.Conclusion = respi18n.TransformResponseLocaleByDefault(logCtx, targetLanguage, "审核文本个数超过限制")
			res.DataList = append(res.DataList, item)
			logger.Log.Errorf(utils.MMark(logCtx) + " censorText text limit")
			continue
		}

		reg := regexp.MustCompile(`\s+`)
		temptext := reg.ReplaceAllString(text, "")
		if len(temptext) <= 0 {
			item.Conclusion = respi18n.TransformResponseLocaleByDefault(logCtx, targetLanguage, "合规")
			item.ConclusionType = 1
			res.DataList = append(res.DataList, item)

			logger.Log.Warnf(utils.MMark(logCtx) + " censorText text is null")
			continue
		}

		// 调用审核接口
		item, err := f.censorText(logCtx, text, accountID)
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+" censorText err:%v", err)
			c.JSON(http.StatusOK, proto.NewCommDataRsp(int(item.ErrorCode), respi18n.TransformResponseLocaleByDefault(
				logCtx, targetLanguage, "文本审核失败"), res))
			return
		}

		res.DataList = append(res.DataList, item)
	}

	// 错误处理
	if len(res.DataList) == 1 {
		if res.DataList[0].ErrorCode != 0 {
			c.JSON(http.StatusOK, proto.NewCommDataRsp(int(res.DataList[0].ErrorCode), respi18n.TransformResponseLocaleByDefault(logCtx, targetLanguage, res.DataList[0].ErrorMsg), res))
			return
		}
	}

	c.JSON(http.StatusOK, proto.NewSuccessRsp(res))
}

func (f *RiskControlText) censorText(logCtx context.Context, text, accountID string) (proto.RiskControlTextItem, error) {
	item := proto.RiskControlTextItem{}
	md5 := handlerUtils.CalculateMD5FromText(text)

	// 使用分布式锁锁住任务防止其他任务重复提交到数据库和任务处理
	// 获取锁
	redisProxy := redisproxy.GetRedisProxy()
	redisLockKey := fmt.Sprintf(RiskControlTextLockFmt, handlerUtils.GetNameByRunEnv(), md5+"_lock")
	redisLock := redislock.NewRedisLock(redisProxy.Rdb, redisLockKey, RiskControlTextLockTimeDuration)

	ok, err := redisLock.Lock(context.Background())
	if err != nil {
		item.ErrorCode = 100005
		item.ErrorMsg = "获取分布式锁失败"
		logger.Log.Errorf(utils.MMark(logCtx)+utils.RedisEsErrMsg+"censorText Lock key: %s error: %+v\n", redisLockKey, err)
		return item, err
	} else if !ok {
		err = acquireCensorLockWithBlock(logCtx, redisLock)
		if err != nil {
			item.ErrorCode = 100006
			item.ErrorMsg = "获取分布式锁失败"
			logger.Log.Errorf(utils.MMark(logCtx)+" acquireCensorLockWithBlock error: %+v\n", err)
			return item, err
		}
	}
	defer redisLock.Unlock(context.Background())

	// 查找数据库
	item, err = findDbCacheByText(logCtx, md5)
	if err == nil {
		// 找到数据
		return item, nil
	}

	if config.LocalConfig.RiskControl.IsInternational {
		// 是否为国际版
		return f.censorTextByInternational(logCtx, text, accountID)
	}

	// 判断是否为小语种
	if ok, err := minicensor.IsMiniLangBytext(logCtx, text); err != nil {
		item.ErrorCode = 100007
		item.ErrorMsg = "文本语种检测失败"
		logger.Log.Errorf(utils.MMark(logCtx)+" isMiniLangBytext err:%v", err)
		return item, err
	} else if !ok {
		// 中文审核
		return f.censorTextByChinese(logCtx, text, accountID)
	}

	// 小语种审核
	return f.censorTextByMiniLanguage(logCtx, text, accountID)
}

func (f *RiskControlText) censorTextChinese(logCtx context.Context, text, accountId string) (proto.TextCensorResponse, string, error) {
	var response proto.TextCensorResponse
	md5 := handlerUtils.CalculateMD5FromText(text)

	starttime := time.Now()
	retryNumber := int64(0)
	censorRes := ""

	for i := 0; i < 3; i++ {
		response = proto.TextCensorResponse{}
		retryNumber += 1
		// 数据库中不存在则调用接口进行审核
		censorRes = f.censorTextPost(text, config.LocalConfig.RiskControl.StrategyId)
		err := json.Unmarshal([]byte(censorRes), &response)
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+"censorText TextCensor is err:%v res:%v", err, censorRes)
			response.ErrorCode = 200001
			response.ErrorMsg = "res:" + censorRes + "err: " + err.Error()
			time.Sleep(1 * time.Second)
			continue
		}

		// 错误信息 IAM Certification failed 这个错误码重复验证一下可能就成功了
		if response.ErrorCode == 18 { // qps 超限
			logger.Log.Errorf(utils.MMark(logCtx)+"censorText ImgCensor qps limit res:%v", censorRes)
			response.ErrorCode = 200002
			response.ErrorMsg = "qps limit"
			time.Sleep(1 * time.Second)
			continue
		} else if response.ErrorCode != 0 {
			logger.Log.Errorf(utils.MMark(logCtx)+"censorText TextCensor IAM Certification failed, res:%v", censorRes)
			response.ErrorCode = 200003
			time.Sleep(1 * time.Second)
			continue
		} else if response.ConclusionType == 4 { // 审核失败 重试三次
			logger.Log.Errorf(utils.MMark(logCtx)+"censorText TextCensor censor failed res:%v", censorRes)
			response.ErrorCode = 200004
			time.Sleep(1 * time.Second)
			continue
		}
		break
	}

	taskstatus := enum.TaskSucceed
	if response.ErrorCode != 0 {
		taskstatus = enum.TaskFailed
	}

	endtime := time.Now()
	duration := endtime.Sub(starttime).Milliseconds()/1000 + 1
	taskid := "rct-" + utils.RandStringRunes(16)

	r := model.RiskControlItem{
		AccountID:      accountId,
		LogId:          utils.GetLogID(logCtx),
		TaskId:         taskid,
		Text:           text,
		Type:           model.RiskControlTypeText,
		Md5:            md5,
		ConclusionType: response.ConclusionType,
		CensorResult:   censorRes,
		Status:         taskstatus,
		Duration:       duration,
		RetryNumber:    retryNumber,
		CreatedAt:      starttime,
		UpdatedAt:      endtime,
	}

	err := r.UpdateRiskControlItem(gomysql.DB)

	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"censorText UpdateRiskControlItem is err:%v text:%s", err, text)
		return response, "", err
	}

	return response, taskid, nil
}

func (f *RiskControlText) censorTextMiniLanguage(logCtx context.Context, text, accountId string) (minicensor.CensorTextMiniLanguageResponse, string, error) {
	var response minicensor.CensorTextMiniLanguageResponse
	md5 := handlerUtils.CalculateMD5FromText(text)
	// 检查队列是否超限
	queueKey := fmt.Sprintf(RiskControlMiniTextQueueFmt, handlerUtils.GetNameByRunEnv())
	ok, err := checkQueueAllowance(queueKey, config.LocalConfig.RiskControl.TextQueueSize, RiskControlRedisKeyMaxInterval)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+" checkQueueAllowance is err:%v", err)
		return response, "", err
	}

	if !ok {
		response.ErrorCode = 100005
		response.ErrorMsg = "队列已满，请稍后重试"
		return response, "", nil
	}

	defer decrementCounter(queueKey)
	// 申请qps限制
	key := fmt.Sprintf(RiskControlMiniTextQpsFmt, handlerUtils.GetNameByRunEnv())
	for {
		ok, err := checkQpsAllowance(key, config.LocalConfig.RiskControl.MiniTextQpsMax)
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+" checkQpsAllowance is err:%v key: %s", err, key)
			return response, "", err
		}

		if !ok {
			time.Sleep(10 * time.Millisecond)
			continue
		}

		break
	}

	starttime := time.Now()
	retryNumber := int64(0)

	censorRes, err := minicensor.CensorTextPost(logCtx, text)
	if err != nil {
		response.ErrorCode = 10004
		response.ErrorMsg = "审核失败，请稍后重试"
		logger.Log.Errorf(utils.MMark(logCtx)+utils.ServiceInternalEsErrMsg+"HandleRequest censorTextMiniLanguage is err:%v", err)
		return response, "", err
	}

	err = json.Unmarshal([]byte(censorRes), &response)
	if err != nil {
		response.ErrorCode = 10004
		response.ErrorMsg = "审核失败，请稍后重试"
		logger.Log.Errorf(utils.MMark(logCtx)+" censorTextMiniLanguage json error: %+v", err)
		return response, "", err
	}

	taskstatus := enum.TaskSucceed
	if response.ErrorCode != 0 {
		taskstatus = enum.TaskFailed
	} else if response.Data.LabelID == 0 || response.Data.LabelID == -1 {
		sensitiveTexts, err := sensitiveInfoDetection(logCtx, text)
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+" sensitiveInfoDetection is err:%v", err)
			taskstatus = enum.TaskFailed
			response.ErrorCode = 200001
			response.ErrorMsg = err.Error()
		} else if len(sensitiveTexts) > 0 {
			response.Data.LabelID = 41
			response.Data.Words = sensitiveTexts
			censorResByte, err := json.Marshal(response)
			if err != nil {
				logger.Log.Errorf(utils.MMark(logCtx)+" sensitiveInfoDetection json error: %+v", err)
				response.ErrorCode = 10004
				response.ErrorMsg = "审核失败，请稍后重试"
				logger.Log.Errorf(utils.MMark(logCtx)+" sensitiveInfoDetection json error: %+v", err)
				return response, "", err
			}
			censorRes = string(censorResByte)
		}
	}

	endtime := time.Now()
	duration := endtime.Sub(starttime).Milliseconds()/1000 + 1
	taskid := "rct-" + utils.RandStringRunes(16)

	r := model.RiskControlItem{
		AccountID:      accountId,
		LogId:          utils.GetLogID(logCtx),
		TaskId:         taskid,
		Text:           text,
		Type:           model.RiskControlTypeTextMiniLanguge,
		Md5:            md5,
		ConclusionType: response.Data.LabelID,
		CensorResult:   censorRes,
		Status:         taskstatus,
		Duration:       duration,
		RetryNumber:    retryNumber,
		CreatedAt:      starttime,
		UpdatedAt:      endtime,
	}

	err = r.UpdateRiskControlItem(gomysql.DB)

	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"censorText UpdateRiskControlItem is err:%v text:%s", err, text)
		return response, "", err
	}

	return response, taskid, nil
}

func (f *RiskControlText) censorTextPost(text string, strategyId int) (result string) {
	data := make(map[string]string)
	data["text"] = text
	data["strategyId"] = strconv.Itoa(strategyId)

	return baseClient.PostUrlForm(__textCensorUserDefinedUrl, data, &f.auth)
}

func (f *RiskControlText) censorTextByChinese(logCtx context.Context, text string, accountID string) (proto.RiskControlTextItem, error) {
	item := proto.RiskControlTextItem{}

	response, taskid, err := f.censorTextChinese(logCtx, text, accountID)
	if err != nil {
		item.ErrorCode = 10004
		item.ErrorMsg = "审核失败，请稍后重试"

		logger.Log.Errorf(utils.MMark(logCtx)+utils.ServiceInternalEsErrMsg+" censorText is err:%v", err)
		return item, err
	}

	item = getRiskControlTextItemByChinese(response, text, taskid)
	return item, nil
}

func (f *RiskControlText) censorTextByMiniLanguage(logCtx context.Context, text string, accountID string) (proto.RiskControlTextItem, error) {
	item := proto.RiskControlTextItem{}
	res, taskID, err := f.censorTextMiniLanguage(logCtx, text, accountID)
	if err != nil {
		item.ErrorCode = 10004
		item.ErrorMsg = "审核失败，请稍后重试"
		logger.Log.Errorf(utils.MMark(logCtx)+utils.ServiceInternalEsErrMsg+"HandleRequest censorTextMiniLanguage is err:%v", err)
		return item, err
	}

	item = getRiskControlTextItemByMiniLanguage(res, text, taskID)
	return item, nil
}

func (f *RiskControlText) censorTextByInternational(logCtx context.Context, text string, accountID string) (proto.RiskControlTextItem, error) {
	md5 := handlerUtils.CalculateMD5FromText(text)
	// 检查队列是否超限
	queueKey := fmt.Sprintf(RiskControlMiniTextQueueFmt, handlerUtils.GetNameByRunEnv())
	ok, err := checkQueueAllowance(queueKey, config.LocalConfig.RiskControl.TextQueueSize, RiskControlRedisKeyMaxInterval)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+" checkQueueAllowance is err:%v", err)
		return proto.RiskControlTextItem{}, err
	}

	if !ok {
		return proto.RiskControlTextItem{
			ErrorCode: 200001,
			ErrorMsg:  "队列已满，请稍后重试",
			TaskID:    "",
		}, nil
	}

	defer decrementCounter(queueKey)
	// 申请qps限制
	key := fmt.Sprintf(RiskControlMiniTextQpsFmt, handlerUtils.GetNameByRunEnv())
	for {
		ok, err := checkQpsAllowance(key, config.LocalConfig.RiskControl.MiniTextQpsMax)
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+" checkQpsAllowance is err:%v key: %s", err, key)
			return proto.RiskControlTextItem{}, err
		}

		if !ok {
			time.Sleep(10 * time.Millisecond)
			continue
		}

		break
	}

	starttime := time.Now()
	retryNumber := int64(0)
	censorRes := ""
	taskstatus := enum.TaskSucceed
	taskID := "rct-" + utils.RandStringRunes(16)
	item, err := censorTextGcp(logCtx, text)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+utils.ServiceInternalEsErrMsg+" HandleRequest censorTextGcp is err:%v", err)
		taskstatus = enum.TaskFailed
		item.ErrorMsg = "审核失败"
		item.ErrorCode = 200003
		censorRes = err.Error()
	} else {
		item.TaskID = taskID
		resByte, err := json.Marshal(item)
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+"censorTextGcp json error: %+v", err)
			taskstatus = enum.TaskFailed
		}
		censorRes = string(resByte)
	}

	endtime := time.Now()
	duration := endtime.Sub(starttime).Milliseconds()/1000 + 1

	r := model.RiskControlItem{
		AccountID:      accountID,
		LogId:          utils.GetLogID(logCtx),
		TaskId:         taskID,
		Text:           text,
		Type:           model.RiskControlTypeTextGCP,
		Md5:            md5,
		ConclusionType: item.ConclusionType,
		CensorResult:   censorRes,
		Status:         taskstatus,
		Duration:       duration,
		RetryNumber:    retryNumber,
		CreatedAt:      starttime,
		UpdatedAt:      endtime,
	}

	err = r.UpdateRiskControlItem(gomysql.DB)

	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"censorText UpdateRiskControlItem is err:%v text:%s", err, text)
		return *item, err
	}

	return *item, nil
}

func getRiskControlTextItemByChinese(response proto.TextCensorResponse, text, taskID string) proto.RiskControlTextItem {
	item := proto.RiskControlTextItem{
		TaskID:         taskID,
		ErrorCode:      response.ErrorCode,
		ErrorMsg:       response.ErrorMsg,
		Conclusion:     response.Conclusion,
		ConclusionType: response.ConclusionType,
		Data:           []proto.RiskControlTextItemDetail{},
	}

	if response.ConclusionType == 2 {
		for _, data := range response.Data {
			// 命中的关键词map 用于去重
			hitWordMap := make(map[string]struct{})
			itemdata := proto.RiskControlTextItemDetail{
				ErrorCode: data.ErrorCode,
				ErrorMsg:  data.ErrorMsg,
				Msg:       data.Msg,
				Words:     []string{},
			}
			// 跳过合规项
			if data.ConclusionType == 1 {
				continue
			}

			for _, itemHits := range data.Hits {
				for _, word := range itemHits.Words {
					words, ok := IsHitWordWithSave(word, hitWordMap)
					if !ok {
						continue
					}

					itemdata.Words = append(itemdata.Words, words...)
				}

				//如果大模型没有命中关键词 再根据模型命中位置去匹配关键词
				if len(itemHits.Words) > 0 {
					continue
				}

				for _, hitpos := range itemHits.ModelHitPositions {
					if len(hitpos) > 2 {
						word := handlerUtils.SubstringByRuneCount(text, int(hitpos[0]), int(hitpos[1]))
						words, ok := IsHitWordWithSave(word, hitWordMap)
						if !ok {
							continue
						}
						itemdata.Words = append(itemdata.Words, words...)
					}
				}

				for _, wordhitpos := range itemHits.WordHitPositions {
					words, ok := IsHitWordWithSave(wordhitpos.Keyword, hitWordMap)
					if !ok {
						continue
					}
					itemdata.Words = append(itemdata.Words, words...)
					for _, pos := range wordhitpos.Positions {
						if len(pos) == 2 {
							word := handlerUtils.SubstringByRuneCount(text, int(pos[0]), int(pos[1]))
							words, ok := IsHitWordWithSave(word, hitWordMap)
							if !ok {
								continue
							}
							itemdata.Words = append(itemdata.Words, words...)
						}
					}
				}

			}

			item.Data = append(item.Data, itemdata)
		}
	}

	return item
}

func getRiskControlTextItemByMiniLanguage(response minicensor.CensorTextMiniLanguageResponse, text, taskID string) proto.RiskControlTextItem {
	item := proto.RiskControlTextItem{
		TaskID:         taskID,
		ErrorCode:      response.ErrorCode,
		ErrorMsg:       response.ErrorMsg,
		Conclusion:     "合规",
		ConclusionType: 1,
		Data:           []proto.RiskControlTextItemDetail{},
	}

	if response.Data.LabelID != 0 && response.Data.LabelID != -1 {
		item.Conclusion = "不合规"
		item.ConclusionType = 2
		words := []string{text}

		if len(response.Data.Words) > 0 {
			words = response.Data.Words
		}

		item.Data = append(item.Data, proto.RiskControlTextItemDetail{
			Msg:       minicensor.GetCensorLabel(response.Data.LabelID),
			Words:     words,
			ErrorCode: 0,
			ErrorMsg:  "",
		})
	}

	return item
}

func findDbCacheByText(logCtx context.Context, md5 string) (proto.RiskControlTextItem, error) {
	item := proto.RiskControlTextItem{}
	// 查找数据库中是否解析过
	r := model.RiskControlItem{}
	riskitem, err := r.GetRiskControlItemFromMd5(gomysql.DB, md5)
	if err != nil {
		logger.Log.Warnf(utils.MMark(logCtx)+"  GetRiskControlItemFromMd5 is err:%v", err)
		return item, err
	}

	// 如果数据库存在则直接获取审核结果并返回
	if riskitem.Type == model.RiskControlTypeText {
		var response proto.TextCensorResponse
		err = handlerUtils.JSONToStruct(riskitem.CensorResult, &response)
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+"censorText JSONToStruct is err:%v", err)
			return item, err
		}

		item = getRiskControlTextItemByChinese(response, riskitem.Text, riskitem.TaskId)
		logger.Log.Infof(utils.MMark(logCtx) + "censorText GetRiskControlItemFromMd5 is success")
	} else if riskitem.Type == model.RiskControlTypeTextMiniLanguge {
		var response minicensor.CensorTextMiniLanguageResponse
		err = handlerUtils.JSONToStruct(riskitem.CensorResult, &response)
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+"censorText JSONToStruct is err:%v", err)
			return item, err
		}

		item = getRiskControlTextItemByMiniLanguage(response, riskitem.Text, riskitem.TaskId)

	} else if riskitem.Type == model.RiskControlTypeTextGCP {
		err = handlerUtils.JSONToStruct(riskitem.CensorResult, &item)
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+"censorText JSONToStruct is err:%v", err)
			return item, err
		}
	} else {
		err = riskitem.HardDeleteRiskControlItem(gomysql.DB, riskitem.ID)
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+" HardDeleteRiskControlItem is err:%v", err)
		}
		logger.Log.Errorf(utils.MMark(logCtx)+" GetRiskControlItemFromMd5 is err:riskitem.Type is not Text or MiniLanguge, riskitem.Type: %s", riskitem.Type)
		return item, errors.New("riskitem.Type is not Text or MiniLanguge")
	}

	return item, nil
}

func sensitiveInfoDetection(logCtx context.Context, text string) ([]string, error) {
	sensitive := minicensor.SensitiveInfoDetection{}

	body := minicensor.SensitiveInfoDetectionRequestBody{
		Token:    sensitive.GenerateToken(),
		Content:  []string{text},
		TaskType: "rule",
	}

	return sensitive.SensitiveInfoDetection(logCtx, body)
}

func censorTextGcp(logCtx context.Context, text string) (*proto.RiskControlTextItem, error) {
	item := &proto.RiskControlTextItem{
		TaskID:         "",
		ErrorCode:      0,
		ErrorMsg:       "",
		Conclusion:     "合规",
		ConclusionType: 1,
		Data:           []proto.RiskControlTextItemDetail{},
	}
	data := []proto.RiskControlTextItemDetail{}
	targetLanguageValue := logCtx.Value("Language")
	targetLanguage := "en"
	if targetLanguageValue != nil {
		targetLanguage = fmt.Sprintf("%s", targetLanguageValue)
	}

	res, err := gcpcensor.CensorTextByGcp(logCtx, text)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+" CensorTextByGcp is err:%v", err)
		return item, err
	}

	if !res.GetLanguageSupported() {
		item.Conclusion = "合规"
		item.ConclusionType = 1
		logger.Log.Infof(utils.MMark(logCtx)+" LanguageSupported: %v\n", res.GetLanguageSupported())
		return item, nil
	}

	// 处理返回的 ModerateTextResponse
	if res.GetModerationCategories() != nil {
		logger.Log.Infof(utils.MMark(logCtx) + " ModerateText Response:\n")
		for _, category := range res.GetModerationCategories() {
			// 输出不当内容的分类及其得分
			logger.Log.Infof(utils.MMark(logCtx)+" Name: %s, Confidence: %v\n", category.Name, category.Confidence)
			// 判断是否在配置的敏感词列表中
			isFind := false
			for _, name := range config.LocalConfig.RiskControl.GCPCensorTextType {
				if name == category.Name {
					isFind = true
					break
				}
			}

			if isFind && category.Confidence > config.LocalConfig.RiskControl.GCPConfidenceMax {
				data = append(data, proto.RiskControlTextItemDetail{
					Msg:   respi18n.TransformResponseLocaleByDefault(logCtx, targetLanguage, category.Name),
					Words: []string{text},
				})
			}
		}
	}

	// 如果有不当内容，则设置结论为“不合规”并添加详细信息
	if len(data) > 0 {
		item.Conclusion = "不合规"
		item.ConclusionType = 2
		item.Data = data
	}

	// 输出语言和是否支持的状态
	logger.Log.Infof(utils.MMark(logCtx)+" languageCode: %s\n", res.GetLanguageCode())
	logger.Log.Infof(utils.MMark(logCtx)+" LanguageSupported: %v\n", res.GetLanguageSupported())

	return item, nil
}
