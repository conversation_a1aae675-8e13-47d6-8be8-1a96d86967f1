package figure

import (
	"acg-ai-go-common/global"
	"acg-ai-go-common/gomysql"
	"acg-ai-go-common/logger"
	"acg-ai-go-common/utils"
	"context"
	"dhlive-external-api/beans/model"
	"dhlive-external-api/beans/proto"
	config "dhlive-external-api/conf"
	"dhlive-external-api/handler/handlerUtils/redislock"
	"encoding/json"
	"net/http"
	"net/url"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
)

type CommRsp struct {
	// 接口返回错误码，正常为0，其他为异常，依据每个接口的定义
	Code int `json:"code"`
	// 根据不同接口不同
	Msg string `json:"msg"`
}
type CommDataRsp struct {
	CommRsp
	Data interface{} `json:"data,omitempty"`
}

const (
	RSP_COM_CODE_SUCCESS                    = 0      // 成功
	RSP_COM_CODE_PARAM_ERR                  = -1000  // 参错错误
	RSP_COM_CODE_NO_LOGIN_TICKET            = -1001  // 缺少登录信息
	RSP_COM_CODE_NO_REQUEST_ID              = -1002  // 缺少requestId
	RSP_COM_CODE_LOGIN_TICKET_NOT_EXIST     = -1003  // 登录信息不存在
	RSP_COM_CODE_APISIGN_EMPTY_ERR          = -1004  // apiSign错误
	RSP_COM_CODE_APISIGN_ERR                = -1005  // apiSign错误
	RSP_COM_CODE_OAUTH_PROJECT_ERR          = -1006  // 项目授权信息错误
	RSP_COM_CODE_GET_PROJECT_ERR            = -1007  // 获取项目授权
	RSP_COM_CODE_SIGN_ERR                   = -1008  // 签名错误
	RSP_COM_CODE_SIGN_TIMESTAMP_ERR         = -1009  // 签名有效期错误
	RSP_COM_CODE_PROJECT_OUTDATE_ERR        = -1010  // 项目已经过期
	RSP_COM_CODE_TICKET_INVALID             = 400001 // ticket无效
	RSP_COM_CODE_PERMISSIONS_INSUFFICIENT   = 400002 // 权限不足
	RSP_COM_CODE_USER_RESOURCE_INSUFFICIENT = 400003 // 资源不足
	RSP_COM_CODE_PROJECT_INVALID            = 400004 // 项目无效
	RSP_COM_CODE_SERVER_ERROR               = 500001 // 服务端通用错误码
	RSP_COM_CODE_INNER_API_ERROR            = 500005 // 服务端内部接口调用异常

	RSP_COM_MSG_SUCCESS                    = "success"
	RSP_COM_MSG_PARAM_ERR                  = "param error"
	RSP_COM_MSG_NO_LOGIN_TICKET            = "miss login info"
	RSP_COM_MSG_NO_REQUEST_ID              = "miss request id"
	RSP_COM_MSG_LOGIN_TICKET_NOT_EXIST     = "login ticket not exist"
	RSP_COM_MSG_APISIGN_EMPTY_ERR          = "api sign is empty"
	RSP_COM_MSG_APISIGN_ERR                = "api sign error"
	RSP_COM_MSG__OAUTH_PROJECT_ERR         = "oauthAppKey or oauthMark is error"
	RSP_COM_MSG_GET_PROJECT_ERR            = "get project info error"
	RSP_COM_MSG_SIGN_ERR                   = "sign error"
	RSP_COM_MSG_SIGN_TIMESTAMP_ERR         = "timestamp error"
	RSP_COM_MSG_PROJECT_OUTDATE_ERR        = "system is overdue"
	RSP_COM_MSG_TICKET_INVALID             = "ticket invalid"
	RSP_COM_MSG_PERMISSIONS_INSUFFICIENT   = "permissions insufficient"
	RSP_COM_MSG_USER_RESOURCE_INSUFFICIENT = "resource insufficient"
	RSP_COM_MSG_PROJECT_INVALID            = "project invalid"
	RSP_COM_MSG_SERVER_ERROR               = "server error"
	RSP_COM_MSG_INNER_API_ERROR            = "server inner api error"
)

// NewServerErrRsp 服务端异常响应结果
func NewServerErrRsp() CommRsp {
	return CommRsp{
		Code: RSP_COM_CODE_SERVER_ERROR,
		Msg:  RSP_COM_MSG_SERVER_ERROR,
	}
}

func NewSuccessRsp(data interface{}) CommDataRsp {
	return CommDataRsp{
		CommRsp: CommRsp{
			Code: RSP_COM_CODE_SUCCESS,
			Msg:  RSP_COM_MSG_SUCCESS,
		},
		Data: data,
	}
}

func NewCommDataRsp(code int, msg string, data interface{}) CommDataRsp {
	return CommDataRsp{
		CommRsp: CommRsp{
			Code: code,
			Msg:  msg,
		},
		Data: data,
	}
}

// CommPageRsp
// 通用分页返回结构体
type CommPageRsp struct {
	Count int64       `json:"count"`
	List  interface{} `json:"list"`
}

type GetRiskControlItemRequest struct {
	AccountId      string `json:"accountId"`
	ConclusionType int64  `json:"conclusionType"` // 0 属于异常 1 合规，2 不合规，3 疑似， 4 审核失败
	PageNo         int    `json:"pageNum"`
	PageSize       int    `json:"pageSize"`
	TaskId         string `json:"taskId"`
	DateRange      string `json:"dateRange"`
}

type CustomRiskControlItemRequest struct {
	TaskId         string `json:"taskId"`
	ConclusionType int64  `json:"conclusionType"` // 0 属于异常 1 合规，2 不合规，3 疑似， 4 审核失败
}

type CustomRiskControlItemCensorResult struct {
	Conclusion     string `json:"conclusion"`
	ConclusionType int    `json:"conclusionType"`
}

func GetRiskControlItemList(c *gin.Context) {
	req := &GetRiskControlItemRequest{}
	if err := c.ShouldBindJSON(req); err != nil {
		c.JSON(http.StatusOK, NewCommDataRsp(100001, "参数错误", nil))
		return
	}
	startTime := ""
	endTime := ""
	if len(req.DateRange) > 0 {
		times := strings.Split(req.DateRange, ",")
		if len(times) != 2 {
			c.JSON(http.StatusOK, NewCommDataRsp(100003, "日期范围格式错误", nil))
			return
		}
		startTime = times[0]
		endTime = times[1]
	}

	list, err := (&model.RiskControlItem{}).GetRiskControlItemList(gomysql.DB, req.AccountId, req.TaskId, req.ConclusionType, req.PageNo, req.PageSize, startTime, endTime)
	if err != nil {
		c.JSON(http.StatusOK, NewCommDataRsp(100002, "数据库查询失败", nil))
		return
	}
	count, err := (&model.RiskControlItem{}).GetRiskControlItemCount(gomysql.DB, req.AccountId, req.TaskId, req.ConclusionType, req.PageNo, req.PageSize, startTime, endTime)
	if err != nil {
		c.JSON(http.StatusOK, NewCommDataRsp(100003, "计算总数失败", nil))
		return
	}
	c.JSON(http.StatusOK, NewSuccessRsp(&CommPageRsp{
		Count: count,
		List:  list,
	}))
}

func CustomRiskControlItem(c *gin.Context) {
	req := &CustomRiskControlItemRequest{}
	if err := c.ShouldBindJSON(req); err != nil {
		c.JSON(http.StatusOK, NewCommDataRsp(100001, "参数错误", nil))
		return
	}
	item, err := (&model.RiskControlItem{}).GetRiskControlItemFromTaskId(gomysql.DB, req.TaskId)
	if err != nil {
		c.JSON(http.StatusOK, NewCommDataRsp(100002, "数据库查询失败", nil))
		return
	}

	var result CustomRiskControlItemCensorResult

	if req.ConclusionType == 1 { // 审核合规
		result.Conclusion = "合规"
		result.ConclusionType = 1
		item.ConclusionType = 1
		item.AudioCensorResult = ""
	} else if req.ConclusionType == 2 { // 审核不合法
		result.Conclusion = "不合规"
		result.ConclusionType = 2
		item.ConclusionType = 2
	} else {
		c.JSON(http.StatusOK, NewCommDataRsp(100004, "不合法的参数值", nil))
		return
	}

	// 先格式化审核结果
	{
		str, err := json.Marshal(result)
		if err != nil {
			c.JSON(http.StatusOK, NewCommDataRsp(100005, "审核结果序列化失败", nil))
			return
		}
		item.CensorResult = string(str)
	}

	if item.Type == model.RiskControlTypeImage || item.Type == model.RiskControlTypeImageMiniLanguge || item.Type == model.RiskControlTypeImageGCP {
		item.Type = model.RiskControlTypeImage
		if result.ConclusionType == 2 {
			response := proto.RiskControlImageCensor{
				Conclusion:     result.Conclusion,
				ConclusionType: int64(result.ConclusionType),
				Data: []proto.Detail{
					{
						Msg: "人工审核失败",
					},
				},
			}
			str, err := json.Marshal(response)
			if err != nil {
				c.JSON(http.StatusOK, NewCommDataRsp(100005, "审核结果序列化失败", nil))
				return
			}
			item.CensorResult = string(str)
		}

	} else if item.Type == model.RiskControlTypeText || item.Type == model.RiskControlTypeTextMiniLanguge || item.Type == model.RiskControlTypeTextGCP {
		item.Type = model.RiskControlTypeText
		if result.ConclusionType == 2 {
			response := proto.TextCensorResponse{
				Conclusion:     result.Conclusion,
				ConclusionType: int64(result.ConclusionType),
				Data: []proto.TextCensorDataItem{
					{
						Conclusion:     result.Conclusion,
						ConclusionType: int64(result.ConclusionType),
						Msg:            "人工审核失败",
					},
				},
			}
			str, err := json.Marshal(response)
			if err != nil {
				c.JSON(http.StatusOK, NewCommDataRsp(100005, "审核结果序列化失败", nil))
				return
			}
			item.CensorResult = string(str)
		}
	} else if item.Type == model.RiskControlTypeShortVideo || item.Type == model.RiskControlTypeLongVideo ||
		item.Type == model.RiskControlTypeShortVideoMiniLanguge || item.Type == model.RiskControlTypeVideoGCP {
		item.Type = model.RiskControlTypeShortVideo
		if result.ConclusionType == 2 {
			response := proto.RiskControlShortVideoCensorResponse{
				Conclusion:     result.Conclusion,
				ConclusionType: int64(result.ConclusionType),
				ConclusionTypeGroupInfos: []proto.ConclusionTypeGroupInfo{
					{
						Msg: "人工审核失败",
					},
				},
			}
			str, err := json.Marshal(response)
			if err != nil {
				c.JSON(http.StatusOK, NewCommDataRsp(100005, "审核结果序列化失败", nil))
				return
			}
			item.CensorResult = string(str)
		}
	} else if item.Type == model.RiskControlTypeShortAudio || item.Type == model.RiskControlTypeLongAudio ||
		item.Type == model.RiskControlTypeAudioMiniLanguge || item.Type == model.RiskControlTypeAudioGCP {
		item.Type = model.RiskControlTypeShortAudio
		if result.ConclusionType == 2 {
			response := proto.RiskControlShortAudioCensorResponse{

				Conclusion:     result.Conclusion,
				ConclusionType: int64(result.ConclusionType),
				Data: []proto.RiskControlLongAudioCensorPullDataDetail{
					{
						Conclusion:     result.Conclusion,
						ConclusionType: result.ConclusionType,
						AuditData: []proto.RiskControlLongAudioCensorPullAuditDataDetail{{
							Msg: "人工审核失败",
						}},
					},
				},
			}
			str, err := json.Marshal(response)
			if err != nil {
				c.JSON(http.StatusOK, NewCommDataRsp(100005, "审核结果序列化失败", nil))
				return
			}
			item.CensorResult = string(str)
		}
	}

	err = item.UpdateRiskControlItem(gomysql.DB)
	if err != nil {
		c.JSON(http.StatusOK, NewCommDataRsp(100006, "更新审核结果失败", nil))
		return
	}
	c.JSON(http.StatusOK, NewSuccessRsp(nil))
}

func VerifyBosUrl(urlPath string) bool {
	// 解析URL
	parsedURL, err := url.Parse(urlPath)
	if err != nil || parsedURL.Scheme == "" || parsedURL.Host == "" {
		// URL 无效
		return false
	}

	if global.ServerSetting.StorageSetting.Type == "bos" {
		// 检查是否包含指定路径
		return strings.Contains(parsedURL.Host, "bcebos.com")
	} else {
		for _, host := range config.LocalConfig.RiskControl.CensorValidDomains {
			if strings.Contains(parsedURL.Host, host) {
				return true
			}
		}
	}
	return false
}

// acquireCensorLockWithBlock 是一个阻塞函数，直到申请到锁为止。
func acquireCensorLockWithBlock(logCtx context.Context, redisLock *redislock.RedisLock) error {
	for {
		ok, err := redisLock.Lock(context.Background())
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+utils.RedisEsErrMsg+" Lock error: %+v\n", err)
			return err
		} else if ok {
			break
		}

		logger.Log.Infof(utils.MMark(logCtx) + " Lock is busy, retrying...\n")
		time.Sleep(1 * time.Second)
	}
	return nil
}
