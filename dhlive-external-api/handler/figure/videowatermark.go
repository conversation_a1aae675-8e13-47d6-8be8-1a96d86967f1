package figure

import (
	"acg-ai-go-common/gomysql"
	"acg-ai-go-common/logger"
	"acg-ai-go-common/utils"
	"acg-ai-go-common/utils/redisproxy"
	"bytes"
	"context"
	"dhlive-external-api/beans/enum"
	"dhlive-external-api/beans/model"
	"dhlive-external-api/beans/proto"
	"dhlive-external-api/handler/handlerUtils"
	"dhlive-external-api/handler/handlerUtils/redislock"
	"dhlive-external-api/handler/handlerUtils/retryhttpclient"
	"encoding/json"
	"fmt"
	"net/http"
	"net/url"
	"path"
	"strings"
	"sync"
	"time"

	config "dhlive-external-api/conf"

	"github.com/baidubce/bce-sdk-go/bce"
	"github.com/baidubce/bce-sdk-go/services/media"
	"github.com/baidubce/bce-sdk-go/services/media/api"
	"github.com/gin-gonic/gin"
	"github.com/go-redis/redis/v8"
)

const (
	BJ                          = "media.bj.baidubce.com"
	GZ                          = "media.gz.baidubce.com"
	SU                          = "media.su.baidubce.com"
	WatermarkBosHostFmt         = "https://%s.bj.bcebos.com/%s"
	WatermarkBosCdnHostFmt      = "https://%s.cdn.bcebos.com/%s"
	WatermarkKeyPre             = "WatermarkKey"                  // 用户归属资源集合前缀
	WatermarkKeyFmt             = "%v:" + WatermarkKeyPre + ":%v" // 先填写环境ID，在添加唯一ID，组合起来是一个redis key
	WatermarkLockTimeDuration   = 5 * time.Minute
	WatermarkStatusTimeDuration = 1 * time.Hour
)

var (
	// 实例化一个VideoWatermark
	VideoWatermarkInstance *VideoWatermark
	// once 用于确保实例化操作只执行一次
	onceVideoWatermark sync.Once
)

type VideoWatermark struct {
	client     *media.Client
	redisproxy *redisproxy.RedisProxy // Redis 客户端实例
}

func GetVideoWatermark() *VideoWatermark {
	onceVideoWatermark.Do(func() {
		VideoWatermarkInstance = newVideoWatermark()
	})
	return VideoWatermarkInstance
}

func newVideoWatermark() *VideoWatermark {
	// 用户的Access Key ID和Secret Access Key
	AK := config.LocalConfig.WatermMark.AK
	SK := config.LocalConfig.WatermMark.SK
	// 用户指定的Endpoint
	ENDPOINT := config.LocalConfig.WatermMark.Endpoint

	// 初始化一个MCPClient
	client, err := media.NewClient(AK, SK, ENDPOINT)
	if err != nil {
		logger.Log.Fatalf("NewVideoWatermark NewClient error: %+v \n", err)
	}

	return &VideoWatermark{client: client, redisproxy: redisproxy.GetRedisProxy()}
}

func (v *VideoWatermark) HandleRequest(c *gin.Context) {
	req := proto.CreateWaterMarkRequest{}
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Log.Errorf("HandleRequest req error: %+v", err)
		c.JSON(http.StatusOK, proto.NewCommServiceRsp(100001, "参数异常", &proto.WaterMarkResponse{
			FileId: req.FileId,
		}))
		return
	}

	logCtx := context.WithValue(context.TODO(), utils.CtxKeyLogID, req.FileId)
	if len(req.SourceKey) <= 0 || len(req.TargetKey) <= 0 ||
		len(req.FileId) <= 0 || len(req.CallbackUrl) <= 0 {
		logger.Log.Errorf(utils.MMark(logCtx)+"HandleRequest req error: %+v", req)
		c.JSON(http.StatusOK, proto.NewCommServiceRsp(100002, "参数异常", &proto.WaterMarkResponse{
			FileId: req.FileId,
		}))
		return
	}

	logger.Log.Infof(utils.MMark(logCtx)+"HandleRequest req: %+v", req)
	sBucket := req.SourceBucket
	tBucket := req.SourceBucket

	// 获取水印信息
	if watermarkOld, err := (&model.Watermark{}).GetWatermarkFromFileIdAndBosKey(gomysql.DB, req.FileId, req.SourceKey); err == nil {
		logger.Log.Warningf(utils.MMark(logCtx)+"HandleRequest video watermark is processed repeatedly: %+v", watermarkOld)
		// 任务已存在，则直接返回
		if watermarkOld.Status == enum.TaskRunning {
			c.JSON(http.StatusOK, proto.NewSuccessServiceRsp(&proto.WaterMarkResponse{
				FileId: req.FileId,
			}))
			return
		} else if watermarkOld.Status == enum.TaskSucceed {
			info := &proto.JobStatusInfo{
				Code:       "",
				ErrMsg:     "",
				FileId:     watermarkOld.FileId,
				JobId:      watermarkOld.JobId,
				JobStatus:  "SUCCESS",
				CreateTime: watermarkOld.CreatedAt.Format("2006-01-02T15:04:05Z"),
				StartTime:  watermarkOld.StartTime.Format("2006-01-02T15:04:05Z"),
				EndTime:    watermarkOld.EndTime.Format("2006-01-02T15:04:05Z"),
				FileUrl:    fmt.Sprintf(WatermarkBosHostFmt, watermarkOld.TargetBucket, watermarkOld.TargetKey),
				Duration:   int(watermarkOld.EndTime.Sub(watermarkOld.StartTime).Seconds()),
			}

			go SendCallbackRequest(watermarkOld.CallbackUrl, info)
			c.JSON(http.StatusOK, proto.NewSuccessServiceRsp(&proto.WaterMarkResponse{
				FileId: req.FileId,
			}))

			return
		} else {
			info := &proto.JobStatusInfo{
				Code:       "",
				ErrMsg:     "",
				FileId:     watermarkOld.FileId,
				JobId:      watermarkOld.JobId,
				JobStatus:  "FAILED",
				CreateTime: watermarkOld.CreatedAt.Format("2006-01-02T15:04:05Z"),
				StartTime:  watermarkOld.StartTime.Format("2006-01-02T15:04:05Z"),
				EndTime:    watermarkOld.EndTime.Format("2006-01-02T15:04:05Z"),
				FileUrl:    fmt.Sprintf(WatermarkBosHostFmt, watermarkOld.TargetBucket, watermarkOld.TargetKey),
				Duration:   int(watermarkOld.EndTime.Sub(watermarkOld.StartTime).Seconds()),
			}

			go SendCallbackRequest(watermarkOld.CallbackUrl, info)
			c.JSON(http.StatusOK, proto.NewSuccessServiceRsp(&proto.WaterMarkResponse{
				FileId: req.FileId,
			}))

			return
		}
	}

	watermark := model.Watermark{
		FileId:       req.FileId,
		SourceBucket: sBucket,
		SourceKey:    req.SourceKey,
		TargetBucket: tBucket,
		TargetKey:    req.SourceKey,
		CallbackUrl:  req.CallbackUrl,
		SourceUrl:    fmt.Sprintf(WatermarkBosCdnHostFmt, sBucket, req.SourceKey),
		TargetUrl:    fmt.Sprintf(WatermarkBosCdnHostFmt, tBucket, req.SourceKey),
		JobId:        "",
		Status:       enum.TaskSucceed,
		StartTime:    time.Time{},
		EndTime:      time.Time{},
		CreatedAt:    time.Now(),
		UpdatedAt:    time.Now(),
	}

	{
		// 直接返回结果
		info := &proto.JobStatusInfo{
			Code:       "",
			ErrMsg:     "",
			FileId:     req.FileId,
			JobId:      "",
			JobStatus:  "SUCCESS",
			CreateTime: time.Now().Format("2006-01-02T15:04:05Z"),
			StartTime:  time.Now().Format("2006-01-02T15:04:05Z"),
			EndTime:    time.Now().Format("2006-01-02T15:04:05Z"),
			FileUrl:    fmt.Sprintf(WatermarkBosCdnHostFmt, sBucket, req.SourceKey),
			Duration:   0,
		}

		if err := SendCallbackRequest(req.CallbackUrl, info); err != nil {
			watermark.Status = enum.TaskFailed
			logger.Log.Errorf(utils.MMark(logCtx)+" SendCallbackRequest error: %+v\n", err)
		}
	}

	// jobID, err := CreateWatermarkTask(&watermark)
	// if err != nil {
	// 	logger.Log.Errorf(utils.MMark(logCtx)+" CreateWatermarkTask error: %+v\n", err)
	// 	c.JSON(http.StatusOK, proto.NewCommServiceRsp(100003, "创建任务失败", &proto.WaterMarkResponse{
	// 		FileId: req.FileId,
	// 	}))
	// 	return
	// }

	// // 设置缓存中的任务状态
	// statusRedisKey := fmt.Sprintf(WatermarkKeyFmt, handlerUtils.GetNameByRunEnv(), watermark.JobId+"_status")
	// err = v.redisproxy.SetKeyValue(statusRedisKey, string(watermark.Status), WatermarkStatusTimeDuration)
	// if err != nil {
	// 	logger.Log.Errorf(utils.MMark(logCtx)+utils.RedisEsErrMsg+" key %s SetKeyValue error: %+v\n", statusRedisKey, err)
	// 	c.JSON(http.StatusOK, proto.NewCommServiceRsp(100004, "设置缓存失败", &proto.WaterMarkResponse{
	// 		FileId: req.FileId,
	// 	}))
	// 	return
	// }

	// // 创建任务成功后，将任务ID赋值给watermark 并更新数据库
	// watermark.JobId = jobID
	if err := watermark.UpdateWatermark(gomysql.DB); err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+utils.MysqlEsErrMsg+"create watermark error: %+v\n", err)
		c.JSON(http.StatusOK, proto.NewCommServiceRsp(100005, "更新数据库失败", &proto.WaterMarkResponse{
			FileId: req.FileId,
		}))
		return
	}

	logger.Log.Infof(utils.MMark(logCtx)+"UpdateWatermark success db: %v. \n", watermark)
	// 创建任务成功后，将任务ID返回给前端
	c.JSON(http.StatusOK, proto.NewSuccessServiceRsp(&proto.WaterMarkResponse{
		FileId: req.FileId,
	}))
}

// WaterMarkCallback 视频水印结果回调接口 暂时不用，使用轮询方式查看结果
func (v *VideoWatermark) WaterMarkCallback(c *gin.Context) {
	req := &proto.WatermarkCallbackMessage{}
	if err := c.ShouldBindJSON(req); err != nil {
		logger.Log.Errorf("WaterMarkCallback MessageBody: %s  bind request body failed: %+v\n", req.MessageBody, err)
		c.JSON(http.StatusOK, proto.NewCommServiceRsp(100001, "参数异常", nil))
		return
	}
	messageBody := proto.WatermarkCallbackMessageBody{}
	err := json.Unmarshal([]byte(req.MessageBody), &messageBody)
	if err != nil {
		logger.Log.Errorf("WaterMarkCallback MessageBody: %s  unmarshal request body failed: %+v\n", req.MessageBody, err)
		c.JSON(http.StatusOK, proto.NewCommServiceRsp(100002, "参数异常", nil))
		return
	}

	logger.Log.Infof("WaterMarkCallback jobID: %s req: %+v\n", messageBody.JobId, req)
	w1 := model.Watermark{}
	watermark, err := w1.GetWatermarkFromJobId(gomysql.DB, messageBody.JobId)
	if err != nil {
		logger.Log.Errorf("WaterMarkCallback jobID: %s  get watermark failed: %+v\n", messageBody.JobId, err)
		c.JSON(http.StatusOK, proto.NewCommServiceRsp(100003, "查询任务失败", &proto.WatermarkCallbackResponse{
			FileId: watermark.FileId,
		}))
		return
	}

	// 更新任务状态
	if messageBody.JobStatus == "SUCCESS" {
		watermark.Status = enum.TaskSucceed
	} else if messageBody.JobStatus == "FAILED" {
		watermark.Status = enum.TaskFailed
	}

	watermark.UpdatedAt = time.Now()
	watermark.StartTime = messageBody.StartTime
	watermark.EndTime = messageBody.EndTime

	err = watermark.UpdateWatermark(gomysql.DB)
	if err != nil {
		logger.Log.Errorf("WaterMarkCallback ID: %s update watermark failed: %+v\n", watermark.FileId, err)
		c.JSON(http.StatusOK, proto.NewCommServiceRsp(100003, "更新任务失败", &proto.WatermarkCallbackResponse{
			FileId: watermark.FileId,
		}))
		return
	}

	// 回调服务
	info := &proto.JobStatusInfo{
		FileId:     watermark.FileId,
		JobId:      messageBody.JobId,
		JobStatus:  messageBody.JobStatus,
		CreateTime: messageBody.CreateTime.String(),
		// StartTime:  messageBody.StartTime,
		// EndTime:    messageBody.EndTime,
		FileUrl: fmt.Sprintf(WatermarkBosCdnHostFmt, watermark.TargetBucket, watermark.TargetKey),
	}

	if messageBody.Error != nil {
		info.Code = messageBody.Error.Code
		info.ErrMsg = messageBody.Error.Message
	}

	err = SendCallbackRequest(watermark.CallbackUrl, info)
	if err != nil {
		logger.Log.Errorf("WaterMarkCallback ID: %s send callback failed: %+v\n", watermark.FileId, err)
		c.JSON(http.StatusOK, proto.NewCommServiceRsp(100004, "发送回调失败", &proto.WatermarkCallbackResponse{
			FileId: watermark.FileId,
		}))
		return
	}

	logger.Log.Infof("WaterMarkCallback ID: %s success", watermark.FileId)
	c.JSON(http.StatusOK, proto.NewSuccessServiceRsp(&proto.WatermarkCallbackResponse{
		FileId: watermark.FileId,
	}))
}

func (v *VideoWatermark) GetWaterMarkResult(c *gin.Context) {
	req := &proto.GetWaterMarkRequest{}
	if err := c.ShouldBindJSON(req); err != nil {
		c.JSON(http.StatusOK, proto.NewCommRsp(100001, "参数异常"))
		return
	}
	jobID := ""
	if len(req.JobId) <= 0 {
		fileId := req.FileId

		w1 := model.Watermark{}
		w2, err := w1.GetWatermarkFromFileId(gomysql.DB, fileId)
		if err != nil {
			logger.Log.Errorf("GetWaterMarkResult GetWatermarkFromFileId ID: %s error: %+v \n", fileId, err)
			c.JSON(http.StatusOK, proto.NewCommRsp(100002, "查询任务失败"))
			return
		}

		jobID = w2.JobId
	} else {
		jobID = req.JobId
	}

	getTranscodingJobResponse, err := v.client.GetTranscodingJob(jobID)
	if err != nil {
		//logger.Log.Errorf("GetWaterMarkResult GetTranscodingJob ID: %s error: %+v \n", w2.JobId, err)
		c.JSON(http.StatusOK, proto.NewCommRsp(100004, "获取任务信息失败"))
		return
	}
	logger.Log.Infof("GetWaterMarkResult success: %+v", getTranscodingJobResponse)

	c.JSON(http.StatusOK, proto.NewSuccessRsp(getTranscodingJobResponse))
}

func (v *VideoWatermark) CreateWaterMarkExtraction(c *gin.Context) {
	logCtx := context.WithValue(context.TODO(), utils.CtxKeyLogID, utils.RandStringRunes(16))
	req := &proto.GetWaterMarkRequest{}
	if err := c.ShouldBindJSON(req); err != nil {
		c.JSON(http.StatusOK, proto.NewCommRsp(100001, "参数异常"))
		return
	}

	fileId := req.FileId
	w1 := model.Watermark{}
	w2, err := w1.GetWatermarkFromFileId(gomysql.DB, fileId)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"GetWaterMarkResult GetWatermarkFromFileId fileID: %s error: %+v \n", fileId, err)
		c.JSON(http.StatusOK, proto.NewCommRsp(100002, "查询任务失败"))
		return
	}

	if w2.Status == enum.TaskFailed {
		logger.Log.Errorf(utils.MMark(logCtx)+"GetWaterMarkResult status is failed fileID: %s  error: %+v \n", fileId, err)
		c.JSON(http.StatusOK, proto.NewCommRsp(100003, "水印视频制作失败"))
		return
	}

	targetKeydecoded, err := url.QueryUnescape(w2.TargetKey)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"CreateWaterMarkExtraction uncode error: %+v fileID: %s targetkey: %s \n", w2.FileId, err, w2.TargetKey)
		c.JSON(http.StatusOK, proto.NewCommRsp(100004, "目标文件名称解码错误"))
		return
	}

	dwmDetect := &api.Dwmdetect{}
	dwmDetect.PipelineName = config.LocalConfig.WatermMark.PipelineName
	dwmDetect.DigitalWmId = config.LocalConfig.WatermMark.DigitalWmId
	dwmDetect.TextWmContent = config.LocalConfig.WatermMark.DigitalWmTextContent
	dwmDetect.DigitalWmType = "text"

	source := &api.DwmSource{}
	source.Bucket = w2.TargetBucket
	source.Key = string(targetKeydecoded)
	dwmDetect.Source = source

	response, err := v.client.CreateDwmDetectJob(dwmDetect)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"create dwm detect job error: %+v", err)
		c.JSON(http.StatusOK, proto.NewCommRsp(100004, "创建任务失败"))
		return
	}
	logger.Log.Infof(utils.MMark(logCtx)+"create dwm detect job success: %+v", response)
	c.JSON(http.StatusOK, proto.NewSuccessRsp(response))
}

func (v *VideoWatermark) GetWaterMarkExtractionResult(c *gin.Context) {
	req := &proto.GetWaterMarkExtractionRequest{}
	if err := c.ShouldBindJSON(req); err != nil {
		c.JSON(http.StatusOK, proto.NewCommRsp(100001, "参数异常"))
		return
	}

	responsePull, err := v.client.GetDwmdetectResult(req.JobId)
	if err != nil {
		logger.Log.Errorf("GetWaterMarkResult GetDwmdetectResult JobId: %s error: %+v \n", req.JobId, err)
		c.JSON(http.StatusOK, proto.NewCommRsp(100003, "获取水印结果失败"))
		return
	}

	logger.Log.Infof("GetDwmdetectResult success: %+v", responsePull)
	c.JSON(http.StatusOK, proto.NewSuccessRsp(responsePull))
}

func (v *VideoWatermark) TestCallback(c *gin.Context) {
	req := &proto.JobStatusInfo{}
	if err := c.ShouldBindJSON(req); err != nil {
		c.JSON(http.StatusOK, proto.NewCommRsp(100001, "参数异常"))
		return
	}
	logger.Log.Infof("TestCallback req: %+v", *req)
	c.JSON(http.StatusOK, proto.NewSuccessServiceRsp(req))
}

func SendCallbackRequest(callbackurl string, info *proto.JobStatusInfo) error {
	logCtx := context.WithValue(context.TODO(), utils.CtxKeyLogID, info.FileId)
	logger.Log.Infof(utils.MMark(logCtx)+"SendCallbackRequse request: %+v\n", info)
	// 序列化请求数据
	jsonData, err := json.Marshal(info)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"SendCallbackRequse Marshal request failed: %+v\n", err)
		return err
	}

	// 创建请求对象
	retclient := retryhttpclient.NewRetryHTTPClient(15*time.Second, 3)
	header := make(map[string]string)
	header["Content-Type"] = "application/json"

	resp, err := retclient.DoRequest(logCtx, "POST", callbackurl, header, bytes.NewBuffer(jsonData))
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+utils.ThirdPartyInterfaceEsErrMsg+"SendCallbackRequse send request failed: %+v\n", err)
		return err
	}

	logger.Log.Infof(utils.MMark(logCtx)+"WaterMarkCallback response: %s\n", resp)
	return nil
}

func CreateWatermarkTask(watermark *model.Watermark) (string, error) {
	logCtx := context.WithValue(context.TODO(), utils.CtxKeyLogID, watermark.FileId)

	logger.Log.Infof(utils.MMark(logCtx)+"watermark create job start,info: %v\n", watermark)
	wm := GetVideoWatermark()
	// 使用url.QueryUnescape进行解码
	sourceKeydecoded, err := url.QueryUnescape(watermark.SourceKey)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"QueryUnescape error: %+v\n", err)
		return "", err
	}

	targetKeydecoded, err := url.QueryUnescape(watermark.TargetKey)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"QueryUnescape error: %+v\n", err)
		return "", err
	}

	// 文件 key
	argsJob := &proto.CreateJobArgs{}
	argsJob.PipelineName = config.LocalConfig.WatermMark.PipelineName
	source := &proto.Source{Clips: &[]proto.SourceClip{{
		Bucket:    watermark.SourceBucket,
		SourceKey: string(sourceKeydecoded)}}}

	argsJob.Source = source

	target := &proto.Target{}
	target.TargetKey = string(targetKeydecoded)

	fileFormat := path.Ext(string(sourceKeydecoded))
	if fileFormat == ".webm" {
		target.PresetName = config.LocalConfig.WatermMark.PresetNameWebm
	} else {
		target.PresetName = config.LocalConfig.WatermMark.PresetName
	}

	target.DigitalWmTextContent = config.LocalConfig.WatermMark.DigitalWmTextContent
	target.JobCfg = &proto.JobCfg{
		Notification: config.LocalConfig.WatermMark.Notification,
	}

	target.DigitalWmAlgVersion = 1
	target.DigitalWmStrength = 0.5
	argsJob.Target = target

	logger.Log.Infof(utils.MMark(logCtx)+"watermark CreateJobCustomize args, sorcekey: %v targetkey: %v\n", sourceKeydecoded, targetKeydecoded)

	jobResponse, err := CreateJobCustomize(wm.client, argsJob) //wm.client.CreateJobCustomize(argsJob) //
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+utils.ThirdPartyInterfaceEsErrMsg+"watermark create job failed,info: %v\n", watermark)
		return "", err
	}

	logger.Log.Infof(utils.MMark(logCtx)+"watermark create job end success,jobID: %v\n", jobResponse.JobId)
	return jobResponse.JobId, nil
}

func QueryJobStatusFromDB() {
	watermarkList, err := (&model.Watermark{}).GetTasksWithStatus(gomysql.DB, enum.TaskRunning)
	if err != nil {
		logger.Log.Errorf("QueryJobStatusFromDB GetAndUpdateStatusToRunning error: %+v\n", err)
		return
	}

	for _, watermark := range watermarkList {
		QueryJobStatusFromWaterMark(watermark)
		// 1s 查询一次,降低数据库访问压力
		time.Sleep(1 * time.Second)
	}
}

func QueryJobStatusFromWaterMark(watermark *model.Watermark) {
	logCtx := context.WithValue(context.TODO(), utils.CtxKeyLogID, watermark.FileId)

	wm := GetVideoWatermark()
	redisProxy := redisproxy.GetRedisProxy()

	info := &proto.JobStatusInfo{
		Code:      "",
		ErrMsg:    "",
		FileId:    watermark.FileId,
		JobId:     watermark.JobId,
		JobStatus: "",
	}

	// 获取锁
	redisLockKey := fmt.Sprintf(WatermarkKeyFmt, handlerUtils.GetNameByRunEnv(), watermark.JobId+"_lock")
	redisLock := redislock.NewRedisLock(redisProxy.Rdb, redisLockKey, WatermarkLockTimeDuration)

	ok, err := redisLock.Lock(context.Background())
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+utils.RedisEsErrMsg+"QueryJobStatusFromWaterMark Lock key: %s error: %+v\n", redisLockKey, err)
		return
	} else if !ok {
		//logger.Log.Warnf(utils.MMark(logCtx)+"QueryJobStatusFromWaterMark Lock is occupied key: %+s \n", redisLockKey)
		return
	}

	// 完成任务后释放锁
	defer redisLock.Unlock(context.Background())

	// 查询缓存中的任务状态
	statusRedisKey := fmt.Sprintf(WatermarkKeyFmt, handlerUtils.GetNameByRunEnv(), watermark.JobId+"_status")
	status, err := redisProxy.GetKeyValue(statusRedisKey)
	if err != nil {
		if string(err.Error()) != string(redis.Nil) {
			logger.Log.Errorf(utils.MMark(logCtx)+"QueryJobStatusFromWaterMark key %s task status error: %+v\n", statusRedisKey, err)
			return
		}
		wmtmp, err := watermark.GetWatermarkFromFileIdAndBosKey(gomysql.DB, watermark.FileId, watermark.SourceKey)
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+"QueryJobStatusFromWaterMark key %s GetWatermarkFromFileId error: %+v\n", statusRedisKey, err)
			return
		}
		// 更新 watermark
		watermark = wmtmp

		err = redisProxy.SetKeyValue(statusRedisKey, string(wmtmp.Status), WatermarkStatusTimeDuration)
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+"QueryJobStatusFromWaterMark key %s SetKeyValue error: %+v\n", statusRedisKey, err)
			return
		}
		status = string(wmtmp.Status)
		// 任务状态不存在，还继续查询任务状态，最后将结果写入redis，防止其他服务重复查询并写入数据库
		logger.Log.Warnf(utils.MMark(logCtx)+"QueryJobStatusFromWaterMark key %s task status is nil set status %s : %+v\n", statusRedisKey, status, err)
	}

	// 任务已经处理过，跳过
	if status == string(enum.TaskSucceed) || status == string(enum.TaskFailed) {
		//logger.Log.Infof(utils.MMark(logCtx)+"QueryJobStatusFromWaterMark key %s task status is %s, skip\n", statusRedisKey, status)
		return
	}

	// 更新缓存任务状态
	defer func() {
		if err = redisProxy.SetKeyValue(statusRedisKey, string(watermark.Status), WatermarkStatusTimeDuration); err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+utils.RedisEsErrMsg+"SetKeyValue key %s error: %+v\n", statusRedisKey, err)
			return
		}
	}()

	// 查询任务状态
	getTranscodingJobResponse, err := wm.client.GetTranscodingJob(watermark.JobId)
	if getTranscodingJobResponse == nil || err != nil {
		if strings.Contains(err.Error(), "VideoExceptions.NoSuchJob") {
			nowtime := time.Now()
			second := nowtime.Sub(watermark.CreatedAt).Milliseconds() / 1000
			if second < 30 {
				logger.Log.Errorf(utils.MMark(logCtx)+"GetTranscodingJob JobID %s error: %+v\n", watermark.JobId, err)
				return
			}
		}
		logger.Log.Errorf(utils.MMark(logCtx)+"GetTranscodingJob JobID %s error: %+v\n", watermark.JobId, err)
		info.Code = "100002"
		info.ErrMsg = "查询任务状态失败"
		info.JobStatus = "FAILED"

		watermark.Status = enum.TaskFailed
		watermark.UpdatedAt = time.Now()

		err = SendCallbackRequest(watermark.CallbackUrl, info)
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+"SendCallbackRequse JobID %s error: %+v\n", watermark.JobId, err)
		}

		if err = watermark.UpdateWatermark(gomysql.DB); err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+utils.MysqlEsErrMsg+"UpdateWatermark JobID %s error: %+v\n", watermark.JobId, err)
		}

		logger.Log.Errorf(utils.MMark(logCtx)+"watermark query end status: %v\n", watermark)
		return
	}

	if getTranscodingJobResponse.JobStatus == "SUCCESS" || getTranscodingJobResponse.JobStatus == "FAILED" {
		logger.Log.Infof(utils.MMark(logCtx)+"JobID %s getTranscodingJobResponse: %+v\n", watermark.JobId, getTranscodingJobResponse)
		if strings.Contains(getTranscodingJobResponse.Error.Code, "JobOverTime") {
			logger.Log.Errorf(utils.MMark(logCtx)+"JobOverTime JobID %s getTranscodingJobResponse: %+v\n", watermark.JobId, getTranscodingJobResponse)
			jobid, err := CreateWatermarkTask(watermark)
			if err != nil {
				logger.Log.Errorf(utils.MMark(logCtx)+"CreateWatermarkTask JobID %s error: %+v\n", jobid, err)
			} else {
				watermark.JobId = jobid
				watermark.UpdatedAt = time.Now()
				err = watermark.UpdateWatermark(gomysql.DB)
				if err != nil {
					logger.Log.Errorf(utils.MMark(logCtx)+utils.ThirdPartyInterfaceEsErrMsg+"UpdateWatermark JobID %s error: %+v\n", jobid, err)
					return
				}
				logger.Log.Infof(utils.MMark(logCtx)+"watermark query end status: %v\n", watermark)
				return
			}
		}
		const layout = "2006-01-02T15:04:05Z"
		st, _ := time.Parse(layout, getTranscodingJobResponse.StartTime)
		se, _ := time.Parse(layout, getTranscodingJobResponse.EndTime)

		info.Code = getTranscodingJobResponse.Error.Code
		info.ErrMsg = getTranscodingJobResponse.Error.Message
		info.JobStatus = getTranscodingJobResponse.JobStatus
		info.StartTime = getTranscodingJobResponse.StartTime
		info.EndTime = getTranscodingJobResponse.EndTime
		info.CreateTime = watermark.CreatedAt.Format("2006-01-02T15:04:05Z")
		info.FileUrl = fmt.Sprintf(WatermarkBosHostFmt, watermark.TargetBucket, watermark.TargetKey)
		info.Duration = int(se.Sub(st).Seconds())

		watermark.StartTime = st
		watermark.EndTime = se

		if getTranscodingJobResponse.JobStatus == "SUCCESS" {
			watermark.Status = enum.TaskSucceed
		} else {
			watermark.Status = enum.TaskFailed
		}

		watermark.UpdatedAt = time.Now()

		err = SendCallbackRequest(watermark.CallbackUrl, info)
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+"SendCallbackRequse JobID %s error: %+v\n", watermark.JobId, err)
			watermark.Status = enum.TaskFailed
		}

		if err = watermark.UpdateWatermark(gomysql.DB); err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+utils.MysqlEsErrMsg+"UpdateWatermark JobID %s error: %+v\n", watermark.JobId, err)
		}

		logger.Log.Infof(utils.MMark(logCtx)+"watermark query end status: %v\n", watermark)
		return
	}
}

func CreateJobCustomize(cli bce.Client, args *proto.CreateJobArgs) (*api.CreateJobResponse, error) {
	req := &bce.BceRequest{}
	req.SetUri("/v3/job/transcoding")
	req.SetMethod("POST")
	req.SetHeader("Content-Type", bce.DEFAULT_CONTENT_TYPE)

	jsonBytes, jsonErr := json.Marshal(args)
	if jsonErr != nil {
		return nil, jsonErr
	}
	body, jsonErr := bce.NewBodyFromBytes(jsonBytes)
	if jsonErr != nil {
		return nil, jsonErr
	}
	req.SetBody(body)
	resp := &bce.BceResponse{}
	if err := cli.SendRequest(req, resp); err != nil {
		return nil, err
	}
	if resp.IsFail() {
		return nil, resp.ServiceError()
	}

	result := &api.CreateJobResponse{}
	if err := resp.ParseJsonBody(result); err != nil {
		return nil, err
	}
	defer func() { resp.Body().Close() }()
	return result, nil
}
