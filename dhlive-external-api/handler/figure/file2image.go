package figure

import (
	"acg-ai-go-common/gomysql"
	"acg-ai-go-common/logger"
	"acg-ai-go-common/utils"
	"acg-ai-go-common/utils/redisproxy"
	"bytes"
	"context"
	"dhlive-external-api/beans/enum"
	"dhlive-external-api/beans/model"
	"dhlive-external-api/beans/proto"
	"dhlive-external-api/handler/handlerUtils"
	"dhlive-external-api/handler/handlerUtils/redislock"
	"encoding/json"
	"errors"
	"fmt"
	"image"
	"image/jpeg"
	_ "image/png" // 导入image/png包以支持PNG格式
	"io"
	"net/http"
	"os"
	"path"
	"path/filepath"
	"time"

	"github.com/baidubce/bce-sdk-go/bce"
	"github.com/baidubce/bce-sdk-go/services/bos"
	"github.com/baidubce/bce-sdk-go/services/bos/api" //导入STS服务模块
	"github.com/gin-gonic/gin"
	"github.com/go-redis/redis/v8"
	"github.com/google/uuid"
)

const (
	File2ImageKeyPre              = "File2ImageKey"                  // 用户归属资源集合前缀
	File2ImageKeyFmt              = "%v:" + File2ImageKeyPre + ":%v" // 先填写环境ID，在添加唯一ID，组合起来是一个redis key
	File2ImageStateKeyExpire      = 1 * time.Hour
	File2ImageKeyLockTimeDuration = 10 * time.Minute

	FileSizeLimitMax = 50 * 1024 * 1024 // 文件大小限制
	FilePageLimitMax = 50               // 文件页数限制
)

type File2Image struct {
	proxy *redisproxy.RedisProxy
}

func NewFile2Image() *File2Image {
	return &File2Image{
		proxy: redisproxy.GetRedisProxy(),
	}
}

func (f *File2Image) CheckFile(c *gin.Context) {
	newtaskid := "fid-" + utils.RandStringRunes(16)
	logCtx := context.WithValue(context.TODO(), utils.CtxKeyLogID, newtaskid)
	accountID := "测试用户"
	aid, ok := c.Get("AccountId")
	if ok {
		accountID = aid.(string)
	}

	if c.Request.Header.Get("Content-Type") == "multipart/form-data" {
		logger.Log.Errorf(utils.MMark(logCtx) + "CheckFile get Header faild")
		c.JSON(http.StatusOK, proto.NewCommDataRsp(100001, "参数异常", &proto.CommResponse{RequestId: utils.MMark(logCtx)}))
		return
	}

	// 通过 FormFile 获取文件
	reqfile, header, err := c.Request.FormFile("filedata") // "file" 是表单中文件输入的名称
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"CheckFile FormFile fail, err:%v", err)
		c.JSON(http.StatusOK, proto.NewCommDataRsp(100002, "请检查文件", &proto.CommResponse{RequestId: utils.MMark(logCtx)}))
		return
	}
	defer reqfile.Close()

	// 获取文件后缀名
	fileFormat := path.Ext(header.Filename)

	if err = os.MkdirAll(DownloadFilePrefix, 0755); err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"CheckFile os.MkdirAll fail, err:%v", err)
		c.JSON(http.StatusOK, proto.NewCommDataRsp(100003, "创建文件目录失败", &proto.CommResponse{RequestId: utils.MMark(logCtx)}))
		return
	}

	downfilepath := DownloadFilePrefix + uuid.New().String() + fileFormat
	// 打开（或创建）文件用于写入
	outFile, err := os.Create(downfilepath)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"Failed to create output file, err:%v", err)
		c.JSON(http.StatusOK, proto.NewCommDataRsp(100004, "写入文件失败", &proto.CommResponse{RequestId: utils.MMark(logCtx)}))
		return
	}
	defer outFile.Close()
	defer os.Remove(downfilepath)

	// 将请求中的文件内容复制到新文件中
	_, err = io.Copy(outFile, reqfile)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"Failed to copy file content, err:%v", err)
		c.JSON(http.StatusOK, proto.NewCommDataRsp(100005, "文件复制失败", &proto.CommResponse{RequestId: utils.MMark(logCtx)}))
		return
	}

	md5, err := handlerUtils.CalculateMD5FromFile(downfilepath)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"CheckFile CalculateMD5 fail, err:%v", err)
		c.JSON(http.StatusOK, proto.NewCommDataRsp(100006, "获取文件MD5失败", &proto.CommResponse{RequestId: utils.MMark(logCtx)}))
		return
	}

	// 通过md5 查询数据库
	file2image := &model.File2Image{}
	f2i, err := file2image.GetFile2ImageFromMd5(gomysql.DB, md5)
	if err == nil {
		if f2i.ID != 0 {
			// 保存 accountID 记录
			err = saveAccountIdRecord(logCtx, f2i.TaskId, accountID, f2i.Md5)
			if err != nil {
				logger.Log.Errorf(utils.MMark(logCtx)+utils.MysqlEsErrMsg+"taskid %s CheckFile saveAccountIdRecord fail, err:%v", f2i.TaskId, err)
				c.JSON(http.StatusOK, proto.NewCommDataRsp(100007, "用户保存访问记录失败", &proto.CommResponse{RequestId: utils.MMark(logCtx)}))
				return
			}

			c.JSON(http.StatusOK, proto.NewSuccessRsp(&proto.FileFormatCheckResponse{
				FileID:                 f2i.TaskId,
				FileFormatIsSupported:  f2i.FileFormatSupported,
				FileSizeIsWithinLimit:  f2i.FileSizeWithinLimit,
				PageCountIsWithinLimit: f2i.PageCountWithinLimit,
				RequestId:              utils.GetLogID(logCtx),
			}))
			return
		}
	}

	fileFormatIsSupported := handlerUtils.CheckFileFormat(logCtx, fileFormat, downfilepath)
	fileSizeIsWithinLimit := header.Size <= FileSizeLimitMax

	if !fileFormatIsSupported {
		c.JSON(http.StatusOK, proto.NewSuccessRsp(&proto.FileFormatCheckResponse{
			FileFormatIsSupported:  fileFormatIsSupported,
			FileSizeIsWithinLimit:  fileSizeIsWithinLimit,
			PageCountIsWithinLimit: false,
			RequestId:              utils.GetLogID(logCtx),
		}))
		return
	}

	pageCountIsWithinLimit, err := handlerUtils.CheckPageCount(logCtx, fileFormat, downfilepath, FilePageLimitMax)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"CheckFile CheckPageCount fail, err:%v", err)
		c.JSON(http.StatusOK, proto.NewCommDataRsp(100008, "文件计算页数失败", &proto.CommResponse{RequestId: utils.MMark(logCtx)}))
		return
	}

	// 文件格式是否支持，文件大小是否在限制内，页数是否在限制内
	if !fileSizeIsWithinLimit || !pageCountIsWithinLimit {
		c.JSON(http.StatusOK, proto.NewSuccessRsp(&proto.FileFormatCheckResponse{
			FileFormatIsSupported:  fileFormatIsSupported,
			FileSizeIsWithinLimit:  fileSizeIsWithinLimit,
			PageCountIsWithinLimit: pageCountIsWithinLimit,
			RequestId:              utils.GetLogID(logCtx),
		}))
		return
	}

	// 文件上传到 BOS
	url, err := RetryUploadBosServiceFromFile(logCtx, FigureFile2ImagePath, downfilepath, fileFormat)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+utils.ThirdPartyInterfaceEsErrMsg+"CheckFile RetryUploadBosService fail, err:%v", err)
		c.JSON(http.StatusOK, proto.NewCommDataRsp(100009, "上传BOS失败", &proto.CommResponse{RequestId: utils.MMark(logCtx)}))
		return
	}

	// 插入数据库
	file2image = &model.File2Image{
		AccountId:            accountID,
		TaskId:               newtaskid,
		FileUrl:              url,
		Md5:                  md5,
		FileFormatSupported:  fileFormatIsSupported,
		FileSizeWithinLimit:  fileSizeIsWithinLimit,
		PageCountWithinLimit: pageCountIsWithinLimit,
		Status:               enum.TaskReady,
		CreatedAt:            time.Now(),
		UpdatedAt:            time.Now(),
	}

	if err := file2image.CreateFile2Image(gomysql.DB); err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+utils.MysqlEsErrMsg+"CheckFile CalculateMD5 fail, err:%v", err)
		c.JSON(http.StatusOK, proto.NewCommDataRsp(200001, "数据库写入失败", &proto.CommResponse{RequestId: utils.MMark(logCtx)}))
		return
	}

	// 保存 accountID 记录
	err = saveAccountIdRecord(logCtx, file2image.TaskId, accountID, file2image.Md5)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+utils.MysqlEsErrMsg+"CheckFile saveAccountIdRecord fail, err:%v", err)
		c.JSON(http.StatusOK, proto.NewCommDataRsp(200002, "用户保存访问记录失败", &proto.CommResponse{RequestId: utils.MMark(logCtx)}))
		return
	}

	c.JSON(http.StatusOK, proto.NewSuccessRsp(&proto.FileFormatCheckResponse{
		FileID:                 file2image.TaskId,
		FileFormatIsSupported:  fileFormatIsSupported,
		FileSizeIsWithinLimit:  fileSizeIsWithinLimit,
		PageCountIsWithinLimit: pageCountIsWithinLimit,
		RequestId:              utils.GetLogID(logCtx),
	}))
}

func (f *File2Image) HandlerRequest(c *gin.Context) {
	logCtx := context.WithValue(context.TODO(), utils.CtxKeyLogID, utils.RandStringRunes(16))
	req := &proto.File2ImageRequest{}
	if err := c.ShouldBindJSON(req); err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"HandlerRequest ShouldBindJSON err:%v\n", err)
		c.JSON(http.StatusOK, proto.NewCommDataRsp(100001, "参数异常", &proto.CommResponse{RequestId: utils.GetLogID(logCtx)}))
		return
	}
	logger.Log.Infof(utils.MMark(logCtx)+"HandlerRequest req:%+v", req)

	if len(req.FileID) == 0 {
		logger.Log.Errorf(utils.MMark(logCtx) + "HandlerRequest FileID is null")
		c.JSON(http.StatusOK, proto.NewCommDataRsp(100002, "请传入文件ID", &proto.CommResponse{RequestId: utils.GetLogID(logCtx)}))
		return
	}
	logCtx = context.WithValue(context.TODO(), utils.CtxKeyLogID, req.FileID)

	// 查询数据库
	f2i := &model.File2Image{}
	file2image, err := f2i.GetFile2ImageFromTaskId(gomysql.DB, req.FileID)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+utils.MysqlEsErrMsg+"HandlerRequest GetFile2Image fail, err:%v", err)
		c.JSON(http.StatusOK, proto.NewCommDataRsp(100003, "数据库查询失败", &proto.CommResponse{RequestId: utils.GetLogID(logCtx)}))
		return
	}

	if file2image.Status == enum.TaskSucceed {
		c.JSON(http.StatusOK, proto.NewSuccessRsp(&proto.CommResponse{RequestId: utils.GetLogID(logCtx)}))
		return
	}

	if file2image.Status == enum.TaskRunning {
		logger.Log.Errorf(utils.MMark(logCtx) + "HandlerRequest task running")
		c.JSON(http.StatusOK, proto.NewCommDataRsp(100005, "任务正在处理，请稍候再试", &proto.CommResponse{RequestId: utils.GetLogID(logCtx)}))
		return
	}

	// 更新缓存
	redisKey := fmt.Sprintf(File2ImageKeyFmt, handlerUtils.GetNameByRunEnv(), req.FileID)
	err = f.proxy.SetKeyValue(redisKey, string(enum.TaskRunning), File2ImageStateKeyExpire)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+utils.RedisEsErrMsg+"HandlerRequest ID:%d SetKeyValue fail, err:%v", req.FileID, err)
		// c.JSON(http.StatusOK, proto.NewCommDataRsp(100006, "缓存更新失败", &proto.CommResponse{RequestId:  utils.GetLogID(logCtx)}))
		// return
	}

	// 更新数据库
	file2image.Status = enum.TaskRunning
	err = file2image.UpdateFile2Image(gomysql.DB)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+utils.MysqlEsErrMsg+"HandlerRequest UpdateFile2Image fail, err:%v", err)
		c.JSON(http.StatusOK, proto.NewCommDataRsp(100007, "数据库更新失败", &proto.CommResponse{RequestId: utils.GetLogID(logCtx)}))
		return
	}

	c.JSON(http.StatusOK, proto.NewSuccessRsp(&proto.CommResponse{RequestId: utils.GetLogID(logCtx)}))
}

func (f *File2Image) GetParseResult(c *gin.Context) {
	logCtx := context.WithValue(context.TODO(), utils.CtxKeyLogID, utils.RandStringRunes(16))
	accountID := "测试用户"
	aid, ok := c.Get("AccountId")
	if ok {
		accountID = aid.(string)
	}

	req := &proto.File2ImageRequest{}
	if err := c.ShouldBindJSON(req); err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"GetParseResult ShouldBindJSON err:%v\n", err)
		c.JSON(http.StatusOK, proto.NewCommDataRsp(100001, "参数异常", &proto.CommResponse{RequestId: utils.GetLogID(logCtx)}))
		return
	}
	logCtx = context.WithValue(context.TODO(), utils.CtxKeyLogID, req.FileID)

	f2i := &model.File2Image{}
	f2Image, err := f2i.GetFile2ImageFromTaskId(gomysql.DB, req.FileID)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+utils.MysqlEsErrMsg+"GetParseResult ID:%d GetFile2Image fail, err:%v", req.FileID, err)
		c.JSON(http.StatusOK, proto.NewCommDataRsp(100002, "数据库查询失败", &proto.CommResponse{RequestId: utils.GetLogID(logCtx)}))
		return
	}

	ok, err = checkAccountId(logCtx, f2Image.TaskId, accountID)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"GetParseResult ID:%s checkAccountId fail, err:%v", req.FileID, err)
		c.JSON(http.StatusOK, proto.NewCommDataRsp(100003, "用户校验失败", &proto.CommResponse{RequestId: utils.GetLogID(logCtx)}))
		return
	}

	if !ok {
		logger.Log.Errorf(utils.MMark(logCtx)+"GetParseResult ID:%s checkAccountId accountid not found accountid:%s", req.FileID, accountID)
		c.JSON(http.StatusOK, proto.NewCommDataRsp(100004, "用户校验失败", &proto.CommResponse{RequestId: utils.GetLogID(logCtx)}))
		return
	}

	if f2Image.Status == enum.TaskFailed {
		logger.Log.Errorf(utils.MMark(logCtx)+"GetParseResult ID:%s task failed", req.FileID)
		c.JSON(http.StatusOK, proto.NewCommDataRsp(100005, "解析文件失败", &proto.CommResponse{RequestId: utils.GetLogID(logCtx)}))
		return
	}

	c.JSON(http.StatusOK, proto.NewSuccessRsp(&proto.File2ImageResponse{
		PageCount:  f2Image.PageCount,
		ImageInfos: f2Image.ImageInfos,
		Status:     f2Image.Status,
		RequestId:  utils.GetLogID(logCtx),
	}))
}

func (f *File2Image) StopParseResult(c *gin.Context) {
	logCtx := context.WithValue(context.TODO(), utils.CtxKeyLogID, utils.RandStringRunes(16))
	accountID := "测试用户"
	aid, ok := c.Get("AccountId")
	if ok {
		accountID = aid.(string)
	}

	req := &proto.File2ImageRequest{}
	if err := c.ShouldBindJSON(req); err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"StopParseResult ShouldBindJSON err:%v\n", err)
		c.JSON(http.StatusOK, proto.NewCommDataRsp(100001, "参数异常", &proto.CommResponse{RequestId: utils.GetLogID(logCtx)}))
		return
	}

	logCtx = context.WithValue(context.TODO(), utils.CtxKeyLogID, req.FileID)

	f2i := &model.File2Image{}
	f2Image, err := f2i.GetFile2ImageFromTaskId(gomysql.DB, req.FileID)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+utils.MysqlEsErrMsg+"StopParseResult ID:%d GetFile2Image fail, err:%v", req.FileID, err)
		c.JSON(http.StatusOK, proto.NewCommDataRsp(100002, "数据库查询失败", &proto.CommResponse{RequestId: utils.GetLogID(logCtx)}))
		return
	}

	ok, err = checkAccountId(logCtx, f2Image.TaskId, accountID)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"StopParseResult ID:%d checkAccountId fail, err:%v", req.FileID, err)
		c.JSON(http.StatusOK, proto.NewCommDataRsp(100003, "用户校验失败", &proto.CommResponse{RequestId: utils.GetLogID(logCtx)}))
		return
	}

	if !ok {
		logger.Log.Errorf(utils.MMark(logCtx)+"StopParseResult ID:%d checkAccountId accountid not found accountid:%s", req.FileID, accountID)
		c.JSON(http.StatusOK, proto.NewCommDataRsp(100004, "用户校验失败", &proto.CommResponse{RequestId: utils.GetLogID(logCtx)}))
		return
	}

	// 更新缓存
	redisKey := fmt.Sprintf(File2ImageKeyFmt, handlerUtils.GetNameByRunEnv(), req.FileID)
	err = f.proxy.SetKeyValue(redisKey, string(enum.TaskStop), File2ImageStateKeyExpire)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+utils.RedisEsErrMsg+"StopParseResult ID:%d SetKeyValue fail, err:%v", req.FileID, err)
		c.JSON(http.StatusOK, proto.NewCommDataRsp(100006, "缓存更新失败", &proto.CommResponse{RequestId: utils.GetLogID(logCtx)}))
		return
	}

	if f2Image.Status == enum.TaskSucceed || f2Image.Status == enum.TaskFailed {
		c.JSON(http.StatusOK, proto.NewSuccessRsp(&proto.CommResponse{RequestId: utils.GetLogID(logCtx)}))
		return
	}

	// 停止任务 更新数据库
	f2Image.Status = enum.TaskStop
	f2Image.CreatedAt = time.Now()
	f2Image.UpdatedAt = f2Image.CreatedAt
	err = f2Image.UpdateFile2Image(gomysql.DB)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+utils.MysqlEsErrMsg+"StopParseResult ID:%d UpdateFile2Image fail, err:%v", req.FileID, err)
		c.JSON(http.StatusOK, proto.NewCommDataRsp(100007, "数据库更新失败", &proto.CommResponse{RequestId: utils.GetLogID(logCtx)}))
		return
	}

	c.JSON(http.StatusOK, proto.NewSuccessRsp(&proto.CommResponse{RequestId: utils.GetLogID(logCtx)}))
}

func (f *File2Image) SetDraftId(c *gin.Context) {
	logCtx := context.WithValue(context.TODO(), utils.CtxKeyLogID, utils.RandStringRunes(16))
	accountID := "测试用户"
	aid, ok := c.Get("AccountId")
	if ok {
		accountID = aid.(string)
	}

	req := &proto.File2ImageSetDraftIdRequest{}
	if err := c.ShouldBindJSON(req); err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"SetDraftId ShouldBindJSON err:%v\n", err)
		c.JSON(http.StatusOK, proto.NewCommDataRsp(100001, "参数异常", &proto.CommResponse{RequestId: utils.GetLogID(logCtx)}))
		return
	}

	logCtx = context.WithValue(context.TODO(), utils.CtxKeyLogID, req.FileID)

	f2i := &model.File2Image{}
	f2Image, err := f2i.GetFile2ImageFromTaskId(gomysql.DB, req.FileID)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+utils.MysqlEsErrMsg+"SetDraftId ID:%d GetFile2Image fail, err:%v", req.FileID, err)
		c.JSON(http.StatusOK, proto.NewCommDataRsp(100002, "数据库查询失败", &proto.CommResponse{RequestId: utils.GetLogID(logCtx)}))
		return
	}

	ok, err = checkAccountId(logCtx, f2Image.TaskId, accountID)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"GetParseResult ID:%d checkAccountId fail, err:%v", req.FileID, err)
		c.JSON(http.StatusOK, proto.NewCommDataRsp(100003, "用户校验失败", &proto.CommResponse{RequestId: utils.GetLogID(logCtx)}))
		return
	}

	if !ok {
		logger.Log.Errorf(utils.MMark(logCtx)+"GetParseResult ID:%d checkAccountId accountid not found accountid:%s", req.FileID, accountID)
		c.JSON(http.StatusOK, proto.NewCommDataRsp(100004, "用户校验失败", &proto.CommResponse{RequestId: utils.GetLogID(logCtx)}))
		return
	}

	draftitem := model.File2ImageDraftId{}
	_, err = draftitem.GetFile2ImageDraftIdFromDraftId(gomysql.DB, req.DraftId, f2Image.TaskId, accountID)
	if err == nil {
		c.JSON(http.StatusOK, proto.NewSuccessRsp(&proto.CommResponse{RequestId: utils.GetLogID(logCtx)}))
		return
	}

	// 未找到则插入
	draftitem = model.File2ImageDraftId{
		DraftId:   req.DraftId,
		TaskId:    f2Image.TaskId,
		AccountID: accountID,
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	err = draftitem.UpdateFile2ImageDraftId(gomysql.DB)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+utils.MysqlEsErrMsg+"SetDraftId ID:%d UpdateFile2ImageDraftId fail, err:%v", req.FileID, err)
		c.JSON(http.StatusOK, proto.NewCommDataRsp(100007, "数据库更新失败", &proto.CommResponse{RequestId: utils.GetLogID(logCtx)}))
		return
	}

	c.JSON(http.StatusOK, proto.NewSuccessRsp(&proto.CommResponse{RequestId: utils.GetLogID(logCtx)}))
}

func FileToImageFromDB() {
	file2ImageList, err := (&model.File2Image{}).GetTasksWithStatus(gomysql.DB, enum.TaskRunning)
	if err != nil {
		logger.Log.Errorf("FileToImageFromDB GetAndUpdateStatusToRunning error: %+v\n", err)
		return
	}

	for _, v := range file2ImageList {
		convertToImage(v)
	}
}

func convertToImage(f2i *model.File2Image) {
	logCtx := context.WithValue(context.TODO(), utils.CtxKeyLogID, f2i.TaskId)
	redisproxy := redisproxy.GetRedisProxy()

	// 获取 redis 分布式锁
	redisLockKey := fmt.Sprintf(File2ImageKeyFmt+"_lock", handlerUtils.GetNameByRunEnv(), f2i.TaskId)
	redisLock := redislock.NewRedisLock(redisproxy.Rdb, redisLockKey, File2ImageKeyLockTimeDuration)
	ok, err := redisLock.Lock(context.Background())
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+utils.RedisEsErrMsg+"convertToImage Lock key: %s error: %+v\n", redisLockKey, err)
		return
	} else if !ok {
		//logger.Log.Warnf(utils.MMark(logCtx)+"convertToImage Lock is occupied key: %+s \n", redisLockKey)
		return
	}

	defer redisLock.Unlock(context.Background())

	// 更新缓存任务状态
	redisKey := fmt.Sprintf(File2ImageKeyFmt, handlerUtils.GetNameByRunEnv(), f2i.TaskId)
	status, err := redisproxy.GetKeyValue(redisKey)
	if err != nil {
		if string(err.Error()) != string(redis.Nil) {
			logger.Log.Errorf(utils.MMark(logCtx)+"convertToImage get task status error: %+v\n", err)
			return
		}

		file2image, err := f2i.GetFile2Image(gomysql.DB, f2i.ID)
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+utils.MysqlEsErrMsg+"convertToImage GetFile2Image err: %v \n", err)
			return
		}
		// 更新 f2i 对象
		f2i = file2image

		err = redisproxy.SetKeyValue(redisKey, string(file2image.Status), File2ImageStateKeyExpire)
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+"convertToImage SetKeyValue err: %v \n", err)
			return
		}

		status = string(file2image.Status)
		// 任务状态不存在，还继续查询任务状态，最后将结果写入redis，防止其他服务重复查询并写入数据库
		logger.Log.Warnf(utils.MMark(logCtx)+"convertToImage get task status is nil set status %s: %+v\n", status, err)
	}

	// 任务已经处理过，跳过
	if status == string(enum.TaskSucceed) || status == string(enum.TaskFailed) {
		//logger.Log.Infof(utils.MMark(logCtx)+"convertToImage task status is %s, skip\n", status)
		return
	}

	logger.Log.Infof(utils.MMark(logCtx)+"convertToImage task status is %s \n", status)
	// 更新任务状态为运行中
	defer func(f2i *model.File2Image) {
		file2image, err := f2i.GetFile2Image(gomysql.DB, f2i.ID)
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+"convertToImage GetFile2Image err: %v \n", err)
			return
		}
		// 如果任务还是运行中，则更新为失败状态
		if file2image.Status == enum.TaskRunning {
			file2image.Status = enum.TaskFailed
			err := file2image.UpdateFile2Image(gomysql.DB)
			if err != nil {
				logger.Log.Errorf(utils.MMark(logCtx)+"convertToImage UpdateFile2Image err: %v \n", err)
			}

			if err := redisproxy.SetKeyValue(redisKey, string(enum.TaskFailed), File2ImageStateKeyExpire); err != nil {
				logger.Log.Errorf(utils.MMark(logCtx)+"convertToImage SetKeyValue err: %v \n", err)
			}
			return
		}

		if err := redisproxy.SetKeyValue(redisKey, string(file2image.Status), File2ImageStateKeyExpire); err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+"convertToImage SetKeyValue err: %v \n", err)
		}
	}(f2i)

	// 下载解析文件
	filekey, err := handlerUtils.ExtractFilenameFromURL(f2i.FileUrl)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"convertToImage ExtractFilenameFromURL err: %v \n", err)
		return
	}
	extension := path.Ext(filekey)

	filename, err := handlerUtils.CreateFileNameFromUrl(DownloadFilePrefix, f2i.FileUrl)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"convertToImage CreateFileNameFromUrl err: %v \n", err)
		return
	}

	pptInfo := proto.PPTFileInfo{}
	// 下载文件,PPT 文件需要下载到本地,转成pdf在本地再进行解析,PDF不需要,读到内存即可,因为PDF文件是直接解析
	if extension == ".pdf" {
		err = downloadBosFileFromPDF(logCtx, filekey, filename)
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+utils.ThirdPartyInterfaceEsErrMsg+"convertToImage downloadBosFile err: %v \n", err)
			return
		}
	} else if extension == ".pptx" {
		filename, pptInfo, err = downloadBosFileFromPPTX(logCtx, filekey)
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+utils.ThirdPartyInterfaceEsErrMsg+"convertToImage downloadBosFileFromPPTX err: %v \n", err)
			return
		}
	} else {
		logger.Log.Errorf(utils.MMark(logCtx)+"not support file type: %s \n", extension)
		return
	}
	defer os.Remove(filename)

	err = ConvertPDFToImages(logCtx, filename, pptInfo, f2i)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"convertToImage ConvertPDFToImages err: %v \n", err)
		return
	}
}

func ConvertPDFToImages(logCtx context.Context, filename string, pptInfo proto.PPTFileInfo, f2i *model.File2Image) error {
	redisproxy := redisproxy.GetRedisProxy()
	startTime := time.Now()
	logger.Log.Infof(utils.MMark(logCtx)+"ConvertPDFToImages start time: %v \n", startTime)

	if len(filename) == 0 {
		return fmt.Errorf(utils.MMark(logCtx) + "ConvertPDFToImages buffer is empty")
	}
	uuid := uuid.New().String()
	outpath := DownloadFilePrefix + uuid + "_%d.png"
	err := handlerUtils.MudrawToImage(filename, outpath)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+utils.ServiceInternalEsErrMsg+"ConvertPDFToImages  %s ConvertPDFToImage err: %v \n", filename, err)
		// return err
	}

	docNumpage, err := handlerUtils.GetPDFPageCountFromMupdf(filename)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+utils.ServiceInternalEsErrMsg+"ConvertPDFToImages %s GetPDFPageCount err: %v \n", filename, err)
		return err
	}

	// 删除临时文件
	defer func(uuid string) {
		for i := 1; i <= docNumpage; i++ {
			filePath := fmt.Sprintf(DownloadFilePrefix+"%s_%d.png", uuid, i)
			os.Remove(filePath)
		}
	}(uuid)

	isFailed := false
	list := &proto.File2ImageInfoList{}
	redisKey := fmt.Sprintf(File2ImageKeyFmt, handlerUtils.GetNameByRunEnv(), f2i.TaskId)

	notesMap := make(map[int]string)
	for _, note := range pptInfo.Notes {
		notesMap[note.Index] = note.Note
	}
	// 遍历PDF文件，解析每一张图片
	for i := 1; i <= docNumpage; i++ {
		filePath := fmt.Sprintf(DownloadFilePrefix+"%s_%d.png", uuid, i)
		// 打开文件
		file, err := os.Open(filePath)
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+utils.ServiceInternalEsErrMsg+"ConvertPDFToImages Open file failed, image index:%d path: %s, err: %v", i, filePath, err)
			isFailed = true
			continue
		}
		defer file.Close()
		// 解码JPG文件到image.Image对象
		img, _, err := image.Decode(file)
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+utils.ServiceInternalEsErrMsg+"ConvertPDFToImages Decode file failed, image index:%d path: %s, err: %v", i, filePath, err)
			isFailed = true
			continue
		}

		// // 获取图片的大小
		width := img.Bounds().Dx()
		height := img.Bounds().Dy()

		var buf bytes.Buffer
		// 使用jpeg.Encode将图像编码为JPEG并写入buf
		err = jpeg.Encode(&buf, img, nil) // 第二个参数是*jpeg.Options，这里传递nil使用默认设置
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+utils.ServiceInternalEsErrMsg+"ConvertPDFToImages Get Image failed, image index:%d, err: %v\n", i, err)
			isFailed = true
			continue
		}

		downloadurl, err := RetryUploadBosServiceFromByte(logCtx, buf, ".jpg")
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+utils.ThirdPartyInterfaceEsErrMsg+"ConvertPDFToImages UploadBosServiceFromByte failed, image index:%d, err: %v\n", i, err)
			isFailed = true
			continue
		}

		note, ok := notesMap[i]
		if !ok {
			note = ""
		}

		// 保存图片信息
		list.ImageInfos = append(list.ImageInfos, proto.File2ImageInfo{
			Index:  i,
			Width:  width,
			Height: height,
			Url:    downloadurl,
			Remark: note,
			ErrMsg: "",
		})

		// 查询状态是否被停止
		taskstatus, err := redisproxy.GetKeyValue(redisKey)
		if err != nil {
			// 缓存查询失败，则从数据库中获取
			f2i, err = f2i.GetFile2Image(gomysql.DB, f2i.ID)
			if err != nil {
				logger.Log.Errorf(utils.MMark(logCtx)+utils.MysqlEsErrMsg+"ConvertPDFToImages GetFile2Image failed, err: %v\n", err)
				continue
			}
			// 	// 更新缓存
			redisproxy.SetKeyValue(redisKey, string(f2i.Status), File2ImageStateKeyExpire)
			taskstatus = string(f2i.Status)
		}

		if taskstatus == string(enum.TaskStop) {
			logger.Log.Infof(utils.MMark(logCtx) + "ConvertPDFToImages parse stop ")
			err = updateDbFile2Image(f2i, docNumpage, list, enum.TaskStop)
			if err != nil {
				logger.Log.Errorf(utils.MMark(logCtx)+"ConvertPDFToImages updateDbFile2Image failed, err: %v\n", err)
				return err
			}
			return nil
		}

		err = updateDbFile2Image(f2i, docNumpage, list, enum.TaskRunning)
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+"ConvertPDFToImages updateDbFile2Image failed, err: %v\n", err)
			continue
		}
	}

	durTime := time.Since(startTime)
	logger.Log.Infof(utils.MMark(logCtx)+"ConvertPDFToImages end time:%v ", durTime)
	// 解析失败，更新数据库
	if isFailed {
		err = updateDbFile2Image(f2i, docNumpage, list, enum.TaskFailed)
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+utils.MysqlEsErrMsg+"ConvertPDFToImages updateDbFile2Image failed, err: %v\n", err)
			return err
		}
		return nil
	}

	// 解析成功，更新数据库
	err = updateDbFile2Image(f2i, docNumpage, list, enum.TaskSucceed)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+utils.MysqlEsErrMsg+"ConvertPDFToImages updateDbFile2Image failed, err: %v\n", err)
		return err
	}

	return nil
}

func updateDbFile2Image(f2i *model.File2Image, pageCount int, list *proto.File2ImageInfoList, status enum.TaskStatus) error {
	// 将结构体转换为JSON字节切片
	jsonBytes, err := json.Marshal(list)
	if err != nil {
		logger.Log.Errorf("updateDbFile2Image Error marshaling JSON:%v \n", err)
		return err
	}
	// 将字节切片转换为字符串（如果需要）
	jsonString := string(jsonBytes)

	f2i.PageCount = pageCount
	f2i.ImageInfos = jsonString
	f2i.Status = status
	if err := f2i.UpdateFile2Image(gomysql.DB); err != nil {
		logger.Log.Errorf("updateDbFile2Image UpdateFile2Image failed, err: %v\n", err)
		return err
	}
	return nil
}

func RetryUploadBosServiceFromByte(logCtx context.Context, filebuff bytes.Buffer, fileFormat string) (string, error) {
	for i := 0; i < 3; i++ {
		url, err := UploadBosServiceFromByte(logCtx, filebuff, fileFormat)
		if err == nil {
			return url, nil
		}
		logger.Log.Errorf(utils.MMark(logCtx)+"FigureVideoUpload UploadBosServiceFromByte fail number of retries: %d , err:%v \n", i, err)
		time.Sleep(time.Second * 3)
	}

	return "", errors.New("file upload failure")
}

func UploadBosServiceFromByte(logCtx context.Context, filebuff bytes.Buffer, fileFormat string) (string, error) {
	bosClient, err := bos.NewClient(BosAk, BosSk, BosEndpoint)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"UploadBosService Create BOS client failed: %v\n", err)
		return "", err
	}
	// 生成文件名
	filename := uuid.NewString() + fileFormat
	objectKey := filepath.Join(string(FigureFile2ImagePath), time.Now().Format("2006-01-02"), filename)
	// 初始化分块上传
	initRsp, err := bosClient.InitiateMultipartUpload(BosBucket, objectKey, "image/jpeg", nil)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"UploadBosService BasicInitiateMultipartUpload fail, err:%v", err)
		return "", err
	}

	uploadId := initRsp.UploadId
	partSize := int64(5 * 1024 * 1024) // 每次上传5M
	buffer := make([]byte, partSize)
	var parts []api.UploadInfoType
	partNumber := 1
	for {
		bytesRead, err := filebuff.Read(buffer)
		if err != nil {
			if err == io.EOF {
				break
			}
			logger.Log.Errorf(utils.MMark(logCtx)+"FigureVideoUpload file.Read fail, err:%v \n", err)
			return "", err
		}

		body, err := bce.NewBodyFromBytes(buffer[:bytesRead])
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+"FigureVideoUpload NewBodyFromBytes fail, err:%v \n", err)
			return "", err
		}
		partEtag, err := bosClient.BasicUploadPart(BosBucket, objectKey, uploadId, partNumber, body)
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+"FigureVideoUpload BasicUploadPart fail, err:%v \n", err)
			return "", err
		}
		parts = append(parts, api.UploadInfoType{
			PartNumber: partNumber,
			ETag:       partEtag,
		})
		partNumber++
	}

	_, err = bosClient.CompleteMultipartUploadFromStruct(BosBucket, objectKey, uploadId, &api.CompleteMultipartUploadArgs{
		Parts: parts,
	})
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"FigureVideoUpload CompleteMultipartUploadFromStruct fail, err:%v \n", err)
		return "", err
	}

	downloadUrl := BosCdnHost + objectKey
	return downloadUrl, nil
}

func downloadBosFileFromPDF(logCtx context.Context, filekey, filename string) error {
	// 使用MkdirAll创建一个多级目录
	err := os.MkdirAll(DownloadFilePrefix, 0755)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"downloadBosFileFromPDF %s MkdirAll err:%v\n", DownloadFilePrefix, err)
		return err
	}

	bosClient, err := bos.NewClient(BosAk, BosSk, BosEndpoint)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"downloadBosFile Create BOS client failed: %v\n", err)
		return err
	}

	// 提供Bucket和Object，直接获取一个对象
	err = bosClient.BasicGetObjectToFile(BosBucket, filekey, filename)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"downloadBosFile Get BOS object failed: %v\n", err)
		return err
	}
	return nil
}

func downloadBosFileFromPPTX(logCtx context.Context, filekey string) (string, proto.PPTFileInfo, error) {
	notesMap := proto.PPTFileInfo{}
	bosClient, err := bos.NewClient(BosAk, BosSk, BosEndpoint)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"downloadBosFile Create BOS client failed: %v\n", err)
		return "", notesMap, err
	}
	// 使用MkdirAll创建一个多级目录
	err = os.MkdirAll(DownloadFilePrefix, 0755)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"downloadBosFileFromPPTX %s MkdirAll err:%v\n", DownloadFilePrefix, err)
		return "", notesMap, err
	}

	// 下载ppt文件
	uuid := uuid.New().String()
	extension := path.Ext(filekey)
	tmpPPTFilename := DownloadFilePrefix + uuid + extension
	tmpPDFFilename := DownloadFilePrefix + uuid + ".pdf"
	err = bosClient.BasicGetObjectToFile(BosBucket, filekey, tmpPPTFilename)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"downloadBosFileFromPPT BasicGetObjectToFile failed: %v\n", err)
		return "", notesMap, err
	}
	defer handlerUtils.DeleteFile(tmpPPTFilename)

	// 使用MkdirAll创建一个多级目录
	err = os.MkdirAll("./cache", 0755)
	if err != nil {
		fmt.Println(utils.MMark(logCtx)+"Error creating multi-level directory with MkdirAll:", err)
		return "", notesMap, err
	}
	startTime := time.Now()

	err = handlerUtils.ConvertPPTtoPDF(logCtx, tmpPPTFilename, DownloadFilePrefix)
	if err != nil {
		logger.Log.Warnf(utils.MMark(logCtx)+"downloadBosFileFromPPT ConvertPPTtoPDF failed: %v\n", err)
		err = handlerUtils.ConvertPPTtoPDFByLibreoffice7(logCtx, tmpPPTFilename, DownloadFilePrefix)
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+"downloadBosFileFromPPT ConvertPPTtoPDFByLibreoffice7 failed: %v\n", err)
			return "", notesMap, err
		}
	}

	if ok, err := handlerUtils.FileExists(tmpPDFFilename); err != nil || !ok {
		logger.Log.Warnf(utils.MMark(logCtx)+"downloadBosFileFromPPT ConvertPPTtoPDF failed: %v\n", err)
		err = handlerUtils.ConvertPPTtoPDFByLibreoffice7(logCtx, tmpPPTFilename, DownloadFilePrefix)
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+"downloadBosFileFromPPT ConvertPPTtoPDFByLibreoffice7 failed: %v\n", err)
			return "", notesMap, err
		}
	}

	endTime := time.Now()
	logger.Log.Infof(utils.MMark(logCtx)+"ConvertPPTtoPDF time cost: %v\n", endTime.Sub(startTime).String())

	if ok, err := handlerUtils.FileExists(tmpPDFFilename); err != nil || !ok {
		logger.Log.Errorf(utils.MMark(logCtx)+"downloadBosFileFromPPT FileExists failed, ConvertPPTtoPDFByLibreoffice7 maybe failed: %v\n", err)
		return "", notesMap, err
	}

	// 获取 ppt文件备注信息
	pptInfo, err := handlerUtils.GetPPTPNotes(tmpPPTFilename)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"downloadBosFileFromPPT GetPPTPNotes failed: %v\n", err)
		return "", notesMap, err
	}

	download, err := UploadBosServiceFromFile(logCtx, FigureFile2ImagePath, tmpPDFFilename, ".pdf")
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"downloadBosFileFromPPT UploadBosServiceFromFile failed: %v\n", err)
	}

	logger.Log.Infof(utils.MMark(logCtx)+"downloadBosFileFromPPT UploadBosServiceFromFile PDF download url : %v\n", download)

	return tmpPDFFilename, pptInfo, nil
}

func saveAccountIdRecord(logCtx context.Context, taskid, accountid, md5 string) error {
	faid := model.File2ImageAccountId{}
	_, err := faid.GetFile2ImageAccountIdFromTaskId(gomysql.DB, taskid, accountid)
	if err == nil {
		return nil
	}

	faid = model.File2ImageAccountId{
		TaskId:    taskid,
		AccountID: accountid,
		Md5:       md5,
		LogId:     utils.GetLogID(logCtx),
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	err = faid.UpdateFile2ImageAccountId(gomysql.DB)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"saveAccountIdRecord failed: %v\n", err)
		return err
	}

	logger.Log.Infof(utils.MMark(logCtx) + "saveAccountIdRecord success\n")
	return nil
}

func checkAccountId(logCtx context.Context, taskid, accountid string) (bool, error) {
	faid := model.File2ImageAccountId{}
	_, err := faid.GetFile2ImageAccountIdFromTaskId(gomysql.DB, taskid, accountid)
	if err != nil {
		return false, err
	}
	logger.Log.Infof(utils.MMark(logCtx) + "CheckAccountId success\n")
	return true, nil
}
