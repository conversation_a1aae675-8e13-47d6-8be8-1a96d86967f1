package figure

import (
	"acg-ai-go-common/gomysql"
	"acg-ai-go-common/logger"
	"acg-ai-go-common/utils"
	"acg-ai-go-common/utils/redisproxy"
	"context"
	"dhlive-external-api/beans/enum"
	"dhlive-external-api/beans/model"
	"dhlive-external-api/beans/proto"
	config "dhlive-external-api/conf"
	"dhlive-external-api/handler/handlerUtils"
	"dhlive-external-api/handler/handlerUtils/asynccensorclient"
	"dhlive-external-api/handler/handlerUtils/ffmpegutils"
	"dhlive-external-api/handler/handlerUtils/redislock"
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"os"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	redis "github.com/redis/go-redis/v9"
)

const (
	RiskControlLongAudioQps    = "riskcontrolaudioqps"
	RiskControlLongAudioQpsFmt = "%s:" + RiskControlLongAudioQps + ":xiling-saas-v3"
	RiskControlLongAudioQpsMax = 20 // 最大并发量

	RiskControlLongAudioPullQps    = "riskcontrolaudiopullqps"
	RiskControlLongAudioPullQpsFmt = "%s:" + RiskControlLongAudioPullQps + ":xiling-saas-v3"
	RiskControlLongAudioPullQpsMax = 20 // 最大并发量

	RiskControlLongAudioLock    = "riskcontrolaudiolock"
	RiskControlLongAudioLockFmt = "%s:" + RiskControlLongAudioLock + ":%s"
	RiskControlLongAudioLockExp = 1 * time.Hour

	RiskControlLongAudioStatus    = "riskcontrolaudiostatus"
	RiskControlLongAudioStatusFmt = "%s:" + RiskControlLongAudioStatus + ":%s"
	RiskControlLongAudioStatusExp = 2 * time.Hour
)

var (
	// 实例化一个AudioWatermark
	RiskControlLongAudioInstance *RiskControlLongAudio
	// once 用于确保实例化操作只执行一次
	onceRiskControlLongAudio sync.Once
)

type RiskControlLongAudio struct {
	client *asynccensorclient.AsyncContentCensorClient
}

func GetRiskControlLongAudio() *RiskControlLongAudio {
	onceRiskControlLongAudio.Do(func() {
		RiskControlLongAudioInstance = newRiskControlLongAudio()
	})
	return RiskControlLongAudioInstance
}

func newRiskControlLongAudio() *RiskControlLongAudio {
	return &RiskControlLongAudio{
		client: asynccensorclient.NewClient(config.LocalConfig.RiskControl.AK, config.LocalConfig.RiskControl.SK),
	}
}

func (a *RiskControlLongAudio) SubmitAudioCensor(c *gin.Context) {
	logCtx := context.WithValue(context.TODO(), utils.CtxKeyLogID, utils.RandStringRunes(16))
	accountID := "ceshi1234567890"
	aid, ok := c.Get("AccountId")
	if ok {
		accountID = aid.(string)
	}

	// 解析请求参数
	resSlice := proto.RiskControlAsyncSubmitResponse{
		LogID:    utils.GetLogID(logCtx),
		DataList: []proto.RiskControlAsyncSubmitResponseItem{},
	}

	req := proto.RiskControlLongAudioSubmitRequest{}
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"SubmitAudioCensor ShouldBindQuery fail, err:%v req:%#v", err, req)
		c.JSON(http.StatusOK, proto.NewCommDataRsp(ParamErrorErrCode, ParamErrorErrMsg, resSlice))
		return
	}

	// 参数校验
	if len(req.AudioUrl) <= 0 {
		logger.Log.Errorf(utils.MMark(logCtx)+"SubmitAudioCensor AudioURL is empty, req:%#v", req)
		c.JSON(http.StatusOK, proto.NewCommDataRsp(ParamEmptyErrCode, ParamEmptyErrMsg, resSlice))
		return
	}

	for _, v := range req.AudioUrl {
		res := proto.RiskControlAsyncSubmitResponseItem{}
		if len(v) <= 0 {
			logger.Log.Errorf(utils.MMark(logCtx)+"SubmitAudioCensor AudioURL is empty, req:%#v", req)
			res.ErrorCode = "100003"
			res.ErrorMsg = "音频地址为空"
			resSlice.DataList = append(resSlice.DataList, res)
			continue
		}

		taskid, err := saveRiskControlSubmitToDb(logCtx, v, accountID, model.RiskControlTypeLongAudio)
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+utils.ServiceInternalEsErrMsg+"SubmitAudioCensor saveSubmitAudioToDb fail, err:%v req:%#v", err, req)
			res.ErrorCode = "100004"
			res.ErrorMsg = err.Error()
		} else {
			res.TaskId = taskid
		}
		resSlice.DataList = append(resSlice.DataList, res)
	}

	c.JSON(http.StatusOK, proto.NewSuccessRsp(resSlice))
}

func (a *RiskControlLongAudio) PullAudioCensor(c *gin.Context) {
	logCtx := context.WithValue(context.TODO(), utils.CtxKeyLogID, utils.RandStringRunes(16))

	// 解析请求参数
	resSlice := proto.RiskControlLongAudioPullResponse{
		LogID:    utils.GetLogID(logCtx),
		DataList: []proto.RiskControlLongAudioPullDataListItem{},
	}

	req := proto.RiskControlLongAudioPullRequest{}
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"PullAudioCensor ShouldBindQuery fail, err:%v req:%#v", err, req)
		c.JSON(http.StatusOK, proto.NewCommDataRsp(ParamErrorErrCode, ParamErrorErrMsg, resSlice))
		return
	}

	// 参数校验
	if len(req.TaskIds) <= 0 {
		logger.Log.Errorf(utils.MMark(logCtx)+"PullAudioCensor AudioURL is empty, req:%#v", req)
		c.JSON(http.StatusOK, proto.NewCommDataRsp(ParamEmptyErrCode, ParamEmptyErrMsg, resSlice))
		return
	}

	for _, taskid := range req.TaskIds {
		res := proto.RiskControlLongAudioPullDataListItem{}
		if len(taskid) <= 0 {
			res.ErrorCode = 100003
			res.ErrorMsg = "任务ID为空"
			logger.Log.Errorf(utils.MMark(logCtx)+"PullAudioCensor taskid is empty, req:%#v", req)
			resSlice.DataList = append(resSlice.DataList, res)
			continue
		}

		rci := model.RiskControlItem{}
		newrci, err := rci.GetRiskControlItemFromTaskId(gomysql.DB, taskid)
		if err != nil {
			res.ErrorCode = 100004
			res.ErrorMsg = err.Error()
			logger.Log.Errorf(utils.MMark(logCtx)+utils.MysqlEsErrMsg+"PullAudioCensor GetRiskControlItemFromTaskId fail,err:%v req:%#v", err, req)
			resSlice.DataList = append(resSlice.DataList, res)
			continue
		}

		res.Status = string(newrci.Status)
		if newrci.Status != enum.TaskSucceed && newrci.Status != enum.TaskFailed {
			resSlice.DataList = append(resSlice.DataList, res)
			continue
		}

		if len(newrci.CensorResult) <= 0 {
			res.ErrorCode = 100005
			res.ErrorMsg = "审核结果为空"
			logger.Log.Errorf(utils.MMark(logCtx)+"PullAudioCensor censorResult is empty,  req:%#v", req)
			resSlice.DataList = append(resSlice.DataList, res)
			continue
		}

		pullres := proto.RiskControlLongAudioCensorPullResponse{}
		err = json.Unmarshal([]byte(newrci.CensorResult), &pullres)
		if err != nil {
			res.ErrorCode = 100006
			res.ErrorMsg = err.Error()
			logger.Log.Errorf(utils.MMark(logCtx)+"PullAudioCensor Unmarshal fail,err:%v req:%#v", err, req)
			resSlice.DataList = append(resSlice.DataList, res)
			continue
		}

		if pullres.ErrorCode != 0 {
			res.ErrorCode = pullres.ErrorCode
			res.ErrorMsg = pullres.ErrorMsg
			logger.Log.Errorf(utils.MMark(logCtx)+"PullAudioCensor GetRiskControlItemFromTaskId fail, req:%#v", req)
			resSlice.DataList = append(resSlice.DataList, res)
			continue
		}

		res = proto.RiskControlLongAudioPullDataListItem{
			Status:         string(newrci.Status),
			Conclusion:     pullres.Conclusion,
			ConclusionType: int(pullres.ConclusionType),
			Data:           make([]proto.RiskControlLongAudioPullData, 0),
		}
		for _, dataDetail := range pullres.Data {
			if dataDetail.ConclusionType != 2 {
				continue
			}
			for _, audodata := range dataDetail.AuditData {
				pdata := proto.RiskControlLongAudioPullData{
					Msg:   audodata.Msg,
					Words: []string{},
				}
				for _, hit := range audodata.Hits {
					pdata.Words = append(pdata.Words, hit.Words...)
				}

				res.Data = append(res.Data, pdata)
			}

		}
		resSlice.DataList = append(resSlice.DataList, res)
	}

	c.JSON(http.StatusOK, proto.NewSuccessRsp(resSlice))
}

func SubmitAudioFromDb() {
	rci := model.RiskControlItem{}
	audios, err := rci.GetTasksWithStatus(gomysql.DB, enum.TaskReady, model.RiskControlTypeLongAudio)
	if err != nil {
		logger.Log.Errorf("SubmitAudioCensor GetTasksWithStatus fail")
		return
	}
	for _, v := range audios {
		submitAudio(v)
	}
}

func submitAudio(rci *model.RiskControlItem) {
	logCtx := context.WithValue(context.TODO(), utils.CtxKeyLogID, rci.TaskId)

	rclv := GetRiskControlLongAudio()
	var res proto.RiskControlLongAudioCensorSubmitResponse

	// 获取 redis 分布式锁
	redisproxy := redisproxy.GetRedisProxy()

	redisLockKey := fmt.Sprintf(RiskControlLongAudioLockFmt, handlerUtils.GetNameByRunEnv(), rci.TaskId)
	redisLock := redislock.NewRedisLock(redisproxy.Rdb, redisLockKey, RiskControlLongAudioLockExp)
	ok, err := redisLock.Lock(context.Background())
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"submitAudio Lock key: %s error: %+v\n", redisLockKey, err)
		return
	} else if !ok {
		return
	}

	defer redisLock.Unlock(context.Background())
	// 获取 任务状态
	// 更新缓存任务状态
	redisKey := fmt.Sprintf(RiskControlLongAudioStatusFmt, handlerUtils.GetNameByRunEnv(), rci.TaskId)
	status, err := redisproxy.GetKeyValue(redisKey)
	if err != nil {
		if string(err.Error()) != string(redis.Nil) {
			logger.Log.Errorf(utils.MMark(logCtx)+"submitAudio get task status error: %+v\n", err)
			return
		}

		newrci, err := rci.GetRiskControlItem(gomysql.DB, rci.ID)
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+"submitAudio ID %s GetFile2Image err: %v \n", rci.TaskId, err)
			return
		}
		// 更新 rci 对象
		rci = newrci

		err = redisproxy.SetKeyValue(redisKey, string(rci.Status), RiskControlLongAudioStatusExp)
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+"submitAudio ID %s SetKeyValue err: %v \n", rci.TaskId, err)
			return
		}

		status = string(rci.Status)
		// 任务状态不存在，还继续查询任务状态，最后将结果写入redis，防止其他服务重复查询并写入数据库
		logger.Log.Warnf(utils.MMark(logCtx)+"submitAudio get task status is nil set status %s\n", status)
	}

	// 任务已经处理过，跳过
	if status == string(enum.TaskRunning) || status == string(enum.TaskSucceed) || status == string(enum.TaskFailed) {
		return
	}

	// 申请qps限制
	key := fmt.Sprintf(RiskControlLongAudioQpsFmt, handlerUtils.GetNameByRunEnv())
	for {
		ok, err = checkQpsAllowance(key, RiskControlLongAudioQpsMax)
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+"submitAudio checkQpsAllowance is err:%v", err)
			return
		}

		if !ok {
			time.Sleep(10 * time.Millisecond)
			continue
		}

		break
	}

	// 需要将音频编码方式转为接口要求的格式
	codecName := "pcm"
	if strings.Contains(rci.AudioType, "pcm") {
		codecName = "pcm"
	} else if strings.Contains(rci.AudioType, "wav") {
		codecName = "wav"
	} else if strings.Contains(rci.AudioType, "amr") {
		codecName = "mar"
	} else if strings.Contains(rci.AudioType, "m4a") {
		codecName = "m4a"
	} else if strings.Contains(rci.AudioType, "aac") {
		codecName = "aac"
	} else if strings.Contains(rci.AudioType, "mp3") {
		codecName = "mp3"
	}

	options := make(map[string]interface{})
	options["extId"] = rci.AccountID
	options["strategyId"] = strconv.Itoa(config.LocalConfig.RiskControl.StrategyId)
	retryNumber := 0
	censorRes := ""
	censorUrl := rci.FileUrl
	if len(rci.CompressionUrl) > 0 {
		censorUrl = rci.CompressionUrl
	}

	// url 转换
	if internalAddress, err := BosUrlReplaceInternalAddress(logCtx, censorUrl); err != nil {
		// 如果失败了，就直接用原来的url去审核，这里打印日志即可，不影响业务逻辑执行
		logger.Log.Errorf(utils.MMark(logCtx)+"BosUrlReplaceInternalAddress fail, err:%v", err)
	} else {
		// 替换成功，则使用内部地址去审核
		censorUrl = internalAddress
	}

	encodeurl, err := handlerUtils.EncodeUrl(censorUrl)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"submitAudio EncodeUrl fail, url:%s", censorUrl)
		return
	}

	for i := 0; i < 3; i++ {
		res = proto.RiskControlLongAudioCensorSubmitResponse{}
		retryNumber += 1
		censorRes = rclv.client.LongAudioCensorSubmit(encodeurl, "", codecName, rci.AudioRate, options)
		err = json.Unmarshal([]byte(censorRes), &res)
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+"submitAudio Unmarshal fail, url:%s", censorUrl)
			continue
		}

		if res.ErrorCode == 18 {
			logger.Log.Errorf(utils.MMark(logCtx)+"submitAudio long audio censor submit fail, url:%s res: %s", encodeurl, censorRes)
			time.Sleep(1 * time.Second)
			continue
		} else if res.ErrorCode != 0 {
			logger.Log.Errorf(utils.MMark(logCtx)+"submitAudio long audio censor submit fail, url:%s res: %s", encodeurl, censorRes)
			time.Sleep(1 * time.Second)
			continue
		} else if len(res.Ret) > 0 && res.Ret != "0" {
			logger.Log.Errorf(utils.MMark(logCtx)+"submitAudio long audio censor submit fail, url:%s res: %s", encodeurl, censorRes)
			time.Sleep(1 * time.Second)
			continue
		} else if len(res.Data.TaskId) <= 0 {
			logger.Log.Errorf(utils.MMark(logCtx)+"submitAudio long audio censor submit fail, url:%s res: %s", encodeurl, censorRes)
			time.Sleep(1 * time.Second)
			continue
		}

		break
	}

	if res.ErrorCode != 0 {
		rci.Status = enum.TaskFailed
	} else if len(res.Ret) > 0 && res.Ret != "0" {
		rci.Status = enum.TaskFailed
	} else if len(res.Data.TaskId) <= 0 {
		rci.Status = enum.TaskFailed
	} else {
		rci.Status = enum.TaskRunning
	}

	rci.SubmitResult = censorRes
	rci.UpdatedAt = time.Now()
	rci.RetryNumber = int64(retryNumber)

	if err = rci.UpdateRiskControlItem(gomysql.DB); err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"submitAudio UpdateRiskControlItem fail, url:%s", censorUrl)
		return
	}

	err = redisproxy.SetKeyValue(redisKey, string(rci.Status), RiskControlLongAudioStatusExp)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"submitAudio ID %s SetKeyValue err: %v \n", rci.TaskId, err)
		return
	}
	logger.Log.Infof(utils.MMark(logCtx)+"submitAudio success, status:%s", rci.Status)
}

func PullAudioFromDb() {
	rci := model.RiskControlItem{}
	audios, err := rci.GetTasksWithStatus(gomysql.DB, enum.TaskRunning, model.RiskControlTypeLongAudio)
	if err != nil {
		logger.Log.Errorf("SubmitAudioCensor GetTasksWithStatus fail")
		return
	}
	for _, v := range audios {
		pullAudio(v)
	}
}

func pullAudio(rci *model.RiskControlItem) {
	logCtx := context.WithValue(context.TODO(), utils.CtxKeyLogID, rci.TaskId)

	// 获取 redis 分布式锁
	redisproxy := redisproxy.GetRedisProxy()
	redisLockKey := fmt.Sprintf(RiskControlLongAudioLockFmt, handlerUtils.GetNameByRunEnv(), rci.TaskId)
	redisLock := redislock.NewRedisLock(redisproxy.Rdb, redisLockKey, RiskControlLongAudioLockExp)
	ok, err := redisLock.Lock(context.Background())
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"pullAudio Lock key: %s error: %+v\n", redisLockKey, err)
		return
	} else if !ok {
		return
	}

	defer redisLock.Unlock(context.Background())

	// 获取 任务状态
	// 更新缓存任务状态
	redisKey := fmt.Sprintf(RiskControlLongAudioStatusFmt, handlerUtils.GetNameByRunEnv(), rci.TaskId)
	status, err := redisproxy.GetKeyValue(redisKey)
	if err != nil {
		if string(err.Error()) != string(redis.Nil) {
			logger.Log.Errorf(utils.MMark(logCtx)+"pullAudio get task status error: %+v\n", err)
			return
		}

		newrci, err := rci.GetRiskControlItem(gomysql.DB, rci.ID)
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+"pullAudio ID %s GetFile2Image err: %v \n", rci.TaskId, err)
			return
		}
		// 更新 rci 对象
		rci = newrci

		err = redisproxy.SetKeyValue(redisKey, string(rci.Status), RiskControlLongAudioStatusExp)
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+"pullAudio ID %s SetKeyValue err: %v \n", rci.TaskId, err)
			return
		}

		status = string(rci.Status)
		// 任务状态不存在，还继续查询任务状态，最后将结果写入redis，防止其他服务重复查询并写入数据库
		logger.Log.Warnf(utils.MMark(logCtx)+"pullAudio get task status is nil set status %s\n", status)
	}

	// 任务已经处理过，跳过
	if status == string(enum.TaskSucceed) || status == string(enum.TaskFailed) {
		return
	}

	rclv := GetRiskControlLongAudio()
	pullres := proto.RiskControlLongAudioCensorPullResponse{}
	retryNumber := 0
	censorRes := ""

	// 解析提交结果
	submitres := proto.RiskControlLongAudioCensorSubmitResponse{}
	err = json.Unmarshal([]byte(rci.SubmitResult), &submitres)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"pullAudio Unmarshal fail, url:%s", rci.FileUrl)
		return
	}

	// 申请qps限制
	key := fmt.Sprintf(RiskControlLongAudioPullQpsFmt, handlerUtils.GetNameByRunEnv())
	for {
		ok, err = checkQpsAllowance(key, RiskControlLongAudioPullQpsMax)
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+"submitAudio checkQpsAllowance is err:%v", err)
			return
		}

		if !ok {
			time.Sleep(10 * time.Millisecond)
			continue
		}

		break
	}

	for i := 0; i < 3; i++ {
		retryNumber += 1
		pullres = proto.RiskControlLongAudioCensorPullResponse{}
		censorRes = rclv.client.LongAudioCensorPull(submitres.Data.TaskId, nil)
		err := json.Unmarshal([]byte(censorRes), &pullres)
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+"pullAudio Unmarshal fail,censorRes:%s taskId:%s", censorRes, rci.TaskId)
			pullres.ErrorCode = 200001
			pullres.ErrorMsg = censorRes
			time.Sleep(1 * time.Second)
			continue
		}
		if pullres.ErrorCode == 282911 {
			//error_code":282911,"error_msg":"voice format conversion error"
			// 错误码为282911，语音格式转换失败，将视频进行转码后在重新审核
			//视频抽帧错误 转码后重新提交
			err = longAudioTransformAndSubmitVideo(logCtx, rci)
			if err != nil {
				logger.Log.Errorf(utils.MMark(logCtx)+" longAudioTransformAndSubmitVideo fail, taskId:%s", rci.TaskId)
				pullres.ErrorCode = 200001
				pullres.ErrorMsg = "音频格式异常"
				break
			}
			return
		} else if pullres.ErrorCode != 0 {
			if pullres.ErrorCode == 282008 {
				// "error_code":282008,"error_msg":"task not complete"
				// 任务未完成，等待下次拉取
				return
			}
			continue
		}
		break
	}

	if pullres.ErrorCode != 0 {
		rci.Status = enum.TaskFailed
	} else {
		rci.Status = enum.TaskSucceed
	}

	rci.ConclusionType = pullres.ConclusionType
	rci.CensorResult = censorRes
	rci.RetryNumber = int64(retryNumber)
	rci.UpdatedAt = time.Now()
	rci.Duration = rci.UpdatedAt.Sub(rci.CreatedAt).Milliseconds()/1000 + 1

	if err := rci.UpdateRiskControlItem(gomysql.DB); err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"pullAudio UpdateRiskControlItem fail, taskId:%s", rci.TaskId)
		return
	}

	err = redisproxy.SetKeyValue(redisKey, string(rci.Status), RiskControlLongAudioStatusExp)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"pullAudio ID %s SetKeyValue err: %v \n", rci.TaskId, err)
		return
	}

	logger.Log.Infof(utils.MMark(logCtx)+"pullAudio taskId:%s status: %s conclusionType:%d", rci.TaskId, rci.Status, pullres.ConclusionType)
}

func longAudioTransformAndSubmitVideo(logCtx context.Context, rci *model.RiskControlItem) error {
	censorVideoUrl := rci.FileUrl
	if len(rci.CompressionUrl) > 0 {
		censorVideoUrl = rci.CompressionUrl
	}

	// 下载文件并计算md5
	downpath := DownloadFilePrefix + uuid.New().String() + ".wav"
	_, err := handlerUtils.CalculateMD5FromUrl(censorVideoUrl, downpath)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"censorVideo CalculateMD5FromUrl fail,err:%v url:%s", err, censorVideoUrl)
		return err
	}
	defer os.Remove(downpath)

	// 转码为16K
	dstaudiopath := DownloadFilePrefix + uuid.New().String() + ".wav"
	err = ffmpegutils.AudioTranscoding(downpath, dstaudiopath)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"ffmpegutils.AudioTranscoding fail, err:%v", err)
		return err
	}

	if ok, err := handlerUtils.FileExists(dstaudiopath); err != nil || !ok {
		logger.Log.Errorf(utils.MMark(logCtx)+"FileExists fail, err:%v", err)
		return errors.New("话音转码文件不存在")
	}

	// 上传bos
	transformUrl, err := RetryUploadBosServiceFromFile(logCtx, FigureRiskControlAudioPath, dstaudiopath, ".wav")
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+" UploadBosServiceFromFile err: %v \n", err)
		return err
	}

	rci.CompressionUrl = transformUrl
	rci.Status = enum.TaskReady
	rci.UpdatedAt = time.Now()

	err = rci.UpdateRiskControlItem(gomysql.DB)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+" UpdateRiskControlItem fail, taskId:%s", rci.TaskId)
		return err
	}

	// 获取 redis 分布式锁
	redisproxy := redisproxy.GetRedisProxy()
	redisKey := fmt.Sprintf(RiskControlLongAudioStatusFmt, handlerUtils.GetNameByRunEnv(), rci.TaskId)
	err = redisproxy.SetKeyValue(redisKey, string(rci.Status), RiskControlLongAudioStatusExp)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"submitAudio ID %s SetKeyValue err: %v \n", rci.TaskId, err)
		return err
	}

	return nil
}
