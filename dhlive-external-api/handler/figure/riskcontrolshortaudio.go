package figure

import (
	"acg-ai-go-common/gomysql"
	"acg-ai-go-common/logger"
	"acg-ai-go-common/utils"
	"acg-ai-go-common/utils/redisproxy"
	"context"
	"dhlive-external-api/beans/enum"
	"dhlive-external-api/beans/model"
	"dhlive-external-api/beans/proto"
	config "dhlive-external-api/conf"
	"dhlive-external-api/handler/gcpcensor"
	"dhlive-external-api/handler/handlerUtils"
	"dhlive-external-api/handler/handlerUtils/ffmpegutils"
	"dhlive-external-api/handler/handlerUtils/redislock"
	"dhlive-external-api/handler/handlerUtils/synccensorclient"
	"dhlive-external-api/handler/i18n/respi18n"
	minicensor "dhlive-external-api/handler/minilanguagecensor"
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"os"
	"path"
	"strconv"
	"strings"
	"sync"
	"time"

	"cloud.google.com/go/speech/apiv1/speechpb"
	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

const (
	RiskControlShortAudioQps    = "riskcontrolshortaudioqps"
	RiskControlShortAudioQpsFmt = "%s:" + RiskControlShortAudioQps + ":xiling-saas-v3"
	RiskControlShortAudioQpsMax = 20 // 最大并发量

	RiskControlMiniAudioQpsFmt = "%s:riskcontrolminiaudioqps:xiling-saas-v3"

	RiskControlShortAudioQueueFmt         = "%s:" + "riskcontrolshortaudioqueue" + ":xiling-saas-v3"
	RiskControlShortAudioQueueMaxInterval = 30 // 单位s

	RiskControlShortAudioLockFmt          = "%s:" + "riskControlshortAudio" + "%s"
	RiskControlShortAudioLockTimeDuration = 10 * time.Minute
)

const (
	ShortAudioDurationMax = 60               // 单位s  不超过1分钟
	ShortAudioFileSizeMax = 10 * 1024 * 1024 // 单位byte  不超过10M
)

var (
	// 实例化一个AudioWatermark
	RiskControlShortAudioInstance *RiskControlShortAudio
	// once 用于确保实例化操作只执行一次
	onceRiskControlShortAudio sync.Once
)

type RiskControlShortAudio struct {
	auth synccensorclient.Auth
}

func GetRiskControlShortAudio() *RiskControlShortAudio {
	onceRiskControlShortAudio.Do(func() {
		RiskControlShortAudioInstance = newRiskControlShortAudio()
	})
	return RiskControlShortAudioInstance
}

func newRiskControlShortAudio() *RiskControlShortAudio {
	auth := synccensorclient.Auth{}
	auth.InitAuth(config.LocalConfig.RiskControl.AK, config.LocalConfig.RiskControl.SK)
	return &RiskControlShortAudio{
		auth: auth,
	}
}

func (rsa *RiskControlShortAudio) CensorAudio(c *gin.Context) {
	logCtx := context.WithValue(context.TODO(), utils.CtxKeyLogID, utils.RandStringRunes(16))
	accountID := "ceshishortaudio"
	aid, ok := c.Get("AccountId")
	if ok {
		accountID = aid.(string)
	}

	// 获取目标语言
	targetLanguage := c.GetHeader("Language")
	logCtx = context.WithValue(logCtx, "Language", targetLanguage)
	logger.Log.Infof(utils.MMark(logCtx)+"CensorAudio langugae in header is: %s", targetLanguage)

	// 解析请求参数
	resSlice := proto.RiskControlShortAudioResponse{
		LogID: utils.GetLogID(logCtx),
	}

	if !config.LocalConfig.RiskControl.IsSwitch {
		c.JSON(http.StatusOK, proto.NewSuccessRsp(resSlice))
		return
	}

	req := proto.RiskControlShortAudioRequest{}
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"CensorAudio ShouldBindQuery fail, err:%v req:%#v", err, req)
		c.JSON(http.StatusOK, proto.NewCommDataRsp(ParamErrorErrCode, ParamErrorErrMsg, resSlice))
		return
	}

	// 参数校验
	if len(req.AudioUrl) <= 0 {
		logger.Log.Errorf(utils.MMark(logCtx)+"CensorAudio VideoURL is empty, req:%#v", req)
		c.JSON(http.StatusOK, proto.NewCommDataRsp(ParamEmptyErrCode, ParamEmptyErrMsg, resSlice))
		return
	}

	if !VerifyBosUrl(req.AudioUrl) {
		logger.Log.Errorf(utils.MMark(logCtx)+"CensorVideo AudioUrl not bos url, req:%#v", req)
		c.JSON(http.StatusOK, proto.NewCommDataRsp(ParamEmptyErrCode, respi18n.TransformResponseLocaleByDefault(
			logCtx, targetLanguage, "音频链接不合法"), resSlice))
		return
	}

	resSlice, err := rsa.censorAudio(logCtx, accountID, req.AudioUrl)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+utils.ServiceInternalEsErrMsg+"CensorAudio censorVideo fail, err:%v", err)
		c.JSON(http.StatusOK, proto.NewCommDataRsp(100003, respi18n.TransformResponseLocaleByDefault(
			logCtx, targetLanguage, "音频审核失败"), resSlice))
		return
	}

	if resSlice.Data.ErrorCode != 0 {
		logger.Log.Errorf(utils.MMark(logCtx)+"CensorAudio censorVideo fail, res:%v", resSlice)
		c.JSON(http.StatusOK, proto.NewCommDataRsp(100004, respi18n.TransformResponseLocaleByDefault(
			logCtx, targetLanguage, "音频审核失败"), resSlice))
		return
	}

	c.JSON(http.StatusOK, proto.NewSuccessRsp(resSlice))
}

func (rsa *RiskControlShortAudio) censorAudio(logCtx context.Context, accountID string, audioURL string) (proto.RiskControlShortAudioResponse, error) {
	resItem := proto.RiskControlShortAudioResponse{
		LogID: utils.GetLogID(logCtx),
	}

	// url 转换
	if internalAddress, err := BosUrlReplaceInternalAddress(logCtx, audioURL); err != nil {
		// 如果失败了，就直接用原来的url去审核，这里打印日志即可，不影响业务逻辑执行
		logger.Log.Errorf(utils.MMark(logCtx)+" BosUrlReplaceInternalAddress fail, err:%v", err)
	} else {
		// 替换成功，则使用内部地址去审核
		audioURL = internalAddress
	}

	urlmd5 := handlerUtils.CalculateMD5FromText(audioURL)
	// 使用分布式锁锁住任务防止其他任务重复提交到数据库和任务处理
	redisProxy := redisproxy.GetRedisProxy()
	redisLockKey := fmt.Sprintf(RiskControlShortAudioLockFmt, handlerUtils.GetNameByRunEnv(), urlmd5+"_lock")
	redisLock := redislock.NewRedisLock(redisProxy.Rdb, redisLockKey, RiskControlShortAudioLockTimeDuration)

	ok, err := redisLock.Lock(context.Background())
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+utils.RedisEsErrMsg+"censorAudio Lock key: %s error: %+v\n", redisLockKey, err)
		return resItem, err
	} else if !ok {
		err = acquireCensorLockWithBlock(logCtx, redisLock)
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+" acquireCensorLockWithBlock error: %+v\n", err)
			return resItem, err
		}
	}

	defer redisLock.Unlock(context.Background())

	// 查询数据库
	resItem, err = findDbCacheByAudioWithUrlMd5(logCtx, urlmd5)
	if err == nil {
		logger.Log.Info(utils.MMark(logCtx) + " found success")
		return resItem, nil
	}

	queueKey := fmt.Sprintf(RiskControlShortAudioQueueFmt, handlerUtils.GetNameByRunEnv())
	ok, err = checkQueueAllowance(queueKey, config.LocalConfig.RiskControl.AudioQueueSize, RiskControlShortAudioQueueMaxInterval)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+" checkQueueAllowance is err:%v", err)
		return resItem, err
	}

	if !ok {
		return resItem, errors.New("queue is full")
	}
	defer decrementCounter(queueKey)

	// 创建文件名称
	fpath, err := handlerUtils.CreateFileNameFromUrl(DownloadFilePrefix, audioURL)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"CreateFileNameFromUrl fail, err:%v", err)
		return resItem, err
	}
	defer os.Remove(fpath)

	// 下载并计算md5
	md5, err := handlerUtils.CalculateMD5FromUrl(audioURL, fpath)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"CalculateMD5FromUrl fail, err:%v", err)
		return resItem, err
	}

	// 即使不同的url，md5也有可能相同
	// 锁一下文件，防止重复提交
	redisLockKey = fmt.Sprintf(RiskControlShortAudioLockFmt, handlerUtils.GetNameByRunEnv(), md5+"_lock")
	redisLockMd5 := redislock.NewRedisLock(redisProxy.Rdb, redisLockKey, RiskControlShortAudioLockTimeDuration)

	ok, err = redisLockMd5.Lock(context.Background())
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+utils.RedisEsErrMsg+" redisLockMd5 Lock key: %s error: %+v\n", redisLockKey, err)
		return resItem, err
	} else if !ok {
		err = acquireCensorLockWithBlock(logCtx, redisLockMd5)
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+" acquireCensorLockWithBlock error: %+v\n", err)
			return resItem, err
		}
	}

	defer redisLockMd5.Unlock(context.Background())

	// 查询数据库
	resItem, err = findDbCacheByAudioWithFileMd5(logCtx, md5, audioURL, urlmd5)
	if err == nil {
		logger.Log.Info(utils.MMark(logCtx) + " found success")
		return resItem, nil
	}

	if config.LocalConfig.RiskControl.IsInternational {
		// 是否为国际版
		return censorAudioByInternational(logCtx, accountID, audioURL, fpath, md5, urlmd5)
	}

	// 判断音频文件是否为小语种
	if ok, err = minicensor.IsMiniLangByAudio(logCtx, fpath); err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"isMiniLangByAudio fail, err:%v", err)
		return resItem, err
	} else if ok {
		// 小语种审核
		return rsa.censorAudioByMiniLanguage(logCtx, accountID, audioURL, fpath, md5, urlmd5)
	}

	// 中文审核
	return rsa.censorAudioByChinese(logCtx, accountID, audioURL, fpath, md5, urlmd5)
}

func (rsa *RiskControlShortAudio) censorAudioByChinese(logCtx context.Context, accountID string, audioURL, fpath, md5, urlmd5 string) (proto.RiskControlShortAudioResponse, error) {
	resItem := proto.RiskControlShortAudioResponse{
		LogID: utils.GetLogID(logCtx),
	}
	// 申请队列
	starttime := time.Now()

	// 判断音频文件属于长音频还是短音频
	duration, err := ffmpegutils.GetAudioDuration(fpath)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"GetAudioDuration fail, err:%v", err)
		return resItem, err
	}
	fsize, err := handlerUtils.GetFileSize(fpath)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"GetFileSize fail, err:%v", err)
		return resItem, err
	}

	ffinfo := ffmpegutils.ExecuteFFprobe(fpath)
	if ffinfo.Error != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"ffprobe fail, err:%v", ffinfo.Error)
		return resItem, ffinfo.Error
	}

	taskIdPrefix := "rca-"
	if duration > ShortAudioDurationMax || fsize > ShortAudioFileSizeMax {
		taskid := taskIdPrefix + utils.RandStringRunes(16)
		rcitem := &model.RiskControlItem{
			AccountID: accountID,
			LogId:     utils.GetLogID(logCtx),
			TaskId:    taskid,
			FileUrl:   audioURL,
			AudioType: ffinfo.CodecName,
			AudioRate: ffinfo.SampleRate,
			Type:      model.RiskControlTypeLongAudio,
			Md5:       md5,
			UrlMd5:    urlmd5,
			Status:    enum.TaskReady,
			CreatedAt: starttime,
			UpdatedAt: time.Now(),
		}

		if err = rcitem.UpdateRiskControlItem(gomysql.DB); err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+"censorAudio UpdateRiskControlItem fail, url:%s", audioURL)
			return resItem, err
		}

		resItem, err = getLongAudioTaskIdCensorResult(logCtx, rcitem.TaskId)
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+"getTaskIdCensorResult fail, err:%v", err)
			return resItem, err
		}
		return resItem, err
	}

	// 申请qps限制
	key := fmt.Sprintf(RiskControlShortAudioQpsFmt, handlerUtils.GetNameByRunEnv())
	for {
		ok, err := checkQpsAllowance(key, RiskControlShortAudioQpsMax)
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+"censorAudio checkQpsAllowance is err:%v", err)
			return resItem, err
		}

		if !ok {
			time.Sleep(10 * time.Millisecond)
			continue
		}

		break
	}

	censorUrl := audioURL
	rate, err := strconv.Atoi(ffinfo.SampleRate)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"strconv.Atoi fail, err:%v", err)
		return resItem, err
	}

	// 判断音频是否需要转码
	dstaudiopath, ok, err := AudioTranscode(logCtx, fpath, ffinfo.CodecName, rate)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"AudioTranscode fail, err:%v", err)
		// return resItem, err
	}
	if ok {
		// 需要转码
		defer os.Remove(dstaudiopath)

		dstaudioUrl, err := RetryUploadBosServiceFromFile(logCtx, FigureRiskControlAudioPath, dstaudiopath, path.Ext(dstaudiopath))
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+"RetryUploadBosServiceFromFile fail, err:%v", err)
			return resItem, err
		}

		censorUrl = dstaudioUrl
		for i := 0; i < 3; i++ {
			if ffinfo = ffmpegutils.ExecuteFFprobe(dstaudiopath); ffinfo.Error != nil {
				time.Sleep(100 * time.Millisecond)
				continue
			}
			break
		}

		rate, err = strconv.Atoi(ffinfo.SampleRate)
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+"strconv.Atoi fail, err:%v", err)
			// return resItem, err
		}
	}

	// 需要将音频编码方式转为接口要求的格式
	codecName := "pcm"
	if strings.Contains(ffinfo.CodecName, "pcm") {
		codecName = "pcm"
	} else if strings.Contains(ffinfo.CodecName, "wav") {
		codecName = "wav"
	} else if strings.Contains(ffinfo.CodecName, "amr") {
		codecName = "mar"
	} else if strings.Contains(ffinfo.CodecName, "m4a") {
		codecName = "m4a"
	} else if strings.Contains(ffinfo.CodecName, "aac") {
		codecName = "aac"
	} else if strings.Contains(ffinfo.CodecName, "mp3") {
		codecName = "mp3"
	}

	// url 转换
	if internalAddress, err := BosUrlReplaceInternalAddress(logCtx, censorUrl); err != nil {
		// 如果失败了，就直接用原来的url去审核，这里打印日志即可，不影响业务逻辑执行
		logger.Log.Errorf(utils.MMark(logCtx)+"BosUrlReplaceInternalAddress fail, err:%v", err)
	} else {
		// 替换成功，则使用内部地址去审核
		censorUrl = internalAddress
	}

	encodeurl, err := handlerUtils.EncodeUrl(censorUrl)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"EncodeUrl fail, url:%s", censorUrl)
		encodeurl = censorUrl
	}

	cres := ""
	retryNumber := 0
	censorRes := proto.RiskControlShortAudioCensorResponse{}
	for i := 0; i < 3; i++ {
		censorRes = proto.RiskControlShortAudioCensorResponse{}
		retryNumber += 1

		cres = rsa.audioCensorPost(encodeurl, rate, codecName, config.LocalConfig.RiskControl.StrategyId, nil)
		err = json.Unmarshal([]byte(cres), &censorRes)
		if err != nil {
			censorRes.ErrorCode = 200001
			censorRes.ErrorMsg = "res: " + cres + " err:" + err.Error()
			logger.Log.Errorf(utils.MMark(logCtx)+"censoraudio Unmarshal fail, res: %s err:%v", cres, err)
			time.Sleep(1 * time.Second)
			continue
		}
		if censorRes.ErrorCode == 18 { // qps 超限
			logger.Log.Errorf(utils.MMark(logCtx)+"censoraudio ImgCensor qps limit res:%v", cres)
			time.Sleep(1 * time.Second)
			continue
		} else if censorRes.ErrorCode == 282903 || censorRes.ErrorCode == 282904 {
			// 282903 音频大小超限 请检查音频大小是否符合要求：不超过10MB
			// 282904 音频格式错误 请检查音频格式是否符合要求，支持的音频格式：pcm、wav、mp3、aac、amr、m4a
			// 以上错误直接返回，不做重试
		} else if censorRes.ErrorCode != 0 || censorRes.ConclusionType == 4 {
			time.Sleep(1 * time.Second)
			continue
		}
		break
	}

	taskstatus := enum.TaskSucceed
	if censorRes.ErrorCode != 0 || censorRes.ConclusionType == 4 {
		taskstatus = enum.TaskFailed
	}
	taskid := taskIdPrefix + utils.RandStringRunes(16)

	endtime := time.Now()
	durationtime := endtime.Sub(starttime).Milliseconds()/1000 + 1
	// 判断话语是否转码过，没有转码过，则置空
	if censorUrl == audioURL {
		censorUrl = ""
	}

	rcitem := &model.RiskControlItem{
		AccountID:      accountID,
		LogId:          utils.GetLogID(logCtx),
		TaskId:         taskid,
		FileUrl:        audioURL,
		CompressionUrl: censorUrl,
		AudioType:      ffinfo.CodecName,
		AudioRate:      ffinfo.SampleRate,
		Type:           model.RiskControlTypeShortAudio,
		Md5:            md5,
		UrlMd5:         urlmd5,
		ConclusionType: censorRes.ConclusionType,
		CensorResult:   cres,
		Status:         taskstatus,
		Duration:       durationtime,
		RetryNumber:    int64(retryNumber),
		CreatedAt:      starttime,
		UpdatedAt:      endtime,
	}

	if err = rcitem.UpdateRiskControlItem(gomysql.DB); err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"censorVideo UpdateRiskControlItem fail, url:%s", audioURL)
		return resItem, err
	}

	resItem, err = getShortAudioTaskIdCensorResult(logCtx, cres, taskid, taskstatus)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"getTaskIdCensorResult fail, err:%v", err)
		return resItem, err
	}

	return resItem, nil
}

func (rsa *RiskControlShortAudio) censorAudioByMiniLanguage(logCtx context.Context, accountID string, audioURL, fpath, md5, urlmd5 string) (proto.RiskControlShortAudioResponse, error) {
	resItem := proto.RiskControlShortAudioResponse{
		LogID: utils.GetLogID(logCtx),
	}
	// 申请队列
	starttime := time.Now()

	ffinfo := ffmpegutils.ExecuteFFprobe(fpath)
	if ffinfo.Error != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"ffprobe fail, err:%v", ffinfo.Error)
		return resItem, ffinfo.Error
	}

	taskIdPrefix := "rca-"
	// 申请qps限制
	key := fmt.Sprintf(RiskControlMiniAudioQpsFmt, handlerUtils.GetNameByRunEnv())
	for {
		ok, err := checkQpsAllowance(key, config.LocalConfig.RiskControl.MiniAudioQpsMax)
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+"censorAudio checkQpsAllowance is err:%v", err)
			return resItem, err
		}

		if !ok {
			time.Sleep(10 * time.Millisecond)
			continue
		}

		break
	}

	censorUrl := audioURL
	rate, err := strconv.Atoi(ffinfo.SampleRate)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"strconv.Atoi fail, err:%v", err)
		return resItem, err
	}

	// 判断音频是否需要转码
	dstaudiopath, ok, err := AudioTranscodeByMiniLanguage(logCtx, fpath, ffinfo.CodecName, rate)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+" AudioTranscodeByMiniLanguage fail, err:%v", err)
		// return resItem, err
	}
	if ok {
		// 需要转码
		defer os.Remove(dstaudiopath)
		fpath = dstaudiopath
	}

	retryNumber := 0
	censorRes := proto.RiskControlShortAudioCensorResponse{}
	cres, err := minicensor.CensorAudioPost(logCtx, fpath)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"audioMiniLanguageCensor fail, url:%s", audioURL)
		censorRes.ErrorCode = 1
		censorRes.ErrorMsg = err.Error()
		censorRes.ConclusionType = 4
	}

	taskstatus := enum.TaskSucceed
	if censorRes.ErrorCode != 0 {
		taskstatus = enum.TaskFailed
	}
	taskid := taskIdPrefix + utils.RandStringRunes(16)

	endtime := time.Now()
	durationtime := endtime.Sub(starttime).Milliseconds()/1000 + 1
	// 判断话语是否转码过，没有转码过，则置空
	if censorUrl == audioURL {
		censorUrl = ""
	}

	rcitem := &model.RiskControlItem{
		AccountID:      accountID,
		LogId:          utils.GetLogID(logCtx),
		TaskId:         taskid,
		FileUrl:        audioURL,
		CompressionUrl: censorUrl,
		AudioType:      ffinfo.CodecName,
		AudioRate:      ffinfo.SampleRate,
		Type:           model.RiskControlTypeAudioMiniLanguge,
		Md5:            md5,
		UrlMd5:         urlmd5,
		ConclusionType: censorRes.ConclusionType,
		CensorResult:   cres,
		Status:         taskstatus,
		Duration:       durationtime,
		RetryNumber:    int64(retryNumber),
		CreatedAt:      starttime,
		UpdatedAt:      endtime,
	}

	if err = rcitem.UpdateRiskControlItem(gomysql.DB); err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"censorVideo UpdateRiskControlItem fail, url:%s", audioURL)
		return resItem, err
	}

	resItem, err = getMiniLanguageAudioTaskIdCensorResult(logCtx, cres, taskid, taskstatus)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"getTaskIdCensorResult fail, err:%v", err)
		return resItem, err
	}

	return resItem, nil
}

func getLongAudioTaskIdCensorResult(logCtx context.Context, taskId string) (proto.RiskControlShortAudioResponse, error) {
	resItem := proto.RiskControlShortAudioResponse{
		LogID: utils.GetLogID(logCtx),
	}

	rcitem := &model.RiskControlItem{}
	for {
		newitem, err := rcitem.GetRiskControlItemFromTaskId(gomysql.DB, taskId)
		if err != nil {
			return resItem, err
		} else if newitem.Status == enum.TaskSucceed || newitem.Status == enum.TaskFailed {
			rcitem = newitem
			break
		}
		time.Sleep(5 * time.Second)
	}

	// 长音频结果
	pullres := proto.RiskControlLongAudioCensorPullResponse{}
	err := json.Unmarshal([]byte(rcitem.CensorResult), &pullres)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"censorVideo Unmarshal fail,pullres:%s err:%v", pullres, err)
		return resItem, err
	}

	resItem = proto.RiskControlShortAudioResponse{
		LogID: utils.GetLogID(logCtx),
		Data: proto.RiskControlAudioResponseData{
			TaskId:                   rcitem.TaskId,
			Status:                   string(rcitem.Status),
			ErrorCode:                pullres.ErrorCode,
			ErrorMsg:                 pullres.ErrorMsg,
			Conclusion:               pullres.Conclusion,
			ConclusionType:           int(pullres.ConclusionType),
			ConclusionTypeGroupInfos: []proto.RiskControlAudioConclusionTypeGroupInfos{},
		},
	}

	for _, dataDetail := range pullres.Data {
		if dataDetail.ConclusionType != 2 {
			continue
		}
		hitWordMap := make(map[string]struct{})
		for _, audodata := range dataDetail.AuditData {
			info := proto.RiskControlAudioConclusionTypeGroupInfos{
				Msg:   audodata.Msg,
				Words: []string{},
			}

			for _, hit := range audodata.Hits {
				for _, word := range hit.Words {
					words, ok := IsHitWordWithSave(word, hitWordMap)
					if !ok {
						continue
					}
					info.Words = append(info.Words, words...)
				}

				//如果大模型没有命中关键词 再根据模型命中位置去匹配关键词
				if len(hit.Words) > 0 {
					continue
				}

				for _, hitpos := range hit.ModelHitPositions {
					if len(hitpos) > 2 {
						word := handlerUtils.SubstringByRuneCount(dataDetail.Text, int(hitpos[0]), int(hitpos[1]))
						words, ok := IsHitWordWithSave(word, hitWordMap)
						if !ok {
							continue
						}
						info.Words = append(info.Words, words...)
					}
				}

				for _, wordhitpos := range hit.WordHitPositions {
					words, ok := IsHitWordWithSave(wordhitpos.Keyword, hitWordMap)
					if !ok {
						continue
					}
					info.Words = append(info.Words, words...)
					for _, pos := range wordhitpos.Positions {
						if len(pos) == 2 {
							word := handlerUtils.SubstringByRuneCount(dataDetail.Text, int(pos[0]), int(pos[1]))
							words, ok := IsHitWordWithSave(word, hitWordMap)
							if !ok {
								continue
							}
							info.Words = append(info.Words, words...)
						}
					}
				}
			}
			resItem.Data.ConclusionTypeGroupInfos = append(resItem.Data.ConclusionTypeGroupInfos, info)
		}

	}

	for _, dataDetail := range pullres.AudioData {
		if dataDetail.ConclusionType != 2 {
			continue
		}

		if dataDetail.Type == 37 && dataDetail.SubType == 0 {
			resItem.Data.Conclusion = "合规"
			resItem.Data.ConclusionType = 1
			continue
		}

		info := proto.RiskControlAudioConclusionTypeGroupInfos{
			Msg:   dataDetail.Msg,
			Words: []string{},
		}

		resItem.Data.ConclusionTypeGroupInfos = append(resItem.Data.ConclusionTypeGroupInfos, info)
	}

	return resItem, nil
}

func getShortAudioTaskIdCensorResult(logCtx context.Context, censorResult, taskid string, status enum.TaskStatus) (proto.RiskControlShortAudioResponse, error) {
	resItem := proto.RiskControlShortAudioResponse{
		LogID: utils.GetLogID(logCtx),
	}

	censorRes := proto.RiskControlShortAudioCensorResponse{}
	err := json.Unmarshal([]byte(censorResult), &censorRes)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"censorVideo Unmarshal fail, err:%v", err)
		return resItem, err
	}

	resItem = proto.RiskControlShortAudioResponse{
		LogID: utils.GetLogID(logCtx),
		Data: proto.RiskControlAudioResponseData{
			TaskId:                   taskid,
			Status:                   string(status),
			ErrorCode:                censorRes.ErrorCode,
			ErrorMsg:                 censorRes.ErrorMsg,
			Conclusion:               censorRes.Conclusion,
			ConclusionType:           int(censorRes.ConclusionType),
			ConclusionTypeGroupInfos: []proto.RiskControlAudioConclusionTypeGroupInfos{},
		},
	}

	for _, dataDetail := range censorRes.Data {
		if dataDetail.ConclusionType != 2 {
			continue
		}
		// 命中的关键词map 用于去重
		hitWordMap := make(map[string]struct{})
		for _, audodata := range dataDetail.AuditData {
			info := proto.RiskControlAudioConclusionTypeGroupInfos{
				Msg:   audodata.Msg,
				Words: []string{},
			}

			for _, hit := range audodata.Hits {
				for _, word := range hit.Words {
					words, ok := IsHitWordWithSave(word, hitWordMap)
					if !ok {
						continue
					}
					info.Words = append(info.Words, words...)
				}

				//如果大模型没有命中关键词 再根据模型命中位置去匹配关键词
				if len(hit.Words) > 0 {
					continue
				}

				for _, hitpos := range hit.ModelHitPositions {
					if len(hitpos) > 2 {
						word := handlerUtils.SubstringByRuneCount(dataDetail.Text, int(hitpos[0]), int(hitpos[1]))
						words, ok := IsHitWordWithSave(word, hitWordMap)
						if !ok {
							continue
						}
						info.Words = append(info.Words, words...)
					}
				}

				for _, wordhitpos := range hit.WordHitPositions {
					words, ok := IsHitWordWithSave(wordhitpos.Keyword, hitWordMap)
					if !ok {
						continue
					}
					info.Words = append(info.Words, words...)
					for _, pos := range wordhitpos.Positions {
						if len(pos) == 2 {
							word := handlerUtils.SubstringByRuneCount(dataDetail.Text, int(pos[0]), int(pos[1]))
							words, ok := IsHitWordWithSave(word, hitWordMap)
							if !ok {
								continue
							}
							info.Words = append(info.Words, words...)
						}
					}
				}
			}
			resItem.Data.ConclusionTypeGroupInfos = append(resItem.Data.ConclusionTypeGroupInfos, info)
		}

	}

	for _, dataDetail := range censorRes.AudioData {
		if dataDetail.ConclusionType != 2 {
			continue
		}

		if dataDetail.Type == 37 && dataDetail.SubType == 0 {
			resItem.Data.Conclusion = "合规"
			resItem.Data.ConclusionType = 1
			continue
		}

		info := proto.RiskControlAudioConclusionTypeGroupInfos{
			Msg:   dataDetail.Msg,
			Words: []string{},
		}

		resItem.Data.ConclusionTypeGroupInfos = append(resItem.Data.ConclusionTypeGroupInfos, info)
	}

	return resItem, nil
}

func getMiniLanguageAudioTaskIdCensorResult(logCtx context.Context, censorResult, taskid string, status enum.TaskStatus) (proto.RiskControlShortAudioResponse, error) {
	resItem := proto.RiskControlShortAudioResponse{
		LogID: utils.GetLogID(logCtx),
	}

	censorRes := minicensor.CensorTextMiniLanguageResponse{}
	err := json.Unmarshal([]byte(censorResult), &censorRes)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"censorVideo Unmarshal fail, err:%v", err)
		return resItem, err
	}

	resItem = proto.RiskControlShortAudioResponse{
		LogID: utils.GetLogID(logCtx),
		Data: proto.RiskControlAudioResponseData{
			TaskId:                   taskid,
			Status:                   string(status),
			ErrorCode:                censorRes.ErrorCode,
			ErrorMsg:                 censorRes.ErrorMsg,
			Conclusion:               "合规",
			ConclusionType:           int(censorRes.Data.LabelID),
			ConclusionTypeGroupInfos: []proto.RiskControlAudioConclusionTypeGroupInfos{},
		},
	}

	if censorRes.Data.LabelID != 0 && censorRes.Data.LabelID != -1 {
		resItem.Data.Conclusion = "不合规"
		resItem.Data.ConclusionType = int(censorRes.Data.LabelID)
		resItem.Data.ConclusionTypeGroupInfos = append(resItem.Data.ConclusionTypeGroupInfos, proto.RiskControlAudioConclusionTypeGroupInfos{
			Msg: minicensor.GetCensorLabel(censorRes.Data.LabelID),
		})
	}

	return resItem, nil
}

func (rsa *RiskControlShortAudio) audioCensorPost(url string, rate int, fmt string, strategyId int, options map[string]interface{}) (result string) {
	data := make(map[string]string)
	data["url"] = url
	data["fmt"] = fmt
	data["rate"] = strconv.FormatInt(int64(rate), 10)
	data["strategyId"] = strconv.Itoa(strategyId)
	for key, val := range options {
		switch val := val.(type) {
		case bool:
			data[key] = strconv.FormatBool(val)
		}
	}
	return synccensorclient.PostUrlForm(__voiceCensorUserDefinedUrl, data, &rsa.auth)
}

func findDbCacheByAudioWithUrlMd5(logCtx context.Context, urlmd5 string) (proto.RiskControlShortAudioResponse, error) {
	resItem := proto.RiskControlShortAudioResponse{
		LogID: utils.GetLogID(logCtx),
	}

	newitem, err := (&model.RiskControlItem{}).GetRiskControlItemFromUrlMd5(gomysql.DB, urlmd5)
	if err != nil {
		logger.Log.Warnf(utils.MMark(logCtx)+" GetRiskControlItemFromUrlMd5 fail, err:%v", err)
		return resItem, err
	}

	logCtx = context.WithValue(context.TODO(), utils.CtxKeyLogID, newitem.LogId)
	logger.Log.Info(utils.MMark(logCtx) + "  found success")
	// 判断是长音频还是短音频
	if newitem.Type == model.RiskControlTypeShortAudio {
		resItem, err = getShortAudioTaskIdCensorResult(logCtx, newitem.CensorResult, newitem.TaskId, newitem.Status)
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+" getShortAudioTaskIdCensorResult fail, err:%v", err)
			return resItem, err
		}
	} else if newitem.Type == model.RiskControlTypeLongAudio {
		resItem, err = getLongAudioTaskIdCensorResult(logCtx, newitem.TaskId)
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+" getLongAudioTaskIdCensorResult fail, err:%v", err)
			return resItem, err
		}
	} else if newitem.Type == model.RiskControlTypeAudioMiniLanguge {
		resItem, err = getMiniLanguageAudioTaskIdCensorResult(logCtx, newitem.CensorResult, newitem.TaskId, newitem.Status)
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+" getTaskIdCensorResult fail, err:%v", err)
			return resItem, err
		}
	} else if newitem.Type == model.RiskControlTypeAudioGCP {
		err = json.Unmarshal([]byte(newitem.CensorResult), &resItem)
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+" Unmarshal fail, err:%v", err)
			return resItem, err
		}
	} else {
		logger.Log.Errorf(utils.MMark(logCtx)+" type not support type:%s", newitem.Type)
		return resItem, fmt.Errorf("type not support type:%s", newitem.Type)
	}

	return resItem, nil
}

func findDbCacheByAudioWithFileMd5(logCtx context.Context, md5, url, urlMd5 string) (proto.RiskControlShortAudioResponse, error) {
	resItem := proto.RiskControlShortAudioResponse{
		LogID: utils.GetLogID(logCtx),
	}

	newitem, err := (&model.RiskControlItem{}).GetRiskControlItemFromMd5(gomysql.DB, md5)
	if err != nil {
		logger.Log.Warnf(utils.MMark(logCtx)+" GetRiskControlItemFromMd5 fail, err:%v", err)
		return resItem, err
	}

	logger.Log.Info(utils.MMark(logCtx) + " found success")

	// 兼容性处理，更新url和urlMd5，因历史的url可能没有进行内网转换处理，导致无法命中缓存
	if newitem.FileUrl != url {
		newitem.FileUrl = url
		newitem.UrlMd5 = urlMd5
		newitem.UpdateRiskControlItem(gomysql.DB)
	}

	// 判断是长音频还是短音频
	if newitem.Type == model.RiskControlTypeShortAudio {
		resItem, err = getShortAudioTaskIdCensorResult(logCtx, newitem.CensorResult, newitem.TaskId, newitem.Status)
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+"  getShortAudioTaskIdCensorResult fail, err:%v", err)
			return resItem, err
		}
	} else if newitem.Type == model.RiskControlTypeLongAudio {
		resItem, err = getLongAudioTaskIdCensorResult(logCtx, newitem.TaskId)
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+" getLongAudioTaskIdCensorResult fail, err:%v", err)
			return resItem, err
		}
	} else if newitem.Type == model.RiskControlTypeAudioMiniLanguge {
		resItem, err = getMiniLanguageAudioTaskIdCensorResult(logCtx, newitem.CensorResult, newitem.TaskId, newitem.Status)
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+" getTaskIdCensorResult fail, err:%v", err)
			return resItem, err
		}
	} else if newitem.Type == model.RiskControlTypeAudioGCP {
		err = json.Unmarshal([]byte(newitem.CensorResult), &resItem)
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+" Unmarshal fail, err:%v", err)
			return resItem, err
		}
	} else {
		logger.Log.Errorf(utils.MMark(logCtx)+" type not support type:%s", newitem.Type)
		return resItem, fmt.Errorf("type not support type:%s", newitem.Type)
	}

	return resItem, nil
}

func AudioTranscode(logCtx context.Context, audiopath, codecName string, rate int) (string, bool, error) {
	dstaudiopath := ""
	if strings.Contains(codecName, "pcm") {
		channel, err := ffmpegutils.GetAudioChannel(audiopath)
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+"ffmpegutils.GetAudioChannel fail, err:%v", err)
			return "", false, err
		}
		if rate > 16000 || channel == 2 {
			// 转码为16K
			dstaudiopath = DownloadFilePrefix + uuid.New().String() + ".wav"
			err = ffmpegutils.AudioTranscoding(audiopath, dstaudiopath)
			if err != nil {
				logger.Log.Errorf(utils.MMark(logCtx)+"ffmpegutils.AudioTranscoding fail, err:%v", err)
				return "", false, err
			}
		} else {
			return audiopath, false, nil
		}
	} else {
		return audiopath, false, nil
	}

	if ok, err := handlerUtils.FileExists(dstaudiopath); err != nil || !ok {
		logger.Log.Errorf(utils.MMark(logCtx)+"FileExists fail, err:%v", err)
		return "", false, errors.New("话音转码文件不存在")
	}

	return dstaudiopath, true, nil
}

func AudioTranscodeByMiniLanguage(logCtx context.Context, audiopath, codecName string, rate int) (string, bool, error) {
	dstaudiopath := ""
	channel, err := ffmpegutils.GetAudioChannel(audiopath)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+" ffmpegutils.GetAudioChannel fail, err:%v", err)
		return audiopath, false, err
	}

	if (strings.Contains(codecName, "pcm") || strings.Contains(codecName, "mp3")) && channel == 1 {
		return audiopath, false, nil
	}

	dstaudiopath = DownloadFilePrefix + uuid.New().String() + ".wav"
	err = ffmpegutils.AudioTranscoding(audiopath, dstaudiopath)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"ffmpegutils.AudioTranscoding fail, err:%v", err)
		return "", false, err
	}

	logger.Log.Infof(utils.MMark(logCtx)+" AudioTranscodeByMiniLanguage success codecName:%s, dstaudiopath:%s", codecName, dstaudiopath)
	if ok, err := handlerUtils.FileExists(dstaudiopath); err != nil || !ok {
		logger.Log.Errorf(utils.MMark(logCtx)+"FileExists fail, err:%v", err)
		return "", false, errors.New("话音转码文件不存在")
	}

	return dstaudiopath, true, nil
}

func censorAudioByInternational(logCtx context.Context, accountID, audioURL, fpath, md5, urlmd5 string) (proto.RiskControlShortAudioResponse, error) {
	resItem := proto.RiskControlShortAudioResponse{
		LogID: utils.GetLogID(logCtx),
	}

	// 申请qps限制
	key := fmt.Sprintf(RiskControlMiniAudioQpsFmt, handlerUtils.GetNameByRunEnv())
	for {
		ok, err := checkQpsAllowance(key, config.LocalConfig.RiskControl.MiniAudioQpsMax)
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+"censorAudio checkQpsAllowance is err:%v", err)
			return resItem, err
		}

		if !ok {
			time.Sleep(10 * time.Millisecond)
			continue
		}

		break
	}
	starttime := time.Now()

	ffinfo := ffmpegutils.ExecuteFFprobe(fpath)
	if ffinfo.Error != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"ffprobe fail, err:%v", ffinfo.Error)
		return resItem, ffinfo.Error
	}

	rate, err := strconv.Atoi(ffinfo.SampleRate)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+" strconv.Atoi fail, err:%v", err)
		return resItem, err
	}

	censorUrl := audioURL
	// 判断音频是否需要转码
	dstaudiopath, ok, err := AudioTranscodeByMiniLanguage(logCtx, fpath, ffinfo.CodecName, rate)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+" AudioTranscodeByMiniLanguage fail, err:%v", err)
		return resItem, err
	}

	if ok {
		// 需要转码
		defer os.Remove(dstaudiopath)
		fpath = dstaudiopath
		// 上传 bos 然后审核
		bosUrl, err := RetryUploadBosServiceFromFile(logCtx, FigureRiskControlAudioPath, fpath, path.Ext(fpath))
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+" RetryUploadBosServiceFromFile fail, err:%v", err)
			return resItem, err
		}
		censorUrl = bosUrl
		ffinfo = ffmpegutils.ExecuteFFprobe(fpath)
		if ffinfo.Error != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+" ffprobe fail, err:%v", ffinfo.Error)
			return resItem, ffinfo.Error
		}

		rate, err = strconv.Atoi(ffinfo.SampleRate)
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+" strconv.Atoi fail, err:%v", err)
			return resItem, err
		}
	}

	encoding := speechpb.RecognitionConfig_LINEAR16
	if strings.Contains(ffinfo.CodecName, "mp3") {
		encoding = speechpb.RecognitionConfig_MP3
	}

	censorRes, err := censorAudioGcp(logCtx, censorUrl, encoding, int32(rate))
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+" censorAudioGcp fail, err:%v", err)
		return resItem, err
	}

	taskstatus := enum.TaskSucceed
	if censorRes.Data.ErrorCode != 0 {
		taskstatus = enum.TaskFailed
	}

	taskid := "rca-" + utils.RandStringRunes(16)

	endtime := time.Now()
	durationtime := endtime.Sub(starttime).Milliseconds()/1000 + 1
	// 判断话语是否转码过，没有转码过，则置空
	if censorUrl == audioURL {
		censorUrl = ""
	}

	censorRes.Data.TaskId = taskid
	censorRes.Data.Status = string(taskstatus)
	cresByte, err := json.Marshal(censorRes)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"json.Marshal fail, err:%v", err)
		return resItem, err
	}

	rcitem := &model.RiskControlItem{
		AccountID:      accountID,
		LogId:          utils.GetLogID(logCtx),
		TaskId:         taskid,
		FileUrl:        audioURL,
		CompressionUrl: censorUrl,
		AudioType:      ffinfo.CodecName,
		AudioRate:      ffinfo.SampleRate,
		Type:           model.RiskControlTypeAudioGCP,
		Md5:            md5,
		UrlMd5:         urlmd5,
		ConclusionType: int64(censorRes.Data.ConclusionType),
		CensorResult:   string(cresByte),
		Status:         taskstatus,
		Duration:       durationtime,
		RetryNumber:    1,
		CreatedAt:      starttime,
		UpdatedAt:      endtime,
	}

	if err := rcitem.UpdateRiskControlItem(gomysql.DB); err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"censorVideo UpdateRiskControlItem fail, url:%s", audioURL)
		return resItem, err
	}

	return censorRes, nil
}

func censorAudioGcp(logCtx context.Context, url string, encoding speechpb.RecognitionConfig_AudioEncoding, sampleRateHertz int32) (proto.RiskControlShortAudioResponse, error) {
	resItem := proto.RiskControlShortAudioResponse{
		LogID: utils.GetLogID(logCtx),
		Data: proto.RiskControlAudioResponseData{
			Conclusion:               "合规",
			ConclusionType:           1,
			ConclusionTypeGroupInfos: []proto.RiskControlAudioConclusionTypeGroupInfos{},
		},
	}
	if url == "" {
		return resItem, errors.New("url is empty")
	}

	text, err := gcpcensor.TranscribeLocalFile(logCtx, url, encoding, sampleRateHertz)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+" TranscribeLocalFile fail, err:%v", err)
		return resItem, err
	}

	if len(text) == 0 {
		return resItem, nil
	}

	textCensorDataItem, err := censorTextGcp(logCtx, text)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+" CensorTextByGcp fail, err:%v", err)
		return resItem, err
	}

	resItem = proto.RiskControlShortAudioResponse{
		LogID: utils.GetLogID(logCtx),
		Data: proto.RiskControlAudioResponseData{
			Conclusion:               textCensorDataItem.Conclusion,
			ConclusionType:           int(textCensorDataItem.ConclusionType),
			ConclusionTypeGroupInfos: []proto.RiskControlAudioConclusionTypeGroupInfos{},
		},
	}

	// 不合规
	if textCensorDataItem.ConclusionType == 2 {
		for _, dataDetails := range textCensorDataItem.Data {
			resItem.Data.ConclusionTypeGroupInfos = append(resItem.Data.ConclusionTypeGroupInfos, proto.RiskControlAudioConclusionTypeGroupInfos{
				Words: dataDetails.Words,
				Msg:   dataDetails.Msg,
			})
		}
	}

	return resItem, nil
}
