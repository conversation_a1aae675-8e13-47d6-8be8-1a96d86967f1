package figure

import (
	"acg-ai-go-common/gomysql"
	"acg-ai-go-common/logger"
	"acg-ai-go-common/utils"
	"context"
	"dhlive-external-api/beans/model"
	"dhlive-external-api/beans/proto"
	"dhlive-external-api/mysqlclient"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

func CreatMiniProgramID(c *gin.Context) {
	aid, _ := c.Get("AccountId")
	accountID := aid.(string)

	// 生成唯一id号
	id := "mini-" + utils.RandStringRunes(16)
	logCtx := context.WithValue(context.TODO(), utils.CtxKeyLogID, id)

	// 查询此accountID是否已存在
	info := model.MiniprogramInfo{}
	newinfo, err := info.GetMiniprogramInfoFromOpenID(gomysql.DB, accountID)
	if err == nil {
		logger.Log.Infof(utils.MMark(logCtx)+"CreatMiniProgramID GetMiniprogramInfoFromOpenID success accountID: %s", accountID)
		c.JSON(http.StatusOK, proto.NewSuccessRsp(&proto.GetMiniprogramIDResponse{
			UniqueId: newinfo.UniqueId,
			LogID:    utils.GetLogID(logCtx),
		}))
		return
	}

	// 不存在则创建
	info = model.MiniprogramInfo{
		OpenId:    accountID,
		UniqueId:  id,
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	err = info.CreateMiniprogramInfo(gomysql.DB)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+utils.MysqlEsErrMsg+"CreatMiniProgramID CreateMiniprogramInfo error: %v", err)
		c.JSON(http.StatusOK, proto.NewCommDataRsp(100002, "创建数据库异常", &proto.GetMiniprogramIDResponse{
			UniqueId: "",
			LogID:    utils.GetLogID(logCtx),
		}))
		return
	}

	c.JSON(http.StatusOK, proto.NewSuccessRsp(&proto.GetMiniprogramIDResponse{
		UniqueId: id,
		LogID:    utils.GetLogID(logCtx),
	}))
}

func CopyProperty(c *gin.Context) {
	logCtx := context.WithValue(context.TODO(), utils.CtxKeyLogID, utils.RandStringRunes(16))
	req := proto.CopyMiniprogramRequst{}
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"CopyProperty ShouldBindJSON error: %+v", err)
		c.JSON(http.StatusOK, proto.NewCommDataRsp(100001, "参数异常", &proto.CopyMiniprogramResponse{
			LogID: utils.GetLogID(logCtx),
		}))
		return
	}

	aid, _ := c.Get("AccountId")
	accountID := aid.(string)

	if len(req.UniqueId) == 0 {
		logger.Log.Errorf(utils.MMark(logCtx) + "CopyProperty req.UniqueId is empty")
		c.JSON(http.StatusOK, proto.NewCommDataRsp(100002, "参数为空", &proto.CopyMiniprogramResponse{
			LogID: utils.GetLogID(logCtx),
		}))
		return
	}

	logCtx = context.WithValue(context.TODO(), utils.CtxKeyLogID, req.UniqueId)
	miniinfo := model.MiniprogramInfo{}

	// 根据uniqueId查询出对应的openId
	newminiinfo, err := miniinfo.GetMiniprogramInfoFromUniqueId(gomysql.DB, req.UniqueId)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"CopyProperty GetMiniprogramInfoFromUniqueId error: %v", err)
		c.JSON(http.StatusOK, proto.NewCommDataRsp(100003, "查询数据库异常", &proto.CopyMiniprogramResponse{
			LogID: utils.GetLogID(logCtx),
		}))
		return
	}

	logger.Log.Infof(utils.MMark(logCtx)+"CopyProperty accountID: %s openid: %s", accountID, newminiinfo.OpenId)

	// 判断openid和当前的accountID是否相同， 相同则无需拷贝资产
	if newminiinfo.OpenId == accountID {
		logger.Log.Warnf(utils.MMark(logCtx)+"CopyProperty req.UniqueId is same as accountID, accountID: %s", accountID)
		c.JSON(http.StatusOK, proto.NewSuccessRsp(&proto.CopyMiniprogramResponse{
			LogID: utils.GetLogID(logCtx),
		}))
		return
	}

	// 判断accountID是否已领取过该专享链接
	if newminiinfo.AccountID == accountID {
		logger.Log.Warnf(utils.MMark(logCtx)+"CopyProperty req.UniqueId is same as accountID, accountID: %s", accountID)
		c.JSON(http.StatusOK, proto.NewCommDataRsp(200001, "您已完成过人像同步及权益领取，请勿重复领取。", &proto.CopyMiniprogramResponse{
			LogID: utils.GetLogID(logCtx),
		}))
		return
	}

	// 判断该专享链接是否被使用过
	if len(newminiinfo.AccountID) != 0 {
		logger.Log.Warnf(utils.MMark(logCtx)+"CopyProperty accountID is not empty, accountID: %s", accountID)
		c.JSON(http.StatusOK, proto.NewCommDataRsp(200002, "该专享链接已被使用过。", &proto.CopyMiniprogramResponse{
			LogID: utils.GetLogID(logCtx),
		}))
		return
	}

	// 判断accountID是否生成过小程序人像
	tmpfv := model.FigureV1{}
	newtmpfv, err := tmpfv.GetFigureV1FromUserIDAndSource(mysqlclient.DbMap[mysqlclient.Text2FigureDBName], accountID, "Applet")
	if err == nil {
		logger.Log.Warnf(utils.MMark(logCtx)+"CopyProperty GetFigureV1FromUserIDAndSource is find id: %d", newtmpfv.ID)
		c.JSON(http.StatusOK, proto.NewCommDataRsp(200003, "您已领取过小程序专享权益，请更换其他账号领取此专享权益。", &proto.CopyMiniprogramResponse{
			LogID: utils.GetLogID(logCtx),
		}))
		return
	}

	// starLight数据库使用事物
	err = mysqlclient.DbMap[mysqlclient.StarLightDBName].Transaction(func(starLightTX *gorm.DB) error {
		// 人像数据库使用事务
		err = mysqlclient.DbMap[mysqlclient.Text2FigureDBName].Transaction(func(tx *gorm.DB) error {
			// 拷贝 UserFigure 和 人像（FigureV1）资产到当前账号
			err = CopyUserFigure(logCtx, starLightTX, tx, newminiinfo.OpenId, accountID)
			if err != nil {
				logger.Log.Errorf(utils.MMark(logCtx)+"CopyProperty CopyUserFigure openID:%s accountID: %s error: %v", err, newminiinfo.OpenId, accountID)
				return err
			}

			err = CopyRecordVideoTaskV1(logCtx, tx, newminiinfo.OpenId, accountID)
			if err != nil {
				logger.Log.Errorf(utils.MMark(logCtx)+"CopyProperty CopyRecordVideoTaskV1 openID:%s accountID: %s error: %v", err, newminiinfo.OpenId, accountID)
				return err
			}

			err = CopyFigureTaskV1(logCtx, tx, newminiinfo.OpenId, accountID)
			if err != nil {
				logger.Log.Errorf(utils.MMark(logCtx)+"CopyProperty CopyFigureTaskV1 openID:%s accountID: %s error: %v", err, newminiinfo.OpenId, accountID)
				return err
			}

			// 最后一个数据库操作 所以可以不用事物数据库操作
			newminiinfo.AccountID = accountID
			newminiinfo.UpdatedAt = time.Now()
			err = newminiinfo.UpdateMiniprogramInfo(gomysql.DB)
			if err != nil {
				logger.Log.Errorf(utils.MMark(logCtx)+"CopyProperty UpdateMiniprogramInfo error: %v newminiinfo: %v", err, newminiinfo)
				return err
			}

			return nil // 提交事务
		})

		return err
	})

	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"CopyProperty Transaction error: %v", err)
		c.JSON(http.StatusOK, proto.NewCommDataRsp(100005, "拷贝人像资产异常", &proto.CopyMiniprogramResponse{
			LogID: utils.GetLogID(logCtx),
		}))
		return
	}

	c.JSON(http.StatusOK, proto.NewSuccessRsp(&proto.CopyMiniprogramResponse{
		LogID: utils.GetLogID(logCtx),
	}))
}

func CopyUserFigure(logCtx context.Context, starLightTX, tx *gorm.DB, openID, accountID string) error {
	// 拷贝 UserFigure 表资产
	uf := model.UserFigure{}
	ufs, err := uf.GetUserFigureByUserID(starLightTX, openID)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"CopyProperty GetUserFigureByUserID error: %v", err)
		return err
	}

	for i, v := range ufs {
		needcopyid := v.ID
		v.ID = 0             // 防止修改原始数据，将数据库id清空
		v.UserID = accountID // 修改userID,拷贝资产到当前账号

		err = v.CreateUserFigure(starLightTX)
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+"CopyProperty CreateUserFigure error: %v needcopyid:%d dbinfo: %v", err, needcopyid, v)
			return err
		}

		logger.Log.Infof(utils.MMark(logCtx)+"CopyProperty UserFigure copy index: %d/%d needcopy id: %d copy id : %d", i, len(ufs), needcopyid, v.ID)
		// 判断FigureV1表中是否更新过starLightId字段
		tmpfv1 := model.FigureV1{}
		// 判断 人像是否保存
		oldv, err := tmpfv1.GetFigureV1FromUserIDAndStarLightID(tx, openID, needcopyid)
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+"CopyProperty GetFigureV1FromUserIDAndStarLightID err:%v StarLight id: %d", err, v.ID)
			continue // 人像没有保存跳过
		}

		needcopyid = int64(oldv.ID)
		oldv.ID = 0
		oldv.UserID = accountID
		oldv.StarLightID = uint64(v.ID)
		err = oldv.CreateFigureV1(tx)
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+"CopyProperty CreateFigureV1 error: %v needcopyid:%d dbinfo: %v", err, needcopyid, oldv)
			return err
		}

		logger.Log.Infof(utils.MMark(logCtx)+"CopyProperty  FigureV1 copy starLight index: %d/%d StarLight id: %d db id : %d", i, len(ufs), oldv.StarLightID, oldv.ID)
	}

	return nil
}

func CopyRecordVideoTaskV1(logCtx context.Context, tx *gorm.DB, openID, accountID string) error {
	// 拷贝 RecordVideoTaskV1表 资产
	rvt := model.RecordVideoTaskV1{}
	rvts, err := rvt.GeteRecordVideoTaskV1FromUserID(tx, openID)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"CopyProperty GeteRecordVideoTaskV1FromUserID error: %v", err)
		return err
	}

	logger.Log.Infof(utils.MMark(logCtx)+"CopyProperty RecordVideoTaskV1 find len: %d", len(rvts))

	for i, v := range rvts {
		needcopyid := v.ID
		v.ID = 0             // 防止修改原始数据，将数据库id清空
		v.UserID = accountID // 修改userID,拷贝资产到当前账号

		// 拷贝资产到当前账号
		err = v.CreateRecordVideoTaskV1(tx)
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+"CopyProperty CreateRecordVideoTaskV1 error: %v needcopyid:%d dbinfo: %v", err, needcopyid, v)
			return err
		}

		logger.Log.Infof(utils.MMark(logCtx)+"CopyProperty RecordVideoTaskV1 copy index: %d/%d needcopyid id: %d copy id : %d", i, len(rvts), needcopyid, v.ID)
	}
	return nil
}

func CopyFigureTaskV1(logCtx context.Context, tx *gorm.DB, openID, accountID string) error {
	// 拷贝 FigureTaskV1 表资产
	ftv1 := model.FigureTaskV1{}
	ftv1s, err := ftv1.GetFigureTaskV1FromUserID(tx, openID)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"CopyProperty GetFigureTaskV1FromUserID error: %v", err)
		return err
	}

	logger.Log.Infof(utils.MMark(logCtx)+"CopyProperty FigureTaskV1 find len: %d", len(ftv1s))
	for i, v := range ftv1s {
		needcopyid := v.ID
		v.ID = 0             // 防止修改原始数据，将数据库id清空
		v.UserID = accountID // 修改userID,拷贝资产到当前账号

		err = v.CreateFigureTaskV1(tx)
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+"CopyProperty CreateFigureTaskV1 error: %v needcopyid:%d dbinfo: %v", err, needcopyid, v)
			return err
		}

		logger.Log.Infof(utils.MMark(logCtx)+"CopyProperty CreateFigureTaskV1 copy index: %d/%d needcopyid id: %d copy id : %d", i, len(ftv1s), needcopyid, v.ID)
	}

	return nil
}
