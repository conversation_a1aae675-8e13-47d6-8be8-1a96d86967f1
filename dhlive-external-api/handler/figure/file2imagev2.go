package figure

import (
	"acg-ai-go-common/gomysql"
	"acg-ai-go-common/logger"
	"acg-ai-go-common/utils"
	"acg-ai-go-common/utils/redisproxy"
	"bytes"
	"context"
	"dhlive-external-api/beans/enum"
	"dhlive-external-api/beans/model"
	"dhlive-external-api/beans/proto"
	"dhlive-external-api/handler/handlerUtils"
	"dhlive-external-api/handler/handlerUtils/redislock"
	"encoding/json"
	"errors"
	"fmt"
	"image"
	"image/jpeg"
	"image/png"

	"io"
	"math"
	"net/http"
	"os"
	"path"
	"strings"
	"time"

	"github.com/disintegration/imaging"
	"github.com/gin-gonic/gin"
	"github.com/go-redis/redis/v8"
	"github.com/google/uuid"
	"gorm.io/gorm"
)

const (
	FileSizeLimitMaxV2 = 100 * 1024 * 1024 // 文件大小限制
	FilePageLimitMaxV2 = 30                // 文件页数限制
)

func UploadCoursewareFile(c *gin.Context) {
	coursewareTaskid := "cid-" + utils.RandStringRunes(16)
	logCtx := context.WithValue(context.TODO(), utils.CtxKeyLogID, coursewareTaskid)
	accountID := ""
	aid, ok := c.Get("AccountId")
	if ok {
		accountID = aid.(string)
	}

	if c.Request.Header.Get("Content-Type") == "multipart/form-data" {
		logger.Log.Errorf(utils.MMark(logCtx) + "CheckFile get Header faild")
		c.JSON(http.StatusOK, proto.NewCommDataRsp(100001, "参数异常", &proto.CommResponse{RequestId: utils.MMark(logCtx)}))
		return
	}

	// 通过 FormFile 获取文件
	reqfile, header, err := c.Request.FormFile("fileData") // "file" 是表单中文件输入的名称
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"CheckFile FormFile fail, err:%v", err)
		c.JSON(http.StatusOK, proto.NewCommDataRsp(100002, "请检查文件", &proto.CommResponse{RequestId: utils.MMark(logCtx)}))
		return
	}
	defer reqfile.Close()

	// 获取文件后缀名
	fileFormat := path.Ext(header.Filename)

	if err = os.MkdirAll(DownloadFilePrefix, 0755); err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"CheckFile os.MkdirAll fail, err:%v", err)
		c.JSON(http.StatusOK, proto.NewCommDataRsp(100003, "创建文件目录失败", &proto.CommResponse{RequestId: utils.MMark(logCtx)}))
		return
	}

	downfilepath := DownloadFilePrefix + uuid.New().String() + fileFormat
	// 打开（或创建）文件用于写入
	outFile, err := os.Create(downfilepath)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"Failed to create output file, err:%v", err)
		c.JSON(http.StatusOK, proto.NewCommDataRsp(100004, "写入文件失败", &proto.CommResponse{RequestId: utils.MMark(logCtx)}))
		return
	}
	defer outFile.Close()
	defer os.Remove(downfilepath)

	// 将请求中的文件内容复制到新文件中
	_, err = io.Copy(outFile, reqfile)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"Failed to copy file content, err:%v", err)
		c.JSON(http.StatusOK, proto.NewCommDataRsp(100005, "文件复制失败", &proto.CommResponse{RequestId: utils.MMark(logCtx)}))
		return
	}

	md5, err := handlerUtils.CalculateMD5FromFile(downfilepath)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"CheckFile CalculateMD5 fail, err:%v", err)
		c.JSON(http.StatusOK, proto.NewCommDataRsp(100006, "获取文件MD5失败", &proto.CommResponse{RequestId: utils.MMark(logCtx)}))
		return
	}

	// 通过md5 查询数据库
	file2image := &model.File2Image{}
	f2i, err := file2image.GetFile2ImageFromMd5(gomysql.DB, md5)
	if err == nil {
		if f2i.ID != 0 {
			// 兼容旧数据
			f2i.PageCountWithinLimit = f2i.PageCount <= FilePageLimitMaxV2
			if !f2i.PageCountWithinLimit {
				c.JSON(http.StatusOK, proto.NewSuccessRsp(&proto.FileFormatCheckResponseV2{
					FileID:                 coursewareTaskid,
					FileFormatIsSupported:  f2i.FileFormatSupported,
					FileSizeIsWithinLimit:  f2i.FileSizeWithinLimit,
					PageCountIsWithinLimit: f2i.PageCountWithinLimit,
					RequestId:              utils.GetLogID(logCtx),
				}))
				return
			}

			// v1版本的ppt解析状态是TaskSucceed 说明是未经过审核的需要修改为审核状态
			if f2i.Status == enum.TaskSucceed {
				f2i.AccountId = accountID
				f2i.Status = enum.TaskCensor
				f2i.UpdatedAt = time.Now()
				err = f2i.UpdateFile2Image(gomysql.DB)
				if err != nil {
					logger.Log.Errorf(utils.MMark(logCtx)+"taskid %s UpdateFile2Image fail, err:%v", f2i.TaskId, err)
					c.JSON(http.StatusOK, proto.NewCommDataRsp(100008, "数据库状态更新失败", &proto.CommResponse{RequestId: utils.MMark(logCtx)}))
					return
				}
			} else if f2i.Status == enum.TaskReady {
				f2i.AccountId = accountID
				f2i.Status = enum.TaskParsing
				f2i.UpdatedAt = time.Now()
				err = f2i.UpdateFile2Image(gomysql.DB)
				if err != nil {
					logger.Log.Errorf(utils.MMark(logCtx)+"taskid %s UpdateFile2Image fail, err:%v", f2i.TaskId, err)
					c.JSON(http.StatusOK, proto.NewCommDataRsp(100008, "数据库状态更新失败", &proto.CommResponse{RequestId: utils.MMark(logCtx)}))
					return
				}
			}

			// 保存 accountID 记录
			err = saveUserCoursewareFile(logCtx, coursewareTaskid, f2i.TaskId, accountID, header.Filename)
			if err != nil {
				logger.Log.Errorf(utils.MMark(logCtx)+utils.MysqlEsErrMsg+"taskid %s CheckFile saveAccountIdRecord fail, err:%v", f2i.TaskId, err)
				c.JSON(http.StatusOK, proto.NewCommDataRsp(100007, "用户保存访问记录失败", &proto.CommResponse{RequestId: utils.MMark(logCtx)}))
				return
			}

			c.JSON(http.StatusOK, proto.NewSuccessRsp(&proto.FileFormatCheckResponseV2{
				FileID:                 coursewareTaskid,
				FileFormatIsSupported:  f2i.FileFormatSupported,
				FileSizeIsWithinLimit:  f2i.FileSizeWithinLimit,
				PageCountIsWithinLimit: f2i.PageCountWithinLimit,
				RequestId:              utils.GetLogID(logCtx),
			}))
			return
		}
	}

	fileFormatIsSupported := handlerUtils.CheckFileFormat(logCtx, fileFormat, downfilepath)
	fileSizeIsWithinLimit := header.Size <= FileSizeLimitMaxV2

	if !fileFormatIsSupported {
		c.JSON(http.StatusOK, proto.NewSuccessRsp(&proto.FileFormatCheckResponseV2{
			FileFormatIsSupported:  fileFormatIsSupported,
			FileSizeIsWithinLimit:  fileSizeIsWithinLimit,
			PageCountIsWithinLimit: false,
			RequestId:              utils.GetLogID(logCtx),
		}))
		return
	}

	pageCountIsWithinLimit, err := handlerUtils.CheckPageCount(logCtx, fileFormat, downfilepath, FilePageLimitMaxV2)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"CheckFile CheckPageCount fail, err:%v", err)
		c.JSON(http.StatusOK, proto.NewCommDataRsp(100009, "文件计算页数失败", &proto.CommResponse{RequestId: utils.MMark(logCtx)}))
		return
	}

	// 文件格式是否支持，文件大小是否在限制内，页数是否在限制内
	if !fileSizeIsWithinLimit || !pageCountIsWithinLimit {
		c.JSON(http.StatusOK, proto.NewSuccessRsp(&proto.FileFormatCheckResponseV2{
			FileFormatIsSupported:  fileFormatIsSupported,
			FileSizeIsWithinLimit:  fileSizeIsWithinLimit,
			PageCountIsWithinLimit: pageCountIsWithinLimit,
			RequestId:              utils.GetLogID(logCtx),
		}))
		return
	}

	// 文件上传到 BOS
	url, err := RetryUploadBosServiceFromFile(logCtx, FigureFile2ImagePath, downfilepath, fileFormat)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+utils.ThirdPartyInterfaceEsErrMsg+"CheckFile RetryUploadBosService fail, err:%v", err)
		c.JSON(http.StatusOK, proto.NewCommDataRsp(100009, "上传BOS失败", &proto.CommResponse{RequestId: utils.MMark(logCtx)}))
		return
	}
	taskid := "fid-" + utils.RandStringRunes(16)
	// 插入数据库
	file2image = &model.File2Image{
		AccountId:            accountID,
		TaskId:               taskid,
		FileUrl:              url,
		Md5:                  md5,
		FileFormatSupported:  fileFormatIsSupported,
		FileSizeWithinLimit:  fileSizeIsWithinLimit,
		PageCountWithinLimit: pageCountIsWithinLimit,
		Status:               enum.TaskParsing,
		CreatedAt:            time.Now(),
		UpdatedAt:            time.Now(),
	}

	if err := file2image.CreateFile2Image(gomysql.DB); err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+utils.MysqlEsErrMsg+"CheckFile CalculateMD5 fail, err:%v", err)
		c.JSON(http.StatusOK, proto.NewCommDataRsp(200001, "数据库写入失败", &proto.CommResponse{RequestId: utils.MMark(logCtx)}))
		return
	}

	// 保存 accountID 记录
	err = saveUserCoursewareFile(logCtx, coursewareTaskid, file2image.TaskId, accountID, header.Filename)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+utils.MysqlEsErrMsg+"CheckFile saveUserCoursewareFile fail, err:%v", err)
		c.JSON(http.StatusOK, proto.NewCommDataRsp(200002, "用户保存访问记录失败", &proto.CommResponse{RequestId: utils.MMark(logCtx)}))
		return
	}

	c.JSON(http.StatusOK, proto.NewSuccessRsp(&proto.FileFormatCheckResponseV2{
		FileID:                 coursewareTaskid,
		FileFormatIsSupported:  fileFormatIsSupported,
		FileSizeIsWithinLimit:  fileSizeIsWithinLimit,
		PageCountIsWithinLimit: pageCountIsWithinLimit,
		RequestId:              utils.GetLogID(logCtx),
	}))
}

func GetParseResultV2(c *gin.Context) {
	logCtx := context.WithValue(context.TODO(), utils.CtxKeyLogID, utils.RandStringRunes(16))
	accountID := ""
	aid, ok := c.Get("AccountId")
	if ok {
		accountID = aid.(string)
	}
	res := &proto.File2ImageResponse{
		PageCount:  0,
		ImageInfos: "",
		Status:     enum.TaskFailed,
		RequestId:  utils.GetLogID(logCtx),
	}

	req := &proto.File2ImageRequestV2{}
	if err := c.ShouldBindJSON(req); err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"GetParseResult ShouldBindJSON err:%v\n", err)
		c.JSON(http.StatusOK, proto.NewCommDataRsp(100001, "参数异常", res))
		return
	}
	logCtx = context.WithValue(context.TODO(), utils.CtxKeyLogID, req.FileID)

	userCoursewareFile, err := (&model.UserCoursewareFile{}).GetUserCoursewareFile(gomysql.DB, accountID, req.FileID)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+" GetUserCoursewareFile failed: %v", err)
		c.JSON(http.StatusOK, proto.NewCommDataRsp(100002, "用户没有上传此文件", res))
		return
	}
	logger.Log.Infof("GetParseResult ID:%s taskid:%s", req.FileID, userCoursewareFile.TaskId)
	f2Image, err := (&model.File2Image{}).GetFile2ImageFromTaskId(gomysql.DB, userCoursewareFile.TaskId)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+utils.MysqlEsErrMsg+"GetParseResult ID:%d GetFile2Image fail, err:%v", req.FileID, err)
		c.JSON(http.StatusOK, proto.NewCommDataRsp(100003, "数据库查询失败", res))
		return
	}

	if f2Image.Status == enum.TaskFailed {
		if userCoursewareFile.Status != enum.TaskFailed {
			userCoursewareFile.Status = enum.TaskFailed
			err = userCoursewareFile.Update(gomysql.DB)
			if err != nil {
				logger.Log.Errorf(utils.MMark(logCtx)+"GetParseResult ID:%d Update failed: %v", req.FileID, err)
			}
		}

		if len(f2Image.Message) > 0 {
			c.JSON(http.StatusOK, proto.NewCommDataRsp(100005, f2Image.Message, res))
			return
		}

		c.JSON(http.StatusOK, proto.NewCommDataRsp(100005, "解析文件失败", res))
		return
	} else if f2Image.Status == enum.TaskCensorFailed {
		logger.Log.Errorf(utils.MMark(logCtx)+"GetParseResult ID:%s task failed", req.FileID)
		if len(f2Image.Message) > 0 {
			censorList := make([]*proto.CensorResultList, 0)
			err = json.Unmarshal([]byte(f2Image.Message), &censorList)
			if err == nil {
				res.CensorResultList = censorList
				c.JSON(http.StatusOK, proto.NewSuccessRsp(res))
				return
			}

			c.JSON(http.StatusOK, proto.NewCommDataRsp(100005, f2Image.Message, res))
			return
		}
		c.JSON(http.StatusOK, proto.NewCommDataRsp(100005, "解析文件失败", res))
		return
	} else if f2Image.Status == enum.TaskReady || f2Image.Status == enum.TaskParsing || f2Image.Status == enum.TaskCensor {
		c.JSON(http.StatusOK, proto.NewSuccessRsp(&proto.File2ImageResponse{
			Status:    f2Image.Status,
			RequestId: utils.GetLogID(logCtx),
		}))
		return
	} else if f2Image.Status == enum.TaskCensorSucceed {
		if userCoursewareFile.Status != enum.TaskCensorSucceed {
			c.JSON(http.StatusOK, proto.NewSuccessRsp(&proto.File2ImageResponse{
				Status:    enum.TaskCensor,
				RequestId: utils.GetLogID(logCtx),
			}))
			return
		}
		// 审核通过进入下面处理即可
	} else {
		logger.Log.Errorf(utils.MMark(logCtx)+" ID:%s task status error", req.FileID)
		c.JSON(http.StatusOK, proto.NewCommDataRsp(100007, "文件状态异常,请联系管理员", res))
		return
	}

	usercourse := &proto.File2ImageInfoList{}
	err = json.Unmarshal([]byte(userCoursewareFile.ImageInfos), &usercourse)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"  fail, err:%v", err)
		c.JSON(http.StatusOK, proto.NewCommDataRsp(100008, "json解析失败", res))
		return
	}

	strinfo, err := json.Marshal(usercourse.ImageInfos)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"  fail, err:%v", err)
		c.JSON(http.StatusOK, proto.NewCommDataRsp(100009, "json解析失败", res))
		return
	}

	c.JSON(http.StatusOK, proto.NewSuccessRsp(&proto.File2ImageResponse{
		PageCount:  len(usercourse.ImageInfos),
		ImageInfos: string(strinfo),
		Status:     userCoursewareFile.Status,
		RequestId:  utils.GetLogID(logCtx),
	}))
}

func GetUserCoursewareFile(c *gin.Context) {
	logCtx := context.WithValue(context.TODO(), utils.CtxKeyLogID, utils.RandStringRunes(16))
	accountID := ""
	aid, ok := c.Get("AccountId")
	if ok {
		accountID = aid.(string)
	}
	res := &proto.GetUserCoursewareFileListResponse{
		LogId:     utils.GetLogID(logCtx),
		PageCount: 0,
		FileList:  []proto.GetUserCoursewareFileListItem{},
	}

	infos, err := (&model.UserCoursewareFile{}).GetUserCoursewareFileList(gomysql.DB, accountID)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+" GetUserCoursewareFile fail, err:%v", err)
		c.JSON(http.StatusOK, proto.NewCommDataRsp(100001, "数据库查询失败", res))
		return
	}

	for _, v := range infos {
		usercourse := &proto.File2ImageInfoList{}
		err = json.Unmarshal([]byte(v.ImageInfos), &usercourse)
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+" GetUserCoursewareFile fail, err:%v", err)
			c.JSON(http.StatusOK, proto.NewCommDataRsp(100002, "json解析失败", res))
			return
		}
		strinfo, err := json.Marshal(usercourse.ImageInfos)
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+" GetUserCoursewareFile fail, err:%v", err)
			c.JSON(http.StatusOK, proto.NewCommDataRsp(100003, "json解析失败", res))
			return
		}
		res.FileList = append(res.FileList, proto.GetUserCoursewareFileListItem{
			Name:       v.FileName,
			FileId:     v.CoursewareTaskId,
			ImageInfos: string(strinfo),
		})
	}

	res.PageCount = len(infos)
	c.JSON(http.StatusOK, proto.NewSuccessRsp(res))
}

func DeletedAtFile2Image(c *gin.Context) {
	logCtx := context.WithValue(context.TODO(), utils.CtxKeyLogID, utils.RandStringRunes(16))
	accountID := ""
	aid, ok := c.Get("AccountId")
	if ok {
		accountID = aid.(string)
	}
	req := &proto.DeleteUserCoursewareFileRequest{}
	if err := c.ShouldBindJSON(req); err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"DeletedAtFile2Image ShouldBindJSON err:%v\n", err)
		return
	}

	err := (&model.UserCoursewareFile{}).SetUserCoursewareFile(gomysql.DB, accountID, req)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+" SetUserCoursewareFile fail, err:%v", err)
		c.JSON(http.StatusOK, proto.NewCommDataRsp(100001, "数据库查询失败", nil))
		return
	}
	redisproxy := redisproxy.GetRedisProxy()
	redisKey := fmt.Sprintf(File2ImageKeyFmt+"_user_courseware_status", handlerUtils.GetNameByRunEnv(), req.FileId)
	err = redisproxy.DeleteKey(redisKey)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+" DeleteKey fail, err:%v", err)
		c.JSON(http.StatusOK, proto.NewCommDataRsp(100002, "缓存清除失败", nil))
		return
	}

	c.JSON(http.StatusOK, proto.NewSuccessRsp(&proto.CommResponse{
		RequestId: utils.GetLogID(logCtx),
	}))
}

func StopParseResultV2(c *gin.Context) {
	logCtx := context.WithValue(context.TODO(), utils.CtxKeyLogID, utils.RandStringRunes(16))
	accountID := ""
	aid, ok := c.Get("AccountId")
	if ok {
		accountID = aid.(string)
	}

	req := &proto.File2ImageRequestV2{}
	if err := c.ShouldBindJSON(req); err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"StopParseResult ShouldBindJSON err:%v\n", err)
		c.JSON(http.StatusOK, proto.NewCommDataRsp(100001, "参数异常", &proto.CommResponse{RequestId: utils.GetLogID(logCtx)}))
		return
	}

	logCtx = context.WithValue(context.TODO(), utils.CtxKeyLogID, req.FileID)
	err := gomysql.DB.Transaction(func(tx *gorm.DB) error {
		userourse, err := (&model.UserCoursewareFile{}).GetUserCoursewareFileByForUpdate(tx, accountID, req.FileID)
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+" GetUserCoursewareFile fail, err:%v", err)
			return errors.New("数据库查询失败")
		}

		err = userourse.DeleteUserCoursewareFile(tx, accountID, req.FileID)
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+" DeleteUserCoursewareFile fail, err:%v", err)
			return errors.New("数据库删除失败")
		}
		return nil
	})

	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+" StopParseResultV2 fail, err:%v", err)
		c.JSON(http.StatusOK, proto.NewCommDataRsp(100002, err.Error(), &proto.CommResponse{RequestId: utils.GetLogID(logCtx)}))
		return
	}

	c.JSON(http.StatusOK, proto.NewSuccessRsp(&proto.CommResponse{RequestId: utils.GetLogID(logCtx)}))
}

// FileToImageFromDBV2 定时器任务，定时查询文件转图片任务
func FileToImageFromDBV2() {
	file2ImageList, err := (&model.File2Image{}).GetTasksWithStatus(gomysql.DB, enum.TaskParsing)
	if err != nil {
		logger.Log.Errorf("FileToImageFromDB GetAndUpdateStatusToRunning error: %+v\n", err)
		return
	}

	for _, v := range file2ImageList {
		convertToImageV2(v)
	}
}

func convertToImageV2(f2i *model.File2Image) {
	logCtx := context.WithValue(context.TODO(), utils.CtxKeyLogID, f2i.TaskId)
	redisproxy := redisproxy.GetRedisProxy()

	// 获取 redis 分布式锁
	redisLockKey := fmt.Sprintf(File2ImageKeyFmt+"_lock", handlerUtils.GetNameByRunEnv(), f2i.TaskId)
	redisLock := redislock.NewRedisLock(redisproxy.Rdb, redisLockKey, File2ImageKeyLockTimeDuration)
	ok, err := redisLock.Lock(context.Background())
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+utils.RedisEsErrMsg+"convertToImage Lock key: %s error: %+v\n", redisLockKey, err)
		return
	} else if !ok {
		//logger.Log.Warnf(utils.MMark(logCtx)+"convertToImage Lock is occupied key: %+s \n", redisLockKey)
		return
	}

	defer redisLock.Unlock(context.Background())

	// 更新缓存任务状态
	redisKey := fmt.Sprintf(File2ImageKeyFmt, handlerUtils.GetNameByRunEnv(), f2i.TaskId)
	status, err := redisproxy.GetKeyValue(redisKey)
	if err != nil {
		if string(err.Error()) != string(redis.Nil) {
			logger.Log.Errorf(utils.MMark(logCtx)+"convertToImage get task status error: %+v\n", err)
			return
		}

		file2image, err := f2i.GetFile2Image(gomysql.DB, f2i.ID)
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+utils.MysqlEsErrMsg+"convertToImage GetFile2Image err: %v \n", err)
			return
		}
		// 更新 f2i 对象
		f2i = file2image

		err = redisproxy.SetKeyValue(redisKey, string(file2image.Status), File2ImageStateKeyExpire)
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+"convertToImage SetKeyValue err: %v \n", err)
			return
		}

		status = string(file2image.Status)
		// 任务状态不存在，还继续查询任务状态，最后将结果写入redis，防止其他服务重复查询并写入数据库
		logger.Log.Warnf(utils.MMark(logCtx)+"convertToImage get task status is nil set status %s: %+v\n", status, err)
	}

	// 任务已经处理过，跳过
	if status == string(enum.TaskCensor) || status == string(enum.TaskFailed) {
		//logger.Log.Infof(utils.MMark(logCtx)+"convertToImage task status is %s, skip\n", status)
		return
	}

	logger.Log.Infof(utils.MMark(logCtx)+"convertToImage task status is %s \n", status)
	// 更新任务状态为运行中
	defer func(f2i *model.File2Image) {
		file2image, err := f2i.GetFile2Image(gomysql.DB, f2i.ID)
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+"convertToImage GetFile2Image err: %v \n", err)
			return
		}
		// 如果任务还是运行中，则更新为失败状态
		if file2image.Status == enum.TaskParsing {
			file2image.Message = "文件格式异常,重新导出成 pptx/pdf 文件,再重新上传"
			file2image.Status = enum.TaskFailed
			err := file2image.UpdateFile2Image(gomysql.DB)
			if err != nil {
				logger.Log.Errorf(utils.MMark(logCtx)+"convertToImage UpdateFile2Image err: %v \n", err)
			}

			if err := redisproxy.SetKeyValue(redisKey, string(enum.TaskFailed), File2ImageStateKeyExpire); err != nil {
				logger.Log.Errorf(utils.MMark(logCtx)+"convertToImage SetKeyValue err: %v \n", err)
			}
			return
		}

		if err := redisproxy.SetKeyValue(redisKey, string(file2image.Status), File2ImageStateKeyExpire); err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+"convertToImage SetKeyValue err: %v \n", err)
		}
	}(f2i)

	// 下载解析文件
	filekey, err := handlerUtils.ExtractFilenameFromURL(f2i.FileUrl)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"convertToImage ExtractFilenameFromURL err: %v \n", err)
		return
	}
	extension := path.Ext(filekey)

	filename, err := handlerUtils.CreateFileNameFromUrl(DownloadFilePrefix, f2i.FileUrl)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"convertToImage CreateFileNameFromUrl err: %v \n", err)
		return
	}

	pptInfo := proto.PPTFileInfo{}
	// 下载文件,PPT 文件需要下载到本地,转成pdf在本地再进行解析,PDF不需要,读到内存即可,因为PDF文件是直接解析
	if extension == ".pdf" {
		err = downloadBosFileFromPDF(logCtx, filekey, filename)
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+utils.ThirdPartyInterfaceEsErrMsg+"convertToImage downloadBosFile err: %v \n", err)
			return
		}
	} else if extension == ".pptx" {
		filename, pptInfo, err = downloadBosFileFromPPTX(logCtx, filekey)
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+utils.ThirdPartyInterfaceEsErrMsg+"convertToImage downloadBosFileFromPPTX err: %v \n", err)
			return
		}
	} else {
		logger.Log.Errorf(utils.MMark(logCtx)+"not support file type: %s \n", extension)
		return
	}
	defer os.Remove(filename)

	err = convertPDFToImagesV2(logCtx, filename, pptInfo, f2i)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"convertToImage ConvertPDFToImages err: %v \n", err)
		return
	}
}

func convertPDFToImagesV2(logCtx context.Context, filename string, pptInfo proto.PPTFileInfo, f2i *model.File2Image) error {
	redisproxy := redisproxy.GetRedisProxy()
	startTime := time.Now()
	logger.Log.Infof(utils.MMark(logCtx)+"ConvertPDFToImages start time: %v \n", startTime)

	if len(filename) == 0 {
		return fmt.Errorf(utils.MMark(logCtx) + "ConvertPDFToImages buffer is empty")
	}
	uuid := uuid.New().String()
	outpath := DownloadFilePrefix + uuid + "_%d.png"
	err := handlerUtils.MudrawToImage(filename, outpath)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+utils.ServiceInternalEsErrMsg+"ConvertPDFToImages  %s ConvertPDFToImage err: %v \n", filename, err)
		// return err
	}

	docNumpage, err := handlerUtils.GetPDFPageCountFromMupdf(filename)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+utils.ServiceInternalEsErrMsg+"ConvertPDFToImages %s GetPDFPageCount err: %v \n", filename, err)
		return err
	}

	// 删除临时文件
	defer func(uuid string) {
		for i := 1; i <= docNumpage; i++ {
			filePath := fmt.Sprintf(DownloadFilePrefix+"%s_%d.png", uuid, i)
			os.Remove(filePath)
		}
	}(uuid)

	isFailed := false
	list := &proto.File2ImageInfoList{}
	redisKey := fmt.Sprintf(File2ImageKeyFmt, handlerUtils.GetNameByRunEnv(), f2i.TaskId)
	isRowFilePpt := strings.ToLower(path.Ext(f2i.FileUrl)) == ".pptx"

	notesMap := make(map[int]string)
	for _, note := range pptInfo.Notes {
		notesMap[note.Index] = note.Note
	}
	pptWidthPx := int(math.Ceil(pptInfo.SlideSize.Width_inches * 300))
	pptHeightPx := int(math.Ceil(pptInfo.SlideSize.Height_inches * 300))
	// 遍历PDF文件，解析每一张图片
	for i := 1; i <= docNumpage; i++ {
		filePath := fmt.Sprintf(DownloadFilePrefix+"%s_%d.png", uuid, i)
		// 打开文件
		file, err := os.Open(filePath)
		if err != nil {
			handlerUtils.MudrawToImageByPage(filename, filePath, i, 72)
			file, err = os.Open(filePath)
			if err != nil {
				logger.Log.Errorf(utils.MMark(logCtx)+utils.ServiceInternalEsErrMsg+"ConvertPDFToImages Open file failed, image index:%d path: %s, err: %v", i, filePath, err)
				isFailed = true
				continue
			}
		}

		defer file.Close()
		// 获取文件信息
		fileInfo, err := file.Stat()
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+utils.ServiceInternalEsErrMsg+" GetFileInfo failed, image index:%d path: %s, err: %v", i, filePath, err)
			isFailed = true
			continue
		}

		if fileInfo.Size() == 0 || fileInfo.Size() > 15*1024*1024 {
			handlerUtils.MudrawToImageByPage(filename, filePath, i, 72)
			file, err = os.Open(filePath)
			if err != nil {
				logger.Log.Errorf(utils.MMark(logCtx)+utils.ServiceInternalEsErrMsg+" file size is zero, image index:%d path: %s, err: %v", i, filePath, err)
				isFailed = true
				continue
			}
		}

		// 解码JPG文件到image.Image对象
		img, err := png.Decode(file)
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+utils.ServiceInternalEsErrMsg+"ConvertPDFToImages Decode file failed, image index:%d path: %s, err: %v", i, filePath, err)
			isFailed = true
			continue
		}

		// // 获取图片的大小
		width := img.Bounds().Dx()
		height := img.Bounds().Dy()
		if isRowFilePpt {
			if width == pptWidthPx && height == pptHeightPx { // 不处理
			} else if math.Abs(float64(pptWidthPx-width)) > 2 && math.Abs(float64(pptHeightPx-height)) > 2 { // 不处理
			} else {
				//非上面两种情况，则认为是图片的大小和ppt有误差，则以ppt的尺寸为准，并裁剪图片
				width = pptWidthPx
				height = pptHeightPx
				cropRect := image.Rect(0, 0, width, height) // 使用 image.Rect 创建裁剪矩形
				// 定义裁剪区域，裁剪出宽度为1600，高度为900的区域
				croppedImg := imaging.Crop(img, cropRect)
				img = croppedImg
			}
		}

		var buf bytes.Buffer
		// 使用jpeg.Encode将图像编码为JPEG并写入buf
		err = jpeg.Encode(&buf, img, nil) // 第二个参数是*jpeg.Options，这里传递nil使用默认设置
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+utils.ServiceInternalEsErrMsg+"ConvertPDFToImages Get Image failed, image index:%d, err: %v\n", i, err)
			isFailed = true
			continue
		}

		downloadurl, err := RetryUploadBosServiceFromByte(logCtx, buf, ".jpg")
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+utils.ThirdPartyInterfaceEsErrMsg+"ConvertPDFToImages UploadBosServiceFromByte failed, image index:%d, err: %v\n", i, err)
			isFailed = true
			continue
		}

		note, ok := notesMap[i]
		if !ok {
			note = ""
		}

		// 保存图片信息
		list.ImageInfos = append(list.ImageInfos, proto.File2ImageInfo{
			Index:  i,
			Width:  width,
			Height: height,
			Url:    downloadurl,
			Remark: note,
			ErrMsg: "",
		})

		// 查询状态是否被停止
		taskstatus, err := redisproxy.GetKeyValue(redisKey)
		if err != nil {
			// 缓存查询失败，则从数据库中获取
			f2i, err = f2i.GetFile2Image(gomysql.DB, f2i.ID)
			if err != nil {
				logger.Log.Errorf(utils.MMark(logCtx)+utils.MysqlEsErrMsg+"ConvertPDFToImages GetFile2Image failed, err: %v\n", err)
				continue
			}
			// 	// 更新缓存
			redisproxy.SetKeyValue(redisKey, string(f2i.Status), File2ImageStateKeyExpire)
			taskstatus = string(f2i.Status)
		}

		if taskstatus == string(enum.TaskStop) {
			logger.Log.Infof(utils.MMark(logCtx) + "ConvertPDFToImages parse stop ")
			err = updateDbFile2Image(f2i, docNumpage, list, enum.TaskStop)
			if err != nil {
				logger.Log.Errorf(utils.MMark(logCtx)+"ConvertPDFToImages updateDbFile2Image failed, err: %v\n", err)
				return err
			}
			return nil
		}

		err = updateDbFile2Image(f2i, docNumpage, list, enum.TaskParsing)
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+"ConvertPDFToImages updateDbFile2Image failed, err: %v\n", err)
			continue
		}
	}

	durTime := time.Since(startTime)
	logger.Log.Infof(utils.MMark(logCtx)+"ConvertPDFToImages end time:%v ", durTime)
	// 解析失败，更新数据库
	if isFailed {
		f2i.Message = "文件解析失败"
		err = updateDbFile2Image(f2i, docNumpage, list, enum.TaskFailed)
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+utils.MysqlEsErrMsg+"ConvertPDFToImages updateDbFile2Image failed, err: %v\n", err)
			return err
		}
		return nil
	}

	// 解析成功，更新数据库
	err = updateDbFile2Image(f2i, docNumpage, list, enum.TaskCensor)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+utils.MysqlEsErrMsg+"ConvertPDFToImages updateDbFile2Image failed, err: %v\n", err)
		return err
	}

	return nil
}

// FileCensorFromDB 定时器任务，定时将待审核文件送去审核
func FileCensorFromDB() {
	file2ImageList, err := (&model.File2Image{}).GetTasksWithStatus(gomysql.DB, enum.TaskCensor)
	if err != nil {
		logger.Log.Errorf("FileToImageFromDB GetAndUpdateStatusToRunning error: %+v\n", err)
		return
	}

	for _, v := range file2ImageList {
		fileCensorByFile2Image(v)
	}
}

func fileCensorByFile2Image(f2i *model.File2Image) {
	logCtx := context.WithValue(context.TODO(), utils.CtxKeyLogID, f2i.TaskId)
	redisproxy := redisproxy.GetRedisProxy()

	// 获取 redis 分布式锁
	redisLockKey := fmt.Sprintf(File2ImageKeyFmt+"_censor_lock", handlerUtils.GetNameByRunEnv(), f2i.TaskId)
	redisLock := redislock.NewRedisLock(redisproxy.Rdb, redisLockKey, File2ImageKeyLockTimeDuration)
	ok, err := redisLock.Lock(context.Background())
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+utils.RedisEsErrMsg+"Lock key: %s error: %+v\n", redisLockKey, err)
		return
	} else if !ok {
		//logger.Log.Warnf(utils.MMark(logCtx)+"convertToImage Lock is occupied key: %+s \n", redisLockKey)
		return
	}

	defer redisLock.Unlock(context.Background())

	// 更新缓存任务状态
	redisKey := fmt.Sprintf(File2ImageKeyFmt, handlerUtils.GetNameByRunEnv(), f2i.TaskId)
	status, err := redisproxy.GetKeyValue(redisKey)
	if err != nil {
		if string(err.Error()) != string(redis.Nil) {
			logger.Log.Errorf(utils.MMark(logCtx)+" get task status error: %+v\n", err)
			return
		}

		file2image, err := f2i.GetFile2Image(gomysql.DB, f2i.ID)
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+utils.MysqlEsErrMsg+" GetFile2Image err: %v \n", err)
			return
		}
		// 更新 f2i 对象
		f2i = file2image

		err = redisproxy.SetKeyValue(redisKey, string(file2image.Status), File2ImageStateKeyExpire)
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+" SetKeyValue err: %v \n", err)
			return
		}

		status = string(file2image.Status)
		// 任务状态不存在，还继续查询任务状态，最后将结果写入redis，防止其他服务重复查询并写入数据库
		logger.Log.Warnf(utils.MMark(logCtx)+" get task status is nil set status %s: %+v\n", status, err)
	}

	// 任务已经处理过，跳过
	if status == string(enum.TaskCensorSucceed) || status == string(enum.TaskFailed) {
		//logger.Log.Infof(utils.MMark(logCtx)+"convertToImage task status is %s, skip\n", status)
		return
	}

	logger.Log.Infof(utils.MMark(logCtx)+" task status is %s \n", status)
	// 更新任务状态为运行中
	defer func(f2i *model.File2Image) {
		file2image, err := f2i.GetFile2Image(gomysql.DB, f2i.ID)
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+" GetFile2Image err: %v \n", err)
			return
		}
		// 如果任务还是运行中，则更新为失败状态
		if file2image.Status == enum.TaskCensor {
			file2image.Message = "文件审核失败，请联系管理员处理"
			file2image.Status = enum.TaskFailed
			err := file2image.UpdateFile2Image(gomysql.DB)
			if err != nil {
				logger.Log.Errorf(utils.MMark(logCtx)+" UpdateFile2Image err: %v \n", err)
			}

			if err := redisproxy.SetKeyValue(redisKey, string(enum.TaskFailed), File2ImageStateKeyExpire); err != nil {
				logger.Log.Errorf(utils.MMark(logCtx)+" SetKeyValue err: %v \n", err)
			}
			return
		}

		if err := redisproxy.SetKeyValue(redisKey, string(file2image.Status), File2ImageStateKeyExpire); err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+" SetKeyValue err: %v \n", err)
		}
	}(f2i)

	err = censorCoursewareFile(logCtx, f2i)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"censorCoursewareFile error: %+v\n", err)
		return
	}
}

func censorCoursewareFile(logCtx context.Context, f2i *model.File2Image) error {
	censorclient := NewRiskControlImage()
	list := &proto.File2ImageInfoList{}
	err := json.Unmarshal([]byte(f2i.ImageInfos), list)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"json unmarshal error: %+v\n", err)
		return err
	}
	censorList := make([]*proto.CensorResultList, 0)
	redisKey := fmt.Sprintf(File2ImageKeyFmt, handlerUtils.GetNameByRunEnv(), f2i.TaskId)
	redisproxy := redisproxy.GetRedisProxy()
	censorTaskid := ""
	startTime := time.Now()
	for _, v := range list.ImageInfos {
		censorRes := &proto.CensorResultList{
			Index:    v.Index,
			Url:      v.Url,
			Messages: make([]string, 0),
		}

		// 查询状态是否被停止
		taskstatus, err := redisproxy.GetKeyValue(redisKey)
		if err != nil {
			// 缓存查询失败，则从数据库中获取
			f2i, err = f2i.GetFile2Image(gomysql.DB, f2i.ID)
			if err != nil {
				logger.Log.Errorf(utils.MMark(logCtx)+utils.MysqlEsErrMsg+"ConvertPDFToImages GetFile2Image failed, err: %v\n", err)
				continue
			}
			// 	// 更新缓存
			redisproxy.SetKeyValue(redisKey, string(f2i.Status), File2ImageStateKeyExpire)
			taskstatus = string(f2i.Status)
		}

		if taskstatus == string(enum.TaskStop) {
			return nil
		}

		strIndex := fmt.Sprintf("图片索引:%d ", v.Index)
		censorres, taskid, err := censorclient.censorImageFromUrl(logCtx, v.Url, f2i.AccountId)
		censorTaskid = taskid
		if err != nil {
			censorRes.Messages = append(censorRes.Messages, "审核失败，请联系管理员处理")
			censorList = append(censorList, censorRes)
			logger.Log.Errorf(utils.MMark(logCtx)+"censorImageFromUrl error: %v\n", err)
			continue
		} else if censorres.ErrorCode != 0 {
			logger.Log.Errorf(utils.MMark(logCtx)+"censorImageFromUrl error: %s\n", strIndex+censorres.ErrorMsg)
			censorRes.Messages = append(censorRes.Messages, censorres.ErrorMsg)
			censorList = append(censorList, censorRes)
			continue
		} else if censorres.ConclusionType == 2 || censorres.ConclusionType == 4 {
			for _, v := range censorres.Data {
				if len(v.Msg) > 0 {
					censorRes.Messages = append(censorRes.Messages, v.Msg)
				} else if len(v.ErrorMsg) > 0 {
					censorRes.Messages = append(censorRes.Messages, v.ErrorMsg)
				} else {
					censorRes.Messages = append(censorRes.Messages, "审核异常，请联系管理员处理")
				}
			}
			censorList = append(censorList, censorRes)
			continue
		} else if censorres.ConclusionType == 1 || censorres.ConclusionType == 3 {
			continue
		} else {
			censorRes.Messages = append(censorRes.Messages, "审核异常，请联系管理员处理")
			censorList = append(censorList, censorRes)
			continue
		}
	}

	logger.Log.Infof(utils.MMark(logCtx)+"censorCoursewareFile cost time: %v\n", time.Since(startTime).String())
	if len(censorList) > 0 {
		censorError, _ := json.Marshal(censorList)
		f2i.Message = string(censorError)
		f2i.Status = enum.TaskCensorFailed
		f2i.UpdatedAt = time.Now()
		f2i.CensorTaskId = censorTaskid
		err = f2i.UpdateFile2Image(gomysql.DB)
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+" UpdateFile2Image err: %v \n", err)
			return err
		}

		logger.Log.Errorf(utils.MMark(logCtx)+"censorCoursewareFile error: %s\n", censorError)
		return errors.New(string(censorError))
	}

	f2i.Status = enum.TaskCensorSucceed
	f2i.UpdatedAt = time.Now()
	err = f2i.UpdateFile2Image(gomysql.DB)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+" UpdateFile2Image err: %v \n", err)
		return err
	}

	// 存储到用户ppt列表
	return nil
}

func saveUserCoursewareFile(logCtx context.Context, coursewareTaskid, taskId, accountId, fileName string) error {
	faid := model.UserCoursewareFile{
		UserID:           accountId,
		FileName:         fileName,
		CoursewareTaskId: coursewareTaskid,
		TaskId:           taskId,
		Status:           enum.TaskReady,
	}

	err := faid.Create(gomysql.DB)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"saveUserCoursewareFile failed: %v", err)
		return err
	}

	logger.Log.Infof(utils.MMark(logCtx) + "saveUserCoursewareFile success")
	return nil
}

func UserCoursewareFileUpdate() {
	file2ImageList, err := (&model.UserCoursewareFile{}).GetUserCoursewareFileByStatusList(gomysql.DB, enum.TaskReady)
	if err != nil {
		logger.Log.Errorf("GetUserCoursewareFileByStatusList error: %+v\n", err)
		return
	}

	for _, v := range file2ImageList {
		userCoursewareFileUpdate(v)
	}
}

func userCoursewareFileUpdate(userFile *model.UserCoursewareFile) {
	logCtx := context.WithValue(context.TODO(), utils.CtxKeyLogID, userFile.CoursewareTaskId)
	redisproxy := redisproxy.GetRedisProxy()

	// 获取 redis 分布式锁
	redisLockKey := fmt.Sprintf(File2ImageKeyFmt+"_user_courseware_lock", handlerUtils.GetNameByRunEnv(), userFile.CoursewareTaskId)
	redisLock := redislock.NewRedisLock(redisproxy.Rdb, redisLockKey, File2ImageKeyLockTimeDuration)
	ok, err := redisLock.Lock(context.Background())
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+utils.RedisEsErrMsg+" Lock key: %s error: %+v\n", redisLockKey, err)
		return
	} else if !ok {
		return
	}

	defer redisLock.Unlock(context.Background())

	// 更新缓存任务状态
	redisKey := fmt.Sprintf(File2ImageKeyFmt+"_user_courseware_status", handlerUtils.GetNameByRunEnv(), userFile.CoursewareTaskId)
	status, err := redisproxy.GetKeyValue(redisKey)
	if err != nil {
		if string(err.Error()) != string(redis.Nil) {
			logger.Log.Errorf(utils.MMark(logCtx)+" get task status error: %+v\n", err)
			return
		}

		newUserFile, err := userFile.GetUserCoursewareFile(gomysql.DB, userFile.UserID, userFile.CoursewareTaskId)
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+utils.MysqlEsErrMsg+" err: %v \n", err)
			return
		}
		// 更新 f2i 对象
		userFile = newUserFile

		err = redisproxy.SetKeyValue(redisKey, string(newUserFile.Status), File2ImageStateKeyExpire)
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+" SetKeyValue err: %v \n", err)
			return
		}

		status = string(newUserFile.Status)
		// 任务状态不存在，还继续查询任务状态，最后将结果写入redis，防止其他服务重复查询并写入数据库
		logger.Log.Warnf(utils.MMark(logCtx)+" get task status is nil set status %s: %+v\n", status, err)
	}

	// 任务已经处理过，跳过
	if status == string(enum.TaskCensorSucceed) || status == string(enum.TaskFailed) {
		logger.Log.Infof(utils.MMark(logCtx)+" task status is %s \n", status)
		return
	}

	logger.Log.Infof(utils.MMark(logCtx)+" task status is %s \n", status)

	f2i, err := (&model.File2Image{}).GetFile2ImageFromTaskId(gomysql.DB, userFile.TaskId)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"GetFile2ImageFromTaskId error: %+v\n", err)
		return
	}

	if f2i.Status == enum.TaskCensorSucceed {
		userFile.Status = enum.TaskSucceed
		userFile.ImageInfos = f2i.ImageInfos
		userFile.Status = enum.TaskCensorSucceed
	} else if f2i.Status == enum.TaskParsing || f2i.Status == enum.TaskCensor || f2i.Status == enum.TaskReady {
		return
	} else if f2i.Status == enum.TaskFailed || f2i.Status == enum.TaskCensorFailed {
		userFile.Status = enum.TaskFailed
	} else if f2i.Status == enum.TaskStop {
		userFile.Status = enum.TaskStop
	} else {
		logger.Log.Errorf(utils.MMark(logCtx)+"unknown status: %s\n", f2i.Status)
		return
	}

	err = gomysql.DB.Transaction(func(tx *gorm.DB) error {
		_, err := userFile.GetUserCoursewareFileByForUpdate(tx, userFile.UserID, userFile.CoursewareTaskId)
		if err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				logger.Log.Warnf(utils.MMark(logCtx) + "GetUserCoursewareFile not found")
				return nil
			}
			logger.Log.Errorf(utils.MMark(logCtx)+"GetUserCoursewareFile error: %+v\n", err)
			return err
		}

		err = userFile.Update(tx)
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+"UpdateUserCoursewareFile error: %+v\n", err)
			return err
		}
		return nil
	})

	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"Transaction error: %+v\n", err)
		return
	}

	// 更新redis状态
	err = redisproxy.SetKeyValue(redisKey, string(userFile.Status), File2ImageStateKeyExpire)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+" SetKeyValue err: %v \n", err)
		return
	}
}
