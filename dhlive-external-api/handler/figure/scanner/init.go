package scanner

import (
	"acg-ai-go-common/logger"
	"dhlive-external-api/handler/figure"
	"dhlive-external-api/handler/handlerUtils/cron"
	"dhlive-external-api/handler/translate"
)

func Init() error {
	c := cron.GetCron()
	_, err := c.RegisterC<PERSON>("@every 10s", figure.QueryJobStatusFromDB)
	if err != nil {
		logger.Log.Fatalf("RegisterCron QueryJobStatusFromDB error: %v\n", err)
		return err
	}
	_, err = c.<PERSON><PERSON>ron("@every 10s", figure.FileToImageFromDB)
	if err != nil {
		logger.Log.Fatalf("RegisterCron FileToImageFromDB error: %v\n", err)
		return err
	}

	_, err = c.RegisterCron("@every 10s", figure.SubmitVideoFromDb)
	if err != nil {
		logger.Log.Fatalf("RegisterCron SubmitVideoFromDb error: %v\n", err)
		return err
	}

	_, err = c.<PERSON>ron("@every 10s", figure.PullVideoFromDb)
	if err != nil {
		logger.Log.Fatalf("RegisterCron FileToImageFromDB error: %v\n", err)
		return err
	}

	_, err = c.RegisterCron("@every 10s", figure.SubmitAudioFromDb)
	if err != nil {
		logger.Log.Fatalf("RegisterCron SubmitAudioFromDb error: %v\n", err)
		return err
	}

	_, err = c.RegisterCron("@every 10s", figure.PullAudioFromDb)
	if err != nil {
		logger.Log.Fatalf("RegisterCron FileToImageFromDB error: %v\n", err)
		return err
	}

	_, err = c.RegisterCron("@every 10s", figure.FileToImageFromDBV2)
	if err != nil {
		logger.Log.Fatalf("RegisterCron FileToImageFromDBV2 error: %v\n", err)
		return err
	}

	_, err = c.RegisterCron("@every 10s", figure.FileCensorFromDB)
	if err != nil {
		logger.Log.Fatalf("RegisterCron FileCensorFromDB error: %v\n", err)
		return err
	}

	_, err = c.RegisterCron("@every 10s", figure.UserCoursewareFileUpdate)
	if err != nil {
		logger.Log.Fatalf("RegisterCron UserCoursewareFileUpdate error: %v\n", err)
		return err
	}

	_, err = c.RegisterCron("@every 20s", translate.TranslateVideoTaskForWait)
	if err != nil {
		logger.Log.Fatalf("RegisterCron TranslateVideoTaskForWait error: %v\n", err)
		return err
	}

	_, err = c.RegisterCron("@every 180s", translate.TranslateVideoTaskForOuttime)
	if err != nil {
		logger.Log.Fatalf("RegisterCron TranslateVideoTaskForOuttime error: %v\n", err)
		return err
	}

	c.StartCron()

	return nil
}
