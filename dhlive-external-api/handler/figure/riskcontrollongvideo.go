package figure

import (
	"acg-ai-go-common/gomysql"
	"acg-ai-go-common/logger"
	"acg-ai-go-common/utils"
	"acg-ai-go-common/utils/redisproxy"
	"context"
	"dhlive-external-api/beans/enum"
	"dhlive-external-api/beans/model"
	"dhlive-external-api/beans/proto"
	config "dhlive-external-api/conf"
	"dhlive-external-api/handler/handlerUtils"
	"dhlive-external-api/handler/handlerUtils/asynccensorclient"
	"dhlive-external-api/handler/handlerUtils/redislock"
	"encoding/json"
	"fmt"
	"net/http"
	"os"
	"strconv"
	"sync"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	redis "github.com/redis/go-redis/v9"
)

const (
	RiskControlLongVideoQps    = "riskcontrolvideoqps"
	RiskControlLongVideoQpsFmt = "%s:" + RiskControlLongVideoQps + ":xiling-saas-v3"
	RiskControlLongVideoQpsMax = 2 // 最大并发量

	RiskControlLongVideoPullQps    = "riskcontrolvideopullqps"
	RiskControlLongVideoPullQpsFmt = "%s:" + RiskControlLongVideoPullQps + ":xiling-saas-v3"
	RiskControlLongVideoPullQpsMax = 20 // 最大并发量

	RiskControlLongVideoLock    = "riskcontrolvideolock"
	RiskControlLongVideoLockFmt = "%s:" + RiskControlLongVideoLock + ":%s"
	RiskControlLongVideoLockExp = 1 * time.Hour

	RiskControlLongVideoStatus    = "riskcontrolvideostatus"
	RiskControlLongVideoStatusFmt = "%s:" + RiskControlLongVideoStatus + ":%s"
	RiskControlLongVideoStatusExp = 2 * time.Hour
)

var (
	// 实例化一个VideoWatermark
	RiskControlLongVideoInstance *RiskControlLongVideo
	// once 用于确保实例化操作只执行一次
	onceRiskControlLongVideo sync.Once
)

type RiskControlLongVideo struct {
	client *asynccensorclient.AsyncContentCensorClient
}

func GetRiskControlLongVideo() *RiskControlLongVideo {
	onceRiskControlLongVideo.Do(func() {
		RiskControlLongVideoInstance = newRiskControlLongVideo()
	})
	return RiskControlLongVideoInstance
}

func newRiskControlLongVideo() *RiskControlLongVideo {
	return &RiskControlLongVideo{
		client: asynccensorclient.NewClient(config.LocalConfig.RiskControl.AK, config.LocalConfig.RiskControl.SK),
	}
}

func (rclv *RiskControlLongVideo) SubmitVideoCensor(c *gin.Context) {
	logCtx := context.WithValue(context.TODO(), utils.CtxKeyLogID, utils.RandStringRunes(16))
	accountID := "ceshi1234567890"
	aid, ok := c.Get("AccountId")
	if ok {
		accountID = aid.(string)
	}
	// 解析请求参数
	resSlice := proto.RiskControlAsyncSubmitResponse{
		LogID:    utils.GetLogID(logCtx),
		DataList: []proto.RiskControlAsyncSubmitResponseItem{},
	}

	req := proto.RiskControlLongVideoSubmitRequest{}
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"SubmitVideoCensor ShouldBindQuery fail, err:%v req:%#v", err, req)
		c.JSON(http.StatusOK, proto.NewCommDataRsp(ParamErrorErrCode, ParamErrorErrMsg, resSlice))
		return
	}

	// 参数校验
	if len(req.VideoURL) <= 0 {
		logger.Log.Errorf(utils.MMark(logCtx)+"SubmitVideoCensor VideoURL is empty, req:%#v", req)
		c.JSON(http.StatusOK, proto.NewCommDataRsp(ParamEmptyErrCode, ParamEmptyErrMsg, resSlice))
		return
	}

	for _, url := range req.VideoURL {
		res := proto.RiskControlAsyncSubmitResponseItem{}
		if len(url) <= 0 {
			res.ErrorCode = "100003"
			res.ErrorMsg = "视频URL为空"
			resSlice.DataList = append(resSlice.DataList, res)

			logger.Log.Errorf(utils.MMark(logCtx)+"SubmitVideoCensor VideoURL is empty, req:%#v", req)
			continue
		}

		taskid, err := saveRiskControlSubmitToDb(logCtx, url, accountID, model.RiskControlTypeLongVideo)
		if err != nil {
			res.ErrorCode = "100004"
			res.ErrorMsg = err.Error()
			resSlice.DataList = append(resSlice.DataList, res)

			logger.Log.Errorf(utils.MMark(logCtx)+utils.ServiceInternalEsErrMsg+"SubmitVideoCensor LongVideoCensorSubmit fail, req:%#v", req)
			continue
		}

		res = proto.RiskControlAsyncSubmitResponseItem{
			TaskId: taskid,
		}

		resSlice.DataList = append(resSlice.DataList, res)
	}

	c.JSON(http.StatusOK, proto.NewSuccessRsp(resSlice))
}

func (rclv *RiskControlLongVideo) PullVideoCensorResult(c *gin.Context) {
	logCtx := context.WithValue(context.TODO(), utils.CtxKeyLogID, utils.RandStringRunes(16))

	// 解析请求参数
	resSlice := proto.RiskControlLongVideoPullResponse{
		LogID:    utils.GetLogID(logCtx),
		DataList: []proto.RiskControlLongVideoPullDataListItem{},
	}

	req := proto.RiskControlLongVideoPullRequest{}
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"PullVideoCensorResult ShouldBindQuery fail, err:%v req:%#v", err, req)
		c.JSON(http.StatusOK, proto.NewCommDataRsp(ParamErrorErrCode, ParamErrorErrMsg, resSlice))
		return
	}

	// 参数校验
	if len(req.TaskIds) <= 0 {
		logger.Log.Errorf(utils.MMark(logCtx)+"PullVideoCensorResult VideoURL is empty, req:%#v", req)
		c.JSON(http.StatusOK, proto.NewCommDataRsp(ParamEmptyErrCode, ParamEmptyErrMsg, resSlice))
		return
	}

	for _, taskid := range req.TaskIds {
		res := proto.RiskControlLongVideoPullDataListItem{}
		if len(taskid) <= 0 {
			res.ErrorCode = 100003
			res.ErrorMsg = "任务ID为空"
			logger.Log.Errorf(utils.MMark(logCtx)+"PullVideoCensorResult taskid is empty, req:%#v", req)
			resSlice.DataList = append(resSlice.DataList, res)
			continue
		}
		rci := model.RiskControlItem{}
		newrci, err := rci.GetRiskControlItemFromTaskId(gomysql.DB, taskid)
		if err != nil {
			res.ErrorCode = 100004
			res.ErrorMsg = err.Error()
			logger.Log.Errorf(utils.MMark(logCtx)+utils.MysqlEsErrMsg+"PullVideoCensorResult GetRiskControlItemFromTaskId fail,err:%v req:%#v", err, req)
			resSlice.DataList = append(resSlice.DataList, res)
			continue
		}

		res.Status = string(newrci.Status)
		if newrci.Status != enum.TaskSucceed && newrci.Status != enum.TaskFailed {
			resSlice.DataList = append(resSlice.DataList, res)
			continue
		}

		if len(newrci.CensorResult) <= 0 {
			res.ErrorCode = 100006
			res.ErrorMsg = "审核结果为空"
			logger.Log.Errorf(utils.MMark(logCtx)+"PullVideoCensorResult censorResult is empty, req:%#v", req)
			resSlice.DataList = append(resSlice.DataList, res)
			continue
		}

		pullres := proto.RiskControlLongVideoCensorPullResponse{}
		err = json.Unmarshal([]byte(newrci.CensorResult), &pullres)
		if err != nil {
			res.ErrorCode = 100005
			res.ErrorMsg = err.Error()
			logger.Log.Errorf(utils.MMark(logCtx)+"PullVideoCensorResult Unmarshal fail,err:%v req:%#v", err, req)
			resSlice.DataList = append(resSlice.DataList, res)
			continue
		}

		if pullres.ErrorCode != 0 {
			res.ErrorCode = pullres.ErrorCode
			res.ErrorMsg = pullres.ErrorMsg
			logger.Log.Errorf(utils.MMark(logCtx)+"PullVideoCensorResult GetRiskControlItemFromTaskId fail, req:%#v", req)
			resSlice.DataList = append(resSlice.DataList, res)
			continue
		} else if pullres.Ret != "0" {
			errcode, _ := strconv.Atoi(pullres.Ret)
			res.ErrorCode = int64(errcode)
			res.ErrorMsg = pullres.Msg
			logger.Log.Errorf(utils.MMark(logCtx)+"PullVideoCensorResult GetRiskControlItemFromTaskId fail, req:%#v", req)
			resSlice.DataList = append(resSlice.DataList, res)
			continue
		}

		res = proto.RiskControlLongVideoPullDataListItem{
			Status:                   string(newrci.Status),
			Conclusion:               pullres.Data.Conclusion,
			ConclusionType:           int(pullres.Data.ConclusionType),
			ConclusionTypeGroupInfos: pullres.Data.ConclusionTypeGroupInfos,
		}

		resSlice.DataList = append(resSlice.DataList, res)
	}

	c.JSON(http.StatusOK, proto.NewSuccessRsp(resSlice))
}

func SubmitVideoFromDb() {
	rci := model.RiskControlItem{}
	videos, err := rci.GetTasksWithStatus(gomysql.DB, enum.TaskReady, model.RiskControlTypeLongVideo)
	if err != nil {
		logger.Log.Errorf("SubmitVideoCensor GetTasksWithStatus fail")
		return
	}
	for _, v := range videos {
		submitVideo(v)
	}
}

func submitVideo(rci *model.RiskControlItem) {
	logCtx := context.WithValue(context.TODO(), utils.CtxKeyLogID, rci.TaskId)
	rclv := GetRiskControlLongVideo()
	var res proto.RiskControlLongVideoCensorSubmitResponse

	// 获取 redis 分布式锁
	redisproxy := redisproxy.GetRedisProxy()

	redisLockKey := fmt.Sprintf(RiskControlLongVideoLockFmt, handlerUtils.GetNameByRunEnv(), rci.TaskId)
	redisLock := redislock.NewRedisLock(redisproxy.Rdb, redisLockKey, RiskControlLongVideoLockExp)
	ok, err := redisLock.Lock(context.Background())
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"submitVideo Lock key: %s error: %+v\n", redisLockKey, err)
		return
	} else if !ok {
		return
	}

	defer redisLock.Unlock(context.Background())
	// 获取 任务状态
	// 更新缓存任务状态
	redisKey := fmt.Sprintf(RiskControlLongVideoStatusFmt, handlerUtils.GetNameByRunEnv(), rci.TaskId)
	status, err := redisproxy.GetKeyValue(redisKey)
	if err != nil {
		if string(err.Error()) != string(redis.Nil) {
			logger.Log.Errorf(utils.MMark(logCtx)+"submitVideo get task status error: %+v\n", err)
			return
		}

		newrci, err := rci.GetRiskControlItem(gomysql.DB, rci.ID)
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+"submitVideo ID %s GetFile2Image err: %v \n", rci.TaskId, err)
			return
		}
		// 更新 rci 对象
		rci = newrci

		err = redisproxy.SetKeyValue(redisKey, string(rci.Status), RiskControlLongVideoStatusExp)
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+"submitVideo ID %s SetKeyValue err: %v \n", rci.TaskId, err)
			return
		}

		status = string(rci.Status)
		// 任务状态不存在，还继续查询任务状态，最后将结果写入redis，防止其他服务重复查询并写入数据库
		logger.Log.Warnf(utils.MMark(logCtx)+"submitVideo get task status is nil set status %s\n", status)
	}

	// 任务已经处理过，跳过
	if status == string(enum.TaskRunning) || status == string(enum.TaskSucceed) || status == string(enum.TaskFailed) {
		return
	}

	// 申请qps限制
	key := fmt.Sprintf(RiskControlLongVideoQpsFmt, handlerUtils.GetNameByRunEnv())
	for {
		ok, err = checkQpsAllowance(key, RiskControlLongVideoQpsMax)
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+"submitVideo checkQpsAllowance is err:%v", err)
			return
		}

		if !ok {
			time.Sleep(10 * time.Millisecond)
			continue
		}

		break
	}

	options := make(map[string]interface{})
	options["extId"] = rci.AccountID
	options["strategyId"] = strconv.Itoa(config.LocalConfig.RiskControl.StrategyId)
	retryNumber := 0
	censorRes := ""
	censorVideoUrl := rci.FileUrl
	if len(rci.CompressionUrl) > 0 {
		censorVideoUrl = rci.CompressionUrl
	}

	// url 转换
	if internalAddress, err := BosUrlReplaceInternalAddress(logCtx, censorVideoUrl); err != nil {
		// 如果失败了，就直接用原来的url去审核，这里打印日志即可，不影响业务逻辑执行
		logger.Log.Errorf(utils.MMark(logCtx)+" BosUrlReplaceInternalAddress fail, err:%v", err)
	} else {
		// 替换成功，则使用内部地址去审核
		censorVideoUrl = internalAddress
	}

	encodeurl, err := handlerUtils.EncodeUrl(censorVideoUrl)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"submitVideo EncodeUrl fail, url:%s", rci.FileUrl)
		return
	}

	for i := 0; i < 3; i++ {
		res = proto.RiskControlLongVideoCensorSubmitResponse{}
		retryNumber += 1
		censorRes = rclv.client.LongVideoCensorSubmit(encodeurl, "", options)

		err = json.Unmarshal([]byte(censorRes), &res)
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+"submitVideo Unmarshal fail,res:%s url:%s", censorRes, encodeurl)
			res.ErrorCode = 200001
			res.ErrorMsg = "res: " + censorRes + " err:" + err.Error()
			time.Sleep(1 * time.Second)
			continue
		}

		if res.ErrorCode == 18 {
			logger.Log.Errorf(utils.MMark(logCtx)+"submitAudio long audio censor submit fail, url:%s res: %s", encodeurl, censorRes)
			time.Sleep(1 * time.Second)
			continue
		} else if res.ErrorCode != 0 {
			logger.Log.Errorf(utils.MMark(logCtx)+"submitVideo long video censor submit fail, url:%s res: %s", encodeurl, censorRes)
			time.Sleep(1 * time.Second)
			continue
		} else if len(res.Ret) > 0 && res.Ret != "0" {
			logger.Log.Errorf(utils.MMark(logCtx)+"submitVideo long video censor submit fail, url:%s res: %s", encodeurl, censorRes)
			time.Sleep(1 * time.Second)
			continue
		} else if len(res.Data.TaskId) <= 0 {
			logger.Log.Errorf(utils.MMark(logCtx)+"submitVideo long video censor submit fail, url:%s res: %s", encodeurl, censorRes)
			time.Sleep(1 * time.Second)
			continue
		}

		break
	}

	if res.ErrorCode != 0 {
		rci.Status = enum.TaskFailed
	} else if len(res.Ret) > 0 && res.Ret != "0" {
		rci.Status = enum.TaskFailed
	} else if len(res.Data.TaskId) <= 0 {
		rci.Status = enum.TaskFailed
	} else {
		rci.Status = enum.TaskRunning
	}

	rci.SubmitResult = censorRes
	rci.UpdatedAt = time.Now()
	rci.RetryNumber = int64(retryNumber)

	if err = rci.UpdateRiskControlItem(gomysql.DB); err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"submitVideo UpdateRiskControlItem fail, url:%s", rci.FileUrl)
		return
	}

	err = redisproxy.SetKeyValue(redisKey, string(rci.Status), RiskControlLongVideoStatusExp)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"submitVideo ID %s SetKeyValue err: %v \n", rci.TaskId, err)
		return
	}
	logger.Log.Infof(utils.MMark(logCtx)+"submitVideo success, status:%s", rci.Status)
}

func PullVideoFromDb() {
	rci := model.RiskControlItem{}
	videos, err := rci.GetTasksWithStatus(gomysql.DB, enum.TaskRunning, model.RiskControlTypeLongVideo)
	if err != nil {
		logger.Log.Errorf("SubmitVideoCensor GetTasksWithStatus fail")
		return
	}
	for _, v := range videos {
		pullVideo(v)
	}
}

func pullVideo(rci *model.RiskControlItem) {
	logCtx := context.WithValue(context.TODO(), utils.CtxKeyLogID, rci.TaskId)

	// 获取 redis 分布式锁
	redisproxy := redisproxy.GetRedisProxy()
	redisLockKey := fmt.Sprintf(RiskControlLongVideoLockFmt, handlerUtils.GetNameByRunEnv(), rci.TaskId)
	redisLock := redislock.NewRedisLock(redisproxy.Rdb, redisLockKey, RiskControlLongVideoLockExp)
	ok, err := redisLock.Lock(context.Background())
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"pullVideo Lock key: %s error: %+v\n", redisLockKey, err)
		return
	} else if !ok {
		return
	}

	defer redisLock.Unlock(context.Background())

	// 获取 任务状态
	// 更新缓存任务状态
	redisKey := fmt.Sprintf(RiskControlLongVideoStatusFmt, handlerUtils.GetNameByRunEnv(), rci.TaskId)
	status, err := redisproxy.GetKeyValue(redisKey)
	if err != nil {
		if string(err.Error()) != string(redis.Nil) {
			logger.Log.Errorf(utils.MMark(logCtx)+"pullVideo get task status error: %+v\n", err)
			return
		}

		newrci, err := rci.GetRiskControlItem(gomysql.DB, rci.ID)
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+"pullVideo ID %s GetFile2Image err: %v \n", rci.TaskId, err)
			return
		}
		// 更新 rci 对象
		rci = newrci

		err = redisproxy.SetKeyValue(redisKey, string(rci.Status), RiskControlLongVideoStatusExp)
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+"pullVideo ID %s SetKeyValue err: %v \n", rci.TaskId, err)
			return
		}

		status = string(rci.Status)
		// 任务状态不存在，还继续查询任务状态，最后将结果写入redis，防止其他服务重复查询并写入数据库
		logger.Log.Warnf(utils.MMark(logCtx)+"pullVideo get task status is nil set status %s\n", status)
	}

	// 任务已经处理过，跳过
	if status == string(enum.TaskSucceed) || status == string(enum.TaskFailed) {
		return
	}

	rclv := GetRiskControlLongVideo()
	pullres := proto.RiskControlLongVideoCensorPullResponse{}
	retryNumber := 0
	censorRes := ""

	// 解析提交结果
	submitres := proto.RiskControlLongVideoCensorSubmitResponse{}
	err = json.Unmarshal([]byte(rci.SubmitResult), &submitres)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"pullVideo Unmarshal fail, url:%s", rci.FileUrl)
		return
	}

	// 申请qps限制
	key := fmt.Sprintf(RiskControlLongVideoPullQpsFmt, handlerUtils.GetNameByRunEnv())
	for {
		ok, err = checkQpsAllowance(key, RiskControlLongVideoPullQpsMax)
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+"submitVideo checkQpsAllowance is err:%v", err)
			return
		}

		if !ok {
			time.Sleep(10 * time.Millisecond)
			continue
		}

		break
	}

	for i := 0; i < 3; i++ {
		retryNumber += 1
		pullres = proto.RiskControlLongVideoCensorPullResponse{}
		censorRes = rclv.client.LongVideoCensorPull(submitres.Data.TaskId, nil)
		err := json.Unmarshal([]byte(censorRes), &pullres)
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+"pullVideo Unmarshal fail, taskId:%s", rci.TaskId)
			continue
		}

		if pullres.ErrorCode == 282877 {
			//视频抽帧错误 转码后重新提交
			err = longVideoTransformAndSubmitVideo(logCtx, rci)
			if err != nil {
				logger.Log.Errorf(utils.MMark(logCtx)+"longVideoTransformAndSubmitVideo fail, taskId:%s", rci.TaskId)
				pullres.ErrorCode = 200002
				pullres.ErrorMsg = "视频格式异常"
				break
			}
			return
		} else if pullres.ErrorCode != 0 {
			continue
		} else if len(pullres.Ret) > 0 && pullres.Ret != "0" {
			if pullres.Ret == "282008" {
				// taskId对应审核任务未审核完成
				nowtime := time.Now()
				if nowtime.Sub(rci.UpdatedAt) > 2*time.Hour {
					pullres.ErrorCode = 200001
					pullres.ErrorMsg = "taskId对应审核任务未审核完成,超时处理"
					censorRes = "taskId对应审核任务未审核完成,超时处理"
				} else {
					return
				}

			}
			continue
		}
		break
	}

	if pullres.ErrorCode != 0 {
		rci.Status = enum.TaskFailed
	} else if len(pullres.Ret) > 0 && pullres.Ret != "0" {
		if pullres.Ret == "282008" {
			// taskId对应审核任务未审核完成
			return
		}
		rci.Status = enum.TaskFailed
	} else if pullres.Data.ErrorCode != 0 || pullres.Data.ConclusionType == 4 { // "error_code":282877,"error_msg":"extract video error"
		rci.Status = enum.TaskFailed
	} else {
		rci.Status = enum.TaskSucceed
	}

	rci.ConclusionType = pullres.Data.ConclusionType
	rci.CensorResult = censorRes
	rci.RetryNumber = int64(retryNumber)
	rci.UpdatedAt = time.Now()
	rci.Duration = rci.UpdatedAt.Sub(rci.CreatedAt).Milliseconds()/1000 + 1

	if err := rci.UpdateRiskControlItem(gomysql.DB); err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"pullVideo UpdateRiskControlItem fail, taskId:%s", rci.TaskId)
		return
	}

	err = redisproxy.SetKeyValue(redisKey, string(rci.Status), RiskControlLongVideoStatusExp)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"pullVideo ID %s SetKeyValue err: %v \n", rci.TaskId, err)
		return
	}

	logger.Log.Infof(utils.MMark(logCtx)+"pullVideo taskId:%s status: %s conclusionType:%d", rci.TaskId, rci.Status, pullres.Data.ConclusionType)
}

func longVideoTransformAndSubmitVideo(logCtx context.Context, rci *model.RiskControlItem) error {
	censorVideoUrl := rci.FileUrl
	if len(rci.CompressionUrl) > 0 {
		censorVideoUrl = rci.CompressionUrl
	}

	// 下载文件并计算md5
	downpath := DownloadFilePrefix + uuid.New().String() + ".mp4"
	_, err := handlerUtils.CalculateMD5FromUrl(censorVideoUrl, downpath)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"censorVideo CalculateMD5FromUrl fail,err:%v url:%s", err, censorVideoUrl)
		return err
	}
	defer os.Remove(downpath)
	// 转码
	transformUrl, err := transcodeVideo(logCtx, downpath)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"censorVideo transcodeVideo fail,err:%v url:%s", err, censorVideoUrl)
		return err
	}

	rci.CompressionUrl = transformUrl
	rci.Status = enum.TaskReady
	rci.UpdatedAt = time.Now()

	err = rci.UpdateRiskControlItem(gomysql.DB)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+" UpdateRiskControlItem fail, taskId:%s", rci.TaskId)
		return err
	}

	// 获取 redis 分布式锁
	redisproxy := redisproxy.GetRedisProxy()
	redisKey := fmt.Sprintf(RiskControlLongVideoStatusFmt, handlerUtils.GetNameByRunEnv(), rci.TaskId)
	err = redisproxy.SetKeyValue(redisKey, string(rci.Status), RiskControlLongVideoStatusExp)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"submitVideo ID %s SetKeyValue err: %v \n", rci.TaskId, err)
		return err
	}
	return nil
}
