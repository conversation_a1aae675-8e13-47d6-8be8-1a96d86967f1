package figure

import (
	"acg-ai-go-common/gomysql"
	"acg-ai-go-common/logger"
	"acg-ai-go-common/storage"
	"acg-ai-go-common/utils"
	"acg-ai-go-common/utils/redisproxy"
	"context"
	"dhlive-external-api/beans/enum"
	"dhlive-external-api/beans/model"
	"dhlive-external-api/beans/proto"
	config "dhlive-external-api/conf"
	"dhlive-external-api/handler/gcpcensor"
	"dhlive-external-api/handler/handlerUtils"
	"dhlive-external-api/handler/handlerUtils/imageutils"
	"dhlive-external-api/handler/handlerUtils/redislock"
	"dhlive-external-api/handler/i18n/respi18n"
	"encoding/json"
	"errors"
	"fmt"
	"log"
	"net/http"
	"os"
	"path"
	"time"

	"cloud.google.com/go/vision/v2/apiv1/visionpb"
	"github.com/Baidu-AIP/golang-sdk/aip/censor"
	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

const (
	ImageSizeWidthMin  = 128
	ImageSizeHeightMin = 128

	ImageSizeWidthMax  = 4096
	ImageSizeHeightMax = 4096

	ImageBase64SizeMax = 4 * 1024 * 1024
	ImageBase64izeMin  = 5 * 1024

	RiskControlImageQueueFmt         = "%s:" + "riskcontrolimagequeue" + ":xiling-saas-v3"
	RiskControlImageQueueMaxInterval = 30

	RiskControlImageQpsFmt = "%s:" + "riskcontrolimageqps" + ":xiling-saas-v3"

	RiskControlImageLockFmt          = "%s:" + "riskControlshortAudio" + "%s"
	RiskControlImageLockTimeDuration = 2 * time.Minute
)

type RiskControlImage struct {
	client *censor.ContentCensorClient
}

func NewRiskControlImage() *RiskControlImage {
	return &RiskControlImage{
		client: censor.NewClient(config.LocalConfig.RiskControl.AK, config.LocalConfig.RiskControl.SK),
	}
}

func (f *RiskControlImage) CensorImage(c *gin.Context) {
	logCtx := context.WithValue(context.TODO(), utils.CtxKeyLogID, utils.RandStringRunes(16))

	accountID := "image"
	aid, ok := c.Get("AccountId")
	if ok {
		accountID = aid.(string)
	}

	// 获取目标语言
	targetLanguage := c.GetHeader("Language")
	logger.Log.Infof(utils.MMark(logCtx)+"  langugae in header is: %s", targetLanguage)
	res := proto.RiskControlImageResponse{
		LogID:    utils.GetLogID(logCtx),
		DataList: []proto.RiskControlImageItem{},
	}

	if !config.LocalConfig.RiskControl.IsSwitch {
		c.JSON(http.StatusOK, proto.NewSuccessRsp(res))
		return
	}

	// 解析请求参数
	req := proto.RiskControlImageRequery{}
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+" BindJSON fail, err:%v", err)
		c.JSON(http.StatusOK, proto.NewCommDataRsp(ParamErrorErrCode, ParamErrorErrMsg, res))
		return
	}

	// 校验参数
	if len(req.ImgUrls) == 0 && len(req.Images) == 0 {
		logger.Log.Errorf(utils.MMark(logCtx) + " imgUrls or Images is empty")
		c.JSON(http.StatusOK, proto.NewCommDataRsp(100003, respi18n.TransformResponseLocaleByDefault(logCtx, targetLanguage, "审核图片列表为空"), res))
		return
	}

	if len(req.ImgUrls) != 0 && len(req.Images) != 0 {
		logger.Log.Errorf(utils.MMark(logCtx) + " imgUrls and Images is not empty")
		c.JSON(http.StatusOK, proto.NewCommDataRsp(100003, respi18n.TransformResponseLocaleByDefault(logCtx, targetLanguage, "存在两种审核图片方式"), res))
		return
	}

	for _, url := range req.ImgUrls {
		if !VerifyBosUrl(url) {
			item := proto.RiskControlImageItem{}
			item.ErrorCode = 100005
			item.ErrorMsg = respi18n.TransformResponseLocaleByDefault(logCtx, targetLanguage, "图片链接不合法")
			res.DataList = append(res.DataList, item)
			continue
		}

		item, err := f.censorImageFromUrl(logCtx, url, accountID)
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+utils.ServiceInternalEsErrMsg+" censorImages fail, err:%v response:%v", err, item)
			item.ErrorCode = 100004
			item.ErrorMsg = respi18n.TransformResponseLocaleByDefault(logCtx, targetLanguage, "图片审核失败, 请稍后重试")
		}

		res.DataList = append(res.DataList, item)
	}

	for _, image := range req.Images {
		item, err := f.censorImageFromBase64(logCtx, image.ImageData, image.ImageName, accountID)
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+utils.ServiceInternalEsErrMsg+" censorImages fail, err:%v response:%v", err, item)
			item.ErrorCode = 100004
			item.ErrorMsg = respi18n.TransformResponseLocaleByDefault(logCtx, targetLanguage, "图片审核失败, 请稍后重试")
		}

		res.DataList = append(res.DataList, item)
	}

	if len(res.DataList) == 1 {
		if res.DataList[0].ErrorCode != 0 {
			c.JSON(http.StatusOK, proto.NewCommDataRsp(100004, res.DataList[0].ErrorMsg, res))
			return
		}
	}

	c.JSON(http.StatusOK, proto.NewSuccessRsp(res))
}

func (f *RiskControlImage) censorImageFromUrl(logCtx context.Context, imagesUrl, accountId string) (proto.RiskControlImageItem, error) {
	var item proto.RiskControlImageItem
	// 校验参数
	if len(imagesUrl) <= 0 {
		logger.Log.Errorf(utils.MMark(logCtx) + " imagesUrl is empty")
		return item, errors.New("imagesUrl is empty")
	}

	// url 转换
	if internalAddress, err := BosUrlReplaceInternalAddress(logCtx, imagesUrl); err != nil {
		// 如果失败了，就直接用原来的url去审核，这里打印日志即可，不影响业务逻辑执行
		logger.Log.Errorf(utils.MMark(logCtx)+" BosUrlReplaceInternalAddress fail, err:%v", err)
	} else {
		// 替换成功，则使用内部地址去审核
		imagesUrl = internalAddress
	}

	urlmd5 := handlerUtils.CalculateMD5FromText(imagesUrl)

	// 使用分布式锁锁住任务防止其他任务重复提交到数据库和任务处理
	// 获取锁
	redisProxy := redisproxy.GetRedisProxy()
	redisLockKey := fmt.Sprintf(RiskControlImageLockFmt, handlerUtils.GetNameByRunEnv(), urlmd5+"_lock")
	redisLock := redislock.NewRedisLock(redisProxy.Rdb, redisLockKey, RiskControlImageLockTimeDuration)

	ok, err := redisLock.Lock(context.Background())
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+utils.RedisEsErrMsg+"censorImageFromUrl Lock key: %s error: %+v\n", redisLockKey, err)
		return item, err
	} else if !ok {
		err = acquireCensorLockWithBlock(logCtx, redisLock)
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+" acquireCensorLockWithBlock error: %+v\n", err)
			return item, err
		}
	}
	defer redisLock.Unlock(context.Background())

	// 从缓存中获取数据
	if cacheItem, err := getCensorImageDbCache(logCtx, urlmd5); err == nil {
		return cacheItem, nil
	}

	// 申请队列
	queueKey := fmt.Sprintf(RiskControlImageQueueFmt, handlerUtils.GetNameByRunEnv())
	ok, err = checkQueueAllowance(queueKey, config.LocalConfig.RiskControl.ImageQueueSize, RiskControlImageQueueMaxInterval)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"censorImages checkQueueAllowance is err:%v", err)
		return item, err
	}

	if !ok {
		return item, errors.New("queue is full")
	}

	defer decrementCounter(queueKey)

	// 申请qps限制
	key := fmt.Sprintf(RiskControlImageQpsFmt, handlerUtils.GetNameByRunEnv())
	for {
		ok, err = checkQpsAllowance(key, config.LocalConfig.RiskControl.ImageQpsMax)
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+"censorImages checkQpsAllowance is err:%v", err)
			return item, err
		}

		if !ok {
			time.Sleep(10 * time.Millisecond)
			continue
		}

		break
	}

	// 判断是否国际版
	if config.LocalConfig.RiskControl.IsInternational {
		return censorImageByGcp(logCtx, imagesUrl, accountId)
	}

	return f.censorImageByChinese(logCtx, imagesUrl, accountId)
}

func (f *RiskControlImage) censorImageByChinese(logCtx context.Context, imagesUrl, accountId string) (proto.RiskControlImageItem, error) {
	var item proto.RiskControlImageItem
	starttime := time.Now()
	retryNumber := 0
	res := ""
	censorurl := imagesUrl
	compressionUrl := ""
	urlmd5 := handlerUtils.CalculateMD5FromText(censorurl)
	// url 转换
	if internalAddress, err := BosUrlReplaceInternalAddress(logCtx, censorurl); err != nil {
		// 如果失败了，就直接用原来的url去审核，这里打印日志即可，不影响业务逻辑执行
		logger.Log.Errorf(utils.MMark(logCtx)+" BosUrlReplaceInternalAddress fail, err:%v", err)
	} else {
		// 替换成功，则使用内部地址去审核
		censorurl = internalAddress
	}

	censorurl, err := handlerUtils.EncodeUrl(censorurl)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"EncodeUrl fail, url:%s", censorurl)
		return item, err
	}

	response := proto.RiskControlImageCensor{}
	for i := 0; i < 3; i++ {
		// 重新初始化
		response = proto.RiskControlImageCensor{}
		retryNumber += 1
		options := make(map[string]interface{})
		options["strategyId"] = config.LocalConfig.RiskControl.StrategyId
		// 调用审核接口
		res = f.client.ImgCensorUrl(censorurl, options)
		logger.Log.Infof(utils.MMark(logCtx)+"censorImages ImgCensor res:%v", res)
		err = json.Unmarshal([]byte(res), &response)
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+"censorImages ImgCensor Unmarshal is err:%v", err)
			time.Sleep(1 * time.Second)
			continue
		}

		// 错误信息 IAM Certification failed 这个错误码重复验证一下可能就成功了
		if response.ErrorCode == 14 {
			logger.Log.Errorf(utils.MMark(logCtx)+"censorImages ImgCensor IAM Certification failed, res:%v", res)
			continue
		} else if response.ConclusionType == 4 { // 审核失败 重试三次
			logger.Log.Errorf(utils.MMark(logCtx)+"censorImages ImgCensor censor failed res:%v", res)
			time.Sleep(1 * time.Second)
			continue
		} else if response.ErrorCode == 216202 || response.ErrorCode == 216203 { // 图片url 过大，需要压缩
			tmpurl, err := f.compressImageUrl(logCtx, censorurl)
			if err != nil {
				logger.Log.Errorf(utils.MMark(logCtx)+"censorImages compressImageUrl is err:%v", err)
				return item, err
			}

			// url 转换
			if internalAddress, err := BosUrlReplaceInternalAddress(logCtx, tmpurl); err != nil {
				// 如果失败了，就直接用原来的url去审核，这里打印日志即可，不影响业务逻辑执行
				logger.Log.Errorf(utils.MMark(logCtx)+"HandleRequest BosUrlReplaceInternalAddress fail, err:%v", err)
			} else {
				// 替换成功，则使用内部地址去审核
				tmpurl = internalAddress
			}

			censorurl = tmpurl
			compressionUrl = tmpurl
			continue
		} else if response.ErrorCode == 216201 { // 图片格式不对
			logger.Log.Errorf(utils.MMark(logCtx)+"censorImages ImgCensor image format error res:%v", res)
			break
		} else if response.ErrorCode == 18 { // qps 超限
			logger.Log.Errorf(utils.MMark(logCtx)+"censorImages ImgCensor qps limit res:%v", res)
			time.Sleep(1 * time.Second)
			continue
		} else if response.ErrorCode != 0 { // 其他错误
			logger.Log.Errorf(utils.MMark(logCtx)+"censorImages ImgCensor failed res:%v", res)
			time.Sleep(1 * time.Second)
			continue
		}

		break
	}

	taskstatus := enum.TaskSucceed
	if response.ErrorCode != 0 || response.ConclusionType == 0 {
		taskstatus = enum.TaskFailed
	}

	endtime := time.Now()
	duration := endtime.Sub(starttime).Milliseconds()/1000 + 1
	taskid := "rci-" + utils.RandStringRunes(16)
	// 更新数据库
	r := model.RiskControlItem{
		AccountID:      accountId,
		LogId:          utils.GetLogID(logCtx),
		TaskId:         taskid,
		FileUrl:        imagesUrl,
		CompressionUrl: compressionUrl,
		Type:           model.RiskControlTypeImage,
		Md5:            urlmd5,
		UrlMd5:         urlmd5,
		ConclusionType: response.ConclusionType,
		CensorResult:   res,
		Status:         taskstatus,
		Duration:       duration,
		RetryNumber:    int64(retryNumber),
		CreatedAt:      time.Now(),
		UpdatedAt:      time.Now(),
	}

	// 更新数据库
	err = r.UpdateRiskControlItem(gomysql.DB)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"censorImages UpdateRiskControlItem is err:%v", err)
		return item, err
	}

	item = getRiskControlImageItemByChinese(&response, taskid)
	return item, nil
}

func censorImageByGcp(logCtx context.Context, imagesUrl, accountId string) (proto.RiskControlImageItem, error) {
	var item proto.RiskControlImageItem
	starttime := time.Now()
	retryNumber := 0

	censorurl := imagesUrl
	urlmd5 := handlerUtils.CalculateMD5FromText(censorurl)
	censorurl, err := handlerUtils.EncodeUrl(censorurl)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"EncodeUrl fail, url:%s", censorurl)
		return item, err
	}

	response, err := gcpcensor.CensorImageByGcp(logCtx, censorurl)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"censorImages ImgCensor res:%v", response)
		return item, err
	}

	logger.Log.Infof(utils.MMark(logCtx)+"censorImages ImgCensor res:%v", response)

	endtime := time.Now()
	duration := endtime.Sub(starttime).Milliseconds()/1000 + 1
	taskid := "rci-" + utils.RandStringRunes(16)

	// 将response 转为 proto.RiskControlImageItem
	item = getRiskControlImageItemByGcp(logCtx, response, taskid)
	taskstatus := enum.TaskSucceed

	if item.ErrorCode != 0 {
		taskstatus = enum.TaskFailed
	}

	itemByte, err := json.Marshal(item)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"censorImages json.Marshal is err:%v", err)
		return item, err
	}

	// 更新数据库
	r := model.RiskControlItem{
		AccountID:      accountId,
		LogId:          utils.GetLogID(logCtx),
		TaskId:         taskid,
		FileUrl:        imagesUrl,
		CompressionUrl: "",
		Type:           model.RiskControlTypeImageGCP,
		Md5:            urlmd5,
		UrlMd5:         urlmd5,
		ConclusionType: item.ConclusionType,
		CensorResult:   string(itemByte),
		Status:         taskstatus,
		Duration:       duration,
		RetryNumber:    int64(retryNumber),
		CreatedAt:      time.Now(),
		UpdatedAt:      time.Now(),
	}

	// 更新数据库
	err = r.UpdateRiskControlItem(gomysql.DB)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"censorImages UpdateRiskControlItem is err:%v", err)
		return item, err
	}

	return item, nil
}

func (f *RiskControlImage) compressImageUrl(logCtx context.Context, imagesUrl string) (string, error) {
	// 获取文件格式
	format, err := handlerUtils.GetFileExtensionFromURL(imagesUrl)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"censorImages GetFileExtensionFromURL is err:%v", err)
		return "", err
	}
	uuid := uuid.New().String()
	downloadFile := DownloadFilePrefix + uuid + format
	adaptiveFile := DownloadFilePrefix + uuid + "_adaptive" + format
	compressFile := DownloadFilePrefix + uuid + "_compress.jpg"
	// 下载图片
	err = storage.RetryDownloadFile(imagesUrl, downloadFile, 3)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"censorImages DownloadFile is err:%v", err)
		return "", err
	}
	// 删除临时文件
	defer os.Remove(downloadFile)
	defer os.Remove(adaptiveFile)
	defer os.Remove(compressFile)

	// 图片尺寸处理
	ok, err := adaptiveImageSize(downloadFile, adaptiveFile)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"censorImageFromBase64 adaptiveImageSize is err:%v", err)
		return "", err
	}

	if !ok {
		adaptiveFile = downloadFile
	}

	// 图片质量处理
	ok, err = compressImages(adaptiveFile, compressFile)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"censorImageFromBase64 compressImages is err:%v", err)
		return "", err
	}

	if !ok {
		compressFile = adaptiveFile
	}
	// 图片上传bos
	// 上传bos
	bosUrl, err := RetryUploadBosServiceFromFile(logCtx, FigureRiskControlImagePath, compressFile, format)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+utils.ThirdPartyInterfaceEsErrMsg+"censorImageFromBase64 RetryUploadBosServiceFromFile is err:%v", err)
		return "", err
	}
	return bosUrl, nil
}

func (f *RiskControlImage) censorImageFromBase64(logCtx context.Context, imageBase64Data, imageFileName, accountID string) (proto.RiskControlImageItem, error) {
	var response proto.RiskControlImageItem
	// 获取文件格式
	format := path.Ext(imageFileName)
	uuid := uuid.New().String()
	downloadFile := DownloadFilePrefix + uuid + format

	// 保存base64编码的图片文件
	err := handlerUtils.SaveDecodeBase64(imageBase64Data, downloadFile)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"censorImageFromBase64 SaveDecodeBase64 is err:%v", err)
		return response, err
	}
	defer os.Remove(downloadFile)
	// 上传bos
	bosUrl, err := RetryUploadBosServiceFromFile(logCtx, FigureRiskControlImagePath, downloadFile, format)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+utils.ThirdPartyInterfaceEsErrMsg+"censorImageFromBase64 RetryUploadBosServiceFromFile is err:%v", err)
		return response, err
	}

	return f.censorImageFromUrl(logCtx, bosUrl, accountID)
}

func adaptiveImageSize(file, adaptiveFile string) (bool, error) {
	// 获取图片尺寸
	img, err := imageutils.GetImageSize(file)
	if err != nil {
		return false, err
	}

	w := img.Bounds().Dx()
	h := img.Bounds().Dy()

	newWidth, newHeight := CalculateScaledDimensions(w, h, ImageSizeWidthMin, ImageSizeWidthMax, ImageSizeHeightMin, ImageSizeHeightMax)

	if newWidth == w && newHeight == h {
		return false, nil
	}

	if err = imageutils.ResizeImage(img, adaptiveFile, newWidth, newHeight); err != nil {
		return false, err
	}

	return true, nil
}

// compressImages 压缩图片函数
//
// 参数：
// adaptiveFile string - 待压缩的图片文件路径
// outputFilePath string - 压缩后的图片输出路径
//
// 返回值：
// bool - 是否成功压缩图片，成功返回true，否则返回false
// error - 返回压缩图片过程中遇到的错误，如果压缩成功则返回nil
func compressImages(adaptiveFile string, outputFilePath string) (bool, error) {
	ok, err := CheckBase64EncodedSize(adaptiveFile)
	if err != nil {
		return false, err
	}

	if ok {
		return false, nil
	}

	encodedSize, err := handlerUtils.GetFileBase64EncodeSize(adaptiveFile)
	if err != nil {
		return false, err
	}

	if encodedSize > ImageBase64SizeMax {
		// 不是JPG或WEBP格式，则转换为JPG,方便后续压缩
		if err = imageutils.ConvertToJPEG(adaptiveFile, outputFilePath, 10); err != nil {
			return false, err
		}
	} else if encodedSize < ImageBase64izeMin {
		if err = imageutils.ConvertToJPEG(adaptiveFile, outputFilePath, 100); err != nil {
			return false, err
		}
	}

	return true, nil
}

// CheckBase64EncodedSize 检查文件Base64编码后的大小是否在指定范围内
// adaptiveFile: 要检查的文件
// handlerUtils: 假设这是提供GetFileBase64EncodeSize方法的工具实例
// 返回两个值：一个布尔值表示大小是否在范围外，以及一个可能的错误
func CheckBase64EncodedSize(adaptiveFile string) (bool, error) {
	encodedSize, err := handlerUtils.GetFileBase64EncodeSize(adaptiveFile)
	if err != nil {
		return false, err
	}

	if encodedSize <= ImageBase64SizeMax && encodedSize >= ImageBase64izeMin {
		return true, nil
	}

	// 大小在范围内，返回false
	return false, nil
}

// CalculateScaledDimensions 计算等比缩放后的宽度和高度，并确保它们不小于最小分辨率，同时如果在范围内则不进行缩放
// 原始宽度、原始高度、最小宽度、最大宽度、最小高度、最大高度
func CalculateScaledDimensions(origWidth, origHeight, minWidth, maxWidth, minHeight, maxHeight int) (int, int) {
	// 如果宽度和高度已经在范围内，则无需修改
	if origWidth >= minWidth && origWidth <= maxWidth && origHeight >= minHeight && origHeight <= maxHeight {
		return origWidth, origHeight
	}

	widthRatio := 1.0
	heightRatio := 1.0
	scaleRatio := 1.0

	if origWidth > maxWidth || origHeight > maxHeight {
		widthRatio = float64(maxWidth) / float64(origWidth)
		heightRatio = float64(maxHeight) / float64(origHeight)
		// 选择较小的缩放比例，以确保宽高比不变
		scaleRatio = widthRatio
		if heightRatio < widthRatio {
			scaleRatio = heightRatio
		}
	} else {
		widthRatio = float64(minWidth) / float64(origWidth)
		heightRatio = float64(minHeight) / float64(origHeight)
		// 选择较小的缩放比例，以确保宽高比不变
		scaleRatio = widthRatio
		if heightRatio > widthRatio {
			scaleRatio = heightRatio
		}
	}

	// 计算新的宽度和高度
	newWidth := int(float64(origWidth) * scaleRatio)
	newHeight := int(float64(origHeight) * scaleRatio)

	// 确保宽度和高度不小于最小值
	if newWidth < minWidth {
		newWidth = minWidth
	} else if newWidth > maxWidth {
		newWidth = maxWidth
	}

	if newHeight < minHeight {
		newHeight = minHeight
	} else if newHeight > maxHeight {
		newHeight = maxHeight
	}

	// 返回新的宽度和高度
	return newWidth, newHeight
}

func getCensorImageDbCache(logCtx context.Context, urlmd5 string) (proto.RiskControlImageItem, error) {
	item := proto.RiskControlImageItem{
		ErrorCode:      0,
		ErrorMsg:       "",
		Conclusion:     "合规",
		ConclusionType: 1,
		Data:           []proto.RiskControlImageItemDetail{},
	}

	// 查找数据库中是否解析过该图片
	riskitem, err := (&model.RiskControlItem{}).GetRiskControlItemFromUrlMd5(gomysql.DB, urlmd5)
	if err != nil {
		logger.Log.Infof(utils.MMark(logCtx)+" GetRiskControlItemFromMd5 is nil urlmd5:%s", urlmd5)
		return item, err
	}

	logCtx = context.WithValue(context.TODO(), utils.CtxKeyLogID, riskitem.LogId)
	// 如果数据库存在则直接获取审核结果并返回
	if riskitem.Type == model.RiskControlTypeImage {
		var response proto.RiskControlImageCensor
		err = handlerUtils.JSONToStruct(riskitem.CensorResult, &response)
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+" JSONToStruct is err:%v", err)
			return item, err
		}
		item = getRiskControlImageItemByChinese(&response, riskitem.TaskId)
	} else if riskitem.Type == model.RiskControlTypeImageGCP {
		err = handlerUtils.JSONToStruct(riskitem.CensorResult, &item)
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+" JSONToStruct is err:%v", err)
			return item, err
		}

	}
	logger.Log.Infof(utils.MMark(logCtx)+" GetRiskControlItemFromMd5 is success db logId:%s", riskitem.LogId)
	return item, nil

}

func getRiskControlImageItemByGcp(logCtx context.Context, censorRes *visionpb.BatchAnnotateImagesResponse, taskID string) proto.RiskControlImageItem {
	item := proto.RiskControlImageItem{
		TaskId:         taskID,
		ErrorCode:      0,
		ErrorMsg:       "",
		Conclusion:     "合规",
		ConclusionType: 1,
		Data:           []proto.RiskControlImageItemDetail{},
	}
	targetLanguageValue := logCtx.Value("Language")
	targetLanguage := "en"
	if targetLanguageValue != nil {
		targetLanguage = targetLanguageValue.(string)
	}

	for _, res := range censorRes.GetResponses() {
		if res.SafeSearchAnnotation == nil {
			log.Println("未检测到安全搜索注释")
			continue
		}

		if res.GetError() != nil {
			item.ErrorCode = int64(res.GetError().Code)
			item.ErrorMsg = res.GetError().Message
			item.Conclusion = "审核失败"
			item.ConclusionType = 4
			return item
		}

		for _, censorType := range config.LocalConfig.RiskControl.GCPCensorImageType {
			if res.SafeSearchAnnotation.Adult >= visionpb.Likelihood_LIKELY && censorType == "Adult" {
				item.Data = append(item.Data, proto.RiskControlImageItemDetail{
					Msg: respi18n.TransformResponseLocaleByDefault(logCtx, targetLanguage, "Adult"),
				})
			}

			if res.SafeSearchAnnotation.Racy >= visionpb.Likelihood_VERY_LIKELY && censorType == "Racy" {
				item.Data = append(item.Data, proto.RiskControlImageItemDetail{
					Msg: respi18n.TransformResponseLocaleByDefault(logCtx, targetLanguage, "Racy"),
				})
			}

			if res.SafeSearchAnnotation.Violence >= visionpb.Likelihood_LIKELY && censorType == "Violence" {
				item.Data = append(item.Data, proto.RiskControlImageItemDetail{
					Msg: respi18n.TransformResponseLocaleByDefault(logCtx, targetLanguage, "Violence"),
				})
			}

			if res.SafeSearchAnnotation.Spoof >= visionpb.Likelihood_VERY_LIKELY && censorType == "Spoof" {
				item.Data = append(item.Data, proto.RiskControlImageItemDetail{
					Msg: respi18n.TransformResponseLocaleByDefault(logCtx, targetLanguage, "Spoof"),
				})
			}

			if res.SafeSearchAnnotation.Medical >= visionpb.Likelihood_VERY_LIKELY && censorType == "Medical" {
				item.Data = append(item.Data, proto.RiskControlImageItemDetail{
					Msg: respi18n.TransformResponseLocaleByDefault(logCtx, targetLanguage, "Medical"),
				})
			}
		}

	}

	if len(item.Data) > 0 {
		item.Conclusion = "不合规"
		item.ConclusionType = 2
	}

	return item
}

func getRiskControlImageItemByChinese(censorRes *proto.RiskControlImageCensor, taskID string) proto.RiskControlImageItem {
	item := proto.RiskControlImageItem{
		TaskId:         taskID,
		ErrorCode:      censorRes.ErrorCode,
		ErrorMsg:       censorRes.ErrorMsg,
		Conclusion:     censorRes.Conclusion,
		ConclusionType: censorRes.ConclusionType,
		Data:           []proto.RiskControlImageItemDetail{},
	}

	if censorRes.ConclusionType != 2 {
		return item
	}

	for _, detail := range censorRes.Data {
		item.Data = append(item.Data, proto.RiskControlImageItemDetail{
			ErrorCode: detail.ErrorCode,
			ErrorMsg:  detail.ErrorMsg,
			Msg:       detail.Msg,
		})
	}

	return item
}
