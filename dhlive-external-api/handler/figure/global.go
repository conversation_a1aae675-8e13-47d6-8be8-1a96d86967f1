package figure

import (
	"acg-ai-go-common/gomysql"
	"acg-ai-go-common/logger"
	"acg-ai-go-common/utils"
	"context"
	"dhlive-external-api/beans/enum"
	"dhlive-external-api/beans/model"
	config "dhlive-external-api/conf"
	"dhlive-external-api/handler/handlerUtils"
	"dhlive-external-api/handler/handlerUtils/ffmpegutils"
	"dhlive-external-api/handler/handlerUtils/ratelimiter"
	"errors"
	"fmt"
	"net/url"
	"os"
	"path/filepath"
	"strings"
	"time"

	"github.com/baidubce/bce-sdk-go/services/bos"
	"github.com/google/uuid"
	"gorm.io/gorm"
)

var (
	BosAk       = "ALTAKxdVkHMs4sp0Tgkpa3zCJP"
	BosSk       = "e4f46ff5d1bf45c5b81dd867d6e3c148"
	BosEndpoint = "bj.bcebos.com"
	BosBucket   = "xiling-dh"
	BosHost     = "https://xiling-dh.bj.bcebos.com"
	BosCdnHost  = "https://xiling-dh.cdn.bcebos.com"
)

type BosObjectKeyPath string

const (
	FigureFile2ImagePath       BosObjectKeyPath = "/figure-file2iamge/"
	FigureRiskControlVideoPath BosObjectKeyPath = "/figure-riskcontrol/video/"
	FigureRiskControlImagePath BosObjectKeyPath = "/figure-riskcontrol/image/"
	FigureRiskControlAudioPath BosObjectKeyPath = "/figure-riskcontrol/audio/"
)

const (
	DownloadFilePrefix          = "./cache/"
	__voiceCensorUserDefinedUrl = "https://aip.baidubce.com/rest/2.0/solution/v1/voice_censor/v3/user_defined"
	__textCensorUserDefinedUrl  = "https://aip.baidubce.com/rest/2.0/solution/v1/text_censor/v2/user_defined"
)

// 错误码
const (
	ParamErrorErrCode = 100001
	ParamErrorErrMsg  = "参数错误"

	ParamEmptyErrCode = 100002
	ParamEmptyErrMsg  = "请求参数为空"
)

func InitBosParam() {
	// 初始化bos
	BosAk = config.LocalConfig.BosSetting.AK
	BosSk = config.LocalConfig.BosSetting.SK
	BosEndpoint = config.LocalConfig.BosSetting.Endpoint
	BosBucket = config.LocalConfig.BosSetting.Bucket
	BosHost = config.LocalConfig.BosSetting.Host
	BosCdnHost = config.LocalConfig.BosSetting.CDNHost
}

func checkQueueAllowance(queueKey string, queueMaxNumber int, queueMaxInterval int64) (bool, error) {
	ok, err := ratelimiter.AllowFromLua(queueKey, queueMaxNumber, queueMaxInterval)
	if err != nil {
		return false, err
	}

	if !ok {
		return false, nil
	}

	return true, nil
}

func decrementCounter(queueKey string) (int64, error) {
	return ratelimiter.DecrementCounter(queueKey)
}

func checkQpsAllowance(key string, qpsMax int) (bool, error) {
	ok, err := ratelimiter.Allow(key, qpsMax, time.Second)
	if err != nil {
		return false, err
	}

	if !ok {
		return false, nil
	}

	return true, nil
}

func searchRiskControlItemFromMd5(logCtx context.Context, md5 string) (*model.RiskControlItem, error) {
	item := &model.RiskControlItem{}

	rcitem, err := item.GetRiskControlItemFromMd5(gomysql.DB, md5)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) { // 使用errors.Is来检查特定错误
			return item, nil
		}
		logger.Log.Errorf(utils.MMark(logCtx) + "searchVideo GetRiskControlItemFromMd5 fail")
		return item, err
	}

	return rcitem, nil
}

func searchRiskControlItemFromUrlMd5(logCtx context.Context, urlmd5 string) (*model.RiskControlItem, error) {
	item := &model.RiskControlItem{}

	rcitem, err := item.GetRiskControlItemFromUrlMd5(gomysql.DB, urlmd5)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) { // 使用errors.Is来检查特定错误
			return item, nil
		}
		logger.Log.Errorf(utils.MMark(logCtx) + "searchVideo GetRiskControlItemFromUrlMd5 fail")
		return item, err
	}

	return rcitem, nil
}

func saveRiskControlSubmitToDb(logCtx context.Context, url, accountID string, taskType model.RiskControlType) (string, error) {
	taskIdPrefix := ""
	md5 := ""
	compressionUrl := ""
	downpath := ""

	var err error
	var ffprobeInfo ffmpegutils.FFprobeInfo

	if taskType == model.RiskControlTypeLongVideo {
		taskIdPrefix = "rcv-"

		fileuuid := uuid.New().String()
		format, err := handlerUtils.GetFileExtensionFromURL(url)
		if err != nil {
			return "", err
		}
		// 下载文件
		downpath = DownloadFilePrefix + fileuuid + format
		md5, err = handlerUtils.CalculateMD5FromUrl(url, downpath)
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+"saveRiskControlSubmitToDb CalculateMD5FromUrl fail,err:%v url:%s", err, url)
			return "", err
		}
		defer os.Remove(downpath)

	} else if taskType == model.RiskControlTypeLongAudio {
		taskIdPrefix = "rca-"

		ffprobeInfo, md5, err = ffmpegutils.CalculateMd5AndFormatAndRate(url, DownloadFilePrefix)
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+"searchVideo CalculateMd5AndFormatAndRate fail, err:%v url:%s", err, url)
			return "", err
		}

		if ffprobeInfo.Error != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+"searchVideo CalculateMd5AndFormatAndRate fail, err:%v url:%s", ffprobeInfo.Error, url)
			return "", ffprobeInfo.Error
		}

	} else {
		return "", fmt.Errorf("task type is not support")
	}

	rcitem, err := searchRiskControlItemFromMd5(logCtx, md5)
	if err != nil {
		return "", err
	}

	if rcitem.ID > 0 {
		return rcitem.TaskId, nil
	}

	if taskType == model.RiskControlTypeLongVideo {
		// 获取文件大小
		fsize, err := handlerUtils.GetFileSize(downpath)
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+"saveRiskControlSubmitToDb GetFileSize err: %v \n", err)
			return "", err
		}
		format, err := handlerUtils.GetFileExtensionFromURL(url)
		if err != nil {
			return "", err
		}
		// 判断文件大小是否超过限制
		if fsize > RiskControlShortVideoFileSizeMax {
			// 压缩文件
			fileuuid := uuid.New().String()
			outpath := DownloadFilePrefix + fileuuid + format
			err = ffmpegutils.CompressVideo(downpath, outpath, "28")
			if err != nil {
				logger.Log.Errorf(utils.MMark(logCtx)+"saveRiskControlSubmitToDb  CompressVideo err: %v \n", err)
				return "", err
			}
			defer os.Remove(outpath)
			// 上传bos
			compressionUrl, err = RetryUploadBosServiceFromFile(logCtx, FigureRiskControlVideoPath, outpath, ".mp4")
			if err != nil {
				logger.Log.Errorf(utils.MMark(logCtx)+"saveRiskControlSubmitToDb UploadBosServiceFromFile err: %v \n", err)
				return "", err
			}
		}
	}

	taskid := taskIdPrefix + utils.RandStringRunes(16)

	rcitem = &model.RiskControlItem{
		AccountID:      accountID,
		LogId:          utils.GetLogID(logCtx),
		TaskId:         taskid,
		FileUrl:        url,
		CompressionUrl: compressionUrl,
		AudioType:      ffprobeInfo.CodecName,
		AudioRate:      ffprobeInfo.SampleRate,
		Type:           taskType,
		Md5:            md5,
		Status:         enum.TaskReady,
		CreatedAt:      time.Now(),
		UpdatedAt:      time.Now(),
	}

	if err = rcitem.UpdateRiskControlItem(gomysql.DB); err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"censorVideo UpdateRiskControlItem fail, url:%s", url)
		return "", err
	}

	return rcitem.TaskId, nil
}

func IsHitWordWithSave(word string, hitmap map[string]struct{}) ([]string, bool) {
	retwords := make([]string, 0)
	words := strings.Split(word, "&")
	for _, w := range words {
		if _, ok := hitmap[w]; !ok {
			retwords = append(retwords, w)
			hitmap[w] = struct{}{}
		}
	}
	return retwords, true
}

func RetryUploadBosServiceFromFile(logCtx context.Context, objectKeyPath BosObjectKeyPath, filePath string, fileFormat string) (string, error) {
	url := ""
	var err error
	for i := 0; i < 3; i++ {
		url, err = UploadBosServiceFromFile(logCtx, objectKeyPath, filePath, fileFormat)
		if err == nil && url != "" {
			return url, nil
		}
		time.Sleep(1 * time.Second)
	}
	return url, err
}

func UploadBosServiceFromFile(logCtx context.Context, objectKeyPath BosObjectKeyPath, filePath string, fileFormat string) (string, error) {
	bosClient, err := bos.NewClient(BosAk, BosSk, BosEndpoint)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"UploadBosServiceFromFile Create BOS client failed: %v\n", err)
		return "", err
	}
	// 生成文件名
	filename := uuid.NewString() + fileFormat
	objectKey := filepath.Join(string(objectKeyPath), time.Now().Format("2006-01-02"), filename)

	// 从本地文件上传
	_, err = bosClient.PutObjectFromFile(BosBucket, objectKey, filePath, nil)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"UploadBosServiceFromFile PutObjectFromFile fail, err:%v", err)
		return "", err
	}
	downloadUrl := BosCdnHost + objectKey
	return downloadUrl, nil
}

// ModifyURL 修改协议为 HTTP 并根据旧的 Host 名称决定是否替换 Host
func ModifyURL(originalURL, oldHost, newHost string) (string, error) {
	// 解析原始 URL
	parsedURL, err := url.Parse(originalURL)
	if err != nil {
		return "", err
	}

	// 只修改协议为 http
	parsedURL.Scheme = "http"

	// 如果 Host 匹配旧的 Host，替换成新的 Host
	if parsedURL.Host == oldHost {
		parsedURL.Host = newHost
	}

	// 返回修改后的 URL
	return parsedURL.String(), nil
}

func BosUrlReplaceInternalAddress(logCtx context.Context, originalURL string) (string, error) {
	// 解析原始 URL
	parsedURL, err := url.Parse(originalURL)
	if err != nil {
		return "", err
	}

	if strings.Contains(parsedURL.Host, "digital-human-pipeline-output.cdn.bcebos.com") {
		return ModifyURL(originalURL, "digital-human-pipeline-output.cdn.bcebos.com", "digital-human-pipeline-output.bj.bcebos.com")
	} else if strings.Contains(parsedURL.Host, "xiling-dh.cdn.bcebos.com") {
		return ModifyURL(originalURL, "xiling-dh.cdn.bcebos.com", "xiling-dh.bj.bcebos.com")
	} else if strings.Contains(parsedURL.Host, "meta-human-editor-prd.cdn.bcebos.com") {
		return ModifyURL(originalURL, "meta-human-editor-prd.cdn.bcebos.com", "meta-human-editor-prd.bj.bcebos.com")
	} else {
		parsedURL.Scheme = "http"
		return parsedURL.String(), nil
	}
}
