package gcpcensor

import (
	"acg-ai-go-common/global"
	"acg-ai-go-common/logger"
	"acg-ai-go-common/utils"
	"context"
	"fmt"
	"log"
	"net/url"
	"strings"
	"sync"

	language "cloud.google.com/go/language/apiv2"
	languagepb "cloud.google.com/go/language/apiv2/languagepb"
	speech "cloud.google.com/go/speech/apiv1"
	speechpb "cloud.google.com/go/speech/apiv1/speechpb"
	video "cloud.google.com/go/videointelligence/apiv1"
	videopb "cloud.google.com/go/videointelligence/apiv1/videointelligencepb"
	vision "cloud.google.com/go/vision/v2/apiv1"
	visionpb "cloud.google.com/go/vision/v2/apiv1/visionpb"
	"google.golang.org/api/option"
)

var (
	visionClient   *vision.ImageAnnotatorClient
	languageClient *language.Client
	speechClient   *speech.Client
	videoClient    *video.Client

	initOnce sync.Once
)

// InitGCPClients 初始化所有 GCP 客户端（只执行一次）
func InitGCPClients(ctx context.Context) {
	initOnce.Do(func() {
		// if err := os.Setenv("GOOGLE_APPLICATION_CREDENTIALS", global.ServerSetting.StorageSetting.GcsSetting.Credential); err != nil {
		// 	log.Panicf("设置环境变量失败: {%v}", err)
		// }
		var err error
		visionClient, err = vision.NewImageAnnotatorClient(ctx, option.WithCredentialsFile(global.ServerSetting.StorageSetting.GcsSetting.Credential))
		if err != nil {
			log.Fatalf("创建 Vision 客户端失败: %v", err)
		}
		// Natural Language
		languageClient, err = language.NewClient(ctx, option.WithCredentialsFile(global.ServerSetting.StorageSetting.GcsSetting.Credential))
		if err != nil {
			log.Fatalf("创建 Natural Language 客户端失败: %v", err)
		}

		// Speech-to-Text
		speechClient, err = speech.NewClient(ctx, option.WithCredentialsFile(global.ServerSetting.StorageSetting.GcsSetting.Credential))
		if err != nil {
			log.Fatalf("创建 Speech 客户端失败: %v", err)
		}

		// Video Intelligence
		videoClient, err = video.NewClient(ctx, option.WithCredentialsFile(global.ServerSetting.StorageSetting.GcsSetting.Credential))
		if err != nil {
			log.Fatalf("创建 Video Intelligence 客户端失败: %v", err)
		}
	})
}

// CloseGCPClients 关闭所有 GCP 客户端
func CloseGCPClients() {
	if languageClient != nil {
		_ = languageClient.Close()
	}
	if speechClient != nil {
		_ = speechClient.Close()
	}
	if videoClient != nil {
		_ = videoClient.Close()
	}
}

// GetVisionClient 获取已初始化的 Vision 客户端
func GetVisionClient() *vision.ImageAnnotatorClient {
	return visionClient
}

// GetLanguageClient 获取已初始化的 Natural Language 客户端
func GetLanguageClient() *language.Client {
	return languageClient
}

// GetSpeechClient 获取已初始化的 Speech 客户端
func GetSpeechClient() *speech.Client {
	return speechClient
}

// GetVideoClient 获取已初始化的 Video Intelligence 客户端
func GetVideoClient() *video.Client {
	return videoClient
}

func CensorImageByGcp(logCtx context.Context, url string) (*visionpb.BatchAnnotateImagesResponse, error) {
	ctx := context.Background()
	uri, err := convertPublicURLToGCSURI(url)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+" convertPublicURLToGCSURI err: %v", err)
		return nil, err
	}

	// 构建请求，只使用 SafeSearchDetection 功能
	req := &visionpb.BatchAnnotateImagesRequest{
		Requests: []*visionpb.AnnotateImageRequest{
			{
				Image: &visionpb.Image{
					Source: &visionpb.ImageSource{
						GcsImageUri: uri,
					},
				},
				Features: []*visionpb.Feature{
					{
						Type: visionpb.Feature_SAFE_SEARCH_DETECTION,
					},
				},
			},
		},
	}

	// 发送请求
	resp, err := GetVisionClient().BatchAnnotateImages(ctx, req)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+" BatchAnnotateImages err: %v", err)
		return nil, err
	}

	return resp, nil
}

func CensorTextByGcp(logCtx context.Context, text string) (*languagepb.ModerateTextResponse, error) {
	// 创建 Google Cloud Natural Language 客户端
	// 创建上下文
	ctx := context.Background()

	// 创建 Document 对象
	document := &languagepb.Document{
		Source: &languagepb.Document_Content{
			Content: text,
		},
		Type: languagepb.Document_PLAIN_TEXT,
	}

	// 调用文本审查 API
	resp, err := GetLanguageClient().ModerateText(ctx, &languagepb.ModerateTextRequest{
		Document: document,
	})
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+" ModerateText err: %v", err)
	}

	return resp, nil

}

func CensorVideoByGcp(logCtx context.Context, url string) (*videopb.AnnotateVideoResponse, error) {
	ctx := context.Background()
	uri, err := convertPublicURLToGCSURI(url)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+" convertPublicURLToGCSURI err: %v", err)
		return nil, err
	}
	// 调用 Video Intelligence API
	op, err := GetVideoClient().AnnotateVideo(ctx, &videopb.AnnotateVideoRequest{
		Features: []videopb.Feature{
			videopb.Feature_EXPLICIT_CONTENT_DETECTION,
		},
		InputUri: uri,
	})

	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+" AnnotateVideo err: %v", err)
		return nil, err
	}
	resp, err := op.Wait(ctx)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+" op.Wait err: %v", err)
		return nil, err
	}
	logger.Log.Infof(utils.MMark(logCtx)+" Video annotation complete resp: %v", resp.String())
	return resp, nil
}

func TranscribeLocalFile(logCtx context.Context, url string, encoding speechpb.RecognitionConfig_AudioEncoding, sampleRateHertz int32) (string, error) {
	// 创建客户端
	ctx := context.Background()
	uri, err := convertPublicURLToGCSURI(url)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+" convertPublicURLToGCSURI err: %v", err)
		return "", err
	}
	// 设置识别请求配置
	req := &speechpb.LongRunningRecognizeRequest{
		Config: &speechpb.RecognitionConfig{
			Encoding:                 encoding,        // 音频编码格式
			SampleRateHertz:          sampleRateHertz, // 音频采样率
			LanguageCode:             "en-US",         // 语言代码
			AlternativeLanguageCodes: []string{"cmn-Hans-CN", "es-ES", "id-ID", "ar-AE"},
		},
		Audio: &speechpb.RecognitionAudio{
			AudioSource: &speechpb.RecognitionAudio_Uri{
				Uri: uri,
			},
		},
	}

	// 发起异步识别请求
	op, err := GetSpeechClient().LongRunningRecognize(ctx, req)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+" LongRunningRecognize err: %v", err)
		return "", err
	}

	// 等待识别操作完成
	logger.Log.Infof(utils.MMark(logCtx) + " Waiting for operation to complete...")
	resp, err := op.Wait(ctx)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+" op.Wait err: %v", err)
		return "", err
	}

	text := ""
	// 处理识别结果
	if len(resp.Results) > 0 {
		for _, result := range resp.Results {
			for _, alt := range result.Alternatives {
				// 打印识别的文本
				logger.Log.Infof(utils.MMark(logCtx)+" Transcript: %s\n", alt.Transcript)
				text += alt.Transcript
			}
		}
	} else {
		logger.Log.Infof(utils.MMark(logCtx) + " No speech was detected in the audio.")
	}

	return text, nil
}

// ConvertPublicURLToGCSURI 将 Google Cloud Storage 的公开 URL 转换为 gs:// 形式
func convertPublicURLToGCSURI(publicURL string) (string, error) {
	parsedURL, err := url.Parse(publicURL)
	if err != nil {
		return "", fmt.Errorf("解析 URL 失败: %v", err)
	}

	if parsedURL.Host != "storage.googleapis.com" {
		return "", fmt.Errorf("不是合法的 GCS 公共 URL")
	}

	// 第一个 path segment 是 bucket
	pathSegments := strings.Split(strings.TrimPrefix(parsedURL.Path, "/"), "/")
	if len(pathSegments) < 2 {
		return "", fmt.Errorf("URL 路径不合法，找不到 bucket 或 object")
	}

	bucket := pathSegments[0]
	objectPath := strings.Join(pathSegments[1:], "/")

	return fmt.Sprintf("gs://%s/%s", bucket, objectPath), nil
}
