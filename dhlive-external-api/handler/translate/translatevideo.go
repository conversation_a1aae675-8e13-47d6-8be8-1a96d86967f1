package translate

import (
	"acg-ai-go-common/logger"
	"acg-ai-go-common/utils"
	"acg-ai-go-common/utils/redisproxy"
	"bytes"
	"dhlive-external-api/handler/handlerUtils/redislock"
	"dhlive-external-api/handler/handlerUtils/retryhttpclient"
	"dhlive-external-api/handler/i18n/respi18n"
	"encoding/json"
	"errors"
	"sort"
	"strconv"
	"strings"

	"context"
	"dhlive-external-api/beans/model"
	"dhlive-external-api/beans/proto"
	config "dhlive-external-api/conf"
	"dhlive-external-api/handler/handlerUtils"

	"dhlive-external-api/mysqlclient"

	"fmt"
	"net/http"

	"time"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

const (
	SlideVidePrefix                = "sd-"
	CoursewarePrefix               = "cd-"
	thymeLeafTranslateMaxNumKeyFmt = "%v:" + "thymeLeafTranslateVideoMaxNum"

	videoTransInvokeThymeleaf        = "VideoTransInvokeThymeleaf"
	videoTransInvokeThymeleafFmt     = "%s:" + videoTransInvokeThymeleaf
	videoTransInvokeThymeleafLockExp = 600 * time.Second

	videoTransOutTime        = "videoTransOutTime"
	videoTransOutTimFmt      = "%s:" + videoTransOutTime
	videoTransOutTimeLockExp = 600 * time.Second
)

type TempDraftEntity struct {
	ID             int64     `json:"id"`
	DraftID        string    `json:"draftId"`
	TransTimbre    string    `json:"timbre"` // 视频翻译的目标音色信息
	TranslateCount int       `json:"translateCount"`
	Tracks         string    `json:"tracks"`
	TranslateLan   string    `json:"lan"` // 翻译语言代码
	CreateTime     time.Time `json:"createTime"`
}

// 视频翻译接口
func TranslateVideo(c *gin.Context) {
	reqId := uuid.New().String()
	logCtx := context.WithValue(context.TODO(), utils.CtxKeyLogID, reqId)
	accountID := "text"
	aid, ok := c.Get("AccountId")
	if ok {
		accountID = aid.(string)
	}
	logger.Log.Infof(utils.MMark(logCtx)+"TranslateVideo start accountID:%s", accountID)
	startTime := time.Now()

	targetLanguage := c.GetHeader("Language")
	logCtx = context.WithValue(logCtx, "Language", targetLanguage)

	// 解析请求参数
	req := proto.TranslateVideoRequest{}
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"TranslateVideo ShouldBindQuery fail, err:%v req:%#v", err, req)
		c.JSON(http.StatusOK, proto.NewCommDataRsp(100001, respi18n.TransformResponseLocaleByDefault(logCtx, targetLanguage, "参数错误"), nil))
		return
	}

	logger.Log.Infof(utils.MMark(logCtx)+"TranslateVideo accountID:%s , reqBody:%#v", accountID, req)
	if len(req.DraftId) == 0 || len(req.TransList) == 0 {
		logger.Log.Errorf(utils.MMark(logCtx)+"TranslateVideo parameter error, DraftId:%s TransList:%#v", req.DraftId, req.TransList)
		c.JSON(http.StatusOK, proto.NewCommDataRsp(100001, respi18n.TransformResponseLocaleByDefault(logCtx, targetLanguage, "参数错误"), nil))
		return
	}
	logCtx = context.WithValue(context.TODO(), utils.CtxKeyLogID, req.DraftId)
	draftId := req.DraftId
	logger.Log.Infof(utils.MMark(logCtx)+"TranslateVideo start reqBody:%+v", req)

	for idx, transItem := range req.TransList {
		// 将百度翻译的language code 转换为谷歌翻译的language code
		googleToLan := LanCodeMapBaiduToGoogle[transItem.Lan]
		if googleToLan == "" {
			googleToLan = transItem.Lan
		}

		// 调用草稿copy接口
		newDraftId, err := invokeCopyDraft(c, logCtx, googleToLan, draftId)
		if len(newDraftId) == 0 || err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+"TranslateVideo invokeCopyDraft error idx:%d, draftId:%s err:%+v", idx, draftId, err)
			c.JSON(http.StatusOK, proto.NewCommDataRsp(100002, respi18n.TransformResponseLocaleByDefault(logCtx, targetLanguage, "invokeCopyDraft error"), nil))
			return
		}

		// 精编视频
		if strings.HasPrefix(newDraftId, SlideVidePrefix) {
			err = (&model.SlideDraftEntity{}).UpdateFieldsByDraftID(mysqlclient.DbMap[mysqlclient.MetaHumanEditorSaasDBName], newDraftId,
				map[string]interface{}{
					"translate_timbre": transItem.Timbre,
					"translate_lan":    googleToLan,
					"translate_status": proto.TransStatusWaiting,
					"translate_count":  0,
				})
			if err != nil {
				logger.Log.Errorf(utils.MMark(logCtx)+"SlideDraftEntity update TransStatusWaiting error newDraftId:%s, errorMsg: %+v\n", newDraftId, err)
				c.JSON(http.StatusOK, proto.NewCommDataRsp(100003, respi18n.TransformResponseLocaleByDefault(logCtx, targetLanguage, "update draft trans status ot waiting error"), nil))
				return
			} else {
				logger.Log.Infof("SlideDraftEntity  update TransStatusWaiting success draftId:%s", newDraftId)

			}
			// 课程视频
		} else if strings.HasPrefix(draftId, CoursewarePrefix) {
			err = (&model.CoursewareDraftEntity{}).UpdateFieldsByDraftID(mysqlclient.DbMap[mysqlclient.MetaHumanEditorSaasDBName], newDraftId,
				map[string]interface{}{
					"translate_timbre": transItem.Timbre,
					"translate_lan":    googleToLan,
					"translate_status": proto.TransStatusWaiting,
					"translate_count":  0,
				})
			if err != nil {
				logger.Log.Errorf(utils.MMark(logCtx)+"CoursewareDraftEntity update TransStatusWaiting error newDraftId:%s, errorMsg: %+v\n", newDraftId, err)
				c.JSON(http.StatusOK, proto.NewCommDataRsp(100003, respi18n.TransformResponseLocaleByDefault(logCtx, targetLanguage, "update draft trans status to waiting error"), nil))
				return
			} else {
				logger.Log.Infof("CoursewareDraftEntity  update TransStatusWaiting success draftId:%s", newDraftId)
			}
		} else {
			logger.Log.Errorf("CopyNewDraftId is error,prefix is not cd or od, newDraftId: %s", newDraftId)
			c.JSON(http.StatusOK, proto.NewCommDataRsp(100003, respi18n.TransformResponseLocaleByDefault(logCtx, targetLanguage, "CopyNewDraftId is error newDraftId:"+newDraftId), nil))
			return
		}
	}
	elapsed := time.Since(startTime)
	logger.Log.Infof(utils.MMark(logCtx)+"TranslateVideo request success,cost:%v", elapsed)
	c.JSON(http.StatusOK, proto.NewSuccessRsp(""))
}

// 视频翻译失败-翻译重试
func TranslateVideoRetry(c *gin.Context) {
	reqId := uuid.New().String()
	logCtx := context.WithValue(context.TODO(), utils.CtxKeyLogID, reqId)
	accountID := "text"
	aid, ok := c.Get("AccountId")
	if ok {
		accountID = aid.(string)
	}
	logger.Log.Infof(utils.MMark(logCtx)+"TranslateVideoRetry start accountID:%s", accountID)
	startTime := time.Now()

	targetLanguage := c.GetHeader("Language")
	logCtx = context.WithValue(logCtx, "Language", targetLanguage)

	// 解析请求参数
	req := proto.TranslateVideoRetryRequest{}
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"TranslateVideoRetry ShouldBindQuery fail, err:%v req:%#v", err, req)
		c.JSON(http.StatusOK, proto.NewCommDataRsp(100001, respi18n.TransformResponseLocaleByDefault(logCtx, targetLanguage, "参数错误"), nil))
		return
	}

	logger.Log.Infof(utils.MMark(logCtx)+"TranslateVideoRetry accountID:%s , reqBody:%#v", accountID, req)
	if len(req.DraftId) == 0 {
		logger.Log.Errorf(utils.MMark(logCtx)+"TranslateVideoRetry parameter error, DraftId:%s", req.DraftId)
		c.JSON(http.StatusOK, proto.NewCommDataRsp(100001, respi18n.TransformResponseLocaleByDefault(logCtx, targetLanguage, "参数错误"), nil))
		return
	}
	logCtx = context.WithValue(context.TODO(), utils.CtxKeyLogID, req.DraftId)
	draftId := req.DraftId
	logger.Log.Infof(utils.MMark(logCtx)+"TranslateVideoRetry start reqBody:%+v", req)

	// 精编视频-翻译重试，把状态置为-待翻译，翻译次数为0
	if strings.HasPrefix(draftId, SlideVidePrefix) {
		err := (&model.SlideDraftEntity{}).UpdateFieldsByDraftID(mysqlclient.DbMap[mysqlclient.MetaHumanEditorSaasDBName], draftId,
			map[string]interface{}{
				"translate_status": proto.TransStatusWaiting,
				"translate_count":  0,
			})
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+"TranslateVideoRetry update slide TransStatusWaiting error newDraftId:%s, errorMsg: %+v\n", draftId, err)
			c.JSON(http.StatusOK, proto.NewCommDataRsp(100003, respi18n.TransformResponseLocaleByDefault(logCtx, targetLanguage, "update draft trans status to waiting error,"+err.Error()), nil))
			return
		} else {
			logger.Log.Infof("TranslateVideoRetry  update slide TransStatusWaiting success draftId:%s", draftId)
		}
		// 课程视频-翻译重试，把状态置为-待翻译，翻译次数为0
	} else if strings.HasPrefix(draftId, CoursewarePrefix) {
		err := (&model.CoursewareDraftEntity{}).UpdateFieldsByDraftID(mysqlclient.DbMap[mysqlclient.MetaHumanEditorSaasDBName], draftId,
			map[string]interface{}{
				"translate_status": proto.TransStatusWaiting,
				"translate_count":  0,
			})
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+"CoursewareDraftEntity update TransStatusWaiting error newDraftId:%s, errorMsg: %+v\n", draftId, err)
			c.JSON(http.StatusOK, proto.NewCommDataRsp(100003, respi18n.TransformResponseLocaleByDefault(logCtx, targetLanguage, "update draft trans status to waiting error,"+err.Error()), nil))
			return
		} else {
			logger.Log.Infof("TranslateVideoRetry course update TransStatusWaiting success draftId:%s", draftId)
		}
	} else {
		logger.Log.Errorf("TranslateVideoRetry draftId is error,prefix is not cd or od, newDraftId: %s", draftId)
		c.JSON(http.StatusOK, proto.NewCommDataRsp(100003, respi18n.TransformResponseLocaleByDefault(logCtx, targetLanguage, "draftId is error draftId:"+draftId), nil))
		return
	}

	elapsed := time.Since(startTime)
	logger.Log.Infof(utils.MMark(logCtx)+"TranslateVideo request success,cost:%v", elapsed)
	c.JSON(http.StatusOK, proto.NewSuccessRsp(""))
}

// 定时任务读取待翻译的任务
func TranslateVideoTaskForWait() {
	// 先从redis中获取maxNum，如果redis中没有，则取配置值
	transMaxNumKey := fmt.Sprintf(thymeLeafTranslateMaxNumKeyFmt, handlerUtils.GetNameByRunEnv())
	maxNumStr, err := redisproxy.GetRedisProxy().GetKeyValue(transMaxNumKey)
	logger.Log.Infof("TranslateVideoTaskForWait start transMaxNumKey:%s, maxNumStr:%s, err:%+v", transMaxNumKey, maxNumStr, err)
	maxNum := 0
	if err != nil {
		tempMaxNum, err := strconv.Atoi(maxNumStr)
		if err != nil && tempMaxNum > 0 {
			maxNum = tempMaxNum
		}
	}
	if maxNum == 0 {
		maxNum = config.LocalConfig.VideoTranslate.TransMaxNum
	}
	logger.Log.Infof("TranslateVideoTaskForWait final maxNum:%d", maxNum)

	// 获取 redis 分布式锁
	redisproxy := redisproxy.GetRedisProxy()
	redisLockKey := fmt.Sprintf(videoTransInvokeThymeleafFmt, handlerUtils.GetNameByRunEnv())
	redisLock := redislock.NewRedisLock(redisproxy.Rdb, redisLockKey, videoTransInvokeThymeleafLockExp)
	ok, err := redisLock.Lock(context.Background())
	if err != nil {
		logger.Log.Errorf("TranslateVideoTaskForWait redisLock.Lock error key: %s error: %+v\n", redisLockKey, err)
		return
	} else if !ok {
		logger.Log.Infof("TranslateVideoTaskForWait not acquire redisLock.Lock, key: %s", redisLockKey)
		return
	}
	logger.Log.Infof("TranslateVideoTaskForWait acquire redisLock.Lock, key: %s", redisLockKey)

	defer func() {
		err = redisLock.Unlock(context.Background())
		if err != nil {
			logger.Log.Errorf("TranslateVideoTaskForWait redisLock.Unlock error: %+v, key: %s", err, redisLockKey)
		} else {
			logger.Log.Infof("TranslateVideoTaskForWait redisLock.Unlock success key: %s", redisLockKey)
		}
	}()

	coursewareEntity := &model.CoursewareDraftEntity{}
	coursewareCurrentNum, err := coursewareEntity.GetTranslateRunningCount(mysqlclient.DbMap[mysqlclient.MetaHumanEditorSaasDBName])
	if err != nil {
		logger.Log.Errorf("TranslateVideo coursewareEntity.GetTranslateRunningCount err:%+v", err)
		return
	}
	slideEntity := &model.SlideDraftEntity{}
	slideEntityCurrentNum, err := slideEntity.GetTranslateRunningCount(mysqlclient.DbMap[mysqlclient.MetaHumanEditorSaasDBName])
	if err != nil {
		logger.Log.Errorf("TranslateVideo slideEntity.GetTranslateRunningCount err:%+v", err)
		return
	}
	// 计算目前还可以执行的任务数
	canDoNum := int(int64(maxNum) - coursewareCurrentNum - slideEntityCurrentNum)
	logger.Log.Infof("TranslateVideo canDoNum:%d,coursewareCurrentNum:%d,slideEntityCurrentNum:%d", canDoNum, coursewareCurrentNum, slideEntityCurrentNum)

	if canDoNum > 0 {
		// 读取课程视频和精编视频中待翻译的任务
		coursewareModels, err := coursewareEntity.GetLimitedCountItemByStatus(mysqlclient.DbMap[mysqlclient.MetaHumanEditorSaasDBName], proto.TransStatusWaiting, canDoNum)

		if err != nil {
			logger.Log.Errorf("coursewareEntity.GetLimitedCountItemByStatus error: %+v", err)
			return
		}
		slideEntityModels, err := slideEntity.GetLimitedCountItemByStatus(mysqlclient.DbMap[mysqlclient.MetaHumanEditorSaasDBName], proto.TransStatusWaiting, canDoNum)
		if err != nil {
			logger.Log.Errorf("slideEntity.GetLimitedCountItemByStatus error: %+v", err)
			return
		}

		tempDrafts := make([]TempDraftEntity, 0)
		for _, v := range coursewareModels {
			tempDrafts = append(tempDrafts, TempDraftEntity{
				ID:             v.ID,
				DraftID:        v.DraftID,
				TranslateCount: v.TranslateCount,
				Tracks:         v.Tracks,
				TransTimbre:    v.TransTimbre,
				TranslateLan:   v.TranslateLan,
			})
		}
		for _, v := range slideEntityModels {
			tempDrafts = append(tempDrafts, TempDraftEntity{
				ID:             v.ID,
				DraftID:        v.DraftID,
				TranslateCount: v.TranslateCount,
				Tracks:         v.Tracks,
				TransTimbre:    v.TransTimbre,
				TranslateLan:   v.TranslateLan,
			})
		}
		logger.Log.Infof("TranslateVideo total tempDrafts.size:%d", len(tempDrafts))
		if len(tempDrafts) > 0 {
			sort.Slice(tempDrafts, func(i, j int) bool {
				return tempDrafts[i].CreateTime.Before(tempDrafts[j].CreateTime)
			})
			// 只取前canDoNum个任务执行
			if len(tempDrafts) > canDoNum {
				tempDrafts = tempDrafts[:canDoNum]
			}
			logger.Log.Infof("TranslateVideo dispatch tempDrafts.size:%d, tempDrafts:%+v", len(tempDrafts), tempDrafts)

			for _, tempDraft := range tempDrafts {
				draft := tempDraft // 创建局部变量副本
				go func() {
					// 调用前端的翻译接口
					_, err := invokeThymeleafTranslate(draft)
					if err != nil {
						logger.Log.Errorf("invokeThymeleafTranslate error draftId:%s, errorMsg: %+v\n", draft.DraftID, err)
					} else {
						logger.Log.Infof("invokeThymeleafTranslate success draftId:%s", draft.DraftID)
					}
				}()
				// 更新数据库，状态为翻译中，翻译次数+1
				if strings.HasPrefix(draft.DraftID, SlideVidePrefix) {
					err = (&model.SlideDraftEntity{}).UpdateFieldsByDraftID(mysqlclient.DbMap[mysqlclient.MetaHumanEditorSaasDBName], draft.DraftID,
						map[string]interface{}{
							"translate_status": proto.TransStatusRunning,
							"translate_count":  draft.TranslateCount + 1,
						})
					if err != nil {
						logger.Log.Errorf("SlideDraftEntity update TransStatus and TransCount error draftId:%s, errorMsg: %+v\n", draft.DraftID, err)
					} else {
						logger.Log.Infof("SlideDraftEntity  update TransStatus and TransCount success draftId:%s", draft.DraftID)

					}
				} else if strings.HasPrefix(draft.DraftID, CoursewarePrefix) {
					err = (&model.CoursewareDraftEntity{}).UpdateFieldsByDraftID(mysqlclient.DbMap[mysqlclient.MetaHumanEditorSaasDBName], draft.DraftID,
						map[string]interface{}{
							"translate_status": proto.TransStatusRunning,
							"translate_count":  draft.TranslateCount + 1,
						})
					if err != nil {
						logger.Log.Errorf("CoursewareDraftEntity update TransStatus and TransCount error draftId:%s, errorMsg: %+v\n", draft.DraftID, err)
					} else {
						logger.Log.Infof("CoursewareDraftEntity  update TransStatus and TransCount success draftId:%s", draft.DraftID)
					}
				}
			}
		}
		logger.Log.Infof("TranslateVideo after sort tempDrafts.size:%d tasks will be dispatched", len(tempDrafts))
	}
}

// 调用前端的mhe-thymeleaf翻译接口
func invokeThymeleafTranslate(tempDraft TempDraftEntity) (*proto.CommDataRsp, error) {
	url := config.LocalConfig.VideoTranslate.ThymeleafTransHost + config.LocalConfig.VideoTranslate.ThymeleafTransPath
	logCtx := context.WithValue(context.TODO(), utils.CtxKeyLogID, tempDraft.DraftID)
	logger.Log.Infof(utils.MMark(logCtx)+"InvokeThymeleafTranslate url: %s, request: %+v\n", url, tempDraft)
	// 序列化请求数据
	jsonData, err := json.Marshal(tempDraft)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"InvokeThymeleafTranslate Marshal request failed: %+v\n", err)
		return nil, err
	}
	beginTime := time.Now()
	// 创建请求对象
	retclient := retryhttpclient.NewRetryHTTPClient(120*time.Second, 3)
	header := make(map[string]string)
	header["Content-Type"] = "application/json"

	resp, err := retclient.DoRequest(logCtx, "POST", url, header, bytes.NewBuffer(jsonData))
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+utils.ServiceInternalEsErrMsg+",InvokeThymeleafTranslate request failed: %+v\n", err)
		return nil, err
	}
	responseModel := &proto.CommDataRsp{}
	err = json.Unmarshal(resp, responseModel)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"InvokeThymeleafTranslate resp json.Unmarshal error: %v", err)
		return nil, err
	}

	costTime := time.Since(beginTime)
	logger.Log.Infof(utils.MMark(logCtx)+"InvokeThymeleafTranslate success costIime: %+v, responseModel: %s\n", costTime, resp)
	return responseModel, nil
}

// 调用slide的草稿复制接口
func invokeCopyDraft(c *gin.Context, logCtx context.Context, lanCode string, draftId string) (string, error) {
	urlPath := ""
	beginTime := time.Now()
	// 精编视频
	if strings.HasPrefix(draftId, SlideVidePrefix) {
		urlPath = config.LocalConfig.VideoTranslate.SlideDraftCopyPath
	} else if strings.HasPrefix(draftId, CoursewarePrefix) {
		urlPath = config.LocalConfig.VideoTranslate.CoursewareDraftCopyPath
	} else {
		logger.Log.Errorf(utils.MMark(logCtx)+"invokeCopyDraft error draftId is error, draftId:%s err:%+v", draftId)
		return "", errors.New("DraftId is error")
	}
	url := config.LocalConfig.VideoTranslate.DraftCopyHost + urlPath

	reqModel := proto.CopyDraftRequest{
		Name:     draftId, // 该接口不需要该字段，但有校验所以传id
		DraftId:  draftId,
		Language: lanCode,
	}
	logger.Log.Infof(utils.MMark(logCtx)+"invokeCopyDraft url: %s, reqModel: %+v\n", url, reqModel)

	// 序列化请求数据
	jsonData, err := json.Marshal(reqModel)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"invokeCopyDraft Marshal request failed: %+v\n", err)
		return "", err
	}
	// 创建请求对象
	retclient := retryhttpclient.NewRetryHTTPClient(120*time.Second, 3)
	header := make(map[string]string)
	// 透传所有请求头
	for key := range c.Request.Header {
		header[key] = c.Request.Header.Get(key)
	}
	header["Content-Type"] = "application/json"
	resp, err := retclient.DoRequest(logCtx, "POST", url, header, bytes.NewBuffer(jsonData))
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+utils.ServiceInternalEsErrMsg+",invokeCopyDraft request failed: %+v\n", err)
		return "", err
	}
	responseModel := &proto.CopyDraftResponse{}
	err = json.Unmarshal(resp, responseModel)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"invokeCopyDraft resp json.Unmarshal error: %v", err)
		return "", err
	}

	costTime := time.Since(beginTime)
	if responseModel.Code == 0 && len(responseModel.ResultModel.DraftId) > 0 {
		logger.Log.Infof(utils.MMark(logCtx)+"invokeCopyDraft  success costIime: %+v, responseModel: %s\n", costTime, resp)
		return responseModel.ResultModel.DraftId, nil
	} else {
		logger.Log.Infof(utils.MMark(logCtx)+"invokeCopyDraft error costIime: %+v, responseModel: %s\n", costTime, resp)
		return "", fmt.Errorf("invokeCopyDraft error:%+v", responseModel)
	}
}

// 视频翻译回调接口
func TranslateVideoCallback(c *gin.Context) {
	reqId := uuid.New().String()
	logCtx := context.WithValue(context.TODO(), utils.CtxKeyLogID, reqId)
	startTime := time.Now()

	// 解析请求参数
	req := proto.TranslateVideoCallbackRequest{}
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"TranslateVideoCallback ShouldBindQuery fail, err:%v req:%#v", err, req)
		c.JSON(http.StatusOK, proto.NewCommDataRsp(100001, "param ShouldBindJSON error", nil))
		return
	}

	logger.Log.Infof(utils.MMark(logCtx)+"TranslateVideoCallback reqBody:%#v", req)
	if len(req.ResultModel.DraftId) == 0 || len(req.ResultModel.Tracks) == 0 {
		logger.Log.Errorf(utils.MMark(logCtx) + "TranslateVideoCallback parameter error DraftId or Tracks len is 0")
		c.JSON(http.StatusOK, proto.NewCommDataRsp(100001, "param error DraftId or Tracks len is 0", nil))
		return
	}
	logCtx = context.WithValue(context.TODO(), utils.CtxKeyLogID, req.ResultModel.DraftId)
	draftId := req.ResultModel.DraftId
	logger.Log.Infof(utils.MMark(logCtx)+"TranslateVideoCallback start reqBody:%+v,draftId:%s", req, draftId)

	if req.Code == 0 {
		if strings.HasPrefix(draftId, SlideVidePrefix) {
			// 精编视频-翻译成功
			err := (&model.SlideDraftEntity{}).UpdateFieldsByDraftID(mysqlclient.DbMap[mysqlclient.MetaHumanEditorSaasDBName], draftId,
				map[string]interface{}{
					"translate_status": proto.TransStatusSuccess,
					"tracks":           req.ResultModel.Tracks,
				})
			if err != nil {
				logger.Log.Errorf(utils.MMark(logCtx)+"TranslateVideoCallback UpdateItemByDraftID error DraftId:%s err:%+v", draftId, err)
				c.JSON(http.StatusOK, proto.NewCommDataRsp(100002, "update tracks to database error:"+err.Error(), nil))
				return
			}
			logger.Log.Infoln(utils.MMark(logCtx) + "TranslateVideoCallback translate success")

		} else if strings.HasPrefix(draftId, CoursewarePrefix) {
			// 课程视频-翻译成功
			err := (&model.CoursewareDraftEntity{}).UpdateFieldsByDraftID(mysqlclient.DbMap[mysqlclient.MetaHumanEditorSaasDBName], draftId,
				map[string]interface{}{
					"translate_status": proto.TransStatusSuccess,
					"tracks":           req.ResultModel.Tracks,
				})
			if err != nil {
				logger.Log.Errorf(utils.MMark(logCtx)+"TranslateVideoCallback UpdateItemByDraftID error DraftId:%s err:%+v", draftId, err)
				c.JSON(http.StatusOK, proto.NewCommDataRsp(100002, "update tracks to database error:"+err.Error(), nil))
				return
			}
			logger.Log.Infof(utils.MMark(logCtx) + "TranslateVideoCallback translate success")
		}
	} else {
		if strings.HasPrefix(draftId, SlideVidePrefix) {
			// 精编视频-翻译失败
			slideEntity, err := (&model.SlideDraftEntity{}).GetItemByDraftID(mysqlclient.DbMap[mysqlclient.MetaHumanEditorSaasDBName], draftId)
			if err != nil {
				logger.Log.Errorf(utils.MMark(logCtx)+"TranslateVideoCallback GetItemByDraftID error DraftId:%s err:%+v", draftId, err)
				c.JSON(http.StatusOK, proto.NewCommDataRsp(100002, "get draft from database error:"+err.Error(), nil))
				return
			}

			transStatus := proto.TransStatusWaiting
			failedReason := ""
			// 如果已翻译次数大于设置的最大重试次数，则状态设置为翻译失败
			if slideEntity.TranslateCount > config.LocalConfig.VideoTranslate.MaxRetryCount {
				transStatus = proto.TransStatusFailed
				failedReason = strconv.Itoa(req.Code) + "," + req.Message.Global
			}

			err = (&model.SlideDraftEntity{}).UpdateFieldsByDraftID(mysqlclient.DbMap[mysqlclient.MetaHumanEditorSaasDBName], draftId,
				map[string]interface{}{
					"translate_status":        transStatus,
					"translate_failed_reason": failedReason,
				})
			if err != nil {
				logger.Log.Errorf(utils.MMark(logCtx)+"TranslateVideoCallback UpdateItemByDraftID error DraftId:%s err:%+v", draftId, err)
				c.JSON(http.StatusOK, proto.NewCommDataRsp(100002, "update tracks to database error:"+err.Error(), nil))
				return
			}
			logger.Log.Errorf(utils.MMark(logCtx) + "TranslateVideoCallback translate failed update draftModel")

		} else if strings.HasPrefix(draftId, CoursewarePrefix) {
			// 课程视频-翻译失败
			coursewareEntity, err := (&model.CoursewareDraftEntity{}).GetItemByDraftID(mysqlclient.DbMap[mysqlclient.MetaHumanEditorSaasDBName], draftId)
			if err != nil {
				logger.Log.Errorf(utils.MMark(logCtx)+"TranslateVideoCallback GetItemByDraftID error DraftId:%s err:%+v", draftId, err)
				c.JSON(http.StatusOK, proto.NewCommDataRsp(100002, "get draft from database error:"+err.Error(), nil))
				return
			}

			transStatus := proto.TransStatusWaiting
			failedReason := ""
			// 如果已翻译次数大于设置的最大重试次数，则状态设置为翻译失败
			if coursewareEntity.TranslateCount > config.LocalConfig.VideoTranslate.MaxRetryCount {
				transStatus = proto.TransStatusFailed
				failedReason = strconv.Itoa(req.Code) + "," + req.Message.Global
			}
			err = (&model.CoursewareDraftEntity{}).UpdateFieldsByDraftID(mysqlclient.DbMap[mysqlclient.MetaHumanEditorSaasDBName], draftId,
				map[string]interface{}{
					"translate_status":        transStatus,
					"translate_failed_reason": failedReason,
				})
			if err != nil {
				logger.Log.Errorf(utils.MMark(logCtx)+"TranslateVideoCallback UpdateItemByDraftID error DraftId:%s err:%+v", draftId, err)
				c.JSON(http.StatusOK, proto.NewCommDataRsp(100002, "update tracks to database error:"+err.Error(), nil))
				return
			}
			logger.Log.Errorf(utils.MMark(logCtx) + "TranslateVideoCallback  translate failed update draftModel")
		}
	}
	elapsed := time.Since(startTime)
	logger.Log.Infof(utils.MMark(logCtx)+"TranslateVideoCallback response success,cost:%v", elapsed)
	c.JSON(http.StatusOK, proto.NewSuccessRsp("success"))
}

// 定时任务读超时的翻译任务 修改状态重新调度
func TranslateVideoTaskForOuttime() {
	// 获取 redis 分布式锁
	redisproxy := redisproxy.GetRedisProxy()
	redisLockKey := fmt.Sprintf(videoTransOutTimFmt, handlerUtils.GetNameByRunEnv())
	redisLock := redislock.NewRedisLock(redisproxy.Rdb, redisLockKey, videoTransOutTimeLockExp)
	logger.Log.Infof("TranslateVideoTaskForOuttime start startredisLockKey:%s", redisLockKey)

	ok, err := redisLock.Lock(context.Background())
	if err != nil {
		logger.Log.Errorf("TranslateVideoTaskForOuttime redisLock.Lock error key: %s error: %+v\n", redisLockKey, err)
		return
	} else if !ok {
		logger.Log.Infof("TranslateVideoTaskForOuttime not acquire redisLock.Lock, key: %s", redisLockKey)
		return
	}
	logger.Log.Infof("TranslateVideoTaskForOuttime acquire redisLock.Lock, key: %s", redisLockKey)

	defer func() {
		err = redisLock.Unlock(context.Background())
		if err != nil {
			logger.Log.Errorf("TranslateVideoTaskForOuttime redisLock.Unlock error: %+v,key: %s", err, redisLockKey)
		} else {
			logger.Log.Infof("TranslateVideoTaskForOuttime redisLock.Unlock success key: %s", redisLockKey)
		}
	}()

	// 读取课程视频和精编视频中的超时任务，并修改状态
	updateCoursewareDrafts, err := (&model.CoursewareDraftEntity{}).GetRunningAndOutTimeItemsForUpdate(mysqlclient.DbMap[mysqlclient.MetaHumanEditorSaasDBName])
	if err != nil {
		logger.Log.Errorf("TranslateVideoTaskForOuttime getAndUpdate outTime CoursewareDraft error: %+v", err)
	}
	logger.Log.Infof("TranslateVideoTaskForOuttime getAndUpdate outTime CoursewareDraft success size: %d", len(updateCoursewareDrafts))

	updateSlideDrafts, err := (&model.SlideDraftEntity{}).GetRunningAndOutTimeItemsForUpdate(mysqlclient.DbMap[mysqlclient.MetaHumanEditorSaasDBName])
	if err != nil {
		logger.Log.Errorf("TranslateVideoTaskForOuttime getAndUpdate outTime SlideDraft error: %+v", err)
	}
	logger.Log.Infof("TranslateVideoTaskForOuttime getAndUpdate outTime SlideDraft success size: %d", len(updateSlideDrafts))
}
