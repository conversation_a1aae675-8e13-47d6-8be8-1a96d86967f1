package translate

import (
	"acg-ai-go-common/gomysql"
	"acg-ai-go-common/logger"
	"acg-ai-go-common/utils"
	"strings"

	// "bytes"
	"context"
	"dhlive-external-api/beans/model"
	"dhlive-external-api/beans/proto"
	config "dhlive-external-api/conf"
	"dhlive-external-api/handler/handlerUtils"
	"dhlive-external-api/handler/handlerUtils/ratelimiter"
	"dhlive-external-api/handler/handlerUtils/retryhttpclient"
	"encoding/json"
	"fmt"
	"net/http"
	"net/url"
	"strconv"
	"sync"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

const (
	TranslateTextQps       = "translateTextQps"
	TranslateTextQpsQpsFmt = "%s:" + TranslateTextQps + ":xiling-saas-v3"
)

func TranslateText(c *gin.Context) {
	reqId := uuid.New().String()
	logCtx := context.WithValue(context.TODO(), utils.CtxKeyLogID, reqId)
	accountID := "text"
	aid, ok := c.Get("AccountId")
	if ok {
		accountID = aid.(string)
	}
	logger.Log.Infof(utils.MMark(logCtx)+"TranslateText start accountID:%s", accountID)
	startTime := time.Now()

	// 解析请求参数
	req := proto.TranslateTextRequest{}
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"TranslateText ShouldBindQuery fail, err:%v req:%#v", err, req)
		c.JSON(http.StatusOK, proto.NewCommDataRsp(100001, "参数错误", nil))
		return
	}

	if len(req.TextList) == 0 {
		logger.Log.Errorf(utils.MMark(logCtx) + "TranslateText req.TextList is empty")
		c.JSON(http.StatusOK, proto.NewCommDataRsp(100002, "参数错误,文本列表为空", nil))
		return
	}

	if len(req.TextList) > 30 {
		logger.Log.Errorf(utils.MMark(logCtx) + "TranslateText req.TextList is to much")
		c.JSON(http.StatusOK, proto.NewCommDataRsp(100002, "参数错误,文本数量超过限制", nil))
		return
	}
	logger.Log.Infof(utils.MMark(logCtx)+"TranslateText accountID:%s , reqBody:%#v", accountID, req)

	var wg sync.WaitGroup
	var rwMutex sync.RWMutex
	apiReslutMap := make(map[int]proto.TransResultModel)
	for seqID, text := range req.TextList {
		// 如果是空字符串直接返回空
		if len(strings.TrimSpace(text)) == 0 {
			rwMutex.Lock()
			apiReslutMap[seqID] = proto.TransResultModel{
				TransID:   "",
				Src:       "",
				Dst:       "",
				From:      "",
				To:        "",
				ErrorCode: "0",
				ErrorMsg:  "",
			}
			logger.Log.Infof(utils.MMark(logCtx)+"TranslateText text is empty continue,seqID:%d", seqID)
			rwMutex.Unlock()
			continue
		}

		// 先从数据库中查询是否已经翻译过
		md5Str := handlerUtils.CalculateMD5FromText(text + req.From + req.To)
		logger.Log.Infof(utils.MMark(logCtx)+"TranslateText text:%s , md5:%s", text, md5Str)
		translateTextItem, err := (&model.TranslateTextItem{}).GetItemByMd5(gomysql.DB, md5Str)
		if err == nil && translateTextItem != nil {
			rwMutex.Lock()
			apiReslutMap[seqID] = proto.TransResultModel{
				TransID:   translateTextItem.TransId,
				Src:       translateTextItem.SrcText,
				Dst:       translateTextItem.DstText,
				From:      translateTextItem.FromLan,
				To:        translateTextItem.ToLan,
				ErrorCode: "0",
				ErrorMsg:  "",
			}
			logger.Log.Infof(utils.MMark(logCtx)+"TranslateText use cache result,seqID:%d,md5:%s,text:%s", seqID, md5Str, text)
			rwMutex.Unlock()
			continue
		}

		// 申请qps限制
		for {
			allow, err := checkQpsAllowance(logCtx)
			if err != nil {
				logger.Log.Errorf(utils.MMark(logCtx)+"TranslateText checkQpsAllowance is err:%v", err)
				c.JSON(http.StatusOK, proto.NewCommDataRsp(100003, "QPS校验失败,请稍后再试", nil))
				return
			}
			if !allow {
				time.Sleep(500 * time.Millisecond)
				continue
			}
			break
		}

		wg.Add(1)
		seqIDCopy := seqID
		textCopy := text
		go func() {
			defer wg.Done()
			// 请求翻译api   logid后加上 id，方便定位问题
			logCtxApi := context.WithValue(context.TODO(), utils.CtxKeyLogID, reqId+"_"+strconv.Itoa(seqIDCopy))
			result, err := requestTransApi(logCtxApi, seqIDCopy, textCopy, req.From, req.To)
			if err == nil && result != nil {
				logger.Log.Infof(utils.MMark(logCtx)+"TranslateText requestTransApi result:%v,seqIDCopy:%v,err:%v", result, seqIDCopy, err)
				if result.ErrorCode == "0" && result.TransResult != nil && len(result.TransResult) > 0 {

					srcStr := ""
					dstStr := ""
					for _, v := range result.TransResult {
						logger.Log.Infof(utils.MMark(logCtx)+"TranslateText requestTransApi transResult:%v,seqIDCopy:%v", v, seqIDCopy)
						srcStr = srcStr + v.Src
						dstStr = dstStr + v.Dst
					}

					rwMutex.Lock()
					apiReslutMap[result.SeqID] = proto.TransResultModel{
						TransID:   reqId + strconv.Itoa(seqIDCopy),
						Src:       srcStr,
						Dst:       dstStr,
						From:      result.From,
						To:        result.To,
						ErrorCode: result.ErrorCode,
						ErrorMsg:  result.ErrorMsg,
					}
					rwMutex.Unlock()
					logger.Log.Infof(utils.MMark(logCtx)+"TranslateText requestTransApi result write to resultMap:%v,seqIDCopy:%v,apiReslutMap[result.SeqID]:%+v", result, seqIDCopy, apiReslutMap[result.SeqID])

					if result.ErrorCode == "0" {
						// 如果翻译结果正确，则写入数据库
						translateItem := model.TranslateTextItem{
							Md5:     handlerUtils.CalculateMD5FromText(textCopy + req.From + req.To),
							SrcText: srcStr,
							DstText: dstStr,
							FromLan: result.From,
							ToLan:   result.To,
							TransId: reqId + strconv.Itoa(seqIDCopy),
						}
						error := (&translateItem).UpdateItem(gomysql.DB)
						if error != nil {
							// 翻译结果写入数据库失败时，不向前端报错
							logger.Log.Errorf(utils.MMark(logCtx)+"TranslateText save translateItem fail,err:%v md5:%s", md5Str, error)
						}
					}
				} else {
					// 翻译报错，不写入数据库
					rwMutex.Lock()
					apiReslutMap[result.SeqID] = proto.TransResultModel{
						TransID:   reqId + strconv.Itoa(seqIDCopy),
						ErrorCode: result.ErrorCode,
						ErrorMsg:  result.ErrorMsg,
					}
					rwMutex.Unlock()
					logger.Log.Infof(utils.MMark(logCtx)+"TranslateText api, result:%v,seqIDCopy:%v", result, seqIDCopy)

				}
			} else {
				rwMutex.Lock()
				apiReslutMap[result.SeqID] = proto.TransResultModel{
					TransID:   reqId + strconv.Itoa(seqIDCopy),
					ErrorCode: "100004",
					ErrorMsg:  "调用翻译失败,errorMsg:" + err.Error(),
				}
				rwMutex.Unlock()
				logger.Log.Infof(utils.MMark(logCtx)+"TranslateText api request error result:%v,seqIDCopy:%v,err:%v", result, seqIDCopy, err)
			}
		}()
	}
	// 等待所有翻译请求返回
	wg.Wait()
	logger.Log.Infof(utils.MMark(logCtx)+"TranslateText get transApi end, apiReslutMap:%+v", apiReslutMap)
	resultModels := make([]proto.TransResultModel, len(apiReslutMap))
	for key := range resultModels {
		resultModels[key] = apiReslutMap[key]
	}
	translateTextResponse := proto.TranslateTextResponse{
		ReqID:       reqId,
		TransResult: resultModels,
	}
	elapsed := time.Since(startTime)
	logger.Log.Infof(utils.MMark(logCtx)+"TranslateText request end,cost:%v, response:%+v", elapsed, translateTextResponse)

	c.JSON(http.StatusOK, proto.NewSuccessRsp(translateTextResponse))
}

func GetApiSignStr(logCtx context.Context, query string, from string, to string) proto.TransApiRequest {
	salt := fmt.Sprintf("%d", time.Now().UnixNano()/int64(time.Millisecond))
	src := config.LocalConfig.TranslateApi.AppID + query + salt + config.LocalConfig.TranslateApi.SecurityKey
	signStr := handlerUtils.CalculateMD5FromText(src)
	logger.Log.Infof(utils.MMark(logCtx)+"getSignStr start AppID: %s, SecurityKey: %s, salt: %s, signStr: %s",
		config.LocalConfig.TranslateApi.AppID, config.LocalConfig.TranslateApi.SecurityKey, salt, signStr)

	transApiReq := proto.TransApiRequest{
		Q:     query,
		From:  from,
		To:    to,
		AppId: config.LocalConfig.TranslateApi.AppID,
		Salt:  salt,
		Sign:  signStr,
	}

	logger.Log.Infof(utils.MMark(logCtx)+"getSignStr end transApiReq: %+v", transApiReq)
	return transApiReq
}

func getUrlParamValues(logCtx context.Context, query string, from string, to string) string {
	salt := fmt.Sprintf("%d", time.Now().UnixNano()/int64(time.Millisecond))
	src := config.LocalConfig.TranslateApi.AppID + query + salt + config.LocalConfig.TranslateApi.SecurityKey
	signStr := handlerUtils.CalculateMD5FromText(src)
	logger.Log.Infof(utils.MMark(logCtx)+"getUrlParamValues start AppID: %s, SecurityKey: %s, salt: %s, signStr: %s",
		config.LocalConfig.TranslateApi.AppID, config.LocalConfig.TranslateApi.SecurityKey, salt, signStr)
	urlsValues := url.Values{}
	urlsValues.Set("q", query)
	urlsValues.Set("from", from)
	urlsValues.Set("to", to)
	urlsValues.Set("appid", config.LocalConfig.TranslateApi.AppID)
	urlsValues.Set("salt", salt)
	urlsValues.Set("sign", signStr)
	values := urlsValues.Encode()
	logger.Log.Infof(utils.MMark(logCtx)+"getUrlParamValues end urlsValues: %+v", values)
	return values
}

func TranslateTextFeedback(c *gin.Context) {
	// reqId := uuid.New().String()
	// logCtx := context.WithValue(context.TODO(), utils.CtxKeyLogID, reqId)
	// accountID := "text"
	// aid, ok := c.Get("AccountId")
	// if ok {
	// 	accountID = aid.(string)
	// }
	// logger.Log.Infof(utils.MMark(logCtx)+"TranslateText ShouldBindQuery fail, err:%v req:%#v", err, req)

	// res := proto.TranslateTextResponse{
	// 	ReqID:       reqId,
	// 	TransResult: []proto.TransResultModel{},
	// }

	// // 解析请求参数
	// req := proto.TranslateTextRequest{}

	// if err := c.ShouldBindJSON(&req); err != nil {
	// 	logger.Log.Errorf(utils.MMark(logCtx)+"TranslateText ShouldBindQuery fail, err:%v req:%#v", err, req)
	// 	c.JSON(http.StatusOK, proto.NewCommDataRsp(100001, "参数错误", nil))
	// 	return
	// }

	// if len(req.TextList) == 0 {
	// 	logger.Log.Errorf(utils.MMark(logCtx) + "TranslateText req.TextList is empty")
	// 	c.JSON(http.StatusOK, proto.NewCommDataRsp(100002, "参数错误,文本列表为空", nil))
	// 	return
	// }

	// c.JSON(http.StatusOK, proto.NewSuccessRsp(res))
}

func requestTransApi(logCtx context.Context, seqID int, text string, from string, to string) (*proto.TransApiResponse, error) {
	urlPath := config.LocalConfig.TranslateApi.URL
	logger.Log.Infof(utils.MMark(logCtx)+"requestTransApi start text: %s, from: %s, to: %s, seqID: %s,url:%s", text, from, to, seqID, urlPath)
	httpclient := retryhttpclient.NewRetryHTTPClient(10*time.Minute, 3)
	headers := make(map[string]string)

	values := getUrlParamValues(logCtx, text, from, to)
	urlPath = urlPath + "?" + values

	logger.Log.Infof(utils.MMark(logCtx)+"requestTransApi, urlPath: %s", urlPath)

	errorCode := "0"
	errorMsg := ""
	for i := 0; i < 3; i++ {
		transApiResponse := &proto.TransApiResponse{
			SeqID:     seqID,
			ErrorCode: "0",
		}
		resp, err := httpclient.DoRequest(logCtx, http.MethodGet, urlPath, headers, nil)
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+"requestTransApi request,retryCount:%d,seqID:%s ,error:%v", i, seqID, err)
			return transApiResponse, err
		}
		logger.Log.Infof(utils.MMark(logCtx)+"requestTransApi,retryCount:%d,seqID:%v, response: %s", i, seqID, string(resp))

		err = json.Unmarshal(resp, transApiResponse)
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+"requestTransApi,retryCount:%d,seqID:%v, Unmarshal error: %v", i, seqID, err)
			return transApiResponse, err
		}

		// 如果返回的错误码需要重试，则进行重试
		logger.Log.Infof(utils.MMark(logCtx)+"requestTransApi,NeedReryErrorCode:%v", config.LocalConfig.TranslateApi.NeedReryErrorCode)
		if !containsString(config.LocalConfig.TranslateApi.NeedReryErrorCode, transApiResponse.ErrorCode) {
			logger.Log.Infof(utils.MMark(logCtx)+"requestTransApi end, seqID:%v, transApiResponse: %+v", seqID, transApiResponse)
			return transApiResponse, nil
		}
		errorCode = transApiResponse.ErrorCode
		errorMsg = transApiResponse.ErrorMsg
		time.Sleep(1 * time.Second) // 等待一秒再重试
		logger.Log.Infof(utils.MMark(logCtx)+"requestTransApi,to retry,retryCount:%d,seqID:%v:", i, seqID)
	}
	transErrorResponse := &proto.TransApiResponse{
		SeqID:     seqID,
		ErrorCode: errorCode,
		ErrorMsg:  errorMsg,
	}
	logger.Log.Infof(utils.MMark(logCtx)+"requestTransApi faild,transErrorResponse:%v", transErrorResponse)
	return transErrorResponse, fmt.Errorf("requestTransApi faild,errorCode:%s,errorMsg:%s", errorCode, errorMsg)
}

// 判断切片中是否包含某个整数
func containsString(slice []string, value string) bool {
	for _, v := range slice {
		if v == value {
			return true
		}
	}
	return false
}

func checkQpsAllowance(logCtx context.Context) (bool, error) {
	key := fmt.Sprintf(TranslateTextQpsQpsFmt, handlerUtils.GetNameByRunEnv())

	// todo  test
	if config.LocalConfig == nil {
		logger.Log.Error(utils.MMark(logCtx) + "checkQpsAllowance config.LocalConfig==nil")
		return true, nil
	}

	if config.LocalConfig.TranslateApi == nil {
		logger.Log.Error(utils.MMark(logCtx) + "checkQpsAllowance config.LocalConfig.TranslateApi==nil")
		return true, nil
	}

	qpsMax := config.LocalConfig.TranslateApi.QpsMax
	logger.Log.Infof(utils.MMark(logCtx)+"checkQpsAllowance qpsMax:%v", qpsMax)
	ok, err := ratelimiter.Allow(key, qpsMax, time.Second)
	if err != nil {
		return false, err
	}

	if !ok {
		return false, nil
	}
	return true, nil
}
