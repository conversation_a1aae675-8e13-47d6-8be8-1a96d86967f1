package translate

import (
	"acg-ai-go-common/gomysql"
	"acg-ai-go-common/logger"
	"acg-ai-go-common/utils"
	"dhlive-external-api/handler/i18n/respi18n"
	"strings"

	// "bytes"
	"context"
	"dhlive-external-api/beans/model"
	"dhlive-external-api/beans/proto"
	config "dhlive-external-api/conf"
	"dhlive-external-api/handler/handlerUtils"
	"dhlive-external-api/handler/handlerUtils/ratelimiter"
	"dhlive-external-api/handler/handlerUtils/retryhttpclient"
	"encoding/json"
	"fmt"
	"net/http"
	"net/url"
	"strconv"
	"sync"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"

	translate "cloud.google.com/go/translate/apiv3"
	"cloud.google.com/go/translate/apiv3/translatepb"
	"google.golang.org/api/option"
)

const (
	TranslateTextQps       = "translateTextQps"
	TranslateTextQpsQpsFmt = "%s:" + TranslateTextQps + ":xiling-saas-v3"

	TranslateGoogleTextQps       = "translateGoogleTextQps"
	TranslateGoogleTextQpsQpsFmt = "%s:" + TranslateGoogleTextQps + ":xiling-saas-v3"

	TranslateBaiduCode  = 0
	TranslateGoogleCode = 1
)

var (
	LanCodeMapBaiduToGoogle = make(map[string]string)
	LanCodeMapGoogleToBaidu = make(map[string]string)
)

func InitLanCode() {
	error := json.Unmarshal([]byte(config.LocalConfig.TranslateApi.LanCodeBaiduToGoogle), &LanCodeMapBaiduToGoogle)
	if error != nil {
		logger.Log.Errorf("InitLanCode LanCodeBaiduToGoogle error:%+v", error)
	}
	error = json.Unmarshal([]byte(config.LocalConfig.TranslateApi.LanCodeGoogleToBaidu), &LanCodeMapGoogleToBaidu)
	if error != nil {
		logger.Log.Errorf("InitLanCode LanCodeGoogleToBaidu error:%+v", error)
	}
	logger.Log.Infof("InitLanCode success, LanCodeMapBaiduToGoogle:%+v,LanCodeGoogleToBaidu:%+v", LanCodeMapBaiduToGoogle, LanCodeMapGoogleToBaidu)
}

func TranslateText(c *gin.Context) {
	reqId := uuid.New().String()
	logCtx := context.WithValue(context.TODO(), utils.CtxKeyLogID, reqId)
	accountID := "text"
	aid, ok := c.Get("AccountId")
	if ok {
		accountID = aid.(string)
	}
	logger.Log.Infof(utils.MMark(logCtx)+"TranslateText start accountID:%s", accountID)
	startTime := time.Now()

	targetLanguage := c.GetHeader("Language")
	logCtx = context.WithValue(logCtx, "Language", targetLanguage)

	// 解析请求参数
	req := proto.TranslateTextRequest{}
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"TranslateText ShouldBindQuery fail, err:%v req:%#v", err, req)
		c.JSON(http.StatusOK, proto.NewCommDataRsp(100001, respi18n.TransformResponseLocaleByDefault(logCtx, targetLanguage, "参数错误"), nil))
		return
	}

	if len(req.TextList) == 0 {
		logger.Log.Errorf(utils.MMark(logCtx) + "TranslateText req.TextList is empty")
		c.JSON(http.StatusOK, proto.NewCommDataRsp(100002, respi18n.TransformResponseLocaleByDefault(logCtx, targetLanguage, "参数错误,文本列表为空"), nil))
		return
	}

	if len(req.TextList) > config.LocalConfig.TranslateApi.TranslateListSizeLimit {
		logger.Log.Errorf(utils.MMark(logCtx) + "TranslateText req.TextList is to much")
		c.JSON(http.StatusOK, proto.NewCommDataRsp(100002, respi18n.TransformResponseLocaleByDefault(logCtx, targetLanguage, "参数错误,文本数量超过限制"), nil))
		return
	}
	logger.Log.Infof(utils.MMark(logCtx)+"TranslateText accountID:%s , reqBody:%#v", accountID, req)
	if len(req.ReqId) != 0 {
		reqId = req.ReqId
	}
	logCtx = context.WithValue(context.TODO(), utils.CtxKeyLogID, reqId)
	logger.Log.Infof(utils.MMark(logCtx)+"TranslateText accountID:%s , reqBody:%#v", accountID, req)

	apiReslutMap := make(map[int]proto.TransResultModel)

	// 调用百度的翻译
	if config.LocalConfig.TranslateApi.WhichTranslate == TranslateBaiduCode {
		var wg sync.WaitGroup
		var rwMutex sync.RWMutex
		for seqID, text := range req.TextList {
			// 如果是空字符串直接返回空
			if len(strings.TrimSpace(text)) == 0 {
				rwMutex.Lock()
				apiReslutMap[seqID] = proto.TransResultModel{
					TransID:   "",
					Src:       "",
					Dst:       "",
					From:      "",
					To:        "",
					ErrorCode: "0",
					ErrorMsg:  "",
				}
				logger.Log.Infof(utils.MMark(logCtx)+"TranslateText text is empty continue,seqID:%d", seqID)
				rwMutex.Unlock()
				continue
			}

			if config.LocalConfig.TranslateApi.UseDataCache {
				// 先从数据库中查询是否已经翻译过
				md5Str := handlerUtils.CalculateMD5FromText(text + req.From + req.To)
				logger.Log.Infof(utils.MMark(logCtx)+"TranslateText text:%s , md5:%s", text, md5Str)
				translateTextItem, err := (&model.TranslateTextItem{}).GetItemByMd5(gomysql.DB, md5Str)
				if err == nil && translateTextItem != nil {
					rwMutex.Lock()
					apiReslutMap[seqID] = proto.TransResultModel{
						TransID:   translateTextItem.TransId,
						Src:       translateTextItem.SrcText,
						Dst:       translateTextItem.DstText,
						From:      translateTextItem.FromLan,
						To:        translateTextItem.ToLan,
						ErrorCode: "0",
						ErrorMsg:  "",
					}
					logger.Log.Infof(utils.MMark(logCtx)+"TranslateText use cache result,seqID:%d,md5:%s,text:%s", seqID, md5Str, text)
					rwMutex.Unlock()
					continue
				}
			} else {
				logger.Log.Infof(utils.MMark(logCtx) + "TranslateText not use cache")
			}

			// 申请qps限制
			for {
				allow, err := checkQpsAllowance(logCtx, TranslateBaiduCode)
				if err != nil {
					logger.Log.Errorf(utils.MMark(logCtx)+"TranslateText checkQpsAllowance is err:%v", err)
					c.JSON(http.StatusOK, proto.NewCommDataRsp(100003, respi18n.TransformResponseLocaleByDefault(logCtx, targetLanguage, "QPS校验失败,请稍后再试"), nil))
					return
				}
				if !allow {
					time.Sleep(500 * time.Millisecond)
					continue
				}
				break
			}

			wg.Add(1)
			seqIDCopy := seqID
			textCopy := text
			go func() {
				defer wg.Done()
				// 请求翻译api   logid后加上 id，方便定位问题
				logCtxApi := context.WithValue(context.TODO(), utils.CtxKeyLogID, reqId+"_"+strconv.Itoa(seqIDCopy))
				result, err := requestTransBaiduApi(logCtxApi, seqIDCopy, textCopy, req.From, req.To)
				if err == nil && result != nil {
					logger.Log.Infof(utils.MMark(logCtx)+"TranslateText requestTransApi result:%v,seqIDCopy:%v,err:%v", result, seqIDCopy, err)
					if result.ErrorCode == "0" && result.TransResult != nil && len(result.TransResult) > 0 {

						srcStr := ""
						dstStr := ""
						for _, v := range result.TransResult {
							logger.Log.Infof(utils.MMark(logCtx)+"TranslateText requestTransApi transResult:%v,seqIDCopy:%v", v, seqIDCopy)
							srcStr = srcStr + v.Src
							dstStr = dstStr + v.Dst
						}

						rwMutex.Lock()
						apiReslutMap[result.SeqID] = proto.TransResultModel{
							TransID:   reqId + strconv.Itoa(seqIDCopy),
							Src:       srcStr,
							Dst:       dstStr,
							From:      result.From,
							To:        result.To,
							ErrorCode: result.ErrorCode,
							ErrorMsg:  result.ErrorMsg,
						}
						rwMutex.Unlock()
						logger.Log.Infof(utils.MMark(logCtx)+"TranslateText requestTransApi result write to resultMap:%v,seqIDCopy:%v,apiReslutMap[result.SeqID]:%+v", result, seqIDCopy, apiReslutMap[result.SeqID])

						if config.LocalConfig.TranslateApi.UseDataCache {
							if result.ErrorCode == "0" {
								// 如果翻译结果正确，则写入数据库
								translateItem := model.TranslateTextItem{
									Md5:     handlerUtils.CalculateMD5FromText(textCopy + req.From + req.To),
									SrcText: srcStr,
									DstText: dstStr,
									FromLan: result.From,
									ToLan:   result.To,
									TransId: reqId + strconv.Itoa(seqIDCopy),
								}
								error := (&translateItem).UpdateItem(gomysql.DB)
								if error != nil {
									// 翻译结果写入数据库失败时，不向前端报错
									logger.Log.Errorf(utils.MMark(logCtx)+"TranslateText save translateItem fail,err:%v seqIDCopy:%s", seqIDCopy, error)
								}
							}
						}
					} else {
						// 翻译报错，不写入数据库
						rwMutex.Lock()
						apiReslutMap[result.SeqID] = proto.TransResultModel{
							TransID:   reqId + strconv.Itoa(seqIDCopy),
							ErrorCode: result.ErrorCode,
							ErrorMsg:  result.ErrorMsg,
						}
						rwMutex.Unlock()
						logger.Log.Infof(utils.MMark(logCtx)+"TranslateText api, result:%v,seqIDCopy:%v", result, seqIDCopy)

					}
				} else {
					rwMutex.Lock()
					apiReslutMap[result.SeqID] = proto.TransResultModel{
						TransID:   reqId + strconv.Itoa(seqIDCopy),
						ErrorCode: "100004",
						ErrorMsg:  respi18n.TransformResponseLocaleByDefault(logCtx, targetLanguage, "get TransApi error") + "errorMsg:" + err.Error(),
					}
					rwMutex.Unlock()
					logger.Log.Infof(utils.MMark(logCtx)+"TranslateText api request error result:%v,seqIDCopy:%v,err:%v", result, seqIDCopy, err)
				}
			}()
		}
		// 等待所有翻译请求返回
		wg.Wait()
		logger.Log.Infof(utils.MMark(logCtx)+"TranslateText get transApi end, apiReslutMap:%+v", apiReslutMap)
	} else {
		// 调用Google的翻译
		logger.Log.Infof(utils.MMark(logCtx)+"TranslateText google, req.To:%s,LanCodeMapBaiduToGoogle:%+v,LanCodeMapGoogleToBaidu:+v", req.To, LanCodeMapBaiduToGoogle, LanCodeMapGoogleToBaidu)
		targetLanCodeGoogle := LanCodeMapBaiduToGoogle[req.To]
		if targetLanCodeGoogle == "" {
			targetLanCodeGoogle = req.To
		}
		logger.Log.Infof(utils.MMark(logCtx)+"TranslateText targetLanCodeGoogle:%s", targetLanCodeGoogle)

		needTransTextArray := make([]string, 0)
		needTransSeqIDArray := make([]int, 0)

		for seqID, text := range req.TextList {
			// 如果是空字符串直接返回空
			if len(strings.TrimSpace(text)) == 0 {
				apiReslutMap[seqID] = proto.TransResultModel{
					TransID:   "",
					Src:       "",
					Dst:       "",
					From:      "",
					To:        "",
					ErrorCode: "0",
					ErrorMsg:  "",
				}
				logger.Log.Infof(utils.MMark(logCtx)+"TranslateText google text is empty continue,seqID:%d", seqID)
				continue
			}

			// 先从数据库中查询是否已经翻译过
			if config.LocalConfig.TranslateApi.UseDataCache {
				md5Str := handlerUtils.CalculateMD5FromText(text + req.To)
				translateTextItem, err := (&model.TranslateTextItem{}).GetItemByMd5(gomysql.DB, md5Str)
				logger.Log.Infof(utils.MMark(logCtx)+"TranslateText google get cache,GetItemByMd5,seqID:%d,md5:%s,text:%s,err:%v", seqID, md5Str, text, err)
				if err == nil && translateTextItem != nil {
					apiReslutMap[seqID] = proto.TransResultModel{
						TransID:   translateTextItem.TransId,
						Src:       translateTextItem.SrcText,
						Dst:       translateTextItem.DstText,
						From:      translateTextItem.FromLan,
						To:        translateTextItem.ToLan,
						ErrorCode: "0",
						ErrorMsg:  "",
					}
					logger.Log.Infof(utils.MMark(logCtx)+"TranslateText google use cache result,seqID:%d,md5:%s,text:%s", seqID, md5Str, text)
					continue
				}
			} else {
				logger.Log.Infof(utils.MMark(logCtx) + "TranslateText google not use cache")
			}
			needTransTextArray = append(needTransTextArray, text)
			needTransSeqIDArray = append(needTransSeqIDArray, seqID)
		}

		if len(needTransTextArray) > 0 {
			// 申请qps限制
			for {
				allow, err := checkQpsAllowance(logCtx, TranslateGoogleCode)
				if err != nil {
					logger.Log.Errorf(utils.MMark(logCtx)+"TranslateText checkQpsAllowance is err:%v", err)
					return
				}
				if !allow {
					time.Sleep(500 * time.Millisecond)
					continue
				}
				break
			}

			googleTranslationResult, err := requestTransGoogleApi(logCtx, targetLanCodeGoogle, needTransTextArray)
			if err != nil {
				logger.Log.Errorf(utils.MMark(logCtx)+"TranslateText requestTransApi error:%v", err)
				c.JSON(http.StatusOK, proto.NewCommDataRsp(100003, respi18n.TransformResponseLocaleByDefault(logCtx, targetLanguage, "get TransApi error")+"errorMsg:"+err.Error(), nil))
				return
			}

			itemsForSave := make([]*model.TranslateTextItem, 0)
			for idx, transResultItem := range googleTranslationResult {
				seqID := needTransSeqIDArray[idx]
				srcText := needTransTextArray[idx]
				dstStr := transResultItem.GetTranslatedText()
				fromLan := transResultItem.GetDetectedLanguageCode()

				logger.Log.Infof(utils.MMark(logCtx)+"TranslateText google end,fromLan:%s", fromLan)
				baiduLanCode := LanCodeMapGoogleToBaidu[fromLan]
				if baiduLanCode != "" {
					fromLan = baiduLanCode
				}
				logger.Log.Infof(utils.MMark(logCtx)+"TranslateText google end,exChange-fromLan:%s", fromLan)

				apiReslutMap[seqID] = proto.TransResultModel{
					TransID:   reqId + strconv.Itoa(seqID),
					Src:       srcText,
					Dst:       dstStr,
					From:      fromLan,
					To:        req.To,
					ErrorCode: "0",
					ErrorMsg:  "",
				}

				if config.LocalConfig.TranslateApi.UseDataCache {
					itemForSave := &model.TranslateTextItem{
						Md5:     handlerUtils.CalculateMD5FromText(srcText + req.To),
						SrcText: srcText,
						DstText: dstStr,
						FromLan: fromLan,
						ToLan:   req.To,
						TransId: reqId + strconv.Itoa(seqID),
					}
					itemsForSave = append(itemsForSave, itemForSave)
				}
			}

			if len(itemsForSave) > 0 {
				error := (&model.TranslateTextItem{}).UpdateItems(gomysql.DB, itemsForSave)
				if error != nil {
					// 翻译结果写入数据库失败时，不向前端报错
					logger.Log.Errorf(utils.MMark(logCtx)+"TranslateText google  save translateItem fail,err:%v", error)
				} else {
					logger.Log.Infof(utils.MMark(logCtx) + "TranslateText google save to database success")
				}
			}
		}
	}

	resultModels := make([]proto.TransResultModel, len(apiReslutMap))
	for key := range resultModels {
		resultModels[key] = apiReslutMap[key]
	}
	translateTextResponse := proto.TranslateTextResponse{
		ReqID:       reqId,
		TransResult: resultModels,
	}
	elapsed := time.Since(startTime)
	logger.Log.Infof(utils.MMark(logCtx)+"TranslateText request end,cost:%v, response:%+v", elapsed, translateTextResponse)
	c.JSON(http.StatusOK, proto.NewSuccessRsp(translateTextResponse))
}

func GetApiSignStr(logCtx context.Context, query string, from string, to string) proto.TransApiRequest {
	salt := fmt.Sprintf("%d", time.Now().UnixNano()/int64(time.Millisecond))
	src := config.LocalConfig.TranslateApi.AppID + query + salt + config.LocalConfig.TranslateApi.SecurityKey
	signStr := handlerUtils.CalculateMD5FromText(src)
	logger.Log.Infof(utils.MMark(logCtx)+"getSignStr start AppID: %s, SecurityKey: %s, salt: %s, signStr: %s",
		config.LocalConfig.TranslateApi.AppID, config.LocalConfig.TranslateApi.SecurityKey, salt, signStr)

	transApiReq := proto.TransApiRequest{
		Q:     query,
		From:  from,
		To:    to,
		AppId: config.LocalConfig.TranslateApi.AppID,
		Salt:  salt,
		Sign:  signStr,
	}

	logger.Log.Infof(utils.MMark(logCtx)+"getSignStr end transApiReq: %+v", transApiReq)
	return transApiReq
}

func getUrlParamValues(logCtx context.Context, query string, from string, to string) string {
	salt := fmt.Sprintf("%d", time.Now().UnixNano()/int64(time.Millisecond))
	src := config.LocalConfig.TranslateApi.AppID + query + salt + config.LocalConfig.TranslateApi.SecurityKey
	signStr := handlerUtils.CalculateMD5FromText(src)
	logger.Log.Infof(utils.MMark(logCtx)+"getUrlParamValues start AppID: %s, SecurityKey: %s, salt: %s, signStr: %s",
		config.LocalConfig.TranslateApi.AppID, config.LocalConfig.TranslateApi.SecurityKey, salt, signStr)
	urlsValues := url.Values{}
	urlsValues.Set("q", query)
	urlsValues.Set("from", from)
	urlsValues.Set("to", to)
	urlsValues.Set("appid", config.LocalConfig.TranslateApi.AppID)
	urlsValues.Set("salt", salt)
	urlsValues.Set("sign", signStr)
	values := urlsValues.Encode()
	logger.Log.Infof(utils.MMark(logCtx)+"getUrlParamValues end urlsValues: %+v", values)
	return values
}

// 调用百度的翻译的能力
func requestTransBaiduApi(logCtx context.Context, seqID int, text string, from string, to string) (*proto.TransApiResponse, error) {
	urlPath := config.LocalConfig.TranslateApi.URL
	logger.Log.Infof(utils.MMark(logCtx)+"requestTransApi start text: %s, from: %s, to: %s, seqID: %s,url:%s", text, from, to, seqID, urlPath)
	httpclient := retryhttpclient.NewRetryHTTPClient(10*time.Minute, 3)
	headers := make(map[string]string)

	values := getUrlParamValues(logCtx, text, from, to)
	urlPath = urlPath + "?" + values

	logger.Log.Infof(utils.MMark(logCtx)+"requestTransApi, urlPath: %s", urlPath)

	errorCode := "0"
	errorMsg := ""
	for i := 0; i < 3; i++ {
		transApiResponse := &proto.TransApiResponse{
			SeqID:     seqID,
			ErrorCode: "0",
		}
		resp, err := httpclient.DoRequest(logCtx, http.MethodGet, urlPath, headers, nil)
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+"requestTransApi request,retryCount:%d,seqID:%s ,error:%v", i, seqID, err)
			return transApiResponse, err
		}
		logger.Log.Infof(utils.MMark(logCtx)+"requestTransApi,retryCount:%d,seqID:%v, response: %s", i, seqID, string(resp))

		err = json.Unmarshal(resp, transApiResponse)
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+"requestTransApi,retryCount:%d,seqID:%v, Unmarshal error: %v", i, seqID, err)
			return transApiResponse, err
		}

		// 如果返回的错误码需要重试，则进行重试
		logger.Log.Infof(utils.MMark(logCtx)+"requestTransApi,NeedReryErrorCode:%v", config.LocalConfig.TranslateApi.NeedReryErrorCode)
		if !containsString(config.LocalConfig.TranslateApi.NeedReryErrorCode, transApiResponse.ErrorCode) {
			logger.Log.Infof(utils.MMark(logCtx)+"requestTransApi end, seqID:%v, transApiResponse: %+v", seqID, transApiResponse)
			return transApiResponse, nil
		}
		errorCode = transApiResponse.ErrorCode
		errorMsg = transApiResponse.ErrorMsg
		time.Sleep(1 * time.Second) // 等待一秒再重试
		logger.Log.Infof(utils.MMark(logCtx)+"requestTransApi,to retry,retryCount:%d,seqID:%v:", i, seqID)
	}
	transErrorResponse := &proto.TransApiResponse{
		SeqID:     seqID,
		ErrorCode: errorCode,
		ErrorMsg:  errorMsg,
	}
	logger.Log.Infof(utils.MMark(logCtx)+"requestTransApi faild,transErrorResponse:%v", transErrorResponse)
	return transErrorResponse, fmt.Errorf("requestTransApi faild,errorCode:%s,errorMsg:%s", errorCode, errorMsg)
}

// 判断切片中是否包含某个整数
func containsString(slice []string, value string) bool {
	for _, v := range slice {
		if v == value {
			return true
		}
	}
	return false
}

func checkQpsAllowance(logCtx context.Context, platform int) (bool, error) {
	redisKey := fmt.Sprintf(TranslateTextQpsQpsFmt, handlerUtils.GetNameByRunEnv())
	if platform == TranslateGoogleCode {
		redisKey = fmt.Sprintf(TranslateGoogleTextQpsQpsFmt, handlerUtils.GetNameByRunEnv())
	}
	logger.Log.Infof(utils.MMark(logCtx)+"checkQpsAllowance redisKey=%s", redisKey)

	if config.LocalConfig == nil {
		logger.Log.Error(utils.MMark(logCtx) + "checkQpsAllowance config.LocalConfig==nil")
		return true, nil
	}

	if config.LocalConfig.TranslateApi == nil {
		logger.Log.Error(utils.MMark(logCtx) + "checkQpsAllowance config.LocalConfig.TranslateApi==nil")
		return true, nil
	}

	qpsMax := config.LocalConfig.TranslateApi.QpsMax
	if platform == TranslateGoogleCode {
		qpsMax = config.LocalConfig.TranslateApi.GoogleQpsMax
	}

	logger.Log.Infof(utils.MMark(logCtx)+"checkQpsAllowance qpsMax:%v", qpsMax)
	ok, err := ratelimiter.Allow(redisKey, qpsMax, time.Second)
	if err != nil {
		return false, err
	}

	if !ok {
		return false, nil
	}
	return true, nil
}

// 调用Google的翻译能力
func requestTransGoogleApi(logCtx context.Context, targetLang string, texts []string) ([]*translatepb.Translation, error) {
	beingTime := time.Now()
	credentialsPath := config.LocalConfig.TranslateApi.GoogleCredentialsPath
	gcsProjectId := config.LocalConfig.TranslateApi.GoogleProjectID
	ctx := context.Background()
	client, err := translate.NewTranslationClient(ctx, option.WithCredentialsFile(credentialsPath))
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"NewTranslationClient error: %w", err)
		return nil, fmt.Errorf("NewTranslationClient: %w", err)
	}
	logger.Log.Infof(utils.MMark(logCtx)+"requestTransGoogleApi start gcsProjectId:%s,credentialsPath:%s,targetLang:%s, texts:%+v", gcsProjectId, credentialsPath, targetLang, texts)

	defer client.Close()
	req := &translatepb.TranslateTextRequest{
		Parent:             fmt.Sprintf("projects/%s/locations/global", gcsProjectId),
		TargetLanguageCode: targetLang,
		MimeType:           "text/plain",
		Contents:           texts,
	}

	resp, err := client.TranslateText(ctx, req)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"client.TranslateText error: %w", err)
		return nil, fmt.Errorf("client.TranslateText: %w", err)
	}

	respTranslations := resp.GetTranslations()
	for _, translationItem := range resp.GetTranslations() {
		logger.Log.Infof(utils.MMark(logCtx)+"client.TranslateText:%s,fromLan:%s", translationItem.GetTranslatedText(), translationItem.GetDetectedLanguageCode())
	}
	costTime := time.Since(beingTime)
	logger.Log.Infof(utils.MMark(logCtx)+"client.TranslateText success, consTime:%s", costTime)
	return respTranslations, nil
}
