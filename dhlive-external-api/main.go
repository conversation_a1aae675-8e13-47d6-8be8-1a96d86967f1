package main

import (
	"acg-ai-go-common/global"
	"acg-ai-go-common/logger"
	"acg-ai-go-common/server"
	"acg-ai-go-common/storage"
	"acg-ai-go-common/utils/mysqlproxy"
	"context"
	"dhlive-external-api/beans/model"
	config "dhlive-external-api/conf"
	"dhlive-external-api/handler/campaign"
	"dhlive-external-api/handler/figure/scanner"
	"dhlive-external-api/handler/gcpcensor"
	"dhlive-external-api/handler/translate"
	"dhlive-external-api/mysqlclient"

	"acg-ai-go-common/utils/redisproxy"
	"dhlive-external-api/routers"
	"log"
	"net/http"
	_ "net/http/pprof"

	"github.com/BurntSushi/toml"
)

func main() {
	go func() {
		log.Println(http.ListenAndServe("localhost:6060", nil))
	}()

	// 初始化公共配置
	server.InitGlobalSetting()
	if _, err := toml.DecodeFile(global.ConfFilePath, config.LocalConfig); err != nil {
		log.Panicf("本地个性化配置加载失败: {%v}", err)
	}

	// 初始化对象存储
	if err := storage.Init(global.ServerSetting); err != nil {
		log.Panicf("初始化对象存储: {%v}", err)
	}

	// 初始化对象存储
	if err := storage.Init(global.ServerSetting); err != nil {
		log.Panicf("初始化对象存储: {%v}", err)
	}
	logger.Log.Infof("init storage type：%s\n", global.ServerSetting.StorageSetting.Type)

	campaign.InitKafkaProxy()
	logger.Log.Info("本地个性化配置加载成功")

	gcpcensor.InitGCPClients(context.Background())
	// 初始化redis
	proxy := redisproxy.GetRedisProxy()
	// 开启携程监听redis是否发生网络中断并进行重连
	go proxy.MonitorRedisConnection()

	// 初始化mysql
	mysqlproxy := mysqlproxy.GetMysqlProxy()
	// 开启携程监听mysql是否发生网络中断并进行重连
	go mysqlproxy.MonitorMysqlConnection()

	// 初始化mysql 因为存在多个数据库这里初始化全局map数据库，在使用时使用map[dbName]即可
	mysqlclient.InitDB(mysqlclient.StarLightDBName, config.LocalConfig.StarLightMysqlSetting)
	mysqlclient.InitDB(mysqlclient.Text2FigureDBName, config.LocalConfig.Text2FigureMysqlSetting)
	mysqlclient.InitDB(mysqlclient.MetaHumanEditorSaasDBName, config.LocalConfig.MetaHumanEditorSaas)

	// 开启携程监听mysql是否发生网络中断并进行重连
	go mysqlclient.MonitorMysqlConnection()
	// 初始化数据库
	err := model.InitMysqlDBTable()
	if err != nil {
		log.Panicf("初始化数据库: {%v}", err)
	}
	// 初始化翻译语言对应code
	translate.InitLanCode()
	// 初始化路由
	routers.InitRouter()
	// 开启定时器 定时加水印
	scanner.Init()
	// 启动服务
	server.Run(routers.GinRouter)
}
