"""
获取 PPT 文件中的备注
"""
import os
import sys
import json
import traceback
from pptx import Presentation


def getPPTNotesJson(ppt_file):
    """
    获取 PPT 文件中的备注函数
    """
    if not os.path.exists(ppt_file):
        raise Exception("file: " + ppt_file + " not found.")
    
    prs = Presentation(ppt_file)
    notes_data = []
    # 获取宽度和高度
    slide_width = prs.slide_width.inches
    slide_height = prs.slide_height.inches
    for i, slide in enumerate(prs.slides):
        if slide.has_notes_slide:
            notes_slide = slide.notes_slide
            notes_text_frame = notes_slide.notes_text_frame if notes_slide else None
            
            if notes_text_frame:
                note_text = notes_text_frame.text
                notes_data.append({
                  'index': i + 1,
                  'note': note_text
                })
            else:
                notes_data.append({
                  'index': i + 1,
                  'note': ""
                })

     # 将幻灯片尺寸和备注内容整合到 JSON 数据中
    result = {
        "slide_size": {
            "width_inches": slide_width,
            "height_inches": slide_height,
        },
        "notes": notes_data
    }
    return json.dumps(result, ensure_ascii=False, indent=4)


if __name__ == "__main__":
    """
    入口函数
    """
    if len(sys.argv) != 2:
        print("Usage: python script.py <ppt_file>")
        sys.exit(1)

    ppt_file = sys.argv[1]
    try:
        result = getPPTNotesJson(ppt_file)
        print(result)
    except Exception as e:
        print("An error occurred: " + str(e))
        traceback.print_exc()
        sys.exit(1)