"""
获取 PowerPoint 文件中的幻灯片数量
"""
import sys
import os
import traceback
from pptx import Presentation



def getPptPageCount(ppt_file):
    """
    获取 PowerPoint 文件中的幻灯片数量函数
    """
    if not os.path.exists(ppt_file):
        raise Exception("file: " + ppt_file + " not found.")
    prs = Presentation(ppt_file)
    return len(prs.slides)


if __name__ == "__main__":
    """
    入口函数
    """
    if len(sys.argv) != 2:
        print("Usage: python script.py <ppt_file>")
        sys.exit(1)

    ppt_file = sys.argv[1]
    try:
        result = getPptPageCount(ppt_file)
        print(result)
    except Exception as e:
        print("An error occurred:" + str(e))
        traceback.print_exc()
        sys.exit(1)