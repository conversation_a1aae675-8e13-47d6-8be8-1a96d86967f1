server:
  port: 8081

digitalhuman:
  cloud:
    grpc:
      config:
        port: 8090
        terminateAwaitSeconds: 3
        keepAliveTimeMinutes: 30
    rtc:
      config:
        internalUrl: wss://rtc.exp.bcelive.com:8989/janus
        externalUrl: wss://rtc.exp.bcelive.com:8989/janus
        appId: 75c664d50ae5432581fcfe2c9c3011d5
        key: gf4xjfwt9356
      mock:
        config:
          mockRoomName: 5681
          mockRoomPushUrl: *************:5634
          mockFeedId: 1005634
    tts:
      config:
        baseUrl: http://*************:8802
        httpQueryParams:
          lan: zh
          pdt: 993
          ctp: 1
          cuid: test
          aue: 6
          xml: 1
        responseContentType: audio/wav
    zookeeper:
      config:
        namespace: digital-human-cloud
        url: localhost:22181
        baseSleepTimeMs: 1000
        maxRetry: 3
  resource-pool:
    client:
      resourcePoolUrl: http://*************:8502

spring:
  profiles:
    active: production
    include: mock

  data:
    mongodb:
      uri: mongodb://*************:8017 #mongodb://*************:8017
      database: digitalhuman
logging:
  level:
    com.baidu.acg: debug