// Copyright (C) 2019 Baidu Inc. All rights reserved.
package com.baidu.acg.piat.digitalhuman.cloud.service.impl;

import static com.baidu.acg.digitalhuman.cloud.grpc.DisposableSessionStage.CLOSE;
import static com.baidu.acg.digitalhuman.cloud.grpc.DisposableSessionStage.INIT;
import static com.baidu.acg.digitalhuman.cloud.grpc.DisposableSessionStage.ONGOING;
import static com.baidu.acg.piat.digitalhuman.common.session.SessionStatus.CLOSED;
import static com.baidu.acg.piat.digitalhuman.common.session.SessionStatus.ERROR;

import com.google.common.collect.Lists;
import com.google.protobuf.TextFormat;
import io.grpc.Status;
import io.grpc.stub.StreamObserver;
import io.micrometer.core.instrument.Gauge;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Timer;
import io.vavr.Tuple;
import io.vavr.Tuple2;
import io.vavr.control.Either;
import io.vavr.control.Try;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicLong;
import java.util.function.BiFunction;

import javax.annotation.PostConstruct;

import com.baidu.acg.digitalhuman.cloud.grpc.BaseResponse;
import com.baidu.acg.digitalhuman.cloud.grpc.CloudCommand;
import com.baidu.acg.digitalhuman.cloud.grpc.CloudCommandResponse;
import com.baidu.acg.digitalhuman.cloud.grpc.DisposableSessionStage;
import com.baidu.acg.digitalhuman.cloud.grpc.ErrorInfo;
import com.baidu.acg.digitalhuman.cloud.grpc.OpenRequest;
import com.baidu.acg.digitalhuman.cloud.grpc.OpenResponse;
import com.baidu.acg.digitalhuman.cloud.grpc.ResponseBody;
import com.baidu.acg.digitalhuman.cloud.grpc.TextFragment;
import com.baidu.acg.digitalhuman.render.proxy.grpc.message.StreamRequest;
import com.baidu.acg.digitalhuman.render.proxy.grpc.message.StreamResponse;
import com.baidu.acg.piat.digitalhuman.cloud.persistence.CloudSession;
import com.baidu.acg.piat.digitalhuman.cloud.persistence.CloudSessionModel;
import com.baidu.acg.piat.digitalhuman.cloud.persistence.CloudSessionRepository;
import com.baidu.acg.piat.digitalhuman.cloud.service.AccessControlService;
import com.baidu.acg.piat.digitalhuman.cloud.service.HouseKeeper;
import com.baidu.acg.piat.digitalhuman.cloud.service.LockService;
import com.baidu.acg.piat.digitalhuman.cloud.service.RenderProxyService;
import com.baidu.acg.piat.digitalhuman.cloud.service.SessionService;
import com.baidu.acg.piat.digitalhuman.cloud.session.SessionContext;
import com.baidu.acg.piat.digitalhuman.common.access.AccessApp;
import com.baidu.acg.piat.digitalhuman.common.cloud.CloudRtcConnection;
import com.baidu.acg.piat.digitalhuman.common.cloud.CloudRtcSession;
import com.baidu.acg.piat.digitalhuman.common.cloud.Selectors;
import com.baidu.acg.piat.digitalhuman.common.cloud.SessionAcquireResult;
import com.baidu.acg.piat.digitalhuman.common.constans.StatisticConstant;
import com.baidu.acg.piat.digitalhuman.common.exception.DigitalHumanCommonException;
import com.baidu.acg.piat.digitalhuman.common.exception.Error;
import com.baidu.acg.piat.digitalhuman.common.model.PageResponse;
import com.baidu.acg.piat.digitalhuman.common.quota.ResourceUsage;
import com.baidu.acg.piat.digitalhuman.common.session.SessionStatus;
import com.baidu.acg.piat.digitalhuman.common.webrtc.resource.pool.model.SpareResponse;
import com.baidu.acg.piat.digitalhuman.resource.pool.client.ResourcePoolClient;
import com.baidu.acg.piat.digitalhuman.tracer.annotation.TraceMethod;
import com.baidu.acg.piat.digitalhuman.common.resource.AppResourceVO;

/**
 * SessionAuthenticator
 *
 * <AUTHOR> Zhensheng(<EMAIL>)
 * @since 2019-08-28
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class SessionServiceImpl implements SessionService {

    /**
     * render返回对应下列错误码,cloud不需要发起重连，一般该类错误属于业务校验异常
     */

    @Value("#{'${digitalhuman.cloud.nonreconnection.cods:1011,1016}'.split(',')}")
    private List<Integer> nonReconnectErrorcodes = Lists.newArrayList();
    private final CloudSessionRepository sessionRepository;

    private final RenderProxyService renderProxyService;

    private final LockService lockService;

    private final AccessControlService accessControlService;

    private final HouseKeeper houseKeeper;

    private final Selectors selectors;

    private final ResourcePoolClient resourcePoolClient;

    private final MeterRegistry meterRegistry;

    private Map<String, AtomicLong> currentSessionMap = new ConcurrentHashMap<>();

    private Map<String, Long> sessionStartTimeMap = new ConcurrentHashMap<>();

    @PostConstruct
    void init() {
        houseKeeper.addStartSessionHook(this::rebuildSession);
        houseKeeper.addShutdownSessionHook((appId, sessionId) -> {
            try {
                innerDelete(appId, sessionId, true);
            } catch (DigitalHumanCommonException ex) {
                if (ex.getCode() == 404) {
                    log.info("Cannot find suitable session to delete, maybe already closed. appId={} sessionId={}",
                            appId, sessionId);
                } else {
                    throw ex;
                }
            }
        });
    }

    private void rebuildSession(String appId, String sessionId) {
        log.info("Rebuilding session appId={} sessionId={}", appId, sessionId);

        var optional = sessionRepository.findBySessionId(sessionId);
        if (optional.isEmpty()) {
            throw new DigitalHumanCommonException("rebuild invalid session");
        }
        var sessionModel = optional.get().setUpdateTime(ZonedDateTime.now());
        sessionRepository.save(sessionModel);
        var session = CloudSession.from(sessionModel);
        Selectors realSelectors =
                session.getSelectors() == null || session.getSelectors().isEmpty() ? selectors : session.getSelectors();
        renderProxyService.open(session, session.getParameters(), realSelectors);
        log.debug("Reopened rtc connection={}, selectors={}", session.getRtcSession(), realSelectors);
    }

    @Override
    @TraceMethod(value = "acquire", tagParams = {"appId"})
    public Tuple2<SessionAcquireResult, CloudSession> acquire(String appId, OpenRequest acquireInfo) {
        log.info("Accept appId={} info={} ", appId, TextFormat.shortDebugString(acquireInfo));
        boolean rtcConnectionInfoPresent = acquireInfo.getRtcConnection() != null;

        CloudRtcSession rtcSession;

        if (rtcConnectionInfoPresent) {
            rtcSession = CloudRtcSession.builder()
                    .clientConnection(CloudRtcConnection.from(acquireInfo.getRtcConnection()))
                    .build();
        } else {
            throw new DigitalHumanCommonException("Invalid parameter, cannot find rtc connection info");
        }

        var now = ZonedDateTime.now();
        AccessApp app = accessControlService.getAppById(appId);
        Selectors selector = acquireInfo.getSelectors().isInitialized() ?
                Selectors.convertSelectors(acquireInfo.getSelectors()) : selectors;
        String characterImage = app.getCharacterImage();
        if (StringUtils.isNotEmpty(acquireInfo.getParameters().get("figureId"))) {
            characterImage = acquireInfo.getParameters().get("figureId");
            log.debug("characterImage is {}", characterImage);
        }
        var session = CloudSession.builder()
                .id(StringUtils.defaultIfBlank(acquireInfo.getSessionId(), ObjectId.get().toHexString()))
                .appId(app.getAppId())
                .userName(app.getUserId())
                .token(UUID.randomUUID().toString())
                .resourceUsage(ResourceUsage.builder().roomUsage(1).build())
                .status(SessionStatus.OPEN)
                .rtcSession(rtcSession)
                .characterImage(characterImage)
                .createTime(now)
                .updateTime(now)
                .selectors(selector)
                .parameters(acquireInfo.getParameters())
                .build();
        var sessionModel = CloudSessionModel.from(session);
        houseKeeper.lease(appId, session.getId(), characterImage, app.getResourceQuota().getRoomLimits(),
                app.getMaxIdleInSecond()).get();
        try {
            sessionModel = sessionRepository.save(sessionModel);
            session = CloudSession.from(sessionModel);
            int maxRetry = 3;
            int left = maxRetry;
            String errorMessage = null;
            int code = 0;
            while (left-- > 0) {
                var openResponse = renderProxyService.open(session, acquireInfo.getParameters(), selector);
                log.debug("Render acquire response={} try={}", TextFormat.shortDebugString(openResponse),
                        maxRetry - left);

                if (openResponse.getErrCode() == 0) {
                    break;
                }
                code = openResponse.getErrCode();
                errorMessage = openResponse.getErrMessage();
                if (nonReconnectErrorcodes.contains(code)) {
                    log.warn("Business exception code:{},message:{},do not require reconnection"
                            , code, errorMessage);
                    throw new DigitalHumanCommonException(code, errorMessage);
                }
                if (left > 0) {
                    try {
                        Thread.sleep(10);
                    } catch (InterruptedException ex) {
                        // ignore
                    }
                }
            }

            if (left < 0) {
                throw new DigitalHumanCommonException(code, errorMessage);
            }

            log.info("Success to acquire session of appId={}, sessionId={}, rtc={}", appId, session.getId(),
                    rtcSession);

            List<CloudRtcConnection> extraRtcConnections = new ArrayList<>();
            startStatisticsSession(app, session.getId());
            return Tuple.of(SessionAcquireResult.builder()
                    .rtcConnection(CloudRtcConnection.from(acquireInfo.getRtcConnection()))
                    .character(characterImage)
                    .sessionId(session.getId())
                    .sessionToken(session.getToken())
                    .parameters(session.getParameters())
                    .extra(SessionAcquireResult.Extra.builder()
                            .extraRtcConnections(extraRtcConnections)
                            .build())
                    .createTime(DateTimeFormatter.ISO_OFFSET_DATE_TIME.format(session.getCreateTime()))
                    .updateTime(DateTimeFormatter.ISO_OFFSET_DATE_TIME.format(session.getUpdateTime()))
                    .build(), session);
            // TODO change status to open and save rtc userIds
        } catch (Exception ex) {
            log.error("Fail to open session, return lease for appId={} sessionId={}", appId, session.getId(), ex);
            houseKeeper.returnLease(appId, session.getId());
            session.setStatus(ERROR);
            sessionModel.merge(session);
            sessionRepository.save(sessionModel);
            throw ex;
        }

    }

    /**
     * @param appId
     * @param sessionId
     * @return
     */
    @Override
    public SessionAcquireResult query(String appId, String sessionId) {
        log.info("Accept query session appId={}  sessionId={} ", appId, sessionId);
        var optional = sessionRepository.findByAppIdAndSessionId(appId, sessionId);
        if (optional.isEmpty()) {
            throw new DigitalHumanCommonException("Session not existed");
        }
        var sessionModel = optional.get();
        var session = CloudSession.from(sessionModel);
        return SessionAcquireResult.builder()
                .sessionId(session.getId())
                .sessionToken(session.getToken())
                .status(session.getStatus())
                .character(session.getCharacterImage())
                .parameters(session.getParameters())
                .createTime(DateTimeFormatter.ISO_OFFSET_DATE_TIME.format(session.getCreateTime()))
                .updateTime(DateTimeFormatter.ISO_OFFSET_DATE_TIME.format(session.getUpdateTime()))
                .rtcConnection(session.getRtcSession().getClientConnection()).build();
    }

    /**
     * @param appId
     * @param sessionId
     * @param pageNo
     * @param pageSize
     * @return
     */
    @Override
    public PageResponse<SessionAcquireResult> list(String appId, String sessionId, Integer pageNo, Integer pageSize) {
        log.info("Accept list sessions appId={}, sessionId={}", appId, sessionId);
        PageRequest pageRequest = PageRequest.of(pageNo - 1, pageSize);
        Page<CloudSessionModel> sessions;
        if (StringUtils.isEmpty(sessionId)) {
            sessions = sessionRepository.findByAppIdOrderByCreateTimeDesc(appId, pageRequest);
        } else {
            sessions = sessionRepository.findAllByAppIdAndSessionIdOrderByCreateTimeDesc(appId, sessionId, pageRequest);
        }
        long totalCount = 0;
        List<SessionAcquireResult> list = Lists.newArrayList();

        if (sessions != null) {
            sessions.forEach(sessionModel -> {
                var session = CloudSession.from(sessionModel);
                list.add(SessionAcquireResult.builder()
                        .sessionId(session.getId())
                        .sessionToken(session.getToken())
                        .status(session.getStatus())
                        .character(session.getCharacterImage())
                        .parameters(session.getParameters())
                        .createTime(DateTimeFormatter.ISO_OFFSET_DATE_TIME.format(session.getCreateTime()))
                        .updateTime(DateTimeFormatter.ISO_OFFSET_DATE_TIME.format(session.getUpdateTime()))
                        .rtcConnection(session.getRtcSession().getClientConnection())
                        .build());
            });
            totalCount = sessions.getTotalElements();
        }
        return PageResponse.success(pageNo, pageSize, totalCount, list);
    }

    @Override
    @TraceMethod(value = "deleteSession", tagParams = {"appId", "sessionId"})
    public void delete(String appId, String sessionId) {
        innerDelete(appId, sessionId, false);
    }

    private void innerDelete(String appId, String sessionId, boolean force) {
        log.debug("Accept delete session appId={} sessionId={} force={}", appId, sessionId, force);
        endStatisticsSession(appId, sessionId);
        var optional = sessionRepository.findByAppIdAndSessionId(appId, sessionId);
        if (optional.isEmpty()) {
            log.error("Though session not existed, return lease anyway, appId={}, sessionId={}", appId, sessionId);
        }
        houseKeeper.returnLease(appId, sessionId);
        if (optional.isEmpty()) {
            throw new DigitalHumanCommonException(404, "Session not existed");
        }
        var session = optional.get();
        if (!force && sessionStatusInvalid(session.getStatus())) {
            log.debug("Session already in closed status, ignore");
            return;
        }
        if (!sessionStatusInvalid(session.getStatus())) {
            session.setUpdateTime(ZonedDateTime.now());
            session.setStatus(SessionStatus.CLOSED);
            session.setToken(UUID.randomUUID().toString());
            sessionRepository.save(session);
        }
        // todo if delete failed
        var response = renderProxyService.close(appId, sessionId);
        log.debug("Render close response={}", TextFormat.shortDebugString(response));
    }

    private boolean sessionStatusInvalid(SessionStatus status) {
        return status == SessionStatus.CLOSED || status == SessionStatus.ERROR;
    }

    @Override
    @TraceMethod(value = "validateToken", tagParams = {"sessionId", "sessionToken"})
    public CloudSession validate(String sessionId, String sessionToken) {
        log.info("Accept validate sessionId={}  sessionToken={} ", sessionId, sessionToken);
        var optional = sessionRepository.findBySessionId(sessionId);
        if (optional.isEmpty()) {
            throw new DigitalHumanCommonException("Invalid sessionId");
        }
        var session = CloudSession.from(optional.get());
        if (!checkToken(session, sessionToken)) {
            throw new DigitalHumanCommonException("Invalid sessionToken");
        }
        if (!checkSessionStatus(session)) {
            throw new DigitalHumanCommonException("Invalid session status");
        }
        return session;
    }

    @Override
    public Map<String, Long> queryActiveSessionStartTimeMis() {

        return sessionStartTimeMap;
    }

    @Override
    public int getAvailableLimit(String appId) {
        AccessApp app = accessControlService.getAppById(appId);

        SpareResponse spareResponse = resourcePoolClient.listSpareByResourceType(
                Collections.singletonList(app.getCharacterImage()));
        // todo min resource pool idle size and app limit
        if (null == spareResponse || spareResponse.getResources().isEmpty()) {
            return 0;
        }

        int leasedNum = houseKeeper.getLeasedNum(appId);
        return app.getResourceQuota().getRoomLimits() - leasedNum;
    }

    private StreamObserver<StreamResponse> responseObserveAdaptor(
            CloudSession session,
            StreamObserver<BaseResponse> cloudResponseObserver,
            Tuple2<String, String> lock) {
        // consume render response
        return new StreamObserver<StreamResponse>() {
            private final AtomicBoolean complete = new AtomicBoolean(false);

            @Override
            public void onNext(StreamResponse value) {
                log.debug("Receive stream response value={} sessionId={}",
                        TextFormat.shortDebugString(value), session.getId());
                var builder = BaseResponse.newBuilder()
                        .setErrorMessage(value.getErrMessage())
                        .setErrorCode(value.getErrCode())
                        .setAck(value.getAck());
                if (value.getBody() != null) {
                    builder.setBody(ResponseBody.newBuilder()
                            .setContent(value.getBody().getContent())
                            .setType(value.getBody().getType())
                            .build());
                }
                cloudResponseObserver.onNext(builder.build());
            }

            @Override
            public void onError(Throwable t) {
                log.warn("Receive error from backend render, reason={} sessionId={}", t.getLocalizedMessage(),
                        session.getId(), t);
                if (!complete.compareAndSet(false, true)) {
                    // todo maybe improve later
                    log.info("sessionId={} has already complete, but some response send from backend, ignore"
                            , session.getId());
                    return;
                }
                endStatisticsSession(session);
                unlock(lock._1, lock._2);
                // todo error code
                cloudResponseObserver.onNext(BaseResponse.newBuilder()
                        .setErrorCode(Error.RENDER_CLOSED.getCode())
                        .setErrorMessage(Error.RENDER_CLOSED.getMessage()).build());
                // close downstream, sdk should close upstream after receive error response
                cloudResponseObserver.onCompleted();
            }

            @Override
            public void onCompleted() {
                log.debug("Receive complete response from backend render for sessionId={}", session.getId());
                if (!complete.compareAndSet(false, true)) {
                    log.info("Stream already receive on error request, ignore on complete");
                    return;
                }
                endStatisticsSession(session);
                unlock(lock._1, lock._2);
                cloudResponseObserver.onCompleted();
            }
        };
    }

    /**
     * @param responseObserver render response observer
     * @return
     */
    @Override
    public Either<Status, StreamObserver<TextFragment>> sendText(SessionContext sessionContext,
                                                                 StreamObserver<BaseResponse> responseObserver) {
        return sendText(sessionContext, responseObserver, false);
    }

    private Either<Status, StreamObserver<TextFragment>> sendText(SessionContext sessionContext,
                                                                  StreamObserver<BaseResponse> responseObserver,
                                                                  Boolean needHeartbeatBody) {
        var session = sessionContext.getSession();
        var sessionId = session.getId();
        Optional<String> lock = tryLock(sessionId);
        if (lock.isPresent()) {
            return renderProxyService.sendText(session,
                            responseObserveAdaptor(session, responseObserver, Tuple.of(sessionId, lock.get())),
                            Collections.emptyMap())
                    .mapLeft(t -> {
                        unlock(sessionId, lock.get());
                        return Status.INTERNAL.withCause(t);
                    })
                    .map(s -> new StreamObserver<TextFragment>() {
                        // cloud request observer
                        @Override
                        public void onNext(TextFragment value) {
                            houseKeeper.bark(session.getAppId(), sessionId);
                            if (value.getContent().isBlank() && value.getAnimojiId().isBlank() 
                                && value.getAudio().isEmpty() && value.getRefresh().isBlank()) {
                                log.debug("Receive heartbeat for sessionId={}", sessionId);
                                // do not forward to backend render
                                BaseResponse.Builder builder = BaseResponse.newBuilder()
                                        .setAck(value.getBaseRequest().getSequenceNum())
                                        .setErrorCode(0)
                                        .setErrorMessage("heartbeat success");
                                // compatible with DhClient since dhClient use body to distinguish heartbeat message
                                // with normal message
                                if (needHeartbeatBody) {
                                    builder.setBody(ResponseBody.newBuilder()
                                            .setType("heartbeat"));
                                }
                                responseObserver.onNext(builder.build());
                                return;
                            }
                            s.onNext(com.baidu.acg.digitalhuman.render.proxy.grpc.message.TextFragment.newBuilder()
                                    .setContent(value.getContent())
                                    .setAnimojiId(value.getAnimojiId())
                                    .setStreamRequest(StreamRequest.newBuilder()
                                            .setSequence((int) value.getBaseRequest().getSequenceNum())
                                            .setTimestamp(value.getBaseRequest().getSendTimestamp()))
                                    .setAudio(value.getAudio())
                                    .setAudioStart(value.getAudioStart())
                                    .setAudioEnd(value.getAudioEnd())
                                    .setInsert(value.getInsert())
                                    .setRefresh(value.getRefresh())
                                    .setDelayed(value.getDelayed())
                                    .build());
                        }

                        @Override
                        public void onError(Throwable t) {
                            s.onError(t);
                        }

                        @Override
                        public void onCompleted() {
                            s.onCompleted();

                        }
                    });
        } else {
            log.info("Fail to open send text request, since sessionId={} is busy, lock failed", sessionId);
            return Either.left(Status.ALREADY_EXISTS.withDescription("failed to lock session on zk"));
        }
    }

    @Override
    public List<AppResourceVO> listAppResource(List<String> appIds){
        List<AppResourceVO> appResourceVOList = new ArrayList<>();
        for(String appId: appIds) {
            AccessApp app = accessControlService.getAppById(appId);
            appResourceVOList.add(AppResourceVO.builder().appId(appId)
                    .resourceType(app.getCharacterImage())
                    .resourceQuota(app.getResourceQuota().getRoomLimits())
                    .usedResources(houseKeeper.getAppUsedResources(appId))
                    .build());
        }
        return appResourceVOList;
    }

    @Override
    public StreamObserver<CloudCommand> disposableSession(StreamObserver<CloudCommandResponse> responseObserver) {
        return new StreamObserver<>() {
            private DisposableSessionStage stage = INIT;
            private StreamObserver<TextFragment> textFragmentStreamObserver;
            private String appId;
            private String sessionId;

            @Override
            public void onNext(CloudCommand value) {
                if (stage == INIT && value.getBodyCase() == CloudCommand.BodyCase.OPEN_REQUEST) {
                    OpenRequest openRequest = value.getOpenRequest();
                    try {
                        Tuple2<SessionAcquireResult, CloudSession> acquire = acquire(openRequest.getAppId(),
                                openRequest);
                        responseObserver.onNext(CloudCommandResponse.newBuilder()
                                .setOpenResponse(acquire._1.toOpenResponse())
                                .build());
                        sessionId = acquire._1.getSessionId();
                        appId = acquire._2.getAppId();
                        stage = ONGOING;
                        SessionContext mockContext = SessionContext.builder()
                                .session(acquire._2)
                                .build();
                        Either<Status, StreamObserver<TextFragment>> either = sendText(mockContext,
                                new StreamObserver<BaseResponse>() {
                                    @Override
                                    public void onNext(BaseResponse value) {
                                        responseObserver.onNext(CloudCommandResponse.newBuilder()
                                                .setBaseResponse(value)
                                                .build());
                                    }

                                    @Override
                                    public void onError(Throwable t) {
                                        responseObserver.onError(t);
                                    }

                                    @Override
                                    public void onCompleted() {
                                        responseObserver.onCompleted();
                                    }
                                }, true);
                        textFragmentStreamObserver =
                                either.getOrElseThrow(s -> new RuntimeException(s.getDescription()));
                    } catch (Throwable ex) {
                        if (ex instanceof DigitalHumanCommonException) {
                            // open failed for "expected error"
                            log.debug("Fail to acquire session for open request={}",
                                    TextFormat.shortDebugString(openRequest), ex);
                        } else {
                            // open failed for unknown error, we should log and fix it if happened
                            log.error("Fail to acquire session for open request={}",
                                    TextFormat.shortDebugString(openRequest), ex);
                        }
                        Pair<Integer, String> error = Error.extractErrorInfo(ex);
                        OpenResponse openResponse = OpenResponse.newBuilder()
                                .setErrorInfo(ErrorInfo.newBuilder()
                                        .setCode(error.getLeft())
                                        .setMessage(error.getRight())
                                        .build())
                                .build();
                        // we close the downstream and will ignore all latter message from upstream
                        responseObserver.onNext(CloudCommandResponse.newBuilder()
                                .setOpenResponse(openResponse)
                                .build());
                        responseObserver.onCompleted();
                        stage = CLOSE;
                    }
                } else if (stage == ONGOING && value.getBodyCase() == CloudCommand.BodyCase.TEXT_FRAGMENT) {
                    TextFragment textFragment = value.getTextFragment();
                    textFragmentStreamObserver.onNext(textFragment);
                } else if (stage == CLOSE) {
                    log.info("Session in close state ignore message={}", TextFormat.shortDebugString(value));
                } else {
                    log.error("Session in stage={} and receive unexpected message={} from upstream, just ignore",
                            stage, TextFormat.shortDebugString(value));
                }
            }

            @Override
            public void onError(Throwable t) {
                log.debug("Receive on error from frontend, session stage={}", stage);
                if (stage == ONGOING) {
                    innerDelete(appId, sessionId, true);
                }
                stage = CLOSE;
                textFragmentStreamObserver.onError(t);
            }

            @Override
            public void onCompleted() {
                log.debug("Receive complete from frontend, session stage={}", stage);
                if (stage == ONGOING) {
                    innerDelete(appId, sessionId, true);
                }
                stage = CLOSE;
                textFragmentStreamObserver.onCompleted();
            }
        };
    }

    private Optional<String> tryLock(String sessionId) {
        return lockService.tryLock(sessionId);
    }

    private void unlock(String sessionId, String token) {
        if (!lockService.unlock(sessionId, token)) {
            log.error("Fail to unlock sessionId={}", sessionId);
        }
    }

    private boolean checkToken(CloudSession session, String tokenToValidate) {
        return session.getToken().equals(tokenToValidate);
    }

    private boolean checkSessionStatus(CloudSession session) {
        return session.getStatus() != ERROR && session.getStatus() != CLOSED;
    }


    private void startStatisticsSession(AccessApp app, String sessionId) {
        Try.run(() -> {
                    if (null != sessionStartTimeMap.putIfAbsent(sessionId, System.currentTimeMillis())) {
                        log.error("Current session has already been statisticsed, sessionId:{}", sessionId);
                        return;
                    }
                    var optional = sessionRepository.findBySessionId(sessionId);
                    if (optional.isEmpty()) {
                        log.error("cloud session is empty");
                        throw new DigitalHumanCommonException("cloud session is empty");
                    }
                    String characterImage = app.getCharacterImage();
                    if (optional.get().getCharacterImage() != null) {
                        characterImage = optional.get().getCharacterImage();
                        log.debug("characterImage is ={}", characterImage);
                    }
                    final String finalCharacterImage = characterImage;
                    String appId = app.getAppId();
                    String resourceType = Try.of(() -> finalCharacterImage.split("-")[0])
                            .getOrElse(finalCharacterImage);
                    String key = appId + "_" + finalCharacterImage + "_" + resourceType;
                    currentSessionMap.computeIfAbsent(key, k -> new AtomicLong(0));
                    AtomicLong result = currentSessionMap.get(key);
                    result.getAndUpdate(x -> x + 1);
                    Gauge gauge = Gauge.builder(StatisticConstant.CURRENT_SESSIONS_NAME, result,
                                    AtomicLong::get).tag("appId", appId).tag("characterImage", finalCharacterImage)
                            .tag("resourceType", resourceType)
                            .register(meterRegistry);
                    log.debug("Start statistics session metric,key:{},gauge.value={},sessionId:{}"
                            , key, gauge.value(), sessionId);
                })
                .onFailure(t -> log.warn("Fail to start statistics session metric", t));

    }

    private void endStatisticsSession(CloudSession session) {
        endStatisticsSession(session.getAppId(), session.getId());
    }

    private void endStatisticsSession(String appId, String sessionId) {
        Try.run(() -> {
                    sessionStartTimeMap.computeIfPresent(sessionId, new BiFunction<String, Long, Long>() {
                        @Override
                        public Long apply(String sessionKey, Long sessionStartTime) {
                            sessionStartTimeMap.remove(sessionKey);
                            AccessApp app = accessControlService.getAppById(appId);
                            var optional = sessionRepository.findBySessionId(sessionId);
                            if (optional.isEmpty()) {
                                log.error("cloud session is empty");
                                throw new DigitalHumanCommonException("cloud session is empty");
                            }
                            String characterImage = app.getCharacterImage();
                            if (optional.get().getCharacterImage() != null) {
                                characterImage = optional.get().getCharacterImage();
                                log.debug("characterImage is ={}", characterImage);
                            }
                            final String finalCharacterImage = characterImage;
                            String resourceType = Try.of(() -> finalCharacterImage.split("-")[0])
                                    .getOrElse(finalCharacterImage);
                            String key = appId + "_" + finalCharacterImage + "_" + resourceType;
                            AtomicLong result = currentSessionMap.get(key);
                            if (result != null && result.get() > 0) {
                                result.getAndUpdate(x -> x - 1);
                                Gauge gauge = Gauge.builder(StatisticConstant.CURRENT_SESSIONS_NAME
                                                , result, AtomicLong::get).tag("appId", appId)
                                        .tag("characterImage", finalCharacterImage)
                                        .tag("resourceType", resourceType)
                                        .register(meterRegistry);
                                log.debug("After decrease current session,key:{}, gaugeMap.value={},sessionId:{}"
                                        , key, gauge.value(), sessionId);
                            }
                            Timer timer = Timer.builder(StatisticConstant.SESSION_LATENCY_NAME)
                                    .publishPercentileHistogram()
                                    .minimumExpectedValue(Duration.ofMillis(1))
                                    .maximumExpectedValue(Duration
                                            .ofMillis(StatisticConstant.SESSION_LATENCY_NAME_MAXVALUE))
                                    .tag("appId", appId).tag("characterImage", finalCharacterImage)
                                    .tag("resourceType", resourceType)
                                    .register(meterRegistry);
                            timer.record(System.currentTimeMillis() - sessionStartTime, TimeUnit.MILLISECONDS);
                            return null;
                        }
                    });
                })
                .onFailure(t -> log.warn("Fail to decrease statistics session metric", t));

    }
}
