package com.baidu.acg.piat.digitalhuman.cloud.service;

import io.vavr.control.Try;

import java.util.function.BiConsumer;

public interface HouseKeeper {

    /**
     * @param appId     app id
     * @param sessionId session id
     * @param character character
     * @param appLimit  app limit
     * @param maxIdle   max idle
     * @return
     */
    // 资源分配： 为指定应用和绘画分配数字资源
    Try<Void> lease(String appId, String sessionId, String character, int appLimit, int maxIdle);
    // 释放资源
    void returnLease(String appId, String sessionId);
    // 活跃性检查方法
    // 心跳机制、超时重制等
    void bark(String appId, String sessionId);

    /**
     * Get num of room in using
     *
     * @param appId app id
     * @return num
     */

    int getLeasedNum(String appId);

    /**
     * @param consumer first parameter appId, second parameter sessionId
     */
    void addShutdownSessionHook(BiConsumer<String, String> consumer);

    /**
     * @param startHook first parameter appId, second parameter sessionId, start parameters
     */
    void addStartSessionHook(StartHook startHook);

    int getAppUsedResources(String appId);

    interface StartHook {
        void start(String appId, String sessionId);
    }
}
/*
     这个接口的设计灵感来自于酒店管家 (HouseKeeper) 的概念：
     lease() - 租赁房间（分配资源）
     returnLease() - 退房（释放资源）
     bark() - 敲门提醒（会话活跃检查）
     getLeasedNum() - 查看已租房间数量
     Hook 机制 - 房间服务回调
 */