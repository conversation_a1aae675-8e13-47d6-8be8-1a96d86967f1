// Copyright (C) 2020 Baidu Inc. All rights reserved.
package com.baidu.acg.piat.digitalhuman.cloud.persistence;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;

import com.baidu.acg.piat.digitalhuman.common.cloud.CloudRtcConnection;

/**
 * CloudRtcConnectionModel
 *
 * <AUTHOR> (<EMAIL>)
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity(name = "cloud_rtc_connection")
public class CloudRtcConnectionModel {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    private String rtcServerUrl;
    private String appId;
    private String roomName;
    private String clientId;
    private String clientToken;
    private String feedId;

    public static CloudRtcConnectionModel from(CloudRtcConnection connection) {
        return CloudRtcConnectionModel.builder()
                .rtcServerUrl(connection.getRtcServerUrl())
                .appId(connection.getAppId())
                .roomName(connection.getRoomName())
                .clientId(connection.getClientId())
                .clientToken(connection.getClientToken())
                .feedId(StringUtils.isEmpty(connection.getFeedId()) ? StringUtils.EMPTY : connection.getFeedId())
                .build();
    }

    public CloudRtcConnection toCloudRtcConnection() {
        return CloudRtcConnection.builder()
                .rtcServerUrl(this.getRtcServerUrl())
                .appId(this.getAppId())
                .roomName(this.getRoomName())
                .clientId(this.getClientId())
                .clientToken(this.getClientToken())
                .feedId(StringUtils.isEmpty(this.getFeedId()) ? null : this.getFeedId())
                .build();
    }

    public void merge(CloudRtcConnection connection) {
        if (StringUtils.isNotEmpty(connection.getRtcServerUrl())) {
            this.rtcServerUrl = connection.getRtcServerUrl();
        }
        if (StringUtils.isNotEmpty(connection.getAppId())) {
            this.appId = connection.getAppId();
        }
        if (StringUtils.isNotEmpty(connection.getRoomName())) {
            this.roomName = connection.getRoomName();
        }
        if (StringUtils.isNotEmpty(connection.getClientId())) {
            this.clientId = connection.getClientId();
        }
        if (StringUtils.isNotEmpty(connection.getClientToken())) {
            this.clientToken = connection.getClientToken();
        }
        if (StringUtils.isNotEmpty(connection.getFeedId())) {
            this.feedId = connection.getFeedId();
        }
    }
}
