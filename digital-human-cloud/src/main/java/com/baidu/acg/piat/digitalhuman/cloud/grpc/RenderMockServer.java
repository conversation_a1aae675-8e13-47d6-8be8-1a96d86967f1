package com.baidu.acg.piat.digitalhuman.cloud.grpc;

import com.google.protobuf.TextFormat;
import io.grpc.Server;
import io.grpc.netty.shaded.io.grpc.netty.NettyServerBuilder;
import io.grpc.stub.StreamObserver;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Profile;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

import com.baidu.acg.digitalhuman.render.proxy.grpc.message.BaseResponse;
import com.baidu.acg.digitalhuman.render.proxy.grpc.message.CloseRequest;
import com.baidu.acg.digitalhuman.render.proxy.grpc.message.OpenRequest;
import com.baidu.acg.digitalhuman.render.proxy.grpc.message.StreamResponse;
import com.baidu.acg.digitalhuman.render.proxy.grpc.message.TextFragment;
import com.baidu.acg.digitalhuman.render.proxy.grpc.service.RenderProxyGrpc;
import com.baidu.acg.piat.digitalhuman.common.hypervisor.request.HeartBeatRequest;
import com.baidu.acg.piat.digitalhuman.common.hypervisor.request.HypervisorRequest;
import com.baidu.acg.piat.digitalhuman.common.hypervisor.response.HypervisorResponse;
import com.baidu.acg.piat.digitalhuman.common.hypervisor.Status;
import com.baidu.acg.piat.digitalhuman.common.resource.ResourceInstance;
import com.baidu.acg.piat.digitalhuman.common.cloud.Selectors;
import com.baidu.acg.piat.digitalhuman.resource.pool.client.ResourcePoolClient;
import com.baidu.acg.piat.digitalhuman.resource.pool.client.ResourcePoolClientException;
import com.baidu.acg.piat.digitalhuman.session.route.client.model.RouteContext;
import com.baidu.acg.piat.digitalhuman.session.route.client.service.SessionRouteService;

@Slf4j
@RestController
@Profile("mock")
public class RenderMockServer {

    private final ResourcePoolClient resourcePoolClient;
    private Server server;
    private ScheduledThreadPoolExecutor executor;

    private final SessionRouteService routeService;

    private Selectors.Labels labels =
            new Selectors.Labels(Map.of("version", "1.0.10", "type", "stable", "create", "jianbin"));

    public RenderMockServer(ResourcePoolClient resourcePoolClient, SessionRouteService routeService) {
        this.resourcePoolClient = resourcePoolClient;
        this.routeService = routeService;
    }

    @PostConstruct
    public void init() throws IOException {
        server = NettyServerBuilder.forPort(9999)
                .addService(new RenderMockService(routeService))
                .permitKeepAliveWithoutCalls(true)
                .build();
        server.start();
        log.info("Render mock server start");

        executor = new ScheduledThreadPoolExecutor(5);
        executor.scheduleWithFixedDelay(() -> {
            try {
                resourcePoolClient.heartBeat("http://localhost:8081", "SYSTEM", Status.IDLE, labels);
            } catch (ResourcePoolClientException e) {
                log.debug("Failed heartbeat to resource pool", e);
            }
        }, 1000, 6000, TimeUnit.MILLISECONDS);
    }

    @PreDestroy
    public void destroy() throws InterruptedException {
        server.shutdownNow();
        executor.shutdownNow();
        log.info("Render mock server shutdown");
    }

    @PostMapping("/api/digitalhuman/hypervisor/resource/acquire")
    public HypervisorResponse acquire(HypervisorRequest request) {
        log.debug("Receive acquire request={}", request);
        Map<String, Integer> ports = new HashMap<>();
        ports.put("harry", 9999);
        return HypervisorResponse.succeed(ResourceInstance.builder()
                        .host("http://localhost:8081")
                        .ip("localhost")
                        .ports(ports)
                        .build(),
                HeartBeatRequest.builder()
                        .host("http://localhost:8081")
                        .resourceType("SYSTEM")
                        .status(Status.IDLE)
                        .labels(labels)
                        .build()
        );
    }

    public static class RenderMockService extends RenderProxyGrpc.RenderProxyImplBase {

        private SessionRouteService routeService;

        public RenderMockService(SessionRouteService sessionRouteService) {
            this.routeService = sessionRouteService;
        }

        @Override
        public void open(OpenRequest request, StreamObserver<BaseResponse> responseObserver) {
            log.debug("Receive open request={}", TextFormat.shortDebugString(request));
            Map<String, Integer> ports = new HashMap<>();
            ports.put("harry", 9999);
            routeService.register(request.getSession().getAppId(),
                    request.getSession().getId(),
                    RouteContext.builder()
                            .resourceInstance(ResourceInstance.builder()
                                    .host("http://localhost:8081")
                                    .ip("localhost")
                                    .ports(ports)
                                    .build())
                            .build()
            );

            responseObserver.onNext(BaseResponse.newBuilder()
                    .setErrCode(0)
                    .setErrMessage("Success")
                    .build());
            responseObserver.onCompleted();
        }

        @Override
        public void close(CloseRequest request, StreamObserver<BaseResponse> responseObserver) {
            log.debug("Receive close request={}", TextFormat.shortDebugString(request));
            routeService.remove(request.getSession().getAppId(), request.getSession().getId());
            responseObserver.onNext(BaseResponse.newBuilder()
                    .setErrCode(0)
                    .setErrMessage("Success")
                    .build());
            responseObserver.onCompleted();
        }




        @Override
        public StreamObserver<TextFragment> text(StreamObserver<StreamResponse> responseObserver) {
            return super.text(responseObserver);
        }
    }
}
