// Copyright (C) 2019 Baidu Inc. All rights reserved.
package com.baidu.acg.piat.digitalhuman.cloud.controller;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

import com.baidu.acg.piat.digitalhuman.common.exception.DigitalHumanCommonException;
import com.baidu.acg.piat.digitalhuman.session.route.client.model.RouteContext;
import com.baidu.acg.piat.digitalhuman.session.route.client.service.SessionRouteService;

/**
 * WatchController
 *
 * <AUTHOR>
 * @since 2019-10-10
 */
@Slf4j
@RestController
@RequestMapping("/api/digitalhuman/v1/watch")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class WatchController {

    private final SessionRouteService sessionRouteService;

    @GetMapping("/session/all")
    public Map<String, Map<String, RouteContext>> listAllSessions() {
        return sessionRouteService.findAll()
                .onFailure(t -> {
                    throw new DigitalHumanCommonException("cannot find all session", t);
                })
                .get();
    }
}
