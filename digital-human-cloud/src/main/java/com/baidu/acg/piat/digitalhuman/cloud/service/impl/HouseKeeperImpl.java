package com.baidu.acg.piat.digitalhuman.cloud.service.impl;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.Sets;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.machinezoo.noexception.Exceptions;
import io.vavr.Tuple;
import io.vavr.Tuple2;
import io.vavr.control.Try;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.util.CollectionUtils;
import org.apache.commons.lang3.RandomUtils;
import org.apache.curator.framework.CuratorFramework;
import org.apache.curator.framework.recipes.cache.ChildData;
import org.apache.curator.framework.recipes.cache.TreeCache;
import org.apache.curator.framework.recipes.leader.LeaderSelector;
import org.apache.curator.framework.recipes.leader.LeaderSelectorListenerAdapter;
import org.apache.curator.framework.recipes.locks.InterProcessSemaphoreMutex;
import org.apache.curator.framework.state.ConnectionState;
import org.apache.zookeeper.KeeperException;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Date;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.TreeMap;
import java.util.TreeSet;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executor;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.RejectedExecutionException;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.BiConsumer;
import java.util.stream.Collectors;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;

import com.baidu.acg.piat.digitalhuman.cloud.service.HouseKeeper;
import com.baidu.acg.piat.digitalhuman.common.exception.DigitalHumanCommonException;
import com.baidu.acg.piat.digitalhuman.common.exception.Error;
import com.baidu.acg.piat.digitalhuman.session.route.client.config.CuratorConfig.ZkConfig;
import com.baidu.acg.piat.digitalhuman.session.route.client.model.RouteContext;
import com.baidu.acg.piat.digitalhuman.session.route.client.service.RouteChangeListener;
import com.baidu.acg.piat.digitalhuman.session.route.client.service.SessionRouteService;
import com.baidu.acg.piat.digitalhuman.tracer.annotation.TraceMethod;


@Slf4j
@Service
public class HouseKeeperImpl implements HouseKeeper {

    private static final Integer EXECUTOR_CORE_POOL_SIZE = 10;

    private static final Integer EXECUTOR_MAX_POOL_SIZE = 10;

    private static final Integer EXECUTOR_MAX_TASK_SIZE = 100;

    private static final Integer EXECUTOR_KEEP_ALIVE_TIME = 0;

    private static final String SESSION_PREFIX = "/sessions";

    private static final String LOCK_PREFIX = "/lock";

    private final CuratorFramework curatorFramework;

    private final SessionRouteService routeService;

    private final ZkConfig zkConfig;

    private TreeCache sessions;

    private final Executor executor = new ThreadPoolExecutor(
            EXECUTOR_CORE_POOL_SIZE,
            EXECUTOR_MAX_POOL_SIZE,
            EXECUTOR_KEEP_ALIVE_TIME,
            TimeUnit.MILLISECONDS,
            new LinkedBlockingQueue<>(EXECUTOR_MAX_TASK_SIZE),
            new ThreadFactoryBuilder().setNameFormat("house-keeper-executor-%d").build());

    private ScheduledExecutorService activeTimeExecutor = Executors.newScheduledThreadPool(10);

    private LeaderSelector leaderSelector;

    private List<StartHook> startConsumers = new ArrayList<>();

    private List<BiConsumer<String, String>> shutdownConsumers = new ArrayList<>();

    private final Map<Tuple2<String, String>, NodeData> sessionActiveTime = new ConcurrentHashMap<>();

    private Date lastCollectingTime = new Date();

    private Date preCollectingTime = new Date();

    public HouseKeeperImpl(CuratorFramework curatorFramework, SessionRouteService routeService, ZkConfig zkConfig) {
        this.curatorFramework = curatorFramework;
        this.routeService = routeService;
        this.zkConfig = zkConfig;
    }

    @PostConstruct
    void init() throws Exception {
        touchPath(SESSION_PREFIX, 3, new byte[0]);
        touchPath(LOCK_PREFIX, 3, new byte[0]);
        sessions = new TreeCache(curatorFramework, SESSION_PREFIX);
        sessions.start();
        sessions.getListenable().addListener((client, event) -> {
            if (event.getData() != null) {
                log.debug("Receive event type={} for path={}", event.getType(), event.getData().getPath());
            }
            // todo 处理一些网络异常的event， 在这种情况下， cache的数据存在不一致的风险
        }, Executors.newSingleThreadExecutor());
        routeService.addRouteChangeListener(event -> {
            log.debug("Add route change event for housekeeper, event={}", event);
            consumeRouteRemovedEvent(event);
        });
        leaderSelector = new LeaderSelector(curatorFramework, "/house-keeper/" + zkConfig.getNamespace(),
                new LeaderSelectorListenerAdapter() {
                    @Override
                    public void takeLeadership(CuratorFramework client) throws Exception {
                        log.info("Acquire leadership, go go go...");
                        Thread.sleep(1_000_000_000_000L);
                        log.info("Lost leadership for housekeeper");
                    }

                    @Override
                    public void stateChanged(CuratorFramework client, ConnectionState newState) {
                        log.warn("Changed zk connection state, newState={}", newState);
                        super.stateChanged(client, newState);
                    }
                });
        leaderSelector.autoRequeue();
        leaderSelector.start();
        // todo use config to control reporting period
        Executors.newSingleThreadScheduledExecutor()
                .scheduleAtFixedRate(this::collectSessionActiveTime, 20, 20, TimeUnit.SECONDS);
        Executors.newSingleThreadScheduledExecutor()
                .scheduleAtFixedRate(this::pickAndShutdownIncorrectSession, 20, 20, TimeUnit.SECONDS);
    }

    @PreDestroy
    private void shutdown() {
        sessions.close();
        leaderSelector.close();
        activeTimeExecutor.shutdown();
    }


    private void pickAndShutdownIncorrectSession() {
        if (!leaderSelector.hasLeadership()) {
            log.debug("Ignore pick inactive session since i am not leader");
            return;
        }

        ExecutorService tempExecutor = Executors.newSingleThreadExecutor();
        CompletableFuture<Void> checkJob = CompletableFuture.supplyAsync(() -> {
            log.debug("Begin to pick inactive sessions");

            Map<String, Map<String, RouteContext>> all = routeService.findAll().get();
            Set<Tuple2<String, String>> appSessionSetInRoute =
                    all.entrySet().stream().flatMap(a -> a.getValue().keySet().stream()
                            .map(s -> Tuple.of(a.getKey(), s))).collect(Collectors.toSet());

            Set<Tuple2<String, String>> expectAppSessionSet = new TreeSet<>();
            Set<Tuple2<String, String>> openingSessions = new TreeSet<>();
            Map<String, Tuple2<AtomicInteger, AtomicInteger>> characterSessions = new TreeMap<>();
            Map<String, ChildData> apps = sessions.getCurrentChildren(SESSION_PREFIX);
            for (String appId : apps.keySet()) {
                String appFullPath = String.format("%s/%s", SESSION_PREFIX, appId);
                Map<String, ChildData> sessionsInApp = sessions.getCurrentChildren(appFullPath);
                sessionsInApp.forEach((sessionId, sessionData) -> {
                    try {
                        NodeData nodeData = NodeData.fromBytes(sessionData.getData());
                        var expireTime = new Date(System.currentTimeMillis() - nodeData.getMaxIdleInSecond() * 1000);
                        if (nodeData.activeTime.before(expireTime)) {
                            shutdownConsumers.forEach(consumer -> {
                                log.debug("Push shutdown in queue which in already inactive for appId={} sessionId={}",
                                        appId, sessionId);
                                executor.execute(() -> {
                                    log.debug("Pop shutdown from queue for appId={} sessionId={}", appId, sessionId);
                                    consumer.accept(appId, sessionId);
                                });
                            });
                        }
                        Tuple2<String, String> appSessionTuple = Tuple.of(appId, sessionId);
                        expectAppSessionSet.add(appSessionTuple);
                        if (nodeData.isOrigin()) {
                            openingSessions.add(appSessionTuple);
                        }

                        String configPath = String.format("%s/%s/config", appFullPath, sessionId);
                        ChildData configData = sessions.getCurrentData(configPath);
                        SessionConfig sessionConfig = SessionConfig.fromBytes(configData.getData());
                        var counter = characterSessions.computeIfAbsent(sessionConfig.getCharacter(), (k) ->
                                Tuple.of(new AtomicInteger(0), new AtomicInteger(0)));
                        counter._1.incrementAndGet();

                        if (appSessionSetInRoute.contains(appSessionTuple)) {
                            counter._2.incrementAndGet();
                        }


                    } catch (IOException e) {
                        log.error("Fail to parse data from zk for the path={}", sessionData.getPath());
                    } catch (RejectedExecutionException e) {
                        log.error("Fail to submit correction task to executor, since the executor is full");
                    }
                });
            }

            log.debug("Statistics total expected={} current={}", expectAppSessionSet.size() - openingSessions.size(),
                    appSessionSetInRoute.size());
            characterSessions.forEach((key, value)
                    -> log.debug("Statistics render {}={} current={}", key, value._1, value._2));

            // find sessions in route but not in expect-sessions
            Sets.difference(appSessionSetInRoute, expectAppSessionSet).forEach(t -> {
                shutdownConsumers.forEach(consumer -> {
                    log.debug("Push shutdown in queue which should closed but find in route appId={} sessionId={}",
                            t._1, t._2);
                    executor.execute(() -> {
                        log.debug("Pop shutdown from queue for appId={} sessionId={}", t._1, t._2);
                        consumer.accept(t._1, t._2);
                    });
                });
            });

            // find expect sessions that are not in route
            // TODO: exponential backoff
            Sets.difference(expectAppSessionSet, appSessionSetInRoute).forEach(t -> {
                startConsumers.forEach(hook -> {
                    if (openingSessions.contains(t)) {
                        log.debug("Ignore session recovery since the session is in opening state appId={} sessionId={}",
                                t._1, t._2);
                        return;
                    }
                    log.debug("Push hook in queue, session is expected but not in route, appId={} sessionId={}",
                            t._1, t._2);
                    executor.execute(() -> {
                        log.debug("Pop hook from queue, session is expected but not in route, appId={} sessionId={}",
                                t._1, t._2);
                        hook.start(t._1, t._2);
                    });
                });
            });

            log.debug("Finish one round of inactive session pick");
            return null;
        }, tempExecutor);
        // no more new task will be submitted
        tempExecutor.shutdown();

        try {
            checkJob.get(30, TimeUnit.SECONDS);
        } catch (Exception e) {
            log.error("Failed to pick the incorrect sessions, force stop", e);
            tempExecutor.shutdownNow();
            if (RandomUtils.nextInt(0, 100) < 37) {
                log.info("Release leadership since i cannot get my job done");
                leaderSelector.interruptLeadership();
            }
        }
    }

    @Override
    public int getAppUsedResources(String appId) {
        String appPath = SESSION_PREFIX + "/" + appId;
        Map<String, ChildData> app = sessions.getCurrentChildren(appPath);
        if (CollectionUtils.isEmpty(app)) {
            log.info("Failed to get current children by appId={}", appId);
            return 0;
        } else {
            return app.size();
        }
    }

    private void collectSessionActiveTime() {
        try {
            preCollectingTime = lastCollectingTime;
            Iterator<Map.Entry<Tuple2<String, String>, NodeData>> iterator = sessionActiveTime.entrySet().iterator();
            while (iterator.hasNext()) {
                Map.Entry<Tuple2<String, String>, NodeData> entry = iterator.next();
                Tuple2<String, String> appSession = entry.getKey();
                NodeData activeTime = entry.getValue();
                if (activeTime.getActiveTime().after(lastCollectingTime)) {
                    activeTimeExecutor.execute(() -> updateSessionActiveTime(appSession._1, appSession._2));
                } else {
                    iterator.remove();
                }
            }

            lastCollectingTime = new Date();
        } catch (Exception ex) {
            log.error("Fail in collect session active time", ex);
        }
    }

    private void updateSessionActiveTime(String appId, String sessionId) {
        var activeTime = sessionActiveTime.get(Tuple.of(appId, sessionId));
        if (activeTime == null) {
            // maybe session is already closed
            return;
        }
        if (activeTime.getActiveTime().before(preCollectingTime)) {
            // 如果多次跳过的话， 会造成active time一直得不到更新， 造成了会话被强行终止
            // 但是不跳过的话， 会赶不上进度， 越积越多， 最终也是被终止， 因此比较之下， 跳过更合适
            log.warn("Active time before last collecting time, appId={} sessionId={} activeTime={} " +
                    "preCollectingTime={} lastCollectingTime={} " +
                    ",ignore this round", appId, sessionId, activeTime, preCollectingTime, lastCollectingTime);
            return;
        }
        var now = Instant.now();
        if (!updatePath(String.format("%s/%s/%s", SESSION_PREFIX, appId, sessionId), 2,
                Exceptions.sneak().get(() -> NodeData.toBytes(NodeData.update(activeTime.getMaxIdleInSecond()))))) {
            log.warn("Fail to update session active time for appId={} sessionId={}", appId, sessionId);
        } else {
            log.debug("Success update active time for appId={} sessionId={} activeTime={}", appId, sessionId, now);
        }
    }

    private void consumeRouteRemovedEvent(RouteChangeListener.ChangeEvent event) {
        if (event.getType() != RouteChangeListener.Type.REMOVED) {
            log.debug("Receive route event={}, ignore", event);
            return;
        }
        Map<String, ChildData> appSessions = this.sessions.getCurrentChildren(SESSION_PREFIX + "/" + event.getAppId());
        if (appSessions == null || appSessions.get(event.getSessionId()) == null) {
            log.debug("Session has already closed, ignore route remove event for the event={}", event);
            return;
        }

        try {
            NodeData nodeData = NodeData.fromBytes(appSessions.get(event.getSessionId()).getData());
            if (nodeData.isOrigin()) {
                log.debug("Session is in opening state and maybe open failed, ignore session remove event");
                return;
            }
        } catch (IOException e) {
            log.error("Failed to parse date for the event={}", event, e);
            return;
        }


        startConsumers.forEach(consumer -> executor.execute(() -> {
            if (!leaderSelector.hasLeadership()) {
                log.debug("Ignore route change event since I am not leader");
                return;
            }


            // get start parameters
            ChildData configData = this.sessions.getCurrentData(String.format("%s/%s/%s/config", SESSION_PREFIX,
                    event.getAppId(), event.getSessionId()));
            SessionConfig sessionConfig;
            try {
                sessionConfig = SessionConfig.fromBytes(configData.getData());
            } catch (Exception e) {
                log.error("Failed to parse start config for appId={} sessionId={}", event.getAppId(),
                        event.getSessionId());
                return;
            }

            consumer.start(event.getAppId(), event.getSessionId());
        }));
        log.debug("Finish consume route change event");
    }

    @Override
    public void addShutdownSessionHook(BiConsumer<String, String> consumer) {
        shutdownConsumers = ImmutableList.<BiConsumer<String, String>>builder()
                .addAll(shutdownConsumers).add(consumer).build();
    }

    @Override
    public void addStartSessionHook(StartHook consumer) {
        startConsumers = ImmutableList.<StartHook>builder()
                .addAll(startConsumers).add(consumer).build();
    }

    @Override
    @TraceMethod(value = "lease", tagParams = {"appId", "sessionId", "character"})
    public Try<Void> lease(String appId, String sessionId, String character, int appLimit, int maxIdle) {
        String appPath = SESSION_PREFIX + "/" + appId;
        Map<String, ChildData> app = sessions.getCurrentChildren(appPath);
        if (app == null) {
            log.debug("Try to create app path for app={}", appId);
            if (!touchPath(appPath, 3, new byte[0])) {
                return Try.failure(new RuntimeException("Fail to create app path"));
            }
        } else if (app.size() >= appLimit) {
            return Try.failure(new DigitalHumanCommonException(Error.APP_RESOURCE_OUT_OF_USAGE.getCode(),
                    Error.APP_RESOURCE_OUT_OF_USAGE.getMessage()));
        }

        return Try.of(() -> {
            String sessionPath = appPath + "/" + sessionId;
            InterProcessSemaphoreMutex lock = new InterProcessSemaphoreMutex(curatorFramework,
                    LOCK_PREFIX + "/" + appId);

            if (!lock.acquire(500, TimeUnit.MILLISECONDS)) {
                log.warn("Failed to lease, failed to acquire zk app lock, appId={} sessionId={}", appId, sessionId);
                throw new DigitalHumanCommonException(Error.NO_AVAILABLE_RESOURCES.getCode(),
                    Error.NO_AVAILABLE_RESOURCES.getMessage());
            }
            if (!touchPath(sessionPath, 2, NodeData.toBytes(NodeData.origin(maxIdle)))) {
                lock.release();
                throw new DigitalHumanCommonException(Error.INTERNAL_SERVER_ERROR.getCode(),
                        Error.INTERNAL_SERVER_ERROR.getMessage());
            }
            lock.release();

            String configPath = sessionPath + "/config";
            if (!touchPath(configPath, 2, SessionConfig.toBytes(new SessionConfig(character)))) {
                returnLease(appId, sessionId);
                throw new DigitalHumanCommonException(Error.INTERNAL_SERVER_ERROR.getCode(),
                        Error.INTERNAL_SERVER_ERROR.getMessage());
            }

            if (sessions.getCurrentChildren(appPath).size() > appLimit) {
                // 这里有风险，本地cache的同步是有延时的， 在这个延时期间， 用户是有可能通过瞬间的并发，来获取超额的会话数的
                // 这里加了锁， 目的就是让这种意外的会话是串行发生的， 即使发生了， 那也不会超太多。并且锁操作也争取了cache同步的时间
                // 同时， 如果这里超并发了， 可能会释放过多的lease。
                returnLease(appId, sessionId);
                throw new DigitalHumanCommonException(Error.INTERNAL_SERVER_ERROR.getCode(),
                    Error.INTERNAL_SERVER_ERROR.getMessage());
            }
            return (Void) null;
        }).onFailure(t -> {
            log.error("Fail to lease for appId={}, sessionId={}", appId, sessionId, t);
        }).onSuccess(v -> log.debug("Success lease for appId={} sessionId={}", appId, sessionId));
    }

    private boolean updatePath(String path, int retry, byte[] bytes) {
        Exception exception = null;
        while (retry-- > 0) {
            try {
                curatorFramework.setData().forPath(path, bytes);
                return true;
            } catch (KeeperException.NoNodeException ex) {
                // node not exist
                log.warn("Ignore update path={} event, since node not exist", path);
                return false;
            } catch (Exception e) {
                exception = e;
                log.warn("Fail to update path={} retry={} reason={}", path, retry, e.getLocalizedMessage());
            }
        }
        log.error("Fail to update path={}", path, exception);
        return false;
    }

    private boolean touchPath(String path, int retry, byte[] bytes) {
        Exception exception = null;
        while (retry-- > 0) {
            try {
                curatorFramework.create().orSetData().creatingParentsIfNeeded().forPath(path, bytes);
                return true;
            } catch (KeeperException.NodeExistsException ex) {
                // just ignore
                return true;
            } catch (Exception e) {
                exception = e;
                log.warn("Fail to touch path={} retry={} reason={}", path, retry, e.getLocalizedMessage());
            }
        }
        log.error("Fail to touch path={}", path, exception);
        return false;
    }


    @Override
    @TraceMethod(value = "returnRelease", tagParams = {"appId", "sessionId"})
    public void returnLease(String appId, String sessionId) {
        try {
            sessionActiveTime.remove(Tuple.of(appId, sessionId));
            curatorFramework.delete().deletingChildrenIfNeeded().forPath(SESSION_PREFIX + "/" + appId + "/" + sessionId);
            log.debug("Success return lease for appId={} sessionId={}", appId, sessionId);
        } catch (KeeperException.NoNodeException ex) {
            log.warn("Cannot find lease for appId={} sessionId={}, ignore", appId, sessionId);
        } catch (Exception e) {
            log.error("Fail to return lease for appId={} sessionId={}, will try later", appId, sessionId);
            // if failed , put in queue and run later
            executor.execute(() -> {
                Exceptions.wrap().run(() -> Thread.sleep(10));
                returnLease(appId, sessionId);
            });
        }
    }

    @Override
    public void bark(String appId, String sessionId) {
        NodeData nodeData = sessionActiveTime.computeIfAbsent(Tuple.of(appId, sessionId), t -> {
            // todo
            try {
                ChildData childData = sessions.getCurrentData(String.format("%s/%s/%s", SESSION_PREFIX, appId,
                        sessionId));
                if (childData != null) {
                    return NodeData.fromBytes(childData.getData());
                } else {
                    throw new RuntimeException("Cannot find session in expect session tree");
                }
            } catch (Exception ex) {
                log.error("Fail to get session info from session tree, reason={}", ex.getLocalizedMessage());
                return null;
            }
        });
        if (nodeData != null) {
            nodeData.setActiveTime(new Date());
        } else {
            log.error("Fail to bark for appId={} sessionId={}", appId, sessionId);
        }

    }

    @Override
    public int getLeasedNum(String appId) {
        String appPath = SESSION_PREFIX + "/" + appId;
        Map<String, ChildData> appNodes = sessions.getCurrentChildren(appPath);
        if (appNodes == null) {
            return 0;
        }
        return appNodes.size();
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    private static class NodeData {
        private Date activeTime;
        private int maxIdleInSecond = 60;
        private boolean origin = true;
        private static final ObjectMapper objectMapper = new ObjectMapper();

        /**
         * node which has never been activated
         *
         * @param maxIdleInSecond max idle in second
         * @return node
         */
        public static NodeData origin(int maxIdleInSecond) {
            return new NodeData(new Date(), maxIdleInSecond, true);
        }

        public static NodeData update(int maxIdleInSecond) {
            return new NodeData(new Date(), maxIdleInSecond, false);
        }


        static byte[] toBytes(NodeData nodeData) throws JsonProcessingException {

            return objectMapper.writeValueAsBytes(nodeData);
        }

        static NodeData fromBytes(byte[] bytes) throws IOException {
            return objectMapper.readValue(bytes, NodeData.class);
        }
    }

    @Data
    @NoArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    @AllArgsConstructor
    private static class SessionConfig {

        private String character = "unknown";

        private static final ObjectMapper objectMapper = new ObjectMapper();

        static byte[] toBytes(SessionConfig sessionConfig) throws JsonProcessingException {

            return objectMapper.writeValueAsBytes(sessionConfig);
        }

        static SessionConfig fromBytes(byte[] bytes) throws IOException {
            return objectMapper.readValue(bytes, SessionConfig.class);
        }
    }
}
