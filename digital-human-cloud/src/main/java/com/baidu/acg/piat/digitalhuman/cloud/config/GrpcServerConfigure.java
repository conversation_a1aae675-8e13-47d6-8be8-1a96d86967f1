// Copyright (C) 2019 Baidu Inc. All rights reserved.
package com.baidu.acg.piat.digitalhuman.cloud.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * GrpcServerConfig
 *
 * <AUTHOR>
 * @since 2019-08-29
 */
@Configuration
public class GrpcServerConfigure {

    @Bean
    @ConfigurationProperties("digitalhuman.cloud.grpc.config")
    public Config config() {
        return new Config();
    }

    @Data
    public static class Config {
        private int port = 8090;

        private int terminateAwaitSeconds = 3;

        private long permitKeepAliveTimeMilliSeconds = 500; // 允许的最小ping周期

        private long keepAliveTimeMilliSeconds = 30000; // 设置保活的ping周期

    }
}
