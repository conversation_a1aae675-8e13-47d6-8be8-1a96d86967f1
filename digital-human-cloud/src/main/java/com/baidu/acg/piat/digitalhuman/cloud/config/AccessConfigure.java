// Copyright (C) 2019 Baidu Inc. All rights reserved.
package com.baidu.acg.piat.digitalhuman.cloud.config;

import com.baidu.acg.piat.digitalhuman.plat.client.PlatformClient;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * AccessConfig
 *
 * <AUTHOR>
 * @since 2019-09-20
 */
@Configuration
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class AccessConfigure {

    @Bean
    @ConfigurationProperties(prefix = "digitalhuman.cloud.access.config")
    public AccessClientConfig accessConfig() {
        return new AccessClientConfig();
    }


    @Bean
    public PlatformClient accessClient() {
        var config = accessConfig();
        return new PlatformClient(config.getBaseUrl());
    }

    @Data
    public static class AccessClientConfig {
        private String baseUrl;
    }

}
