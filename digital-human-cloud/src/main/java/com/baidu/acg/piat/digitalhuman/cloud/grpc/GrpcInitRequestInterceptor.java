// Copyright (C) 2019 Baidu Inc. All rights reserved.
package com.baidu.acg.piat.digitalhuman.cloud.grpc;

import com.baidu.acg.digitalhuman.cloud.grpc.InitRequest;
import com.baidu.acg.digitalhuman.cloud.grpc.InitResponse;
import com.baidu.acg.piat.digitalhuman.cloud.service.SessionService;
import com.baidu.acg.piat.digitalhuman.cloud.session.SessionContext;
import com.baidu.acg.piat.digitalhuman.common.exception.DigitalHumanCommonException;
import com.google.protobuf.InvalidProtocolBufferException;
import io.grpc.*;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Base64;

/**
 * GrpcServiceInterceptor
 *
 * <AUTHOR>
 * @since 2019-08-28
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class GrpcInitRequestInterceptor implements ServerInterceptor {

    private final SessionService sessionService;

    @Override
    public <ReqT, RespT> ServerCall.Listener<ReqT> interceptCall(
            ServerCall<ReqT, RespT> serverCall,
            Metadata metadata,
            ServerCallHandler<ReqT, RespT> serverCallHandler) {
        String methodName = serverCall.getMethodDescriptor().getFullMethodName();
        log.debug("Intercept service name={}", methodName);

        if (!methodName.equals("com.baidu.acg.pie.DHService/sendText")) {
            return serverCallHandler.startCall(serverCall, metadata);
        }

        final InitResponseHolder initResponseHolder = new InitResponseHolder();
        SessionContext sessionContext;
        try {
            log.info("Intercept {} metadata ", metadata);
            var initRequest = extractInitRequest(metadata);
            var session = sessionService.validate(initRequest.getSessionId(), initRequest.getSessionToken());
            sessionContext = SessionContext.builder()
                    .session(session)
                    .build();
            initResponseHolder.setInitResponse(InitResponse.newBuilder()
                    .setErrorCode(0).setErrorMessage("ok").build());

        } catch (Exception e) {
            log.warn("Check metadata auth failed {} ", metadata, e);
            sessionContext = SessionContext.builder().build();
            serverCall.close(Status.PERMISSION_DENIED, injectInitResponse(InitResponse.newBuilder().setErrorCode(-1)
                    .setErrorMessage(e.getMessage()).build()));

        }

        var ctx = Context.current().withValue(ContextKey.SESSION_CONTEXT, sessionContext);
        return Contexts.interceptCall(ctx, serverCall, metadata, serverCallHandler);
    }

    private Metadata injectInitResponse(InitResponse initResponse) {
        var headers = new Metadata();
        headers.put(Metadata.Key.of("init_response", Metadata.ASCII_STRING_MARSHALLER),
                Base64.getEncoder().encodeToString(initResponse.toByteArray()));
        return headers;
    }


    private InitRequest extractInitRequest(Metadata metadata) {
        var base64 = metadata.get(Metadata.Key.of("init_request", Metadata.ASCII_STRING_MARSHALLER));
        if (base64 == null) {
            log.warn("Cannot find init_request in metadata");
            throw new DigitalHumanCommonException("Cannot find init_request in metadata");
        }
        InitRequest request;
        try {
            request = InitRequest.parseFrom(Base64.getDecoder().decode(base64));
        } catch (InvalidProtocolBufferException e) {
            log.error("Fail to decode metadata as init init_request ", e);
            throw new DigitalHumanCommonException("Fail to decode metadata as init init_request");
        }
        return request;
    }

    @Data
    public static class InitResponseHolder {
        private InitResponse initResponse;
    }
}
