// Copyright (C) 2020 Baidu Inc. All rights reserved.
package com.baidu.acg.piat.digitalhuman.cloud.persistence;

import java.io.IOException;
import java.time.ZonedDateTime;
import java.util.HashMap;
import java.util.Map;

import javax.persistence.Access;
import javax.persistence.AccessType;
import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.OneToOne;
import javax.persistence.Transient;
import javax.persistence.Version;

import org.apache.commons.lang3.StringUtils;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import com.baidu.acg.piat.digitalhuman.common.cloud.Selectors;
import com.baidu.acg.piat.digitalhuman.common.quota.ResourceUsage;
import com.baidu.acg.piat.digitalhuman.common.session.SessionStatus;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;

/**
 * CloudSessionDO
 *
 * <AUTHOR> Junyi (<EMAIL>)
 */
@Data
@Slf4j
@Builder
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
@Entity(name = "cloud_session")
public class CloudSessionModel {
    private static final ObjectMapper mapper = new ObjectMapper();

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    private String sessionId;

    /**
     * alias for userId
     */
    private String userName;

    private String token;

    @Transient
    private SessionStatus status;

    @OneToOne(cascade = CascadeType.ALL)
    @JoinColumn(name = "rtc_connection_id", referencedColumnName = "id")
    private CloudRtcConnectionModel rtcSession;

    private String appId;

    @Transient
    private ResourceUsage resourceUsage;

    private String characterImage;

    @Transient
    private Selectors selectors;

    @Transient
    private Map<String, String> parameters = new HashMap<>();

    @CreationTimestamp
    @Column(insertable = false, updatable = false)
    private ZonedDateTime createTime;

    @UpdateTimestamp
    @Column(updatable = false)
    private ZonedDateTime updateTime;

    @Version
    private Integer version;

    @JsonIgnore
    @Column(name = "status")
    @Access(AccessType.PROPERTY)
    public String getStatusName() {
        return this.status.name();
    }

    public void setStatusName(String status) {
        if (StringUtils.isNotEmpty(status)) {
            this.status = SessionStatus.valueOf(status);
        } else {
            this.status = null;
        }
    }

    @JsonIgnore
    @Column(name = "resource_usage")
    @Access(AccessType.PROPERTY)
    public String getResourceUsageString() {
        if (this.resourceUsage != null) {
            try {
                return mapper.writeValueAsString(this.resourceUsage);
            } catch (JsonProcessingException e) {
                log.error("Parse resource usage to json failed, ex=", e);
            }
        }
        return StringUtils.EMPTY;
    }

    public void setResourceUsageString(String resourceUsage) {
        if (StringUtils.isNotEmpty(resourceUsage)) {
            try {
                this.resourceUsage = mapper.readValue(resourceUsage, ResourceUsage.class);
            } catch (IOException e) {
                log.error("Exception when parse resource usage from json string, string={}, ex= ", resourceUsage, e);
            }
        } else {
            this.resourceUsage = null;
        }
    }

    @JsonIgnore
    @Column(name = "selectors")
    @Access(AccessType.PROPERTY)
    public String getSelectorsString() {
        if (this.selectors != null) {
            try {
                return mapper.writeValueAsString(this.selectors);
            } catch (JsonProcessingException e) {
                log.error("Parse selectors to json failed, ex=", e);
            }
        }
        return StringUtils.EMPTY;
    }

    public void setSelectorsString(String selectors) {
        if (StringUtils.isNotEmpty(selectors)) {
            try {
                this.selectors = mapper.readValue(selectors, Selectors.class);
            } catch (IOException e) {
                log.error("Exception when parse selectors from json string, string={}, ex= ", selectors, e);
            }
        } else {
            this.selectors = null;
        }
    }

    @JsonIgnore
    @Column(name = "parameters")
    @Access(AccessType.PROPERTY)
    public String getParametersString() {
        if (this.parameters != null) {
            try {
                return mapper.writeValueAsString(this.parameters);
            } catch (JsonProcessingException e) {
                log.error("Parse parameters to json failed, ex=", e);
            }
        }
        return StringUtils.EMPTY;
    }

    public void setParametersString(String parameters) {
        if (StringUtils.isNotEmpty(parameters)) {
            try {
                this.parameters = mapper.readValue(parameters,
                        mapper.getTypeFactory().constructParametricType(Map.class, String.class, String.class));
            } catch (IOException e) {
                log.error("Exception when parse parameters from json string, string={}, ex= ", parameters, e);
            }
        } else {
            this.parameters = new HashMap<>();
        }
    }

    public static CloudSessionModel from(CloudSession session) {
        return CloudSessionModel.builder()
                .sessionId(session.getId())
                .userName(session.getUserName())
                .token(session.getToken())
                .status(session.getStatus())
                .rtcSession(session.getRtcSession() == null ? null :
                        CloudRtcConnectionModel.from(session.getRtcSession().getClientConnection()))
                .appId(session.getAppId())
                .resourceUsage(session.getResourceUsage())
                .characterImage(session.getCharacterImage())
                .selectors(session.getSelectors())
                .parameters(session.getParameters())
                .createTime(session.getCreateTime())
                .updateTime(session.getUpdateTime())
                .build();
    }

    public void merge(CloudSession session) {
        if (StringUtils.isNotEmpty(session.getId())) {
            this.sessionId = session.getId();
        }
        if (StringUtils.isNotEmpty(session.getUserName())) {
            this.userName = session.getUserName();
        }

        if (StringUtils.isNotEmpty(session.getToken())) {
            this.token = session.getToken();
        }
        if (session.getStatus() != null) {
            this.status = session.getStatus();
        }
        if (session.getRtcSession() != null && session.getRtcSession().getClientConnection() != null) {
            if (this.rtcSession == null) {
                this.rtcSession = CloudRtcConnectionModel.from(session.getRtcSession().getClientConnection());
            } else {
                this.rtcSession.merge(session.getRtcSession().getClientConnection());
            }
        }
        if (StringUtils.isNotEmpty(session.getAppId())) {
            this.appId = session.getAppId();
        }
        if (session.getResourceUsage() != null) {
            this.resourceUsage = session.getResourceUsage();
        }
        if (StringUtils.isNotEmpty(session.getCharacterImage())) {
            this.characterImage = session.getCharacterImage();
        }
    }
}
