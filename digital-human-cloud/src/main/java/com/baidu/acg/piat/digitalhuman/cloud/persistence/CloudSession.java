// Copyright (C) 2019 Baidu Inc. All rights reserved.
package com.baidu.acg.piat.digitalhuman.cloud.persistence;

import java.time.ZonedDateTime;
import java.util.HashMap;
import java.util.Map;

import com.baidu.acg.piat.digitalhuman.common.cloud.CloudRtcSession;
import com.baidu.acg.piat.digitalhuman.common.cloud.Selectors;
import com.baidu.acg.piat.digitalhuman.common.quota.ResourceUsage;
import com.baidu.acg.piat.digitalhuman.common.session.SessionStatus;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * CloudSession
 *
 * <AUTHOR>
 * @since 2019-08-28
 */
@Data
@Builder
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
public class CloudSession {

    private String id;

    /**
     * alias for userId
     */
    private String userName;


    private String token;

    private SessionStatus status;

    private CloudRtcSession rtcSession;

    private ZonedDateTime createTime;

    private ZonedDateTime updateTime;

    private String appId;

    private ResourceUsage resourceUsage;

    private String characterImage;

    private Map<String, String> parameters = new HashMap<>();

    private Selectors selectors;

    public static CloudSession from(CloudSessionModel sessionDO) {
        return CloudSession.builder()
                .id(sessionDO.getSessionId())
                .userName(sessionDO.getUserName())
                .token(sessionDO.getToken())
                .status(sessionDO.getStatus())
                .rtcSession(CloudRtcSession.builder()
                        .clientConnection(sessionDO.getRtcSession() == null ? null :
                                sessionDO.getRtcSession().toCloudRtcConnection())
                        .build())
                .appId(sessionDO.getAppId())
                .resourceUsage(sessionDO.getResourceUsage())
                .characterImage(sessionDO.getCharacterImage())
                .selectors(sessionDO.getSelectors())
                .parameters(sessionDO.getParameters())
                .createTime(sessionDO.getCreateTime())
                .updateTime(sessionDO.getUpdateTime())
                .build();
    }
}
