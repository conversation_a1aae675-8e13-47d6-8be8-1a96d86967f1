server:
  port: 8081

digitalhuman:
  cloud:
    access:
      config:
        baseUrl: http://localhost:8083
    grpc:
      config:
        port: 8092
        terminateAwaitSeconds: 3
        keepAliveTimeMinutes: 30
    rtc:
      config:
        internalUrl: wss://rtc.exp.bcelive.com:8989/janus
        externalUrl: wss://rtc.exp.bcelive.com:8989/janus
        appId: 75c664d50ae5432581fcfe2c9c3011d5
        key: gf4xjfwt9356
    tts:
      config:
        baseUrl: http://*************:8802
        httpQueryParams:
          lan: zh
          pdt: 993
          ctp: 1
          cuid: test
          aue: 6
          xml: 1
        responseContentType: audio/wav
  resource-pool:
      client:
        resourcePoolUrl: http://localhost:8101

spring:
  data:
    mongodb:
      uri: mongodb://localhost:27017 #mongodb://*************:8017
      database: digitalhuman
logging:
  level:
    com.baidu.acg: debug
