package com.baidu.acg.piat.digitalhuman.cloud.service.impl;

import static org.junit.jupiter.api.Assertions.*;

import io.vavr.control.Try;
import org.apache.curator.framework.recipes.cache.ChildData;
import org.apache.curator.framework.recipes.cache.TreeCache;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Answers;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.springframework.cglib.core.ReflectUtils;
import com.baidu.acg.digitalhuman.render.proxy.grpc.message.TextFragment;
import com.baidu.acg.digitalhuman.render.proxy.grpc.message.Tracing;
import com.google.protobuf.ByteString;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import com.baidu.acg.piat.digitalhuman.common.resource.ResourceInstance;
import com.baidu.acg.piat.digitalhuman.session.route.client.model.RouteContext;
import com.baidu.acg.piat.digitalhuman.session.route.client.service.SessionRouteService;

class RenderProxyServiceImplTest {
    @Mock(answer = Answers.RETURNS_DEEP_STUBS)
    private SessionRouteService sessionRouteService;

    @Mock
    TreeCache treeCache;

    @BeforeEach
    void beforeEach() {
        MockitoAnnotations.initMocks(this);
        Mockito.when(sessionRouteService.findSession(Mockito.anyString(), Mockito.anyString()))
                .thenReturn(Try.success(RouteContext.builder()
                        .resourceInstance(ResourceInstance.builder()
                                .host(null)
                                .build())
                        .build()));
    }

    @Test
    void testClose() {
        RenderProxyServiceImpl renderProxyService = new RenderProxyServiceImpl(null,
                sessionRouteService, null, null, null);

        renderProxyService.close("appId", "sessionId");
    }

    @Test
    void testIdleToSilence() {
        List<String> notSupportIdleCharacterImages =new ArrayList<>();
        RenderProxyServiceImpl renderProxyService = new RenderProxyServiceImpl(null,
                sessionRouteService, null, null, null);
        String text = "<fusion>\n" +
                "    <speak><content>你好<     idle    duration   =    \'999\'   />哈哈哈哈<idle duration = \'800\'/>呵呵呵呵</content></speak>\n" +
                "    <a2a_emotion delay=\"40\" duration=\"800\"><id>kaixin</id></a2a_emotion>\n" +
                "  </fusion>    ";
        TextFragment value = TextFragment.newBuilder().setContent(text).setTracing(Tracing.newBuilder().build()).build();
        notSupportIdleCharacterImages.add("str");
        String characterImage = "str";
        if (notSupportIdleCharacterImages.contains(characterImage)) {
            String toSilenceContent = renderProxyService.idleToSilence(value.getContent());
            value = value.toBuilder().setContent(toSilenceContent).build();
        }
    }

}