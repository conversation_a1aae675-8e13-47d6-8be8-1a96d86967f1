package com.baidu.acg.piat.digitalhuman.cloud.service.impl;

import io.vavr.control.Try;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.time.ZonedDateTime;
import java.util.Collections;

import com.baidu.acg.digitalhuman.cloud.grpc.OpenRequest;
import com.baidu.acg.digitalhuman.render.proxy.grpc.message.BaseResponse;
import com.baidu.acg.piat.digitalhuman.cloud.persistence.CloudSession;
import com.baidu.acg.piat.digitalhuman.cloud.persistence.CloudSessionModel;
import com.baidu.acg.piat.digitalhuman.cloud.persistence.CloudSessionRepository;
import com.baidu.acg.piat.digitalhuman.cloud.service.AccessControlService;
import com.baidu.acg.piat.digitalhuman.cloud.service.HouseKeeper;
import com.baidu.acg.piat.digitalhuman.cloud.service.RenderProxyService;
import com.baidu.acg.piat.digitalhuman.common.access.AccessApp;
import com.baidu.acg.piat.digitalhuman.common.cloud.CloudRtcConnection;
import com.baidu.acg.piat.digitalhuman.common.cloud.CloudRtcSession;
import com.baidu.acg.piat.digitalhuman.common.constans.RenderOpenParameters;
import com.baidu.acg.piat.digitalhuman.common.exception.DigitalHumanCommonException;
import com.baidu.acg.piat.digitalhuman.common.exception.Error;
import com.baidu.acg.piat.digitalhuman.common.quota.ResourceQuota;
import com.baidu.acg.piat.digitalhuman.common.session.SessionStatus;
import com.baidu.acg.piat.digitalhuman.common.cloud.Selectors;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * Created on 2020/7/23 21:33.
 *
 * <AUTHOR>
 */
public class SessionServiceTest {

    @InjectMocks
    private SessionServiceImpl sessionService;

    @Mock
    private CloudSessionRepository repository;

    @Mock
    private RenderProxyService renderProxyService;

    @Mock
    private AccessControlService accessControlService;

    @Mock
    private HouseKeeper houseKeeper;

    private AccessApp accessApp;

    private CloudSession cloudSession;

    private OpenRequest acquireInfo;

    @BeforeEach
    public void setup() {
        MockitoAnnotations.initMocks(this);
        accessApp = AccessApp.builder()
                .name("name")
                .userId("userId")
                .description("description")
                .appId("appId")
                .maxIdleInSecond(120)
                .characterImage("A2A")
                .resourceQuota(new ResourceQuota(2))
                .enabled(true)
                .build();

        acquireInfo = OpenRequest.newBuilder()
                .putParameters(RenderOpenParameters.rtcConnectionCnt.name(), "1")
                .build();
        cloudSession = CloudSession.builder()
                .appId("appId")
                .characterImage("A2A")
                .status(SessionStatus.OPEN)
                .userName("userName")
                .id("sessionId")
                .token("token")
                .rtcSession(CloudRtcSession.builder()
                        .clientConnection(CloudRtcConnection.builder()
                                .appId("roomAppId")
                                .clientId("clientId")
                                .clientToken("clientToken")
                                .feedId("feedId")
                                .roomName("roomName")
                                .rtcServerUrl("rtcServerUrl")
                                .build())
                        .build())
                .createTime(ZonedDateTime.now())
                .updateTime(ZonedDateTime.now())
                .build();
    }

    @Test
    public void acquireInternalError() {
        when(accessControlService.getAppById(any())).thenReturn(accessApp);
        when(houseKeeper.lease(anyString(), anyString(), anyString(), anyInt(), anyInt())).thenReturn(Try.success(null));
        when(renderProxyService.open(any(), any(), any())).thenReturn(BaseResponse.newBuilder().setErrCode(-1).setErrMessage("").build());
        when(repository.save(any())).thenReturn(CloudSessionModel.from(cloudSession));
        var selectors = new Selectors();
        selectors.setFilters(Collections.singletonList(new Selectors.Selector(Selectors.Operator.CONTAIN, "label",
                "data")));
        assertThrows(DigitalHumanCommonException.class, () -> sessionService.acquire("appId", acquireInfo));
        verify(renderProxyService, times(3)).open(any(), any(), any());
        verify(houseKeeper, times(1)).lease(anyString(), anyString(), anyString(), anyInt(), anyInt());
        verify(houseKeeper, times(1)).returnLease(any(), any());
        verify(repository, times(2)).save(any());

        when(renderProxyService.open(any(), any(), any())).thenReturn(BaseResponse.newBuilder()
                .setErrCode(Error.INVALID_FIGURE_CUT_PARAMS.getCode())
                .setErrMessage(Error.INVALID_FIGURE_CUT_PARAMS.getMessage()).build());
        var throwable = assertThrows(DigitalHumanCommonException.class, () -> sessionService.acquire("appId",
                acquireInfo));
        assertEquals(Error.INVALID_FIGURE_CUT_PARAMS.getCode(), throwable.getCode());
        assertEquals(Error.INVALID_FIGURE_CUT_PARAMS.getMessage(), throwable.getMessage());
    }

    @Test
    public void acquire() {
        when(accessControlService.getAppById(any())).thenReturn(accessApp);
        when(houseKeeper.lease(anyString(), anyString(), anyString(), anyInt(), anyInt())).thenReturn(Try.success(null));
        when(renderProxyService.open(any(), any(), any())).thenReturn(BaseResponse.newBuilder().setErrCode(0).setErrMessage("").build());
        when(repository.save(any())).thenReturn(CloudSessionModel.from(cloudSession));
    }

}
