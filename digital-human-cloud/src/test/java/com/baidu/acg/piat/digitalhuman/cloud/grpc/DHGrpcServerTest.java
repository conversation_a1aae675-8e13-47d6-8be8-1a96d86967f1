package com.baidu.acg.piat.digitalhuman.cloud.grpc;

import static org.junit.jupiter.api.Assertions.assertThrows;

import com.baidu.acg.digitalhuman.cloud.grpc.BaseRequest;
import com.baidu.acg.digitalhuman.cloud.grpc.BaseResponse;
import com.baidu.acg.digitalhuman.cloud.grpc.DHServiceGrpc;
import com.baidu.acg.digitalhuman.cloud.grpc.InitRequest;
import com.baidu.acg.digitalhuman.cloud.grpc.TextFragment;
import com.baidu.acg.piat.digitalhuman.cloud.persistence.CloudSession;
import com.baidu.acg.piat.digitalhuman.cloud.service.SessionService;
import com.baidu.acg.piat.digitalhuman.cloud.session.SessionContext;
import com.baidu.acg.piat.digitalhuman.common.exception.DigitalHumanCommonException;

import io.grpc.Context;
import io.grpc.ManagedChannelBuilder;
import io.grpc.Metadata;
import io.grpc.stub.MetadataUtils;
import io.grpc.stub.StreamObserver;

import java.util.Base64;
import java.util.concurrent.CountDownLatch;

import io.micrometer.core.instrument.MeterRegistry;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;


public class DHGrpcServerTest {

    @InjectMocks
    private DHGrpcService service;

    @Mock
    private SessionService sessionService;

    @Mock
    private MeterRegistry meterRegistry;

    @Mock
    private Context context;

    private StreamObserver<BaseResponse> responseStreamObserver;

    @BeforeEach
    public void setup() {
        MockitoAnnotations.initMocks(this);
        responseStreamObserver = new StreamObserver<BaseResponse>() {
            @Override
            public void onNext(BaseResponse baseResponse) {

            }

            @Override
            public void onError(Throwable throwable) {

            }

            @Override
            public void onCompleted() {

            }
        };
        var sessionContext = SessionContext.builder()
                .session(CloudSession.builder().build())
                .build();
        context = Context.current().withValue(ContextKey.SESSION_CONTEXT, sessionContext);
    }

    @Test
    public void send() throws InterruptedException {

        CountDownLatch latch = new CountDownLatch(1);
//        var channel = ClientInterceptors.intercept(ManagedChannelBuilder
//                .forAddress("************", 8090)
//                .usePlaintext()
//                .build(), new ClientInterceptor());
        var channel = ManagedChannelBuilder
                .forAddress("************", 8090)
                .usePlaintext()
                .build();

        var stub = DHServiceGrpc.newStub(channel);


        var callStub = MetadataUtils.attachHeaders(stub, prepareMetadata());

        var requestObserver = callStub.sendText(new StreamObserver<BaseResponse>() {
            @Override
            public void onNext(BaseResponse value) {
                System.out.println(value);
            }

            @Override
            public void onError(Throwable t) {
                t.printStackTrace();
            }

            @Override
            public void onCompleted() {

            }
        });

        requestObserver.onNext(TextFragment.newBuilder()
                .setBaseRequest(BaseRequest.newBuilder().setSendTimestamp(100)
                        .setSequenceNum(0).build()).setContent("你好").build());

    }

    private Metadata prepareMetadata() {
        var metadata = new Metadata();
        var initRequest = InitRequest.newBuilder()
                .setSessionId("5d68b6f180b10f4ee43e7662")
                .setSessionToken("92e8f0c6-3676-4636-aa21-e9a6d4ad3cf9")
                .build();
        metadata.put(Metadata.Key.of("init_request", Metadata.ASCII_STRING_MARSHALLER),
                Base64.getEncoder().encodeToString(initRequest.toByteArray()));
        return metadata;
    }

}