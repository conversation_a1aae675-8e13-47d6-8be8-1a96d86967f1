#!/bin/sh
# From Gerrit Code Review 3.3.1-32-ge3515b069b
# Downloaded directly from iCode proxy
#
# Part of Gerrit Code Review (https://www.gerritcodereview.com/)
#
# Copyright (C) 2009 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
# http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# avoid [[ which is not POSIX sh.
if test "$#" != 1 ; then
  echo "$0 requires an argument."
  exit 1
fi

if test ! -f "$1" ; then
  echo "file does not exist: $1"
  exit 1
fi

# Do not create a change id if requested
if test "false" = "`git config --bool --get gerrit.createChangeId`" ; then
  exit 0
fi

# $RANDOM will be undefined if not using bash, so don't use set -u
random=$( (whoami ; hostname ; date; cat $1 ; echo $RANDOM) | git hash-object --stdin)
dest="$1.tmp.${random}"

trap 'rm -f "${dest}"' EXIT

if ! git stripspace --strip-comments < "$1" > "${dest}" ; then
   echo "cannot strip comments from $1"
   exit 1
fi

if test ! -s "${dest}" ; then
  echo "file is empty: $1"
  exit 1
fi

reviewurl="$(git config --get gerrit.reviewUrl)"
if test -n "${reviewurl}" ; then
  if ! git interpret-trailers --parse < "$1" | grep -q '^Link:.*/id/I[0-9a-f]\{40\}$' ; then
    if ! git interpret-trailers \
          --trailer "Link: ${reviewurl%/}/id/I${random}" < "$1" > "${dest}" ; then
      echo "cannot insert link footer in $1"
      exit 1
    fi
  fi
else
  # Avoid the --in-place option which only appeared in Git 2.8
  # Avoid the --if-exists option which only appeared in Git 2.15
  if ! git -c trailer.ifexists=doNothing interpret-trailers \
        --trailer "Change-Id: I${random}" < "$1" > "${dest}" ; then
    echo "cannot insert change-id line in $1"
    exit 1
  fi
fi

if ! mv "${dest}" "$1" ; then
  echo "cannot mv ${dest} to $1"
  exit 1
fi