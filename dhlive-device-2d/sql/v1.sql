CREATE TABLE `barrage_profile_stat`
(
    `id`                                   bigint(20) unsigned NOT NULL AUTO_INCREMENT,
    `datetime_begin`                       datetime            NOT NULL COMMENT '本次统计的开始时间（包含）',
    `datetime_end`                         datetime            NOT NULL COMMENT '本次统计的结束时间（不包含）',
    `platform`                             varchar(32)                  DEFAULT '',
    `total_num`                            int(11) unsigned    NOT NULL DEFAULT '0' COMMENT '弹幕数量',
    `normal_num`                           int(10) unsigned    NOT NULL DEFAULT '0',
    `not_hit_num`                          int(10) unsigned    NOT NULL DEFAULT '0',
    `exception_num`                        int(10) unsigned    NOT NULL DEFAULT '0',
    `receive_cost_total_time`              bigint(20)          NOT NULL DEFAULT '0',
    `receive_cost_num`                     int(10) unsigned    NOT NULL DEFAULT '0',
    `receive_cost_max_time`                bigint(20)          NOT NULL DEFAULT '0' COMMENT '第一条日志的时间与第三方的时间插值，平均值',
    `first_package_total_time`             bigint(20)          NOT NULL DEFAULT '0',
    `first_package_num`                    int(10) unsigned    NOT NULL DEFAULT '0',
    `first_package_max_time`               bigint(20)          NOT NULL DEFAULT '0' COMMENT '从服务端收到弹幕，到发送首个回复的最大耗时',
    `faq_hitting_total_time`               bigint(20)          NOT NULL DEFAULT '0',
    `faq_hitting_num`                      int(11) unsigned             DEFAULT '0',
    `doc_hitting_total_time`               bigint(20)          NOT NULL DEFAULT '0',
    `doc_hitting_num`                      int(10) unsigned    NOT NULL DEFAULT '0',
    `spoke_reply_first_hitting_total_time` bigint(20)          NOT NULL DEFAULT '0',
    `spoke_reply_first_hitting_num`        int(10) unsigned    NOT NULL DEFAULT '0',
    `spoke_reply_last_hitting_total_time`  bigint(20)          NOT NULL DEFAULT '0',
    `spoke_reply_last_hitting_num`         int(11) unsigned    NOT NULL DEFAULT '0',
    `stat_error`                           varchar(1024)       NOT NULL DEFAULT '' COMMENT '本次统计的异常',
    `debug_info`                           json                NOT NULL COMMENT '本次统计的debug 信息',
    `create_time`                          datetime            NOT NULL ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `x_datetime_begin_total_num` (`datetime_begin`, `platform`, `total_num`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4;

CREATE TABLE `device_info`
(
    `id`                bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '自增id',
    `user_id`           varchar(63)         NOT NULL DEFAULT '' COMMENT 'userId',
    `device_id`         varchar(127)        NOT NULL DEFAULT '' COMMENT '设备id',
    `gather_type`       varchar(15)         NOT NULL DEFAULT '' COMMENT 'JS/WIN_TOOL 触发上传的类型',
    `container_type`    varchar(31)         NOT NULL DEFAULT '' COMMENT '具体的浏览器或者插件类型',
    `container_version` varchar(31)         NOT NULL DEFAULT '' COMMENT '浏览器或者插件的版本号',
    `os_platform`       varchar(31)         NOT NULL DEFAULT '' COMMENT '操作系统名称',
    `os_version`        varchar(31)         NOT NULL DEFAULT '' COMMENT '操作系统版本号',
    `os_info`           json                NOT NULL COMMENT '操作系统其它信息',
    `cpu_info`          json                NOT NULL COMMENT 'cpu信息，list',
    `memory_size`       int(10) unsigned    NOT NULL DEFAULT '0' COMMENT '内存大小（M）',
    `memory_info`       json                NOT NULL COMMENT '内存其它信息',
    `gpu_info`          json                NOT NULL COMMENT 'gpu信息，list',
    `disk_info`         json                NOT NULL COMMENT 'disk信息，list',
    `extra`             json                NOT NULL COMMENT '其它信息',
    `create_time`       datetime            NOT NULL COMMENT '新建时间',
    `update_time`       datetime            NOT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `x_user_id_create_time` (`create_time`, `user_id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4;

CREATE TABLE `device_info_device_id`
(
    `id`          bigint(20) unsigned NOT NULL AUTO_INCREMENT,
    `user_id`     varchar(127)        NOT NULL DEFAULT '',
    `device_id`   varchar(127)        NOT NULL DEFAULT '',
    `create_time` datetime            NOT NULL,
    PRIMARY KEY (`id`),
    KEY `x_createtime` (`create_time`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4;

CREATE TABLE `device_runtime`
(
    `id`           bigint(20) unsigned NOT NULL AUTO_INCREMENT,
    `user_id`      varchar(63)         NOT NULL DEFAULT '',
    `device_id`    varchar(127)        NOT NULL DEFAULT '' COMMENT '设备id',
    `gather_type`  varchar(15)         NOT NULL DEFAULT '' COMMENT 'JS/WIN_TOOL',
    `cpu`          json                NOT NULL COMMENT 'cpu运行信息，list',
    `gpu`          json                NOT NULL COMMENT 'gpu运行信息，list',
    `memory_usage` double(10, 2)       NOT NULL DEFAULT '0.00' COMMENT '内存运行百分比',
    `disk`         json                NOT NULL COMMENT '硬盘信息，list',
    `start_time`   bigint(20)          NOT NULL DEFAULT '0' COMMENT '开机到现在的秒数',
    `create_time`  datetime            NOT NULL COMMENT '新建时间',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4;

CREATE TABLE `barrage_profile_stat`
(
    `id`                                   bigint(20) unsigned NOT NULL AUTO_INCREMENT,
    `datetime_begin`                       datetime            NOT NULL COMMENT '本次统计的开始时间（包含）',
    `datetime_end`                         datetime            NOT NULL COMMENT '本次统计的结束时间（不包含）',
    `platform`                             varchar(32)                  DEFAULT NULL,
    `total_num`                            int(11)             NOT NULL DEFAULT '0' COMMENT '弹幕数量',
    `normal_num`                           int(10) unsigned    NOT NULL DEFAULT '0',
    `not_hit_num`                          int(10) unsigned    NOT NULL DEFAULT '0',
    `exception_num`                        int(10) unsigned    NOT NULL DEFAULT '0',
    `receive_cost_total_time`              bigint(20) unsigned NOT NULL DEFAULT '0',
    `receive_cost_num`                     int(10) unsigned    NOT NULL,
    `receive_cost_max_time`                bigint(20)          NOT NULL COMMENT '第一条日志的时间与第三方的时间插值，平均值',
    `first_package_total_time`             bigint(20) unsigned NOT NULL DEFAULT '0',
    `first_package_num`                    int(10) unsigned    NOT NULL,
    `first_package_max_time`               bigint(20)          NOT NULL DEFAULT '0' COMMENT '从服务端收到弹幕，到发送首个回复的最大耗时',
    `faq_hitting_total_time`               bigint(20) unsigned NOT NULL,
    `faq_hitting_num`                      int(11)                      DEFAULT NULL,
    `doc_hitting_total_time`               bigint(20) unsigned NOT NULL,
    `doc_hitting_num`                      int(10) unsigned    NOT NULL,
    `spoke_reply_first_hitting_total_time` bigint(20) unsigned NOT NULL DEFAULT '0',
    `spoke_reply_first_hitting_num`        int(10) unsigned    NOT NULL,
    `spoke_reply_last_hitting_total_time`  bigint(20) unsigned NOT NULL,
    `spoke_reply_last_hitting_num`         int(11)             NOT NULL,
    `stat_error`                           varchar(1024)       NOT NULL DEFAULT '' COMMENT '本次统计的异常',
    `debug_info`                           json                NOT NULL COMMENT '本次统计的debug 信息',
    `create_time`                          datetime            NOT NULL ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `x_datetime_begin_total_num` (`datetime_begin`, `platform`, `total_num`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4;

