server:
  port: 8181
logging:
  level:
    com.baidu.acg: debug
  access:
    config: classpath:logback-access.xml

spring:
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
    serialization:
      write-dates-as-timestamps: false
  jpa:
    properties:
      hibernate:
        jdbc:
          batch_size: 30
    database: MYSQL
    show-sql: true
    open-in-view: true
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ************************************************************************************************************************************************************************************************************************************************
    username: dhlive_tp_plat
    password: 'Hi109@123'
digital-human:
  barrage-log:
    elastic-search:
      enabled: true
      host: localhost
      port: 6555
      prefix: sandbox
      username: superuser
      password: Baidu_dh123
    service:
      es-index: barrage-profile
  auth:
    enabled: true
    paths: /api/digitalhuman/console/v1/star/**,/api/digitalhuman/starlight/**,/api/digitalhuman/starlight/v1/text2audio/**
    exclude-paths: /star/light/**,/api/digitalhuman/console/v1/star/user/login
    url:
      base: http://localhost:8020
      login: https://login.bcetest.baidu.com/?redirect=https://persona.baidu.com:8444/artistelite
    bce:
      enabled: true
      endpoint: http://gzbh-sandbox19-6271.gzbh.baidu.com:8587/v4
      accessKey: ALTAK70YrgfU8s69809DorhoqO
      secretKey: ab78e068490c4f6b88253a349438c953

