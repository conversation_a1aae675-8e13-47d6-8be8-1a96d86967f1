package com.baidu.acg.piat.digitalhuman.speech.authentication.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import com.baidu.acg.piat.digitalhuman.speech.authentication.client.ESClient;
import com.baidu.acg.piat.digitalhuman.speech.authentication.model.BarrageProfile;
import com.baidu.acg.piat.digitalhuman.speech.authentication.service.ProfileLogService;
import com.baidu.acg.piat.digitalhuman.speech.authentication.util.DateUtil;

@Service
@RequiredArgsConstructor
@Slf4j
public class ProfileLogServiceImpl implements ProfileLogService {

    private final ESClient esClient;

    @Value("${digital-human.barrage-log.service.es-index}")
    private String esIndex;

    @Override
    public void writeLog(List<BarrageProfile> profiles) throws IOException {
        if (profiles.isEmpty()) {
            return;
        }
        Map<String, List<BarrageProfile>> profilesMap = new LinkedHashMap<>();
        for (BarrageProfile profile : profiles) {
            String index = esIndex + "-" + DateUtil.formatEsDateIndex(LocalDateTime.now());
            profilesMap.computeIfAbsent(index, k -> new ArrayList<>()).add(profile);
        }
        for (Map.Entry<String, List<BarrageProfile>> entry : profilesMap.entrySet()) {
            esClient.bulkIndex(entry.getValue(), entry.getKey());
        }
    }

}
