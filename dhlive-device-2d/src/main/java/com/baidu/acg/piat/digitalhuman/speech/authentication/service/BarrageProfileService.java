package com.baidu.acg.piat.digitalhuman.speech.authentication.service;

import org.springframework.data.util.Pair;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

import com.baidu.acg.piat.digitalhuman.speech.authentication.client.EsSearchCriteria;
import com.baidu.acg.piat.digitalhuman.speech.authentication.model.BarrageProfile;
import com.baidu.acg.piat.digitalhuman.speech.authentication.model.BarrageProfileSingleStat;
import com.baidu.acg.piat.digitalhuman.speech.authentication.model.http.PageResult;
import com.baidu.acg.piat.digitalhuman.speech.authentication.model.search.BarrageSearchCriteria;

public interface BarrageProfileService {

    PageResult<BarrageProfileSingleStat> searchBarrageProfiles(BarrageSearchCriteria barrageSearchCriteria)
        throws IOException;

    Pair<PageResult<BarrageProfile>, Map<String, List<BarrageProfile>>> searchBarrageProfilesByStart(
        EsSearchCriteria esSearchCriteria,
        LocalDateTime beginTime,
        LocalDateTime endTime
    ) throws IOException;
}
