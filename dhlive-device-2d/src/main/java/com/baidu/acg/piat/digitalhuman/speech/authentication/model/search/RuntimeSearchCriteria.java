package com.baidu.acg.piat.digitalhuman.speech.authentication.model.search;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.hibernate.validator.constraints.Length;

import com.baidu.acg.piat.digitalhuman.speech.authentication.model.GatherTypeEnum;

@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Data
public class RuntimeSearchCriteria extends TimeRangePageableCriteria {

    @Length(max = 100)
    public String userId;

    public GatherTypeEnum gatherType;


}
