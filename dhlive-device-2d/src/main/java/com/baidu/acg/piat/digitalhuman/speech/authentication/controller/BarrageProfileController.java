package com.baidu.acg.piat.digitalhuman.speech.authentication.controller;


import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

import com.baidu.acg.piat.digitalhuman.speech.authentication.model.BarrageProfile;
import com.baidu.acg.piat.digitalhuman.speech.authentication.service.ProfileLogService;

@RestController
@RequestMapping("/api/device-2d/v1/barrage/profile")
@Slf4j
@Validated
@RequiredArgsConstructor
public class BarrageProfileController {

    private final ProfileLogService profileLogService;

    @ResponseBody
    @PostMapping("/log")
    public void addLogs(@Valid @RequestBody List<BarrageProfile> barrageProfiles) throws IOException {
        log.info("Request addLog barrageProfiles: {}", barrageProfiles);
        barrageProfiles.forEach(BarrageProfile::fixData);
        profileLogService.writeLog(barrageProfiles);
    }
}
