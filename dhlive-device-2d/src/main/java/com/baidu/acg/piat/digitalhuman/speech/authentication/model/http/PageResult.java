// Copyright (C) 2021 Baidu Inc. All rights reserved.

package com.baidu.acg.piat.digitalhuman.speech.authentication.model.http;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 翻页结果，包含totalCount
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PageResult<T> {

    int pageNo;

    int pageSize;

    long totalCount;

    List<T> result;


    public static <S> PageResult<S> of(List<S> result, int pageNo, int pageSize, long totalCount) {
        PageResult<S> res = new PageResult<>();
        res.result = result;
        res.pageNo = pageNo;
        res.pageSize = pageSize;
        res.totalCount = totalCount;
        return res;
    }
}
