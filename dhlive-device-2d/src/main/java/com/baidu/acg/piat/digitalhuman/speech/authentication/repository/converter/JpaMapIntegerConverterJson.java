package com.baidu.acg.piat.digitalhuman.speech.authentication.repository.converter;

import com.fasterxml.jackson.core.JsonProcessingException;
import lombok.extern.slf4j.Slf4j;

import javax.persistence.AttributeConverter;
import java.util.LinkedHashMap;
import java.util.Map;

import com.baidu.acg.piat.digitalhuman.speech.authentication.util.JsonUtil;

@Slf4j
public class JpaMapIntegerConverterJson implements AttributeConverter<Map<String, Integer>, String> {
    @Override
    public String convertToDatabaseColumn(Map<String, Integer> o) {
        try {
            return JsonUtil.writeValueAsString(o);
        } catch (JsonProcessingException e) {
            log.warn("Catch json serialize error", e);
            return "";
        }
    }

    @Override
    public Map<String, Integer> convertToEntityAttribute(String s) {
        try {
            return JsonUtil.readValue(s, LinkedHashMap.class);
        } catch (JsonProcessingException e) {
            log.warn("Catch json parse error", e);
            return new LinkedHashMap<>();
        }
    }
}
