package com.baidu.acg.piat.digitalhuman.speech.authentication.controller.internal;


import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.io.IOException;
import java.time.LocalDateTime;

import com.baidu.acg.piat.digitalhuman.speech.authentication.exception.InputException;
import com.baidu.acg.piat.digitalhuman.speech.authentication.model.BarrageProfileStat;
import com.baidu.acg.piat.digitalhuman.speech.authentication.model.http.PageResult;
import com.baidu.acg.piat.digitalhuman.speech.authentication.model.search.BarrageProfileStatSearchCriteria;
import com.baidu.acg.piat.digitalhuman.speech.authentication.service.BarrageProfileStatCronService;
import com.baidu.acg.piat.digitalhuman.speech.authentication.service.BarrageProfileStatService;


@RequestMapping("/api/device-2d/v1/internal/openapi/profile/stat")
@Slf4j
@Validated
@RequiredArgsConstructor
public class ProfileStatController {

    private final BarrageProfileStatService barrageProfileStatService;

    private final BarrageProfileStatCronService barrageProfileStatCronService;

    @PostMapping("/fill")
    public void fill(@Valid @NotNull @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss") LocalDateTime beginTime,
                     @Valid @NotNull @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss") LocalDateTime endTime)
        throws IOException {
        barrageProfileStatCronService.writeBarrageStatToDBAsync(beginTime, endTime);
    }


    @GetMapping("")
    public PageResult<BarrageProfileStat> list(@Valid BarrageProfileStatSearchCriteria criteria) throws InputException {
        criteria.fixData();
        return barrageProfileStatService.search(criteria);
    }


}
