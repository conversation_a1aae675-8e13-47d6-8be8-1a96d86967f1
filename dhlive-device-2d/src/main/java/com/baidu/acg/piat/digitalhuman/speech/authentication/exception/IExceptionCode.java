package com.baidu.acg.piat.digitalhuman.speech.authentication.exception;

public interface IExceptionCode {

    /**
     * 未知业务相关错误
     */
    int ERROR_EXCEPTION_BUSINESS_UNKNOWN = 12;

    /**
     * 未知错误
     */
    int ERROR_EXCEPTION_UNKNOWN = 11;

    /**
     * 输入报错
     */
    int ERROR_INPUT_VALIDATION = 400;

    int ERROR_REWRITE_UNKNOWN = 4031;

    int ERROR_INTRANET_CHECK = 403;

    int ERROR_AUTH_INPUT_HEADERS = 500;

    int ERROR_AUTH_PROJECT = 501;

    int ERROR_AUTH_PROJECT_CLIENT_RESPONSE = 502;

    int ERROR_AUTH_PROJECT_CLIENT = 503;

    int ERROR_AUTH_BCE_DHUSER_EMPTY = 601;

    int ERROR_STAT = 101;

}
