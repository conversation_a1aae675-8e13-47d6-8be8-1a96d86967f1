package com.baidu.acg.piat.digitalhuman.speech.authentication.repository;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.repository.CrudRepository;

import javax.transaction.Transactional;
import java.time.LocalDateTime;

import com.baidu.acg.piat.digitalhuman.speech.authentication.model.BarrageProfileStat;
import com.baidu.acg.piat.digitalhuman.speech.authentication.model.PlatformEnum;

public interface BarrageProfileStatRepository extends CrudRepository<BarrageProfileStat, Long> {
    @Transactional
    int deleteByDatetimeBeginGreaterThanEqualAndDatetimeBeginLessThan(LocalDateTime start, LocalDateTime end);


    Page<BarrageProfileStat>
    findByDatetimeBeginGreaterThanEqualAndDatetimeBeginLessThanAndPlatformAndTotalNumGreaterThanEqual(
        LocalDateTime start, LocalDateTime end, PlatformEnum platform, int totalNum, Pageable pageable);
}
