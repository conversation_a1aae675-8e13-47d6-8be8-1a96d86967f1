package com.baidu.acg.piat.digitalhuman.speech.authentication.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import com.baidu.acg.piat.digitalhuman.speech.authentication.client.RobotHttpClient;

@Configuration
public class RobotClientConfigure {

    @Bean
    @ConfigurationProperties(prefix = "digital-human.device2d.project-auth")
    RobotConfig device2dConfig() {
        return new RobotConfig();
    }

    @Bean
    public RobotHttpClient device2dHttpClient(RobotConfig robotConfig) {
        return new RobotHttpClient(robotConfig);
    }

}
