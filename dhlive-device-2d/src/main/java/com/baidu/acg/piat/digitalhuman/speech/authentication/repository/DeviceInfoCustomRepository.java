package com.baidu.acg.piat.digitalhuman.speech.authentication.repository;

import org.springframework.data.domain.Page;

import java.util.List;

import com.baidu.acg.piat.digitalhuman.speech.authentication.model.DeviceInfo;
import com.baidu.acg.piat.digitalhuman.speech.authentication.model.search.RuntimeSearchCriteria;

public interface DeviceInfoCustomRepository {
    Page<DeviceInfo> findAll(RuntimeSearchCriteria criteria);
}
