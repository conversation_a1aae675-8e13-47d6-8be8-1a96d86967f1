package com.baidu.acg.piat.digitalhuman.speech.authentication.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;

import javax.persistence.Convert;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;

import com.baidu.acg.piat.digitalhuman.speech.authentication.repository.converter.JpaListMapConverterJson;

@Data
@NoArgsConstructor
@Entity(name = "device_runtime")
public class DeviceRuntime {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    private String userId;

    private String deviceId;

    private String gatherType;

    @Convert(converter = JpaListMapConverterJson.class)
    private List<HashMap<String, String>> cpu = new ArrayList<>();

    @Convert(converter = JpaListMapConverterJson.class)
    private List<HashMap<String, String>> gpu = new ArrayList<>();

    private Double memoryUsage;

    @Convert(converter = JpaListMapConverterJson.class)
    private List<HashMap<String, String>> disk = new ArrayList<>();

    private Long startTime;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    @CreationTimestamp
    private Date createTime;

}
