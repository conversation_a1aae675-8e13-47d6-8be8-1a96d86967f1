package com.baidu.acg.piat.digitalhuman.speech.authentication.repository.converter;

import com.fasterxml.jackson.core.JsonProcessingException;
import lombok.extern.slf4j.Slf4j;

import javax.persistence.AttributeConverter;
import java.util.HashMap;

import com.baidu.acg.piat.digitalhuman.speech.authentication.util.JsonUtil;

@Slf4j
public class JpaMapConverterJson implements AttributeConverter<HashMap<String, String>, String> {
    @Override
    public String convertToDatabaseColumn(HashMap<String, String> o) {
        try {
            return JsonUtil.writeValueAsString(o);
        } catch (JsonProcessingException e) {
            log.warn("Catch json serialize error", e);
            return "";
        }
    }

    @Override
    public HashMap<String, String> convertToEntityAttribute(String s) {
        try {
            return JsonUtil.readValue(s, HashMap.class);
        } catch (JsonProcessingException e) {
            log.warn("Catch json parse error", e);
            return new HashMap<>();
        }
    }
}
