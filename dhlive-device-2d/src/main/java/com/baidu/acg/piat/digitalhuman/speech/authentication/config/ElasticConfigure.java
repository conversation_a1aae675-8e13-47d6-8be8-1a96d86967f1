package com.baidu.acg.piat.digitalhuman.speech.authentication.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import com.baidu.acg.piat.digitalhuman.speech.authentication.client.ESClient;


@Slf4j
@Configuration
public class ElasticConfigure {


    @Bean
    @ConfigurationProperties(prefix = "digital-human.barrage-log.elastic-search")
    public ElasticSearchConfig elasticSearchConfig() {
        log.info("Try to create ElasticSearchConfig");
        return new ElasticSearchConfig();
    }

    @Bean
    public ESClient esClient(ElasticSearchConfig elasticSearchConfig) {
        return new ESClient(elasticSearchConfig);
    }


}
