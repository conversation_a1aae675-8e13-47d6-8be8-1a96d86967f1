// Copyright (C) 2021 Baidu Inc. All rights reserved.

package com.baidu.acg.piat.digitalhuman.speech.authentication.model.http;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * http接口的返回
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
public class HttpResponse<T> extends BaseResponse {
    private T result;

    public static <T> HttpResponse<T> success(T result) {
        return new HttpResponse<>(result);
    }

    public static <T> HttpResponse<T> success(T result, String logId) {
        HttpResponse<T> response = new HttpResponse<>(result);
        response.setLogId(logId);
        return response;
    }

    public static <T> HttpResponse<T> fail(int code, String message) {
        HttpResponse<T> response = new HttpResponse<T>();
        response.setCode(code);
        response.setSuccess(false);
        response.setMessage(new Message(message));
        return response;
    }

    public static <T> HttpResponse<T> fail(String message) {
        return fail(-1, message);
    }


}