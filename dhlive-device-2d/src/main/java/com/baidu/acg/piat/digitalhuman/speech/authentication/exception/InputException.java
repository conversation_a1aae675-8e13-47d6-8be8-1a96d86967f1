package com.baidu.acg.piat.digitalhuman.speech.authentication.exception;

import lombok.Getter;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

public class InputException extends BusinessException {

    @Getter
    private final Map<String, String> fieldErrors = new ConcurrentHashMap<>();

    public InputException(int errorCode, String message) {
        super(errorCode, message);
    }

    public InputException(int errorCode, String message, String field) {
        this(errorCode, message, field, message);
    }

    public InputException(int errorCode, String message, String field, String fieldErrorMsg) {
        super(errorCode, message);
        putFieldError(field, fieldErrorMsg);
    }

    public InputException(int errorCode, String message, String field, Throwable cause) {
        super(errorCode, message, cause);
        putFieldError(field, message);
    }

    public InputException(int errorCode, String message, String field, String fieldErrorMsg, Throwable cause) {
        super(errorCode, message, cause);
        putFieldError(field, fieldErrorMsg);
    }

    public void putFieldError(String field, String errorMsg) {
        fieldErrors.put(field, errorMsg);
    }
}
