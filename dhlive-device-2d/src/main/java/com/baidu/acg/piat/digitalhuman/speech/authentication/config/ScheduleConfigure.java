package com.baidu.acg.piat.digitalhuman.speech.authentication.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

@Configuration
public class ScheduleConfigure {

    @Bean
    @ConfigurationProperties(prefix = "digital-human.schedule")
    public ScheduleConfig scheduleConfig() {
        return new ScheduleConfig();
    }


    @Bean
    public ThreadPoolTaskExecutor threadPoolTaskExecutor(ScheduleConfig scheduleConfig) {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(scheduleConfig.getCorePoolSize());
        executor.setMaxPoolSize(scheduleConfig.getMaxPoolSize());
        executor.setQueueCapacity(scheduleConfig.getQueueCapacity());
        ;
        executor.setThreadNamePrefix("ThreadPoolTaskExecutor-Scheduler-");
        return executor;
    }


}
