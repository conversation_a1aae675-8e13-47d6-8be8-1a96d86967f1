// Copyright (C) 2021 Baidu Inc. All rights reserved.

package com.baidu.acg.piat.digitalhuman.speech.authentication.model.http;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;


@Data
@NoArgsConstructor
@AllArgsConstructor
public class BaseResponse {

    private int code = 0;

    private boolean success = true;

    private Message message = new Message();

    private String logId;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Message {
        private String global = "success";

        @Override
        public String toString() {
            return global;
        }
    }
}
