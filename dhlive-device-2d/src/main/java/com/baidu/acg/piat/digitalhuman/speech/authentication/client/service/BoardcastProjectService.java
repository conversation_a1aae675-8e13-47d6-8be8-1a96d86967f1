package com.baidu.acg.piat.digitalhuman.speech.authentication.client.service;


import retrofit2.Call;
import retrofit2.http.HeaderMap;
import retrofit2.http.POST;

import java.util.Map;

import com.baidu.acg.piat.digitalhuman.speech.authentication.model.robot.RobotResponse;

public interface BoardcastProjectService {


    @POST("/api/broadcast/project/token/auth")
    Call<RobotResponse<Void>> projectAuth(@HeaderMap Map<String, String> headers);
}
