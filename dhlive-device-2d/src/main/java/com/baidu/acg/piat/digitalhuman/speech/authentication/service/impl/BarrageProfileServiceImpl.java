package com.baidu.acg.piat.digitalhuman.speech.authentication.service.impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import com.baidu.acg.piat.digitalhuman.speech.authentication.client.ESClient;
import com.baidu.acg.piat.digitalhuman.speech.authentication.client.EsSearchCriteria;
import com.baidu.acg.piat.digitalhuman.speech.authentication.model.BarrageProfile;
import com.baidu.acg.piat.digitalhuman.speech.authentication.model.BarrageProfileSingleStat;
import com.baidu.acg.piat.digitalhuman.speech.authentication.model.BarrageProfileStat;
import com.baidu.acg.piat.digitalhuman.speech.authentication.model.http.PageResult;
import com.baidu.acg.piat.digitalhuman.speech.authentication.model.search.BarrageSearchCriteria;
import com.baidu.acg.piat.digitalhuman.speech.authentication.service.BarrageProfileService;
import com.baidu.acg.piat.digitalhuman.speech.authentication.util.JsonUtil;

@Service
@RequiredArgsConstructor
@Slf4j
public class BarrageProfileServiceImpl implements BarrageProfileService {

    private static final String START_SUB_TYPE = "receive";

    private final ESClient esClient;

    private final DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss");

    private final DateTimeFormatter esDateFormatter = DateTimeFormatter.ofPattern("yyyy.MM.dd");

    @Value(value = "${digital-human.barrage-log.service.es-index}")
    private String esIndex;


    public PageResult<BarrageProfileSingleStat> searchBarrageProfiles(BarrageSearchCriteria barrageSearchCriteria) throws IOException {
        EsSearchCriteria esSearchCriteria = new EsSearchCriteria();
        if (barrageSearchCriteria.getPlatform() != null) {
            esSearchCriteria.putExactQuery("platform.keyword", barrageSearchCriteria.getPlatform().name());
        }
        if (StringUtils.isNotEmpty(barrageSearchCriteria.getLogId())) {
            esSearchCriteria.putExactQuery("logId.keyword", barrageSearchCriteria.getLogId());
        }
        if (StringUtils.isNotEmpty(barrageSearchCriteria.getUserId())) {
            esSearchCriteria.putExactQuery("userId.keyword", barrageSearchCriteria.getUserId());
        }
        esSearchCriteria.setFrom(barrageSearchCriteria.getPageNo() * barrageSearchCriteria.getPageSize());
        esSearchCriteria.setSize(barrageSearchCriteria.getPageSize());
        esSearchCriteria.putSortField("createTime.keyword", false);
        Pair<PageResult<BarrageProfile>, Map<String, List<BarrageProfile>>> result = searchBarrageProfilesByStart(esSearchCriteria,
            barrageSearchCriteria.getBeginTime(), barrageSearchCriteria.getEndTime());
        Map<String, List<BarrageProfile>> subLogsMap = result.getSecond();
        List<BarrageProfileSingleStat> barrageProfileSingleStats = new ArrayList<>();
        PageResult<BarrageProfile> pageResult = result.getFirst();
        for (BarrageProfile barrageProfile : pageResult.getResult()) {
            BarrageProfileSingleStat barrageProfileSingleStat = new BarrageProfileSingleStat();
            BeanUtils.copyProperties(barrageProfile, barrageProfileSingleStat);
            // 计算 barrageProfileSingleStat
            List<BarrageProfile> subLogs = subLogsMap.getOrDefault(barrageProfile.getLogId(), new ArrayList<>());
            barrageProfileSingleStat.setLogs(subLogs);
            BarrageProfileStat barrageProfileStat = new BarrageProfileStat();
            barrageProfileStat.putBarrageProfileStat(barrageProfile.getLogId(), barrageProfileSingleStat.getLogs());
            BarrageProfileStatCalc barrageProfileStatCalc = new BarrageProfileStatCalc(barrageProfileStat);
            barrageProfileStatCalc.startStat();

            barrageProfileSingleStat.fillBarrageProfileStat(barrageProfileStat);
            barrageProfileSingleStats.add(barrageProfileSingleStat);

            boolean isAiDocEnabled = BarrageProfileStatCalc
                .findLastBarrageProfile(BarrageProfileStat.FAQ_NOT_OPEN_SUB_TYPE, subLogs) != null;
            barrageProfileSingleStat.setAiDocEnabled(isAiDocEnabled);

        }

        return PageResult.of(barrageProfileSingleStats, pageResult.getPageNo(), pageResult.getPageSize(), pageResult.getTotalCount());
    }

    private Map<String, List<BarrageProfile>> barrageListToMapLogId(List<BarrageProfile> barrageProfiles) {
        Map<String, List<BarrageProfile>> result = new LinkedHashMap<>();
        for (BarrageProfile barrageProfile : barrageProfiles) {
            result.computeIfAbsent(barrageProfile.getLogId(), k -> new ArrayList<>());
            result.get(barrageProfile.getLogId()).add(barrageProfile);
        }
        return result;
    }


    public Pair<PageResult<BarrageProfile>, Map<String, List<BarrageProfile>>>
    searchBarrageProfilesByStart(EsSearchCriteria esSearchCriteria,
                                 LocalDateTime beginTime, LocalDateTime endTime) throws IOException {
        log.debug("Try to searchBarrageProfilesByStart() barrage profile by start time: {}-{}, {}",
            beginTime, endTime, esSearchCriteria);
        LocalDateTime fixIndexTime = beginTime.minusHours(8);
        LocalDateTime fixEndIndexTime = endTime.minusHours(8);
        Set<String> dataStrList = new LinkedHashSet<>();
        while (fixIndexTime.isBefore(fixEndIndexTime)) {
            dataStrList.add(fixIndexTime.format(esDateFormatter));
            dataStrList.add(fixIndexTime.minusMinutes(30).format(esDateFormatter));
            fixIndexTime = fixIndexTime.plusDays(1);
        }
        dataStrList.add(fixEndIndexTime.format(esDateFormatter));
        dataStrList.add(fixEndIndexTime.plusHours(1).format(esDateFormatter));
        List<String> indexList = dataStrList.stream().map(date -> esIndex + "-" + date).collect(Collectors.toList());
        log.debug("Try to searchBarrageProfilesByStart() with index: {}", indexList);
        String allIndex = StringUtils.join(indexList, ",");
        esSearchCriteria.setEsIndex(allIndex);
        EsSearchCriteria esSearchCriteriaSubType = cloneEsSearchCriteria(esSearchCriteria);
        esSearchCriteriaSubType.putExactListQuery("subType.keyword", List.of(START_SUB_TYPE));
        PageResult<BarrageProfile> result = searchEsWithTimeStamp(beginTime, endTime, esSearchCriteriaSubType);
        if (result.getResult().isEmpty()) {
            return Pair.of(result, new LinkedHashMap<>());
        }
        List<String> logIds = result.getResult().stream().map(BarrageProfile::getLogId).collect(Collectors.toList());
        log.info("Try to find Es BarrageProfileStat logIds: {}", logIds);

        EsSearchCriteria esSearchCriteriaLogIds = cloneEsSearchCriteria(esSearchCriteria);
        esSearchCriteriaLogIds.putExactListQuery("logId.keyword", logIds);
        esSearchCriteriaLogIds.removeSortField("createTime.keyword");
        esSearchCriteriaLogIds.putSortField("createTime.keyword", true);


        esSearchCriteriaLogIds.setPageAll();
        PageResult<BarrageProfile> logIdBarrageProfiles = searchEsWithTimeStamp(beginTime, endTime.plusMinutes(10), esSearchCriteriaLogIds);

        return Pair.of(result, barrageListToMapLogId(logIdBarrageProfiles.getResult()));
    }

    private EsSearchCriteria cloneEsSearchCriteria(EsSearchCriteria esSearchCriteria) {
        try {
            String json = JsonUtil.writeValueAsString(esSearchCriteria);
            return JsonUtil.readValue(json, EsSearchCriteria.class);
        } catch (JsonProcessingException e) {
            log.error("Fail to cloneEsSearchCriteria:" + e.getMessage(), e);
            return esSearchCriteria;
        }

    }


    private PageResult<BarrageProfile> searchEsWithTimeStamp(LocalDateTime beginTime, LocalDateTime endTime,
                                                             EsSearchCriteria esSearchCriteria) throws IOException {
        esSearchCriteria.putRange("@timestamp", beginTime.format(dateTimeFormatter), endTime.format(dateTimeFormatter));
        return esClient.search(esSearchCriteria, BarrageProfile.class);
    }


}
