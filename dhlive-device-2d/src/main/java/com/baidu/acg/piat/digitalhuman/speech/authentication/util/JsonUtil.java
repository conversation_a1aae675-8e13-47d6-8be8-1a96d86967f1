// Copyright (C) 2021 Baidu Inc. All rights reserved.

package com.baidu.acg.piat.digitalhuman.speech.authentication.util;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.TreeNode;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.json.JsonMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;

import java.io.IOException;
import java.util.List;
import java.util.Map;

public class JsonUtil {

    private static final ObjectMapper OM = JsonMapper.builder()
        .addModule(new JavaTimeModule())
        .configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)
        .build();

    private static final ObjectMapper OM_NOT_NULL = new ObjectMapper()
        .setSerializationInclusion(JsonInclude.Include.NON_NULL);

    public static <T> T readValue(String json, Class<T> clazz) throws JsonProcessingException {
        return OM.readValue(json, clazz);
    }

    public static <T> T readValue(String json, T object) throws JsonProcessingException {
        return OM.readerForUpdating(object).readValue(json);
    }

    public static <T> T readValue(Object from, T toObject) throws JsonProcessingException {
        return OM.readerForUpdating(toObject).readValue(writeValueAsString(from));
    }

    public static <T> T readValue(byte[] json, Class<T> clazz) throws IOException {
        return OM.readValue(json, clazz);
    }

    public static <T> T readValue(Object object, Class<T> clazz) throws JsonProcessingException {
        if (object == null) {
            return null;
        }
        return OM.readValue(JsonUtil.writeValueAsString(object), clazz);
    }

    public static <T> T readValue(String json, TypeReference<T> typeReference) throws IOException {
        return OM.readValue(json, typeReference);
    }

    public static <T> T readValue(TreeNode node, Class<T> clazz) throws IOException {
        if (node == null) {
            throw new IOException("input ObjectNode is null");
        }
        return OM.treeToValue(node, clazz);
    }

    public static <T> List<T> toArray(String json, Class<Map> clazz) throws IOException {
        return OM.readValue(json, OM.getTypeFactory().constructCollectionType(List.class, clazz));
    }

    public static String writeValueAsString(Object object) throws JsonProcessingException {
        return OM.writeValueAsString(object);
    }

    public static byte[] writeValueAsBytes(Object object) throws JsonProcessingException {
        return OM.writeValueAsBytes(object);
    }

    public static String writeValueAsStringIgnoreNull(Object object) throws JsonProcessingException {
        return OM_NOT_NULL.writeValueAsString(object);
    }


}
