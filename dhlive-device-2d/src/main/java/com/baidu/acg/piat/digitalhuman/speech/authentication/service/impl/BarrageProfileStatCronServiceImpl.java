package com.baidu.acg.piat.digitalhuman.speech.authentication.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import com.baidu.acg.piat.digitalhuman.speech.authentication.client.EsSearchCriteria;
import com.baidu.acg.piat.digitalhuman.speech.authentication.model.BarrageProfile;
import com.baidu.acg.piat.digitalhuman.speech.authentication.model.BarrageProfileStat;
import com.baidu.acg.piat.digitalhuman.speech.authentication.model.PlatformEnum;
import com.baidu.acg.piat.digitalhuman.speech.authentication.repository.BarrageProfileStatRepository;
import com.baidu.acg.piat.digitalhuman.speech.authentication.service.BarrageProfileService;
import com.baidu.acg.piat.digitalhuman.speech.authentication.service.BarrageProfileStatCronService;

@Service
@RequiredArgsConstructor
@Slf4j
public class BarrageProfileStatCronServiceImpl implements BarrageProfileStatCronService {

    private static final List<PlatformEnum> PLATFORM_LIST
        = List.of(PlatformEnum.DOU_YIN, PlatformEnum.MEI_TUAN, PlatformEnum.OFFICIAL_WEBSITE,
        PlatformEnum.WEB, PlatformEnum.OFFICIAL_WEBSITE, PlatformEnum.OTHER);

    private final BarrageProfileStatRepository barrageProfileStatRepository;

    private final ExecutorService executorService = Executors.newSingleThreadExecutor();

    private final BarrageProfileService barrageProfileService;

    @Value(value = "${digital-human.barrage-log.service.stat-duration-seconds:30}")
    private int statDurationSeconds;

    @Override
    public void writeBarrageStatToDBAsync(LocalDateTime beginTime, LocalDateTime endTime) throws IOException {
        executorService.execute(() -> {
            try {
                writeBarrageStatToDB(beginTime, endTime);
            } catch (IOException e) {
                log.error("Write BarrageProfileStat to DB failed:" + e.getMessage(), e);
            }
        });
    }

    @Override
    public void writeBarrageStatToDB(LocalDateTime beginTime, LocalDateTime endTime) throws IOException {
        int deletedLines = barrageProfileStatRepository
            .deleteByDatetimeBeginGreaterThanEqualAndDatetimeBeginLessThan(beginTime, endTime);
        log.info("Try Write BarrageProfileStat to DB, : {}, {} and deleted db lines: {} ", beginTime, endTime, deletedLines);
        LocalDateTime currentTime = beginTime;
        while (currentTime.compareTo(endTime) < 0) {
            LocalDateTime currentEndTime = currentTime.plusSeconds(statDurationSeconds);
            try {
                writeBarrageStatToDBDirect(currentTime, currentEndTime);
            } catch (Exception e) {
                log.error("Write BarrageProfileStat to DB failed:" + e.getMessage(), e);
            }
            currentTime = currentEndTime;
        }
    }

    private void writeBarrageStatToDBDirect(LocalDateTime beginTime, LocalDateTime endTime) throws IOException {
        EsSearchCriteria esSearchCriteria = new EsSearchCriteria();
        esSearchCriteria.setPageAll();
        // Pair<PageResult<BarrageProfile>, Map<String, List<BarrageProfile>>>
        var result = barrageProfileService.searchBarrageProfilesByStart(esSearchCriteria, beginTime, endTime);
        Map<String, List<BarrageProfile>> profileAllMap = result.getSecond();
        Map<PlatformEnum, BarrageProfileStat> barrageProfileStatMap = prepareAndFillStats(profileAllMap, beginTime, endTime);
        barrageProfileStatMap.forEach((platform, stat) -> {
            try {
                BarrageProfileStatCalc barrageProfileStatCalc = new BarrageProfileStatCalc(stat);
                barrageProfileStatCalc.startStat();
                log.debug("Finish stat: {}", stat);
            } catch (Exception e) {
                log.error("Calc BarrageProfileStat to DB failed:" + e.getMessage(), e);
            }
        });
        barrageProfileStatRepository.saveAll(barrageProfileStatMap.values());
    }


    private Map<PlatformEnum, BarrageProfileStat> prepareAndFillStats(Map<String, List<BarrageProfile>> profileAllMap,
                                                                      LocalDateTime beginTime, LocalDateTime endTime) {
        Map<PlatformEnum, BarrageProfileStat> barrageProfileStatList = new HashMap<>();
        ArrayList<PlatformEnum> allPlatforms = new ArrayList<>(List.of(PlatformEnum.values()));
        allPlatforms.add(PlatformEnum.ALL);
        allPlatforms.forEach(platform -> {
            BarrageProfileStat stat = new BarrageProfileStat();
            stat.setPlatform(platform);
            stat.setDatetimeBegin(beginTime);
            stat.setDatetimeEnd(endTime);
            barrageProfileStatList.put(platform, stat);

        });
        barrageProfileStatList.get(PlatformEnum.ALL).setBarrageProfileMap(profileAllMap);
        for (Map.Entry<String, List<BarrageProfile>> entry : profileAllMap.entrySet()) {
            List<BarrageProfile> barrageProfiles = entry.getValue();
            PlatformEnum platform = PlatformEnum.OTHER;
            if (barrageProfiles.size() > 0) {
                PlatformEnum firstPlatform = barrageProfiles.get(0).getPlatform();
                if (firstPlatform != null) {
                    platform = firstPlatform;
                }
            }
            barrageProfileStatList.get(platform).putBarrageProfileStat(entry.getKey(), barrageProfiles);
        }
        return barrageProfileStatList;
    }

}
