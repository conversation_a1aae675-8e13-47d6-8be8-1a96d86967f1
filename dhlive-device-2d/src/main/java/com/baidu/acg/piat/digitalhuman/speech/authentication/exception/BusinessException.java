package com.baidu.acg.piat.digitalhuman.speech.authentication.exception;

import lombok.Getter;

public class BusinessException extends Exception implements IExceptionCode {

    private static final long serialVersionUID = -2499007167949875772L;

    @Getter
    private int errorCode = ERROR_EXCEPTION_BUSINESS_UNKNOWN;

    public BusinessException(String message) {
        super(message);
    }

    public BusinessException(String message, Throwable cause) {
        super(message, cause);
    }

    public BusinessException(int errorCode, String message) {
        super(message);
        this.errorCode = errorCode;
    }

    public BusinessException(int errorCode, String message, Throwable cause) {
        super(message, cause);
        this.errorCode = errorCode;
    }


}
