package com.baidu.acg.piat.digitalhuman.speech.authentication.config;

import lombok.Data;

@Data
public class ElasticSearchConfig {

    private boolean enabled = false;

    private String host;

    private int port = 9200;

    private String prefix = "sandbox";

    private String username;

    private String password;


    /**
     * 和下面3个一起，构成线程池参数
     */
    private int corePoolSize = 1;

    private int maxPoolSize = 4;

    private int keepAliveTime = 5000;

    private int queueSize = 1024;
}
