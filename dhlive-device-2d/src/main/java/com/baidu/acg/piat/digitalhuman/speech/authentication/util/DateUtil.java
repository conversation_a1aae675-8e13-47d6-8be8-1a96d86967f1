package com.baidu.acg.piat.digitalhuman.speech.authentication.util;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;

public class DateUtil {

    private static DateTimeFormatter esIndexDateTimeFormatter = DateTimeFormatter.ofPattern("yyyy.MM.dd");

    private static ThreadLocal<SimpleDateFormat> simpleDateFormatDotRef
        = ThreadLocal.withInitial(() -> new SimpleDateFormat("yyyy.MM.dd"));

    private static ThreadLocal<SimpleDateFormat> simpleDateFormatMinusRef
        = ThreadLocal.withInitial(() -> new SimpleDateFormat("yyyy-MM-dd"));

    public static String formatDotDate(Date date) {
        return simpleDateFormatDotRef.get().format(date);
    }

    public static String formatDotDateFromMinus(String dateStr) throws ParseException {
        Date date = simpleDateFormatMinusRef.get().parse(dateStr);
        return simpleDateFormatDotRef.get().format(date);
    }

    public static String formatEsDateIndex(LocalDateTime time) {
        return esIndexDateTimeFormatter.format(time.minusHours(8));
    }

    public static String formatEsDateIndex(ZonedDateTime time) {
        return esIndexDateTimeFormatter.format(time.minusHours(8));
    }
}
