package com.baidu.acg.piat.digitalhuman.speech.authentication.client;

import co.elastic.clients.elasticsearch.ElasticsearchClient;
import co.elastic.clients.elasticsearch._types.FieldSort;
import co.elastic.clients.elasticsearch._types.FieldValue;
import co.elastic.clients.elasticsearch._types.SortOptions;
import co.elastic.clients.elasticsearch._types.SortOrder;
import co.elastic.clients.elasticsearch._types.query_dsl.MatchQuery;
import co.elastic.clients.elasticsearch._types.query_dsl.Query;
import co.elastic.clients.elasticsearch._types.query_dsl.RangeQuery;
import co.elastic.clients.elasticsearch._types.query_dsl.TermQuery;
import co.elastic.clients.elasticsearch._types.query_dsl.TermsQuery;
import co.elastic.clients.elasticsearch.core.BulkRequest;
import co.elastic.clients.elasticsearch.core.BulkResponse;
import co.elastic.clients.elasticsearch.core.SearchResponse;
import co.elastic.clients.elasticsearch.core.bulk.BulkResponseItem;
import co.elastic.clients.elasticsearch.core.search.Hit;
import co.elastic.clients.json.JsonData;
import co.elastic.clients.json.jackson.JacksonJsonpMapper;
import co.elastic.clients.transport.ElasticsearchTransport;
import co.elastic.clients.transport.rest_client.RestClientTransport;
import co.elastic.clients.util.Pair;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.Header;
import org.apache.http.HttpHost;
import org.apache.http.HttpResponseInterceptor;
import org.apache.http.auth.AuthScope;
import org.apache.http.auth.UsernamePasswordCredentials;
import org.apache.http.client.CredentialsProvider;
import org.apache.http.impl.client.BasicCredentialsProvider;
import org.apache.http.message.BasicHeader;
import org.elasticsearch.client.RestClient;
import org.elasticsearch.client.RestClientBuilder;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import com.baidu.acg.piat.digitalhuman.speech.authentication.config.ElasticSearchConfig;
import com.baidu.acg.piat.digitalhuman.speech.authentication.model.http.PageResult;

@RequiredArgsConstructor
@Slf4j
public class ESClient {

    private final ElasticSearchConfig elasticSearchConfig;

    @Getter
    private volatile ElasticsearchClient client;

    private volatile ThreadPoolExecutor executorService;


    @PostConstruct
    protected void init() {
        if (!elasticSearchConfig.isEnabled()) {
            log.info("Skip init, as elasticSearch is disable");
            return;
        }

        log.info("ElasticSearchConfig is : {} ", elasticSearchConfig);

        executorService = new ThreadPoolExecutor(
            elasticSearchConfig.getCorePoolSize(),
            elasticSearchConfig.getMaxPoolSize(),
            elasticSearchConfig.getKeepAliveTime(), TimeUnit.MILLISECONDS,
            new ArrayBlockingQueue<>(elasticSearchConfig.getQueueSize()),
            r -> {
                Thread thread = new Thread(r);
                thread.setName("barrage-log-" + thread.getId());
                return thread;
            });

        RestClientBuilder builder = RestClient.builder(
            new HttpHost(elasticSearchConfig.getHost(), elasticSearchConfig.getPort()));

        builder.setDefaultHeaders(new Header[]{new BasicHeader("Content-type", "application/json")});
        if (StringUtils.isNotEmpty(elasticSearchConfig.getUsername())) {
            final CredentialsProvider credentialsProvider = new BasicCredentialsProvider();
            credentialsProvider.setCredentials(
                AuthScope.ANY,
                new UsernamePasswordCredentials(elasticSearchConfig.getUsername(), elasticSearchConfig.getPassword()));
            builder.setHttpClientConfigCallback(
                httpClientBuilder -> httpClientBuilder
                    .setDefaultCredentialsProvider(credentialsProvider)
                    .addInterceptorLast((HttpResponseInterceptor) (response, context)
                        -> response.addHeader("X-Elastic-Product", "Elasticsearch")));
            // es服务端低版本需要用旧的head
        }
        JacksonJsonpMapper jsonpMapper = new JacksonJsonpMapper();
        ObjectMapper objectMapper = jsonpMapper.objectMapper();
        objectMapper.registerModule(new JavaTimeModule());
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        objectMapper.configure(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);
        objectMapper.configure(SerializationFeature.WRITE_DATES_WITH_ZONE_ID, true);
        objectMapper.configure(DeserializationFeature.ADJUST_DATES_TO_CONTEXT_TIME_ZONE, false);
        ElasticsearchTransport transport = new RestClientTransport(builder.build(), jsonpMapper);

        client = new ElasticsearchClient(transport);
        log.info("Succeed to init elasticsearch client");
    }


    public <T> void bulkIndexAsync(List<T> documents, String esIndex) {
        if (client == null) {
            log.warn("Skip as client is not inited");
            return;
        }

        log.debug("Try to sendLog to index {}, of size {} ", formatIndex(esIndex), documents.size());
        executorService.execute(() -> {
            try {
                bulkIndex(documents, esIndex);
            } catch (Exception e) {
                log.error("Fail to send barrage log to es:{} ", e.getMessage(), e);
            }
        });
    }

    public <T> List<T> search(String esIndex, Map<String, String> containQueries, Map<String, String> exactQueries, int from, int size, Class<T> clazz) throws IOException {
        if (client == null) {
            log.warn("Skip as client is not inited");
            return new ArrayList<>();
        }
        String index = formatIndex(esIndex);
        log.debug("Try to search for index: {}, containQueries: {} , exactQueries: {}", index, containQueries, exactQueries);
        List<Query> exactQueryList = generateQueries(exactQueries, true);
        List<Query> containQueryList = generateQueries(containQueries, false);
        SearchResponse<T> response = client.search(s -> s.trackTotalHits(b -> {
            b.enabled(true);
            return b;
        }).index(index).query(q -> q.bool(b -> {
            for (Query query : exactQueryList) {
                b.must(query);
            }
            for (Query query : containQueryList) {
                b.must(query);
            }

            return b;
        })).from(from).size(size), clazz);
        long totalSize = response.hits().total().value();
        List<Hit<T>> hits = response.hits().hits();
        List<T> result = new ArrayList<>();
        for (Hit<T> hit : hits) {
            T doc = hit.source();
            result.add(doc);
        }
        log.debug("Finish search for index: {}, containQueries: {} , exactQueries: {}, resultSize: {}", index, containQueries, exactQueries, result.size());
        return result;

    }

    public <T> PageResult<T> search(EsSearchCriteria esSearchCriteria, Class<T> clazz) throws IOException {

        int from = esSearchCriteria.getFrom();
        int size = esSearchCriteria.getSize();
        if (client == null) {
            log.warn("Skip as client is not inited");
            return PageResult.of(new ArrayList<>(), from / size, size, 0);
        }
        String index = esSearchCriteria.getFullEsIndex();
        if (StringUtils.isEmpty(index)) {
            index = formatIndex(esSearchCriteria.getEsIndex());
        }
        final String esIndex = index;
        log.debug("Try to search index={} , criteria={}", index, esSearchCriteria);
        List<Query> exactQueryList = generateQueries(esSearchCriteria.getExactQueries(), true);
        List<Query> containQueryList = generateQueries(esSearchCriteria.getContainQueries(), false);
        List<Query> rangeQueryList = generateRangeQueries(esSearchCriteria.getRanges(), true);
        List<Query> listQueryList = generateListQueries(esSearchCriteria.exactListQueries);
        List<Query> totalQueryList = new ArrayList<>();
        totalQueryList.addAll(exactQueryList);
        totalQueryList.addAll(containQueryList);
        totalQueryList.addAll(rangeQueryList);
        totalQueryList.addAll(listQueryList);
        List<SortOptions> sortOrders = generateSortOptions(esSearchCriteria.getSortFields());
        log.debug("Try to search for index: {}, esSearchCriteria: {}", esIndex, esSearchCriteria);
        SearchResponse<T> response = client.search(s -> {
            s.ignoreUnavailable(true).trackTotalHits(b -> {
                b.enabled(esSearchCriteria.isNeedTotal());
                return b;
            }).index(esIndex).query(q ->
                q.bool(b -> {
                    for (Query query : totalQueryList) {
                        b.must(query);
                    }

                    return b;
                })).from(from).size(size);
            if (sortOrders.size() > 1) {
                log.error("Only support one sort field");
            }
            if (sortOrders.size() > 0) {
                s.sort(sortOrders.get(0));
            }
            return s;
        }, clazz);
        long totalSize = 0;
        if (response.hits().total() != null) {
            totalSize = response.hits().total().value();
        }
        List<Hit<T>> hits = response.hits().hits();
        List<T> result = new ArrayList<>();
        for (Hit<T> hit : hits) {
            T doc = hit.source();
            result.add(doc);
        }
        log.debug("Finish search for index: {},resultSize: {}, total: {}", esIndex, result.size(), totalSize);
        return PageResult.of(result, from / size, size, totalSize);
    }


    private List<Query> generateQueries(Map<String, String> queries, boolean isExact) {
        List<Query> queryList = new ArrayList<>();
        for (Map.Entry<String, String> entry : queries.entrySet()) {
            String name = entry.getKey();
            String value = entry.getValue();
            Query query;
            if (isExact) {
                query = TermQuery.of(m -> m.field(name).value(value))._toQuery();
            } else {
                query = MatchQuery.of(m -> m.field(name).query(value))._toQuery();
            }
            queryList.add(query);
        }
        return queryList;
    }

    private List<Query> generateListQueries(Map<String, List<String>> queries) {
        List<Query> queryList = new ArrayList<>();
        try {
            for (Map.Entry<String, List<String>> entry : queries.entrySet()) {
                String name = entry.getKey();
                List<String> value = entry.getValue();
                List<FieldValue> fieldValues = value.stream().map(FieldValue::of).collect(Collectors.toList());
                TermsQuery termsQuery = TermsQuery.of(m -> m.field(name).terms(b -> b.value(fieldValues)));
                // Query query = TermQuery.of(m -> m.field(name).value(FieldValue.of(value)))._toQuery();
                queryList.add(termsQuery._toQuery());
            }
        } catch (Exception e) {
            log.error("Failed to generate list query", e);
        }
        return queryList;
    }

    private List<Query> generateRangeQueries(Map<String, Pair<String, String>> rangeQueries, boolean isTimeZone) {
        List<Query> queryList = new ArrayList<>();
        for (Map.Entry<String, Pair<String, String>> entry : rangeQueries.entrySet()) {
            Query query = RangeQuery.of(m -> {
                m.field(entry.getKey());
                Pair<String, String> pair = entry.getValue();
                if (!pair.key().isEmpty()) {
                    m.gte(JsonData.of(pair.key()));
                }
                if (!pair.value().isEmpty()) {
                    m.lt(JsonData.of(pair.value()));
                }
                if (isTimeZone) {
                    m.timeZone("+08:00");
                }
                return m;
            })._toQuery();
            queryList.add(query);
        }
        return queryList;

    }

    private List<SortOptions> generateSortOptions(Map<String, Boolean> sortFields) {
        List<FieldSort> fieldSorts = generateFieldSorts(sortFields);
        List<SortOptions> sortOptionList = new ArrayList<>();
        for (FieldSort sort : fieldSorts) {
            SortOptions sortOptions = SortOptions.of(f -> f.field(sort));
            sortOptionList.add(sortOptions);
        }
        return sortOptionList;
    }


    private List<FieldSort> generateFieldSorts(Map<String, Boolean> sortFields) {
        List<FieldSort> fieldSorts = new ArrayList<>();

        for (Map.Entry<String, Boolean> entry : sortFields.entrySet()) {
            FieldSort fieldSort = FieldSort.of(f -> f.field(entry.getKey())
                .order(entry.getValue() ? SortOrder.Asc : SortOrder.Desc));
            fieldSorts.add(fieldSort);
        }
        return fieldSorts;
    }


    private String formatIndex(String index) {
        String prefix = elasticSearchConfig.getPrefix();
        if (StringUtils.isEmpty(prefix)) {
            return index;
        }
        List<String> indexes = List.of(StringUtils.split(index, ','));
        return indexes.stream().map(ind -> prefix + "-" + ind).collect(Collectors.joining(","));
    }

    public <T> void bulkIndex(List<T> documents, String esIndex) throws IOException {
        String index = formatIndex(esIndex);
        log.info("Try to write barrage log to es {}: documents: {}", index, documents);
        BulkRequest.Builder br = new BulkRequest.Builder();
        for (Object doc : documents) {
            br.operations(op -> op.index(b -> b.index(index).document(doc)));
        }

        BulkResponse result = null;
        try {
            result = client.bulk(br.build());
        } catch (IOException e) {
            log.error("Fail to bulkIndex barrage log to es: {}, documents {}", e.getMessage(), documents, e);
            throw e;
        }
        log.info("Succeed to receive Es Result is : {}", result);
        if (result.errors()) {
            log.error("Bulk had errors");
            for (BulkResponseItem item : result.items()) {
                if (item.error() != null) {
                    log.error(item.error().reason());
                }
            }
        }
    }
}
