package com.baidu.acg.piat.digitalhuman.speech.authentication.service.impl;

import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;

import com.baidu.acg.piat.digitalhuman.speech.authentication.model.BarrageProfileStat;
import com.baidu.acg.piat.digitalhuman.speech.authentication.model.http.PageResult;
import com.baidu.acg.piat.digitalhuman.speech.authentication.model.search.BarrageProfileStatSearchCriteria;
import com.baidu.acg.piat.digitalhuman.speech.authentication.repository.BarrageProfileStatRepository;
import com.baidu.acg.piat.digitalhuman.speech.authentication.service.BarrageProfileStatService;

@Service
@RequiredArgsConstructor
public class BarrageProfileStatServiceImpl implements BarrageProfileStatService {

    private final BarrageProfileStatRepository barrageProfileStatRepository;

    @Override
    public PageResult<BarrageProfileStat> search(BarrageProfileStatSearchCriteria criteria) {
        Page<BarrageProfileStat> result = barrageProfileStatRepository
            .findByDatetimeBeginGreaterThanEqualAndDatetimeBeginLessThanAndPlatformAndTotalNumGreaterThanEqual(
                criteria.getBeginTime(),
                criteria.getEndTime(),
                criteria.getPlatform(),
                criteria.getTotalNumMin(),
                PageRequest.of(criteria.getPageNo(), criteria.getPageSize())
            );
        PageResult<BarrageProfileStat> pageResult
            = PageResult.of(result.getContent(), result.getNumber(), result.getSize(), result.getTotalElements());
        return pageResult;
    }
}
