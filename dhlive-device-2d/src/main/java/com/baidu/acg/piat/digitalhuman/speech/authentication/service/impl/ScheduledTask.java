package com.baidu.acg.piat.digitalhuman.speech.authentication.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.time.LocalDateTime;

import com.baidu.acg.piat.digitalhuman.speech.authentication.service.BarrageProfileStatCronService;

@Component
@Slf4j
@RequiredArgsConstructor
public class ScheduledTask {

    // private final ESClient esClient;
    private final BarrageProfileStatCronService barrageProfileStatCronService;


    @Value(value = "${digital-human.barrage-log.service.stat-duration-seconds:30}")
    private int statDurationSeconds;

    @Value(value = "${digital-human.device-2d.scheduler.stat-enabled: false}")
    private boolean isStatEnabled = true;

    @Scheduled(cron = "${digital-human.device-2d.scheduler.cron: 0/30 * * * * ?}")
    public void scheduleTaskWithInitialDelay() {
        if (!isStatEnabled) {
            log.debug("Skip to schedule barrage profile stat");
            return;
        }
        LocalDateTime statTime = LocalDateTime.now().minusMinutes(10);
        log.info("Fixed Schedule Task :: Execution Time: {}, Stat Begin Time: {}", LocalDateTime.now(), statTime);
        LocalDateTime statTimeEnd = statTime.plusSeconds(statDurationSeconds);
        try {
            barrageProfileStatCronService.writeBarrageStatToDB(statTime, statTimeEnd);
        } catch (IOException e) {
            log.error(e.getMessage(), e);
        }
        /*
        ElasticsearchClient client = esClient.getClient();
        SearchRequest request = new SearchRequest.Builder().index("barrage-profile-v2-2024.04.28").query(q->q.range(
            r-> r.timeZone("+08:00").field("@timestamp").gte(JsonData.of("2024-04-28T19:32:15.34"))
        )).build();
        try {
            SearchResponse<BarrageProfile> response = client.search(request, BarrageProfile.class);
            log.info("response: {}", response.hits().hits());
        } catch (IOException e) {
            log.error(e.getMessage(), e);
            throw new RuntimeException(e);
        }*/
        /*
        HashMap<String, Pair<String, String>> ranges = new HashMap<>();
        ranges.put("@timestamp", new Pair<String, String>("2024-04-28T18:00:00", "2024-04-28T19:00:00"));
        try {
            Pair<String, Boolean> sort = Pair.of("createTime.keyword", true);
            PageResult<BarrageProfile> response= esClient.search2("barrage-profile-v2-2024.04.28",
                new HashMap<>(),
                new HashMap<>(),
                ranges,0, 2,sort, BarrageProfile.class);
            log.debug("Succeed to obtain result total: {} ", response);
            for (BarrageProfile barrageProfile : response.getResult()) {
                log.info("barrage: {}, {}",barrageProfile.getLogId(), barrageProfile);
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new RuntimeException(e);
        }*/
        /*
        EsSearchCriteria esSearchCriteria = new EsSearchCriteria();
        esSearchCriteria.putExactListQuery("subType.keyword", List.of("start_handle_before_sleep", "receive"));
        esSearchCriteria.putSortField("createTime.keyword", true);
        esSearchCriteria.setFullEsIndex("barrage-profile-v2-2024.04.28");
        esSearchCriteria.putRange("@timestamp", "2024-04-28T19:32:15", "2024-04-28T19:40:15");
        esSearchCriteria.setSize(5);
        try {
            PageResult<BarrageProfile> response = esClient.search(esSearchCriteria, BarrageProfile.class);
            for (BarrageProfile barrageProfile : response.getResult()) {
                log.info("barrage: {}, {}", barrageProfile.getCreateTime(), barrageProfile);
            }
        } catch (IOException e) {
            log.error(e.getMessage(), e);
            throw new RuntimeException(e);
        }*/


    }


}
