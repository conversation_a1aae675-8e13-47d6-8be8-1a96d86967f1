package com.baidu.acg.piat.digitalhuman.speech.authentication.controller;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.baidu.acg.piat.digitalhuman.speech.authentication.exception.IExceptionCode;
import com.baidu.acg.piat.digitalhuman.speech.authentication.exception.InputException;
import com.baidu.acg.piat.digitalhuman.speech.authentication.exception.robotclient.RobotClientException;
import com.baidu.acg.piat.digitalhuman.speech.authentication.model.DeviceInfo;
import com.baidu.acg.piat.digitalhuman.speech.authentication.model.DeviceRuntime;
import com.baidu.acg.piat.digitalhuman.speech.authentication.service.DeviceService;
import com.baidu.acg.piat.digitalhuman.speech.authentication.service.ProjectAuthService;
import com.baidu.acg.piat.digitalhuman.speech.authentication.util.RequestUtil;

@RestController
@RequestMapping("/api/device-2d/v1/device")
@Slf4j
@Validated
@RequiredArgsConstructor
public class DeviceController {

    private final DeviceService deviceService;

    private final ProjectAuthService projectAuthService;

    @PostMapping("/info/web")
    public DeviceInfo createInfoByWeb(HttpServletRequest request,
                                      @Valid @RequestBody DeviceInfo deviceInfo) {
        String userId = RequestUtil.getUserId(request);
        log.info("Request for create deviceInfoWeb: {} {}", userId, deviceInfo);
        if (userId == null) {
            userId = "";
        }
        deviceInfo.setUserId(userId);
        DeviceInfo result = createInfo(deviceInfo);
        result.setId(0L);
        return result;
    }

    @PostMapping("/openapi/info/plugin")
    public DeviceInfo createInfoByPlugin(
        @RequestHeader Map<@NotBlank String, @NotBlank String> headers,
        @Valid @RequestBody DeviceInfo deviceInfo
    ) throws InputException, RobotClientException {
        log.info("Request for create deviceInfoPlugin: headers={}", headers);

        checkPluginHeaders(headers);

        return createInfo(deviceInfo);
    }

    private Map<String, String> checkPluginHeaders(Map<String, String> headers)
        throws InputException, RobotClientException {
        List<String> notEmptyHeaders = List.of("AK", "Authorization", "DeviceSn");
        List<String> optionalHeaders = List.of("Version", "x-request-id", "x-trace-id");

        Map<String, String> filteredHeaders = new HashMap<>();

        for (String h : notEmptyHeaders) {
            String value = headers.get(h.toLowerCase()); // header is case-insensitive
            if (value == null) {
                throw new InputException(IExceptionCode.ERROR_INPUT_VALIDATION, "Miss input of header: " + h);
            }
            filteredHeaders.put(h, value);
        }
        for (String h : optionalHeaders) {
            String value = headers.get(h.toLowerCase());

            if (value != null) {
                filteredHeaders.put(h, value);
            }
        }
        if (!projectAuthService.auth(filteredHeaders)) {
            throw new InputException(IExceptionCode.ERROR_AUTH_INPUT_HEADERS, "Auth failed for headers");
        }
        return filteredHeaders;
    }


    private DeviceInfo createInfo(DeviceInfo deviceInfo) {
        deviceInfo.setId(0L);
        deviceInfo.setCreateTime(null);
        deviceInfo.setUpdateTime(null);
        deviceInfo = deviceService.saveDeviceInfo(deviceInfo);
        return deviceInfo;
    }

    @PostMapping("/openapi/runtime/plugin")
    public DeviceRuntime createRuntime(
        @RequestHeader Map<@NotBlank String, @NotBlank String> headers,
        @Valid @RequestBody DeviceRuntime deviceRuntime) throws RobotClientException, InputException {
        log.info("Request for create createRuntime: headers={}", headers);
        checkPluginHeaders(headers);
        deviceRuntime.setId(0L);
        deviceService.saveDeviceRuntime(deviceRuntime);
        return deviceRuntime;
    }

}
