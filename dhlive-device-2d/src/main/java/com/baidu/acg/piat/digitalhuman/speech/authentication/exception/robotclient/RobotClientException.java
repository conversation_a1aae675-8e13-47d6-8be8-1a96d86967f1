package com.baidu.acg.piat.digitalhuman.speech.authentication.exception.robotclient;

import com.baidu.acg.piat.digitalhuman.speech.authentication.exception.BusinessException;

public class RobotClientException extends BusinessException {
    public RobotClientException(String message) {
        super(ERROR_AUTH_PROJECT_CLIENT, message);
    }

    public RobotClientException(String message, Throwable cause) {
        super(ERROR_AUTH_PROJECT_CLIENT, message, cause);
    }
}
