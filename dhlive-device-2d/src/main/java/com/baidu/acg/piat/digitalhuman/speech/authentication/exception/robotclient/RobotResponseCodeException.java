package com.baidu.acg.piat.digitalhuman.speech.authentication.exception.robotclient;

import com.baidu.acg.piat.digitalhuman.speech.authentication.exception.BusinessException;

public class RobotResponseCodeException extends BusinessException {
    public RobotResponseCodeException(String message) {
        super(ERROR_AUTH_PROJECT_CLIENT_RESPONSE, message);
    }

    public RobotResponseCodeException(String message, Throwable cause) {
        super(ERROR_AUTH_PROJECT_CLIENT_RESPONSE, message, cause);
    }
}
