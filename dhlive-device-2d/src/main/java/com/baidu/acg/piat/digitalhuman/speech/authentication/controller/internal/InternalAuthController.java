package com.baidu.acg.piat.digitalhuman.speech.authentication.controller.internal;


import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestAttribute;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.LinkedHashMap;
import java.util.Map;

import com.baidu.acg.piat.digitalhuman.speech.authentication.exception.IExceptionCode;
import com.baidu.acg.piat.digitalhuman.speech.authentication.exception.InputException;

@RestController
@RequestMapping("/api/v1/device-2d/internal/openapi/auth")
@Slf4j
@Validated
@RequiredArgsConstructor
public class InternalAuthController {

    private static final String[] ATTRIBUTE_AUTH_STRING_NAMES = {"username", "accountId", "uid", "bceAccountId",
        "bceUserId", "tags", "accountMenus", "roleLevel", "userChannel", "bceSessionContext"};


    @RequestMapping("error")
    public void authFailed(@RequestAttribute(value = "errorCode", required = false) Integer errorNo,
                           @RequestAttribute(value = "errorMsg", required = false) String errorMsg)
        throws InputException {
        if (errorNo == null) {
            errorNo = IExceptionCode.ERROR_REWRITE_UNKNOWN;
        }
        if (errorMsg == null) {
            errorMsg = "Internal error message unknown";
        }
        throw new InputException(errorNo, errorMsg);
    }

    @RequestMapping("openapi/respond")
    public Map<String, Object> authRespond(HttpServletRequest request) throws InputException {
        Map<String, Object> authInfos;

        authInfos = parseAuthRequestFromAttribute(request);
        if (!authInfos.containsKey("username")) {
            log.error("Fail to find username in attributes : {}", authInfos);
            throw new InputException(IExceptionCode.ERROR_AUTH_BCE_DHUSER_EMPTY,
                "Fail to find username in attributes");
        }
        return authInfos;
    }

    public Map<String, Object> parseAuthRequestFromAttribute(HttpServletRequest request) {
        Map<String, Object> authInfos = new LinkedHashMap<>();
        for (String attributeName : ATTRIBUTE_AUTH_STRING_NAMES) {
            Object attributeValue = request.getAttribute(attributeName);
            if (attributeValue != null) {
                authInfos.put(attributeName, attributeValue);
            }
        }
        return authInfos;
    }

}
