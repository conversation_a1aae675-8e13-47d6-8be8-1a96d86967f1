package com.baidu.acg.piat.digitalhuman.speech.authentication.client;

import co.elastic.clients.util.Pair;
import lombok.Data;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Data
public class EsSearchCriteria {

    Map<String, String> containQueries = new HashMap<>();

    Map<String, String> exactQueries = new HashMap<>();

    Map<String, List<String>> exactListQueries = new HashMap<>();

    Map<String, Pair<String, String>> ranges = new HashMap<>();

    Map<String, Boolean> sortFields = new HashMap<>();

    int from = 0;

    int size = 9999;

    /**
     * 查询的索引名称 , 会在EsClient中加上前缀
     */
    private String esIndex;

    /**
     * 查询的索引名称 , 不会在EsClient中加上前缀，fullEsIndex优先级高于esIndex
     */
    private String fullEsIndex;

    private boolean needTotal = true;

    public boolean putExactQuery(String field, String value) {
        String result = exactQueries.putIfAbsent(field, value);
        return result == null;
    }

    public boolean putContainQuery(String field, String value) {
        String result = containQueries.putIfAbsent(field, value);
        return result == null;
    }

    public boolean putSortField(String field, boolean asc) {
        Boolean result = sortFields.putIfAbsent(field, asc);
        return result == null;
    }

    public void removeSortField(String field) {
        sortFields.remove(field);
    }

    public void putRange(String field, String fromInclusive, String toNotInclusive) {
        ranges.put(field, Pair.of(fromInclusive, toNotInclusive));
    }

    public boolean putExactListQuery(String field, List<String> values) {
        List<String> result = exactListQueries.putIfAbsent(field, values);
        return result == null;
    }

    public void removeExactListQuery(String field) {
        exactListQueries.remove(field);
    }


    public void setPageAll() {
        from = 0;
        size = 9999;
        needTotal = false;
    }

}
