package com.baidu.acg.piat.digitalhuman.speech.authentication.util;

import org.springframework.data.domain.Page;

import com.baidu.acg.piat.digitalhuman.speech.authentication.model.http.PageResult;

public class JpaUtil {

    public static <T> PageResult<T> toPageResult(Page<T> page) {
        return PageResult.of(page.getContent(), page.getNumber(), page.getSize(), page.getTotalElements());
    }
}
