package com.baidu.acg.piat.digitalhuman.speech.authentication.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.hibernate.annotations.CreationTimestamp;

import javax.persistence.Convert;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Transient;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

import com.baidu.acg.piat.digitalhuman.speech.authentication.repository.converter.JpaMapObjectConverterJson;

@Data
@Entity(name = "barrage_profile_stat")
@NoArgsConstructor
public class BarrageProfileStat {

    public static final String RECEIVE_SUB_TYPE = "receive";

    public static final String FAQ_NOT_OPEN_SUB_TYPE = "not_open_ai_faq";

    @Transient
    @ToString.Exclude
    @JsonIgnore
    private Map<String, List<BarrageProfile>> barrageProfileMap = new LinkedHashMap<>();

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    private LocalDateTime datetimeBegin;

    private LocalDateTime datetimeEnd;

    @Enumerated(EnumType.STRING)
    private PlatformEnum platform;

    private int totalNum = 0;

    private int normalNum = 0;

    private int notHitNum = 0;

    private int exceptionNum = 0;

    private long firstPackageTotalTime = 0;

    private int firstPackageNum = 0;

    private long firstPackageMaxTime = Long.MIN_VALUE;

    private long receiveCostMaxTime = Long.MIN_VALUE;


    private long receiveCostTotalTime = 0;

    private int receiveCostNum = 0;

    private long docHittingTotalTime = 0;

    private int docHittingNum = 0;


    private long faqHittingTotalTime = 0;

    private int faqHittingNum = 0;


    private long spokeReplyFirstHittingTotalTime = 0;

    private int spokeReplyFirstHittingNum = 0;


    private long spokeReplyLastHittingTotalTime = 0;

    private int spokeReplyLastHittingNum = 0;


    private String statError = "";

    @CreationTimestamp
    private LocalDateTime createTime;

    @Transient
    @ToString.Exclude
    @JsonIgnore
    private Map<Status, List<String>> logIds = new LinkedHashMap<>();

    @Transient
    @ToString.Exclude
    @JsonIgnore
    private Map<String, Integer> platformNums = new LinkedHashMap<>();

    @Convert(converter = JpaMapObjectConverterJson.class)
    private Map<String, Object> debugInfo = new LinkedHashMap<>();

    public void addBarrageProfileStat(BarrageProfile barrageProfile) {
        String logId = barrageProfile.getLogId();
        barrageProfileMap.putIfAbsent(logId, new ArrayList<>());
        barrageProfileMap.get(logId).add(barrageProfile);
    }

    public void putBarrageProfileStat(String logId, List<BarrageProfile> barrageProfiles) {
        barrageProfileMap.put(logId, barrageProfiles);
    }


    public Set<String> getLogIds() {
        return barrageProfileMap.keySet();
    }

    public void incrPlatform(String platform) {
        platformNums.putIfAbsent(platform, 0);
        platformNums.put(platform, platformNums.get(platform) + 1);
        totalNum++;

    }

    public void incrStatus(Status status, String logId) {
        logIds.putIfAbsent(status, new ArrayList<>());
        logIds.get(status).add(logId);
        if (status == Status.NORMAL) {
            normalNum++;
        } else if (status == Status.NOT_HIT) {
            notHitNum++;
        } else if (status == Status.EXCEPTION) {
            exceptionNum++;
        }
    }

    public void addStat(StatField statField, long time) {
        if (statField == StatField.FIRST_PACKAGE_TIME) {
            firstPackageTotalTime += time;
            firstPackageNum++;
        } else if (statField == StatField.RECEIVE_COST_TIME) {
            receiveCostTotalTime += time;
            receiveCostNum++;
        } else if (statField == StatField.DOC_AI_HITTING_TIME) {
            docHittingTotalTime += time;
            docHittingNum++;
        } else if (statField == StatField.FAQ_HITTING_TIME) {
            faqHittingTotalTime += time;
            faqHittingNum++;
        } else if (statField == StatField.SPOKEN_REPLY_FIRST_HITTING_TIME) {
            spokeReplyFirstHittingTotalTime += time;
            spokeReplyFirstHittingNum++;
        } else if (statField == StatField.SPOKEN_REPLY_LAST_HITTING_TIME) {
            spokeReplyLastHittingTotalTime += time;
            spokeReplyLastHittingNum++;
        }

        if (statField == StatField.FIRST_PACKAGE_TIME && firstPackageMaxTime < time) {
            firstPackageMaxTime = time;
        } else if (statField == StatField.RECEIVE_COST_TIME && receiveCostMaxTime < time) {
            receiveCostMaxTime = time;
        }
    }

    public void addStatError(String errMessage) {
        if (!statError.isEmpty()) {
            statError += ";" + errMessage;
        } else {
            statError = errMessage;
        }
        statError = statError.substring(0, 1024);
    }

    public void finishStat() {
        debugInfo.put("logIds", logIds);
        debugInfo.put("platformNums", platformNums);
        if (receiveCostMaxTime == Long.MIN_VALUE) {
            receiveCostMaxTime = 0;
        }
        if (firstPackageMaxTime == Long.MIN_VALUE) {
            firstPackageMaxTime = 0;
        }
    }


    public enum StatField {

        RECEIVE_COST_TIME,

        FIRST_PACKAGE_TIME,
        FAQ_HITTING_TIME,
        DOC_AI_HITTING_TIME,

        SPOKEN_REPLY_FIRST_HITTING_TIME,
        SPOKEN_REPLY_LAST_HITTING_TIME,

    }

    public enum Status {
        NORMAL,

        NOT_HIT,
        EXCEPTION
    }

}
