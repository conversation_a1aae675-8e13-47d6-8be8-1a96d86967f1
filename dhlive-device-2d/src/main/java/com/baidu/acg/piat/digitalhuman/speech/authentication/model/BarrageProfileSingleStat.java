package com.baidu.acg.piat.digitalhuman.speech.authentication.model;

import lombok.Data;

import java.util.List;

@Data
public class BarrageProfileSingleStat extends BarrageProfile {

    private long receiveCost;

    private long firstPackageTime = -1;

    private boolean isAiDocEnabled = false;

    private long spokenReplyFirstHitCost = -1;

    private long spokenReplyLastHitCost = -1;

    private List<BarrageProfile> logs;

    private int logsNum = 0;


    public void fillBarrageProfileStat(BarrageProfileStat barrageProfileStat) {
        receiveCost = barrageProfileStat.getReceiveCostMaxTime();
        firstPackageTime = barrageProfileStat.getFirstPackageMaxTime();
        spokenReplyFirstHitCost = barrageProfileStat.getSpokeReplyFirstHittingTotalTime();
        spokenReplyLastHitCost = barrageProfileStat.getSpokeReplyLastHittingTotalTime();

    }

    public void setLogs(List<BarrageProfile> logs) {
        this.logs = logs;
        logsNum = logs.size();
    }
}
