package com.baidu.acg.piat.digitalhuman.speech.authentication.model.http;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class AisudaResponse<T> {
    private int status = 0;

    private String msg = "";

    private String logId = "";

    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Object data;

    public static <T> AisudaResponse<T> from(PageResult<T> pageResult, String logId) {
        AisudaPageData<T> data = new AisudaPageData<T>();
        data.items = pageResult.getResult();
        data.total = pageResult.getTotalCount();
        AisudaResponse<T> response = new AisudaResponse<T>();
        response.data = data;
        response.logId = logId;
        return response;
    }

    public static <T> AisudaResponse<T> from(HttpResponse<T> res) {
        AisudaResponse<T> response = new AisudaResponse<>();
        response.status = res.getCode();
        if (response.status == 0 && !res.isSuccess()) {
            response.status = 500;
        }
        response.msg = res.getMessage().getGlobal();
        response.data = res.getResult();
        response.logId = res.getLogId();
        return response;
    }


    @Data
    public static class AisudaPageData<T> {

        private long total = 0;

        private List<T> items = new ArrayList<>();

    }

}
