package com.baidu.acg.piat.digitalhuman.speech.authentication.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.UUID;

import com.baidu.acg.piat.digitalhuman.speech.authentication.client.RobotHttpClient;
import com.baidu.acg.piat.digitalhuman.speech.authentication.exception.robotclient.RobotClientException;
import com.baidu.acg.piat.digitalhuman.speech.authentication.service.ProjectAuthService;

@Slf4j
@Service
@RequiredArgsConstructor
public class ProjectAuthServiceImpl implements ProjectAuthService {

    private final RobotHttpClient robotHttpClient;

    @Override
    public boolean auth(Map<String, String> authHeaders) throws RobotClientException {
        log.info("Try to auth: {}", authHeaders);
        String requestId = authHeaders.get("x-request-id");
        if (requestId != null) {
            requestId = "device-2d+" + requestId;
        } else {
            requestId = "device-2d-" + UUID.randomUUID().toString();
        }
        authHeaders.put("x-request-id", requestId);

        boolean isSuccessful = robotHttpClient.auth(authHeaders);
        return isSuccessful;
    }
}
