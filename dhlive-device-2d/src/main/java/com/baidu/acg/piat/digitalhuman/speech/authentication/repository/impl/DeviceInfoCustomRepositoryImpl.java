package com.baidu.acg.piat.digitalhuman.speech.authentication.repository.impl;

import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.stereotype.Repository;

import javax.persistence.EntityManager;
import javax.persistence.TypedQuery;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import com.baidu.acg.piat.digitalhuman.speech.authentication.model.DeviceInfo;
import com.baidu.acg.piat.digitalhuman.speech.authentication.model.search.RuntimeSearchCriteria;
import com.baidu.acg.piat.digitalhuman.speech.authentication.repository.DeviceInfoCustomRepository;

@Repository
@RequiredArgsConstructor
public class DeviceInfoCustomRepositoryImpl implements DeviceInfoCustomRepository {

    private final EntityManager em;

    @Override
    public Page<DeviceInfo> findAll(RuntimeSearchCriteria criteria) {
        CriteriaBuilder cb = em.getCriteriaBuilder();
        List<Predicate> predicates = new ArrayList<>();
        CriteriaQuery<DeviceInfo> cq = cb.createQuery(DeviceInfo.class);
        Root<DeviceInfo> root = cq.from(DeviceInfo.class);
        if (StringUtils.isNotEmpty(criteria.getUserId())) {
            predicates.add(cb.equal(root.get("userId"), criteria.getUserId()));
        }

        if (criteria.getGatherType() != null) {
            predicates.add(cb.equal(root.get("gatherType"), criteria.getGatherType().name()));
        }

        if (criteria.getBeginTime() != null) {
            predicates.add(cb.greaterThanOrEqualTo(root.<LocalDateTime>get("createTime"), criteria.getBeginTime()));
        }
        if (criteria.getEndTime() != null) {
            predicates.add(cb.lessThanOrEqualTo(root.<LocalDateTime>get("createTime"), criteria.getEndTime()));
        }
        Predicate[] predicatesArray = predicates.toArray(new Predicate[0]);
        cq.where(predicatesArray);
        cq.orderBy(cb.desc(root.<Date>get("updateTime")));

        TypedQuery<DeviceInfo> typedQuery
            = em.createQuery(cq).setMaxResults(criteria.getPageSize())
            .setFirstResult(criteria.getPageNo() * criteria.getPageSize());
        List<DeviceInfo> deviceInfos = typedQuery.getResultList();


        return new PageImpl<>(deviceInfos, criteria.getPageable(), totalCount(predicatesArray));
    }

    private long totalCount(Predicate[] predicatesArray) {
        CriteriaBuilder qbCount = em.getCriteriaBuilder();
        CriteriaQuery<Long> cqCount = qbCount.createQuery(Long.class);
        cqCount.select(qbCount.count(cqCount.from(DeviceInfo.class)));
        cqCount.where(predicatesArray);
        return em.createQuery(cqCount).getSingleResult();
    }

}
