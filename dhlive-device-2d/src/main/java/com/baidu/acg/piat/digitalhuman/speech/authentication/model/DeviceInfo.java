package com.baidu.acg.piat.digitalhuman.speech.authentication.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import javax.persistence.Convert;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Objects;

import com.baidu.acg.piat.digitalhuman.speech.authentication.repository.converter.JpaListMapConverterJson;
import com.baidu.acg.piat.digitalhuman.speech.authentication.repository.converter.JpaMapConverterJson;

@Data
@Entity(name = "device_info")
public class DeviceInfo {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    private String userId;

    @NotBlank
    @NotNull
    private String deviceId;

    private String gatherType;

    private String containerType;

    private String containerVersion;

    private String osPlatform;

    private String osVersion;

    @Convert(converter = JpaMapConverterJson.class)
    private HashMap<String, String> osInfo = new HashMap<>();


    @Convert(converter = JpaListMapConverterJson.class)
    private List<HashMap<String, String>> cpuInfo = new ArrayList<>();

    private int memorySize;

    @Convert(converter = JpaListMapConverterJson.class)
    private List<HashMap<String, String>> memoryInfo = new ArrayList<>();

    @Convert(converter = JpaListMapConverterJson.class)
    private List<HashMap<String, String>> gpuInfo = new ArrayList<>();


    @Convert(converter = JpaListMapConverterJson.class)
    private List<HashMap<String, String>> diskInfo = new ArrayList<>();

    @Convert(converter = JpaMapConverterJson.class)
    private HashMap<String, String> extra = new HashMap<>();


    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    @CreationTimestamp
    private LocalDateTime createTime;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    @UpdateTimestamp
    // @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ssZ")
    private LocalDateTime updateTime;

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        DeviceInfo that = (DeviceInfo) o;
        return memorySize == that.memorySize
                && Objects.equals(userId, that.userId)
                && Objects.equals(deviceId, that.deviceId)
                && Objects.equals(gatherType, that.gatherType)
                && Objects.equals(containerType, that.containerType)
                && Objects.equals(containerVersion, that.containerVersion)
                && Objects.equals(osPlatform, that.osPlatform)
                && Objects.equals(osVersion, that.osVersion)
                && Objects.equals(osInfo, that.osInfo)
                && Objects.equals(cpuInfo, that.cpuInfo)
                && Objects.equals(memoryInfo, that.memoryInfo)
                && Objects.equals(gpuInfo, that.gpuInfo)
                && Objects.equals(diskInfo, that.diskInfo);
    }

    @Override
    public int hashCode() {
        return Objects.hash(userId, deviceId, gatherType, containerType,
                containerVersion, osPlatform, osVersion, osInfo, cpuInfo,
                memorySize, memoryInfo, gpuInfo, diskInfo);
    }

}
