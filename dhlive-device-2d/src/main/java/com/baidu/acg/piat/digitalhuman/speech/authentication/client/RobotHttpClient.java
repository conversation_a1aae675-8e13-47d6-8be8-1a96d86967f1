package com.baidu.acg.piat.digitalhuman.speech.authentication.client;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import retrofit2.Call;
import retrofit2.Response;
import retrofit2.Retrofit;
import retrofit2.converter.jackson.JacksonConverterFactory;

import java.io.IOException;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import com.baidu.acg.piat.digitalhuman.speech.authentication.client.service.BoardcastProjectService;
import com.baidu.acg.piat.digitalhuman.speech.authentication.config.RobotConfig;
import com.baidu.acg.piat.digitalhuman.speech.authentication.exception.robotclient.RobotClientException;
import com.baidu.acg.piat.digitalhuman.speech.authentication.exception.robotclient.RobotResponseCodeException;
import com.baidu.acg.piat.digitalhuman.speech.authentication.model.robot.RobotResponse;

@RequiredArgsConstructor
@Slf4j
public class RobotHttpClient {

    private final RobotConfig robotConfig;

    private final Retrofit retrofit;

    private final BoardcastProjectService robotProjectService;

    public RobotHttpClient(RobotConfig robotConfig) {
        this.robotConfig = robotConfig;
        if (!robotConfig.isEnabled()) {
            log.warn("Skip to RobotHttpClient since RobotConfig is disabled");

            retrofit = null;
            robotProjectService = null;
            return;
        }

        OkHttpClient httpClient = new OkHttpClient.Builder()
            .connectTimeout(robotConfig.getConnectTimeout(), TimeUnit.MILLISECONDS)
            .readTimeout(robotConfig.getReadTimeout(), TimeUnit.MILLISECONDS)
            .writeTimeout(robotConfig.getWriteTimeout(), TimeUnit.MILLISECONDS)
            .build();

        ObjectMapper objectMapper = new ObjectMapper().findAndRegisterModules()
            .configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);

        retrofit = new Retrofit.Builder()
            .addConverterFactory(JacksonConverterFactory.create(objectMapper))
            .baseUrl(robotConfig.getBaseUrl())
            .client(httpClient)
            .build();
        robotProjectService = retrofit.create(BoardcastProjectService.class);
    }

    public boolean auth(Map<String, String> headers) throws RobotClientException {
        try {
            call(robotProjectService.projectAuth(headers));
            return true;
        } catch (RobotResponseCodeException e) {
            log.warn("Fail to Auth :" + e.getMessage());
            return false;
        }
    }

    private <T> RobotResponse<T> call(Call<RobotResponse<T>> call) throws RobotClientException, RobotResponseCodeException {
        if (!robotConfig.isEnabled()) {
            throw new RobotClientException("Device2dConfig is disabled");
        }
        // log.debug("====CALL headers: " + call.request().headers());
        String url = call.request().url().toString();
        try {
            Response<RobotResponse<T>> response = call.execute();
            if (!response.isSuccessful()) {
                throw new RobotClientException(
                    "HttpResponse code: " + response.code() + ";  + url: "
                        + url + "; message:" + response.message());
            }
            RobotResponse<T> body = response.body();
            if (body == null || body.getCode() != 0) {
                throw new RobotResponseCodeException("Response body is not success: " + body
                    + "; url: " + url);
            }
            return response.body();
        } catch (IOException e) {
            throw new RobotClientException("Call url failed :" + e.getMessage() + "; url: " + url, e);
        }
    }


}
