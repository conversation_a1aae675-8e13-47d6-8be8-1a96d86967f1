package com.baidu.acg.piat.digitalhuman.speech.authentication.repository.converter;

import com.fasterxml.jackson.core.JsonProcessingException;
import lombok.extern.slf4j.Slf4j;

import javax.persistence.AttributeConverter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

import com.baidu.acg.piat.digitalhuman.speech.authentication.util.JsonUtil;

@Slf4j
public class JpaListMapConverterJson implements AttributeConverter<List<HashMap<String, String>>, String> {
    @Override
    public String convertToDatabaseColumn(List<HashMap<String, String>> o) {
        try {
            return JsonUtil.writeValueAsString(o);
        } catch (JsonProcessingException e) {
            log.warn("Catch json serialize error", e);
            return "";
        }
    }

    @Override
    public List<HashMap<String, String>> convertToEntityAttribute(String s) {
        try {
            return JsonUtil.readValue(s, List.class);
        } catch (JsonProcessingException e) {
            log.warn("Catch json parse error", e);
            return new ArrayList<>();
        }
    }
}
