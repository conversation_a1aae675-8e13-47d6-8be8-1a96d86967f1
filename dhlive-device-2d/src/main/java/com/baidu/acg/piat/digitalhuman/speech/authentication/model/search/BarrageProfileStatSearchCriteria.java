package com.baidu.acg.piat.digitalhuman.speech.authentication.model.search;


import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;

import com.baidu.acg.piat.digitalhuman.speech.authentication.exception.InputException;
import com.baidu.acg.piat.digitalhuman.speech.authentication.model.PlatformEnum;

@EqualsAndHashCode(callSuper = true)
@Data
@ToString(callSuper = true)
public class BarrageProfileStatSearchCriteria extends TimeRangePageableCriteria {

    PlatformEnum platform;

    @Min(0)
    @Max(100)
    Integer totalNumMin = 0;


    @Override
    public void fixData() throws InputException {
        super.fixData();
        if (platform == null) {
            platform = PlatformEnum.ALL;
        }
        if (totalNumMin == null) {
            totalNumMin = 0;
        }
    }
}
