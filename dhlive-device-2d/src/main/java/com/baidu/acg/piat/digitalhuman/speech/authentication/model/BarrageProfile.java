package com.baidu.acg.piat.digitalhuman.speech.authentication.model;


import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.core.JsonProcessingException;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.Max;
import javax.validation.constraints.NotBlank;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import com.baidu.acg.piat.digitalhuman.speech.authentication.util.JsonUtil;


@Data
@Slf4j
public class BarrageProfile {

    private static final int DEFAULT_VERSION = 1;

    private String userId;

    private String nickName;

    private PlatformEnum platform;

    @NotBlank
    @Length(max = 64)
    private String logId;

    @Length(max = 32)
    private String type = "LOG_BARRAGE_CHAIN";

    @NotBlank
    @Length(max = 32)
    private String subType;

    @Max(10)
    private int version = DEFAULT_VERSION;

    @Max(10000)
    private int index;

    @NotBlank
    @Length(min = 1, max = 1024)
    private String message;

    private long cost = 0;

    @Length(max = 512)
    private String mark = "";

    // private Map<String, Object> content = new HashMap<>();

    private String content = "{}";

    @JsonIgnore
    private volatile Map<String, Object> contentMap = null;

    private String container = "";

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonProperty(value = "localCreateTime", access = JsonProperty.Access.WRITE_ONLY)
    private Date localCreateTime;

    private String createTime;

    @JsonProperty("@timestamp")
    private String timestampStr;


    public Map<String, Object> getContentMap() {
        if (contentMap == null) {
            try {
                contentMap = JsonUtil.readValue(content, HashMap.class);
            } catch (JsonProcessingException e) {
                log.warn("Parse Barrage profile error :{}", e.getMessage());
                contentMap = new HashMap<>();
            }
        }
        return contentMap;
    }

    public Object getContentKey(String key) {
        return getContentMap().get(key);
    }


    public void fixData() {
        if (localCreateTime != null) {
            ZonedDateTime zonedDateTime = ZonedDateTime.ofInstant(localCreateTime.toInstant(), ZoneId.systemDefault());
            createTime = zonedDateTime.toString();
        }
        ZonedDateTime timestamp = ZonedDateTime.now().withZoneSameInstant(ZoneId.of("+0800"));
        timestampStr = timestamp.toString();
    }

}