package com.baidu.acg.piat.digitalhuman.speech.authentication.model.search;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;

import com.baidu.acg.piat.digitalhuman.speech.authentication.exception.IExceptionCode;
import com.baidu.acg.piat.digitalhuman.speech.authentication.exception.InputException;

@Data
@Slf4j
public class TimeRangePageableCriteria {

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
    public LocalDateTime beginTime;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
    public LocalDateTime endTime;

    public String dateRange;

    @Min(value = 0)
    private Integer pageNo = 0;

    @Min(value = 0)
    @Max(value = 10000)
    private Integer pageSize = 100;

    public void fixData() throws InputException {
        if (pageNo == null) {
            pageNo = 0;
        }
        if (pageSize == null) {
            pageSize = 100;
        }

        if (StringUtils.isEmpty(dateRange)) {
            return;
        }
        String[] ranges = dateRange.split(",");
        if (ranges.length != 2) {
            throw new InputException(IExceptionCode.ERROR_INPUT_VALIDATION, "range cannot split by comma :" +  dateRange);
        }
        try{
            long begin = Long.parseLong(ranges[0]);
            long end = Long.parseLong(ranges[1]);
            beginTime = LocalDateTime.ofInstant(Instant.ofEpochMilli(begin * 1000), ZoneId.systemDefault());
            endTime = LocalDateTime.ofInstant(Instant.ofEpochMilli(end * 1000), ZoneId.systemDefault());
        }catch (RuntimeException e){
            log.warn("Fail to parse range: " + dateRange, e);
            throw new InputException(IExceptionCode.ERROR_INPUT_VALIDATION, "range cannot be parsed :"
                +  dateRange + ";" + e.getMessage());
        }
    }

    public void fixData(boolean beginTimeRequired, boolean endTimeRequired) throws InputException {
        fixData();
        if (beginTimeRequired && beginTime == null) {
            throw new InputException(IExceptionCode.ERROR_INPUT_VALIDATION, "beginTime is required");
        }
        if (endTimeRequired && endTime == null) {
            throw new InputException(IExceptionCode.ERROR_INPUT_VALIDATION, "endTimeRequired is required");
        }
    }

    public Pageable getPageable(){
        return PageRequest.of(pageNo, pageSize);
    }

}
