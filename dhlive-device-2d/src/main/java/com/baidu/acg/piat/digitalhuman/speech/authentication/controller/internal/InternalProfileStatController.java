package com.baidu.acg.piat.digitalhuman.speech.authentication.controller.internal;


import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.io.IOException;
import java.time.LocalDateTime;

import com.baidu.acg.piat.digitalhuman.speech.authentication.exception.IExceptionCode;
import com.baidu.acg.piat.digitalhuman.speech.authentication.exception.InputException;
import com.baidu.acg.piat.digitalhuman.speech.authentication.model.BarrageProfileSingleStat;
import com.baidu.acg.piat.digitalhuman.speech.authentication.model.BarrageProfileStat;
import com.baidu.acg.piat.digitalhuman.speech.authentication.model.http.PageResult;
import com.baidu.acg.piat.digitalhuman.speech.authentication.model.search.BarrageProfileStatSearchCriteria;
import com.baidu.acg.piat.digitalhuman.speech.authentication.model.search.BarrageSearchCriteria;
import com.baidu.acg.piat.digitalhuman.speech.authentication.service.BarrageProfileService;
import com.baidu.acg.piat.digitalhuman.speech.authentication.service.BarrageProfileStatCronService;
import com.baidu.acg.piat.digitalhuman.speech.authentication.service.BarrageProfileStatService;

@RestController
@RequestMapping("/api/device-2d/v1/internal/openapi/profile/stat")
@Slf4j
@Validated
@RequiredArgsConstructor
public class InternalProfileStatController {

    private final BarrageProfileStatService barrageProfileStatService;

    private final BarrageProfileStatCronService barrageProfileStatCronService;

    private final BarrageProfileService barrageProfileService;

    @GetMapping("/fill")
    public void fill(@Valid @NotNull @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss") LocalDateTime beginTime,
                     @Valid @NotNull @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss") LocalDateTime endTime)
        throws IOException {
        barrageProfileStatCronService.writeBarrageStatToDBAsync(beginTime, endTime);
    }


    @GetMapping("")
    public PageResult<BarrageProfileStat> list(@Valid BarrageProfileStatSearchCriteria criteria) throws InputException {
        criteria.fixData();
        return barrageProfileStatService.search(criteria);
    }


    @ResponseBody
    @GetMapping("/single")
    public PageResult<BarrageProfileSingleStat> getSingleStats(@Valid BarrageSearchCriteria barrageSearchCriteria)
        throws IOException, InputException {
        log.info("Request getSingleStat barrageSearchCriteria: {}", barrageSearchCriteria);
        barrageSearchCriteria.fixData(true, true);
        if (barrageSearchCriteria.getBeginTime().isAfter(barrageSearchCriteria.getEndTime())) {
            throw new InputException(IExceptionCode.ERROR_INPUT_VALIDATION, "BeginTime should be before endTime :"
                + barrageSearchCriteria.getBeginTime() + ";" + barrageSearchCriteria.getEndTime(),
                "beginTime");
        }
        if (barrageSearchCriteria.getBeginTime().plusDays(15).isBefore(LocalDateTime.now())) {
            throw new InputException(IExceptionCode.ERROR_INPUT_VALIDATION,
                "Fail to check dateRanges : only support data in 15 days; "
                    + barrageSearchCriteria.getBeginTime(), "beginTime");
        }
        return barrageProfileService.searchBarrageProfiles(barrageSearchCriteria);
    }
}
