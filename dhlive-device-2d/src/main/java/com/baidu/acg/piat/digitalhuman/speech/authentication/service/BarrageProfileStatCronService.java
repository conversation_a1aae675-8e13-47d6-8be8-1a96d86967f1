package com.baidu.acg.piat.digitalhuman.speech.authentication.service;

import java.io.IOException;
import java.time.LocalDateTime;

public interface BarrageProfileStatCronService {

    /**
     * @param beginTime
     * @param endTime
     * @throws IOException
     */
    void writeBarrageStatToDBAsync(LocalDateTime beginTime, LocalDateTime endTime) throws IOException;

    void writeBarrageStatToDB(LocalDateTime beginTime, LocalDateTime endTime) throws IOException;
}
