package com.baidu.acg.piat.digitalhuman.speech.authentication.util;

import javax.servlet.http.HttpServletRequest;

public class RequestUtil {
    public static String getUserId(HttpServletRequest request) {
        Object username = request.getAttribute("username");
        if (username == null) {
            username = request.getHeader("username");
        }
        return username == null ? "" : username.toString();
    }
}
