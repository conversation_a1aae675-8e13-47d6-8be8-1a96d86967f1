package com.baidu.acg.piat.digitalhuman.speech.authentication.model.search;

import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;

@Data
public class LogSearchCriteria {

    private String date;

    private String logId;

    private String content;

    private String userId;

    private String type;

    private String subType;

    private String mark;

    @Min(0)
    @Max(100)
    private int pageNo = 0;

    @Max(1000)
    private int pageSize = 100;
}
