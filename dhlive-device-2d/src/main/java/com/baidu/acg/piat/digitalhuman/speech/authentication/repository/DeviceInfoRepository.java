package com.baidu.acg.piat.digitalhuman.speech.authentication.repository;

import org.springframework.data.repository.CrudRepository;

import java.util.Optional;

import com.baidu.acg.piat.digitalhuman.speech.authentication.model.DeviceInfo;

public interface DeviceInfoRepository extends CrudRepository<DeviceInfo, Long> {

    Optional<DeviceInfo> findFirstByUserIdOrderByUpdateTimeDesc(String userId);
}
