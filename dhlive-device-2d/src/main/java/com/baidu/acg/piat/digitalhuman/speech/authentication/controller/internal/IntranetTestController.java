package com.baidu.acg.piat.digitalhuman.speech.authentication.controller.internal;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.Collections;
import java.util.LinkedHashMap;
import java.util.Map;

@RestController
@RequestMapping("/api/device-2d/v1/internal/openapi/test")
@Slf4j
@Validated
@RequiredArgsConstructor
public class IntranetTestController {

    @RequestMapping("/header")
    public Map<String, Object> test(HttpServletRequest request) {
        log.info("test");
        Map<String, String> headers = new LinkedHashMap<>();
        Map<String, String> attributes = new LinkedHashMap<>();
        for (String name : Collections.list(request.getHeaderNames())) {
            headers.put(name, request.getHeader(name));
        }
        for (String name : Collections.list(request.getAttributeNames())) {
            attributes.put(name, request.getAttribute(name).toString());
        }
        return Map.of("headers", headers, "attributes", attributes);
    }
}
