package com.baidu.acg.piat.digitalhuman.speech.authentication.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.Optional;

import com.baidu.acg.piat.digitalhuman.speech.authentication.model.DeviceInfo;
import com.baidu.acg.piat.digitalhuman.speech.authentication.model.DeviceInfoDeviceId;
import com.baidu.acg.piat.digitalhuman.speech.authentication.model.DeviceRuntime;
import com.baidu.acg.piat.digitalhuman.speech.authentication.model.http.PageResult;
import com.baidu.acg.piat.digitalhuman.speech.authentication.model.search.RuntimeSearchCriteria;
import com.baidu.acg.piat.digitalhuman.speech.authentication.repository.DeviceInfoCustomRepository;
import com.baidu.acg.piat.digitalhuman.speech.authentication.repository.DeviceInfoDeviceIdRepository;
import com.baidu.acg.piat.digitalhuman.speech.authentication.repository.DeviceInfoRepository;
import com.baidu.acg.piat.digitalhuman.speech.authentication.repository.DeviceRuntimeRepository;
import com.baidu.acg.piat.digitalhuman.speech.authentication.service.DeviceService;
import com.baidu.acg.piat.digitalhuman.speech.authentication.util.JpaUtil;

@Slf4j
@Service
@RequiredArgsConstructor
public class DeviceServiceImpl implements DeviceService {

    private final DeviceInfoRepository deviceInfoRepository;

    private final DeviceRuntimeRepository deviceRuntimeRepository;

    private final DeviceInfoCustomRepository deviceInfoCustomRepository;

    private final DeviceInfoDeviceIdRepository deviceInfoDeviceIdRepository;

    @Override
    public DeviceInfo saveDeviceInfo(DeviceInfo deviceInfo) {
        log.info("Try to saveDeviceInfo:{}", deviceInfo);

        if (StringUtils.isNotEmpty(deviceInfo.getUserId())) {
            Optional<DeviceInfo> lastDeviceInfoOpt
                = deviceInfoRepository.findFirstByUserIdOrderByUpdateTimeDesc(deviceInfo.getUserId());
            if (lastDeviceInfoOpt.isPresent()) {
                DeviceInfo lastDeviceInfo = lastDeviceInfoOpt.get();
                if (lastDeviceInfo.getDeviceId().equals(deviceInfo.getDeviceId())) {
                    log.info("Detect DeviceInfo DeviceId not changed , skip saving :{}", deviceInfo.getDeviceId());
                    return lastDeviceInfo;
                }
                lastDeviceInfo.setDeviceId(deviceInfo.getDeviceId());
                if (deviceInfo.equals(lastDeviceInfo)) {
                    lastDeviceInfo.setCreateTime(deviceInfo.getCreateTime());
                    deviceInfo = lastDeviceInfo;
                    log.info("Detect DeviceInfo not changed, but update DeviceId {} ", deviceInfo.getDeviceId());
                } else {
                    log.info("Detect DeviceInfo changed {} ", deviceInfo);
                }
            }
        }

        if (deviceInfo.getCreateTime() == null) {
            deviceInfo.setCreateTime(LocalDateTime.now());
        }

        deviceInfo.setUpdateTime(LocalDateTime.now());
        DeviceInfo savedDeviceInfo = deviceInfoRepository.save(deviceInfo);
        DeviceInfoDeviceId deviceInfoDeviceId = new DeviceInfoDeviceId();
        deviceInfoDeviceId.setUserId(savedDeviceInfo.getUserId());
        deviceInfoDeviceId.setDeviceId(savedDeviceInfo.getDeviceId());
        deviceInfoDeviceIdRepository.save(deviceInfoDeviceId);
        return savedDeviceInfo;

    }

    public PageResult<DeviceInfo> searchDeviceInfo(RuntimeSearchCriteria criteria) {
        Page<DeviceInfo> page = deviceInfoCustomRepository.findAll(criteria);
        return JpaUtil.toPageResult(page);

    }


    @Override
    public DeviceRuntime saveDeviceRuntime(DeviceRuntime deviceRuntime) {
        log.info("Try to saveDeviceRuntime:{}", deviceRuntime);
        deviceRuntime.setCreateTime(new Date());
        return deviceRuntimeRepository.save(deviceRuntime);

    }


}
