package com.baidu.acg.piat.digitalhuman.speech.authentication.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import com.baidu.acg.piat.digitalhuman.speech.authentication.exception.BusinessException;
import com.baidu.acg.piat.digitalhuman.speech.authentication.exception.IExceptionCode;
import com.baidu.acg.piat.digitalhuman.speech.authentication.model.BarrageProfile;
import com.baidu.acg.piat.digitalhuman.speech.authentication.model.BarrageProfileStat;
import com.baidu.acg.piat.digitalhuman.speech.authentication.model.PlatformEnum;

@RequiredArgsConstructor
@Slf4j
public class BarrageProfileStatCalc {

    private static final String START_SUB_TYPE = BarrageProfileStat.RECEIVE_SUB_TYPE;

    private final BarrageProfileStat barrageProfileStat;

    private final Map<String, Map<String, BarrageProfile>> barrageProfileMap;

    public BarrageProfileStatCalc(BarrageProfileStat barrageProfileStat) {
        this.barrageProfileStat = barrageProfileStat;
        barrageProfileMap = new LinkedHashMap<>();

        for (Map.Entry<String, List<BarrageProfile>> entry : barrageProfileStat.getBarrageProfileMap().entrySet()) {
            barrageProfileMap.putIfAbsent(entry.getKey(), new LinkedHashMap<>());
            Map<String, BarrageProfile> subTypeMap = barrageProfileMap.get(entry.getKey());
            for (BarrageProfile barrageProfile : entry.getValue()) {
                subTypeMap.putIfAbsent(barrageProfile.getSubType(), barrageProfile);
            }
        }
    }

    public static BarrageProfile findLastBarrageProfile(String subType, List<BarrageProfile> barrageProfiles) {
        for (int i = barrageProfiles.size() - 1; i >= 0; i--) {
            BarrageProfile barrageProfile = barrageProfiles.get(i);
            if (barrageProfile.getSubType().equals(subType)) {
                return barrageProfile;
            }
        }
        return null;
    }

    public void startStat() {
        for (Map.Entry<String, Map<String, BarrageProfile>> entry : barrageProfileMap.entrySet()) {
            try {
                String logId = entry.getKey();
                Map<String, BarrageProfile> subTypeMap = entry.getValue();

                barrageProfileStat.incrPlatform(subTypeMap.get(START_SUB_TYPE).getPlatform().name());

                boolean hasAiResponse = !subTypeMap.containsKey("not_open_ai_faq");

                calcStatus(subTypeMap, logId, hasAiResponse);
                calcTimeCost(subTypeMap, logId, barrageProfileStat.getBarrageProfileMap().get(logId));


            } catch (Exception e) {
                log.error("Stat barrageProfile error:" + e.getMessage() + ";" + e.getClass().getSimpleName(), e);
            }

        }
        barrageProfileStat.finishStat();
    }

    private void calcStatus(Map<String, BarrageProfile> subTypeMap, String logId, boolean hasAiResponse) {
        BarrageProfileStat.Status status = BarrageProfileStat.Status.NORMAL;
        if (subTypeMap.containsKey("err_upload_log_to_device_2d")) {
            status = BarrageProfileStat.Status.EXCEPTION;
        } else {
            BarrageProfile firstResponseProfile = findFirstResponse(subTypeMap);
            if (firstResponseProfile == null) {
                status = BarrageProfileStat.Status.NOT_HIT;
            }
        }
        barrageProfileStat.incrStatus(status, logId);
    }

    BarrageProfile findFirstResponse(Map<String, BarrageProfile> subTypeMap) {
        BarrageProfile firstResponseProfile = subTypeMap.get("query_llm_text_faq_send_response");
        if (firstResponseProfile == null) {
            firstResponseProfile = subTypeMap.get("query_llm_audio_faq_send_response");
        }
        if (firstResponseProfile == null) {
            firstResponseProfile = subTypeMap.get("query_llm_spoke_reply");
        }
        return firstResponseProfile;
    }

    private void calcTimeCost(Map<String, BarrageProfile> subTypeMap, String logId,
                              List<BarrageProfile> barrageProfiles) throws BusinessException {
        BarrageProfile startBarrageProfile = findBarrageProfile(START_SUB_TYPE, subTypeMap, logId);
        long startCost = startBarrageProfile.getCost();
        barrageProfileStat.addStat(BarrageProfileStat.StatField.RECEIVE_COST_TIME, startCost);
        BarrageProfile firstResponseProfile = findFirstResponse(subTypeMap);
        if (firstResponseProfile != null) {
            barrageProfileStat.addStat(BarrageProfileStat.StatField.FIRST_PACKAGE_TIME,
                firstResponseProfile.getCost() - startCost);
        } else {
            log.info("Not found FIRST_RESPONSE_PACKAGE_TIME:" + logId);
        }

        BarrageProfile faqStartProfile = subTypeMap.get("query_llm_faq_start");
        if (faqStartProfile != null) {
            BarrageProfile faqEndProfile = subTypeMap.get("query_llm_faq_end");
            if (faqEndProfile == null) {
                barrageProfileStat.addStatError("Not found query_llm_faq_end:" + logId);
            } else {
                barrageProfileStat.addStat(BarrageProfileStat.StatField.FAQ_HITTING_TIME,
                    faqEndProfile.getCost() - faqStartProfile.getCost());
            }
        }
        BarrageProfile aiStartProfile = subTypeMap.get("query_llm_ai_start");
        if (aiStartProfile != null) {
            BarrageProfile aiEndProfile = subTypeMap.get("query_llm_ai_end");
            if (aiEndProfile == null) {
                barrageProfileStat.addStatError("Not found query_llm_ai_end:" + logId);
            } else {
                barrageProfileStat.addStat(BarrageProfileStat.StatField.DOC_AI_HITTING_TIME,
                    aiEndProfile.getCost() - aiStartProfile.getCost());
                long spokenReplyStartCost = aiEndProfile.getCost();
                BarrageProfile spokenReplyFirstHittingProfile = subTypeMap.get("query_llm_spoke_reply");
                BarrageProfile spokenReplyLastHittingProfile
                    = findLastBarrageProfile("query_llm_spoke_reply", barrageProfiles);
                if (spokenReplyFirstHittingProfile == null) {
                    log.warn("Not found query_llm_spoke_reply: " + logId);
                } else {
                    barrageProfileStat.addStat(BarrageProfileStat.StatField.SPOKEN_REPLY_FIRST_HITTING_TIME,
                        spokenReplyFirstHittingProfile.getCost() - spokenReplyStartCost);
                    barrageProfileStat.addStat(BarrageProfileStat.StatField.SPOKEN_REPLY_LAST_HITTING_TIME,
                        spokenReplyLastHittingProfile.getCost() - spokenReplyStartCost);
                }
            }
        }
    }

    private BarrageProfile findBarrageProfile(String subType, Map<String, BarrageProfile> subTypeMap, String logId) throws BusinessException {
        BarrageProfile startBarrageProfile = subTypeMap.get(START_SUB_TYPE);
        if (startBarrageProfile == null) {
            String errMessage = "Not found :" + START_SUB_TYPE + ";" + logId;
            barrageProfileStat.addStatError("Not found :" + START_SUB_TYPE + ";" + logId);
            throw new BusinessException(IExceptionCode.ERROR_STAT, "Not found: " + START_SUB_TYPE + ";" + logId);
        }
        return startBarrageProfile;
    }

    private PlatformEnum calcPlatformEnum(BarrageProfile receiveBarrageProfile) {
        PlatformEnum platformEnum = PlatformEnum.OTHER;

        Object platform = receiveBarrageProfile.getContentKey("platform");
        if (platform != null) {
            try {
                platformEnum = PlatformEnum.valueOf(platform.toString());
            } catch (Exception e) {
                log.error("PlatformEnum parse error for platform: {}, {}", platform, e.getMessage(), e);
            }
        }
        return platformEnum;
    }

    private BarrageProfile findFirstBarrageProfile(String subType, List<BarrageProfile> barrageProfiles) {
        for (int i = 0; i < barrageProfiles.size(); i++) {
            BarrageProfile barrageProfile = barrageProfiles.get(i);
            if (barrageProfile.getSubType().equals(subType)) {
                return barrageProfile;
            }
        }
        return null;
    }
}
