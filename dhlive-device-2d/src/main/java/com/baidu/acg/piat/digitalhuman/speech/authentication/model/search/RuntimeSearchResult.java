package com.baidu.acg.piat.digitalhuman.speech.authentication.model.search;

import lombok.Data;
import lombok.RequiredArgsConstructor;

import java.util.List;

import com.baidu.acg.piat.digitalhuman.speech.authentication.model.DeviceInfo;
import com.baidu.acg.piat.digitalhuman.speech.authentication.model.DeviceRuntime;

@Data
@RequiredArgsConstructor
public class RuntimeSearchResult {

    private final List<DeviceRuntime> runtimes;

    private final List<DeviceInfo> infos;
}
