package com.baidu.acg.piat.digitalhuman.speech.authentication.controller.internal;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

import com.baidu.acg.piat.digitalhuman.speech.authentication.exception.InputException;
import com.baidu.acg.piat.digitalhuman.speech.authentication.model.DeviceInfo;
import com.baidu.acg.piat.digitalhuman.speech.authentication.model.http.PageResult;
import com.baidu.acg.piat.digitalhuman.speech.authentication.model.search.RuntimeSearchCriteria;
import com.baidu.acg.piat.digitalhuman.speech.authentication.service.DeviceService;

@RestController
@RequestMapping("/api/device-2d/v1/internal/openapi/device")
@Slf4j
@Validated
@RequiredArgsConstructor
public class InternalDeviceController {


    private final DeviceService deviceService;

    @GetMapping("/info")
    public PageResult<DeviceInfo> findInfos(@Valid RuntimeSearchCriteria criteria) throws InputException {
        criteria.fixData();
        log.info("Request for search deviceInfo: {}", criteria);
        return deviceService.searchDeviceInfo(criteria);
    }
}
