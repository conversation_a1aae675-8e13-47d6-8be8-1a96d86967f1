package com.baidu.acg.piat.digitalhuman.speech.authentication.service;

import javax.validation.Valid;

import com.baidu.acg.piat.digitalhuman.speech.authentication.model.DeviceInfo;
import com.baidu.acg.piat.digitalhuman.speech.authentication.model.DeviceRuntime;
import com.baidu.acg.piat.digitalhuman.speech.authentication.model.http.PageResult;
import com.baidu.acg.piat.digitalhuman.speech.authentication.model.search.RuntimeSearchCriteria;
import com.baidu.acg.piat.digitalhuman.speech.authentication.model.search.RuntimeSearchResult;

public interface DeviceService {

    public DeviceInfo saveDeviceInfo(DeviceInfo deviceInfo);


    DeviceRuntime saveDeviceRuntime(DeviceRuntime deviceRuntime);



    PageResult<DeviceInfo> searchDeviceInfo(RuntimeSearchCriteria criteria);
}
