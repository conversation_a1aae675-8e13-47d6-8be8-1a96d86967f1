package com.baidu.acg.piat.digitalhuman.speech.authentication.service;

import com.baidu.acg.piat.digitalhuman.speech.authentication.model.BarrageProfileStat;
import com.baidu.acg.piat.digitalhuman.speech.authentication.model.http.PageResult;
import com.baidu.acg.piat.digitalhuman.speech.authentication.model.search.BarrageProfileStatSearchCriteria;

public interface BarrageProfileStatService {
    PageResult<BarrageProfileStat> search(BarrageProfileStatSearchCriteria criteria);
}
