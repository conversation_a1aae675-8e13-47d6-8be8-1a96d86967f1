package com.baidu.acg.piat.digitalhuman.speech.authentication.model.search;


import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

import com.baidu.acg.piat.digitalhuman.speech.authentication.model.PlatformEnum;

@EqualsAndHashCode(callSuper = true)
@Data
@ToString(callSuper = true)
public class BarrageProfileSingleStatSearchCriteria extends TimeRangePageableCriteria {

    @Valid
    @NotNull
    PlatformEnum platform;
}
