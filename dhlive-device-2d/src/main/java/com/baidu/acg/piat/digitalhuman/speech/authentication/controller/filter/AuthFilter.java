package com.baidu.acg.piat.digitalhuman.speech.authentication.controller.filter;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.server.ServletServerHttpRequest;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.servlet.Filter;
import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletRequestWrapper;
import java.io.IOException;
import java.net.Inet4Address;
import java.net.InetAddress;
import java.net.UnknownHostException;
import java.util.List;
import java.util.UUID;

import com.baidu.acg.piat.digitalhuman.speech.authentication.exception.IExceptionCode;

@Component
@Slf4j
@RequiredArgsConstructor
/**
 * 判断请求是否只能内网访问，如果是则校验ip是否在内网ip段
 */
public class AuthFilter implements Filter {

    @Value(value = "${digital-human.internal.urls:}")
    private final List<String> intranetUrls;

    @PostConstruct
    private void printLog() {
        log.info("AuthFilter intranetUrls, size={}, list={}", intranetUrls.size(), intranetUrls);
    }

    @Override
    public void doFilter(ServletRequest servletRequest, ServletResponse servletResponse, FilterChain filterChain)
        throws IOException, ServletException {
        HttpServletRequest httpServletRequest = (HttpServletRequest) servletRequest;
        ServletServerHttpRequest request = new ServletServerHttpRequest(httpServletRequest);
        checkLogId(httpServletRequest, request);
        log.debug("Start at beginning of request method={}, logId={}, url={} ", httpServletRequest.getMethod(),
            httpServletRequest.getAttribute("logId"),
            request.getURI()
        );
        if (isIntranetUrl(request)) {
            log.info("AuthFilter: " + request.getRemoteAddress().getAddress().getHostAddress());
            if (!isIntranetIp(getRemoteAddress(httpServletRequest, request))) {
                HttpServletRequestWrapper newRequest = new HttpServletRequestWrapper(httpServletRequest) {
                    @Override
                    public String getRequestURI() {
                        return "/api/v1/device-2d/internal/openapi/auth/error";
                    }
                };
                newRequest.setAttribute("errorMsg", "fail to check intranet");
                newRequest.setAttribute("errorCode", IExceptionCode.ERROR_INTRANET_CHECK);
                log.debug("Fail to check intranet ; forward to :" + newRequest.getRequestURI());
                filterChain.doFilter(newRequest, servletResponse);
                return;
            }
        }
        filterChain.doFilter(servletRequest, servletResponse);

    }

    private void checkLogId(HttpServletRequest servletRequest, ServletServerHttpRequest request) {
        String logId = servletRequest.getParameter("logId");
        if (StringUtils.isBlank(logId)) {
            logId = "i-" + UUID.randomUUID();
        }
        servletRequest.setAttribute("logId", logId);
    }

    private InetAddress getRemoteAddress(HttpServletRequest servletRequest, ServletServerHttpRequest request) {
        String remoteIp = servletRequest.getHeader("x-original-forwarded-for");
        if (StringUtils.isBlank(remoteIp)) {
            return request.getRemoteAddress().getAddress();
        } else {
            String[] ips = remoteIp.split(",");
            if (ips.length > 0) {
                try {
                    return InetAddress.getByName(ips[ips.length - 1]); // 有多个值时，拿最后一个是对的
                } catch (UnknownHostException e) {
                    log.warn("Fail to parse header x-original-forwarded-for: " + e.getMessage());
                }
            }
        }
        return null;
    }

    private boolean isIntranetUrl(ServletServerHttpRequest request) {
        String url = request.getURI().getPath();

        for (String intranetUrl : intranetUrls) {
            if (intranetUrl.endsWith("/**")) {
                String prefix = intranetUrl.substring(0, intranetUrl.length() - 3);
                if (url.equals(prefix) || url.startsWith(prefix + "/")) {
                    return true;
                }
            } else {
                if (url.equals(intranetUrl) || url.equals(intranetUrl + "/")) {
                    return true;
                }
            }

        }
        return false;
    }


    private boolean isIntranetIp(InetAddress address) {
        if (!(address instanceof Inet4Address)) {
            log.debug("Fail to test IPv4 address instanceof Inet4Address:" + address);
            return false;
        }
        Inet4Address inet = (Inet4Address) address;
        boolean isLocal = inet.isSiteLocalAddress() || inet.isLoopbackAddress();

        if (!isLocal) {
            log.debug("Fail to test local address:" + address);
        }
        return isLocal;
    }


}
