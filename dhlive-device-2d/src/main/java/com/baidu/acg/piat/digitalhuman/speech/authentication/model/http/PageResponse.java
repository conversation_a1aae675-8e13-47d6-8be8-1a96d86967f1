// Copyright (C) 2021 Baidu Inc. All rights reserved.

package com.baidu.acg.piat.digitalhuman.speech.authentication.model.http;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * Page类型的HttpResponse
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PageResponse<T> extends BaseResponse {

    private PageResult<T> page;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Object result;

    public static <T> PageResponse<T> fail(String errorMessage) {
        PageResponse<T> result = new PageResponse<>();
        result.setMessage(new Message(errorMessage));
        result.setSuccess(false);
        return result;
    }

    /**
     * @param pageNo   input 0 , output 1
     * @param pageSize
     * @param total
     * @param contents
     * @param <T>
     * @return
     */
    public static <T> PageResponse<T> success(int pageNo, int pageSize, long total, List<T> contents) {
        PageResponse<T> result = new PageResponse<>();
        result.setPage(PageResult.<T>builder()
            .pageNo(pageNo)
            .pageSize(pageSize)
            .totalCount(total)
            .result(contents)
            .build());
        return result;
    }

    public static <T> PageResponse<T> success(PageResult<T> page) {
        PageResponse<T> result = new PageResponse<>();
        result.setPage(page);
        return result;
    }

    public static <T> PageResponse<T> empty(int pageSize, int total) {
        PageResponse<T> result = new PageResponse<>();
        result.setPage(PageResult.<T>builder()
            .pageNo(1)
            .pageSize(pageSize)
            .totalCount(total)
            .build());
        return result;
    }
}
