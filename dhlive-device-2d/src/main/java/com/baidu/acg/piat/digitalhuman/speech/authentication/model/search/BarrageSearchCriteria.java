package com.baidu.acg.piat.digitalhuman.speech.authentication.model.search;

import lombok.Data;
import org.hibernate.validator.constraints.Length;

import com.baidu.acg.piat.digitalhuman.speech.authentication.model.PlatformEnum;

@Data
public class BarrageSearchCriteria extends TimeRangePageableCriteria{

    private PlatformEnum platform;

    @Length(max = 256)
    private String logId;

    @Length(max = 256)
    private String userId;

}
