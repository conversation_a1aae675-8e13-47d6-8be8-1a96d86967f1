package handler

import (
	commProto "acg-ai-go-common/beans/proto"
	"acg-ai-go-common/logger"
	"bytes"
	"context"
	"ecloud-custom-faq/beans"
	"ecloud-custom-faq/beans/proto"
	"ecloud-custom-faq/es"
	"ecloud-custom-faq/faqutils"
	"ecloud-custom-faq/handler/service"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"net/http"
	"regexp"
	"sort"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/mozillazg/go-pinyin"
)

var isPinYin = true

func FaqList(c *gin.Context) {
	req := &beans.SearchQueryRequest{}
	if err := c.BindJSON(&req); err != nil {
		logger.Log.Errorf("FaqList bind param fail, err:[%v]", err)
		c.JSO<PERSON>(http.StatusOK, commProto.NewCommRsp(commProto.RSP_COM_CODE_PARAM_ERR, fmt.Sprintf("%v,%v", commProto.RSP_COM_MSG_PARAM_ERR, err.Error())))
		return
	}

	logCtx := faqutils.GenerateLogCtx(req.LogId)
	reqData, _ := json.Marshal(req)
	logger.Log.Infof(faqutils.MMark(logCtx)+"logId: %s ,request : %v", req.LogId, string(reqData))

	list, err := es.NewEsSelect().SearchFaqList(logCtx, req.AppID)
	if err != nil {
		logger.Log.Errorf(faqutils.MMark(logCtx)+"FaqList SearchFaqList fail, err:[%v]", err)
		c.JSON(http.StatusOK, commProto.NewCommRsp(commProto.RSP_COM_CODE_SERVER_ERROR,
			fmt.Sprintf("%v,%v", commProto.RSP_COM_MSG_SERVER_ERROR, err.Error())))
	}
	c.JSON(http.StatusOK, commProto.NewSuccessRsp(list))
}

func FaqSearch(c *gin.Context) {
	req := &beans.SearchQueryRequest{}

	if err := c.BindJSON(&req); err != nil {
		logger.Log.Errorf("QuerySimilaritySearch bind param fail, err:[%v]", err)
		c.JSON(http.StatusOK, commProto.NewCommRsp(commProto.RSP_COM_CODE_PARAM_ERR, fmt.Sprintf("%v,%v", commProto.RSP_COM_MSG_PARAM_ERR, err.Error())))
		return
	}

	logCtx := faqutils.GenerateLogCtx(req.LogId)
	reqData, _ := json.Marshal(req)
	logger.Log.Infof(faqutils.MMark(logCtx)+"logId: %s ,request : %v", req.LogId, string(reqData))

	if req.AppID == "" || req.Query == "" {
		logger.Log.Errorf(faqutils.MMark(logCtx)+"QuerySimilaritySearch bind param fail, err: questionInfos or query is null , req %v", req)
		c.JSON(http.StatusOK, commProto.NewCommRsp(commProto.RSP_COM_CODE_PARAM_ERR, fmt.Sprintf("%v,%v", commProto.RSP_COM_MSG_PARAM_ERR, "questionInfos or query is null")))
		return
	}

	if req.ClarificationThreshold == 0 {
		req.ClarificationThreshold = 60
	}

	if req.HitThreshold == 0 {
		req.HitThreshold = 80
	}

	// 澄清置信度不能大于命中置信度
	if req.ClarificationThreshold > req.HitThreshold {
		req.ClarificationThreshold = req.HitThreshold
	}

	maxScope := 0.0

	resp := beans.QueryResponse{
		QuestionInfo: make([]beans.QuestionInfo, 0),
		CosineScore:  maxScope,
	}

	logger.Log.Infof(faqutils.MMark(logCtx)+"appId:%v", req.AppID)
	// 开启文心向量或者是系统画像的AppID使用向量检索
	// 使用文心向量检索失败时，降级到（Es文本匹配+拼音匹配+NLP文本相似度）策略
	if req.OpenEmbedding {
		faqEmbedding, err := SearchFaqEmbeddingTopK(logCtx, req.AppID, req.Query, 1)
		if err != nil {
			logger.Log.Errorf(faqutils.MMark(logCtx)+"QuerySimilaritySearch SearchFaqEmbeddingTopK fail, err:[%v]", err)
			c.JSON(http.StatusOK, commProto.NewSuccessRsp(resp))
			return
		} else {
			questions, err := es.SearchFaqByQId(logCtx, req.AppID, faqEmbedding[0].QID)
			if err != nil {
				logger.Log.Errorf(faqutils.MMark(logCtx)+"QuerySimilaritySearch SearchFaqByQId fail, err:[%v]", err)
				c.JSON(http.StatusOK, commProto.NewCommRsp(commProto.RSP_COM_CODE_SERVER_ERROR,
					fmt.Sprintf("%v,%v", commProto.RSP_COM_MSG_SERVER_ERROR, err.Error())))
				return
			}
			logger.Log.Infof(faqutils.MMark(logCtx)+"FaqSearch SearchFaqByQId questions len:%v", len(questions))
			if len(questions) > 0 {
				value, _ := strconv.ParseFloat(fmt.Sprintf("%.2f", faqEmbedding[0].Score/100), 64)
				resp.CosineScore = value
				switch {
				case resp.CosineScore >= float64(req.HitThreshold)/100:
					resp.Confidence = beans.ConfidenceFaq
				case resp.CosineScore >= float64(req.ClarificationThreshold)/100 &&
					resp.CosineScore < float64(req.HitThreshold)/100:
					resp.Confidence = beans.ConfidenceClarify
				}
				resp.QuestionInfo = questions

				logger.Log.Infof(faqutils.MMark(logCtx)+"QuerySimilaritySearch SearchFaqEmbeddingTopK success, resp:[%v] socre:%v",
					resp, resp.CosineScore)
				c.JSON(http.StatusOK, commProto.NewSuccessRsp(resp))
				return
			}
		}
	}

	questions, err := es.NewEsSelect().SearchFaqQuery(logCtx, req.AppID, "questions", req.Query, 1, 5)
	if err != nil {
		logger.Log.Errorf(faqutils.MMark(logCtx)+"QuerySimilaritySearch es search , err:[%v]", err)
		c.JSON(http.StatusOK, commProto.NewCommRsp(commProto.RSP_COM_CODE_SERVER_ERROR,
			fmt.Sprintf("%v,%v", commProto.RSP_COM_MSG_SERVER_ERROR, err.Error())))
		return
	}

	if len(questions) == 0 {
		c.JSON(http.StatusOK, commProto.NewSuccessRsp(resp))
		return
	}

	texts := make([]beans.NlpcErnipData, 0)
	respm := make(map[int]beans.QuestionInfo, 0)
	num := 0
	for _, v1 := range questions {
		for _, v2 := range v1.Questions {
			if strings.EqualFold(v2, req.Query) {
				resp.QuestionInfo = append(resp.QuestionInfo, v1)
				resp.Confidence = beans.ConfidenceFaq
				resp.CosineScore = 1
				c.JSON(http.StatusOK, commProto.NewSuccessRsp(resp))
				return
			}
			texts = append(texts, beans.NlpcErnipData{
				TextA: req.Query,
				TextB: v2,
			})
			respm[num] = v1
			num++
		}
	}

	// nlpc 最大支持100个text
	loopNum := num / 90

	clarificationInfos := make(map[float64]beans.QuestionInfo, 0)
	clarificationSort := make([]float64, 0)

	hitInfos := beans.QuestionInfo{}

	for i := 1; i <= loopNum+1; i++ {
		nlpcReq := beans.NlpcErnipRequest{
			Inputs: make([]beans.NlpcErnipData, 0),
		}
		if i == loopNum+1 {
			nlpcReq.Inputs = texts[(i-1)*90:]
		} else {
			nlpcReq.Inputs = texts[(i-1)*90 : i*90]
			//nlpcReq.Inputs = texts
		}
		nlpcResp := &beans.NlpcResponse{}

		err = PostJson(beans.NlpcTextSimUrl, nlpcReq, nlpcResp)
		if err != nil {
			logger.Log.Errorf(faqutils.MMark(logCtx)+"QuerySimilaritySearch get nlpc response fail, err: %v", err)
			return
		}

		// 这里可能会出现某一条结果失败，状态码就会返回非0的情况
		//if nlpcResp.Status != 0 {
		//	logger.Log.Errorf("get nlpc_erniesim_light_100 resust error, err: %v", nlpcResp)
		//	return
		//}

		for idx, v3 := range nlpcResp.Results {
			// 去除相似度小于澄清置信度的问题
			if v3.CosineScore < float64(req.ClarificationThreshold)/100 {
				continue
			}

			// 保留相似度大于澄清置信度小于命中置信度的问题
			if v3.CosineScore > float64(req.ClarificationThreshold)/100 &&
				v3.CosineScore < float64(req.HitThreshold)/100 {
				if value, ok := respm[idx+((i-1)*90)]; ok {
					key := GetNotInMapKey(v3.CosineScore, clarificationInfos)
					clarificationInfos[key] = value
					clarificationSort = append(clarificationSort, key)
				}
				continue
			}

			// 查找命中问题中相似度最高的问题
			if v3.CosineScore > maxScope && v3.CosineScore >= float64(req.HitThreshold)/100 {
				maxScope = v3.CosineScore
				hitInfos = respm[idx+((i-1)*90)]
			}
		}
	}

	// 如果没有成功命中召回，尝试使用pinyin召回、及关键词召回
	if hitInfos.AppID == "" {

		search, confidence := PinYinAndKeyWordSearch(logCtx, req.AppID, req.Query)
		if confidence != 0 {
			resp.QuestionInfo = append(resp.QuestionInfo, search)
			resp.Confidence = confidence
			resp.CosineScore = 1
			logger.Log.Infof(faqutils.MMark(logCtx)+"QuerySimilaritySearch PinYin or KeyWork search , resp:[%v]", resp)
			c.JSON(http.StatusOK, commProto.NewSuccessRsp(resp))
			return
		}
	}

	if hitInfos.AppID != "" {
		resp.QuestionInfo = append(resp.QuestionInfo, hitInfos)
		resp.Confidence = beans.ConfidenceFaq
	} else if len(clarificationInfos) > 0 {
		//
		sort.Sort(sort.Reverse(sort.Float64Slice(clarificationSort)))
		for _, valueSort := range clarificationSort {
			// 去除相似问导致的问题一致
			if GetNotInMapValue(clarificationInfos[valueSort], resp.QuestionInfo) {
				continue
			}
			resp.QuestionInfo = append(resp.QuestionInfo, clarificationInfos[valueSort])
		}
		resp.Confidence = beans.ConfidenceClarify
	}

	resp.CosineScore = maxScope
	logger.Log.Infof(faqutils.MMark(logCtx)+"QuerySimilaritySearch search , resp:[%v]", resp)
	c.JSON(http.StatusOK, commProto.NewSuccessRsp(resp))
	return

}

func PinYinAndKeyWordSearch(ctx context.Context, appId, query string) (resp beans.QuestionInfo, confidence int) {

	a1 := pinyin.NewArgs()
	joinPinYin := strings.Join(pinyin.LazyPinyin(query, a1), " ")

	questions := make([]beans.QuestionInfo, 0)
	if isPinYin {
		questions, _ = es.NewEsSelect().SearchFaqQueryPinAndKeyWord(ctx, appId, joinPinYin, query, 1, 5)
	} else {
		questions, _ = es.NewEsSelect().SearchFaqQuery(ctx, appId, "keywordGroups", query, 1, 5)
	}

	if len(questions) == 0 {
		return resp, 0
	}

	keyWordMap := make(map[int]int, 0)
	keyWordList := make([]int, 0)

	for idx, v := range questions {
		if v.Qid == "" {
			continue
		}

		// 查看关键词是否命中
		if len(v.KeywordGroups) > 0 {
			for _, v2 := range v.KeywordGroups {
				split := strings.Split(v2, ";")
				if len(split) == 0 {
					continue
				}

				mat := ".*?"
				for _, v3 := range split {
					mat = mat + ".*?" + v3
				}

				mat = mat + ".*?"

				ok, err := regexp.MatchString(mat, query)
				if err != nil {
					continue
				}

				if ok {
					keyWordMap[len(split)] = idx
					keyWordList = append(keyWordList, len(split))
				}
			}

		}

		// 查看pinYin是否命中
		if len(v.PinYin) == 0 {
			continue
		}

		if !isPinYin {
			continue
		}

		for _, v4 := range v.PinYin {
			if joinPinYin == v4 {
				return v, beans.ConfidencePinYin
			}
		}

	}

	if len(keyWordList) != 0 {
		sort.Sort(sort.Reverse(sort.IntSlice(keyWordList)))
		return questions[keyWordMap[keyWordList[0]]], beans.ConfidenceKeyWord
	}

	return resp, 0
}

func SearchFaqEmbeddingTopK(ctx context.Context, appId, query string, topK int) ([]*proto.SearchFaqEmbeddingItem, error) {
	embeddings, qpsLimit, err := service.GetErnieEmbedding(ctx, query)
	if err != nil {
		return nil, err
	}
	logger.Log.Infof(faqutils.MMark(ctx)+"GetErnieEmbedding qpsLimit: %v", qpsLimit)
	return es.SearchFaqEmbeddingTopK(ctx, appId, embeddings.Embedding, topK)
}

// GetNotInMapValue 判断这个map是否包含
func GetNotInMapValue(value beans.QuestionInfo, data []beans.QuestionInfo) bool {
	for _, v := range data {
		if v.Qid == value.Qid {
			return true
		}
	}
	return false
}

// GetNotInMapKey 判断这个分数是否再map中有重复的数据，如果有则将当前分数加0.01
func GetNotInMapKey(key float64, data map[float64]beans.QuestionInfo) float64 {
	sign := true
	for sign {
		if _, ok := data[key]; !ok {
			sign = false
			return key
		}
		key = key + 0.01
	}
	return key
}

func PostJson(URL string, data interface{}, rsp interface{}) error {
	// 超时时间：10秒
	client := &http.Client{Timeout: 10 * time.Second}
	bodyBytes, _ := json.Marshal(data)
	resp, err := client.Post(URL, "application/json;charset=utf-8", bytes.NewBuffer(bodyBytes))
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	result, err := ioutil.ReadAll(resp.Body)
	return json.Unmarshal(result, rsp)
}
