package handler

import (
	"ecloud-custom-faq/beans"
	"encoding/json"
	"fmt"
	"github.com/mozillazg/go-pinyin"
	"regexp"
	"strings"
	"testing"
)

func TestPingIn(t *testing.T) {
	hans1 := "党总支和支部委员是如何产生的？"
	a1 := pinyin.NewArgs()
	//a1.Style = pinyin.Initials
	//a1.Heteronym = true

	t.Log(strings.Join(pinyin.LazyPinyin(hans1, a1), " "))
	fmt.Println()
}

func TestMatch(t *testing.T) {
	t.Log(regexp.MatchString(".*?你好.*?天气.*?", "好，今天天气怎么样"))
}

func TestJson(t *testing.T) {
	req := beans.NluQuestionSaveRequest{
		Question: make([]beans.NluQuestion, 0),
		LogId:    "ccccccccccccccccccccc",
	}
	for i := 0; i <= 100; i++ {
		req.Question = append(req.Question, beans.NluQuestion{
			BotID:      "test",
			QueryStrID: "aaa",
			DicID:      "bbb",
			DicValID:   "ccc",
			Querys: beans.Querys{
				Query:    fmt.Sprintf("啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊%d", i),
				Synonyms: []string{"啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊", "啊啊啊啊啊啊啊啊啊啊啊啊", "啊啊啊啊啊啊啊啊啊啊啊啊", "啊啊啊啊啊啊啊啊啊啊啊啊"},
			},
			Answer: "",
		})
	}

	marshal, err := json.Marshal(req)
	if err != nil {
		t.Log(err)
		t.Fail()
	}
	t.Log(string(marshal))

}

func TestJsonA(t *testing.T) {
	req := beans.NluQuestionSaveRequest{
		Question: make([]beans.NluQuestion, 0),
		LogId:    "ccccccccccccccccccccc",
	}
	req.Question = append(req.Question, beans.NluQuestion{
		BotID:      "test",
		QueryStrID: "aaa",
		DicID:      "bbb",
		DicValID:   "ccc",
		Querys: beans.Querys{
			Query:    fmt.Sprintf("啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊"),
			Synonyms: []string{"啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊啊", "啊啊啊啊啊啊啊啊啊啊啊啊", "啊啊啊啊啊啊啊啊啊啊啊啊", "啊啊啊啊啊啊啊啊啊啊啊啊"},
		},
		Answer: "",
	})

	marshal, err := json.Marshal(req)
	if err != nil {
		t.Log(err)
	}
	for i := 0; i < 100; i++ {
		t.Log(fmt.Sprintf(`{"index":{"_index":"lagou1"}}`))
		t.Log(string(marshal))
	}

}
