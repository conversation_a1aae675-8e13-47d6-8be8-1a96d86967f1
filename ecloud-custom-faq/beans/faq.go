package beans

import "time"

// const NlpcTextSimUrl = "http://bj01.nlpc.baidu.com/nlpc_textsim_103?username=guolicheng&app=nlpc_2022122914185517397&expiredate=1680070735&access_token=551e5f34e76d7b16f00bc2938a5e0ed7"
const (
	NlpcTextSimUrl    = "http://szzj-mct-online-15.szzj:8037/v1/req"
	ConfidenceFaq     = 1
	ConfidenceClarify = 2
	ConfidencePinYin  = 3
	ConfidenceKeyWord = 4
)

const (
	QuestionTypeSystem = "SYSTEM"
	QuestionTypeUser   = "USER"
)

type QuestionDelRequest struct {
	AppID string `json:"appId"`
	Qid   string `json:"qid"`
	LogId string `json:"logId"`
}

type QuestionClearRequest struct {
	AppID string `json:"appId"`
	LogId string `json:"logId"`
}

type QuestionInfos struct {
	Infos []QuestionInfo `json:"infos"`
}

type QuestionInfo struct {
	AppID         string      `json:"appId"`
	Qid           string      `json:"qid"`
	FirstClass    string      `json:"firstClass"`
	SecondClass   string      `json:"secondClass"`
	General       bool        `json:"general"`
	KeywordGroups []string    `json:"keywordGroups"`
	Questions     []string    `json:"questions"`
	PinYin        []string    `json:"pinYin"`
	Answers       []FaqAnswer `json:"answers"`
	CreateTime    string      `json:"createTime"`
	LogId         string      `json:"logId"`
	OpenEmbedding bool        `json:"openEmbedding"`
	CallbackURL   string      `json:"callbackUrl"`
	QuestionType  string      `json:"questionType"`
}

type QuestionInfo2 struct {
	AppID         string      `json:"appId"`
	Qid           string      `json:"qid"`
	FirstClass    string      `json:"firstClass"`
	SecondClass   string      `json:"secondClass"`
	General       bool        `json:"general"`
	KeywordGroups []string    `json:"keywordGroups"`
	Questions     []string    `json:"questions"`
	PinYin        []string    `json:"pinYin"`
	Answers       []FaqAnswer `json:"answers"`
	CreateTime    int64       `json:"createTime"`
	LogId         string      `json:"logId"`
	OpenEmbedding bool        `json:"openEmbedding"`
	CallbackURL   string      `json:"callbackUrl"`
	QuestionType  string      `json:"questionType"`
}

type SearchQueryRequest struct {
	AppID                  string `json:"appId"`
	Query                  string `json:"query"`
	HitThreshold           int    `json:"hitThreshold"`
	ClarificationThreshold int    `json:"clarificationThreshold"`
	LogId                  string `json:"logId"`
	OpenEmbedding          bool   `json:"openEmbedding"`
}

type NlpcTextSimResp struct {
	Textsim float64 `json:"textsim"`
}

type NlpcChanMsg struct {
	Source       float64
	QuestionInfo QuestionInfo
}

type NlpcErnipRequest struct {
	Inputs []NlpcErnipData `json:"inputs"`
}

type NlpcErnipData struct {
	TextA string `json:"text_a"`
	TextB string `json:"text_b"`
}

type NlpcResponse struct {
	Status  int    `json:"status"`
	Version string `json:"version"`
	Results []struct {
		CosineScore float64     `json:"cosine_score"`
		Retcode     int         `json:"retcode"`
		Input       interface{} `json:"input"`
	} `json:"results"`
}

type QueryResponse struct {
	QuestionInfo []QuestionInfo `json:"question_info"`
	CosineScore  float64        `json:"cosine_score"`
	// 1命中、2澄清、3拼音命中、4关键词命中
	Confidence int `json:"confidence"`
}

type FaqAnswer struct {
	Type                    string                   `json:"type"`
	Tags                    []string                 `json:"tags"`
	Speak                   []FaqAnswerText          `json:"speak"`
	Expression              string                   `json:"expression"`
	Kv                      map[string]string        `json:"kv"`
	Text                    FaqAnswerTextV2          `json:"text"`
	Videos                  []FaqVideoAnswerV2       `json:"videos"`
	VideosDisplayFullScreen bool                     `json:"videosDisplayFullScreen"`
	Images                  []FaqImageSingleAnswerV2 `json:"images"`
	ImagesDisplayFullScreen bool                     `json:"imagesDisplayFullScreen"`
	HtmlUrl                 string                   `json:"htmlUrl"`
	Html5DisplayFullScreen  bool                     `json:"html5DisplayFullScreen"`
	Recordings              AudioAnswer              `json:"recordings"`
	Action                  ActionLibraryAnswer      `json:"action"`
	HttpRequest             HttpRequestAnswer        `json:"httpRequest"`
	NotInterrupted          bool                     `json:"notInterrupted"`
	RichText                RichTextAnswer           `json:"richText"`
}

type FaqAnswerText struct {
	Answer string `json:"answer"`
}

type FaqAnswerTextV2 struct {
	Text string `json:"text"`
	Url  string `json:"url"`
}

type FaqVideoAnswerV2 struct {
	VideoUrl string `json:"videoUrl"`
	FrameUrl string `json:"frameUrl"`
}

type FaqImageSingleAnswerV2 struct {
	ImageUrl string `json:"imageUrl"`
}

type AudioAnswer struct {
	AudioUrl string `json:"audioUrl"`
}

type ActionLibraryAnswer struct {
	GroupId   string `json:"groupId"`
	GroupName string `json:"groupName"`
}

type HttpRequestAnswer struct {
	Url        string `json:"url"`
	HttpMethod string `json:"httpMethod"`
	Body       string `json:"body"`
}

type RichTextAnswer struct {
	Content           string `json:"content"`
	DisplayFullScreen bool   `json:"displayFullScreen"`
}

type FaqQuestion struct {
	Qid           string          `json:"qid"`
	UnitFaqID     int             `json:"unitFaqId,omitempty"`
	UnitIntentID  int             `json:"unitIntentId"`
	UnitBotID     int             `json:"unitBotId"`
	AppID         string          `json:"appId"`
	Questions     []Questions     `json:"questions"`
	KeywordGroups []KeywordGroups `json:"keywordGroups,omitempty"`
	Status        int             `json:"status"`
	Version       string          `json:"version"`
	CreateTime    time.Time       `json:"createTime"`
	UpdateTime    time.Time       `json:"updateTime"`
}
type Questions struct {
	Question                string `json:"question"`
	NlpcRewriteResultExists bool   `json:"nlpcRewriteResultExists"`
	Type                    string `json:"type,omitempty"`
}
type KeywordGroups struct {
	KeywordGroup string `json:"keywordGroup"`
}

type FaqData struct {
	FaqID                 string      `json:"faqId"`
	AppID                 string      `json:"appId"`
	Qid                   string      `json:"qid"`
	Answers               []FaqAnswer `json:"answers"`
	AnswerGranularityType string      `json:"answerGranularityType"`
	Status                int         `json:"status"`
	Version               string      `json:"version"`
	SearchTextForKeyword  string      `json:"searchTextForKeyword"`
	CreateTime            time.Time   `json:"createTime"`
	UpdateTime            time.Time   `json:"updateTime"`
	FirstClassification   string      `json:"firstClassification,omitempty"`
	SecondClassification  string      `json:"secondClassification,omitempty"`
	SearchTextForRobot    string      `json:"searchTextForRobot,omitempty"`
}

type SyncDataRequest struct {
	AppID string `json:"appId"`
	Env   string `json:"env"`
}

type EsSearchResponse struct {
	Shards   Shards `json:"_shards"`
	Hits     Hits1  `json:"hits"`
	TimedOut bool   `json:"timed_out"`
	Took     int    `json:"took"`
}
type Shards struct {
	Failed     int `json:"failed"`
	Skipped    int `json:"skipped"`
	Successful int `json:"successful"`
	Total      int `json:"total"`
}

type Hits2 struct {
	ID     string       `json:"_id"`
	Index  string       `json:"_index"`
	Score  float64      `json:"_score"`
	Source QuestionInfo `json:"_source"`
}
type Total struct {
	Relation string `json:"relation"`
	Value    int    `json:"value"`
}
type Hits1 struct {
	Hits     []Hits2 `json:"hits"`
	MaxScore float64 `json:"max_score"`
	Total    Total   `json:"total"`
}

type EsNluSearchResponse struct {
	Shards   Shards   `json:"_shards"`
	Hits     HitsNlu1 `json:"hits"`
	TimedOut bool     `json:"timed_out"`
	Took     int      `json:"took"`
}

type HitsNlu1 struct {
	Hits     []HitsNlu2 `json:"hits"`
	MaxScore float64    `json:"max_score"`
	Total    Total      `json:"total"`
}

type HitsNlu2 struct {
	ID     string      `json:"_id"`
	Index  string      `json:"_index"`
	Score  float64     `json:"_score"`
	Source NluQuestion `json:"_source"`
}
