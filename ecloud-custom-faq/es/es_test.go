package es

import (
	"acg-ai-go-common/global"
	"acg-ai-go-common/logger"
	"acg-ai-go-common/mongodb"
	"context"
	"ecloud-custom-faq/beans"
	"errors"
	"github.com/mozillazg/go-pinyin"
	"github.com/olivere/elastic"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"
	"strings"
	"testing"
	"time"
)

//func init() {
//	config := &global.ServerConfig{
//		RunEnv:        "",
//		ServerName:    "",
//		ServerPort:    0,
//		LogFilePrefix: "",
//		LogFilePath:   "",
//		ConsulSetting: nil,
//		MongoDBSetting: &global.MongoDBSetting{
//			URI: "************************************************",
//			//URI:    "*********************************************************************************************************************************************",
//			DBName: "aibot",
//		},
//		MysqlSetting: nil,
//		RedisSetting: nil,
//		ESSetting: &global.ElasticsearchSetting{
//			HostPortSetting: global.HostPortSetting{
//				Host: "http://*************:8200",
//				Port: 0,
//			},
//			//UserNamePwdSetting: global.UserNamePwdSetting{},
//		},
//		//PlatoSetting: nil,
//	}
//
//	global.ServerSetting = config
//	//
//	//InitEs()
//}

func TestSaveQuestion(t *testing.T) {

	question := beans.QuestionInfo{
		AppID:         "333",
		Qid:           "666",
		FirstClass:    "333",
		SecondClass:   "444",
		General:       false,
		KeywordGroups: []string{"11"},
		Questions:     []string{"niu nai hao he ma", "n n h h m", "u i o e a"},
		Answers:       make([]beans.FaqAnswer, 0),
		CreateTime:    time.Now().Format("2006-01-02 15:04:05"),
	}

	answer1 := beans.FaqAnswer{
		Type: "TEXT",
		Tags: nil,
		Speak: []beans.FaqAnswerText{
			{
				Answer: "还行1",
			},
			{
				Answer: "还行2",
			},
		},
		Expression:              "",
		Kv:                      nil,
		Text:                    beans.FaqAnswerTextV2{},
		Videos:                  nil,
		VideosDisplayFullScreen: false,
		Images:                  []beans.FaqImageSingleAnswerV2{},
		ImagesDisplayFullScreen: false,
		HtmlUrl:                 "",
		Html5DisplayFullScreen:  false,
		Recordings:              beans.AudioAnswer{},
		Action:                  beans.ActionLibraryAnswer{},
		HttpRequest:             beans.HttpRequestAnswer{},
		NotInterrupted:          false,
		RichText:                beans.RichTextAnswer{},
	}

	question.Answers = append(question.Answers, answer1)

	err := EsV8{}.SaveQuestion(question.AppID, question.Qid, question)
	t.Log(err)
}

func TestDelQuestion(t *testing.T) {
	err := EsV8{}.DelQuestion("222", "QJcKEIYBiZ8CnC9jD81I")
	if elastic.IsNotFound(err) {
		t.Log("not fount")
	}
	t.Log(err)
}

func TestSearchQuery(t *testing.T) {
	s := "党总支和支部渭源是如何产生的"
	a1 := pinyin.NewArgs()
	//a1.Style = pinyin.Initials
	//a1.Heteronym = true

	join := strings.Join(pinyin.LazyPinyin(s, a1), " ")
	query, err := EsV8{}.SearchFaqQuery("616d5ffc75d66f1e361f2a78", "pinYin", join, 1, 1)
	if err != nil {
		t.Log(err)

	}
	t.Log(query)
}

func TestTransferFAQToES(t *testing.T) {

	InitEsV8()
	err := mongodb.InitMongoDB()
	if err != nil {
		t.Log(err)
		return
	}

	num := int64(0)

	faqQuestions := make([]*beans.FaqQuestion, 0)

	type FindType struct {
		Status int `bson:"status"`
		//AppId  string `bson:"appId"`
	}

	Find("aibot", "unit_faq_question", 100, num, FindType{
		Status: 1,
		//AppId:  "5e5f178c171dfa7fe586431d",
	}, &faqQuestions)

	datas := make([]beans.QuestionInfo, 0)

	for i := len(faqQuestions); i >= 0; i-- {
		v := faqQuestions[i]

		if len(v.Questions) == 0 {
			continue
		}

		ond, err := FindOnd("aibot", "unit_faq_answer_v2", v.Qid)

		if err != nil {
			t.Log("find unit_faq_answer_v2 error:", err.Error(), "-qid:", v.Qid)
			continue
		}

		if ond.Qid == "" || ond.AppID == "" {
			t.Log("appId or Qid is null :", v)
			continue
		}

		general := false
		if ond.AnswerGranularityType != "" {
			general = ond.AnswerGranularityType == "GENERAL"
		}

		keyWord := make([]string, 0)

		if len(v.KeywordGroups) != 0 {
			for _, v2 := range v.KeywordGroups {
				keyWord = append(keyWord, v2.KeywordGroup)
			}
		}

		question := make([]string, 0)
		questionpinyin := make([]string, 0)

		for _, v3 := range v.Questions {
			question = append(question, v3.Question)
			a1 := pinyin.NewArgs()

			lazyPinyin := pinyin.LazyPinyin(v3.Question, a1)
			join := strings.Join(lazyPinyin, " ")
			if len(lazyPinyin) != 0 && join != "" {
				questionpinyin = append(questionpinyin, join)
			}
		}

		questionInfo := beans.QuestionInfo{
			AppID:         ond.AppID,
			Qid:           ond.Qid,
			FirstClass:    ond.FirstClassification,
			SecondClass:   ond.SecondClassification,
			General:       general,
			KeywordGroups: keyWord,
			Questions:     question,
			PinYin:        questionpinyin,
			Answers:       make([]beans.FaqAnswer, 0),
			CreateTime:    v.UpdateTime.Format("2006-01-02 15:04:05"),
		}

		questionInfo.Answers = ond.Answers
		datas = append(datas, questionInfo)
		num++

		if num == 100 {
			num = 0
			err = EsV8{}.SaveQuestions(datas)
			t.Log(datas, err)
			datas = make([]beans.QuestionInfo, 0)
		}
	}

	if len(datas) > 0 {
		err = EsV8{}.SaveQuestions(datas)
		t.Log(err)
	}

}

func TestDataV2(t *testing.T) {

	InitEsV8()
	err := mongodb.InitMongoDB()
	if err != nil {
		t.Log(err)
		t.Fail()
	}

	faqQuestions := make([]*beans.FaqQuestion, 0)

	type FindType struct {
		Status int `bson:"status"`
	}

	Find2(global.ServerSetting.MongoDBSetting.DBName, "unit_faq_question", FindType{
		Status: 1,
	}, &faqQuestions)

	num := 0
	datas := make([]beans.QuestionInfo, 0)

	for i := len(faqQuestions) - 1; i >= 0; i-- {
		v := faqQuestions[i]
		if len(v.Questions) == 0 {
			continue
		}

		ond, err := FindOnd(global.ServerSetting.MongoDBSetting.DBName, "unit_faq_answer_v2", v.Qid)

		if err != nil {
			logger.Log.Errorf("find mongo error , err:[%v]", err)
			continue
		}

		if ond.Qid == "" || ond.AppID == "" {
			logger.Log.Errorf("Qid or Appid is null value : [%v]", v)
			continue
		}

		general := false
		if ond.AnswerGranularityType != "" {
			general = ond.AnswerGranularityType == "GENERAL"
		}

		keyWord := make([]string, 0)

		if len(v.KeywordGroups) != 0 {
			for _, v2 := range v.KeywordGroups {
				keyWord = append(keyWord, v2.KeywordGroup)
			}
		}

		question := make([]string, 0)
		questionpinyin := make([]string, 0)

		for _, v3 := range v.Questions {
			question = append(question, v3.Question)
			a1 := pinyin.NewArgs()

			lazyPinyin := pinyin.LazyPinyin(v3.Question, a1)
			join := strings.Join(lazyPinyin, " ")
			if len(lazyPinyin) != 0 && join != "" {
				questionpinyin = append(questionpinyin, join)
			}
		}

		questionInfo := beans.QuestionInfo{
			AppID:         ond.AppID,
			Qid:           ond.Qid,
			FirstClass:    ond.FirstClassification,
			SecondClass:   ond.SecondClassification,
			General:       general,
			KeywordGroups: keyWord,
			Questions:     question,
			PinYin:        questionpinyin,
			Answers:       make([]beans.FaqAnswer, 0),
			CreateTime:    v.UpdateTime.Format("2006-01-02 15:04:05"),
		}

		questionInfo.Answers = ond.Answers
		datas = append(datas, questionInfo)
		num++

		if num == 100 {
			num = 0
			err = EsV8{}.SaveQuestions(datas)
			t.Log(datas)
			datas = make([]beans.QuestionInfo, 0)
		}
	}
	err = EsV8{}.SaveQuestions(datas)
}

// Find 查找
func Find(db string, collectName string, limit, page int64, queryModel interface{}, data interface{}) {
	collection := mongodb.GetConnect(db, collectName)
	if collection == nil {
		logger.Log.Error(db, collectName, "collection is nil")
		return
	}
	adoptions := &options.FindOptions{}
	if limit > 0 {
		adoptions.SetLimit(limit)
		adoptions.SetSkip((page - 1) * limit)
		adoptions.SetSort(bson.D{{"updateTime", -1}})
	}

	cursor, err := collection.Find(context.TODO(), queryModel, adoptions)
	if err != nil {
		logger.Log.Error(db, collectName, "Find error", err)
		return
	}

	// 延迟关闭
	defer func() {
		if err = cursor.Close(context.TODO()); err != nil {
			logger.Log.Fatal(err)
		}
	}()

	err = cursor.All(context.Background(), data)
	if err != nil {
		logger.Log.Error(db, collectName, err.Error())
		return
	}

	return
}

// Find 查找
func Find2(db string, collectName string, queryModel interface{}, data interface{}) {
	collection := mongodb.GetConnect(db, collectName)
	if collection == nil {
		logger.Log.Error(db, collectName, "collection is nil")
		return
	}

	cursor, err := collection.Find(context.TODO(), queryModel)
	if err != nil {
		logger.Log.Error(db, collectName, "Find error", err)
		return
	}

	// 延迟关闭
	defer func() {
		if err = cursor.Close(context.TODO()); err != nil {
			logger.Log.Fatal(err)
		}
	}()

	err = cursor.All(context.Background(), data)
	if err != nil {
		logger.Log.Error(db, collectName, err.Error())
		return
	}

	return
}

// Find 查找
func FindOnd(db string, collectName, qid string) (beans.FaqData, error) {
	data := &beans.FaqData{}
	collection := mongodb.GetConnect(db, collectName)
	if collection == nil {
		logger.Log.Error(db, collectName, "collection is nil")
		return *data, errors.New("collection is null ")
	}

	filter := bson.D{{"qid", qid}, {"status", 1}}
	one := collection.FindOne(context.TODO(), filter)

	err := one.Decode(data)

	if err != nil {
		logger.Log.Error(db, collectName, err.Error())
		return *data, err
	}

	return *data, nil
}
