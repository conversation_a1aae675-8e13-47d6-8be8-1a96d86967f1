<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.baidu.acg.piat</groupId>
    <artifactId>digital-human</artifactId>
    <packaging>pom</packaging>
    <version>${revision}</version>
    <properties>
        <revision>2.0.39-SNAPSHOT-v8</revision>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <argLine>--illegal-access=permit</argLine>
        <!-- dependencies version -->
        <junit5.version>5.8.2</junit5.version>
        <grpc.version>1.19.0</grpc.version>
        <protobuf.version>3.6.1</protobuf.version>
        <spring-boot.version>2.6.6</spring-boot.version>
        <atomix.version>3.1.5</atomix.version>
        <retrofit2.version>2.5.0</retrofit2.version>
        <curator.version>4.2.0</curator.version>
        <spdb.version>1.17.92-SNAPSHOT-v13</spdb.version>
        <jackson.version>********</jackson.version>
        <digitalhuman.tracer.version>2.1.32</digitalhuman.tracer.version>
        <common.utility.version>*********</common.utility.version>
        <user.client.version>3.3.2-SNAPSHOT</user.client.version>
    </properties>

    <description>
        2.0.5 deploy tts-client 请求前支持url-encode，返回消息封pom装riff头
        2.0.6 deploy tts-client 当发生错误时，不仅发送错误的complete消息，还发送错误的partial消息
        2.0.7 deploy digital-human-common in cloud, normalize the error code and message
        2.0.11 deploy rich-text-common 富文本解析动态配置
        2.0.12 deploy digital-human-common project新增conversationConfig、hotWordsReplaceReg、prologueParams等参数
        2.0.13 deploy digital-human-common 重构错误码
        ******** deploy digital-human-common project PaintChartOnPictureParams新增 paintWidgetOnPicture参数
        project JsonInclude 设置为NON_NULL
        2.0.15 build alita、video-pipeline、cloud、render-proxy-a2a、render-proxy-speech、render-proxy-ue4 reinforce code
        2.0.16 deploy digital-human-common 移除domutil，修改RichTextParser兼容用户不使用CDATA编写fusion内容
        2.0.17 project新增subtitleSplittable参数
        2.0.19 project新增html5Url，生成renderParameters的时候新增h5Emdded
        2.0.22-SNAPSHOT 修改project内嵌字幕生效逻辑
    </description>

    <modules>
        <module>digital-human-common</module>
        <module>digital-human-resource-pool</module>
        <module>digital-human-console</module>
        <module>digital-human-resource-pool-client</module>
        <module>digital-human-hypervisor-client</module>
        <module>digital-human-cloud</module>
        <module>proto-convoy</module>
        <module>digital-human-session-route-client</module>
        <module>digital-human-video-pipeline</module>
        <module>digital-human-plat</module>
        <module>digital-human-plat-client</module>
        <module>digital-human-video-pipeline-client</module>
        <module>digital-human-llm-dm-client</module>
    </modules>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.redisson</groupId>
                <artifactId>redisson-spring-boot-starter</artifactId>
                <version>3.17.3</version>
            </dependency>
            <dependency>
                <groupId>org.gagravarr</groupId>
                <artifactId>vorbis-java-core</artifactId>
                <version>0.8</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>easyexcel</artifactId>
                <version>3.3.1</version>
            </dependency>
            <!-- constant -->
            <dependency>
                <groupId>javax.annotation</groupId>
                <artifactId>javax.annotation-api</artifactId>
                <version>1.3.2</version>
            </dependency>

            <dependency>
                <groupId>javax.servlet</groupId>
                <artifactId>javax.servlet-api</artifactId>
                <version>3.0.1</version>
            </dependency>

            <dependency>
                <groupId>org.slf4j</groupId>
                <artifactId>slf4j-api</artifactId>
                <version>1.7.25</version>
            </dependency>

            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>1.18.24</version>
                <scope>provided</scope>
            </dependency>

            <dependency>
                <groupId>com.squareup.okhttp3</groupId>
                <artifactId>okhttp</artifactId>
                <version>3.12.2</version>
            </dependency>

            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-lang3</artifactId>
                <version>3.8.1</version>
            </dependency>

            <dependency>
                <groupId>commons-codec</groupId>
                <artifactId>commons-codec</artifactId>
                <version>1.12</version>
            </dependency>

            <dependency>
                <groupId>org.codehaus.janino</groupId>
                <artifactId>janino</artifactId>
                <version>3.0.12</version>
            </dependency>

            <!-- jackson -->
            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-databind</artifactId>
                <version>${jackson.version}</version>
            </dependency>

            <dependency>
                <groupId>javax.validation</groupId>
                <artifactId>validation-api</artifactId>
                <version>2.0.1.Final</version>
            </dependency>

            <!-- grpc -->
            <dependency>
                <groupId>io.grpc</groupId>
                <artifactId>grpc-netty-shaded</artifactId>
                <version>${grpc.version}</version>
            </dependency>

            <dependency>
                <groupId>io.grpc</groupId>
                <artifactId>grpc-netty</artifactId>
                <version>${grpc.version}</version>
            </dependency>

            <dependency>
                <groupId>io.grpc</groupId>
                <artifactId>grpc-protobuf</artifactId>
                <version>${grpc.version}</version>
            </dependency>

            <dependency>
                <groupId>io.grpc</groupId>
                <artifactId>grpc-stub</artifactId>
                <version>${grpc.version}</version>
            </dependency>

            <dependency>
                <groupId>io.grpc</groupId>
                <artifactId>grpc-core</artifactId>
                <version>${grpc.version}</version>
            </dependency>

            <dependency>
                <groupId>com.googlecode.protobuf-java-format</groupId>
                <artifactId>protobuf-java-format</artifactId>
                <version>1.4</version>
            </dependency>

            <dependency>
                <groupId>com.google.protobuf</groupId>
                <artifactId>protobuf-java-util</artifactId>
                <version>${protobuf.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.curator</groupId>
                <artifactId>curator-recipes</artifactId>
                <version>${curator.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.curator</groupId>
                <artifactId>curator-test</artifactId>
                <version>${curator.version}</version>
            </dependency>

            <!-- spring -->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-web</artifactId>
                <version>${spring-boot.version}</version>
            </dependency>

            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter</artifactId>
                <version>${spring-boot.version}</version>
            </dependency>

            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-websocket</artifactId>
                <version>${spring-boot.version}</version>
            </dependency>

            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-web</artifactId>
                <version>5.3.22</version>
            </dependency>

            <!-- 考虑换成spring bom -->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-cache</artifactId>
                <version>${spring-boot.version}</version>
            </dependency>

            <dependency>
                <groupId>com.baidu.aip</groupId>
                <artifactId>java-sdk</artifactId>
                <version>4.12.0</version>
            </dependency>

            <dependency>
                <groupId>com.baidu.acg.piat</groupId>
                <artifactId>unit-robot-sdk</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- mongo -->

            <dependency>
                <groupId>org.mongodb</groupId>
                <artifactId>bson</artifactId>
                <version>3.11.2</version>
                <scope>compile</scope>
            </dependency>

            <!-- jpa -->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-data-jpa</artifactId>
                <version>${spring-boot.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.data</groupId>
                <artifactId>spring-data-commons</artifactId>
                <version>2.6.3</version>
            </dependency>

            <dependency>
                <groupId>org.hibernate.javax.persistence</groupId>
                <artifactId>hibernate-jpa-2.1-api</artifactId>
                <version>1.0.0.Final</version>
            </dependency>

            <dependency>
                <groupId>org.hibernate</groupId>
                <artifactId>hibernate-core</artifactId>
                <version>5.6.10.Final</version>
            </dependency>

            <!-- sql -->
            <dependency>
                <groupId>mysql</groupId>
                <artifactId>mysql-connector-java</artifactId>
                <version>8.0.11</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.google.protobuf</groupId>
                        <artifactId>protobuf-java</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <!-- sql test-->
            <dependency>
                <groupId>com.h2database</groupId>
                <artifactId>h2</artifactId>
                <version>1.4.193</version>
                <scope>test</scope>
            </dependency>

            <!-- browser related -->
            <dependency>
                <groupId>org.seleniumhq.selenium</groupId>
                <artifactId>selenium-java</artifactId>
                <version>4.0.0-alpha-7</version>
            </dependency>

            <!-- xml -->
            <dependency>
                <groupId>com.fasterxml.jackson.dataformat</groupId>
                <artifactId>jackson-dataformat-xml</artifactId>
                <version>2.9.8</version>
            </dependency>

            <dependency>
                <groupId>jdom</groupId>
                <artifactId>jdom</artifactId>
                <version>1.0</version>
            </dependency>
            <dependency>
                <groupId>org.jdom</groupId>
                <artifactId>jdom2</artifactId>
                <version>2.0.6</version>
            </dependency>
            <dependency>
                <groupId>org.dom4j</groupId>
                <artifactId>dom4j</artifactId>
                <version>2.1.1</version>
            </dependency>

            <!-- digital human -->

            <dependency>
                <groupId>com.baidu.acg.piat</groupId>
                <artifactId>digital-human-tracer</artifactId>
                <version>${digitalhuman.tracer.version}</version>
            </dependency>

            <dependency>
                <groupId>com.baidu.acg.piat</groupId>
                <artifactId>digital-human-common</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.baidu.acg.piat</groupId>
                <artifactId>digital-human-common-utility</artifactId>
                <version>${common.utility.version}</version>
            </dependency>

            <dependency>
                <groupId>com.baidu.acg.piat</groupId>
                <artifactId>digital-human-resource-pool</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.baidu.acg.piat</groupId>
                <artifactId>digital-human-hypervisor</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.baidu.acg.piat</groupId>
                <artifactId>digital-human-plat</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.baidu.acg.piat</groupId>
                <artifactId>digital-human-notifier</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.baidu.acg.piat</groupId>
                <artifactId>video-structured</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.baidu.acg.piat</groupId>
                <artifactId>threejs-render-client</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.baidu.acg.piat</groupId>
                <artifactId>aicp-sdk-java</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.baidu.acg.piat</groupId>
                <artifactId>chatbot-adaptor</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.baidu.acg.piat</groupId>
                <artifactId>ibngd-sdk-java</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.baidu.acg.piat</groupId>
                <artifactId>ngd-sdk-java</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.baidu.acg.piat</groupId>
                <artifactId>digital-human-plat-client</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.baidu.acg.piat</groupId>
                <artifactId>digital-human-resource-pool-client</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.baidu.acg.piat</groupId>
                <artifactId>digital-human-video-pipeline-client</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.baidu.acg.piat</groupId>
                <artifactId>digital-human-tts-client</artifactId>
                <version>${common.utility.version}</version>
            </dependency>

            <dependency>
                <groupId>com.baidu.acg.piat</groupId>
                <artifactId>digital-human-storage</artifactId>
                <version>${common.utility.version}</version>
            </dependency>

            <dependency>
                <groupId>com.baidu.acg.piat</groupId>
                <artifactId>digital-human-hypervisor-client</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.baidu.acg.piat</groupId>
                <artifactId>brtc-agent-sdk</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.baidu.acg.piat</groupId>
                <artifactId>digital-human-session-route-client</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.baidu.acg.pie</groupId>
                <artifactId>digital-human-sdk-java</artifactId>
                <version>1.0.30</version>
            </dependency>

            <dependency>
                <groupId>com.baidu.acg.piat</groupId>
                <artifactId>rich-text-common</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.baidu.acg.digitalhuman</groupId>
                <artifactId>render-proxy-a2a</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.baidu.acg.digitalhuman</groupId>
                <artifactId>render-proxy-base</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.baidu.acg.digitalhuman</groupId>
                <artifactId>render-proxy-common</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.baidu.acg.digitalhuman</groupId>
                <artifactId>render</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.baidu.acg.digitalhuman</groupId>
                <artifactId>render-proxy-vis-2d</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.baidu.acg.piat</groupId>
                <artifactId>digital-human-cloud</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.baidu.acg.piat</groupId>
                <artifactId>digital-human-console</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.baidu.acg.piat</groupId>
                <artifactId>proto-convoy</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.baidu.acg.piat</groupId>
                <artifactId>threejs-common</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.baidu.acg.piat</groupId>
                <artifactId>digital-human-video-pipeline</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.baidu.acg.piat</groupId>
                <artifactId>digital-human-alita</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.baidu.acg.piat</groupId>
                <artifactId>digital-human-alita-slim</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.baidu.acg.piat</groupId>
                <artifactId>threejs-render</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.baidu.acg.piat</groupId>
                <artifactId>digital-human-agent</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.baidu.acu-bot</groupId>
                <artifactId>dh-user-client</artifactId>
                <version>${user.client.version}</version>
            </dependency>

            <dependency>
                <groupId>org.glassfish.jersey.core</groupId>
                <artifactId>jersey-client</artifactId>
                <version>2.9.1</version>
            </dependency>
            <dependency>
                <groupId>org.glassfish.jersey.connectors</groupId>
                <artifactId>jersey-apache-connector</artifactId>
                <version>2.9.1</version>
            </dependency>
            <dependency>
                <groupId>org.glassfish.jersey.media</groupId>
                <artifactId>jersey-media-json-jackson</artifactId>
                <version>2.9.1</version>
            </dependency>
            <dependency>
                <groupId>org.glassfish.jersey.core</groupId>
                <artifactId>jersey-common</artifactId>
                <version>2.9.1</version>
            </dependency>
            <dependency>
                <groupId>org.glassfish.jersey.media</groupId>
                <artifactId>jersey-media-multipart</artifactId>
                <version>2.9.1</version>
            </dependency>
            <dependency>
                <groupId>org.glassfish.jersey.core</groupId>
                <artifactId>jersey-server</artifactId>
                <version>2.9.1</version>
            </dependency>

            <!-- retrofit -->
            <dependency>
                <groupId>com.squareup.retrofit2</groupId>
                <artifactId>retrofit</artifactId>
                <version>${retrofit2.version}</version>
            </dependency>

            <dependency>
                <groupId>com.squareup.retrofit2</groupId>
                <artifactId>converter-gson</artifactId>
                <version>${retrofit2.version}</version>
            </dependency>

            <dependency>
                <groupId>com.squareup.retrofit2</groupId>
                <artifactId>converter-jackson</artifactId>
                <version>${retrofit2.version}</version>
            </dependency>

            <!-- atomix -->
            <dependency>
                <groupId>io.atomix</groupId>
                <artifactId>atomix</artifactId>
                <version>${atomix.version}</version>
            </dependency>

            <dependency>
                <groupId>io.atomix</groupId>
                <artifactId>atomix-primary-backup</artifactId>
                <version>${atomix.version}</version>
            </dependency>

            <dependency>
                <groupId>io.atomix</groupId>
                <artifactId>atomix-raft</artifactId>
                <version>${atomix.version}</version>
            </dependency>

            <!-- baidu -->
            <dependency>
                <groupId>com.baidu.acu.pie</groupId>
                <artifactId>audio-streaming-client-java</artifactId>
                <version>1.1.12</version>
                <exclusions>
                    <exclusion>
                        <groupId>io.grpc</groupId>
                        <artifactId>grpc-netty</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>io.grpc</groupId>
                        <artifactId>grpc-core</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.slf4j</groupId>
                        <artifactId>slf4j-simple</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.slf4j</groupId>
                        <artifactId>slf4j-api</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.fasterxml.jackson.core</groupId>
                        <artifactId>jackson-core</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.fasterxml.jackson.core</groupId>
                        <artifactId>jackson-annotations</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.baidu.scan</groupId>
                <artifactId>safesdk-java</artifactId>
                <version>2.0.1-SNAPSHOT</version>
            </dependency>

            <!-- local cache -->
            <!-- https://mvnrepository.com/artifact/com.github.ben-manes.caffeine/caffeine -->
            <dependency>
                <groupId>com.github.ben-manes.caffeine</groupId>
                <artifactId>caffeine</artifactId>
                <version>2.8.0</version>
            </dependency>

            <!-- bos -->
            <dependency>
                <groupId>com.baidubce</groupId>
                <artifactId>bce-java-sdk</artifactId>
                <version>0.10.70</version>
            </dependency>

            <!-- test -->
            <dependency>
                <groupId>org.mockito</groupId>
                <artifactId>mockito-core</artifactId>
                <version>2.23.4</version>
            </dependency>

            <dependency>
                <groupId>org.junit.jupiter</groupId>
                <artifactId>junit-jupiter-api</artifactId>
                <version>${junit5.version}</version>
                <scope>test</scope>
            </dependency>

            <dependency>
                <groupId>org.junit.jupiter</groupId>
                <artifactId>junit-jupiter-engine</artifactId>
                <version>${junit5.version}</version>
                <scope>test</scope>
            </dependency>

            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-test</artifactId>
                <version>${spring-boot.version}</version>
                <scope>test</scope>
            </dependency>

            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-aop</artifactId>
                <version>${spring-boot.version}</version>
            </dependency>

            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-data-redis</artifactId>
                <version>${spring-boot.version}</version>
            </dependency>

            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-actuator</artifactId>
                <version>${spring-boot.version}</version>
            </dependency>

            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-configuration-processor</artifactId>
                <optional>true</optional>
                <version>${spring-boot.version}</version>
            </dependency>

            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>28.1-jre</version>
            </dependency>

            <dependency>
                <groupId>org.redisson</groupId>
                <artifactId>redisson</artifactId>
                <version>3.17.3</version>
            </dependency>

            <dependency>
                <groupId>io.vavr</groupId>
                <artifactId>vavr</artifactId>
                <version>0.9.3</version>
            </dependency>

            <dependency>
                <groupId>com.machinezoo.noexception</groupId>
                <artifactId>noexception</artifactId>
                <version>1.4.4</version>
            </dependency>

            <!-- dependencies from spdb digital human repository
            http://icode.baidu.com/repos/baidu/acu-piat/digital-human/tree/master -->


            <dependency>
                <groupId>com.baidu.acg.piat</groupId>
                <artifactId>digital-human-common-in-private</artifactId>
                <version>${spdb.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.springframework.data</groupId>
                        <artifactId>spring-data-mongodb</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.baidu.acg.piat</groupId>
                <artifactId>digital-human-animations</artifactId>
                <version>${spdb.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.springframework.data</groupId>
                        <artifactId>spring-data-mongodb</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.baidu.acg.piat</groupId>
                <artifactId>digital-human-blend-shape</artifactId>
                <version>${spdb.version}</version>
            </dependency>

            <dependency>
                <groupId>com.baidu.acg.piat</groupId>
                <artifactId>digital-human-animations-proxy</artifactId>
                <version>${spdb.version}</version>
            </dependency>

            <dependency>
                <groupId>com.baidu.acg.piat</groupId>
                <artifactId>digital-human-video2bs</artifactId>
                <version>${spdb.version}</version>
            </dependency>

            <!-- <dependency>
                <groupId>com.baidu.acg.piat</groupId>
                <artifactId>digital-human-agent</artifactId>
                <version>${spdb.version}</version>
            </dependency> -->

            <dependency>
                <groupId>com.baidu.acg.piat</groupId>
                <artifactId>richtext-common</artifactId>
                <version>${spdb.version}</version>
            </dependency>

            <dependency>
                <groupId>com.baidu.acg.piat</groupId>
                <artifactId>richtext-framework</artifactId>
                <version>${spdb.version}</version>
            </dependency>

            <dependency>
                <groupId>com.baidu.acg.piat</groupId>
                <artifactId>richtext-handler</artifactId>
                <version>${spdb.version}</version>
            </dependency>

            <dependency>
                <groupId>com.auth0</groupId>
                <artifactId>java-jwt</artifactId>
                <version>3.4.1</version>
            </dependency>

            <dependency>
                <groupId>io.springfox</groupId>
                <artifactId>springfox-boot-starter</artifactId>
                <version>3.0.0</version>
            </dependency>

            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-collections4</artifactId>
                <version>4.4</version>
                <scope>compile</scope>
            </dependency>

            <dependency>
                <groupId>commons-beanutils</groupId>
                <artifactId>commons-beanutils</artifactId>
                <version>1.8.0</version>
            </dependency>

            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-text</artifactId>
                <version>1.9</version>
            </dependency>

<!--            <dependency>-->
<!--                <groupId>ch.qos.logback</groupId>-->
<!--                <artifactId>logback-access</artifactId>-->
<!--            </dependency>-->

            <dependency>
                <groupId>org.hibernate.validator</groupId>
                <artifactId>hibernate-validator</artifactId>
                <version>6.1.5.Final</version>
            </dependency>

            <dependency>
                <groupId>javax.xml.bind</groupId>
                <artifactId>jaxb-api</artifactId>
                <version>2.3.0</version>
            </dependency>

            <dependency>
                <groupId>javax.activation</groupId>
                <artifactId>javax.activation-api</artifactId>
                <version>1.2.0</version>
            </dependency>

            <!--            <dependency>-->
            <!--                <groupId>org.glassfish.jersey.core</groupId>-->
            <!--                <artifactId>jersey-common</artifactId>-->
            <!--                <version>2.9.1</version>-->
            <!--            </dependency>-->

            <dependency>
                <groupId>com.baidu.bce</groupId>
                <artifactId>bce-plat-web-framework-iam</artifactId>
                <version>2.2.1.8-SNAPSHOT</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.springframework.boot</groupId>
                        <artifactId>spring-boot-actuator-autoconfigure</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>ch.qos.logback</groupId>
                        <artifactId>logback-core</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>ch.qos.logback</groupId>
                        <artifactId>logback-access</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>ch.qos.logback</groupId>
                        <artifactId>logback-classic</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.baidubce.formula</groupId>
                        <artifactId>config-client-spring-boot-starter</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.baidubce.formula</groupId>
                        <artifactId>discovery-spring-boot-starter</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.baidubce.formula</groupId>
                        <artifactId>formula-engine</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.baidubce.formula</groupId>
                        <artifactId>ratelimiter-spring-boot-starter</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.baidubce.formula</groupId>
                        <artifactId>route-spring-boot-starter</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>commons-collections</groupId>
                        <artifactId>commons-collections</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.glassfish.jersey.core</groupId>
                        <artifactId>jersey-common</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.springframework.cloud</groupId>
                        <artifactId>spring-cloud-netflix-ribbon</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.baidu.bce</groupId>
                        <artifactId>bce-plat-web-framework</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.springframework.boot</groupId>
                        <artifactId>spring-boot-actuator-autoconfigure</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>it.sauronsoftware</groupId>
                <artifactId>jave</artifactId>
                <version>1.0.2</version>
            </dependency>

            <!-- kafka -->
            <dependency>
                <groupId>org.apache.kafka</groupId>
                <artifactId>kafka-clients</artifactId>
                <version>0.10.1.1</version>
            </dependency>

            <!-- prometheus -->
            <dependency>
                <groupId>io.micrometer</groupId>
                <artifactId>micrometer-registry-prometheus</artifactId>
                <version>1.8.4</version>
            </dependency>

            <dependency>
                <groupId>io.prometheus</groupId>
                <artifactId>simpleclient</artifactId>
                <version>0.15.0</version>
            </dependency>

            <dependency>
                <groupId>org.json</groupId>
                <artifactId>json</artifactId>
                <version>20201115</version>
            </dependency>

            <dependency>
                <groupId>org.postgresql</groupId>
                <artifactId>postgresql</artifactId>
                <version>42.2.18</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <dependencies>
        <!-- constant -->
        <dependency>
            <groupId>javax.annotation</groupId>
            <artifactId>javax.annotation-api</artifactId>
        </dependency>

        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-api</artifactId>
        </dependency>

        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
        </dependency>

        <dependency>
            <groupId>org.codehaus.janino</groupId>
            <artifactId>janino</artifactId>
        </dependency>

        <!-- test -->
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter-api</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter-engine</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>org.postgresql</groupId>
            <artifactId>postgresql</artifactId>
        </dependency>
    </dependencies>

    <build>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>kr.motd.maven</groupId>
                    <artifactId>os-maven-plugin</artifactId>
                    <version>1.4.1.Final</version>
                </plugin>

                <plugin>
                    <groupId>org.xolstice.maven.plugins</groupId>
                    <artifactId>protobuf-maven-plugin</artifactId>
                    <version>0.5.1</version>
                </plugin>

                <plugin>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <version>3.8.0</version>
                </plugin>

                <plugin>
                    <artifactId>maven-source-plugin</artifactId>
                    <version>3.0.1</version>
                </plugin>

                <plugin>
                    <artifactId>maven-surefire-plugin</artifactId>
                    <version>2.22.1</version>
                </plugin>

                <plugin>
                    <groupId>org.jacoco</groupId>
                    <artifactId>jacoco-maven-plugin</artifactId>
                    <version>0.8.2</version>
                </plugin>

            </plugins>
        </pluginManagement>

        <plugins>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>flatten-maven-plugin</artifactId>
                <version>1.1.0</version>
                <configuration>
                    <updatePomFile>true</updatePomFile>
                    <flattenMode>resolveCiFriendliesOnly</flattenMode>
                </configuration>
                <executions>
                    <execution>
                        <id>flatten</id>
                        <phase>process-resources</phase>
                        <goals>
                            <goal>flatten</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>flatten.clean</id>
                        <phase>clean</phase>
                        <goals>
                            <goal>clean</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <release>11</release>
                </configuration>
            </plugin>

            <plugin>
                <artifactId>maven-source-plugin</artifactId>
                <executions>
                    <execution>
                        <phase>package</phase>
                        <goals>
                            <goal>jar</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>

            <plugin>
                <artifactId>maven-surefire-plugin</artifactId>
            </plugin>

            <plugin>
                <groupId>org.jacoco</groupId>
                <artifactId>jacoco-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <goals>
                            <goal>prepare-agent</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

    <profiles>
        <profile>
            <id>cloud</id>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
            <build>
                <pluginManagement>
                    <plugins>
                        <plugin>
                            <groupId>org.springframework.boot</groupId>
                            <artifactId>spring-boot-maven-plugin</artifactId>
                            <version>${spring-boot.version}</version>
                        </plugin>
                    </plugins>
                </pluginManagement>
            </build>
        </profile>
    </profiles>

    <repositories>
        <repository>
            <id>baidu-nexus</id>
            <url>http://maven.baidu-int.com/nexus/content/groups/public</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
        </repository>
    </repositories>

    <pluginRepositories>
        <pluginRepository>
            <id>baidu-nexus</id>
            <url>http://maven.baidu-int.com/nexus/content/groups/public</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
        </pluginRepository>
    </pluginRepositories>

    <distributionManagement>
        <repository>
            <id>Baidu_Local</id>
            <url>http://maven.baidu-int.com/nexus/content/repositories/Baidu_Local</url>
        </repository>
        <snapshotRepository>
            <id>Baidu_Local_Snapshots</id>
            <url>http://maven.baidu-int.com/nexus/content/repositories/Baidu_Local_Snapshots</url>
        </snapshotRepository>
    </distributionManagement>
</project>
