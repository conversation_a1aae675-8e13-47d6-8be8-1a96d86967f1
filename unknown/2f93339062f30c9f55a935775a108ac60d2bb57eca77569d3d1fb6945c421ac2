package voice_clone

import (
	"acg-ai-go-common/utils/httputil"
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"mime/multipart"
	"net/http"
	"os"
	"path/filepath"
	"time"
)

// AddVoice 向 ElevenLabs 添加声音
func AddVoice(logCtx context.Context, apiKey string, filePaths []string, reqData AddVoiceRequest) (*AddVoiceResponse,
	error) {
	buf := &bytes.Buffer{}
	writer := multipart.NewWriter(buf)

	if err := writer.WriteField("name", reqData.Name); err != nil {
		return nil, fmt.Errorf("WriteField error: %w", err)
	}

	if len(reqData.Labels) > 0 {
		if err := writer.WriteField("labels", reqData.Labels); err != nil {
			return nil, fmt.Errorf("WriteField error: %w", err)
		}
	}
	if len(reqData.Description) > 0 {
		if err := writer.WriteField("description", reqData.Description); err != nil {
			return nil, fmt.Errorf("WriteField error: %w", err)
		}
	}
	//if err := writer.WriteField("remove_background_noise", fmt.Sprintf("%v", reqData.RemoveBackgroundNoise)); err != nil {
	//	return nil, fmt.Errorf("WriteField error: %w", err)
	//}

	// 添加音频文件
	for _, path := range filePaths {
		file, err := os.Open(path)
		if err != nil {
			return nil, fmt.Errorf("open file failed: %v", err)
		}
		defer file.Close()

		part, err := writer.CreateFormFile("files", filepath.Base(path))
		if err != nil {
			return nil, fmt.Errorf("create form failed: %v", err)
		}

		if _, err := io.Copy(part, file); err != nil {
			return nil, fmt.Errorf("write form failed: %v", err)
		}
	}

	if err := writer.Close(); err != nil {
		return nil, fmt.Errorf("close multipart writer: %v", err)
	}

	// 设置 headers
	headers := map[string]string{
		"Xi-Api-Key":   apiKey,
		"Content-Type": writer.FormDataContentType(),
	}

	// 构造请求客户端
	httpclient := httputil.NewRetryHTTPClient(15*time.Minute, 3)
	respBody, err := httpclient.DoRequest(logCtx, http.MethodPost, ADD_VOICE_URL, headers, buf)
	if err != nil {
		return nil, fmt.Errorf("failed to send AddVoice request: %w", err)
	}

	var result AddVoiceResponse
	err = json.Unmarshal(respBody, &result)
	if err != nil {
		return nil, fmt.Errorf("unmarshal response failed: %v", err)
	}

	if len(result.VoiceID) < 1 {
		return nil, fmt.Errorf("something error: %v", string(respBody))
	}

	return &result, nil
}

// GetVoiceInfo 查询指定 voice_id 的详细信息
func GetVoiceInfo(logCtx context.Context, apiKey, voiceID string) (*VoiceInfo, error) {
	URL := fmt.Sprintf(GET_VOICE_URL, voiceID)

	headers := map[string]string{
		"Xi-Api-Key": apiKey,
	}

	httpclient := httputil.NewRetryHTTPClient(15*time.Minute, 3)
	respBody, err := httpclient.DoRequest(logCtx, http.MethodGet, URL, headers, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to get voice info: %w", err)
	}

	var result VoiceInfo
	err = json.Unmarshal(respBody, &result)
	if err != nil {
		return nil, fmt.Errorf("unmarshal response failed: %v, resp: %v", err, string(respBody))
	}

	if len(result.VoiceId) < 1 {
		return nil, fmt.Errorf("something error: %v", string(respBody))
	}

	return &result, nil
}

// DeleteVoice 删除指定 voice_id
func DeleteVoice(logCtx context.Context, apiKey, voiceID string) error {
	URL := fmt.Sprintf(GET_VOICE_URL, voiceID)

	headers := map[string]string{
		"Xi-Api-Key": apiKey,
	}

	httpclient := httputil.NewRetryHTTPClient(15*time.Minute, 3)
	respBody, err := httpclient.DoRequest(logCtx, http.MethodDelete, URL, headers, nil)
	if err != nil {
		return fmt.Errorf("failed to get voice info: %w", err)
	}

	var result DeleteResponse
	err = json.Unmarshal(respBody, &result)
	if err != nil {
		return fmt.Errorf("unmarshal response failed: %v", err)
	}

	if len(result.Status) < 1 || result.Status != "ok" {
		return fmt.Errorf("something error: %v", string(respBody))
	}

	return nil
}

func DoTts(logCtx context.Context, apiKey, voiceID string, text string, speed float64) (*TtsResponse, error) {
	url := fmt.Sprintf("%s/%s/with-timestamps?output_format=pcm_16000", TTS_VOICE_URL, voiceID)

	headers := map[string]string{
		"Xi-Api-Key":   apiKey,
		"Content-Type": "application/json",
	}
	req := TTSRequest{
		Text: text,
		VoiceSettings: &VoiceSettings{
			Speed: speed,
		},
	}

	body, err := json.Marshal(req)
	if err != nil {
		return nil, fmt.Errorf("marshal failed: %v", err)
	}

	httpclient := httputil.NewRetryHTTPClient(15*time.Minute, 3)
	respBody, err := httpclient.DoRequest(logCtx, http.MethodPost, url, headers, bytes.NewReader(body))
	if err != nil {
		return nil, fmt.Errorf("failed to get voice info: %w", err)
	}

	rsp := &TtsResponse{}
	err = json.Unmarshal(respBody, &rsp)
	if err != nil {
		return nil, fmt.Errorf("unmarshal failed: %v， { %s }", err, string(respBody))
	}

	if len(rsp.AudioBase64) < 1 {
		if err != nil {
			return nil, fmt.Errorf("server error: %s", string(respBody))
		}
	}

	return rsp, nil
}
