package schedeuler

import (
	"acg-ai-go-common/logger"
	"acg-ai-go-common/utils/redisproxy"
	"digital-human-micro-video-trans/conf"
	"digital-human-micro-video-trans/thirdparty/sutils"
	"digital-human-micro-video-trans/thirdparty/sutils/concurrentlimiter"
	"digital-human-micro-video-trans/thirdparty/sutils/fileutils"
	"fmt"
	"os"
	"strings"
	"sync/atomic"
	"time"
)

var (
	Scheduler *TaskScheduler
)

var (
	LockCleanCacheTime string = "lock-micro-video-translate-clean-cache-time"
	LockCleanVoiceTime string = "lock-micro-video-translate-clean-voice-time"
)

type TaskScheduler struct {
	running int32

	// 各个环节的限流器
	QuotaTimeout time.Duration
	SetTimeout   time.Duration
	LockTimeout  time.Duration

	AsrLimiter       *concurrentlimiter.ConcurrentLimiter
	TranslateLimiter *concurrentlimiter.ConcurrentLimiter
	TtsLimiter       *concurrentlimiter.ConcurrentLimiter

	// 清理缓存的时间
	LastCleanTime      time.Time
	LastCleanVoiceTime time.Time

	// 声音处理的prompt
	VoicePrompt string
}

// NewTaskScheduler 初始化调度器，需要指定可用的执行器数量
func NewTaskScheduler(workerCount int) *TaskScheduler {
	podName, err := sutils.GetPodName()
	if err != nil {
		podName = "scheduler"
		logger.Log.Warnf("get pod name error，relace with [%s]", podName)
	}

	tc := &TaskScheduler{
		running:            0,
		QuotaTimeout:       time.Duration(conf.LocalConfig.LimiterSettings.QuotaTimeoutHour) * time.Hour,
		SetTimeout:         time.Duration(conf.LocalConfig.LimiterSettings.SetTimeoutHour) * time.Hour,
		LockTimeout:        time.Duration(conf.LocalConfig.LimiterSettings.LockTimeoutSec) * time.Second,
		LastCleanTime:      time.Now(),
		LastCleanVoiceTime: time.Now(),
	}

	tc.VoicePrompt = "下面给出以下参数：1，text：原始文本；2，newText：翻译后的文本；3，currentDuration：" +
		"翻译后的文本合成音频的长度；4，targetDuration：翻译后的文本合成音频期望的长度；5，targetLang：翻译的目标语种。" +
		"你需要根据当前情况，判断newText文本内容需要变长还是变短，当currentDuration小于targetDuration，则文本需要变长；currentDuration大于targetDuration，则文本需要变短。" +
		"同时，这个差值越小，则本次修改的幅度越小，涉及的单词改动就越少。每一次都只进行极其轻微的修改，不可以出现超过10个词的变化。" +
		"根据这个规则进一步修改newText，过程中保证newText的语种为targetLang。" +
		"在保持语义完整的情况下，使得该文本在合成后，音频时长逼近目标时长。" +
		"仅输出调整后的newText文本，不要输出任何其他东西。"
	if len(conf.LocalConfig.VoiceTuningSettings.PromptFile) > 0 && fileutils.IsExists(conf.LocalConfig.
		VoiceTuningSettings.PromptFile) {
		data, err := os.ReadFile(conf.LocalConfig.VoiceTuningSettings.PromptFile)
		if err != nil {
			logger.Log.Warnf("read voice prompt failed: %v", err)
		} else {
			tc.VoicePrompt = strings.ReplaceAll(string(data), "\n", "")
			logger.Log.Infof("read voice prompt: { %s } ", tc.VoicePrompt)
		}
	}

	tc.TtsLimiter = concurrentlimiter.NewConcurrentLimiter(redisproxy.GetRedisProxy().Rdb,
		fmt.Sprintf("%s-tts-limiter", sutils.GetNameByRunEnv()),
		int64(conf.LocalConfig.ElevenLabsSettings.ConcurrencyLimit), tc.QuotaTimeout, tc.SetTimeout, tc.LockTimeout)
	tc.AsrLimiter = concurrentlimiter.NewConcurrentLimiter(redisproxy.GetRedisProxy().Rdb,
		fmt.Sprintf("%s-asr-limiter", sutils.GetNameByRunEnv()),
		int64(conf.LocalConfig.ElevenLabsSettings.ConcurrencyLimit), tc.QuotaTimeout, tc.SetTimeout, tc.LockTimeout)
	tc.TranslateLimiter = concurrentlimiter.NewConcurrentLimiter(redisproxy.GetRedisProxy().Rdb,
		fmt.Sprintf("%s-translate-limiter", sutils.GetNameByRunEnv()),
		int64(conf.LocalConfig.GeminiSettings.ConcurrencyLimit), tc.QuotaTimeout, tc.SetTimeout, tc.LockTimeout)
	return tc
}

func (p *TaskScheduler) RunScheduler() {
	if atomic.LoadInt32(&p.running) >= int32(conf.LocalConfig.ScheduleSettings.MaxRunningSize) {
		return
	}
	atomic.AddInt32(&p.running, 1)
	defer atomic.AddInt32(&p.running, -1)
	logger.Log.Info("start run center scheduler")

	// 处理刚提交的任务
	p.HandleSubmit()

	// 预处理
	p.HandlePreprocess()

	// 翻译与音频克隆
	p.HandleVoiceClone()

	// 视频合成
	p.HandleVideoTransfer()

	// 后处理
	p.HandlePostProcess()

	// 超时判断
	p.HandleTimeout()

	// 失败重试
	p.HandleFailed()

	// 清理
	if time.Now().Sub(p.LastCleanTime) > time.Duration(conf.LocalConfig.CacheSettings.CleanPeriodDay)*24*time.Hour {
		p.HandleClean()
		p.LastCleanTime = time.Now()
	}
	if time.Now().Sub(p.LastCleanVoiceTime) > time.Duration(conf.LocalConfig.ElevenLabsSettings.DeleteVoiceHour)*time.Hour {
		p.HandleCleanVoice()
		p.LastCleanVoiceTime = time.Now()
	}
}
