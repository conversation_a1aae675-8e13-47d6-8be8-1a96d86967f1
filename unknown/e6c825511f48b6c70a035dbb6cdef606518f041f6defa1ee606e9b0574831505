package schedeuler

import (
	"acg-ai-go-common/gomysql"
	"acg-ai-go-common/logger"
	"acg-ai-go-common/utils"
	"acg-ai-go-common/utils/redisproxy"
	"context"
	"digital-human-micro-video-trans/beans/enums"
	"digital-human-micro-video-trans/beans/model"
	"digital-human-micro-video-trans/beans/proto"
	"digital-human-micro-video-trans/conf"
	"digital-human-micro-video-trans/thirdparty/sutils"
	"digital-human-micro-video-trans/thirdparty/sutils/fileutils"
	"digital-human-micro-video-trans/thirdparty/sutils/jsonutils"
	"digital-human-micro-video-trans/thirdparty/sutils/redislock"
	"fmt"
	"gorm.io/gorm"
	"time"
)

func (p *TaskScheduler) HandleClean() {
	logger.Log.Info("start handle clean task cache")

	cleanId := fmt.Sprintf("lock-videotrans-cleancache-%s", sutils.GetNameByRunEnv())

	logCtx := context.WithValue(context.TODO(), utils.CtxKeyLogID, cleanId)
	redisProxy := redisproxy.GetRedisProxy()

	// 加锁
	redisLockKey := fmt.Sprintf(TaskScheduleLockFmt, sutils.GetNameByRunEnv(), cleanId)
	redisLock := redislock.NewRedisLock(redisProxy.Rdb, redisLockKey, TaskScheduleLockExpireTime)
	ok, err := redisLock.Lock(context.Background())
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"clean cache Lock key: %s error: %+v", redisLockKey, err)
		return
	} else if !ok {
		logger.Log.Warnf(utils.MMark(logCtx) + "redis key locked by other instance")
		return
	}
	defer redisLock.Unlock(context.Background())

	// 列出所有缓存文件夹
	folders, err := fileutils.ListFolders(conf.LocalConfig.CacheSettings.RootPath)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"get folders in cache path: %s failed: %+v", conf.LocalConfig.CacheSettings.RootPath, err)
		return
	}

	tNow := time.Now()
	for _, folder := range folders {
		if tNow.Sub(folder.ModTime) > time.Duration(conf.LocalConfig.CacheSettings.CleanTimeoutDay)*24*time.Hour {
			logger.Log.Infof(utils.MMark(logCtx)+"task folder %s is timeout > %d day, clean it.",
				folder.Name, conf.LocalConfig.CacheSettings.CleanTimeoutDay)

			err = fileutils.DeleteFolder(folder.Path)
			if err != nil {
				logger.Log.Errorf(utils.MMark(logCtx)+"clean task cache: %s failed: %+v", folder.Name, err)
			}
		}
	}
}

func (p *TaskScheduler) HandleCleanVoice() {
	logger.Log.Info("start handle clean voice clone cache")

	cleanId := fmt.Sprintf("lock-videotrans-cleanvoice-%s", sutils.GetNameByRunEnv())

	logCtx := context.WithValue(context.TODO(), utils.CtxKeyLogID, cleanId)
	redisProxy := redisproxy.GetRedisProxy()

	// 加锁
	redisLockKey := fmt.Sprintf(TaskScheduleLockFmt, sutils.GetNameByRunEnv(), cleanId)
	redisLock := redislock.NewRedisLock(redisProxy.Rdb, redisLockKey, TaskScheduleLockExpireTime)
	ok, err := redisLock.Lock(context.Background())
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"clean cache Lock key: %s error: %+v", redisLockKey, err)
		return
	} else if !ok {
		logger.Log.Warnf(utils.MMark(logCtx) + "redis key locked by other instance")
		return
	}
	defer redisLock.Unlock(context.Background())

	list, err := (&model.VideoTranslateTask{}).GetTasksWithStatusAndVoice(gomysql.DB, enums.Failed, 0)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"get task with voice not delete failed: %v", err)
		return
	}

	for _, item := range list {
		err = gomysql.DB.Transaction(func(tx *gorm.DB) error {
			progress := proto.NewProgress()
			var temp proto.Progress
			if err := jsonutils.JsonMapToStruct(item.Progress, &temp); err == nil {
				progress = &temp
			}

			err = DeleteCloneVoice(logCtx, progress)
			if err != nil {
				return err
			}

			// 保存状态
			jsConfig, err := jsonutils.StructToJsonMap(progress)
			if err != nil {
				return fmt.Errorf("save progress detail failed: %v", err)
			}
			item.Progress = jsConfig

			// 更新
			item.VoiceDeleted = 1
			err = item.Update(tx)
			if err != nil {
				return fmt.Errorf("update to: %s failed: %v", item.Status, err)
			}
			return nil
		})

		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+"delete voice failed, err: %v", err)
		}
	}

}
