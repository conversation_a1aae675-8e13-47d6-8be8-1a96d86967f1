package elevenlabs_test

import (
	"context"
	"digital-human-micro-video-trans/thirdparty/heygen"
	"fmt"
	"testing"
)

const (
	HeygenUrl string = "https://api.heygen.com/v2/video_translate"
	InputUrl  string = "https://saas-water-mark-output.bj.bcebos.com/f881885c-1ee4-49f5-be7a-76927545f765/e5741ab2-d5db-4ed8-9b0a-de85b4480e2b/single_dot.mp4"
	ApiKey    string = "************************************************************"
)

func TestVideoTranslate(t *testing.T) {
	logCtx := context.Background()

	req := heygen.TranslateVideoRequest{
		VideoURL:              InputUrl,
		OutputLanguage:        "English",
		EnableDynamicDuration: false,
	}
	task, err := heygen.SubmitTask(logCtx, HeygenUrl, Api<PERSON><PERSON>, &req)
	if err != nil {
		fmt.Printf("submit failed: %v\n", err)
		return
	}
	fmt.Printf("submit task: %v\n", task)
}

func TestVideoTranslateCheck(t *testing.T) {
	logCtx := context.Background()

	taskId := "6ad203e7a5034da8a969a4f687572c88"
	task, err := heygen.CheckTask(logCtx, HeygenUrl, ApiKey, taskId)
	if err != nil {
		fmt.Printf("CheckTask failed: %v\n", err)
		return
	}
	fmt.Printf("CheckTask task: %+v\n", task.Data)
}

func TestVideoTranslateCaption(t *testing.T) {
	logCtx := context.Background()

	taskId := "1182381e42d84f1ebe4d94098846b7bf"
	task, err := heygen.GetCaption(logCtx, HeygenUrl, ApiKey, taskId, "vtt")
	if err != nil {
		fmt.Printf("GetCaption failed: %v\n", err)
		return
	}
	fmt.Printf("GetCaption task: %+v\n", task.Data)
}
