package speech2text

type Character struct {
	Text  string  `json:"text,omitempty"`
	Start float64 `json:"start,omitempty"`
	End   float64 `json:"end,omitempty"`
}

type Word struct {
	Text       string       `json:"text,omitempty"`
	Type       string       `json:"type,omitempty"`
	Start      float64      `json:"start,omitempty"`
	End        float64      `json:"end,omitempty"`
	SpeakerId  string       `json:"speaker_id,omitempty"`
	Characters []*Character `json:"characters,omitempty"`
}

type AdditionalFormat struct {
	RequestFormat   string `json:"request_format,omitempty"`
	FileExtension   string `json:"file_extension,omitempty"`
	ContentType     string `json:"content_type,omitempty"`
	IsBase64Encoded bool   `json:"is_base64_encoded,omitempty"`
	Content         string `json:"content,omitempty"`
}

type Response struct {
	LanguageCode        string              `json:"language_code,omitempty"`
	LanguageProbability float64             `json:"language_probability,omitempty"`
	Text                string              `json:"text,omitempty"`
	Words               []*Word             `json:"words,omitempty"`
	AdditionalFormats   []*AdditionalFormat `json:"additional_formats,omitempty"`
}

type RequestAdditionalFormat struct {
	Format                      string  `json:"format,omitempty"`
	IncludeSpeakers             bool    `json:"include_speakers,omitempty"`
	IncludeTimestamps           bool    `json:"include_timestamps,omitempty"`
	MaxCharactersPerLine        int     `json:"max_characters_per_line,omitempty"`
	MaxSegmentChars             int     `json:"max_segment_chars,omitempty"`
	MaxSegmentDurationS         float64 `json:"max_segment_duration_s,omitempty"`
	SegmentOnSilenceLongerThanS float64 `json:"segment_on_silence_longer_than_s,omitempty"`
}

type Request struct {
	ModelId               string                     `json:"model_id,omitempty"`
	File                  string                     `json:"file,omitempty"`
	LanguageCode          string                     `json:"language_code,omitempty"`
	TagAudioEvents        bool                       `json:"tag_audio_events,omitempty"`
	NumSpeakers           int                        `json:"num_speakers,omitempty"`
	TimestampsGranularity string                     `json:"timestamps_granularity,omitempty"`
	Diarize               bool                       `json:"diarize,omitempty"`
	AdditionalFormats     []*RequestAdditionalFormat `json:"additional_formats,omitempty"`
	FileFormat            string                     `json:"file_format,omitempty"`
	CloudStorageUrl       string                     `json:"cloud_storage_url,omitempty"`
	Temperature           float64                    `json:"temperature,omitempty"`
}

type Sentence struct {
	Text      string  `json:"text,omitempty"`
	NewText   string  `json:"newText,omitempty"`
	Start     float64 `json:"start,omitempty"`
	End       float64 `json:"end,omitempty"`
	LastStart float64 `json:"lastStart,omitempty"`
	AudioFile string  `json:"audio,omitempty"`

	// 下面的字段是业务加入的，原接口没有
	TtsFile string  `json:"ttsFile,omitempty"`
	TtsUrl  string  `json:"ttsUrl,omitempty"`
	TtStart float64 `json:"ttStart,omitempty"`
	TtsEnd  float64 `json:"ttsEnd,omitempty"`
	Scale   float64 `json:"scale,omitempty"`
	IsAdd   bool    `json:"isAdd,omitempty"`

	Alignment struct {
		Characters                 []string  `json:"characters,omitempty"`
		CharacterStartTimesSeconds []float64 `json:"character_start_times_seconds,omitempty"`
		CharacterEndTimesSeconds   []float64 `json:"character_end_times_seconds,omitempty"`
	} `json:"alignment,omitempty"`
}

type Speaker struct {
	Id        string      `json:"id,omitempty"`
	Sentences []*Sentence `json:"sentences,omitempty"`
	Lang      string      `json:"lang,omitempty"`
	Duration  float64     `json:"duration,omitempty"`
	AudioNum  int         `json:"audioNum,omitempty"`
}
