package gemini

import (
	"cloud.google.com/go/vertexai/genai"
	"context"
	"fmt"
	"google.golang.org/api/option"
)

func GenerateGeminiContent(logCtx context.Context, credential, projectId, location, modelType, systemInstruction string,
	contentTexts string) (string,
	error) {
	client, err := genai.NewClient(logCtx, projectId, location, option.WithCredentialsFile(credential))
	if err != nil {
		return "", fmt.<PERSON>rro<PERSON>("new client failed: %v", err)
	}
	defer func(client *genai.Client) {
		client.Close()
	}(client)
	model := client.GenerativeModel(modelType)

	var inputParts []genai.Part
	inputParts = append(inputParts, genai.Text(fmt.Sprintf("%s\n%s", systemInstruction, contentTexts)))

	resp, err := model.GenerateContent(logCtx, inputParts...)
	if err != nil {
		return "", fmt.Errorf("generate content failed: %v", err)
	}

	if len(resp.Candidates) == 0 || len(resp.Candidates[0].Content.Parts) == 0 {
		return "", fmt.Errorf("empty response from model")
	}

	if text, ok := resp.Candidates[0].Content.Parts[0].(genai.Text); ok {
		return string(text), nil
	}
	return "", fmt.Errorf("get content from part failed: %v", resp.Candidates[0].Content.Parts[0])
}

func GenerateGeminiChatContent(logCtx context.Context, credential, projectId, location, modelType string,
	messages []Message) (string, error) {

	client, err := genai.NewClient(logCtx, projectId, location, option.WithCredentialsFile(credential))
	if err != nil {
		return "", fmt.Errorf("new client failed: %v", err)
	}
	defer client.Close()

	model := client.GenerativeModel(modelType)

	// 创建对话会话
	chat := model.StartChat()

	for _, msg := range messages {
		content := &genai.Content{
			Role: string(msg.Role),
			Parts: []genai.Part{
				genai.Text(msg.Content),
			},
		}
		chat.History = append(chat.History, content)
	}

	// 获取最后一轮用户输入
	lastUserMsg := ""
	for i := len(messages) - 1; i >= 0; i-- {
		if messages[i].Role == "user" {
			lastUserMsg = messages[i].Content
			break
		}
	}
	if lastUserMsg == "" {
		return "", fmt.Errorf("no user message found for generation")
	}

	// 发送最新一轮用户输入
	resp, err := chat.SendMessage(logCtx, genai.Text(lastUserMsg))
	if err != nil {
		return "", fmt.Errorf("generate content failed: %v", err)
	}

	if len(resp.Candidates) == 0 || len(resp.Candidates[0].Content.Parts) == 0 {
		return "", fmt.Errorf("empty response from model")
	}

	if text, ok := resp.Candidates[0].Content.Parts[0].(genai.Text); ok {
		return string(text), nil
	}
	return "", fmt.Errorf("get content from part failed: %v", resp.Candidates[0].Content.Parts[0])
}
