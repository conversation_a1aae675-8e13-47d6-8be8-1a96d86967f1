package morpheus

type TrainSubmitRequest struct {
	UserId           string `json:"userId,omitempty"`
	VideoUrl         string `json:"videoUrl,omitempty"`
	VideoUrl2        string `json:"videoUrl2,omitempty"`
	MaskVideoUrl     string `json:"maskVideoUrl,omitempty"`
	BackgroundImgUrl string `json:"backgroundImgUrl,omitempty"`
	BackgroundRemove bool   `json:"backgroundRemove,omitempty"`
	ForegroundCrop   bool   `json:"foregroundCrop,omitempty"`
	Name             string `json:"name,omitempty"`
	Gender           string `json:"gender,omitempty"`
	ResolutionWidth  int    `json:"resolutionWidth,omitempty"`
	ResolutionHeight int    `json:"resolutionHeight,omitempty"`
	Shared           bool   `json:"shared,omitempty"`
	TaskId           string `json:"taskId,omitempty"`
	ResourceLabel    string `json:"resourceLabel,omitempty"`
	CallbackUrl      string `json:"callbackUrl,omitempty"`
	SceneLabel       string `json:"sceneLabel,omitempty"`
}

type SubmitResult struct {
	Id int `json:"id,omitempty"`
}

type TrainSubmitResponse struct {
	Code    int  `json:"code"`
	Success bool `json:"success"`
	Message struct {
		Global string `json:"global"`
	} `json:"message"`
	Result *SubmitResult `json:"result,omitempty"`
}

type FigureQueryRequest struct {
	UserId       string `json:"userId"`
	FilterSystem bool   `json:"filterSystem,omitempty"`
	Available    bool   `json:"available,omitempty"`
	FigureIds    []int  `json:"figureIds"`
}

type Result struct {
	TaskId    string `json:"task_id"`
	Status    string `json:"status"`
	FigureId  string `json:"figure_id"`
	FigureUrl string `json:"figure_url"`
	Msg       string `json:"msg"`
}

type ResultMessage struct {
	Success      bool        `json:"success"`
	Ip           string      `json:"ip"`
	Result       interface{} `json:"result"`
	Width        int         `json:"width"`
	Height       int         `json:"height"`
	TaskId       string      `json:"task_id"`
	ErrCode      int         `json:"err_code"`
	ErrMsg       string      `json:"err_msg"`
	FaceLocation string      `json:"face_location"`
}

type FigureData struct {
	Id               int    `json:"id,omitempty"`
	Source           string `json:"source,omitempty"`
	Name             string `json:"name,omitempty"`
	Status           string `json:"status,omitempty"`
	ResourceLabel    string `json:"resourceLabel,omitempty"`
	TemplateImg      string `json:"templateImg,omitempty"`
	TemplateVideo    string `json:"templateVideo,omitempty"`
	FigureName       string `json:"figureName,omitempty"`
	SubmitTime       string `json:"submitTime,omitempty"`
	ConsumeTime      string `json:"consumeTime,omitempty"`
	Info             string `json:"info,omitempty"`
	IsNew            bool   `json:"isNew,omitempty"`
	LastUsedTime     string `json:"lastUsedTime,omitempty"`
	Collected        bool   `json:"collected,omitempty"`
	RecentlyUsed     bool   `json:"recentlyUsed,omitempty"`
	SystemProvided   bool   `json:"systemProvided,omitempty"`
	Type             string `json:"type,omitempty"`
	ResolutionWidth  int    `json:"resolutionWidth,omitempty"`
	ResolutionHeight int    `json:"resolutionHeight,omitempty"`
	VideoUrl         string `json:"videoUrl,omitempty"`
	VideoUrl2        string `json:"videoUrl2,omitempty"`
	MaskVideoUrl     string `json:"maskVideoUrl,omitempty"`
	FigureResult     string `json:"figureResult,omitempty"`
	EffectsThumbnail string `json:"effectsThumbnail,omitempty"`
	Effects          string `json:"effects,omitempty"`
	MatchTts         string `json:"matchTts,omitempty"`
	Scene            string `json:"scene,omitempty"`
	Thumbnail        string `json:"thumbnail,omitempty"`
	IsDelete         int    `json:"isDelete,omitempty"`
	Gender           string `json:"gender,omitempty"`
	ResultMessage    string `json:"resultMessage,omitempty"`
	TaskId           string `json:"taskId,omitempty"`
}

type Page struct {
	PageNo     int           `json:"pageNo,omitempty"`
	PageSize   int           `json:"pageSize,omitempty"`
	TotalCount int           `json:"totalCount,omitempty"`
	Result     []*FigureData `json:"result,omitempty"`
}

type FigureQueryResponse struct {
	Code    int  `json:"code"`
	Success bool `json:"success"`
	Message struct {
		Global string `json:"global"`
	} `json:"message"`
	Page *Page `json:"page,omitempty"`
}
