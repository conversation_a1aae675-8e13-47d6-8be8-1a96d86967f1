package music_ai_test

import (
	"acg-ai-go-common/utils"
	"context"
	"digital-human-micro-video-trans/thirdparty/musicai"
	"encoding/json"
	"fmt"
	"testing"
)

const (
	Name     string = "test-triple"
	InputUrl string = "https://storage.googleapis.com/xiling_us_central1_bucket/micro-video-translate/task/2025-07-14/vte-rcAhPKvyHVzE5mGb/in_task_item.wav"

	JobId  string = "a619dee1-03e8-497c-ab80-6b8e49e6f72d"
	ApiKey string = "6e41d274-9033-40d3-9c77-31a58eeed3ad"
)

func TestSubmitJob(t *testing.T) {
	logCtx := context.WithValue(context.TODO(), utils.CtxKeyLogID, utils.RandStringRunes(10))

	jobId, err := musicai.SubmitJob(logCtx, Name, "cinematic-stems", InputUrl, "<PERSON>", <PERSON><PERSON><PERSON><PERSON>)
	if err != nil {
		fmt.Printf("SubmitJob failed: %v\n", err)
		return
	}

	fmt.Printf("SubmitJob result id: %s\n", jobId)
}

func TestCheckJob(t *testing.T) {
	logCtx := context.WithValue(context.TODO(), utils.CtxKeyLogID, utils.RandStringRunes(10))

	job, err := musicai.GetJobResult(logCtx, "028be5ac-3faf-480e-b82f-33c14f7899aa", ApiKey)
	if err != nil {
		fmt.Printf("GetJobResult failed: %v\n", err)
		return
	}

	b, _ := json.MarshalIndent(job, "", "  ")
	fmt.Printf("GetJobResult result: %v\n", string(b))

	if job.Status == "SUCCEEDED" {
		fmt.Printf("GetJobResult result: %v\n", job.Result)
	}
}

func TestDeleteJob(t *testing.T) {
	logCtx := context.WithValue(context.TODO(), utils.CtxKeyLogID, utils.RandStringRunes(10))

	job, err := musicai.DeleteJob(logCtx, "ss", ApiKey)
	if err != nil {
		fmt.Printf("DeleteJob failed: %v\n", err)
		return
	}

	b, _ := json.MarshalIndent(job, "", "  ")
	fmt.Printf("DeleteJob result: %v\n", string(b))
}
