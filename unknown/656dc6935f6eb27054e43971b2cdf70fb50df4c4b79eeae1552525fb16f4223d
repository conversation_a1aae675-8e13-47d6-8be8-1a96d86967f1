package ffmpeg

import (
	"acg-ai-go-common/utils"
	"context"
	"digital-human-micro-video-trans/thirdparty/ffmpeg"
	"fmt"
	"testing"
)

func TestExtraAudio(t *testing.T) {
	inputFile := "/Users/<USER>/Downloads/youtube-test.mp4"
	outputFile := "/Users/<USER>/Downloads/youtube-test-out.wav"
	logCtx := context.WithValue(context.TODO(), utils.CtxKeyLogID, utils.RandStringRunes(10))
	err := ffmpeg.ExtractAudio(logCtx, inputFile, outputFile, "pcm_s16le", 1, 16000)
	if err != nil {
		fmt.Printf("ExtractAudio failed: %v\n", err)
		return
	}

	fmt.Printf("ExtractAudio result: %v\n", outputFile)
}

func TestAudioValid(t *testing.T) {
	inputFile := "/Users/<USER>/Downloads/youtube-test.mp4"
	logCtx := context.WithValue(context.TODO(), utils.CtxKeyLogID, utils.RandStringRunes(10))
	valid, err := ffmpeg.HasValidAudio(logCtx, inputFile)
	if err != nil {
		fmt.Printf("HasValidAudio failed: %v\n", err)
		return
	}

	fmt.Printf("HasValidAudio result: %v\n", valid)
}
