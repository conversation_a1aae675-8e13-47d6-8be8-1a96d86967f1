package test

import (
	"digital-human-micro-video-trans/handler/services/subtitle"
	"digital-human-micro-video-trans/thirdparty/elevenlabs/speech2text"
	"encoding/json"
	"fmt"
	"os"
	"testing"
)

const (
	InputFile string = "./input/test_tts_text.json"
)

func TestMakeSubtitle(t *testing.T) {
	data, err := os.ReadFile(InputFile)
	if err != nil {
		fmt.Printf("read file error: %v\n", err)
		return
	}
	var result []*speech2text.Speaker
	if err := json.Unmarshal(data, &result); err != nil {
		fmt.Printf("json unmarshal error: %v\n", err)
		return
	}

	// 生成字幕
	content, err := subtitle.GenerateSubtitleFromSpeakers(result, true, true, 30, 3.0, true, 14, 3)
	if err != nil {
		fmt.Printf("make subtitle failed: %v\n", err)
		return
	}

	fmt.Printf("\n%s\n", content)
}
