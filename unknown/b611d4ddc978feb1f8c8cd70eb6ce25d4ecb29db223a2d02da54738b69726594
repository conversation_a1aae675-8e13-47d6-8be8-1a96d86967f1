package schedeuler

import (
	"acg-ai-go-common/gomysql"
	"acg-ai-go-common/logger"
	"acg-ai-go-common/utils"
	"acg-ai-go-common/utils/redisproxy"
	"context"
	"digital-human-micro-video-trans/beans/enums"
	"digital-human-micro-video-trans/beans/model"
	"digital-human-micro-video-trans/beans/proto"
	"digital-human-micro-video-trans/conf"
	"digital-human-micro-video-trans/thirdparty/sutils"
	"digital-human-micro-video-trans/thirdparty/sutils/jsonutils"
	"digital-human-micro-video-trans/thirdparty/sutils/redislock"
	"fmt"
	"gorm.io/gorm"
	"time"
)

func (p *TaskScheduler) HandleTimeout() {
	logger.Log.Info("start check running timeout task")

	runningStatues := []enums.TaskStatus{enums.Submit, enums.Success, enums.Failed}
	taskList, err := (&model.VideoTranslateTask{}).GetTasksWithoutStatuses(gomysql.DB, runningStatues)
	if err != nil {
		logger.Log.Errorf("get task without status: %v, err: %v", runningStatues, err)
		return
	}

	if len(taskList) < 1 {
		logger.Log.Infof("no running task need check timeout")
		return
	}

	scheduleCount := 0
	for _, item := range taskList {
		err := p.timeoutCheck(item)
		if err != nil {
			logger.Log.Errorf("schedule task: %s failed, cause: %v", item.TaskId, err)
		}
		scheduleCount++
		if scheduleCount >= conf.LocalConfig.ScheduleSettings.MaxScheduleSize {
			logger.Log.Infof("scheduled reach max control size: %d/%d", scheduleCount,
				conf.LocalConfig.ScheduleSettings.MaxScheduleSize)
			break
		}
	}
}

func (p *TaskScheduler) timeoutCheck(task *model.VideoTranslateTask) error {
	logCtx := context.WithValue(context.TODO(), utils.CtxKeyLogID, task.TaskId)
	redisProxy := redisproxy.GetRedisProxy()

	// 加锁
	redisLockKey := fmt.Sprintf(TaskScheduleLockFmt, sutils.GetNameByRunEnv(), task.TaskId)
	redisLock := redislock.NewRedisLock(redisProxy.Rdb, redisLockKey, TaskScheduleLockExpireTime)
	ok, err := redisLock.Lock(context.Background())
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"PreProcessing Lock key: %s error: %+v\n", redisLockKey, err)
		return fmt.Errorf("get redis lock err: %v", err)
	} else if !ok {
		logger.Log.Warnf(utils.MMark(logCtx) + "redis key locked by other instance, skip it.")
		return nil
	}
	defer redisLock.Unlock(context.Background())

	// 重新查询
	item, err := task.GetTasksWithTaskId(gomysql.DB, task.TaskId)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"refresh task err: %v", err)
		return fmt.Errorf("get refresh task err: %v", err)
	}

	// 这些是不需要处理超时的任务
	runningStatues := []enums.TaskStatus{enums.Submit, enums.Success, enums.Failed}

	// 状态变了，就不用处理了
	if containsStatus(runningStatues, item.Status) {
		return fmt.Errorf("task status nolonger %v, now: %v", runningStatues, item.Status)
	}

	progress := proto.NewProgress()
	var temp proto.Progress
	if err := jsonutils.JsonMapToStruct(item.Progress, &temp); err == nil {
		progress = &temp
	}

	var config proto.SubmitConfig
	if err := jsonutils.JsonMapToStruct(item.Config, &config); err != nil {
		return fmt.Errorf("parse submit config failed: %v", err)
	}

	//start := time.Now()
	timeout := 0
	err = gomysql.DB.Transaction(func(tx *gorm.DB) error {
		cache, err := GetTaskCache(item)
		if err != nil {
			return fmt.Errorf("parse task cache failed: %v", err)
		}

		var subErr error
		timeout, subErr = p.checkTimout(logCtx, item, &config, progress, cache)
		if subErr != nil {
			return subErr
		}

		if timeout == 1 {
			item.Status = enums.Failed
			item.Message = "task timeout"
		}

		// 更新
		err = item.Update(tx)
		if err != nil {
			return fmt.Errorf("update to: %s failed: %v", item.Status, err)
		}

		return nil
	})

	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"schedule failed, err: %+v\n", err)

		// 更新任务状态
		item.Status = enums.Failed
		item.Message = fmt.Sprintf("%s", err.Error())

		// 也需要保存状态，有些部分不需要重复重试
		jsConfig, err := jsonutils.StructToJsonMap(progress)
		if err != nil {
			return fmt.Errorf("save progress detail failed: %v", err)
		}
		item.Progress = jsConfig

		err = item.Update(gomysql.DB)
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+"update item failed: %+v, err: %+v\n", item, err)
			return err
		}

		return fmt.Errorf("schedule task err: %v", err)
	}
	return nil
}

func containsStatus(statuses []enums.TaskStatus, target enums.TaskStatus) bool {
	for _, s := range statuses {
		if s == target {
			return true
		}
	}
	return false
}

func (p *TaskScheduler) checkTimout(logCtx context.Context, item *model.VideoTranslateTask,
	config *proto.SubmitConfig, progress *proto.Progress, cache *CachePath) (int, error) {
	tNow := time.Now()
	lastUpdateTime := item.UpdatedAt

	if item.Status == enums.VideoTransfer && item.SubStatus == enums.VideoFigureTrainWait {
		// 训练超时
		if tNow.Sub(lastUpdateTime) > time.Duration(conf.LocalConfig.TimeoutSettings.TaskTrainTimeoutHour)*time.
			Hour {
			return 1, nil
		}
	} else if item.Status == enums.VideoTransfer && item.SubStatus == enums.VideoTransferWait {
		// 合成超时
		if tNow.Sub(lastUpdateTime) > time.Duration(conf.LocalConfig.TimeoutSettings.TaskVideoTimeoutHour)*time.
			Hour {
			return 1, nil
		}
	} else {
		// 任务整体持续时间超时
		if tNow.Sub(lastUpdateTime) > time.Duration(conf.LocalConfig.TimeoutSettings.TaskTimeoutHour)*time.
			Hour {
			return 1, nil
		}
	}

	return 0, nil
}
