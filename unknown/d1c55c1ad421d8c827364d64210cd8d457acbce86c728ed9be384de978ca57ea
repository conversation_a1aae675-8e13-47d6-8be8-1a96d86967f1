package audio_isolation

import (
	"acg-ai-go-common/utils/httputil"
	"bytes"
	"context"
	"fmt"
	"io"
	"mime/multipart"
	"net/http"
	"os"
	"path/filepath"
	"time"
)

const (
	URL string = "https://api.elevenlabs.io/v1/audio-isolation"
)

func DoAudioIsolate(
	logCtx context.Context,
	apiKey string,
	filePath string,
) ([]byte, error) {
	file, err := os.Open(filePath)
	if err != nil {
		return nil, fmt.Errorf("open file failed: %v", err)
	}
	defer file.Close()

	var buf bytes.Buffer
	writer := multipart.NewWriter(&buf)

	part, err := writer.CreateFormFile("audio", filepath.Base(filePath))
	if err != nil {
		return nil, fmt.Errorf("CreateFormFile failed: %v", err)
	}

	if _, err := io.Copy(part, file); err != nil {
		return nil, fmt.Errorf("io.Copy failed: %v", err)
	}

	if err := writer.Close(); err != nil {
		return nil, fmt.Errorf("writer.Close failed: %v", err)
	}
	headers := map[string]string{
		"xi-api-key":   apiKey,
		"Content-Type": writer.FormDataContentType(),
	}

	httpclient := httputil.NewRetryHTTPClient(15*time.Minute, 3)
	respBody, err := httpclient.DoRequest(logCtx, http.MethodPost, URL, headers, &buf)
	if err != nil {
		return nil, fmt.Errorf("DoRequest failed: %v", err)
	}

	return respBody, nil
}
