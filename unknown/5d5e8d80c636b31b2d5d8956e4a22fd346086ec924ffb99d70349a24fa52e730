package schedeuler

import (
	"acg-ai-go-common/gomysql"
	"acg-ai-go-common/logger"
	"acg-ai-go-common/utils"
	"acg-ai-go-common/utils/redisproxy"
	"context"
	"digital-human-micro-video-trans/beans/enums"
	"digital-human-micro-video-trans/beans/model"
	"digital-human-micro-video-trans/beans/proto"
	"digital-human-micro-video-trans/conf"
	"digital-human-micro-video-trans/thirdparty/sutils"
	"digital-human-micro-video-trans/thirdparty/sutils/jsonutils"
	"digital-human-micro-video-trans/thirdparty/sutils/redislock"
	"fmt"
	"gorm.io/gorm"
	"time"
)

func (p *TaskScheduler) HandleFailed() {
	logger.Log.Info("start handle failed task")

	taskList, err := (&model.VideoTranslateTask{}).GetTasksWithStatusAndRetry(gomysql.DB, enums.Failed)
	if err != nil {
		logger.Log.Errorf("get task with status: %s, err: %v", enums.Failed, err)
		return
	}

	if len(taskList) < 1 {
		logger.Log.Infof("no task with status: %s need schedule", enums.Failed)
		return
	}

	scheduleCount := 0
	for _, item := range taskList {
		err := p.retryTask(item)
		if err != nil {
			logger.Log.Errorf("schedule task: %s failed, cause: %v", item.TaskId, err)
		}
		scheduleCount++
		if scheduleCount >= conf.LocalConfig.ScheduleSettings.MaxScheduleSize {
			logger.Log.Infof("scheduled reach max control size: %d/%d", scheduleCount,
				conf.LocalConfig.ScheduleSettings.MaxScheduleSize)
			break
		}
	}
	logger.Log.Infof("scheduled task: %d with status: %s", scheduleCount, enums.Failed)
}

func (p *TaskScheduler) retryTask(task *model.VideoTranslateTask) error {
	logCtx := context.WithValue(context.TODO(), utils.CtxKeyLogID, task.TaskId)
	redisProxy := redisproxy.GetRedisProxy()

	// 加锁
	redisLockKey := fmt.Sprintf(TaskScheduleLockFmt, sutils.GetNameByRunEnv(), task.TaskId)
	redisLock := redislock.NewRedisLock(redisProxy.Rdb, redisLockKey, TaskScheduleLockExpireTime)
	ok, err := redisLock.Lock(context.Background())
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"PreProcessing Lock key: %s error: %+v\n", redisLockKey, err)
		return fmt.Errorf("get redis lock err: %v", err)
	} else if !ok {
		logger.Log.Warnf(utils.MMark(logCtx) + "redis key locked by other instance, skip it.")
		return nil
	}
	defer redisLock.Unlock(context.Background())

	// 重新查询
	item, err := task.GetTasksWithTaskId(gomysql.DB, task.TaskId)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"refresh task err: %v", err)
		return fmt.Errorf("get refresh task err: %v", err)
	}

	// 状态变了，就不用处理了
	if item.Status != enums.Failed {
		return fmt.Errorf("task status nolonger %s", enums.Failed)
	}

	progress := proto.NewProgress()
	var temp proto.Progress
	if err := jsonutils.JsonMapToStruct(item.Progress, &temp); err == nil {
		progress = &temp
	}

	var config proto.SubmitConfig
	if err := jsonutils.JsonMapToStruct(item.Config, &config); err != nil {
		return fmt.Errorf("parse submit config failed: %v", err)
	}

	//start := time.Now()
	needRetry := 1
	err = gomysql.DB.Transaction(func(tx *gorm.DB) error {
		cache, err := GetTaskCache(item)
		if err != nil {
			return fmt.Errorf("parse task cache failed: %v", err)
		}

		var subErr error
		needRetry, subErr = p.checkRetryStatus(logCtx, item, &config, progress, cache)
		if subErr != nil {
			return subErr
		}

		if needRetry == 1 {
			// 如果可以重试的话，进行重试，需要判定重试的规则
			if item.RetryType == enums.RetryTypeAll {
				item.Status = enums.Submit
				item.SubStatus = enums.None
				item.RetryAttempts--
			} else {
				// 先恢复一下主状态
				item.Status, _ = ParseTaskSubStatus(item.SubStatus)
				if item.RetryType == enums.RetryTypeAllSubStatus {
					// 重试所有子状态的话，置为第一个状态
					item.SubStatus, _ = GetFirstTaskSubStatus(item.Status)
				}
				item.RetryAttempts--
			}
			item.Message = ""

			logger.Log.Infof(utils.MMark(logCtx) + "task can be retry, refresh it")
		} else {
			item.Retry = 0

			// 到这里就结束了
			tNow := time.Now()
			item.EndTime = &tNow
			if item.StartTime != nil && item.EndTime != nil {
				item.CostTime = int(item.EndTime.Sub(*item.StartTime).Seconds())
			}
		}

		// 更新
		err = item.Update(tx)
		if err != nil {
			return fmt.Errorf("update to: %s failed: %v", item.Status, err)
		}

		err = CallbackToApp(logCtx, item)
		if err != nil {
			logger.Log.Warnf(utils.MMark(logCtx)+"callback failed, err: %v", err)
		}

		return nil
	})

	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"schedule failed, err: %+v\n", err)

		// 更新任务状态
		item.Retry = needRetry
		item.Status = enums.Failed
		item.Message = fmt.Sprintf("%s", err.Error())

		// 也需要保存状态，有些部分不需要重复重试
		jsConfig, err := jsonutils.StructToJsonMap(progress)
		if err != nil {
			return fmt.Errorf("save progress detail failed: %v", err)
		}
		item.Progress = jsConfig

		err = item.Update(gomysql.DB)
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+"update item failed: %+v, err: %+v\n", item, err)
			return err
		}

		return fmt.Errorf("schedule task err: %v", err)
	}
	return nil
}

func (p *TaskScheduler) checkRetryStatus(logCtx context.Context, item *model.VideoTranslateTask,
	config *proto.SubmitConfig, progress *proto.Progress, cache *CachePath) (int, error) {
	if item.Retry < 1 {
		return 0, nil
	}
	if item.RetryAttempts < 1 {
		return 0, nil
	}
	return 1, nil
}
