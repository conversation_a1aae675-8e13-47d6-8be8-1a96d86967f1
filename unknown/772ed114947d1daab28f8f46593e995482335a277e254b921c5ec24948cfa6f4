package speech2text

import (
	"acg-ai-go-common/utils/httputil"
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"mime/multipart"
	"net/http"
	"os"
	"path/filepath"
	"sort"
	"strconv"
	"strings"
	"time"
)

const (
	URL string = "https://api.elevenlabs.io/v1/speech-to-text"
)

func DoSpeechToText(
	logCtx context.Context,
	apiKey string,
	req *Request,
) (*Response, error) {
	if req == nil {
		return nil, fmt.Errorf("request struct is null")
	}
	if req.ModelId == "" {
		return nil, fmt.Errorf("model_id is required")
	}
	if req.File == "" && req.CloudStorageUrl == "" {
		return nil, fmt.Errorf("either file or cloud_storage_url is required")
	}

	var buf bytes.Buffer
	writer := multipart.NewWriter(&buf)

	write := func(field, value string) error {
		return writer.WriteField(field, value)
	}

	if err := write("model_id", req.ModelId); err != nil {
		return nil, fmt.Errorf("write model_id: %v", err)
	}
	if req.LanguageCode != "" {
		if err := write("language_code", req.LanguageCode); err != nil {
			return nil, fmt.Errorf("write language_code: %v", err)
		}
	}
	if req.TagAudioEvents {
		if err := write("tag_audio_events", "true"); err != nil {
			return nil, fmt.Errorf("write tag_audio_events: %v", err)
		}
	}
	if req.NumSpeakers > 0 {
		if err := write("num_speakers", strconv.Itoa(req.NumSpeakers)); err != nil {
			return nil, fmt.Errorf("write num_speakers: %v", err)
		}
	}
	if req.TimestampsGranularity != "" {
		if err := write("timestamps_granularity[]", req.TimestampsGranularity); err != nil {
			return nil, fmt.Errorf("write timestamps_granularities: %v", err)
		}
	}
	if req.Diarize {
		if err := write("diarize", "true"); err != nil {
			return nil, fmt.Errorf("write diarize: %v", err)
		}
	}
	if req.FileFormat != "" {
		if err := write("file_format", req.FileFormat); err != nil {
			return nil, fmt.Errorf("write file_format: %v", err)
		}
	}

	for _, format := range req.AdditionalFormats {
		if format == nil {
			continue
		}
		prefix := "additional_formats[]"
		if err := write(prefix+"[format]", format.Format); err != nil {
			return nil, fmt.Errorf("write format: %v", err)
		}
		if err := write(prefix+"[include_speakers]", strconv.FormatBool(format.IncludeSpeakers)); err != nil {
			return nil, fmt.Errorf("write include_speakers: %v", err)
		}
		if err := write(prefix+"[include_timestamps]", strconv.FormatBool(format.IncludeTimestamps)); err != nil {
			return nil, fmt.Errorf("write include_timestamps: %v", err)
		}
		if format.MaxCharactersPerLine > 0 {
			if err := write(prefix+"[max_characters_per_line]", strconv.Itoa(format.MaxCharactersPerLine)); err != nil {
				return nil, fmt.Errorf("write max_characters_per_line: %v", err)
			}
		}
		if format.MaxSegmentChars > 0 {
			if err := write(prefix+"[max_segment_chars]", strconv.Itoa(format.MaxSegmentChars)); err != nil {
				return nil, fmt.Errorf("write max_segment_chars: %v", err)
			}
		}
		if format.MaxSegmentDurationS > 0 {
			if err := write(prefix+"[max_segment_duration_s]", fmt.Sprintf("%.2f", format.MaxSegmentDurationS)); err != nil {
				return nil, fmt.Errorf("write max_segment_duration_s: %v", err)
			}
		}
		if format.SegmentOnSilenceLongerThanS > 0 {
			if err := write(prefix+"[segment_on_silence_longer_than_s]", fmt.Sprintf("%.2f", format.SegmentOnSilenceLongerThanS)); err != nil {
				return nil, fmt.Errorf("write segment_on_silence_longer_than_s: %v", err)
			}
		}
	}

	if req.File != "" {
		file, err := os.Open(req.File)
		if err != nil {
			return nil, fmt.Errorf("open file: %v", err)
		}
		defer file.Close()
		part, err := writer.CreateFormFile("file", filepath.Base(req.File))
		if err != nil {
			return nil, fmt.Errorf("create form file: %v", err)
		}
		if _, err := io.Copy(part, file); err != nil {
			return nil, fmt.Errorf("copy file: %v", err)
		}
	} else if req.CloudStorageUrl != "" {
		if err := write("url", req.CloudStorageUrl); err != nil {
			return nil, fmt.Errorf("write cloud_storage_url: %v", err)
		}
	}

	if err := writer.Close(); err != nil {
		return nil, fmt.Errorf("close writer: %v", err)
	}

	headers := map[string]string{
		"xi-api-key":   apiKey,
		"Content-Type": writer.FormDataContentType(),
	}

	httpclient := httputil.NewRetryHTTPClient(15*time.Minute, 3)
	respBody, err := httpclient.DoRequest(logCtx, http.MethodPost, URL, headers, &buf)
	if err != nil {
		return nil, fmt.Errorf("DoRequest failed: %v", err)
	}

	var resp Response
	if err := json.Unmarshal(respBody, &resp); err != nil {
		return nil, fmt.Errorf("unmarshal response: %v, body: %s", err, string(respBody))
	}

	return &resp, nil
}

var (
	Punctuation = map[string]bool{
		"。": true, "！": true, "？": true,
		".": true, "!": true, "?": true,
	}
)

func SplitResponseBySpeakerAndPause(resp *Response, gapThreshold float64, timeThreshold float64, minDuration float64,
	minGap float64, totalDuration float64) []*Speaker {
	if resp == nil || len(resp.Words) == 0 {
		return nil
	}

	speakerMap := make(map[string][]*Word)
	speakerOrder := []string{} // 顺序记录

	// Step 1: Group words by speaker and maintain order
	for _, word := range resp.Words {
		if word == nil || word.Type == "audio_event" {
			continue
		}
		if _, exists := speakerMap[word.SpeakerId]; !exists {
			speakerOrder = append(speakerOrder, word.SpeakerId)
		}
		speakerMap[word.SpeakerId] = append(speakerMap[word.SpeakerId], word)
	}

	var speakers []*Speaker

	// Step 2: Process in order of appearance
	for _, speakerID := range speakerOrder {
		words := speakerMap[speakerID]
		sentences := []*Sentence{}
		var currentWords []*Word

		var prev *Word
		var sentenceStartTime float64
		for i, word := range words {
			if len(currentWords) == 0 {
				currentWords = append(currentWords, word)
				sentenceStartTime = word.Start
				continue
			}

			isAdd := false
			if prev != nil && word.Type == "word" {
				timeGap := word.Start - prev.End

				lastDot := getLastDot(word.Text)
				isPunct := Punctuation[lastDot]                                  // 是标点
				isGap := timeGap > gapThreshold                                  // 是气口
				isTimeOverFlow := (word.End - sentenceStartTime) > timeThreshold // 是很长的一句话

				isLastPunct := false
				lastRune := ""
				if prev != nil {
					lastRune = prev.Text
				}
				if len(lastRune) > 0 {
					lastRuneDot := getLastDot(lastRune)
					isLastPunct = Punctuation[lastRuneDot]
				}

				// 是标点，且是气口或者时间超了
				if (isPunct || isLastPunct) && (isGap || isTimeOverFlow) {
					if !isLastPunct {
						currentWords = append(currentWords, word)
						isAdd = true
					}

					if prev == nil || word.Type == "word" {
						prev = currentWords[len(currentWords)-1]
					}
					sentences = append(sentences, createSentenceFromWords(currentWords))
					currentWords = []*Word{}
					isGap = false
					prev = nil
				}
			}

			if !isAdd {
				currentWords = append(currentWords, word)
				if prev == nil || word.Type == "word" {
					prev = currentWords[len(currentWords)-1]
				}
			}

			if i == len(words)-1 && len(currentWords) > 0 {
				sentences = append(sentences, createSentenceFromWords(currentWords))
			}
		}

		for i, _ := range sentences {
			// 这里要处理一下静音区，这个识别引擎多加了2s的静音拖尾
			if totalDuration > 0 && totalDuration-sentences[i].End > 2 && sentences[i].End-2 > sentences[i].LastStart {
				sentences[i].End -= 2
			}
		}

		// 句子重整
		var finalSentences []*Sentence
		lastSentenceEndTime := 0.0
		for _, item := range sentences {
			if len(finalSentences) < 1 {
				lastSentenceEndTime = item.End
				finalSentences = append(finalSentences, item)
				continue
			}

			// 如果这个句子的持续时间太短，那最好是合入到其他的句子中
			// 这种句子一般只出现在长句的末尾，可能会出现一个单词
			if item.End-item.Start > minDuration {
				lastSentenceEndTime = item.End
				finalSentences = append(finalSentences, item)
				continue
			}

			if item.Start-lastSentenceEndTime < minGap {
				// 合并到上一个句子
				idx := len(finalSentences) - 1
				lastSentence := finalSentences[idx]

				// 合并文本，更新时间
				lastSentence.Text += item.Text
				lastSentence.End = item.End
				lastSentenceEndTime = item.End
				lastSentence.LastStart = item.LastStart
				finalSentences[idx] = lastSentence
			} else {
				lastSentenceEndTime = item.End
				finalSentences = append(finalSentences, item)
			}
		}

		speakers = append(speakers, &Speaker{
			Id:        speakerID,
			Sentences: finalSentences,
			Lang:      resp.LanguageCode,
		})
	}

	return speakers
}

func getLastDot(input string) string {
	// 去除头尾空白字符（空格、制表符、换行等）
	trimmed := strings.TrimSpace(input)

	// 用 rune 处理 Unicode 字符（支持中英文、全角、emoji）
	runes := []rune(trimmed)

	// 长度为0或1，直接返回
	if len(runes) <= 1 {
		return trimmed
	}

	// 返回最后一个字符（无论是标点、符号还是文字）
	return string(runes[len(runes)-1])
}

func NormalizeSpeakers(input []*Speaker, speakNum int) []*Speaker {
	speakers := input

	// 先把讲话人按有效音频时长从大到小进行排列
	for i, _ := range speakers {
		speakers[i].Duration = 0
		speakers[i].AudioNum = len(speakers[i].Sentences)
		for s, _ := range speakers[i].Sentences {
			speakers[i].Duration += speakers[i].Sentences[s].End - speakers[i].Sentences[s].Start
		}
	}
	sort.Slice(speakers, func(i, j int) bool {
		return speakers[i].Duration > speakers[j].Duration
	})

	// 如果检测出来的讲话人比预期要少，直接返回
	if len(speakers) <= speakNum {
		return speakers
	}

	var output []*Speaker
	// 如果比预期要多，那么从长到短保留，剩下的合入第一个讲话人
	for i := 0; i < len(speakers); i++ {
		if i < speakNum {
			output = append(output, speakers[i])
		} else {
			for _, sentence := range speakers[i].Sentences {
				sentence.IsAdd = true
				speakers[0].Sentences = append(speakers[0].Sentences, sentence)
				speakers[0].Duration += sentence.End - sentence.Start
			}
		}
	}

	// 重新整理每一个讲话人的句子，按开始时间整理排序
	for spk, _ := range output {
		sort.Slice(output[spk].Sentences, func(i, j int) bool {
			return output[spk].Sentences[i].Start < output[spk].Sentences[j].Start
		})
	}

	return output
}

func createSentenceFromWords(words []*Word) *Sentence {
	if len(words) == 0 {
		return nil
	}
	textParts := make([]string, 0, len(words))
	for _, w := range words {
		textParts = append(textParts, w.Text)
	}

	setence := Sentence{
		Text:  strings.Join(textParts, ""),
		Start: words[0].Start,
		End:   words[len(words)-1].End,
	}
	// 去掉首尾的空字符
	setence.Text = strings.TrimSpace(setence.Text)

	if len(words)-2 >= 0 {
		setence.LastStart = words[len(words)-2].Start
	} else {
		setence.LastStart = words[len(words)-1].Start
	}

	lastWordIdx := len(words) - 1
	for i := lastWordIdx; i > 0; i-- {
		if words[i].Type == "word" {
			setence.End = words[i].End
			lastWordIdx = i
			break
		}
	}

	for i := lastWordIdx - 1; i > 0; i-- {
		if words[i].Type == "word" {
			setence.LastStart = words[i].Start
			break
		}
	}

	return &setence
}
