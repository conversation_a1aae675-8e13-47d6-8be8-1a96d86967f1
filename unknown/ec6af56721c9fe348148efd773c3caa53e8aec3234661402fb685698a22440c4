package concurrentlimiter

import (
	"context"
	"errors"
	"fmt"
	"time"

	"github.com/redis/go-redis/v9"
)

// ConcurrentLimiter 使用Redis实现的分布式并发限制器
type ConcurrentLimiter struct {
	rdb         *redis.Client
	mainKey     string        // 主键名称
	lockKey     string        // 操作锁名称
	limit       int64         // 最大并发数
	quotaTTL    time.Duration // 配额的存活时间
	setTTL      time.Duration // set的存活时间
	lockTimeout time.Duration // 锁的超时时间
}

// NewConcurrentLimiter 创建一个新的并发限制器
func NewConcurrentLimiter(rdb *redis.Client, mainKey string, limit int64, quotaTTL, setTTL, lockTimeout time.Duration) *ConcurrentLimiter {
	return &ConcurrentLimiter{
		rdb:         rdb,
		mainKey:     mainKey,
		lockKey:     fmt.Sprintf("%s:lock", mainKey),
		limit:       limit,
		quotaTTL:    quotaTTL,
		setTTL:      setTTL,
		lockTimeout: lockTimeout,
	}
}

// Check 检查当前配额使用情况
func (l *ConcurrentLimiter) Check(ctx context.Context) (int64, error) {
	// 获取分布式锁
	locked, err := l.acquireLock(ctx)
	if err != nil {
		return 0, fmt.Errorf("failed to acquire lock: %v", err)
	}
	if !locked {
		return 0, fmt.Errorf("failed to acquire lock: timeout")
	}
	defer l.releaseLock(ctx)

	// 清理过期的配额
	err = l.cleanup(ctx)
	if err != nil {
		return 0, fmt.Errorf("failed to cleanup: %v", err)
	}

	// 获取当前配额数量
	count, err := l.rdb.SCard(ctx, l.mainKey).Result()
	if errors.Is(err, redis.Nil) {
		return 0, nil
	}
	if err != nil {
		return 0, fmt.Errorf("failed to get count: %v", err)
	}

	return count, nil
}

// Acquire 尝试获取一个配额
func (l *ConcurrentLimiter) Acquire(ctx context.Context, timeout time.Duration) (string, error) {
	deadline := time.Now().Add(timeout)
	retryInterval := 100 * time.Millisecond

	for {
		if time.Now().After(deadline) {
			return "", fmt.Errorf("timeout waiting for quota")
		}

		if err := ctx.Err(); err != nil {
			return "", err
		}

		// 获取分布式锁
		locked, err := l.acquireLock(ctx)
		if err != nil {
			return "", fmt.Errorf("failed to acquire lock: %v", err)
		}
		if !locked {
			select {
			case <-ctx.Done():
				return "", ctx.Err()
			case <-time.After(retryInterval):
				continue
			}
		}

		// 清理过期配额
		err = l.cleanup(ctx)
		if err != nil {
			l.releaseLock(ctx)
			return "", fmt.Errorf("failed to cleanup: %v", err)
		}

		// 检查是否还有可用配额
		count, err := l.rdb.SCard(ctx, l.mainKey).Result()
		if err != nil && !errors.Is(err, redis.Nil) {
			l.releaseLock(ctx)
			return "", fmt.Errorf("failed to get count: %v", err)
		}

		if count >= l.limit {
			l.releaseLock(ctx)
			select {
			case <-ctx.Done():
				return "", ctx.Err()
			case <-time.After(retryInterval):
				continue
			}
		}

		// 生成新的配额ID
		quotaID := fmt.Sprintf("%s:%d", l.mainKey, time.Now().UnixNano())

		// 添加配额
		pipe := l.rdb.Pipeline()
		pipe.Set(ctx, quotaID, "1", l.quotaTTL)
		pipe.SAdd(ctx, l.mainKey, quotaID)
		pipe.Expire(ctx, l.mainKey, l.setTTL)

		_, err = pipe.Exec(ctx)
		l.releaseLock(ctx)

		if err != nil {
			return "", fmt.Errorf("failed to acquire quota: %v", err)
		}

		return quotaID, nil
	}
}

// Release 释放一个配额
func (l *ConcurrentLimiter) Release(ctx context.Context, quotaID string) error {
	pipe := l.rdb.Pipeline()
	pipe.Del(ctx, quotaID)
	pipe.SRem(ctx, l.mainKey, quotaID)

	_, err := pipe.Exec(ctx)
	if err != nil {
		return fmt.Errorf("failed to release quota: %v", err)
	}

	return nil
}

// cleanup 清理过期的配额
func (l *ConcurrentLimiter) cleanup(ctx context.Context) error {
	// 获取所有配额ID
	quotaIDs, err := l.rdb.SMembers(ctx, l.mainKey).Result()
	if err != nil && !errors.Is(err, redis.Nil) {
		return err
	}

	if len(quotaIDs) == 0 {
		return nil
	}

	// 检查每个配额是否存在
	pipe := l.rdb.Pipeline()
	exists := make([]*redis.IntCmd, len(quotaIDs))
	for i, id := range quotaIDs {
		exists[i] = pipe.Exists(ctx, id)
	}

	_, err = pipe.Exec(ctx)
	if err != nil {
		return err
	}

	// 删除不存在的配额
	var toRemove []string
	for i, exist := range exists {
		if exist.Val() == 0 {
			toRemove = append(toRemove, quotaIDs[i])
		}
	}

	if len(toRemove) > 0 {
		err = l.rdb.SRem(ctx, l.mainKey, toRemove).Err()
		if err != nil {
			return err
		}
	}

	return nil
}

// acquireLock 获取分布式锁
func (l *ConcurrentLimiter) acquireLock(ctx context.Context) (bool, error) {
	return l.rdb.SetNX(ctx, l.lockKey, "1", l.lockTimeout).Result()
}

// releaseLock 释放分布式锁
func (l *ConcurrentLimiter) releaseLock(ctx context.Context) {
	l.rdb.Del(ctx, l.lockKey)
}
