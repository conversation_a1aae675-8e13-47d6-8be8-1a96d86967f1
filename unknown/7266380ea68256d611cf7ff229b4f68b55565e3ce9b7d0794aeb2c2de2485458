package morpheus

import (
	"acg-ai-go-common/utils/httputil"
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"time"
)

func QueryFigure(logCtx context.Context, host, userId string, figureId int) (*FigureData, error) {
	var body []byte

	req := FigureQueryRequest{
		UserId:       userId,
		FilterSystem: false,
		FigureIds:    []int{figureId},
	}

	body, err := json.Marshal(req)
	if err != nil {
		return nil, fmt.Errorf("marshal failed: %v", err)
	}

	headers := map[string]string{
		"Content-Type": "application/json",
	}

	tarUrl := fmt.Sprintf("%s/api/internal/digitalhuman/starlight/v1/open/query", host)
	httpclient := httputil.NewRetryHTTPClient(15*time.Minute, 3)
	respBody, err := httpclient.DoRequest(logCtx, http.MethodPost, tarUrl, headers, bytes.NewReader(body))
	if err != nil {
		return nil, fmt.Errorf("http request failed: %v", err)
	}

	rsp := &FigureQueryResponse{}
	err = json.Unmarshal(respBody, &rsp)
	if err != nil {
		return nil, fmt.Errorf("unmarshal failed: %v", err)
	}

	if !rsp.Success || rsp.Page == nil || len(rsp.Page.Result) < 1 {
		return nil, fmt.Errorf("query error: %s, request: %s", string(respBody), string(body))
	}

	return rsp.Page.Result[0], nil
}
