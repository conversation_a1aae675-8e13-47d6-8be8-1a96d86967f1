package schedeuler

import (
	"acg-ai-go-common/logger"
	"acg-ai-go-common/utils"
	"acg-ai-go-common/utils/httputil"
	"bytes"
	"context"
	"digital-human-micro-video-trans/beans/enums"
	"digital-human-micro-video-trans/beans/model"
	"digital-human-micro-video-trans/beans/proto"
	"digital-human-micro-video-trans/conf"
	"digital-human-micro-video-trans/thirdparty/elevenlabs/voice_clone"
	"digital-human-micro-video-trans/thirdparty/minimax"
	"digital-human-micro-video-trans/thirdparty/sutils/fileutils"
	"encoding/json"
	"fmt"
	"net/http"
	"time"
)

const (
	// TaskScheduleLockFmt 处理任务的redis锁
	TaskScheduleLockFmt        = "%s:TaskSchedule:%s"
	TaskScheduleLockExpireTime = 1 * time.Hour

	BosRootPath = "/task/%s/%s/"
)

type CachePath struct {
	Root       string `json:"root"`       // 任务缓存的根目录
	BosRoot    string `json:"bosRoot"`    // 对象存储的根目录
	Name       string `json:"name"`       // 任务文件使用的名称，无后缀
	SimpleName string `json:"simpleName"` // 简单名称
	FileName   string `json:"fileName"`   // 文件原始名称

	Temp string `json:"temp"` // 临时目录

	InputFile        string `json:"inputFile"`        // 原始文件
	VideoSuffix      string `json:"videoSuffix"`      // 原始文件后缀
	InputVideo       string `json:"inputVideo"`       // 处理后的视频
	InputVideoSuffix string `json:"inputVideoSuffix"` // 处理后的视频后缀
	InputAudio       string `json:"inputAudio"`       // 提取出的原始音频文件
	AudioSuffix      string `json:"audioSuffix"`      // 原始音频文件后缀，.wav

	VocalAudio            string `json:"vocalAudio"`            // 提取出的人声
	VocalAudioTemp        string `json:"vocalAudioTemp"`        // 提取出的人声
	VocalAudioSuffix      string `json:"vocalAudioSuffix"`      // 提取出的人声
	BgmAudio              string `json:"bgmAudio"`              // 提取出的背景音
	BgmAudioTemp          string `json:"bgmAudioTemp"`          // 提取出的背景音
	BgmAudioSuffix        string `json:"bgmAudioSuffix"`        // 提取出的背景音
	BackEffectAudio       string `json:"backEffectAudio"`       // 提取出的其他背景音
	BackEffectAudioTemp   string `json:"backEffectAudioTemp"`   // 提取出的其他背景音
	BackEffectAudioSuffix string `json:"backEffectAudioSuffix"` // 提取出的其他背景音

	AsrResult         string   `json:"asrResult"`         // 语音识别的结果
	AsrSentenceResult string   `json:"asrSentenceResult"` // 语音识别段落区分的结果
	AsrResultSuffix   string   `json:"asrResultSuffix"`   // 语音识别的结果文件后缀
	AsrAudio          []string `json:"asrAudio"`          // 语音识别的段落文件
	AsrAudioSuffix    string   `json:"asrAudioSuffix"`    // 语音识别的段落文件后缀

	TranslateResult       string `json:"translateResult"`       // 翻译的结果
	TranslateResultSuffix string `json:"TranslateResultSuffix"` // 翻译的结果文件后缀

	TtsResult       string `json:"ttsResult"`       // TTS 结果
	TtsResultSuffix string `json:"ttsResultSuffix"` // TTS 结果文件后缀
	TtsText         string `json:"ttsText"`         // TTS 调整后的结果
	TtsTextSuffix   string `json:"ttsTextSuffix"`   // TTS 调整后的结果文件后缀
	TtsAudio        string `json:"ttsAudio"`        // TTS 音频结果
	TtsAudioSuffix  string `json:"ttsAudioSuffix"`  // TTS 音频结果后缀

	TemplateVocalVideo       string `json:"templateVocalVideo"`
	TemplateVocalVideoSuffix string `json:"templateVocalVideoSuffix"`
	TemplateVideo            string `json:"templateVideo"`
	TemplateVideoSuffix      string `json:"templateVideoSuffix"`

	BgmAudioOut              string `json:"bgmAudioOut"` // 处理后的BGM
	BgmAudioOutSuffix        string `json:"bgmAudioOutSuffix"`
	BackEffectAudioOut       string `json:"backEffectAudioOut"` // 处理后的背景音
	BackEffectAudioOutSuffix string `json:"backEffectAudioOutSuffix"`
	VideoFigureOut           string `json:"videoFigureOut"` // 人像合成的结果
	VideoFigureOutSuffix     string `json:"videoFigureOutSuffix"`
	VideoOutputNoBgm         string `json:"videoOutputNoBgm"` // 人像合成的视频转换
	VideoOutputNoBgmSuffix   string `json:"videoOutputNoBgmSuffix"`
	VideoOutput              string `json:"videoOutput"` // 合入BGM的视频
	VideoOutputSuffix        string `json:"videoOutputSuffix"`
	SubtitleOutput           string `json:"subtitleOutput"`
	SubtitleOutputSuffix     string `json:"subtitleOutputSuffix"`

	SubtitleUpload       string `json:"subtitleUpload"`
	SubtitleUploadSuffix string `json:"subtitleUploadSuffix"`

	VideoFinal       string `json:"videoFinal"`
	VideoFinalSuffix string `json:"videoFinalSuffix"`

	VideoUpload       string `json:"videoUpload"`
	VideoUploadSuffix string `json:"videoUploadSuffix"`

	Thumbnail       string `json:"thumbnail"`
	ThumbnailSuffix string `json:"thumbnailSuffix"`
}

func GetTaskCache(item *model.VideoTranslateTask) (*CachePath, error) {
	dateStr := time.Now().Format("2006-01-02")
	cache := &CachePath{
		Root:       fmt.Sprintf("%s/%s", conf.LocalConfig.CacheSettings.RootPath, item.TaskId),
		BosRoot:    fmt.Sprintf(BosRootPath, dateStr, item.TaskId),
		SimpleName: "task_item",
		FileName:   item.Name,
	}

	// 如果name是空的话，就是用TaskId代替
	if len(cache.FileName) < 1 {
		cache.FileName = item.TaskId
	}

	_, suffix, err := fileutils.GetFilenameAndExt(item.InputUrl)
	if err != nil {
		return nil, fmt.Errorf("decompose url file ext failed: %v", err)
	}
	cache.Name = fileutils.Sha256Hash(item.InputUrl)
	cache.InputFile = fmt.Sprintf("%s/%s%s", cache.Root, cache.Name, suffix)
	cache.VideoSuffix = suffix
	cache.InputVideo = fmt.Sprintf("%s/input/in_%s.mp4", cache.Root, cache.SimpleName)
	cache.InputVideoSuffix = ".mp4"
	cache.InputAudio = fmt.Sprintf("%s/input/in_%s.wav", cache.Root, cache.SimpleName)
	cache.AudioSuffix = ".wav"
	cache.VocalAudio = fmt.Sprintf("%s/input/in_%s_vocal.wav", cache.Root, cache.SimpleName)
	cache.VocalAudioTemp = fmt.Sprintf("%s/input/in_%s_vocal_temp.wav", cache.Root, cache.SimpleName)
	cache.VocalAudioSuffix = ".wav"
	cache.BgmAudio = fmt.Sprintf("%s/input/in_%s_bgm.wav", cache.Root, cache.SimpleName)
	cache.BgmAudioTemp = fmt.Sprintf("%s/input/in_%s_bgm_temp.wav", cache.Root, cache.SimpleName)
	cache.BgmAudioSuffix = ".wav"
	cache.BackEffectAudio = fmt.Sprintf("%s/input/in_%s_back.wav", cache.Root, cache.SimpleName)
	cache.BackEffectAudioTemp = fmt.Sprintf("%s/input/in_%s_back_temp.wav", cache.Root, cache.SimpleName)
	cache.BackEffectAudioSuffix = ".wav"
	err = fileutils.EnsureDir(cache.InputAudio)
	if err != nil {
		return nil, fmt.Errorf("mkdir task/input failed: %v", err)
	}

	cache.AsrResult = fmt.Sprintf("%s/asr/asr_%s_result.json", cache.Root, cache.SimpleName)
	cache.AsrSentenceResult = fmt.Sprintf("%s/asr/asr_%s_sentence.json", cache.Root, cache.SimpleName)
	cache.AsrResultSuffix = ".json"
	err = fileutils.EnsureDir(cache.AsrResult)
	if err != nil {
		return nil, fmt.Errorf("mkdir task/asr failed: %v", err)
	}

	cache.TranslateResult = fmt.Sprintf("%s/translate/trans_%s_sentence.json", cache.Root, cache.SimpleName)
	cache.TranslateResultSuffix = ".json"
	err = fileutils.EnsureDir(cache.TranslateResult)
	if err != nil {
		return nil, fmt.Errorf("mkdir task/translate failed: %v", err)
	}

	cache.TtsResult = fmt.Sprintf("%s/tts/tts_%s_sentence.json", cache.Root, cache.SimpleName)
	cache.TtsResultSuffix = ".json"
	cache.TtsAudio = fmt.Sprintf("%s/tts/tts_%s.wav", cache.Root, cache.SimpleName)
	cache.TtsAudioSuffix = ".wav"
	err = fileutils.EnsureDir(cache.TtsResult)
	if err != nil {
		return nil, fmt.Errorf("mkdir task/tts failed: %v", err)
	}

	cache.VideoFigureOut = fmt.Sprintf("%s/video/figure_%s_out.mp4", cache.Root, cache.SimpleName)
	cache.VideoFigureOutSuffix = ".webm"
	cache.TemplateVideo = fmt.Sprintf("%s/video/figure_%s_template.mp4", cache.Root, cache.SimpleName)
	cache.TemplateVideoSuffix = ".mp4"
	cache.TemplateVocalVideo = fmt.Sprintf("%s/video/figure_%s_template_vocal.mp4", cache.Root, cache.SimpleName)
	cache.TemplateVocalVideoSuffix = ".mp4"
	cache.VideoOutput = fmt.Sprintf("%s/video/video_%s_out.mp4", cache.Root, cache.SimpleName)
	cache.VideoOutputSuffix = ".mp4"
	cache.VideoOutputNoBgm = fmt.Sprintf("%s/video/video_%s_no_bgm.mp4", cache.Root, cache.SimpleName)
	cache.VideoOutputNoBgmSuffix = ".mp4"
	cache.BgmAudioOut = fmt.Sprintf("%s/video/video_%s_bgm.wav", cache.Root, cache.SimpleName)
	cache.BgmAudioOutSuffix = ".wav"
	cache.BackEffectAudioOut = fmt.Sprintf("%s/video/video_%s_effect.wav", cache.Root, cache.SimpleName)
	cache.BackEffectAudioOutSuffix = ".wav"
	cache.Thumbnail = fmt.Sprintf("%s/video/video_%s.jpg", cache.Root, cache.SimpleName)
	cache.ThumbnailSuffix = ".jpg"
	cache.TtsText = fmt.Sprintf("%s/video/video_%s_text.json", cache.Root, cache.SimpleName)
	cache.TtsTextSuffix = ".json"

	// 最终的文件名称需要处理
	cache.VideoFinal = fmt.Sprintf("%s/video/video_%s_final.mp4", cache.Root, cache.SimpleName)
	cache.VideoFinalSuffix = ".mp4"
	cache.VideoUpload = fmt.Sprintf("%s/video/video_%s.mp4", cache.Root, cache.SimpleName)
	cache.VideoUploadSuffix = ".mp4"
	cache.SubtitleOutput = fmt.Sprintf("%s/video/video_%s_final.vtt", cache.Root, cache.SimpleName)
	cache.SubtitleOutputSuffix = ".vtt"
	cache.SubtitleUpload = fmt.Sprintf("%s/video/video_%s.vtt", cache.Root, cache.SimpleName)
	cache.SubtitleUploadSuffix = ".vtt"

	if conf.LocalConfig.CacheSettings.UseTrueFileName {
		cache.VideoUpload = fmt.Sprintf("%s/video/%s.mp4", cache.Root, cache.FileName)
		cache.SubtitleUpload = fmt.Sprintf("%s/video/%s.vtt", cache.Root, cache.FileName)
	}

	err = fileutils.EnsureDir(cache.VideoFigureOut)
	if err != nil {
		return nil, fmt.Errorf("mkdir task/video failed: %v", err)
	}

	cache.Temp = fmt.Sprintf("%s/temp/", cache.Root)
	err = fileutils.EnsureDir(cache.Temp)
	if err != nil {
		return nil, fmt.Errorf("mkdir task/temp failed: %v", err)
	}

	return cache, nil
}

func (p *CachePath) GetTtsPartName(speaker, sentence int) string {
	return fmt.Sprintf("%s/tts/tts_%s_%d_%d.wav", p.Root, p.SimpleName, speaker, sentence)
}

func (p *CachePath) GetTtsFileName(speaker int) string {
	return fmt.Sprintf("%s/tts/tts_%s_spk_%d.wav", p.Root, p.SimpleName, speaker)
}

func (p *CachePath) GetAsrPartName(speaker, sentence int) string {
	return fmt.Sprintf("%s/asr/asr_%s_%d_%d.wav", p.Root, p.SimpleName, speaker, sentence)
}

func (p *CachePath) GetAsrFileName(speaker int) string {
	return fmt.Sprintf("%s/asr/asr_%s_spk_%d.wav", p.Root, p.SimpleName, speaker)
}

func (p *CachePath) GetTtsSpeakerFileName(speaker int) string {
	return fmt.Sprintf("%s/tts/clone_%s_spk_%d.wav", p.Root, p.SimpleName, speaker)
}

func ParseTaskSubStatus(status enums.TaskSubStatus) (enums.TaskStatus, error) {
	if status == enums.Isolation || status == enums.IsolationWait || status == enums.ASR || status == enums.
		ASRIntegration || status == enums.Translate {
		return enums.PreProcessing, nil
	}

	if status == enums.VoiceCloneSubmit || status == enums.VoiceCloneWait || status == enums.
		VoiceCloneTts || status == enums.VoiceCloneIntegration || status == enums.VoiceCloneSubtitle {
		return enums.VoiceClone, nil
	}

	if status == enums.VideoFigureTrainUpload || status == enums.VideoFigureTrainSubmit || status == enums.
		VideoFigureTrainWait || status == enums.VideoTransferSubmit || status == enums.VideoTransferWait {
		return enums.VideoTransfer, nil
	}

	if status == enums.PostProcessWait {
		return enums.PostProcess, nil
	}
	return enums.Submit, fmt.Errorf("valid status not found")
}

func GetFirstTaskSubStatus(status enums.TaskStatus) (enums.TaskSubStatus, error) {
	if status == enums.PreProcessing {
		return enums.Isolation, nil
	}

	if status == enums.VoiceClone {
		return enums.VoiceCloneSubmit, nil
	}

	if status == enums.VideoTransfer {
		return enums.VideoFigureTrainUpload, nil
	}

	if status == enums.PostProcess {
		return enums.PostProcessWait, nil
	}

	return enums.None, fmt.Errorf("valid status not found")
}

func CallbackToApp(logCtx context.Context, item *model.VideoTranslateTask) error {
	if item == nil {
		return fmt.Errorf("video translate item is null")
	}
	if len(item.CallbackUrl) < 1 {
		logger.Log.Infof(utils.MMark(logCtx) + "no need to callback, url is empty")
		return nil
	}

	req := proto.VideoTaskItem{
		TaskId:           item.TaskId,
		Name:             item.Name,
		InputUrl:         item.InputUrl,
		VideoUrl:         item.DownloadUrl,
		CaptionUrl:       item.CaptionUrl,
		FigureVideoUrl:   item.DownloadUrl,
		Thumbnail:        item.Thumbnail,
		Duration:         item.Duration,
		ResolutionWidth:  item.ResolutionWidth,
		ResolutionHeight: item.ResolutionHeight,
		CreatedAt:        item.CreatedAt,
		UpdatedAt:        item.UpdatedAt,
	}

	if item.Status == enums.Failed && item.Retry == 0 {
		req.Status = string(enums.Failed)
		req.Message = item.Message
	} else if item.Status == enums.Success {
		req.Status = string(enums.Success)
	} else {
		return nil
	}

	var body []byte
	body, err := json.Marshal(req)
	if err != nil {
		return fmt.Errorf("marshal failed: %v", err)
	}

	headers := map[string]string{
		"Content-Type": "application/json",
	}

	tarUrl := fmt.Sprintf("%s", item.CallbackUrl)
	httpclient := httputil.NewRetryHTTPClient(15*time.Minute, 3)
	respBody, err := httpclient.DoRequest(logCtx, http.MethodPost, tarUrl, headers, bytes.NewReader(body))
	if err != nil {
		return fmt.Errorf("http request failed: %v", err)
	}

	rsp := &proto.CommonResponse{}
	err = json.Unmarshal(respBody, &rsp)
	if err != nil {
		return fmt.Errorf("unmarshal failed: %v", err)
	}

	if !rsp.Success {
		return fmt.Errorf("callback error: %s", string(respBody))
	}

	logger.Log.Infof(utils.MMark(logCtx)+"callback success, req: %s, resp: %s", string(body), string(respBody))
	return nil
}

// DeleteCloneVoice 删除克隆的音色
func DeleteCloneVoice(logCtx context.Context, progress *proto.Progress) error {
	if progress == nil {
		return fmt.Errorf("voice progress is null")
	}

	for i, _ := range progress.Voice {
		if len(progress.Voice[i].VoiceId) > 0 {
			if progress.Voice[i].Engine == "elevenlabs" {
				err := voice_clone.DeleteVoice(logCtx, conf.LocalConfig.ElevenLabsSettings.ApiKey, progress.Voice[i].VoiceId)
				if err != nil {
					logger.Log.Errorf(utils.MMark(logCtx)+"delete elevenlabs voice: %s failed: %v", progress.Voice[i].VoiceId,
						err)
					continue
				}
			} else if progress.Voice[i].Engine == "minimax" {
				_, _, err := minimax.DeleteVoice(conf.LocalConfig.MinimaxSettings.Host,
					conf.LocalConfig.MinimaxSettings.ApiKey, "voice_cloning", progress.Voice[i].VoiceId)
				if err != nil {
					logger.Log.Errorf(utils.MMark(logCtx)+"delete elevenlabs voice: %s failed: %v", progress.Voice[i].VoiceId,
						err)
					continue
				}
			}
			progress.Voice[i].Deleted = true
			logger.Log.Infof(utils.MMark(logCtx)+"delete [%s] voice: %s", progress.Voice[i].Engine,
				progress.Voice[i].VoiceId)
		}
	}

	return nil
}
