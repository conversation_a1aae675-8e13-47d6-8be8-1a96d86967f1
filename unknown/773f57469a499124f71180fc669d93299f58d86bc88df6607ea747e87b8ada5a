package google

import (
	"acg-ai-go-common/utils"
	"context"
	"digital-human-micro-video-trans/thirdparty/google/cloud_translate"
	"fmt"
	"testing"
)

const (
	CredentialFile string = "./input/gcs_credentials.json"
)

var (
	Contents = []string{"我是一个chinese, ok？ How are you babe? Come on every body, " +
		"DJ sunny in the house. Come on every body, are you crazy in tonight", "你说啥呢你 啊"}
	InputText string = "How are you babe? Come on every body, DJ sunny in the house. Come on every body, are you crazy in tonight"
)

func TestDetectLanguage(t *testing.T) {
	logCtx := context.WithValue(context.TODO(), utils.CtxKeyLogID, utils.RandStringRunes(10))
	result, err := cloud_translate.DetectLanguage(logCtx, CredentialFile, Contents)
	if err != nil {
		fmt.Printf("DetectLanguage failed: %v\n", err)
		return
	}
	fmt.Printf("detect result: %+v\n", result)
}

func TestTranslate(t *testing.T) {
	logCtx := context.WithValue(context.TODO(), utils.CtxKeyLogID, utils.RandStringRunes(10))
	result, err := cloud_translate.Translate(logCtx, CredentialFile, "zh-CN", InputText)
	if err != nil {
		fmt.Printf("Translate failed: %v\n", err)
		return
	}
	fmt.Printf("Translate result: %+v\n", result)
}
