package schedeuler

import (
	"acg-ai-go-common/gomysql"
	"acg-ai-go-common/logger"
	"acg-ai-go-common/utils"
	"acg-ai-go-common/utils/redisproxy"
	"context"
	"digital-human-micro-video-trans/beans/enums"
	"digital-human-micro-video-trans/beans/model"
	"digital-human-micro-video-trans/conf"
	"digital-human-micro-video-trans/thirdparty/ffmpeg"
	"digital-human-micro-video-trans/thirdparty/sutils"
	"digital-human-micro-video-trans/thirdparty/sutils/fileutils"
	"digital-human-micro-video-trans/thirdparty/sutils/redislock"
	"fmt"
	"gorm.io/gorm"
	"time"
)

func (p *TaskScheduler) HandleSubmit() {
	logger.Log.Info("start handle submit task")
	taskList, err := (&model.VideoTranslateTask{}).GetTasksWithStatus(gomysql.DB, enums.Submit)
	if err != nil {
		logger.Log.Errorf("get task with status: %s, err: %v", enums.Submit, err)
		return
	}

	if len(taskList) < 1 {
		logger.Log.Infof("skip cause no %s task", enums.Submit)
		return
	}

	scheduleCount := 0
	for _, item := range taskList {
		err := p.ProcessSubmit(item)
		if err != nil {
			logger.Log.Errorf("schedule task: %s failed, cause: %v", item.TaskId, err)
		}

		scheduleCount++
		if scheduleCount >= conf.LocalConfig.ScheduleSettings.MaxScheduleSize {
			logger.Log.Infof("scheduled reach max control size: %d/%d", scheduleCount,
				conf.LocalConfig.ScheduleSettings.MaxScheduleSize)
			break
		}
	}
	logger.Log.Infof("scheduled task: %d with status: %s", scheduleCount, enums.Submit)
}

func (p *TaskScheduler) ProcessSubmit(task *model.VideoTranslateTask) error {
	logCtx := context.WithValue(context.TODO(), utils.CtxKeyLogID, task.TaskId)
	redisProxy := redisproxy.GetRedisProxy()

	// 加锁
	redisLockKey := fmt.Sprintf(TaskScheduleLockFmt, sutils.GetNameByRunEnv(), task.TaskId)
	redisLock := redislock.NewRedisLock(redisProxy.Rdb, redisLockKey, TaskScheduleLockExpireTime)
	ok, err := redisLock.Lock(context.Background())
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"ProcessSubmit Lock key: %s error: %+v\n", redisLockKey, err)
		return fmt.Errorf("get redis lock err: %v", err)
	} else if !ok {
		logger.Log.Warnf(utils.MMark(logCtx) + "redis key locked by other instance, skip it.")
		return nil
	}
	defer redisLock.Unlock(context.Background())

	// 重新查询
	item, err := task.GetTasksWithTaskId(gomysql.DB, task.TaskId)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"refresh task err: %v", err)
		return fmt.Errorf("get refresh task err: %v", err)
	}

	// 状态变了，就不用处理了
	if item.Status != enums.Submit {
		logger.Log.Warnf(utils.MMark(logCtx)+"task status nolonger %s, skip.", enums.Submit)
		return fmt.Errorf("task status nolonger %s", enums.Submit)
	}

	needRetry := 1
	tNow := time.Now()
	err = gomysql.DB.Transaction(func(tx *gorm.DB) error {
		item.StartTime = &tNow
		tStart := time.Now()

		cache, err := GetTaskCache(item)
		if err != nil {
			return fmt.Errorf("parse task cache failed: %v", err)
		}

		// 如果本地不存在，则下载文件
		if !fileutils.IsExists(cache.InputFile) {
			logger.Log.Infof(utils.MMark(logCtx)+"start to download file: %v", cache.InputFile)
			err = fileutils.RetryDownloadFile(item.InputUrl, cache.InputFile,
				conf.LocalConfig.ScheduleSettings.HttpRetryCount)
			if err != nil {
				return fmt.Errorf("download video failed: %v", err)
			}
			logger.Log.Infof(utils.MMark(logCtx)+"download to local done, file: %v", cache.InputFile)
		}

		// 检查视频参数
		videoInfo, err := ffmpeg.GetVideoInfo(cache.InputFile)
		if err != nil {
			return fmt.Errorf("get video info failed: %v", err)
		}
		logger.Log.Infof(utils.MMark(logCtx)+"get video info: %+v", videoInfo)

		item.ResolutionWidth = videoInfo.Width
		item.ResolutionHeight = videoInfo.Height
		item.Duration = int(videoInfo.Duration)

		// 检查文件格式
		_, retry, err := checkInputVideo(logCtx, cache.InputFile)
		if err != nil {
			needRetry = retry
			return fmt.Errorf("check failed: %v", err)
		}

		// 转码
		err = ffmpeg.ConvertToMP4(logCtx, 25, cache.InputFile, cache.InputVideo)
		if err != nil {
			return fmt.Errorf("convert video failed: %v", err)
		}

		// 分离音频
		err = ffmpeg.ExtractAudio(logCtx, cache.InputFile, cache.InputAudio, "pcm_s16le", 1, 16000)
		if err != nil {
			return fmt.Errorf("ffmpeg split audio failed: %v", err)
		}

		timeCost := float64(time.Since(tStart).Milliseconds()) / 1000.0
		logger.Log.Infof(utils.MMark(logCtx)+"elapsed with: %.3fs", timeCost)

		// 更新状态
		item.Status = enums.PreProcessing
		err = item.Update(tx)
		if err != nil {
			return fmt.Errorf("update to: %s failed: %v", item.Status, err)
		}
		return nil
	})

	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"schedule failed, err: %+v\n", err)

		// 更新任务状态
		if needRetry == 0 {
			// 把次数置为0
			item.RetryAttempts = 0
		}
		item.Status = enums.Failed
		item.Message = fmt.Sprintf("%s", err.Error())

		err := item.Update(gomysql.DB)
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+"update item failed: %+v, err: %+v\n", item, err)
			return err
		}

		return fmt.Errorf("schedule task err: %v", err)
	}
	return nil
}

func checkInputVideo(logCtx context.Context, inputFile string) (bool, int, error) {
	// 检查视频流编码器支持
	byFfmpeg, codec, err := ffmpeg.IsPlayableByFfmpeg(logCtx, inputFile)
	if err != nil {
		return false, 1, fmt.Errorf("ffprobe check vcodec failed: %v", err)
	}
	logger.Log.Infof(utils.MMark(logCtx)+"get video codec: %+v, support: %d", codec, byFfmpeg)
	if !byFfmpeg {
		return false, 0, fmt.Errorf("codec %s not supported by ffmpeg", codec)
	}

	// 检查是否有音视频流
	video, audio, err := ffmpeg.HasValidAudioAndVideo(logCtx, inputFile)
	if err != nil {
		return false, 1, fmt.Errorf("ffpobe check v/a stream failed: %v", err)
	}
	logger.Log.Infof(utils.MMark(logCtx)+"get stream, video: %d, audio: %d", video, audio)
	if !video || !audio {
		return false, 0, fmt.Errorf("video[%v] or audio[%v] not valid", video, audio)
	}

	// 检查是否有有效音频
	validAudio, err := ffmpeg.HasValidAudio(logCtx, inputFile)
	if err != nil {
		return false, 1, fmt.Errorf("ffprobe check valid audio failed: %v", err)
	}
	logger.Log.Infof(utils.MMark(logCtx)+"get valid audio: %d", validAudio)
	if !validAudio {
		return false, 0, fmt.Errorf("no valid audio content")
	}

	return true, 1, nil
}
