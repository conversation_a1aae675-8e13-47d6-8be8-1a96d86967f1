package videopipeline

import (
	"acg-ai-go-common/utils/httputil"
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"time"
)

func QueryTask(logCtx context.Context, host, videoId string) (*ProgressResult,
	error) {
	var body []byte
	headers := map[string]string{
		"Content-Type": "application/json",
	}

	tarUrl := fmt.Sprintf("%s/api/digitalhuman/video/v1/progress/%s", host, videoId)
	httpclient := httputil.NewRetryHTTPClient(15*time.Minute, 3)
	respBody, err := httpclient.DoRequest(logCtx, http.MethodGet, tarUrl, headers, bytes.NewReader(body))
	if err != nil {
		return nil, fmt.Errorf("http request failed: %v", err)
	}

	rsp := &TaskQueryResponse{}
	err = json.Unmarshal(respBody, &rsp)
	if err != nil {
		return nil, fmt.Errorf("unmarshal failed: %v", err)
	}

	if !rsp.Success || rsp.Result == nil {
		return nil, fmt.Errorf("query error: %s", string(respBody))
	}

	return rsp.Result, nil
}
