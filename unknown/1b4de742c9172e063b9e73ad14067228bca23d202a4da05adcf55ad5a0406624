package gemini

import "context"

type Client struct {
	Credential string
	ProjectID  string
	Location   string
	ModelType  string

	SessionID string
	System    string
	Context   []Message
}

func NewClient(id string, systemInfo string, credential, projectId, location, modelType string) *Client {
	client := Client{
		SessionID: id,
		System:    systemInfo,

		Credential: credential,
		ProjectID:  projectId,
		Location:   location,
		ModelType:  modelType,
	}

	client.Context = append(client.Context, Message{
		Role:    RoleUser,
		Content: client.System,
	})

	return &client
}

func (p *Client) Chat(logCtx context.Context, content string) (string, error) {
	p.Context = append(p.Context, Message{
		Role:    RoleUser,
		Content: content,
	})
	result, err := GenerateGeminiChatContent(logCtx, p.Credential, p.ProjectID, p.Location, p.ModelType, p.Context)
	if err != nil {
		return "", err
	}

	p.Context = append(p.Context, Message{
		Role:    RoleModel,
		Content: result,
	})

	return result, nil
}
