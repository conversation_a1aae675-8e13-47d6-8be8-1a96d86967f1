package subtitle

import (
	"digital-human-micro-video-trans/thirdparty/elevenlabs/speech2text"
	"fmt"
	"strings"
)

var (
	Punctuation = map[string]bool{
		"。": true, "！": true, "？": true, "，": true,
		".": true, "!": true, "?": true, ",": true,
		"、": true, "：": true, ":": true, ";": true, "；": true, " ": true,
	}
)

type SubtitleLine struct {
	Start float64
	End   float64
	Text  string
}

type Token struct {
	Text  string
	Start float64
	End   float64
}

func tokenizeWithAlignment(chars []string, starts, ends []float64) []Token {
	var tokens []Token
	for i, ch := range chars {
		if ch != "" && ch != "\n" {
			tokens = append(tokens, Token{
				Text:  ch,
				Start: starts[i],
				End:   ends[i],
			})
		}
	}
	return tokens
}

func formatTime(t float64, isVTT bool) string {
	h := int(t) / 3600
	m := (int(t) % 3600) / 60
	s := int(t) % 60
	ms := int((t - float64(int(t))) * 1000)
	if isVTT {
		return fmt.Sprintf("%02d:%02d:%02d.%03d", h, m, s, ms)
	}
	return fmt.Sprintf("%02d:%02d:%02d,%03d", h, m, s, ms)
}

func SplitByAlignment(sentence *speech2text.Sentence, maxChars int, maxDuration float64, useTranslation bool, usePunctuation bool) []SubtitleLine {
	var lines []SubtitleLine

	tokens := tokenizeWithAlignment(sentence.Alignment.Characters, sentence.Alignment.CharacterStartTimesSeconds, sentence.Alignment.CharacterEndTimesSeconds)

	var (
		buffer    strings.Builder
		startTime float64
		endTime   float64
		charCount int
	)

	flush := func() {
		if buffer.Len() > 0 {
			lines = append(lines, SubtitleLine{
				Start: sentence.Start + startTime,
				End:   sentence.Start + endTime,
				Text:  buffer.String(),
			})
			buffer.Reset()
			charCount = 0
		}
	}

	for i, token := range tokens {
		if buffer.Len() == 0 {
			startTime = token.Start
		}
		buffer.WriteString(token.Text)
		charCount++
		endTime = token.End

		isLast := i == len(tokens)-1
		isPunct := Punctuation[token.Text]
		duration := endTime - startTime

		if usePunctuation {
			if isLast || (isPunct && (charCount >= maxChars || duration >= maxDuration)) {
				flush()
			}
		} else {
			if isLast || charCount >= maxChars || duration >= maxDuration {
				flush()
			}
		}
	}
	return lines
}

func wrapText(text string, limit int, maxLines int) string {
	runes := []rune(text)
	hasSpaces := strings.Contains(text, " ")
	var lines []string

	// 根据是否含空格决定按词还是按字符分割
	if hasSpaces {
		words := strings.Fields(text)
		var currentLine strings.Builder

		for _, word := range words {
			if currentLine.Len()+len(word)+1 <= limit {
				if currentLine.Len() > 0 {
					currentLine.WriteString(" ")
				}
				currentLine.WriteString(word)
			} else {
				line := strings.TrimSpace(currentLine.String())
				if line != "" && line != "\n" {
					lines = append(lines, line)
				}
				currentLine.Reset()
				currentLine.WriteString(word)
			}
		}
		if currentLine.Len() > 0 {
			line := strings.TrimSpace(currentLine.String())
			if line != "" && line != "\n" {
				lines = append(lines, line)
			}
		}
	} else {
		for i := 0; i < len(runes); i += limit {
			end := i + limit
			if end > len(runes) {
				end = len(runes)
			}
			line := strings.TrimSpace(string(runes[i:end]))
			if line != "" && line != "\n" {
				lines = append(lines, line)
			}
		}
	}

	// 截断最大行数限制
	if maxLines > 0 && len(lines) > maxLines {
		remaining := strings.Join(lines[maxLines-1:], hasSpacesOrEmpty(hasSpaces))
		lines = append(lines[:maxLines-1], strings.TrimSpace(remaining))
	}

	return strings.Join(lines, "\n")
}

// 辅助函数：决定拼接时是否加空格
func hasSpacesOrEmpty(hasSpaces bool) string {
	if hasSpaces {
		return " "
	}
	return ""
}

func GenerateSubtitleFromSpeakers(
	speakers []*speech2text.Speaker,
	useTranslation bool,
	isVTT bool,
	maxChars int,
	maxDuration float64,
	usePunctuation bool,
	lineWidthLimit int,
	lineLimit int,
) (string, error) {
	var builder strings.Builder
	index := 1

	if isVTT {
		builder.WriteString("WEBVTT\n\n")
	}

	for _, speaker := range speakers {
		for _, sentence := range speaker.Sentences {
			lines := SplitByAlignment(sentence, maxChars, maxDuration, useTranslation, usePunctuation)

			for _, line := range lines {
				start := formatTime(line.Start, isVTT)
				end := formatTime(line.End, isVTT)
				if !isVTT {
					builder.WriteString(fmt.Sprintf("%d\n", index))
				}
				builder.WriteString(fmt.Sprintf("%s --> %s\n", start, end))
				text := wrapText(line.Text, lineWidthLimit, lineLimit)
				builder.WriteString(text + "\n\n")
				index++
			}
		}
	}
	return builder.String(), nil
}
