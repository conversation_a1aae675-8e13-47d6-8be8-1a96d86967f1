package elevenlabs_test

import (
	"context"
	"digital-human-micro-video-trans/thirdparty/dh123"
	"fmt"
	"testing"
)

const (
	Dh123Url      string = "https://persona.baidu.com:8850"
	InputVideoUrl string = "https://saas-water-mark-output.bj.bcebos." +
		"com/f881885c-1ee4-49f5-be7a-76927545f765/e5741ab2-d5db-4ed8-9b0a-de85b4480e2b/single_dot.mp4"
	InputAudioUrl string = "https://xiling-dh.cdn.bcebos.com/video-translate/vte-cZ3ynPGywMJVczD37ba7dYrA/a58181bc-3da8-4224-a3ea-627f3a376f93.wav"

	AppId  string = "i-qicsmyfe2vfkp"
	ApiKey string = "10qydimkanjfaa0w6edu"
)

func TestSubmit(t *testing.T) {
	logCtx := context.Background()

	task, err := dh123.SubmitTask(logCtx, Dh123Url, AppId, <PERSON><PERSON><PERSON><PERSON>, InputVideoUrl, InputAudioUrl, "")
	if err != nil {
		fmt.Printf("submit failed: %v\n", err)
		return
	}
	fmt.Printf("submit task: %v\n", task)
}

func TestCheck(t *testing.T) {
	logCtx := context.Background()

	taskId := "vf3-rfjsufizxtkwt1ds"
	task, err := dh123.CheckTask(logCtx, Dh123Url, AppId, ApiKey, taskId)
	if err != nil {
		fmt.Printf("CheckTask failed: %v\n", err)
		return
	}
	fmt.Printf("CheckTask task: %+v\n", task.Result)
}

func TestUpload(t *testing.T) {
	logCtx := context.Background()

	filePath := "/Users/<USER>/Downloads/video-translate/cache/task/vte-F8yXdk8jQfrS5qvwcwSkRfJu/item.mp4"
	provideType := "OPEN_CUSTOMIZATION_2D_GENERAL"
	task, err := dh123.UploadFile(logCtx, Dh123Url, AppId, ApiKey, filePath, provideType)
	if err != nil {
		fmt.Printf("UploadFile failed: %v\n", err)
		return
	}
	fmt.Printf("UploadFile task: %+v\n", task.Result)
}

func TestTrainCheck(t *testing.T) {
	logCtx := context.Background()

	task, err := dh123.TrainTaskCheck(logCtx, Dh123Url, AppId, ApiKey, "241068")
	if err != nil {
		fmt.Printf("TrainTaskCheck failed: %v\n", err)
		return
	}
	fmt.Printf("TrainTaskCheck task: %+v\n", task.Result)
}

func TestVideoCheck(t *testing.T) {
	logCtx := context.Background()

	task, err := dh123.FigureVideoTaskCheck(logCtx, Dh123Url, AppId, ApiKey, "vid-rexw2v759p9ik4i5")
	if err != nil {
		fmt.Printf("FigureVideoTaskCheck failed: %v\n", err)
		return
	}
	fmt.Printf("FigureVideoTaskCheck task: %+v\n", task.Result)
}
