package schedeuler

import (
	"acg-ai-go-common/gomysql"
	"acg-ai-go-common/logger"
	"acg-ai-go-common/storage"
	"acg-ai-go-common/utils"
	"acg-ai-go-common/utils/redisproxy"
	"context"
	"digital-human-micro-video-trans/beans/enums"
	"digital-human-micro-video-trans/beans/model"
	"digital-human-micro-video-trans/beans/proto"
	"digital-human-micro-video-trans/conf"
	"digital-human-micro-video-trans/thirdparty/ffmpeg"
	"digital-human-micro-video-trans/thirdparty/sutils"
	"digital-human-micro-video-trans/thirdparty/sutils/fileutils"
	"digital-human-micro-video-trans/thirdparty/sutils/jsonutils"
	"digital-human-micro-video-trans/thirdparty/sutils/redislock"
	"fmt"
	"gorm.io/gorm"
	"time"
)

func (p *TaskScheduler) HandlePostProcess() {
	logger.Log.Info("start handle post process task")

	taskList, err := (&model.VideoTranslateTask{}).GetTasksWithStatus(gomysql.DB, enums.PostProcess)
	if err != nil {
		logger.Log.Errorf("get task with status: %s, err: %v", enums.PostProcess, err)
		return
	}

	if len(taskList) < 1 {
		logger.Log.Infof("no task with status: %s need schedule", enums.PostProcess)
		return
	}

	scheduleCount := 0
	for _, item := range taskList {
		err := p.postProcess(item)
		if err != nil {
			logger.Log.Errorf("schedule task: %s failed, cause: %v", item.TaskId, err)
		}
		scheduleCount++
		if scheduleCount >= conf.LocalConfig.ScheduleSettings.MaxRunningSize {
			logger.Log.Infof("scheduled reach max control size: %d/%d", scheduleCount,
				conf.LocalConfig.ScheduleSettings.MaxRunningSize)
			break
		}
	}
	logger.Log.Infof("scheduled task: %d with status: %s", scheduleCount, enums.PostProcess)
}

func (p *TaskScheduler) postProcess(task *model.VideoTranslateTask) error {
	logCtx := context.WithValue(context.TODO(), utils.CtxKeyLogID, task.TaskId)
	redisProxy := redisproxy.GetRedisProxy()

	// 加锁
	redisLockKey := fmt.Sprintf(TaskScheduleLockFmt, sutils.GetNameByRunEnv(), task.TaskId)
	redisLock := redislock.NewRedisLock(redisProxy.Rdb, redisLockKey, TaskScheduleLockExpireTime)
	ok, err := redisLock.Lock(context.Background())
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"PreProcessing Lock key: %s error: %+v\n", redisLockKey, err)
		return fmt.Errorf("get redis lock err: %v", err)
	} else if !ok {
		logger.Log.Warnf(utils.MMark(logCtx) + "redis key locked by other instance, skip it.")
		return nil
	}
	defer redisLock.Unlock(context.Background())

	// 重新查询
	item, err := task.GetTasksWithTaskId(gomysql.DB, task.TaskId)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"refresh task err: %v", err)
		return fmt.Errorf("get refresh task err: %v", err)
	}

	// 状态变了，就不用处理了
	if item.Status != enums.PostProcess {
		return fmt.Errorf("task status nolonger %s", enums.PostProcess)
	}

	progress := proto.NewProgress()
	var temp proto.Progress
	if err := jsonutils.JsonMapToStruct(item.Progress, &temp); err == nil {
		progress = &temp
	}

	var config proto.SubmitConfig
	if err := jsonutils.JsonMapToStruct(item.Config, &config); err != nil {
		return fmt.Errorf("parse submit config failed: %v", err)
	}

	//start := time.Now()
	needRetry := 1
	err = gomysql.DB.Transaction(func(tx *gorm.DB) error {
		cache, err := GetTaskCache(item)
		if err != nil {
			return fmt.Errorf("parse task cache failed: %v", err)
		}

		if len(item.SubStatus) < 1 {
			item.SubStatus = enums.PostProcessWait
		}

		tStart := time.Now()
		var subErr error
		switch item.SubStatus {
		case enums.PostProcessWait:
			{
				// 做后处理
				subErr = p.doPostProcess(logCtx, item, &config, progress, cache)
				break
			}
		}
		timeCost := float64(time.Since(tStart).Milliseconds()) / 1000.0
		logger.Log.Infof(utils.MMark(logCtx)+"elapsed with: %.3fs", timeCost)
		if subErr != nil {
			return subErr
		}

		// 保存状态
		jsConfig, err := jsonutils.StructToJsonMap(progress)
		if err != nil {
			return fmt.Errorf("save progress detal failed: %v", err)
		}
		item.Progress = jsConfig

		// 更新
		err = item.Update(tx)
		if err != nil {
			return fmt.Errorf("update to: %s failed: %v", item.Status, err)
		}

		return nil
	})

	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"schedule failed, err: %+v\n", err)

		// 更新任务状态
		item.Retry = needRetry
		item.Status = enums.Failed
		item.Message = fmt.Sprintf("%s", err.Error())

		// 也需要保存状态，有些部分不需要重复重试
		jsConfig, err := jsonutils.StructToJsonMap(progress)
		if err != nil {
			return fmt.Errorf("save progress detail failed: %v", err)
		}
		item.Progress = jsConfig

		err = item.Update(gomysql.DB)
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+"update item failed: %+v, err: %+v\n", item, err)
			return err
		}

		return fmt.Errorf("schedule task err: %v", err)
	}
	return nil
}

func (p *TaskScheduler) doPostProcess(logCtx context.Context, item *model.VideoTranslateTask,
	config *proto.SubmitConfig, progress *proto.Progress, cache *CachePath) error {
	logger.Log.Infof(utils.MMark(logCtx) + "doPostProcess")

	if config.TranslateAudioOnly || progress.VideoTransfer.Figure.Skip == 1 {
		// 只需要翻译音频的情况下，把处理好的底板视频和TTS音频合并即可
		templateVideo := cache.TemplateVideo
		if !fileutils.IsExists(templateVideo) {
			templateVideo = cache.InputVideo
		}

		// 合并TTS
		err := ffmpeg.MergeAudioVideo(logCtx, templateVideo, cache.TtsAudio, cache.VideoFigureOut, "aac", 1, 16000)
		if err != nil {
			return fmt.Errorf("merge TTS to video failed: %v", err)
		}
	} else {
		// 下载到本地来
		err := fileutils.RetryDownloadFile(progress.VideoTransfer.Video.VideoUrl, cache.VideoFigureOut,
			conf.LocalConfig.ScheduleSettings.HttpRetryCount)
		if err != nil {
			return fmt.Errorf("download video failed: %v", err)
		}
	}

	// 把输出转化为 mp4
	err := ffmpeg.ConvertToMP4(logCtx, 25, cache.VideoFigureOut, cache.VideoOutputNoBgm)
	if err != nil {
		return fmt.Errorf("convert video failed: %v", err)
	}

	// 合并BGM
	if !config.RemoveBackgroundAudio {
		mergeAudioFiles := []string{cache.BgmAudioOut, cache.BackEffectAudioOut}
		err := ffmpeg.MixAudiosIntoVideo(logCtx, cache.VideoOutputNoBgm, mergeAudioFiles,
			cache.VideoOutput,
			"aac", 1,
			16000, conf.LocalConfig.VoiceSettings.VocalVolume, conf.LocalConfig.VoiceSettings.BackVolume)
		if err != nil {
			return fmt.Errorf("mix bgm to video failed: %v", err)
		}

		logger.Log.Infof(utils.MMark(logCtx)+"donot remove bgm, merge bgm&effect to: %s", cache.VideoOutput)
	} else {
		mergeAudioFiles := []string{cache.BackEffectAudioOut}
		err := ffmpeg.MixAudiosIntoVideo(logCtx, cache.VideoOutputNoBgm, mergeAudioFiles,
			cache.VideoOutput,
			"aac", 1,
			16000, conf.LocalConfig.VoiceSettings.VocalVolume, conf.LocalConfig.VoiceSettings.BackVolume)
		if err != nil {
			return fmt.Errorf("mix effect to video failed: %v", err)
		}

		logger.Log.Infof(utils.MMark(logCtx)+"no bgm, merge effect to: %s", cache.VideoOutput)
	}

	// 上传
	fileUrl, err := storage.RetryUploadFromFile(logCtx, cache.BosRoot, cache.VideoOutput, cache.VideoOutputSuffix)
	if err != nil || len(fileUrl) < 1 {
		return fmt.Errorf("upload video failed: %v", err)
	}
	logger.Log.Infof(utils.MMark(logCtx)+"upload video without subtitle: %s", fileUrl)
	item.VideoUrl = fileUrl

	// 如果需要字幕压制
	if config.EnableCaptions {
		err := ffmpeg.MergeSubtitle(logCtx, config.TargetLangs[0], cache.VideoOutput, cache.SubtitleOutput, cache.VideoFinal)
		if err != nil {
			return fmt.Errorf("merge subtitle to video failed: %v", err)
		}
		logger.Log.Infof(utils.MMark(logCtx)+"need subtitle, merge it to: %s", cache.VideoFinal)
	} else {
		err := fileutils.CopyFile(cache.VideoOutput, cache.VideoFinal)
		if err != nil {
			return fmt.Errorf("copy video to final failed: %v", err)
		}
		logger.Log.Infof(utils.MMark(logCtx)+"no need subtitle, use it: %s", cache.VideoFinal)
	}

	// 拷贝真实文件名
	err = fileutils.CopyFile(cache.VideoFinal, cache.VideoUpload)
	if err != nil {
		return fmt.Errorf("copy video to upload failed: %v", err)
	}
	logger.Log.Infof(utils.MMark(logCtx)+"prepare file: %s", cache.VideoUpload)

	// 上传
	finalUrl, err := storage.RetryUploadFromFileWithCDNAndContentType(logCtx, cache.BosRoot, cache.VideoUpload,
		cache.VideoFinalSuffix, false, storage.ContentTypeStream)
	if err != nil || len(fileUrl) < 1 {
		return fmt.Errorf("upload video failed: %v", err)
	}
	logger.Log.Infof(utils.MMark(logCtx)+"upload final video: %s", finalUrl)
	item.DownloadUrl = finalUrl

	// 生成预览图
	err = ffmpeg.MakeThumbnail(logCtx, cache.VideoFinal, cache.Thumbnail)
	if err != nil {
		return fmt.Errorf("make thumbnail failed: %v", err)
	}

	// 上传
	thumbnailUrl, err := storage.RetryUploadFromFile(logCtx, cache.BosRoot, cache.Thumbnail, cache.ThumbnailSuffix)
	if err != nil || len(fileUrl) < 1 {
		return fmt.Errorf("upload thumbnail failed: %v", err)
	}
	logger.Log.Infof(utils.MMark(logCtx)+"upload thumbnail: %s", finalUrl)
	item.Thumbnail = thumbnailUrl

	// 有必要的话回调到业务层
	err = CallbackToApp(logCtx, item)
	if err != nil {
		logger.Log.Warnf(utils.MMark(logCtx)+"callback failed: %v", err)
	}

	// 删除刚才克隆的音色
	err = DeleteCloneVoice(logCtx, progress)
	if err != nil {
		logger.Log.Warnf(utils.MMark(logCtx)+"delete clone voice failed: %v", err)
	}
	item.VoiceDeleted = 1

	logger.Log.Infof(utils.MMark(logCtx) + "task finish.")

	item.Status = enums.Success
	item.SubStatus = enums.FINISH
	item.Message = "success"

	tNow := time.Now()
	item.EndTime = &tNow
	if item.StartTime != nil && item.EndTime != nil {
		item.CostTime = int(item.EndTime.Sub(*item.StartTime).Seconds())
	}
	return nil
}
