package schedeuler

import (
	"acg-ai-go-common/gomysql"
	"acg-ai-go-common/logger"
	"acg-ai-go-common/storage"
	"acg-ai-go-common/utils"
	"acg-ai-go-common/utils/redisproxy"
	"context"
	"digital-human-micro-video-trans/beans/enums"
	"digital-human-micro-video-trans/beans/model"
	"digital-human-micro-video-trans/beans/proto"
	"digital-human-micro-video-trans/conf"
	"digital-human-micro-video-trans/thirdparty/morpheus"
	"digital-human-micro-video-trans/thirdparty/sutils"
	"digital-human-micro-video-trans/thirdparty/sutils/fileutils"
	"digital-human-micro-video-trans/thirdparty/sutils/jsonutils"
	"digital-human-micro-video-trans/thirdparty/sutils/redislock"
	"digital-human-micro-video-trans/thirdparty/videopipeline"
	"encoding/json"
	"fmt"
	"gorm.io/gorm"
	"strings"
	"time"
)

func (p *TaskScheduler) HandleVideoTransfer() {
	logger.Log.Info("start handle video transfer task")

	taskList, err := (&model.VideoTranslateTask{}).GetTasksWithStatus(gomysql.DB, enums.VideoTransfer)
	if err != nil {
		logger.Log.Errorf("get task with status: %s, err: %v", enums.VideoTransfer, err)
		return
	}

	if len(taskList) < 1 {
		logger.Log.Infof("no task with status: %s need schedule", enums.VideoTransfer)
		return
	}

	scheduleCount := 0
	for _, item := range taskList {
		err := p.videoTransfer(item)
		if err != nil {
			logger.Log.Errorf("schedule task: %s failed, cause: %v", item.TaskId, err)
		}
		scheduleCount++
		if scheduleCount >= conf.LocalConfig.ScheduleSettings.MaxScheduleSize {
			logger.Log.Infof("scheduled reach max control size: %d/%d", scheduleCount,
				conf.LocalConfig.ScheduleSettings.MaxScheduleSize)
			break
		}
	}
	logger.Log.Infof("scheduled task: %d with status: %s", scheduleCount, enums.VideoTransfer)
}

func (p *TaskScheduler) videoTransfer(task *model.VideoTranslateTask) error {
	logCtx := context.WithValue(context.TODO(), utils.CtxKeyLogID, task.TaskId)
	redisProxy := redisproxy.GetRedisProxy()

	// 加锁
	redisLockKey := fmt.Sprintf(TaskScheduleLockFmt, sutils.GetNameByRunEnv(), task.TaskId)
	redisLock := redislock.NewRedisLock(redisProxy.Rdb, redisLockKey, TaskScheduleLockExpireTime)
	ok, err := redisLock.Lock(context.Background())
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"PreProcessing Lock key: %s error: %+v\n", redisLockKey, err)
		return fmt.Errorf("get redis lock err: %v", err)
	} else if !ok {
		logger.Log.Warnf(utils.MMark(logCtx) + "redis key locked by other instance, skip it.")
		return nil
	}
	defer redisLock.Unlock(context.Background())

	// 重新查询
	item, err := task.GetTasksWithTaskId(gomysql.DB, task.TaskId)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"refresh task err: %v", err)
		return fmt.Errorf("get refresh task err: %v", err)
	}

	// 状态变了，就不用处理了
	if item.Status != enums.VideoTransfer {
		return fmt.Errorf("task status nolonger %s", enums.VideoTransfer)
	}

	needRetry := 1
	progress := proto.NewProgress()
	var temp proto.Progress
	if err := jsonutils.JsonMapToStruct(item.Progress, &temp); err == nil {
		progress = &temp
	}

	var config proto.SubmitConfig
	if err := jsonutils.JsonMapToStruct(item.Config, &config); err != nil {
		return fmt.Errorf("parse submit config failed: %v", err)
	}

	//start := time.Now()
	err = gomysql.DB.Transaction(func(tx *gorm.DB) error {
		cache, err := GetTaskCache(item)
		if err != nil {
			return fmt.Errorf("parse task cache failed: %v", err)
		}

		if len(item.SubStatus) < 1 {
			item.SubStatus = enums.VideoFigureTrainUpload
		}

		tStart := time.Now()
		var subErr error
		switch item.SubStatus {
		case enums.VideoFigureTrainUpload:
			{
				// 上传人像底板视频
				needRetry, subErr = p.doFigureTrainUpload(logCtx, item, &config, progress, cache)
				break
			}
		case enums.VideoFigureTrainSubmit:
			{
				// 提交人像训练任务
				needRetry, subErr = p.doFigureTrainSubmit(logCtx, tx, item, &config, progress, cache)
				break
			}
		case enums.VideoFigureTrainWait:
			{
				// 查询人像训练任务
				needRetry, subErr = p.doFigureTrainCheck(logCtx, item, &config, progress, cache)
				break
			}
		case enums.VideoTransferSubmit:
			{
				// 提交视频合成任务
				needRetry, subErr = p.doFigureVideoSubmit(logCtx, tx, item, &config, progress, cache)
				break
			}
		case enums.VideoTransferWait:
			{
				// 检查视频合成任务
				needRetry, subErr = p.doFigureVideoCheck(logCtx, item, &config, progress, cache)
				break
			}
		}
		timeCost := float64(time.Since(tStart).Milliseconds()) / 1000.0
		logger.Log.Infof(utils.MMark(logCtx)+"elapsed with: %.3fs", timeCost)
		if subErr != nil {
			return subErr
		}

		// 保存状态
		jsConfig, err := jsonutils.StructToJsonMap(progress)
		if err != nil {
			return fmt.Errorf("save progress detal failed: %v", err)
		}
		item.Progress = jsConfig

		// 更新
		err = item.Update(tx)
		if err != nil {
			return fmt.Errorf("update to: %s failed: %v", item.Status, err)
		}

		return nil
	})

	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"schedule failed, err: %+v\n", err)

		// 更新任务状态
		if needRetry == 0 {
			// 把次数置为0
			item.RetryAttempts = 0
		}
		item.Status = enums.Failed
		item.Message = fmt.Sprintf("%s", err.Error())

		// 也需要保存状态，有些部分不需要重复重试
		jsConfig, err := jsonutils.StructToJsonMap(progress)
		if err != nil {
			return fmt.Errorf("save progress detail failed: %v", err)
		}
		item.Progress = jsConfig

		err = item.Update(gomysql.DB)
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+"update item failed: %+v, err: %+v\n", item, err)
			return err
		}

		return fmt.Errorf("schedule task err: %v", err)
	}
	return nil
}

func (p *TaskScheduler) doFigureTrainUpload(logCtx context.Context, item *model.VideoTranslateTask,
	config *proto.SubmitConfig, progress *proto.Progress, cache *CachePath) (int, error) {
	logger.Log.Infof(utils.MMark(logCtx) + "doFigureTrainUpload")
	progress.VideoTransfer.CreateAt = time.Now()
	progress.VideoTransfer.UpdateAt = time.Now()

	progress.VideoTransfer.Figure.CreateAt = time.Now()
	// 处理视频
	templateVideo := cache.TemplateVideo
	if !fileutils.IsExists(templateVideo) {
		templateVideo = cache.InputVideo
	}

	// 上传到Bos
	fileUrl, err := storage.RetryUploadFromFile(logCtx, cache.BosRoot, templateVideo, cache.TemplateVideoSuffix)
	if err != nil {
		return 1, fmt.Errorf("upload figure failed: %v", err)
	}

	logger.Log.Infof(utils.MMark(logCtx)+"upload figure template video: %s", fileUrl)

	progress.VideoTransfer.Figure.TemplateUrl = fileUrl
	progress.VideoTransfer.Figure.Status = enums.ProgressSubmit

	progress.VideoTransfer.UpdateAt = time.Now()
	progress.VideoTransfer.Figure.CreateAt = time.Now()

	item.SubStatus = enums.VideoFigureTrainSubmit
	return 1, nil
}

func (p *TaskScheduler) doFigureTrainSubmit(logCtx context.Context, tx *gorm.DB, item *model.VideoTranslateTask,
	config *proto.SubmitConfig, progress *proto.Progress, cache *CachePath) (int, error) {
	logger.Log.Infof(utils.MMark(logCtx) + "doFigureTrainSubmit")

	progress.VideoTransfer.Figure.FigureName = fmt.Sprintf("vte3-%s", utils.RandStringRunes(16))
	progress.VideoTransfer.Figure.Name = fmt.Sprintf("%s-%s", item.TaskId, utils.RandStringRunes(4))
	if len(conf.LocalConfig.MorpheusSettings.CallbackHost) > 0 {
		progress.VideoTransfer.Figure.CallbackUrl = fmt.Sprintf(
			"%s/api/digitalhuman/video/translate/acg/v1/callback/figurecenter",
			conf.LocalConfig.MorpheusSettings.CallbackHost)
	}

	// 限制同时训练的人像数量
	list, err := item.GetTasksWithAllStatus(tx, enums.VideoTransfer, enums.VideoFigureTrainWait)
	if err != nil {
		return 1, fmt.Errorf("query training task failed: %v", err)
	}
	if len(list) > conf.LocalConfig.MorpheusSettings.ConcurrencyLimit {
		logger.Log.Warnf(utils.MMark(logCtx)+"too many training task: %d, max: %d, try later.", len(list),
			conf.LocalConfig.MorpheusSettings.ConcurrencyLimit)
		return 1, nil
	}

	figureId, err := morpheus.SubmitTask(logCtx, conf.LocalConfig.MorpheusSettings.Host,
		progress.VideoTransfer.Figure.TemplateUrl,
		progress.VideoTransfer.Figure.Name, progress.VideoTransfer.Figure.FigureName, item.UserID,
		item.ResolutionWidth, item.ResolutionHeight, conf.LocalConfig.MorpheusSettings.ResourceLabel,
		conf.LocalConfig.MorpheusSettings.SceneLabel, "")
	if err != nil {
		return 1, fmt.Errorf("figure submit failed: %v", err)
	}

	logger.Log.Infof(utils.MMark(logCtx)+"figure train submit done with figureID: %d", figureId)

	progress.VideoTransfer.Figure.FigureId = figureId
	progress.VideoTransfer.Figure.Status = enums.ProgressSubmit

	progress.VideoTransfer.UpdateAt = time.Now()
	progress.VideoTransfer.Figure.UpdateAt = time.Now()

	item.SubStatus = enums.VideoFigureTrainWait
	return 1, nil
}

func (p *TaskScheduler) doFigureTrainCheck(logCtx context.Context, item *model.VideoTranslateTask,
	config *proto.SubmitConfig, progress *proto.Progress, cache *CachePath) (int, error) {
	logger.Log.Infof(utils.MMark(logCtx) + "doFigureTrainCheck")
	check, err := morpheus.QueryFigure(logCtx, conf.LocalConfig.MorpheusSettings.Host, item.UserID,
		progress.VideoTransfer.Figure.FigureId)
	if err != nil {
		return 1, fmt.Errorf("figure query failed: %v", err)
	}

	body, _ := json.Marshal(check)
	logger.Log.Infof(utils.MMark(logCtx)+"train check status: %s", string(body))

	if check.Id == progress.VideoTransfer.Figure.FigureId {
		if strings.ToLower(check.Status) == "success" {
			item.SubStatus = enums.VideoTransferSubmit
			progress.VideoTransfer.Figure.Status = enums.ProgressSuccess
			progress.VideoTransfer.UpdateAt = time.Now()
			progress.VideoTransfer.Figure.UpdateAt = time.Now()
			progress.VideoTransfer.Figure.Duration = int(progress.VideoTransfer.Figure.UpdateAt.Sub(progress.VideoTransfer.Figure.CreateAt).
				Seconds())

		} else if strings.ToLower(check.Status) == "failed" {
			progress.VideoTransfer.Figure.Status = enums.ProgressFailed

			progress.VideoTransfer.Figure.Message = check.ResultMessage
			progress.VideoTransfer.UpdateAt = time.Now()
			progress.VideoTransfer.Figure.UpdateAt = time.Now()
			progress.VideoTransfer.Figure.Duration = int(progress.VideoTransfer.Figure.UpdateAt.Sub(progress.VideoTransfer.Figure.CreateAt).
				Seconds())

			canRetry := 0

			// train-server 意外导致的训练失败可以重试
			if len(check.ResultMessage) > 0 {
				var msg morpheus.ResultMessage
				err = json.Unmarshal([]byte(check.ResultMessage), &msg)
				if err != nil {
					logger.Log.Warnf(utils.MMark(logCtx)+"unmarshal result message failed: %v", err)
				} else {
					if msg.ErrCode == 1502 {
						logger.Log.Warnf(utils.MMark(logCtx)+"unexcepted train server status, retry later, detail: %s", check.ResultMessage)
						canRetry = 1
					} else if msg.ErrCode == 10005 {
						// 这个错误是因为视频中没有人像，那么可以按仅翻译音频来处理
						logger.Log.Warnf(utils.MMark(logCtx) + "train failed cause no face detect, just translate audio")
						item.Status = enums.PostProcess
						item.SubStatus = enums.PostProcessWait
						progress.VideoTransfer.Figure.Skip = 1
						return 1, nil
					}
				}
			}

			item.Status = enums.Failed
			return canRetry, fmt.Errorf("train failed: %v", progress.VideoTransfer.Figure.Message)
		}
	}

	return 1, nil
}

func (p *TaskScheduler) doFigureVideoSubmit(logCtx context.Context, tx *gorm.DB, item *model.VideoTranslateTask,
	config *proto.SubmitConfig, progress *proto.Progress, cache *CachePath) (int, error) {
	logger.Log.Infof(utils.MMark(logCtx) + "doFigureVideoSubmit")

	// 限制同时生成的人像数量
	list, err := item.GetTasksWithAllStatus(tx, enums.VideoTransfer, enums.VideoTransferWait)
	if err != nil {
		return 1, fmt.Errorf("query videopipeline task failed: %v", err)
	}
	if len(list) > conf.LocalConfig.VideoPipelineSettings.ConcurrencyLimit {
		logger.Log.Warnf(utils.MMark(logCtx)+"too many videopipeline task: %d, max: %d, try later.", len(list),
			conf.LocalConfig.VideoPipelineSettings.ConcurrencyLimit)
		return 1, nil
	}

	progress.VideoTransfer.Video.CreateAt = time.Now()

	progress.VideoTransfer.Video.Name = fmt.Sprintf("%s", item.TaskId)
	if len(conf.LocalConfig.VideoPipelineSettings.CallbackHost) > 0 {
		progress.VideoTransfer.Video.CallbackUrl = fmt.Sprintf(
			"%s/api/digitalhuman/video/translate/acg/v1/callback/videopipeline", conf.LocalConfig.VideoPipelineSettings.
				CallbackHost)
	}
	progress.VideoTransfer.Video.Transparent = conf.LocalConfig.VideoPipelineSettings.Transparent
	progress.VideoTransfer.Video.FigureName = progress.VideoTransfer.Figure.FigureName

	task, err := videopipeline.SubmitTask(logCtx, conf.LocalConfig.VideoPipelineSettings.Host,
		conf.LocalConfig.VideoPipelineSettings.AppId, conf.LocalConfig.VideoPipelineSettings.AppKey, item.UserID,
		item.ResolutionWidth, item.ResolutionHeight, item.Duration, 0,
		progress.VideoTransfer.Figure.FigureName, progress.VideoTransfer.Video.Name, progress.VideoTransfer.Video.CallbackUrl,
		progress.VideoTransfer.Video.AudioUrl, progress.VideoTransfer.Video.Transparent)
	if err != nil {
		return 1, fmt.Errorf("videopipeline submit failed: %v", err)
	}

	logger.Log.Infof(utils.MMark(logCtx)+"submit to video pipeline with videoId: %s", task.VideoId)

	progress.VideoTransfer.Video.VideoId = task.VideoId
	progress.VideoTransfer.Video.Status = enums.ProgressSubmit
	progress.VideoTransfer.UpdateAt = time.Now()
	progress.VideoTransfer.Video.UpdateAt = time.Now()

	item.SubStatus = enums.VideoTransferWait
	return 1, nil
}

func (p *TaskScheduler) doFigureVideoCheck(logCtx context.Context, item *model.VideoTranslateTask,
	config *proto.SubmitConfig, progress *proto.Progress, cache *CachePath) (int, error) {
	logger.Log.Infof(utils.MMark(logCtx) + "doFigureVideoCheck")

	task, err := videopipeline.QueryTask(logCtx, conf.LocalConfig.VideoPipelineSettings.Host, progress.VideoTransfer.Video.VideoId)
	if err != nil {
		return 1, fmt.Errorf("videopipeline check failed: %v", err)
	}

	body, _ := json.Marshal(task)
	logger.Log.Infof(utils.MMark(logCtx)+"video pipeline check status: %s", string(body))

	if strings.ToLower(task.Status) == "succeed" {
		progress.VideoTransfer.Video.VideoUrl = task.DownloadUrl
		progress.VideoTransfer.Video.CaptionUrl = task.SubtitleFileUrl

		progress.VideoTransfer.Video.Status = enums.ProgressSuccess
		progress.VideoTransfer.UpdateAt = time.Now()
		progress.VideoTransfer.Video.UpdateAt = time.Now()
		progress.VideoTransfer.Video.Duration = int(progress.VideoTransfer.Video.UpdateAt.Sub(progress.VideoTransfer.Video.CreateAt).
			Seconds())

		item.Status = enums.PostProcess
		item.SubStatus = enums.PostProcessWait
	} else if strings.ToLower(task.Status) == "error" {
		progress.VideoTransfer.Video.Status = enums.ProgressFailed
		progress.VideoTransfer.UpdateAt = time.Now()
		progress.VideoTransfer.Video.UpdateAt = time.Now()
		progress.VideoTransfer.Video.Duration = int(progress.VideoTransfer.Video.UpdateAt.Sub(progress.VideoTransfer.Video.CreateAt).
			Seconds())

		progress.VideoTransfer.Video.Message = task.ShowMessage

		item.Status = enums.Failed
	}

	return 1, nil
}
