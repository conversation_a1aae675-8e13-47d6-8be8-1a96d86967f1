package voice_clone

// AddVoiceRequest 封装上传声音的请求参数
type AddVoiceRequest struct {
	Name                  string
	RemoveBackgroundNoise bool
	Labels                string
	Description           string
}

type AddVoiceResponse struct {
	VoiceID              string `json:"voice_id,omitempty"`
	RequiresVerification bool   `json:"requires_verification,omitempty"`
}

type VoiceSample struct {
	SampleId                string  `json:"sample_id"`
	FileName                string  `json:"file_name"`
	MimeType                string  `json:"mime_type"`
	SizeBytes               int     `json:"size_bytes"`
	Hash                    string  `json:"hash"`
	DurationSecs            float64 `json:"duration_secs"`
	RemoveBackgroundNoise   bool    `json:"remove_background_noise"`
	HasIsolatedAudio        bool    `json:"has_isolated_audio"`
	HasIsolatedAudioPreview bool    `json:"has_isolated_audio_preview"`
	SpeakerSeparation       struct {
		VoiceId  string `json:"voice_id"`
		SampleId string `json:"sample_id"`
		Status   string `json:"status"`
	} `json:"speaker_separation"`
	TrimStart int `json:"trim_start"`
	TrimEnd   int `json:"trim_end"`
}

type FineTuning struct {
	IsAllowedToFineTune bool `json:"is_allowed_to_fine_tune"`
	State               struct {
		ElevenMultilingualV2 string `json:"eleven_multilingual_v2"`
	} `json:"state"`
	VerificationFailures        []string `json:"verification_failures"`
	VerificationAttemptsCount   int      `json:"verification_attempts_count"`
	ManualVerificationRequested bool     `json:"manual_verification_requested"`
	Language                    string   `json:"language"`
	Progress                    struct {
		Key float64 `json:"key"`
	} `json:"progress"`
	Message struct {
		Key string `json:"key"`
	} `json:"message"`
	DatasetDurationSeconds float64 `json:"dataset_duration_seconds"`
	VerificationAttempts   []struct {
		Text                string  `json:"text"`
		DateUnix            int     `json:"date_unix"`
		Accepted            bool    `json:"accepted"`
		Similarity          float64 `json:"similarity"`
		LevenshteinDistance int     `json:"levenshtein_distance"`
		Recording           struct {
			RecordingId    string `json:"recording_id"`
			MimeType       string `json:"mime_type"`
			SizeBytes      int    `json:"size_bytes"`
			UploadDateUnix int    `json:"upload_date_unix"`
			Transcription  string `json:"transcription"`
		} `json:"recording"`
	} `json:"verification_attempts"`
	SliceIds           []string `json:"slice_ids"`
	ManualVerification struct {
		ExtraText       string `json:"extra_text"`
		RequestTimeUnix int    `json:"request_time_unix"`
		Files           []struct {
			FileId         string `json:"file_id"`
			FileName       string `json:"file_name"`
			MimeType       string `json:"mime_type"`
			SizeBytes      int    `json:"size_bytes"`
			UploadDateUnix int    `json:"upload_date_unix"`
		} `json:"files"`
	} `json:"manual_verification"`
	MaxVerificationAttempts                int `json:"max_verification_attempts"`
	NextMaxVerificationAttemptsResetUnixMs int `json:"next_max_verification_attempts_reset_unix_ms"`
	FinetuningState                        struct {
		Key string `json:"key"`
	} `json:"finetuning_state"`
}

type Labels struct {
	Accent      string `json:"accent"`
	Age         string `json:"age"`
	Description string `json:"description"`
	Gender      string `json:"gender"`
	UseCase     string `json:"use_case"`
}

type Settings struct {
	Stability       float64 `json:"stability"`
	SimilarityBoost float64 `json:"similarity_boost"`
	Style           float64 `json:"style"`
	UseSpeakerBoost bool    `json:"use_speaker_boost"`
	Speed           float64 `json:"speed"`
}

type Sharing struct {
	Status                  string   `json:"status"`
	HistoryItemSampleId     string   `json:"history_item_sample_id"`
	DateUnix                int      `json:"date_unix"`
	WhitelistedEmails       []string `json:"whitelisted_emails"`
	PublicOwnerId           string   `json:"public_owner_id"`
	OriginalVoiceId         string   `json:"original_voice_id"`
	FinancialRewardsEnabled bool     `json:"financial_rewards_enabled"`
	FreeUsersAllowed        bool     `json:"free_users_allowed"`
	LiveModerationEnabled   bool     `json:"live_moderation_enabled"`
	Rate                    float64  `json:"rate"`
	NoticePeriod            int      `json:"notice_period"`
	DisableAtUnix           int      `json:"disable_at_unix"`
	VoiceMixingAllowed      bool     `json:"voice_mixing_allowed"`
	Featured                bool     `json:"featured"`
	Category                string   `json:"category"`
	ReaderAppEnabled        bool     `json:"reader_app_enabled"`
	ImageUrl                string   `json:"image_url"`
	BanReason               string   `json:"ban_reason"`
	LikedByCount            int      `json:"liked_by_count"`
	ClonedByCount           int      `json:"cloned_by_count"`
	Name                    string   `json:"name"`
	Description             string   `json:"description"`
	Labels                  struct {
		Accent string `json:"accent"`
		Gender string `json:"gender"`
	} `json:"labels"`
	ReviewStatus      string `json:"review_status"`
	ReviewMessage     string `json:"review_message"`
	EnabledInLibrary  bool   `json:"enabled_in_library"`
	InstagramUsername string `json:"instagram_username"`
	TwitterUsername   string `json:"twitter_username"`
	YoutubeUsername   string `json:"youtube_username"`
	TiktokUsername    string `json:"tiktok_username"`
	ModerationCheck   struct {
		DateCheckedUnix  int       `json:"date_checked_unix"`
		NameValue        string    `json:"name_value"`
		NameCheck        bool      `json:"name_check"`
		DescriptionValue string    `json:"description_value"`
		DescriptionCheck bool      `json:"description_check"`
		SampleIds        []string  `json:"sample_ids"`
		SampleChecks     []float64 `json:"sample_checks"`
		CaptchaIds       []string  `json:"captcha_ids"`
		CaptchaChecks    []float64 `json:"captcha_checks"`
	} `json:"moderation_check"`
	ReaderRestrictedOn []struct {
		ResourceType string `json:"resource_type"`
		ResourceId   string `json:"resource_id"`
	} `json:"reader_restricted_on"`
}

type VerifiedLanguages struct {
	Language   string `json:"language"`
	ModelId    string `json:"model_id"`
	Accent     string `json:"accent"`
	Locale     string `json:"locale"`
	PreviewUrl string `json:"preview_url"`
}

type VoiceVerification struct {
	RequiresVerification      bool     `json:"requires_verification"`
	IsVerified                bool     `json:"is_verified"`
	VerificationFailures      []string `json:"verification_failures"`
	VerificationAttemptsCount int      `json:"verification_attempts_count"`
	Language                  string   `json:"language"`
	VerificationAttempts      []struct {
		Text                string  `json:"text"`
		DateUnix            int     `json:"date_unix"`
		Accepted            bool    `json:"accepted"`
		Similarity          float64 `json:"similarity"`
		LevenshteinDistance int     `json:"levenshtein_distance"`
		Recording           struct {
			RecordingId    string `json:"recording_id"`
			MimeType       string `json:"mime_type"`
			SizeBytes      int    `json:"size_bytes"`
			UploadDateUnix int    `json:"upload_date_unix"`
			Transcription  string `json:"transcription"`
		} `json:"recording"`
	} `json:"verification_attempts"`
}

type VoiceInfo struct {
	VoiceId                 string               `json:"voice_id"`
	Name                    string               `json:"name"`
	Samples                 []*VoiceSample       `json:"samples"`
	Category                string               `json:"category"`
	FineTuning              *FineTuning          `json:"fine_tuning"`
	Labels                  *Labels              `json:"labels"`
	Description             string               `json:"description"`
	PreviewUrl              string               `json:"preview_url"`
	AvailableForTiers       []string             `json:"available_for_tiers"`
	Settings                *Settings            `json:"settings"`
	Sharing                 *Sharing             `json:"sharing"`
	HighQualityBaseModelIds []string             `json:"high_quality_base_model_ids"`
	VerifiedLanguages       []*VerifiedLanguages `json:"verified_languages"`
	SafetyControl           string               `json:"safety_control"`
	VoiceVerification       *VoiceVerification   `json:"voice_verification"`
	PermissionOnResource    string               `json:"permission_on_resource"`
	IsOwner                 bool                 `json:"is_owner"`
	IsLegacy                bool                 `json:"is_legacy"`
	IsMixed                 bool                 `json:"is_mixed"`
	CreatedAtUnix           int                  `json:"created_at_unix"`
}

type DeleteResponse struct {
	Status string `json:"status"`
}

type VoiceSettings struct {
	Stability       int     `json:"stability,omitempty"`
	SimilarityBoost int     `json:"similarity_boost,omitempty"`
	Style           int     `json:"style,omitempty"`
	UseSpeakerBoost bool    `json:"use_speaker_boost,omitempty"`
	Speed           float64 `json:"speed,omitempty"`
}

type PronunciationDictionaryLocators struct {
	PronunciationDictionaryId string `json:"pronunciation_dictionary_id,omitempty"`
}

type Text2SpeechRequest struct {
	Text                            string                             `json:"text,omitempty"`
	ModelId                         string                             `json:"model_id,omitempty"`
	LanguageCode                    string                             `json:"language_code,omitempty"`
	VoiceSettings                   *VoiceSettings                     `json:"voice_settings,omitempty"`
	Seed                            int                                `json:"seed,omitempty"`
	PreviousText                    string                             `json:"previous_text,omitempty"`
	NextText                        string                             `json:"next_text,omitempty"`
	ApplyTextNormalization          string                             `json:"apply_text_normalization,omitempty"`
	ApplyLanguageTextNormalization  bool                               `json:"apply_language_text_normalization,omitempty"`
	PronunciationDictionaryLocators []*PronunciationDictionaryLocators `json:"pronunciation_dictionary_locators,omitempty"`
	PreviousRequestIds              []string                           `json:"previous_request_ids,omitempty"`
	NextRequestIds                  []string                           `json:"next_request_ids,omitempty"`
}

type TTSRequest struct {
	Text                            string         `json:"text,omitempty"`
	ModelID                         string         `json:"model_id,omitempty"`
	LanguageCode                    string         `json:"language_code,omitempty"`
	VoiceSettings                   *VoiceSettings `json:"voice_settings,omitempty"`
	PronunciationDictionaryLocators []string       `json:"pronunciation_dictionary_locators,omitempty"`
	Seed                            int            `json:"seed,omitempty"`
	PreviousText                    string         `json:"previous_text,omitempty"`
	NextText                        string         `json:"next_text,omitempty"`
	PreviousRequestIds              []string       `json:"previous_request_ids,omitempty"`
	NextRequestIds                  []string       `json:"next_request_ids,omitempty"`
	ApplyTextNormalization          string         `json:"apply_text_normalization,omitempty"`
	ApplyLanguageTextNormalization  bool           `json:"apply_language_text_normalization,omitempty"`
	UsePVCAsIVC                     bool           `json:"use_pvc_as_ivc,omitempty"`
}

type TtsResponse struct {
	Detail struct {
		Status  string `json:"status"`
		Message string `json:"message"`
	} `json:"detail,omitempty"`
	AudioBase64 string `json:"audio_base64,omitempty"`
	Alignment   struct {
		Characters                 []string  `json:"characters,omitempty"`
		CharacterStartTimesSeconds []float64 `json:"character_start_times_seconds,omitempty"`
		CharacterEndTimesSeconds   []float64 `json:"character_end_times_seconds,omitempty"`
	} `json:"alignment,omitempty"`
	NormalizedAlignment struct {
		Characters                 []string  `json:"characters,omitempty"`
		CharacterStartTimesSeconds []float64 `json:"character_start_times_seconds,omitempty"`
		CharacterEndTimesSeconds   []float64 `json:"character_end_times_seconds,omitempty"`
	} `json:"normalized_alignment,omitempty"`
}
