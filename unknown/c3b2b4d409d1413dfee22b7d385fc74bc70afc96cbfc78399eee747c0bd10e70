package helper

import (
	"acg-ai-go-common/logger"
	"acg-ai-go-common/utils"
	"context"
	"digital-human-micro-video-trans/conf"
	"digital-human-micro-video-trans/thirdparty/elevenlabs/voice_clone"
	"digital-human-micro-video-trans/thirdparty/minimax"
	"digital-human-micro-video-trans/thirdparty/sutils/fileutils"
	"encoding/json"
	"fmt"
	"os"
)

type Alignment struct {
	Characters                 []string  `json:"characters,omitempty"`
	CharacterStartTimesSeconds []float64 `json:"character_start_times_seconds,omitempty"`
	CharacterEndTimesSeconds   []float64 `json:"character_end_times_seconds,omitempty"`
}

type TtsResponse struct {
	AudioBase64 string    `json:"audio_base64,omitempty"`
	Alignment   Alignment `json:"alignment,omitempty"`
}

func ConvertElevenLabsSpeech(tempPath string, input *voice_clone.TtsResponse) (*TtsResponse, error) {
	resp := TtsResponse{}
	resp.AudioBase64 = input.AudioBase64
	resp.Alignment = input.Alignment
	return &resp, nil
}

func ConvertMinimaxSpeech(logCtx context.Context, tempPath string, input *minimax.SynthesisResponse) (*TtsResponse,
	error) {
	resp := TtsResponse{}

	var fileNeedRemove []string
	defer func() {
		//for _, f := range fileNeedRemove {
		//	os.Remove(f)
		//}
	}()
	if input.Data != nil {
		// 音频数据
		resp.AudioBase64 = input.Data.Audio

		// 字幕数据
		if len(input.Data.SubtitleFile) > 0 {
			// 下载字幕
			subtitlePath := fmt.Sprintf("%s/cache/subtitle-%s.txt", tempPath, utils.RandStringRunes(6))
			fileNeedRemove = append(fileNeedRemove, subtitlePath)
			err := fileutils.RetryDownloadFile(input.Data.SubtitleFile, subtitlePath,
				conf.LocalConfig.ScheduleSettings.HttpRetryCount)
			if err != nil {
				return &resp, fmt.Errorf("download subtitle: %v", err)
			}
			logger.Log.Infof(utils.MMark(logCtx)+"download subtitle %s", input.Data.SubtitleFile)

			// 加载字幕内容
			data, err := os.ReadFile(subtitlePath)
			if err != nil {
				return &resp, fmt.Errorf("read file: %w", err)
			}
			var input []minimax.Subtitle
			if err := json.Unmarshal(data, &input); err != nil {
				return &resp, fmt.Errorf("json unmarshal: %w", err)
			}

			// 转换
			resp.Alignment = *subtitleToAlignment(input)
		}
	}
	return &resp, nil
}

func subtitleToAlignment(sub []minimax.Subtitle) *Alignment {
	var (
		characters                 []string
		characterStartTimesSeconds []float64
		characterEndTimesSeconds   []float64
	)

	for _, words := range sub {
		for _, word := range words.TimestampedWords {
			if word.Word == "[SPACE]" {
				word.Word = " "
			}
			characters = append(characters, word.Word)
			characterStartTimesSeconds = append(characterStartTimesSeconds, word.TimeBegin/1000)
			characterEndTimesSeconds = append(characterEndTimesSeconds, word.TimeEnd/1000)
		}
	}

	return &Alignment{
		Characters:                 characters,
		CharacterStartTimesSeconds: characterStartTimesSeconds,
		CharacterEndTimesSeconds:   characterEndTimesSeconds,
	}
}
