package testing

import (
	"acg-ai-go-common/utils"
	"context"
	"digital-human-micro-chroma/handler/sutils/ffmpegutils"
	"fmt"
	"testing"
)

func TestGetVideoInfo(t *testing.T) {
	info, err := ffmpegutils.GetVideoInfo("https://digital-human-pipeline-output.cdn.bcebos." +
		"com/69fcede7-7812-438f-bfbb-0982879a8292.mp4")
	if err != nil {
		fmt.Printf("ffprobe err: %v, info: %v\n", err, info)
		return
	}
	fmt.Printf("ffprobe result: %v\n", info)
}

func TestSplitVideo(t *testing.T) {
	logCtx := context.WithValue(context.TODO(), utils.CtxKeyLogID, "test")
	err := ffmpegutils.SplitVideoIntoPartsForceKeyFrameWithNoAudio(logCtx, "https://digital-human-pipeline-output.cdn.bcebos."+
		"com/69fcede7-7812-438f-bfbb-0982879a8292.mp4", "./test_part_%03d.mp4", 40, 50)
	if err != nil {
		fmt.Printf("ffmpeg err: %v\n", err)
		return
	}
}
