package schedeuler

import (
	"acg-ai-go-common/gomysql"
	"acg-ai-go-common/logger"
	"acg-ai-go-common/storage"
	"acg-ai-go-common/utils"
	"acg-ai-go-common/utils/elevenlabs"
	"acg-ai-go-common/utils/redisproxy"
	"context"
	"digital-human-micro-video-trans/beans/enums"
	"digital-human-micro-video-trans/beans/model"
	"digital-human-micro-video-trans/beans/proto"
	"digital-human-micro-video-trans/conf"
	"digital-human-micro-video-trans/thirdparty/elevenlabs/speech2text"
	"digital-human-micro-video-trans/thirdparty/ffmpeg"
	"digital-human-micro-video-trans/thirdparty/google/cloud_translate"
	"digital-human-micro-video-trans/thirdparty/google/gemini"
	"digital-human-micro-video-trans/thirdparty/musicai"
	"digital-human-micro-video-trans/thirdparty/sutils"
	"digital-human-micro-video-trans/thirdparty/sutils/fileutils"
	"digital-human-micro-video-trans/thirdparty/sutils/jsonutils"
	"digital-human-micro-video-trans/thirdparty/sutils/redislock"
	"encoding/json"
	"fmt"
	"gorm.io/gorm"
	"os"
	"time"
)

func (p *TaskScheduler) HandlePreprocess() {
	logger.Log.Info("start handle preprocess task")

	taskList, err := (&model.VideoTranslateTask{}).GetTasksWithStatus(gomysql.DB, enums.PreProcessing)
	if err != nil {
		logger.Log.Errorf("get task with status: %s, err: %v", enums.PreProcessing, err)
		return
	}

	if len(taskList) < 1 {
		logger.Log.Infof("skip cause no %s task", enums.PreProcessing)
		return
	}

	scheduleCount := 0
	for _, item := range taskList {
		err := p.PreProcessing(item)
		if err != nil {
			logger.Log.Errorf("schedule task: %s failed, cause: %v", item.TaskId, err)
		}
		scheduleCount++
		if scheduleCount >= conf.LocalConfig.ScheduleSettings.MaxScheduleSize {
			logger.Log.Infof("scheduled reach max control size: %d/%d", scheduleCount,
				conf.LocalConfig.ScheduleSettings.MaxScheduleSize)
			break
		}
	}
	logger.Log.Infof("scheduled task: %d with status: %s", scheduleCount, enums.PreProcessing)
}

func (p *TaskScheduler) PreProcessing(task *model.VideoTranslateTask) error {
	logCtx := context.WithValue(context.TODO(), utils.CtxKeyLogID, task.TaskId)
	redisProxy := redisproxy.GetRedisProxy()

	// 加锁
	redisLockKey := fmt.Sprintf(TaskScheduleLockFmt, sutils.GetNameByRunEnv(), task.TaskId)
	redisLock := redislock.NewRedisLock(redisProxy.Rdb, redisLockKey, TaskScheduleLockExpireTime)
	ok, err := redisLock.Lock(context.Background())
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"PreProcessing Lock key: %s error: %+v\n", redisLockKey, err)
		return fmt.Errorf("get redis lock err: %v", err)
	} else if !ok {
		logger.Log.Warnf(utils.MMark(logCtx) + "redis key locked by other instance, skip it.")
		return nil
	}
	defer redisLock.Unlock(context.Background())

	// 重新查询
	item, err := task.GetTasksWithTaskId(gomysql.DB, task.TaskId)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"refresh task err: %v", err)
		return fmt.Errorf("get refresh task err: %v", err)
	}

	// 状态变了，就不用处理了
	if item.Status != enums.PreProcessing {
		return fmt.Errorf("task status nolonger %s", enums.PreProcessing)
	}

	// 需要重试
	needRetry := 1

	progress := proto.NewProgress()
	var temp proto.Progress
	if err := jsonutils.JsonMapToStruct(item.Progress, &temp); err == nil {
		progress = &temp
	}

	var config proto.SubmitConfig
	if err := jsonutils.JsonMapToStruct(item.Config, &config); err != nil {
		return fmt.Errorf("parse submit config failed: %v", err)
	}

	err = gomysql.DB.Transaction(func(tx *gorm.DB) error {
		cache, err := GetTaskCache(item)
		if err != nil {
			return fmt.Errorf("parse task cache failed: %v", err)
		}

		if len(item.SubStatus) < 1 {
			item.SubStatus = enums.Isolation
		}

		var subErr error
		tStart := time.Now()
		switch item.SubStatus {
		case enums.Isolation:
			{
				// 分离音频
				needRetry, subErr = p.doIsolation(logCtx, tx, item, &config, progress, cache)
				break
			}
		case enums.IsolationWait:
			{
				// 等待分离结果
				needRetry, subErr = p.waitIsolation(logCtx, item, &config, progress, cache)
				break
			}
		case enums.ASR:
			{
				// 语音识别
				needRetry, subErr = p.doAsr(logCtx, item, &config, progress, cache)
				break
			}
		case enums.ASRIntegration:
			{
				// 根据语音结果确定段落时长
				needRetry, subErr = p.doAsrIntegration(logCtx, item, &config, progress, cache)
				break
			}
		case enums.Translate:
			{
				// 翻译
				needRetry, subErr = p.doTranslate(logCtx, item, &config, progress, cache)
				break
			}
		}
		timeCost := float64(time.Since(tStart).Milliseconds()) / 1000.0
		logger.Log.Infof(utils.MMark(logCtx)+"elapsed with: %.3fs", timeCost)
		if subErr != nil {
			return subErr
		}

		// 保存状态
		jsConfig, err := jsonutils.StructToJsonMap(progress)
		if err != nil {
			return fmt.Errorf("save progress detail failed: %v", err)
		}
		item.Progress = jsConfig

		// 更新
		err = item.Update(tx)
		if err != nil {
			return fmt.Errorf("update to: %s failed: %v", item.Status, err)
		}

		return nil
	})

	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"schedule failed, err: %+v\n", err)

		// 更新任务状态
		if needRetry == 0 {
			// 把次数置为0
			item.RetryAttempts = 0
		}
		item.Status = enums.Failed
		item.Message = fmt.Sprintf("%s", err.Error())

		// 也需要保存状态，有些部分不需要重复重试
		jsConfig, err := jsonutils.StructToJsonMap(progress)
		if err != nil {
			return fmt.Errorf("save progress detail failed: %v", err)
		}
		item.Progress = jsConfig

		err = item.Update(gomysql.DB)
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+"update item failed: %+v, err: %+v\n", item, err)
			return err
		}

		return fmt.Errorf("schedule task err: %v", err)
	}
	return nil
}

func (p *TaskScheduler) doIsolation(logCtx context.Context, tx *gorm.DB, item *model.VideoTranslateTask,
	config *proto.SubmitConfig,
	progress *proto.Progress, cache *CachePath) (int, error) {
	logger.Log.Infof(utils.MMark(logCtx) + "doIsolation")

	// 当数据表中已经有一定的任务为是分离中，暂停提交任务
	list, err := item.GetTasksWithAllStatus(tx, enums.PreProcessing, enums.IsolationWait)
	if err != nil {
		return 1, fmt.Errorf("query isolating task failed: %v", err)
	}
	if len(list) > conf.LocalConfig.MusicAiSettings.ConcurrencyLimit {
		logger.Log.Warnf(utils.MMark(logCtx)+"too many isolating task: %d, max: %d, try later.", len(list),
			conf.LocalConfig.MusicAiSettings.ConcurrencyLimit)
		return 1, nil
	}
	progress.Isolation.CreateAt = time.Now()

	// 如果没有上传过文件，上传音频
	if len(progress.Isolation.InputUrl) < 1 || item.RetryType != enums.RetryTypeFailedParts {
		fileUrl, err := storage.RetryUploadFromFile(logCtx, cache.BosRoot, cache.InputAudio, cache.AudioSuffix)
		if err != nil || len(fileUrl) < 1 {
			return 1, fmt.Errorf("upload audio failed: %v", err)
		}
		progress.Isolation.InputUrl = fileUrl
		logger.Log.Infof(utils.MMark(logCtx)+"upload audio to: %s", fileUrl)
	}

	// 对音频进行背景和人声提取
	if len(progress.Isolation.JobId) < 1 || item.RetryType != enums.RetryTypeFailedParts {
		jobId, err := musicai.SubmitJob(logCtx, item.TaskId, conf.LocalConfig.MusicAiSettings.Workflow, progress.Isolation.InputUrl,
			conf.LocalConfig.MusicAiSettings.User, conf.LocalConfig.MusicAiSettings.ApiKey)
		if err != nil {
			return 1, fmt.Errorf("submit to isolation engine failed: %v", err)
		}
		progress.Isolation.JobId = jobId
		progress.Isolation.UpdateAt = time.Now()
		logger.Log.Infof(utils.MMark(logCtx)+"submit audio isolation: %s", progress.Isolation.JobId)
	}

	// 更新数据
	item.SubStatus = enums.IsolationWait
	return 1, nil
}

func (p *TaskScheduler) waitIsolation(logCtx context.Context, item *model.VideoTranslateTask,
	config *proto.SubmitConfig,
	progress *proto.Progress, cache *CachePath) (int, error) {
	logger.Log.Infof(utils.MMark(logCtx) + "waitIsolation")
	// 去查询一下结果
	result, err := musicai.GetJobResult(logCtx, progress.Isolation.JobId, conf.LocalConfig.MusicAiSettings.ApiKey)
	if err != nil || result == nil || len(result.Status) < 1 {
		progress.Isolation.Status = enums.ProgressFailed
		progress.Isolation.Message = fmt.Sprintf("%s", err)
		return 1, fmt.Errorf("get isolation result failed: %v", err)
	}
	body, _ := json.Marshal(result)
	logger.Log.Infof(utils.MMark(logCtx)+"get audio isolation: %s", string(body))

	if result.Status == "SUCCEEDED" {
		progress.Isolation.Status = enums.ProgressSuccess
		progress.Isolation.JobVocal = result.Result.Dialogue
		progress.Isolation.JobBgm = result.Result.Music
		progress.Isolation.JobEffect = result.Result.EffectsBackgroundNoise

		err = fileutils.RetryDownloadFile(progress.Isolation.JobVocal, cache.VocalAudioTemp,
			conf.LocalConfig.ScheduleSettings.HttpRetryCount)
		if err != nil {
			return 1, fmt.Errorf("download vocal failed: %v", err)
		}
		logger.Log.Infof(utils.MMark(logCtx)+"download vocal done, file: %v", cache.VocalAudioTemp)

		err = fileutils.RetryDownloadFile(progress.Isolation.JobBgm, cache.BgmAudioTemp,
			conf.LocalConfig.ScheduleSettings.HttpRetryCount)
		if err != nil {
			return 1, fmt.Errorf("download bgm failed: %v", err)
		}
		logger.Log.Infof(utils.MMark(logCtx)+"download bgm done, file: %v", cache.BgmAudioTemp)

		err = fileutils.RetryDownloadFile(progress.Isolation.JobEffect, cache.BackEffectAudioTemp,
			conf.LocalConfig.ScheduleSettings.HttpRetryCount)
		if err != nil {
			return 1, fmt.Errorf("download effects failed: %v", err)
		}
		logger.Log.Infof(utils.MMark(logCtx)+"download effects done, file: %v", cache.BackEffectAudioTemp)

		err = ffmpeg.ExtractAudio(logCtx, cache.VocalAudioTemp, cache.VocalAudio, "pcm_s16le", 1, 16000)
		if err != nil {
			return 1, fmt.Errorf("ffmpeg convert vocal audio: %v", err)
		}

		err = ffmpeg.ExtractAudio(logCtx, cache.BgmAudioTemp, cache.BgmAudio, "pcm_s16le", 1, 16000)
		if err != nil {
			return 1, fmt.Errorf("ffmpeg convert bgm audio: %v", err)
		}

		err = ffmpeg.ExtractAudio(logCtx, cache.BackEffectAudioTemp, cache.BackEffectAudio, "pcm_s16le", 1, 16000)
		if err != nil {
			return 1, fmt.Errorf("ffmpeg convert effetcs audio: %v", err)
		}

		// 第三方的存储有时间限制，需要转存到我们自己的存储上来，以便于问题排查
		vocalUrl, err := storage.RetryUploadFromFile(logCtx, cache.BosRoot, cache.VocalAudio, cache.VocalAudioSuffix)
		if err != nil {
			return 1, fmt.Errorf("resave vocal to bos failed: %v", err)
		}
		progress.Isolation.Vocal = vocalUrl
		logger.Log.Infof(utils.MMark(logCtx)+"resave vocal to bos: %s", progress.Isolation.Vocal)

		bgmUrl, err := storage.RetryUploadFromFile(logCtx, cache.BosRoot, cache.BgmAudio, cache.BgmAudioSuffix)
		if err != nil {
			return 1, fmt.Errorf("resave bgm to bos failed: %v", err)
		}
		progress.Isolation.Bgm = bgmUrl
		logger.Log.Infof(utils.MMark(logCtx)+"resave bgm to bos: %s", progress.Isolation.Bgm)

		effectUrl, err := storage.RetryUploadFromFile(logCtx, cache.BosRoot, cache.BackEffectAudio, cache.BackEffectAudioSuffix)
		if err != nil {
			return 1, fmt.Errorf("resave effects to bos failed: %v", err)
		}
		progress.Isolation.Effects = effectUrl
		logger.Log.Infof(utils.MMark(logCtx)+"resave effects to bos: %s", progress.Isolation.Effects)

		item.SubStatus = enums.ASR
		progress.Isolation.UpdateAt = time.Now()
		progress.Isolation.Duration = int(progress.Isolation.UpdateAt.Sub(progress.Isolation.CreateAt).Seconds())

	} else if result.Status == "FAILED" {
		progress.Isolation.Status = enums.ProgressFailed
		progress.Isolation.Message = fmt.Sprintf("%+v", result)
		progress.Isolation.UpdateAt = time.Now()
		progress.Isolation.Duration = int(progress.Isolation.UpdateAt.Sub(progress.Isolation.CreateAt).Seconds())

		return 0, fmt.Errorf("isolation failed: %s", result)
	}

	return 1, nil
}

func (p *TaskScheduler) doAsr(logCtx context.Context, item *model.VideoTranslateTask, config *proto.SubmitConfig,
	progress *proto.Progress, cache *CachePath) (int, error) {
	logger.Log.Infof(utils.MMark(logCtx) + "doAsr")

	// 限流，限制同一时刻发起ASR的数量
	lock, err := p.AsrLimiter.Acquire(logCtx, 5*time.Second)
	if err != nil {
		logger.Log.Warnf(utils.MMark(logCtx)+"to many asr request currently, retry later, limiter err: %v", err)
		return 1, nil
	}
	defer p.AsrLimiter.Release(logCtx, lock)

	progress.Asr.CreateAt = time.Now()

	req := elevenlabs.AsrRequest{
		ModelID:     conf.LocalConfig.ElevenLabsSettings.AsrModelId,
		File:        cache.VocalAudio,
		Diarize:     true,
		NumSpeakers: 0,
	}

	// 一期在仅针对单人像场景时，检测时暂不指定讲话人，避免识别问题
	if config.SpeakerNum > 0 && config.SpeakerNum != 1 {
		req.NumSpeakers = config.SpeakerNum
	}

	// 语音识别
	resp, err := elevenlabs.DoSpeechToText(logCtx, conf.LocalConfig.ElevenLabsSettings.AsrUrl,
		conf.LocalConfig.ElevenLabsSettings.ApiKey, &req)
	if err != nil {
		return 1, fmt.Errorf("do asr failed: %v", err)
	}
	logger.Log.Infof(utils.MMark(logCtx)+"get asr result: %v", resp.Text)

	jsonBytes, err := json.Marshal(resp)
	if err != nil {
		return 1, fmt.Errorf("marshal failed: %v", err)
	}

	err = os.WriteFile(cache.AsrResult, jsonBytes, 0644)
	if err != nil {
		return 1, fmt.Errorf("write to out file failed: %v", err)
	}

	// 上传一份到云端
	fileUrl, err := storage.RetryUploadFromFile(logCtx, cache.BosRoot, cache.AsrResult, cache.AsrResultSuffix)
	if err != nil || len(fileUrl) < 1 {
		return 1, fmt.Errorf("upload audio failed: %v", err)
	}
	logger.Log.Infof(utils.MMark(logCtx)+"save asr result to bos: %s", fileUrl)

	progress.Asr.AsrResultUrl = fileUrl
	progress.Asr.UpdateAt = time.Now()

	item.SubStatus = enums.ASRIntegration
	return 1, nil
}

func (p *TaskScheduler) doAsrIntegration(logCtx context.Context, item *model.VideoTranslateTask,
	config *proto.SubmitConfig, progress *proto.Progress, cache *CachePath) (int, error) {
	logger.Log.Infof(utils.MMark(logCtx) + "doAsrIntegration")
	data, err := os.ReadFile(cache.AsrResult)
	if err != nil {
		return 1, fmt.Errorf("read file error: %w", err)
	}
	var result speech2text.Response
	if err := json.Unmarshal(data, &result); err != nil {
		return 1, fmt.Errorf("json unmarshal error: %w", err)
	}

	videoInfo, _ := ffmpeg.GetVideoInfo(cache.InputFile)

	// 根据时间戳组合出段落结果
	speakersSentence := speech2text.SplitResponseBySpeakerAndPause(&result,
		conf.LocalConfig.ElevenLabsSettings.AsrSentenceGap, conf.LocalConfig.ElevenLabsSettings.AsrSentenceTime,
		conf.LocalConfig.ElevenLabsSettings.AsrMinSentenceTime, conf.LocalConfig.ElevenLabsSettings.AsrMinSentenceGap,
		videoInfo.Duration)
	logger.Log.Infof(utils.MMark(logCtx)+"get %d speakers", len(speakersSentence))

	// 要整理一下讲话人，归一化到要求的人数
	speakers := speech2text.NormalizeSpeakers(speakersSentence, config.SpeakerNum)
	logger.Log.Infof(utils.MMark(logCtx)+"normalize to %d speakers", len(speakers))

	// 整理音频
	progress.Asr.NumSpeaker = len(speakers)
	for spk, _ := range speakers {
		for seq, sentence := range speakers[spk].Sentences {
			speakers[spk].Sentences[seq].AudioFile = cache.GetAsrPartName(spk, seq)
			err := ffmpeg.ExtractAudioSegment(logCtx, cache.VocalAudio, sentence.Start, sentence.End-sentence.Start,
				"pcm_s16le", 1, 16000,
				speakers[spk].Sentences[seq].AudioFile)
			if err != nil {
				return 1, fmt.Errorf("extract audio segment failed: %v", err)
			}

			logger.Log.Infof(utils.MMark(logCtx)+"extract speaker: %s sentence: %d with duration: %.3f",
				speakers[spk].Id, seq, sentence.End-sentence.Start)
		}
		progress.Asr.MaxSpeaker = append(progress.Asr.MaxSpeaker, speakers[spk].Id)
	}

	jsonBytes, err := json.Marshal(speakers)
	if err != nil {
		return 1, fmt.Errorf("marshal failed: %v", err)
	}

	err = os.WriteFile(cache.AsrSentenceResult, jsonBytes, 0644)
	if err != nil {
		return 1, fmt.Errorf("write to out file failed: %v", err)
	}

	// 上传一份到云端
	fileUrl, err := storage.RetryUploadFromFile(logCtx, cache.BosRoot, cache.AsrSentenceResult, cache.AsrResultSuffix)
	if err != nil || len(fileUrl) < 1 {
		return 1, fmt.Errorf("upload sentences failed: %v", err)
	}
	logger.Log.Infof(utils.MMark(logCtx)+"save asr sentence result to bos: %s", fileUrl)

	progress.Asr.AsrSentenceUrl = fileUrl
	progress.Asr.UpdateAt = time.Now()
	progress.Asr.Duration = int(progress.Asr.UpdateAt.Sub(progress.Asr.CreateAt).Seconds())

	item.SubStatus = enums.Translate
	return 1, nil
}

func (p *TaskScheduler) doTranslate(logCtx context.Context, item *model.VideoTranslateTask,
	config *proto.SubmitConfig, progress *proto.Progress, cache *CachePath) (int, error) {
	logger.Log.Infof(utils.MMark(logCtx) + "doTranslate")

	// 限流，限制调用翻译接口的数量
	lock, err := p.TranslateLimiter.Acquire(logCtx, 5*time.Second)
	if err != nil {
		logger.Log.Warnf(utils.MMark(logCtx)+"to many translate request currently, retry later, limiter err: %v", err)
		return 1, nil
	}
	defer p.TranslateLimiter.Release(logCtx, lock)

	data, err := os.ReadFile(cache.AsrSentenceResult)
	if err != nil {
		return 1, fmt.Errorf("read file error: %w", err)
	}
	var result []*speech2text.Speaker
	if err := json.Unmarshal(data, &result); err != nil {
		return 1, fmt.Errorf("json unmarshal error: %w", err)
	}

	progress.Translate.CreateAt = time.Now()

	// 翻译文本
	tarSentences := result
	for i, _ := range tarSentences {
		for w, _ := range tarSentences[i].Sentences {
			duration := tarSentences[i].Sentences[w].End - tarSentences[i].Sentences[w].Start
			text, err := p.doTranslateWithLLM(logCtx, tarSentences[i].Sentences[w].Text, config.TargetLangs[0], duration)
			if err != nil {
				logger.Log.Errorf(utils.MMark(logCtx)+"doTranslateWithLLM failed: %v, use cloud translate instead", err)
				text, err = cloud_translate.Translate(logCtx, conf.LocalConfig.TranslateSettings.GoogleCredential,
					config.TargetLangs[0], tarSentences[i].Sentences[w].Text)
			}

			if err != nil {
				return 1, fmt.Errorf("translate failed: %v", err)
			}
			tarSentences[i].Sentences[w].NewText = text
			tarSentences[i].Lang = config.TargetLangs[0]

			logger.Log.Infof(utils.MMark(logCtx)+"translate with language: %s, result: %s", tarSentences[i].Lang, tarSentences[i].Sentences[w].NewText)

			time.Sleep(time.Duration(sutils.RandomFloatInRange(0.5, 1.0)) * time.Second)
		}
	}

	jsonBytes, err := json.Marshal(tarSentences)
	if err != nil {
		return 1, fmt.Errorf("marshal failed: %v", err)
	}

	err = os.WriteFile(cache.TranslateResult, jsonBytes, 0644)
	if err != nil {
		return 1, fmt.Errorf("write to out file failed: %v", err)
	}

	// 上传一份到云端
	fileUrl, err := storage.RetryUploadFromFile(logCtx, cache.BosRoot, cache.TranslateResult, cache.TranslateResultSuffix)
	if err != nil || len(fileUrl) < 1 {
		return 1, fmt.Errorf("upload translate failed: %v", err)
	}
	logger.Log.Infof(utils.MMark(logCtx)+"save translate first file: %s", fileUrl)

	progress.Translate.ResultUrl = fileUrl
	progress.Translate.UpdateAt = time.Now()
	progress.Translate.Duration = int(progress.Translate.UpdateAt.Sub(progress.Translate.CreateAt).Seconds())

	item.SubStatus = enums.VoiceCloneSubmit
	item.Status = enums.VoiceClone
	return 1, nil
}

func (p *TaskScheduler) doTranslateWithLLM(logCtx context.Context, text string, tarLang string,
	tarDuration float64) (string, error) {
	systemSetting := "下面给出以下参数：1，text：原始文本；2，tarLang：要求翻译的文本；3，targetDuration：翻译后的文本合成音频期望的长度。" +
		"你需要在保持语义完整的情况下，翻译原文本得到newText，要求newText文本在合成后，音频时长尽可能接近目标时长。仅输出翻译后的newText文本，不要输入其他任何东西。"
	content := fmt.Sprintf("text:%s,tarLang:%s,targetDuration:%.3f", text, tarLang, tarDuration)
	result, err := gemini.GenerateGeminiContent(logCtx, conf.LocalConfig.GeminiSettings.GoogleCredential,
		conf.LocalConfig.GeminiSettings.ProjectId, conf.LocalConfig.GeminiSettings.Location,
		conf.LocalConfig.GeminiSettings.Model, systemSetting, content)
	if err != nil {
		return "", fmt.Errorf("LLM assit translate failed: %v", err)
	}
	return result, nil
}
