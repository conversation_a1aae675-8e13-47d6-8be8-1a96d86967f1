package redislock

import (
	"context"
	"fmt"
	"time"

	"github.com/redis/go-redis/v9"
)

type RedisLock struct {
	rdb      *redis.Client
	lockKey  string
	lockVal  string
	duration time.Duration
}

func NewRedisLock(rdb *redis.Client, lockKey string, duration time.Duration) *RedisLock {
	return &RedisLock{
		rdb:      rdb,
		lockKey:  lockKey,
		lockVal:  fmt.Sprintf("%d", time.Now().UnixNano()),
		duration: duration,
	}
}

func NewEnvRedisLock(rdb *redis.Client, lockKey string, duration time.Duration, env string) *RedisLock {
	return &RedisLock{
		rdb:      rdb,
		lockKey:  lockKey,
		lockVal:  fmt.Sprintf("%s-%d", env, time.Now().UnixNano()),
		duration: duration,
	}
}

func (rl *RedisLock) Lock(ctx context.Context) (bool, error) {
	result, err := rl.rdb.SetNX(ctx, rl.lock<PERSON><PERSON>, rl.lockVal, rl.duration).Result()
	if err != nil {
		return false, err
	}
	return result, nil
}

func (rl *RedisLock) Unlock(ctx context.Context) error {
	script := `
		if redis.call("get", KEYS[1]) == ARGV[1] then
			return redis.call("del", KEYS[1])
		else
			return 0
		end
	`
	keys := []string{rl.lockKey}
	args := []interface{}{rl.lockVal}
	_, err := rl.rdb.Eval(ctx, script, keys, args...).Result()
	return err
}

// LockWithTimeout 尝试获取锁，如果获取不到则在超时时间内持续重试
func (rl *RedisLock) LockWithTimeout(ctx context.Context, timeout time.Duration, retryInterval time.Duration) (bool, error) {
	deadline := time.Now().Add(timeout)

	for {
		// 检查是否超时
		if time.Now().After(deadline) {
			// 获取当前锁的持有者
			currentLockValue, err := rl.rdb.Get(ctx, rl.lockKey).Result()
			if err != nil && err != redis.Nil {
				return false, fmt.Errorf("lock timeout and failed to get current lock holder: %v", err)
			}
			if err == redis.Nil {
				return false, fmt.Errorf("lock timeout, no current holder")
			}
			return false, fmt.Errorf("lock timeout, current holder: %s", currentLockValue)
		}

		// 检查上下文是否已取消
		if ctx.Err() != nil {
			return false, ctx.Err()
		}

		// 尝试获取锁
		acquired, err := rl.Lock(ctx)
		if err != nil {
			return false, err
		}

		if acquired {
			return true, nil
		}

		// 等待一段时间后重试
		select {
		case <-ctx.Done():
			return false, ctx.Err()
		case <-time.After(retryInterval):
			continue
		}
	}
}
