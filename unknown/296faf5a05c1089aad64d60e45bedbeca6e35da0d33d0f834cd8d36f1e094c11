package elevenlabs

import (
	"digital-human-micro-video-trans/thirdparty/elevenlabs/speech2text"
	"encoding/json"
	"fmt"
	"os"
	"testing"
)

var (
	InputFile string = "./input/result.json"
)

func TestSplit(t *testing.T) {
	data, err := os.ReadFile(InputFile)
	if err != nil {
		fmt.Printf("read file error: %v\n", err)
		return
	}
	var result speech2text.Response
	if err := json.Unmarshal(data, &result); err != nil {
		fmt.Printf("json unmarshal error: %v\n", err)
		return
	}

	// 根据时间戳组合出段落结果
	speakersSentence := speech2text.SplitResponseBySpeakerAndPause(&result,
		0.3, 10, 2, 1, 0)

	jsonBytes, err := json.Marshal(speakersSentence)
	if err != nil {
		fmt.Printf("marshal failed: %v\n", err)
		return
	}

	fmt.Printf("%s\n", string(jsonBytes))
}
