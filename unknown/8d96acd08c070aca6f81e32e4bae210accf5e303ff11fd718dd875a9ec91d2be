package test

import (
	"digital-human-micro-video-trans/thirdparty/minimax"
	"fmt"
	"testing"
)

var (
	//Host    = "https://api.minimaxi.com/v1"
	Host    = "https://gcp-api.subsup.net/v1"
	Model   = minimax.ModelSpeechHd01
	GroupID = "1940037532485951986"
	ApiKey  = "eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.eyJHcm91cE5hbWUiOiLnjovpm7ciLCJVc2VyTmFtZSI6IueOi-mbtyIsIkFjY291bnQ" +
		"iOiIiLCJTdWJqZWN0SUQiOiIxOTQwMDM3NTMyNDk0MzQwNTk0IiwiUGhvbmUiOiIxMzY1MTA3MDM0OSIsIkdyb3VwSUQiOiIxOTQwMDM3NTMyN" +
		"Dg1OTUxOTg2IiwiUGFnZU5hbWUiOiIiLCJNYWlsIjoiIiwiQ3JlYXRlVGltZSI6IjIwMjUtMDctMDMgMTI6NTc6MjYiLCJUb2tlblR5cGUiOjE" +
		"sImlzcyI6Im1pbmltYXgifQ.t8dpkKeTV9g-rXSOoCRAte2sUOVZXWNRAZ0d45k8s9HdbF1uSY5w5gsEzwLH9O2fnlfWCnSB2kRWZwaT401mdM" +
		"dFexGdX1bIJTnRHKDU_hoaM-uRbkbHmklOf02HmwLwdq9A7qXxJnP__Kw-8BP_U7CULyarc39PakW-V4eeVt9Nnpm9bSt4xpplAHiD3e6BZNC2" +
		"n4uw9-_y7wotNkYwmgFoA1qMGiPo9hWonxHXKrT_Rxs0dHDYUKpaJvfnzu3fITyE9Tmg2jcAFF1fOYcVKtWRXq-fJX8fKH5bG_9QtSTL4ZJy52" +
		"cLE406uKrvL5QLAvv2_aK0CXi30tE-boNhsg"
)

func TestMiniMaxUpload(t *testing.T) {
	_, s, err := minimax.UploadFile(Host, GroupID, ApiKey, "/Users/<USER>/Downloads/output.wav")
	if err != nil {
		panic(err)
	}

	fmt.Println(s)

	// {"file":{"file_id":286579862335817,"bytes":4674980,"created_at":**********,"filename":"output.wav", "purpose":"voice_clone"},"base_resp":{"status_code":0,"status_msg":"success"}}
}

func TestMiniMaxVoiceClone(t *testing.T) {
	req := minimax.VoiceCloneRequest{
		FileID:                  286579862335817,
		VoiceID:                 "wanglei-test",
		NeedNoiseReduction:      true,
		NeedVolumeNormalization: true,
	}

	_, s, err := minimax.DoVoiceClone(Host, GroupID, ApiKey, req)
	if err != nil {
		panic(err)
	}

	fmt.Println(s)

	// {"input_sensitive":false,"input_sensitive_type":0,"demo_audio":"","base_resp":{"status_code":0,"status_msg":"success"}}
}

func TestMiniMaxVoiceQuery(t *testing.T) {
	_, s, err := minimax.GetVoiceList(Host, ApiKey, "voice_cloning")
	if err != nil {
		panic(err)
	}

	fmt.Println(s)

	// {"system_voice":null,"voice_cloning":[{"voice_id":"wanglei-test","description":[],"voice_name":"",
	// "created_time":"2025-07-03"}],"voice_generation":null,"music_generation":null,"voice_slots":null,
	// "base_resp":{"status_code":0,"status_msg":"success"}}
}

func TestMiniMaxSpeechToTextSync(t *testing.T) {
	req := minimax.SynthesisRequest{
		Model:          minimax.ModelSpeechTurbo02,
		Text:           "건강한 식습관은 현대 사회에서 중요한 관심사입니다. 사회의 빠른 변화로 인해 사람들은 식단에 대한 관심이 높아지고 있습니다. 균형 잡힌 식단은 신체의 기본적인 요구를 충족시킬 뿐만 아니라 질병을 예방하고 신체적, 정신적 건강을 보호합니다.",
		Stream:         false,
		LanguageBoost:  "auto",
		OutputFormat:   "hex",
		SubtitleEnable: true,
		SubtitleType:   "word",
		VoiceSetting: &minimax.VoiceSetting{
			VoiceID: "wanglei-test",
			Speed:   1,
			Vol:     1,
			Pitch:   0,
		},
		AudioSetting: &minimax.AudioSetting{
			SampleRate: 16000,
			Channel:    1,
			Format:     "wav",
		},
	}
	resp, s, err := minimax.SynthesizeSpeech(Host, GroupID, ApiKey, &req)
	if err != nil {
		panic(err)
	}

	fmt.Println(s)

	// {"data":{"audio":"https://minimax-algeng-chat-tts.oss-cn-wulanchabu.aliyuncs.
	// com/audio%2Ftts-20250703132525-FWXmUfIIYxsHqzWe.
	// wav?Expires=86401751520325\u0026OSSAccessKeyId=LTAI5tGLnRTkBjLuYPjNcKQ8\u0026Signature
	// =XmWc04oUYZzZhauiejZ9ts4QZlo%3D","status":2,"ced":""},"extra_info":{"audio_length":2937,
	// "audio_sample_rate":16000,"audio_size":95246,"bitrate":128000,"word_count":17,"invisible_character_ratio":0,
	// "usage_characters":33,"audio_format":"wav","audio_channel":1},"trace_id":"04b54740445fa90f6fcdf0b0f17dbb5b",
	//"base_resp":{"status_code":0,"status_msg":"success"}}

	if req.OutputFormat == "hex" {
		err = minimax.SaveHexAudioToFile(resp.Data.Audio, "/Users/<USER>/Downloads//minimax-test.wav")
		if err != nil {
			panic(err)
		}
	}
}

func TestMiniMaxSpeechToTextAsync(t *testing.T) {
	req := minimax.AsyncRequest{
		Model:         minimax.ModelSpeechHd01,
		Text:          "大家好，这是一段我的音色克隆的音频",
		LanguageBoost: "auto",
		VoiceSetting: &minimax.VoiceSetting{
			VoiceID: "wanglei-test",
			Speed:   1,
			Vol:     1,
			Pitch:   0,
		},
		AudioSetting: &minimax.AudioSetting{
			SampleRate: 16000,
			Channel:    1,
			Format:     "pcm",
		},
	}
	_, s, err := minimax.AsyncSpeechSubmit(Host, GroupID, ApiKey, &req)
	if err != nil {
		panic(err)
	}

	fmt.Println(s)

	// {"task_id":286593088905319,"task_token":"eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.
	// eyJhdWQiOiIxOTQwMDM3NTMyNDg1OTUxOTg2IiwiaXNzIjoibWluaW1heCIsInN1YiI6IjI4NjU5MzA4ODkwNTMxOSJ9.
	// muUtrhIXOHFfVT6Bokjb4_zg6dnw-yGXhVqN379hITWE_rSMzww4d4WhcdqbYRfjJf4CsqJ7mHNPoDEvw68QGcq-VwZ1Uv_Z8QirnLO
	// -w3RdkXz1phFLYZAW_FsAOIyEdKnh1vG1EGLdQQEFjeGxJFMHS5wNxYG73Z5oI8XtF162BhhNUSSsWtucmwGW393N_MunAPi0nDalrah
	//v6SyIZrG4PkGbWb11BTS6oB1SuHdAP6cepjg6CRZUrbZPsft1R_doDEVFXasRVe9NCeYvckBBRptVattJAk6OtG82UZCp0IDRB5_1vjxYD
	//mDX6VqqwsT92rs1o0JoB0z56RQLDA","file_id":286593088905319,"usage_characters":33,"base_resp":{"status_code":0,"status_msg":"success"}}
}

func TestMiniMaxSpeechToTextAsyncQuery(t *testing.T) {
	_, s, err := minimax.AsyncSpeechQuery(Host, GroupID, ApiKey, 286593088905319)
	if err != nil {
		panic(err)
	}

	fmt.Println(s)

	// {"status":"Success","task_id":286593088905319,"file_id":286593088905319,"base_resp":{"status_code":0,"status_msg":"success"}}
}

func TestMiniMaxFileRetrieve(t *testing.T) {
	_, s, err := minimax.RetrieveFile(Host, GroupID, ApiKey, 286593088905319)
	if err != nil {
		panic(err)
	}

	fmt.Println(s)

	// {"file":{"file_id":286593088905319,"bytes":0,"created_at":1751523038,"filename":"286593088905319.tar",
	// "purpose":"t2a_async","download_url":"https://minimax-algeng-chat-tts.oss-cn-wulanchabu.aliyuncs.
	// com/t2a_async%2Fprod%2Foutput%2F1940037532485951986_202507031410_286593088905319.
	// tar?Expires=1751555760&OSSAccessKeyId=LTAI5tGLnRTkBjLuYPjNcKQ8&Signature=hHzVnLLfHXRl1bSa%2BdYe1mjhnDU%3D"},
	// "base_resp":{"status_code":0,"status_msg":"success"}}
}
