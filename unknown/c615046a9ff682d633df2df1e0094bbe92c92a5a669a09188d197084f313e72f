package concurrentlimiter

import (
	"context"
	"fmt"
	"sync"
	"testing"
	"time"

	"github.com/redis/go-redis/v9"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func setupTest(t *testing.T) (*redis.Client, func()) {
	rdb := redis.NewClient(&redis.Options{
		Addr: "localhost:6777",
		DB:   0,
	})

	// 测试Redis连接
	ctx := context.Background()
	_, err := rdb.Ping(ctx).Result()
	require.NoError(t, err, "Redis连接失败")

	// 返回清理函数
	cleanup := func() {
		// 清理测试使用的所有键
		keys, err := rdb.Keys(ctx, "test:*").Result()
		require.NoError(t, err)
		if len(keys) > 0 {
			rdb.Del(ctx, keys...)
		}
		rdb.Close()
	}

	return rdb, cleanup
}

func TestNewConcurrentLimiter(t *testing.T) {
	rdb, cleanup := setupTest(t)
	defer cleanup()

	limiter := NewConcurrentLimiter(rdb, "test:limiter", 10,
		time.Second, time.Second*5, time.Second)

	assert.NotNil(t, limiter)
	assert.Equal(t, "test:limiter", limiter.mainKey)
	assert.Equal(t, "test:limiter:lock", limiter.lockKey)
	assert.Equal(t, int64(10), limiter.limit)
	assert.Equal(t, time.Second, limiter.quotaTTL)
	assert.Equal(t, time.Second*5, limiter.setTTL)
	assert.Equal(t, time.Second, limiter.lockTimeout)
}

func TestCheck(t *testing.T) {
	rdb, cleanup := setupTest(t)
	defer cleanup()

	ctx := context.Background()
	limiter := NewConcurrentLimiter(rdb, "test:check", 5,
		time.Second, time.Second*5, time.Second)

	t.Run("空集合时检查", func(t *testing.T) {
		count, err := limiter.Check(ctx)
		require.NoError(t, err)
		assert.Equal(t, int64(0), count)
	})

	t.Run("有配额时检查", func(t *testing.T) {
		// 添加一些测试配额
		quotaID1 := "test:check:1"
		quotaID2 := "test:check:2"
		pipe := rdb.Pipeline()
		pipe.Set(ctx, quotaID1, "1", time.Second)
		pipe.Set(ctx, quotaID2, "1", time.Second)
		pipe.SAdd(ctx, limiter.mainKey, quotaID1, quotaID2)
		_, err := pipe.Exec(ctx)
		require.NoError(t, err)

		count, err := limiter.Check(ctx)
		require.NoError(t, err)
		assert.Equal(t, int64(2), count)
	})

	t.Run("过期配额自动清理", func(t *testing.T) {
		quotaID := "test:check:expired"
		pipe := rdb.Pipeline()
		pipe.Set(ctx, quotaID, "1", time.Millisecond)
		pipe.SAdd(ctx, limiter.mainKey, quotaID)
		_, err := pipe.Exec(ctx)
		require.NoError(t, err)

		// 等待配额过期
		time.Sleep(time.Millisecond * 10)

		count, err := limiter.Check(ctx)
		require.NoError(t, err)
		assert.Equal(t, int64(2), count) // 只有前面添加的两个配额
	})
}

func TestAcquire(t *testing.T) {
	rdb, cleanup := setupTest(t)
	defer cleanup()

	ctx := context.Background()
	limiter := NewConcurrentLimiter(rdb, "test:acquire", 2,
		10*time.Second, 10*time.Second, time.Second)

	t.Run("基本获取", func(t *testing.T) {
		quotaID, err := limiter.Acquire(ctx, time.Second)
		require.NoError(t, err)
		assert.NotEmpty(t, quotaID)

		// 验证配额已创建
		exists, err := rdb.Exists(ctx, quotaID).Result()
		require.NoError(t, err)
		assert.Equal(t, int64(1), exists)

		// 验证set中包含配额
		isMember, err := rdb.SIsMember(ctx, limiter.mainKey, quotaID).Result()
		require.NoError(t, err)
		assert.True(t, isMember)
	})

	t.Run("达到限制时获取", func(t *testing.T) {
		// 再获取一个配额（现在应该有2个了）
		quotaID2, err := limiter.Acquire(ctx, time.Second)
		require.NoError(t, err)
		assert.NotEmpty(t, quotaID2)

		// 尝试获取第三个配额（应该超时）
		_, err = limiter.Acquire(ctx, time.Millisecond*100)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "timeout")
	})

	fmt.Printf("done\n")
}

func TestRelease(t *testing.T) {
	rdb, cleanup := setupTest(t)
	defer cleanup()

	ctx := context.Background()
	limiter := NewConcurrentLimiter(rdb, "test:release", 2,
		time.Second, time.Second*5, time.Second)

	t.Run("释放存在的配额", func(t *testing.T) {
		quotaID, err := limiter.Acquire(ctx, time.Second)
		require.NoError(t, err)

		err = limiter.Release(ctx, quotaID)
		require.NoError(t, err)

		// 验证配额已被删除
		exists, err := rdb.Exists(ctx, quotaID).Result()
		require.NoError(t, err)
		assert.Equal(t, int64(0), exists)

		// 验证set中不包含配额
		isMember, err := rdb.SIsMember(ctx, limiter.mainKey, quotaID).Result()
		require.NoError(t, err)
		assert.False(t, isMember)
	})

	t.Run("释放不存在的配额", func(t *testing.T) {
		err := limiter.Release(ctx, "test:release:nonexistent")
		require.NoError(t, err) // 应该不报错，静默失败
	})
}

func TestConcurrent(t *testing.T) {
	rdb, cleanup := setupTest(t)
	defer cleanup()

	ctx := context.Background()
	limiter := NewConcurrentLimiter(rdb, "test:concurrent", 5,
		5*time.Second, time.Second*5, 200*time.Millisecond)

	var wg sync.WaitGroup
	successCount := int64(0)
	workerCount := 5
	var mu sync.Mutex

	for i := 0; i < workerCount; i++ {
		wg.Add(1)
		go func(workerID int) {
			defer wg.Done()

			quotaID, err := limiter.Acquire(ctx, time.Second*5)
			if err == nil {
				mu.Lock()
				successCount++
				mu.Unlock()

				// 模拟一些工作
				time.Sleep(time.Second * 5)

				err = limiter.Release(ctx, quotaID)
				require.NoError(t, err)
			}
		}(i)
	}

	wg.Wait()

	// 验证只有5个worker能够成功获取配额
	assert.Equal(t, int64(5), successCount)

	// 验证最终没有遗留的配额
	count, err := limiter.Check(ctx)
	require.NoError(t, err)
	assert.Equal(t, int64(0), count)
}

func TestEdgeCases(t *testing.T) {
	rdb, cleanup := setupTest(t)
	defer cleanup()

	ctx := context.Background()
	limiter := NewConcurrentLimiter(rdb, "test:edge", 2,
		time.Second, time.Second*5, time.Second)

	t.Run("上下文取消", func(t *testing.T) {
		ctxWithCancel, cancel := context.WithCancel(ctx)
		// 先获取两个配额占满
		quotaID1, err := limiter.Acquire(ctx, time.Second)
		require.NoError(t, err)
		quotaID2, err := limiter.Acquire(ctx, time.Second)
		require.NoError(t, err)

		// 启动一个goroutine尝试获取第三个配额
		errChan := make(chan error)
		go func() {
			_, err := limiter.Acquire(ctxWithCancel, time.Second*2)
			errChan <- err
		}()

		// 取消上下文
		time.Sleep(time.Millisecond * 100)
		cancel()

		// 验证获取操作被取消
		err = <-errChan
		assert.ErrorIs(t, err, context.Canceled)

		// 清理
		limiter.Release(ctx, quotaID1)
		limiter.Release(ctx, quotaID2)
	})

	t.Run("Redis连接中断", func(t *testing.T) {
		// 创建一个指向错误地址的Redis客户端
		badRdb := redis.NewClient(&redis.Options{
			Addr: "localhost:6380", // 假设这个端口没有Redis服务
		})
		badLimiter := NewConcurrentLimiter(badRdb, "test:bad", 2,
			time.Second, time.Second*5, time.Second)

		_, err := badLimiter.Acquire(ctx, time.Second)
		assert.Error(t, err)

		err = badLimiter.Release(ctx, "any")
		assert.Error(t, err)

		_, err = badLimiter.Check(ctx)
		assert.Error(t, err)

		badRdb.Close()
	})
}

func TestQuotaTTL(t *testing.T) {
	rdb, cleanup := setupTest(t)
	defer cleanup()

	ctx := context.Background()
	limiter := NewConcurrentLimiter(rdb, "test:ttl", 2,
		time.Millisecond*100, time.Second*5, time.Second)

	t.Run("配额自动过期", func(t *testing.T) {
		quotaID, err := limiter.Acquire(ctx, time.Second)
		require.NoError(t, err)

		// 等待配额过期
		time.Sleep(time.Millisecond * 200)

		// 检查配额是否已过期
		exists, err := rdb.Exists(ctx, quotaID).Result()
		require.NoError(t, err)
		assert.Equal(t, int64(0), exists)

		// 验证可以获取新配额
		newQuotaID, err := limiter.Acquire(ctx, time.Second)
		require.NoError(t, err)
		assert.NotEmpty(t, newQuotaID)

		// 清理
		limiter.Release(ctx, newQuotaID)
	})
}

func TestSetTTL(t *testing.T) {
	rdb, cleanup := setupTest(t)
	defer cleanup()

	ctx := context.Background()
	limiter := NewConcurrentLimiter(rdb, "test:set_ttl", 2,
		5*time.Second, time.Second*5, time.Second)

	t.Run("Set自动过期", func(t *testing.T) {
		quotaID, err := limiter.Acquire(ctx, 3*time.Second)
		require.NoError(t, err)

		// 等待set过期
		time.Sleep(time.Second * 5)

		// 验证set已过期
		exists, err := rdb.Exists(ctx, limiter.mainKey).Result()
		require.NoError(t, err)
		assert.Equal(t, int64(0), exists)

		// 清理
		limiter.Release(ctx, quotaID)
	})
}
