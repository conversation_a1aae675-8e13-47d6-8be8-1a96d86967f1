package test

import (
	"acg-ai-go-common/utils"
	"context"
	"digital-human-micro-video-trans/thirdparty/morpheus"
	"fmt"
	"testing"
)

const (
	Host          string = "http://127.0.0.1:8080"
	UserId        string = "1145e61f-103e-430a-813b-72bd4fee0d42"
	CallbackUrl   string = ""
	ResourceLabel string = "2D_TRAIN"
	SceneLabel    string = "videoTranslate"
	InputVideo    string = "https://storage.googleapis.com/xiling_asia-southeast1_bucket/materials/system/ae135be0-eb60-43b2-8de2-f9138bd35c6b.mp4"
	Name          string = ""
)

func TestSubmit(t *testing.T) {
	taskId := "vf3-" + utils.RandStringRunes(16)
	task, err := morpheus.SubmitTask(context.Background(), Host, InputVideo, utils.RandStringRunes(10), taskId,
		UserId, 1080, 1920,
		<PERSON><PERSON><PERSON><PERSON>,
		<PERSON>Label, CallbackUrl)
	if err != nil {
		fmt.Printf("submit error: %v\n", err)
		return
	}
	fmt.Printf("submit task: %v\n", task)
}

func TestQuery(t *testing.T) {
	task, err := morpheus.QueryFigure(context.Background(), Host, UserId, 228335)
	if err != nil {
		fmt.Printf("query error: %v\n", err)
		return
	}
	fmt.Printf("query task: %v\n", task)
}
