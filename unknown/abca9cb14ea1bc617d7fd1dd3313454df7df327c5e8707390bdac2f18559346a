package videopipeline

import (
	"acg-ai-go-common/utils/httputil"
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"time"
)

func SubmitTask(logCtx context.Context, host, appId, appKey, userId string, width, height, duration int,
	figureId int, figureName,
	videoName, callbackUrl string, inputAudio string, transparent bool) (*ProgressResult, error) {
	var body []byte

	character := CharacterConfig{
		Figure: &Figure{
			Name: figureName,
		},
	}
	if figureId > 0 {
		character.Figure.Id = figureId
	}
	characterStr, err := json.Marshal(character)
	if err != nil {
		return nil, fmt.Errorf("marshal figure failed: %v", err)
	}

	retry := RetryPolicy{
		RetryEnabled:        true,
		Type:                "fixed",
		MaxRetryTimes:       4,
		ExpireInSeconds:     86400,
		RetryIntervalMillis: 30000,
	}
	render := RenderParams{
		Transparent: transparent,
	}

	text := fmt.Sprintf("<speak><audio src=\"%s\"/></speak>", inputAudio)

	req := SubmitTaskRequest{
		AppId:            appId,
		AppKey:           appKey,
		ResolutionWidth:  width,
		ResolutionHeight: height,
		RetryPolicy:      &retry,
		RenderParams:     &render,
		CallbackUrl:      callbackUrl,
		VideoName:        videoName,
		UserId:           userId,
		VideoDuration:    int64(duration),
		CharacterConfig:  string(characterStr),
		Texts:            []string{text},
	}

	body, err = json.Marshal(req)
	if err != nil {
		return nil, fmt.Errorf("marshal failed: %v", err)
	}

	headers := map[string]string{
		"Content-Type": "application/json",
	}

	tarUrl := fmt.Sprintf("%s/api/digitalhuman/video/v1/progress/submit", host)
	httpclient := httputil.NewRetryHTTPClient(15*time.Minute, 3)
	respBody, err := httpclient.DoRequest(logCtx, http.MethodPost, tarUrl, headers, bytes.NewReader(body))
	if err != nil {
		return nil, fmt.Errorf("http request failed: %v", err)
	}

	rsp := &TaskSubmitResponse{}
	err = json.Unmarshal(respBody, &rsp)
	if err != nil {
		return nil, fmt.Errorf("unmarshal failed: %v", err)
	}

	if !rsp.Success || rsp.Result == nil || len(rsp.Result) < 1 {
		return nil, fmt.Errorf("submit error: %s", string(respBody))
	}

	return rsp.Result[0], nil
}
