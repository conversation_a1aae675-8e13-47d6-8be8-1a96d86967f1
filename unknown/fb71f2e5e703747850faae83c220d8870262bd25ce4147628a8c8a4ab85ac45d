package videopipeline

import (
	"digital-human-micro-video-trans/thirdparty/sutils/jsonutils"
)

type FigureCutParams struct {
	CutXPercent            string `json:"cutXPercent,omitempty"`
	CutYPercent            string `json:"cutYPercent,omitempty"`
	CutWidthPercent        string `json:"cutWidthPercent,omitempty"`
	CutHeightPercent       string `json:"cutHeightPercent,omitempty"`
	WidthRatio             string `json:"widthRatio,omitempty"`
	PositionCenterXPercent string `json:"positionCenterXPercent,omitempty"`
	PositionBottomYPercent string `json:"positionBottomYPercent,omitempty"`
}

type SubtitlePolicy string

const (
	SubtitlePolicySRT SubtitlePolicy = "SRT"
)

type SubtitleParams struct {
	Enabled        bool           `json:"enabled,omitempty"`
	SubtitlePolicy SubtitlePolicy `json:"subtitlePolicy,omitempty"`
}

type Figure2dLiteCallbackRequest struct {
	ID            int64  `json:"id,omitempty"`            // 对应 Java 的 Long 类型
	Status        string `json:"status,omitempty"`        // 可为空
	ResultMessage string `json:"resultMessage,omitempty"` // 算法整体返回值
}

type ProgressResult struct {
	VideoId                 string `json:"videoId,omitempty"`
	Text                    string `json:"text,omitempty"`
	Status                  string `json:"status,omitempty"`
	DownloadUrl             string `json:"downloadUrl,omitempty"`
	AudioUrl                string `json:"audioUrl,omitempty"`
	ErrorCode               int    `json:"errorCode,omitempty"`
	FailureCause            string `json:"failureCause,omitempty"`
	ShowMessage             string `json:"showMessage,omitempty"`
	ScheduleTimes           int    `json:"scheduleTimes,omitempty"`
	SubmitTime              string `json:"submitTime,omitempty"`
	LastScheduleStartTime   string `json:"lastScheduleStartTime,omitempty"`
	LastScheduleFinishTime  string `json:"lastScheduleFinishTime,omitempty"`
	VideoGenerateFinishTime string `json:"videoGenerateFinishTime,omitempty"`
	VideoGenerateCostMillis int64  `json:"videoGenerateCostMillis,omitempty"`
	Thumbnail               string `json:"thumbnail,omitempty"`
	ResolutionWidth         int    `json:"resolutionWidth,omitempty"`
	ResolutionHeight        int    `json:"resolutionHeight,omitempty"`
	VideoName               string `json:"videoName,omitempty"`
	VideoDuration           int64  `json:"videoDuration,omitempty"`
	SubtitleFileUrl         string `json:"subtitleFileUrl,omitempty"`
}

type TtsParams struct {
	Speed       string            `json:"speed,omitempty"`
	Pitch       string            `json:"pitch,omitempty"`
	Volume      string            `json:"volume,omitempty"`
	Person      string            `json:"person,omitempty"`
	TtsId       string            `json:"ttsId,omitempty"`
	ExtraParams jsonutils.JSONMap `json:"extraParams,omitempty"`
}

type RetryPolicy struct {
	RetryEnabled        bool   `json:"retryEnabled,omitempty"`
	Type                string `json:"type,omitempty"`
	MaxRetryTimes       int    `json:"maxRetryTimes,omitempty"`
	ExpireInSeconds     int    `json:"expireInSeconds,omitempty"`
	RetryIntervalMillis int    `json:"retryIntervalMillis,omitempty"`
}

type RenderParams struct {
	FigureId            string `json:"figureId,omitempty"`
	CameraId            int    `json:"cameraId,omitempty"`
	X264ParamRcIBitrate int    `json:"x264_param_rc_i_bitrate,omitempty"`
	AutoAnimoji         bool   `json:"autoAnimoji,omitempty"`
	VideoMode           string `json:"videoMode,omitempty"`
	Transparent         bool   `json:"transparent,omitempty"`
}

type Figure struct {
	Id   int    `json:"id,omitempty"`
	Name string `json:"name,omitempty"`
	Type string `json:"type,omitempty"`
}

type CharacterConfig struct {
	Figure *Figure    `json:"figure,omitempty"`
	Tts    *TtsParams `json:"tts,omitempty"`
}

type SubmitTaskRequest struct {
	AppId              string           `json:"appId,omitempty"`
	AppKey             string           `json:"appKey,omitempty"`
	BackgroundImageUrl string           `json:"backgroundImageUrl,omitempty"`
	ResolutionWidth    int              `json:"resolutionWidth,omitempty"`
	ResolutionHeight   int              `json:"resolutionHeight,omitempty"`
	ProjectId          string           `json:"projectId,omitempty"`
	TtsParams          *TtsParams       `json:"ttsParams,omitempty"`
	FigureCutParams    *FigureCutParams `json:"figureCutParams,omitempty"`
	Preset             string           `json:"preset,omitempty"`
	RetryPolicy        *RetryPolicy     `json:"retryPolicy,omitempty"`
	RenderParams       *RenderParams    `json:"renderParams,omitempty"`
	Selectors          interface{}      `json:"selectors,omitempty"`
	CallbackUrl        string           `json:"callbackUrl,omitempty"`
	CharacterConfigId  string           `json:"characterConfigId,omitempty"`
	CharacterConfig    string           `json:"characterConfig,omitempty"`
	VideoName          string           `json:"videoName,omitempty"`
	VideoDuration      int64            `json:"videoDuration,omitempty"`
	SubtitleParams     *SubtitleParams  `json:"subtitleParams,omitempty"`

	Texts  []string `json:"texts,omitempty"`
	UserId string   `json:"userId,omitempty"`
}

type TaskSubmitResponse struct {
	Code    int  `json:"code"`
	Success bool `json:"success"`
	Message struct {
		Global string `json:"global"`
	} `json:"message"`
	Result []*ProgressResult `json:"result,omitempty"`
}

type TaskQueryResponse struct {
	Code    int  `json:"code"`
	Success bool `json:"success"`
	Message struct {
		Global string `json:"global"`
	} `json:"message"`
	Result *ProgressResult `json:"result,omitempty"`
}
