package jsonutils

import (
	"database/sql/driver"
	"encoding/json"
	"errors"
)

type JSONMap map[string]interface{}

func (jm JSONMap) Value() (driver.Value, error) {
	if jm == nil {
		return nil, nil
	}
	b, err := json.Marshal(jm)
	if err != nil {
		return nil, err
	}
	return string(b), nil
}

func (jm *JSONMap) Scan(input interface{}) error {
	if input == nil {
		return nil
	}
	bytes, ok := input.([]byte)
	if !ok {
		return errors.New("type assertion to []byte failed")
	}
	return json.Unmarshal(bytes, jm)
}

func StructToJSON(v interface{}) (string, error) {
	bytes, err := json.Marshal(v)
	if err != nil {
		return "", err
	}
	return string(bytes), nil
}

func JSONToStruct(jsonString string, v interface{}) error {
	return json.Unmarshal([]byte(jsonString), v)
}

func GetStringFromJson(in map[string]interface{}, key string) (string, error) {
	if ret, ok := in[key]; ok {
		if str, isString := ret.(string); isString {
			return str, nil
		} else {
			return "", errors.New("key is not string type")
		}
	}
	return "", errors.New("key not found")
}

func StructToJsonMap(v interface{}) (map[string]interface{}, error) {
	data, err := json.Marshal(v)
	if err != nil {
		return nil, err
	}

	var result map[string]interface{}
	err = json.Unmarshal(data, &result)
	if err != nil {
		return nil, err
	}

	return result, nil
}

func JsonMapToStruct(data map[string]interface{}, result interface{}) error {
	jsonData, err := json.Marshal(data)
	if err != nil {
		return err
	}

	err = json.Unmarshal(jsonData, &result)
	if err != nil {
		return err
	}

	return nil
}
