package helper

import (
	"acg-ai-go-common/utils"
	"context"
	"digital-human-micro-video-trans/thirdparty/ffmpeg"
	"digital-human-micro-video-trans/thirdparty/sutils/fileutils"
	"fmt"
	"os"
	"os/exec"
	"path/filepath"
)

func isStandardWav(path string, codec string, sampleRate int, channels int) (bool, error) {
	info, err := ffmpeg.GetAudioInfo(path)
	if err != nil {
		return false, fmt.Errorf("ffprobe: %v", err)
	}

	if info.SampleRate != sampleRate || info.CodecName != codec || info.Channels != channels {
		return false, nil
	}

	return true, nil
}

// CombineWavInOne 将给出的wav文件进行组合输出到一个文件中，如果时间不足 minDuration则补齐，如果超过了，则抛弃超出的文件
func CombineWavInOne(logCtx context.Context, list []string, outputPath string, tempPath string, audioCodec string,
	channels int, sampleRate int, minDuration, maxDuration float64, maxSize int64) error {

	var totalSize int64
	var totalDuration float64

	// 检查并转换所有输入文件为标准格式
	var processedFiles []string
	totalSize = 0
	totalDuration = 0
	needCut := false
	for i, path := range list {
		// 检查格式
		infoAudio, err := ffmpeg.GetAudioInfo(path)
		if err != nil {
			return fmt.Errorf("ffprobe: %v", err)
		}

		// 转换格式
		finalFile := path
		if infoAudio.SampleRate != sampleRate || infoAudio.CodecName != audioCodec || infoAudio.Channels != channels {
			finalFile = fmt.Sprintf("%s/%d-%s.wav", tempPath, i, utils.RandStringRunes(4))
			err := ffmpeg.ExtractAudio(logCtx, path, finalFile, "pcm_s16le", 1, 16000)
			if err != nil {
				return fmt.Errorf("ffmpeg convert audio: %v", err)
			}
		}

		// 检查时间和大小
		infoFile, err := os.Stat(finalFile)
		if err != nil {
			return fmt.Errorf("open file: %v", err)
		}

		// 大小超了
		if totalSize+infoFile.Size() > maxSize {
			if len(processedFiles) > 0 {
				break
			} else {
				// 第一个文件就过大了，那就截断
				needCut = true
			}
		}

		// 时长超了
		if totalDuration+infoAudio.Duration > maxDuration {
			if len(processedFiles) > 0 {
				break
			} else {
				// 第一个文件就过大了，那就截断
				needCut = true
			}
		}

		processedFiles = append(processedFiles, path)
		totalSize += infoFile.Size()
		totalDuration += infoAudio.Duration
		if needCut {
			break
		}
	}

	tmpListFile := fmt.Sprintf("%s/input-files-%s.txt", tempPath, utils.RandStringRunes(4))
	defer os.Remove(tmpListFile)

	inputTxt := ""
	for _, file := range processedFiles {
		absPath, _ := filepath.Abs(file)
		inputTxt += fmt.Sprintf("file '%s'\n", absPath)
	}
	if err := os.WriteFile(tmpListFile, []byte(inputTxt), 0644); err != nil {
		return fmt.Errorf("write list.txt: %v", err)
	}

	// 拼接
	intermediate := fmt.Sprintf("%s/concat-temp-%s.wav", tempPath, utils.RandStringRunes(4))
	defer os.Remove(intermediate)

	cmd1 := exec.Command("ffmpeg", "-f", "concat", "-safe", "0", "-i", tmpListFile, "-c", "copy", "-y", intermediate)
	cmd1.Stderr = os.Stderr
	if err := cmd1.Run(); err != nil {
		return fmt.Errorf("concat: %v", err)
	}

	if needCut || totalDuration < minDuration {
		// 需要补充的话，计算一下时间
		padSeconds := 0.0
		if totalDuration < minDuration {
			padSeconds = minDuration - totalDuration
		}

		// 需要截断的话，计算一下截断的时间
		finalDuration := totalDuration + padSeconds
		if finalDuration > maxDuration {
			finalDuration = maxDuration
		}

		cmd2 := exec.Command(
			"ffmpeg",
			"-i", intermediate,
			"-af", fmt.Sprintf("apad=pad_dur=%f", padSeconds),
			"-t", fmt.Sprintf("%f", finalDuration),
			"-y", outputPath,
		)
		cmd2.Stderr = os.Stderr
		if err := cmd2.Run(); err != nil {
			return fmt.Errorf("ffmpeg pad: %v", err)
		}
	} else {
		err := fileutils.CopyFile(intermediate, outputPath)
		if err != nil {
			return fmt.Errorf("copy: %v", err)
		}
	}

	return nil
}
