package google

import (
	"acg-ai-go-common/utils"
	"context"
	"digital-human-micro-video-trans/thirdparty/google/cloud_speech2text"
	"encoding/json"
	"fmt"
	"testing"
)

var (
	InputUrl     string = "gs://xiling_us_central1_bucket/rd-test/vocals_16k.wav"
	LangCode     string = "zh-CN"
	AltLanguages        = []string{}
)

func TestStartSpeech2Text(t *testing.T) {
	logCtx := context.WithValue(context.TODO(), utils.CtxKeyLogID, utils.RandStringRunes(10))
	result, err := cloud_speech2text.TranscribeLongAudio(logCtx, CredentialFile, LangCode, AltLanguages, InputUrl,
		16000, 1, false, false)
	if err != nil {
		fmt.Printf("recognize failed: %v\n", err)
		return
	}

	b, _ := json.MarshalIndent(result, "", "  ")
	fmt.Printf("result: %v\n", string(b))
}
