package schedeuler

import (
	"acg-ai-go-common/gomysql"
	"acg-ai-go-common/logger"
	"acg-ai-go-common/storage"
	"acg-ai-go-common/utils"
	"acg-ai-go-common/utils/redisproxy"
	"bytes"
	"context"
	"digital-human-micro-video-trans/beans/enums"
	"digital-human-micro-video-trans/beans/model"
	"digital-human-micro-video-trans/beans/proto"
	"digital-human-micro-video-trans/conf"
	"digital-human-micro-video-trans/handler/services/helper"
	"digital-human-micro-video-trans/handler/services/subtitle"
	"digital-human-micro-video-trans/thirdparty/elevenlabs/speech2text"
	"digital-human-micro-video-trans/thirdparty/elevenlabs/voice_clone"
	"digital-human-micro-video-trans/thirdparty/ffmpeg"
	"digital-human-micro-video-trans/thirdparty/google/gemini"
	"digital-human-micro-video-trans/thirdparty/minimax"
	"digital-human-micro-video-trans/thirdparty/sutils"
	"digital-human-micro-video-trans/thirdparty/sutils/fileutils"
	"digital-human-micro-video-trans/thirdparty/sutils/jsonutils"
	"digital-human-micro-video-trans/thirdparty/sutils/redislock"
	"encoding/base64"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"gorm.io/gorm"
	"math"
	"os"
	"os/exec"
	"path/filepath"
	"sort"
	"strings"
	"time"
)

func (p *TaskScheduler) HandleVoiceClone() {
	logger.Log.Info("start handle voice clone task")

	taskList, err := (&model.VideoTranslateTask{}).GetTasksWithStatus(gomysql.DB, enums.VoiceClone)
	if err != nil {
		logger.Log.Errorf("get task with status: %s, err: %v", enums.VoiceClone, err)
		return
	}

	if len(taskList) < 1 {
		logger.Log.Infof("no task with status: %s need schedule", enums.VoiceClone)
		return
	}

	scheduleCount := 0
	for _, item := range taskList {
		err := p.VoiceClone(item)
		if err != nil {
			logger.Log.Errorf("schedule task: %s failed, cause: %v", item.TaskId, err)
		}
		scheduleCount++
		if scheduleCount >= conf.LocalConfig.ScheduleSettings.MaxScheduleSize {
			logger.Log.Infof("scheduled reach max control size: %d/%d", scheduleCount,
				conf.LocalConfig.ScheduleSettings.MaxScheduleSize)
			break
		}
	}
	logger.Log.Infof("scheduled task: %d with status: %s", scheduleCount, enums.VoiceClone)
}

func (p *TaskScheduler) VoiceClone(task *model.VideoTranslateTask) error {
	logCtx := context.WithValue(context.TODO(), utils.CtxKeyLogID, task.TaskId)
	redisProxy := redisproxy.GetRedisProxy()

	// 加锁
	redisLockKey := fmt.Sprintf(TaskScheduleLockFmt, sutils.GetNameByRunEnv(), task.TaskId)
	redisLock := redislock.NewRedisLock(redisProxy.Rdb, redisLockKey, TaskScheduleLockExpireTime)
	ok, err := redisLock.Lock(context.Background())
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"PreProcessing Lock key: %s error: %+v\n", redisLockKey, err)
		return fmt.Errorf("get redis lock err: %v", err)
	} else if !ok {
		logger.Log.Warnf(utils.MMark(logCtx) + "redis key locked by other instance, skip it.")
		return nil
	}
	defer redisLock.Unlock(context.Background())

	// 重新查询
	item, err := task.GetTasksWithTaskId(gomysql.DB, task.TaskId)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"refresh task err: %v", err)
		return fmt.Errorf("get refresh task err: %v", err)
	}

	// 状态变了，就不用处理了
	if item.Status != enums.VoiceClone {
		return fmt.Errorf("task status nolonger %s", enums.VoiceClone)
	}

	needRetry := 1

	progress := proto.NewProgress()
	var temp proto.Progress
	if err := jsonutils.JsonMapToStruct(item.Progress, &temp); err == nil {
		progress = &temp
	}

	var config proto.SubmitConfig
	if err := jsonutils.JsonMapToStruct(item.Config, &config); err != nil {
		return fmt.Errorf("parse submit config failed: %v", err)
	}

	//start := time.Now()
	err = gomysql.DB.Transaction(func(tx *gorm.DB) error {
		cache, err := GetTaskCache(item)
		if err != nil {
			return fmt.Errorf("parse task cache failed: %v", err)
		}

		if len(item.SubStatus) < 1 {
			item.SubStatus = enums.VoiceCloneSubmit
		}

		tStart := time.Now()
		var subErr error
		switch item.SubStatus {
		case enums.VoiceCloneSubmit:
			{
				// 提交音频克隆
				needRetry, subErr = p.doVoiceCloneSubmit(logCtx, tx, item, &config, progress, cache)
				break
			}
		case enums.VoiceCloneWait:
			{
				// 查询音频克隆
				needRetry, subErr = p.doVoiceCloneCheck(logCtx, tx, item, &config, progress, cache)
				break
			}
		case enums.VoiceCloneTts:
			{
				// 合成音频
				needRetry, subErr = p.doVoiceCloneTts(logCtx, tx, item, &config, progress, cache)
				break
			}
		case enums.VoiceCloneIntegration:
			{
				// 音频重整
				needRetry, subErr = p.doTtsIntegration(logCtx, item, &config, progress, cache)
				break
			}
		case enums.VoiceCloneSubtitle:
			{
				// 制作字幕
				needRetry, subErr = p.doTtsSubtitle(logCtx, item, &config, progress, cache)
				break
			}
		}
		timeCost := float64(time.Since(tStart).Milliseconds()) / 1000.0
		logger.Log.Infof(utils.MMark(logCtx)+"elapsed with: %.3fs", timeCost)
		if subErr != nil {
			return subErr
		}

		// 保存状态
		jsConfig, err := jsonutils.StructToJsonMap(progress)
		if err != nil {
			return fmt.Errorf("save progress detal failed: %v", err)
		}
		item.Progress = jsConfig

		// 更新
		err = item.Update(tx)
		if err != nil {
			return fmt.Errorf("update to: %s failed: %v", item.Status, err)
		}

		return nil
	})

	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"schedule failed, err: %+v\n", err)

		// 更新任务状态
		if needRetry == 0 {
			// 把次数置为0
			item.RetryAttempts = 0
		}
		item.Status = enums.Failed
		item.Message = fmt.Sprintf("%s", err.Error())

		// 也需要保存状态，有些部分不需要重复重试
		jsConfig, err := jsonutils.StructToJsonMap(progress)
		if err != nil {
			return fmt.Errorf("save progress detail failed: %v", err)
		}
		item.Progress = jsConfig

		err = item.Update(gomysql.DB)
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+"update item failed: %+v, err: %+v\n", item, err)
			return err
		}

		return fmt.Errorf("schedule task err: %v", err)
	}
	return nil
}

func (p *TaskScheduler) doVoiceCloneSubmit(logCtx context.Context, tx *gorm.DB, item *model.VideoTranslateTask,
	config *proto.SubmitConfig, progress *proto.Progress, cache *CachePath) (int, error) {
	logger.Log.Infof(utils.MMark(logCtx) + "doVoiceCloneSubmit")

	// 限流，已经有多个克隆音色了
	list, err := item.GetTasksWithSubStatus(tx, enums.VoiceCloneWait)
	if err != nil {
		return 1, fmt.Errorf("query voice cloning task failed: %v", err)
	}
	if len(list) > conf.LocalConfig.ElevenLabsSettings.ConcurrencyLimit {
		logger.Log.Warnf(utils.MMark(logCtx)+"too many voice cloning task: %d, max: %d, try later.", len(list),
			conf.LocalConfig.ElevenLabsSettings.ConcurrencyLimit)
		return 1, nil
	}

	data, err := os.ReadFile(cache.TranslateResult)
	if err != nil {
		return 1, fmt.Errorf("read file error: %w", err)
	}
	var speakers []*speech2text.Speaker
	if err := json.Unmarshal(data, &speakers); err != nil {
		return 1, fmt.Errorf("json unmarshal error: %w", err)
	}

	for i, speaker := range speakers {
		var files []string
		for i, sentence := range speaker.Sentences {
			if i < speaker.AudioNum && len(files) < 24 && !sentence.IsAdd {
				files = append(files, sentence.AudioFile)
			}
		}

		if len(files) < 1 {
			return 1, fmt.Errorf("no voice samples found")
		}

		detail := proto.VoiceCloneDetail{
			Engine:   conf.LocalConfig.VoiceSettings.CloneEngine,
			Status:   enums.ProgressSubmit,
			CreateAt: time.Now(),
			UpdateAt: time.Now(),
		}

		cloneVoice := cache.GetTtsSpeakerFileName(i)
		err := helper.CombineWavInOne(logCtx, files, cloneVoice, cache.Temp, "pcm_s16le", 1, 16000,
			conf.LocalConfig.VoiceSettings.SampleMinDuration, conf.LocalConfig.VoiceSettings.SampleMaxDuration,
			conf.LocalConfig.VoiceSettings.SampleMaxSize*1024)
		if err != nil {
			return 1, fmt.Errorf("combine voice clone samples: %v", err)
		}
		detail.Sample = cloneVoice

		if conf.LocalConfig.VoiceSettings.CloneEngine == "elevenlabs" {
			// 使用 elevenlabs 音色克隆
			req := voice_clone.AddVoiceRequest{
				Name:                  fmt.Sprintf("%s-spk-%d", item.TaskId, i),
				Description:           fmt.Sprintf("video translate`s temp voice: %s", item.TaskId),
				RemoveBackgroundNoise: true,
			}
			voice, err := voice_clone.AddVoice(logCtx, conf.LocalConfig.ElevenLabsSettings.ApiKey, []string{cloneVoice}, req)
			if err != nil {
				return 1, fmt.Errorf("voice[%d] clone failed: %v", i, err)
			}
			body, _ := json.Marshal(voice)
			logger.Log.Infof(utils.MMark(logCtx)+"[%s]voice clone: %s", conf.LocalConfig.VoiceSettings.CloneEngine,
				string(body))

			detail.VoiceId = voice.VoiceID
			logger.Log.Infof(utils.MMark(logCtx)+"[%d]speaker: %s, add voice clone with id: %s", i, speaker.Id,
				voice.VoiceID)
		} else if conf.LocalConfig.VoiceSettings.CloneEngine == "minimax" {
			// 使用 minimax 克隆
			miniFile, s, err := minimax.UploadFile(conf.LocalConfig.MinimaxSettings.Host,
				conf.LocalConfig.MinimaxSettings.GroupId,
				conf.LocalConfig.MinimaxSettings.ApiKey, cloneVoice)
			if err != nil {
				return 1, fmt.Errorf("minimax upload: %v, %s", err, s)
			}
			if miniFile.BaseResp.StatusCode != 0 {
				logger.Log.Errorf(utils.MMark(logCtx)+"[%s]upload voice failed: [%d] %s",
					conf.LocalConfig.VoiceSettings.CloneEngine, miniFile.BaseResp.StatusCode, miniFile.BaseResp.StatusMsg)
				return 0, fmt.Errorf("voice clone upload failed: [%d] %s", miniFile.BaseResp.StatusCode, miniFile.BaseResp.StatusMsg)
			}
			logger.Log.Infof(utils.MMark(logCtx)+"[%s]upload voice with file id: %d",
				conf.LocalConfig.VoiceSettings.CloneEngine,
				miniFile.File.FileId)

			req := minimax.VoiceCloneRequest{
				FileID:                  miniFile.File.FileId,
				VoiceID:                 fmt.Sprintf("%s-spk-%d-%s", item.TaskId, i, utils.RandStringRunes(4)),
				NeedNoiseReduction:      true,
				NeedVolumeNormalization: true,
			}
			miniVoice, s, err := minimax.DoVoiceClone(conf.LocalConfig.MinimaxSettings.Host,
				conf.LocalConfig.MinimaxSettings.GroupId,
				conf.LocalConfig.MinimaxSettings.ApiKey, req)
			if miniVoice.BaseResp.StatusCode != 0 {
				logger.Log.Errorf(utils.MMark(logCtx)+"[%s]submit voice clone failed: [%d] %s",
					conf.LocalConfig.VoiceSettings.CloneEngine, miniVoice.BaseResp.StatusCode, miniVoice.BaseResp.StatusMsg)
				return 0, fmt.Errorf("voice clone submit failed: [%d] %s", miniVoice.BaseResp.StatusCode, miniVoice.BaseResp.StatusMsg)
			}
			if miniVoice.InputSensitive {
				logger.Log.Errorf(utils.MMark(logCtx)+"[%s]submit voice clone with sensitive: %d",
					conf.LocalConfig.VoiceSettings.CloneEngine, miniVoice.InputSensitiveType)
				return 0, fmt.Errorf("voice clone sensitive: %d", miniVoice.InputSensitiveType)
			}
			detail.VoiceId = req.VoiceID
			logger.Log.Infof(utils.MMark(logCtx)+"[%s]submit voice clone: %s",
				conf.LocalConfig.VoiceSettings.CloneEngine, s)
		}

		progress.Voice = append(progress.Voice, detail)
	}

	jsonBytes, err := json.Marshal(speakers)
	if err != nil {
		return 1, fmt.Errorf("marshal failed: %v", err)
	}

	err = os.WriteFile(cache.TtsResult, jsonBytes, 0644)
	if err != nil {
		return 1, fmt.Errorf("write to out file failed: %v", err)
	}

	item.SubStatus = enums.VoiceCloneWait
	return 1, nil
}

func (p *TaskScheduler) doVoiceCloneCheck(logCtx context.Context, tx *gorm.DB, item *model.VideoTranslateTask,
	config *proto.SubmitConfig, progress *proto.Progress, cache *CachePath) (int, error) {
	logger.Log.Infof(utils.MMark(logCtx) + "doVoiceCloneCheck")

	for i, _ := range progress.Voice {
		if progress.Voice[i].Engine == "elevenlabs" {
			voice, err := voice_clone.GetVoiceInfo(logCtx, conf.LocalConfig.ElevenLabsSettings.ApiKey,
				progress.Voice[i].VoiceId)

			body, _ := json.Marshal(voice)
			logger.Log.Infof(utils.MMark(logCtx)+"voice clone check: %s", string(body))

			if err != nil {
				return 1, fmt.Errorf("voice check failed: %v", err)
			}
		} else if progress.Voice[i].Engine == "minimax" {
			voices, _, err := minimax.GetVoiceList(conf.LocalConfig.MinimaxSettings.Host,
				conf.LocalConfig.MinimaxSettings.ApiKey, "voice_cloning")

			if err != nil {
				return 1, fmt.Errorf("voice check failed: %v", err)
			}

			logger.Log.Infof(utils.MMark(logCtx)+"voice clone check: %d", len(voices.VoiceCloning))
		}

		progress.Voice[i].UpdateAt = time.Now()
	}
	item.SubStatus = enums.VoiceCloneTts
	return 1, nil
}

func (p *TaskScheduler) doVoiceCloneTts(logCtx context.Context, tx *gorm.DB, item *model.VideoTranslateTask,
	config *proto.SubmitConfig, progress *proto.Progress, cache *CachePath) (int, error) {
	logger.Log.Infof(utils.MMark(logCtx) + "doVoiceCloneTts")
	// 限流
	lock, err := p.TtsLimiter.Acquire(logCtx, 5*time.Second)
	if err != nil {
		logger.Log.Warnf(utils.MMark(logCtx)+"to many tts request currently, retry later, limiter err: %v", err)
		return 1, nil
	}
	defer p.TtsLimiter.Release(logCtx, lock)

	data, err := os.ReadFile(cache.TtsResult)
	if err != nil {
		return 1, fmt.Errorf("read file error: %w", err)
	}
	var result []*speech2text.Speaker
	if err := json.Unmarshal(data, &result); err != nil {
		return 1, fmt.Errorf("json unmarshal error: %w", err)
	}

	// 检查视频参数
	videoInfo, err := ffmpeg.GetVideoInfo(cache.InputFile)
	if err != nil {
		return 1, fmt.Errorf("get video info failed: %v", err)
	}
	logger.Log.Infof(utils.MMark(logCtx)+"get video time: %v", videoInfo.Duration)

	// 计算一下音频时间适应的阈值
	tarDiffScale := -1.0
	if config.EnableDynamicDuration {
		tarDiffScale = (videoInfo.Duration + conf.LocalConfig.VoiceTuningSettings.MaxThreshold) / videoInfo.Duration
		// 给一些限制
		tarDiffScale -= 1.0
		if tarDiffScale > 0.5 {
			tarDiffScale = 0.2
		}
	}

	// 合成文本
	tarSentences := result
	for i, _ := range tarSentences {
		for w, _ := range tarSentences[i].Sentences {
			// 没有设置的情况下，默认0.3s
			tarAudioDiff := conf.LocalConfig.VoiceTuningSettings.Threshold
			if tarDiffScale > 0 {
				tarAudioDiff = (tarSentences[i].Sentences[w].End - tarSentences[i].Sentences[w].Start) * tarDiffScale
				if tarAudioDiff < conf.LocalConfig.VoiceTuningSettings.Threshold {
					tarAudioDiff = conf.LocalConfig.VoiceTuningSettings.Threshold
				}
			}
			// 迭代以适应TTS长度
			tts, err := p.DoTtsAutoFit(logCtx, progress.Voice[i].Engine, progress.Voice[i].VoiceId,
				tarSentences[i].Sentences[w],
				tarSentences[i].Lang, tarAudioDiff, conf.LocalConfig.VoiceTuningSettings.MaxStep, cache)
			if err != nil {
				return 1, fmt.Errorf("tts for [%s] failed: %v", tarSentences[i].Sentences[w].NewText, err)
			}

			tarSentences[i].Sentences[w].Alignment = tts.Alignment
			tarSentences[i].Sentences[w].TtStart = tts.Alignment.CharacterStartTimesSeconds[0]

			last := len(tts.Alignment.CharacterEndTimesSeconds) - 1
			tarSentences[i].Sentences[w].TtsEnd = tts.Alignment.CharacterEndTimesSeconds[last]

			filePath := cache.GetTtsPartName(i, w)
			if progress.Voice[i].Engine == "elevenlabs" {
				pcmData, err := base64.StdEncoding.DecodeString(tts.AudioBase64)
				if err != nil {
					return 1, fmt.Errorf("base64 decode failed: %w", err)
				}

				if err := fileutils.WritePCMAsWav(pcmData, filePath, 16000, 1); err != nil {
					return 1, fmt.Errorf("write tts wav file failed: %w", err)
				}
			} else if progress.Voice[i].Engine == "minimax" {
				audioBytes, err := hex.DecodeString(tts.AudioBase64)
				if err != nil {
					return 1, fmt.Errorf("hex decode: %v", err)
				}

				err = os.WriteFile(filePath, audioBytes, 0644)
				if err != nil {
					return 1, fmt.Errorf("write wav: %v", err)
				}
			}
			tarSentences[i].Sentences[w].TtsFile = filePath

			fileUrl, err := storage.RetryUploadFromFile(logCtx, cache.BosRoot, filePath, ".wav")
			if err != nil || len(fileUrl) < 1 {
				return 1, fmt.Errorf("upload tts failed: %v", err)
			}
			tarSentences[i].Sentences[w].TtsUrl = fileUrl

			logger.Log.Infof(utils.MMark(logCtx)+"voice clone tts: %s -> %s", tarSentences[i].Sentences[w].NewText, fileUrl)
		}
		progress.Voice[i].UpdateAt = time.Now()
		progress.Voice[i].Status = enums.ProgressSuccess
		progress.Voice[i].Duration = int(progress.Voice[i].UpdateAt.Sub(progress.Voice[i].CreateAt).Seconds())
	}

	jsonBytes, err := json.Marshal(tarSentences)
	if err != nil {
		return 1, fmt.Errorf("marshal failed: %v", err)
	}

	err = os.WriteFile(cache.TtsResult, jsonBytes, 0644)
	if err != nil {
		return 1, fmt.Errorf("write to out file failed: %v", err)
	}

	// 上传一份到云端
	fileUrl, err := storage.RetryUploadFromFile(logCtx, cache.BosRoot, cache.TtsResult, cache.TtsResultSuffix)
	if err != nil || len(fileUrl) < 1 {
		return 1, fmt.Errorf("upload translate failed: %v", err)
	}

	item.SubStatus = enums.VoiceCloneIntegration
	return 1, nil
}

func (p *TaskScheduler) doTtsIntegration(logCtx context.Context, item *model.VideoTranslateTask,
	config *proto.SubmitConfig, progress *proto.Progress, cache *CachePath) (int, error) {
	logger.Log.Infof(utils.MMark(logCtx) + "doTtsIntegration")
	data, err := os.ReadFile(cache.TtsResult)
	if err != nil {
		return 1, fmt.Errorf("read file error: %w", err)
	}
	var result []*speech2text.Speaker
	var tarSentences []*speech2text.Speaker
	if err := json.Unmarshal(data, &result); err != nil {
		return 1, fmt.Errorf("json unmarshal error: %w", err)
	}
	if err := json.Unmarshal(data, &tarSentences); err != nil {
		return 1, fmt.Errorf("json unmarshal error: %w", err)
	}

	// 检查视频参数
	videoInfo, err := ffmpeg.GetVideoInfo(cache.InputFile)
	if err != nil {
		return 1, fmt.Errorf("get video info failed: %v", err)
	}
	logger.Log.Infof(utils.MMark(logCtx)+"get video time: %v", videoInfo.Duration)

	// 音频整合
	var mixAudio []string

	for i, _ := range tarSentences {
		var pcm []byte
		if config.EnableDynamicDuration {
			pcm, err = p.CombineSentencesToPcm(tarSentences[i].Sentences, 16000, 1, videoInfo.Duration)
		} else {
			pcm, err = p.CombineSentencesToPcmWithScale(tarSentences[i].Sentences, 16000, 1, videoInfo.Duration)
		}

		if err != nil {
			return 1, fmt.Errorf("combine pcm failed: %v", err)
		}

		filePath := cache.GetTtsFileName(i)
		err = fileutils.WritePCMAsWav(pcm, filePath, 16000, 1)
		if err != nil {
			return 1, fmt.Errorf("write wav failed: %v", err)
		}

		// 需要混合
		mixAudio = append(mixAudio, filePath)

		// 上传一份到云端
		fileUrl, err := storage.RetryUploadFromFile(logCtx, cache.BosRoot, filePath, cache.TtsAudioSuffix)
		if err != nil || len(fileUrl) < 1 {
			return 1, fmt.Errorf("upload tts failed: %v", err)
		}

		logger.Log.Infof(utils.MMark(logCtx)+"upload tts file: %s", fileUrl)

		progress.Voice[i].VoiceUrl = fileUrl
		progress.Voice[i].UpdateAt = time.Now()
		progress.Voice[i].Status = enums.ProgressSuccess
	}

	// 如果有多个讲话人音轨，也需要合并
	if len(mixAudio) > 1 {
		err := ffmpeg.MixMultipleAudioFiles(logCtx, cache.Temp, mixAudio, cache.TtsAudio, 16000, 1)
		if err != nil {
			return 1, fmt.Errorf("mix audio files failed: %v", err)
		}
		// 上传一份到云端
		fileUrl, err := storage.RetryUploadFromFile(logCtx, cache.BosRoot, cache.TtsAudio, cache.TtsAudioSuffix)
		if err != nil || len(fileUrl) < 1 {
			return 1, fmt.Errorf("upload tts failed: %v", err)
		}

		progress.VideoTransfer.Video.AudioUrl = fileUrl
	} else {
		if len(progress.Voice) > 0 {
			err := fileutils.CopyFile(mixAudio[0], cache.TtsAudio)
			if err != nil {
				return 1, fmt.Errorf("copy tts audio to output failed: %v", err)
			}
			logger.Log.Infof(utils.MMark(logCtx)+"copy tts audio: %s", cache.TtsAudio)

			progress.VideoTransfer.Video.AudioUrl = progress.Voice[0].VoiceUrl
		} else {
			return 1, fmt.Errorf("no voice")
		}
	}

	// 把人声和底板视频合并
	err = ffmpeg.MergeAudioVideo(logCtx, cache.InputVideo, cache.VocalAudio, cache.TemplateVocalVideo, "aac", 1,
		16000)
	if err != nil {
		return 1, fmt.Errorf("ffmpeg merge vocal failed: %v", err)
	}
	logger.Log.Infof(utils.MMark(logCtx)+"merge vocal to template video: %s", cache.TemplateVocalVideo)

	// 如果允许动态调节时长的话，那么底板视频和BGM都需要处理一遍
	if config.EnableDynamicDuration {
		// 处理底板视频，音频也一起处理
		err = RunFFmpegOneShot(logCtx, cache.TemplateVocalVideo, cache.TemplateVideo, result[0].Sentences,
			tarSentences[0].Sentences,
			videoInfo.Duration, cache)
		if err != nil {
			return 1, fmt.Errorf("ffmpeg blend failed: %v", err)
		}
		logger.Log.Infof(utils.MMark(logCtx)+"process template video: %s", cache.TemplateVideo)

		// 按照同样的逻辑处理一遍BGM
		err = RunFFmpegAudioOnly(logCtx, cache.BgmAudio, cache.BgmAudioOut, result[0].Sentences, tarSentences[0].Sentences,
			videoInfo.Duration, cache)
		if err != nil {
			return 1, fmt.Errorf("ffmpeg scale bgm failed: %v", err)
		}
		logger.Log.Infof(utils.MMark(logCtx)+"process template bgm: %s", cache.BgmAudioOut)

		// 按照同样的逻辑处理一遍 effects
		err = RunFFmpegAudioOnly(logCtx, cache.BackEffectAudio, cache.BackEffectAudioOut, result[0].Sentences,
			tarSentences[0].Sentences,
			videoInfo.Duration, cache)
		if err != nil {
			return 1, fmt.Errorf("ffmpeg scale effect failed: %v", err)
		}
		logger.Log.Infof(utils.MMark(logCtx)+"process template effect: %s", cache.BackEffectAudioOut)
	} else {
		err := fileutils.CopyFile(cache.TemplateVocalVideo, cache.TemplateVideo)
		if err != nil {
			return 1, fmt.Errorf("copy template video to output failed: %v", err)
		}
		logger.Log.Infof(utils.MMark(logCtx)+"copy template video: %s", cache.TemplateVideo)

		err = fileutils.CopyFile(cache.BgmAudio, cache.BgmAudioOut)
		if err != nil {
			return 1, fmt.Errorf("copy bgm to output failed: %v", err)
		}
		logger.Log.Infof(utils.MMark(logCtx)+"copy bgm audio: %s", cache.BgmAudioOut)

		err = fileutils.CopyFile(cache.BackEffectAudio, cache.BackEffectAudioOut)
		if err != nil {
			return 1, fmt.Errorf("copy effect to output failed: %v", err)
		}
		logger.Log.Infof(utils.MMark(logCtx)+"copy effect audio: %s", cache.BackEffectAudioOut)
	}

	jsonBytes, err := json.Marshal(tarSentences)
	if err != nil {
		return 1, fmt.Errorf("marshal failed: %v", err)
	}

	err = os.WriteFile(cache.TtsText, jsonBytes, 0644)
	if err != nil {
		return 1, fmt.Errorf("write to out file failed: %v", err)
	}

	// 上传一份到云端
	fileUrl, err := storage.RetryUploadFromFile(logCtx, cache.BosRoot, cache.TtsText, cache.TtsTextSuffix)
	if err != nil || len(fileUrl) < 1 {
		return 1, fmt.Errorf("upload tts text failed: %v", err)
	}

	progress.VideoTransfer.TtsTextUrl = fileUrl

	item.SubStatus = enums.VoiceCloneSubtitle
	return 1, nil
}

// CombineSentencesToPcmWithScale 整合多个句子的 TTS 音频为一段完整 PCM 数据
func (p *TaskScheduler) CombineSentencesToPcmWithScale(sentences []*speech2text.Sentence,
	sampleRate int, numChannels int, totalDuration float64) ([]byte,
	error) {
	bytesPerSample := 2
	frameSize := numChannels * bytesPerSample
	totalFrames := int(totalDuration * float64(sampleRate))
	fullPCM := make([]byte, totalFrames*frameSize)

	sort.Slice(sentences, func(i, j int) bool {
		return sentences[i].Start < sentences[j].Start
	})

	for i, s := range sentences {
		wavInfo, err := fileutils.ReadWavFile(s.TtsFile)
		if err != nil {
			return nil, fmt.Errorf("read wav failed: %v", err)
		}
		if int(wavInfo.SampleRate) != sampleRate || int(wavInfo.NumChannels) != numChannels || wavInfo.
			BitsPerSample != 16 {
			return nil, fmt.Errorf("unsurpprted params: %s", filepath.Base(s.TtsFile))
		}

		// 取出下一段开始的时间，以及上一段结束的时间
		nextStart := s.End
		if len(sentences) > i+1 {
			nextStart = sentences[i+1].Start
		}
		lastEnd := s.Start
		if i == 0 {
			lastEnd = 0
		} else if i > 0 && len(sentences) > i {
			lastEnd = sentences[i-1].End
		}
		scaled, start, end := fileutils.ScalePCMWithChannels(wavInfo.PCMData, sampleRate, 0, s.TtsEnd, s.Start, s.End,
			numChannels, nextStart, lastEnd)
		sentences[i].Scale = (end - start) / (sentences[i].End - sentences[i].Start)
		sentences[i].Start = start
		sentences[i].End = end

		insertFrameStart := int(s.Start * float64(sampleRate))
		for i := 0; i < len(scaled); i++ {
			dstIndex := insertFrameStart*frameSize + i
			if dstIndex >= len(fullPCM) {
				break
			}
			fullPCM[dstIndex] = scaled[i]
		}
	}

	return fullPCM, nil
}
func (p *TaskScheduler) CombineSentencesToPcm(
	sentences []*speech2text.Sentence,
	sampleRate int,
	numChannels int,
	totalDuration float64,
) ([]byte, error) {
	bytesPerSample := 2
	frameSize := numChannels * bytesPerSample
	var bytesPerSecond float64 = float64(sampleRate * numChannels * 2)

	sort.Slice(sentences, func(i, j int) bool {
		return sentences[i].Start < sentences[j].Start
	})

	var result []byte
	lastEnd := 0.0
	totalTime := 0.0

	for i, _ := range sentences {
		// 插入静音段（如果 start > lastEnd）
		if sentences[i].Start > lastEnd {
			silenceDuration := sentences[i].Start - lastEnd
			numFrames := int(silenceDuration * float64(sampleRate))
			silence := make([]byte, numFrames*frameSize)
			result = append(result, silence...)

			totalTime += silenceDuration
		}

		// 因为音频时间变化了，那么段落时间也需要更新，字幕时间才能对上
		sentences[i].Start = totalTime
		sentences[i].Scale = 1.0

		// 读取 wav 文件
		wavInfo, err := fileutils.ReadWavFile(sentences[i].TtsFile)
		if err != nil {
			return nil, fmt.Errorf("read wav failed: %v", err)
		}
		if int(wavInfo.SampleRate) != sampleRate || int(wavInfo.NumChannels) != numChannels || wavInfo.BitsPerSample != 16 {
			return nil, fmt.Errorf("unsupported wav format: %s", filepath.Base(sentences[i].TtsFile))
		}

		// 添加音频数据
		result = append(result, wavInfo.PCMData...)

		sentences[i].TtsEnd = float64(len(wavInfo.PCMData)) / bytesPerSecond
		totalTime += sentences[i].TtsEnd

		// 更新 lastEnd 为当前句子的 End
		lastEnd = sentences[i].End
		sentences[i].End = totalTime

		// 更新新的结束时间
		sentences[i].End = sentences[i].Start + sentences[i].TtsEnd
	}

	// 原来的文件末尾还有一段静音
	if lastEnd < totalDuration {
		silenceDuration := totalDuration - lastEnd
		numFrames := int(silenceDuration * float64(sampleRate))
		silence := make([]byte, numFrames*frameSize)
		result = append(result, silence...)
		totalTime += silenceDuration
	}

	// 如果最后一句结束后还有剩余时间，补上静音
	//if totalDuration > totalTime {
	//	silenceDuration := totalDuration - totalTime
	//	numFrames := int(silenceDuration * float64(sampleRate))
	//	silence := make([]byte, numFrames*frameSize)
	//	result = append(result, silence...)
	//}

	return result, nil
}

func buildATempoChain(target float64) []string {
	var filters []string
	const minTempo, maxTempo = 0.5, 2.0
	const epsilon = 0.01

	for target < minTempo || target > maxTempo {
		if target < minTempo {
			filters = append(filters, fmt.Sprintf("atempo=%.3f", minTempo))
			target /= minTempo
		} else {
			filters = append(filters, fmt.Sprintf("atempo=%.3f", maxTempo))
			target /= maxTempo
		}
	}

	// 最后一个稳定值
	if math.Abs(target-1.0) > epsilon {
		filters = append(filters, fmt.Sprintf("atempo=%.5f", target))
	}
	return filters
}

func RunFFmpegOneShot(logCtx context.Context, inputVideo string, outputVideo string,
	srcSentences []*speech2text.Sentence,
	tarSentences []*speech2text.Sentence,
	totalDuration float64, cache *CachePath) error {
	var filter strings.Builder
	var videoConcatInputs strings.Builder
	var audioConcatInputs strings.Builder
	segmentIndex := 0
	lastEnd := 0.0

	for i := range tarSentences {
		// 1. 静音段（跳过0长度）
		if srcSentences[i].Start > lastEnd {
			filter.WriteString(fmt.Sprintf(
				"[0:v]trim=start=%.3f:end=%.3f,setpts=PTS-STARTPTS[v%do];\n",
				lastEnd, srcSentences[i].Start, segmentIndex,
			))
			videoConcatInputs.WriteString(fmt.Sprintf("[v%do]", segmentIndex))

			filter.WriteString(fmt.Sprintf(
				"[0:a]atrim=start=%.3f:end=%.3f,asetpts=PTS-STARTPTS[a%do];\n",
				lastEnd, srcSentences[i].Start, segmentIndex,
			))
			audioConcatInputs.WriteString(fmt.Sprintf("[a%do]", segmentIndex))

			segmentIndex++
		}

		// 2. 内容段（插帧/变速）
		origDur := srcSentences[i].End - srcSentences[i].Start
		tarDur := tarSentences[i].End - tarSentences[i].Start
		if origDur <= 0 || tarDur <= 0 {
			return fmt.Errorf("invalid sentence duration at index %d", i)
		}
		ratio := tarDur / origDur
		tempo := 1.0 / ratio

		// 视频处理
		filter.WriteString(fmt.Sprintf(
			"[0:v]trim=start=%.3f:end=%.3f,setpts=PTS-STARTPTS,minterpolate=fps=25:mi_mode=blend,setpts=%.6f*PTS[v%do];\n",
			srcSentences[i].Start, srcSentences[i].End, ratio, segmentIndex,
		))
		videoConcatInputs.WriteString(fmt.Sprintf("[v%do]", segmentIndex))

		// 音频处理
		filter.WriteString(fmt.Sprintf(
			"[0:a]atrim=start=%.3f:end=%.3f,asetpts=PTS-STARTPTS",
			srcSentences[i].Start, srcSentences[i].End,
		))
		atempoFilters := buildATempoChain(tempo)
		for _, f := range atempoFilters {
			filter.WriteString("," + f)
		}
		filter.WriteString(fmt.Sprintf("[a%do];\n", segmentIndex))
		audioConcatInputs.WriteString(fmt.Sprintf("[a%do]", segmentIndex))

		segmentIndex++
		lastEnd = srcSentences[i].End
	}

	// 3. 处理最后一个句子后剩余未覆盖区域（原速）
	if lastEnd < totalDuration {
		filter.WriteString(fmt.Sprintf(
			"[0:v]trim=start=%.3f:end=%.3f,setpts=PTS-STARTPTS[v%do];\n",
			lastEnd, totalDuration, segmentIndex,
		))
		videoConcatInputs.WriteString(fmt.Sprintf("[v%do]", segmentIndex))

		filter.WriteString(fmt.Sprintf(
			"[0:a]atrim=start=%.3f:end=%.3f,asetpts=PTS-STARTPTS[a%do];\n",
			lastEnd, totalDuration, segmentIndex,
		))
		audioConcatInputs.WriteString(fmt.Sprintf("[a%do]", segmentIndex))
		segmentIndex++
	}

	// 3. 拼接所有视频和音频段
	filter.WriteString(fmt.Sprintf("%sconcat=n=%d:v=1:a=0[basev];\n", videoConcatInputs.String(), segmentIndex))
	filter.WriteString(fmt.Sprintf("%sconcat=n=%d:v=0:a=1[basea];\n", audioConcatInputs.String(), segmentIndex))

	// 4. 如果最后不足 totalDuration，补齐尾部（视频）
	if totalDuration > lastEnd {
		padDur := totalDuration - lastEnd
		filter.WriteString(fmt.Sprintf("[basev]tpad=stop_duration=%.3f:stop_mode=clone[vout];", padDur))
	} else {
		filter.WriteString("[basev]copy[vout];")
	}
	filter.WriteString("[basea]acopy[aout]")

	filterScriptFile := fmt.Sprintf("%sfilter-script-%s.txt", cache.Temp, utils.RandStringRunes(6))
	err := os.WriteFile(filterScriptFile, []byte(filter.String()), 0644)
	if err != nil {
		return fmt.Errorf("write filter script failed: %w", err)
	}

	// 5. 构建 ffmpeg 命令
	args := []string{
		"-y",
		"-i", inputVideo,
		//"-filter_complex", filter.String(),
		"-filter_complex_script", filterScriptFile,
		"-map", "[vout]",
		"-map", "[aout]",
		"-c:v", "libx264",
		"-crf", "20",
		"-preset", "slow",
		"-c:a", "aac",
		"-ar", "16000",
		"-ac", "1",
		outputVideo,
	}

	cmd := exec.Command("ffmpeg", args...)
	cmdStr := strings.Join(cmd.Args, " ")
	logger.Log.Infof(utils.MMark(logCtx)+"ffmpeg video blend filter: { %s }", filter.String())
	logger.Log.Infof(utils.MMark(logCtx)+"ffmpeg video blend cmd: { %s }", cmdStr)

	//cmd.Stdout = os.Stdout
	//cmd.Stderr = os.Stderr

	// 仅捕获异常信息
	var stderr bytes.Buffer
	cmd.Stderr = &stderr

	tStart := time.Now()
	if err := cmd.Run(); err != nil {
		return fmt.Errorf("RunFFmpegOneShot error: %v, detail: %s", err, stderr.String())
	}

	duration := float64(time.Since(tStart).Milliseconds()) / 1000.0
	logger.Log.Infof(utils.MMark(logCtx)+"finish ffmpeg with duration: %.3fs", duration)

	return nil
}

func RunFFmpegAudioOnly(logCtx context.Context, inputAudio string, outputAudio string,
	srcSentences []*speech2text.Sentence,
	tarSentences []*speech2text.Sentence,
	totalDuration float64, cache *CachePath) error {

	var filter strings.Builder
	var audioConcatInputs strings.Builder
	segmentIndex := 0
	lastEnd := 0.0

	for i := range tarSentences {
		// 1. 静音段（跳过0长度）
		if srcSentences[i].Start > lastEnd {
			filter.WriteString(fmt.Sprintf(
				"[0:a]atrim=start=%.3f:end=%.3f,asetpts=PTS-STARTPTS[a%do];\n",
				lastEnd, srcSentences[i].Start, segmentIndex,
			))
			audioConcatInputs.WriteString(fmt.Sprintf("[a%do]", segmentIndex))
			segmentIndex++
		}

		// 2. 内容段（变速）
		origDur := srcSentences[i].End - srcSentences[i].Start
		tarDur := tarSentences[i].End - tarSentences[i].Start
		if origDur <= 0 || tarDur <= 0 {
			return fmt.Errorf("invalid sentence duration at index %d", i)
		}
		ratio := tarDur / origDur
		tempo := 1.0 / ratio

		filter.WriteString(fmt.Sprintf(
			"[0:a]atrim=start=%.3f:end=%.3f,asetpts=PTS-STARTPTS",
			srcSentences[i].Start, srcSentences[i].End,
		))
		atempoFilters := buildATempoChain(tempo)
		for _, f := range atempoFilters {
			filter.WriteString("," + f)
		}
		filter.WriteString(fmt.Sprintf("[a%do];\n", segmentIndex))
		audioConcatInputs.WriteString(fmt.Sprintf("[a%do]", segmentIndex))
		segmentIndex++
		lastEnd = srcSentences[i].End
	}

	// 3. 末尾未覆盖区域（原速）
	if lastEnd < totalDuration {
		filter.WriteString(fmt.Sprintf(
			"[0:a]atrim=start=%.3f:end=%.3f,asetpts=PTS-STARTPTS[a%do];\n",
			lastEnd, totalDuration, segmentIndex,
		))
		audioConcatInputs.WriteString(fmt.Sprintf("[a%do]", segmentIndex))
		segmentIndex++
	}

	// 4. 拼接音频段
	filter.WriteString(fmt.Sprintf("%sconcat=n=%d:v=0:a=1[aout]", audioConcatInputs.String(), segmentIndex))

	filterScriptFile := fmt.Sprintf("%sfilter-script-%s.txt", cache.Temp, utils.RandStringRunes(6))
	err := os.WriteFile(filterScriptFile, []byte(filter.String()), 0644)
	if err != nil {
		return fmt.Errorf("write filter script failed: %w", err)
	}

	// 5. 构建 FFmpeg 命令
	args := []string{
		"-y",
		"-i", inputAudio,
		//"-filter_complex", filter.String(),
		"-filter_complex_script", filterScriptFile,
		"-map", "[aout]",
		"-c:a", "pcm_s16le",
		"-ar", "16000",
		"-ac", "1",
		outputAudio,
	}

	cmd := exec.Command("ffmpeg", args...)
	cmdStr := strings.Join(cmd.Args, " ")
	logger.Log.Infof(utils.MMark(logCtx)+"ffmpeg audio-only blend filter: { %s }", filter.String())
	logger.Log.Infof(utils.MMark(logCtx)+"ffmpeg audio-only blend cmd: { %s }", cmdStr)

	//cmd.Stdout = os.Stdout
	//cmd.Stderr = os.Stderr

	// 仅捕获异常信息
	var stderr bytes.Buffer
	cmd.Stderr = &stderr

	tStart := time.Now()
	if err := cmd.Run(); err != nil {
		return fmt.Errorf("RunFFmpegAudioOnly error: %v, detail: %s", err, stderr.String())
	}

	duration := float64(time.Since(tStart).Milliseconds()) / 1000.0
	logger.Log.Infof(utils.MMark(logCtx)+"finish ffmpeg with duration: %.3fs", duration)

	return nil
}

func (p *TaskScheduler) doTts(logCtx context.Context, engine, voiceId, text, tempPath string,
	speed float64) (*helper.TtsResponse,
	error) {
	var minResp *helper.TtsResponse
	if engine == "elevenlabs" {
		resp, err := voice_clone.DoTts(logCtx, conf.LocalConfig.ElevenLabsSettings.ApiKey, voiceId,
			text, speed)
		if err != nil {
			return nil, fmt.Errorf("tts in [%s] for [%s] failed: %v", conf.LocalConfig.VoiceSettings.CloneEngine,
				text, err)
		}

		minResp, err = helper.ConvertElevenLabsSpeech(tempPath, resp)
		if err != nil {
			return nil, fmt.Errorf("normalize tts in [%s] response failed: %v", engine, err)
		}
	} else if engine == "minimax" {
		req := minimax.NewSyncSpeechRequest(minimax.ModelSpeechTurbo02, voiceId, text, speed)
		resp, _, err := minimax.SynthesizeSpeech(conf.LocalConfig.MinimaxSettings.Host,
			conf.LocalConfig.MinimaxSettings.GroupId, conf.LocalConfig.MinimaxSettings.ApiKey, req)
		if err != nil {
			return nil, fmt.Errorf("tts in [%s] for [%s] failed: %v", conf.LocalConfig.VoiceSettings.CloneEngine,
				text, err)
		}

		minResp, err = helper.ConvertMinimaxSpeech(logCtx, tempPath, resp)
		if err != nil {
			return nil, fmt.Errorf("normalize tts in [%s] response failed: %v", engine, err)
		}
	}
	return minResp, nil
}

func (p *TaskScheduler) DoTtsAutoFit(logCtx context.Context, engine, voiceId string,
	sentence *speech2text.Sentence, targetLang string, targetDiff float64, retry int, cache *CachePath) (*helper.TtsResponse, error) {
	// 目标时长
	targetDuration := sentence.End - sentence.Start

	minResp, err := p.doTts(logCtx, engine, voiceId, sentence.NewText, cache.Temp, 1.0)
	if err != nil {
		return nil, fmt.Errorf("tts: %v", err)
	}

	if len(minResp.Alignment.CharacterEndTimesSeconds) < 1 {
		return nil, fmt.Errorf("tts for [%s], get character align end failed: %+v", sentence.NewText, minResp)
	}
	last := len(minResp.Alignment.CharacterEndTimesSeconds) - 1

	// 当前时长
	currentDuration := minResp.Alignment.CharacterEndTimesSeconds[last]
	logger.Log.Infof(utils.MMark(logCtx)+"duration: %.2f/%.2f, text: %s", currentDuration, targetDuration, sentence.NewText)

	minDiff := math.Abs(targetDuration - currentDuration)

	// 上一轮的
	lastText := sentence.NewText
	lastDuration := currentDuration

	// 小于目标长度且差值最小的
	shortText := sentence.NewText
	shortDuration := currentDuration
	shortResp := minResp
	shortDiff := minDiff

	// 迭代，以满足差距
	systemSetting := p.VoicePrompt
	client := gemini.NewClient(voiceId, systemSetting, conf.LocalConfig.GeminiSettings.GoogleCredential,
		conf.LocalConfig.GeminiSettings.ProjectId, conf.LocalConfig.GeminiSettings.Location,
		conf.LocalConfig.GeminiSettings.Model)
	step := 0
	for step < retry {
		if math.Abs(targetDuration-currentDuration) <= targetDiff {
			break
		}
		step++

		time.Sleep(time.Duration(sutils.RandomFloatInRange(0.5, 2.0)) * time.Second)
		content := fmt.Sprintf("text:%s,newText:%s,currentDuration:%.3f,targetDuration:%.3f，targetLang:%s",
			sentence.Text, lastText, lastDuration, targetDuration, targetLang)
		result, err := client.Chat(logCtx, content)
		if err != nil {
			return nil, fmt.Errorf("LLM assit tts failed: %v", err)
		}

		tts, err := p.doTts(logCtx, engine, voiceId, result, cache.Temp, 1.0)
		if err != nil {
			return nil, fmt.Errorf("tts: %v", err)
		}

		if len(tts.Alignment.CharacterEndTimesSeconds) < 1 {
			return nil, fmt.Errorf("tts for [%s], get character align end failed: %+v", sentence.NewText, tts)
		}
		last := len(tts.Alignment.CharacterEndTimesSeconds) - 1
		tempDuration := tts.Alignment.CharacterEndTimesSeconds[last]
		tempDiff := math.Abs(targetDuration - tempDuration)

		logger.Log.Infof(utils.MMark(logCtx)+"step: %d, target diff: %.2f, new duration: %.2f/%.2f, text: %s", step,
			targetDiff, tempDuration,
			targetDuration, result)

		// 存储一个差值最小的结果
		if tempDiff < minDiff {
			minDiff = tempDiff
			sentence.NewText = result
			currentDuration = tempDuration
			minResp = tts
		}

		// 存储一个小于原长度的情况下差值最小的结果
		if tempDuration < targetDuration && tempDiff < shortDiff {
			shortDuration = tempDuration
			shortText = result
			shortResp = tts
			shortDiff = tempDiff
		}

		// 上一次的结果
		lastDuration = tempDuration
		lastText = result
	}

	// 如果最终生成的长度还是比原音频时间长
	if minDiff > targetDiff && currentDuration > targetDuration {
		// 如果长的不是很多，比如目标阈值浮动 0.5
		if minDiff < (targetDiff + 0.5) {
			return minResp, nil
		}
		// 如果这个最小结果的差值还是在允许范围内 + 0.3 那么也可以用
		if shortDiff < (targetDiff + 0.3) {
			minDiff = shortDiff
			sentence.NewText = shortText
			currentDuration = shortDuration
			minResp = shortResp

			logger.Log.Infof(utils.MMark(logCtx)+"use short result, target diff: %.2f, new duration: %.2f/%.2f, "+
				"text: %s", targetDiff, currentDuration, targetDuration, sentence.NewText)
		}
	}

	// 如果LLM也解决不了时长问题，就使用合成的语速参数调整
	if minDiff > targetDiff && currentDuration > targetDuration {
		scale := currentDuration / targetDuration

		for i := 0; i < retry; i++ {
			newScale := currentDuration / targetDuration
			if newScale > 1.0 {
				scale += conf.LocalConfig.VoiceTuningSettings.SpeedWeight
			}
			scale = math.Max(math.Min(scale, 1.2), 0.7)

			tts, err := p.doTts(logCtx, engine, voiceId, sentence.NewText, cache.Temp, scale)
			if err != nil {
				return nil, fmt.Errorf("tts: %v", err)
			}

			if len(tts.Alignment.CharacterEndTimesSeconds) < 1 {
				return nil, fmt.Errorf("tts for [%s], get character align end failed: %+v", sentence.NewText, tts)
			}
			last := len(tts.Alignment.CharacterEndTimesSeconds) - 1
			currentDuration := tts.Alignment.CharacterEndTimesSeconds[last]
			tempDiff := math.Abs(targetDuration - currentDuration)
			logger.Log.Infof(utils.MMark(logCtx)+"[%d]speed: %.2f, target diff: %.2f, new duration: %.2f/%.2f, "+
				"text: %s", i, scale, targetDiff, currentDuration, targetDuration, sentence.NewText)

			if tempDiff < minDiff {
				minResp = tts
			}

			if tempDiff <= targetDiff || currentDuration < targetDuration {
				minResp = tts
				break
			}

			time.Sleep(time.Duration(sutils.RandomFloatInRange(0.5, 1.0)) * time.Second)
		}
	}

	return minResp, nil
}

func (p *TaskScheduler) doTtsSubtitle(logCtx context.Context, item *model.VideoTranslateTask,
	config *proto.SubmitConfig, progress *proto.Progress, cache *CachePath) (int, error) {
	logger.Log.Infof(utils.MMark(logCtx) + "doTtsSubtitle")
	data, err := os.ReadFile(cache.TtsText)
	if err != nil {
		return 1, fmt.Errorf("read file error: %w", err)
	}
	var result []*speech2text.Speaker
	if err := json.Unmarshal(data, &result); err != nil {
		return 1, fmt.Errorf("json unmarshal error: %w", err)
	}

	if config.EnableCaptions {
		maxChars := conf.LocalConfig.SubtitleSettings.MaxChars
		maxLineChars := conf.LocalConfig.SubtitleSettings.MaxLineChars
		maxDuration := conf.LocalConfig.SubtitleSettings.MaxDuration

		// 判断一下语言，有的语言字符比较宽，行字符数需要减少
		isWide := false
		language := strings.ToLower(config.TargetLangs[0])
		logger.Log.Infof(utils.MMark(logCtx)+"default config on language: %s, %d/%d/%.2f", language, maxChars,
			maxLineChars, maxDuration)

		for _, lang := range conf.LocalConfig.SubtitleSettings.WideCharLanguage {
			if strings.Contains(language, lang) {
				isWide = true
				break
			}
		}
		if isWide {
			maxChars = conf.LocalConfig.SubtitleSettings.MaxWideChars
			maxLineChars = conf.LocalConfig.SubtitleSettings.MaxWideLineChars
			maxDuration = conf.LocalConfig.SubtitleSettings.MaxWideDuration
			logger.Log.Infof(utils.MMark(logCtx)+"use wide config on language: %s, %d/%d/%.2f", language, maxChars,
				maxLineChars, maxDuration)
		}

		// 判断一下是不是横屏，横屏可以多一点
		if item.ResolutionWidth > item.ResolutionHeight {
			maxChars = int(float64(maxChars) * conf.LocalConfig.SubtitleSettings.MaxLandscapeCharsScale)
			maxLineChars = int(float64(maxLineChars) * conf.LocalConfig.SubtitleSettings.MaxLandscapeLineCharsScale)
			maxDuration = maxDuration * conf.LocalConfig.SubtitleSettings.MaxLandscapeDurationScale
			logger.Log.Infof(utils.MMark(logCtx)+"use landscape config on language: %s, %d/%d/%.2f", language, maxChars,
				maxLineChars, maxDuration)
		}

		// 生成字幕
		content, err := subtitle.GenerateSubtitleFromSpeakers(result, true, true, maxChars,
			maxDuration, conf.LocalConfig.SubtitleSettings.UsePunctuation, maxLineChars, conf.LocalConfig.SubtitleSettings.MaxLines)
		if err != nil {
			return 1, fmt.Errorf("make subtitle failed: %v", err)
		}

		// 写入文件
		err = os.WriteFile(cache.SubtitleOutput, []byte(content), 0644)
		if err != nil {
			return 1, fmt.Errorf("write subtitle file failed: %v", err)
		}
		logger.Log.Infof(utils.MMark(logCtx)+"make subtitle done: %s", cache.SubtitleOutput)

		// 写入文件
		err = os.WriteFile(cache.SubtitleUpload, []byte(content), 0644)
		if err != nil {
			return 1, fmt.Errorf("write subtitle file failed: %v", err)
		}
		logger.Log.Infof(utils.MMark(logCtx)+"make subtitle done: %s", cache.SubtitleUpload)

		// 上传云端
		fileUrl, err := storage.RetryUploadFromFileWithCDNAndContentType(logCtx, cache.BosRoot, cache.SubtitleUpload,
			cache.SubtitleUploadSuffix, false, storage.ContentTypeStream)
		if err != nil {
			return 1, fmt.Errorf("upload to bos failed: %v", err)
		}
		logger.Log.Infof(utils.MMark(logCtx)+"upload subtitle: %s", fileUrl)

		item.CaptionUrl = fileUrl
	}

	if config.TranslateAudioOnly {
		// 只翻译音频的情况下不需要训练和推理口型
		item.Status = enums.PostProcess
		item.SubStatus = enums.PostProcessWait
	} else {
		item.Status = enums.VideoTransfer
		item.SubStatus = enums.VideoFigureTrainUpload
	}
	return 1, nil
}
