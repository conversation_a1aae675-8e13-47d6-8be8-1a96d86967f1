<?xml version="1.0" encoding="UTF-8"?>
<configuration debug="true">
    <!-- access_debug filter, /api prefix only, css/html/js file content will not be logged  -->
    <!--<property name="logging.access_debug_uri_prefix" value="/"/>-->
    <statusListener class="ch.qos.logback.core.status.OnConsoleStatusListener"/>
    <if condition='isDefined("spring.config.location")'>
        <then>
            <property file="${spring.config.location}"/>
        </then>
    </if>
    <if condition='!isDefined("spring.config.location")'>
        <then>
            <property resource="application.properties"/>
        </then>
    </if>
    <property name="logging.has_console_appender" value="${logging.has_console_appender:-true}"/>
    <property name="logging.max_log_file_size" value="${logging.max_log_file_size:-50MB}"/>
    <property name="logging.access_debug_uri_prefix" value="${logging.access_debug_uri_prefix:-/}"/>
    <if condition='isDefined("logging.access_log_file_path")'>
        <then>
            <appender name="ACCESS_LOG_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
                <encoder>
                    <charset>UTF-8</charset>
                    <pattern>%i{ClientIp} %h %l %u [%t] "%r" %s %b "%i{Referer}" "%i{User-Agent}"
                        %i{x-ssl-header} %D
                    </pattern>
                </encoder>
                <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
                    <fileNamePattern>${logging.access_log_file_path}.%d{yyyyMMddHH}</fileNamePattern>
                    <if condition='isDefined("logging.access_log_max_history_in_hours")'>
                        <then>
                            <maxHistory>${logging.access_log_max_history_in_hours}</maxHistory>
                        </then>
                    </if>
                </rollingPolicy>
            </appender>
        </then>
    </if>
    <if condition='isDefined("logging.access_debug_log_file_path")'>
        <then>
            <appender name="ACCESS_DEBUG_LOG_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
                <filter class="ch.qos.logback.core.filter.EvaluatorFilter">
                    <evaluator name="ApiOnly">
                        <expression>
                            event.getRequestURI().startsWith("${logging.access_debug_uri_prefix}")
                        </expression>
                    </evaluator>
                    <onMismatch>DENY</onMismatch>
                </filter>
                <encoder>
                    <charset>UTF-8</charset>
                    <pattern>%i{ClientIp} %h %l %u [%t] "%r" %s %b "%i{Referer}" "%i{User-Agent}"
                        %i{x-ssl-header}
                        %n======&gt;%n%fullRequest%n&lt;======
                        %n'x-bce-request-id: %responseHeader{x-bce-request-id}'
                        %n'x-bce-cdn-domains-checksum: %responseHeader{x-bce-cdn-domains-checksum}'
                        %n'x-bce-cdn-certs-checksum: %responseHeader{x-bce-cdn-certs-checksum}'
                    </pattern>
                </encoder>
                <file>${logging.access_debug_log_file_path}</file>
                <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
                    <fileNamePattern>
                        ${logging.access_debug_log_file_path}-%d{yyyy-MM-dd}/%d{yyyy-MM-dd}.%i.access.debug.log
                    </fileNamePattern>
                    <if condition='isDefined("logging.access_debug_log_max_history_in_days")'>
                        <then>
                            <maxHistory>${logging.access_debug_log_max_history_in_days}</maxHistory>
                        </then>
                    </if>
                    <maxFileSize>${logging.max_log_file_size}</maxFileSize>
                </rollingPolicy>
            </appender>
        </then>
    </if>
    <!-- add appender -->
    <if condition='"true".equals(property("logging.has_console_appender"))'>
        <then>
            <appender-ref ref="CONSOLE"/>
            <appender-ref ref="CONSOLE_DEBUG"/>
        </then>
    </if>
    <if condition='isDefined("logging.access_log_file_path")'>
        <then>
            <appender-ref ref="ACCESS_LOG_FILE"/>
        </then>
    </if>
    <if condition='isDefined("logging.access_debug_log_file_path")'>
        <then>
            <appender-ref ref="ACCESS_DEBUG_LOG_FILE"/>
        </then>
    </if>
</configuration>