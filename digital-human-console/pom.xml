<?xml version="1.0" encoding="UTF-8"?>
<!-- Copyright (C) 2019 Baidu Inc. All rights reserved. -->
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>digital-human</artifactId>
        <groupId>com.baidu.acg.piat</groupId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>digital-human-console</artifactId>

    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-redis</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>io.netty</groupId>
                    <artifactId>netty-common</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-databind</artifactId>
            <version>2.13.2</version>
        </dependency>
        <dependency>
            <groupId>io.netty</groupId>
            <artifactId>netty-common</artifactId>
            <version>4.1.77.Final</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-aop</artifactId>
        </dependency>
        <dependency>
            <groupId>com.baidu.acg.piat</groupId>
            <artifactId>digital-human-common</artifactId>
        </dependency>

        <dependency>
            <groupId>com.baidu.acg.pie</groupId>
            <artifactId>digital-human-sdk-java</artifactId>
        </dependency>

        <dependency>
            <groupId>com.baidu.acg.piat</groupId>
            <artifactId>digital-human-plat-client</artifactId>
        </dependency>

        <dependency>
            <groupId>com.baidu.acg.piat</groupId>
            <artifactId>device-manager-v2-client</artifactId>
            <version>1.2.0-beta3-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.squareup.okhttp3</groupId>
            <artifactId>okhttp</artifactId>
            <version>3.12.3</version>
        </dependency>

        <dependency>
            <groupId>com.baidu.acg.piat</groupId>
            <artifactId>digital-human-storage</artifactId>
            <exclusions>

<!--                <exclusion>-->
<!--                    <groupId>ch.qos.logback</groupId>-->
<!--                    <artifactId>logback-classic</artifactId>-->
<!--                </exclusion>-->
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-log4j12</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>io.netty</groupId>
                    <artifactId>netty-all</artifactId>
                </exclusion>


            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.baidu.acg.piat</groupId>
            <artifactId>digital-human-resource-pool-client</artifactId>
        </dependency>

        <dependency>
            <groupId>com.baidu.acg.piat</groupId>
            <artifactId>digital-human-video-pipeline-client</artifactId>
        </dependency>

        <dependency>
            <groupId>com.baidu.acg.piat</groupId>
            <artifactId>digital-human-tracer</artifactId>
        </dependency>

        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-boot-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>com.baidu.acg.piat</groupId>
            <artifactId>digital-human-tts-client</artifactId>
        </dependency>

<!--        <dependency>-->
<!--            <groupId>ch.qos.logback</groupId>-->
<!--            <artifactId>logback-access</artifactId>-->
<!--        </dependency>-->

        <dependency>
            <groupId>javax.xml.bind</groupId>
            <artifactId>jaxb-api</artifactId>
        </dependency>

        <dependency>
            <groupId>org.glassfish.jersey.core</groupId>
            <artifactId>jersey-common</artifactId>
        </dependency>

        <dependency>
            <groupId>javax.activation</groupId>
            <artifactId>javax.activation-api</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.curator</groupId>
            <artifactId>curator-recipes</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>log4j</artifactId>
                    <groupId>log4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.apache.curator</groupId>
            <artifactId>curator-test</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>com.baidu.acu-bot</groupId>
            <artifactId>dh-user-client</artifactId>
        </dependency>

        <dependency>
            <groupId>com.auth0</groupId>
            <artifactId>java-jwt</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-exec</artifactId>
            <version>1.3</version>
            <scope>compile</scope>
        </dependency>

    </dependencies>

    <build>
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>