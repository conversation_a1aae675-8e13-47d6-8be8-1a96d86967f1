# !/bin/sh

script=$0
if [[ ${script:0:1} == "/" ]]; then
    bin=`dirname $script`
else
    bin=`pwd`/`dirname $script`
fi

root=${bin}/..
app=digital-human-console

APPLICATION=${bin}/${app}.jar
SPRING_CONFIG_FILE=${root}/conf/application.yaml
MAX_MEMORY=2048M
INFO_LOG_FILE_PATH=${root}/log/info/${app}.info.log
ERROR_LOG_FILE_PATH=${root}/log/error/${app}.error.log
WARN_LOG_FILE_PATH=${root}/log/warn/${app}.warn.log
DEBUG_LOG_FILE_PATH=${root}/log/debug/${app}.debug.log
ACCESS_LOG_FILE_PATH=${root}/log/access/${app}.access.log
ACCESS_DEBUG_LOG_FILE_PATH=${root}/log/access_debug/${app}.access_debug.log
LOGBACK_FILE_PATH=${root}/conf/logback.xml
LOGBACK_ACCESS_FILE_PATH=${root}/conf/logback-access.xml

java -javaagent:/home/<USER>/rasp/boot.jar \
    -Drasp.app.id=f476352a93c7ae7980f1580f0454b7c07aedfd4b \
    -Drasp.user.tag=site_xiling.cloud.baidu.com:icode_baidu/acg-digital-human/digital-human:image_ccr-246im3mw-pub.cnc.bj.baidubce.com/digitalhuman/${app} \
    -Drasp.cloud.endpoint=http://rasp-cloud.baidubce.com:10088 \
    -Dspring.config.location=${SPRING_CONFIG_FILE} -Dfile.encoding=UTF-8 \
    -Dlogging.config=$LOGBACK_FILE_PATH -Dlogging.access.config=$LOGBACK_ACCESS_FILE_PATH\
    -Dlogging.info_log_file_path=$INFO_LOG_FILE_PATH -Dlogging.error_log_file_path=$ERROR_LOG_FILE_PATH \
    -Dlogging.warn_log_file_path=$WARN_LOG_FILE_PATH -Dlogging.debug_log_file_path=$DEBUG_LOG_FILE_PATH \
    -Dlogging.access_log_file_path=$ACCESS_LOG_FILE_PATH -Dlogging.access_debug_log_file_path=$ACCESS_DEBUG_LOG_FILE_PATH \
    -Dlogging.info_log_max_history_in_hours=168 -Dlogging.error_log_max_history_in_days=30 \
    -Dlogging.warn_log_max_history_in_days=30 -Dlogging.debug_log_max_history_in_days=7 \
    -Xmx${MAX_MEMORY} -jar ${APPLICATION}  