package com.baidu.acg.piat.digitalhuman.console.interceptors;

import com.baidu.acg.dh.user.client.UserClient;
import com.baidu.acg.dh.user.client.UserResult;
import com.baidu.acg.dh.user.client.model.vo.UserGetResVO;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.mock.web.MockHttpServletResponse;

import javax.servlet.http.HttpServletResponse;

import com.baidu.acg.piat.digitalhuman.common.access.AccessApp;
import com.baidu.acg.piat.digitalhuman.console.model.common.AuthorizationInfo;
import com.baidu.acg.piat.digitalhuman.plat.client.PlatformClient;

import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

/**
 * AuthInterceptorTest
 *
 * <AUTHOR>
 * @since 2021/01/17
 */
class AuthInterceptorTest {

    @InjectMocks
    private AuthInterceptor authInterceptor;

    @Mock
    private PlatformClient platformClient;

    @Mock
    private UserClient userClient;

    private UserGetResVO userGetResVO;

    private static final String APP_ID = "aAppId";
    private static final String APP_KEY = "aAppKey";
    private static final String USER_ID = "auser";
    private static final AccessApp APP = AccessApp.builder().appId(APP_ID).appKey(APP_KEY).userId(USER_ID).build();
    private static final String SIGNATURE = "ff42f14c7a03e992044b4336828bad0191ad99e314099982f897efda47eb37b8";

    @BeforeEach
    public void init() {
        MockitoAnnotations.initMocks(this);
        when(platformClient.getApp(APP_ID)).thenReturn(APP);

        userGetResVO = UserGetResVO.builder()
                .uid("uid")
                .userName("userName")
                .build();
    }

    @Test
    public void preHandleTest() throws Exception {
        when(userClient.getUserById(anyString())).thenReturn(new UserResult());
        MockHttpServletRequest request = new MockHttpServletRequest();
        request.setRequestURI("/api/digitalhuman/console/v1");
        request.addHeader("Authorization", "BDH " + APP_ID + "/" + SIGNATURE + "/" + "2050-10-23T09:28:55.207Z");
        HttpServletResponse response = new MockHttpServletResponse();
        Assertions.assertTrue(authInterceptor.preHandle(request, response, null));
        AuthorizationInfo authInfo = (AuthorizationInfo) request.getAttribute("authorization");
        Assertions.assertNotNull(authInfo);
        Assertions.assertEquals(APP_ID, authInfo.getAppId());
        Assertions.assertEquals(APP_KEY, authInfo.getAppKey());
        Assertions.assertEquals(USER_ID, authInfo.getUserId());
    }
}