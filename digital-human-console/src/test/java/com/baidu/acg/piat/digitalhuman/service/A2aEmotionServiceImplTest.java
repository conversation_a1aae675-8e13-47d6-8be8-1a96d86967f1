package com.baidu.acg.piat.digitalhuman.service;


import com.baidu.acg.piat.digitalhuman.common.utils.JsonUtil;
import com.baidu.acg.piat.digitalhuman.console.config.A2aEmotionConfigure;
import com.baidu.acg.piat.digitalhuman.console.model.a2aemotion.A2aEmotionRecognitionRequest;
import com.baidu.acg.piat.digitalhuman.console.model.a2aemotion.A2aEmotionRecognitionResponse;
import com.baidu.acg.piat.digitalhuman.console.model.a2aemotion.AiEmotionRecognitionRes;
import com.baidu.acg.piat.digitalhuman.console.service.a2aemotion.impl.A2aEmotionServiceImpl;
import com.baidu.acg.piat.digitalhuman.console.service.nlp.AiCloudHttpClient;
import com.google.common.collect.Maps;
import org.assertj.core.util.Lists;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.io.IOException;
import java.util.Map;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;


public class A2aEmotionServiceImplTest {

    @InjectMocks
    private A2aEmotionServiceImpl a2AEmotionServiceImpl;

    @Mock
    private AiCloudHttpClient aiCloudHttpClient;
    @Mock
    private A2aEmotionConfigure.Config a2aEmotionConfig;


    @BeforeEach
    public void setUp() {
        MockitoAnnotations.initMocks(this);

    }


    @Test
    public void recognition() throws IOException {
        A2aEmotionRecognitionRequest request = new A2aEmotionRecognitionRequest();
        request.setTexts(Lists.newArrayList("232,323", "1，111"));
        String responseJson = "{\"log_id\":0,\"text\":\"你真笨\",\"items\":[{\"prob\":0.725283,\"label\":\"pessimistic\",\"subitems\":[{\"prob\":0.725283,\"label\":\"disgusting\"}],\"replies\":[\"我会努力的\"]},{\"prob\":0.273508,\"label\":\"neutral\",\"subitems\":[],\"replies\":[]},{\"prob\":0.00120925,\"label\":\"optimistic\",\"subitems\":[],\"replies\":[]}]}";
        AiEmotionRecognitionRes response = JsonUtil.readValue(responseJson, AiEmotionRecognitionRes.class);

        when(aiCloudHttpClient.aiEmotionDetection(any())).thenReturn(response);
        Map<String, String> a2aEmotionMappingMap = Maps.newHashMap();
        a2aEmotionMappingMap.put("happy", "kaixin");
        a2aEmotionMappingMap.put("like", "jingya");
        a2aEmotionMappingMap.put("angry", "natural");
        a2aEmotionMappingMap.put("disgusting", "natural");
        a2aEmotionMappingMap.put("fearful", "natural");
        a2aEmotionMappingMap.put("sad", "shangxin");
        when(a2aEmotionConfig.getA2aEmotionMappingMap()).thenReturn(a2aEmotionMappingMap);


        A2aEmotionRecognitionResponse recognition = a2AEmotionServiceImpl.recognition(request);
        int i = 0;
    }

}
