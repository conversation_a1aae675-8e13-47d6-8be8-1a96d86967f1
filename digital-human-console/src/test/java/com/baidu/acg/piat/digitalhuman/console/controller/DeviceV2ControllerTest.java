package com.baidu.acg.piat.digitalhuman.console.controller;
import com.baidu.acg.digitalhuman.device.common.v2.model.DeviceV2;
import com.baidu.acg.piat.digitalhuman.common.model.PageRequest;
import com.baidu.acg.piat.digitalhuman.console.model.device.DeviceDeleteRequest;
import com.baidu.acg.piat.digitalhuman.console.model.device.DeviceEventListRequest;
import com.baidu.acg.piat.digitalhuman.console.service.device.DeviceControlService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import static org.junit.jupiter.api.Assertions.assertEquals;

import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

/**
 * @Author: <EMAIL>
 * @Date: 2023/8/24
 */
class DeviceV2ControllerTest {

    @InjectMocks
    DeviceV2Controller deviceV2Controller;

    @Mock
    DeviceControlService deviceControlService;

    private DeviceV2 deviceV2;

    private DeviceEventListRequest deviceEventListRequest;

    private DeviceDeleteRequest deviceDeleteRequest;

    @BeforeEach
    public void setup() {
        MockitoAnnotations.initMocks(this);
        deviceV2Controller = new DeviceV2Controller(deviceControlService);
        deviceV2 = new DeviceV2();
        deviceV2.setDeviceId("deviceId");
        deviceV2.setDeviceName("deviceName");

        deviceEventListRequest = new DeviceEventListRequest();
        deviceEventListRequest.setDeviceId("deviceId");
        deviceDeleteRequest = new DeviceDeleteRequest();
        deviceDeleteRequest.setDeviceId("deviceId");
    }

    @Test
    void createDevice() {
        assertEquals(0, deviceV2Controller.createDevice(deviceV2, "accountId").getCode());
    }

    @Test
    void updateDevice() {
        assertEquals(0, deviceV2Controller.updateDevice(deviceV2).getCode());
    }

    @Test
    void getDeviceList() {
        deviceV2Controller.getDeviceList(new PageRequest(), "accountId");
        verify(deviceControlService,times(1)).listDeviceAll("accountId", 1, 20);
    }

    @Test
    void getDeviceEventList() {
        deviceV2Controller.getDeviceEventList( deviceEventListRequest, "accountId");
        verify(deviceControlService,times(1)).listDeviceEventAll("deviceId","accountId", 1, 20);
    }

    @Test
    void deleteDevice() {
        assertEquals(0, deviceV2Controller.deleteDevice(deviceDeleteRequest, "accountId").getCode());
    }

    @Test
    void getDeviceTypeList() {
        deviceV2Controller.getDeviceTypeList("accountId");
        verify(deviceControlService,times(1)).listDeviceAll("accountId", 1, 20);
    }
}