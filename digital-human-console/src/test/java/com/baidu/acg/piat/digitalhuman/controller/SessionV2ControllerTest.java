package com.baidu.acg.piat.digitalhuman.controller;

import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import com.baidu.acg.piat.digitalhuman.console.controller.SessionV2Controller;
import com.baidu.acg.piat.digitalhuman.console.model.session.SessionListRequest;
import com.baidu.acg.piat.digitalhuman.console.model.session.SessionRequest;
import com.baidu.acg.piat.digitalhuman.console.service.session.SessionService;

/**
 * Created on 2020/7/24 16:48.
 *
 * <AUTHOR>
 */
public class SessionV2ControllerTest {

    @InjectMocks
    private SessionV2Controller sessionController;

    @Mock
    private SessionService sessionService;

    @BeforeEach
    public void setup() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void list() {
        var sessionListRequest = new SessionListRequest();
        sessionListRequest.setAppId("appId");
        sessionListRequest.setSessionId("sessionId");
        sessionController.list(sessionListRequest);
        verify(sessionService, times(1)).list("appId", "sessionId", 1, 20);
    }

    @Test
    public void delete() {
        var sessionRequest = new SessionRequest();
        sessionRequest.setAppId("appId");
        sessionRequest.setSessionId("sessionId");
        sessionController.delete(sessionRequest);
        verify(sessionService, times(1)).delete("appId", "sessionId");

    }

    @Test
    public void get() {
        var sessionRequest = new SessionRequest();
        sessionRequest.setAppId("appId");
        sessionRequest.setSessionId("sessionId");
        sessionController.get(sessionRequest);
        verify(sessionService, times(1)).detail("appId", "sessionId");

    }
}
