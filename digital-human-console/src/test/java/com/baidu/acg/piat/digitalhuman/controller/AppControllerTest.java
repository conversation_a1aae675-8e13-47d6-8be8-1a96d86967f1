package com.baidu.acg.piat.digitalhuman.controller;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.mock.web.MockHttpServletRequest;

import com.baidu.acg.piat.digitalhuman.common.access.AccessApp;
import com.baidu.acg.piat.digitalhuman.common.exception.DigitalHumanCommonException;
import com.baidu.acg.piat.digitalhuman.console.controller.AppController;
import com.baidu.acg.piat.digitalhuman.console.model.app.AppListRequest;
import com.baidu.acg.piat.digitalhuman.console.model.common.AuthorizationInfo;
import com.baidu.acg.piat.digitalhuman.console.service.access.AccessControlService;

/**
 * Created on 2020/7/22 21:32.
 *
 * <AUTHOR>
 */
public class AppControllerTest {

    @InjectMocks
    private AppController appController;

    @Mock
    private AccessControlService accessControlService;

    private MockHttpServletRequest httpServletRequest = new MockHttpServletRequest();

    private AccessApp accessApp;

    @BeforeEach
    public void setup() {
        MockitoAnnotations.initMocks(this);
        appController = new AppController(accessControlService);
        accessApp = AccessApp.builder()
                .appId("appId")
                .characterImage("A2A")
                .description("description")
                .maxIdleInSecond(120)
                .userId("userId")
                .name("name")
                .enabled(true)
                .build();
    }

    @Test
    public void createTest() {
        httpServletRequest.setAttribute("authorization", new AuthorizationInfo());
        assertEquals(0, appController.createApp(httpServletRequest, accessApp).getCode());
    }

    @Test
    public void createWithNoAuth() {
        assertThrows(DigitalHumanCommonException.class, () -> appController.createApp(httpServletRequest, accessApp));
    }

    @Test
    public void deleteWithNoAuth() {
        assertThrows(DigitalHumanCommonException.class, () -> appController.deleteApp(httpServletRequest, accessApp));
    }

    @Test
    public void getWithNoAuth() {
        assertThrows(DigitalHumanCommonException.class, () -> appController.getApp(httpServletRequest, accessApp));
    }

    @Test
    public void listWithNoAuth() {
        assertThrows(DigitalHumanCommonException.class, () -> appController.listAll(httpServletRequest,
                new AppListRequest()));
    }

    @Test
    public void updateWithNoAuth() {
        assertThrows(DigitalHumanCommonException.class, () -> appController.updateApp(httpServletRequest,
                accessApp));
    }
}
