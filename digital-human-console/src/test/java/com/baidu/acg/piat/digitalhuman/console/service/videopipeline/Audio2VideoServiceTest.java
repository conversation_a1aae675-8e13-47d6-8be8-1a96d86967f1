// Copyright (C) 2020 Baidu Inc. All rights reserved.
package com.baidu.acg.piat.digitalhuman.console.service.videopipeline;

import com.baidu.acg.piat.digitalhuman.tts.cache.TtsCache;
import com.baidu.acg.piat.digitalhuman.tts.model.TtsQueryParams;
import com.baidu.acg.piat.digitalhuman.tts.model.TtsResult;
import org.apache.commons.codec.digest.DigestUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.List;

/**
 * Audio2VideoServiceTest
 *
 * <AUTHOR>
 * @since 2020-06-03
 */
class Audio2VideoServiceTest {

    private TtsQueryParams ttsQueryParams;

    private TtsCache ttsCacheRedis ;

    private Audio2VideoService audio2VideoService;

    @BeforeEach
    public void init() {
//        Mockito.when()
//        Mockito.doNothing().when(ttsCacheRedis).cache(any(), any());
        ttsQueryParams = TtsQueryParams.builder()
                .lan("zh").pdt("993").ctp("1").cuid("test").aue("1").per("100")
                .spd("1").pit("1").vol("1").xml("1").build();
        ttsCacheRedis = new TtsCache() {
            @Override
            public TtsResult retrieve(String text) {
                return null;
            }

            @Override
            public void cache(String text, TtsResult result) {

            }
        };
        audio2VideoService = new Audio2VideoService(List.of(ttsQueryParams), ttsCacheRedis);

    }

    @Test
    void generateToken() {
        byte[] bytes = new byte[] {0, 1, 3, 4};

        String token = audio2VideoService.generateToken(bytes);

        Assertions.assertEquals(DigestUtils.md5Hex(bytes).toUpperCase(), token);

    }



}