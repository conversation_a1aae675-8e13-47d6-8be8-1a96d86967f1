package com.baidu.acg.piat.digitalhuman.console.service.sce;

import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

import com.baidu.acg.piat.digitalhuman.common.project.Project;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.Collections;

import com.baidu.acg.piat.digitalhuman.common.model.PageResult;
import com.baidu.acg.piat.digitalhuman.console.service.sce.impl.ProjectServiceImpl;
import com.baidu.acg.piat.digitalhuman.plat.client.PlatformClient;

/**
 * Created on 2021/1/25 11:40.
 *
 * <AUTHOR>
 */
public class ProjectServiceImplTest {

    @InjectMocks
    private ProjectServiceImpl projectService;

    @Mock
    private PlatformClient platformClient;

    Project project;

    @BeforeEach
    public void init() {
        MockitoAnnotations.initMocks(this);

        project = Project.builder()
                .userId("userid")
                .name("name")
                .build();
    }

    @Test
    public void testApply() {
        projectService.apply("projectId", "projectRelease", Collections.singletonList("appId"));
    }

    @Test
    public void testList() {
        when(platformClient.listWithMaxProjectVersion(anyString(), anyInt(), anyInt(), anyInt(), anyString())).thenReturn(new PageResult<>());
        projectService.list("userId", "", 1, 1, 20, "all");

        when(platformClient.getProjectListByUserIdNameAndVersion(anyString(), anyString(), any(),
                anyInt(), anyInt(), anyInt(), anyString())).thenReturn(new PageResult<>());
        projectService.list("userId", "name", 1, 1, 20, "all");
    }

    @Test
    public void deleteByName() {
        projectService.deleteByName(project.getName(), "userid", 1);
        verify(platformClient, times(1)).deleteProjectByName(project.getName(), "userid", 1);
    }
}
