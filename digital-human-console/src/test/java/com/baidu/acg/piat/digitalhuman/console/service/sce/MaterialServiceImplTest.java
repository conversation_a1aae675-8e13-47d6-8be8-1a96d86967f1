package com.baidu.acg.piat.digitalhuman.console.service.sce;

import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.google.common.collect.Lists;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import com.baidu.acg.piat.digitalhuman.common.material.Material;
import com.baidu.acg.piat.digitalhuman.common.model.PageResult;
import com.baidu.acg.piat.digitalhuman.console.service.sce.impl.MaterialServiceImpl;
import com.baidu.acg.piat.digitalhuman.plat.client.PlatformClient;

/**
 * Created on 2021/8/11 3:38 下午
 *
 * <AUTHOR>
 */

class MaterialServiceImplTest {

    @InjectMocks
    private MaterialServiceImpl materialService;

    @Mock
    private PlatformClient platformClient;

    private Material material;

    @BeforeEach
    public void init() {
        MockitoAnnotations.initMocks(this);

        material = Material.builder()
                .userId("userid")
                .name("name")
                .build();
    }

    @Test
    void create() {
        materialService.create(material);
        verify(platformClient, times(1)).createMaterial(material);
    }

    @Test
    void delete() {
        materialService.delete(material.getId());
        verify(platformClient, times(1)).deleteMaterial(material.getId());
    }

    @Test
    void update() {
        materialService.update(material);
        verify(platformClient, times(1)).updateMaterial(material.getId(), material);
    }

    @Test
    void detail() {
        materialService.detail(material.getId());
        verify(platformClient, times(1)).detailMaterial(material.getId());
    }

    @Test
    void list() {
        when(platformClient.listMaterial(anyString(), anyString(), anyList(), anyList(), anyInt(), anyInt()))
                .thenReturn(new PageResult<Material>(1, 20, 100, null));
        materialService.list("userId", "name", Lists.newArrayList("image"),
                Lists.newArrayList("positionId"), 1, 20);
        verify(platformClient, times(1)).listMaterial(anyString(), anyString(), anyList(), anyList(),
                anyInt(), anyInt());
    }
}