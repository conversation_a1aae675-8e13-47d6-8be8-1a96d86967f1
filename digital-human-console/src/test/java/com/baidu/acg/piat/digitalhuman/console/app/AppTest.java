package com.baidu.acg.piat.digitalhuman.console.app;

import com.baidu.acg.piat.digitalhuman.common.OkClient;
import com.baidu.acg.piat.digitalhuman.common.access.AccessApp;
import com.baidu.acg.piat.digitalhuman.common.quota.ResourceQuota;
import com.baidu.acg.piat.digitalhuman.console.model.common.PageRequest;
import com.google.gson.Gson;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import java.util.HashMap;
import java.util.Map;

/**
 *  App管理的相关单测
 * @Author: <EMAIL>
 * @Date: 2020/4/20
 */
@Slf4j
public class AppTest extends OkClient {

    String userId = "5e9e85bd12162074a44e68cd";
    String name = "NGD";
    String description = "a test app for testing";
    String auth = "BDH efcc35f7-79ed-4ef5-9972-f18c939f809e/eb0b0adc762d52ca200bd9f95558173a8b70b"
            + "b0318ab23dc8b9112c200d644f4/2020-12-03T10:15:30+01:00[Europe/Paris]";
    String appId = "efcc35f7-79ed-4ef5-9972-f18c939f809e";


    // 创建app
    @Test
    public void createApp() {
        String url = baseUrl + "/api/digitalhuman/console/v1/app/create";
        Map<String, String> tags = new HashMap<>();
        ResourceQuota resourceQuota = new ResourceQuota();
        resourceQuota.setRoomLimits(100);
        AccessApp accessApp = AccessApp.buildCreateRequest(userId, name, description, tags, resourceQuota);
        Gson gson = new Gson();
        String json = gson.toJson(accessApp);
        post(url, json, auth);
    }

    // 删除app
    @Test
    public void deleteApp() {
        String url = baseUrl + "/api/digitalhuman/console/v1/app/delete";
        AccessApp accessApp = new AccessApp();
        accessApp.setUserId(userId);
        accessApp.setAppId(appId);
        Gson gson = new Gson();
        String json = gson.toJson(accessApp);
        post(url, json, auth);
    }

    // 更新app

    @Test
    public void updateApp() {
        String url = baseUrl + "/api/digitalhuman/console/v1/app/update";
        AccessApp accessApp = new AccessApp();

        Map<String, String> map = new HashMap<>();
        map.put("backgroundImageId", "http://127.0.0.1:8080/xxxxx");
        accessApp.setTags(map);
        accessApp.setUserId(userId);
        accessApp.setAppId(appId);
        accessApp.setEnabled(true);
        ResourceQuota resourceQuota = new ResourceQuota();
        resourceQuota.setRoomLimits(20);
        accessApp.setResourceQuota(resourceQuota);
        Gson gson = new Gson();
        String json = gson.toJson(accessApp);
        post(url, json, auth);
    }

    // 展示用户所有的app

    @Test
    public void listApp() {
        String url = baseUrl + "/api/digitalhuman/console/v1/app/list";
        PageRequest page = new PageRequest();
        page.setPageNo(1);
        page.setPageSize(10);
        Gson gson = new Gson();
        String json = gson.toJson(page);
        post(url, json, auth);
    }

    // app明细

    @Test
    public void appDetail() {
        String url = baseUrl + "/api/digitalhuman/console/v1/app/detail";
        AccessApp accessApp = new AccessApp();
        accessApp.setAppId(appId);
        Gson gson = new Gson();
        String json = gson.toJson(accessApp);
        post(url, json, auth);
    }

}
