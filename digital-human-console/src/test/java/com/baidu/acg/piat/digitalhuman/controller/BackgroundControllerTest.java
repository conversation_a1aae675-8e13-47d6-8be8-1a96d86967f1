package com.baidu.acg.piat.digitalhuman.controller;

import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import com.baidu.acg.dh.user.client.model.vo.UserGetResVO;
import com.baidu.acg.piat.digitalhuman.console.controller.sce.BackgroundController;
import com.baidu.acg.piat.digitalhuman.console.model.background.BackgroundListRequest;
import com.baidu.acg.piat.digitalhuman.console.model.background.BackgroundRequest;
import com.baidu.acg.piat.digitalhuman.console.service.character.BackgroundService;

/**
 * Created on 2020/7/22 21:32.
 *
 * <AUTHOR>
 */
public class BackgroundControllerTest {

    @InjectMocks
    private BackgroundController backgroundController;

    @Mock
    private BackgroundService backgroundService;

    private UserGetResVO userGetResVO;

    @BeforeEach
    public void setup() {
        MockitoAnnotations.initMocks(this);

        userGetResVO = UserGetResVO.builder()
                .uid("uid")
                .userName("name")
                .password("password")
                .build();
    }

    @Test
    public void testGetBackground() {
        var backgroundRequest = new BackgroundRequest();
        backgroundRequest.setUserId("userId");
        backgroundRequest.setName("name");
        backgroundRequest.setImageBase64("imageBase64");
        backgroundRequest.setBackgroundImageId("backgroundImageId");
        backgroundController.get(backgroundRequest, "uid");
        verify(backgroundService, times(1)).get("backgroundImageId");

    }

    @Test
    public void testDeleteBackground() {
        var backgroundRequest = new BackgroundRequest();
        backgroundRequest.setUserId("userId");
        backgroundRequest.setName("name");
        backgroundRequest.setImageBase64("imageBase64");
        backgroundRequest.setBackgroundImageId("backgroundImageId");
        backgroundController.delete(backgroundRequest, "uid");
        verify(backgroundService, times(1)).delete("backgroundImageId");

    }

    @Test
    public void testUploadBackground() {
        var backgroundRequest = new BackgroundRequest();
        backgroundRequest.setUserId("uid");
        backgroundRequest.setName("name");
        backgroundRequest.setImageBase64("imageBase64");
        backgroundRequest.setBackgroundImageId("backgroundImageId");
        backgroundController.upload(backgroundRequest, "uid");
        verify(backgroundService, times(1)).upload("uid", backgroundRequest);

    }

    @Test
    public void testListBackgrounds() {
        var backgroundListRequest = new BackgroundListRequest();
        backgroundListRequest.setUserId("userId");
        backgroundController.list(backgroundListRequest, "uid");
        verify(backgroundService, times(1)).list("uid", 1, 20);
    }

}
