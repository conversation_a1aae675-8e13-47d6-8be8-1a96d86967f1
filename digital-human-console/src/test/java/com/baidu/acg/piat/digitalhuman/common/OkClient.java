package com.baidu.acg.piat.digitalhuman.common;

import okhttp3.Call;
import okhttp3.FormBody;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import java.util.concurrent.TimeUnit;

/**
 * @Author: <EMAIL>
 * @Date: 2020/4/20
 */
public class OkClient {

   public static String baseUrl = "http://127.0.0.1:8080";

   public static OkHttpClient okHttpClient  = new OkHttpClient.Builder()
            .connectTimeout(10, TimeUnit.SECONDS)
            .writeTimeout(10,TimeUnit.SECONDS)
            .readTimeout(20, TimeUnit.SECONDS)
            .build();


   public static void post(String url, String json, String authorization) {
      RequestBody requestBody = FormBody.create(MediaType.parse("application/json; charset=utf-8"), json);
      Request request = new Request.Builder().url(url)
              .post(requestBody)
              .addHeader("authorization", authorization).build();

      Call call = okHttpClient.newCall(request);

      // 同步打印结果
      try {
         System.out.println("result sync:" + call.execute().body().string());
      } catch (Exception e) {
         e.printStackTrace();
      }


      //加入队列 异步操作

//      call.enqueue(new Callback() {
//         //请求错误回调方法
//         @Override
//         public void onFailure(Call call, IOException e) {
//            System.out.println("连接失败");
//         }
//         @Override
//         public void onResponse(Call call, Response response) throws IOException {
//            System.out.println("result:" + response.body().string());
//         }
//      });

   }

}
