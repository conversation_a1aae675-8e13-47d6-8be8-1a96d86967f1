package com.baidu.acg.piat.digitalhuman.console.controller.sce;

import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.verify;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import com.baidu.acg.dh.user.client.model.vo.UserGetResVO;
import com.baidu.acg.piat.digitalhuman.console.service.character.CharacterService;

/**
 * Created on 2021/10/12 8:03 下午
 *
 * <AUTHOR>
 */

class SceCharacterControllerTest {

    @InjectMocks
    private SceCharacterController sceCharacterController;

    @Mock
    private CharacterService characterService;

    private UserGetResVO userGetResVO;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.initMocks(this);

        userGetResVO = UserGetResVO.builder()
                .uid("uid")
                .build();
    }

    @Test
    void characterImageVisibleForSce() {
        sceCharacterController.characterImageVisibleForSce("uid");
        verify(characterService).characterImagesVisibleForSce(anyString());
    }
}