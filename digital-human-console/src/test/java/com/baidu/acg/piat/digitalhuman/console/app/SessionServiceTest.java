package com.baidu.acg.piat.digitalhuman.console.app;

import com.baidu.acg.piat.digitalhuman.common.cloud.CloudRtcConnection;
import com.baidu.acg.piat.digitalhuman.common.cloud.SessionAcquireResult;
import com.baidu.acg.piat.digitalhuman.common.model.PageResponse;
import com.baidu.acg.piat.digitalhuman.common.model.PageResult;
import com.baidu.acg.piat.digitalhuman.common.session.SessionStatus;
import com.baidu.acg.piat.digitalhuman.console.model.session.SessionListRequest;
import com.baidu.acg.piat.digitalhuman.console.service.cloud.CloudHttpClient;
import com.baidu.acg.piat.digitalhuman.console.service.cloud.CloudHttpService;
import com.baidu.acg.piat.digitalhuman.console.service.session.SessionService;
import com.baidu.acg.piat.digitalhuman.console.service.session.SessionServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import retrofit2.Call;
import retrofit2.Response;

import java.io.IOException;
import java.util.Collections;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

/**
 * Created on 2020/07/01 11:15.
 *
 * <AUTHOR>
 */
@Slf4j
public class SessionServiceTest {

    private SessionService sessionService;

    private CloudHttpClient cloudHttpClient;

    @Mock
    private CloudHttpService cloudHttpService;

    @Mock
    private Call<PageResponse<SessionAcquireResult>> mockPageResponse;

    @Mock
    private Call<SessionAcquireResult> mockResponse;

    private static final String TEST_APP_ID = "test_app_id";
    private static final String TEST_SESSION_ID = "test_session_id";
    private static final String TEST_SESSION_TOKEN = "test_session_token";
    private static final String TEST_CHARACTER = "test_character";

    private static final String TEST_RTC_APP_ID = "test_rtc_app_id";
    private static final String TEST_RTC_ROOM_NAME = "test_rtc_room_name";
    private static final String TEST_RTC_CLIENT_ID = "test_rtc_client_id";
    private static final String TEST_RTC_CLIENT_TOKEN = "test_rtc_client_token";
    private static final String TEST_RTC_SERVER_URL = "http://127.0.0.1:8080";


    @BeforeEach
    public void init() throws IOException {
        MockitoAnnotations.initMocks(this);
        cloudHttpClient = new CloudHttpClient("");
        sessionService = new SessionServiceImpl(cloudHttpClient);
        cloudHttpClient.httpService = cloudHttpService;
    }

    @Test
    public void list() {

        SessionListRequest request = new SessionListRequest();
        request.setAppId(TEST_APP_ID);
        request.setPageNo(1);
        request.setPageSize(10);

        var sessionAcquireResult = SessionAcquireResult.builder()
                .sessionId(TEST_SESSION_ID)
                .status(SessionStatus.CLOSED)
                .character(TEST_CHARACTER)
                .sessionToken(TEST_SESSION_TOKEN)
                .rtcConnection(CloudRtcConnection.builder()
                        .appId(TEST_RTC_APP_ID)
                        .clientId(TEST_RTC_CLIENT_ID)
                        .clientToken(TEST_RTC_CLIENT_TOKEN)
                        .roomName(TEST_RTC_ROOM_NAME)
                        .rtcServerUrl(TEST_RTC_SERVER_URL).build())
                .build();

        var pageResponse = PageResponse.<SessionAcquireResult>builder()
                .page(PageResult.<SessionAcquireResult>builder()
                        .pageNo(1)
                        .pageSize(10)
                        .totalCount(1)
                        .result(Collections.singletonList(sessionAcquireResult)).build())
                .build();

        Response<PageResponse<SessionAcquireResult>> response = Response.success(pageResponse);
        try {
            Mockito.when(mockPageResponse.execute()).thenReturn(response);
        } catch (IOException e) {
            e.printStackTrace();
        }

        when(cloudHttpService.findPageByAppIdAndSessionId(anyString(), anyString(), anyInt(), anyInt()))
                .thenReturn(mockPageResponse);

        var result = sessionService.list(TEST_APP_ID, TEST_SESSION_ID, 1, 10);

        assertEquals(result.getPage().getPageNo(), 1);
        assertEquals(result.getPage().getPageSize(), 10);
        assertEquals(result.getPage().getTotalCount(), 1);
        assertEquals(result.getPage().getResult().size(), 1);
        assertEquals(result.getPage().getResult().get(0).getCharacter(), TEST_CHARACTER);
        assertEquals(result.getPage().getResult().get(0).getSessionId(), TEST_SESSION_ID);
        assertEquals(result.getPage().getResult().get(0).getStatus(), SessionStatus.CLOSED);
        assertEquals(result.getPage().getResult().get(0).getRtcConnection().getAppId(), TEST_RTC_APP_ID);
        assertEquals(result.getPage().getResult().get(0).getRtcConnection().getClientId(), TEST_RTC_CLIENT_ID);
        assertEquals(result.getPage().getResult().get(0).getRtcConnection().getClientToken(), TEST_RTC_CLIENT_TOKEN);
        assertEquals(result.getPage().getResult().get(0).getRtcConnection().getRoomName(), TEST_RTC_ROOM_NAME);
        assertEquals(result.getPage().getResult().get(0).getRtcConnection().getRtcServerUrl(), TEST_RTC_SERVER_URL);
    }


    @Test
    public void get() {

        var sessionAcquireResult = SessionAcquireResult.builder()
                .sessionId(TEST_SESSION_ID)
                .status(SessionStatus.CLOSED)
                .character(TEST_CHARACTER)
                .sessionToken(TEST_SESSION_TOKEN)
                .rtcConnection(CloudRtcConnection.builder()
                        .appId(TEST_RTC_APP_ID)
                        .clientId(TEST_RTC_CLIENT_ID)
                        .clientToken(TEST_RTC_CLIENT_TOKEN)
                        .roomName(TEST_RTC_ROOM_NAME)
                        .rtcServerUrl(TEST_RTC_SERVER_URL).build())
                .build();

        Response<SessionAcquireResult> response = Response.success(sessionAcquireResult);
        try {
            Mockito.when(mockResponse.execute()).thenReturn(response);
        } catch (IOException e) {
            e.printStackTrace();
        }

        when(cloudHttpService.findBySessionIdAndAppId(anyString(), anyString()))
                .thenReturn(mockResponse);

        var result = sessionService.detail(TEST_APP_ID, TEST_SESSION_ID);

        assertEquals(result.getSessionId(), TEST_SESSION_ID);
        assertEquals(result.getCharacter(), TEST_CHARACTER);
        assertEquals(result.getStatus(), SessionStatus.CLOSED);
        assertEquals(result.getRtcConnection().getAppId(), TEST_RTC_APP_ID);
        assertEquals(result.getRtcConnection().getRoomName(), TEST_RTC_ROOM_NAME);
        assertEquals(result.getRtcConnection().getClientId(), TEST_RTC_CLIENT_ID);
        assertEquals(result.getRtcConnection().getClientToken(), TEST_RTC_CLIENT_TOKEN);
        assertEquals(result.getRtcConnection().getRtcServerUrl(), TEST_RTC_SERVER_URL);

    }

}
