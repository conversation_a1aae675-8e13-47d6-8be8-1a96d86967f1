package com.baidu.acg.piat.digitalhuman.console.interceptors;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.mock.web.MockHttpServletResponse;

import javax.servlet.http.HttpServletResponse;

import com.baidu.acg.piat.digitalhuman.common.access.AccessUser;
import com.baidu.acg.piat.digitalhuman.console.model.common.AuthorizationInfo;
import com.baidu.acg.piat.digitalhuman.plat.client.PlatformClient;

/**
 * AuthV2InterceptorTest
 *
 * <AUTHOR>
 * @since 2021/01/17
 */
class AuthV2InterceptorTest {

    @InjectMocks
    private AuthV2Interceptor authV2Interceptor;

    @Mock
    private PlatformClient platformClient;

    private static final String USER_ID = "auser";
    private static final String USER_NAME = "ausername";
    private static final String PASSWORD = "apassword";
    private static final AccessUser USER =
            AccessUser.builder().userId(USER_ID).name(USER_NAME).password(PASSWORD).build();
    private static final String SIGNATURE = "241d379bd7e28186f86f92364d0abb5f1b37fe1b4555044912a512beafa6113a";

    @BeforeEach
    public void init() {
        MockitoAnnotations.initMocks(this);
        Mockito.when(platformClient.getUserByName(USER_NAME)).thenReturn(USER);
    }

    @Test
    public void preHandleTest() throws Exception {
        MockHttpServletRequest request = new MockHttpServletRequest();
        request.setRequestURI("/api/digitalhuman/console/v2");
        request.addHeader("Authorization", "BDH " + USER_NAME + "/" + SIGNATURE + "/" + "2050-10-23T09:28:55.207Z");
        HttpServletResponse response = new MockHttpServletResponse();
        Assertions.assertTrue(authV2Interceptor.preHandle(request, response, null));
        AuthorizationInfo authInfo = (AuthorizationInfo) request.getAttribute("authorization");
        Assertions.assertNotNull(authInfo);
        Assertions.assertEquals(USER_NAME, authInfo.getUserName());
        Assertions.assertEquals(PASSWORD, authInfo.getPassword());
        Assertions.assertEquals(USER_ID, authInfo.getUserId());
    }
}