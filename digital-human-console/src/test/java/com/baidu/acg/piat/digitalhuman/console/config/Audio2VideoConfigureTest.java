// Copyright (C) 2022 Baidu Inc. All rights reserved.

package com.baidu.acg.piat.digitalhuman.console.config;

import static org.junit.jupiter.api.Assertions.*;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

/**
 * Audio2VideoConfigureTest
 *
 * <AUTHOR> (<EMAIL>)
 */
class Audio2VideoConfigureTest {

    @Test
    void redisCacheConfig() {
        var configure = new Audio2VideoConfigure();
        var res = configure.redisCacheConfig();
        Assertions.assertNotNull(res);
    }
}