package com.baidu.acg.piat.digitalhuman.console.service.videopipeline;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.baidu.bce.iam.facade.model.bcepass.session.LoginUserInfo;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.function.Executable;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.Spy;
import org.mockito.invocation.InvocationOnMock;
import org.mockito.stubbing.Answer;

import java.util.List;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.TimeUnit;

import com.baidu.acg.piat.digitalhuman.common.console.video.ProgressResult;
import com.baidu.acg.piat.digitalhuman.common.console.video.ProgressStatus;
import com.baidu.acg.piat.digitalhuman.common.console.video.Text2VideoRequest;
import com.baidu.acg.piat.digitalhuman.common.console.video.VideoSubmitRequest;
import com.baidu.acg.piat.digitalhuman.common.exception.DigitalHumanCommonException;
import com.baidu.acg.piat.digitalhuman.common.model.Response;
import com.baidu.acg.piat.digitalhuman.console.config.VideoPipelineConfigure;
import com.baidu.acg.piat.digitalhuman.videopipeline.client.VideoProgressHttpClient;
import com.baidu.acg.pie.digitalhuman.client.DhClient;
import com.baidu.acg.pie.digitalhuman.client.EventCallback;
import com.baidu.acg.pie.digitalhuman.client.exception.DigitalHumanException;
import com.baidu.acg.pie.digitalhuman.client.model.EventData;
import com.baidu.acg.pie.digitalhuman.client.model.SessionResult;
import com.baidu.acg.pie.digitalhuman.client.model.response.DhResponse;

/**
 * Created on 2020/7/27 13:59.
 *
 * <AUTHOR>
 */
public class VideoPipelineServiceTest {

    @Mock
    private DhClient dhClient;

    @Spy
    private VideoPipelineConfigure.CloudConfig cloudConfig = new VideoPipelineConfigure.CloudConfig();

    @Mock
    private VideoDhClientFactory videoDhClientFactory;

    @Mock
    private VideoProgressHttpClient videoProgressHttpClient;

    @Spy
    private VideoPipelineService.FailedStatusAdapter failedStatusAdapter =
            new VideoPipelineService.FailedStatusAdapter(new VideoPipelineConfigure.ProgressConfig());


    @InjectMocks
    private VideoPipelineService videoPipelineService;

    @Mock
    private ZkLockService zkLockService;

    private String appId = "testAppId";
    private String appKey = "testAppKey";
    private String sessionId = "testSessionId";
    private String videoId = "testVideoId";

    private Text2VideoRequest text2VideoRequest = new Text2VideoRequest();

    private SessionResult acquireResult = SessionResult.builder().sessionId(sessionId).build();
    private ProgressResult progressResult = ProgressResult.builder()
            .videoId(videoId).status(ProgressStatus.SCHEDULED).build();

    private CompletableFuture<DhResponse> ackFuture;

    private ScheduledFuture<?> ackTask;

    private ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(2);

    private VideoSubmitRequest videoSubmitRequest;

    @BeforeEach
    public void setup() {
        MockitoAnnotations.initMocks(this);

        when(zkLockService.tryLock(any())).thenReturn(Optional.of("token"));
        when(zkLockService.unlock(any(), any())).thenReturn(true);

        cloudConfig.setAckWaitingMillis(5000);

        cloudConfig.setRenderStartWaitingMillis(3000);

        ackFuture = new CompletableFuture<>();

        text2VideoRequest.setText("测试");

        videoSubmitRequest = new VideoSubmitRequest();
        videoSubmitRequest.setAppId("appId");
        videoSubmitRequest.setAppKey("appKey");
        videoSubmitRequest.setTexts(List.of("test1", "test2"));

        when(videoDhClientFactory.createDhClient(any(), any(), any())).thenReturn(dhClient);

        when(videoProgressHttpClient.initProgress(any())).thenReturn(Response.success(progressResult));
        when(videoProgressHttpClient.submitProgress(any())).thenReturn(Response.success(List.of(progressResult)));
        when(dhClient.acquire()).thenReturn(acquireResult);

        when(videoProgressHttpClient.updateProgress(any())).thenReturn(Response.success(progressResult));
        when(videoProgressHttpClient.pollProgress(any()))
                .thenReturn(Response.success(ProgressResult.builder().videoId(videoId)
                        .status(ProgressStatus.RENDERING).build()));

        Mockito.doAnswer(new Answer() {
            @Override
            public Object answer(InvocationOnMock invocation) throws Throwable {
                EventCallback eventCallback = ((EventCallback) invocation.getArgument(1));
                scheduler.schedule(() -> eventCallback.onEvent(1, new EventData("RENDER_START", "")), 2,
                        TimeUnit.SECONDS);
                scheduler.schedule(() -> eventCallback.onEvent(1, new EventData("RENDER_COMPLETED", "")), 5,
                        TimeUnit.SECONDS);
                return ackFuture;
            }

        }).when(dhClient).send(any(), any());

        ackTask = scheduler.schedule(() -> {
            ackFuture.complete(new DhResponse(1, 0, "ok", null));
        }, 1, TimeUnit.SECONDS);

    }

    @Test
    public void testSubmitInvliadArgs() {
        Assertions.assertThrows(IllegalArgumentException.class, new Executable() {
            @Override
            public void execute() throws Throwable {
                videoSubmitRequest.setAppId("");
                videoPipelineService.submit(videoSubmitRequest);

            }
        }, "invalidAppID");

        Assertions.assertThrows(IllegalArgumentException.class, new Executable() {
            @Override
            public void execute() throws Throwable {

                videoSubmitRequest.setAppKey("");
                videoPipelineService.submit(videoSubmitRequest);

            }
        }, "invalidAppKey");

        Assertions.assertThrows(IllegalArgumentException.class, new Executable() {
            @Override
            public void execute() throws Throwable {
                videoSubmitRequest.setTexts(List.of());
                videoPipelineService.submit(videoSubmitRequest);

            }
        }, "invalidTexts");

        Assertions.assertThrows(IllegalArgumentException.class, new Executable() {
            @Override
            public void execute() throws Throwable {
                videoSubmitRequest.setTexts(List.of(""));
                videoPipelineService.submit(videoSubmitRequest);

            }
        }, "invalidText");

    }

    @Test
    public void successSubmit() {

        videoPipelineService.submit(videoSubmitRequest);

    }


    @Test
    public void startEmptyText() {

        Assertions.assertThrows(IllegalArgumentException.class, new Executable() {
            @Override
            public void execute() throws Throwable {
                videoPipelineService.start(appId, appKey, new Text2VideoRequest());
            }
        }, "startEmptyText");

    }

    @Test
    public void startWithSubmitFailed() {

        when(videoProgressHttpClient.submitProgress(any())).thenReturn(Response.success(List.of()));

        Assertions.assertThrows(DigitalHumanCommonException.class, new Executable() {
            @Override
            public void execute() throws Throwable {
                videoPipelineService.start(appId, appKey, text2VideoRequest);
            }
        }, "startSubmitFail");
    }

    @Test
    public void startSubmitFail() {

        when(videoProgressHttpClient.submitProgress(any())).thenReturn(Response.fail(1001, "error"));

        Assertions.assertThrows(DigitalHumanCommonException.class, new Executable() {
            @Override
            public void execute() throws Throwable {
                videoPipelineService.start(appId, appKey, text2VideoRequest);
            }
        }, "startSubmitFail");
    }

    @Test
    public void acquireSessionFailure() {

        Mockito.doThrow(DigitalHumanException.fail(1001, "no available resources")).when(dhClient).acquire();

        Assertions.assertThrows(DigitalHumanCommonException.class, new Executable() {
            @Override
            public void execute() throws Throwable {
                videoPipelineService.start(appId, appKey, text2VideoRequest);
            }
        }, "acquireSessionFailure");
    }

    @Test
    public void updateStatusFailed() {

        when(videoProgressHttpClient.updateProgress(any())).thenReturn(Response.fail(1002, "fail to update status"));

        Assertions.assertThrows(DigitalHumanCommonException.class, new Executable() {
            @Override
            public void execute() throws Throwable {
                videoPipelineService.start(appId, appKey, text2VideoRequest);
            }
        }, "updateStatusFailed");
    }

    @Test
    public void ackTimeout() {

        ackTask.cancel(true);

        Assertions.assertThrows(DigitalHumanCommonException.class, new Executable() {
            @Override
            public void execute() throws Throwable {
                videoPipelineService.start(appId, appKey, text2VideoRequest);
            }
        }, "ackTimeout");

    }

    @Test
    public void ackFailed() {

        ackTask.cancel(true);
        ackTask = scheduler.schedule(() -> {
            ackFuture.complete(new DhResponse(1, 1003, "mock failed", null));
        }, 1, TimeUnit.SECONDS);

        Assertions.assertThrows(DigitalHumanCommonException.class, new Executable() {
            @Override
            public void execute() throws Throwable {
                videoPipelineService.start(appId, appKey, text2VideoRequest);
            }
        }, "ackFailed");
    }

    @Test
    public void renderStartTimeout() {
        Mockito.doAnswer(new Answer() {
            @Override
            public Object answer(InvocationOnMock invocation) throws Throwable {
                EventCallback eventCallback = ((EventCallback) invocation.getArgument(1));
                //                scheduler.schedule(() -> eventCallback.onEvent(1, new EventData("RENDER_START", "")
                //                ), 2,
                //                        TimeUnit.SECONDS);
                //                scheduler.schedule(() -> eventCallback.onEvent(1, new EventData("RENDER_COMPLETED",
                //                "")), 5,
                //                        TimeUnit.SECONDS);
                return ackFuture;
            }

        }).when(dhClient).send(any(), any());

        Assertions.assertThrows(DigitalHumanCommonException.class, new Executable() {
            @Override
            public void execute() throws Throwable {
                videoPipelineService.start(appId, appKey, text2VideoRequest);
            }
        }, "renderStartTimeout");

    }

    @Test
    public void renderStartFail() {
        Mockito.doAnswer(new Answer() {
            @Override
            public Object answer(InvocationOnMock invocation) throws Throwable {
                EventCallback eventCallback = ((EventCallback) invocation.getArgument(1));
                scheduler.schedule(() -> eventCallback.onEvent(1, new EventData("RENDER_ERROR", "")), 2,
                        TimeUnit.SECONDS);
                //                                scheduler.schedule(() -> eventCallback.onEvent(1, new EventData
                //                                ("RENDER_COMPLETED", "")), 5,
                //                                        TimeUnit.SECONDS);
                return ackFuture;
            }

        }).when(dhClient).send(any(), any());

        Assertions.assertThrows(DigitalHumanCommonException.class, new Executable() {
            @Override
            public void execute() throws Throwable {
                videoPipelineService.start(appId, appKey, text2VideoRequest);
            }
        }, "renderStartTimeout");
    }

    @Test
    public void test_pretreatmentWithEmptyText() {
        Assertions.assertThrows(IllegalArgumentException.class, () -> {
            videoPipelineService.complexTextPretreatment(new Text2VideoRequest.ComplexText2VideoRequest(
                    new Text2VideoRequest("")));
        });

        Assertions.assertDoesNotThrow(() -> {

            Text2VideoRequest.ComplexText2VideoRequest complexText = new Text2VideoRequest.ComplexText2VideoRequest(
                    new Text2VideoRequest(),
                    List.of("123123", "<speak>123123</speak>",
                            "<speak interruptible=\"true\">123123</speak>", "<idle duration=\"1000\"/>",
                            "<client></client><speak>123123</speak>",
                            "<speak>123123</speak><client></client>"));

            videoPipelineService.complexTextPretreatment(complexText);
            Assertions.assertEquals(complexText.getContinueTexts().size(), 6);
            Assertions.assertEquals(complexText.getContinueTexts().get(0),
                    "<speak interruptible=\"false\">123123</speak>");
            Assertions.assertEquals(complexText.getContinueTexts().get(1),
                    "<speak interruptible=\"false\">123123</speak>");
            Assertions.assertEquals(complexText.getContinueTexts().get(2),
                    "<speak interruptible=\"false\">123123</speak>");
            Assertions.assertEquals(complexText.getContinueTexts().get(3),
                    "<speak interruptible=\"false\"><idle duration=\"1000\"/></speak>");
            Assertions.assertEquals(complexText.getContinueTexts().get(4),
                    "<speak interruptible=\"false\"><client></client><speak>123123</speak></speak>");
            Assertions.assertEquals(complexText.getContinueTexts().get(5),
                    "<speak interruptible=\"false\"><speak>123123</speak><client></client></speak>");
        });
    }

    @Test
    public void renderCompleted() throws ExecutionException, InterruptedException {

        CompletableFuture<EventData> renderCompleted = new CompletableFuture<>();
        Mockito.doAnswer(new Answer() {
            @Override
            public Object answer(InvocationOnMock invocation) throws Throwable {
                EventCallback eventCallback = ((EventCallback) invocation.getArgument(1));
                scheduler.schedule(() -> eventCallback.onEvent(1, new EventData("RENDER_START", "")), 1,
                        TimeUnit.SECONDS);
                scheduler.schedule(() -> {
                    EventData event = new EventData("RENDER_COMPLETED", "");
                    eventCallback.onEvent(1, event);
                    renderCompleted.complete(event);
                }, 5, TimeUnit.SECONDS);
                return ackFuture;
            }

        }).when(dhClient).send(any(), any());

        videoPipelineService.start(appId, appKey, text2VideoRequest);
        EventData result = renderCompleted.get();
        Assertions.assertEquals("RENDER_COMPLETED", result.getType());

    }

    @Test
    public void success() {

        ProgressResult result = videoPipelineService.start(appId, appKey, text2VideoRequest);
        System.out.println(result);
    }
}
