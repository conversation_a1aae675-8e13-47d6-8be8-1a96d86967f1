package com.baidu.acg.piat.digitalhuman.controller;

import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import com.baidu.acg.dh.user.client.model.vo.UserGetResVO;
import com.baidu.acg.piat.digitalhuman.common.access.AccessApp;
import com.baidu.acg.piat.digitalhuman.console.controller.RoomController;
import com.baidu.acg.piat.digitalhuman.console.model.room.RoomDetailRequest;
import com.baidu.acg.piat.digitalhuman.console.model.room.RoomListRequest;
import com.baidu.acg.piat.digitalhuman.console.model.room.RoomSessionDetailRequest;
import com.baidu.acg.piat.digitalhuman.console.model.room.RoomSessionDialogRequest;
import com.baidu.acg.piat.digitalhuman.console.model.room.RoomSessionListRequest;
import com.baidu.acg.piat.digitalhuman.console.service.room.RoomService;

/**
 * Created on 2020/7/22 21:32.
 *
 * <AUTHOR>
 */
public class RoomControllerTest {

    @InjectMocks
    private RoomController roomController;

    @Mock
    private RoomService roomService;

    private UserGetResVO userGetResVO;

    @BeforeEach
    public void setup() {
        MockitoAnnotations.initMocks(this);

        userGetResVO = UserGetResVO.builder()
                .uid("uid")
                .userName("name")
                .password("password")
                .build();
    }

    @Test
    public void testList() {
        var roomListRequest = new RoomListRequest();
        roomListRequest.setAppId("appId");
        roomListRequest.setRoomName("roomName");
        roomController.list(roomListRequest, "uid");
        verify(roomService, times(1)).list(1, 20, "uid", "appId", "roomName");

    }

    @Test
    public void testDetail() {
        var roomDetailRequest = new RoomDetailRequest();
        roomDetailRequest.setAppId("appId");
        roomDetailRequest.setRoomId("roomId");
        roomDetailRequest.setRoomName("roomName");
        roomController.detail(roomDetailRequest);
        verify(roomService, times(1)).detail("appId", "roomId");

    }

    @Test
    public void testUpdate() {
        var roomDetailRequest = new RoomDetailRequest();
        roomDetailRequest.setAppId("appId");
        roomDetailRequest.setRoomId("roomId");
        roomDetailRequest.setRoomName("roomName");
        roomController.update(roomDetailRequest);
        verify(roomService, times(1)).update(roomDetailRequest);

    }

    @Test
    public void testListSessions() {
        var roomSessionListRequest = new RoomSessionListRequest();
        roomSessionListRequest.setRoomId("roomId");
        roomController.listSessions(roomSessionListRequest);
        verify(roomService, times(1)).listSessions(1, 20, "roomId");
    }

    @Test
    public void testSessionDetail() {
        var roomSessionDetailRequest = new RoomSessionDetailRequest();
        roomSessionDetailRequest.setSessionId("sessionId");
        roomController.sessionDetail(roomSessionDetailRequest);
        verify(roomService, times(1)).getSession("sessionId");
    }

    @Test
    public void testListDialogsByRoomId() {
        var roomSessionDialogRequest = new RoomSessionDialogRequest();
        roomSessionDialogRequest.setSessionId("sessionId");
        roomController.listDialogsByRoomId(roomSessionDialogRequest);
        verify(roomService, times(1)).listDialogsBySessionId(1, 20, "sessionId");

        roomSessionDialogRequest = new RoomSessionDialogRequest();
        roomSessionDialogRequest.setRoomId("roomId");
        roomSessionDialogRequest.setSessionId(null);
        roomController.listDialogsByRoomId(roomSessionDialogRequest);
        verify(roomService, times(1)).listDialogsByRoomId(1, 20, "roomId");
    }
}
