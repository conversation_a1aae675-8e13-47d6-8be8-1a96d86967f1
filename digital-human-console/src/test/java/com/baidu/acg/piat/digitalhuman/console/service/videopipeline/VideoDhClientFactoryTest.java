// Copyright (C) 2020 Baidu Inc. All rights reserved.

package com.baidu.acg.piat.digitalhuman.console.service.videopipeline;

import io.grpc.Channel;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.Spy;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import com.baidu.acg.piat.digitalhuman.common.access.AccessApp;
import com.baidu.acg.piat.digitalhuman.common.console.video.Text2VideoParams;
import com.baidu.acg.piat.digitalhuman.common.console.video.Text2VideoRequest;
import com.baidu.acg.piat.digitalhuman.common.project.Project;
import com.baidu.acg.piat.digitalhuman.console.config.VideoPipelineConfigure;
import com.baidu.acg.piat.digitalhuman.plat.client.PlatformClient;
import com.baidu.acg.pie.digitalhuman.client.DhClient;
import com.baidu.acg.pie.digitalhuman.client.config.ClientConfig;

/**
 * VideoDhClientFactoryTest
 *
 * <AUTHOR> Zhensheng(<EMAIL>)
 * @since 2020-12-07
 */
class VideoDhClientFactoryTest {

    @Mock
    PlatformClient platformClient;

    @Spy
    VideoPipelineConfigure.CloudConfig cloudConfig;

    @Mock
    Channel channel;

    ExecutorService callbackExecutor = Executors.newFixedThreadPool(10);

    VideoDhClientFactory factory;

    @BeforeEach
    public void setUp() throws IllegalAccessException, NoSuchFieldException {

        MockitoAnnotations.initMocks(this);

        cloudConfig.setHost("localhost");
        cloudConfig.setPort(8090);
        factory = new VideoDhClientFactory(platformClient, cloudConfig);
        Field field = VideoDhClientFactory.class.getDeclaredField("channel");
        field.setAccessible(true);
        field.set(factory, channel);

    }

    @Test
    void createDhClient() {

        Text2VideoRequest request = new Text2VideoRequest();
        request.setText("你好");
        DhClient result =
                factory.createDhClient("testAppid", "testAppKey", request);

        Assertions.assertNotNull(result);

    }

    @Test
    public void buildClientConfigOfOutputVideoTest() throws Exception {
        Method method = factory.getClass().getDeclaredMethod("buildClientConfigOfOutputVideo",
                String.class, String.class, Text2VideoParams.class);
        method.setAccessible(true);
        String appId = "appid";
        String appKey = "appkey";
        Text2VideoParams request = new Text2VideoParams();
        String projectId = "projectid";
        String preset = "apreset";
        AccessApp app = AccessApp.builder().projectId(projectId).build();
        Project project = Project.builder().preset(preset).build();
        Mockito.when(cloudConfig.getHost()).thenReturn("");
        Mockito.when(platformClient.getApp(appId)).thenReturn(app);
        Mockito.when(platformClient.getProjectById(projectId)).thenReturn(project);
        ClientConfig clientConfig = (ClientConfig) method.invoke(factory, appId, appKey, request);
        Assertions.assertEquals(preset, clientConfig.getParameters().get("preset"));
    }

}