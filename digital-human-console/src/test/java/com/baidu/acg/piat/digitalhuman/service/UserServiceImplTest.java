package com.baidu.acg.piat.digitalhuman.service;

import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import org.glassfish.jersey.internal.util.Base64;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.mock.web.MockHttpServletRequest;

import com.baidu.acg.dh.user.client.UserClient;
import com.baidu.acg.dh.user.client.UserResult;
import com.baidu.acg.dh.user.client.model.vo.UserCreateReqVO;
import com.baidu.acg.dh.user.client.model.vo.UserCreateTokenReq;
import com.baidu.acg.dh.user.client.model.vo.UserGetResVO;
import com.baidu.acg.dh.user.client.model.vo.UserUpdateReqVO;
import com.baidu.acg.piat.digitalhuman.common.exception.DigitalHumanCommonException;
import com.baidu.acg.piat.digitalhuman.console.service.jwt.JwtTokenService;
import com.baidu.acg.piat.digitalhuman.console.service.user.UserServiceImpl;

/**
 * Created on 2021/2/4 13:46.
 *
 * <AUTHOR>
 */
public class UserServiceImplTest {

    @InjectMocks
    private UserServiceImpl userService;

    @Mock
    private UserClient userClient;

    @Mock
    private JwtTokenService jwtTokenService;

    private UserGetResVO userGetResVO;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.initMocks(this);

        userGetResVO = UserGetResVO.builder()
                .uid("uid")
                .userName("name")
                .password("cb825a05d743c50112becede14b2c132")
                .build();
    }

    @Test
    public void create() {

        UserResult<UserGetResVO> userResult = UserResult.<UserGetResVO> builder()
                .success(true)
                .result(userGetResVO)
                .build();
        when(userClient.getUserByName(anyString())).thenReturn(userResult);
        when(userClient.addUser(any())).thenReturn(userResult);

        var userCreateReqVo = new UserCreateReqVO();
        userCreateReqVo.setUserName("userName");
        userCreateReqVo.setPassword("password");
        userCreateReqVo.setDescription("desc");
        userCreateReqVo.setRole("role");

        userCreateReqVo.setPassword("QAZWSX@123");

        UserResult<UserGetResVO> getUserByNameResult = UserResult.<UserGetResVO> builder()
                .success(true)
                .result(null)
                .build();
        when(userClient.getUserByName(anyString())).thenReturn(getUserByNameResult);
        UserResult<UserGetResVO> addUserResult = UserResult.<UserGetResVO> builder()
                .success(true)
                .result(userGetResVO)
                .build();
        when(userClient.addUser(any())).thenReturn(addUserResult);
        userService.create(userCreateReqVo);

    }

    @Test
    public void testGetUserByName() {
        UserResult<UserGetResVO> getUserByNameResult = UserResult.<UserGetResVO> builder()
                .success(true)
                .result(null)
                .build();
        when(userClient.getUserByName(anyString())).thenReturn(getUserByNameResult);
        assertThrows(DigitalHumanCommonException.class, () -> userService.getUserByName("name"));

        getUserByNameResult.setResult(userGetResVO);
        userService.getUserByName("name");
    }

    @Test
    public void testGetUserById() {
        UserResult<UserGetResVO> getUserByNameResult = UserResult.<UserGetResVO> builder()
                .success(true)
                .result(null)
                .build();
        when(userClient.getUserById(anyString())).thenReturn(getUserByNameResult);
        assertThrows(DigitalHumanCommonException.class, () -> userService.getUserById("uid"));

        getUserByNameResult.setResult(userGetResVO);
        userService.getUserById("uid");
    }

    @Test
    public void testDeleteByUserName() {
        userService.deleteByUserName("name");
        verify(userClient, times(1)).delete("name");
    }

    @Test
    public void testUpdateByUserName() {
        UserResult<UserGetResVO> getUserByNameResult = UserResult.<UserGetResVO> builder()
                .success(true)
                .result(null)
                .build();
        when(userClient.getUserByName(anyString())).thenReturn(getUserByNameResult);

        var userUpdateReqVo = new UserUpdateReqVO();
        userUpdateReqVo.setUserName("name");
        userUpdateReqVo.setPassword("password");
        assertThrows(DigitalHumanCommonException.class, () -> userService.updateUserByName(userUpdateReqVo));
        verify(userClient, times(0)).update(userUpdateReqVo);

        getUserByNameResult.setResult(userGetResVO);
        when(userClient.getUserByName(anyString())).thenReturn(getUserByNameResult);
        assertThrows(DigitalHumanCommonException.class, () -> userService.updateUserByName(userUpdateReqVo));

        when(userClient.update(any())).thenReturn(getUserByNameResult);
        userUpdateReqVo.setPassword("QAZWSX@123");
        userService.updateUserByName(userUpdateReqVo);
    }

    @Test
    public void testCreateToken() {
        var userCreateTokenReq = new UserCreateTokenReq();
        userCreateTokenReq.setUserName("name");
        userCreateTokenReq.setPassword("password");
        userService.createToken(userCreateTokenReq);
    }

    @Test
    public void testLoginCheck() {
        var userCreateReqVo = new UserCreateReqVO();
        userCreateReqVo.setUserName("name");
        userCreateReqVo.setPassword("password");
        userCreateReqVo.setDescription("desc");
        userCreateReqVo.setRole("role");

        UserResult<UserGetResVO> getUserByNameResult = UserResult.<UserGetResVO> builder()
                .success(true)
                .result(null)
                .build();
        when(userClient.getUserByName(anyString())).thenReturn(getUserByNameResult);
        assertThrows(DigitalHumanCommonException.class, () -> userService.loginCheck(userCreateReqVo, new MockHttpServletRequest()));

        getUserByNameResult.setResult(userGetResVO);
        when(jwtTokenService.createToken(any())).thenReturn("token");
        assertThrows(DigitalHumanCommonException.class, () -> userService.loginCheck(userCreateReqVo, new MockHttpServletRequest()));

        userCreateReqVo.setPassword(Base64.encodeAsString("namepassword"));
        userService.loginCheck(userCreateReqVo, new MockHttpServletRequest());
    }

    @Test
    public void testLoginCheckForApi() {
        var userCreateReqVo = new UserCreateReqVO();
        userCreateReqVo.setUserName("name");
        userCreateReqVo.setPassword("password");
        userCreateReqVo.setDescription("desc");
        userCreateReqVo.setRole("role");

        UserResult<UserGetResVO> getUserByNameResult = UserResult.<UserGetResVO> builder()
                .success(true)
                .result(null)
                .build();
        when(userClient.getUserByName(anyString())).thenReturn(getUserByNameResult);
        assertThrows(DigitalHumanCommonException.class, () -> userService.loginCheckForAPI(userCreateReqVo));

        getUserByNameResult.setResult(userGetResVO);
        when(jwtTokenService.createToken(any())).thenReturn("token");
        userService.loginCheckForAPI(userCreateReqVo);
    }

    @Test
    public void testGetTokenExpireDate() {
        userService.getTokenExpireDate("token");
        verify(jwtTokenService, times(1)).getExpireDate("token");
    }

    @Test
    public void testIsTokenExpire() {
        userService.isTokenExpire("token");
        verify(jwtTokenService, times(1)).isExpire("token");
    }

}
