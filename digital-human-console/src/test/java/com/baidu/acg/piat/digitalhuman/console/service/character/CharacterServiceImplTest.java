package com.baidu.acg.piat.digitalhuman.console.service.character;

import com.baidu.acg.piat.digitalhuman.common.access.AccessUser;
import com.baidu.acg.piat.digitalhuman.common.character.BaseCharacterInfo;
import com.baidu.acg.piat.digitalhuman.common.entity.CharacterModel;
import com.baidu.acg.piat.digitalhuman.common.model.PageResult;
import com.baidu.acg.piat.digitalhuman.plat.client.PlatformClient;

import org.assertj.core.util.Lists;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

/**
 * Created on 2021/7/29 3:46 下午
 *
 * <AUTHOR>
 */

class CharacterServiceImplTest {

    @InjectMocks
    CharacterServiceImpl characterService;

    @Mock
    PlatformClient platformClient;

    private AccessUser accessUser;

    private CharacterModel characterMeta;

    private PageResult pageResult;

    @BeforeEach
    public void setup() {
        MockitoAnnotations.initMocks(this);

        accessUser = AccessUser.builder()
                .userId("userId")
                .name("name")
                .build();

        characterMeta = CharacterModel.builder()
                .characterId("id")
                .build();

        pageResult = PageResult.builder()
                .result(Lists.emptyList())
                .build();
    }

    @Test
    void findCharacterByCharacterImage() {
        var request = CharacterModel.builder().characterId("id")
            .facialList(List.of(new BaseCharacterInfo())).makeupList(List.of(new BaseCharacterInfo())).build();

        when(platformClient.findCharacterByType("characterImage", 0))
                .thenReturn(request);
        when(platformClient.findCharacterById("id")).thenReturn(request);
        when(platformClient.updateCharacterById("id", request)).thenReturn(request);
        var characterImage = characterService.findCharacterByCharacterImage("characterImage");

        characterService.update(characterImage);

        when(platformClient.findCharacterByType("characterImage2", 0))
                .thenReturn(CharacterModel.builder().facialList(new ArrayList<>())
                        .makeupList(new ArrayList<>()).build());
        var characterImage2 = characterService.findCharacterByCharacterImage("characterImage2");


        Assertions.assertNotNull(characterImage);
    }

    @Test
    void characterImagesVisibleForSce() {
        when(platformClient.findCharacterVisibleForSce(anyBoolean())).thenReturn(pageResult);
        when(platformClient.getUser(anyString())).thenReturn(accessUser);
        when(platformClient.findCharacterByType(anyString(), anyInt())).thenReturn(characterMeta);
        characterService.characterImagesVisibleForSce(anyString());

        accessUser.setTags(Map.of("sceVisibleCharacters", "[\"UE4_RENDER_LINUX\"]"));
        characterService.characterImagesVisibleForSce(anyString());

    }
}