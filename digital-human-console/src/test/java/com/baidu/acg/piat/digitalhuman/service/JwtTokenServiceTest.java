package com.baidu.acg.piat.digitalhuman.service;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.MockitoAnnotations;

import com.baidu.acg.dh.user.client.model.vo.UserCreateTokenReq;
import com.baidu.acg.piat.digitalhuman.console.service.jwt.JwtTokenService;

/**
 * Created on 2021/2/4 19:10.
 *
 * <AUTHOR>
 */
public class JwtTokenServiceTest {

    @InjectMocks
    private JwtTokenService jwtTokenService;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testCreate() {
        UserCreateTokenReq req = new UserCreateTokenReq();
        req.setUserName("name");
        req.setPassword("password");
        var token = jwtTokenService.createToken(req);
        assert token != null;
    }

    @Test
    public void testGetExpireDate() {
        UserCreateTokenReq req = new UserCreateTokenReq();
        req.setUserName("name");
        req.setPassword("password");
        var token = jwtTokenService.createToken(req);
        jwtTokenService.getExpireDate(token);

    }

    @Test
    public void testIsExpire() {
        UserCreateTokenReq req = new UserCreateTokenReq();
        req.setUserName("name");
        req.setPassword("password");
        var token = jwtTokenService.createToken(req);
        boolean expire = jwtTokenService.isExpire(token);
        assert expire;
    }


}
