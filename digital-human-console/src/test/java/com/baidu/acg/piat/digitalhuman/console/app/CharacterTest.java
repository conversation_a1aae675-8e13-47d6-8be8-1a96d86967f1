package com.baidu.acg.piat.digitalhuman.console.app;

import com.baidu.acg.piat.digitalhuman.common.OkClient;
import com.baidu.acg.piat.digitalhuman.console.model.character.BindingCharacterRequest;
import com.baidu.acg.piat.digitalhuman.console.model.background.PreviewThumbnailRequest;
import com.google.gson.Gson;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;

/**
 * 模型管理的相关单测
 *
 * @Author: <EMAIL>
 * @Date: 2020/4/20
 */

@Slf4j
public class CharacterTest extends OkClient {


    String auth = "BDH efcc35f7-79ed-4ef5-9972-f18c939f809e/eb0b0adc762d52ca200bd9f95558173a8b70bb031"
            + "8ab23dc8b9112c200d644f4/2020-12-03T10:15:30+01:00[Europe/Paris]";
    String appId = "efcc35f7-79ed-4ef5-9972-f18c939f809e";
    String backgroundImageId = "";
    String characterType = "";

    // 展示人像模型

    @Test
    public void findCharacterByCharacterImage() {
        String url = baseUrl + "/api/digitalhuman/console/v1/character/image";
        String characterImage = "characterImageType";

        Gson gson = new Gson();
        String json = gson.toJson(characterImage);
        post(url, json, auth);
    }

    // 展示App的人像

    @Test
    public void findCharacterByAppId() {
        String url = baseUrl + "/api/digitalhuman/console/v1/character/image/app";

        Gson gson = new Gson();
        String json = gson.toJson(appId);
        post(url, json, auth);
    }

    // 更新App绑定的人像和背景

    @Test
    public void update() {
        BindingCharacterRequest request = new BindingCharacterRequest();
        request.setAppId(appId);
        request.setBackgroundImageId(backgroundImageId);
        request.setCharacterImage(characterType);
        String url = baseUrl + "/api/digitalhuman/console/v1/character/image/update";

        Gson gson = new Gson();
        String json = gson.toJson(request);
        post(url, json, auth);
    }

    // 生成人像和背景合成的缩略图

    @Test
    public void preview() {
        PreviewThumbnailRequest request = new PreviewThumbnailRequest();
        request.setAppId(appId);
        request.setBackgroundImageId(backgroundImageId);
        request.setCharacterImage(characterType);
        String url = baseUrl + "/api/digitalhuman/console/v1/character/image/preview";

        Gson gson = new Gson();
        String json = gson.toJson(request);
        post(url, json, auth);
    }

}
