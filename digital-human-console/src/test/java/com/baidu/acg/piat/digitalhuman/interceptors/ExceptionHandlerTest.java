package com.baidu.acg.piat.digitalhuman.interceptors;

import com.baidu.acg.piat.digitalhuman.common.exception.DigitalHumanCommonException;
import com.baidu.acg.piat.digitalhuman.common.exception.Error;
import com.baidu.acg.piat.digitalhuman.console.interceptors.ExceptionHandler;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.MockitoAnnotations;

import java.sql.SQLException;

import static org.junit.jupiter.api.Assertions.assertEquals;

/**
 * Created on 2020/7/27 15:42.
 *
 * <AUTHOR>
 */
public class ExceptionHandlerTest {

    @InjectMocks
    private ExceptionHandler exceptionHandler;

    @BeforeEach
    public void init() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void handle() {
        var response = exceptionHandler.handle(new Exception());
        assertEquals(response.getCode(), Error.INTERNAL_SERVER_ERROR.getCode());
        assertEquals(response.getMessage().getGlobal(), Error.INTERNAL_SERVER_ERROR.getMessage());

        response = exceptionHandler.handle(new DigitalHumanCommonException("test"));
        assertEquals(response.getCode(), Error.INTERNAL_SERVER_ERROR.getCode());
        assertEquals(response.getMessage().getGlobal(), "test");

        response = exceptionHandler.handle(new IllegalArgumentException("test"));
        assertEquals(response.getCode(), Error.INTERNAL_SERVER_ERROR.getCode());
        assertEquals(response.getMessage().getGlobal(), "test");

        response = exceptionHandler.handle(new SQLException("11111111111111",
                new SQLException("22222222222222222", new SQLException("test error message"))));
        Assertions.assertEquals(response.getMessage().toString(), "sql exception, root cause=test error message");
    }
}
