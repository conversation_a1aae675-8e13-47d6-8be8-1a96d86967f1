package com.baidu.acg.piat.digitalhuman.console.sce;

import static org.junit.jupiter.api.Assertions.assertEquals;

import com.google.gson.Gson;
import java.util.Date;
import java.util.Map;
import java.util.UUID;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.baidu.acg.piat.digitalhuman.common.OkClient;
import com.baidu.acg.piat.digitalhuman.common.constans.RenderOpenParameters;
import com.baidu.acg.piat.digitalhuman.common.project.PaintChartOnPictureParams;
import com.baidu.acg.piat.digitalhuman.common.project.PaintSubtitleOnPictureParams;
import com.baidu.acg.piat.digitalhuman.common.project.Project;

/**
 * Created on 2020/4/23 11:15.
 *
 * <AUTHOR>
 */
@Slf4j
public class ProjectTest extends OkClient {

    private static final String testProjectId = UUID.randomUUID().toString();
    private static final String testUserId = UUID.randomUUID().toString();
    private static final Date now = new Date();
    private static final String testBackgroundId = UUID.randomUUID().toString();
    private static final String testBackgroundUrl =
            "https://bj.bcebos.com/v1/digital-human-material/test/character/yidong/background.jpg";
    private static final String auth = "BDH efcc35f7-79ed-4ef5-9972-f18c939f809e/eb0b0adc762d52ca200bd9f95558173a8b70bb0318"
            + "ab23dc8b9112c200d644f4/2020-12-03T10:15:30+01:00[Europe/Paris]";

    private static Project project;

    @BeforeEach
    public void setUp() {

        project = Project.builder()
                .id(testProjectId)
                .userId(testUserId)
                .name(UUID.randomUUID().toString())
                .characterImage("UE4_RENDER_VERTICAL")
                .createTime(now.toString())
                .updateTime(now.toString())
                .backgroundImageUrl(testBackgroundUrl)
                .backgroundImageId(testBackgroundId)
                .description("description")
                .build();

    }

    @Test
    public void toRenderParams() {
        project = Project.builder()
                .id(testProjectId)
                .userId(testUserId)
                .name(UUID.randomUUID().toString())
                .characterImage("UE4_RENDER_VERTICAL")
                .createTime(now.toString())
                .updateTime(now.toString())
                .backgroundImageUrl(testBackgroundUrl)
                .backgroundImageId(testBackgroundId)
                .description("description")
                .paintSubtitleOnPictureParams(PaintSubtitleOnPictureParams.builder()
                        .subtitleBackgroundColor("255,255,255")
                        .subtitleColor("255,255,0")
                        .paintSubtitleOnPicture(true)
                        .subtitleFontSize(40)
                        .subtitleMarginPx(10)
                        .subtitleTTL(-1)
                        .subtitleSplittable(true)
                        .build())
                .paintChartOnPictureParams(PaintChartOnPictureParams.builder()
                        .paintChartOnPicture(true)
                        .paintWidgetOnPicture(true)
                        .build())
                .build();
        Map<String, String> renderParams = project.toRenderParams();
        assertEquals(renderParams.get(RenderOpenParameters.background_image_url.name()), testBackgroundUrl);
        assertEquals(renderParams.get(RenderOpenParameters.paintSubtitleOnPicture.name()), "true");
        assertEquals(renderParams.get(RenderOpenParameters.paintWidgetOnPicture.name()), "true");
    }

    @Test
    void create() {

        String url = baseUrl + "/api/digitalhuman/project/v1";

        Gson gson = new Gson();
        String json = gson.toJson(project);
        post(url, json, auth);
    }


}
