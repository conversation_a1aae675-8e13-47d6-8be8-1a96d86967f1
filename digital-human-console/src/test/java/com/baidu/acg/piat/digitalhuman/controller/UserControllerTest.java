package com.baidu.acg.piat.digitalhuman.controller;

import com.baidu.acg.dh.user.client.model.vo.UserCreateReqVO;
import com.baidu.acg.dh.user.client.model.vo.UserGetResVO;
import com.baidu.acg.dh.user.client.model.vo.UserUpdateReqVO;
import com.baidu.acg.piat.digitalhuman.common.access.AccessUser;
import com.baidu.acg.piat.digitalhuman.console.controller.UserController;
import com.baidu.acg.piat.digitalhuman.console.service.user.UserService;
import com.baidu.acg.piat.digitalhuman.plat.client.PlatformClient;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.mock.web.MockHttpSession;

import javax.servlet.http.Cookie;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;


public class UserControllerTest {

    @InjectMocks
    private UserController userController;

    @Mock
    private MockHttpServletRequest httpServletRequest;

    @Mock
    private MockHttpServletResponse httpServletResponse;

    @Mock
    private PlatformClient platformClient;

    @Mock
    private UserService userService;

    private AccessUser accessUser;

    private UserGetResVO userGetResVO;

    private UserCreateReqVO userCreateReqVO;

    private UserUpdateReqVO userUpdateReqVO;

    @BeforeEach
    public void setup() {
        MockitoAnnotations.initMocks(this);
        httpServletRequest.setAttribute("authorization", "authorization");
        accessUser = AccessUser.builder()
                .description("description")
                .userId("userId")
                .name("name")
                .password("password")
                .build();
        userGetResVO = UserGetResVO.builder()
                .uid("uid")
                .userName("userName")
                .build();
        userCreateReqVO = UserCreateReqVO.builder()
                .userName("userName")
                .build();
        userUpdateReqVO = UserUpdateReqVO.builder()
                .userName("userName").build();
    }

    @Test
    public void login() {
        when(platformClient.validateUserName(any())).thenReturn(accessUser);
        userController.login(httpServletRequest, httpServletResponse, accessUser);

        var cookies = new Cookie[1];
        cookies[0] = new Cookie("userId", "userId");
        when(httpServletRequest.getCookies()).thenReturn(cookies);
        userController.login(httpServletRequest, httpServletResponse, accessUser);
    }

    @Test
    public void loginCheck() {
        when(userService.loginCheck(any(), any())).thenReturn("token");
        var userCreateVo = UserCreateReqVO.builder()
                .userName("name")
                .password("password")
                .build();
        userController.loginCheck(httpServletRequest, userCreateVo);
    }

    @Test
    public void logout() {
        when(httpServletRequest.getSession()).thenReturn(new MockHttpSession());
        userController.logout(httpServletRequest, httpServletResponse);
    }

    @Test
    void create() {
        userController.create(userCreateReqVO);
        verify(userService, times(1)).create(any());
    }

    @Test
    void deleteByUserName() {
        userController.deleteByUserName(userGetResVO);
        verify(userService, times(1)).deleteByUserName(any());
    }

    @Test
    void updateUserByName() {
        userController.updateUserByName(userUpdateReqVO);
        verify(userService, times(1)).updateUserByName(any());
    }

    @Test
    void getUserById() {
        userController.getUserById(userGetResVO);
        verify(userService, times(1)).getUserById(any());
    }

    @Test
    void getUserByName() {
        userController.getUserByName(userGetResVO);
        verify(userService, times(1)).getUserByName(any());
    }
}
