package com.baidu.acg.piat.digitalhuman.console.controller.sce;

import com.baidu.acg.dh.user.client.model.vo.UserGetResVO;
import com.baidu.acg.piat.digitalhuman.common.model.PageResponse;
import com.baidu.acg.piat.digitalhuman.common.model.PageResult;
import com.baidu.acg.piat.digitalhuman.common.project.Project;
import com.baidu.acg.piat.digitalhuman.common.project.ProjectApplyRequest;
import com.baidu.acg.piat.digitalhuman.common.project.ProjectCopy;
import com.baidu.acg.piat.digitalhuman.common.project.ProjectName;
import com.baidu.acg.piat.digitalhuman.common.richconfig.RichtextConfig;
import com.baidu.acg.piat.digitalhuman.console.service.sce.ProjectService;
import com.baidu.acg.piat.digitalhuman.console.service.sce.RichtextConfigService;

import org.assertj.core.util.Lists;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.Collections;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * Created on 2021/1/25 10:30.
 *
 * <AUTHOR>
 */
public class SceProjectControllerTest {

    @InjectMocks()
    private SceProjectController sceProjectController;

    @Mock
    private ProjectService projectService;

    @Mock
    private RichtextConfigService richtextConfigService;

    private Project project;
    private UserGetResVO user;

    @BeforeEach
    public void setup() {
        MockitoAnnotations.initMocks(this);

        user = UserGetResVO.builder()
                .uid("userid")
                .build();

        project = Project.builder()
                .userId("userid")
                .name("name")
                .build();
    }

    @Test
    public void testApply() {
        var projectApplyRequest = new ProjectApplyRequest();
        projectApplyRequest.setProjectId("projectId");
        projectApplyRequest.setProjectVersion("projectVersion");
        projectApplyRequest.setAppIds(Collections.singletonList("appId"));
        sceProjectController.apply(projectApplyRequest);
    }

    @Test
    public void copy() {

        ProjectCopy request = new ProjectCopy("name", "rename");

        when(projectService.getByUserIdNameAndVersion("userid", project.getName(), null, 1)).thenReturn(project);
        when(projectService.create(project)).thenReturn(project);

        var copy = sceProjectController.copy(request, "userid", "username");
        var result = copy.getResult();

        Assertions.assertEquals(Project.builder().name("rename").userId("userid").userName("username").editor("username").build(), result);
    }

    @Test
    public void delete() {

        when(richtextConfigService.list(any())).thenReturn(PageResponse.<RichtextConfig>builder()
                .page(
                        PageResult.<RichtextConfig>builder()
                                .result(Lists.newArrayList(RichtextConfig.builder().configId("configId").build()))
                                .build())
                .build());
        ProjectName request = new ProjectName("name", null);
        sceProjectController.delete(request, "userid");
        verify(projectService, times(1)).deleteByName("name", "userid", 1);
        verify(richtextConfigService).list(any());
    }
}
