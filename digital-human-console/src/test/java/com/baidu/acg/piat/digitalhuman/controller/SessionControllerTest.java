package com.baidu.acg.piat.digitalhuman.controller;

import com.baidu.acg.piat.digitalhuman.common.exception.DigitalHumanCommonException;
import com.baidu.acg.piat.digitalhuman.console.controller.SessionController;
import com.baidu.acg.piat.digitalhuman.console.model.session.SessionListRequest;
import com.baidu.acg.piat.digitalhuman.console.model.session.SessionRequest;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import javax.servlet.http.HttpServletRequest;

import static org.junit.jupiter.api.Assertions.assertThrows;

/**
 * Created on 2020/7/24 16:48.
 *
 * <AUTHOR>
 */
public class SessionControllerTest {

    @InjectMocks
    private SessionController sessionController;

    @Mock
    HttpServletRequest httpServletRequest;

    @BeforeEach
    public void setup() {
        MockitoAnnotations.initMocks(this);
        httpServletRequest.setAttribute("authorization", "authorization");
    }

    @Test
    public void list() {
        assertThrows(DigitalHumanCommonException.class, () -> sessionController.list(httpServletRequest, new SessionListRequest()));
    }

    @Test
    public void delete() {
        assertThrows(DigitalHumanCommonException.class, () -> sessionController.delete(httpServletRequest, new SessionRequest()));
    }

    @Test
    public void get() {
        assertThrows(DigitalHumanCommonException.class, () -> sessionController.get(httpServletRequest, new SessionRequest()));
    }
}
