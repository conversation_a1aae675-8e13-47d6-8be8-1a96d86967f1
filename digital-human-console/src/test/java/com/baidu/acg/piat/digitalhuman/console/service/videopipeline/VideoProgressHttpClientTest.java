// Copyright (C) 2020 Baidu Inc. All rights reserved.

package com.baidu.acg.piat.digitalhuman.console.service.videopipeline;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.baidu.acg.piat.digitalhuman.common.model.PageResponse;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import retrofit2.Call;

import java.io.IOException;
import java.lang.reflect.Field;
import java.util.List;

import com.baidu.acg.piat.digitalhuman.common.console.video.ProgressResult;
import com.baidu.acg.piat.digitalhuman.common.console.video.ProgressStatus;
import com.baidu.acg.piat.digitalhuman.common.console.video.VideoFailRequest;
import com.baidu.acg.piat.digitalhuman.common.console.video.VideoSubmitRequest;
import com.baidu.acg.piat.digitalhuman.common.console.video.VideoUpdateRequest;
import com.baidu.acg.piat.digitalhuman.common.model.Response;
import com.baidu.acg.piat.digitalhuman.videopipeline.client.VideoProgressHttpClient;
import com.baidu.acg.piat.digitalhuman.videopipeline.client.VideoProgressHttpService;

/**
 * VideoProgressHttpClientTest
 *
 * <AUTHOR> Zhensheng(<EMAIL>)
 * @since 2020-12-11
 */
class VideoProgressHttpClientTest {

    @Mock
    Call<Response<ProgressResult>> call;

    @Mock
    Call<Response<List<ProgressResult>>> batchCall;

    @Mock
    Call<Response<Void>> voidCall;

    @Mock
    Call<PageResponse<ProgressResult>> pageCall;

    retrofit2.Response<Response<Void>> voidResponse;

    retrofit2.Response<PageResponse<ProgressResult>> pageResponse;

    retrofit2.Response<Response<ProgressResult>> response;

    retrofit2.Response<Response<List<ProgressResult>>> batchResponse;

    Response<ProgressResult> successResponse;

    Response<List<ProgressResult>> batchSuccessResponse;

    @InjectMocks
    VideoProgressHttpClient httpClient;

    @Mock
    VideoProgressHttpService httpService;

    @BeforeEach
    public void init() throws IOException, NoSuchFieldException, IllegalAccessException {
        MockitoAnnotations.initMocks(this);
        Field field = httpClient.getClass().getDeclaredField("httpService");
        field.setAccessible(true);
        field.set(httpClient, httpService);
        successResponse = Response.success(ProgressResult.builder()
                .videoId("testVideoId")
                .status(ProgressStatus.SCHEDULED)
                .build());
        batchSuccessResponse = Response.success(List.of(ProgressResult.builder()
                .videoId("testVideoId")
                .status(ProgressStatus.SCHEDULED)
                .build()));
        response = retrofit2.Response.success(successResponse);
        batchResponse = retrofit2.Response.success(batchSuccessResponse);
        voidResponse = retrofit2.Response.success(Response.success());
        pageResponse = retrofit2.Response.success(PageResponse.success(1, 20, 20, null));
        Mockito.when(httpService.initProgress(any())).thenReturn(call);
        Mockito.when(httpService.updateProgress(any())).thenReturn(call);
        Mockito.when(httpService.submitProgress(any())).thenReturn(batchCall);
        Mockito.when(httpService.failProgress(any())).thenReturn(call);
        Mockito.when(httpService.pollProgress(any())).thenReturn(call);
        Mockito.when(httpService.pollBatchProgress(any())).thenReturn(batchCall);

        Mockito.when(call.execute()).thenReturn(response);
        Mockito.when(batchCall.execute()).thenReturn(batchResponse);

        when(voidCall.execute()).thenReturn(voidResponse);
        when(pageCall.execute()).thenReturn(pageResponse);
    }

    @Test
    void submitProgress() {
        Response<List<ProgressResult>> listResponse = httpClient.submitProgress(new VideoSubmitRequest());
        Assertions.assertEquals(true, listResponse.isSuccess());
        Assertions.assertEquals("testVideoId", listResponse.getResult().get(0).getVideoId());
    }

    @Test
    void failProgress() {
        Response<ProgressResult> result = httpClient.failProgress(new VideoFailRequest());
        Assertions.assertEquals(true, result.isSuccess());
        Assertions.assertEquals("testVideoId", result.getResult().getVideoId());
    }

    @Test
    void initProgress() {
        Response<ProgressResult> result = httpClient.initProgress(new VideoSubmitRequest());

        Assertions.assertEquals(true, result.isSuccess());
        Assertions.assertEquals("testVideoId", result.getResult().getVideoId());

    }

    @Test
    void updateProgress() {
        Response<ProgressResult> result = httpClient.updateProgress(VideoUpdateRequest.builder().build());
        Assertions.assertEquals(true, result.isSuccess());
        Assertions.assertEquals("testVideoId", result.getResult().getVideoId());
    }

    @Test
    void pollProgress() {

        Response<ProgressResult> result = httpClient.pollProgress("testVideoId");
        Assertions.assertEquals(true, result.isSuccess());
        Assertions.assertEquals("testVideoId", result.getResult().getVideoId());
    }

    @Test
    void pollBatchProgress() {

        Response<List<ProgressResult>> result = httpClient.pollBatchProgress(List.of("testVideoId"));
        Assertions.assertEquals(true, result.isSuccess());
        Assertions.assertEquals(1, result.getResult().size());
        Assertions.assertEquals("testVideoId", result.getResult().get(0).getVideoId());
    }
}