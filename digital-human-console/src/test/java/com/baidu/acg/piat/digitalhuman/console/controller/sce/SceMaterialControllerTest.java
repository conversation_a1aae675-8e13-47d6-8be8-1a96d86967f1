package com.baidu.acg.piat.digitalhuman.console.controller.sce;

import com.baidu.acg.dh.user.client.model.vo.UserGetResVO;
import com.baidu.acg.piat.digitalhuman.common.material.Material;
import com.baidu.acg.piat.digitalhuman.common.material.MaterialId;
import com.baidu.acg.piat.digitalhuman.common.material.MaterialListRequest;
import com.baidu.acg.piat.digitalhuman.console.service.sce.MaterialService;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import static org.mockito.Mockito.verify;

/**
 * Created on 2021/8/11 3:07 下午
 *
 * <AUTHOR>
 */

class SceMaterialControllerTest {

    @InjectMocks
    private SceMaterialController sceMaterialController;

    @Mock
    private MaterialService materialService;

    private Material material;

    private UserGetResVO user;

    private MaterialId materialId;

    private MaterialListRequest materialListRequest;

    @BeforeEach
    public void setup() {
        MockitoAnnotations.initMocks(this);

        user = UserGetResVO.builder()
                .uid("userid")
                .build();

        material = Material.builder()
                .userId("userid")
                .name("name")
                .build();

        materialId = new MaterialId("1");

        materialListRequest = MaterialListRequest.builder()
                .userId("userId")
                .name("name")
                .build();
    }

    @Test
    void create() {
        sceMaterialController.create(material, "uid");
        verify(materialService, Mockito.times(1)).create(material);
    }

    @Test
    void delete() {
        var materialId = new MaterialId("1");
        sceMaterialController.delete(materialId);
        verify(materialService, Mockito.times(1)).delete(materialId.getId());
    }

    @Test
    void update() {
        sceMaterialController.update(material, "uid");
        verify(materialService, Mockito.times(1)).update(material);
    }

    @Test
    void detail() {
        sceMaterialController.detail(materialId);
        verify(materialService, Mockito.times(1)).detail(materialId.getId());
    }

    @Test
    void list() {
        sceMaterialController.list(materialListRequest, "uid");
        verify(materialService, Mockito.times(1))
                .list(materialListRequest.getUserId(), materialListRequest.getName(), materialListRequest.getType(),
                        materialListRequest.getPositionId(), materialListRequest.getPageNo(),
                        materialListRequest.getPageSize());
    }
}