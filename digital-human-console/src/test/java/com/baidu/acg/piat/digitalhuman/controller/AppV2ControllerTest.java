package com.baidu.acg.piat.digitalhuman.controller;

import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import com.baidu.acg.dh.user.client.model.vo.UserGetResVO;
import com.baidu.acg.piat.digitalhuman.common.access.AccessApp;
import com.baidu.acg.piat.digitalhuman.console.controller.AppV2Controller;
import com.baidu.acg.piat.digitalhuman.console.model.app.AppListRequest;
import com.baidu.acg.piat.digitalhuman.console.service.access.AccessControlService;

import java.util.HashMap;

/**
 * Created on 2020/7/22 21:32.
 *
 * <AUTHOR>
 */
public class AppV2ControllerTest {

    @InjectMocks
    private AppV2Controller appController;

    @Mock
    private AccessControlService accessControlService;

    private AccessApp accessApp;

    private UserGetResVO userGetResVO;

    @BeforeEach
    public void setup() {
        MockitoAnnotations.initMocks(this);
        accessApp = AccessApp.builder()
                .appId("appId")
                .characterImage("A2A")
                .description("description")
                .maxIdleInSecond(120)
                .userId("userId")
                .name("name")
                .enabled(true)
                .build();

        userGetResVO = UserGetResVO.builder()
                .uid("uid")
                .userName("name")
                .password("password")
                .build();
    }

    @Test
    public void create() {
        appController.createApp(accessApp, "accountId", "name","uid");
        verify(accessControlService, times(1)).createApp(accessApp);

    }

    @Test
    public void delete() {
        appController.deleteApp(accessApp, "accountId" ,"uid");
        verify(accessControlService, times(1)).deleteApp("uid",2,"appId");
    }

    @Test
    public void get() {
        appController.getApp(accessApp);
        verify(accessControlService, times(1)).getApp("appId");

    }

    @Test
    public void list() {
        var appListRequest = new AppListRequest();
        appListRequest.setAppId("appId");
        appListRequest.setName("name");
        appController.listAll(appListRequest, "uid");
        verify(accessControlService, times(1)).listAll("appId", "uid", "name", null, null,2, 1, 20);
    }

    @Test
    public void update() {
        appController.updateApp(accessApp, "accountId", "name","uid");
        verify(accessControlService, times(1)).updateApp(accessApp);
    }
}
