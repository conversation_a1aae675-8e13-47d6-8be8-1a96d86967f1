package com.baidu.acg.piat.digitalhuman.console.model;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;

import com.baidu.acg.piat.digitalhuman.common.utils.AccessSignUtil;
import com.baidu.acg.piat.digitalhuman.console.model.common.AuthorizationInfo;

import org.junit.jupiter.api.Test;

import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;

class AuthorizationInfoTest {

    @Test
    public void extract() {
        var appId = "BDH 11857674-7a53-4578-9e42-9c4744843fb6";
        var rawAppId = "11857674-7a53-4578-9e42-9c4744843fb6";
        var signature = "d6e0a41a3ade3f23734c68501ad848599af7f3da0ed1469d7f26988d71b2c403";
        var expireTime = DateTimeFormatter.ISO_OFFSET_DATE_TIME.format(ZonedDateTime.now().plusDays(1));
        var auth = "BDH 11857674-7a53-4578-9e42-9c4744843fb6" +
                "/d6e0a41a3ade3f23734c68501ad848599af7f3da0ed1469d7f26988d71b2c403/" + expireTime;
        var authInfo = AuthorizationInfo.extract(auth);
        assertEquals(appId, authInfo.getAppId());
        assertNull(authInfo.getAppKey());
        assertEquals(signature, authInfo.getSignature());
        assertEquals(expireTime, expireTime);
    }

    @Test
    public void signature() {
        String signature = AccessSignUtil.sign("91431e37-7340-4419-9e81-0e0eecc54e37",
                "a330c167-81ce-4c61-894d-8904084251bd",
                "2020-12-03T10:15:30+01:00[Europe/Paris]");
        System.out.println("signature:" + signature);
    }
}