package com.baidu.acg.piat.digitalhuman.console.service.access;

import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import com.baidu.acg.piat.digitalhuman.common.access.AccessApp;
import com.baidu.acg.piat.digitalhuman.common.exception.DigitalHumanCommonException;
import com.baidu.acg.piat.digitalhuman.plat.client.PlatformClient;

/**
 * Created on 2021/10/28 3:59 下午
 *
 * <AUTHOR>
 */

class AccessControlServiceImplTest {

    @InjectMocks
    private AccessControlServiceImpl accessControlService;

    @Mock
    private PlatformClient platformClient;

    private AccessApp accessApp;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.initMocks(this);

        accessApp = AccessApp.builder()
                .appId("appId")
                .appKey("appKey")
                .characterImage("characterImage")
                .build();
    }

    @Test
    void createApp() {
        accessControlService.createApp(accessApp);

        when(platformClient.findCharacterByType(anyString(), anyInt())).thenThrow(new DigitalHumanCommonException("sss"));
        Assertions.assertThrows(DigitalHumanCommonException.class, () -> accessControlService.createApp(accessApp));
    }
}