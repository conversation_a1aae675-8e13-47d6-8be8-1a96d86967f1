package com.baidu.acg.piat.digitalhuman.controller;


import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.mock.web.MockHttpServletRequest;

import java.util.ArrayList;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import com.baidu.acg.piat.digitalhuman.common.console.video.ProgressResult;
import com.baidu.acg.piat.digitalhuman.common.console.video.Text2VideoBatchRequest;
import com.baidu.acg.piat.digitalhuman.common.console.video.Text2VideoRequest;
import com.baidu.acg.piat.digitalhuman.common.console.video.VideoQueryRequest;
import com.baidu.acg.piat.digitalhuman.common.exception.DigitalHumanCommonException;
import com.baidu.acg.piat.digitalhuman.common.model.PageResponse;
import com.baidu.acg.piat.digitalhuman.common.model.Response;
import com.baidu.acg.piat.digitalhuman.common.project.FigureCutParams;
import com.baidu.acg.piat.digitalhuman.common.project.TtsParams;
import com.baidu.acg.piat.digitalhuman.console.controller.VideoPipelineController;
import com.baidu.acg.piat.digitalhuman.console.model.BatchIdRequest;
import com.baidu.acg.piat.digitalhuman.console.model.VideoIdRequest;
import com.baidu.acg.piat.digitalhuman.console.model.VideoListRequest;
import com.baidu.acg.piat.digitalhuman.console.model.common.AuthorizationInfo;
import com.baidu.acg.piat.digitalhuman.console.service.videopipeline.VideoPipelineService;
import com.baidu.acg.piat.digitalhuman.console.service.videopipeline.VideoProgressPollingService;
import com.baidu.acg.piat.digitalhuman.videopipeline.client.VideoProgressHttpClient;

/**
 * Created on 2020/7/22 21:48.
 *
 * <AUTHOR>
 */
public class VideoPipelineControllerTest {

    @InjectMocks
    private VideoPipelineController controller;

    @Mock
    private VideoPipelineService videoPipelineService;

    @Mock
    private VideoProgressPollingService pollingService;

    @Mock
    private VideoProgressHttpClient videoProgressHttpClient;

    private MockHttpServletRequest httpServletRequest;

    @BeforeEach
    public void setup() {
        MockitoAnnotations.initMocks(this);
        controller = new VideoPipelineController(videoPipelineService, pollingService, videoProgressHttpClient);
        httpServletRequest = new MockHttpServletRequest();
        AuthorizationInfo authorizationInfo = new AuthorizationInfo();
        authorizationInfo.setAppKey("testt");
        authorizationInfo.setAppId("testt");
        httpServletRequest.setAttribute("authorization", authorizationInfo);

    }

    @Test
    public void text2video() {
        Text2VideoRequest textRequest = new Text2VideoRequest();
        textRequest.setText("text");
        assertDoesNotThrow(() -> controller.text2Video(httpServletRequest, textRequest));
    }

    @Test
    public void text2videoWithAuth() {
        AuthorizationInfo authInfo = new AuthorizationInfo();
        authInfo.setAppId("2fb33122-5787-4528-937b-a8b81b2519bb");
        authInfo.setAppKey("2fb33122-5787-4528-937b-a8b81b2519bb");
        authInfo.setSignature("e23421cf60967ccc0a0392bcd266e29f3b85b7056efdbf7bd3c26083630a4023");
        authInfo.setExpireTime("2020-07-23T09:35:57.244Z");
        httpServletRequest.setAttribute("authorization", authInfo);
        Text2VideoRequest textRequest = new Text2VideoRequest();
        textRequest.setText("text");
        controller.text2Video(httpServletRequest, textRequest);
        verify(videoPipelineService, times(1)).start(any(), anyString(),
                any(Text2VideoRequest.ComplexText2VideoRequest.class));
    }

    @Test
    public void text2videoSync() {
        Text2VideoRequest textRequest = new Text2VideoRequest();
        textRequest.setText("text");
        httpServletRequest.setAttribute("authorization", null);

        assertThrows(DigitalHumanCommonException.class, () -> controller.text2videoSync(httpServletRequest,
                textRequest));
    }

    @Test
    public void text2videoSyncWithAuth() {
        AuthorizationInfo authInfo = new AuthorizationInfo();
        authInfo.setAppId("2fb33122-5787-4528-937b-a8b81b2519bb");
        authInfo.setAppKey("2fb33122-5787-4528-937b-a8b81b2519bb");
        authInfo.setSignature("e23421cf60967ccc0a0392bcd266e29f3b85b7056efdbf7bd3c26083630a4023");
        authInfo.setExpireTime("2020-07-23T09:35:57.244Z");
        httpServletRequest.setAttribute("authorization", authInfo);

        System.out.println(httpServletRequest.getAttribute("authorization"));

        Text2VideoRequest textRequest = new Text2VideoRequest();
        textRequest.setText("text");
        textRequest.setTtsParams(new TtsParams());
        textRequest.setFigureCutParams(new FigureCutParams());
        assertDoesNotThrow(() -> controller.text2videoSync(httpServletRequest,
                textRequest));
    }

    @Test
    public void submit() {
        AuthorizationInfo authInfo = new AuthorizationInfo();
        authInfo.setAppId("2fb33122-5787-4528-937b-a8b81b2519bb");
        authInfo.setAppKey("2fb33122-5787-4528-937b-a8b81b2519bb");
        authInfo.setSignature("e23421cf60967ccc0a0392bcd266e29f3b85b7056efdbf7bd3c26083630a4023");
        authInfo.setExpireTime("2020-07-23T09:35:57.244Z");
        httpServletRequest.setAttribute("authorization", authInfo);
        Text2VideoBatchRequest batchRequest = new Text2VideoBatchRequest();

        assertDoesNotThrow(() -> {
            controller.text2VideoSubmit(httpServletRequest, batchRequest);
        });
    }

    @Test
    public void submitInvalid() {

        Text2VideoBatchRequest batchRequest = new Text2VideoBatchRequest();
        httpServletRequest.setAttribute("authorization", null);
        assertThrows(DigitalHumanCommonException.class, () -> {
                    controller.text2VideoSubmit(httpServletRequest, batchRequest);

                }
        );
    }

    @Test
    void queryProgress() {
        assertDoesNotThrow(() -> {
            controller.queryProgress(new VideoIdRequest());
        });
    }

    @Test
    void queryBatchProgress() {
        assertDoesNotThrow(() -> {
            controller.queryBatchProgress(new BatchIdRequest());
        });
    }

    @Test
    void queryProgressOfApp() {

        when(videoProgressHttpClient.pollByAppId(any(), eq(""), anyInt(), anyInt()))
                .thenReturn(Response.success(PageResponse.success(1, 20, 20, new ArrayList<ProgressResult>())));

        assertDoesNotThrow(() -> {
            VideoListRequest req = new VideoListRequest();
            req.setPageNo(1);
            req.setPageSize(20);
            req.setVideoName("");
            controller.queryProgressOfApp(httpServletRequest, req);
        });
    }

    @Test
    public void pollProgressByUserNameAndProjectNameOrCharacterConfigId() {
        /**
         * 1.0 使用projectName
         */
        var queryRequest1 = VideoQueryRequest.builder()
                .userName("")
                .pageNo(0)
                .pageSize(0)
                .build();
        DigitalHumanCommonException digitalHumanCommonException = assertThrows(DigitalHumanCommonException.class,
                () -> controller.pollProgressByUserNameAndProjectNameOrCharacterConfigId(queryRequest1));
        assertEquals(digitalHumanCommonException.getLocalizedMessage(), "No valid parameters");

        queryRequest1.setUserName("userName");
        queryRequest1.setProjectName("projectName");
        digitalHumanCommonException = assertThrows(DigitalHumanCommonException.class,
                () -> controller.pollProgressByUserNameAndProjectNameOrCharacterConfigId(queryRequest1));
        assertEquals(digitalHumanCommonException.getLocalizedMessage(), "Page no cannot less then 1");

        queryRequest1.setPageNo(1);
        digitalHumanCommonException = assertThrows(DigitalHumanCommonException.class,
                () -> controller.pollProgressByUserNameAndProjectNameOrCharacterConfigId(queryRequest1));
        assertEquals(digitalHumanCommonException.getLocalizedMessage(), "Page size cannot less then 1");

        queryRequest1.setPageSize(10);
        when(videoProgressHttpClient.pollProgressByUserNameAndProjectNameOrCharacterConfigId(
                        anyString(), anyString(), any(), anyInt(), anyInt()))
                .thenReturn(Response.success(PageResponse.success(1, 10, 20, new ArrayList<ProgressResult>())));
        assertDoesNotThrow(() -> controller.pollProgressByUserNameAndProjectNameOrCharacterConfigId(queryRequest1));

        /**
         * 2.0 使用characterConfigId，不与ProjectName共存
         */
        when(videoProgressHttpClient.pollProgressByUserNameAndProjectNameOrCharacterConfigId(
                anyString(), any(), anyString(), anyInt(), anyInt()))
                .thenReturn(Response.success(PageResponse.success(1, 10, 20, new ArrayList<ProgressResult>())));
        var queryRequest2 = VideoQueryRequest.builder()
                .userName("userName")
                .characterConfigId("characterConfigId")
                .pageNo(1)
                .pageSize(10)
                .build();
        assertDoesNotThrow(() -> controller.pollProgressByUserNameAndProjectNameOrCharacterConfigId(queryRequest2));
    }
}
