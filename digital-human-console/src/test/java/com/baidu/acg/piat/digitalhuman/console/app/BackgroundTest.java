package com.baidu.acg.piat.digitalhuman.console.app;

import com.baidu.acg.piat.digitalhuman.common.OkClient;
import com.baidu.acg.piat.digitalhuman.common.utils.DateTimeUtil;
import com.baidu.acg.piat.digitalhuman.console.model.background.BackgroundListRequest;
import com.baidu.acg.piat.digitalhuman.console.model.background.BackgroundRequest;
import com.google.gson.Gson;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;

/**
 * 背景管理
 *
 * @Author: <EMAIL>
 * @Date: 2020/4/20
 */

@Slf4j
public class BackgroundTest extends OkClient {


    String userId = "5e9e85bd12162074a44e68cd";
    String name = "NGD";
    String description = "a test app for testing";
    String auth = "BDH efcc35f7-79ed-4ef5-9972-f18c939f809e/eb0b0adc762d52ca200bd9f95558173a8b70bb0318"
            + "ab23dc8b9112c200d644f4/2020-12-03T10:15:30+01:00[Europe/Paris]";
    String appId = "efcc35f7-79ed-4ef5-9972-f18c939f809e";

    // 展示App的背景图
    @Test
    public void findBackground() {
        String url = baseUrl + "/api/digitalhuman/console/v1/background/image";

        BackgroundRequest request = new BackgroundRequest();
        request.setBackgroundImageId("backGroundId");

        Gson gson = new Gson();
        String json = gson.toJson(request);
        post(url, json, auth);
    }


    // 展示用户下的背景列表
    @Test
    public void findBackgroundListByUserId() {
        String url = baseUrl + "/api/digitalhuman/console/v1/background/image/list";

        BackgroundListRequest request = new BackgroundListRequest();
        request.setUserId("userId");
        request.setPageNo(1);
        request.setPageSize(10);
        Gson gson = new Gson();
        String json = gson.toJson(request);
        post(url, json, auth);
    }

    // 上传背景图
    @Test
    public void uploadBackgroundImage() {
        String url = baseUrl + "/api/digitalhuman/console/v1/background/image/upload";

        BackgroundRequest request = new BackgroundRequest();
        request.setUserId(userId);
        request.setCreateTime(DateTimeUtil.getNowTimeString());
        request.setDescription("desc....");
        request.setName("bgName");
        request.setImageBase64("");

        Gson gson = new Gson();
        String json = gson.toJson(request);
        post(url, json, auth);
    }

    // 删除背景图
    @Test
    public void delBackgroundByAppId() {
        String url = baseUrl + "/api/digitalhuman/console/v1/background/image/delete";

        BackgroundRequest request = new BackgroundRequest();
        request.setBackgroundImageId("bgImageId");

        Gson gson = new Gson();
        String json = gson.toJson(request);
        post(url, json, auth);
    }






}
