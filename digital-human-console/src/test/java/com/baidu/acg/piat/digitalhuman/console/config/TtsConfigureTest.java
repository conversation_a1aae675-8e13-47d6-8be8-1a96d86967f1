package com.baidu.acg.piat.digitalhuman.console.config;

import com.google.common.base.Charsets;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import com.baidu.acg.piat.digitalhuman.tts.TtsService;
import com.baidu.acg.piat.digitalhuman.tts.TtsServiceException;

/**
 * Created on 2022/3/29 5:53 下午
 *
 * <AUTHOR>
 */

@Disabled
@ExtendWith(SpringExtension.class)
@SpringBootTest
class TtsConfigureTest {

    @Autowired
    TtsService ttsService;

    @Test
    void testTts() {

        try {
            var output = ttsService.textToAudio("你好你好");
            System.out.println(new String(output.getAudio(), Charsets.UTF_8));
        } catch (TtsServiceException e) {
            e.printStackTrace();
        }
    }
}