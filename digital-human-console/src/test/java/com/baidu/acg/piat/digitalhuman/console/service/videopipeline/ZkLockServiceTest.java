// Copyright (C) 2021 Baidu Inc. All rights reserved.

package com.baidu.acg.piat.digitalhuman.console.service.videopipeline;

import static org.mockito.Mockito.when;

import org.apache.curator.framework.CuratorFramework;
import org.apache.curator.framework.WatcherRemoveCuratorFramework;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.Optional;

/**
 * ZkLockServiceTest
 *
 * <AUTHOR>
 * @date 2021-01-15
 */
class ZkLockServiceTest {

    @Mock
    private CuratorFramework curatorFramework;

    @Mock
    private WatcherRemoveCuratorFramework watcherRemoveCuratorFramework;

    @InjectMocks
    private ZkLockService zkLockService;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.initMocks(this);

        when(curatorFramework.newWatcherRemoveCuratorFramework()).thenReturn(watcherRemoveCuratorFramework);
        zkLockService.setZkLockPrefix("/test");
    }

    void tryLockFailed() {

        Optional<String> acquired = zkLockService.tryLock("xxxx");

        Assertions.assertEquals(true, acquired.isEmpty());
    }

    @Test
    void tryLockSuccess() {

    }

    @Test
    void unlock() {

        zkLockService.unlock("test", "testToken");
    }
}