package com.baidu.acg.piat.digitalhuman.console.service.device;
import com.baidu.acg.digitalhuman.device.common.v2.exception.ClientException;
import com.baidu.acg.digitalhuman.device.common.v2.model.DeviceEvent;
import com.baidu.acg.digitalhuman.device.common.v2.model.DeviceV2;
import com.baidu.acg.digitalhuman.device.common.v2.model.http.PageResult;
import com.baidu.acg.piat.digitalhuman.plat.client.PlatformClient;

import com.baidu.acg.piat.device.manager2.client.DeviceManagerV2Client;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.Mockito.*;

/**
 * @Author: taoch<PERSON><EMAIL>
 * @Date: 2023/8/24
 */
class DeviceControlServiceImplTest {

    @InjectMocks
    DeviceControlServiceImpl deviceControlService;

    @Mock
    DeviceManagerV2Client deviceManagerV2Client;

    @Mock
    PlatformClient platformClient;

    private DeviceV2 deviceV2;

    @BeforeEach
    public void setup() {
        MockitoAnnotations.initMocks(this);
        deviceControlService = new DeviceControlServiceImpl(deviceManagerV2Client, platformClient);
        deviceV2 = new DeviceV2();
        deviceV2.setDeviceId("deviceId");
        deviceV2.setDeviceName("deviceName");
    }

    @Test
    void createDevice() {
        deviceControlService.createDevice(deviceV2, "accountId");
        try {
            verify(deviceManagerV2Client, times(1)).createDevice(deviceV2);
        } catch (ClientException e) {
            throw new RuntimeException(e);
        }
    }

    @Test
    void updateDevice() {
        deviceControlService.updateDevice("deviceId", deviceV2);
        try {
            verify(deviceManagerV2Client, times(1)).updateDevice("deviceId", deviceV2);
        } catch (ClientException e) {
            throw new RuntimeException(e);
        }
    }

    @Test
    void listDeviceAll() {
        try {
            when(deviceManagerV2Client.listDevices(anyString(), anyInt(), anyInt(), true))
                    .thenReturn(new PageResult<DeviceV2>(1, 20, 100, null));
            deviceControlService.listDeviceAll("accountId", 1, 20);
            verify(deviceManagerV2Client, times(1)).listDevices(anyString(), anyInt(), anyInt(), true);
        } catch (ClientException e) {
            throw new RuntimeException(e);
        }
    }

    @Test
    void listDeviceEventAll() {
        try {
            when(deviceManagerV2Client.listEvents(anyString(), anyString(), anyInt(), anyInt()))
                    .thenReturn(new PageResult<DeviceEvent>(1, 20, 100, null));
            deviceControlService.listDeviceEventAll("deviceId","accountId",1,20);
            verify(deviceManagerV2Client, times(1)).listEvents(anyString(), anyString(), anyInt(), anyInt());
        } catch (ClientException e) {
            throw new RuntimeException(e);
        }
    }

    @Test
    void deleteDevice() {
        deviceControlService.deleteDevice("deviceId", "accountId");
        try {
            verify(deviceManagerV2Client, times(1)).deleteDevice("deviceId", "accountId");
        } catch (ClientException e) {
            throw new RuntimeException(e);
        }
    }
}