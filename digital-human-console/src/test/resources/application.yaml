swagger:
  enable: true

server:
  port: 8082

opentracing:
  jaeger:
    from-env: false
    service-name: digital-human-animations-proxy
    enabled: true
    udp-sender:
      host: *************
      port: 6831
spring:
  profiles:
    active: production
  servlet:
    multipart:
      max-file-size: 100MB
      max-request-size: 1000MB
jwt:
  expire:
    time: 30 * 60 * 1000
    refresh:
      threshold: 30 * 60 * 1000
digitalhuman:
  auth:
    enabled: true
    paths: /api/digitalhuman/console/v2/**,/api/digitalhuman/sce/**
    exclude-paths: /api/digitalhuman/console/v2/user/**,/api/digitalhuman/sce/v1/user/**
    url:
      base: http://dh-user:80
      login: /dep/login
  object:
    type: bos
    storage:
      host: bj.bcebos.com
      endpoint: https://bj.bcebos.com
      bucket: digital-human-sce
      path: /video/
      ak: 931b804661f2462a9ae36eea84357241
      sk: 334e16982621474ea29cac75ba5d8cb8
  zookeeper:
    config:
      namespace: digital-human-console
      url: 127.0.0.1:2181
      baseSleepTimeMs: 1000
      maxRetry: 3
  user:
    config:
      baseUrl: http://localhost:8083
  sce:
    login:
      type: offline
  plat:
    config:
      baseUrl: http://localhost:8084
  tts:
    config:
      ttsStrategy: HTTP
      #      baseUrl: http://localhost:8802
      baseUrl: http://tts.baidu.com
      httpQueryParams:
        lan: zh
        pdt: 10061
        ctp: 1
        cuid: MAC
        aue: 6
        xml: 1
        per: 5137
        spd: 5
        pit: 5
        vol: 5
        sk: c4a91d4d29dc436ca96e4faaa7abb5c5
      responseContentType: audio/wav
  audio2video:
    enabled: false
    tts-params:
      - lan: zh  #vis-2d and a2a
        pdt: 993
        ctp: 1
        cuid: test
        aue: 4
        xml: 1
        per: 5117
        spd: 5
        pit: 5
        vol: 5
      - lan: zh  # ue4
        pdt: 993
        ctp: 1
        cuid: test
        aue: 6
        xml: 1
        per: 5117
        spd: 5
        pit: 5
        vol: 5
      - lan: zh  # textsplit_grpc
        pdt: 993
        ctp: 10
        cuid: test&xml=1
        aue: 4
        xml: 1
        per: 5117
        spd: 5
        pit: 5
        vol: 5
    tts-cache:
      serverAddress: redis://127.0.0.1:6379
      mapKey: ttscache
      maxCacheSize: 200
  videopipeline:
    cloud:
      host: *************
      port: 8090
      closeDelayMs: 500
    progress:
#      baseUrl: http://digital-human-video-pipeline:8080
      baseUrl: http://127.0.0.1:8888
      pollIntervalMillis: 5000
      notExistedTTL: 5
      retryTimes: 3
      retrySleepMillis: 100
  cloud:
    config:
      baseUrl: http://localhost:8081
      pollIntervalMillis: 5000
      notExistedTTL: 5
      retryTimes: 3
      retrySleepMillis: 100
  resource-pool:
    client:
      resourcePoolUrl: http://localhost:8080
#=================== login & access ===================#
login:
  url: https://login.bcetest.baidu.com/?redirect={referredUrl}
  has_authentication_filter_config: true
  urls:
    not.need.auth: /swagger/**;/api/digitalhuman/sce/v1/user/**
    need.auth: /api/digitalhuman/sce/**
  cookie:
    md5:
      key: 19920908
cookie:
  domain: localhost

#================ passport modify ================#
passport:
  appid: 1240
  app.username: digitalhuman
  app.password: digitalhuman
  session.endpoint: http://***********:7801
  passgateEndpoint: http://***********:8300/passgate

uc:
  app.id: 285
  server: ************:8880
iam:
  access.failed.jump.url: https://qasandbox.bcetest.baidu.com/iam/access?redirect={referredUrl}
  console:
    username: test
    password: test
  access:
    paths: /dev/**
    exclude.paths: /api-docs;/api-docs/**;/api;/api/**
  csrf:
    paths: /api/**
    exclude.paths: /api-docs;/api-docs/**
    is.need.check.valid: false
  refer.is.need.check.valid: false
  permission.is.need.check.valid: true