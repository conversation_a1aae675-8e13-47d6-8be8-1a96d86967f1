swagger:
  enable: true

server:
  port: 8082

opentracing:
  jaeger:
    from-env: false
    service-name: digital-human-animations-proxy
    enabled: true
    udp-sender:
      host: *************
      port: 6831
spring:
  mvc:
    pathmatch:
      matching-strategy: ANT_PATH_MATCHER
  profiles:
    active: production
  servlet:
    multipart:
      max-file-size: 100MB
      max-request-size: 1000MB
jwt:
  expire:
    time: 30 * 60 * 1000
    refresh:
      threshold: 30 * 60 * 1000
digitalhuman:
  console.app.home-path: /Users/<USER>/code/acg-digital-human/digital-human/digital-human-console/src/main/resources
  aicloud:
    config:
      baseUrl: https://aip.baidubce.com
      accessToken: 24.dca3d
  auth:
    enabled: false
    paths: /api/digitalhuman/console/v2/**
    exclude-paths: /api/digitalhuman/console/v2/user/**,/api/digitalhuman/sce/v1/user/**
    url:
      base: http://dh-user:80
      login: /dep/login
  object:
    type: bos
    storage:
      host: bj.bcebos.com
      endpoint: https://bj.bcebos.com
      bucket: digital-human-sce
      path: /video/
      ak: 931b804661f2462a9ae36eea84357241
      sk: 334e16982621474ea29cac75ba5d8cb8
  zookeeper:
    config:
      namespace: digital-human-console
      url: 127.0.0.1:2181
      baseSleepTimeMs: 1000
      maxRetry: 3
  user:
    config:
      baseUrl: http://localhost:8083
  sce:
    login:
      type: offline
  plat:
    config:
      baseUrl: http://localhost:8480
  devicemanager:
    config:
      baseUrl: http://device-manager-v2:8080
  storage:
#    type: bos
#    bos:
#      endpoint: https://digital-human-pipeline-output.cdn.bcebos.com
#      accessKeyId: 931b804661f2462a9ae36eea84357241
#      secretAccessKey: 334e16982621474ea29cac75ba5d8cb8
#      bucket: digital-human-pipeline-output
#      retryTimes: 1
#      retrySleepMillis: 100
#      urlExpireSeconds: -1
    type: standalone-nginx
    standalone-nginx:
      localDirectory: "/tmp/standalone-nginx/"
      host: http://127.0.0.1
      port: 9999
    devicemanager:
      config:
        baseUrl: http://device-manager-v2:8080

  tts:
    timeout: 15
    http:
      ttsStrategy: HTTP
#      baseUrl: http://localhost:8802
      baseUrl: http://tts.baidu.com
      httpQueryParams:
        lan: zh
        pdt: 10061
        ctp: 1
        cuid: MAC
        aue: 6
        xml: 1
        per: 5137
        spd: 5
        pit: 5
        vol: 5
        sk: c4a91d4d29dc436ca96e4faaa7abb5c5
      responseContentType: audio/wav
    grpc:
      baseUrl: http://localhost:8803
      httpQueryParams:
        lan: zh
        pdt: 10061
        #      sk: c4a91d4d29dc436ca96e4faaa7abb5c5
        ctp: 10
        cuid: test
        aue: 4
        xml: 1
        per: 5116
        spd: 5
        pit: 5
        vol: 5
      ttsStrategy: TEXTSPLIT_GRPC
      responseContentType: audio/basic;codec=pcm;rate=16000;channel=1
      useCache: false
      cacheAsync: false
      cacheStrategy: redis
      memoryCacheConfig:
        maxCacheSize: 200
      redisCacheConfig:
        serverAddress: redis://127.0.0.1:6379
        mapKey: TtsCacheProduct
        maxCacheSize: 1000
        ttlSeconds: 604800
      textSplitConfig:
        subThreadNumPerTask: 1
        maxSize: 0
        splitFlags: ';,；，。?？!！'
        flagsPattern: '[;|,|；|，|。|?|？|!|！]'
        internalTagPattern: "<(?:say-as.*?</say-as|silence.*?</silence|phoneme.*?</phoneme)>|<silence time=.*?/>|<client>.*?</client>|<fusion>.*?</fusion>|<display>.*?</display>"
        splitByFlagsPattern: true
        threadNum: 5
        maxThreadNum: 10
      ctpLimitLength: 4
      httpConfig:
        connectTimeoutMillis: 2000
        readTimeoutMillis: 20000
      grpcConfig:
        serverHost: localhost
        port: 8081
        keepAliveSeconds: 30
        keepAliveTimeoutSeconds: 30
      cmwwConfig:
        appkey: B7EA45E6D276AD5AED170710E1F45C3F
        secret: 4B1EFD3E2E3451272DF8A0D498FA23C9
        textSplitConfig:
          maxSize: 0
          splitFlags: ';；。?？!！'
          flagsPattern: '[. |。|?|？|!|！]'
          internalTagPattern: "<(?:say-as.*?</say-as|silence.*?</silence|phoneme.*?</phoneme)>|<silence time=.*?/>|<client>.*?</client>|<fusion>.*?</fusion>|<display>.*?</display>"
          splitByFlagsPattern: true
          threadNum: 10
          maxThreadNum: 20
  audio2video:
    enabled: false
    tts-params:
      - lan: zh  #vis-2d and a2a
        pdt: 993
        ctp: 1
        cuid: test
        aue: 4
        xml: 1
        per: 5117
        spd: 5
        pit: 5
        vol: 5
      - lan: zh  # ue4
        pdt: 993
        ctp: 1
        cuid: test
        aue: 6
        xml: 1
        per: 5117
        spd: 5
        pit: 5
        vol: 5
      - lan: zh  # textsplit_grpc
        pdt: 993
        ctp: 10
        cuid: test&xml=1
        aue: 4
        xml: 1
        per: 5117
        spd: 5
        pit: 5
        vol: 5
    tts-cache:
      serverAddress: redis://127.0.0.1:6379
      mapKey: ttscache
      maxCacheSize: 200
      #password: IQsY4iD4Sz
      #clusterAddressList:
      #  - redis://************:6379
      #  - redis://*************:6379
      #  - redis://*************:6379
      #  - redis://*************:6379
      #  - redis://************:6379
      #  - redis://*************:6379
  videopipeline:
    cloud:
      host: localhost
      port: 8381
      closeDelayMs: 500
    progress:
#      baseUrl: http://digital-human-video-pipeline:8080
      baseUrl: http://127.0.0.1:8280
      pollIntervalMillis: 5000
      notExistedTTL: 5
      retryTimes: 3
      retrySleepMillis: 100
  cloud:
    config:
      baseUrl: http://localhost:8380
      pollIntervalMillis: 5000
      notExistedTTL: 5
      retryTimes: 3
      retrySleepMillis: 100
  resource-pool:
    client:
      resourcePoolUrl: http://localhost:8580
#=================== login & access ===================#
login:
  url: https://login.bcetest.baidu.com/?redirect={referredUrl}
  has_authentication_filter_config: true
  urls:
    not.need.auth: /swagger/**;/api/digitalhuman/sce/v1/user/**
    need.auth: /api/digitalhuman/sce/**
  cookie:
    md5:
      key: 19920908
cookie:
  domain: localhost

#================ passport modify ================#
passport:
  appid: 1240
  app.username: digitalhuman
  app.password: digitalhuman
  session.endpoint: http://***********:7801
  passgateEndpoint: http://***********:8300/passgate

uc:
  app.id: 285
  server: ************:8880
iam:
  access.failed.jump.url: https://qasandbox.bcetest.baidu.com/iam/access?redirect={referredUrl}
  console:
    username: test
    password: test
  access:
    paths: /dev/**
    exclude.paths: /api-docs;/api-docs/**;/api;/api/**
  csrf:
    paths: /api/**
    exclude.paths: /api-docs;/api-docs/**
    is.need.check.valid: false
  refer.is.need.check.valid: false
  permission.is.need.check.valid: true

debug: true