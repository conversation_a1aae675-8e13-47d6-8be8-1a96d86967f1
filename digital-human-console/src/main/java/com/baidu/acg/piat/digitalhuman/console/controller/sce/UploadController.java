package com.baidu.acg.piat.digitalhuman.console.controller.sce;

import com.baidu.acg.piat.digitalhuman.common.exception.DigitalHumanCommonException;
import com.baidu.acg.piat.digitalhuman.console.model.upload.FetchBosUrlRequest;
import com.baidu.acg.piat.digitalhuman.console.model.upload.FetchBosUrlResponse;
import com.baidu.acg.piat.digitalhuman.console.model.upload.UploadAuthRequest;
import com.baidu.acg.piat.digitalhuman.console.model.upload.UploadAuthResponse;
import com.baidu.acg.piat.digitalhuman.console.model.upload.UploadConfig;
import com.baidu.acg.piat.digitalhuman.console.model.upload.ConvertEnum;
import com.baidu.acg.piat.digitalhuman.common.utils.FileUtil;
import com.baidu.acg.piat.digitalhuman.console.service.ffmpeg.FfmpegService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.UUID;

import com.baidu.acg.piat.digitalhuman.common.model.Response;
import com.baidu.acg.piat.digitalhuman.console.service.upload.UploadService;
import com.baidu.acg.piat.digitalhuman.storage.service.StorageService;

/**
 * Created on 2021/7/9 下午4:58.
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@RequestMapping({"/api/digitalhuman/sce/v1/upload", "/api/digitalhuman/console/v1/upload"})
public class UploadController {

    private final UploadService uploadService;

    private final StorageService storageService;

    private final FfmpegService ffmpegService;

    @PostMapping("/config")
    public Response<UploadConfig> bosConfig() {
        return Response.success(uploadService.config());
    }

    @PostMapping("/auth")
    public Response<UploadAuthResponse> uploadAuth(@RequestBody UploadAuthRequest request) {
        log.info("Success to receive upload auth request={}", request);
        var uploadAuthResponse = uploadService.auth(request);
        return Response.success(uploadAuthResponse);
    }

    @PostMapping("/genUrl")
    public Response<FetchBosUrlResponse> genUrl(@RequestBody FetchBosUrlRequest request) {
        log.info("Success to receive gen url request={}", request);
        var response = uploadService.genUrl(request);
        return Response.success(response);
    }


    /**
     * 上传文件接口
     */
    @PostMapping("/file")
    public Response<String> uploadFile(@RequestPart MultipartFile file,
            @RequestParam(value = "convert", required = false, defaultValue = "-1") Integer convert)
            throws Exception {

        var filename = file.getOriginalFilename();
        log.info("Success to receive file={}, contentType={}, fileSize={}, convert={}",
                file.getOriginalFilename(), file.getContentType(), file.getSize(), convert);
        ConvertEnum convertEnum = ConvertEnum.getConvertEnumByType(convert);
        String name = getFileName(file, convertEnum);
        byte[] byteData = getFileData(file, convertEnum);
        var url = storageService.save(byteData, name);
        log.info("Success to Upload file={}, new file name={}, url={}", filename, name, url.toString());

        return Response.success(url.toString());
    }

    @PostMapping("/file/new")
    public Response<String> uploadFileNew(@RequestPart MultipartFile file) throws Exception {

        var filename = file.getOriginalFilename();
        log.info("Success to receive file={}, contentType={}, fileSize={}}",
                file.getOriginalFilename(), file.getContentType(), file.getSize());
        String uuid = UUID.randomUUID().toString();
        String newFilename = uuid + getExtension(filename);
        var url = storageService.save(file.getBytes(), newFilename);
        log.info("Success to Upload file={}, new file name={}, url={}", filename, newFilename, url.toString());

        return Response.success(url.toString());
    }

    /**
     * 获取文件数据
     * @param file
     * @param convertEnum
     * @return
     * @throws IOException
     */
    private byte[] getFileData(MultipartFile file, ConvertEnum convertEnum) throws IOException {
        byte[] byteData = null;
        String fileFormat = FileUtil.getFileFormat(file);
        if (ConvertEnum.MP3_TO_WAV_TTS16K.equals(convertEnum) &&
                (FileUtil.MP3_FORMAT.equals(fileFormat) || FileUtil.WAV_FORMAT.equals(fileFormat))) {
            byteData = ffmpegService.convertAudio(file, convertEnum);
        } else {
            byteData = file.getBytes();
        }
        if (byteData == null) {
            throw new DigitalHumanCommonException("文件格式有误，请更换文件");
        }
        return byteData;
    }

    /**
     * 获取文件名
     * @param file
     * @param convertEnum
     * @return
     */
    private String getFileName(MultipartFile file, ConvertEnum convertEnum) {
        String baseName = ZonedDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME) + "/";
        String newName = baseName + file.getOriginalFilename();
        String fileFormat = FileUtil.getFileFormat(file);
        if (ConvertEnum.MP3_TO_WAV_TTS16K.equals(convertEnum) &&
                (FileUtil.MP3_FORMAT.equals(fileFormat) || FileUtil.WAV_FORMAT.equals(fileFormat))) {
            newName = baseName + FileUtil.getFileName(file) + FileUtil.WAV_FORMAT;
        }
        return newName;
    }

    private static String getExtension(String filename) {
        // 如果文件名为空，返回默认扩展名
        if (filename == null || filename.isEmpty()) {
            return ".mp3";
        }

        // 获取最后一个小数点的位置
        int lastDotIndex = filename.lastIndexOf('.');
        if (lastDotIndex == -1) {
            // 如果没有小数点，返回默认扩展名
            return ".mp3";
        } else {
            // 返回从最后一个小数点开始到文本结束的子字符串
            return filename.substring(lastDotIndex).toLowerCase();
        }
    }
}
