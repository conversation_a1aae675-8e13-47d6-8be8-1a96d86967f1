package com.baidu.acg.piat.digitalhuman.console.controller.sce;

import com.baidu.acg.piat.digitalhuman.common.access.AccessApp;
import com.baidu.acg.piat.digitalhuman.common.app.DefaultAppQueryRequest;
import com.baidu.acg.piat.digitalhuman.common.model.PageResponse;
import com.baidu.acg.piat.digitalhuman.common.model.Response;
import com.baidu.acg.piat.digitalhuman.console.model.app.AppListRequest;
import com.baidu.acg.piat.digitalhuman.console.service.access.AccessControlService;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestAttribute;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;


/**
 * sce 需要的和 app 相关的 api
 *
 * <AUTHOR>
 * @since 2020/08/06
 */
@Slf4j
@RequestMapping("/api/digitalhuman/sce/v1/app")
@RestController
@RequiredArgsConstructor
public class SceAppController {

    private final AccessControlService accessControlService;

    private static final int DEFAULT_API_VERSION = 1;

    @PostMapping("/default")
    public Response<AccessApp> getDefaultAppByImage(@RequestBody DefaultAppQueryRequest request) {
        return Response.success(accessControlService.getDefaultAppByImage(request.getCharacterImage(),
                DEFAULT_API_VERSION));
    }

    @PostMapping("/list")
    @ApiOperation(value = "app列表")
    public PageResponse<AccessApp> listAll(@RequestBody AppListRequest request,
                                           @RequestAttribute("accountId") String uid) {
        log.debug("SceAppController list all app pageNo:{}, pageSize:{}", request.getPageNo(), request.getPageSize());
        return accessControlService.listAll(null, uid, request.getName(), DEFAULT_API_VERSION,
                request.getPageNo(), request.getPageSize());
    }

    @PostMapping("/create")
    @ApiOperation(value = "创建app")
    public Response<AccessApp> createApp(@Valid @RequestBody AccessApp request,
                                         @RequestAttribute("accountId") String uid) {
        log.info("AppController create app, userId={}, name={}", uid, request.getName());
        request.setUserId(uid);
        request.setApiVersion(DEFAULT_API_VERSION);
        return Response.success(accessControlService.createApp(request));
    }

    @PostMapping("/delete")
    @ApiOperation(value = "删除app")
    public Response<Void> deleteApp(@Valid @RequestBody AccessApp request,
                                    @RequestAttribute("accountId") String accountId,
                                    @RequestAttribute("uid") String uid) {
        log.debug("AppController delete app, accountId:{}, appId:{}", accountId, request.getAppId());
        accessControlService.deleteApp(uid, 1, request.getAppId());
        return Response.success(null);
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新app")
    public Response<AccessApp> updateApp(@Valid @RequestBody AccessApp request,
                                         @RequestAttribute("accountId") String uid) {
        log.debug("AppController update app, userId:{}, appId:{}", uid, request.getAppId());
        request.setUserId(uid);
        request.setApiVersion(DEFAULT_API_VERSION);
        return Response.success(accessControlService.updateApp(request));
    }

    @PostMapping("/detail")
    @ApiOperation(value = "app明细")
    public Response<AccessApp> getApp(@NotEmpty @RequestBody AccessApp request) {
        log.debug("AppController get app, appId:{}", request.getAppId());
        return Response.success(accessControlService.getApp(request.getAppId()));
    }
}
