package com.baidu.acg.piat.digitalhuman.console.controller.sce;

import com.baidu.acg.piat.digitalhuman.common.model.PageResponse;
import com.baidu.acg.piat.digitalhuman.common.model.Response;
import com.baidu.acg.piat.digitalhuman.console.model.background.BackgroundListRequest;
import com.baidu.acg.piat.digitalhuman.console.model.background.BackgroundRequest;
import com.baidu.acg.piat.digitalhuman.console.model.background.BackgroundResult;
import com.baidu.acg.piat.digitalhuman.console.service.character.BackgroundService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestAttribute;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import java.util.Base64;

/**
 * @Author: <EMAIL>
 * @Date: 2020/3/24
 */

@Slf4j
@RequestMapping({"/api/digitalhuman/sce/v1/background", "/api/digitalhuman/console/v1/background"})
@RestController
@RequiredArgsConstructor
@Api(tags = "背景图管理")
public class BackgroundController {

    private final BackgroundService backgroundService;

    @PostMapping("/image/detail")
    @ApiOperation(value = "展示App的背景图")
    public Response<BackgroundResult> get(@Valid @RequestBody BackgroundRequest request,
                                          @RequestAttribute("accountId") String uid) {
        request.setUserId(uid);
        var backgroundResult = backgroundService.get(request.getBackgroundImageId());
        return Response.success(backgroundResult);
    }

    @PostMapping("/image/list")
    @ApiOperation(value = "展示用户下的背景列表")
    public PageResponse<BackgroundResult> list(@Valid @RequestBody BackgroundListRequest request,
                                               @RequestAttribute("accountId") String uid) {
        // 如果传入的userId为system，说明查询系统库的背景图
        if ("system".equals(request.getUserId())) {
            request.setUserId("System");
        } else {
            request.setUserId(uid);
        }
        return backgroundService.list(request.getUserId(), request.getPageNo(), request.getPageSize());
    }


    @PostMapping("/image/upload")
    @ApiOperation(value = "上传背景图")
    public Response<BackgroundResult> upload(@Valid @RequestBody BackgroundRequest request,
                                             @RequestAttribute("accountId") String uid) {
        request.setUserId(uid);
        return Response.success(backgroundService.upload(request.getUserId(), request));
    }


    @PostMapping("/image/delete")
    @ApiOperation(value = "删除背景图")
    public Response<Void> delete(@Valid @RequestBody BackgroundRequest request,
                                 @RequestAttribute("accountId") String uid) {
        request.setUserId(uid);
        backgroundService.delete(request.getBackgroundImageId());
        return Response.success(null);
    }

    @PostMapping("/image/upload/system")
    @ApiOperation(value = "上传系统背景图")
    public Response<BackgroundResult> uploadSystem(@RequestParam("file") MultipartFile file,
                                                   @RequestParam("fileName") String fileName,
                                                   @RequestAttribute("accountId") String uid,
                                                   @RequestAttribute("roleLevel") int roleLevel) {
        BackgroundRequest request = new BackgroundRequest();
        request.setName(fileName);
        if (roleLevel == 3) {
            request.setUserId("System");
        } else {
            return Response.fail("该账户权限不足");
        }
        try {
            // 获取上传的文件内容
            byte[] imageBytes = file.getBytes();

            // 将图片转换为Base64编码
            String imageBase64 = Base64.getEncoder().encodeToString(imageBytes);

            request.setImageBase64(imageBase64);

            return Response.success(backgroundService.upload(request.getUserId(), request));

        } catch (Exception e) {
            return Response.fail("Image upload failed: " + e.getMessage());
        }

    }

    @PostMapping("/image/delete/system")
    @ApiOperation(value = "删除系统背景图")
    public Response<Void> deleteSystem(@Valid @RequestBody BackgroundRequest request,
                                       @RequestAttribute("accountId") String uid,
                                       @RequestAttribute("roleLevel") int roleLevel) {
        if (roleLevel == 3) {
            request.setUserId("System");
        } else {
            return Response.fail("该账户权限不足");
        }
        backgroundService.delete(request.getBackgroundImageId());
        return Response.success(null);
    }

}