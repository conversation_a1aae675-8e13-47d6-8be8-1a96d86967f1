// Copyright (C) 2020 Baidu Inc. All rights reserved.

package com.baidu.acg.piat.digitalhuman.console.service.videopipeline;

import java.io.BufferedWriter;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.OutputStreamWriter;
import java.net.URL;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.Duration;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Base64;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.HashMap;
import java.util.Map;

import com.baidu.acg.piat.digitalhuman.common.access.AccessApp;
import com.baidu.acg.piat.digitalhuman.common.audio.PartialTtsAudioInfo;
import com.baidu.acg.piat.digitalhuman.common.console.video.SubtitleParams;
import com.baidu.acg.piat.digitalhuman.common.helper.VideoErrorShowMsgHelper;
import com.baidu.acg.piat.digitalhuman.common.project.TtsParams;
import com.baidu.acg.piat.digitalhuman.console.config.VisConfigure;
import com.baidu.acg.piat.digitalhuman.console.model.TtsDetailResponse;
import com.baidu.acg.piat.digitalhuman.console.model.character.VisCharacter;
import com.baidu.acg.piat.digitalhuman.console.model.vis.VisVideoReq;
import com.baidu.acg.piat.digitalhuman.console.model.vis.VisVideoRes;
import com.baidu.acg.piat.digitalhuman.console.service.access.AccessControlService;
import com.baidu.acg.piat.digitalhuman.console.service.tts.TtsSplitServiceImpl;
import com.baidu.acg.piat.digitalhuman.console.service.vis.client.VisHttpClient;
import com.baidu.acg.piat.digitalhuman.console.util.JsonUtil;
import com.baidu.acg.piat.digitalhuman.storage.service.StorageService;
import com.baidu.acg.piat.digitalhuman.tts.model.TtsQueryParams;
import com.google.common.collect.Maps;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.exec.CommandLine;
import org.apache.commons.exec.DefaultExecutor;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.w3c.dom.Document;

import com.baidu.acg.piat.digitalhuman.common.console.video.ProgressResult;
import com.baidu.acg.piat.digitalhuman.common.console.video.ProgressStatus;
import com.baidu.acg.piat.digitalhuman.common.console.video.Text2VideoRequest;
import com.baidu.acg.piat.digitalhuman.common.console.video.VideoFailRequest;
import com.baidu.acg.piat.digitalhuman.common.console.video.VideoScheduleRequest;
import com.baidu.acg.piat.digitalhuman.common.console.video.VideoSubmitRequest;
import com.baidu.acg.piat.digitalhuman.common.console.video.VideoUpdateRequest;
import com.baidu.acg.piat.digitalhuman.common.constans.RenderOpenParameters;
import com.baidu.acg.piat.digitalhuman.common.exception.DigitalHumanCommonException;
import com.baidu.acg.piat.digitalhuman.common.exception.Error;
import com.baidu.acg.piat.digitalhuman.common.model.Response;
import com.baidu.acg.piat.digitalhuman.common.util.DomUtil;
import com.baidu.acg.piat.digitalhuman.console.config.VideoPipelineConfigure;
import com.baidu.acg.piat.digitalhuman.richtext.common.json.JsonVideo.Mode;
import com.baidu.acg.piat.digitalhuman.tracer.annotation.TraceMethod;
import com.baidu.acg.piat.digitalhuman.videopipeline.client.VideoProgressHttpClient;
import com.baidu.acg.pie.digitalhuman.client.ClientStateListener;
import com.baidu.acg.pie.digitalhuman.client.DhClient;
import com.baidu.acg.pie.digitalhuman.client.exception.DigitalHumanException;
import com.baidu.acg.pie.digitalhuman.client.model.EventData;
import com.baidu.acg.pie.digitalhuman.client.model.SessionResult;
import com.baidu.acg.pie.digitalhuman.client.model.request.TextRequest;
import com.baidu.acg.pie.digitalhuman.client.model.response.DhResponse;
import com.baidu.acg.piat.digitalhuman.common.exception.VisKouboError;
import com.google.common.annotations.VisibleForTesting;
import com.google.common.base.Preconditions;

import io.vavr.control.Try;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

/**
 * VideoCompositeService
 *
 * <AUTHOR> Zhensheng(<EMAIL>)
 * @since 2020-02-10
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class VideoPipelineService {

    /**
     * 视频帧开始渲染事件
     */
    private static final String RENDER_START = "RENDER_START";
    /**
     * 中间帧渲染报错事件
     */
    private static final String RENDER_ERROR = "RENDER_ERROR";

    /**
     * TTS 音频信息
     */
    private static final String DOWN_TTS_INFO = "DOWN_TTS_INFO";

    private static final String SRT_SUBTITLE_TEMP_DIR = "/tmp/video-subtitle-srt/";
    private static final String AUDIO_TEMP_DIR = "/tmp/video-audio/";

    private static final DateTimeFormatter SRT_TIME_FORMATTER = DateTimeFormatter.ofPattern("HH:mm:ss,SSS");

    private final ZkLockService zkLockService;

    private final VideoDhClientFactory videoDhClientFactory;

    private final VideoProgressHttpClient videoProgressHttpClient;

    private final VideoPipelineConfigure.CloudConfig cloudConfig;

    private final FailedStatusAdapter failedStatusAdapter;

    private final VideoErrorShowMsgHelper.ErrorMsgConf videoErrorMsgConf;

    private final StorageService storageService;

    private final VisConfigure.VisConfig visConfig;

    @Autowired
    private TtsSplitServiceImpl ttsSplitServiceImpl;

    private final VisHttpClient visHttpClient;

    private final AccessControlService accessControlService;

    private final ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(20);

    private static final Pattern PLAIN_TEXT_PATTERN = Pattern.compile("<.+?>", Pattern.DOTALL);

    @TraceMethod(value = "video.submit", tagParams = {"appId", "appKey"})
    public List<ProgressResult> submit(VideoSubmitRequest submitRequest) {
        Preconditions.checkArgument(StringUtils.isNotEmpty(submitRequest.getAppId()), "appId cannot be empty");
        Preconditions.checkArgument(StringUtils.isNotEmpty(submitRequest.getAppKey()), "appKey cannot be empty");
        Preconditions.checkArgument(CollectionUtils.isNotEmpty(submitRequest.getTexts()), "texts cannot be empty");
        submitRequest.getTexts().forEach(text -> {
            Preconditions.checkArgument(StringUtils.isNotEmpty(text), "text cannot be empty");
        });

        return submitProgress(submitRequest);
    }

    @TraceMethod(value = "video.schedule", tagParams = {"appId", "appKey"})
    public ProgressResult schedule(VideoScheduleRequest.ComplexVideoScheduleRequest textRequest) {
        Optional<String> lock = zkLockService.tryLock(textRequest.getVideoId());
        if (lock.isEmpty()) {
            throw new DigitalHumanCommonException(Error.VIDEO_TASK_ALREADY_SCHEDULED);
        }

        try {
            String appId = textRequest.getAppId();
            String appKey = textRequest.getAppKey();
            AccessApp accessApp = accessControlService.getApp(appId);
            if (accessApp != null && (accessApp.getCharacterImage().equals(visConfig.getCharacterImage())
                    || accessApp.getCharacterImage().equals(visConfig.getCharacterImageV4()))) {
                ProgressResult progressResult = doScheduleVisVideoWebm(textRequest, accessApp.getCharacterImage());
                log.info("Schedule vis video result={}, videoId={}", progressResult, textRequest.getVideoId());
                // 这里的error可以重试的
                if (progressResult.getStatus().equals(ProgressStatus.ERROR)) {
                    failProgress(VideoFailRequest.builder()
                            .id(progressResult.getVideoId())
                            .canRetry(false)
                            .errorMsg(progressResult.getFailureCause())
                            .errorCode(progressResult.getErrorCode())
                            .build());
                } else {
                    updateProgressResult(VideoUpdateRequest.builder()
                            .status(progressResult.getStatus())
                            .id(textRequest.getVideoId())
                            .build());
                }
                return progressResult;
            }

            if (CollectionUtils.isEmpty(textRequest.getTexts())) {
                log.warn("Fail to generation video by empty texts, appId={}, appKey={}", appId, appKey);
                throw new DigitalHumanCommonException(Error.INVALID_PARAMETER);
            }
            var client = videoDhClientFactory.createDhClient(appId, appKey, textRequest);
            VideoStartProgressRequest startProgressRequest = VideoStartProgressRequest.builder()
                    .dhClient(client).appId(appId).videoId(textRequest.getVideoId())
                    .renderTexts(extractTexts(textRequest))
                    .subtitleParams(textRequest.getSubtitleParams())
                    .originRenderTextSize(textRequest.getTexts().size())
                    .build();
            return doStartProgress(startProgressRequest);
        } finally {
            zkLockService.unlock(textRequest.getVideoId(), lock.get());
        }
    }

    private ProgressResult doScheduleVisVideoWebm(VideoScheduleRequest.ComplexVideoScheduleRequest textRequest,
                                                  String characterImage) {
        TtsParams ttsParams = textRequest.getTtsParams();
        String videoId = textRequest.getVideoId();
        ProgressResult progressResult = ProgressResult.builder().build();
        // 先去更新pipiline的视频状态
        updateProgressResult(VideoUpdateRequest.builder()
                .status(ProgressStatus.SCHEDULED)
                .id(textRequest.getVideoId())
                .build());

        progressResult.setStatus(ProgressStatus.SCHEDULED);
        progressResult.setVideoId(videoId);
        // wav文件存储
        String wavUrl = "";
        if (!textRequest.getTexts().get(0).contains("<audio")) {
            if (ttsParams == null || StringUtils.isEmpty(ttsParams.getPerson())) {
                progressResult.setStatus(ProgressStatus.ERROR);
                progressResult.setErrorCode(1001);
                progressResult.setFailureCause("Tts参数不能为空");
                return progressResult;
            }

            Map<String, Object> ttsDetailParams = new HashMap<>();
            ttsDetailParams.put("tex", textRequest.getTexts().get(0));
            ttsDetailParams.put("per", ttsParams.getPerson());
            ttsDetailParams.put("pit", ttsParams.getPitch() == null ? "5" : ttsParams.getPitch().toString());
            ttsDetailParams.put("spd", ttsParams.getSpeed() == null ? "5" : ttsParams.getSpeed().toString());
            ttsDetailParams.put("vol", ttsParams.getVolume() == null ? "5" : ttsParams.getVolume().toString());

            TtsQueryParams ttsQueryParams = TtsQueryParams.builder().build();
            Try.run(() -> {
                BeanUtils.populate(ttsQueryParams, ttsDetailParams);
                if (ttsParams.getExtraParams() != null) {
                    ttsQueryParams.setExtraParams(ttsParams.getExtraParams());
                }
            }).onFailure(e -> {
                log.error("Fail to populate ttsQueryParams, videoId={}", videoId, e);
                progressResult.setStatus(ProgressStatus.ERROR);
                progressResult.setFailureCause("Fail to populate ttsQueryParams");
            });

            TtsDetailResponse ttsDetailResponse = Try.of(() -> ttsSplitServiceImpl.getTtsDetailResponse(ttsDetailParams.get(
                            "tex").toString(),
                    ttsQueryParams)).onFailure(e -> {
                log.error("Fail to get tts detail response, text={}, params={}", textRequest.getTexts().get(0),
                        ttsDetailParams, e);
            }).getOrNull();
            if (ttsDetailResponse == null) {
                progressResult.setStatus(ProgressStatus.ERROR);
                progressResult.setErrorCode(1001);
                progressResult.setFailureCause("获取tts失败");
                return progressResult;
            }

            Map<Integer, Pair<Long, String>> ttsResultDurationMap = Maps.newHashMap();
            for (int i = 0; i < ttsDetailResponse.getDetail().size(); i++) {
                TtsDetailResponse.SentenceDetail sentenceDetail = ttsDetailResponse.getDetail().get(i);
                ttsResultDurationMap.put(i, Pair.of(sentenceDetail.getDuration(), sentenceDetail.getText()));
            }
            Map<Long, Map<Integer, Pair<Long, String>>> ttsResultDurationMaps = Maps.newHashMap();
            ttsResultDurationMaps.put(1L, ttsResultDurationMap);
            // 字幕文件存储
            doWritSubtitlelFile(ttsResultDurationMaps, textRequest.getVideoId(), textRequest.getVideoId());


            try {
                String filePath = AUDIO_TEMP_DIR + videoId + ".wav";
                File dirFile = new File(AUDIO_TEMP_DIR);
                if (!dirFile.exists()) {
                    dirFile.mkdir();
                }
                FileOutputStream writer = new FileOutputStream(filePath);
                writer.write(Base64.getDecoder().decode(ttsDetailResponse.getAudioWavBase64()));
                Path path = Paths.get(filePath);
                wavUrl = storageService.save(path, videoId + ".wav").toString();
                log.info("Success to save wav file, url={}", wavUrl);
                wavUrl = visConfig.getAudioBosUrl() + videoId + ".wav";
                Files.deleteIfExists(path);
            } catch (Exception e) {
                log.error("Fail to save wav file, videoId={}", videoId, e);
                progressResult.setErrorCode(1001);
                progressResult.setStatus(ProgressStatus.ERROR);
                progressResult.setFailureCause("Fail to save wav file");
                return progressResult;
            }
        } else {
            // 正则表达式模式，用来匹配src属性
            Pattern pattern = Pattern.compile("src=\"([^\"]*)\"");
            Matcher matcher = pattern.matcher(textRequest.getTexts().get(0));

            // 检查是否找到匹配
            if (matcher.find()) {
                String srcUrl = matcher.group(1); // 提取捕获的组，即src属性的值
                log.info("Audio srcUrl={}", srcUrl);
                wavUrl = srcUrl;
            } else {
                System.out.println("No src attribute found.");
            }
        }
        VisVideoReq visVideoReq = new VisVideoReq();
        if (characterImage.equals(visConfig.getCharacterImage())) {
            visVideoReq.setBusinessName(visConfig.getBusinessName());
            visVideoReq.setAuthKey(visConfig.getAuthKey());
            visVideoReq.setFeatureName(visConfig.getFeatureName());
        } else {
            visVideoReq.setBusinessName(visConfig.getV4BusinessName());
            visVideoReq.setAuthKey(visConfig.getV4AuthKey());
            visVideoReq.setFeatureName(visConfig.getV4featureName());
        }
        visVideoReq.setResourceKey(videoId);

        VisVideoReq.VisVideoReqData visVideoReqData = new VisVideoReq.VisVideoReqData();
        if (characterImage.equals(visConfig.getCharacterImageV4())) {
            visVideoReqData.setAction(visConfig.getActionV4());
            visVideoReqData.setVideoFormat("mp4");
        } else {
            // 默认走v2
            visVideoReqData.setAction(visConfig.getAction());
        }
        // 设置任务id
        visVideoReqData.setTaskId(videoId);
        // 设置音频的bos地址
        visVideoReqData.setReqAudioId(wavUrl);

        // {\"figure\":{\"name\":\"people_XEAJRt6qWuxFM2XdxnW8B8\"}}
        String characterConfig = textRequest.getCharacterConfig();
        VisCharacter visCharacter = JsonUtil.read(characterConfig, VisCharacter.class);
        if (visCharacter == null) {
            log.error("Fail to parse character config, characterConfig={}, videoId={}", characterConfig, videoId);
            progressResult.setStatus(ProgressStatus.ERROR);
            progressResult.setErrorCode(1001);
            progressResult.setFailureCause("人像为空");
            return progressResult;
        }
        // 设置人像地址
        visVideoReqData.setTrackUrl(visConfig.getVisFigureBosUrl() + visCharacter.getFigure().getName() + ".tar.gz");
        if (characterImage.equals(visConfig.getCharacterImageV4())) {
            visVideoReqData.setTrackUrl(visConfig.getFigureV4Url());
        }
        log.info("Request vis video, visVideoReqData={}", visVideoReqData);
        visVideoReq.setData(Base64.getEncoder().encodeToString(JsonUtil.toJson(visVideoReqData).getBytes()));
        VisVideoReq.Callback callback = new VisVideoReq.Callback();
        callback.setHost(visConfig.getCallbackHost());
        callback.setPath(visConfig.getCallbackPath());
        callback.setPort(visConfig.getCallbackPort());
        log.info("Request vis video, callback={}", callback);
        visVideoReq.setCallback(JsonUtil.toJson(callback));
        try {
            VisVideoRes visVideoRes = visHttpClient.submitVideo(visConfig.getHost(), visVideoReq);
            if (visVideoRes.getCode() != 0) {
                progressResult.setStatus(ProgressStatus.ERROR);
                progressResult.setErrorCode(1001);
                progressResult.setFailureCause("Fail to submit video");
                return progressResult;
            }
        } catch (Exception e) {
            log.error("Fail to submit video, videoId={}", videoId, e);
            progressResult.setStatus(ProgressStatus.ERROR);
            progressResult.setErrorCode(1001);
            progressResult.setFailureCause("Fail to submit video");
            return progressResult;
        }

        progressResult.setStatus(ProgressStatus.SCHEDULED);
        return progressResult;
    }

    private List<String> extractTexts(VideoScheduleRequest.ComplexVideoScheduleRequest textRequest) {
        List<String> res = new ArrayList<>(textRequest.getTexts());
        if (textRequest.getRenderParams() != null) {
            if (textRequest.getRenderParams().containsKey(RenderOpenParameters.video_mode.name()) &&
                    textRequest.getRenderParams().get(RenderOpenParameters.video_mode.name())
                            .equals(Mode.mrq.name())) {
                res.add("<video><cmd>stop</cmd><mode>mrq</mode></video>");
            }
            if (textRequest.getRenderParams().containsKey(RenderOpenParameters.character_type.name()) &&
                    textRequest.getRenderParams().get(RenderOpenParameters.character_type.name())
                            .equals("digital_twin")) {
                res.add("<finishVideoTask></finishVideoTask>");
            }
        }
        return res;
    }

    public ProgressResult start(String appId, String appKey,
                                Text2VideoRequest.ComplexText2VideoRequest text2VideoRequestList) {
        Preconditions.checkArgument(CollectionUtils.isNotEmpty(text2VideoRequestList.getContinueTexts()),
                "text request list cannot be empty");
        complexTextPretreatment(text2VideoRequestList);

        List<ProgressResult> result = submit(text2VideoRequestList.toVideoSubmitRequest(appId, appKey));

        if (result.isEmpty()) {
            throw new DigitalHumanCommonException(Error.VIDEO_TASK_SUBMIT_FAILED);
        }

        return schedule(text2VideoRequestList.toVideoScheduleRequest(appId, appKey, result.get(0).getVideoId()));
    }

    public ProgressResult startPreview(String appId, String appKey,
                                       Text2VideoRequest.ComplexText2VideoRequest text2VideoRequestList) {
        Preconditions.checkArgument(CollectionUtils.isNotEmpty(text2VideoRequestList.getContinueTexts()),
                "text request list cannot be empty");
        complexTextPretreatmentPreview(text2VideoRequestList);

        List<ProgressResult> result = submit(text2VideoRequestList.toVideoSubmitRequest(appId, appKey));

        if (result.isEmpty()) {
            throw new DigitalHumanCommonException(Error.VIDEO_TASK_SUBMIT_FAILED);
        }

        return schedule(text2VideoRequestList.toVideoScheduleRequest(appId, appKey, result.get(0).getVideoId()));
    }

    /**
     * 1. 检查输入
     * 2. 所有文本都不可打断
     */
    @VisibleForTesting
    protected void complexTextPretreatment(Text2VideoRequest.ComplexText2VideoRequest text2VideoRequestList) {
        List<String> newContinueTexts = new ArrayList<>();

        for (String text : text2VideoRequestList.getContinueTexts()) {
            Preconditions.checkArgument(StringUtils.isNotBlank(text), "text render cannot empty");

            /**
             * 有根节点
             * 1. 那么只需要修改其interruptible属性，这个文本就是不可打断的
             * 2. 没有根节点，加上根节点即可
             */
            Document doc = DomUtil.getDocument(text);
            if (doc == null) {
                // case 1 = no root tag: <client></client><speak>123</speak>
                // case 2 = error xml: <client>
                newContinueTexts.add("<speak interruptible=\"false\">" + text + "</speak>");
            } else {
                if (StringUtils.equals(doc.getDocumentElement().getTagName(), "speak")) {
                    doc.getDocumentElement().setAttribute("interruptible", "false");
                    newContinueTexts.add(DomUtil.dumpNode(doc));
                } else {
                    newContinueTexts.add("<speak interruptible=\"false\">" + text + "</speak>");
                }
            }
        }
        log.debug("text2VideoRequestList={}", text2VideoRequestList);

        text2VideoRequestList.setContinueTexts(newContinueTexts);
    }

    @VisibleForTesting
    protected void complexTextPretreatmentPreview(Text2VideoRequest.ComplexText2VideoRequest text2VideoRequestList) {
        List<String> newContinueTexts = new ArrayList<>();

        for (String text : text2VideoRequestList.getContinueTexts()) {
            Preconditions.checkArgument(StringUtils.isNotBlank(text), "text render cannot empty");

            /**
             * 有根节点
             * 1. 那么只需要修改其interruptible属性，这个文本就是不可打断的
             * 2. 没有根节点，加上根节点即可
             */

            if (text.startsWith("<speak><audio src")) {
                String temText = text.substring(19);
                String tts = temText.substring(0, temText.length() - 11);
                // download tts file, and split
                tts = splitAudio(tts);
                text = "<speak><audio src=\"" + tts + "\"/></speak>";
            } else {
                text = getFirstNCharactersWithTags(text, visConfig.getMaxPreviewCharsLength());
            }


            Document doc = DomUtil.getDocument(text);
            if (doc == null) {
                // case 1 = no root tag: <client></client><speak>123</speak>
                // case 2 = error xml: <client>
                newContinueTexts.add("<speak interruptible=\"false\">" + text + "</speak>");
            } else {
                if (StringUtils.equals(doc.getDocumentElement().getTagName(), "speak")) {
                    doc.getDocumentElement().setAttribute("interruptible", "false");
                    newContinueTexts.add(DomUtil.dumpNode(doc));
                } else {
                    newContinueTexts.add("<speak interruptible=\"false\">" + text + "</speak>");
                }
            }
        }
        log.debug("text2VideoRequestList={}", text2VideoRequestList);

        text2VideoRequestList.setContinueTexts(newContinueTexts);
    }

    @Deprecated
    public ProgressResult start(String appId, String appKey, Text2VideoRequest textRequest) {
        Preconditions.checkArgument(StringUtils.isNotEmpty(textRequest.getText()), "text cannot be empty");

        List<ProgressResult> result = submit(textRequest.toVideoSubmitRequest(appId, appKey));

        if (result.isEmpty()) {
            throw new DigitalHumanCommonException(Error.VIDEO_TASK_SUBMIT_FAILED);
        }

        VideoScheduleRequest simpleScheduleRequest = textRequest.toVideoScheduleRequest(
                appId, appKey, result.get(0).getVideoId());
        return schedule(VideoScheduleRequest.ComplexVideoScheduleRequest.convert(simpleScheduleRequest));
    }

    public String getFirstNCharactersWithTags(String input, int n) {
        log.debug("split text, src={}, split length={}", input, n);
        if ("<finishVideoTask></finishVideoTask>".equals(input)) {
            return input;
        }
        String originInput = input;
        // 获取标签列表
        int totalLength = originInput.length();
        if (totalLength <= n) {
            log.debug("split text, return src input={}", originInput);
            return originInput;
        }

        return originInput.substring(0, n);
    }

    public String splitAudio(String audioUrl) throws DigitalHumanCommonException {
        String resultStr = audioUrl;
        try {
            log.debug("split audio, audioUrl is={}", audioUrl);
            String srcFilePath = audioUrl.substring(audioUrl.lastIndexOf("/") + 1, audioUrl.length());
            String subSrcFileName = srcFilePath.substring(srcFilePath.lastIndexOf(".") + 1);
            srcFilePath = UUID.randomUUID().toString() + "." + subSrcFileName;
            log.debug("split audio, srcFile is={}", srcFilePath);
            File srcFile = new File(srcFilePath);
            FileUtils.copyURLToFile(new URL(audioUrl), srcFile);
            String subFileName = srcFilePath.substring(srcFilePath.lastIndexOf(".") + 1);
            String destFilePath = "split-" + UUID.randomUUID().toString() + "." + subFileName;
            String cmd = "ffmpeg -i " + srcFilePath + " -ss 00:00:00 -t " + visConfig.getMaxPreviewAudioSeconds() + " " + destFilePath;
            log.debug("split audio, ffmpeg cmd is={}", cmd);
            int result = execCmd(cmd);
            log.debug("split audio, ffmpeg result is={}", result);
            File destFile = new File(destFilePath);
            Path localPath = Paths.get(destFile.getAbsolutePath());
            URL save = storageService.save(localPath);
            log.debug("split audio, save result is={}", save.toString());
            resultStr = save.toString();
            srcFile.delete();
            destFile.delete();
        } catch (Exception e) {
            log.error("fail to split audio", e);
            throw new DigitalHumanCommonException("failed to split audio");
        }
        return resultStr;
    }

    public int execCmd(String cmd) {
        int exitValue = 1;
        try {
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            ByteArrayOutputStream errorStream = new ByteArrayOutputStream();
            //            PumpStreamHandler streamHandler = new PumpStreamHandler(outputStream, errorStream);
            CommandLine cmdLine = CommandLine.parse(cmd);
            DefaultExecutor executor = new DefaultExecutor();
            //            executor.setStreamHandler(streamHandler);
            exitValue = executor.execute(cmdLine);
        } catch (Exception e) {
            log.error("fail to execute cmd={}", e);
        }
        return exitValue;
    }

    private ProgressResult doStartProgress(VideoStartProgressRequest startProgressRequest) {

        DhClient dhClient = startProgressRequest.getDhClient();
        String appId = startProgressRequest.getAppId();
        String videoId = startProgressRequest.getVideoId();
        List<String> renderTexts = startProgressRequest.getRenderTexts();
        ProgressResult progress =
                updateProgressResult(VideoUpdateRequest.scheduledStatus(videoId, appId));

        final AtomicReference<SessionResult> sessionReference = new AtomicReference<>();
        AtomicBoolean closed = new AtomicBoolean(false);
        try {
            return doProduceVideo(dhClient, appId, renderTexts,
                    progress, sessionReference, closed
                    , startProgressRequest.getOriginRenderTextSize());
        } catch (Exception e) {
            log.warn("Start to update error status of video progress={}", progress, e);
            releaseAndShutdown(dhClient, sessionReference.get(), closed);
            Try.run(() -> {
                int errorCode = -1;
                String errorMessage;
                if (e instanceof DigitalHumanCommonException) {
                    errorCode = ((DigitalHumanCommonException) e).getCode();
                    errorMessage = e.getMessage();
                } else {
                    errorCode = Error.FAIL_TO_START_VIDEO_PROCESSING.getCode();
                    errorMessage = Error.FAIL_TO_START_VIDEO_PROCESSING.getMessage() + ", cause: " + e.getMessage();
                }

                failProgress(VideoFailRequest.builder().id(progress.getVideoId())
                        .errorCode(errorCode).errorMsg(errorMessage)
                        .showErrMsg(videoErrorMsgConf.getRenderingStartupErrorMsg()).build());
            }).onFailure(t -> {
                log.error("Fail to update the failure status of video progress={}", progress.getVideoId(), t);
            });
            throw e;
        }
    }

    private ProgressResult doProduceVideo(final DhClient dhClient
            , String appId, List<String> renderTexts
            , ProgressResult progress
            , AtomicReference<SessionResult> sessionReference
            , AtomicBoolean closed
            , int originRenderTextSize) {
        String videoId = progress.getVideoId();
        log.info("Try to catch render texts = {}, videoId = {}", renderTexts, videoId);
        Preconditions.checkArgument(!renderTexts.isEmpty(), "Text request list cannot empty.");

        prepareRenderSession(dhClient, progress.getVideoId(), closed, appId, sessionReference);

        SessionResult acquireResult = sessionReference.get();

        CompletableFuture<EventData> renderStartFuture = null;
        CompletableFuture<DhResponse> firstAckFuture = null;
        // key1:sequenceId key2:ttsResult sentenceIndex key2:ttsResult sentenceIndex
        Map<Long, Map<Integer, Pair<Long, String>>> ttsResultDurationMap = Maps.newConcurrentMap();
        AtomicBoolean ttsOccurErrorFlag = new AtomicBoolean(false);

        for (String renderText : renderTexts) {
            Pair<CompletableFuture<DhResponse>, CompletableFuture<EventData>> sendFuture =
                    sendTextRender(dhClient, videoId, renderText, acquireResult, closed,
                            (sequenceId) -> sequenceId == renderTexts.size()
                            , ttsResultDurationMap
                            , originRenderTextSize
                            , ttsOccurErrorFlag);
            if (firstAckFuture == null) {
                firstAckFuture = sendFuture.getLeft();
            }
            if (renderStartFuture == null) {
                renderStartFuture = sendFuture.getRight();
            }
        }

        return afterSendTextRender(firstAckFuture, renderStartFuture, acquireResult, videoId);
    }

    private ProgressResult afterSendTextRender(CompletableFuture<DhResponse> ackFuture,
                                               CompletableFuture<EventData> renderStartFuture,
                                               SessionResult acquireResult, String videoId) {
        try {
            if (ackFuture != null) {
                DhResponse dhResponse = ackFuture.get(cloudConfig.getAckWaitingMillis(), TimeUnit.MILLISECONDS);
                log.debug("Received the ack of sessionId={}, response={}", acquireResult.getSessionId(), dhResponse);
                if (dhResponse.getErrorCode() != 0) {
                    // TODO 将这类解析的错误反映到视频合成结果中
                    log.warn("Fail to get the success ack, ack={}, sessionId={}",
                            dhResponse, acquireResult.getSessionId());
                    throw new DigitalHumanCommonException(dhResponse.getErrorCode(), dhResponse.getErrorMessage());
                }
            }

            EventData renderStartEvent =
                    renderStartFuture.get(cloudConfig.getRenderStartWaitingMillis(), TimeUnit.MILLISECONDS);
            if (!RENDER_START.equals(renderStartEvent.getType())) {
                log.info("Fail to receive RENDER_START, shutdown sessionId={}", acquireResult.getSessionId());
                throw new DigitalHumanCommonException(Error.FAIL_TO_START_VIDEO_PROCESSING.getCode(),
                        Error.FAIL_TO_START_VIDEO_PROCESSING.getMessage() + ", cause:" + renderStartEvent.getContent());
            }

            log.info("Success to accept the request signal= ACK and RENDER_START of sessionId={}",
                    acquireResult.getSessionId());
        } catch (Exception e) {
            log.error("Fail to start video processing task, id={} cause: ", acquireResult.getSessionId(), e);
            //            releaseAndShutdown(dhClient, acquireResult, closed);
            if (e instanceof DigitalHumanCommonException) {
                throw ((DigitalHumanCommonException) e);
            }
            throw new DigitalHumanCommonException(Error.FAIL_TO_START_VIDEO_PROCESSING.getCode(),
                    Error.FAIL_TO_START_VIDEO_PROCESSING.getMessage());

        }

        ProgressResult latestStatus = checkVideoProgressAlreadyRendering(videoId);

        log.info("Success to start video pipeline job of sessionId={}, lastestStatus={}", acquireResult.getSessionId(),
                latestStatus);
        return latestStatus;
    }

    /**
     * send text render to cloud.
     *
     * @param dhClient         dh client of cloud
     * @param videoId          video id
     * @param text             text for render
     * @param acquireResult    acquire response of cloud
     * @param closed           forbid duplicate close.
     * @param releaseCondition release condition by sequence id which return from cloud
     * @return ack received future task of left, render start received future of right
     */
    private Pair<CompletableFuture<DhResponse>, CompletableFuture<EventData>> sendTextRender(
            final DhClient dhClient, String videoId, String text,
            SessionResult acquireResult,
            AtomicBoolean closed
            , Function<Long, Boolean> releaseCondition
            , Map<Long, Map<Integer, Pair<Long, String>>> ttsResultDurationMap
            , int originRenderTextSize
            , AtomicBoolean ttsOccurErrorFlag) {
        Preconditions.checkArgument(StringUtils.isNotBlank(text), "text cannot be empty.");
        CompletableFuture<EventData> renderStartFuture = new CompletableFuture<>();
        CompletableFuture<DhResponse> ackFuture = ((CompletableFuture<DhResponse>) dhClient
                .send(TextRequest.builder().text(text).build(), (sequenceId, eventData) -> {
                    log.info("Received the event  of sessionId={}, data={}",
                            acquireResult.getSessionId(), eventData);
                    if (RENDER_START.equals(eventData.getType())) {
                        renderStartFuture.complete(eventData);
                        return;
                    }
                    // TTS 音频数据 当前只有分身视频实现了该协议
                    if (DOWN_TTS_INFO.equals(eventData.getType())) {
                        PartialTtsAudioInfo partialTtsAudioInfo =
                                JsonUtil.read(eventData.getContent(), PartialTtsAudioInfo.class);
                        if (null == partialTtsAudioInfo || ttsOccurErrorFlag.get()) {
                            failProgress(VideoFailRequest.builder().id(videoId)
                                    .errorCode(-1).errorMsg("Down tts audio error")
                                    .showErrMsg(videoErrorMsgConf.getRenderErrorMsg()).build());
                            return;
                        }
                        if (partialTtsAudioInfo.getErrorCode() != 0) {
                            log.error("Tts audio occurred error,sessionId={}", acquireResult.getSessionId());
                            ttsOccurErrorFlag.set(true);
                            ttsResultDurationMap.clear();
                            return;
                        }
                        if (partialTtsAudioInfo.getText() != null) {
                            ttsResultDurationMap.putIfAbsent(sequenceId, Maps.newConcurrentMap());
                            ttsResultDurationMap.get(sequenceId)
                                    .computeIfAbsent(partialTtsAudioInfo.getSentenceIndex(), sentenceIndex -> {
                                        return Pair.of(0L, toPlainText(partialTtsAudioInfo.getText()));
                                    });
                            ttsResultDurationMap.get(sequenceId)
                                    .computeIfPresent(partialTtsAudioInfo.getSentenceIndex(),
                                            (sentenceIndex, oldPair) -> {
                                                Long duration = partialTtsAudioInfo.getAudioSize() / 32L;
                                                return Pair.of(oldPair.getLeft() + duration, oldPair.getRight());
                                            });
                        }
                        if (partialTtsAudioInfo.isCompleted() && sequenceId == originRenderTextSize) {
                            doWritSubtitlelFile(ttsResultDurationMap, videoId, acquireResult.getSessionId());
                        }
                        return;
                    }
                    //  finish flags
                    if (EventData.EventType.RENDER_COMPLETED.name().equals(eventData.getType()) ||
                            EventData.EventType.RENDER_INTERRUPTED.name().equals(eventData.getType()) ||
                            RENDER_ERROR.equals(eventData.getType())) {
                        try {
                            log.info("Finish the video progress of sequence={}, for event={}, of sessionId={}, ",
                                    sequenceId, eventData, acquireResult.getSessionId());
                            if (RENDER_ERROR.equals(eventData.getType())) {
                                if (!renderStartFuture.isDone()) {
                                    renderStartFuture.complete(eventData);
                                }
                                Try.run(() -> {

                                    Map<String, Object> result = new HashMap<>();
                                    result.put("errorCode", Error.VIDEO_PROGRESS_RENDER_ERROR.getCode());
                                    result.put("errorMessage", Error.VIDEO_PROGRESS_RENDER_ERROR.getMessage() +
                                            ", cause: " + eventData.getContent());
                                    String content = eventData.getContent();

                                    handleVisKouboError(content, VisKouboError.IMAGE_VALIDATE_FAILED, result);
                                    handleVisKouboError(content, VisKouboError.MISSING_REQUIRED_PARAMETERS, result);
                                    handleVisKouboError(content, VisKouboError.BAD_REQUEST, result);
                                    handleVisKouboError(content, VisKouboError.POSITION_VALIDATE_FAILED, result);
                                    handleVisKouboError(content, VisKouboError.PARAMETER_VALUE_ERROR, result);
                                    handleVisKouboError(content, VisKouboError.TTS_VALIDATE_FAILED, result);
                                    handleVisKouboError(content, VisKouboError.SERVER_ERROR, result);
                                    handleVisKouboError(content, VisKouboError.ADD_TASK_ERROR, result);
                                    handleVisKouboError(content, VisKouboError.TASK_ID_ERROR, result);
                                    handleVisKouboError(content, VisKouboError.DOWNLOAD_VIDEO_ERROR, result);
                                    handleVisKouboError(content, VisKouboError.CALLBACK_ERROR, result);
                                    handleVisKouboError(content, VisKouboError.FAILED_TO_CREATE_VIS_TASK, result);
                                    handleVisKouboError(content, VisKouboError.FAILED_TO_QUERY_VIS_TASK, result);
                                    handleVisKouboError(content, VisKouboError.FAILED_TO_GET_VIS_VIDEO, result);
                                    handleVisKouboError(content, VisKouboError.FAILED_TO_UPLOAD_VIS_VIDEO, result);
                                    handleVisKouboError(content, VisKouboError.VIS_VIDEO_TIMEOUT, result);
                                    handleVisKouboError(content, VisKouboError.FAILED_TO_UPDATE_VIDEO_PROGRESS, result);
                                    int errorCode = (int) result.get("errorCode");
                                    String errorMessage = (String) result.get("errorMessage");

                                    failProgress(VideoFailRequest.builder().id(videoId)
                                            .errorCode(errorCode).errorMsg(errorMessage)
                                            .showErrMsg(videoErrorMsgConf.getRenderErrorMsg()).build());
                                });

                                releaseAndShutdown(dhClient, acquireResult, closed);

                                // TODO 如果异步已经返回，但是这个时候请求流报错，那么这个错误也要反应到视频合成过程中。
                            }
                        } finally {
                            if (releaseCondition.apply(sequenceId)) {
                                releaseAndShutdown(dhClient, acquireResult, closed);
                            }
                        }
                    }
                }));

        return Pair.of(ackFuture, renderStartFuture);
    }

    private void doWritSubtitlelFile(Map<Long, Map<Integer, Pair<Long, String>>> ttsResultDurationMap
            , String videoId, String sessionId) {
        if (ttsResultDurationMap.isEmpty()) {
            return;
        }
        AtomicReference<String> lastIndexSrtTime = new AtomicReference<>();
        AtomicInteger index = new AtomicInteger(1);
        String filePath = SRT_SUBTITLE_TEMP_DIR + videoId + ".srt";
        File dirFile = new File(SRT_SUBTITLE_TEMP_DIR);
        if (!dirFile.exists()) {
            dirFile.mkdir();
        }
        try (BufferedWriter writer = new BufferedWriter(new OutputStreamWriter(
                new FileOutputStream(filePath), "UTF-8"))) {
            List<Long> sequenceIdKeyCollect =
                    ttsResultDurationMap.keySet().stream().sorted().collect(Collectors.toList());
            for (Long sequenceId : sequenceIdKeyCollect) {
                Map<Integer, Pair<Long, String>> sequenceIdPairMap = ttsResultDurationMap.get(sequenceId);
                List<Integer> subKeyCollect = sequenceIdPairMap.keySet().stream().sorted().collect(Collectors.toList());
                for (Integer subKey : subKeyCollect) {
                    Pair<Long, String> durationPair = sequenceIdPairMap.get(subKey);
                    writer.write(index.get() + "");
                    writer.newLine();
                    StringBuilder timeBuilder = new StringBuilder();
                    if (null == lastIndexSrtTime.get()) {
                        lastIndexSrtTime.set("00:00:00,000");
                    }
                    if (index.get() == 1) {
                        timeBuilder.append(lastIndexSrtTime.get());
                    } else {
                        timeBuilder.append(addDuration(lastIndexSrtTime.get(), 1));
                    }

                    timeBuilder.append(" --> ");
                    String endSrtTime = addDuration(lastIndexSrtTime.get(), durationPair.getLeft());
                    timeBuilder.append(endSrtTime);
                    writer.write(timeBuilder.toString());
                    writer.newLine();
                    writer.write(durationPair.getRight());
                    writer.newLine();
                    writer.newLine();
                    lastIndexSrtTime.set(endSrtTime);
                    index.incrementAndGet();
                }
            }
            ttsResultDurationMap.clear();
        } catch (Exception e) {
            log.error("Do process srt file error,sessionId={}, videoId={}", sessionId, videoId, e);
        }
        Try.run(() -> {
            File srtFile = new File(filePath);
            if (srtFile.exists()) {
                String url = storageService.save(Paths.get(filePath), videoId + ".srt").toString();
                log.info("Success to save srt file, url={}, sessionId={}, videoId={}", url, sessionId, videoId);
                updateProgressResult(VideoUpdateRequest.builder()
                        .subtitleFileUrl(url)
                        .id(videoId)
                        .build());
                srtFile.delete();
            } else {
                log.error("Srt file not exist ,srtFile={}, sessionId={}, videoId={}", srtFile, sessionId, videoId);
            }
        }).onFailure(throwable -> {
            log.error("Save srt file error, sessionId={}, videoId={]", sessionId, videoId, throwable);
        });

    }

    private void prepareRenderSession(final DhClient dhClient, String videoId, AtomicBoolean closed,
                                      String appId, AtomicReference<SessionResult> sessionReference) {

        try {
            log.info("Start to acquire for appId={} ", appId);

            /* blocking */
            SessionResult acquireResult = dhClient.acquire();
            sessionReference.set(acquireResult);

            updateProgressResult(VideoUpdateRequest.prepareSessionId(videoId, appId, acquireResult.getSessionId()));

            dhClient.registerStateListener(new ClientStateListener() {

                @Override
                public void onStreamError(Throwable t) {

                    log.warn("Video generation is not work properly , shutdown it, session={}",
                            acquireResult, t);
                    releaseAndShutdown(dhClient, acquireResult, closed);

                }

                @Override
                public void onStreamCompleted() {
                    log.warn("Video generation is not work properly , shutdown it, session={}", acquireResult);
                    releaseAndShutdown(dhClient, acquireResult, closed);
                }
            });
            log.info("Success to acquire session={} ", acquireResult.getSessionId());
        } catch (DigitalHumanException e) {
            log.warn("Fail to acquire session of appId={}", appId, e);
            throw new DigitalHumanCommonException(e.getErrorCode(), e.getErrorMessage());
        }
    }

    private List<ProgressResult> submitProgress(VideoSubmitRequest submitRequest) {
        log.info("Start to submit the text to video request, appId={}, appKey={}, request={}", submitRequest.getAppId(),
                submitRequest.getAppKey(), submitRequest);

        Response<List<ProgressResult>> rsp = videoProgressHttpClient.submitProgress(submitRequest);

        if (!rsp.isSuccess()) {
            log.warn("Fail to submit the video progress,  request={}, msg={}", submitRequest, rsp.getMessage());
            throw new DigitalHumanCommonException(rsp.getCode(),
                    "fail to submit video task, cause: " + rsp.getMessage());
        }
        log.info("Success to submit video progress, videoIds={}",
                rsp.getResult().stream().map(ProgressResult::getVideoId).collect(Collectors.toList()));
        return rsp.getResult();
    }

    public ProgressResult failProgress(VideoFailRequest failRequest) {
        failedStatusAdapter.accept(failRequest);
        log.info("Start to update the failed status of request={}", failRequest);

        Response<ProgressResult> response = videoProgressHttpClient.failProgress(failRequest);
        if (!response.isSuccess()) {
            log.warn("Fail to update the failed status of request={}, msg={}",
                    failRequest, response.getMessage().getGlobal());
            throw new DigitalHumanCommonException(response.getCode(),
                    "fail to update the failed status , cause: " + response.getMessage().getGlobal());

        }
        return response.getResult();
    }

    public ProgressResult updateProgressResult(VideoUpdateRequest updateRequest) {
        log.info("Start to update the video progress status , target status={}", updateRequest);

        Response<ProgressResult> rsp =
                videoProgressHttpClient.updateProgress(updateRequest);
        if (!rsp.isSuccess()) {
            log.warn("Fail to update the progress status of videoId={}, msg={}", updateRequest, rsp.getMessage());
            throw new DigitalHumanCommonException(rsp.getCode(),
                    "fail to update the video progress status, cause: " + rsp.getMessage().getGlobal());

        }
        return rsp.getResult();
    }

    @SneakyThrows(value = {InterruptedException.class})
    private ProgressResult checkVideoProgressAlreadyRendering(String videoId) {
        try {
            return checkProgress(videoId);
        } catch (Exception e) {
            log.warn("Video progress is not ready at once, never mind, lets retry");
        }

        TimeUnit.MILLISECONDS.sleep(cloudConfig.getRenderStartCheckDelayMillis());
        return checkProgress(videoId);
    }

    private ProgressResult checkProgress(String videoId) {
        Response<ProgressResult> result =
                videoProgressHttpClient.pollProgress(videoId);

        if (!result.isSuccess()) {
            log.warn("Video already encounter failure, result={} ", result);
            throw new DigitalHumanCommonException(result.getCode(), result.getMessage().getGlobal());
        }
        if (ProgressStatus.SCHEDULED.equals(result.getResult().getStatus()) ||
                ProgressStatus.INIT.equals(result.getResult().getStatus())) {
            log.warn("Video progress still in INIT/SCHEDULED, after receive ack ");
            throw new DigitalHumanCommonException(Error.VIDEO_PROGRESS_ACCEPT_STREAM_TIMEOUT.getCode(),
                    Error.VIDEO_PROGRESS_ACCEPT_STREAM_TIMEOUT.getMessage());
        }

        return result.getResult();
    }

    private void releaseAndShutdown(DhClient dhClient, SessionResult acquireResult, AtomicBoolean shutdown) {

        String sessionId = acquireResult == null ? null : acquireResult.getSessionId();
        log.info("Received release shutdown event of sessionId={}", sessionId);
        if (!shutdown.compareAndSet(false, true)) {
            log.info("Client already shutdown of sessionId");
            return;
        }

        scheduler.schedule(() -> {
            try {
                dhClient.release();
                log.info("Success release the session={}", sessionId);
            } catch (Exception e) {
                log.warn("Fail to release session={}", sessionId, e);
            }

            try {
                dhClient.shutdown();
                log.info("Success shutdown the client, sessionId={}", sessionId);
            } catch (Exception e) {
                log.error("Fail to shutdown client, sessionId={}", sessionId, e);
            }
        }, cloudConfig.getCloseDelayMs(), TimeUnit.MILLISECONDS);
    }

    @RequiredArgsConstructor
    public static class FailedStatusAdapter implements Consumer<VideoFailRequest> {

        private final VideoPipelineConfigure.ProgressConfig progressConfig;

        @Override
        public void accept(VideoFailRequest videoFailRequest) {
            // 调度失败 则能够重新调度， 不增加重试次数
            videoFailRequest.setScheduleFailed(
                    progressConfig.getErrorsScheduleFailed().contains(videoFailRequest.getErrorCode()));

            videoFailRequest
                    .setCanRetry(!progressConfig.getErrorsCannotRetry().contains(videoFailRequest.getErrorCode()));
        }
    }

    private static Map<String, Object> handleVisKouboError(String content, VisKouboError errorType,
                                                           Map<String, Object> result) {
        if (content.contains("VisKouboError:" + String.valueOf(errorType.getSrcCode()))) {
            result.put("errorCode", errorType.getDstCode());
            result.put("errorMessage", Error.VIDEO_PROGRESS_RENDER_ERROR.getMessage() +
                    ", cause: " + errorType.getMessage());
        }
        return result;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    private static class VideoStartProgressRequest {
        private DhClient dhClient;
        private String appId;
        private String videoId;
        private List<String> renderTexts;
        private int originRenderTextSize;
        private SubtitleParams subtitleParams;
    }

    /**
     * 将毫秒转换为 SRT 字幕文件的时间格式
     *
     * @param duration 毫秒数
     * @return 格式化的时间字符串
     */
    private static String formatDuration(long duration) {
        // 创建从00:00开始的时间
        LocalTime time = LocalTime.ofNanoOfDay(duration * 1000000);
        // 格式化时间
        return SRT_TIME_FORMATTER.format(time);
    }

    public String addDuration(String time, long millisToAdd) {
        LocalTime localTime = LocalTime.parse(time, SRT_TIME_FORMATTER);
        Duration duration = Duration.ofMillis(millisToAdd);
        LocalTime newTime = localTime.plus(duration);
        return newTime.format(SRT_TIME_FORMATTER);
    }

    private String toPlainText(String text) {
        return PLAIN_TEXT_PATTERN.matcher(text).replaceAll("");
    }

}
