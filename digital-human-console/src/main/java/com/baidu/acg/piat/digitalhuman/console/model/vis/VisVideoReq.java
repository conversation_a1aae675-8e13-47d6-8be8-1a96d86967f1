package com.baidu.acg.piat.digitalhuman.console.model.vis;

import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Data;

/*
 * https://console.cloud.baidu-int.com/vic/admin/feature/4773/
 */
@Data
public class VisVideoReq {

    @JsonProperty("business_name")
    private String businessName;

    @JsonProperty("resource_key")
    private String resourceKey;

    @JsonProperty("auth_key")
    private String authKey;

    @JsonProperty("feature_name")
    private String featureName;

    String data;

    String  callback;

    @Data
    public static class Callback {

        private String path;

        private String host;

        private int port;

        @JsonProperty("retry_times")
        private int retryTimes = 3;
    }

    @Data
    public static class VisVideoReqData {

        private String action = "lipkol";

        @JsonProperty("task_id")
        private String taskId;

        @JsonProperty("track_url")
        private String trackUrl;

        @JsonProperty("req_audio_id")
        private String reqAudioId;

        @JsonProperty("video_format")
        private String videoFormat;
    }
}
