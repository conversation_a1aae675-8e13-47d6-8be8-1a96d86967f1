package com.baidu.acg.piat.digitalhuman.console.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;

import com.baidu.acg.piat.digitalhuman.common.access.AccessApp;
import com.baidu.acg.piat.digitalhuman.common.exception.DigitalHumanCommonException;
import com.baidu.acg.piat.digitalhuman.common.exception.Error;
import com.baidu.acg.piat.digitalhuman.common.model.PageResponse;
import com.baidu.acg.piat.digitalhuman.common.model.Response;
import com.baidu.acg.piat.digitalhuman.console.model.app.AppListRequest;
import com.baidu.acg.piat.digitalhuman.console.model.common.AuthorizationInfo;
import com.baidu.acg.piat.digitalhuman.console.service.access.AccessControlService;

/**
 * @Author: <EMAIL>
 * @Date: 2020/3/23
 */

@Slf4j
@RequestMapping(value = {"/api/digitalhuman/console/v1/app"})
@RestController
@RequiredArgsConstructor
@Api(tags = "App管理")
public class AppController {

    private final AccessControlService accessControlService;

    private static final int DEFAULT_API_VERSION = 1;

    @PostMapping("/create")
    @ApiOperation(value = "创建app")
    public Response<AccessApp> createApp(HttpServletRequest httpServletRequest,
                                         @Valid @RequestBody AccessApp request) {
        AuthorizationInfo authInfo = checkAuth(httpServletRequest);
        if (StringUtils.isBlank(request.getUserId())) {
            request.setUserId(authInfo.getUserId());
        }
        log.info("AppController create app, userId={}, name={}", request.getUserId(), request.getName());
        return Response.success(accessControlService.createApp(request));
    }

    @PostMapping("/delete")
    @ApiOperation(value = "删除app")
    public Response<Void> deleteApp(HttpServletRequest httpServletRequest,
                                    @Valid @RequestBody AccessApp request) {
        checkAuth(httpServletRequest);
        log.debug("AppController delete app, userId:{}, appId:{}", request.getUserId(), request.getAppId());
        accessControlService.deleteApp(null, 1, request.getAppId());
        return Response.success(null);
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新app")
    public Response<AccessApp> updateApp(HttpServletRequest httpServletRequest,
                                         @Valid @RequestBody AccessApp request) {
        checkAuth(httpServletRequest);
        log.debug("AppController update app, userId:{}, appId:{}", request.getUserId(), request.getAppId());
        return Response.success(accessControlService.updateApp(request));
    }

    @PostMapping("/list")
    @ApiOperation(value = "app列表")
    public PageResponse<AccessApp> listAll(HttpServletRequest httpServletRequest,
                                           @Valid @RequestBody AppListRequest request) {
        var authInfo = checkAuth(httpServletRequest);
        log.debug("AppController list all app pageNo:{}, pageSize:{}", request.getPageNo(), request.getPageSize());
        return accessControlService.listAll(authInfo.getAppId(), request.getUserId(), request.getName(),
                DEFAULT_API_VERSION, request.getPageNo(), request.getPageSize());
    }

    @PostMapping("/detail")
    @ApiOperation(value = "app明细")
    public Response<AccessApp> getApp(HttpServletRequest httpServletRequest,
                                      @NotEmpty @RequestBody AccessApp request) {
        checkAuth(httpServletRequest);
        log.debug("AppController get app, appId:{}", request.getAppId());
        return Response.success(accessControlService.getApp(request.getAppId()));
    }

    private AuthorizationInfo checkAuth(HttpServletRequest httpServletRequest) {
        var authInfo = (AuthorizationInfo) httpServletRequest.getAttribute("authorization");
        if (authInfo == null) {
            throw new DigitalHumanCommonException(Error.AUTHORIZATION_NOT_PRESENT.getCode(),
                    Error.AUTHORIZATION_NOT_PRESENT.getMessage());
        }
        return authInfo;
    }

}
