package com.baidu.acg.piat.digitalhuman.console.service.user;

import com.baidu.acg.piat.digitalhuman.common.access.AccessUser;

/**
 * Created on 2021/2/3 16:23.
 *
 * <AUTHOR>
 */
public class UserThreadLocal {

    private static ThreadLocal<AccessUser> accessUserThreadLocal = new ThreadLocal<>();

    public static AccessUser getAccessUser() {
        return accessUserThreadLocal.get();
    }

    public static void setAccessUser(AccessUser accessUser) {
        accessUserThreadLocal.set(accessUser);
    }

    public static void removeAccessUser() {
        accessUserThreadLocal.remove();
    }

    public static String getUserId() {
        return accessUserThreadLocal.get().getUserId();
    }

}
