package com.baidu.acg.piat.digitalhuman.console.controller.sce;

import javax.servlet.http.HttpServletRequest;

import com.baidu.acg.piat.digitalhuman.console.config.TtsSquareConfigure;
import com.fasterxml.jackson.databind.JsonNode;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;

@Slf4j
@RequestMapping({"/api/digitalhuman/sce/v1/text2audio"})
@RestController
@RequiredArgsConstructor
public class TtsSquareController {

    private final TtsSquareConfigure ttsSquareClientConfig;
    private final RestTemplate restTemplate = new RestTemplate();


    @RequestMapping(value = "/**", method = {RequestMethod.GET, RequestMethod.POST, RequestMethod.PUT, RequestMethod.DELETE})
    public ResponseEntity<JsonNode> forwardRequest(
            HttpServletRequest request,
            @RequestHeader HttpHeaders headers,
            @RequestParam Map<String, String> queryParams,
            @RequestParam(required = false) String responseType,
            @RequestBody(required = false) String body) {

        // 获取属性
        String uid = (String) request.getAttribute("uid");
        String accountId = (String) request.getAttribute("accountId");
        String language = request.getHeader("Language");
        log.info("start forward uid:{}, accountId: {}, language: {}", uid, accountId, language);

        // 处理空值情况
        if (uid == null) {
            uid = "";
        }
        if (accountId == null) {
            accountId = "";
        }
        if (language == null) {
            language = "";
        }


        // 获取完整路径
        String fullPath = request.getRequestURI();

        // 获取 "/api/digitalhuman/sce/v1/text2audio" 之后的路径
        String basePath = "/api/digitalhuman/sce/v1/text2audio";
        String subPath = fullPath.substring(request.getContextPath().length() + basePath.length());

        // 获取请求方法
        HttpMethod method = HttpMethod.resolve(request.getMethod());

        // 添加 accountID, userID header
        headers.set("accountID", accountId);
        headers.set("userID", uid);
        headers.set("Language", language);


        // 创建新的 HttpEntity 包含 header 和 body
        HttpEntity<String> requestEntity = new HttpEntity<>(body, headers);

        // 构建带有查询参数的 URL
        UriComponentsBuilder uriBuilder = UriComponentsBuilder.fromHttpUrl(ttsSquareClientConfig.ttsSquareClientConfig().
                getHost()).pathSegment(ttsSquareClientConfig.ttsSquareClientConfig().getPath()).path(subPath);
        // 添加查询参数
        if (queryParams != null && !queryParams.isEmpty()) {
            queryParams.forEach(uriBuilder::queryParam);
        }

        String url = uriBuilder.build().toUriString();

        log.info("api/digitalhuman/sce/v1/text2audio Forwarding request to URL: {}，accountID: {},userID: {},Language: {}", url, accountId, accountId, language);
        // 转发请求到第三方
        ResponseEntity<JsonNode> response = restTemplate.exchange(url, method, requestEntity, JsonNode.class);
        log.info("api/digitalhuman/sce/v1/text2audio Forwarding, response.body: {}", response.getBody());
        return new ResponseEntity<>(response.getBody(), response.getStatusCode());
    }
}
