package com.baidu.acg.piat.digitalhuman.console.interceptors.privatecloud;

import com.baidu.acg.piat.digitalhuman.common.exception.DigitalHumanCommonException;
import com.baidu.acg.piat.digitalhuman.console.config.LoginConfigure;
import com.baidu.acg.piat.digitalhuman.console.model.common.Constant;
import com.baidu.acg.piat.digitalhuman.console.service.user.UserThreadLocal;
import com.baidu.acg.piat.digitalhuman.plat.client.PlatformClient;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * Created on 2020/4/27 16:38.
 *
 * <AUTHOR>
 */
@Deprecated
@Slf4j
@Component
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class UserInterceptor extends HandlerInterceptorAdapter {

    private final PlatformClient accessClient;

    private final LoginConfigure loginConfig;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler)
            throws Exception {
        if (Constant.LOGIN_TYPE_ONLINE.equals(loginConfig.loginConfig().getType())
                || !request.getRequestURI().startsWith("/api/digitalhuman/sce")
                || request.getRequestURI().startsWith("/api/digitalhuman/sce/v1/user/login")) {
            // 对于IAM的请求 && 静态资源请求，不作头部校验
            return true;
        }
        if (request.getCookies() == null || request.getCookies().length == 0) {
            return redirect(response);
        }
        var cookies = request.getCookies();
        String userId = null;
        for (Cookie cookie : cookies) {
            if (cookie.getName().equals("userId")) {
                userId = cookie.getValue();
            }
        }
        if (StringUtils.isEmpty(userId)) {
            return redirect(response);
        }
        try {
            var accessUser = accessClient.getUser(userId);
            UserThreadLocal.setAccessUser(accessUser);
        } catch (DigitalHumanCommonException e) {
            log.debug("Cannot find user");
            return redirect(response);
        }
        return true;
    }

    private boolean redirect(HttpServletResponse response) throws IOException {
        var redirectUrl = "/sce/login";
        var redirect = "{\"success\":false,\"message\":{\"redirect\":\"" + redirectUrl + "\"}}";
        response.getWriter().print(redirect);
        return false;
    }

    @Override
    public void postHandle(HttpServletRequest request, HttpServletResponse response, Object handler,
                           ModelAndView modelAndView) {
        UserThreadLocal.removeAccessUser();
        log.debug("removeAccessUser");
    }
}
