package com.baidu.acg.piat.digitalhuman.console.controller.sce;

import java.util.ArrayList;

import com.baidu.acg.piat.digitalhuman.common.common.AddType;
import com.baidu.acg.piat.digitalhuman.common.llmrole.LlmRole;
import com.baidu.acg.piat.digitalhuman.common.llmrole.LlmRoleCopy;
import com.baidu.acg.piat.digitalhuman.common.llmrole.RoleDeleteRequest;
import com.baidu.acg.piat.digitalhuman.common.llmrole.RoleWithProject;
import com.baidu.acg.piat.digitalhuman.common.model.PageResponse;
import com.baidu.acg.piat.digitalhuman.common.model.Response;
import com.baidu.acg.piat.digitalhuman.common.project.Project;
import com.baidu.acg.piat.digitalhuman.console.service.sce.LlmRoleService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestAttribute;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;



/**
 * <AUTHOR> Xiaoyu
 * @since 2023/11/28 10:18
 */
@Slf4j
@RequestMapping({"/api/digitalhuman/sce/v1/llm/role"})
@RestController
@RequiredArgsConstructor
public class SceLlmRoleController {

    private static final int DEFAULT_V2_API_VERSION = 2;

    private final LlmRoleService llmRoleService;

    @PostMapping("/create")
    public Response<LlmRole> create(@RequestBody RoleWithProject request,
                                    @RequestAttribute("accountId") String accountId,
                                    @RequestAttribute("username") String username,
                                    @RequestAttribute("uid") String uid) {
        if (StringUtils.isBlank(request.getRole().getName())) {
            return Response.fail("名称不能为空");
        }
        if (request.getRole() == null) {
            request.setRole(new LlmRole());
        }
        if (request.getProject() == null) {
            request.setProject(new Project());
        }
        request.getRole().setAccountId(accountId);
        request.getRole().setEditor(username);
        request.getProject().setUserId(accountId);
        request.getProject().setEditor(username);
        request.getProject().setApiVersion(DEFAULT_V2_API_VERSION);
        request.getProject().setUid(uid);
        request.getProject().setName(request.getRole().getName());
        request.getProject().setAddType(AddType.CREATE.name());
        return llmRoleService.create(request);
    }

    @PostMapping("/delete/batch")
    public Response<Void> batchDelete(@RequestBody RoleDeleteRequest request,
                                      @RequestAttribute("accountId") String accountId) {
        return llmRoleService.delete(accountId, request.getIds());
    }

    @PutMapping("/update/{llmRoleId}")
    public Response<LlmRole> update(@PathVariable(value = "llmRoleId") String llmRoleId,
                                    @RequestBody RoleWithProject request,
                                    @RequestAttribute("accountId") String accountId,
                                    @RequestAttribute("username") String username,
                                    @RequestAttribute("uid") String uid) {
        if (request.getRole() == null) {
            request.setRole(new LlmRole());
        }
        if (request.getProject() == null) {
            request.setProject(new Project());
        }
        request.getRole().setAccountId(accountId);
        request.getRole().setEditor(username);
        request.getProject().setUserId(accountId);
        request.getProject().setEditor(username);
        request.getProject().setApiVersion(DEFAULT_V2_API_VERSION);
        request.getProject().setUid(uid);
        var update = llmRoleService.update(llmRoleId, request);
        return update;
    }

    @PostMapping("/createByTemplateId/{llmRoleTemplateId}")
    public Response<LlmRole> update(@PathVariable(value = "llmRoleTemplateId") String llmRoleTemplateId,
                                    @RequestAttribute("accountId") String accountId,
                                    @RequestAttribute("username") String username,
                                    @RequestAttribute("uid") String uid, @RequestBody(required = false) LlmRole requestLLmRole) {

        LlmRole llmRole = new LlmRole();
        llmRole.setAccountId(accountId);
        llmRole.setEditor(username);
        llmRole.setUid(uid);
        if (requestLLmRole != null && StringUtils.isNotEmpty(requestLLmRole.getName())) {
            llmRole.setName(requestLLmRole.getName());
        }
        return llmRoleService.createLlmRoleByTemplate(llmRoleTemplateId, llmRole);
    }

    /**
     * 发布角色及项目。
     *
     * @param llmRoleId   角色ID
     * @param request     请求体，包含角色和项目信息
     * @param accountId   用户ID
     * @param username    用户名
     * @param uid         用户唯一标识符
     * @return 返回响应，包含角色对象和项目对象
     */
    @PutMapping("/publish/{llmRoleId}")
    public Response<String> publish(@PathVariable(value = "llmRoleId") String llmRoleId,
                                    @RequestBody RoleWithProject request,
                                    @RequestAttribute("accountId") String accountId,
                                    @RequestAttribute("username") String username,
                                    @RequestAttribute("uid") String uid) {
        if (request.getRole() == null) {
            request.setRole(new LlmRole());
        }
        if (request.getProject() == null) {
            request.setProject(new Project());
        }
        request.getRole().setAccountId(accountId);
        request.getRole().setEditor(username);
        request.getProject().setUserId(accountId);
        request.getProject().setEditor(username);
        request.getProject().setApiVersion(DEFAULT_V2_API_VERSION);
        request.getProject().setUid(uid);
        return llmRoleService.publish(llmRoleId, request);
    }

    @GetMapping("/list")
    public PageResponse<LlmRole> list(@RequestAttribute("accountId") String accountId,
                                      @RequestParam int roleType,
                                      @RequestParam(required = false, defaultValue = "") String name,
                                      @RequestParam int pageNo,
                                      @RequestParam int pageSize) {

        if (StringUtils.isEmpty(accountId)) {
            return PageResponse.success(pageNo, pageSize, 0, new ArrayList<>());
        }
        var llmRoles = llmRoleService.list(accountId, roleType, name, pageNo, pageSize);
        return llmRoles;
    }

    @GetMapping("/detail")
    public Response<LlmRole> detail(@RequestAttribute("accountId") String accountId, @RequestParam String llmRoleId) {

        var llmRole = llmRoleService.detail(accountId, llmRoleId);
        return llmRole;
    }

    @PostMapping("/copy")
    public Response<LlmRole> copy(@RequestAttribute("accountId") String accountId, @RequestBody LlmRoleCopy copy) {

        var llmRole = llmRoleService.copy(accountId, copy);
        return llmRole;
    }

    @PostMapping("/template/create")
    public Response<LlmRole> createTemplate(@RequestBody RoleWithProject request,
                                            @RequestAttribute("accountId") String accountId,
                                            @RequestAttribute("username") String username,
                                            @RequestAttribute("uid") String uid,
                                            @RequestAttribute("roleLevel") int roleLevel) {
        if (roleLevel != 3) {
            return Response.fail("该账户权限不足");
        }
        if (StringUtils.isBlank(request.getRole().getName())) {
            return Response.fail("名称不能为空");
        }
        if (request.getRole() == null) {
            request.setRole(new LlmRole());
        }
        if (request.getProject() == null) {
            request.setProject(new Project());
        }
        request.getRole().setAccountId(accountId);
        request.getRole().setEditor(username);
        request.getProject().setUserId(accountId);
        request.getProject().setEditor(username);
        request.getProject().setApiVersion(DEFAULT_V2_API_VERSION);
        request.getProject().setUid(uid);
        request.getProject().setName(request.getRole().getName());
        request.getProject().setAddType(AddType.CREATE.name());
        return llmRoleService.createTemplate(request);
    }

    @PostMapping("/template/delete/batch")
    public Response<Void> batchDeleteTemplate(@RequestBody RoleDeleteRequest request,
                                              @RequestAttribute("accountId") String accountId,
                                              @RequestAttribute("username") String username,
                                              @RequestAttribute("roleLevel") int roleLevel) {
        if (roleLevel != 3) {
            return Response.fail("该账户权限不足");
        }
        return llmRoleService.deleteTemplate(accountId, request.getIds());
    }

    @PutMapping("/template/update/{llmRoleId}")
    public Response<LlmRole> updateTemplate(@PathVariable(value = "llmRoleId") String llmRoleId,
                                            @RequestBody RoleWithProject request,
                                            @RequestAttribute("accountId") String accountId,
                                            @RequestAttribute("username") String username,
                                            @RequestAttribute("uid") String uid,
                                            @RequestAttribute("roleLevel") int roleLevel) {
        if (roleLevel != 3) {
            return Response.fail("该账户权限不足");
        }
        if (request.getRole() == null) {
            request.setRole(new LlmRole());
        }
        if (request.getProject() == null) {
            request.setProject(new Project());
        }
        request.getRole().setAccountId(accountId);
        request.getRole().setEditor(username);
        request.getProject().setUserId(accountId);
        request.getProject().setEditor(username);
        request.getProject().setApiVersion(DEFAULT_V2_API_VERSION);
        request.getProject().setUid(uid);
        var update = llmRoleService.updateTemplate(llmRoleId, request);
        return update;
    }
}
