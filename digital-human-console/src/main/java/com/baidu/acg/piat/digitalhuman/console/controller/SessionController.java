package com.baidu.acg.piat.digitalhuman.console.controller;

import com.baidu.acg.piat.digitalhuman.common.cloud.CloudSessionResult;
import com.baidu.acg.piat.digitalhuman.common.exception.DigitalHumanCommonException;
import com.baidu.acg.piat.digitalhuman.common.exception.Error;
import com.baidu.acg.piat.digitalhuman.common.model.PageResponse;
import com.baidu.acg.piat.digitalhuman.common.model.Response;
import com.baidu.acg.piat.digitalhuman.console.model.common.AuthorizationInfo;
import com.baidu.acg.piat.digitalhuman.console.model.session.SessionListRequest;
import com.baidu.acg.piat.digitalhuman.console.model.session.SessionRequest;
import com.baidu.acg.piat.digitalhuman.console.service.session.SessionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;


@Slf4j
@RequestMapping(value = {"/api/digitalhuman/console/v1/session"})
@RestController
@RequiredArgsConstructor
@Api(tags = "会话管理")
public class SessionController {

    private final SessionService sessionService;

    @PostMapping("/list")
    @ApiOperation(value = "会话列表")
    public PageResponse<CloudSessionResult> list(HttpServletRequest httpServletRequest,
                                                 @Valid @RequestBody SessionListRequest request) {
        checkAuth(httpServletRequest);
        return sessionService.list(request.getAppId(), request.getSessionId(), request.getPageNo(),
                request.getPageSize());
    }

    @PostMapping("/delete")
    @ApiOperation(value = "会话关闭")
    public Response<Void> delete(HttpServletRequest httpServletRequest,
                                 @Valid @RequestBody SessionRequest request) {
        checkAuth(httpServletRequest);
        log.info("CloudSession close session by appId={}, sessionId={}", request.getAppId(), request.getSessionId());
        sessionService.delete(request.getAppId(), request.getSessionId());
        return Response.success(null);
    }

    @PostMapping("/detail")
    @ApiOperation(value = "查询单个会话")
    public Response<CloudSessionResult> get(HttpServletRequest httpServletRequest,
                                            @Valid @RequestBody SessionRequest request) {
        checkAuth(httpServletRequest);
        log.info("CloudSession get session by appId={}, sessionId={}", request.getAppId(), request.getSessionId());
        return Response.success(sessionService.detail(request.getAppId(), request.getSessionId()));
    }

    private void checkAuth(HttpServletRequest httpServletRequest) {
        AuthorizationInfo authInfo = (AuthorizationInfo) httpServletRequest.getAttribute("authorization");
        if (authInfo == null) {
            throw new DigitalHumanCommonException(Error.AUTHORIZATION_NOT_PRESENT.getCode(),
                    Error.AUTHORIZATION_NOT_PRESENT.getMessage());
        }
    }

}
