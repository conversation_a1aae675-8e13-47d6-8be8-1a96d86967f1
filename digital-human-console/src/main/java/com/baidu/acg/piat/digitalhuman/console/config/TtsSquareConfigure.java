package com.baidu.acg.piat.digitalhuman.console.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import lombok.Data;

@Configuration
public class TtsSquareConfigure {

    @Bean
    @ConfigurationProperties(prefix = "digitalhuman.tts-square.client")
    public TtsSquareClientConfig ttsSquareClientConfig() {
        return new TtsSquareClientConfig();
    }

    @Data
    public static class TtsSquareClientConfig {

        private String host;

        private String path;
    }
}
