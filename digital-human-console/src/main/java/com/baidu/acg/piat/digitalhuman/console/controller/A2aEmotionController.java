package com.baidu.acg.piat.digitalhuman.console.controller;

import com.baidu.acg.piat.digitalhuman.common.model.Response;
import com.baidu.acg.piat.digitalhuman.console.model.a2aemotion.A2aEmotionRecognitionRequest;
import com.baidu.acg.piat.digitalhuman.console.model.a2aemotion.A2aEmotionRecognitionResponse;
import com.baidu.acg.piat.digitalhuman.console.service.a2aemotion.A2aEmotionService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/digitalhuman/console/v2/a2aemotion")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class A2aEmotionController {

    private final A2aEmotionService a2aEmotionService;

    @PostMapping("/recognition")
    public Response<A2aEmotionRecognitionResponse> recognition
            (@RequestBody A2aEmotionRecognitionRequest request) {

        return Response.success(a2aEmotionService.recognition(request));
    }



}
