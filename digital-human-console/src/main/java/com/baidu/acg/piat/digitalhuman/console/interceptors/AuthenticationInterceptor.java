package com.baidu.acg.piat.digitalhuman.console.interceptors;

import com.auth0.jwt.JWT;
import com.auth0.jwt.JWTVerifier;
import com.auth0.jwt.algorithms.Algorithm;
import com.auth0.jwt.exceptions.JWTDecodeException;
import com.auth0.jwt.exceptions.JWTVerificationException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

import java.util.Date;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.baidu.acg.dh.user.client.UserClient;
import com.baidu.acg.dh.user.client.UserResult;
import com.baidu.acg.dh.user.client.model.vo.UserCreateTokenReq;
import com.baidu.acg.dh.user.client.model.vo.UserGetResVO;
import com.baidu.acg.piat.digitalhuman.common.exception.DigitalHumanCommonException;
import com.baidu.acg.piat.digitalhuman.console.model.common.Constant;

@Deprecated
@Component
@Slf4j
@RequiredArgsConstructor
public class AuthenticationInterceptor implements HandlerInterceptor {

    private final UserClient userClient;

    @Value("#{${jwt.expire.refresh.threshold}}")
    private long refreshThreshold;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response,
                             Object object) throws Exception {
        // 从请求中拿到token信息
        String token = request.getHeader("token");
        // 执行认证,token的判断逻辑
        if (token == null) {
            // 处理A标签下载请求，截取url中的token值
            throw new DigitalHumanCommonException("非法token,请重新登录");
        }
        // 获取 token 中的 user name
        String userName;
        try {
            userName = JWT.decode(token).getSubject();
            log.info("login user {}", userName);
        } catch (JWTDecodeException j) {
            throw new DigitalHumanCommonException("非法token,请重新登录");
        }
        // 如果token过期，直接返回；在有效期内的从token中拿到uid，去数据库中查询该用户是否合法
        UserResult<Boolean> booleanUserResult = userClient.isTokenExpire(token);
        if (booleanUserResult.getResult()) {
            log.info("{} token过期", userName);
            throw new DigitalHumanCommonException("过期token,请重新登录");
        }
        UserResult<UserGetResVO> userGetResVOUserResult = userClient.getUserByName(userName);
        UserGetResVO userGetResVO = userGetResVOUserResult.getResult();
        if (userGetResVO == null) {
            log.info("{} 不存在", userName);
            throw new DigitalHumanCommonException("非法token,请重新登录");
        }
        // 使用 用户的密码私钥进行解密，数据库密码加密了，需要解密
        JWTVerifier jwtVerifier = JWT.require(Algorithm.HMAC256(userGetResVO.getPassword()))
                .withIssuer(Constant.JWT_ISSUER)
                .build();
        try {
            // 验证token
            jwtVerifier.verify(token);
            UserResult<Date> dateUserResult = userClient.getTokenExpireDate(token);
            Date expireDate = dateUserResult.getResult();
            long currentTimeMillis = System.currentTimeMillis();
            long duration = expireDate.getTime() - currentTimeMillis;
            UserCreateTokenReq userCreateTokenReq = new UserCreateTokenReq();
            BeanUtils.copyProperties(userGetResVO, userCreateTokenReq);
            if (duration < refreshThreshold) {
                UserResult<String> stringUserResult = userClient.createToken(userCreateTokenReq);
                String refreshToken = stringUserResult.getResult();
                response.setHeader(Constant.JWT_TOKEN, refreshToken);
            }
            request.getSession().setAttribute(Constant.USR_SESSIONID, userGetResVO);
        } catch (JWTVerificationException e) {
            log.info("{} token验证失败", userName);
            throw new DigitalHumanCommonException("非法token,请重新登录");
        }
        return true;
    }

    @Override
    public void postHandle(HttpServletRequest httpServletRequest,
                           HttpServletResponse httpServletResponse,
                           Object o, ModelAndView modelAndView) throws Exception {
    }

    @Override
    public void afterCompletion(HttpServletRequest httpServletRequest,
                                HttpServletResponse httpServletResponse,
                                Object o, Exception e) throws Exception {
    }
}
