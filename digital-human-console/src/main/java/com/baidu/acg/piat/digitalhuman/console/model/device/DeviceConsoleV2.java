package com.baidu.acg.piat.digitalhuman.console.model.device;

import com.baidu.acg.digitalhuman.device.common.v2.model.DeviceV2;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.beans.BeanUtils;

/**
 * @Author: <EMAIL>
 * @Date: 2023/9/4
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class DeviceConsoleV2 extends DeviceV2 {

    /**
     * tts发音人
     */
    private String person;

    /**
     * 绑定人像
     */
    private String figureAlias;

    /**
     * DeviceV2转为DeviceConsoleV2
     * @param deviceV2
     * @return
     */
    public static DeviceConsoleV2 toDeviceConsoleV2(DeviceV2 deviceV2) {
        DeviceConsoleV2 deviceConsoleV2 = new DeviceConsoleV2();
        BeanUtils.copyProperties(deviceV2, deviceConsoleV2);
        return deviceConsoleV2;
    }

}