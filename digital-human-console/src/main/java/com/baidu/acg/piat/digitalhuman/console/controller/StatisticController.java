package com.baidu.acg.piat.digitalhuman.console.controller;

import com.baidu.acg.piat.digitalhuman.common.common.PlatCode;
import com.baidu.acg.piat.digitalhuman.common.model.PageResult;
import com.baidu.acg.piat.digitalhuman.common.model.Response;
import com.baidu.acg.piat.digitalhuman.common.statistic.CharacterPreferencesData;
import com.baidu.acg.piat.digitalhuman.common.statistic.CharacterResourceResponse;
import com.baidu.acg.piat.digitalhuman.common.statistic.CharacterUsageTrendData;
import com.baidu.acg.piat.digitalhuman.common.statistic.ProductionStatisticData;
import com.baidu.acg.piat.digitalhuman.common.statistic.SessionStatisticPageResult;
import com.baidu.acg.piat.digitalhuman.common.statistic.StatisticResponse;
import com.baidu.acg.piat.digitalhuman.common.utils.JsonUtil;
import com.baidu.acg.piat.digitalhuman.console.model.statistic.StatisticRequest;
import com.baidu.acg.piat.digitalhuman.console.service.statistic.StatisticService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestAttribute;

import javax.validation.Valid;
import java.io.ByteArrayOutputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@Slf4j
@RequestMapping("/api/digitalhuman/console/v2/statistic")
@RestController
@RequiredArgsConstructor
public class StatisticController {

    private final StatisticService statisticService;

    @PostMapping("/activeSession")
    public Response<StatisticResponse> activeSession(@Valid @RequestBody StatisticRequest request) {
        return Response.success(statisticService.getActiveSession(request));
    }

    @PostMapping("/totalSession")
    public Response<StatisticResponse> totalSession(@Valid @RequestBody StatisticRequest request) {
        return Response.success(statisticService.getTotalSession(request));
    }

    @PostMapping("/avgSessionTime")
    public Response<StatisticResponse> avgSessionTime(@Valid @RequestBody StatisticRequest request) {
        return Response.success(statisticService.getAvgSessionTime(request));
    }

    @PostMapping("/totalDialog")
    public Response<StatisticResponse> totalDialog(@Valid @RequestBody StatisticRequest request) {
        return Response.success(statisticService.getTotalDialog(request));
    }

    @PostMapping("/sessionTrend")
    public Response<StatisticResponse> sessionTrend(@Valid @RequestBody StatisticRequest request) {
        return Response.success(statisticService.getSessionTrend(request));
    }
    @GetMapping("/characterResource")
    public Response<CharacterResourceResponse> characterResource(
            @RequestParam("platCode") PlatCode platCode
            , @RequestAttribute("tags") Map<String, String> tags
            , @RequestAttribute("accountId") String accountId
            , @RequestAttribute("roleLevel") int roleLevel) {
        String accountVisibleCharacters = Optional.ofNullable(tags).orElse(new HashMap<>())
                .getOrDefault("accountVisibleCharacters", "all");
        return Response.success(statisticService.characterResource(platCode
                , accountVisibleCharacters, accountId, roleLevel));
    }

    @GetMapping("/characterUsageTrend")
    public Response<List<CharacterUsageTrendData>> characterUsageTrend(
            @RequestParam("platCode") PlatCode platCode
            , @RequestParam("startInMs") long startInMs
            , @RequestParam("endInMs") long endInMs
            , @RequestParam("intervalInSeconds") long intervalInSeconds
            , @RequestAttribute("tags") Map<String, String> tags
            , @RequestAttribute("accountId") String accountId
            , @RequestAttribute("roleLevel") int roleLevel) {
        String accountVisibleCharacters = Optional.ofNullable(tags).orElse(new HashMap<>())
                .getOrDefault("accountVisibleCharacters", "all");
        return Response.success(statisticService.characterUsageTrend(
                platCode, startInMs, endInMs, intervalInSeconds
                , accountVisibleCharacters, accountId, roleLevel));
    }

    @GetMapping("/characterPreferences")
    public Response<List<CharacterPreferencesData>> characterPreferences(
            @RequestParam("platCode") PlatCode platCode
            , @RequestAttribute("tags") Map<String, String> tags
            , @RequestAttribute("accountId") String accountId
            , @RequestAttribute("accountMenus") List<String> accountMenus
            , @RequestAttribute("roleLevel") int roleLevel) {
        String accountVisibleCharacters = Optional.ofNullable(tags).orElse(new HashMap<>())
                .getOrDefault("accountVisibleCharacters", "all");
        return Response.success(statisticService.characterPreferences(platCode, accountMenus
                , accountVisibleCharacters, accountId, roleLevel));
    }

    @GetMapping("/sessionService")
    public Response<SessionStatisticPageResult> sessionService(
            @RequestParam("platCode") PlatCode platCode
            , @RequestAttribute("accountId") String accountId
            , @RequestAttribute("roleLevel") int roleLevel
            , @RequestParam(value = "pageNo", defaultValue = "1") int pageNo,
            @RequestParam(value = "pageSize", defaultValue = "20") int pageSize,
            @RequestParam("startInDate") String startInDate,
            @RequestParam("endInDate") String endInDate,
            @RequestParam(value = "orderBy", defaultValue = "sessionTotalCount") String orderBy,
            @RequestParam(value = "order", defaultValue = "desc") String order) {

        return Response.success(statisticService.sessionService(platCode
                , accountId, roleLevel, pageNo, pageSize, startInDate
                , endInDate, orderBy, order));
    }

    @GetMapping("/sessionService/export")
    public ResponseEntity<byte[]> exportSessionService(
            @RequestParam("platCode") PlatCode platCode
            , @RequestAttribute("accountId") String accountId
            , @RequestAttribute("roleLevel") int roleLevel
            , @RequestParam("startInDate") String startInDate,
            @RequestParam("endInDate") String endInDate,
            @RequestParam(value = "orderBy", defaultValue = "sessionTotalCount") String orderBy,
            @RequestParam(value = "order", defaultValue = "desc") String order) {
        HttpHeaders responseHeader = new HttpHeaders();
        try {
            String filename;
            if (startInDate.equals(endInDate)) {
                filename = String.format("服务情况_%s.xlsx", endInDate);
            } else {
                filename = String.format("服务情况_%s_%s.xlsx", startInDate, endInDate);
            }
            String encodedFilename = URLEncoder.encode(filename, StandardCharsets.UTF_8.toString());
            responseHeader.add(HttpHeaders.CONTENT_DISPOSITION
                    , String.format("attachment; filename=%s", encodedFilename));
            ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
            statisticService.exportSessionService(platCode, accountId, roleLevel
                    , startInDate, endInDate, orderBy, order, byteArrayOutputStream);
            return ResponseEntity.ok()
                    .headers(responseHeader)
                    .contentType(MediaType.parseMediaType("application/octet-stream"))
                    .body(byteArrayOutputStream.toByteArray());
        } catch (Exception e) {
            log.error("ExportSessionService error", e);
            responseHeader.setContentType(MediaType.APPLICATION_JSON);
            return new ResponseEntity<>(JsonUtil.writeValueAsStringQuietly(
                    Response.fail(e.getMessage())).getBytes(),
                    responseHeader, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @GetMapping("/production")
    public Response<PageResult<ProductionStatisticData>> production(
            @RequestParam("platCode") PlatCode platCode
            , @RequestAttribute("accountId") String accountId
            , @RequestAttribute("roleLevel") int roleLevel
            , @RequestParam(value = "pageNo", defaultValue = "1") int pageNo,
            @RequestParam(value = "pageSize", defaultValue = "20") int pageSize,
            @RequestParam("startInDate") String startInDate,
            @RequestParam("endInDate") String endInDate) {
        return Response.success(statisticService.production(platCode
                , accountId, roleLevel, pageNo, pageSize, startInDate
                , endInDate));
    }
}
