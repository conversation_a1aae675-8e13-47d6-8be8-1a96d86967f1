package com.baidu.acg.piat.digitalhuman.console.controller.sce;

import com.baidu.acg.piat.digitalhuman.common.model.PageResponse;
import com.baidu.acg.piat.digitalhuman.common.model.Response;
import com.baidu.acg.piat.digitalhuman.common.position.PositionListRequest;
import com.baidu.acg.piat.digitalhuman.common.position.PositionVo;
import com.baidu.acg.piat.digitalhuman.console.service.sce.PositionService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestAttribute;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * Created on 2021/8/6 11:08 上午
 *
 * <AUTHOR>
 */
@Slf4j
@RequestMapping("/api/digitalhuman/sce/v1/position")
@RestController
@RequiredArgsConstructor
public class ScePositionController {

    private final PositionService positionService;

    @PostMapping("/create")
    public Response<PositionVo> create(@RequestBody PositionVo request,
                                       @RequestAttribute("accountId") String uid) {
        request.setUserId(uid);
        return Response.success(positionService.create(request));
    }

    @PostMapping("/delete")
    public Response<Void> delete(@RequestBody PositionVo request) {
        positionService.delete(request.getPositionId());
        return Response.success(null);
    }

    @PostMapping("/update")
    public Response<PositionVo> update(@RequestBody PositionVo request,
                                       @RequestAttribute("accountId") String uid) {
        request.setUserId(uid);
        return Response.success(positionService.update(request));
    }

    @PostMapping("/detail")
    public Response<PositionVo> detail(@RequestBody PositionVo request) {
        return Response.success(positionService.detail(request.getPositionId()));
    }

    @PostMapping("/list")
    public PageResponse<PositionVo> list(@RequestBody PositionListRequest request,
                                         @RequestAttribute("accountId") String uid) {
        request.setUserId(uid);
        return positionService.list(request.getUserId(), request.getPageNo(), request.getPageSize());
    }
}
