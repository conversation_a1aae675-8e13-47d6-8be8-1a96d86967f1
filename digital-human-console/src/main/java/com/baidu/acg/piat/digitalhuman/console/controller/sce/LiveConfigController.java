package com.baidu.acg.piat.digitalhuman.console.controller.sce;

import com.baidu.acg.piat.digitalhuman.common.live.LiveConfig;
import com.baidu.acg.piat.digitalhuman.common.model.PageResponse;
import com.baidu.acg.piat.digitalhuman.common.model.Response;
import com.baidu.acg.piat.digitalhuman.plat.client.PlatformClient;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestAttribute;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * 直播设置
 *
 * <AUTHOR>
 * @since 2021/08/10
 */
@Slf4j
@RequestMapping({"/api/digitalhuman/sce/v1/liveconfig", "/api/digitalhuman/sce/v2/liveconfig"})
@RestController
@RequiredArgsConstructor
public class LiveConfigController {

    private final PlatformClient platformClient;

    @PostMapping("create")
    public Response<LiveConfig> create(@Valid @RequestBody LiveConfig config,
                                       @RequestAttribute("accountId") String uid) {
        config.setUserId(uid);
        return Response.success(platformClient.createLiveConfig(config));
    }

    @PostMapping("update")
    public Response<LiveConfig> update(@RequestParam("configId") String configId,
                                       @Valid @RequestBody LiveConfig config,
                                       @RequestAttribute("accountId") String uid) {
        config.setConfigId(configId);
        config.setUserId(uid);
        return Response.success(platformClient.updateLiveConfig(config));
    }

    @PostMapping("delete")
    public Response<Void> delete(@RequestParam("configId") String configId) {
        platformClient.deleteLiveConfig(configId);
        return Response.success(null);
    }

    @GetMapping("get")
    public Response<LiveConfig> getByConfigId(@RequestParam("configId") String configId) {
        return Response.success(platformClient.findLiveConfig(configId));
    }

    @GetMapping("user/latest")
    public Response<LiveConfig> getLatestOneByUserId(@RequestAttribute("accountId") String uid) {
        return Response.success(platformClient.findLatestLiveConfigByUserId(uid));
    }

    @GetMapping("user/all")
    public PageResponse<LiveConfig> getAllByUserId(@RequestParam(required = false, defaultValue = "1") int pageNo,
                                                   @RequestParam(required = false, defaultValue = "20") int pageSize,
                                                   @RequestAttribute("accountId") String uid) {
        var pageResult = platformClient.findAllLiveConfigByUserId(uid, pageNo, pageSize);
        return PageResponse.success(pageNo, pageSize, pageResult.getTotalCount(), pageResult.getResult());
    }
}
