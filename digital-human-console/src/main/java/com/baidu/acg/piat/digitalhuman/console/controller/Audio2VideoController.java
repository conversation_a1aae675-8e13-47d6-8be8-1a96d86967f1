// Copyright (C) 2020 Baidu Inc. All rights reserved.
package com.baidu.acg.piat.digitalhuman.console.controller;

import com.baidu.acg.piat.digitalhuman.common.exception.DigitalHumanCommonException;
import com.baidu.acg.piat.digitalhuman.common.model.Response;
import com.baidu.acg.piat.digitalhuman.console.model.Audio2VideoResponse;
import com.baidu.acg.piat.digitalhuman.console.service.videopipeline.Audio2VideoService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;

/**
 * Audio2VideoController
 *
 * <AUTHOR>
 * @since 2020-06-03
 */
@Controller
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@RequestMapping("/api/digitalhuman/console/v1/video/audio2video")
public class Audio2VideoController {

    private final Audio2VideoService audio2VideoService;

    @ResponseBody
    @PostMapping(value = "/token")
    public Response<Audio2VideoResponse> generationToken(
            @RequestParam("audio") MultipartFile audio) {

        byte[] audioBytes;
        try {
            audioBytes = audio.getBytes();
        } catch (Exception e) {
            throw new DigitalHumanCommonException("fail to load audio", e);
        }
        return Response.success(Audio2VideoResponse.builder()
                .generationToken(audio2VideoService.generateToken(audioBytes))
                .build());
    }

}
