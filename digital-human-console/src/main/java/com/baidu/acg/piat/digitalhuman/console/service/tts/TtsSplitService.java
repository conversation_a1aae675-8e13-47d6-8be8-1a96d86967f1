package com.baidu.acg.piat.digitalhuman.console.service.tts;

import com.baidu.acg.piat.digitalhuman.console.model.TtsDetailAndAudioResponse;
import com.baidu.acg.piat.digitalhuman.console.model.TtsDetailResponse;
import com.baidu.acg.piat.digitalhuman.tts.model.TtsQueryParams;

/**
 * ttsSplitService
 *
 * <AUTHOR>
 */
public interface TtsSplitService {
    byte[] text2audioWithSplit(String language, String tex, TtsQueryParams ttsQueryParams) throws Exception;

    /*
     * 获取切分后的语句及其对应的时长
     **/
    TtsDetailResponse getTtsDetailResponse(String language, String tex, TtsQueryParams ttsQueryParams)
            throws Exception;

    TtsDetailAndAudioResponse getTtsDetailAndAudioResponse(String language, String tex, TtsQueryParams ttsQueryParams)
            throws Exception;
}
