package com.baidu.acg.piat.digitalhuman.console.controller;

import com.baidu.acg.dh.user.client.model.vo.UserCreateReqVO;
import com.baidu.acg.dh.user.client.model.vo.UserGetResVO;
import com.baidu.acg.dh.user.client.model.vo.UserUpdateReqVO;
import com.baidu.acg.piat.digitalhuman.common.access.AccessUser;
import com.baidu.acg.piat.digitalhuman.common.model.Response;
import com.baidu.acg.piat.digitalhuman.console.model.common.Constant;
import com.baidu.acg.piat.digitalhuman.console.service.user.UserService;
import com.baidu.acg.piat.digitalhuman.plat.client.PlatformClient;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * 用户相关的接口
 *
 * <AUTHOR>
 */
@Slf4j
@RequestMapping("/api/digitalhuman")
@RestController
@RequiredArgsConstructor
public class UserController {

    private final UserService userService;

    private final PlatformClient platformClient;

    @PostMapping("/console/v1/app/login")
    public Response<Void> consoleLogin() {
        return Response.success(null);
    }

    @PostMapping({"/sce/v1/user/login", "/console/v2/user/login"})
    public Response<Void> login(HttpServletRequest request, HttpServletResponse response,
                                @RequestBody AccessUser accessUser) {

        accessUser = platformClient.validateUserName(accessUser);
        addCookie(accessUser.getUserId(), response);
        return Response.success(null);
    }

    @PostMapping({"/sce/v1/user/login/check", "/console/v2/user/login/check"})
    public Response<String> loginCheck(HttpServletRequest request,
                                       @RequestBody UserCreateReqVO userCreateReqVO) {

        var token = userService.loginCheck(userCreateReqVO, request);
        return Response.success(token);
    }

    @GetMapping("/generate/token")
    public String generateToken(@RequestParam String appId, @RequestParam String appKey,
                                @RequestParam(required = false) Integer duration) {
        if (duration == null) {
            duration = 1;
        }
        return userService.generateTokenByApp(appId, appKey, duration);
    }

    @PostMapping({"/sce/v1/user/logout", "/console/v2/user/logout"})
    public Response<Void> logout(HttpServletRequest request, HttpServletResponse response) {
        request.getSession().invalidate();
        response.setHeader(Constant.JWT_TOKEN, null);
        return Response.success(null);
    }

    @PostMapping({"/sce/v1/user/detail", "/console/v2/user/detail"})
    public Response<AccessUser> detail(@ApiIgnore @ModelAttribute("user") UserGetResVO user) {
        return Response.success(AccessUser.builder()
                .userId(user.getUid())
                .name(user.getUserName())
                .build());
    }

    private void addCookie(String userId, HttpServletResponse response) {
        var cookie = new Cookie("userId", userId);
        cookie.setPath("/");
        response.addCookie(cookie);
    }

    /**
     * user的操作，暴露给pm使用
     */
    @PostMapping("/console/v1/user/create")
    public Response<UserGetResVO> create(@RequestBody UserCreateReqVO userRequest) {
        return Response.success(userService.create(userRequest));
    }

    @PostMapping("/console/v1/user/delete")
    public Response<Void> deleteByUserName(@RequestBody UserGetResVO userName) {
        userService.deleteByUserName(userName.getUserName());
        return Response.success(null);
    }

    @PostMapping("/console/v1/user/update")
    public Response<UserGetResVO> updateUserByName(@RequestBody UserUpdateReqVO userRequest) {
        return Response.success(userService.updateUserByName(userRequest));
    }

    @PostMapping("/console/v1/user/getUserById")
    public Response<UserGetResVO> getUserById(@RequestBody UserGetResVO userId) {
        return Response.success(userService.getUserById(userId.getUid()));
    }

    @PostMapping("/console/v1/user/getUserByName")
    public Response<UserGetResVO> getUserByName(@RequestBody UserGetResVO userName) {
        return Response.success(userService.getUserByName(userName.getUserName()));
    }

}
