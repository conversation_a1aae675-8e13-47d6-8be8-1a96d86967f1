package com.baidu.acg.piat.digitalhuman.console.controller.sce;

import com.baidu.acg.piat.digitalhuman.common.material.Material;
import com.baidu.acg.piat.digitalhuman.common.material.MaterialId;
import com.baidu.acg.piat.digitalhuman.common.material.MaterialListRequest;
import com.baidu.acg.piat.digitalhuman.common.model.PageResponse;
import com.baidu.acg.piat.digitalhuman.common.model.Response;
import com.baidu.acg.piat.digitalhuman.console.service.sce.MaterialService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestAttribute;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * Created on 2021/8/6 11:08 上午
 *
 * <AUTHOR>
 */
@Slf4j
@RequestMapping("/api/digitalhuman/sce/v1/material")
@RestController
@RequiredArgsConstructor
public class SceMaterialController {

    private final MaterialService materialService;

    @PostMapping("/create")
    public Response<Material> create(@RequestBody Material request,
                                     @RequestAttribute("accountId") String uid) {
        request.setUserId(uid);
        return Response.success(materialService.create(request));
    }

    @PostMapping("/delete")
    public Response<Void> delete(@RequestBody MaterialId request) {
        materialService.delete(request.getId());
        return Response.success(null);
    }

    @PostMapping("/update")
    public Response<Material> update(@RequestBody Material request,
                                     @RequestAttribute("accountId") String uid) {
        request.setUserId(uid);
        return Response.success(materialService.update(request));
    }

    @PostMapping("/detail")
    public Response<Material> detail(@RequestBody MaterialId request) {
        return Response.success(materialService.detail(request.getId()));
    }

    @PostMapping("/list")
    public PageResponse<Material> list(@RequestBody MaterialListRequest request,
                                       @RequestAttribute("accountId") String uid) {
        request.setUserId(uid);
        return materialService.list(request.getUserId(), request.getName(), request.getType(), request.getPositionId(),
                request.getPageNo(), request.getPageSize());
    }
}
