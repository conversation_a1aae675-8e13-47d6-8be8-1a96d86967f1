package com.baidu.acg.piat.digitalhuman.console.interceptors;

import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

import com.baidu.acg.dh.user.client.model.vo.UserGetResVO;
import com.baidu.acg.piat.digitalhuman.console.model.common.Constant;

/**
 * 增强拦截器
 */
@ControllerAdvice("com.baidu.acg.piat.digitalhuman.console.controller")
@RestController
public class UserInfoAdvice {

    @ModelAttribute("user")
    public UserGetResVO getUser(HttpServletRequest request) {
        return (UserGetResVO) request.getSession().getAttribute(Constant.USR_SESSIONID);
    }

    @ModelAttribute("test")
    public UserGetResVO getUser1(HttpServletRequest request) {

        return UserGetResVO.builder().uid("testid").userName("testName").build();

    }
}
