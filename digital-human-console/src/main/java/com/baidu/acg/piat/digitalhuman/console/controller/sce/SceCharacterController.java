package com.baidu.acg.piat.digitalhuman.console.controller.sce;

import com.baidu.acg.piat.digitalhuman.common.model.PageResponse;
import com.baidu.acg.piat.digitalhuman.common.model.Response;
import com.baidu.acg.piat.digitalhuman.console.model.character.CharacterImageInfo;
import com.baidu.acg.piat.digitalhuman.console.service.character.CharacterService;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestAttribute;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RequestMapping({"/api/digitalhuman/sce/v1/character/image", "/api/digitalhuman/console/v1/character/image"})
@RestController
@RequiredArgsConstructor
public class SceCharacterController {

    private final CharacterService characterService;

    @PostMapping("/detail")
    @ApiOperation(value = "展示人像模型")
    public Response<CharacterImageInfo> findByCharacterImage(@RequestBody CharacterImageInfo request) {
        log.info("SceCharacterController find character by characterImage={}", request.getCharacterImage());
        return Response.success(characterService.findCharacterByCharacterImage(request.getCharacterImage()));
    }

    @PostMapping("/visibleForSce")
    @ApiOperation(value = "展示当前用户SCE可视化人像模型")
    public PageResponse<CharacterImageInfo> characterImageVisibleForSce(
            @RequestAttribute("accountId") String uid) {
        log.info("SceCharacterController characterImage visible for sce request, userId={}", uid);
        return PageResponse.<CharacterImageInfo> builder()
                .page(characterService.characterImagesVisibleForSce(uid))
                .build();
    }
}
