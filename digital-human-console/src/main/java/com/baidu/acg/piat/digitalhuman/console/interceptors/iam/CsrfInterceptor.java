// Copyright (C) 2019 Baidu Inc. All rights reserved.

package com.baidu.acg.piat.digitalhuman.console.interceptors.iam;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.util.AntPathMatcher;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * copy from com.baidu.bce.plat.webframework.iam.config.access.web.BceCsrfInterceptor.
 *
 * <AUTHOR>
 */
@Slf4j
public class CsrfInterceptor extends HandlerInterceptorAdapter {

    private AntPathMatcher pathMatcher = new AntPathMatcher();

    private String cookieMD5Key;

    private String[] csrfPathExcluded;

    public CsrfInterceptor(AccessConfiguration config) {
        this.cookieMD5Key = config.getAuthCookieSecret();
        csrfPathExcluded = config.getCsrfPathExcluded().split(";");
    }

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler)
            throws Exception {
        if (!needCheckCSRF(request, response)) {
            return true;
        }
        return checkCSRF(request, response);
    }

    private boolean checkCSRF(HttpServletRequest request, HttpServletResponse response) throws IOException {

        String csrfToken = request.getHeader("csrftoken");
        String md5Path = DigestUtils.md5Hex(request.getRequestURI());
        if (null == csrfToken) {
            log.info("We may got CSRF attack, no csrftoken found in header");
        } else if (csrfToken.equals(md5Path)) {
            return true;
        } else {
            log.info("csrftoken:{} in header validate failed", csrfToken);
        }

        handleCheckCSRFFailed(response);
        return false;
    }

    private boolean needCheckCSRF(HttpServletRequest request, HttpServletResponse response) {
        return !"GET".equals(request.getMethod()) && needCSRFCheck(request.getRequestURI());
    }

    private void handleCheckCSRFFailed(HttpServletResponse response) throws IOException {
        response.setContentType("application/json");
        response.setCharacterEncoding("UTF-8");
        String checkFailedResult = getCheckFailedResult();
        response.getWriter().print(checkFailedResult);

        log.debug("construct response {}", checkFailedResult);
    }

    private String getCheckFailedResult() {
        StringBuilder sb = new StringBuilder();
        sb.append("{\"success\":\"false\",");
        sb.append("\"message\":");
        sb.append("{\"global\":\"csrftoken校验错误，可能是跨站点攻击\"");
        sb.append("}}");
        return sb.toString();
    }

    private boolean needCSRFCheck(String url) {
        url = url.toLowerCase();
        for (String path : csrfPathExcluded) {
            if (pathMatcher.match(path.toLowerCase(), url)) {
                log.debug("match accessPathExcluded rule {}", path);
                return false;
            }
        }
        return true;
    }
}
