package com.baidu.acg.piat.digitalhuman.console.service.session;

import com.baidu.acg.piat.digitalhuman.common.cloud.CloudSessionResult;
import com.baidu.acg.piat.digitalhuman.common.cloud.SessionAcquireResult;
import com.baidu.acg.piat.digitalhuman.common.model.PageResponse;
import com.baidu.acg.piat.digitalhuman.common.model.PageResult;
import com.baidu.acg.piat.digitalhuman.console.service.cloud.CloudHttpClient;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * Created on 2020/4/17 21:04.
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class SessionServiceImpl implements SessionService {

    private final CloudHttpClient cloudHttpClient;

    @Override
    public PageResponse<CloudSessionResult> list(String appId, String sessionId, int pageNo, int pageSize) {
        PageResponse<SessionAcquireResult> pageResponse = cloudHttpClient.list(appId, sessionId, pageNo, pageSize);
        List<CloudSessionResult> cloudSessionResultList = new ArrayList<>();
        pageResponse.getPage().getResult().forEach(r -> cloudSessionResultList.add(
                CloudSessionResult.builder()
                        .sessionId(r.getSessionId())
                        .sessionToken(r.getSessionToken())
                        .status(r.getStatus())
                        .rtcConnection(CloudSessionResult.RtcConnection.convert(r.getRtcConnection()))
                        .character(r.getCharacter())
                        .parameters(r.getParameters())
                        .extra(r.getExtra())
                        .createTime(r.getCreateTime())
                        .updateTime(r.getUpdateTime()).build()));
        return PageResponse.<CloudSessionResult> builder()
                .page(PageResult.<CloudSessionResult> builder()
                        .pageNo(pageNo)
                        .pageSize(pageSize)
                        .totalCount(pageResponse.getPage().getTotalCount())
                        .result(cloudSessionResultList)
                        .build())
                .build();
    }

    @Override
    public CloudSessionResult detail(String appId, String sessionId) {
        SessionAcquireResult result = cloudHttpClient.get(appId, sessionId);
        return CloudSessionResult.builder()
                .sessionId(result.getSessionId())
                .sessionToken(result.getSessionToken())
                .status(result.getStatus())
                .rtcConnection(CloudSessionResult.RtcConnection.convert(result.getRtcConnection()))
                .character(result.getCharacter())
                .parameters(result.getParameters())
                .extra(result.getExtra())
                .createTime(result.getCreateTime())
                .updateTime(result.getUpdateTime()).build();
    }

    @Override
    public void delete(String appId, String sessionId) {
        cloudHttpClient.delete(appId, sessionId);
    }
}
