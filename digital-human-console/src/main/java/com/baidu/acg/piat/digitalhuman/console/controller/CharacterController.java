package com.baidu.acg.piat.digitalhuman.console.controller;

import com.baidu.acg.piat.digitalhuman.common.access.AccessApp;
import com.baidu.acg.piat.digitalhuman.common.model.PageResult;
import com.baidu.acg.piat.digitalhuman.common.model.Response;
import com.baidu.acg.piat.digitalhuman.console.model.character.CharacterImageInfo;
import com.baidu.acg.piat.digitalhuman.console.model.common.PageRequest;
import com.baidu.acg.piat.digitalhuman.console.model.background.PreviewThumbnailRequest;
import com.baidu.acg.piat.digitalhuman.console.service.access.AccessControlService;
import com.baidu.acg.piat.digitalhuman.console.service.character.CharacterService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * @Author: <EMAIL>
 * @Date: 2020/3/23
 */

@Slf4j
@RequestMapping("/api/digitalhuman/console/v2/character/image")
@RestController
@RequiredArgsConstructor
@Api(tags = "模型管理")
public class CharacterController {

    private final AccessControlService accessControlService;

    private final CharacterService characterService;

    @PostMapping("/detail")
    @ApiOperation(value = "展示人像模型")
    public Response<CharacterImageInfo> findCharacterByCharacterImage(@RequestBody CharacterImageInfo request) {
        log.info("CharacterController find character by characterImage={}", request.getCharacterImage());
        return Response.success(characterService.findCharacterByCharacterImage(request.getCharacterImage()));
    }

    @PostMapping("/list")
    public PageResult<CharacterImageInfo> list(@RequestBody PageRequest request) {
        log.info("CharacterController list all character images");
        return characterService.list(request.getPageNo(), request.getPageSize());
    }

    @PostMapping("/update")
    public Response<Void> update(@RequestBody CharacterImageInfo request) {
        log.info("CharacterController update character images");
        characterService.update(request);
        return Response.success(null);
    }

    @PostMapping("/app")
    @ApiOperation(value = "展示App的人像")
    public Response<CharacterImageInfo> findCharacterByAppId(@Valid @RequestBody String appId) {
        log.debug("CharacterController findCharacterByAppId appId:{}", appId);
        AccessApp appDetail = accessControlService.getApp(appId);
        if (appDetail != null && appDetail.getCharacterImage() != null) {
            var response = characterService.findCharacterByCharacterImage(appDetail.getCharacterImage());
            return Response.success(response);
        }
        return Response.success(null);
    }

    @PostMapping("/preview")
    @ApiOperation(value = "生成人像和背景合成的缩略图")
    public Response<byte[]> preview(@Valid @RequestBody PreviewThumbnailRequest request) {
        log.info("CharacterController preview, appId = {}, characterImage = {}, backgroundImageId = {}",
                request.getAppId(), request.getCharacterImage(), request.getBackgroundImageId());

        var result = characterService.preview(request.getCharacterImage(), request.getBackgroundImageId());
        return Response.success(result);
    }

}
