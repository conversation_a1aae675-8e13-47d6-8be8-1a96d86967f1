// Copyright (C) 2019 Baidu Inc. All rights reserved.

package com.baidu.acg.piat.digitalhuman.console.interceptors.iam;

import lombok.Data;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.BeanFactory;
import org.springframework.beans.factory.BeanFactoryAware;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;

/**
 * copy from com.baidu.bce.plat.webframework.iam.config.access.web.BceAccessConfiguration.
 *
 * <AUTHOR>
 */
@Order(Ordered.HIGHEST_PRECEDENCE + 10)
@Configuration
@Data
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class AccessConfiguration implements BeanFactoryAware {

    @Value("${iam.mfa.header:Bce-MFA-Code}")
    private String mfaAuthHeader;

    @Value("${login.cookie.md5.key}")
    private String authCookieSecret;

    @Value("${login.urls.need.auth:/**}")
    private String needLoginPathAdded;

    @Value("${login.urls.not.need.auth:/asset/**;/dep/**;/esl.js;/swagger/**}")
    private String needLoginPathExcluded;

    @Value("${iam.access.exclude.paths:}")
    private String accessPathExcluded;

    @Value("${iam.csrf.paths:/api/**}")
    private String csrfPathAdded;

    @Value("${iam.csrf.exclude.paths:}")
    private String csrfPathExcluded;

    @Value("${login.url}")
    private String loginUrl;

    @Value("${iam.access.failed.jump.url:/iam/access}")
    private String accessFailedJumpUrl;

    @Value("${userlogin.reset.password.url:/iam/#/iam/user/reset}")
    private String resetPasswordUrl;

    @Value("${iam.csrf.is.need.check.valid:true}")
    private boolean needCheckCSRF;

    @Value("${iam.refer.is.need.check.valid:true}")
    private boolean needCheckRefer;

    @Value("${cookie.domain}")
    private String authCookieDomain;

    @Value("${login.collaborator.uri:/collaborator}")
    private String collaboratorLoginPath;

    @Value("${login.userlogin.uri:/login}")
    private String userLoginPath;

    @Value("${login.domain.address.redirect.to.userlogin:false}")
    private boolean domainLoginAddressRedirectUserLogin;

    @Value("${system.config.noPermissionPage:/#/index/overview}")
    private String toRedirect;

    @Value("${iam.qualify.is.need.check.valid:false}")
    private boolean needCheckQualify;

    @Value("${iam.qualify.forbiddenUrls:/api/bos/create**}")
    private String forbiddenUrls;

    @Value("${iam.qualify.homepage:/#/index/overview}")
    private String qualifyHome;

    @Value("${iam.qualify.enabledPaths:/api/**}")
    private String qualifyPathAdded;

    @Value("${iam.access.redirect.script:false}")
    private boolean scriptRedirect;

    @Value("${iam.access.federation.support:false}")
    private boolean federationSupport;

    @Value("${iam.access.deny.url:/#/403}")
    private String accessDenyUrl;

    @Value("${user.authcode.phone.product_line:VerifyUserMobilePhone}")
    private String authCodeProductLine;

    @Value("${login.cookie.expires.time:30}")
    private int cookieExpiresTime;

    @Value("${user.authcode.phone.product_line:VerifyUserMobilePhone}")
    private String productLine;

    @Value("${iam.mfa.need:true}")
    private Boolean needMFA;

    @Value("${endpoint.default.regionName:bj}")
    private String iamRegion;

    @Value("${Access-Control-Allow-Origin:*}")
    private String origin;

    private BeanFactory beanFactory;

    @Override
    public void setBeanFactory(BeanFactory beanFactory) throws BeansException {
        this.beanFactory = beanFactory;
    }
}
