package com.baidu.acg.piat.digitalhuman.console.controller;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.baidu.acg.piat.digitalhuman.common.console.video.ProgressStatus;
import com.baidu.acg.piat.digitalhuman.common.console.video.VideoFailRequest;
import com.baidu.acg.piat.digitalhuman.common.console.video.VideoUpdateRequest;
import com.baidu.acg.piat.digitalhuman.console.model.vis.VisVideoCallbackReq;
import com.baidu.acg.piat.digitalhuman.console.model.vis.VisVideoCallbackRes;
import com.baidu.acg.piat.digitalhuman.console.service.videopipeline.VideoPipelineService;
import com.baidu.acg.piat.digitalhuman.console.util.DownloadFileWithDisabledSSL;
import com.baidu.acg.piat.digitalhuman.console.util.JsonUtil;
import com.baidu.acg.piat.digitalhuman.storage.service.StorageService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RestController
@RequestMapping("/api/digitalhuman/console/video/vis/callback")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class VisCallbackController {

    private final VideoPipelineService videoPipelineService;

    private final StorageService storageService;

    private static final String VIDEO_TEMP_DIR = "/tmp/video/";

    @PostMapping
    public VisVideoCallbackRes callback(@RequestBody VisVideoCallbackReq visVideoCallbackReq)
            throws IOException, StorageService.ResourceException {
        log.info("Received vis callback, video={}", visVideoCallbackReq);

        VisVideoCallbackReq.VisVideoCallbackValue value =
                JsonUtil.read(visVideoCallbackReq.getFeatureResult().getValue(),
                        VisVideoCallbackReq.VisVideoCallbackValue.class);
        log.info("Received vis callback, value={}", value);
        if (value == null) {
            log.error("Received vis callback err, value is empty");
            String taskId = visVideoCallbackReq.getSourceKey();
            VideoFailRequest videoFailRequest = VideoFailRequest.builder()
                    .id(taskId)
                    .canRetry(false)
                    .errorMsg(visVideoCallbackReq.getStatus())
                    .errorCode(visVideoCallbackReq.getCode())
                    .build();
            // 5016的状态码表示队列当前慢，需要后面重新提交任务
            if (visVideoCallbackReq.getCode() == 5016) {
                videoFailRequest.setCanRetry(true);
            }
            log.info("Fail video progress, videoFailRequest={}", videoFailRequest);
            videoPipelineService.failProgress(videoFailRequest);
            return VisVideoCallbackRes.builder().code(0).build();
        }
        String taskId = value.getTaskId();
        if (StringUtils.isEmpty(taskId)) {
            log.error("Received vis callback err, taskId is empty");
            return VisVideoCallbackRes.builder().code(1).build();
        }
        VideoUpdateRequest videoUpdateRequest = new VideoUpdateRequest();
        videoUpdateRequest.setId(taskId);
        if (visVideoCallbackReq.getErrCode() != 0 || value.getErrCode() != 0) {
            videoUpdateRequest.setStatus(ProgressStatus.ERROR);
            videoUpdateRequest.setMessage(value.getErrMsg());
            videoUpdateRequest.setCode(value.getErrCode());
        } else {
            String originVideoUrl = value.getData().getVideoUrl();
            log.info("Try to download url:{}, videoId={}", originVideoUrl, taskId);
            String extent = ".webm";
            if (!originVideoUrl.contains("webm")) {
                extent = ".mp4";
            }
            String downloadFilename = VIDEO_TEMP_DIR + taskId + extent;
            File dirFile = new File(VIDEO_TEMP_DIR);
            if (!dirFile.exists()) {
                dirFile.mkdir();
            }
            File downloadFile = new File(downloadFilename);
            Files.deleteIfExists(downloadFile.toPath());
            try {
                DownloadFileWithDisabledSSL.downloadFile(originVideoUrl, downloadFilename);
                if (downloadFile.exists()) {
                    String uploadUrl = storageService.save(downloadFile.toPath(), taskId + extent).toString();
                    log.info("Upload video to storage, uploadUrl={}", uploadUrl);
                    // 下载成功
                    videoUpdateRequest.setDownloadUrl(uploadUrl);
                    videoUpdateRequest.setStatus(ProgressStatus.SUCCEED);
                    Files.deleteIfExists(downloadFile.toPath());
                }
            } catch (Exception e) {
                log.info("Load err:{}", e);
                videoUpdateRequest.setStatus(ProgressStatus.ERROR);
                videoUpdateRequest.setMessage("上传视频是吧");
                videoUpdateRequest.setCode(1);
            }
        }
        log.info("Update video progress, videoUpdateRequest={}", videoUpdateRequest);
        if (videoUpdateRequest.getStatus().equals(ProgressStatus.ERROR)) {
            videoPipelineService.failProgress(VideoFailRequest.builder()
                    .id(videoUpdateRequest.getId())
                    .canRetry(false)
                    .errorMsg(videoUpdateRequest.getMessage())
                    .errorCode(videoUpdateRequest.getCode())
                    .build());
        } else {
            videoPipelineService.updateProgressResult(videoUpdateRequest);
        }
        return VisVideoCallbackRes.builder().code(0).build();
    }
}