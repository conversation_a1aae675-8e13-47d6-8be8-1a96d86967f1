// Copyright (C) 2019 Baidu Inc. All rights reserved.
package com.baidu.acg.piat.digitalhuman.console.config;

import lombok.Data;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * AccessConfigure
 *
 * <AUTHOR>
 * @since 2019-11-28
 */
@Configuration
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class SessionStatisticExportConfigure {

    @Bean
    @ConfigurationProperties(prefix = "digitalhuman.statistic.config.sessionstatistic.adminsheetheader")
    public SessionStatisticExportConfigure.SheetHeaderConfig adminSheetHeader() {
        return new SessionStatisticExportConfigure.SheetHeaderConfig();
    }

    @Bean
    @ConfigurationProperties(prefix = "digitalhuman.statistic.config.sessionstatistic.tenancysheetheader")
    public SessionStatisticExportConfigure.SheetHeaderConfig tenancySheetHeader() {
        return new SessionStatisticExportConfigure.SheetHeaderConfig();
    }

    @Data
    public static class SheetHeaderConfig {
        private String name;

        private String sessionTotalCountName;

        private String sessionAvgDurationName;

        private String dialogTotalCountName;

        private String sheetName;

    }


}
