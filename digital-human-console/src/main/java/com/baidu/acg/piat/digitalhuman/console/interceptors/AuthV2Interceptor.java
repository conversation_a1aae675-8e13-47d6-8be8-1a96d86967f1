// Copyright (C) 2019 Baidu Inc. All rights reserved.
package com.baidu.acg.piat.digitalhuman.console.interceptors;

import com.baidu.acg.piat.digitalhuman.common.exception.DigitalHumanCommonException;
import com.baidu.acg.piat.digitalhuman.common.utils.AccessSignUtil;
import com.baidu.acg.piat.digitalhuman.console.model.common.AuthorizationInfo;
import com.baidu.acg.piat.digitalhuman.console.service.user.UserThreadLocal;
import com.baidu.acg.piat.digitalhuman.plat.client.PlatformClient;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.util.Objects;

/**
 * AuthInterceptor
 *
 * <AUTHOR> Zhensheng(<EMAIL>)
 * @since 2019-11-28
 */
@Deprecated
@Slf4j
@Component
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class AuthV2Interceptor extends HandlerInterceptorAdapter {

    private final PlatformClient platformClient;

    @Override
    public boolean preHandle(HttpServletRequest request,
                             HttpServletResponse response,
                             Object handler) throws Exception {
        if (!request.getRequestURI().startsWith("/api/digitalhuman/console/v2")) {
            // 对于静态资源请求和非V2开头的请求，不作头部校验
            return true;
        }

        var authorization = request.getHeader("Authorization");
        if (Objects.isNull(authorization)) {
            log.warn("Authorization is empty");
            return false;
        }
        try {
            log.info("authorization value: {}", authorization);
            if (!authorization.startsWith("BDH ")) {
                throw new DigitalHumanCommonException("authorization invalid");
            }
            var authInfo = AuthorizationInfo.extractUser(authorization.substring(4));
            var user = platformClient.getUserByName(URLDecoder.decode(authInfo.getUserName(),
                    StandardCharsets.UTF_8.name()));
            var signature = AccessSignUtil.sign(user.getPassword(), authInfo.getUserName(),
                    authInfo.getExpireTime());
            if (signature.equals(authInfo.getSignature())) {
                authInfo.setUserId(user.getUserId());
                authInfo.setPassword(user.getPassword());
                request.setAttribute("authorization", authInfo);
                UserThreadLocal.setAccessUser(user);
                return true;
            } else {
                throw new DigitalHumanCommonException("signature invalid");
            }
        } catch (DigitalHumanCommonException e) {
            log.warn("Fail to pass auth interceptor, cause:", e);
            response.sendError(400, e.getMessage());
            return false;
        }
    }

    @Override
    public void postHandle(HttpServletRequest request,
                           HttpServletResponse response,
                           Object handler,
                           ModelAndView modelAndView) throws Exception {
    }
}
