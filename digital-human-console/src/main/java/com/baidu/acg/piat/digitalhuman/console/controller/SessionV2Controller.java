package com.baidu.acg.piat.digitalhuman.console.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

import com.baidu.acg.piat.digitalhuman.common.cloud.CloudSessionResult;
import com.baidu.acg.piat.digitalhuman.common.model.PageResponse;
import com.baidu.acg.piat.digitalhuman.common.model.Response;
import com.baidu.acg.piat.digitalhuman.console.model.session.SessionListRequest;
import com.baidu.acg.piat.digitalhuman.console.model.session.SessionRequest;
import com.baidu.acg.piat.digitalhuman.console.service.session.SessionService;

@Slf4j
@RequestMapping(value = {"/api/digitalhuman/console/v2/session"})
@RestController
@RequiredArgsConstructor
@Api(tags = "会话管理")
public class SessionV2Controller {

    private final SessionService sessionService;

    @PostMapping("/list")
    @ApiOperation(value = "会话列表")
    public PageResponse<CloudSessionResult> list(@Valid @RequestBody SessionListRequest request) {
        return sessionService.list(request.getAppId(), request.getSessionId(), request.getPageNo(),
                request.getPageSize());
    }

    @PostMapping("/delete")
    @ApiOperation(value = "会话关闭")
    public Response<Void> delete(@Valid @RequestBody SessionRequest request) {
        log.info("CloudSession close session by appId={}, sessionId={}", request.getAppId(), request.getSessionId());
        sessionService.delete(request.getAppId(), request.getSessionId());
        return Response.success(null);
    }

    @PostMapping("/detail")
    @ApiOperation(value = "查询单个会话")
    public Response<CloudSessionResult> get(@Valid @RequestBody SessionRequest request) {
        log.info("CloudSession get session by appId={}, sessionId={}", request.getAppId(), request.getSessionId());
        return Response.success(sessionService.detail(request.getAppId(), request.getSessionId()));
    }

}
