package com.baidu.acg.piat.digitalhuman.console.controller;

import com.baidu.acg.digitalhuman.device.common.v2.model.DeviceEvent;
import com.baidu.acg.digitalhuman.device.common.v2.model.DeviceType;
import com.baidu.acg.digitalhuman.device.common.v2.model.http.PageResponse;
import com.baidu.acg.piat.digitalhuman.common.model.PageRequest;
import com.baidu.acg.piat.digitalhuman.common.model.Response;
import com.baidu.acg.piat.digitalhuman.console.model.device.DeviceConfigRequest;
import com.baidu.acg.piat.digitalhuman.console.model.device.DeviceConsoleV2;
import com.baidu.acg.piat.digitalhuman.console.model.device.DeviceDeleteRequest;
import com.baidu.acg.piat.digitalhuman.console.model.device.DeviceEventListRequest;
import com.baidu.acg.piat.digitalhuman.console.service.device.DeviceControlService;
import com.baidu.acg.digitalhuman.device.common.v2.model.DeviceV2;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.RequestAttribute;
import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * @Author: <EMAIL>
 * @Date: 2023/8/22
 */
@Slf4j
@RequestMapping(value = {"/api/digitalhuman/console/v2/device/manager"})
@RestController
@RequiredArgsConstructor
@Api(tags = "设备管理")
public class DeviceV2Controller {

    private final DeviceControlService deviceControlService;

    @PostMapping("/device/create")
    @ApiOperation(value = "添加设备")
    public Response<DeviceV2> createDevice(@RequestBody DeviceV2 request,
                                           @RequestAttribute("accountId") String accountId) {
        return Response.success(deviceControlService.createDevice(request, accountId));
    }

    @PostMapping("/device/update")
    @ApiOperation(value = "修改设备")
    public Response<DeviceV2> updateDevice(@RequestBody DeviceV2 request) {
        return Response.success(deviceControlService.updateDevice(request.getDeviceId(), request));
    }

    @PostMapping("/device/list")
    @ApiOperation(value = "查询设备")
    public PageResponse<DeviceConsoleV2> getDeviceList(@Valid @RequestBody PageRequest request,
                                                       @RequestAttribute("accountId") String accountId) {
        return deviceControlService.listDeviceAll(accountId, request.getPageNo(), request.getPageSize());
    }

    @PostMapping("/event/list")
    @ApiOperation(value = "查询设备事件")
    public PageResponse<DeviceEvent> getDeviceEventList(@Valid @RequestBody DeviceEventListRequest request,
                                                        @RequestAttribute("accountId") String accountId) {
        return deviceControlService.listDeviceEventAll(request.getDeviceId(), accountId, request.getPageNo(), request.getPageSize());
    }

    @PostMapping("/device/delete")
    @ApiOperation(value = "删除设备")
    public Response<Void> deleteDevice(@Valid @RequestBody DeviceDeleteRequest request,
                                       @RequestAttribute("accountId") String accountId) {
        deviceControlService.deleteDevice(request.getDeviceId(), accountId);
        return Response.success(null);
    }

    @PostMapping("/device/type/list")
    @ApiOperation(value = "查询设备类型")
    public Response<List<DeviceType>> getDeviceTypeList(@RequestAttribute("accountId") String accountId) {
        return Response.success(deviceControlService.listDeviceTypes(accountId));
    }

    @PostMapping("/device/config")
    @ApiOperation(value = "查询设备类型")
    public Response<Map<String, Object>> getDeviceConfig(@Valid @RequestBody DeviceConfigRequest request,
                                                         @RequestAttribute("accountId") String accountId) {
        return Response.success(deviceControlService.getConfigOfDevice(request.getDeviceId(), accountId));
    }

}