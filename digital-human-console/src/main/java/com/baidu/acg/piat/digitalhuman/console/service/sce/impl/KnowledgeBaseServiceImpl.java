package com.baidu.acg.piat.digitalhuman.console.service.sce.impl;

import com.baidu.acg.piat.digitalhuman.common.exception.DigitalHumanCommonException;
import com.baidu.acg.piat.digitalhuman.common.llmrole.KnowledgeBase;
import com.baidu.acg.piat.digitalhuman.common.llmrole.KnowledgeFile;
import com.baidu.acg.piat.digitalhuman.common.model.PageResponse;
import com.baidu.acg.piat.digitalhuman.common.model.Response;
import com.baidu.acg.piat.digitalhuman.common.utils.IDGenerator;
import com.baidu.acg.piat.digitalhuman.console.service.llmdm.LlmDmHttpClient;
import com.baidu.acg.piat.digitalhuman.console.service.sce.KnowledgeBaseService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.MediaType;
import okhttp3.MultipartBody;
import okhttp3.RequestBody;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import java.io.File;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> Xiaoyu
 * @since 2023/11/28 11:16
 */

@Slf4j
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class KnowledgeBaseServiceImpl implements KnowledgeBaseService {

    private final LlmDmHttpClient llmDmHttpClient;

    private final IDGenerator idGenerator = new IDGenerator(new IDGenerator.IDGeneratorConfig());

    @Override
    public Response<KnowledgeBase> create(KnowledgeBase request) {
        return llmDmHttpClient.createKnowledgeBase(request);
    }

    @Override
    public PageResponse<KnowledgeBase> list(String accountId, int type, String name, int pageNo, int pageSize) {
        PageResponse<KnowledgeBase> result = llmDmHttpClient.listKnowledgeBase(accountId, type, name, pageNo, pageSize);
        return result;
    }

    @Override
    public Response<Void> delete(String accountId, List<String> ids) {
        return llmDmHttpClient.batchDeleteKnowledgeBase(accountId, ids);
    }

    @Override
    public Response<KnowledgeBase> detail(String accountId, String llmRoleId) {
        return llmDmHttpClient.detailKnowledgeBase(accountId, llmRoleId);
    }

    @Override
    public Response<Map<String, String>> uploadFile(MultipartFile textFile, String fileName) {
        String filePath = "/tmp/llm-upload-files/" + idGenerator.generate(9)+ fileName;
        File convertedFile = new File(filePath);
        FileOutputStream fos = null;
        try {
            Files.createDirectories(Paths.get("/tmp/llm-upload-files"));
            fos = new FileOutputStream(convertedFile);
            fos.write(textFile.getBytes());
            fos.close();
        } catch (FileNotFoundException e) {
            throw new DigitalHumanCommonException("转换MultipartFile文件失败", e);
        } catch (IOException e) {
            throw new DigitalHumanCommonException("转换MultipartFile文件失败", e);
        }

        RequestBody descriptionPart = RequestBody.create(MultipartBody.FORM, "file description");
        RequestBody fileNamePart = RequestBody.create(MultipartBody.FORM, fileName);
        RequestBody filePart = RequestBody.create(MediaType.parse("multipart/form-data"), convertedFile);
        MultipartBody.Part fileBody = MultipartBody.Part.createFormData("file", fileName, filePart);
        Response<Map<String, String>> response = llmDmHttpClient.uploadFile(descriptionPart, fileBody, fileNamePart);

        return response;

    }

    @Override
    public Response<KnowledgeFile> importFile(String accountId, String datasetId, List<KnowledgeFile> files) {
        return llmDmHttpClient.batchImportKnowledgeFile(accountId, datasetId, files);
    }

    @Override
    public Response<Void> deleteFile(String accountId, String datasetId, List<String> fileIds) {
        return llmDmHttpClient.batchDeleteKnowledgeFile(accountId, datasetId, fileIds);
    }

    @Override
    public PageResponse<KnowledgeFile> listFile(String accountId, String datasetId, String name, int pageNo, int pageSize) {
        return llmDmHttpClient.listKnowledgeFile(accountId, datasetId, name, pageNo, pageSize);
    }
}
