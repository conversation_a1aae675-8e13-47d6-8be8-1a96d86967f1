// Copyright (C) 2019 Baidu Inc. All rights reserved.

package com.baidu.acg.piat.digitalhuman.console.interceptors.iam;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * copy from com.baidu.bce.plat.webframework.authentication.AuthenticationFilterConfig.
 *
 * <AUTHOR>
 */
@Component
@Data
public class AuthenticationConfig {

    @Value("${Access-Control-Allow-Origin:*}")
    private String origin;
    @Value("${Access-Control-Allow-Methods:GET,POST,HEAD,OPTIONS}")
    private String methods;
    @Value("${Access-Control-Allow-Headers:referer,content-type}")
    private String headers;
    @Value("${Access-Control-Allow-Credentials:true}")
    private String credentials;

    /**
     * login page, redirect url
     */
    @Value("${login.url}")
    private String loginUrl;

    /**
     * url that need login "/api/" means exact "/api/" "/api/**" means startWith
     * "/api/"
     */
    @Value("#{'${login.urls.not.need.auth:/asset/**;/dep/**;/esl.js;/swagger/**}'.split(';')}")
    private List<String> notNeedLoginUrlList = new ArrayList<String>();

    /**
     * url that need login "/api/" means exact "/api/" "/api/**" means startWith
     * "/api/"
     */
    @Value("#{'${login.urls.need.auth:/**}'.split(';')}")
    private List<String> needLoginUrlList = new ArrayList<String>();

    @Value("${login.collaborator.uri:/collaborator}")
    private String collaboratorLoginPath;

    @Value("${login.userlogin.uri:/login}")
    private String userLoginPath;

    @Value("${login.domain.address.redirect.to.userlogin:false}")
    private boolean domainLoginAddressRedirectUserLogin;

    /**
     * uc distribute app id ,it is 285 more info to see iuc.baidu.com
     */
    @Value("${uc.app.id}")
    private String ucAppId;

    /**
     * uc authentication server host
     */
    @Value("${uc.server}")
    private String ucServer;

    /**
     * used to uc authentication fill cookie domain when authenticate path
     * variable named "castk" successfully
     */
    @Value("${cookie.domain:.baidu.com}")
    private String ucAuthCookieDomain;

    /**
     * passport distribute app id
     */
    @Value("${passport.appid}")
    private String passportAppId;

    /**
     * used to passport authentication it's server host for interface
     * getSesssionData.
     */
    @Value("${passport.session.endpoint:http://localhost:8088/passport}")
    private String passportEndpoint;

    @Value("${passport.passgateEndpoint}")
    private String passportPassgateEndpoint;

    @Value("${passport.app.username}")
    private String passportUsername;

    @Value("${passport.app.password}")
    private String passportPassword;

    /**
     * used to uc authentication
     */
    @Value("${uc.server.jump.url:https://mycas.baidu.com:8443/?action=check&appid=}")
    private String ucServerJumpUrl;

    @Value("${iam.console.username}")
    private String iamConsoleName;

    @Value("${iam.console.password}")
    private String iamConsolePassword;

    /**
     * second
     */
    @Value("${login.cookie.effective.create.time:-1}")
    private int cookieEffectiveCreatedTime;

    /**
     * the secret key for make md5 string
     */
    @Value("${login.cookie.md5.key:19920908}")
    private String cookieMD5Key;

    /**
     * thirty minute
     */
    @Value("${login.cookie.expires.time:30}")
    private int cookieExpiresTime;

    @Value("${login.cookie.is.need.check.valid:true}")
    private boolean isNeedCheckCookieValid;

    @Value("${login.console.token.renew.advance:60}")
    private int consoleTokenRenewAdvanceSeconds;

    @Value("${login.federated.default.role:FederatedAdmin}")
    private String federatedUserDefaultRole;

    @Value("${sce.apply.url:https://digitalhuman.baidu.com/sce/login}")
    private String sceLoginUrl;

    @Value("#{'${sce.urls.not.need.auth:/api/digitalhuman/sce/v1/user/*}'.split(';')}")
    private List<String> notNeedAuthUrlList = new ArrayList<String>();

    private boolean notAutoRedirect;

    private boolean isContinueWhenAuthenticateFailed;

    public boolean isContinueWhenAuthenticateFailed() {
        return isContinueWhenAuthenticateFailed;
    }

    public void setContinueWhenAuthenticateFailed(boolean isContinueWhenAuthenticateFailed) {
        this.isContinueWhenAuthenticateFailed = isContinueWhenAuthenticateFailed;
    }

    @Override
    public String toString() {
        return "AuthenticationConfig{"
                + "loginUrl='" + loginUrl + '\''
                + ", notNeedLoginUrlList=" + notNeedLoginUrlList
                + ", needLoginUrlList=" + needLoginUrlList
                + ", collaboratorLoginPath='" + collaboratorLoginPath + '\''
                + ", ucAppId='" + ucAppId + '\''
                + ", ucServer='" + ucServer + '\''
                + ", ucAuthCookieDomain='" + ucAuthCookieDomain + '\''
                + ", passportAppId='" + passportAppId + '\''
                + ", passportEndpoint='" + passportEndpoint + '\''
                + ", passportPassgateEndpoint='" + passportPassgateEndpoint + '\''
                + ", passportUsername='" + passportUsername + '\''
                + ", passportPassword='" + passportPassword + '\''
                + ", ucServerJumpUrl='" + ucServerJumpUrl + '\''
                + ", iamConsoleName='" + iamConsoleName + '\''
                + ", iamConsolePassword='" + iamConsolePassword + '\''
                + ", cookieEffectiveCreatedTime=" + cookieEffectiveCreatedTime
                + ", cookieMD5Key='" + cookieMD5Key + '\''
                + ", cookieExpiresTime=" + cookieExpiresTime
                + ", isNeedCheckCookieValid=" + isNeedCheckCookieValid
                + ", consoleTokenRenewAdvanceSeconds=" + consoleTokenRenewAdvanceSeconds
                + ", federatedUserDefaultRole='" + federatedUserDefaultRole + '\''
                + ", notAutoRedirect=" + notAutoRedirect
                + ", isContinueWhenAuthenticateFailed=" + isContinueWhenAuthenticateFailed
                + '}';
    }

    public AuthenticationConfig withIsContinueWhenAuthenticateFailed(
            final boolean isContinueWhenAuthenticateFailed) {
        this.isContinueWhenAuthenticateFailed = isContinueWhenAuthenticateFailed;
        return this;
    }

    public AuthenticationConfig withNotAutoRedirect(final boolean notAutoRedirect) {
        this.notAutoRedirect = notAutoRedirect;
        return this;
    }

    public boolean isNotAutoRedirect() {
        return notAutoRedirect;
    }

    public void setNotAutoRedirect(boolean notAutoRedirect) {
        this.notAutoRedirect = notAutoRedirect;
    }

    public AuthenticationConfig withIsNeedCheckCookieValid(final boolean isNeedCheckCookieValid) {
        this.isNeedCheckCookieValid = isNeedCheckCookieValid;
        return this;
    }

    public boolean isNeedCheckCookieValid() {
        return isNeedCheckCookieValid;
    }

    public void setNeedCheckCookieValid(boolean isNeedCheckCookieValid) {
        this.isNeedCheckCookieValid = isNeedCheckCookieValid;
    }

    public AuthenticationConfig withCookieExpiresTime(final int cookieExpiresTime) {
        this.cookieExpiresTime = cookieExpiresTime;
        return this;
    }

    public int getCookieExpiresTime() {
        return cookieExpiresTime;
    }

    public void setCookieExpiresTime(int cookieExpiresTime) {
        this.cookieExpiresTime = cookieExpiresTime;
    }

    public AuthenticationConfig withUcServerJumpUrl(final String ucServerJumpUrl) {
        this.ucServerJumpUrl = ucServerJumpUrl;
        return this;
    }

    public AuthenticationConfig withCookieMD5Key(final String cookieMD5Key) {
        this.cookieMD5Key = cookieMD5Key;
        return this;
    }

    public String getCookieMD5Key() {
        return cookieMD5Key;
    }

    public void setCookieMD5Key(String cookieMD5Key) {
        this.cookieMD5Key = cookieMD5Key;
    }

    public AuthenticationConfig withCookieEffectiveCreatedTime(final int cookieEffectiveCreatedTime) {
        this.cookieEffectiveCreatedTime = cookieEffectiveCreatedTime;
        return this;
    }

    public int getCookieEffectiveCreatedTime() {
        return cookieEffectiveCreatedTime;
    }

    public void setCookieEffectiveCreatedTime(int cookieEffectiveCreatedTime) {
        this.cookieEffectiveCreatedTime = cookieEffectiveCreatedTime;
    }

    public String getUcServerJumpUrl() {
        return ucServerJumpUrl;
    }

    public void setUcServerJumpUrl(String ucServerJumpUrl) {
        this.ucServerJumpUrl = ucServerJumpUrl;
    }

    public AuthenticationConfig withUcAuthCookieDomain(final String ucAuthCookieDomain) {
        this.ucAuthCookieDomain = ucAuthCookieDomain;
        return this;
    }

    public AuthenticationConfig withPassportAppId(final String passportAppId) {
        this.passportAppId = passportAppId;
        return this;
    }

    public AuthenticationConfig withPassportEndpoint(final String passportEndpoint) {
        this.passportEndpoint = passportEndpoint;
        return this;
    }

    public AuthenticationConfig withUcAppId(final String ucAppId) {
        this.ucAppId = ucAppId;
        return this;
    }

    public AuthenticationConfig withUcServer(final String ucServer) {
        this.ucServer = ucServer;
        return this;
    }

    public AuthenticationConfig withLoginUrl(final String loginUrl) {
        this.loginUrl = loginUrl;
        return this;
    }

    public AuthenticationConfig addNeedLoginUrl(String needLoginUrl) {
        this.needLoginUrlList.add(needLoginUrl);
        return this;
    }

    public AuthenticationConfig addNotNeedLoginUrl(String... notNeedLoginUrl) {
        for (String url : notNeedLoginUrl) {
            this.notNeedLoginUrlList.add(url);
        }
        return this;
    }

    public String getLoginUrl() {
        return loginUrl;
    }

    public void setLoginUrl(String loginUrl) {
        this.loginUrl = loginUrl;
    }

    public String getUcAuthCookieDomain() {
        return ucAuthCookieDomain;
    }

    public void setUcAuthCookieDomain(String ucAuthCookieDomain) {
        this.ucAuthCookieDomain = ucAuthCookieDomain;
    }

    public String getPassportAppId() {
        return passportAppId;
    }

    public void setPassportAppId(String passportAppId) {
        this.passportAppId = passportAppId;
    }

    public String getPassportEndpoint() {
        return passportEndpoint;
    }

    public void setPassportEndpoint(String passportEndpoint) {
        this.passportEndpoint = passportEndpoint;
    }

    public String getUcAppId() {
        return ucAppId;
    }

    public void setUcAppId(String ucAppId) {
        this.ucAppId = ucAppId;
    }

    public String getUcServer() {
        return ucServer;
    }

    public void setUcServer(String ucServer) {
        this.ucServer = ucServer;
    }

    public List<String> getNeedLoginUrlList() {
        return needLoginUrlList;
    }

    public void setNeedLoginUrlList(List<String> needLoginUrlList) {
        this.needLoginUrlList = needLoginUrlList;
    }

    public List<String> getNotNeedLoginUrlList() {
        return notNeedLoginUrlList;
    }

    public void setNotNeedLoginUrlList(List<String> notNeedLoginUrlList) {
        this.notNeedLoginUrlList = notNeedLoginUrlList;
    }

    public String getIamConsolePassword() {
        return iamConsolePassword;
    }

    public void setIamConsolePassword(String iamConsolePassword) {
        this.iamConsolePassword = iamConsolePassword;
    }

    public String getIamConsoleName() {
        return iamConsoleName;
    }

    public void setIamConsoleName(String iamConsoleName) {
        this.iamConsoleName = iamConsoleName;
    }

    public String getCollaboratorLoginPath() {
        return collaboratorLoginPath;
    }

    public void setCollaboratorLoginPath(String collaboratorLoginPath) {
        this.collaboratorLoginPath = collaboratorLoginPath;
    }

    public String getPassportPassgateEndpoint() {
        return passportPassgateEndpoint;
    }

    public void setPassportPassgateEndpoint(String passportPassgateEndpoint) {
        this.passportPassgateEndpoint = passportPassgateEndpoint;
    }

    public String getPassportUsername() {
        return passportUsername;
    }

    public void setPassportUsername(String passportUsername) {
        this.passportUsername = passportUsername;
    }

    public String getPassportPassword() {
        return passportPassword;
    }

    public void setPassportPassword(String passportPassword) {
        this.passportPassword = passportPassword;
    }

    public int getConsoleTokenRenewAdvanceSeconds() {
        return consoleTokenRenewAdvanceSeconds;
    }

    public void setConsoleTokenRenewAdvanceSeconds(int consoleTokenRenewAdvanceSeconds) {
        this.consoleTokenRenewAdvanceSeconds = consoleTokenRenewAdvanceSeconds;
    }

    public String getFederatedUserDefaultRole() {
        return federatedUserDefaultRole;
    }

    public void setFederatedUserDefaultRole(String federatedUserDefaultRole) {
        this.federatedUserDefaultRole = federatedUserDefaultRole;
    }
}
