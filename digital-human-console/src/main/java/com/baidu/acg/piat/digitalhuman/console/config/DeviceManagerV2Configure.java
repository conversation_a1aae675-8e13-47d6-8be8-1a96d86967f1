package com.baidu.acg.piat.digitalhuman.console.config;

import com.baidu.acg.piat.device.manager2.client.DeviceManagerV2Client;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * @Author: <EMAIL>
 * @Date: 2023/8/22
 */
@Configuration
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class DeviceManagerV2Configure {

    @Bean
    @ConfigurationProperties(prefix = "digitalhuman.devicemanager.config")
    public DeviceManagerV2Configure.Config deviceManagerConfig() {
        return new DeviceManagerV2Configure.Config();
    }

    @Bean
    public DeviceManagerV2Client deviceManagerV2Client() {
        var config = deviceManagerConfig();
        return new DeviceManagerV2Client(config.getBaseUrl());
    }

    @Data
    public static class Config {

        private String baseUrl;

    }
}