package com.baidu.acg.piat.digitalhuman.console.service.upload;

import com.baidu.acg.piat.digitalhuman.console.model.upload.FetchBosUrlRequest;
import com.baidu.acg.piat.digitalhuman.console.model.upload.FetchBosUrlResponse;
import com.baidu.acg.piat.digitalhuman.console.model.upload.UploadAuthRequest;
import com.baidu.acg.piat.digitalhuman.console.model.upload.UploadAuthResponse;
import com.baidu.acg.piat.digitalhuman.console.model.upload.UploadConfig;

/**
 * Created on 2021/7/9 下午5:43.
 *
 * <AUTHOR>
 */
public interface UploadService {

    UploadConfig config();

    UploadAuthResponse auth(UploadAuthRequest uploadAuthRequest);

    FetchBosUrlResponse genUrl(FetchBosUrlRequest request);
}
