package com.baidu.acg.piat.digitalhuman.console.controller.sce;

import com.baidu.acg.piat.digitalhuman.common.llmrole.FileImportRequest;
import com.baidu.acg.piat.digitalhuman.common.llmrole.KnowledgeBase;
import com.baidu.acg.piat.digitalhuman.common.llmrole.KnowledgeBatchRequest;
import com.baidu.acg.piat.digitalhuman.common.llmrole.KnowledgeFile;
import com.baidu.acg.piat.digitalhuman.common.llmrole.RoleDeleteRequest;
import com.baidu.acg.piat.digitalhuman.common.model.PageResponse;
import com.baidu.acg.piat.digitalhuman.common.model.Response;
import com.baidu.acg.piat.digitalhuman.console.service.sce.KnowledgeBaseService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestAttribute;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.util.ArrayList;
import java.util.Map;


/**
 * <AUTHOR> Xiaoyu
 * @since 2023/11/28 10:18
 */
@Slf4j
@RequestMapping({"/api/digitalhuman/sce/v1/llm/knowledge"})
@RestController
@RequiredArgsConstructor
public class KnowledgeBaseController {

    private final KnowledgeBaseService knowledgeBaseService;

    @PostMapping("/create")
    public Response<KnowledgeBase> create(@RequestBody KnowledgeBase request,
                                          @RequestAttribute("accountId") String accountId,
                                          @RequestAttribute("username") String username) {
        log.debug("start to create knowledgeBase:{}", request);
        request.setType(2);
        request.setAccountId(accountId);
        request.setEditor(username);
        return knowledgeBaseService.create(request);
    }

    @PostMapping("/delete/batch")
    public Response<Void> batchDelete(@RequestBody RoleDeleteRequest request,
                                      @RequestAttribute("accountId") String accountId) {

        log.debug("start to dele knowledgeBase, accountId is {}, ids is {}", accountId, request);
        return knowledgeBaseService.delete(accountId, request.getIds());
    }


    @GetMapping("/list")
    public PageResponse<KnowledgeBase> list(@RequestAttribute("accountId") String accountId,
                                            @RequestParam(required = false, defaultValue = "2") int type,
                                            @RequestParam(required = false, defaultValue = "") String name,
                                            @RequestParam int pageNo,
                                            @RequestParam int pageSize) {
        log.debug("start to list knowledgeBase name is {}, accountId is {}, type is {}", name, accountId, type);
        if (StringUtils.isEmpty(accountId)) {
            return PageResponse.success(pageNo, pageSize, 0, new ArrayList<>());
        }
        if (type != 2) {
            return PageResponse.fail("暂不支持该类型查询");
        }
        var knowledgeBases = knowledgeBaseService.list(accountId, type, name, pageNo, pageSize);
        return knowledgeBases;
    }

    @GetMapping("/detail")
    public Response<KnowledgeBase> detail(@RequestAttribute("accountId") String accountId, @RequestParam String knowledgeBaseId) {

        log.debug("start to detail knowledgeBase, accountId is {}, knowledgeBaseId is {}", accountId, knowledgeBaseId);
        var knowledgeBase = knowledgeBaseService.detail(accountId, knowledgeBaseId);
        return knowledgeBase;
    }


    @PostMapping("/file/upload")
    public Response<Map<String, String>> uploadFile(@RequestParam("file") MultipartFile file,
                                          @RequestParam("fileName") String fileName) {
        log.debug("start to upload file,  files is {}, file name is {}", file.getName(), fileName);
        return knowledgeBaseService.uploadFile(file, fileName);
    }

    @PostMapping("/file/import")
    public Response<KnowledgeFile> importFile(@RequestBody FileImportRequest request,
                                              @RequestAttribute("accountId") String accountId,
                                              @RequestAttribute("username") String username
                                          ) {
        log.debug("start to import file, accountId is {}, file import request is {}", accountId, request);
        return knowledgeBaseService.importFile(accountId, request.getKnowledgeBaseId(), request.getFiles());
    }

    @PostMapping("/file/delete")
    public Response<Void> batchDeleteFile(@RequestBody KnowledgeBatchRequest request,
                                              @RequestAttribute("accountId") String accountId,
                                              @RequestAttribute("username") String username) {
        log.debug("start to delete file, accountId is {}, request is {}", accountId, request);
        return knowledgeBaseService.deleteFile(accountId, request.getKnowledgeBaseId(), request.getFileIds());
    }

    @GetMapping("/file/list")
    public PageResponse<KnowledgeFile> listFiles(@RequestAttribute("accountId") String accountId,
                                            @RequestParam String knowledgeBaseId,
                                            @RequestParam(required = false, defaultValue = "") String name,
                                            @RequestParam int pageNo,
                                            @RequestParam int pageSize) {
        log.debug("start to list file, accountId is {}, knowledgeBaseId is {}, name is {}", accountId, knowledgeBaseId, name);
        var knowledgeFiles = knowledgeBaseService.listFile(accountId, knowledgeBaseId, name, pageNo, pageSize);
        return knowledgeFiles;
    }

}
