package com.baidu.acg.piat.digitalhuman.console.controller.sce;

import com.baidu.acg.piat.digitalhuman.common.common.AddType;
import com.baidu.acg.piat.digitalhuman.common.project.Project;
import com.baidu.acg.piat.digitalhuman.common.project.ProjectApplyRequest;
import com.baidu.acg.piat.digitalhuman.common.project.ProjectCopy;
import com.baidu.acg.piat.digitalhuman.common.project.ProjectId;
import com.baidu.acg.piat.digitalhuman.common.project.ProjectListRequest;
import com.baidu.acg.piat.digitalhuman.common.project.ProjectName;
import com.baidu.acg.piat.digitalhuman.common.project.ProjectDelByNameReq;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestAttribute;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

import com.baidu.acg.piat.digitalhuman.common.model.PageResponse;
import com.baidu.acg.piat.digitalhuman.common.model.Response;
import com.baidu.acg.piat.digitalhuman.common.richconfig.RichtextConfig;
import com.baidu.acg.piat.digitalhuman.common.richconfig.RichtextListRequest;
import com.baidu.acg.piat.digitalhuman.console.service.sce.ProjectService;
import com.baidu.acg.piat.digitalhuman.console.service.sce.RichtextConfigService;

/**
 * Created on 2020/4/21 18:31.
 *
 * <AUTHOR>
 */
@Slf4j
@RequestMapping({"/api/digitalhuman/sce/v2/project", "/api/digitalhuman/console/v2/project"})
@RestController
@RequiredArgsConstructor
public class SceProjectV2Controller {

    private static final int DEFAULT_V2_API_VERSION = 2;

    private final ProjectService projectService;

    private final RichtextConfigService richtextConfigService;

    @PostMapping("/create")
    public Response<Project> create(@RequestBody Project request,
                                    @RequestAttribute("accountId") String accountId,
                                    @RequestAttribute("username") String username,
                                    @RequestAttribute("uid") String uid) {
        request.setUserId(accountId);
        request.setEditor(username);
        request.setApiVersion(DEFAULT_V2_API_VERSION);
        request.setUid(uid);
        request.setAddType(AddType.CREATE.name());
        var project = projectService.create(request);
        project.setUserName(username);
        return Response.success(project);
    }

    @PostMapping("/copy")
    public Response<Project> copy(@RequestBody ProjectCopy request,
                                  @RequestAttribute("accountId") String accountId,
                                  @RequestAttribute("username") String username,
                                  @RequestAttribute("uid") String uid) {
        // 获取最新版project
        var project = projectService.getByUserIdNameAndVersion(accountId, request.getName(), null,
                DEFAULT_V2_API_VERSION);
        project.setName(request.getRename());
        project.setEditor(username);
        project.setApiVersion(DEFAULT_V2_API_VERSION);
        project.setUid(uid);
        project.setAddType(AddType.COPY.name());
        project = projectService.create(project);
        project.setUserName(username);
        return Response.success(project);
    }

    @PostMapping("/delete")
    public Response<Void> delete(@RequestBody ProjectName request,
                                 @RequestAttribute("accountId") String accountId,
                                 @RequestAttribute("username") String username,
                                 @RequestAttribute("uid") String uid) {

        if (CollectionUtils.isNotEmpty(request.getNames())) {
            request.getNames().forEach(name -> {
                ProjectDelByNameReq delByNameReq = ProjectDelByNameReq.builder()
                        .userId(accountId).apiVersion(DEFAULT_V2_API_VERSION).uid(uid)
                        .projectName(name).build();
                projectService.deleteByName(delByNameReq);

                // 删除与项目相关的业务配置
                richtextConfigService.list(RichtextListRequest.builder()
                                .projectName(request.getName())
                                .userId(accountId)
                                .pageNo(1)
                                .pageSize(1000000)
                                .build()).getPage().getResult().stream()
                        .map(RichtextConfig::getConfigId).forEach(richtextConfigService::delete);
            });
            return Response.success(null);
        }
        ProjectDelByNameReq delByNameReq = ProjectDelByNameReq.builder()
                .userId(accountId).apiVersion(DEFAULT_V2_API_VERSION).uid(uid)
                .projectName(request.getName()).build();
        projectService.deleteByName(delByNameReq);

        // 删除与项目相关的业务配置
        richtextConfigService.list(RichtextListRequest.builder()
                .projectName(request.getName())
                .userId(accountId)
                .pageNo(1)
                .pageSize(1000000)
                .build()).getPage().getResult().stream()
                .map(RichtextConfig::getConfigId).forEach(richtextConfigService::delete);
        return Response.success(null);
    }

    @PostMapping("/delete/batch")
    public Response<Void> batchDelete(@RequestBody ProjectName request,
                                      @RequestAttribute("accountId") String accountId,
                                      @RequestAttribute("username") String username,
                                      @RequestAttribute("uid") String uid) {
        request.getNames().forEach(name -> {
            ProjectDelByNameReq delByNameReq = ProjectDelByNameReq.builder()
                    .userId(accountId).apiVersion(DEFAULT_V2_API_VERSION).uid(uid)
                    .projectName(name).build();
            projectService.deleteByName(delByNameReq);

            // 删除与项目相关的业务配置
            richtextConfigService.list(RichtextListRequest.builder()
                            .projectName(name)
                            .userId(accountId)
                            .pageNo(1)
                            .pageSize(1000000)
                            .build()).getPage().getResult().stream()
                    .map(RichtextConfig::getConfigId).forEach(richtextConfigService::delete);
        });
        return Response.success(null);
    }

    @PostMapping("/update")
    public Response<Project> update(@RequestBody Project request,
                                    @RequestAttribute("accountId") String accountId,
                                    @RequestAttribute("username") String username,
                                    @RequestAttribute("uid") String uid) {
        request.setUserId(accountId);
        request.setEditor(username);
        request.setApiVersion(DEFAULT_V2_API_VERSION);
        request.setUid(uid);
        var update = projectService.update(request);
        update.setUserName(username);
        return Response.success(update);
    }

    @PostMapping("/updateByUserIdAndName")
    public Response<Project> updateByUserIdAndName(@RequestBody Project request,
                                                   @RequestAttribute("accountId") String accountId,
                                                   @RequestAttribute("username") String username,
                                                   @RequestAttribute("uid") String uid) {
        request.setUserId(accountId);
        request.setEditor(username);
        request.setApiVersion(DEFAULT_V2_API_VERSION);
        request.setUid(uid);
        var update = projectService.updateByUserIdAndName(request);
        update.setUserName(username);
        return Response.success(update);
    }

    @PostMapping("/detail")
    public Response<Project> detail(@RequestBody ProjectId request,
                                    @RequestAttribute("accountId") String uid,
                                    @RequestAttribute("username") String username) {
        var update = projectService.detail(request.getId());
        update.setUserName(username);
        return Response.success(update);
    }

    @PostMapping("/detailByUserIdAndName")
    public Response<Project> getByUserIdNameAndVersion(@RequestBody Project request,
                                                       @RequestAttribute("accountId") String uid,
                                                       @RequestAttribute("username") String username) {
        request.setUserId(uid);
        request.setApiVersion(DEFAULT_V2_API_VERSION);
        var update = projectService.getByUserIdNameAndVersion(request.getUserId(), request.getName(),
                request.getProjectVersion(), request.getApiVersion());
        update.setUserName(username);
        return Response.success(update);
    }

    @PostMapping("/list")
    public PageResponse<Project> list(@RequestBody ProjectListRequest request,
                                      @RequestAttribute("accountId") String uid,
                                      @RequestAttribute("username") String username,
                                      @RequestAttribute("tags") Map<String, String> tags) {
        request.setUserId(uid);
        String visibleCharacters = tags.get("visibleCharacters");
        var projects = projectService.list(request.getUserId(), request.getName(),
                DEFAULT_V2_API_VERSION, request.getPageNo(), request.getPageSize(), visibleCharacters);
        projects.getPage().getResult().forEach(project -> project.setUserName(username));
        return projects;
    }

    @PostMapping("/apply")
    public Response<Void> apply(@RequestBody ProjectApplyRequest request) {
        projectService.apply(request.getProjectId(), request.getProjectVersion(), request.getAppIds());
        return Response.success(null);
    }
}
