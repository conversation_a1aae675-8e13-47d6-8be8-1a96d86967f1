package com.baidu.acg.piat.digitalhuman.console.controller.sce;

import com.baidu.acg.piat.digitalhuman.common.llmrole.LlmRoleTemplate;
import com.baidu.acg.piat.digitalhuman.common.model.PageResponse;
import com.baidu.acg.piat.digitalhuman.common.model.Response;
import com.baidu.acg.piat.digitalhuman.console.service.sce.LlmTemplateService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Slf4j
@RequestMapping({"/api/digitalhuman/sce/v1/llm/template"})
@RestController
@RequiredArgsConstructor
public class SceLlmTemplateController {

    private final LlmTemplateService llmTemplateService;

    @PostMapping("/create")
    public Response<LlmRoleTemplate> create(@RequestBody LlmRoleTemplate llmRoleTemplate) {
        return llmTemplateService.createLlmTemplate(llmRoleTemplate);
    }

    @PostMapping("/delete/batch")
    public Response<Void> batchDelete(@RequestBody List<String> llmTemplateIds) {
        return llmTemplateService.batchDeleteTemplate(llmTemplateIds);
    }

    @PutMapping("/update")
    public Response<LlmRoleTemplate> update(@RequestBody LlmRoleTemplate llmRoleTemplate) {
        return llmTemplateService.updateLlmTemplate(llmRoleTemplate);
    }

    @GetMapping("/detail")
    public Response<LlmRoleTemplate> detail(@RequestParam String llmRoleTemplateId) {
        return llmTemplateService.detailLlmTemplate(llmRoleTemplateId);
    }

    @GetMapping("/list")
    public PageResponse<LlmRoleTemplate> list(@RequestParam(required = false, defaultValue = "1") int templateType,
                                              @RequestParam(required = false, defaultValue = "") String templateName,
                                              @RequestParam String screenType,
                                              @RequestParam int pageNo,
                                              @RequestParam int pageSize) {
        return llmTemplateService.listByTemplateType(templateType, templateName, screenType, pageNo, pageSize);
    }
}
