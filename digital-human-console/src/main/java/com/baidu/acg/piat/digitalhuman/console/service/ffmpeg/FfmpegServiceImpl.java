package com.baidu.acg.piat.digitalhuman.console.service.ffmpeg;

import com.baidu.acg.piat.digitalhuman.common.exception.DigitalHumanCommonException;
import com.baidu.acg.piat.digitalhuman.common.utils.FFmpegUtil;
import com.baidu.acg.piat.digitalhuman.common.utils.FileUtil;
import com.baidu.acg.piat.digitalhuman.common.utils.PathUtil;
import com.baidu.acg.piat.digitalhuman.console.model.upload.ConvertEnum;
import io.vavr.control.Try;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.nio.file.Path;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;

@Slf4j
@Service
public class FfmpegServiceImpl implements FfmpegService {

    private static final String RESULT_AUDIO_PATH = "/tmp/material/audio/result/";
    private static final String TARGET_AUDIO_PATH = "/tmp/material/audio/target/";
    private static final int CONVERT_AUDIO_TIMEOUT = 300;

    @Override
    public byte[] convertAudio(MultipartFile multipartFile, ConvertEnum convertEnum) {
        String time = ZonedDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME);
        String fileName = FileUtil.getFileName(multipartFile);
        String fileFormat = FileUtil.getFileFormat(multipartFile);
        String targetFilePath = TARGET_AUDIO_PATH + fileName.hashCode() + time + fileFormat;
        String resultFilePath = RESULT_AUDIO_PATH + fileName.hashCode() + time + FileUtil.WAV_FORMAT;
        String targetFile = FileUtil.saveMultipartFile(multipartFile, targetFilePath);
        if (StringUtils.isBlank(targetFile)) {
            throw new DigitalHumanCommonException("saveMultipartFile failed");
        }
        byte[] byteData = null;
        try {
            if (ConvertEnum.MP3_TO_WAV_TTS16K.equals(convertEnum)) {
                FFmpegUtil.mp3ToWavTts16k(targetFile, resultFilePath, CONVERT_AUDIO_TIMEOUT);
            }
            byteData = FileUtil.fileToByteData(new File(resultFilePath));
        } catch (Exception e) {
            log.error("Fail to convertAudio convertEnum={} e={}", convertEnum, e);
        } finally {
            Try.run(() -> {
                try {
                    PathUtil.delete(Path.of(targetFilePath));
                } catch (Exception exception) {
                    log.error("Fail to delete audio: {}", targetFilePath, exception);
                }
                try {
                    PathUtil.delete(Path.of(resultFilePath));
                } catch (Exception exception) {
                    log.error("Fail to delete audio: {}", resultFilePath, exception);
                }

            });
        }
        return byteData;
    }

}
