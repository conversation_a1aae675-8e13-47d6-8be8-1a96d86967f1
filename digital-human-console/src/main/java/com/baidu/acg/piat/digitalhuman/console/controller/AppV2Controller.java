package com.baidu.acg.piat.digitalhuman.console.controller;

import com.baidu.acg.piat.digitalhuman.common.access.AccessApp;
import com.baidu.acg.piat.digitalhuman.common.model.PageResponse;
import com.baidu.acg.piat.digitalhuman.common.model.Response;
import com.baidu.acg.piat.digitalhuman.console.model.app.AppListRequest;
import com.baidu.acg.piat.digitalhuman.console.service.TagsHelper;
import com.baidu.acg.piat.digitalhuman.console.service.access.AccessControlService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestAttribute;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import java.util.Map;


@Slf4j
@RequestMapping(value = {"/api/digitalhuman/console/v2/app"})
@RestController
@RequiredArgsConstructor
@Api(tags = "App管理")
public class AppV2Controller {

    private final AccessControlService accessControlService;

    private static final int DEFAULT_V2_API_VERSION = 2;

    @PostMapping("/create")
    @ApiOperation(value = "创建app")
    public Response<AccessApp> createApp(@Valid @RequestBody AccessApp request,
                                         @RequestAttribute("accountId") String accountId,
                                         @RequestAttribute("username") String username,
                                         @RequestAttribute("uid") String uid) {
        log.info("AppController create app, userId={}, name={}", accountId, request.getName());
        request.setUserId(accountId);
        request.setEditor(username);
        request.setApiVersion(DEFAULT_V2_API_VERSION);
        request.setUid(uid);
        return Response.success(accessControlService.createApp(request));
    }

    @PostMapping("/delete")
    @ApiOperation(value = "删除app")
    public Response<Void> deleteApp(@Valid @RequestBody AccessApp request,
                                    @RequestAttribute("accountId") String accountId,
                                    @RequestAttribute("uid") String uid) {
        log.debug("AppController delete app, accountId:{}, appId:{}", accountId, request.getAppId());
        accessControlService.deleteApp(uid, 2, request.getAppId());
        return Response.success(null);
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新app")
    public Response<AccessApp> updateApp(@Valid @RequestBody AccessApp request,
                                         @RequestAttribute("accountId") String accountId,
                                         @RequestAttribute("username") String username,
                                         @RequestAttribute("uid") String uid) {
        log.debug("AppController update app, userId:{}, appId:{}", accountId, request.getAppId());
        request.setUserId(accountId);
        request.setEditor(username);
        request.setApiVersion(DEFAULT_V2_API_VERSION);
        request.setUid(uid);
        return Response.success(accessControlService.updateApp(request));
    }

    @PostMapping("/list")
    @ApiOperation(value = "app列表")
    public PageResponse<AccessApp> listAll(@Valid @RequestBody AppListRequest request,
                                           @RequestAttribute("accountId") String uid) {
        log.debug("AppController list all app pageNo:{}, pageSize:{}", request.getPageNo(), request.getPageSize());
        return accessControlService.listAll(request.getAppId(), uid, request.getName(),
                TagsHelper.get("visibleCharacters"), request.getProjectIds(),
                DEFAULT_V2_API_VERSION, request.getPageNo(),
                request.getPageSize());
    }

    @PostMapping("/detail")
    @ApiOperation(value = "app明细")
    public Response<AccessApp> getApp(@NotEmpty @RequestBody AccessApp request) {
        log.debug("AppController get app, appId:{}", request.getAppId());
        return Response.success(accessControlService.getApp(request.getAppId()));
    }

}
