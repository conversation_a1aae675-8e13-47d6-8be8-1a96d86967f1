package com.baidu.acg.piat.digitalhuman.console.service.cloud;

import com.baidu.acg.piat.digitalhuman.common.cloud.SessionAcquireResult;
import com.baidu.acg.piat.digitalhuman.common.model.PageResponse;
import retrofit2.Call;
import retrofit2.http.DELETE;
import retrofit2.http.GET;
import retrofit2.http.Path;
import retrofit2.http.Query;

/**
 * @Author: <EMAIL>
 * @Date: 2020/3/19
 */
public interface CloudHttpService {

    @GET("/api/digitalhuman/v1/app/{app}/session")
    Call<PageResponse<SessionAcquireResult>> findPageByAppIdAndSessionId(@Path("app") String app,
                                                                         @Query("sessionId") String sessionId,
                                                                         @Query("pageNo") int pageNo,
                                                                         @Query("pageSize") int pageSize);

    @GET("/api/digitalhuman/v1/app/{app}/session/{sessionId}")
    Call<SessionAcquireResult> findBySessionIdAndAppId(@Path("app") String app,
                                                       @Path("sessionId") String sessionId);

    @DELETE("/api/digitalhuman/v1/app/{app}/session/{sessionId}")
    Call<Void> deleteBySessionIdAndAppId(@Path("app") String app,
                                         @Path("sessionId") String sessionId);

    @GET("/api/digitalhuman/v1/app/{app}/session/limit/available")
    Call<Integer> queryAvailableNum(@Path("app") String app);

}
