package com.baidu.acg.piat.digitalhuman.console.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import com.baidu.acg.piat.digitalhuman.resource.pool.client.ResourcePoolClient;
import com.baidu.acg.piat.digitalhuman.resource.pool.client.ResourcePoolClientConfig;
import com.baidu.acg.piat.digitalhuman.resource.pool.client.ResourcePoolClientFactory;

@Configuration
public class ResourcePoolConfigure {

    @Bean
    @ConfigurationProperties(prefix = "digitalhuman.resource-pool.client")
    public ResourcePoolClientConfig resourcePoolClientConfig() {
        return new ResourcePoolClientConfig();
    }

    @Bean
    public ResourcePoolClient resourcePoolClient() {
        return ResourcePoolClientFactory.create(resourcePoolClientConfig());
    }
}
