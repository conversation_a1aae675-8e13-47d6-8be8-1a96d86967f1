package com.baidu.acg.piat.digitalhuman.console.controller;

import com.baidu.acg.piat.digitalhuman.common.model.PageResponse;
import com.baidu.acg.piat.digitalhuman.common.model.Response;
import com.baidu.acg.piat.digitalhuman.common.room.Room;
import com.baidu.acg.piat.digitalhuman.common.room.session.RoomSession;
import com.baidu.acg.piat.digitalhuman.common.session.Dialog;
import com.baidu.acg.piat.digitalhuman.console.model.room.RoomAvailableRequest;
import com.baidu.acg.piat.digitalhuman.console.model.room.RoomDetailRequest;
import com.baidu.acg.piat.digitalhuman.console.model.room.RoomListRequest;
import com.baidu.acg.piat.digitalhuman.console.model.room.RoomSessionDetailRequest;
import com.baidu.acg.piat.digitalhuman.console.model.room.RoomSessionDialogRequest;
import com.baidu.acg.piat.digitalhuman.console.model.room.RoomSessionListRequest;
import com.baidu.acg.piat.digitalhuman.console.service.room.RoomService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestAttribute;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * Created on 2020/7/21 14:22.
 *
 * <AUTHOR>
 */
@Slf4j
@RequestMapping(value = "/api/digitalhuman/console/v2/room")
@RestController
@RequiredArgsConstructor
public class RoomController {

    private final RoomService roomService;

    @PostMapping("/list")
    public PageResponse<Room> list(@Valid @RequestBody RoomListRequest request,
                                   @RequestAttribute("accountId") String uid) {
        request.setUserId(uid);
        return roomService.list(request.getPageNo(), request.getPageSize(), request.getUserId(),
                request.getAppId(), request.getRoomName());
    }

    @PostMapping("/detail")
    public Response<Room> detail(@Valid @RequestBody RoomDetailRequest request) {
        return Response.success(roomService.detail(request.getAppId(), request.getRoomId()));
    }

    @PostMapping("/update")
    public Response<Room> update(@Valid @RequestBody RoomDetailRequest request) {
        return Response.success(roomService.update(request));
    }

    @PostMapping("session/list")
    public PageResponse<RoomSession> listSessions(@Valid @RequestBody RoomSessionListRequest request) {
        return roomService.listSessions(request.getPageNo(), request.getPageSize(), request.getRoomId());
    }

    @PostMapping("session/detail")
    public Response<RoomSession> sessionDetail(@Valid @RequestBody RoomSessionDetailRequest request) {
        return Response.success(roomService.getSession(request.getSessionId()));
    }

    @PostMapping("session/available")
    public Response<Integer> queryAvailableNum(@Valid @RequestBody RoomAvailableRequest request) {
        return Response.success(roomService.getAvailableNum(request.getAppId()));
    }

    @PostMapping("dialog/list")
    public PageResponse<Dialog> listDialogsByRoomId(@Valid @RequestBody RoomSessionDialogRequest request) {
        if (StringUtils.isNotEmpty(request.getSessionId())) {
            return roomService.listDialogsBySessionId(request.getPageNo(), request.getPageSize(),
                    request.getSessionId());
        }
        return roomService.listDialogsByRoomId(request.getPageNo(), request.getPageSize(), request.getRoomId());
    }

}
