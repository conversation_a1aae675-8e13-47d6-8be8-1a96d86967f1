// Copyright (C) 2021 Baidu Inc. All rights reserved.

package com.baidu.acg.piat.digitalhuman.console.config;

import com.baidu.acg.piat.digitalhuman.common.helper.VideoErrorShowMsgHelper;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Lazy;

@Configuration
public class VideoErrorShowMsgConfigure {

    @Value("${digitalhuman.console.app.home-path:/home/<USER>/digital-human-console}")
    private String appHomePath;

    @Bean
    @Lazy
    public VideoErrorShowMsgHelper.ErrorMsgConf videoErrorMsgConf() {
        return VideoErrorShowMsgHelper.getSingleton(appHomePath).getErrorMsgConf();
    }

}
