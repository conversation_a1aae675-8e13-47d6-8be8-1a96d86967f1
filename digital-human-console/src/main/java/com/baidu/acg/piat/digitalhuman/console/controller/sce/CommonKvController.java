package com.baidu.acg.piat.digitalhuman.console.controller.sce;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestAttribute;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

import com.baidu.acg.piat.digitalhuman.common.commonkv.CommonKv;
import com.baidu.acg.piat.digitalhuman.common.commonkv.CommonKvQueryVO;
import com.baidu.acg.piat.digitalhuman.common.model.PageResponse;
import com.baidu.acg.piat.digitalhuman.common.model.Response;
import com.baidu.acg.piat.digitalhuman.plat.client.PlatformClient;

/**
 * 通用 kv 对，for FE
 *
 * <AUTHOR>
 * @since 2021/08/05
 */
@Slf4j
@RequestMapping("/api/digitalhuman/sce/v1/kv")
@RestController
@RequiredArgsConstructor
public class CommonKvController {
    private final PlatformClient platformClient;

    @PostMapping("create")
    public Response<CommonKv> saveOrUpdate(@Valid @RequestBody CommonKv kv,
                                           @RequestAttribute("accountId") String uid) {
        kv.setUserId(uid);
        return Response.success(platformClient.saveOrUpdateCommonKv(kv));
    }

    @PostMapping("delete")
    public Response<Void> delete(@Valid @RequestBody CommonKv kv) {
        platformClient.deleteCommonKv(kv);
        return Response.success();
    }

    @PostMapping("query")
    public PageResponse<CommonKv> query(@Valid @RequestBody CommonKvQueryVO queryVO,
                                        @RequestAttribute("accountId") String uid) {
        queryVO.setUserId(uid);
        return PageResponse.success(platformClient.queryCommonKv(queryVO));
    }
}
