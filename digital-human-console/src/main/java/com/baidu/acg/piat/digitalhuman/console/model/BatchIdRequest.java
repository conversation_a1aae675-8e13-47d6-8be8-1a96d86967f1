// Copyright (C) 2021 Baidu Inc. All rights reserved.

package com.baidu.acg.piat.digitalhuman.console.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

import javax.validation.constraints.NotEmpty;

/**
 * BatchIdRequest
 *
 * <AUTHOR>
 * @date 2021-01-12
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BatchIdRequest {

    @NotEmpty(message = "ids cannot be empty")
    private List<String> ids;
}
