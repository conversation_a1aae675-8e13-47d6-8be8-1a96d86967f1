package com.baidu.acg.piat.digitalhuman.console.interceptors;

import lombok.extern.slf4j.Slf4j;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.mvc.method.annotation.ResponseEntityExceptionHandler;

import com.baidu.acg.piat.digitalhuman.common.exception.DigitalHumanCommonException;
import com.baidu.acg.piat.digitalhuman.common.exception.Error;
import com.baidu.acg.piat.digitalhuman.common.model.Response;

import java.sql.SQLException;

/**
 * 捕获controller异常
 * <AUTHOR>
 */
@Slf4j
@ResponseBody
@ControllerAdvice
public class ExceptionHandler  extends ResponseEntityExceptionHandler {

    @org.springframework.web.bind.annotation.ExceptionHandler(value = Exception.class)
    public Response handle(Exception e) {

        if (e instanceof DigitalHumanCommonException
                || e instanceof IllegalArgumentException
                || e instanceof MethodArgumentNotValidException
                || e instanceof MissingServletRequestParameterException
                || e instanceof HttpMessageNotReadableException
                || e instanceof HttpRequestMethodNotSupportedException) {
            log.warn("Caught the expected exception: ", e);
            return Response.fail(e.getMessage());
        }

        // 捕获sql相关异常
        if (e.getCause() != null && e.getCause().getCause() != null &&
                e.getCause().getCause() instanceof SQLException) {
            log.error("sql exception: ", e.getCause().getCause());
            return Response.fail(String.format("sql exception, root cause=%s", e.getCause().getCause().getMessage()));
        }

        log.error("Caught exception: ", e);
        return Response.fail(Error.INTERNAL_SERVER_ERROR.getMessage());
    }

}
