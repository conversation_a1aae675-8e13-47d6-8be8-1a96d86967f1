// Copyright (C) 2020 Baidu Inc. All rights reserved.
package com.baidu.acg.piat.digitalhuman.console.config;

import java.util.ArrayList;
import java.util.List;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import com.baidu.acg.piat.digitalhuman.common.cache.RedisConfig;
import com.baidu.acg.piat.digitalhuman.tts.cache.TtsCache;
import com.baidu.acg.piat.digitalhuman.tts.cache.impl.TtsCacheMemoryImpl;
import com.baidu.acg.piat.digitalhuman.tts.cache.impl.TtsCacheRedisImpl;
import com.baidu.acg.piat.digitalhuman.tts.model.TtsQueryParams;

/**
 * Audio2VideoConfigure
 *
 * <AUTHOR>
 * @since 2020-06-03
 */
@Configuration
public class Audio2VideoConfigure {

    @Value("${digitalhuman.audio2video.enabled:false}")
    private boolean audio2VideoEnabled;

    @Value("${digitalhuman.audio2video.cacheKey:ttsCache}")
    private String cacheKey;

    @Bean
    @ConfigurationProperties(prefix = "digitalhuman.audio2video.tts-params")
    public List<TtsQueryParams> ttsQueryParams() {
        return new ArrayList<>();
    }

    @Bean
    @ConfigurationProperties(prefix = "digitalhuman.audio2video.tts-cache")
    public RedisConfig redisCacheConfig() {
        return new RedisConfig();
    }

    @Bean
    public TtsCache ttsCache() {
        if (audio2VideoEnabled) {
            return new TtsCacheRedisImpl(cacheKey, redisCacheConfig());
        }
        else {
            return new TtsCacheMemoryImpl(new TtsCacheMemoryImpl.MemoryCacheConfig());
        }
    }

}
