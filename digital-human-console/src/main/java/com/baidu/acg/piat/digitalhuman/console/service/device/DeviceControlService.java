package com.baidu.acg.piat.digitalhuman.console.service.device;


import com.baidu.acg.digitalhuman.device.common.v2.model.DeviceEvent;
import com.baidu.acg.digitalhuman.device.common.v2.model.DeviceType;
import com.baidu.acg.digitalhuman.device.common.v2.model.DeviceV2;
import com.baidu.acg.digitalhuman.device.common.v2.model.http.PageResponse;
import com.baidu.acg.piat.digitalhuman.console.model.device.DeviceConsoleV2;


import java.util.List;
import java.util.Map;

/**
 * @Author: <EMAIL>
 * @Date: 2023/8/22
 */
public interface DeviceControlService {

    /**
     * 添加大屏设备信息
     * @param request
     * @return DeviceV2
     */
    DeviceV2 createDevice(DeviceV2 request, String accountId);

    /**
     * 修改大屏设备信息
     * @param deviceId 设备Id
     * @param request
     * @return DeviceV2
     */
    DeviceV2 updateDevice(String deviceId, DeviceV2 request);

    /**
     * 查询所有大屏设备信息
     * @param accountId 账号Id
     * @param pageNo
     * @param pageSize
     * @return PageResponse<DeviceConsoleV2>
     */
    PageResponse<DeviceConsoleV2> listDeviceAll(String accountId, int pageNo, int pageSize);

    /**
     * 查询所有大屏设备事件
     * @param deviceId 设备Id
     * @param accountId 账号Id
     * @param pageNo
     * @param pageSize
     * @return PageResponse<DeviceEvent>
     */
    PageResponse<DeviceEvent> listDeviceEventAll(String deviceId, String accountId, int pageNo, int pageSize);

    /**
     * 删除大屏设备
     * @param deviceId 设备Id
     * @param accountId 账号Id
     */
    void deleteDevice(String deviceId, String accountId);

    /**
     * 查选设备类型列表
     * @param accountId 账号id
     * @return List<DeviceType>
     */
    List<DeviceType> listDeviceTypes(String accountId);

    /**
     * 查询配置文件
     * @param deviceId
     * @param accountId
     * @return
     */
    Map<String, Object> getConfigOfDevice(String deviceId, String accountId);
}