package com.baidu.acg.piat.digitalhuman.console.controller.sce;

import com.baidu.acg.piat.digitalhuman.common.model.PageResponse;
import com.baidu.acg.piat.digitalhuman.common.model.Response;
import com.baidu.acg.piat.digitalhuman.common.richconfig.JsonToXmlRequest;
import com.baidu.acg.piat.digitalhuman.common.richconfig.RichtextConfig;
import com.baidu.acg.piat.digitalhuman.common.richconfig.RichtextListRequest;
import com.baidu.acg.piat.digitalhuman.console.service.sce.RichtextConfigService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestAttribute;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * Created on 2020/4/22 21:40.
 *
 * <AUTHOR>
 */
@Slf4j
@RequestMapping(value = {"/api/digitalhuman/sce/v1/richtext/config", "/api/digitalhuman/console/v1/richtext/config"})
@RestController
@RequiredArgsConstructor
public class RichtextConfigController {

    private final RichtextConfigService richtextConfigService;

    @PostMapping("/create")
    public Response<RichtextConfig> create(@RequestBody RichtextConfig request,
                                           @RequestAttribute("accountId") String uid) {
        request.setUserId(uid);
        return Response.success(richtextConfigService.create(request));
    }

    @PostMapping("/delete")
    public Response<Void> delete(@RequestBody RichtextConfig request) {
        richtextConfigService.delete(request.getConfigId());
        return Response.success(null);
    }

    @PostMapping("/update")
    public Response<RichtextConfig> update(@RequestBody RichtextConfig request) {
        return Response.success(richtextConfigService.update(request));
    }

    @PostMapping("/list")
    public PageResponse<RichtextConfig> list(@Valid @RequestBody RichtextListRequest request,
                                             @RequestAttribute("accountId") String uid) {
        request.setUserId(uid);
        return richtextConfigService.list(request);
    }

    @PostMapping("/json2xml")
    public Response<RichtextConfig> json2xml(@RequestBody JsonToXmlRequest request) {
        return Response.success(richtextConfigService.json2xml(request));
    }

}
