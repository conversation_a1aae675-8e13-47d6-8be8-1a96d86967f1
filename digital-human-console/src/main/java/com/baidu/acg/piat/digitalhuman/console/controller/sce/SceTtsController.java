package com.baidu.acg.piat.digitalhuman.console.controller.sce;

import com.baidu.acg.piat.digitalhuman.common.model.Response;
import com.baidu.acg.piat.digitalhuman.console.model.TtsDetailAndAudioResponse;
import com.baidu.acg.piat.digitalhuman.console.model.TtsDetailResponse;
import com.baidu.acg.piat.digitalhuman.console.service.tts.TtsServiceImpl;
import com.baidu.acg.piat.digitalhuman.console.service.tts.TtsSplitServiceImpl;
import com.baidu.acg.piat.digitalhuman.storage.service.StorageService;
import com.baidu.acg.piat.digitalhuman.tts.model.TtsQueryParams;
import com.google.common.base.Charsets;
import com.google.common.base.Joiner;
import com.google.common.hash.HashFunction;
import com.google.common.hash.Hashing;

import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.beanutils.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;

import java.util.Map;
import java.util.Objects;

/**
 * Created on 2022/3/29 7:31 下午
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@RequestMapping(value = {"/api/digitalhuman/sce/tts", "/api/internal/v1/sce/tts", "/api/digitalhuman/console/v1"})
public class SceTtsController {
    private static final int TTS_WAV_HEADER_BYTES = 44;

    private static final int TTS_WAV_SAMPLE_RATE = 16000;

    private static final int TTS_WAV_SAMPLE_BYTES = 2;

    private static final HashFunction HASH_FUNCTION = Hashing.murmur3_128();

    @Autowired
    private TtsSplitServiceImpl ttsSplitServiceImpl;

    @Autowired
    private TtsServiceImpl ttsServiceImpl;

    private final StorageService storageService;

    @Value("${digitalhuman.tts.word-limit:2000}")
    private Integer ttsWordLimit;

    @GetMapping("/text2audio")
    public void text2audio(@RequestParam Map<String, Object> request, HttpServletResponse httpServletResponse,
                           @RequestHeader(value = "Language", defaultValue = "") String language)
            throws Exception {
        TtsQueryParams ttsQueryParams = TtsQueryParams.builder().build();
        BeanUtils.populate(ttsQueryParams, request);
        var result = ttsSplitServiceImpl.text2audioWithSplit(language, request.getOrDefault("tex", "大家好，我是虚拟数字人，这是我的声音").toString(),
                ttsQueryParams);

        httpServletResponse.setContentType("audio/wav");
        httpServletResponse.setContentLengthLong(result.length);

        httpServletResponse.getOutputStream().write(result);

    }

    @PostMapping("/split/text2audio")
    public void text2audioWithSplit(@RequestBody Map<String, Object> request, HttpServletResponse httpServletResponse,
                                    @RequestHeader(value = "Language", defaultValue = "") String language)
            throws Exception {
        log.debug("text2audioWithSplit request:{}", request);
        TtsQueryParams ttsQueryParams = TtsQueryParams.builder().build();
        BeanUtils.populate(ttsQueryParams, request);
        byte[] result = ttsSplitServiceImpl.text2audioWithSplit(language,
                request.getOrDefault("tex", "大家好，我是虚拟数字人，这是我的声音").toString(), ttsQueryParams);
        httpServletResponse.setContentType("audio/wav");
        httpServletResponse.setContentLengthLong(result.length);
        httpServletResponse.getOutputStream().write(result);
    }

    @PostMapping("/detail")
    public Response<TtsDetailResponse> detail(@RequestBody Map<String, Object> request,
                                              @RequestHeader(value = "Language", defaultValue = "") String language) throws Exception {
        log.debug("detail request:{},language:{}", request, language);
        TtsQueryParams ttsQueryParams = TtsQueryParams.builder().build();
        BeanUtils.populate(ttsQueryParams, request);
        // !只是为了单独处理出门问问的报错
        try {
            TtsDetailResponse resp = ttsSplitServiceImpl.getTtsDetailResponse(language,
                    request.getOrDefault("tex", "大家好，我是虚拟数字人，这是我的声音").toString(),
                    ttsQueryParams
            );
            return Response.success(resp);
        } catch (Exception e) {
            log.debug("detail getTtsDetailResponse error:{}", e.getMessage());
            return Response.fail(-1, e.getMessage());
        }
    }

    @PostMapping("/detail/audio")
    public Response<TtsDetailAndAudioResponse> detailAudio(@RequestBody Map<String, Object> request,
                                                           @RequestHeader(value = "Language", defaultValue = "") String language) throws Exception {
        log.debug("detail request:{},language:{}", request, language);
        TtsQueryParams ttsQueryParams = TtsQueryParams.builder().build();
        BeanUtils.populate(ttsQueryParams, request);
        // !只是为了单独处理出门问问的报错
        try {
            TtsDetailAndAudioResponse resp = ttsSplitServiceImpl.getTtsDetailAndAudioResponse(language,
                    request.getOrDefault("tex", "大家好，我是虚拟数字人，这是我的声音").toString(),
                    ttsQueryParams
            );
            return Response.success(resp);
        } catch (Exception e) {
            log.debug("detail getTtsDetailResponse error:{}", e.getMessage());
            return Response.fail(-1, e.getMessage());
        }
    }

    @PostMapping("/detail/single")
    public Response<TtsDetailResponse> detailSingle(@RequestBody Map<String, Object> request,
                                                    @RequestHeader(value = "Language", defaultValue = "") String language)
            throws Exception {
        log.debug("detailSingle request:{}", request);
        TtsQueryParams ttsQueryParams = TtsQueryParams.builder().build();
        BeanUtils.populate(ttsQueryParams, request);
        try {
            TtsDetailResponse resp = ttsServiceImpl.getTtsDetailResponse(language,
                    request.getOrDefault("tex", "大家好，我是虚拟数字人，这是我的声音").toString(),
                    ttsQueryParams
            );
            return Response.success(resp);
        } catch (Exception e) {
            return Response.fail(-1, e.getMessage());
        }
    }

    @PostMapping("/detail/upload")
    public Response<WavResult> text2audioAndUpload(@RequestBody Map<String, Object> request,
                                                   @RequestHeader(value = "Language", defaultValue = "") String language) throws Exception {
        try {
            log.debug("text2audioAndUpload request:{}", request);

            TtsQueryParams ttsQueryParams = TtsQueryParams.builder().build();
            BeanUtils.populate(ttsQueryParams, request);
            String path = cacheKey(request.getOrDefault("tex", "大家好，我是虚拟数字人，这是我的声音").toString(), ttsQueryParams);
            path = path + ".wav";
            log.debug("text2audioAndUpload path:{}", path);
            try {
                StorageService.DownloadObject downloadObject = storageService.download(path);
                log.debug("text2audioAndUpload download path:{}", path);
                if (downloadObject.data != null && downloadObject.data.length != 0) {
                    log.debug("Get tts url from cache, path={}", path);
                    return Response.success(
                            new WavResult().setUrl(downloadObject.url.toString()).setSize(downloadObject.data.length).setDuration(
                                    (downloadObject.data.length - TTS_WAV_HEADER_BYTES) / (TTS_WAV_SAMPLE_RATE / 1000
                                            * TTS_WAV_SAMPLE_BYTES)));
                }
            } catch (Throwable e) {
                log.debug("Get tts url from cache error, path={}, error={}", path, e);
            } finally {
                log.debug("text2audioAndUpload download finally path:{}", path);
            }

            log.debug("text2audioAndUpload download end path:{}", path);

            byte[] wav = ttsSplitServiceImpl.text2audioWithSplit(language,
                    request.getOrDefault("tex", "大家好，我是虚拟数字人，这是我的声音").toString(), ttsQueryParams);
            var url = storageService.save(wav, path);
            WavResult result = new WavResult().setUrl(url.toString()).setSize(wav.length).setDuration(
                    (wav.length - TTS_WAV_HEADER_BYTES) / (TTS_WAV_SAMPLE_RATE / 1000 * TTS_WAV_SAMPLE_BYTES));
            return Response.success(result);
        } catch (Throwable e) {
            log.error("Failed to get tts url1 :{}", e.getMessage());
            log.error("Failed to get tts url :{}", e);
            return Response.fail("服务器繁忙，请重试");
        }
    }

    private String cacheKey(String text, TtsQueryParams params) {
        String tmp = Joiner.on("_").join(
                Objects.toString(text, ""),
                Objects.toString(params.getAue(), ""),
                Objects.toString(params.getCtp(), ""),
                Objects.toString(params.getCuid(), ""),
                Objects.toString(params.getLan(), ""),
                Objects.toString(params.getPdt(), ""),
                Objects.toString(params.getPer(), ""),
                Objects.toString(params.getSpd(), ""),
                Objects.toString(params.getPit(), ""),
                Objects.toString(params.getVol(), ""),
                Objects.toString(params.getXml(), ""),
                Objects.toString(params.getExtraParams(), "")
        );

        return this.HASH_FUNCTION.hashString(tmp, Charsets.UTF_8).toString();
    }

    @Data
    @Accessors(chain = true)
    public static class WavResult {

        private String url;

        private long duration;

        private long size;
    }
}
