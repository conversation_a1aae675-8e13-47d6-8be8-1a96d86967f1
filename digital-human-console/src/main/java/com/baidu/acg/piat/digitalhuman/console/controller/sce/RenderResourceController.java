package com.baidu.acg.piat.digitalhuman.console.controller.sce;

import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import com.baidu.acg.piat.digitalhuman.common.model.PageResponse;
import com.baidu.acg.piat.digitalhuman.common.webrtc.resource.pool.model.SpareResponse;
import com.baidu.acg.piat.digitalhuman.console.model.render.RenderResourceListRequest;
import com.baidu.acg.piat.digitalhuman.console.model.render.RenderResourceVO;
import com.baidu.acg.piat.digitalhuman.resource.pool.client.ResourcePoolClient;

/**
 * 渲染资源
 *
 * <AUTHOR>
 * @since 2021/08/09
 */
@Slf4j
@RequestMapping(value = {"/api/digitalhuman/sce/v1/resource"})
@RestController
@RequiredArgsConstructor
public class RenderResourceController {
    private final ResourcePoolClient resourcePoolClient;

    @PostMapping("/type")
    public PageResponse<RenderResourceVO> listRenderResource(@RequestBody RenderResourceListRequest request) {
        log.info("Query resource list, request={}", request);
        SpareResponse response;
        if (request.isSpare()) {
            response = resourcePoolClient.listSpareByResourceType(request.getResourceTypes());
        } else {
            response = resourcePoolClient.listAllByResourceType(request.getResourceTypes());
        }
        List<RenderResourceVO> contents = Lists.newArrayList();
        if (response.getResources() != null) {
            for (String type : response.getResources().keySet()) {
                contents.add(RenderResourceVO.builder()
                        .resourceType(type)
                        .instances(response.getResources().get(type))
                        .build());
            }
        }
        return PageResponse.success(contents);
    }
}
