package com.baidu.acg.piat.digitalhuman.console.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * Created in 2023/3/1
 * <p>
 */
@Slf4j
public class TagsHelper {

    public static final String RESOURCE_QUOTA_PREFIX = "RESOURCE_QUOTA_";

    @SuppressWarnings("unchecked")
    public static Map<String, String> getTags() {
        try {
            ServletRequestAttributes requestAttributes = (ServletRequestAttributes)
                    RequestContextHolder.currentRequestAttributes();
            Object tags = requestAttributes.getRequest().getAttribute("tags");
            if (null == tags) {
                return new HashMap<>();
            }
            return (Map<String, String>) tags;
        } catch (IllegalStateException e) {
            log.warn("No thread-bound request found, it's not an actual web request");
            return Collections.emptyMap();
        }
    }

    public static String get(String key) {
        return getTags().get(key);
    }

    /**
     * @param characterType character image type
     * @return -1: without limit
     */
    public static int getResourceQuota(String characterType) {
        String limit = get(RESOURCE_QUOTA_PREFIX + characterType);
        if (null == limit) {
            return -1;
        }
        return Integer.parseInt(limit);
    }
}
