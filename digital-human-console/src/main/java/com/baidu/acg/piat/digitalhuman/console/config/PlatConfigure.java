// Copyright (C) 2019 Baidu Inc. All rights reserved.
package com.baidu.acg.piat.digitalhuman.console.config;

import com.baidu.acg.piat.digitalhuman.common.character.CharacterImages;
import com.baidu.acg.piat.digitalhuman.plat.client.PlatformClient;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * AccessConfigure
 *
 * <AUTHOR>
 * @since 2019-11-28
 */
@Configuration
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class PlatConfigure {

    @Bean
    @ConfigurationProperties(prefix = "digitalhuman.plat.config")
    public PlatConfigure.Config platConfig() {
        return new PlatConfigure.Config();
    }

    @Bean
    public PlatformClient platformClient() {
        var config = platConfig();
        return new PlatformClient(config.getBaseUrl());
    }

    @Bean
    @ConfigurationProperties(prefix = "digitalhuman.character.image")
    public CharacterImages characterImageList() {
        return new CharacterImages();
    }


    @Data
    public static class Config {

        private String baseUrl;

        private int pollIntervalMillis = 5000;

        private int notExistedTTL = 5;

        private int retryTimes = 3;

        private int retrySleepMillis = 100;

    }


}
