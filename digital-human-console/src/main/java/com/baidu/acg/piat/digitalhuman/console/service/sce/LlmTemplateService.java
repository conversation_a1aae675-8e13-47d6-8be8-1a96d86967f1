package com.baidu.acg.piat.digitalhuman.console.service.sce;

import com.baidu.acg.piat.digitalhuman.common.llmrole.LlmRoleTemplate;
import com.baidu.acg.piat.digitalhuman.common.model.PageResponse;
import com.baidu.acg.piat.digitalhuman.common.model.Response;

import java.util.List;

public interface LlmTemplateService {
    Response<LlmRoleTemplate> createLlmTemplate(LlmRoleTemplate llmRoleTemplate);

    Response<Void> batchDeleteTemplate(List<String> llmTemplateIds);

    Response<LlmRoleTemplate> updateLlmTemplate(LlmRoleTemplate llmTemplate);

    Response<LlmRoleTemplate> detailLlmTemplate(String llmRoleTemplateId);

    PageResponse<LlmRoleTemplate> listByTemplateType(int templateType, String templateName, String screenType, int pageNo, int pageSize);
}
