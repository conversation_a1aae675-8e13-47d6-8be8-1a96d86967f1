package com.baidu.acg.piat.digitalhuman.console.service.statistic;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.metadata.Head;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.excel.write.handler.CellWriteHandler;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.metadata.holder.WriteTableHolder;
import com.baidu.acg.piat.digitalhuman.common.common.PlatCode;
import com.baidu.acg.piat.digitalhuman.common.model.PageResult;
import com.baidu.acg.piat.digitalhuman.common.statistic.CharacterPreferencesData;
import com.baidu.acg.piat.digitalhuman.common.statistic.CharacterResourceResponse;
import com.baidu.acg.piat.digitalhuman.common.statistic.CharacterUsageTrendData;
import com.baidu.acg.piat.digitalhuman.common.statistic.ProductionStatisticData;
import com.baidu.acg.piat.digitalhuman.common.statistic.SessionStatisticData;
import com.baidu.acg.piat.digitalhuman.common.statistic.SessionStatisticPageResult;
import com.baidu.acg.piat.digitalhuman.common.statistic.StatisticResponse;
import com.baidu.acg.piat.digitalhuman.console.config.SessionStatisticExportConfigure;
import com.baidu.acg.piat.digitalhuman.console.model.statistic.StatisticRequest;
import com.baidu.acg.piat.digitalhuman.plat.client.PlatformClient;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.poi.ss.usermodel.Row;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.PropertyPlaceholderHelper;

import java.io.OutputStream;
import java.util.List;
import java.util.Properties;

@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class StatisticServiceImpl implements StatisticService {

    private final PlatformClient platformClient;

    private final SessionStatisticExportConfigure.SheetHeaderConfig adminSheetHeader;

    private final SessionStatisticExportConfigure.SheetHeaderConfig tenancySheetHeader;

    @Override
    public StatisticResponse getActiveSession(StatisticRequest request) {
        return platformClient.currentSessions(request.getAppId(), request.getStartInMs(), request.getEndInMs());
    }

    @Override
    public StatisticResponse getTotalSession(StatisticRequest request) {
        return platformClient.totalSessions(request.getAppId(), request.getStartInMs(), request.getEndInMs());
    }

    @Override
    public StatisticResponse getAvgSessionTime(StatisticRequest request) {
        return platformClient.avgSessionTime(request.getAppId(), request.getStartInMs(), request.getEndInMs());
    }

    @Override
    public StatisticResponse getTotalDialog(StatisticRequest request) {
        return platformClient.totalDialogs(request.getAppId(), request.getStartInMs(), request.getEndInMs());
    }

    @Override
    public StatisticResponse getSessionTrend(StatisticRequest request) {
        return platformClient.sessionTrend(request.getAppId(), request.getStartInMs(), request.getEndInMs(),
                request.getIntervalInSeconds());
    }

    @Override
    public CharacterResourceResponse characterResource(PlatCode platCode
            , String accountVisibleCharacters, String accountId, int roleLevel) {
        return platformClient.characterResource(platCode.name(), accountId, roleLevel, accountVisibleCharacters);
    }

    @Override
    public List<CharacterUsageTrendData> characterUsageTrend(PlatCode platCode
            , long startInMs, long endInMs, long intervalInSeconds
            , String accountVisibleCharacters, String accountId, int roleLevel) {

        return platformClient.characterUsageTrend(platCode.name(), startInMs, endInMs
                , intervalInSeconds, accountVisibleCharacters, accountId, roleLevel);
    }

    @Override
    public List<CharacterPreferencesData> characterPreferences(PlatCode platCode
            , List<String> accountMenus
            , String accountVisibleCharacters
            , String accountId, int roleLevel) {

        return platformClient.characterPreferences(platCode.name(), accountId
                , roleLevel, accountVisibleCharacters, accountMenus);
    }

    @Override
    public SessionStatisticPageResult sessionService(PlatCode platCode
            , String accountId, int roleLevel
            , int pageNo, int pageSize
            , String startInDate, String endInDate, String orderBy, String order) {
        int startInDateInt = Integer.valueOf(startInDate.replaceAll("-", ""));
        int endInDateInt = Integer.valueOf(endInDate.replaceAll("-", ""));
        return platformClient.sessionService(platCode.name(), accountId
                , roleLevel, pageNo, pageSize, startInDateInt, endInDateInt, orderBy, order);
    }

    @Override
    public void exportSessionService(PlatCode platCode, String accountId
            , int roleLevel, String startInDate
            , String endInDate, String orderBy, String order
            , OutputStream outputStream) {
        boolean isSuperAdmin = true;
        if (!(PlatCode.ADMIN.equals(platCode) && roleLevel == 3)) {
            isSuperAdmin = false;
        }
        SessionStatisticExportConfigure.SheetHeaderConfig sheetHeader = isSuperAdmin
                ? adminSheetHeader : tenancySheetHeader;
        SessionStatisticPageResult pageResult = sessionService(platCode, accountId
                , roleLevel, 1, Integer.MAX_VALUE
                , startInDate, endInDate, orderBy, order);
        EasyExcel.write(outputStream)
                .head(SessionStatisticData.class)
                .excelType(ExcelTypeEnum.XLSX)
                .registerWriteHandler(new SessionServiceExcelCellWriteHandler(sheetHeader))
                .sheet(sheetHeader.getSheetName())
                .doWrite(pageResult.getResult());
    }

    @Override
    public PageResult<ProductionStatisticData> production(PlatCode platCode
            , String accountId, int roleLevel, int pageNo, int pageSize
            , String startInDate, String endInDate) {
        int startInDateInt = Integer.valueOf(startInDate.replaceAll("-", ""));
        int endInDateInt = Integer.valueOf(endInDate.replaceAll("-", ""));
        return platformClient.production(platCode.name(), accountId
                , roleLevel, pageNo, pageSize, startInDateInt, endInDateInt);
    }


    static class SessionServiceExcelCellWriteHandler implements CellWriteHandler {
        private SessionStatisticExportConfigure.SheetHeaderConfig sheetHeader;

        PropertyPlaceholderHelper placeholderHelper =
                new PropertyPlaceholderHelper("${", "}");

        public SessionServiceExcelCellWriteHandler(SessionStatisticExportConfigure
                                                           .SheetHeaderConfig sheetHeader) {
            this.sheetHeader = sheetHeader;
        }

        @Override
        public void beforeCellCreate(WriteSheetHolder writeSheetHolder, WriteTableHolder writeTableHolder,
                                     Row row, Head head, Integer integer, Integer integer1, Boolean aBoolean) {
            if (head != null) {
                List<String> headNameList = head.getHeadNameList();
                if (CollectionUtils.isNotEmpty(headNameList)) {
                    Properties properties = new Properties();
                    properties.setProperty("name", sheetHeader.getName());
                    properties.setProperty("sessionTotalCount", sheetHeader.getSessionTotalCountName());
                    properties.setProperty("sessionAvgDuration", sheetHeader.getSessionAvgDurationName());
                    properties.setProperty("dialogTotalCount", sheetHeader.getDialogTotalCountName());
                    for (int i = 0; i < headNameList.size(); i++) {
                        headNameList.set(i, placeholderHelper
                                .replacePlaceholders(headNameList.get(i), properties));
                    }
                }
            }
        }
    }
}
