package com.baidu.acg.piat.digitalhuman.console.model;

import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * TtsDetailResponse
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TtsDetailResponse {
    private Long totalDuration;
    private List<SentenceDetail> detail;
    private String audioWavBase64;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SentenceDetail {
        private String text;
        private Long duration;
        private Long startTime;
        private Long endTime;
        private List<SentenceDetail> detail;
        private int index;
    }
}
