package com.baidu.acg.piat.digitalhuman.console.service.jwt;

import com.auth0.jwt.JWT;
import com.auth0.jwt.algorithms.Algorithm;
import com.auth0.jwt.exceptions.JWTDecodeException;
import com.auth0.jwt.interfaces.DecodedJWT;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import com.baidu.acg.dh.user.client.model.vo.UserCreateTokenReq;
import com.baidu.acg.piat.digitalhuman.console.model.common.Constant;

/**
 * Created on 2021/2/2 19:05.
 *
 * <AUTHOR>
 */
@Service
public class JwtTokenService {

    @Value("#{${jwt.expire.time}}")
    private long EXPIRE_TIME;

    public String createToken(UserCreateTokenReq userCreateTokenReq) {

        long currentTimeMillis = System.currentTimeMillis();

        Date expireDate = new Date(currentTimeMillis + 3 * EXPIRE_TIME);

        Map<String, Object> map = new HashMap<String, Object>();
        map.put("alg", "HS256");
        map.put("typ", "JWT");
        Algorithm algorithm = Algorithm.HMAC256(userCreateTokenReq.getPassword());
        Date nowDate = new Date();
        String token = "";
        token = JWT.create()
                .withHeader(map)
                .withSubject(userCreateTokenReq.getUserName())
                .withAudience(userCreateTokenReq.getUid()) // 将 user id 保存到 token 里面
                .withIssuer(Constant.JWT_ISSUER)
                .withIssuedAt(nowDate)
                .withExpiresAt(expireDate)
                .sign(algorithm); // 以 password 作为 token 的密钥
        return token;

    }

    public Date getExpireDate(String token) {
        try {
            DecodedJWT jwt = JWT.decode(token);
            return jwt.getExpiresAt();
        } catch (JWTDecodeException e) {
            return null;
        } catch (Exception e) {
            return null;
        }
    }

    public boolean isExpire(String token) {
        try {
            DecodedJWT jwt = JWT.decode(token);
            return jwt.getExpiresAt().compareTo(new Date()) <= 0 ? true : false;
        } catch (JWTDecodeException e) {
            return true;
        } catch (Exception e) {
            return true;
        }
    }
}
