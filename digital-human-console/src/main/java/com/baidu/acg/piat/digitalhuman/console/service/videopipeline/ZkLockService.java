// Copyright (C) 2021 Baidu Inc. All rights reserved.

package com.baidu.acg.piat.digitalhuman.console.service.videopipeline;

import io.vavr.Tuple;
import io.vavr.Tuple2;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.curator.framework.CuratorFramework;
import org.apache.curator.framework.recipes.locks.InterProcessSemaphoreMutex;

import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

@Slf4j
@RequiredArgsConstructor
public class ZkLockService {

    private final CuratorFramework curatorFramework;

    @Setter
    private String zkLockPrefix;

    public ZkLockService(CuratorFramework curatorFramework, String zkLockPrefix) {
        this.curatorFramework = curatorFramework;
        this.zkLockPrefix = zkLockPrefix;
    }

    private Map<String, Tuple2<InterProcessSemaphoreMutex, String>> lockMap = new ConcurrentHashMap<>();

    public Optional<String> tryLock(String lock) {
        var mutexAndToken = lockMap.computeIfAbsent(lock, k ->
                Tuple.of(new InterProcessSemaphoreMutex(curatorFramework, zkLockPrefix + "/" + k),
                        RandomStringUtils.random(10)));
        try {
            if (mutexAndToken._1.acquire(100, TimeUnit.MILLISECONDS)) {
                log.debug("Success lock in zk for lock={}", lock);
                Tuple2<InterProcessSemaphoreMutex, String> mutexAndNewToken =
                        mutexAndToken.map2(t -> RandomStringUtils.random(10));
                lockMap.put(lock, mutexAndNewToken);
                return Optional.of(mutexAndNewToken._2);
            } else {
                log.info("Fail to acquire lock={} maybe locked by others", lock);
                return Optional.empty();
            }
        } catch (Exception e) {
            log.error("Fail to try lock in zk for lock={}", lock, e);
            return Optional.empty();
        }
    }

    public boolean unlock(String lock, String token) {
        var mutexAndLock = lockMap.get(lock);
        if (mutexAndLock == null) {
            log.error("Fail to find mutex for lock={}", lock);
            return false;
        }
        if (!mutexAndLock._2.equals(token)) {
            log.error("Fail to unlock lock={} since token is invalid", lock);
            return false;
        }
        try {
            lockMap.remove(lock);
            mutexAndLock._1.release();
            log.debug("Success to unlock zk lock={}", lock);
            return true;
        } catch (Exception e) {
            log.error("Fail to release lock in zk for lock={}", lock, e);
            return false;
        }
    }
}


