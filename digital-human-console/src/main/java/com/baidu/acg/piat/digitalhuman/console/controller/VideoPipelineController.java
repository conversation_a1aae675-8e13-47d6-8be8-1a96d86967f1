// Copyright (C) 2020 Baidu Inc. All rights reserved.

package com.baidu.acg.piat.digitalhuman.console.controller;

import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.function.BiConsumer;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestAttribute;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.context.request.async.DeferredResult;

import com.baidu.acg.piat.digitalhuman.common.console.video.ProgressResult;
import com.baidu.acg.piat.digitalhuman.common.console.video.ProgressStatus;
import com.baidu.acg.piat.digitalhuman.common.console.video.Text2VideoBatchRequest;
import com.baidu.acg.piat.digitalhuman.common.console.video.Text2VideoRequest;
import com.baidu.acg.piat.digitalhuman.common.console.video.VideoQueryRequest;
import com.baidu.acg.piat.digitalhuman.common.console.video.VideoScheduleRequest;
import com.baidu.acg.piat.digitalhuman.common.exception.DigitalHumanCommonException;
import com.baidu.acg.piat.digitalhuman.common.exception.Error;
import com.baidu.acg.piat.digitalhuman.common.model.PageResponse;
import com.baidu.acg.piat.digitalhuman.common.model.Response;
import com.baidu.acg.piat.digitalhuman.common.videoprogress.VideoBatchRequest;
import com.baidu.acg.piat.digitalhuman.console.model.BatchIdRequest;
import com.baidu.acg.piat.digitalhuman.console.model.VideoIdRequest;
import com.baidu.acg.piat.digitalhuman.console.model.VideoListRequest;
import com.baidu.acg.piat.digitalhuman.console.model.common.AuthorizationInfo;
import com.baidu.acg.piat.digitalhuman.console.service.videopipeline.VideoPipelineService;
import com.baidu.acg.piat.digitalhuman.console.service.videopipeline.VideoProgressPollingService;
import com.baidu.acg.piat.digitalhuman.videopipeline.client.VideoProgressHttpClient;

import io.vavr.control.Try;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * VideoCompositeController
 *
 * <AUTHOR> Zhensheng(<EMAIL>)
 * @since 2020-02-10
 */
@Slf4j
@RequestMapping({"/api/digitalhuman/sce/v1/video",
        "/api/digitalhuman/console/v1/video",
        "/api/digitalhuman/sce/v2/video"})
@RestController
@RequiredArgsConstructor
public class VideoPipelineController {
    private static final int MIN_PAGE_NO = 1;
    private static final int MIN_PAGE_SIZE = 1;

    private final VideoPipelineService videoPipelineService;

    private final VideoProgressPollingService pollingService;

    private final VideoProgressHttpClient videoProgressHttpClient;

    /**
     * 离线接口，后续不推荐客户直接使用离线接口，因为当前离线接口的提交过程，需要同步开始执行，仍然会发生失败，
     * 该接口等效于 submit + schedule
     *
     * @param httpServletRequest
     * @param textRequest
     * @return
     */
    @PostMapping("/text2video")
    @Deprecated
    public Response<ProgressResult> text2Video(
            HttpServletRequest httpServletRequest,
            @Valid @RequestBody Text2VideoRequest textRequest) {
        AuthorizationInfo authInfo = (AuthorizationInfo) httpServletRequest.getAttribute("authorization");
        if (authInfo == null) {
            throw new DigitalHumanCommonException(Error.AUTHORIZATION_NOT_PRESENT);
        }
        log.debug("Accept the text2Video request={} ", textRequest);
        return Response.success(videoPipelineService.start(
                authInfo.getAppId(), authInfo.getAppKey(),
                new Text2VideoRequest.ComplexText2VideoRequest(textRequest)));
    }

    @PostMapping("/text2video/complex")
    public Response<ProgressResult> text2VideoComplex(
            HttpServletRequest httpServletRequest,
            @Valid @RequestBody Text2VideoRequest.ComplexText2VideoRequest textRequest) {
        AuthorizationInfo authInfo = (AuthorizationInfo) httpServletRequest.getAttribute("authorization");
        if (authInfo == null) {
            return Response.fail(Error.AUTHORIZATION_NOT_PRESENT);
        }

        log.debug("Accept the text2Video complex request={} ", textRequest);
        try {
            return Response.success(videoPipelineService.start(authInfo.getAppId(), authInfo.getAppKey(), textRequest));
        } catch (DigitalHumanCommonException e) {
            return Response.fail(e.getCode(), e.getMessage());
        }
    }

    @PostMapping("/text2video/preview")
    public Response<ProgressResult> text2VideoPreview(
            HttpServletRequest httpServletRequest,
            @Valid @RequestBody Text2VideoRequest.ComplexText2VideoRequest textRequest) {
        AuthorizationInfo authInfo = (AuthorizationInfo) httpServletRequest.getAttribute("authorization");
        if (authInfo == null) {
            return Response.fail(Error.AUTHORIZATION_NOT_PRESENT);
        }

        log.debug("Accept the text2Video complex request={} ", textRequest);
        try {
            return Response.success(videoPipelineService.startPreview(authInfo.getAppId(), authInfo.getAppKey(), textRequest));
        } catch (DigitalHumanCommonException e) {
            return Response.fail(e.getCode(), e.getMessage());
        }
    }

    @PostMapping("/text2video/submit")
    public Response<List<ProgressResult>> text2VideoSubmit(
            HttpServletRequest httpServletRequest,
            @Valid @RequestBody Text2VideoBatchRequest batchRequest) {
        AuthorizationInfo authInfo = (AuthorizationInfo) httpServletRequest.getAttribute("authorization");
        log.debug("Accept the text2VideoSubmit request={}, authInfo={}", batchRequest, authInfo);
        if (authInfo == null) {
            throw new DigitalHumanCommonException(Error.AUTHORIZATION_NOT_PRESENT);
        }
        log.debug("Accept the text2VideoSubmit request={}, appId={}", batchRequest, authInfo.getAppId());

        return Response.success(videoPipelineService.submit(
                batchRequest.toVideoSubmitRequest(authInfo.getAppId(), authInfo.getAppKey())));
    }

    @PostMapping("/text2video/schedule")
    public Response<ProgressResult> text2VideoSchedule(
            HttpServletRequest httpServletRequest,
            @Valid @RequestBody VideoScheduleRequest videoScheduleRequest) {
        AuthorizationInfo authInfo = (AuthorizationInfo) httpServletRequest.getAttribute("authorization");
        if (authInfo == null) {
            throw new DigitalHumanCommonException(Error.AUTHORIZATION_NOT_PRESENT);
        }
        videoScheduleRequest.setAppId(authInfo.getAppId());
        videoScheduleRequest.setAppKey(authInfo.getAppKey());
        log.debug("Accept the text2VideoSchedule request={}, appId={}", videoScheduleRequest, authInfo.getAppId());
        return Response.success(videoPipelineService.schedule(
                VideoScheduleRequest.ComplexVideoScheduleRequest.convert(videoScheduleRequest)));
    }

    @PostMapping("/text2video/complex/sync")
    public DeferredResult<Response<ProgressResult>> text2videoComplexSync(
            HttpServletRequest httpServletRequest,
            @Valid @RequestBody Text2VideoRequest.ComplexText2VideoRequest textRequest) {
        log.debug("Accept the text2video complex sync request={}", textRequest);

        // disable retry when sync creating video
        textRequest.getRetryPolicy().setRetryEnabled(false);

        DeferredResult<Response<ProgressResult>> deferredResult = new DeferredResult<>(30 * 60 * 1000L,
                () -> Response.fail(Error.PRODUCE_VIDEO_TIMEOUT.getCode(), Error.PRODUCE_VIDEO_TIMEOUT.getMessage()));

        AuthorizationInfo authInfo = (AuthorizationInfo) httpServletRequest.getAttribute("authorization");
        if (authInfo == null) {
            throw new DigitalHumanCommonException(Error.AUTHORIZATION_NOT_PRESENT);
        }

        // todo: 判断图片是否有效
        CompletableFuture.supplyAsync(
                        () -> videoPipelineService.start(authInfo.getAppId(), authInfo.getAppKey(), textRequest))
                .thenAccept(result -> {
                    var callback = new BiConsumer<ProgressResult, DigitalHumanCommonException>() {
                        @Override
                        public void accept(ProgressResult r, DigitalHumanCommonException e) {
                            log.info("Success to trigger the poll task callback of sessionId={} {} ",
                                    result.getVideoId(), r, e);
                            if (r != null) {
                                if (r.getStatus() == ProgressStatus.ERROR) {
                                    deferredResult.setResult(Response.fail(r.getErrorCode(), r.getFailureCause()));
                                } else {
                                    deferredResult.setResult(Response.success(r));
                                }
                            } else if (e != null) {
                                deferredResult.setResult(Response.fail(e.getCode(), e.getMessage()));
                            } else {
                                log.error("Unexpected result and exception all empty of video={}",
                                        result.getVideoId());
                                deferredResult.setResult(Response.fail(Error.INTERNAL_SERVER_ERROR.getCode(),
                                        Error.INTERNAL_SERVER_ERROR.getMessage()));
                            }
                        }
                    };
                    Runnable cancelTask = () -> {
                        log.error("Cancel the DeferredResult of videoId={} , ", result.getVideoId());
                        pollingService.removePollingTask(result.getVideoId(), callback);
                    };
                    deferredResult.onTimeout(cancelTask);

                    deferredResult.onError(t -> {
                        log.error("DeferredResult on error of videoId={} , ", result.getVideoId(), t);
                        cancelTask.run();

                    });

                    pollingService.addPollingTask(result.getVideoId(), callback);
                }).exceptionally(t -> {
                    Try.run(() -> {
                        log.error("Fail to start to video progress, return the failed task result", t);

                        Throwable cause = t.getCause();
                        if (cause instanceof DigitalHumanCommonException) {
                            deferredResult.setResult(Response.fail(((DigitalHumanCommonException) cause).getCode(),
                                    cause.getMessage()));
                        } else {
                            deferredResult.setResult(Response.fail(t.getMessage()));
                        }
                    }).onFailure(e -> {
                        log.error("Cannot reach here, fail to handle the exception, t={}, ", t.getMessage(), e);
                        deferredResult.setResult(Response.fail(t.getMessage()));
                    });

                    return null;
                });

        return deferredResult;
    }

    @PostMapping("/text2video/sync")
    public DeferredResult<Response<ProgressResult>> text2videoSync(
            HttpServletRequest httpServletRequest,
            @Valid @RequestBody Text2VideoRequest textRequest) {
        log.debug("Accept the text2videoSync request={}", textRequest);

        return text2videoComplexSync(httpServletRequest, new Text2VideoRequest.ComplexText2VideoRequest(textRequest));
    }

    @PostMapping("/progress")
    public Response<ProgressResult> queryProgress(
            @Valid @RequestBody VideoIdRequest videoRequest) {
        return videoProgressHttpClient.pollProgress(videoRequest.getId());
    }

    @PostMapping("/progress/batch")
    public Response<List<ProgressResult>> queryBatchProgress(@Valid @RequestBody BatchIdRequest videoRequest) {
        return videoProgressHttpClient.pollBatchProgress(videoRequest.getIds());
    }

    @DeleteMapping("/progress/{videoId}")
    public Response<Void> deleteProgress(@PathVariable(name = "videoId") String videoId) {
        return videoProgressHttpClient.deleteProgress(videoId);
    }

    @DeleteMapping("/progress/batchDelete")
    public Response deleteProgressBatch(@RequestBody VideoBatchRequest videoBatchRequest) {
        videoProgressHttpClient.deleteProgressBatch(videoBatchRequest);
        return Response.success();
    }

    @DeleteMapping("/progress/batchDelete/app")
    public Response deleteProgressBatchByAppId(@RequestAttribute("authorization") AuthorizationInfo authInfo,
                                               @RequestBody VideoBatchRequest videoBatchRequest) {
        videoBatchRequest.setAppId(authInfo.getAppId());
        videoProgressHttpClient.batchDeleteProgressByAppId(videoBatchRequest);
        return Response.success();
    }

    @PostMapping("/progress/query")
    public PageResponse<ProgressResult> pollProgressByUserNameAndProjectNameOrCharacterConfigId(
            @Valid @RequestBody VideoQueryRequest queryRequest) {
        if (StringUtils.isEmpty(queryRequest.getUserName()) && (StringUtils.isEmpty(queryRequest.getProjectName())
                && StringUtils.isEmpty(queryRequest.getCharacterConfigId()))) {
            throw new DigitalHumanCommonException("No valid parameters");
        }

        if (queryRequest.getPageNo() < MIN_PAGE_NO) {
            throw new DigitalHumanCommonException(String.format("Page no cannot less then %d", MIN_PAGE_NO));
        }

        if (queryRequest.getPageSize() < MIN_PAGE_SIZE) {
            throw new DigitalHumanCommonException(String.format("Page size cannot less then %d", MIN_PAGE_SIZE));
        }

        return videoProgressHttpClient.pollProgressByUserNameAndProjectNameOrCharacterConfigId(
                queryRequest.getUserName(),
                queryRequest.getProjectName(), queryRequest.getCharacterConfigId(),
                queryRequest.getPageNo(), queryRequest.getPageSize()).getResult();
    }

    @PostMapping("/list")
    public PageResponse<ProgressResult> queryProgressOfApp(
            HttpServletRequest httpServletRequest,
            @Valid @RequestBody VideoListRequest videoListRequest) {
        var authInfo = (AuthorizationInfo) httpServletRequest.getAttribute("authorization");
        var appId = authInfo.getAppId();
        if (appId.isEmpty()) {
            throw new DigitalHumanCommonException("AppId cannot be empty");
        }

        if (videoListRequest.getPageNo() < MIN_PAGE_NO) {
            throw new DigitalHumanCommonException(
                    String.format("Page no cannot less then %d", MIN_PAGE_NO));
        }

        if (videoListRequest.getPageSize() < MIN_PAGE_SIZE) {
            throw new DigitalHumanCommonException(
                    String.format("Page size cannot less then %d", MIN_PAGE_SIZE));
        }

        return videoProgressHttpClient.pollByAppId(
                appId, videoListRequest.getVideoName(),
                videoListRequest.getPageNo(), videoListRequest.getPageSize()).getResult();
    }
}
