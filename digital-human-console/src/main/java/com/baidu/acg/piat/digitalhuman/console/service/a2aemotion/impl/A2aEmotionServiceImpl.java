package com.baidu.acg.piat.digitalhuman.console.service.a2aemotion.impl;

import com.baidu.acg.piat.digitalhuman.common.exception.DigitalHumanCommonException;
import com.baidu.acg.piat.digitalhuman.console.config.A2aEmotionConfigure;
import com.baidu.acg.piat.digitalhuman.console.model.a2aemotion.AiEmotionRecognitionRes;
import com.baidu.acg.piat.digitalhuman.console.model.a2aemotion.A2aEmotionRecognitionRequest;
import com.baidu.acg.piat.digitalhuman.console.model.a2aemotion.AiEmotionRequest;
import com.baidu.acg.piat.digitalhuman.console.model.a2aemotion.A2aEmotion;
import com.baidu.acg.piat.digitalhuman.console.model.a2aemotion.A2aEmotionRecognitionResponse;
import com.baidu.acg.piat.digitalhuman.console.service.a2aemotion.A2aEmotionService;
import com.baidu.acg.piat.digitalhuman.console.service.nlp.AiCloudHttpClient;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.google.common.util.concurrent.RateLimiter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import com.baidu.acg.piat.digitalhuman.common.exception.Error;

import java.util.Set;
import java.util.List;
import java.util.Arrays;
import java.util.Comparator;
import java.util.Collections;
import java.util.Optional;
import java.util.stream.Collectors;

@Slf4j
@Service
public class A2aEmotionServiceImpl implements A2aEmotionService {

    @Autowired
    private AiCloudHttpClient aiCloudHttpClient;

    @Autowired
    private A2aEmotionConfigure.Config a2aEmotionConfig;

    RateLimiter rateLimiter = RateLimiter.create(10);


    @Override
    public A2aEmotionRecognitionResponse recognition(A2aEmotionRecognitionRequest request) {
        validateRequest(request);
        A2aEmotionRecognitionResponse result = new A2aEmotionRecognitionResponse();
        result.setData(Lists.newArrayList());
        request.getTexts().forEach(text -> {
            List<String> splitList = Arrays.stream(text.split(",|，")).collect(Collectors.toList());

            Set<A2aEmotion> a2aEmotionSet = Sets.newHashSet();
            for (String splitItem : splitList) {
                rateLimiter.acquire();
                AiEmotionRecognitionRes res =
                        aiCloudHttpClient.aiEmotionDetection(AiEmotionRequest.builder().text(splitItem).build());
                a2aEmotionSet.add(processEmotionRecognitionRes(res));
            }
            result.getData().add(nlpEmotionMapping(a2aEmotionSet).name());
        });
        return result;
    }


    private A2aEmotion processEmotionRecognitionRes(AiEmotionRecognitionRes res) {
        if (null == res || CollectionUtils.isEmpty(res.getItems())) {
            return A2aEmotion.natural;
        }
        List<AiEmotionRecognitionRes.RecognitionItem> collectList = res.getItems()
                .stream().filter((t) -> null != t && null != t.getProb()
                        && StringUtils.isNotEmpty(t.getLabel()))
                .collect(Collectors.toList());
        if (collectList.isEmpty()) {
            return A2aEmotion.natural;
        }
        Collections.sort(collectList, new Comparator<AiEmotionRecognitionRes.RecognitionItem>() {
            @Override
            public int compare(AiEmotionRecognitionRes.RecognitionItem o1, AiEmotionRecognitionRes.RecognitionItem o2) {
                return o1.getProb() > o2.getProb() ? 1 : -1;
            }
        });
        AiEmotionRecognitionRes.RecognitionItem recognitionItem = collectList.get(collectList.size() - 1);
        if (CollectionUtils.isEmpty(recognitionItem.getSubitems())) {
            return nlpEmotionMapping(recognitionItem.getLabel());
        }

        for (AiEmotionRecognitionRes.RecognitionSubItem item : recognitionItem.getSubitems()) {
            if (StringUtils.isEmpty(item.getLabel())) {
                continue;
            }
            return nlpEmotionMapping(item.getLabel());
        }
        return A2aEmotion.natural;
    }


    /**
     * https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/53G-5UwZAd/-K6au-QXKU/gRAo272cC2fO_6(映射逻辑详情见mrd)
     *
     * @param a2aEmotionSet
     * @return
     */
    private A2aEmotion nlpEmotionMapping(Set<A2aEmotion> a2aEmotionSet) {
        if (CollectionUtils.isEmpty(a2aEmotionSet)) {
            return A2aEmotion.natural;
        }
        switch (a2aEmotionSet.size()) {
            case 1:
                return a2aEmotionSet.stream().findFirst().get();
            case 2:
                if (!a2aEmotionSet.contains(A2aEmotion.natural)) {
                    return A2aEmotion.natural;
                } else {
                    for (A2aEmotion item : a2aEmotionSet) {
                        if (!item.equals(A2aEmotion.natural)) {
                            return item;
                        }
                    }
                }
                break;
            default:
                return A2aEmotion.natural;

        }
        return A2aEmotion.natural;
    }

    /**
     * https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/53G-5UwZAd/-K6au-QXKU/gRAo272cC2fO_6(映射逻辑详情见mrd)
     *
     * @param recognitionItemlable
     * @return
     */
    private A2aEmotion nlpEmotionMapping(String recognitionItemlable) {
        Optional<A2aEmotion> a2aEmotion = A2aEmotion.val(a2aEmotionConfig
                .getA2aEmotionMappingMap().get(recognitionItemlable));
        return a2aEmotion.isPresent() ? a2aEmotion.get() : A2aEmotion.natural;
    }

    private void validateRequest(A2aEmotionRecognitionRequest request) {
        if (null == request || CollectionUtils.isEmpty(request.getTexts())) {
            throw new DigitalHumanCommonException(Error.INVALID_PARAMETER.getCode()
                    , "request or texts field must not be empty");
        }
        int tempSize = request.getTexts().stream()
                .filter(s -> !StringUtils.isEmpty(s)).collect(Collectors.toList()).size();
        if (request.getTexts().size() != tempSize) {
            throw new DigitalHumanCommonException(Error.INVALID_PARAMETER.getCode()
                    , "texts field all elements  must not be empty");
        }
    }
}
