// Copyright (C) 2020 Baidu Inc. All rights reserved.
package com.baidu.acg.piat.digitalhuman.console.service.videopipeline;

import com.baidu.acg.piat.digitalhuman.tts.cache.TtsCache;
import com.baidu.acg.piat.digitalhuman.tts.model.TtsQueryParams;
import com.baidu.acg.piat.digitalhuman.tts.model.TtsResult;
import lombok.RequiredArgsConstructor;
import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Audio2VideoService
 *
 * <AUTHOR>
 * @since 2020-06-03
 */
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class Audio2VideoService {

    private final List<TtsQueryParams> ttsQueryParams;

    private final TtsCache ttsCache;



    public String generateToken(byte[] audio){

        String token = DigestUtils.md5Hex(audio).toUpperCase();

        String text = "<speak>" + token + "</speak>";
        ttsQueryParams.forEach(ttsQueryParam->{
            String cacheKey = ttsCache.cacheKey(text, ttsQueryParam);
            TtsResult cacheValue = new TtsResult(audio);
            ttsCache.cache(cacheKey, cacheValue);
        });

        return token;
    }

}
