package com.baidu.acg.piat.digitalhuman.console.service.tts;

import com.baidu.acg.piat.digitalhuman.common.audio.WavUtil;
import com.baidu.acg.piat.digitalhuman.common.exception.DigitalHumanCommonException;
import com.baidu.acg.piat.digitalhuman.console.model.TtsDetailAndAudioResponse;
import com.baidu.acg.piat.digitalhuman.console.model.TtsDetailResponse;
import com.baidu.acg.piat.digitalhuman.multilingual.MultilingualClient;
import com.baidu.acg.piat.digitalhuman.multilingual.model.MultilingualResult;
import com.baidu.acg.piat.digitalhuman.storage.service.StorageService;
import com.baidu.acg.piat.digitalhuman.tts.TtsServiceConfig;
import com.baidu.acg.piat.digitalhuman.tts.TtsStreamingResult;
import com.baidu.acg.piat.digitalhuman.tts.TtsStreamingServiceGrpcImpl;
import com.baidu.acg.piat.digitalhuman.tts.model.SubtitleTtsResult;
import com.baidu.acg.piat.digitalhuman.tts.model.TtsQueryParams;
import com.baidu.acg.piat.digitalhuman.tts.model.TtsResult;
import com.baidu.acg.piat.digitalhuman.tts.util.CmwwTtsClient;
import com.baidu.acg.piat.digitalhuman.tts.util.TextSplitHelper;
import com.baidu.acg.piat.digitalhuman.tts.util.srt.Subtitle;

import lombok.extern.slf4j.Slf4j;

import org.apache.http.util.TextUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Base64;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.atomic.AtomicReference;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * ttsSplitServiceImpl
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class TtsServiceImpl implements TtsSplitService {

    @Autowired
    private TtsStreamingServiceGrpcImpl ttsService;

    @Value("${digitalhuman.tts.timeout}")
    private Integer ttsTimeout;

    @Autowired
    private TtsServiceConfig ttsServiceConfig;

    @Autowired
    private MultilingualClient multilingualClient;

    @Autowired
    private StorageService storageService;

    private final Pattern pattern = Pattern.compile("<.+?>", Pattern.DOTALL);

    @Override
    public byte[] text2audioWithSplit(String language, String tex, TtsQueryParams ttsQueryParams) throws Exception {

        try {
            TtsDetailResponse detail = getTtsDetailResponse(language, tex, ttsQueryParams);
            return Base64.getDecoder().decode(detail.getAudioWavBase64());
        } catch (DigitalHumanCommonException e) {
            throw new DigitalHumanCommonException("试听失败", e);
        }
    }

    @Override
    public TtsDetailResponse getTtsDetailResponse(String language, String tex, TtsQueryParams ttsQueryParams)
            throws Exception {
        log.debug("getTtsDetailResponse text:{}, tts params:{}", tex, ttsQueryParams);
        CountDownLatch latch = new CountDownLatch(1);

        String errorMsg = "获取音频详情失败";
        String resultMsg = getI18nLanguageKey("key_get_audio_detail_Failed", language);
        if (!TextUtils.isEmpty(resultMsg)) {
            errorMsg = resultMsg;
        }

        AtomicReference<String> ttsErrorMessage = new AtomicReference<>(errorMsg);
        SubtitleTtsResult ttsResult;
        if (ttsQueryParams.getPer().startsWith("CM_")) {
            ttsResult = getResultFromCmww(tex, ttsQueryParams, latch, ttsErrorMessage);
        } else {
            ttsResult = getResultFromBaidu(language, tex, ttsQueryParams, latch, ttsErrorMessage);
        }
        return convertToResponse(ttsResult);
    }

    @Override
    public TtsDetailAndAudioResponse getTtsDetailAndAudioResponse(String language, String tex, TtsQueryParams ttsQueryParams)
            throws Exception {
        log.debug("getTtsDetailAndAudioResponse text:{},language:{}, tts params:{}", tex, language, ttsQueryParams);
        CountDownLatch latch = new CountDownLatch(1);

        String errorMsg = "获取音频详情失败";
        String resultMsg = getI18nLanguageKey("key_get_audio_detail_Failed", language);
        if (!TextUtils.isEmpty(resultMsg)) {
            errorMsg = resultMsg;
        }

        AtomicReference<String> ttsErrorMessage = new AtomicReference<>(errorMsg);
        SubtitleTtsResult ttsResult;
        if (ttsQueryParams.getPer().startsWith("CM_")) {
            ttsResult = getResultFromCmww(tex, ttsQueryParams, latch, ttsErrorMessage);
        } else {
            ttsResult = getResultFromBaidu(language, tex, ttsQueryParams, latch, ttsErrorMessage);
        }
        TtsDetailResponse detailResp = convertToResponse(ttsResult);

        String uuid = UUID.randomUUID().toString();
        String newFilename = uuid + ".wav";
        var audiourl = storageService.save(ttsResult.getAudio(), newFilename);
        log.debug("getTtsDetailAndAudioResponse audiourl:{}", audiourl.toString());

        List<TtsDetailResponse.SentenceDetail> detail = detailResp.getDetail();
        return TtsDetailAndAudioResponse.builder().totalDuration(detailResp.getTotalDuration())
                .audioWavBase64(detailResp.getAudioWavBase64())
                .audioUrl(audiourl.toString())
                .detail(detail).build();
    }

    private TtsDetailResponse convertToResponse(SubtitleTtsResult ttsResult) {
        AtomicInteger index = new AtomicInteger(-1);
        AtomicLong lastTimeout = new AtomicLong(0L);
        List<TtsDetailResponse.SentenceDetail> sentenceDetailList = ttsResult.getSubtitles().stream()
                .map(subtitle ->
                        TtsDetailResponse.SentenceDetail.builder()
                                .text(toPlainText(subtitle.getText()))
                                // 避免某些tts服务字幕时间跳过 silence
                                .duration(subtitle.getTimeOut() - lastTimeout.getAndSet(subtitle.getTimeOut()))
                                .index(index.incrementAndGet())
                                .build())
                .collect(Collectors.toList());

        String audioWavBase64 = Base64.getEncoder().encodeToString(ttsResult.getAudio());
        return TtsDetailResponse.builder().totalDuration((ttsResult.getAudio().length - 44) / 32L)
                .audioWavBase64(audioWavBase64)
                .detail(sentenceDetailList).build();

    }

    private SubtitleTtsResult getResultFromCmww(String tex, TtsQueryParams ttsQueryParams, CountDownLatch latch,
                                                AtomicReference<String> ttsErrorMessage)
            throws Exception {
        CmwwTtsClient client = new CmwwTtsClient(ttsServiceConfig.getCmwwConfig());
        TextSplitHelper textSplitHelper = new TextSplitHelper(ttsServiceConfig.getCmwwConfig().getTextSplitConfig());
        List<String> splitted = textSplitHelper.splitToChunks(tex,
                ttsServiceConfig.getCmwwConfig().getTextSplitConfig().getCmwwMaxLength(), null);
        List<SubtitleTtsResult> results = new ArrayList<>();
        // 考虑出门问问的qps限制和实际测试，这里先单并发调用
        for (String splitTex : splitted) {
            results.add(client.textToAudioWithSrt(splitTex, ttsQueryParams));
        }
        return mergeSubtitleTtsResult(results);
    }

    private SubtitleTtsResult mergeSubtitleTtsResult(List<SubtitleTtsResult> results) {
        List<Subtitle> subtitles = new ArrayList<>();

        // merge subtitle & count audio size
        int audioPcmSize = 0;
        for (SubtitleTtsResult result : results) {
            // 计算字幕偏移量, 16k/16bit
            int durationOffset = audioPcmSize / 16 / 2;
            result.getSubtitles().forEach(subtitle -> {
                subtitle.setTimeIn(subtitle.getTimeIn() + durationOffset);
                subtitle.setTimeOut(subtitle.getTimeOut() + durationOffset);
            });
            subtitles.addAll(result.getSubtitles());
            audioPcmSize += result.getAudio().length - 44;
        }

        // merge audio
        byte[] audio = new byte[audioPcmSize];
        int offset = 0;
        for (SubtitleTtsResult result : results) {
            System.arraycopy(result.getAudio(), 44, audio, offset, result.getAudio().length - 44);
            offset += result.getAudio().length - 44;
        }
        audio = WavUtil.attachHeader(audio, (short) 1, 16000, (short) 16);

        SubtitleTtsResult merged = new SubtitleTtsResult();
        merged.setSubtitles(subtitles);
        merged.setAudio(audio);
        return merged;
    }

    private SubtitleTtsResult getResultFromBaidu(String language, String tex, TtsQueryParams ttsQueryParams, CountDownLatch latch,
                                                 AtomicReference<String> ttsErrorMessage)
            throws InterruptedException {
        log.debug("getResultFromBaidu text:{}, tts params:{}", tex, ttsQueryParams);

        AtomicReference<TtsResult> ttsResult = new AtomicReference<>(new TtsResult());

        // 避免被指定使用wav/mp3等编码导致异常
        ttsQueryParams.setAue(null);

        Map<Integer, List<TtsStreamingResult>> streamingResultMap = new HashMap<>();
        if (!tex.startsWith("<speak")) {
            tex = "<speak>" + tex + "</speak>";
        }
        ttsService.textToAudio(tex, ttsQueryParams
                , (ttsStreamingResult) -> {

                    if (ttsStreamingResult.getErrCode() != 0) {
                        log.error("getResultFromBaidu,Fail to get partial result ,tts task cancelled,, errCode={}, errMsg={}",
                                ttsStreamingResult.getErrCode(), ttsStreamingResult.getErrMsg());
                        for (TtsStreamingResult.TTSError value : TtsStreamingResult.TTSError.values()) {
                            if (value.getCode() == ttsStreamingResult.getErrCode()) {
                                String errorMsg = value.getMessage();
                                if (ttsStreamingResult.getErrCode() == TtsStreamingResult.TTSError.TEXT_LENGTH_EXCEED_THRESHOLD.getCode()) {
                                    String resultMsg = getI18nLanguageKey("key_Fail_to_get_tts_result.text_exceeded_1024_bytes", language);
                                    if (!TextUtils.isEmpty(resultMsg)) {
                                        errorMsg = resultMsg;
                                    }
                                } else if (ttsStreamingResult.getErrCode() == TtsStreamingResult.TTSError.TTS_COMMON_ERROR.getCode()) {
                                    String resultMsg = getI18nLanguageKey("key_Fail_to_get_tts_result.get_tts_error", language);
                                    if (!TextUtils.isEmpty(resultMsg)) {
                                        errorMsg = resultMsg;
                                    }

                                } else if (ttsStreamingResult.getErrCode() == TtsStreamingResult.TTSError.TTS_COMMON_ERROR.getCode()) {
                                    String resultMsg = getI18nLanguageKey("key_Fail_to_get_tts_result.timbre_is_not_supported", language);
                                    if (!TextUtils.isEmpty(resultMsg)) {
                                        errorMsg = resultMsg;
                                    }
                                }
                                log.error("getResultFromBaidu,Fail to get partial result,getMultiLanguage,errCode={}, errMsg={}",
                                        ttsStreamingResult.getErrCode(), errorMsg);
                                ttsErrorMessage.set(errorMsg);
                                break;
                            }
                        }
                        latch.countDown();
                        return;
                    }

                    if (ttsStreamingResult.getText() != null) {
                        streamingResultMap.computeIfAbsent(ttsStreamingResult.getSentenceIndex(),
                                        k -> new ArrayList<>())
                                .add(ttsStreamingResult);
                    }

                    if (ttsStreamingResult.isCompleted()) {
                        log.debug("Tts streaming compute complete，audio size: {}",
                                ttsStreamingResult.getResult().getAudio().length);
                        var wavAudio = WavUtil.attachHeader(ttsStreamingResult.getResult().getAudio(),
                                (short) 1, 16000,
                                (short) 16);
                        ttsResult.get().setAudio(wavAudio);
                        latch.countDown();
                    }
                });
        if (!latch.await(ttsTimeout == null ? 10 : ttsTimeout, TimeUnit.SECONDS)) {
            log.warn("Tts overall timeout,limit = {}", ttsTimeout == null ? 10 : ttsTimeout);
        }
        if (ttsResult.get().getAudio() == null) {
            throw new DigitalHumanCommonException(ttsErrorMessage.get());
        }

        AtomicLong lastTime = new AtomicLong(0);
        List<Subtitle> subtitles =
                streamingResultMap.keySet().stream().sorted()
                        .map(index -> {
                            List<TtsStreamingResult> ttsStreamingResults = streamingResultMap.get(index);

                            Long splitDuration = ttsStreamingResults.stream()
                                    .map(streamingResult -> streamingResult.getResult().getAudio().length / 32L)
                                    .reduce(0L, Long::sum);

                            TtsStreamingResult ttsStreamingResult = ttsStreamingResults.get(0);
                            Subtitle subtitle = new Subtitle();
                            subtitle.setText(toPlainText(ttsStreamingResult.getText()));
                            subtitle.setTimeIn(lastTime.get());
                            subtitle.setTimeOut(lastTime.addAndGet(splitDuration));
                            return subtitle;
                        }).collect(Collectors.toList());

        SubtitleTtsResult result = new SubtitleTtsResult();
        result.setAudio(ttsResult.get().getAudio());
        result.setSubtitles(subtitles);
        return result;
    }

    private String toPlainText(String text) {
        return pattern.matcher(text).replaceAll("");
    }

    private String getI18nLanguageKey(String key, String language) {
        String consoleTtsErrorMsgLanguageKeySubType = "consonle.tts.error.msg";
        String serverName = "digital-human-console";
        String result = "";

        try {
            if (!TextUtils.isEmpty(language)) {
                List<String> keyList = new ArrayList<>();
                keyList.add(key);
                log.debug("getI18nLanguageKey start key:{},language:{}, keyList:{}", key, language, keyList);
                MultilingualResult lanRes =
                        multilingualClient.getMultilangStr(serverName, consoleTtsErrorMsgLanguageKeySubType, keyList, language);
                log.debug("getI18nLanguageKey end  key:{}, language:{}, MultilingualResult:{}", key, language, lanRes);
                if (lanRes.getErrCode() == 0) {
                    String msgI18n = lanRes.getLanguageMap().get(key);
                    if (!TextUtils.isEmpty(msgI18n)) {
                        result = msgI18n;
                    }
                }
            }
        } catch (Exception e) {
            log.error("getI18nLanguageKey failed key:{}, errorMsg:{}", key, e.getMessage());
        }
        return result;
    }
}
