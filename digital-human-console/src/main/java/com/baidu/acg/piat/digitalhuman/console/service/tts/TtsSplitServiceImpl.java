package com.baidu.acg.piat.digitalhuman.console.service.tts;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Base64;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.atomic.AtomicReference;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.baidu.acg.piat.digitalhuman.common.audio.WavUtil;
import com.baidu.acg.piat.digitalhuman.common.exception.DigitalHumanCommonException;
import com.baidu.acg.piat.digitalhuman.console.model.TtsDetailResponse;
import com.baidu.acg.piat.digitalhuman.tts.TtsServiceConfig;
import com.baidu.acg.piat.digitalhuman.tts.TtsStreamingResult;
import com.baidu.acg.piat.digitalhuman.tts.TtsStreamingService;
import com.baidu.acg.piat.digitalhuman.tts.model.SubtitleTtsResult;
import com.baidu.acg.piat.digitalhuman.tts.model.TtsQueryParams;
import com.baidu.acg.piat.digitalhuman.tts.model.TtsResult;
import com.baidu.acg.piat.digitalhuman.tts.util.CmwwTtsClient;
import com.baidu.acg.piat.digitalhuman.tts.util.TextSplitHelper;
import com.baidu.acg.piat.digitalhuman.tts.util.srt.Subtitle;

import lombok.extern.slf4j.Slf4j;

/**
 * ttsSplitServiceImpl
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class TtsSplitServiceImpl implements TtsSplitService {

    @Autowired
    @Qualifier("ttsStreamingService")
    private TtsStreamingService ttsServiceWithSplit;

    @Value("${digitalhuman.tts.timeout}")
    private Integer ttsTimeout;

    @Autowired
    private TtsServiceConfig ttsServiceConfig;

    private final Pattern pattern = Pattern.compile("<.+?>", Pattern.DOTALL);

    @Override
    public byte[] text2audioWithSplit(String tex, TtsQueryParams ttsQueryParams) throws Exception {

        if (ttsQueryParams.getExtraParams() != null && ttsQueryParams.getExtraParams().containsKey("figureSource")) {
            if (ttsQueryParams.getExtraParams() == null) {
                ttsQueryParams.setExtraParams(new HashMap<>());
            }
            // 后续可以放到配置文件中配置
            ttsQueryParams.getExtraParams().put("splitByPattern", "[。|?|？|!|！]");
        }

        try {
            TtsDetailResponse detail = getTtsDetailResponse(tex, ttsQueryParams);
            return Base64.getDecoder().decode(detail.getAudioWavBase64());
        } catch (DigitalHumanCommonException e) {
            throw new DigitalHumanCommonException("试听失败", e);
        }
    }

    @Override
    public TtsDetailResponse getTtsDetailResponse(String tex, TtsQueryParams ttsQueryParams)
            throws Exception {
        log.debug("getTtsDetailResponse text:{}, tts params:{}", tex, ttsQueryParams);
        CountDownLatch latch = new CountDownLatch(1);
        AtomicReference<String> ttsErrorMessage = new AtomicReference<>("获取音频详情失败");
        SubtitleTtsResult ttsResult;
        if (ttsQueryParams.getPer().startsWith("CM_")) {
            ttsResult = getResultFromCmww(tex, ttsQueryParams, latch, ttsErrorMessage);
        } else {
            ttsResult = getResultFromBaidu(tex, ttsQueryParams, latch, ttsErrorMessage);
        }
        return convertToResponse(ttsResult);
    }

    @Override
    public TtsDetailResponse getTtsDetailCaptionsResponse(String tex, TtsQueryParams ttsQueryParams)
            throws Exception {
        log.debug("getTtsDetailCaptionsResponse text:{}, tts params:{}", tex, ttsQueryParams);
        CountDownLatch latch = new CountDownLatch(1);
        AtomicReference<String> ttsErrorMessage = new AtomicReference<>("获取音频详情失败");
        SubtitleTtsResult ttsResult;
        if (ttsQueryParams.getPer().startsWith("CM_")) {
            ttsResult = getResultFromCmww(tex, ttsQueryParams, latch, ttsErrorMessage);
        } else {
            ttsResult = getResultFromBaidu(tex, ttsQueryParams, latch, ttsErrorMessage);
        }
        return convertToCaptionsResponse(ttsResult);
    }

    private TtsDetailResponse convertToResponse(SubtitleTtsResult ttsResult) {
        AtomicInteger index = new AtomicInteger(-1);
        AtomicLong lastTimeout = new AtomicLong(0L);
        List<TtsDetailResponse.SentenceDetail> sentenceDetailList = ttsResult.getSubtitles().stream()
                .map(subtitle ->
                        TtsDetailResponse.SentenceDetail.builder()
                                .text(toPlainText(subtitle.getText()))
                                // 避免某些tts服务字幕时间跳过 silence
                                .duration(subtitle.getTimeOut() - lastTimeout.getAndSet(subtitle.getTimeOut()))
                                .index(index.incrementAndGet())
                                .build())
                .collect(Collectors.toList());

        String audioWavBase64 = Base64.getEncoder().encodeToString(ttsResult.getAudio());
        return TtsDetailResponse.builder().totalDuration((ttsResult.getAudio().length - 44) / 32L)
                .audioWavBase64(audioWavBase64)
                .detail(sentenceDetailList).build();

    }

    private TtsDetailResponse convertToCaptionsResponse(SubtitleTtsResult ttsResult) {
        AtomicInteger index = new AtomicInteger(-1);
        AtomicLong lastTimeout = new AtomicLong(0L);
        List<TtsDetailResponse.SentenceDetail> sentenceDetailList = ttsResult.getSubtitles().stream()
                .map(subtitle ->
                        TtsDetailResponse.SentenceDetail.builder()
                                .text(toPlainText(subtitle.getText()))
                                // 避免某些tts服务字幕时间跳过 silence
                                .duration(subtitle.getTimeOut() - lastTimeout.getAndSet(subtitle.getTimeOut()))
                                .startTime(subtitle.getTimeIn())
                                .endTime(subtitle.getTimeOut())
                                .index(index.incrementAndGet())
                                .build())
                .collect(Collectors.toList());


        long audioLength = ttsResult.getAudio().length - 44;
        long remainder = audioLength % 1280;
        log.debug("convertToCaptionsResponse originAudioLength:{},remainder:{}", audioLength, remainder);
        if (remainder != 0) {
            // 不是1280的倍数，需补全
            long paddingLength = 1280 - remainder;
            byte[] padded = new byte[(int) (audioLength + paddingLength)];
            System.arraycopy(ttsResult.getAudio(), 44, padded, 0, ttsResult.getAudio().length - 44);
            log.debug("convertToCaptionsResponse audioLength:{}", padded.length);
            var wavAudio = WavUtil.attachHeader(padded,
                    (short) 1, 16000,
                    (short) 16);
            ttsResult.setAudio(wavAudio);
            log.debug("convertToCaptionsResponse wavLength:{}", ttsResult.getAudio().length);
        }

        String audioWavBase64 = Base64.getEncoder().encodeToString(ttsResult.getAudio());
        return TtsDetailResponse.builder().totalDuration((ttsResult.getAudio().length - 44) / 32L)
                .audioWavBase64(audioWavBase64)
                .detail(sentenceDetailList).build();

    }

    private SubtitleTtsResult getResultFromCmww(String tex, TtsQueryParams ttsQueryParams, CountDownLatch latch,
                                                AtomicReference<String> ttsErrorMessage)
            throws Exception {
        log.debug("getResultFromCmww text:{}, ttsQueryParams:{}", tex, ttsQueryParams);
        CmwwTtsClient client = new CmwwTtsClient(ttsServiceConfig.getCmwwConfig());
        TextSplitHelper textSplitHelper = new TextSplitHelper(ttsServiceConfig.getCmwwConfig().getTextSplitConfig());
        List<String> splitted = textSplitHelper.splitToChunks(tex,
                ttsServiceConfig.getCmwwConfig().getTextSplitConfig().getCmwwMaxLength(), null);
        List<SubtitleTtsResult> results = new ArrayList<>();

        try {
            // 考虑出门问问的qps限制和实际测试，这里先单并发调用
            for (String splitTex : splitted) {
                results.add(client.textToAudioWithSrt(splitTex, ttsQueryParams));
            }
        } catch (Exception e) {
            // 这里 client的解析异常直接抛到前端了，暂不修改client，拦截一下
            log.error("CMWW: Fail in tts client, err=" + e.getMessage());
            throw new IOException("音频合成失败，请检查语种及文本后重试");
        }
        return mergeSubtitleTtsResult(results, tex);
    }

    private SubtitleTtsResult mergeSubtitleTtsResult(List<SubtitleTtsResult> results, String text) throws Exception {
        List<Subtitle> subtitles = new ArrayList<>();
        log.debug("mergeSubtitleTtsResult text:{} , subtitles : {}", text, subtitles);

        // todo: 后续将合成服务集成到 tts-streaming 中统一处理出门问问错误问题，当前临时处理错误信息

        if (text != null && text.startsWith("<speak>")) {
            text = text.substring("<speak>".length());
        }
        // 1.合成为空
        if (results == null || results.isEmpty()) {
            log.error("CMWW: Fail to get tts result, result is empty");
            throw new IOException("音频合成失败，请检查语种及文本后重试");
        }

        // merge subtitle & count audio size
        int audioPcmSize = 0;
        for (SubtitleTtsResult result : results) {
            log.debug("mergeSubtitleTtsResult SubtitleTtsResult result :{}", result);
            // 2.合成的音频为空
            if (result.getAudio() == null || result.getAudio().length < 44) {
                log.error("CMWW: Fail to get tts audio, audio is empty");
                throw new IOException("音频合成失败，请检查语种及文本后重试");
            }

            // 3.合成结果的字幕为空
            if (result.getSubtitles() == null) {
                log.error("CMWW: Fail to get tts srt, srt is empty");
                throw new IOException("音频合成失败，请检查语种及文本后重试");
            }

            // 计算字幕偏移量, 16k/16bit
            int durationOffset = audioPcmSize / 16 / 2;
            result.getSubtitles().forEach(subtitle -> {
                subtitle.setTimeIn(subtitle.getTimeIn() + durationOffset);
                subtitle.setTimeOut(subtitle.getTimeOut() + durationOffset);
            });
            subtitles.addAll(mergeSubtitlesByParagraph(result.getSubtitles(), text));
            audioPcmSize += result.getAudio().length - 44;
        }

        // merge audio
        byte[] audio = new byte[audioPcmSize];
        int offset = 0;
        for (SubtitleTtsResult result : results) {
            System.arraycopy(result.getAudio(), 44, audio, offset, result.getAudio().length - 44);
            offset += result.getAudio().length - 44;
        }
        audio = WavUtil.attachHeader(audio, (short) 1, 16000, (short) 16);

        SubtitleTtsResult merged = new SubtitleTtsResult();
        merged.setSubtitles(subtitles);
        merged.setAudio(audio);
        return merged;
    }

    public List<Subtitle> mergeSubtitlesByParagraph(List<Subtitle> subtitles, String text) {
        log.debug("mergeSubtitlesByParagraph , subtitles: {} , text : {} ", subtitles, text);
        List<Subtitle> mergedList = new ArrayList<>();
        if (subtitles == null || subtitles.isEmpty()) {
            return mergedList;
        }

        for (Subtitle subtitle : subtitles) {

            int targetStart = text.indexOf(subtitle.getText());
            if (targetStart == -1) {
                mergedList.add(subtitle);
                continue;
            }
            boolean flag = isPrecedingCharPunctOrSpace(subtitle.getText(), text);
            log.debug("mergeSubtitlesByParagraph flag : {} , subText: {} , text : {} ", flag, subtitle.getText(), text);
            if (flag) {
                mergedList.add(subtitle);
                log.debug("mergeSubtitlesByParagraph mergedList : {}", subtitle);
            } else {
                Subtitle currentParagraph = mergedList.get(mergedList.size() - 1);
                currentParagraph.setText(currentParagraph.getText() + subtitle.getText());
                currentParagraph.setTimeOut(subtitle.getTimeOut());
                log.debug("mergeSubtitlesByParagraph currentParagraph : {}", currentParagraph);

            }
        }

        return mergedList;
    }

    private boolean isPrecedingCharPunctOrSpace(String subtitleText, String text) {
        if (subtitleText == null || text == null) {
            return true;
        }
        int targetStart = text.indexOf(subtitleText);
        if (targetStart == -1) {
            return false;
        }

        if (targetStart == 0) {
            return true;
        }

        char precedingChar = text.charAt(targetStart - 1);
        String ch = String.valueOf(precedingChar);
        log.debug("isPrecedingCharPunctOrSpace precedingChar : {} , ch : {} , splitFlags : {}", precedingChar, ch,
                ttsServiceConfig.getCmwwConfig().getTextSplitConfig().getSplitFlags());
        return ttsServiceConfig.getCmwwConfig().getTextSplitConfig().getSplitFlags().contains(ch);
    }

    private SubtitleTtsResult getResultFromBaidu(String tex, TtsQueryParams ttsQueryParams, CountDownLatch latch,
                                                 AtomicReference<String> ttsErrorMessage)
            throws InterruptedException {
        log.debug("getResultFromBaidu text:{}, tts params:{}", tex, ttsQueryParams);

        AtomicReference<TtsResult> ttsResult = new AtomicReference<>(new TtsResult());

        // 避免被指定使用wav/mp3等编码导致异常
        ttsQueryParams.setAue(null);

        Map<Integer, List<TtsStreamingResult>> streamingResultMap = new HashMap<>();
        if (!tex.startsWith("<speak")) {
            tex = "<speak>" + tex + "</speak>";
        }
        ttsServiceWithSplit.textToAudio(tex, ttsQueryParams
                , (ttsStreamingResult) -> {
                    if (ttsStreamingResult.getErrCode() != 0) {
                        log.warn("Fail to get partial result ,tts task cancelled, errMsg={}",
                                ttsStreamingResult.getErrMsg());
                        for (TtsStreamingResult.TTSError value : TtsStreamingResult.TTSError.values()) {
                            if (value.getCode() == ttsStreamingResult.getErrCode()) {
                                ttsErrorMessage.set(value.getMessage());
                                break;
                            }
                        }
                        latch.countDown();
                        return;
                    }

                    if (ttsStreamingResult.getText() != null) {
                        streamingResultMap.computeIfAbsent(ttsStreamingResult.getSentenceIndex(),
                                        k -> new ArrayList<>())
                                .add(ttsStreamingResult);
                    }

                    if (ttsStreamingResult.isCompleted()) {
                        log.debug("Tts streaming compute complete，audio size: {}",
                                ttsStreamingResult.getResult().getAudio().length);
                        var wavAudio = WavUtil.attachHeader(ttsStreamingResult.getResult().getAudio(),
                                (short) 1, 16000,
                                (short) 16);
                        ttsResult.get().setAudio(wavAudio);
                        latch.countDown();
                    }
                });
        if (!latch.await(ttsTimeout == null ? 10 : ttsTimeout, TimeUnit.SECONDS)) {
            log.warn("Tts overall timeout,limit = {}", ttsTimeout == null ? 10 : ttsTimeout);
        }
        if (ttsResult.get().getAudio() == null) {
            throw new DigitalHumanCommonException(ttsErrorMessage.get());
        }

        AtomicLong lastTime = new AtomicLong(0);
        List<Subtitle> subtitles =
                streamingResultMap.keySet().stream().sorted()
                        .map(index -> {
                            List<TtsStreamingResult> ttsStreamingResults = streamingResultMap.get(index);

                            Long splitDuration = ttsStreamingResults.stream()
                                    .map(streamingResult -> streamingResult.getResult().getAudio().length / 32L)
                                    .reduce(0L, Long::sum);

                            TtsStreamingResult ttsStreamingResult = ttsStreamingResults.get(0);
                            Subtitle subtitle = new Subtitle();
                            subtitle.setText(toPlainText(ttsStreamingResult.getText()));
                            subtitle.setTimeIn(lastTime.get());
                            subtitle.setTimeOut(lastTime.addAndGet(splitDuration));
                            return subtitle;
                        }).collect(Collectors.toList());

        SubtitleTtsResult result = new SubtitleTtsResult();
        result.setAudio(ttsResult.get().getAudio());
        result.setSubtitles(subtitles);
        return result;
    }

    private String toPlainText(String text) {
        return pattern.matcher(text).replaceAll("");
    }
}
