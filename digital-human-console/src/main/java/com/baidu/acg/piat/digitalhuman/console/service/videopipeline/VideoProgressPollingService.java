// Copyright (C) 2020 Baidu Inc. All rights reserved.

package com.baidu.acg.piat.digitalhuman.console.service.videopipeline;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.BiConsumer;

import com.baidu.acg.piat.digitalhuman.common.console.video.ProgressResult;
import com.baidu.acg.piat.digitalhuman.common.exception.DigitalHumanCommonException;
import com.baidu.acg.piat.digitalhuman.console.config.VideoPipelineConfigure;
import com.baidu.acg.piat.digitalhuman.videopipeline.client.VideoProgressHttpClient;

/**
 * VideoCompositeProgressPollingService
 *
 * <AUTHOR> Zhensheng(<EMAIL>)
 * @since 2020-02-10
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class VideoProgressPollingService {

    private final VideoPipelineConfigure.ProgressConfig pollingConfig;

    private final VideoProgressHttpClient httpClient;

    private Map<String, List<BiConsumer<ProgressResult, DigitalHumanCommonException>>> pollingCallbacks = new ConcurrentHashMap<>();

    private Map<String, AtomicInteger> notExistedCounter = new ConcurrentHashMap<>();

    /**
     * 加入polling的任务必须是已经确认开始执行的。
     *
     * @param videoId
     * @param callback
     */
    public void addPollingTask(String videoId, BiConsumer<ProgressResult, DigitalHumanCommonException> callback) {
        var callbacks = pollingCallbacks.computeIfAbsent(videoId, k -> new CopyOnWriteArrayList<>());
        callbacks.add(callback);
    }

    public void removePollingTask(String videoId, BiConsumer<ProgressResult, DigitalHumanCommonException> callback) {
        var callbacks = pollingCallbacks.get(videoId);
        callbacks.remove(callback);
    }


    @Scheduled(fixedRateString = "${digitalhuman.videopipeline.progress.pollIntervalMillis:2000}")
    public void fixRatePolling() {
        List<String> pollingIds = new ArrayList<>(pollingCallbacks.keySet());

        if (CollectionUtils.isEmpty(pollingIds)) {
            return;
        }

        log.info("Polling videoIds {}...", pollingIds);
        var response = httpClient.pollBatchProgress(pollingIds);
        if (!response.isSuccess()) {
            log.error("Fail to poll video progress of videoIds={} ", pollingIds, response.getMessage());
            return;
        }
        var videos = response.getResult();

        /**/
        videos.forEach(video -> {
            pollingIds.remove(video.getVideoId());

            if (video.judgeFinishStatus()) {
                var callbacks = pollingCallbacks.remove(video.getVideoId());
                callbacks.forEach(callback -> {
                    try {
                        callback.accept(video, null);
                    } catch (DigitalHumanCommonException e) {
                        log.error("Fail to call polling task callback of video={}", video.getVideoId(), e);
                        callback.accept(null, e);
                    }
                });
            }
        });

        /*leftIds */
        pollingIds.forEach(leftId -> {
            var counter = notExistedCounter.computeIfAbsent(leftId, k -> new AtomicInteger(0));
            if (counter.incrementAndGet() >= pollingConfig.getNotExistedTTL()) {
                log.error("Fail to accept the vis start flag fo video={}, close the polling task", leftId);
                var callbacks = pollingCallbacks.remove(leftId);
                callbacks.forEach(callback -> {
                    try {
                        callback.accept(null, new DigitalHumanCommonException("start video task timeout"));
                    } catch (Exception e) {
                        log.error("Never reach here video={}", leftId, e);
                    }
                });
                notExistedCounter.remove(leftId);
            }
        });

    }
}
