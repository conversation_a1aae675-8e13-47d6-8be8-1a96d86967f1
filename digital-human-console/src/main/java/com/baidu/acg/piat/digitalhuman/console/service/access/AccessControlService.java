package com.baidu.acg.piat.digitalhuman.console.service.access;

import com.baidu.acg.piat.digitalhuman.common.access.AccessApp;
import com.baidu.acg.piat.digitalhuman.common.model.PageResponse;

/**
 * @Author: <EMAIL>
 * @Date: 2020/3/24
 */

public interface AccessControlService {

    AccessApp createApp(AccessApp request);

    void deleteApp(String uid, int apiVersion, String appId);

    AccessApp updateApp(AccessApp request);

    AccessApp getApp(String appId);

    /**
     * 根据人像类型获取默认的 App
     *
     * @param characterImage
     * @return
     */
    AccessApp getDefaultAppByImage(String characterImage, int apiVersion);

    PageResponse<AccessApp> listAll(String appId, String userId, String name, int apiVersion, int pageNo, int pageSize);

    PageResponse<AccessApp> listAll(String appId, String userId, String name, String visibleCharacters,
                                    int apiVersion, int pageNo, int pageSize);
    
    PageResponse<AccessApp> listAll(String appId, String userId, String name,
                                    String visibleCharacters, String projectIds,
                                    int apiVersion, int pageNo, int pageSize);


}
