// Copyright (C) 2020 Baidu Inc. All rights reserved.

package com.baidu.acg.piat.digitalhuman.console.service.videopipeline;

import com.baidu.acg.piat.digitalhuman.common.cloud.Selectors;
import io.grpc.Channel;
import io.grpc.ClientInterceptors;
import io.grpc.ManagedChannelBuilder;
import io.vavr.control.Try;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.DependsOn;
import org.springframework.stereotype.Service;

import javax.annotation.Nonnull;
import javax.annotation.PostConstruct;

import com.baidu.acg.piat.digitalhuman.common.constans.RenderOpenParameters;
import com.baidu.acg.piat.digitalhuman.common.project.FigureCutParams;
import com.baidu.acg.piat.digitalhuman.common.project.TtsParams;
import com.baidu.acg.piat.digitalhuman.common.console.video.Text2VideoParams;
import com.baidu.acg.piat.digitalhuman.common.project.Project;
import com.baidu.acg.piat.digitalhuman.common.util.JsonUtil;
import com.baidu.acg.piat.digitalhuman.console.config.VideoPipelineConfigure;
import com.baidu.acg.piat.digitalhuman.plat.client.PlatformClient;
import com.baidu.acg.piat.digitalhuman.tracer.utils.TraceContext;
import com.baidu.acg.pie.digitalhuman.client.DhClient;
import com.baidu.acg.pie.digitalhuman.client.DhClientImpl;
import com.baidu.acg.pie.digitalhuman.client.config.ClientConfig;

/**
 * VideoDhClientFactory
 *
 * <AUTHOR> Zhensheng(<EMAIL>)
 * @since 2020-12-07
 */
@Slf4j
@Service
@DependsOn("traceContext")
@RequiredArgsConstructor
public class VideoDhClientFactory {

    private final PlatformClient platformClient;

    private final VideoPipelineConfigure.CloudConfig cloudConfig;

    private final ExecutorService callbackExecutor = Executors.newFixedThreadPool(10);

    private Channel channel;

    @PostConstruct
    public void init() {

        channel = ClientInterceptors.intercept(ManagedChannelBuilder
                        .forAddress(cloudConfig.getHost(), cloudConfig.getPort())
                        .keepAliveTime(cloudConfig.getGrpcKeepAliveTimeSeconds(), TimeUnit.SECONDS)
                        .keepAliveTimeout(cloudConfig.getGrpcKeepAliveTimeoutSeconds(), TimeUnit.SECONDS)
                        .usePlaintext().build(),
                TraceContext.grpcClientInterceptor());
    }

    public DhClient createDhClient(String appId, String appKey, Text2VideoParams textRequest) {

        ClientConfig config = buildClientConfigOfOutputVideo(appId, appKey, textRequest);
        // 通过心跳保证视频合成过程中，不被maxIdle机制清理。
        config.setHeartbeatEnabled(true);

        return new DhClientImpl(config, channel);
    }

    private ClientConfig buildClientConfigOfOutputVideo(String appId, String appKey,
            Text2VideoParams request) {
        var config = ClientConfig.builder()
                .appId(appId)
                .appKey(appKey)
                .serverHost(cloudConfig.getHost())
                .serverPort(cloudConfig.getPort())
                .heartbeatEnabled(true)
                .callbackExecutor(callbackExecutor)
                .parameters(new HashMap<>())
                .labelSelectors(convertSelectors(request.getSelectors()))
                .build();

        config.getParameters().put(RenderOpenParameters.output_target.name(), "video");

        if (StringUtils.isBlank(request.getProjectId())) {
            request.setProjectId(Try.of(() -> platformClient.getApp(appId).getProjectId())
                    .onFailure(t -> log.error("Fail to get app info from platform, appId={}", appId, t))
                    .getOrNull());
        }

        if (StringUtils.isNoneBlank(request.getProjectId())) {
            config.getParameters().putAll(parseClientConfigParamsFromProject(request.getProjectId()));
            Project project = platformClient.getProjectById(request.getProjectId());
            if (project != null) {
                // project如果绑定了character_config_id，需要把characterConfig放到RenderOpenParameters里
                Optional.ofNullable(project.getCharacterConfigId())
                        .filter(id -> !StringUtils.isEmpty(id))
                        .map(id -> platformClient.retrieveCharacterConfig(id).getConfig())
                        .ifPresent(c -> config.getParameters().put(RenderOpenParameters.characterConfig.name(), c));
            }
        }

        Optional.ofNullable(request.getCharacterConfig())
            .or(() -> Optional.ofNullable(request.getCharacterConfigId())
                .map(id -> Try.of(() -> platformClient.retrieveCharacterConfig(id).getConfig()).getOrNull()))
            .ifPresent(c -> config.getParameters().put(RenderOpenParameters.characterConfig.name(), c));

        if (!StringUtils.isEmpty(request.getBackgroundImageUrl())) {
            config.setBackgroundImageUrl(request.getBackgroundImageUrl());
        }

        if (Objects.nonNull(request.getResolutionWidth())) {
            config.setResolutionWidth(request.getResolutionWidth());
        }

        if (Objects.nonNull(request.getResolutionHeight())) {
            config.setResolutionHeight(request.getResolutionHeight());
        }
        if (null != request.getSubtitleParams() && request.getSubtitleParams().isEnabled()) {
            config.getParameters().put(RenderOpenParameters.ttsAudioInfoToUpStreamEnable.name(), "true");
        }
        if (request.getTtsParams() != null) {
            TtsParams ttsParams = request.getTtsParams();
            if (ttsParams.getPitch() != null) {
                config.getParameters().put("ttsPitch", String.valueOf(ttsParams.getPitch()));
            }
            if (ttsParams.getVolume() != null) {
                config.getParameters().put("ttsVolume", String.valueOf(ttsParams.getVolume()));
            }
            if (ttsParams.getSpeed() != null) {
                config.getParameters().put("ttsSpeed", String.valueOf(ttsParams.getSpeed()));
            }
            if (ttsParams.getPerson() != null) {
                config.getParameters().put("ttsPerson", String.valueOf(ttsParams.getPerson()));
            }
            if (ttsParams.getExtraParams() != null) {
                config.getParameters().put("ttsExtraParams", JsonUtil.writeValueAsStringNoThrow(ttsParams.getExtraParams()));
            }
        }

        if (request.getFigureCutParams() != null) {
            FigureCutParams figureParams = request.getFigureCutParams();
            if (figureParams.getCutXPercent() != null) {
                config.getParameters().put("figureCutXPercent", String.valueOf(figureParams.getCutXPercent()));
            }
            if (figureParams.getCutYPercent() != null) {
                config.getParameters().put("figureCutYPercent", String.valueOf(figureParams.getCutYPercent()));
            }
            if (figureParams.getCutWidthPercent() != null) {
                config.getParameters().put("figureCutWidthPercent", String.valueOf(figureParams.getCutWidthPercent()));
            }
            if (figureParams.getCutHeightPercent() != null) {
                config.getParameters()
                        .put("figureCutHeightPercent", String.valueOf(figureParams.getCutHeightPercent()));
            }
            if (figureParams.getWidthRatio() != null) {
                config.getParameters().put("figureWidthRatio", String.valueOf(figureParams.getWidthRatio()));
            }
            if (figureParams.getPositionCenterXPercent() != null) {
                config.getParameters()
                        .put("figurePositionCenterXPercent", String.valueOf(figureParams.getPositionCenterXPercent()));
            }
            if (figureParams.getPositionBottomYPercent() != null) {
                config.getParameters()
                        .put("figurePositionBottomYPercent", String.valueOf(figureParams.getPositionBottomYPercent()));
            }
        }

        if (StringUtils.isNoneBlank(request.getPreset())) {
            config.getParameters().put("preset", request.getPreset());
        }

        // 临时render params > project
        if (Objects.nonNull(request.getRenderParams())) {
            config.getParameters().putAll(request.getRenderParams());
        }
        return config;
    }

    private Map<String, String> parseClientConfigParamsFromProject(@Nonnull String projectId) {
        Project project = platformClient.getProjectById(projectId);
        Map<String, String> renderParams = project.toRenderParams();
        log.info("Render params: {} from projectId:{}", renderParams, projectId);
        return renderParams;
    }

    private com.baidu.acg.pie.digitalhuman.client.config.Selectors convertSelectors(Selectors selectors) {
        if (null == selectors) {
            return new com.baidu.acg.pie.digitalhuman.client.config.Selectors();
        }
        List<com.baidu.acg.pie.digitalhuman.client.config.Selectors.Selector> filters = Optional
                .ofNullable(selectors.getFilters()).map(f ->
                        f.stream().map(selector -> com.baidu.acg.pie.digitalhuman.client.config.Selectors.Selector
                                .builder().operator(com.baidu.acg.pie.digitalhuman.client.config.Selectors.Operator
                                        .valueOf(selector.getOperator().name()))
                                .label(selector.getLabel())
                                .data(selector.getData())
                                .build()).collect(Collectors.toList())
                ).orElse(new ArrayList<>());
        List<com.baidu.acg.pie.digitalhuman.client.config.Selectors.Selector> comparators = Optional
                .ofNullable(selectors.getComparators()).map(f ->
                        f.stream().map(selector -> com.baidu.acg.pie.digitalhuman.client.config.Selectors.Selector
                                .builder().operator(com.baidu.acg.pie.digitalhuman.client.config.Selectors.Operator
                                        .valueOf(selector.getOperator().name()))
                                .label(selector.getLabel())
                                .data(selector.getData())
                                .build()).collect(Collectors.toList())
                ).orElse(new ArrayList<>());
        return com.baidu.acg.pie.digitalhuman.client.config.Selectors.builder()
                .filters(filters)
                .comparators(comparators)
                .build();
    }
}
