package com.baidu.bce.plat.webframework.exception;

import java.util.Objects;

/**
 * Created on 2020/5/28 12:10.
 *
 * <AUTHOR>
 */
public class BceException extends RuntimeException {
    private int httpStatus = 400;
    private String code;
    private String requestId;

    public BceException(String message) {
        super(message);
        String name = this.getClass().getSimpleName();
        if (this.getClass().getName().contains("$")) {
            String[] parts = getClass().getCanonicalName().split("\\.");
            name = parts[parts.length - 2] + "." + parts[parts.length - 1];
        }
        this.code = name;
    }

    public BceException(String message, int httpStatus) {
        this(message);
        this.httpStatus = httpStatus;
    }

    public BceException(String message, String code) {
        super(message);
        this.code = code;
    }

    public BceException(String message, int httpStatus, String code) {
        super(message);
        this.httpStatus = httpStatus;
        this.code = code;
    }

    @Override
    public String toString() {
        return "BecResponseException{" +
                "httpStatus='" + httpStatus + '\'' +
                ", requestId='" + requestId + '\'' +
                ", code='" + code + '\'' +
                ", message='" + getMessage() + "\'" +
                '}';
    }

    public int getHttpStatus() {
        return httpStatus;
    }

    public void setHttpStatus(int httpStatus) {
        this.httpStatus = httpStatus;
    }

    public String getRequestId() {
        return requestId;
    }

    public void setRequestId(String requestId) {
        this.requestId = requestId;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }

        BceException that = (BceException) o;

        if (httpStatus != that.httpStatus) {
            return false;
        }
        if (!Objects.equals(code, that.code)) {
            return false;
        }
        return Objects.equals(requestId, that.requestId);
    }

    @Override
    public int hashCode() {
        int result = httpStatus;
        result = 31 * result + (requestId != null ? requestId.hashCode() : 0);
        result = 31 * result + (code != null ? code.hashCode() : 0);
        return result;
    }
}
