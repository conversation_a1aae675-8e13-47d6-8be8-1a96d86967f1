
ALTER TABLE `digital_human_plat`.`project`
ADD COLUMN `alita_params` varchar(256) COMMENT 'alita_params' AFTER `hit_shield_reply`,
ADD COLUMN `camera`VARCHAR(256) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT 'camera params' AFTER `character_image`;


ALTER TABLE `project` ADD COLUMN `logo_uid` varchar(100) NOT NULL DEFAULT '' COMMENT 'logo uid' AFTER `background_image_url`;
ALTER TABLE `project` ADD COLUMN `logo_url` varchar(1024) NOT NULL DEFAULT '' COMMENT 'logo url' AFTER `logo_uid`;
ALTER TABLE `project` ADD COLUMN `thumbnail_url` VARCHAR(1024) NOT NULL DEFAULT '' COMMENT 'thumbnail url' AFTER `project_version`;

ALTER TABLE `project` ADD COLUMN `asr_part_event` VARCHAR(256) NOT NULL DEFAULT '' COMMENT 'asr_part_event' AFTER `alita_params`;
ALTER TABLE `project` ADD COLUMN `character_config_id` VARCHAR(256) NOT NULL DEFAULT '' COMMENT 'character_config_id' AFTER `preset`;
ALTER TABLE `project` ADD COLUMN `character_config_name` VARCHAR(256) NOT NULL DEFAULT '' COLLATE utf8mb4_unicode_ci NULL COMMENT 'character_config_name' AFTER `character_config_id`;
ALTER TABLE `project` ADD COLUMN `figure_alias` VARCHAR(256) NOT NULL DEFAULT '' COLLATE utf8mb4_unicode_ci NULL COMMENT 'figure_alias' AFTER `character_image`;
ALTER TABLE `project` ADD COLUMN `api_version` TINYINT(4) NOT NULL DEFAULT 1 COMMENT 'api version';

ALTER TABLE `project` DROP INDEX `uk_user_id_name_project_version`;
ALTER TABLE `project` DROP INDEX `idx_user_id`;
ALTER TABLE `project` ADD UNIQUE KEY `uk_user_id_name_project_version_api_version` (`user_id`,`name`,`project_version`,`api_version`);
ALTER TABLE `project` ADD KEY `idx_user_id_is_default_api_version_character_image` (`user_id`,`is_default`,`api_version`,`character_image`);