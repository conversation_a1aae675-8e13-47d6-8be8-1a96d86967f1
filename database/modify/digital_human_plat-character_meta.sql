
ALTER TABLE `digital_human_plat`.`character_meta`
ADD COLUMN `camera_list` VARCHAR(512) NOT NULL DEFAULT '' COMMENT 'camera list' AFTER `style`,
ADD COLUMN `hair_style_list` VARCHAR(512) NOT NULL DEFAULT '' COMMENT 'hair style list' AFTER `camera_list`,
ADD COLUMN `clothing_style_list` VARCHAR(512) NOT NULL DEFAULT '' COMMENT 'clothing style list' AFTER `hair_style_list`,
ADD COLUMN `badge_style_list` VARCHAR(256) NOT NULL DEFAULT '' COMMENT 'badge style list' AFTER `clothing_style_list`,
ADD COLUMN `shoe_style_list` VARCHAR(256) NOT NULL DEFAULT '' COMMENT 'shoe style list' AFTER `badge_style_list`,
MODIFY COLUMN `animoji_list` MEDIUMTEXT NOT NULL DEFAULT '' COMMENT 'animoji list' AFTER `badge_style`,
ADD COLUMN `emotion_list` VARCHAR(1024) NOT NULL DEFAULT '' COMMENT 'emotion list' AFTER `animoji_list`,
ADD COLUMN `support_callback` TINYINT NOT NULL DEFAULT 0 COMMENT '是否支持回调(0：不支持，1：支持)' AFTER `animojis`,
ADD COLUMN `support_rtc_datachannel` TINYINT NOT NULL DEFAULT 0 COMMENT '是否支持该 data_channel(0：不支持，1：支持)' AFTER `support_callback`,
ADD COLUMN `visible_for_sce` TINYINT NOT NULL DEFAULT 0 COMMENT '是否对SCE可见(0：不支持，1：支持)' AFTER `support_rtc_datachannel`,
ADD COLUMN `label` VARCHAR(20) NOT NULL DEFAULT '' COMMENT 'character label (2D or 3D)' AFTER `visible_for_sce`,
ADD COLUMN `app_id` VARCHAR(40) NOT NULL DEFAULT '' COMMENT 'default appId' AFTER `label`,
ADD COLUMN `app_key` VARCHAR(40) NOT NULL DEFAULT '' COMMENT 'default appKey' AFTER `app_id`;
ADD COLUMN scene_list VARCHAR(512) NOT NULL DEFAULT '' COMMENT 'scene list' AFTER camera_list,
ADD COLUMN tts_list VARCHAR(512) NOT NULL DEFAULT '' COMMENT 'tts list' AFTER camera_list;

ALTER TABLE `digital_human_plat`.`character_meta` MODIFY COLUMN `animoji_list` TEXT NULL COMMENT 'animoji list' AFTER `camera_list`,
MODIFY COLUMN `hair_style_list` TEXT NULL COMMENT 'hair_style_list' AFTER `camera_list`,
MODIFY COLUMN `clothing_style_list` TEXT NULL COMMENT 'clothing_style_list' AFTER `camera_list`,
MODIFY COLUMN `badge_style_list` TEXT NULL COMMENT 'badge_style_list' AFTER `camera_list`,
MODIFY COLUMN `shoe_style_list` TEXT NULL COMMENT 'shoe_style_list' AFTER `camera_list`,
MODIFY COLUMN `emotion_list` TEXT NULL COMMENT 'emotion_list' AFTER `camera_list`,
MODIFY COLUMN `facial_list` TEXT NULL COMMENT 'facial_list' AFTER `camera_list`,
MODIFY COLUMN `makeup_list` TEXT NULL COMMENT 'makeup_list' AFTER `camera_list`,
MODIFY COLUMN `scene_list` TEXT NULL COMMENT 'scene_list' AFTER `camera_list`,
ADD COLUMN `visible_for_live` TINYINT NOT NULL DEFAULT 0 COMMENT '是否对直播可见(0：不支持，1：支持)' AFTER `visible_for_sce`;

ALTER TABLE `digital_human_plat`.`character_meta`
ADD COLUMN `base_ar_url` VARCHAR(1024) COLLATE utf8mb4_unicode_ci NULL COMMENT 'base_ar_url',
ADD COLUMN `base_ar_md5` VARCHAR(100) COLLATE utf8mb4_unicode_ci NULL COMMENT 'base_ar_md5',
ADD COLUMN `resource_url` VARCHAR(1024) COLLATE utf8mb4_unicode_ci NULL COMMENT '形象文件',
ADD COLUMN `md5` VARCHAR(100) COLLATE utf8mb4_unicode_ci NULL COMMENT '形象文件的md5';

ALTER TABLE `digital_human_plat`.`character_meta`
ADD COLUMN `deploy_version` int(11) NOT NULL DEFAULT 0 COMMENT 'deploy version' AFTER `enable_look_front`;