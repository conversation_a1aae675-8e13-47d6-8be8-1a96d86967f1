USE digital_human_plat;

DROP TABLE IF EXISTS `live_config`;
CREATE TABLE `live_config` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `config_id` varchar(100) DEFAULT NULL,
  `character_name` VARCHAR(100) DEFAULT NULL,
  `name` varchar(100) DEFAULT NULL,
  `user_id` varchar(100) DEFAULT NULL,
  `resolution_height` int(11) DEFAULT NULL COMMENT '分辨率-高',
  `resolution_width` int(11) DEFAULT NULL COMMENT '分辨率-宽',
  `bitrate` int(11) DEFAULT NULL COMMENT '码率',
  `rtmp_url_list` varchar(4096) DEFAULT NULL,
  `render_type` varchar(50) DEFAULT NULL COMMENT '渲染类型：CLOUD/WORKSTATION',
  `video_directory_id` varchar(100) DEFAULT NULL,
  `workstation_label` varchar(100) DEFAULT NULL,
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'create time',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'last update time',
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  UNIQUE KEY `config_id` (`config_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='直播设置';

ALTER TABLE `live_config`
ADD COLUMN `character_config_id` varchar(100) NULL DEFAULT '' COMMENT 'character config id' AFTER `character_name`;