USE digital_human_plat;

DROP TABLE IF EXISTS `access_app`;
CREATE TABLE `access_app` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `app_id` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT 'app id',
  `app_key` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT 'app key',
  `user_id` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT 'user id',
  `name` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT 'name',
  `description` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT 'description',
  `project_id` varchar(100) COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT 'project id',
  `project_name` varchar(100) COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT 'project name',
  `project_version` varchar(40) COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT 'project version',
  `tags` varchar(1024) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT 'tags',
  `resource_quota` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT 'resource quota',
  `max_idle_in_second` int(11) NOT NULL DEFAULT '30' COMMENT 'max idle in second',
  `character_image` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT 'character image',
  `enabled` tinyint(4) NOT NULL DEFAULT '1' COMMENT 'enabled',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'create time',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'last update time',
  `version` int(11) NOT NULL COMMENT 'for optimistic locking',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_app_id` (`app_id`),
  KEY `idx_user_id_name` (`user_id`,`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='access app table';

DROP TABLE IF EXISTS `room`;
CREATE TABLE `room` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `room_id` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'room id',
  `user_id` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT 'user id',
  `app_id` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT 'app id',
  `room_name` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT 'room name',
  `status` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'status',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'create time',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'last update time',
  `version` int(11) NOT NULL COMMENT 'for optimistic locking',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_room_id` (`room_id`),
  KEY `idx_user_id_room_name` (`user_id`,`room_name`),
  KEY `idx_app_id_room_name` (`app_id`,`room_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='room table';

DROP TABLE IF EXISTS `room_session`;
CREATE TABLE `room_session` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `room_session_id` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT 'room session id',
  `session_id` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT 'session id',
  `room_id` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT 'room id',
  `app_id` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT 'app id',
  `app_token` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT 'app token',
  `project_id` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT 'project id',
  `status` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT 'status',
  `duration` bigint(20) NOT NULL DEFAULT '0' COMMENT 'duration',
  `time_stamp` bigint(20) NOT NULL DEFAULT '0' COMMENT 'time stamp',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'create time',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'last update time',
  `version` int(11) NOT NULL COMMENT 'for optimistic locking',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_room_session_id` (`room_session_id`),
  KEY `idx_session_id` (`session_id`),
  KEY `idx_room_id` (`room_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='room session table';

DROP TABLE IF EXISTS `dialog`;
CREATE TABLE `dialog` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `dialog_id` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT 'dialog id',
  `app_id` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT 'app id',
  `session_id` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT 'session id',
  `room_id` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT 'room id',
  `audio_id` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'audio_id',
  `speaker` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT 'speaker',
  `type` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT 'type',
  `content` varchar(7168) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT 'content',
  `time_stamp` bigint(20) NOT NULL DEFAULT '0' COMMENT 'time stamp',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'create time',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'last update time',
  `version` int(11) NOT NULL COMMENT 'for optimistic locking',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_dialog_id` (`dialog_id`),
  KEY `idx_app_timestamp` (`app_id`,`time_stamp`),
  KEY `idx_session_timestamp` (`session_id`,`time_stamp`),
  KEY `idx_room_timestamp` (`room_id`,`time_stamp`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='dialog table';

DROP TABLE IF EXISTS `background`;
CREATE TABLE `background` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `background_id` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT 'background id',
  `user_id` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT 'user id',
  `name` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT 'name',
  `description` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT 'description',
  `is_live_bg` TINYINT NOT NULL DEFAULT 0 COMMENT '区分形象管理和直播管理模块上传(0：形象，1：直播)',
  `url` varchar(1024) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT 'url',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'create time',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'last update time',
  `version` int(11) NOT NULL COMMENT 'for optimistic locking',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_background_id` (`background_id`),
  KEY `idx_user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='background table';

DROP TABLE IF EXISTS `character_meta`;
CREATE TABLE `character_meta` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `character_id` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT 'character id',
  `type` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT 'type',
  `name` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT 'name',
  `description` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT 'description',
  `style` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT 'style',
  `support_callback` TINYINT NOT NULL DEFAULT 0 COMMENT '是否支持回调(0：不支持，1：支持)',
  `support_rtc_datachannel` TINYINT NOT NULL DEFAULT 0 COMMENT '是否支持该 data_channel(0：不支持，1：支持)',
  `visible_for_sce` TINYINT NOT NULL DEFAULT 0 COMMENT '是否对SCE可见(0：不支持，1：支持)',
  `visible_for_live` TINYINT NOT NULL DEFAULT 0 COMMENT '是否对直播可见(0：不支持，1：支持)',
  `visible_for_vis_mocap` TINYINT NOT NULL DEFAULT 0 COMMENT '是否支持vis动面捕(0：不支持，1：支持)',
  `label` VARCHAR(20) NOT NULL DEFAULT '' COMMENT 'character label (2D or 3D)',
  `app_id` VARCHAR(40) NOT NULL DEFAULT '' COMMENT 'default appId',
  `app_key` VARCHAR(40) NOT NULL DEFAULT '' COMMENT 'default appKey',
  `tts_list` VARCHAR(512) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT 'tts list',
  `camera_list` VARCHAR(512) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT 'camera list',
  `hair_style_list` TEXT COLLATE utf8mb4_unicode_ci NULL COMMENT 'hair style list',
  `clothing_style_list` TEXT COLLATE utf8mb4_unicode_ci NULL COMMENT 'clothing style list',
  `badge_style_list` TEXT COLLATE utf8mb4_unicode_ci NULL COMMENT 'badge style list',
  `shoe_style_list` TEXT COLLATE utf8mb4_unicode_ci NULL COMMENT 'shoe style list',
  `hair_style` varchar(512) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT 'hair style',
  `clothing_style` varchar(512) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT 'clothing style',
  `badge_style` TEXT COLLATE utf8mb4_unicode_ci NULL COMMENT 'badge style',
  `animoji_list` TEXT COLLATE utf8mb4_unicode_ci NULL COMMENT 'animoji list',
  `emotion_list` TEXT COLLATE utf8mb4_unicode_ci NULL COMMENT 'emotion list',
  `facial_list` TEXT COLLATE utf8mb4_unicode_ci NULL COMMENT 'facial list',
  `makeup_list` TEXT COLLATE utf8mb4_unicode_ci NULL COMMENT 'makeup list',
  `scene_list` TEXT COLLATE utf8mb4_unicode_ci NULL COMMENT 'scene list',
  `front_image_url` varchar(1024) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT 'front image url',
  `mask_image_url` varchar(1024) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT 'mask image url',
  `background_image_url` varchar(1024) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT 'background image url',
  `thumbnail_image_url` varchar(1024) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT 'thumbnail image url',
  `enable_look_front` TINYINT NOT NULL DEFAULT 0 COMMENT '人像是否支持面向前方',
  `deploy_version` int(11) NOT NULL DEFAULT 0 COMMENT 'deploy version',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'create time',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'last update time',
  `version` int(11) NOT NULL COMMENT 'for optimistic locking',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_type` (`type`),
  KEY `uk_character_id` (`character_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='character meta table';

DROP TABLE IF EXISTS `project`;
CREATE TABLE `project` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `project_id` varchar(100) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT 'project id',
  `name` varchar(100) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT 'name',
  `description` varchar(100) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT 'description',
  `user_id` varchar(100) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT 'user id',
  `is_default` tinyint(4) NOT NULL DEFAULT '0' COMMENT 'is default',
  `project_version` varchar(40) COLLATE utf8mb4_bin NOT NULL DEFAULT '1.0.0' COMMENT 'project version',
  `preset` varchar(1024) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT 'preset',
  `background_image_id` varchar(100) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT 'background image id',
  `background_image_url` varchar(1024) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT 'background image url',
  `thumbnail_url` varchar(1024) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT 'thumbnail url',
  `bot_params` varchar(256) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT 'bot params',
  `tts_params` varchar(256) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT 'tts params',
  `character_image` varchar(100) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT 'character image',
  `resolution_params` varchar(256) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT 'resolution params',
  `camera`VARCHAR(256) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT 'camera params',
  `figure_cut_params` varchar(1024) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT 'figure cut params',
  `character_params` varchar(1024) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT 'character params',
  `paint_chart_on_picture_params` varchar(512) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT 'paint chart on picture params',
  `paint_subtitle_on_picture_params` varchar(256) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT 'paint subtitle on picture params',
  `media_output` varchar(256) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT 'media output',
  `conversation_config_id` bigint(20) DEFAULT NULL COMMENT 'conversation config id',
  `hot_words` mediumtext COLLATE utf8mb4_bin NOT NULL COMMENT 'hot_words',
  `user_inactive` varchar(256) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT 'user inactive',
  `prologue_params` varchar(512) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT 'prologue params',
  `hit_shield_reply` varchar(1024) COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT 'hit shield reply',
  `alita_params` varchar(256) COLLATE utf8mb4_bin DEFAULT NULL COMMENT 'alita_params',
  `asr_part_event` varchar(256) COLLATE utf8mb4_bin DEFAULT '' NOT NULL COMMENT 'asr_part_event',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'create time',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'last update time',
  `version` int(11) NOT NULL COMMENT 'for optimistic locking',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_id_name_project_version` (`user_id`,`name`,`project_version`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_project_id` (`project_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='project table';

DROP TABLE IF EXISTS `richtext_config`;
CREATE TABLE `richtext_config` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `config_id` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT 'config id',
  `project_id` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT 'config id',
  `content` varchar(7168) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT 'content',
  `download_url` varchar(1024) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT 'download url',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'create time',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'last update time',
  `version` int(11) NOT NULL COMMENT 'for optimistic locking',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_config_id` (`config_id`),
  KEY `idx_project_id` (`project_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='richtext config table';

DROP TABLE IF EXISTS `conversation_config`;
CREATE TABLE `conversation_config` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `pullback_switch` tinyint(4) NOT NULL DEFAULT '0' COMMENT 'pullback switch',
  `pullback_trigger_round` int(11) NOT NULL DEFAULT '0' COMMENT 'pullback trigger round',
  `pullback_config_list` varchar(2048) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT 'pullback config list',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='conversation config table';

DROP TABLE IF EXISTS `common_kv`;
CREATE TABLE `common_kv` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `user_id` varchar(100) DEFAULT NULL,
  `k` varchar(100) DEFAULT NULL COMMENT '键',
  `value` varchar(4096) DEFAULT NULL COMMENT '值',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'create time',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'last update time',
  PRIMARY KEY (`id`),
  UNIQUE KEY `user_key_uni` (`user_id`,`k`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='通用键值对，for FE';

DROP TABLE IF EXISTS `live_config`;
CREATE TABLE `live_config` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `config_id` varchar(100) DEFAULT NULL,
  `character_name` VARCHAR(100) DEFAULT NULL,
  `character_config_id` VARCHAR(100) DEFAULT NULL,
  `name` varchar(100) DEFAULT NULL,
  `user_id` varchar(100) DEFAULT NULL,
  `resolution_height` int(11) DEFAULT NULL COMMENT '分辨率-高',
  `resolution_width` int(11) DEFAULT NULL COMMENT '分辨率-宽',
  `bitrate` int(11) DEFAULT NULL COMMENT '码率',
  `rtmp_url_list` varchar(4096) DEFAULT NULL,
  `render_type` varchar(50) DEFAULT NULL COMMENT '渲染类型：CLOUD/WORKSTATION',
  `video_directory_id` varchar(100) DEFAULT NULL,
  `workstation_label` varchar(100) DEFAULT NULL,
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'create time',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'last update time',
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  UNIQUE KEY `config_id` (`config_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='直播设置';

DROP TABLE IF EXISTS `material`;
CREATE TABLE `material` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
  `material_id` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT 'material_id',
  `user_id` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT 'user_id',
  `position_id` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT 'position_id',
  `name` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT 'name',
  `type` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT 'type',
  `pic_url` varchar(1024) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT 'pic_url',
  `content` varchar(4096) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT 'json',
  `drml` varchar(4096) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT 'drml',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'create time',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'last update time',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='material table';

DROP TABLE IF EXISTS `position`;
CREATE TABLE `position` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
  `position_id` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT 'position_id',
  `user_id` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT 'user_id',
  `name` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT 'name',
  `content` varchar(4096) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT 'json',
  `width` double COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 0 COMMENT 'width',
  `height` double COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 0 COMMENT 'height',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'create time',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'last update time',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_position_id` (`position_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='position table';

DROP TABLE IF EXISTS `property`;
CREATE TABLE `property` (
	`id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
	`name` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT 'name',
	`property_id` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT 'property id',
	`type` varchar(40) NOT NULL DEFAULT '' COMMENT 'type',
	`character_id` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT 'character id',
	`create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'create time',
    `front_image_url` varchar(1024) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT 'front image url',
	`property_url` varchar(1024) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT 'property url',
	`frame_count` bigint(20) NOT NULL DEFAULT 0 COMMENT 'frame count(animoji)',
	`enable` TINYINT NOT NULL DEFAULT 1 COMMENT '资产是否可用 （1 可用 0 已被删除）',
	`md5` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT 'md5',
	`property_json` varchar(3096) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '参数类素材的骨骼信息',
	`basic_resource` TINYINT NOT NULL DEFAULT 0 COMMENT '1:基础资源 0:扩展资源',
	`dh_drml` varchar(1024) COLLATE utf8mb4_unicode_ci NULL COMMENT 'dh_drml',
	PRIMARY KEY (`id`),
  	UNIQUE KEY `idx_property_id_character` (`property_id`,`character_id`),
  	KEY `idx_character_id` (`character_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='资产列表（基础模型、资源类资产、参数类资产）';
