CREATE DATABASE IF NOT EXISTS `digital_human_video_pipeline`;
USE digital_human_video_pipeline;

DROP TABLE IF EXISTS `video_progress`;
CREATE TABLE `video_progress` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `video_id` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'video id',
  `app_id` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'app id',
  `session_id` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'session_id',
  `user_id` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT 'user id',
  `user_name` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT 'user name',
  `project_name` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT 'project name',
  `code` int(11) NOT NULL COMMENT 'code',
  `status` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'status',
  `message` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'message',
  `download_url` varchar(1024) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT 'download url',
  `active_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'active time',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'create time',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'last update time',
  `version` int(11) NOT NULL COMMENT 'for optimistic locking',
  `text` varchar(3000) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT 'text',
  `submit_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'submit_time',
  `last_schedule_start_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'last_schedule_start_time',
  `last_schedule_finish_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'last_schedule_finish_time',
  `video_generate_finish_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'video_generate_finish_time',
  `video_generate_cost_millis` bigint(20) DEFAULT NULL COMMENT 'video_generate_cost_millis',
  `scheduled_times` int(11) NOT NULL COMMENT 'scheduled_times',
  `retry_policy` varchar(500) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'retry_policy',
  `submit_params` varchar(3000) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'submit_params',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_video_id` (`video_id`),
  KEY `idx_app_status` (`app_id`,`status`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='video progress table';


