spring:
  profiles:
    active: production
  cache:
    type: caffeine
    cache-names:
      - characterBackgroundImage
      - characterMaskImage
      - characterBackgroundImageResized
      - characterMaskImageResized
    caffeine:
      spec: expireAfterWrite=30m

digitalhuman:
  videopipeline:
    material:
      character:
        scheme: https
        host: digital-human-material.bj.bcebos.com
        rootPath: character
        maskSubDirectory: mask
        maskImageExtension: jpg
        backgroundImageName: background.jpg
        fragmentIndexLength: 5
      video:
        fragmentIndexLength: 8
        rootPath: ./material/video/
        imageFileExtension: jpg
        imageSubDirectory: image
        audioSubDirectory: audio
        audioFileExtension: wav
        audioFormatSampleRate: 16000
        audioFormatSampleSizeInBits: 16
        audioFormatChannels: 1
        audioFormatSigned: True
        audioFormatBigEndian: False
        videoFileExtension: mp4
    bos:
      accessKeyId: 931b804661f2462a9ae36eea84357241
      secretAccessKey: 334e16982621474ea29cac75ba5d8cb8
      bucket: digital-human-pipeline-output
      retryTimes: 1
      retrySleepMillis: 100
      urlExpireSeconds: 2592000
    ffmpeg:
      frameRate: 25
      exeTimeoutSeconds: 20
    characterthreadpool:
      corePoolSize: 16
      maxCorePoolSize: 17
    grpc:
      config:
        port: 8090
        terminateAwaitSeconds: 3
        keepAliveTimeMinutes: 30