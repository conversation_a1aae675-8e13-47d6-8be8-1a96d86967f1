package com.baidu.acg.piat.digitalhuman.videopipeline.proxy.controller;

import com.baidu.acg.piat.digitalhuman.common.model.Response;
import com.baidu.acg.piat.digitalhuman.videopipeline.proxy.service.VideoSubmitRecordService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequiredArgsConstructor
@RequestMapping("/api/digitalhuman/video/v1/")
public class StatisticsController {

    private final VideoSubmitRecordService videoSubmitRecordService;

    @GetMapping("/topCharacters")
    public Response<List<VideoSubmitRecordService.CharacterResponse>> getTopUseCharacters(@RequestParam("userId") String userId) {
        return Response.success(videoSubmitRecordService.getUserRecent3UseCharacter(userId));
    }

}
