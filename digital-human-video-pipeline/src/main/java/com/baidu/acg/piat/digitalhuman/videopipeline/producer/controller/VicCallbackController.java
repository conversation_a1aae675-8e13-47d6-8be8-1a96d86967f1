package com.baidu.acg.piat.digitalhuman.videopipeline.producer.controller;

import com.baidu.acg.piat.digitalhuman.common.model.Response;
import com.baidu.acg.piat.digitalhuman.common.utils.JsonUtil;
import com.baidu.acg.piat.digitalhuman.videopipeline.producer.VicVideoProducer;
import com.baidu.acg.piat.digitalhuman.videopipeline.producer.client.VicVideoClient;
import io.vavr.control.Try;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequiredArgsConstructor
@RequestMapping("/api/digitalhuman/callback/vic")
@Slf4j
public class VicCallbackController {

    private final VicVideoProducer vicVideoProducer;
    @PostMapping
    public Response callback(@RequestBody VicVideoClient.VicResponse vicResponse) {
        log.debug("receive vic callback vicResponse: {}", vicResponse);
        vicVideoProducer.vicVideoResultCallback(getVicResponse(vicResponse));
        return Response.success();
    }

    private VicVideoClient.ValidVicResponse getVicResponse(VicVideoClient.VicResponse vicResponse) {
        String value = vicResponse.getFeatureResult().getValue();
        VicVideoClient.ValidVicResponse validVicResponse =
                Try.of(() -> JsonUtil.readValue(value, VicVideoClient.ValidVicResponse.class))
                        .onFailure(e -> log.error("parse vic response error", e))
                        .getOrNull();
        log.debug("valid vic response: {}", validVicResponse);
        return validVicResponse;
    }

}
