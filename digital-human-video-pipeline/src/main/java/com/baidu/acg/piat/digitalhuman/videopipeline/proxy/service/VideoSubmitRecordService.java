package com.baidu.acg.piat.digitalhuman.videopipeline.proxy.service;

import lombok.Builder;
import lombok.Data;

import java.util.List;

public interface VideoSubmitRecordService {

    public void recordOneSubmitTask(String userId, String figureId);

    public List<CharacterResponse> getUserRecent3UseCharacter(String userId);

    @Data
    @Builder
    public static class CharacterResponse {
        private String characterId;

        private String characterImageUrl;

        private String performanceStage;
    }
}
