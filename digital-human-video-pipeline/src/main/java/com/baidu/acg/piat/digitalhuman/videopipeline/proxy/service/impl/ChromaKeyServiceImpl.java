package com.baidu.acg.piat.digitalhuman.videopipeline.proxy.service.impl;

import java.io.BufferedReader;
import java.io.BufferedWriter;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.UUID;

import javax.annotation.PostConstruct;

import com.baidu.acg.piat.digitalhuman.common.micro.chromakey.ChromakeyReqParams;
import com.baidu.acg.piat.digitalhuman.common.micro.chromakey.ChromakeyRespBody;
import com.baidu.acg.piat.digitalhuman.common.model.Response;
import com.baidu.acg.piat.digitalhuman.storage.service.StorageService;
import com.baidu.acg.piat.digitalhuman.videopipeline.proxy.client.MicroChromaClient;
import com.baidu.acg.piat.digitalhuman.videopipeline.proxy.persistence.VideoProgressModel;
import org.apache.directory.api.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;

import com.baidu.acg.piat.digitalhuman.common.exception.DigitalHumanCommonException;
import com.baidu.acg.piat.digitalhuman.videopipeline.config.ChromakeyConfigure;
import com.baidu.acg.piat.digitalhuman.videopipeline.model.wrapper.FFmpegWrapper;
import com.baidu.acg.piat.digitalhuman.videopipeline.proxy.service.ChromaKeyService;

import io.vavr.control.Try;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * ChromaKeyServiceImpl
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ChromaKeyServiceImpl implements ChromaKeyService {

    private final FFmpegWrapper ffmpegWrapper;

    private final ChromakeyConfigure.ChromakeyConfig chromakeyConfig;

    @Autowired
    private MicroChromaClient microChromaClient;

    private final StorageService storageService;

    @PostConstruct
    public void init() {
        if (chromakeyConfig.getVertexShaderTemplatePath() == null) {
            copyFromClassPath("chromakey_vertex_shader.glsl", "/tmp/chromakey_vertex_shader.glsl");
            chromakeyConfig.setVertexShaderTemplatePath("/tmp/chromakey_vertex_shader.glsl");
        }

        if (chromakeyConfig.getFragmentShaderTemplatePath() == null) {
            copyFromClassPath("chromakey_fragment_shader.glsl", "/tmp/chromakey_fragment_shader.glsl");
            chromakeyConfig.setFragmentShaderTemplatePath("/tmp/chromakey_fragment_shader.glsl");
        }

        if (chromakeyConfig.getVertexShaderTemplatePathV2() == null) {
            copyFromClassPath("chromakey_vertex_shader_v2.glsl", "/tmp/chromakey_vertex_shader_v2.glsl");
            chromakeyConfig.setVertexShaderTemplatePathV2("/tmp/chromakey_vertex_shader_v2.glsl");
        }

        if (chromakeyConfig.getFragmentShaderTemplatePathV2() == null) {
            copyFromClassPath("chromakey_fragment_shader_v2.glsl", "/tmp/chromakey_fragment_shader_v2.glsl");
            chromakeyConfig.setFragmentShaderTemplatePathV2("/tmp/chromakey_fragment_shader_v2.glsl");
        }

        if (chromakeyConfig.getVertexPostShaderTemplatePathV2() == null) {
            copyFromClassPath("chromakey_v2_post_vertex.glsl", "/tmp/chromakey_v2_post_vertex.glsl");
            chromakeyConfig.setVertexPostShaderTemplatePathV2("/tmp/chromakey_v2_post_vertex.glsl");
        }

        if (chromakeyConfig.getFragmentPostShaderTemplatePathV2() == null) {
            copyFromClassPath("chromakey_v2_post_shader.glsl", "/tmp/chromakey_v2_post_shader.glsl");
            chromakeyConfig.setFragmentPostShaderTemplatePathV2("/tmp/chromakey_v2_post_shader.glsl");
        }
    }

    private void copyFromClassPath(String classpathLocation, String dstPath) {
        ClassPathResource resource = new ClassPathResource(classpathLocation);

        try (InputStream inputStream = resource.getInputStream();
             OutputStream outputStream = new FileOutputStream(dstPath)) {
            byte[] buffer = new byte[4096];
            int bytesRead;
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                outputStream.write(buffer, 0, bytesRead);
            }
        } catch (IOException e) {
            throw new RuntimeException("Failed to copy resource file from classpath to " + dstPath, e);
        }
    }

    @Override
    public Path chromaKeyV1(Path videoPath, ChromaKeyParamV1 param) {
        Path tmpVertexShaderPath = null;
        try {
            Path vertexShaderTemplatePath = Try.of(
                    () -> Path.of(chromakeyConfig.getVertexShaderTemplatePath())
            ).getOrElseThrow(e -> new DigitalHumanCommonException("Vertex shader template not found", e));

            tmpVertexShaderPath = Try.of(
                    () -> constructVertexShader(param, vertexShaderTemplatePath)
            ).getOrElseThrow(e -> new DigitalHumanCommonException("Fail to construct vertex shader", e));

            if (!Files.exists(tmpVertexShaderPath)) {
                throw new DigitalHumanCommonException("Vertex shader file not found: " + tmpVertexShaderPath);
            }

            Path fragmentShaderPath = Try.of(
                    () -> Path.of(chromakeyConfig.getFragmentShaderTemplatePath())
            ).getOrElseThrow(e -> new DigitalHumanCommonException("Fragment shader not found", e));

            return ffmpegWrapper.chromakey(param.getWidth(),
                    param.getHeight(),
                    videoPath,
                    tmpVertexShaderPath,
                    fragmentShaderPath, null, null);
        } catch (Exception e) {
            throw new DigitalHumanCommonException("Exception when chroma key", e);
        } finally {
            try {
                if (tmpVertexShaderPath != null && !chromakeyConfig.isDebug()) {
                    Files.deleteIfExists(tmpVertexShaderPath);
                }
            } catch (IOException e) {
                log.warn("Fail to delete vertex shader file: {}", tmpVertexShaderPath, e);
            }
        }
    }

    @Override
    public boolean microChromaKeyV1(Path videoPath, ChromaKeyParamV1 param, VideoProgressModel progress) {
        Path tmpVertexShaderPath = null;
        try {
            Path vertexShaderTemplatePath = Try.of(
                    () -> Path.of(chromakeyConfig.getVertexShaderTemplatePath())
            ).getOrElseThrow(e -> new DigitalHumanCommonException("Vertex shader template not found", e));

            tmpVertexShaderPath = Try.of(
                    () -> constructVertexShader(param, vertexShaderTemplatePath)
            ).getOrElseThrow(e -> new DigitalHumanCommonException("Fail to construct vertex shader", e));

            if (!Files.exists(tmpVertexShaderPath)) {
                throw new DigitalHumanCommonException("Vertex shader file not found: " + tmpVertexShaderPath);
            }

            Path fragmentShaderPath = Try.of(
                    () -> Path.of(chromakeyConfig.getFragmentShaderTemplatePath())
            ).getOrElseThrow(e -> new DigitalHumanCommonException("Fragment shader not found", e));

//            ffmpegWrapper.chromakey(param.getWidth(),
//                    param.getHeight(),
//                    videoPath,
//                    tmpVertexShaderPath,
//                    fragmentShaderPath, null, null);

            // 把 shaders 上传到BOS上
            ChromakeyReqParams chromakeyParams = new ChromakeyReqParams();
            try {
                // 如果有配置好的URL就不用再上传了
                var tempShaderUrl = chromakeyConfig.getFragmentShaderTemplatePathUrl();
                if (!Strings.isEmpty(tempShaderUrl)) {
                    log.info("Use local fragment shader: {}", tempShaderUrl);
                    chromakeyParams.setFragmentShader(tempShaderUrl);
                } else {
                    var tempNewShaderUrl = storageService.save(fragmentShaderPath);
                    chromakeyParams.setFragmentShader(tempNewShaderUrl.toString());
                }

            } catch (StorageService.ResourceException e) {
                log.error("Fail to upload shader of videoId={}, shader: {}", progress.getVideoId()
                        , fragmentShaderPath, e);
                throw new DigitalHumanCommonException("Failed to upload shader");
            }
            try {
                var tempShaderUrl = storageService.save(tmpVertexShaderPath);
                chromakeyParams.setVertexShader(tempShaderUrl.toString());
            } catch (StorageService.ResourceException e) {
                log.error("Fail to upload shader of videoId={}, shader: {}", progress.getVideoId()
                        , tmpVertexShaderPath, e);
                throw new DigitalHumanCommonException("Failed to upload shader");
            }
            Response<ChromakeyRespBody> response = microChromaClient.submitChromakeyTask(progress, "v1",
                    param.getWidth(),
                    param.getHeight(), chromakeyParams);
            if (!response.isSuccess()) {
                log.error("Fail to submit videoId={}, resp={}", progress.getVideoId(), response);
                throw new DigitalHumanCommonException("Failed to submit micro chromakey");
            }
            log.info("Success to submit videoId={} to micro-chromakey-v1", progress.getVideoId());
            return true;
        } catch (Exception e) {
            throw new DigitalHumanCommonException("Exception when chroma key", e);
        } finally {
            try {
                if (tmpVertexShaderPath != null && !chromakeyConfig.isDebug()) {
                    Files.deleteIfExists(tmpVertexShaderPath);
                }
            } catch (IOException e) {
                log.warn("Fail to delete vertex shader file: {}", tmpVertexShaderPath, e);
            }
        }
    }

    private Path constructVertexShader(ChromaKeyParamV1 param, Path vertexShaderPath) throws IOException {
        /*
         dynamic insert params in Java:

        const vec4 screen=vec4(28./255.,179./255.,110./255.,1.);
        const float screenWeight=1.;
        const float balance=1.;
        const float clipBlack=0.;
        const float clipWhite=1.;
        const vec2 resolution=vec2(1080.,1920.);
        */
        File templateFile = vertexShaderPath.toFile();
        Path result = Path.of("/tmp", UUID.randomUUID() + "-" + templateFile.getName());

        try (BufferedReader reader = Files.newBufferedReader(vertexShaderPath);
             BufferedWriter writer = Files.newBufferedWriter(result);
        ) {
            writer.write("const vec4 screen = vec4("
                    + param.getScreen()[0] / 255.0f + ","
                    + param.getScreen()[1] / 255.0f + ","
                    + param.getScreen()[2] / 255.0f + ", 1.0);");
            writer.newLine();
            writer.write("const float screenWeight = " + param.getWeight() + ";");
            writer.newLine();
            writer.write("const float balance = " + param.getBalance() + ";");
            writer.newLine();
            writer.write("const float clipBlack = " + param.getClipBlack() + ";");
            writer.newLine();
            writer.write("const float clipWhite = " + param.getClipWhite() + ";");
            writer.newLine();
            writer.write("const vec2 resolution = vec2("
                    + (float) param.getWidth() + ","
                    + (float) param.getHeight() + ");");
            writer.newLine();

            // copy from template
            String line;
            while ((line = reader.readLine()) != null) {
                writer.write(line);
                writer.newLine();
            }
        } catch (Exception e) {
            throw e;
        }
        return result;
    }

    @Override
    public Path chromaKeyV2(Path videoPath, ChromaKeyParamV2 param) {

        if (param.getEdgeShrink() == null) {
            param.setEdgeShrink(3.0f);
        }

        Path tmpVertexShaderPath = null;
        Path tmpPostVertexShaderPath = null;
        try {
            Path vertexShaderTemplatePath = Try.of(() -> Path.of(chromakeyConfig.getVertexShaderTemplatePathV2()))
                    .getOrElseThrow(e -> new DigitalHumanCommonException("V2 vertex shader template not found", e));

            tmpVertexShaderPath = Try.of(
                    () -> constructVertexShaderV2(param, vertexShaderTemplatePath)
            ).getOrElseThrow(e -> new DigitalHumanCommonException("Fail to construct v2 vertex shader", e));

            if (!Files.exists(tmpVertexShaderPath)) {
                throw new DigitalHumanCommonException("V2 Vertex shader file not found: " + tmpVertexShaderPath);
            }

            Path fragmentShaderPath = Try.of(() -> Path.of(chromakeyConfig.getFragmentShaderTemplatePathV2()))
                    .getOrElseThrow(e -> new DigitalHumanCommonException("V2 fragment shader not found", e));

            Path postVertexTemplatePath = Try.of(() -> Path.of(chromakeyConfig.getVertexPostShaderTemplatePathV2()))
                    .getOrElseThrow(e -> new DigitalHumanCommonException("V2 post vertex template not found", e));

            tmpPostVertexShaderPath = Try.of(
                    () -> constructPostVertexShaderV2(param, postVertexTemplatePath)
            ).getOrElseThrow(e -> new DigitalHumanCommonException("Fail to construct v2 post vertex", e));

            if (!Files.exists(tmpPostVertexShaderPath)) {
                throw new DigitalHumanCommonException("V2 post vertex file not found: " + tmpPostVertexShaderPath);
            }

            Path postFragmentShaderPath = Try.of(() -> Path.of(chromakeyConfig.getFragmentPostShaderTemplatePathV2()))
                    .getOrElseThrow(e -> new DigitalHumanCommonException("V2 post fragment shader not found", e));

            return ffmpegWrapper.chromakey(param.getWidth(),
                    param.getHeight(),
                    videoPath,
                    tmpVertexShaderPath,
                    fragmentShaderPath,
                    tmpPostVertexShaderPath,
                    postFragmentShaderPath
            );
        } catch (Exception e) {
            throw new DigitalHumanCommonException("Exception when chroma key v2", e);
        } finally {
            try {
                if (tmpVertexShaderPath != null && !chromakeyConfig.isDebug()) {
                    Files.deleteIfExists(tmpVertexShaderPath);
                }
            } catch (IOException e) {
                log.warn("Fail to delete v2 vertex shader file: {}", tmpVertexShaderPath, e);
            }

            try {
                if (tmpPostVertexShaderPath != null && !chromakeyConfig.isDebug()) {
                    Files.deleteIfExists(tmpPostVertexShaderPath);
                }
            } catch (IOException e) {
                log.warn("Fail to delete v2 post vertex shader file: {}", tmpPostVertexShaderPath, e);
            }
        }
    }

    @Override
    public boolean microChromaKeyV2(Path videoPath, ChromaKeyParamV2 param, VideoProgressModel progress) {
        if (param.getEdgeShrink() == null) {
            param.setEdgeShrink(3.0f);
        }

        Path tmpVertexShaderPath = null;
        Path tmpPostVertexShaderPath = null;
        try {
            Path vertexShaderTemplatePath = Try.of(() -> Path.of(chromakeyConfig.getVertexShaderTemplatePathV2()))
                    .getOrElseThrow(e -> new DigitalHumanCommonException("V2 vertex shader template not found", e));

            tmpVertexShaderPath = Try.of(
                    () -> constructVertexShaderV2(param, vertexShaderTemplatePath)
            ).getOrElseThrow(e -> new DigitalHumanCommonException("Fail to construct v2 vertex shader", e));

            if (!Files.exists(tmpVertexShaderPath)) {
                throw new DigitalHumanCommonException("V2 Vertex shader file not found: " + tmpVertexShaderPath);
            }

            Path fragmentShaderPath = Try.of(() -> Path.of(chromakeyConfig.getFragmentShaderTemplatePathV2()))
                    .getOrElseThrow(e -> new DigitalHumanCommonException("V2 fragment shader not found", e));

            Path postVertexTemplatePath = Try.of(() -> Path.of(chromakeyConfig.getVertexPostShaderTemplatePathV2()))
                    .getOrElseThrow(e -> new DigitalHumanCommonException("V2 post vertex template not found", e));

            tmpPostVertexShaderPath = Try.of(
                    () -> constructPostVertexShaderV2(param, postVertexTemplatePath)
            ).getOrElseThrow(e -> new DigitalHumanCommonException("Fail to construct v2 post vertex", e));

            if (!Files.exists(tmpPostVertexShaderPath)) {
                throw new DigitalHumanCommonException("V2 post vertex file not found: " + tmpPostVertexShaderPath);
            }

            Path postFragmentShaderPath = Try.of(() -> Path.of(chromakeyConfig.getFragmentPostShaderTemplatePathV2()))
                    .getOrElseThrow(e -> new DigitalHumanCommonException("V2 post fragment shader not found", e));

//            ffmpegWrapper.chromakey(param.getWidth(),
//                    param.getHeight(),
//                    videoPath,
//                    tmpVertexShaderPath,
//                    fragmentShaderPath,
//                    tmpPostVertexShaderPath,
//                    postFragmentShaderPath
//            );

            // 把 shaders 上传到BOS上
            ChromakeyReqParams chromakeyParams = new ChromakeyReqParams();
            try {
                // 如果有配置好的URL就不用再上传了
                var tempShaderLocalUrl = chromakeyConfig.getFragmentShaderTemplatePathV2Url();
                if (!Strings.isEmpty(tempShaderLocalUrl)) {
                    log.info("Use local V2 fragment shader: {}", tempShaderLocalUrl);
                    chromakeyParams.setFragmentShader(tempShaderLocalUrl);
                } else {
                    var tempShaderUrl = storageService.save(fragmentShaderPath);
                    chromakeyParams.setFragmentShader(tempShaderUrl.toString());
                }
            } catch (StorageService.ResourceException e) {
                log.error("Fail to upload shader of videoId={}, shader: {}", progress.getVideoId()
                        , fragmentShaderPath, e);
                throw new DigitalHumanCommonException("Failed to upload shader");
            }
            try {
                var tempShaderUrl = storageService.save(tmpVertexShaderPath);
                chromakeyParams.setVertexShader(tempShaderUrl.toString());
            } catch (StorageService.ResourceException e) {
                log.error("Fail to upload shader of videoId={}, shader: {}", progress.getVideoId()
                        , tmpVertexShaderPath, e);
                throw new DigitalHumanCommonException("Failed to upload shader");
            }
            try {
                var tempShaderLocalUrl = chromakeyConfig.getFragmentPostShaderTemplatePathV2Url();
                if (!Strings.isEmpty(tempShaderLocalUrl)) {
                    log.info("Use local V2 post fragment shader: {}", tempShaderLocalUrl);
                    chromakeyParams.setPostFragmentShader(tempShaderLocalUrl);
                } else {
                    var tempShaderUrl = storageService.save(postFragmentShaderPath);
                    chromakeyParams.setPostFragmentShader(tempShaderUrl.toString());
                }
            } catch (StorageService.ResourceException e) {
                log.error("Fail to upload shader of videoId={}, shader: {}", progress.getVideoId()
                        , postFragmentShaderPath, e);
                throw new DigitalHumanCommonException("Failed to upload shader");
            }
            try {
                var tempShaderUrl = storageService.save(tmpPostVertexShaderPath);
                chromakeyParams.setPostVertexShader(tempShaderUrl.toString());
            } catch (StorageService.ResourceException e) {
                log.error("Fail to upload shader of videoId={}, shader: {}", progress.getVideoId()
                        , tmpPostVertexShaderPath, e);
                throw new DigitalHumanCommonException("Failed to upload shader");
            }
            Response<ChromakeyRespBody> response = microChromaClient.submitChromakeyTask(progress, "v2",
                    param.getWidth(),
                    param.getHeight(), chromakeyParams);
            if (!response.isSuccess()) {
                log.error("Fail to submit videoId={}, resp={}", progress.getVideoId(), response);
                throw new DigitalHumanCommonException("Failed to submit micro chromakey");
            }

            log.info("Success to submit videoId={} to micro-chromakey-v2", progress.getVideoId());
            return true;
        } catch (Exception e) {
            throw new DigitalHumanCommonException("Exception when chroma key v2", e);
        } finally {
            try {
                if (tmpVertexShaderPath != null && !chromakeyConfig.isDebug()) {
                    Files.deleteIfExists(tmpVertexShaderPath);
                }
            } catch (IOException e) {
                log.warn("Fail to delete v2 vertex shader file: {}", tmpVertexShaderPath, e);
            }

            try {
                if (tmpPostVertexShaderPath != null && !chromakeyConfig.isDebug()) {
                    Files.deleteIfExists(tmpPostVertexShaderPath);
                }
            } catch (IOException e) {
                log.warn("Fail to delete v2 post vertex shader file: {}", tmpPostVertexShaderPath, e);
            }
        }
    }

    protected Path constructVertexShaderV2(ChromaKeyParamV2 param, Path vertexShaderPath) throws IOException {
        /*
         dynamic insert params in Java:

         const vec4 screen = vec4(0.0, 1.0, 0.0, 1.0);
         const float similarity = 120.0;
         const float smoothness = 80.0;
         const float spill = 100.0;
         const float contrast = 0.0;
         const float gamma = 0.0;
         const vec2 resolution = vec2(1080.0, 1920.0);
         const float opacity = 1.0;
         const float brightness = 0.0;
        */

        File templateFile = vertexShaderPath.toFile();
        Path result = Path.of("/tmp", UUID.randomUUID() + "-" + templateFile.getName());

        try (BufferedReader reader = Files.newBufferedReader(vertexShaderPath);
             BufferedWriter writer = Files.newBufferedWriter(result);
        ) {
            writer.write("const vec4 screen = vec4("
                    + param.getScreen()[0] / 255.0f + ","
                    + param.getScreen()[1] / 255.0f + ","
                    + param.getScreen()[2] / 255.0f + ", 1.0);");
            writer.newLine();
            writer.write("const float similarity = " + (float) param.getSimilarity() + ";");
            writer.newLine();
            writer.write("const float smoothness = " + (float) param.getSmoothness() + ";");
            writer.newLine();
            writer.write("const float spill = " + (float) param.getSpill() + ";");
            writer.newLine();
            writer.write("const float contrast = " + param.getContrast() + ";");
            writer.newLine();
            writer.write("const float gamma = " + param.getGamma() + ";");
            writer.newLine();
            writer.write("const vec2 resolution = vec2("
                    + (float) param.getWidth() + ","
                    + (float) param.getHeight() + ");");
            writer.newLine();
            writer.write("const float opacity = " + param.getOpacity() + ";");
            writer.newLine();
            writer.write("const float brightness = " + param.getBrightness() + ";");
            writer.newLine();

            // copy from template
            String line;
            while ((line = reader.readLine()) != null) {
                writer.write(line);
                writer.newLine();
            }
        } catch (Exception e) {
            throw e;
        }
        return result;
    }

    protected Path constructPostVertexShaderV2(ChromaKeyParamV2 param, Path vertexShaderPath) throws IOException {
        /*
        dynamic insert in Java

        const vec2 resolution = vec2(1080.0, 1920.0);
        const float edge_shrink = 3.0;
        */

        File templateFile = vertexShaderPath.toFile();
        Path result = Path.of("/tmp", UUID.randomUUID() + "-" + templateFile.getName());

        try (BufferedReader reader = Files.newBufferedReader(vertexShaderPath);
             BufferedWriter writer = Files.newBufferedWriter(result);
        ) {
            writer.write("const vec2 resolution = vec2("
                    + (float) param.getWidth() + ","
                    + (float) param.getHeight() + ");");
            writer.newLine();
            writer.write("const float edge_shrink = " + param.getEdgeShrink() + ";");
            writer.newLine();

            // copy from template
            String line;
            while ((line = reader.readLine()) != null) {
                writer.write(line);
                writer.newLine();
            }
        } catch (Exception e) {
            throw e;
        }
        return result;
    }
}
