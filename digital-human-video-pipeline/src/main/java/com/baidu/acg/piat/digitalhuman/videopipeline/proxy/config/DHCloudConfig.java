package com.baidu.acg.piat.digitalhuman.videopipeline.proxy.config;

import com.baidu.acg.piat.digitalhuman.videopipeline.proxy.client.CloudHttpClient;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * @Author: <EMAIL>
 * @Date: 2020/3/19
 */
@Configuration
public class DHCloudConfig {

    @Bean
    @ConfigurationProperties(prefix = "digitalhuman.cloud.config")
    public CloudConfig cloudConfig() {
        return new CloudConfig();
    }

    @Bean
    public CloudHttpClient cloudHttpClient() {
        return new CloudHttpClient(cloudConfig().getBaseUrl());
    }

    @Data
    public static class CloudConfig {

        private String baseUrl;

        private int pollIntervalMillis = 5000;

        private int notExistedTTL = 5;

        private int retryTimes = 3;

        private int retrySleepMillis = 100;

    }

}
