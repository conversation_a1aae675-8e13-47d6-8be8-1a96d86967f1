package com.baidu.acg.piat.digitalhuman.videopipeline.model.warehouse;

import lombok.Data;

/**
 * 文件长度
 * <AUTHOR>
 */
@Data
public class VideoMaterialConf {
    /**
     * 文件index的长度：00001.png => fragmentIndexLength = 5
     */
    private int fragmentIndexLength;

    // root

    private String rootPath;

    // image

    private String imageFileExtension;
    private String imageSubDirectory = "image";

    // audio

    private String audioSubDirectory = "audio";
    private String audioFileExtension;

    // audio format

    private float audioFormatSampleRate;
    private int audioFormatSampleSizeInBits;
    private int audioFormatChannels;
    private boolean audioFormatSigned;
    private boolean audioFormatBigEndian;

    // video

    private String videoFileExtension;
    private String videoSubDirectory = "video";
}
