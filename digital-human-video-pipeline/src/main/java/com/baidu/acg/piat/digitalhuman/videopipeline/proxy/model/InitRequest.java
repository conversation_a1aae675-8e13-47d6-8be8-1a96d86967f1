// Copyright (C) 2020 Baidu Inc. All rights reserved.

package com.baidu.acg.piat.digitalhuman.videopipeline.proxy.model;

import java.io.IOException;

import org.springframework.util.StringUtils;

import com.baidu.acg.piat.digitalhuman.common.exception.DigitalHumanCommonException;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * InitRequest
 *
 * <AUTHOR>
 * @since 2019-12-05
 */
@Slf4j
@Data
@NoArgsConstructor
@AllArgsConstructor
public class InitRequest {

    private static ObjectMapper objectMapper = new ObjectMapper()
            .enable(JsonParser.Feature.IGNORE_UNDEFINED)
            .configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)
            .setPropertyNamingStrategy(PropertyNamingStrategy.LOWER_CAMEL_CASE);

    private static ThreadLocal<InitRequest> local = new ThreadLocal<>();

    private String sessionId;

    private String resourceType;

    private Integer resolutionHeight;

    private Integer resolutionWidth;

    private String backgroundImageUrl;

    public String writeAsJson() throws DigitalHumanCommonException {
        try {
            return objectMapper.writeValueAsString(this);
        } catch (JsonProcessingException e) {
            throw new DigitalHumanCommonException("cannot write as json");
        }
    }

    public static void set(String initRequest) throws DigitalHumanCommonException {
        try {
            if (StringUtils.isEmpty(initRequest)) {
                local.set(null);
                throw new DigitalHumanCommonException("empty init request");
            }
            var request = objectMapper.readValue(initRequest, InitRequest.class);
            local.set(request);
        } catch (IOException e) {
            local.set(null);
            throw new DigitalHumanCommonException("cannot parse the init request " + initRequest);
        }

    }

    public static InitRequest get() {
        return local.get();
    }

    public static void remove() {
        local.remove();
    }


}
