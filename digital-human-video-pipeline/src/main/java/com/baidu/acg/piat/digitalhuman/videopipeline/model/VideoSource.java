package com.baidu.acg.piat.digitalhuman.videopipeline.model;

import lombok.Getter;

import java.nio.file.Path;

public abstract class VideoSource {

    @Getter
    protected VideoSourceFrom from;

    public abstract String getSource();

    public static class LocalFileVideoSource extends VideoSource {
        private final Path path;


        public LocalFileVideoSource(Path path) {
            this.path = path;
            this.from = VideoSourceFrom.LOCAL_FILE;
        }

        @Override
        public String getSource() {
            return path.toAbsolutePath().toString();
        }

        @Override
        public String toString() {
            return "LocalFileVideoSource{" +
                    "from=" + from +
                    ", path=" + path +
                    '}';
        }
    }

    public static class HttpUrlVideoSource extends VideoSource {

        private final String url;

        public HttpUrlVideoSource(String url) {
            this.url = url;
            this.from = VideoSourceFrom.HTTP_URL;
        }

        @Override
        public String getSource() {
            return url;
        }

        @Override
        public String toString() {
            return "HttpUrlVideoSource{" +
                    "from=" + from +
                    ", url='" + url + '\'' +
                    '}';
        }
    }

    public enum VideoSourceFrom {
        /**
         * 本地文件
         */
        LOCAL_FILE,
        /**
         * HTTP url
         */
        HTTP_URL;
    }
}
