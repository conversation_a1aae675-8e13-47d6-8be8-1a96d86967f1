package com.baidu.acg.piat.digitalhuman.videopipeline.model;

import com.baidu.acg.piat.digitalhuman.common.utils.IntUtil;
import lombok.Builder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.awt.image.BufferedImage;

/**
 * <AUTHOR>
 */
@Slf4j
@Builder
@Component(value = "characterMixer")
public class CharacterMixer {

    /**
     * 融合背景和mask后的数据
     *
     * @return
     */
    public BufferedImage mixInInt(byte[] rawYuvData, int[] back, byte[] mask, ImageSize imageSize) {
        int width = imageSize.getWidth();
        int height = imageSize.getHeight();

        int numOfPixel = width * height;
        int positionOfV = numOfPixel;
        int positionOfU = numOfPixel / 4 + numOfPixel;

        BufferedImage out = new BufferedImage(width, height, BufferedImage.TYPE_3BYTE_BGR);

        for (int i = 0; i < height; i++) {
            int startY = i * width;
            int step = (i / 2) * (width / 2);
            int startU = positionOfV + step;
            int startV = positionOfU + step;
            for (int j = 0; j < width; j++) {
                int y = startY + j;
                int u = startU + j / 2;
                int v = startV + j / 2;
                var ftPixel = convertYuvToRgbInPixel(rawYuvData[y], rawYuvData[u], rawYuvData[v]);

                int offset = i * width + j;
                var maskVal = mask[offset] & 0xff;
                if (maskVal == 0) {
                    out.setRGB(j, i, back[offset]);
                } else if (maskVal < 255) {
                    var bgPixel = back[offset];

                    var rgb = maskVal << 24;
                    rgb = rgb | (((ftPixel & 0xff0000) * maskVal
                            + (bgPixel & 0xff0000) * (256 - maskVal)) & 0xff000000) >> 8
                            | (((ftPixel & 0xff00) * maskVal + (bgPixel & 0xff00) * (256 - maskVal)) & 0xff0000) >> 8 |
                            (((ftPixel & 0xff) * maskVal + (bgPixel & 0xff) * (256 - maskVal)) & 0xff00) >> 8;
                    out.setRGB(j, i, rgb);
                } else {
                    out.setRGB(j, i, ftPixel);
                }
            }
        }

        return out;
    }

    public BufferedImage i420YuvToRgbImage(byte[] src, int width, int height) {
        BufferedImage out = new BufferedImage(width, height, BufferedImage.TYPE_INT_RGB);
        int numOfPixel = width * height;
        int positionOfV = numOfPixel;
        int positionOfU = numOfPixel / 4 + numOfPixel;
        for (int i = 0; i < height; i++) {
            int startY = i * width;
            int step = (i / 2) * (width / 2);
            int startU = positionOfV + step;
            int startV = positionOfU + step;
            for (int j = 0; j < width; j++) {
                int y = startY + j;
                int u = startU + j / 2;
                int v = startV + j / 2;
                var tmp = convertYuvToRgbInPixel(src[y], src[u], src[v]);
                out.setRGB(j, i, tmp);
            }
        }

        return out;
    }

    public int[] read(BufferedImage image) {
        int[] result = new int[image.getWidth() * image.getHeight()];

        for (int i = 0; i < image.getHeight(); i++) {
            for (int j = 0; j < image.getWidth(); j++) {
                result[i * image.getWidth() + j] = image.getRGB(j, i);
            }
        }

        return result;
    }

    public byte[] readLast8Byte(BufferedImage image) {
        byte[] result = new byte[image.getWidth() * image.getHeight()];

        for (int i = 0; i < image.getHeight(); i++) {
            for (int j = 0; j < image.getWidth(); j++) {
                result[i * image.getWidth() + j] = (byte) image.getRGB(j, i);
            }
        }

        return result;
    }

    /**
     * @param y
     * @param u
     * @param v
     * @return
     */
    private int convertYuvToRgbInPixel(byte y, byte u, byte v) {
        byte[] pixelValue = new byte[4];
        int r = (int) ((y & 0xff) + 1.4075 * ((v & 0xff) + Byte.MIN_VALUE));
        int g = (int) ((y & 0xff) - 0.3455 * ((u & 0xff) + Byte.MIN_VALUE) - 0.7169 * ((v & 0xff) + Byte.MIN_VALUE));
        int b = (int) ((y & 0xff) + 1.779 * ((u & 0xff) + Byte.MIN_VALUE));
        pixelValue[0] = Byte.MIN_VALUE;
        pixelValue[1] = (byte) (r < 0 ? 0 : Math.min(r, 255));
        pixelValue[2] = (byte) (g < 0 ? 0 : Math.min(g, 255));
        pixelValue[3] = (byte) (b < 0 ? 0 : Math.min(b, 255));
        return IntUtil.fromByteArray(pixelValue);
    }
}
