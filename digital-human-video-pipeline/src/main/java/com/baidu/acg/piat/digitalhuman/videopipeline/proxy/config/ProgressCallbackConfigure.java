package com.baidu.acg.piat.digitalhuman.videopipeline.proxy.config;

import com.baidu.acg.piat.digitalhuman.plat.client.PlatformClient;
import lombok.RequiredArgsConstructor;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import com.baidu.acg.piat.digitalhuman.videopipeline.proxy.persistence.VideoProgressRepository;
import com.baidu.acg.piat.digitalhuman.videopipeline.proxy.service.VideoFinalProgressCallback;
import com.baidu.acg.piat.digitalhuman.videopipeline.proxy.service.impl.VideoFinalProgressCallbackImpl;

@Configuration
@RequiredArgsConstructor
public class ProgressCallbackConfigure {

    private final VideoProgressRepository repository;

    private final PlatformClient platformClient;

    @Bean
    public VideoFinalProgressCallback videoFinalProgressCallback() {
        return new VideoFinalProgressCallbackImpl(repository, videoProgressCallbackConfig(), platformClient);
    }

    @Bean
    @ConfigurationProperties(prefix = "digitalhuman.videopipeline.callback")
    public VideoFinalProgressCallbackImpl.VideoProgressCallbackConfig videoProgressCallbackConfig() {
        return new VideoFinalProgressCallbackImpl.VideoProgressCallbackConfig();
    }
}
