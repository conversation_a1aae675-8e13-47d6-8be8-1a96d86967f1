package com.baidu.acg.piat.digitalhuman.videopipeline.proxy.controller;

import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.baidu.acg.piat.digitalhuman.common.console.video.ProgressResult;
import com.baidu.acg.piat.digitalhuman.common.model.Response;

@Slf4j
@RestController
@RequestMapping("/api/digitalhuman/video/callback")
public class CallbackController {

    @PostMapping
    public Response callback(@RequestBody ProgressResult progress) {
        log.info("Received callback, videoId={}", progress.getVideoId());
        return Response.success(null);
    }
}