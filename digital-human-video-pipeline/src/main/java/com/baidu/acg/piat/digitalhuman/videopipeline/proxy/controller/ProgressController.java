// Copyright (C) 2020 Baidu Inc. All rights reserved.

package com.baidu.acg.piat.digitalhuman.videopipeline.proxy.controller;

import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.OptimisticLockingFailureException;
import org.springframework.data.domain.PageRequest;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.baidu.acg.piat.digitalhuman.common.console.video.ProgressResult;
import com.baidu.acg.piat.digitalhuman.common.console.video.VideoBatchCanceleRequest;
import com.baidu.acg.piat.digitalhuman.common.console.video.VideoErrorRequest;
import com.baidu.acg.piat.digitalhuman.common.console.video.VideoFailRequest;
import com.baidu.acg.piat.digitalhuman.common.console.video.VideoSubmitRequest;
import com.baidu.acg.piat.digitalhuman.common.console.video.VideoSucceedRequest;
import com.baidu.acg.piat.digitalhuman.common.console.video.VideoUpdateRequest;
import com.baidu.acg.piat.digitalhuman.common.exception.DigitalHumanCommonException;
import com.baidu.acg.piat.digitalhuman.common.model.PageResponse;
import com.baidu.acg.piat.digitalhuman.common.model.Response;
import com.baidu.acg.piat.digitalhuman.videopipeline.proxy.model.VideoBatchRequest;
import com.baidu.acg.piat.digitalhuman.videopipeline.proxy.service.VideoProgressManager;
import com.baidu.acg.piat.digitalhuman.videopipeline.proxy.service.VideoProgressManager.VideoProgressManagerException;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * ProgressController
 *
 * <AUTHOR> Zhensheng(<EMAIL>)
 * @since 2019-12-06
 */
@Slf4j
@RestController
@RequestMapping("/api/digitalhuman/video/v1")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class ProgressController {
    private static final int MAX_PAGE_SIZE = 50;

    private final VideoProgressManager videoProgressManager;

    /**
     * submit
     *
     * @param submitRequest
     *
     * @return
     */
    @PostMapping("/progress/submit")
    public Response<List<ProgressResult>> submitProgress(@RequestBody VideoSubmitRequest submitRequest) {
        log.info("Accept the video init request={}", submitRequest);
        return Response.success(videoProgressManager.submitProgress(submitRequest));
    }

    /**
     * 将fail 和 update 状态单独独立出来，因为fail 还需要判断是否需要重新调度等。
     */
    @PostMapping("/progress/fail")
    public Response<ProgressResult> failProgress(@RequestBody VideoFailRequest failRequest) {
        log.info("Accept hte video fail request={}", failRequest);
        Throwable e = null;
        for (int i = 0; i < 3; i++) {
            try {
                return Response.success(videoProgressManager.failProgress(failRequest));
            } catch (OptimisticLockingFailureException t) {
                e = t;
            }
        }
        return Response.fail(e.getMessage());
    }

    @PostMapping("/progress/update")
    public Response<ProgressResult> updateProgress(@RequestBody VideoUpdateRequest updateRequest) {
        log.info("Accept hte video update request={}", updateRequest);
        Throwable e = null;
        for (int i = 0; i < 3; i++) {
            try {
                return Response.success(videoProgressManager.updateVideoProgress(updateRequest));
            } catch (OptimisticLockingFailureException t) {
                e = t;
            }
        }
        return Response.fail(e.getMessage());
    }

    @PostMapping("/progress/batch/cancele")
    public Response<Void> batchCanceleProgress(@RequestBody VideoBatchCanceleRequest request) {
        log.info("Accept batch cancele video update request={}", request);
        Throwable e = null;
        for (int i = 0; i < 3; i++) {
            try {
                videoProgressManager.batchCanceleProgress(request);
                return Response.success(null);
            } catch (OptimisticLockingFailureException t) {
                e = t;
            }
        }
        return Response.fail(e.getMessage());
    }

    @GetMapping("/progress/{videoId}")
    public Response<ProgressResult> queryProgress(@PathVariable(name = "videoId") String videoId) {
        return Response.success(videoProgressManager.pollProgress(videoId));
    }

    @DeleteMapping("/progress/{videoId}")
    public Response<Void> deleteProgress(@PathVariable(name = "videoId") String videoId) {
        videoProgressManager.deleteProgress(videoId);
        return Response.success(null);
    }

    @PostMapping("/progress/batchDelete")
    public Response<Void> deleteProgressBatch(@RequestBody VideoBatchRequest videoBatchRequest) {
        videoProgressManager.deleteProgressBatch(videoBatchRequest.getVideoIds());
        return Response.success(null);
    }

    @PostMapping("/progress/batchDelete/app")
    public Response<Void> batchDeleteProgressByAppId(@RequestBody VideoBatchRequest videoBatchRequest) {
        log.debug("Accept batch delete request={}", videoBatchRequest);
        videoProgressManager.batchDeleteProgressByAppId(videoBatchRequest.getAppId(), videoBatchRequest.getVideoIds());
        return Response.success(null);
    }

    @PostMapping("/progress")
    public Response<List<ProgressResult>> queryBatchProgress(
            @RequestBody VideoBatchRequest videoBatchRequest) {
        return Response.success(videoProgressManager.pollBatchProgress(videoBatchRequest.getVideoIds()));
    }

    @GetMapping("/progress/query")
    public Response<PageResponse<ProgressResult>> queryProgressByUserNameAndProjectNameOrCharacterConfigId(
            @RequestParam(required = false, defaultValue = "") String userName,
            @RequestParam(required = false, defaultValue = "") String projectName,
            @RequestParam(required = false, defaultValue = "") String characterConfigId,
            @RequestParam(required = false, defaultValue = "1") int pageNo,
            @RequestParam(required = false, defaultValue = "10") int pageSize) {
        if (StringUtils.isEmpty(userName) &&
                (StringUtils.isEmpty(projectName) && StringUtils.isEmpty(characterConfigId))) {
            throw new DigitalHumanCommonException("No valid parameters");
        }
        if (pageSize > MAX_PAGE_SIZE) {
            log.info("Page size = {} greater then max {}",
                    pageSize, MAX_PAGE_SIZE);
            pageSize = MAX_PAGE_SIZE;
        }

        PageRequest pageRequest = PageRequest.of(pageNo - 1, pageSize);
        log.info("Query video progress params: userName={}, projectName={} , " +
                        "characterConfigId={} , pageNo={}, pgeSize={}",
                userName, projectName, characterConfigId, pageNo, pageSize);
        if (StringUtils.isNotEmpty(characterConfigId)) {
            return Response.success(videoProgressManager
                    .pollProgressByUserNameAndCharacterConfigId(userName, characterConfigId, pageRequest));
        }
        else {
            return Response.success(videoProgressManager
                    .pollProgressByUserNameAndProjectName(userName, projectName, pageRequest));
        }

    }

    @GetMapping("/app/{appId}/progress")
    public Response<PageResponse<ProgressResult>> queryById(
            @PathVariable(value = "appId") String appId,
            @RequestParam(required = false) String videoName,
            @RequestParam(required = false, defaultValue = "1") int pageNo,
            @RequestParam(required = false, defaultValue = "10") int pageSize) {
        if (pageSize > MAX_PAGE_SIZE) {
            log.info("Page size = {} greater then max {}",
                    pageSize, MAX_PAGE_SIZE);
            pageSize = MAX_PAGE_SIZE;
        }

        PageRequest pageRequest = PageRequest.of(pageNo - 1, pageSize);

        log.info("Poll params: appId = {}, pageNo = {}, pgeSize = {}",
                appId, pageNo, pageSize);
        PageResponse<ProgressResult> resp = null;
        if (StringUtils.isNotBlank(videoName)) {
            resp = videoProgressManager.pollProgressByAppIdAndName(appId, videoName, pageRequest);
        } else {
            resp = videoProgressManager.pollProgressByAppId(appId, pageRequest);
        }
        return Response.success(resp);
    }

    @PostMapping("/progress/succeed-mrq")
    public Response<ProgressResult> succeed(
        @RequestPart VideoSucceedRequest request, @RequestPart MultipartFile video, @RequestPart MultipartFile audio) {
        String sessionId = request.getSessionId();
        log.info("Received succeeded video, sessionId={}", sessionId);
        try {
            return Response.success(videoProgressManager.succeededMrqVideo(request, video, audio));
        } catch (VideoProgressManagerException e) {
            log.error("Failed to update succeeded video, sessionId={}", sessionId, e);
            return Response.fail(e.getLocalizedMessage());
        }
    }

    @PostMapping("/progress/error")
    public Response<ProgressResult> error(@RequestBody VideoErrorRequest request) {
        String sessionId = request.getSessionId();
        log.info("Received error video, sessionId={}", sessionId);
        try {
            return Response.success(videoProgressManager.errorVideo(sessionId, request.getErrorMsg()
                    , request.getShowErrorMsg()));
        } catch (VideoProgressManagerException e) {
            log.error("Failed to update error video, sessionId={}", sessionId, e);
            return Response.fail(e.getLocalizedMessage());
        }
    }
}
