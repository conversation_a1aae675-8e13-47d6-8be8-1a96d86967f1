// Copyright (C) 2020 Baidu Inc. All rights reserved.

package com.baidu.acg.piat.digitalhuman.videopipeline.proxy.service;

import java.io.IOException;
import java.net.URI;
import java.net.URISyntaxException;
import java.net.URL;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Consumer;
import java.util.stream.Collectors;
import java.util.UUID;

import com.baidu.acg.piat.digitalhuman.common.utils.IDGenerator;
import com.baidu.acg.piat.digitalhuman.common.utils.JsonUtil;
import com.baidu.acg.piat.digitalhuman.common.utils.PathUtil;
import com.baidu.acg.piat.digitalhuman.common.utils.RetryPolicyUtil;
import com.baidu.acg.piat.digitalhuman.common.utils.SignUtil;
import com.baidu.acg.piat.digitalhuman.videopipeline.config.ChromakeyConfigure;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

import javax.annotation.Nullable;

import com.baidu.acg.piat.digitalhuman.common.access.AccessApp;
import com.baidu.acg.piat.digitalhuman.common.entity.CharacterModel;
import com.baidu.acg.piat.digitalhuman.plat.client.PlatformClient;
import com.baidu.acg.piat.digitalhuman.videopipeline.proxy.client.StarLightClient;
import com.baidu.acg.piat.digitalhuman.videopipeline.proxy.service.impl.VideoTaskSchedulerZkImpl;
import com.google.common.collect.Maps;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Pageable;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import com.baidu.acg.piat.digitalhuman.common.intelligentanimojiemotion.IntelligentAnimojiEmotionRequest;
import com.baidu.acg.piat.digitalhuman.common.console.video.ProgressResult;
import com.baidu.acg.piat.digitalhuman.common.console.video.ProgressStatus;
import com.baidu.acg.piat.digitalhuman.common.console.video.RetryPolicy;
import com.baidu.acg.piat.digitalhuman.common.console.video.Text2VideoRequest;
import com.baidu.acg.piat.digitalhuman.common.console.video.VideoBatchCanceleRequest;
import com.baidu.acg.piat.digitalhuman.common.console.video.VideoFailRequest;
import com.baidu.acg.piat.digitalhuman.common.console.video.VideoSubmitRequest;
import com.baidu.acg.piat.digitalhuman.common.console.video.VideoSucceedRequest;
import com.baidu.acg.piat.digitalhuman.common.console.video.VideoUpdateRequest;
import com.baidu.acg.piat.digitalhuman.common.constans.PostProcessingType;
import com.baidu.acg.piat.digitalhuman.common.constans.RenderOpenParameters;
import com.baidu.acg.piat.digitalhuman.common.exception.DigitalHumanCommonException;
import com.baidu.acg.piat.digitalhuman.common.helper.VideoErrorShowMsgHelper;
import com.baidu.acg.piat.digitalhuman.common.hook.PostProcessingRequest;
import com.baidu.acg.piat.digitalhuman.common.model.PageResponse;
import com.baidu.acg.piat.digitalhuman.common.model.PageResult;
import com.baidu.acg.piat.digitalhuman.notifier.service.DigitalHumanEventNotifier;
import com.baidu.acg.piat.digitalhuman.storage.service.StorageService;
import com.baidu.acg.piat.digitalhuman.storage.service.StorageService.ResourceException;
import com.baidu.acg.piat.digitalhuman.videopipeline.exception.FFmpegBaseException;
import com.baidu.acg.piat.digitalhuman.videopipeline.model.UploadUploaderImpl.UploaderConfig;
import com.baidu.acg.piat.digitalhuman.videopipeline.model.VideoInfoUtil;
import com.baidu.acg.piat.digitalhuman.videopipeline.model.warehouse.VideoMaterial;
import com.baidu.acg.piat.digitalhuman.videopipeline.proxy.persistence.VideoProgressModel;
import com.baidu.acg.piat.digitalhuman.videopipeline.proxy.persistence.VideoProgressRepository;
import com.baidu.acg.pie.UploadServiceOuterClass.Response;
import com.baidu.acg.pie.UploadServiceOuterClass.Video;
import com.baidu.acg.pie.UploadServiceOuterClass.X264Frame;
import com.google.common.collect.Lists;

import io.grpc.stub.StreamObserver;
import io.vavr.control.Try;
import lombok.Builder;
import lombok.Data;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * VideoProgressManager
 *
 * <AUTHOR> Zhensheng(<EMAIL>)
 * @since 2019-12-06
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class VideoProgressManager {

    private final IDGenerator videoIdGenerator;

    private final VideoProgressRepository videoProgressRepository;
    private final Map<String, VideoSession> progressMap = new ConcurrentHashMap<>();

    private final DigitalHumanEventNotifier notifier;

    private final VideoMaterial videoMaterial;

    private final StorageService storageService;

    private final UploaderConfig uploaderConfig;

    private final VideoErrorShowMsgHelper.ErrorMsgConf videoErrorMsgConf;

    private final VideoSubmitRecordService videoSubmitRecordService;

    private final PlatformClient platformClient;

    private final StarLightClient starLightClient;

    private final ChromaKeyService chromaKeyService;

    private final ChromakeyConfigure.ChromakeyConfig chromakeyConfig;

    @Value("${digitalhuman.twin.character-image:2D_LITE_SAAS}")
    private String twinCharacterImage;

    @Scheduled(fixedRateString = "${digitalhuman.videopipeline.progress.heartbeatIntervalMillis:5000}")
    public void fixRateHeartbeat() {
        List<String> videoIds = null;
        try {
            videoIds = progressMap.values().stream().map(VideoSession::getVideoId)
                    .collect(Collectors.toList());
            // videoProgressRepository.updateActiveTimeByVideoIdIn(videoIds, ZonedDateTime.now());
            Iterable<VideoProgressModel> result = videoProgressRepository.findAllByVideoIdIn(videoIds);
            result.forEach(t -> t.setActiveTime(ZonedDateTime.now()));
            videoProgressRepository.saveAll(result);
        } catch (Exception e) {
            log.error("Fail to update hearts of videos={}", videoIds, e);
        }

    }

    /**
     * 直接添加一条progress记录到数据库，只允许状态机不会再改变的记录，避免引起状态机调度异常
     *
     * @param model VideoProgressModel, status must be SUCCEED or ERROR
     * @return Optional of ProgressResult
     */
    public Optional<ProgressResult> addProgress(VideoProgressModel model) {
        if (model.getStatus() != ProgressStatus.ERROR && model.getStatus() != ProgressStatus.SUCCEED) {
            log.warn("Cannot save record directly with status={}", model.getStatus());
            return Optional.empty();
        }

        if (StringUtils.isEmpty(model.getText())) {
            model.setText(StringUtils.EMPTY);
        }

        log.info("Start to save one record to database, model={}", model);
        return Try.of(() -> Optional.ofNullable(videoProgressRepository.save(model).toProgressResult()))
                .onSuccess(res -> log.info("Success to save one record to database, model={}", res))
                .onFailure(t -> log.error("Fail to add video progress={}", model, t))
                .getOrElse(Optional.empty());
    }

    /**
     * delete one progress
     *
     * @param videoId
     * @return void
     */
    public void deleteProgress(String videoId) {
        Optional<VideoProgressModel> optional = videoProgressRepository.findByVideoId(videoId);
        if (optional.isEmpty()) {
            throw new DigitalHumanCommonException("video progress not found");
        }
        videoProgressRepository.deleteByVideoId(videoId);
        log.info("Success to delete one record from database, videoId={}", videoId);
    }

    /**
     * delete more progress
     *
     * @param videoIds
     * @return void
     */
    public void deleteProgressBatch(List<String> videoIds) {
        videoProgressRepository.deleteAllByVideoIdIn(videoIds);
        log.info("Success to delete more record from database, videoIds={}", videoIds);
    }

    public void batchDeleteProgressByAppId(String appId, List<String> videoIds) {
        videoProgressRepository.deleteByAppIdAndVideoIdIn(appId, videoIds);
        log.info("Batch delete by appId success.");
    }

    public List<ProgressResult> submitProgress(VideoSubmitRequest videoSubmitRequest) {
        String userId = videoSubmitRequest.getUserId();
        if (userId != null) {
            String figureId = null;

            try {
                String characterConfig = videoSubmitRequest.getCharacterConfig();
                VideoTaskSchedulerZkImpl.FigureConfig figureConfig =
                        JsonUtil.readValue(characterConfig, VideoTaskSchedulerZkImpl.FigureConfig.class);
                if (figureConfig != null && figureConfig.getFigure() != null) {
                    figureId = figureConfig.getFigure().getId();
                }
            } catch (Exception e) {
                log.error("failed to get figure id from character config, videoSubmitRequest={}, e={}",
                        videoSubmitRequest, e);
            }
            if (figureId == null) {
                log.debug("get figure id from character config figureId is null, videoSubmitRequest={}",
                        videoSubmitRequest);
                String appId = videoSubmitRequest.getAppId();
                AccessApp app = Try.of(() -> platformClient.getApp((appId)))
                        .getOrElseThrow(e -> new DigitalHumanCommonException("failed to get app from plat"));
                // 分身视频 添加渲染参数 x264_param_rc_i_bitrate character_type
                if (app != null && twinCharacterImage.equals(app.getCharacterImage())) {
                    if (null == videoSubmitRequest.getRenderParams()) {
                        videoSubmitRequest.setRenderParams(Maps.newHashMap());
                    }
                    videoSubmitRequest.getRenderParams().put("x264_param_rc_i_bitrate", "5000000");
                    videoSubmitRequest.getRenderParams().put("character_type", "digital_twin");
                }
                // get character id from plat
                if (app != null) {
                    try {
                        CharacterModel characterModel = platformClient.findCharacterByType(app.getCharacterImage(), 0);
                        figureId = characterModel.getCharacterId();
                    } catch (Exception e) {
                        log.error("failed to get character id from plat, videoSubmitRequest={}, e={}",
                                videoSubmitRequest, e);
                    }
                }
            }
            if (figureId != null) {
                videoSubmitRecordService.recordOneSubmitTask(userId, figureId);
            }
        }


        List<Text2VideoRequest> submitParams = videoSubmitRequest.toBatchText2VideoRequests();
        List<VideoProgressModel> models = submitParams.stream().map(submitParam -> {
            String text = submitParam.getText();
            Map<String, String> renderParams = submitParam.getRenderParams();

            String characterConfig = submitParam.getCharacterConfig();
            log.debug("characterConfig is {}", characterConfig);
            String characterImage = null;
            String figureName = null;
            if (renderParams != null) {
                boolean hasAutoAnimoji = renderParams.containsKey("autoAnimoji") && StringUtils.isNotEmpty(
                        renderParams.get("autoAnimoji"));
                if (StringUtils.isNotEmpty(characterConfig) && hasAutoAnimoji) {
                    ObjectMapper mapper = new ObjectMapper();
                    JsonNode rootNode = null;
                    try {
                        rootNode = mapper.readTree(characterConfig);
                    } catch (JsonProcessingException e) {
                        log.error("Fail to parse characterConfig={}", characterConfig, e);
                    }
                    JsonNode figureNode = rootNode.path("figure");
                    figureName = figureNode.path("name").asText();
                    log.debug("figureName is {}", figureName);
                    characterImage = platformClient.findCharacterType(figureName).getType();
                }
                if (hasAutoAnimoji && StringUtils.isNotEmpty(characterImage)) {
                    log.debug("renderParams is {}", renderParams);
                    if (Boolean.parseBoolean(submitParam.getRenderParams().get("autoAnimoji"))) {
                        IntelligentAnimojiEmotionRequest intelligentAnimojiEmotionRequest =
                                IntelligentAnimojiEmotionRequest.builder()
                                        .requestId(UUID.randomUUID().toString())
                                        .text(text)
                                        .characterConfigId(null)
                                        .characterImage(characterImage)
                                        .interruptible(false).build();
                        text = acquireIntelligentAnimoji(intelligentAnimojiEmotionRequest);
                    }
                }
            }
            return VideoProgressModel.builder()
                    .videoId(videoIdGenerator.generate())
                    .name(videoSubmitRequest.getVideoName() == null ? "" : videoSubmitRequest.getVideoName())
                    .text(text)
                    .status(ProgressStatus.SUBMIT)
                    .appId(submitParam.getAppId())
                    .submitParams(submitParam.toSubmitParams())
                    .subtitleParams(submitParam.getSubtitleParams())
                    .submitTime(ZonedDateTime.now())
                    .retryPolicy(submitParam.getRetryPolicy())
                    .callbackUrl(videoSubmitRequest.getCallbackUrl())
                    .userId(videoSubmitRequest.getUserId())
                    .build();
        }).collect(Collectors.toList());
        log.info("Start to save the models={}", models);
        return Try.of(() -> Lists.newArrayList(videoProgressRepository.saveAll(models))
                        .stream().map(VideoProgressModel::toProgressResult).collect(Collectors.toList()))
                .getOrElseThrow(t -> {
                    log.error("Fail to submit video progress", t);
                    throw new DigitalHumanCommonException("fail to init video progress");
                });

    }

    public ProgressResult failProgress(VideoFailRequest videoFailRequest) {
        String videoId = videoFailRequest.getId();
        if (StringUtils.isEmpty(videoId)) {
            if (StringUtils.isEmpty(videoFailRequest.getSessionId())) {
                throw new DigitalHumanCommonException("video id or session id is absent");
            }
            Optional<VideoProgressModel> progressOpt = videoProgressRepository.findBySessionId(
                    videoFailRequest.getSessionId());
            if (progressOpt.isEmpty()) {
                log.warn("video progress not found by sessionId={}", videoFailRequest.getSessionId());
                throw new DigitalHumanCommonException("video progress not found");
            }
            videoId = progressOpt.get().getVideoId();
        }

        Optional<VideoProgressModel> optional = videoProgressRepository.findByVideoId(videoId);
        if (optional.isEmpty()) {
            throw new DigitalHumanCommonException("video progress not found");
        }
        VideoProgressModel progress = optional.get();
        log.info("Start to update the failed progress of videoId={}, retryPolicy={} , retryTimes={}",
                progress.getVideoId(), progress.getRetryPolicy(), progress.getScheduledTimes());
        if (!videoFailRequest.getCanRetry()) {
            return updateProgressPatch(progress, VideoUpdateRequest.error(
                    progress.getVideoId(), progress.getAppId(), videoFailRequest.getErrorCode(),
                    videoFailRequest.getErrorMsg()
                    , videoFailRequest.getShowErrMsg()));
        }
        RetryPolicy retryPolicy = progress.getRetryPolicy();

        if (videoFailRequest.getScheduleFailed() && progress.getStatus() == ProgressStatus.SCHEDULED
                && progress.getScheduledTimes() > 0) {
            log.debug("Accept the scheduled failed request , revert the schedule cnt of videoId={}",
                    progress.getVideoId());
            progress.setScheduledTimes(progress.getScheduledTimes() - 1);
        }

        if (RetryPolicyUtil.needRetry(progress.getScheduledTimes(), retryPolicy)) {
            // need retry
            if (progress.getStatus() != null
                    && progress.getStatus().isTerminated()) {
                log.warn("status flow error, ignoring update status.  videoId:{}", progress.getVideoId());
                return progress.toProgressResult();
            }
            progress.setStatus(ProgressStatus.FAILED);
            progress.setCode(videoFailRequest.getErrorCode());
            progress.setMessage(videoFailRequest.getErrorMsg());
            progress.setShowMessage(videoFailRequest.getShowErrMsg());
            if (null == videoFailRequest.getShowErrMsg()) {
                progress.setShowMessage(videoFailRequest.getErrorMsg());
            }
            progress.setLastScheduleFinishTime(ZonedDateTime.now());
            ProgressResult result = videoProgressRepository.save(progress).toProgressResult();
            log.debug("Success to update failed status of video={} ,the video can be scheduled at next time",
                    result.getVideoId());
            return result;
        } else {

            // cannot retry
            return updateProgressPatch(progress, VideoUpdateRequest.error(
                    progress.getVideoId(), progress.getAppId(), videoFailRequest.getErrorCode(),
                    videoFailRequest.getErrorMsg(), videoFailRequest.getShowErrMsg()));
        }

    }

    // todo error check
    public ProgressResult updateVideoProgress(VideoUpdateRequest videoUpdateRequest) {
        if (Objects.isNull(videoUpdateRequest.getId()) && videoUpdateRequest.getSessionId() == null) {
            throw new DigitalHumanCommonException("video id or session id is absent");
        }
        if (videoUpdateRequest.getStatus() == ProgressStatus.FAILED
                || videoUpdateRequest.getStatus() == ProgressStatus.ERROR) {
            throw new DigitalHumanCommonException("failed status MUST update by request '/progress/fail'");
        }
        if (videoUpdateRequest.getStatus() == ProgressStatus.SUBMIT) {
            throw new DigitalHumanCommonException("submit status MUST update by request '/progress/submit'");
        }
        Optional<VideoProgressModel> optional = videoProgressRepository.findByVideoId(videoUpdateRequest.getId());
        if (optional.isEmpty() && StringUtils.isNotBlank(videoUpdateRequest.getSessionId())) {
            optional = videoProgressRepository.findBySessionId(videoUpdateRequest.getSessionId());
        }
        if (optional.isEmpty()) {
            log.warn("Video progress not present, video id={}", videoUpdateRequest.getId());
            throw new DigitalHumanCommonException("video progress not present");
        }
        return updateProgressPatch(optional.get(), videoUpdateRequest);
    }

    public ProgressResult updateProgressPatch(VideoProgressModel progress, VideoUpdateRequest videoUpdateRequest) {
        log.info("Start to update the progress of videoId={} , target status={}", progress.getVideoId(),
                videoUpdateRequest);
        if (progress.getStatus() != videoUpdateRequest.getStatus()
                && progress.getStatus() != null
                && progress.getStatus().isTerminated()) {
            log.warn("status flow error, ignoring update status.  videoId:{}", progress.getVideoId());
            return progress.toProgressResult();
        }
        Optional.ofNullable(videoUpdateRequest.getSessionId())
                .ifPresent(progress::setSessionId);
        Optional.ofNullable(videoUpdateRequest.getAppId())
                .ifPresent(progress::setAppId);
        Optional.ofNullable(videoUpdateRequest.getSubtitleFileUrl())
                .ifPresent(progress::setSubtitleFileUrl);
        if (null == videoUpdateRequest.getShowMessage()) {
            videoUpdateRequest.setShowMessage(videoUpdateRequest.getMessage());
        }
        Optional.ofNullable(videoUpdateRequest.getStatus()).ifPresent(status -> {
            progress.setStatus(status);
            /**
             * 重新reset msg信息
             */

            // 不增加重试次数的vp设置
            if (status.equals(ProgressStatus.SCHEDULED) && progress.getCode() == 3001) {
                progress.setScheduledTimes(progress.getScheduledTimes() - 1);
            }
            progress.setCode(0);
            progress.setMessage("");
            progress.setShowMessage("");
            switch (status) {
                case INIT:
                case SCHEDULED: {
                    progress.setScheduledTimes(progress.getScheduledTimes() + 1);
                    progress.setLastScheduleStartTime(ZonedDateTime.now());
                    break;
                }
                case CANCELED:
                case RENDERING:
                case PROCESSING:
                case PREPARE_RENDER: {
                    if (videoUpdateRequest.getAudioUrl() != null) {
                        progress.setAudioUrl(videoUpdateRequest.getAudioUrl());
                        log.debug("Update the audio={}", videoUpdateRequest.getAudioUrl());
                    }
                    log.debug("Update the status={}", status);
                    break;
                }
                case ERROR:
                case FAILED: {
                    progress.setCode(videoUpdateRequest.getCode());
                    progress.setMessage(videoUpdateRequest.getMessage());
                    progress.setShowMessage(videoUpdateRequest.getShowMessage());
                    progress.setLastScheduleFinishTime(ZonedDateTime.now());
                    break;
                }
                case SUCCEED:
                    progress.setCode(0);
                    progress.setMessage("success");
                    progress.setShowMessage("success");
                    progress.setStatus(ProgressStatus.SUCCEED);
                    progress.setDownloadUrl(videoUpdateRequest.getDownloadUrl());
                    progress.setAudioUrl(videoUpdateRequest.getAudioUrl());
                    progress.setLastScheduleFinishTime(ZonedDateTime.now());
                    progress.setVideoGenerateFinishTime(ZonedDateTime.now());
                    progress.setVideoGenerateCostMillis(ZonedDateTime.now().toInstant().toEpochMilli()
                            - (progress.getLastScheduleStartTime() == null ? progress.getCreateTime() :
                            progress.getLastScheduleStartTime()).toInstant().toEpochMilli());
                    break;
                case SUBMIT:
                default: {
                    throw new DigitalHumanCommonException("not allowed here");
                }
            }

        });

        VideoProgressModel result = videoProgressRepository.save(progress);
        log.info("Success to update the progress target status={} ", result);
        return result.toProgressResult();
    }

    public VideoSession startX264(String sessionId, StreamObserver<X264Frame> requestObserver) {

        return progressMap.computeIfAbsent(sessionId, k -> {

            Optional<VideoProgressModel> optional = videoProgressRepository.findBySessionId(sessionId);

            if (optional.isEmpty()) {
                log.error("Fail to find the video progress match the sessionId={}", sessionId);
                throw new DigitalHumanCommonException(
                        "fail to find the video progress match the sessionId: " + sessionId);
            }
            return VideoSession.builder()
                    .sessionId(sessionId)
                    .videoId(optional.get().getVideoId())
                    .appId(optional.get().getAppId())
                    .progressChangeCallback(progressChangeCallback())
                    .x264RequestObserver(requestObserver)
                    .build();
        });
    }

    public ProgressResult succeededMrqVideo(VideoSucceedRequest request, MultipartFile video, MultipartFile audio)
            throws VideoProgressManagerException {
        String sessionId = request.getSessionId();
        Optional<VideoProgressModel> progressOpt = videoProgressRepository.findBySessionId(sessionId);
        if (progressOpt.isEmpty()) {
            throw new VideoProgressManagerException("failed to find video by session", null);
        }
        final VideoProgressModel progress = progressOpt.get();
        if (progress.getStatus() != null
                && progress.getStatus().isTerminated()) {
            log.warn("status flow error, ignoring update status.  videoId:{}", progress.getVideoId());
            return progress.toProgressResult();
        }
        URL videoUrl;
        try {
            Path videoPath = videoMaterial.getVideoPath(progress.getVideoId(), request.getSuffix());
            Files.copy(video.getInputStream(), videoPath, StandardCopyOption.REPLACE_EXISTING);
            Long duration = 0L;
            try {
                duration = VideoInfoUtil.getVideoDuration(videoPath.toString());
            } catch (FFmpegBaseException e) {
                log.error("fail to get duration", e);
            }
            progress.setVideoDuration(duration);
            videoUrl = storageService.save(videoPath);
        } catch (IOException | ResourceException e) {
            throw new VideoProgressManagerException("failed to save video", e);
        }
        log.info("Saved video, sessionId={} url={}", sessionId, videoUrl);

        URL audioUrl;
        try {
            byte[] wav = audio.getBytes();
            Path audioPath = videoMaterial.getRootPath(progress.getVideoId()).resolve(progress.getVideoId() + ".wav");
            Files.write(audioPath, wav);
            audioUrl = storageService.save(audioPath);
        } catch (IOException | ResourceException e) {
            throw new VideoProgressManagerException("failed to save audio", e);
        }
        log.info("Saved audio, sessionId={} url={}", sessionId, audioUrl);

        ProgressResult res = updateProgressPatch(progress, VideoUpdateRequest.builder().status(ProgressStatus.SUCCEED)
                .downloadUrl(videoUrl.toString()).audioUrl(audioUrl.toString()).build());
        if (!uploaderConfig.isPreserveMaterial()) {
            try {
                Files.delete(videoMaterial.getRootPath(progress.getVideoId()));
            } catch (IOException e) {
                log.warn("Failed to clean video, id={}", progress.getVideoId());
            }
        }

        return res;
    }

    public ProgressResult errorVideo(String sessionId, String errorMsg
            , String showErrorMsg) throws VideoProgressManagerException {
        Optional<VideoProgressModel> progressOpt = videoProgressRepository.findBySessionId(sessionId);
        if (progressOpt.isEmpty()) {
            throw new VideoProgressManagerException("failed to find video by session", null);
        }
        final VideoProgressModel progress = progressOpt.get();

        return updateProgressPatch(progress,
                VideoUpdateRequest.builder().status(ProgressStatus.ERROR)
                        .showMessage(showErrorMsg)
                        .code(-1).message(errorMsg).build());
    }

    public ProgressResult errorVideoById(String videoId, String errorMsg, String showErrorMsg) {
        Optional<VideoProgressModel> progressOpt = videoProgressRepository.findByVideoId(videoId);
        if (progressOpt.isEmpty()) {
            log.debug("Failed to find video by id: {}", videoId);
        }
        final VideoProgressModel progress = progressOpt.get();

        return updateProgressPatch(progress,
                VideoUpdateRequest.builder().status(ProgressStatus.ERROR)
                        .showMessage(showErrorMsg)
                        .code(-1).message(errorMsg).build());
    }

    public ProgressResult failedVideoById(String videoId, String errorMsg, String showErrorMsg) {
        Optional<VideoProgressModel> progressOpt = videoProgressRepository.findByVideoId(videoId);
        if (progressOpt.isEmpty()) {
            log.debug("Failed to find video by id: {}", videoId);
        }
        final VideoProgressModel progress = progressOpt.get();

        return updateProgressPatch(progress,
                VideoUpdateRequest.builder().status(ProgressStatus.FAILED)
                        .showMessage(showErrorMsg)
                        .code(-1).message(errorMsg).build());
    }

    public void deliverX264Frame(VideoSession session, X264Frame x264Frame) {
        if (session == null) {
            log.error("Fail to deliver x264frame for null session ");
            return;
        }
        session.getX264RequestObserver().onNext(x264Frame);

    }

    public ProgressResult pollProgress(String videoId) {
        if (StringUtils.isEmpty(videoId)) {
            throw new DigitalHumanCommonException("video id not present");
        }
        var optional = videoProgressRepository.findByVideoId(videoId);
        if (optional.isEmpty()) {
            throw new DigitalHumanCommonException("video progress not found");
        }
        var progress = optional.get();
        return progress.toProgressResult();
    }

    public List<ProgressResult> pollBatchProgress(List<String> videoIds) {
        if (CollectionUtils.isEmpty(videoIds)) {
            throw new DigitalHumanCommonException("video ids not present");
        }
        List<ProgressResult> result = new ArrayList<>();
        videoProgressRepository.findAllByVideoIdIn(videoIds).forEach(progress -> {
            result.add(progress.toProgressResult());
        });
        return result;
    }

    public PageResponse<ProgressResult> pollProgressByUserNameAndProjectName(
            String userName, String projectName, Pageable pageable) {
        var repositoryResult = videoProgressRepository
                .findAllByUserNameAndProjectNameAndStatusNameOrderByCreateTimeDesc(
                        userName, projectName, ProgressStatus.SUCCEED.name(), pageable);
        List<ProgressResult> pageContentList = repositoryResult.stream()
                .map(VideoProgressModel::toProgressResult).collect(Collectors.toList());

        return PageResponse.<ProgressResult>builder()
                .page(PageResult.<ProgressResult>builder()
                        .pageNo(pageable.getPageNumber() + 1)
                        .pageSize(pageable.getPageSize())
                        .totalCount(repositoryResult.getTotalElements())
                        .result(pageContentList)
                        .build())
                .build();
    }

    public PageResponse<ProgressResult> pollProgressByUserNameAndCharacterConfigId(
            String userName, String characterConfigId, Pageable pageable) {
        var repositoryResult = videoProgressRepository
                .findAllByUserNameAndCharacterConfigIdAndStatusNameOrderByCreateTimeDesc(
                        userName, characterConfigId, ProgressStatus.SUCCEED.name(), pageable);
        List<ProgressResult> pageContentList = repositoryResult.stream()
                .map(VideoProgressModel::toProgressResult).collect(Collectors.toList());

        return PageResponse.<ProgressResult>builder()
                .page(PageResult.<ProgressResult>builder()
                        .pageNo(pageable.getPageNumber() + 1)
                        .pageSize(pageable.getPageSize())
                        .totalCount(repositoryResult.getTotalElements())
                        .result(pageContentList)
                        .build())
                .build();
    }

    /**
     * 只输出成功的视频
     *
     * @param appId
     * @param pageable
     * @return
     */
    public PageResponse<ProgressResult> pollProgressByAppId(
            @NonNull String appId, Pageable pageable) {

        var repositoryResult = videoProgressRepository.findAllByAppIdAndStatusNameOrderByCreateTimeDesc(
                appId, ProgressStatus.SUCCEED.name(), pageable);
        List<ProgressResult> pageContentList = new ArrayList<>();
        repositoryResult.getContent().forEach(progress -> pageContentList.add(progress.toProgressResult()));

        return PageResponse.<ProgressResult>builder()
                .page(PageResult.<ProgressResult>builder()
                        .pageNo(pageable.getPageNumber() + 1)
                        .pageSize(pageable.getPageSize())
                        .totalCount(repositoryResult.getTotalElements())
                        .result(pageContentList)
                        .build())
                .build();
    }

    /**
     * 只输出成功的视频
     *
     * @param appId
     * @param pageable
     * @return
     */
    public PageResponse<ProgressResult> pollProgressByAppIdAndName(
            @NonNull String appId, String videoName, Pageable pageable) {

        var repositoryResult = videoProgressRepository.
                findAllByAppIdAndStatusNameAndNameContainingOrderByCreateTimeDesc(
                        appId, ProgressStatus.SUCCEED.name(), videoName, pageable);
        List<ProgressResult> pageContentList = new ArrayList<>();
        repositoryResult.getContent().forEach(progress -> pageContentList.add(progress.toProgressResult()));

        return PageResponse.<ProgressResult>builder()
                .page(PageResult.<ProgressResult>builder()
                        .pageNo(pageable.getPageNumber() + 1)
                        .pageSize(pageable.getPageSize())
                        .totalCount(repositoryResult.getTotalElements())
                        .result(pageContentList)
                        .build())
                .build();
    }

    public void updateProgress(String sessionId, ProgressStatus status, Response response) {
        var session = progressMap.get(sessionId);
        if (session == null) {
            log.warn("Fail to find the session={} , cannot update progress {} ", sessionId, status);
            return;
        }
        try {
            if (session.getStatus().equals(status)) {
                log.info("Status of session ={} not changed ,still {}, not calling callback",
                        sessionId, status);
            } else {
                session.getProgressChangeCallback().onStatusChange(session, status, response);
            }
        } catch (Exception e) {
            log.error("Fail to notify the status change of session ={} , status{} ", sessionId, status, e);
        }
    }

    public VideoSession destroy(String sessionId) {
        log.info("Success to finish the video progress of sessionId={}", sessionId);
        var session = progressMap.remove(sessionId);
        if (session != null) {
            if (session.getX264RequestObserver() != null) {
                session.getX264RequestObserver().onCompleted();
            }
            if (session.getVideoRequestObserver() != null) {
                session.getX264RequestObserver().onCompleted();

            }
            log.info("Success to close the connection of video pipeline of sessionId={}", sessionId);
        }
        return session;
    }

    private ProgressChangeCallback progressChangeCallback() {
        return (session, after, payload) -> {
            try {
                log.info("Session status switch from {} to {} of sessionId={}", session.getStatus(), after
                        , session.getSessionId());
                switch (after) {
                    case SCHEDULED:
                    case INIT: {
                        log.error("No way to change status to INIT/SCHEDULED, sessionId={}", session.getSessionId());
                        break;
                    }
                    case RENDERING: {
                        session.setStatus(after);
                        log.info("Success to offer video pipeline job of sessionId={}",
                                session.getSessionId());
                        switchStatusAndSave(session, after, progress -> {
                            progress.setCode(0);
                            progress.setMessage("");
                            progress.setShowMessage("");
                        });
                        break;
                    }
                    case PROCESSING: {
                        session.setStatus(after);
                        log.info("Success to start generate video of session={}", session.getSessionId());
                        switchStatusAndSave(session, after, progress -> {
                            progress.setCode(0);
                            progress.setMessage("");
                            progress.setShowMessage("");
                        });
                        break;
                    }
                    case SUCCEED: {
                        postProcess(session, after, payload);
                        break;
                    }
                    case ERROR: {
                        log.info("Receive error video progress status of session={}", session.getSessionId());
                        var response = (Response) payload;
                        session.setStatus(after);
                        switchStatusAndSave(session, after, progress -> {
                            progress.setMessage(response.getMessage());
                            progress.setCode(-1);
                            progress.setShowMessage(videoErrorMsgConf.getVideoGenerationErrorMsg());
                            progress.setLastScheduleFinishTime(ZonedDateTime.now());
                        });
                        this.destroy(session.getSessionId());
                        break;
                    }
                    case FAILED: {
                        var response = (Response) payload;
                        log.error("Receive failed video progress status of session={}, response={}",
                                session.getSessionId(), response);
                        session.setStatus(after);
                        switchStatusAndSave(session, after, progress -> {
                            progress.setMessage(response.getMessage());
                            progress.setCode(-1);
                            progress.setShowMessage(videoErrorMsgConf.getVideoGenerationErrorMsg());
                            progress.setLastScheduleFinishTime(ZonedDateTime.now());
                        });
                        this.destroy(session.getSessionId());
                        break;
                    }
                    default: {
                        log.warn("Accept unsupported status of sessionId = {} ", session.getSessionId());
                        throw new DigitalHumanCommonException("unsupported type");
                    }

                }
            } catch (Exception e) {
                log.error("Fail to run callback of session={}", session.getSessionId(), e);
            }
        };
    }

    private void postProcess(VideoSession session,
                             ProgressStatus after,
                             Object payload) {
        session.setStatus(after);
        var response = (Response) payload;

        // 如果使用微服务进行抠绿，则修改状态，为等待抠绿
        if (chromakeyConfig.isUseMicroChromakey()) {
            after = ProgressStatus.WAIT_POST_PROCESS;
        }

        switchStatusAndSave(session, after, progress -> {
            var downloadUrl = response.getVideoDownloadUrl();
            var audioUrl = response.getAudioDownloadUrl();
            String thumbnail = response.getThumbnailUrl();
            log.info("Success to get video={}, audio={}", downloadUrl, audioUrl);
            Long duration = 0L;

            // 备份一个原始的URL
            var originUrl = downloadUrl;

            // 不需要额外抠绿服务，还是走之前的逻辑，这里就进行抠绿
            if (!chromakeyConfig.isUseMicroChromakey()) {
                log.info("use local chromakey, videoId={}", progress.getVideoId());
                if (StringUtils.isNotEmpty(downloadUrl)) {
                    try {
                        downloadUrl = videoChroma(downloadUrl, progress);
                    } catch (DigitalHumanCommonException e) {
                        throw new DigitalHumanCommonException("Fail to chroma video!");
                    }
                }
            }

            try {
                duration = VideoInfoUtil.getVideoDuration(downloadUrl);
            } catch (FFmpegBaseException e) {
                log.error("fail to get duration", e);
            }

            try {
                var videoBosUrl = storageService.save(Paths.get(downloadUrl));
                downloadUrl = videoBosUrl.toString();
                log.debug("Success to upload video {} to bos: {}", downloadUrl, videoBosUrl);
            } catch (ResourceException e) {
                log.error("Fail to upload video of session={}, video: {}",
                        session.getSessionId(), downloadUrl, e);
                throw new DigitalHumanCommonException("Failed to upload video");
            }
            progress.setDownloadUrl(downloadUrl);

            // 需要额外抠绿服务，在BOS存储成功后，提交一下，如果失败则重试几次
            if (chromakeyConfig.isUseMicroChromakey()) {
                log.info("use micro chromakey service, videoId={}", progress.getVideoId());
                // 尝试使用CFS进行本地文件共享
                copyLocalFile(originUrl, progress);

                for (int i = 0; i < 3; ++i) {
                    if (videoMicroChroma(downloadUrl, progress)) {
                        progress.setStatus(ProgressStatus.SUBMIT_POST_PROCESS);
                        break;
                    } else {
                        // 有一些人像不需要抠绿
                        log.info("no chromakey need, videoId={}", progress.getVideoId());
                        progress.setStatus(ProgressStatus.SUCCEED);
                    }

                    if (progress.getStatus() == ProgressStatus.SUCCEED) {
                        break;
                    }

                    try {
                        Thread.sleep(5000);
                    } catch (InterruptedException e) {
                        throw new RuntimeException(e);
                    }
                }

                // 如果遇到了意外情况，比如此刻抠绿服务在重启部署或者网络发生问题
                // 可以依然使用原抠绿逻辑
                if (ProgressStatus.SUCCEED != progress.getStatus() && ProgressStatus.SUBMIT_POST_PROCESS != progress.getStatus()) {
                    log.info("use micro chromakey failed, try local chromakey, videoId={}", progress.getVideoId());
                    if (StringUtils.isNotEmpty(originUrl)) {
                        try {
                            originUrl = videoChroma(originUrl, progress);
                        } catch (DigitalHumanCommonException e) {
                            throw new DigitalHumanCommonException("Fail to chroma video!");
                        }
                    }

                    try {
                        var videoBosUrl = storageService.save(Paths.get(originUrl));
                        originUrl = videoBosUrl.toString();
                        log.debug("Success to upload video {} to bos: {}", originUrl, videoBosUrl);
                    } catch (ResourceException e) {
                        log.error("Fail to upload video of session={}, video: {}",
                                session.getSessionId(), originUrl, e);
                        throw new DigitalHumanCommonException("Failed to upload video");
                    }
                    progress.setDownloadUrl(originUrl);
                }
            }

            progress.setAudioUrl(audioUrl);
            progress.setLastScheduleFinishTime(ZonedDateTime.now());
            progress.setVideoGenerateFinishTime(ZonedDateTime.now());
            progress.setVideoGenerateCostMillis(ZonedDateTime.now().toInstant().toEpochMilli()
                    - progress.getLastScheduleStartTime().toInstant().toEpochMilli());
            progress.setThumbnail(thumbnail);
            // revert the error in past
            progress.setCode(0);
            progress.setMessage("success");
            progress.setVideoDuration(duration);
            progress.setShowMessage("success");

            // 不需要额外抠绿服务的，成功后直接进行下一环节
            if (!chromakeyConfig.isUseMicroChromakey() || progress.getStatus() == ProgressStatus.SUCCEED) {
                postProcessing(progress);
            }
        });
        this.destroy(session.getSessionId());
        log.info("Success to generate video of session={}", session.getSessionId());
    }

    private String getEditIdFromUrl(String url) throws URISyntaxException {
        URI uri = new URI(url);
        String query = uri.getQuery();

        if (query != null) {
            Map<String, String> paramMap =
                    List.of(query.split("&")).stream()
                            .map(param -> param.split("="))
                            .collect(Collectors.toMap(p -> p[0], p -> p.length > 1 ? p[1] : ""));
            return paramMap.get("editId");
        }
        return null;
    }

    private String copyLocalFile(String inputVideo, VideoProgressModel progress) {
        if (!chromakeyConfig.isUseCfs() || StringUtils.isEmpty(chromakeyConfig.getCfsRootPath())) {
            return "";
        }

        if (StringUtils.isEmpty(progress.getCallbackUrl())) {
            log.info("edit-id in callbackUrl is empty, videoId={}", progress.getVideoId());
            return "";
        }

        String editId = "";
        try {
            editId = getEditIdFromUrl(progress.getCallbackUrl());
        } catch (Exception e) {
            log.warn("get edit from callback url failed: {}", e.getMessage());
            return "";
        }

        String outFilename = SignUtil.sha256Hash(progress.getDownloadUrl());
        String suffix = PathUtil.getFileExtension(inputVideo);

        if (StringUtils.isEmpty(outFilename) || StringUtils.isEmpty(suffix)) {
            log.info("dst filename or suffix is empty, videoId={}", progress.getVideoId());
            return "";
        }

        String outFolder = chromakeyConfig.getCfsRootPath() + "/" + editId + "/chromakey/" + outFilename + "/";
        String outPath = outFolder + outFilename + "." + suffix;
        log.info("dst filename={}, videoId={}", outPath, progress.getVideoId());

        Path source = Paths.get(inputVideo);
        Path targetDir = Paths.get(outFolder);
        Path targetFile = targetDir.resolve(outPath);

        try {
            Files.createDirectories(targetDir);
            Files.copy(source, targetFile, StandardCopyOption.REPLACE_EXISTING);
        } catch (IOException e) {
            return "";
        }

        log.info("copy video file to: {}", outPath);
        return outPath;
    }

    /**
     * 扣绿
     */
    public String videoChroma(String srcVideoUrl, VideoProgressModel progress) throws DigitalHumanCommonException {
        String tarVideoUrl = srcVideoUrl;
        try {
            Map<String, Object> effectParams = getChromaKeyParam(progress);
            ObjectMapper om = new ObjectMapper();
            if (effectParams != null) {
                if (effectParams.get("chromaKey") == null) {
                    log.debug("No chroma is null, skip, vp: {}", progress.getVideoId());
                    return tarVideoUrl;
                }
                if ((int) effectParams.getOrDefault("version", 1) == 1) {
                    var paramV1 = om.convertValue(
                            effectParams.get("chromaKey"), ChromaKeyService.ChromaKeyParamV1.class);
                    if (progress.getSubmitParams() != null) {
                        paramV1.setHeight(progress.getSubmitParams().getResolutionHeight());
                        paramV1.setWidth(progress.getSubmitParams().getResolutionWidth());
                    }
                    log.debug("Get v1 chroma key param: {}, vpId: {}", paramV1, progress.getVideoId());
                    Path tarPath = chromaKeyService.chromaKeyV1(Paths.get(tarVideoUrl), paramV1);
                    log.debug("After v1 chroma: path: {}, vpId: {}", tarPath, progress.getVideoId());
                    tarVideoUrl = tarPath.toString();
                } else {
                    var paramV2 = om.convertValue(
                            effectParams.get("chromaKey"), ChromaKeyService.ChromaKeyParamV2.class);
                    if (progress.getSubmitParams() != null) {
                        paramV2.setHeight(progress.getSubmitParams().getResolutionHeight());
                        paramV2.setWidth(progress.getSubmitParams().getResolutionWidth());
                    }
                    log.debug("Get v2 chroma key param: {}, vpId: {}", paramV2, progress.getVideoId());
                    Path tarPath = chromaKeyService.chromaKeyV2(Paths.get(tarVideoUrl), paramV2);
                    log.debug("After v2 chroma: path: {}, vpId: {}", tarPath, progress.getVideoId());
                    tarVideoUrl = tarPath.toString();
                }
            }
        } catch (Exception e) {
            log.error("Get error when chroma, video id = {}", progress.getVideoId(), e);
            errorVideoById(progress.getVideoId(), "Get error when attempt to chroma", "扣绿失败");
            throw new DigitalHumanCommonException("fail to chroma video");
        }
        return tarVideoUrl;
    }

    public boolean videoMicroChroma(String srcVideoUrl, VideoProgressModel progress) throws DigitalHumanCommonException {
        try {
            Map<String, Object> effectParams = getChromaKeyParam(progress);
            ObjectMapper om = new ObjectMapper();
            if (effectParams != null) {
                if (effectParams.get("chromaKey") == null) {
                    log.debug("No chroma is null, skip, vp: {}", progress.getVideoId());
                    return false;
                }
                if ((int) effectParams.getOrDefault("version", 1) == 1) {
                    var paramV1 = om.convertValue(
                            effectParams.get("chromaKey"), ChromaKeyService.ChromaKeyParamV1.class);
                    if (progress.getSubmitParams() != null) {
                        paramV1.setHeight(progress.getSubmitParams().getResolutionHeight());
                        paramV1.setWidth(progress.getSubmitParams().getResolutionWidth());
                    }
                    log.debug("Get v1 chroma key param: {}, vpId: {}", paramV1, progress.getVideoId());
                    return chromaKeyService.microChromaKeyV1(Paths.get(srcVideoUrl), paramV1, progress);
                } else {
                    var paramV2 = om.convertValue(
                            effectParams.get("chromaKey"), ChromaKeyService.ChromaKeyParamV2.class);
                    if (progress.getSubmitParams() != null) {
                        paramV2.setHeight(progress.getSubmitParams().getResolutionHeight());
                        paramV2.setWidth(progress.getSubmitParams().getResolutionWidth());
                    }
                    log.debug("Get v2 chroma key param: {}, vpId: {}", paramV2, progress.getVideoId());
                    return chromaKeyService.microChromaKeyV2(Paths.get(srcVideoUrl), paramV2, progress);
                }
            }
        } catch (Exception e) {
            log.error("Get error when submit to micro chroma, video id = {}", progress.getVideoId(), e);
            errorVideoById(progress.getVideoId(), "Get error when attempt to chroma", "扣绿失败");
            throw new DigitalHumanCommonException("fail to chroma video");
        }
        return false;
    }

    private Map<String, Object> getChromaKeyParam(VideoProgressModel progress) throws Exception {
        try {
            var submitParams = progress.getSubmitParams();
            String characterConfigStr = submitParams.getCharacterConfig();
            VideoTaskSchedulerZkImpl.FigureConfig figureConfig = JsonUtil.readValue(
                    characterConfigStr, VideoTaskSchedulerZkImpl.FigureConfig.class);
            if (figureConfig == null || figureConfig.getFigure() == null || figureConfig.getFigure().getId() == null) {
                return null;
            }
            var figureModel = starLightClient.getFigureModelById(figureConfig.getFigure().getId());
            if (figureModel != null && figureModel.getEffects() != null) {
                return JsonUtil.readValue(figureModel.getEffects(), Map.class);
            }
            return null;
        } catch (Exception e) {
            throw new Exception(e);
        }
    }

    /**
     * 后处理方法
     *
     * @param progress
     */
    public void postProcessing(VideoProgressModel progress) {

        if (progress.getSubmitParams() == null
                || CollectionUtils.isEmpty(progress.getSubmitParams().getRenderParams())) {
            log.debug("No render parameters, skip post processing");
            return;
        }

        var renderParams = progress.getSubmitParams().getRenderParams();

        var notifyHook = Boolean.valueOf(renderParams.getOrDefault(PostProcessingType.notifyHook.name(), "false"));

        // 如果不需要消息通知，就跳过
        if (!notifyHook) {
            return;
        }

        var reviewRequest = PostProcessingRequest.builder()
                .event(PostProcessingRequest.Event.video)
                .data(PostProcessingRequest.Data.builder()
                        .videoUrl(progress.getDownloadUrl())
                        .build())
                .parameters(PostProcessingRequest.Parameters.builder()
                        .extraInfo(renderParams.getOrDefault(RenderOpenParameters.extraInfo.name(), ""))
                        .build())
                .build();

        log.info("Send post process notify={}", reviewRequest);
        notifier.notifyAsync(reviewRequest);
    }

    private void switchStatusAndSave(VideoSession session,
                                     ProgressStatus after,
                                     Consumer<VideoProgressModel> additional) {

        var optional = videoProgressRepository.findByVideoId(session.getVideoId());
        if (optional.isEmpty()) {
            log.error("Cannot reach here , cannot find the video progress sessionId={}",
                    session.getSessionId());
            throw new DigitalHumanCommonException("Cannot find the video " + session.getVideoId()
                    + ", when status change to " + after);
        }
        if (optional.get().getStatus() != null
                && optional.get().getStatus().isTerminated()) {
            log.warn("status flow error, ignoring update status.  videoId:{}", optional.get().getVideoId());
            return;
        }
        if ((optional.get().getStatus() == ProgressStatus.FAILED
                || optional.get().getStatus() == ProgressStatus.ERROR)) {
            // 一个正常的视频合成进度不可能直接从failed 更新成succced，反而可能是源于更新了失败的视频合成人物，
            // 更新之后x264流才断，才合成视频，对于这种情况，我们直接将组织这次更新请求
            // FAILED 状态智能通过update请求切回SCHEDULED状态
            // TODO 更多的发生这样的错误，其实可以直接关闭当前的流
            throw new DigitalHumanCommonException(
                    "Fail to update the failed progress to " + after.name() + " status: " + session.getVideoId());
        }
        log.info("Start to update progress status of videoId={}, before={}, after={}", session.videoId,
                optional.get().getStatus(), after);
        var progress = optional.get();
        progress.setUpdateTime(ZonedDateTime.now());
        progress.setStatus(after);
        additional.accept(progress);
        progress = videoProgressRepository.save(progress);

        if ((after == ProgressStatus.ERROR || after == ProgressStatus.SUCCEED) &&
                StringUtils.isNotBlank(progress.getCallbackUrl())) {
//            finalProgressCallback.asyncCallback(progress);
        }
    }

    private String acquireIntelligentAnimoji(IntelligentAnimojiEmotionRequest intelligentAnimojiEmotionRequest) {
        var response = platformClient.intelligentText2Drml(intelligentAnimojiEmotionRequest);
        log.info("acquire IntelligentAnimojiEmotion Response: {}", response);
        return response.getDrml();
    }

    public void batchCanceleProgress(VideoBatchCanceleRequest request) {
        if (CollectionUtils.isEmpty(request.getVideoIds())) {
            return;
        }
        Iterable<VideoProgressModel> videos = videoProgressRepository.findAllByVideoIdIn(request.getVideoIds());

        List<VideoProgressModel> saves = Lists.newArrayList(videos).stream().filter(progress -> {
            return !(progress.getStatus() != null
                    && progress.getStatus().isTerminated());
        }).map(item -> {
            item.setCode(0);
            item.setMessage("");
            item.setShowMessage("");
            item.setStatus(ProgressStatus.CANCELED);
            return item;
        }).collect(Collectors.toList());
        if (saves.isEmpty()) {
            return;
        }
        videoProgressRepository.saveAll(saves);
    }

    public VideoProgressModel saveProgress(VideoProgressModel model) {
        return videoProgressRepository.save(model);
    }

    @Data
    @Builder
    public static class VideoSession {

        private String videoId;

        private String sessionId;

        @Builder.Default
        private String appId = "";

        @Builder.Default
        private boolean success = true;

        @Builder.Default
        private int code = 0;

        @Builder.Default
        private String message = "ok";

        @Builder.Default
        private ProgressStatus status = ProgressStatus.SCHEDULED;

        private String downloadUrl;

        private ProgressChangeCallback progressChangeCallback;

        private StreamObserver<Video> videoRequestObserver;

        private StreamObserver<X264Frame> x264RequestObserver;

        @Nullable
        private String callbackUrl;
    }

    /**
     * ProgressChangeCallback
     *
     * <AUTHOR> Zhensheng(<EMAIL>)
     * @since 2019-12-06
     */
    public interface ProgressChangeCallback {

        void onStatusChange(VideoSession session, ProgressStatus after, Object payload);
    }

    public static class VideoProgressManagerException extends Exception {
        VideoProgressManagerException(String msg, Throwable cause) {
            super(msg, cause);
        }
    }
}
