package com.baidu.acg.piat.digitalhuman.videopipeline.model.wrapper;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

import javax.annotation.Nullable;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.baidu.acg.piat.digitalhuman.common.exception.DigitalHumanCommonException;
import com.baidu.acg.piat.digitalhuman.common.image.Rect;
import com.baidu.acg.piat.digitalhuman.common.utils.PathUtil;
import com.baidu.acg.piat.digitalhuman.common.utils.StringUtil;
import com.baidu.acg.piat.digitalhuman.videopipeline.config.FFmpegConfigure.FFmpegConf;
import com.baidu.acg.piat.digitalhuman.videopipeline.exception.FFmpegBaseException;
import com.baidu.acg.piat.digitalhuman.videopipeline.exception.FFmpegFailedException;
import com.baidu.acg.piat.digitalhuman.videopipeline.exception.FFmpegTimeoutException;
import com.baidu.acg.piat.digitalhuman.videopipeline.model.VideoSource;

import lombok.Data;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

/**
 * 包装一些ffmpeg的指令
 *
 * <AUTHOR>
 */
@Data
@Slf4j
@Component(value = "fFmpegWrapper")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class FFmpegWrapper {

    private final FFmpegConf conf;

    @Getter
    @Setter
    private String lastErrorMessage = "";

    // region public method

    public Process createVideoFromImagesAsStream(Path outFile) throws FFmpegBaseException {
        List<String> command = new ArrayList<>();
        command.add("ffmpeg");
        command.add("-r");
        command.add(String.valueOf(conf.getFrameRate()));
        command.add("-f");
        command.add("image2pipe");
        command.add("-i");
        command.add("-");
        command.add(outFile.toAbsolutePath().toString());
        command.add("-y");

        return createProcessFromBuilder(command);
    }

    /**
     * @param imagePath
     * @param outFile
     * @param imageFormat
     * @param indexLength
     * @return
     * @throws IOException
     */
    public Path createVideoFromImages(Path imagePath, Path outFile, String imageFormat, int indexLength)
            throws IOException, FFmpegBaseException {
        beforeNewCommand();

        Files.deleteIfExists(outFile);

        /*
          -r rate  -> set frame rate (Hz value, fraction or abbreviation)
          -f Image file demuxer
          -i input file
          -y overwrite output files
         */
        var command = String.format("ffmpeg -r %f -f image2 -i %s %s -y",
                conf.getFrameRate(), "%" + indexLength + "d." + imageFormat, outFile.toAbsolutePath());
        exec(command, imagePath.toAbsolutePath());
        return outFile;
    }

    /**
     * @param videoFile
     * @param outFile
     * @return
     * @throws IOException
     */
    public Path createVideoThumbnail(Path videoFile, Path outFile)
            throws IOException, FFmpegBaseException {
        beforeNewCommand();

        Files.deleteIfExists(outFile);

        /*
          -r rate  -> set frame rate (Hz value, fraction or abbreviation)
          -f Image file demuxer
          -i input file
          -y overwrite output files
         */
        String command = "ffmpeg -i %s -r 25 -t 0.04 -qmin 1 -qmax 1 -qscale:v 1 -y -f image2 %s";
        command = String.format(command, videoFile.toAbsolutePath(), outFile.toAbsolutePath());
        exec(command, null);
        return outFile;
    }


    /**
     * 拼接多个音频片段（区别音频融合，是指音轨叠加）
     *
     * @param inputListFile 文件内容：
     *                      file '001.mp3'
     *                      file '002.mp3'
     * @param outFile
     */
    public Path joinAudioFragment(Path inputListFile, Path outFile)
            throws IOException, FFmpegBaseException {

        beforeNewCommand();

        if (!Files.exists(inputListFile)) {
            throw new IOException(String.format("input list file strange: %s please check",
                    inputListFile.toString()));
        }

        Files.deleteIfExists(outFile);

        /*
          -f file demuxer
          -i input
          -y input y
          -acodec recode to output file
          -safe 0 ignore error: unsafe file name
         */
        var command = String.format("ffmpeg -f concat -safe 0 -i %s -c:a libmp3lame %s -y",
                inputListFile.toAbsolutePath(), outFile.toAbsolutePath());
        exec(command, PathUtil.parent(inputListFile).toAbsolutePath());
        return outFile;
    }

    /**
     * 把音频合成到视频
     */
    public Path mixAudio2Video(float frameRate, Path video, Path audio, Path out, int audioSampleRate,
                               int audioChannelNum)
            throws IOException, FFmpegBaseException {

        beforeNewCommand();

        if (!Files.exists(video) || !Files.exists(audio)) {
            throw new IOException(String.format("video:%s or audio:%s is not exist",
                    video.toString(), audio.toString()));
        }

        /*
          -i input file
          -map ?
          -c copy metadata to new file
          -shortest choose shortest time length
         */
        var command = String.format("ffmpeg -r %f -i %s -f s16le -ar %d -ac %d -i %s -map 0:v -map 1:a -c:v copy " +
                        "-shortest %s -y",
                frameRate,
                video.toAbsolutePath(),
                audioSampleRate,
                audioChannelNum,
                audio.toAbsolutePath(),
                out.toAbsolutePath());
        exec(command, null);
        return out;
    }

    public Path mix2VideoByKeepInnerAspectRatio(VideoSource sourceVideo, VideoSource innerPicture, Path out,
                                                int width, int xCenterOffset, int yCenterOffset,
                                                int innerPictureOffsetMillis) throws FFmpegBaseException, IOException {
        String widthEval = Integer.toString(width);

        // If one and only one of the values is -n with n >= 1,
        // the scale filter will use a value that maintains the aspect ratio of the input image,
        // calculated from the other specified dimension. After that it will,
        // however, make sure that the calculated dimension is divisible by n and adjust the value if necessary.
        String heightEval = "-1";

        int widthUsage = (xCenterOffset - width / 2);
        String overlayXEval = "main_w/2+" + widthUsage;

        // 在知道高宽的情况下计算出来的real值
        String heightEvalReal = widthEval + "*(overlay_h/overlay_w)";

        String overlayYEval = "main_h/2-" + heightEvalReal + "/2-" + yCenterOffset;

        return mix2Video(sourceVideo, innerPicture, out,
                widthEval, heightEval, overlayXEval, overlayYEval,
                innerPictureOffsetMillis);
    }

    private Path mix2Video(VideoSource sourceVideo, VideoSource innerPicture,
                           Path out, String widthEval, String heightEval, String overlayXEval, String overlayYEval,
                           int innerPictureOffsetMillis) throws FFmpegBaseException, IOException {
        String command = String.format("ffmpeg -i %s -itsoffset %dms -i %s -filter_complex " +
                        "[1:v]scale=w=%s:h=%s[in];[0:v][in]overlay=x=%s:y=%s:eof_action=pass;[1:a]adelay=%d|%d[da1];" +
                        "[0:a][da1]amix=inputs=2:duration=first[a] -map [a] -ac 2 %s -y",
                sourceVideo.getSource(),
                innerPictureOffsetMillis,
                innerPicture.getSource(),
                widthEval,
                heightEval,
                overlayXEval,
                overlayYEval,
                innerPictureOffsetMillis,
                innerPictureOffsetMillis,
                out.toAbsolutePath());
        exec(command, null);
        return out;
    }

    public Path mix2Video(VideoSource sourceVideo, VideoSource innerPicture,
                          Path out, Rect innerPicturePosition, int innerPictureOffsetMillis)
            throws FFmpegBaseException, IOException {

        beforeNewCommand();

        String width = Integer.toString(innerPicturePosition.getWidth());
        String height = Integer.toString(innerPicturePosition.getHeight());

        String command = String.format("ffmpeg -i %s -itsoffset %dms -i %s -filter_complex " +
                        "[1:v]scale=w=%s:h=%s[in];[0:v][in]overlay=x=%d:y=%d:eof_action=pass;[1:a]adelay=%d|%d[da1];" +
                        "[0:a][da1]amix=inputs=2:duration=first[a] -map [a] -ac 2 %s -y",
                sourceVideo.getSource(),
                innerPictureOffsetMillis,
                innerPicture.getSource(),
                width,
                height,
                innerPicturePosition.getX(),
                innerPicturePosition.getY(),
                innerPictureOffsetMillis,
                innerPictureOffsetMillis,
                out.toAbsolutePath());
        exec(command, null);
        return out;
    }

    /**
     * 转换音频格式为s16le
     * @param input 输入音频路径
     * @param output 输出音频路径
     * @param audioSampleRate 采样率
     * @param audioChannelNum 声道
     * @return 输出音频路径
     * @throws IOException
     * @throws FFmpegBaseException
     */
    public Path convertAudio(Path input, Path output, int audioSampleRate, int audioChannelNum)
            throws IOException, FFmpegBaseException {
        String command = String.format("ffmpeg -y -f s16le -ar %d -ac %d -i %s %s",
                audioSampleRate, audioChannelNum, input, output);

        exec(command, null);
        return output;
    }
    // endregion public method

    // region private method

    private Process createProcessFromBuilder(List<String> command)
            throws FFmpegBaseException {
        ProcessBuilder processBuilder = new ProcessBuilder(command);
        processBuilder.redirectErrorStream(true);
        processBuilder.redirectOutput(ProcessBuilder.Redirect.PIPE);
        processBuilder.redirectInput(ProcessBuilder.Redirect.PIPE);

        try {
            return processBuilder.start();
        } catch (IOException exception) {
            log.warn("set ffmpeg command failed", exception);
            throw new FFmpegFailedException("cannot set up ffmpeg command");
        }
    }

    private void beforeNewCommand() {
        this.setLastErrorMessage("");
    }

    /**
     * @param command command
     * @param workDir path
     * @return
     */
    private void exec(String command, Path workDir)
            throws IOException, FFmpegBaseException {
        log.info("command: {}", command);

        Process process;
        if (Objects.isNull(workDir)) {
            process = Runtime.getRuntime().exec(command);
            clearProcessStream(process, true);
            clearProcessStream(process, false);
            log.info("process info={}", process.info());
        } else {
            log.info("work dir: {}", workDir);
            process = Runtime.getRuntime().exec(command, null, workDir.toFile());
        }

        var result = false;
        try {
            result = process.waitFor(conf.getExeTimeoutSeconds(), TimeUnit.SECONDS);
        } catch (InterruptedException e) {
            throw new FFmpegBaseException(String.format("command: %s was interrupted. detail:%s",
                    command, e.getMessage()));
        }

        if (!result) {
            var errorMessage = String.format(
                    "command:%s timeout %s.",
                    command,
                    Objects.isNull(workDir) ? "" : workDir.toString()
            );
            throw new FFmpegTimeoutException(errorMessage);
        } else if (process.exitValue() != 0) {
            var errorMessage = StringUtil.fromInputStream(process.getErrorStream());
            throw new FFmpegFailedException(process.exitValue(), errorMessage);
        }
    }

    private static void clearProcessStream(Process process, boolean std) {
        new Thread(() -> {
            while (process.isAlive()) {
                log.debug("Process output std={}, content={}", std, StringUtil.fromInputStream(std ?
                        process.getInputStream() : process.getErrorStream()));
                try {
                    TimeUnit.MILLISECONDS.sleep(100);
                } catch (InterruptedException e) {
                    // nothing
                }
            }
        }).start();
    }

    public Path chromakey(int width, int height, Path videoPath, Path vertexShaderPath, Path fragmentShaderPath,
        @Nullable Path postVertexShaderPath, @Nullable Path postFragmentShaderPath) throws FFmpegBaseException, IOException {

        String srcVideoPathStr = videoPath.toAbsolutePath().toString();
        String dstVideoPathStr = srcVideoPathStr.substring(0, srcVideoPathStr.length() - 4) + ".webm";
        Path result = Path.of(dstVideoPathStr);

        String postShaderFilterStr = "";
        if (postVertexShaderPath != null && postFragmentShaderPath != null) {
            postShaderFilterStr = String.format("plusglshader=sdsource='%s':vxsource='%s'[out2];[out2]",
                postFragmentShaderPath, postVertexShaderPath);
        }

        String[] command = new String[] {
            "xvfb-run", "-a",
            String.format("--server-args=-screen 0 %dx%dx24 -ac -nolisten tcp -dpi 96 +extension RANDR", width,
                height),
            "ffmpeg", "-i", videoPath.toAbsolutePath().toString(),
            "-filter_complex",
            String.format("[0:v]format=rgba,plusglshader=sdsource='%s':vxsource='%s'[out1];[out1]", fragmentShaderPath,
                vertexShaderPath) +
                postShaderFilterStr +
                String.format("scale=%d:%d,format=yuva420p", width, height),
            "-vcodec", "libvpx", "-b:v", "5000k",
            "-cpu-used", "4",
            "-threads", "4",
            "-acodec", "libopus",
            "-pix_fmt", "yuva420p",
            "-auto-alt-ref", "0",
            "-y", dstVideoPathStr
        };

        Process process = Runtime.getRuntime().exec(command);
        clearProcessStream(process, true);
        clearProcessStream(process, false);
        log.info("command={},process info={}", command, process.info());
        var processResult = false;
        try {
            processResult = process.waitFor(conf.getChromakeyTimeoutMinutes(), TimeUnit.MINUTES);
        } catch (InterruptedException e) {
            throw new FFmpegBaseException(String.format("command: %s was interrupted. detail:%s",
                    command, e.getMessage()));
        }

        if (!processResult) {
            var errorMessage = String.format("command:%s timeout.", command);
            throw new FFmpegTimeoutException(errorMessage);
        } else if (process.exitValue() != 0) {
            var errorMessage = StringUtil.fromInputStream(process.getErrorStream());
            throw new FFmpegFailedException(process.exitValue(), errorMessage);
        }

        if (Files.exists(result)) {
            return result;
        } else {
            throw new DigitalHumanCommonException("No dst video found");
        }
    }

    // endregion private method
}
