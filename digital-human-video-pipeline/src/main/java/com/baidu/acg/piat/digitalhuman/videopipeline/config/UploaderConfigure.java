package com.baidu.acg.piat.digitalhuman.videopipeline.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import com.baidu.acg.piat.digitalhuman.videopipeline.model.UploadUploaderImpl.UploaderConfig;

@Configuration
public class UploaderConfigure {

    @Bean
    @ConfigurationProperties(prefix = "digitalhuman.videopipeline.uploader")
    public UploaderConfig uploaderConfig() {
        return new UploaderConfig();
    }
}
