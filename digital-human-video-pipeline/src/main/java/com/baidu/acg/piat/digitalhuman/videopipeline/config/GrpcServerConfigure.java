// Copyright (C) 2019 Baidu Inc. All rights reserved.
package com.baidu.acg.piat.digitalhuman.videopipeline.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;
import org.springframework.util.SocketUtils;

import com.baidu.acg.piat.digitalhuman.videopipeline.grpc.VideoPipelineGrpcServerInterceptor;

/**
 * <AUTHOR>
 */
@Configuration
public class GrpcServerConfigure {

    @Bean
    @Profile("production")
    @ConfigurationProperties("digitalhuman.videopipeline.grpc.config")
    public Config config() {
        return new Config();
    }

    @Bean
    @Profile("unit-test")
    public Config unitTestConfig() {
        int randomPort = SocketUtils.findAvailableTcpPort(4000);
        var config = new Config();
        config.setPort(randomPort);
        config.setTerminateAwaitSeconds(3);
        config.setPermitKeepAliveTimeMilliSeconds(500);
        config.setKeepAliveTimeMilliSeconds(30000);
        return config;
    }

    @Bean
    public VideoPipelineGrpcServerInterceptor videoPipelineGrpcServerInterceptor() {
        return new VideoPipelineGrpcServerInterceptor();
    }

    @Data
    public static class Config {
        private int port = 8090;

        private int terminateAwaitSeconds = 3;

        private long permitKeepAliveTimeMilliSeconds = 500; // 允许的最小ping周期

        private long keepAliveTimeMilliSeconds = 30000; // 设置保活的ping周期

    }
}
