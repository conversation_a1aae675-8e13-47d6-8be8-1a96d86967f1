package com.baidu.acg.piat.digitalhuman.videopipeline.proxy.service.impl;

import com.baidu.acg.piat.digitalhuman.videopipeline.proxy.persistence.VideoProgressModel;
import com.baidu.acg.piat.digitalhuman.videopipeline.proxy.service.ChromaKeyService;

import java.nio.file.Path;

public class MockChromaKeyServiceImpl implements ChromaKeyService {

    @Override
    public Path chromaKeyV2(Path videoPath, ChromaKeyParamV2 param) {
        return videoPath;
    }

    @Override
    public boolean microChromaKeyV2(Path videoPath, ChromaKeyParamV2 param, VideoProgressModel progress) {
        return false;
    }

    @Override
    public Path chromaKeyV1(Path videoPath, ChromaKeyParamV1 param) {
        return videoPath;
    }

    @Override
    public boolean microChromaKeyV1(Path videoPath, ChromaKeyParamV1 param, VideoProgressModel progress) {
        return false;
    }
}
