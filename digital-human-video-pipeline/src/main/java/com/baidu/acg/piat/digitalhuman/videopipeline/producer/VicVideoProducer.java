package com.baidu.acg.piat.digitalhuman.videopipeline.producer;

import com.baidu.acg.piat.digitalhuman.common.audio.WavUtil;
import com.baidu.acg.piat.digitalhuman.common.console.video.ProgressStatus;
import com.baidu.acg.piat.digitalhuman.common.console.video.VideoFailRequest;
import com.baidu.acg.piat.digitalhuman.common.console.video.VideoUpdateRequest;
import com.baidu.acg.piat.digitalhuman.common.exception.DigitalHumanCommonException;
import com.baidu.acg.piat.digitalhuman.common.exception.Error;
import com.baidu.acg.piat.digitalhuman.common.exception.VisKouboError;
import com.baidu.acg.piat.digitalhuman.common.helper.VideoErrorShowMsgHelper;
import com.baidu.acg.piat.digitalhuman.common.project.TtsParams;
import com.baidu.acg.piat.digitalhuman.common.utils.IDGenerator;
import com.baidu.acg.piat.digitalhuman.storage.service.StorageService;
import com.baidu.acg.piat.digitalhuman.tts.TtsStreamingService;
import com.baidu.acg.piat.digitalhuman.tts.model.TtsQueryParams;
import com.baidu.acg.piat.digitalhuman.tts.model.TtsResult;
import com.baidu.acg.piat.digitalhuman.videopipeline.exception.VideoProducerServiceException;
import com.baidu.acg.piat.digitalhuman.videopipeline.producer.client.VicVideoClient;
import com.baidu.acg.piat.digitalhuman.videopipeline.proxy.persistence.VideoProgressModel;
import com.baidu.acg.piat.digitalhuman.videopipeline.proxy.service.VideoFinalProgressCallback;
import com.baidu.acg.piat.digitalhuman.videopipeline.proxy.service.VideoProgressManager;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;

@Slf4j
@RequiredArgsConstructor
public class VicVideoProducer{
    private final VideoProgressManager videoProgressManager;

    private final TtsStreamingService ttsService;

    private final IDGenerator idGenerator;

    private final VideoErrorShowMsgHelper.ErrorMsgConf videoErrorMsgConf;

    private final StorageService storageService;

    private final VicVideoClient vicVideoClient;

    private final VideoFinalProgressCallback videoFinalProgressCallback;

    private final Map<String, VideoProgressModel> vicTaskId2VideoProgressModel = new ConcurrentHashMap<>();

    @Value("${digitalhuman.videopipeline.ttsTimeout}")
    private int ttsTimeout = 60;

    public void produceVideo(VideoProgressModel videoProgressModel, String modelUrl,
                             TtsParams tts, VicModel vicModel) {
        String videoId = videoProgressModel.getVideoId();

        videoProgressManager.updateVideoProgress(VideoUpdateRequest.builder().
                id(videoProgressModel.getVideoId()).
                status(ProgressStatus.SCHEDULED).build());
        String audio;
        long time;
        try {
            String text = videoProgressModel.getText();
            byte[] bytes = getTtsResult(text, ttsQueryParams(tts));
            String audioKey = String.join("/", "v2", "audio", idGenerator.generate("a") + ".wav");
            audio = storageService.save(bytes, audioKey).toString();
            time = bytes.length / 32L;
        } catch (InterruptedException | StorageService.ResourceException | DigitalHumanCommonException e) {
            log.error("get tts failed because " + e.getMessage());
            videoRenderError(videoId, "failed to get tts");
            return;
        }
        videoProgressManager.updateVideoProgress(VideoUpdateRequest.builder().id(videoId)
                .status(ProgressStatus.PREPARE_RENDER).audioUrl(audio).build());
        log.debug("Try to request vic server with audio:{}, duration:{}, video id:{}", audio, time, videoId);
        String vicTaskId = videoId;

        vicTaskId2VideoProgressModel.put(vicTaskId, videoProgressModel);
        try {
            vicVideoClient.submitVideoTask(modelUrl, audio, vicTaskId, vicModel, videoProgressModel);
        } catch (Exception e) {
            log.error("submit video task failed because " + e.getMessage());
            videoRenderError(videoId, "failed to render video");
        }
    }

    private TtsQueryParams ttsQueryParams(TtsParams ttsParams) {
        TtsQueryParams.TtsQueryParamsBuilder builder = TtsQueryParams.builder();
        if (ttsParams != null) {
            builder.per(ttsParams.getPerson())
                    .pit(ttsParams.getPitch())
                    .spd(ttsParams.getSpeed())
                    .vol(ttsParams.getVolume()).extraParams(ttsParams.getExtraParams());
        }
        return builder.build();
    }

    public byte[] getTtsResult(String text, TtsQueryParams queryParams) throws InterruptedException {
        CountDownLatch latch = new CountDownLatch(1);
        AtomicReference<TtsResult> ttsResult = new AtomicReference<>(new TtsResult());
        ttsService.textToAudio(text, queryParams
                , (ttsStreamingResult) -> {
                    log.debug("Receive tts result, index: {}, length: {}, is completed: {}",
                            ttsStreamingResult.getIndex(), ttsStreamingResult.getResult().getAudio().length,
                            ttsStreamingResult.isCompleted());
                    if (ttsStreamingResult.getErrCode() != 0) {
                        log.error("partial failed ,tts task cancelled");
                        latch.countDown();
                    }

                    if (ttsStreamingResult.isCompleted()) {
                        log.debug("tts streaming compute complete，audio size: {}",
                                ttsStreamingResult.getResult().getAudio().length);
                        var wavAudio = WavUtil.attachHeader(ttsStreamingResult.getResult().getAudio(),
                                (short) 1, 16000,
                                (short) 16);
                        log.debug("attached wav header to audio，wav audio size: {}", wavAudio.length);
                        ttsResult.get().setAudio(wavAudio);
                        latch.countDown();
                    }
                });
        if (!latch.await(ttsTimeout, TimeUnit.SECONDS)) {
            log.error("tts overall timeout,limit = {}", ttsTimeout);
        }
        if (ttsResult.get().getAudio() == null) {
            throw new DigitalHumanCommonException("获取音频失败");
        }
        return ttsResult.get().getAudio();
    }

    public void vicVideoResultCallback(VicVideoClient.ValidVicResponse validVicResponse) {
        if (validVicResponse == null) {
            throw new VideoProducerServiceException("Invalid result form vic server");
        }

        VideoProgressModel videoProgressModel = vicTaskId2VideoProgressModel.remove(validVicResponse.getTaskId());
        if (videoProgressModel == null) {
            log.debug("task id = {} may timeout", validVicResponse.getTaskId());
            return;
        }

        if (validVicResponse.getErrCode() != 0) {
            videoRenderError(videoProgressModel.getVideoId(),
                    validVicResponse.getErrMsg() + ", vicErrorCode: " + validVicResponse.getErrCode());
        } else {
            VideoUpdateRequest videoUpdateRequest = VideoUpdateRequest.builder().id(videoProgressModel.getVideoId())
                    .status(ProgressStatus.SUCCEED)
                    .audioUrl(videoProgressModel.getAudioUrl())
                    .downloadUrl(validVicResponse.getData().getVideoUrl()).build();
            videoProgressManager.updateVideoProgress(videoUpdateRequest);
            log.debug("video render success, video id: {}", videoProgressModel.getVideoId());
        }
        videoFinalProgressCallback.asyncCallback(videoProgressModel);
    }


    // todo: 调用star light获取人像模型包url
    private String getFigureModelUrl(String figureId) {
        return null;
    }

    private void videoRenderError(String videoId, String errMsg) {
        Map<String, Object> result = new HashMap<>();
        result.put("errorCode", Error.VIDEO_PROGRESS_RENDER_ERROR.getCode());
        result.put("errorMessage", Error.VIDEO_PROGRESS_RENDER_ERROR.getMessage() +
                ", cause: " + errMsg);
        String content = errMsg;

        handleVisKouboError(content, VisKouboError.IMAGE_VALIDATE_FAILED, result);
        handleVisKouboError(content, VisKouboError.MISSING_REQUIRED_PARAMETERS, result);
        handleVisKouboError(content, VisKouboError.BAD_REQUEST, result);
        handleVisKouboError(content, VisKouboError.POSITION_VALIDATE_FAILED, result);
        handleVisKouboError(content, VisKouboError.PARAMETER_VALUE_ERROR, result);
        handleVisKouboError(content, VisKouboError.TTS_VALIDATE_FAILED, result);
        handleVisKouboError(content, VisKouboError.SERVER_ERROR, result);
        handleVisKouboError(content, VisKouboError.ADD_TASK_ERROR, result);
        handleVisKouboError(content, VisKouboError.TASK_ID_ERROR, result);
        handleVisKouboError(content, VisKouboError.DOWNLOAD_VIDEO_ERROR, result);
        handleVisKouboError(content, VisKouboError.CALLBACK_ERROR, result);
        handleVisKouboError(content, VisKouboError.FAILED_TO_CREATE_VIS_TASK, result);
        handleVisKouboError(content, VisKouboError.FAILED_TO_QUERY_VIS_TASK, result);
        handleVisKouboError(content, VisKouboError.FAILED_TO_GET_VIS_VIDEO, result);
        handleVisKouboError(content, VisKouboError.FAILED_TO_UPLOAD_VIS_VIDEO, result);
        handleVisKouboError(content, VisKouboError.VIS_VIDEO_TIMEOUT, result);
        handleVisKouboError(content, VisKouboError.FAILED_TO_UPDATE_VIDEO_PROGRESS, result);
        int errorCode = (int) result.get("errorCode");
        String errorMessage = (String) result.get("errorMessage");

        videoProgressManager.failProgress(VideoFailRequest.builder()
                .id(videoId)
                .errorCode(errorCode)
                .errorMsg(errorMessage)
                .showErrMsg(videoErrorMsgConf.getRenderErrorMsg())
                .build());
    }

    private static Map<String, Object> handleVisKouboError(String content, VisKouboError errorType, Map<String, Object> result) {
        if (content.contains("VisKouboError:" + String.valueOf(errorType.getSrcCode()))) {
            result.put("errorCode", errorType.getDstCode());
            result.put("errorMessage", Error.VIDEO_PROGRESS_RENDER_ERROR.getMessage() +
                    ", cause: " + errorType.getMessage());
        }
        return result;
    }

    @Data
    public static class CharacterConfig {
        private TtsParams tts;

        private String figureId;

        private String modelUrl;

        private VicModel vicModel;
    }

    public static enum VicModel {
        V2, V4;
    }

}
