<assembly xmlns="http://maven.apache.org/ASSEMBLY/2.0.0"
          xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
          xsi:schemaLocation="http://maven.apache.org/ASSEMBLY/2.0.0 http://maven.apache.org/xsd/assembly-2.0.0.xsd">
    <id>release-package</id>
    <formats>
        <format>dir</format>
    </formats>
    <includeBaseDirectory>false</includeBaseDirectory>
    <dependencySets>
        <dependencySet>
            <outputDirectory>/lib</outputDirectory>
        </dependencySet>
    </dependencySets>
    <fileSets>
        <fileSet>
            <directory>${basedir}/libs</directory>
            <outputDirectory>${file.separator}lib</outputDirectory>
        </fileSet>
        <fileSet>
            <directory>${basedir}/src/main/resources</directory>
            <excludes>
                <exclude>assembly/**</exclude>
            </excludes>
            <outputDirectory>${file.separator}conf</outputDirectory>
        </fileSet>
    </fileSets>
</assembly>