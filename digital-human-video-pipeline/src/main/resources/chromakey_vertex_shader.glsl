/*
const vec4 screen=vec4(28./255.,179./255.,110./255.,1.);
const float screenWeight=1.;
const float balance=1.;
const float clipBlack=0.;
const float clipWhite=1.;
*/


attribute vec2 position;
attribute vec2 texCoord;

varying vec4 screen_v;
varying float screenWeight_v;
varying float balance_v;
varying float clipBlack_v;
varying float clipWhite_v;
varying float screenSat;
varying vec3 screenPrimary;
varying vec2 vTexCoord;

void main(void){
    screen_v = screen;
    screenWeight_v = screenWeight;
    balance_v = balance;
    clipBlack_v = clipBlack;
    clipWhite_v = clipWhite;

    float fmin=min(min(screen.r,screen.g),screen.b);
    float fmax=max(max(screen.r,screen.g),screen.b);
    float secondaryComponents;
    screenPrimary=step(fmax,screen.rgb);
    secondaryComponents=dot(1.-screenPrimary,screen.rgb);
    screenSat=fmax-mix(secondaryComponents-fmin,secondaryComponents/2.,balance);

    gl_Position=vec4(position,0,1);
    vTexCoord=position;
}