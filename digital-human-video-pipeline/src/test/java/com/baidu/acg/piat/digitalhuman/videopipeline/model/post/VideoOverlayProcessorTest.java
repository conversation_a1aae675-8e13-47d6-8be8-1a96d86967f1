package com.baidu.acg.piat.digitalhuman.videopipeline.model.post;

import com.baidu.acg.piat.digitalhuman.common.image.Rect;
import com.baidu.acg.piat.digitalhuman.common.widget.Position;
import com.baidu.acg.piat.digitalhuman.videopipeline.config.WidgetRenderConfigure;
import com.baidu.acg.piat.digitalhuman.videopipeline.exception.FFmpegBaseException;
import com.baidu.acg.piat.digitalhuman.videopipeline.model.ImageSize;
import com.baidu.acg.piat.digitalhuman.videopipeline.model.VideoSource;
import com.baidu.acg.piat.digitalhuman.videopipeline.model.wrapper.FFmpegWrapper;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.mockito.*;

import java.io.IOException;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.rmi.UnexpectedException;
import java.util.PriorityQueue;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

@Slf4j
class VideoOverlayProcessorTest {

    @Mock(answer = Answers.RETURNS_DEEP_STUBS)
    FFmpegWrapper fFmpegWrapper;

    WidgetRenderConfigure.Config widgetRenderConfig = new WidgetRenderConfigure.Config(0.5);

    VideoOverlayProcessor videoOverlayProcessor;

    ImageSize resolution = new ImageSize(1080, 1920);

    VideoOverlayProcessor.VideoOverlayOptions videoOverlayOptions;

    @BeforeEach
    void beforeEach() {
        MockitoAnnotations.initMocks(this);

        videoOverlayOptions = VideoOverlayProcessor.VideoOverlayOptions.builder()
                .innerPicturePosition(Position.builder()
                        .aspectRatio(null)
                        .width(500)
                        .widthRatio(null)
                        .xCenterOffset(0.0)
                        .yCenterOffset(-0.3)
                        .build())
                .offsetMillsSeconds(0)
                .sessionId("sessionId")
                .videoSource(new VideoSource.LocalFileVideoSource(
                        Paths.get("/Users/<USER>/tmpmov_bbb.mp4")))
                .build();

        videoOverlayProcessor = new VideoOverlayProcessor(
                resolution,
                widgetRenderConfig, fFmpegWrapper, videoOverlayOptions);
    }

    @Test
    public void test_handleWithoutWidth() throws FFmpegBaseException, IOException {
        videoOverlayOptions.getInnerPicturePosition().setAspectRatio(null);
        videoOverlayOptions.getInnerPicturePosition().setWidth(null);
        videoOverlayOptions.getInnerPicturePosition().setXCenterOffset(0.1);
        videoOverlayOptions.getInnerPicturePosition().setYCenterOffset(null);

        ArgumentCaptor<Integer> widthCaptor = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<Integer> xCenterOffset = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<Integer> yCenterOffset = ArgumentCaptor.forClass(Integer.class);
        assertThrows(UnexpectedException.class, () -> {
            videoOverlayProcessor.handle(Path.of("/Users/<USER>/tmp", "60f39f717a5d1d6d3692cecd.mp4"),
                    Path.of("/Users/<USER>/tmp", UUID.randomUUID().toString()));
        });

        verify(fFmpegWrapper, times(1)).mix2VideoByKeepInnerAspectRatio(
                any(VideoSource.class), any(VideoSource.class), any(Path.class),
                widthCaptor.capture(), xCenterOffset.capture(), yCenterOffset.capture(), anyInt());
        assertEquals(widthCaptor.getValue(), (int) (resolution.getWidth() * widgetRenderConfig.getDefaultWidthRatio()));
        assertEquals(xCenterOffset.getValue(),
                (int) ((videoOverlayOptions.getInnerPicturePosition().getXCenterOffset()) * resolution.getWidth()));
        assertEquals(yCenterOffset.getValue(),
                (int) ((videoOverlayOptions.getInnerPicturePosition().getYCenterOffset()) * resolution.getHeight()));
    }

    @Test
    public void test_handleWithoutAspectRatio() throws IOException, FFmpegBaseException {
        videoOverlayOptions.getInnerPicturePosition().setAspectRatio(null);
        videoOverlayOptions.getInnerPicturePosition().setWidth(100);
        videoOverlayOptions.getInnerPicturePosition().setXCenterOffset(0.1);
        videoOverlayOptions.getInnerPicturePosition().setYCenterOffset(0.2);
        ArgumentCaptor<Integer> widthCaptor = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<Integer> xCenterOffset = ArgumentCaptor.forClass(Integer.class);
        ArgumentCaptor<Integer> yCenterOffset = ArgumentCaptor.forClass(Integer.class);
        assertThrows(UnexpectedException.class, () -> {
            videoOverlayProcessor.handle(Path.of("/Users/<USER>/tmp", "60f39f717a5d1d6d3692cecd.mp4"),
                    Path.of("/Users/<USER>/tmp", UUID.randomUUID().toString()));
        });

        verify(fFmpegWrapper, times(1)).mix2VideoByKeepInnerAspectRatio(
                any(VideoSource.class), any(VideoSource.class), any(Path.class),
                widthCaptor.capture(), xCenterOffset.capture(), yCenterOffset.capture(), anyInt());
        assertEquals(widthCaptor.getValue(), videoOverlayOptions.getInnerPicturePosition().getWidth());
        assertEquals(xCenterOffset.getValue(),
                (int) ((videoOverlayOptions.getInnerPicturePosition().getXCenterOffset()) * resolution.getWidth()));
        assertEquals(yCenterOffset.getValue(),
                (int) ((videoOverlayOptions.getInnerPicturePosition().getYCenterOffset()) * resolution.getHeight()));
    }

    @Test
    public void test_handleWithAspectRatio() throws IOException, FFmpegBaseException {
        videoOverlayOptions.getInnerPicturePosition().setAspectRatio(0.3);
        videoOverlayOptions.getInnerPicturePosition().setWidth(100);
        videoOverlayOptions.getInnerPicturePosition().setXCenterOffset(0.1);
        videoOverlayOptions.getInnerPicturePosition().setYCenterOffset(0.2);
        ArgumentCaptor<Rect> positionCaptor = ArgumentCaptor.forClass(Rect.class);
        assertThrows(UnexpectedException.class, () -> {
            videoOverlayProcessor.handle(Path.of("/Users/<USER>/tmp", "60f39f717a5d1d6d3692cecd.mp4"),
                    Path.of("/Users/<USER>/tmp", UUID.randomUUID().toString()));
        });

        verify(fFmpegWrapper, times(1)).mix2Video(
                any(VideoSource.class), any(VideoSource.class), any(Path.class), positionCaptor.capture(), anyInt());
        Rect position = positionCaptor.getValue();
        assertEquals(position.getWidth(), videoOverlayOptions.getInnerPicturePosition().getWidth());
        assertEquals(position.getHeight(),
                (int) (videoOverlayOptions.getInnerPicturePosition().getWidth() / videoOverlayOptions.getInnerPicturePosition().getAspectRatio()));
        assertEquals(position.getX(),
                resolution.getWidth() / 2 - (int) ((videoOverlayOptions.getInnerPicturePosition().getXCenterOffset()) * resolution.getWidth()) - position.getWidth() / 2);

        assertEquals(position.getY(),
                resolution.getHeight() / 2 - (int) ((videoOverlayOptions.getInnerPicturePosition().getYCenterOffset()) * resolution.getHeight()) - position.getHeight() / 2);
    }

    @Test
    public void test_videoOverlayOptionCompare() {
        PriorityQueue<VideoOverlayProcessor.VideoOverlayOptions> options = new PriorityQueue<>();
        options.add(VideoOverlayProcessor.VideoOverlayOptions.builder()
                .offsetMillsSeconds(9)
                .build());
        options.add(VideoOverlayProcessor.VideoOverlayOptions.builder()
                .offsetMillsSeconds(100)
                .build());
        options.add(VideoOverlayProcessor.VideoOverlayOptions.builder()
                .offsetMillsSeconds(4)
                .build());
        int preview = -1;
        for (int i = 0; i < options.size(); i++) {
            VideoOverlayProcessor.VideoOverlayOptions option = options.poll();
            assertTrue(option.getOffsetMillsSeconds() > preview);
            preview = option.getOffsetMillsSeconds();
        }
    }
}