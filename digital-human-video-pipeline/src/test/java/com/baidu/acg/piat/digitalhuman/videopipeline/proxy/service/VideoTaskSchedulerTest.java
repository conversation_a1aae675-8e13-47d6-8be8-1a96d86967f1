// Copyright (C) 2021 Baidu Inc. All rights reserved.

package com.baidu.acg.piat.digitalhuman.videopipeline.proxy.service;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.fasterxml.jackson.core.JsonProcessingException;
import org.apache.curator.framework.CuratorFramework;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.Spy;

import java.time.ZonedDateTime;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

import com.baidu.acg.piat.digitalhuman.common.console.video.ProgressStatus;
import com.baidu.acg.piat.digitalhuman.common.console.video.RetryPolicy;
import com.baidu.acg.piat.digitalhuman.common.console.video.Text2VideoParams;
import com.baidu.acg.piat.digitalhuman.videopipeline.proxy.client.VideoTaskScheduleClient;
import com.baidu.acg.piat.digitalhuman.videopipeline.proxy.config.ProgressSchedulerConfigure;
import com.baidu.acg.piat.digitalhuman.videopipeline.proxy.persistence.VideoProgressModel;
import com.baidu.acg.piat.digitalhuman.videopipeline.proxy.persistence.VideoProgressRepository;
import com.baidu.acg.piat.digitalhuman.videopipeline.proxy.service.impl.VideoTaskSchedulerZkImpl;

/**
 * VideoTaskSchedulerTest
 *
 * <AUTHOR> Zhensheng(<EMAIL>)
 * @date 2021-01-13
 */
class VideoTaskSchedulerTest {

    private ScheduledExecutorService scheduledExecutorService = Executors.newScheduledThreadPool(2);

    @Mock
    private VideoProgressRepository videoProgressRepository;

    @Mock
    private CuratorFramework curatorFramework;

    @Spy
    private ProgressSchedulerConfigure.Config config;

    @Mock
    private VideoTaskScheduleClient videoTaskScheduleClient;

    @Mock
    private VideoFinalProgressCallback videoFinalProgressCallback;

    @Spy
    private ExecutorService scheduleTaskExecutor = Executors.newFixedThreadPool(2);

    @InjectMocks
    private VideoTaskSchedulerZkImpl videoTaskScheduler;

    private VideoProgressModel scheduleNotAtTime;

    private VideoProgressModel needRetry;

    private VideoProgressModel firstRetry;

    private VideoProgressModel invalidModel;

    private VideoProgressModel expireModel;

    private AtomicInteger scheduleCnt;

    @BeforeEach
    public void setUp() throws JsonProcessingException {

        MockitoAnnotations.initMocks(this);

        config.setScanIntervalMillis(2000);

        needRetry = create("needRetry");
        scheduleNotAtTime = create("scheduleNotAtTime");
        scheduleNotAtTime.setLastScheduleFinishTime(ZonedDateTime.now());

        firstRetry = create("firstRetry");
        firstRetry.setStatus(ProgressStatus.SUBMIT);
        firstRetry.setScheduledTimes(0);

        invalidModel = create("invalidModel");
        invalidModel.setRetryPolicy(null);

        expireModel = create("expire");
        expireModel.setSubmitTime(ZonedDateTime.now().minusHours(25));

        scheduleCnt = new AtomicInteger(0);

        when(videoProgressRepository.findAllByStatusNameIn(any()))
                .thenReturn(List.of(needRetry, scheduleNotAtTime, firstRetry, invalidModel, expireModel));

        scheduledExecutorService.schedule(() -> {
            videoTaskScheduler.stopScan();
        }, 2, TimeUnit.SECONDS);

        Mockito.doAnswer(invocation -> {
            scheduleCnt.incrementAndGet();
            return null;
        }).when(videoTaskScheduleClient).scheduleVideoTask(any());

    }

    private VideoProgressModel create(String videoID) throws JsonProcessingException {
        Text2VideoParams params = new Text2VideoParams();
        params.setAppId("testId");
        params.setAppKey("appKey");
        VideoProgressModel result = new VideoProgressModel();
        result.setVideoId(videoID);
        result.setStatus(ProgressStatus.FAILED);
        result.setScheduledTimes(1);
        result.setLastScheduleFinishTime(ZonedDateTime.now().minusSeconds(100));
        result.setRetryPolicy(RetryPolicy.defaultPolicy);
        result.setSubmitParams(params);
        return result;
    }

    @Test
    public void testScanTask() {

        videoTaskScheduler.scanProgressesToBeScheduled();

        Assertions.assertEquals(2, scheduleCnt.get());

    }

}