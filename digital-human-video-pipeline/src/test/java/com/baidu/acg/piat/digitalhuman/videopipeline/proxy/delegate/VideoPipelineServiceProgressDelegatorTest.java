// Copyright (C) 2020 Baidu Inc. All rights reserved.

package com.baidu.acg.piat.digitalhuman.videopipeline.proxy.delegate;

import com.google.protobuf.TextFormat;
import io.grpc.stub.StreamObserver;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.core.env.Environment;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import java.util.Arrays;
import java.util.UUID;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;

import com.baidu.acg.piat.digitalhuman.common.console.video.ProgressStatus;
import com.baidu.acg.piat.digitalhuman.videopipeline.proxy.persistence.VideoProgressModel;
import com.baidu.acg.piat.digitalhuman.videopipeline.proxy.persistence.VideoProgressRepository;
import com.baidu.acg.piat.digitalhuman.videopipeline.proxy.service.VideoProgressManager;
import com.baidu.acg.pie.UploadServiceGrpc;
import com.baidu.acg.pie.UploadServiceOuterClass;

/**
 * VideoPipelineServiceProgressDelagatorTest
 *
 * <AUTHOR> Zhensheng(<EMAIL>)
 * @since 2020-02-10
 */
@Disabled
@Slf4j
@ExtendWith(SpringExtension.class)
@SpringBootTest
@ActiveProfiles(value = "unit-test")
public class VideoPipelineServiceProgressDelegatorTest {

    @Autowired
    private VideoProgressRepository videoProgressRepository;

    @Autowired
    private VideoProgressManager videoProgressManager;

    @Autowired
    private Environment environment;

    private VideoPipelineServiceProgressDelegator delegator;

    @BeforeEach
    public void init() {
        log.info("activated profile: {}", Arrays.toString(environment.getActiveProfiles()));

        delegator = new VideoPipelineServiceProgressDelegator(new MockService(), videoProgressManager);
        videoProgressRepository.deleteAll();
        videoProgressRepository.save(VideoProgressModel.builder().sessionId("testId").videoId("testId").build());

    }

    @AfterEach
    public void destroy() {
        videoProgressRepository.deleteAll();

    }

    @Test
    public void x264() {
        var requestObserver = delegator.x264(new StreamObserver<UploadServiceOuterClass.Response>() {
            @Override
            public void onNext(UploadServiceOuterClass.Response value) {
                System.out.println("onResponse" + TextFormat.shortDebugString(value));
            }

            @Override
            public void onError(Throwable t) {

            }

            @Override
            public void onCompleted() {

            }
        });

        for (int i = 0; i < 5; i++) {
            requestObserver.onNext(UploadServiceOuterClass.X264Frame.newBuilder()
                    .setVideoId("testId")
                    .setFrameId(UUID.randomUUID().toString())
                    .setFlagWriteDown(false).build());
        }
        var optional = videoProgressRepository.findBySessionId("testId");
        Assertions.assertNotNull(optional.get());
        Assertions.assertEquals(optional.get().getStatus(), ProgressStatus.RENDERING);

        requestObserver.onCompleted();

        optional = videoProgressRepository.findBySessionId("testId");
        Assertions.assertNotNull(optional.get());
        Assertions.assertEquals(optional.get().getStatus(), ProgressStatus.PROCESSING);
        System.out.println(optional);

        try {
            Thread.sleep(3100);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        optional = videoProgressRepository.findBySessionId("testId");
        Assertions.assertNotNull(optional.get());
        //        Assertions.assertEquals(optional.get().getStatus(), ProgressStatus.SUCCEED);
        System.out.println(optional);

    }

    public class MockService extends UploadServiceGrpc.UploadServiceImplBase {

        private final ScheduledExecutorService scheduledExecutorService = Executors.newScheduledThreadPool(10);

        @Override
        public StreamObserver<UploadServiceOuterClass.X264Frame> x264(
                StreamObserver<UploadServiceOuterClass.Response> responseObserver) {

            return new StreamObserver<UploadServiceOuterClass.X264Frame>() {

                AtomicBoolean first = new AtomicBoolean(true);
                AtomicBoolean writeDown = new AtomicBoolean(false);

                @Override
                public void onNext(UploadServiceOuterClass.X264Frame value) {

                    if (first.get()) {
                        first.set(false);
                        synchronized(this) {
                            responseObserver.onNext(buildResponse(
                                    UploadServiceOuterClass.UploadStatus.NOT_START, null
                            ));

                        }
                    }
                    if (!writeDown.get() && value.getFlagWriteDown()) {
                        var reponse = buildResponse(
                                UploadServiceOuterClass.UploadStatus.PROCESSING, null);
                        responseObserver.onNext(reponse);
                        writeDown.set(true);
                        scheduledExecutorService.schedule(() -> {
                            responseObserver.onNext(buildResponse(
                                    UploadServiceOuterClass.UploadStatus.SUCCEED,
                                    "testUrl"));
                            //                            responseObserver.onCompleted();
                        }, 3, TimeUnit.SECONDS);
                        return;
                    } else if (!value.getFlagWriteDown()) {
                        var response = buildResponse(
                                UploadServiceOuterClass.UploadStatus.NOT_START, null
                        );
                        responseObserver.onNext(response);
                    }

                }

                @Override
                public void onError(Throwable t) {
                    responseObserver.onError(t);
                }

                @Override
                public void onCompleted() {
                    responseObserver.onCompleted();
                }
            };
        }

        private UploadServiceOuterClass.Response buildResponse(
                UploadServiceOuterClass.UploadStatus status, String url) {

            return UploadServiceOuterClass.Response.newBuilder()
                    .setUploadStatus(status)
                    .setVideoDownloadUrl(url == null ? "" : url)
                    .build();
        }
    }

}