package com.baidu.acg.piat.digitalhuman.videopipeline;

import com.google.protobuf.ByteString;

import javax.imageio.ImageIO;
import javax.sound.sampled.AudioInputStream;
import java.awt.image.BufferedImage;
import java.io.*;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.Random;

import static org.junit.jupiter.api.Assertions.assertEquals;

public class Utils {
    public static String readAll(Path path) throws IOException {
        if (!Files.isReadable(path) || Files.isDirectory(path)) {
            throw new IOException(String.format("cannot read:%s", path.toString()));
        }

        long fileLengthLong = path.toFile().length();
        byte[] fileContent = new byte[(int) fileLengthLong];
        FileInputStream inputStream = new FileInputStream(path.toFile());
        inputStream.read(fileContent);
        inputStream.close();
        return new String(fileContent);
    }

    public static ByteString toProtoBufferByteString(AudioInputStream audioInputStream) throws IOException {
        return ByteString.copyFrom(toByteArray(audioInputStream));
    }

    public static ByteString toProtoBufferByteString(BufferedImage bufferedImage, String format) throws IOException {
        return ByteString.copyFrom(toByteArray(bufferedImage, format));
    }

    public static byte[] toByteArray(File file) throws IOException {
        return (new FileInputStream(file)).readAllBytes();
    }

    public static byte[] toByteArray(AudioInputStream audio) throws IOException {
        return audio.readAllBytes();
    }

    public static byte[] toByteArray(BufferedImage image, String imageFormat) throws IOException {
        ByteArrayOutputStream stream = new ByteArrayOutputStream();
        ImageIO.write(image, imageFormat, stream);
        return stream.toByteArray();
    }

    public static String insertAtBegin(int value, char fillCh, int targetLength) {
        return String.valueOf(fillCh).repeat(Math.max(0, targetLength - Integer.toString(value).length()))
                + value;
    }

    public static boolean isEqualInData(byte[] data1, byte[] data2) {
        if (data1.length != data2.length) {
            return false;
        }

        for (int i = 0; i < data1.length; i++) {
            if (data1[i] != data2[i]) {
                return false;
            }
        }
        return true;
    }

    public static boolean isEqualInData(BufferedImage expect, BufferedImage actual) {
        if (expect.getWidth() != actual.getWidth()) {
            return false;
        }

        if (expect.getHeight() != actual.getHeight()) {
            return false;
        }

        int randomX = new Random().nextInt(expect.getWidth());
        int randomY = new Random().nextInt(expect.getHeight());

        return expect.getRGB(randomX, randomY) == actual.getRGB(randomX, randomY);
    }
}
