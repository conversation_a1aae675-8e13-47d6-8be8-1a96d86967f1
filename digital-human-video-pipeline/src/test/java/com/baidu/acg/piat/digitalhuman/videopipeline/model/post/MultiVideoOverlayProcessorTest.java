package com.baidu.acg.piat.digitalhuman.videopipeline.model.post;

import com.baidu.acg.piat.digitalhuman.common.image.Rect;
import com.baidu.acg.piat.digitalhuman.videopipeline.config.WidgetRenderConfigure;
import com.baidu.acg.piat.digitalhuman.videopipeline.exception.FFmpegBaseException;
import com.baidu.acg.piat.digitalhuman.videopipeline.model.ImageSize;
import com.baidu.acg.piat.digitalhuman.videopipeline.model.VideoSource;
import com.baidu.acg.piat.digitalhuman.videopipeline.model.warehouse.VideoMaterial;
import com.baidu.acg.piat.digitalhuman.videopipeline.model.wrapper.FFmpegWrapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.mockito.*;

import java.io.IOException;
import java.nio.file.Path;
import java.rmi.UnexpectedException;
import java.util.PriorityQueue;
import java.util.Random;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

class MultiVideoOverlayProcessorTest {

    @Mock
    WidgetRenderConfigure.Config widgetRenderConfig;

    @Mock
    FFmpegWrapper fFmpegWrapper;

    @Mock
    VideoMaterial videoMaterial;

    @BeforeEach
    void beforeEach() throws IOException {
        MockitoAnnotations.initMocks(this);
        when(videoMaterial.getVideoPathname(anyString(), anyString()))
                .thenReturn(Path.of(UUID.randomUUID().toString()));
        when(videoMaterial.getVideoPathname(any(), anyString()))
                .thenReturn(Path.of(UUID.randomUUID().toString()));
    }

    @Disabled
    @Test
    void test_handleMultiInput() throws IOException, FFmpegBaseException {
        PriorityQueue<VideoOverlayProcessor.VideoOverlayOptions> videoOverlayOptions = new PriorityQueue<>();
        VideoOverlayProcessor.VideoOverlayOptions option1 = Mockito.mock(
                VideoOverlayProcessor.VideoOverlayOptions.class, Answers.RETURNS_DEEP_STUBS);
        VideoOverlayProcessor.VideoOverlayOptions option2 = Mockito.mock(
                VideoOverlayProcessor.VideoOverlayOptions.class, Answers.RETURNS_DEEP_STUBS);

        when(option1.getVideoSource()).thenReturn(
                new VideoSource.LocalFileVideoSource(Path.of("option1.mp4")));
        when(option2.getVideoSource()).thenReturn(
                new VideoSource.LocalFileVideoSource(Path.of("option2.mp4")));
        videoOverlayOptions.add(option1);
        videoOverlayOptions.add(option2);

        MultiVideoOverlayProcessor processor = new MultiVideoOverlayProcessor(
                ImageSize.builder()
                        .width(1080)
                        .height(1920)
                        .build(),
                widgetRenderConfig, fFmpegWrapper, videoOverlayOptions, videoMaterial);

        Path input = Path.of("input.mp4");
        Path target = Path.of("target.mp4");
        processor.handle(input, target);

        ArgumentCaptor<Path> argumentCaptor = ArgumentCaptor.forClass(Path.class);
        ArgumentCaptor<VideoSource> videoSourceCaptor = ArgumentCaptor.forClass(VideoSource.class);
        verify(fFmpegWrapper, times(2)).mix2Video(
                videoSourceCaptor.capture(),
                any(VideoSource.class),
                argumentCaptor.capture(),
                any(Rect.class),
                anyInt());
        assertEquals(videoSourceCaptor.getAllValues().get(0).getSource(), input.toAbsolutePath().toString());
        assertNotEquals(videoSourceCaptor.getAllValues().get(1).getSource(), input.toAbsolutePath().toString());

        assertEquals(argumentCaptor.getAllValues().size(), 2);
        assertEquals(argumentCaptor.getAllValues().get(1), target);
    }
}