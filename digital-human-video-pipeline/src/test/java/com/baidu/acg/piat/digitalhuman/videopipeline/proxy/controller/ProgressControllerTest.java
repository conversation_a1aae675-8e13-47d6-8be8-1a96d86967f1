// Copyright (C) 2021 Baidu Inc. All rights reserved.

package com.baidu.acg.piat.digitalhuman.videopipeline.proxy.controller;

import com.baidu.acg.piat.digitalhuman.common.console.video.ProgressResult;
import com.baidu.acg.piat.digitalhuman.common.model.PageResponse;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import com.baidu.acg.piat.digitalhuman.common.console.video.VideoFailRequest;
import com.baidu.acg.piat.digitalhuman.common.console.video.VideoSubmitRequest;
import com.baidu.acg.piat.digitalhuman.common.console.video.VideoUpdateRequest;
import com.baidu.acg.piat.digitalhuman.videopipeline.proxy.model.VideoBatchRequest;
import com.baidu.acg.piat.digitalhuman.videopipeline.proxy.service.VideoProgressManager;

import java.util.ArrayList;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;

/**
 * ProgressControllerTest
 *
 * <AUTHOR> Zhensheng(<EMAIL>)
 * @date 2021-01-18
 */
class ProgressControllerTest {

    @Mock
    VideoProgressManager videoProgressManager;

    ProgressController progressController;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        progressController = new ProgressController(videoProgressManager);

    }

    @Test
    void submitProgress() {

        Assertions.assertDoesNotThrow(() -> {
            progressController.submitProgress(new VideoSubmitRequest());
        });
    }

    @Test
    void failProgress() {
        Assertions.assertDoesNotThrow(() -> {
            progressController.failProgress(new VideoFailRequest());
        });
    }

    @Test
    void updateProgress() {
        Assertions.assertDoesNotThrow(() -> {
            progressController.updateProgress(new VideoUpdateRequest());
        });
    }

    @Test
    void queryProgress() {

        Assertions.assertDoesNotThrow(() -> {
            progressController.queryProgress("test");
        });
    }

    @Test
    void queryBatchProgress() {
        Assertions.assertDoesNotThrow(() -> {
            progressController.queryBatchProgress(new VideoBatchRequest());
        });

    }

    @Test
    void queryById() {

        Assertions.assertDoesNotThrow(() -> {
            progressController.queryById("test", "", 1, 1);
        });
    }

    @Test
    void queryProgressByUserNameAndProjectNameOrCharacterConfigId() {
        /**
         * 1.0 使用projectName
         */
        Mockito.when(videoProgressManager.pollProgressByUserNameAndProjectName(anyString(), anyString(), any()))
                .thenReturn(PageResponse.success(1, 20, 20, new ArrayList<ProgressResult>()));
        Assertions.assertDoesNotThrow(() -> {
            progressController.queryProgressByUserNameAndProjectNameOrCharacterConfigId(
                    "userName", "projectName", "", 1, 10);
        });

        /**
         * 2.0 使用characterConfigId
         */
        Mockito.when(videoProgressManager.pollProgressByUserNameAndCharacterConfigId(anyString(), anyString(), any()))
                .thenReturn(PageResponse.success(1, 20, 20, new ArrayList<ProgressResult>()));
        Assertions.assertDoesNotThrow(() -> {
            progressController.queryProgressByUserNameAndProjectNameOrCharacterConfigId(
                    "userName", "", "characterConfigId", 1, 10);
        });


    }
}