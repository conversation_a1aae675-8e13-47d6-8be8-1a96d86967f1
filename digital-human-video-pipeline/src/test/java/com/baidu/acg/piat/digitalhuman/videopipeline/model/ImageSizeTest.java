package com.baidu.acg.piat.digitalhuman.videopipeline.model;

import org.junit.jupiter.api.Test;

import java.awt.image.BufferedImage;

import static org.junit.jupiter.api.Assertions.*;

class ImageSizeTest {

    @Test
    void equalsMulti() {
        BufferedImage img1 = new BufferedImage(1, 1, BufferedImage.TYPE_INT_RGB);
        BufferedImage img2 = new BufferedImage(1, 2, BufferedImage.TYPE_INT_RGB);
        BufferedImage img3 = new BufferedImage(1, 3, BufferedImage.TYPE_INT_RGB);

        assertFalse(ImageSize.equalsMulti(img1, img2, img3));

        assertTrue(ImageSize.equalsMulti(img1));

        assertTrue(ImageSize.equalsMulti(img1, img1, img1));

        assertTrue(ImageSize.equalsMulti());
    }

    @Test
    void compareTo() {
        ImageSize size1 = new ImageSize(1, 1);
        BufferedImage img = new BufferedImage(1, 1, BufferedImage.TYPE_INT_RGB);
        assertEquals(0, size1.compareTo(img));

        img = new BufferedImage(1, 2, BufferedImage.TYPE_INT_RGB);
        assertNotEquals(0, size1.compareTo(img));
    }
}