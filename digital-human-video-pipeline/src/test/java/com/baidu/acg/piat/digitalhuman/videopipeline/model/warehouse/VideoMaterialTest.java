package com.baidu.acg.piat.digitalhuman.videopipeline.model.warehouse;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.MockitoAnnotations;
import org.mockito.Spy;

import java.io.BufferedReader;
import java.io.FileReader;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.regex.Pattern;

import com.baidu.acg.piat.digitalhuman.common.utils.PathUtil;
import com.baidu.acg.piat.digitalhuman.videopipeline.Utils;

@Slf4j
        //@ExtendWith(SpringExtension.class)
        //@SpringBootTest
        //@ActiveProfiles(profiles = "unit-test")
class VideoMaterialTest {

    @Spy
    private VideoMaterialConf videoMaterialConf;

    @InjectMocks
    private VideoMaterial videoMaterial;

    private static final String videoId = "123";

    private static final int audioFragmentId = 0;

    private static final int imageFragmentId = 0;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        videoMaterialConf.setFragmentIndexLength(8);
        videoMaterialConf.setRootPath("./output");
        videoMaterialConf.setImageFileExtension("jpg");
        videoMaterialConf.setImageSubDirectory("image");
        videoMaterialConf.setAudioSubDirectory("audio");
        videoMaterialConf.setAudioFileExtension("wav");
        videoMaterialConf.setAudioFormatSampleRate(16000);
        videoMaterialConf.setAudioFormatSampleSizeInBits(16);
        videoMaterialConf.setAudioFormatChannels(1);
        videoMaterialConf.setAudioFormatSigned(true);
        videoMaterialConf.setAudioFormatBigEndian(false);
        videoMaterialConf.setVideoFileExtension("mp4");
    }

    /**
     * test set up unit test in spring boot
     */
    @Test
    void testMaterial() {
        var value = videoMaterial.getAudioFileExtension();
        log.info("audio file extension: {}", value);
        assertEquals("wav", value);
    }

    @Test
    void generateAudioFileList() throws IOException {
        PathUtil.delete(videoMaterial.getAudioSubDirectory(videoId));

        // without file
        var audioListFile = videoMaterial.generateAudioFileList(videoId);
        var length = audioListFile.toFile().length();
        assertEquals(0, length);

        // with file
        Files.createFile(videoMaterial.getAudioFragmentPathname(videoId, audioFragmentId));
        audioListFile = videoMaterial.generateAudioFileList(videoId);
        length = audioListFile.toFile().length();
        assertNotEquals(0, length);

        var bufferReader = new BufferedReader(new FileReader(audioListFile.toFile()));
        var line = bufferReader.readLine();
        System.out.println(line);
        var isMatch = Pattern.matches("^file '.*'", line);
        assertTrue(isMatch);

        // test filter
        Files.createFile(
                Paths.get(videoMaterial.getAudioSubDirectory(videoId).toString(),
                        "test.mp3"));
        audioListFile = videoMaterial.generateAudioFileList(videoId);
        String content = Utils.readAll(audioListFile);
        assertFalse(content.contains("test.mp3"));
    }

    @Test
    void getAudioFragmentPathname() throws IOException {
        var path = videoMaterial.getAudioFragmentPathname(videoId, audioFragmentId);
        var builder = new StringBuilder();
        builder.append("0".repeat(Math.max(0, videoMaterialConf.getFragmentIndexLength() - Integer.toString(audioFragmentId).length())));
        builder.append(audioFragmentId);
        builder.append('.');
        builder.append(videoMaterial.getAudioFileExtension());
        assertEquals(path.getName(path.getNameCount() - 1).toString(), builder.toString());
    }

    @Test
    void getImageFragmentPathname() throws IOException {
        var path = videoMaterial.getImageFragmentPathname(videoId, imageFragmentId);
        var builder = new StringBuilder();
        builder.append("0".repeat(Math.max(0, videoMaterialConf.getFragmentIndexLength() - Integer.toString(imageFragmentId).length())));
        builder.append(imageFragmentId);
        builder.append('.');
        builder.append(videoMaterial.getImageFileExtension());
        assertEquals(path.getName(path.getNameCount() - 1).toString(), builder.toString());
    }
}