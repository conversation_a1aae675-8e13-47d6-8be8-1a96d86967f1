// Copyright (C) 2020 Baidu Inc. All rights reserved.

package com.baidu.acg.piat.digitalhuman.videopipeline.proxy.delegate;

import io.grpc.stub.StreamObserver;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import com.baidu.acg.piat.digitalhuman.videopipeline.proxy.service.VideoProgressManager;
import com.baidu.acg.pie.UploadServiceGrpc;
import com.baidu.acg.pie.UploadServiceOuterClass;

/**
 * VideoPipelineServiceProgressDelagatorTest
 *
 * <AUTHOR>
 * @since 2020-02-10
 */

@Slf4j
public class VideoPipelineServiceProgressDelegatorEnabledTest {

    @Mock
    private VideoProgressManager progressManager;

    private VideoPipelineServiceProgressDelegator delegator;

    @BeforeEach
    public void init() {
        MockitoAnnotations.initMocks(this);
        delegator = new VideoPipelineServiceProgressDelegator(new MockService(), progressManager);

    }

    @Test
    void x264InLive() {
        var requestObserver = delegator.x264InLive(new StreamObserver<>() {
            @Override
            public void onNext(UploadServiceOuterClass.Response value) {
                Assertions.assertEquals(UploadServiceOuterClass.UploadStatus.SUCCEED, value.getUploadStatus());
            }

            @Override
            public void onError(Throwable t) {

            }

            @Override
            public void onCompleted() {
                log.info("Response on complete");
            }
        });

        requestObserver.onNext(UploadServiceOuterClass.X264Frame.newBuilder()
                .setVideoId("11111_222")
                .build());

        requestObserver.onCompleted();
    }

    public static class MockService extends UploadServiceGrpc.UploadServiceImplBase {
        @Override
        public StreamObserver<UploadServiceOuterClass.X264Frame> x264InLive(
                StreamObserver<UploadServiceOuterClass.Response> responseObserver) {
            return new StreamObserver<>() {
                @Override
                public void onNext(UploadServiceOuterClass.X264Frame value) {
                    responseObserver.onNext(UploadServiceOuterClass.Response.newBuilder()
                            .setUploadStatus(UploadServiceOuterClass.UploadStatus.SUCCEED)
                            .setVideoDownloadUrl("http://xxxxx")
                            .setMessage("ok")
                            .build());
                }

                @Override
                public void onError(Throwable t) {

                }

                @Override
                public void onCompleted() {

                }
            };
        }
    }

}