// Copyright (C) 2020 Baidu Inc. All rights reserved.
package com.baidu.acg.piat.digitalhuman.videopipeline.proxy.persistence;

import static com.baidu.acg.piat.digitalhuman.common.console.video.ProgressStatus.INIT;
import static com.baidu.acg.piat.digitalhuman.common.console.video.ProgressStatus.SUCCEED;
import static java.lang.Thread.sleep;

import org.assertj.core.util.Lists;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.platform.commons.util.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.orm.jpa.DataJpaTest;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.test.context.junit4.SpringRunner;

import java.time.ZonedDateTime;
import java.util.List;
import java.util.UUID;

/**
 * VideoProgressRepositoryTest, test only locally
 *
 * <AUTHOR> (<EMAIL>)
 */
@DataJpaTest
class VideoProgressRepositoryTest {
    private final List<String> videoIds = Lists.newArrayList();

    @Autowired
    private VideoProgressRepository repository;

    @BeforeEach
    public void init() {
        for (int i = 0; i < 10; i++) {
            String videoId = UUID.randomUUID().toString();
            videoIds.add(videoId);
            var model = VideoProgressModel.builder()
                    .videoId(videoId)
                    .appId("appId")
                    .status(INIT)
                    .message("ok")
                    .downloadUrl("url")
                    .build();
            repository.save(model);
            try {
                sleep(500);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        }
    }

    @Test
    void testAll() {
        String videoId = videoIds.get(0);
        var before = repository.findByVideoId(videoId);

        try {
            sleep(5000);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }

        repository.updateActiveTimeByVideoIdIn(videoIds, ZonedDateTime.now());
        var after = repository.findByVideoId(videoId);

        Assertions.assertTrue(before.isPresent());
        Assertions.assertTrue(after.isPresent());
        Assertions.assertNotEquals(before.get().getActiveTime(), after.get().getActiveTime());

        var optional = repository.findByVideoId(videoId);
        Assertions.assertTrue(optional.isPresent());
        var model = optional.get();
        Assertions.assertEquals(model.getVideoId(), videoId);
        Assertions.assertNotNull(model.getId());
        Assertions.assertTrue(StringUtils.isNotBlank(model.toProgressResult().getDownloadUrl()));

        var iterator = repository.findAllByVideoIdIn(videoIds);
        List<VideoProgressModel> models = Lists.newArrayList(iterator);
        Assertions.assertEquals(videoIds.size(), models.size());

        Pageable pageable = PageRequest.of(0, 20);
        var result = repository
                .findAllByAppIdAndStatusNameOrderByCreateTimeDesc("appId", SUCCEED.name(), pageable);
        models = result.getContent();
        Assertions.assertEquals(0, models.size());

        result = repository
                .findAllByAppIdAndStatusNameOrderByCreateTimeDesc("appId", INIT.name(), pageable);
        models = result.getContent();
        Assertions.assertEquals(videoIds.size(), models.size());
        Assertions.assertEquals(videoIds.get(0), models.get(models.size() - 1).getVideoId());
    }
}