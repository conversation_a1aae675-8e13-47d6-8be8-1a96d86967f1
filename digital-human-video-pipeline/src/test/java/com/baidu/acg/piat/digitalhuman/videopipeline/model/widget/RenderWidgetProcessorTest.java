package com.baidu.acg.piat.digitalhuman.videopipeline.model.widget;

import com.baidu.acg.piat.digitalhuman.videopipeline.config.WidgetRenderConfigure;
import com.baidu.acg.piat.digitalhuman.videopipeline.model.ImageSize;
import com.baidu.acg.piat.digitalhuman.videopipeline.model.post.VideoOverlayProcessor;
import com.baidu.acg.piat.digitalhuman.videopipeline.model.warehouse.VideoMaterial;
import com.baidu.acg.piat.digitalhuman.videopipeline.model.wrapper.FFmpegWrapper;
import com.baidubce.util.JsonUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.ArgumentMatcher;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.HashMap;
import java.util.Map;
import java.util.PriorityQueue;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

class RenderWidgetProcessorTest {

    @Mock
    WidgetRenderConfigure.Config widgetRenderConfig;

    @Mock
    VideoMaterial videoMaterial;

    @Mock
    FFmpegWrapper fFmpegWrapper;

    @BeforeEach
    void beforeEach() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    void test_filterAndCollect() {
        RenderWidgetProcessor widgetProcessor = new RenderWidgetProcessor(
                ImageSize.builder()
                        .width(1080)
                        .height(1929)
                        .build(),
                "sessionId", widgetRenderConfig, videoMaterial, fFmpegWrapper);
        Map<String, String> params = new HashMap<>();
        String renderEventBody = "{\"widget\":{\"position\":{\"aspectRatio\":\"0.5\",\"width\":\"400\",\"xCenterOffset\":\"0\"},\"ttl\":\"-1\",\"type\":\"video\",\"version\":\"2.0\",\"video\":{\"id\":\"1\",\"url\":\"https://digital-human-video-output.bj.bcebos.com/video%252Fc16ba1ba-e459-4250-a3bc-df7c2af2d95f\"}}}";
        RenderWidgetProcessor.RenderEvent renderEvent =
                new RenderWidgetProcessor.RenderEvent("DOWN_CLIENT", renderEventBody);

        params.put(RenderWidgetProcessor.RENDER_WIDGET_KEY, JsonUtils.toJsonString(renderEvent));
        ArgumentCaptor<VideoOverlayProcessor.VideoOverlayOptions> optionCaptor = ArgumentCaptor.forClass(
                VideoOverlayProcessor.VideoOverlayOptions.class);

        widgetProcessor.filterRenderWidgetAndCollect(params, 10);
        PriorityQueue<VideoOverlayProcessor.VideoOverlayOptions> options = widgetProcessor.getVideoOverlayOptions();
        assertEquals(options.size(), 1);
        VideoOverlayProcessor.VideoOverlayOptions top = options.peek();
        assertNotNull(top);
        assertEquals(top.getOffsetMillsSeconds(), 10);
        assertEquals(top.getVideoSource().getSource(),
                "https://digital-human-video-output.bj.bcebos.com/video%252Fc16ba1ba-e459-4250-a3bc-df7c2af2d95f");
        assertEquals(top.getInnerPicturePosition().getWidth(), 400);
        assertEquals(top.getInnerPicturePosition().getHeight(400, 0.1), 800);
    }
}