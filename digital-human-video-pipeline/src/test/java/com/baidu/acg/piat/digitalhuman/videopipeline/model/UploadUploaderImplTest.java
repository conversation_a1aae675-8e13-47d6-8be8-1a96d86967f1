// Copyright (C) 2021 Baidu Inc. All rights reserved.

package com.baidu.acg.piat.digitalhuman.videopipeline.model;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

import com.baidu.acg.piat.digitalhuman.videopipeline.model.widget.RenderWidgetProcessor;
import com.google.protobuf.ByteString;
import io.grpc.stub.StreamObserver;

import java.io.IOException;
import java.net.MalformedURLException;
import java.net.URL;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Optional;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Answers;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import com.baidu.acg.piat.digitalhuman.common.utils.ReflectUtil;
import com.baidu.acg.piat.digitalhuman.storage.service.StorageService;
import com.baidu.acg.piat.digitalhuman.videopipeline.config.FFmpegConfigure;
import com.baidu.acg.piat.digitalhuman.videopipeline.config.WidgetRenderConfigure;
import com.baidu.acg.piat.digitalhuman.videopipeline.model.warehouse.VideoMaterial;
import com.baidu.acg.pie.UploadServiceOuterClass;

/**
 * UploadUploaderImplTest
 *
 * <AUTHOR> Junyi (<EMAIL>)
 */
class UploadUploaderImplTest {

    @Mock(answer = Answers.RETURNS_DEEP_STUBS)
    private StorageService storageService;

    @Mock(answer = Answers.RETURNS_DEEP_STUBS)
    private VideoMaterial videoMaterial;

    @Mock(answer = Answers.RETURNS_DEEP_STUBS)
    private VideoGenerator videoGenerator;

    @Mock(answer = Answers.RETURNS_DEEP_STUBS)
    private X264VideoGenerator x264VideoGenerator;

    @Mock(answer = Answers.RETURNS_DEEP_STUBS)
    private RenderWidgetProcessor widgetProcessor;

    @InjectMocks
    private UploadUploaderImpl uploadUploader;

    @BeforeEach
    void init() throws NoSuchFieldException, IllegalAccessException {
        MockitoAnnotations.initMocks(this);
        uploadUploader = new UploadUploaderImpl(new UploadUploaderImpl.UploaderConfig(), storageService,
                new FFmpegConfigure.FFmpegConf(), UploadServiceOuterClass.InitialRequest.getDefaultInstance(),
                new WidgetRenderConfigure.Config(), videoMaterial);
        ReflectUtil.setFieldValue(uploadUploader, "videoGenerator", videoGenerator);
        ReflectUtil.setFieldValue(uploadUploader, "x264VideoGenerator", x264VideoGenerator);
        ReflectUtil.setFieldValue(uploadUploader, "widgetProcessor", widgetProcessor);
        ReflectUtil.getFieldValue(uploadUploader, "videoGenerator");
    }

    @Test
    void upload() throws StorageService.ResourceException, IOException {
        var response = uploadUploader.upload(UploadServiceOuterClass.Video.newBuilder()
                .build(), buildResponseObserver());
        Assertions.assertTrue(response.getSuccess());
        Assertions.assertEquals(response.getUploadStatus(), UploadServiceOuterClass.UploadStatus.NOT_START);

        when(videoGenerator.generate(any())).thenReturn(Paths.get("1111.jpg"));
        when(storageService.save(any())).thenReturn(new URL("http://localhost"));
        when(storageService.save((Path) any(), anyString())).thenReturn(new URL("http://localhost"));
        response = uploadUploader.upload(UploadServiceOuterClass.Video.newBuilder()
                .setFlagWriteDown(true)
                .build(), buildResponseObserver());
        Assertions.assertTrue(response.getSuccess());
        Assertions.assertEquals(response.getUploadStatus(), UploadServiceOuterClass.UploadStatus.SUCCEED);

        response = uploadUploader.upload(UploadServiceOuterClass.Video.newBuilder()
                .setFlagWriteDown(true)
                .build(), buildResponseObserver());
        Assertions.assertTrue(response.getSuccess());
        Assertions.assertEquals(response.getUploadStatus(), UploadServiceOuterClass.UploadStatus.SUCCEED);

        byte[] bytes = new byte[10];
        when(videoGenerator.saveImage(any(UploadServiceOuterClass.Video.class))).thenReturn(false);
        response = uploadUploader.upload(UploadServiceOuterClass.Video.newBuilder()
                .setImage(ByteString.copyFrom(bytes))
                .build(), buildResponseObserver());
        Assertions.assertFalse(response.getSuccess());
        Assertions.assertEquals(response.getUploadStatus(), UploadServiceOuterClass.UploadStatus.NOT_START);
    }

    @Test
    void uploadImage() throws StorageService.ResourceException, MalformedURLException {
        var response = uploadUploader.uploadImage(UploadServiceOuterClass.Image.newBuilder()
                .setImageId("imageId")
                .setImageName("imageName")
                .setData(ByteString.EMPTY)
                .build());
        Assertions.assertFalse(response.getSuccess());
        Assertions.assertEquals(response.getUploadStatus(), UploadServiceOuterClass.UploadStatus.FAILED);

        when(videoGenerator.saveImage(any(UploadServiceOuterClass.Image.class)))
                .thenReturn(Optional.of(Paths.get("1111.jpg")));
        when(storageService.save(any())).thenReturn(new URL("http://localhost"));
        when(storageService.save((Path) any(), anyString())).thenReturn(new URL("http://localhost"));
        response = uploadUploader.uploadImage(UploadServiceOuterClass.Image.newBuilder()
                .setImageId("imageId")
                .setImageName("imageName")
                .setDirectory("directory")
                .setData(ByteString.EMPTY)
                .build());
        Assertions.assertTrue(response.getSuccess());
        Assertions.assertEquals(response.getUploadStatus(), UploadServiceOuterClass.UploadStatus.SUCCEED);
        Assertions.assertEquals(response.getVideoDownloadUrl(), "http://localhost");

        response = uploadUploader.uploadImage(UploadServiceOuterClass.Image.newBuilder()
                .setImageId("imageId")
                .setData(ByteString.EMPTY)
                .build());
        Assertions.assertTrue(response.getSuccess());
        Assertions.assertEquals(response.getUploadStatus(), UploadServiceOuterClass.UploadStatus.SUCCEED);
        Assertions.assertEquals(response.getVideoDownloadUrl(), "http://localhost");

        when(storageService.save(any())).thenThrow(new RuntimeException("failed"));
        when(storageService.save((Path) any(), anyString())).thenThrow(new RuntimeException("failed"));
        response = uploadUploader.uploadImage(UploadServiceOuterClass.Image.newBuilder()
                .setImageId("imageId")
                .setImageName("imageName")
                .setData(ByteString.EMPTY)
                .build());
        Assertions.assertFalse(response.getSuccess());
        Assertions.assertEquals(response.getUploadStatus(), UploadServiceOuterClass.UploadStatus.FAILED);
    }


    @Test
    void test_uploadX264Frame() throws MalformedURLException, StorageService.ResourceException {
        Mockito.when(storageService.save(Mockito.any(Path.class))).thenReturn(new URL("https://www.baidu.com"));

        UploadServiceOuterClass.Response res = uploadUploader.uploadX264Frame(
                UploadServiceOuterClass.X264Frame.newBuilder()
                        .setFlagWriteDown(true)
                        .build(), new StreamObserver<>() {
                    @Override
                    public void onNext(UploadServiceOuterClass.Response response) {

                    }

                    @Override
                    public void onError(Throwable throwable) {

                    }

                    @Override
                    public void onCompleted() {

                    }
                });
    }

    private StreamObserver<UploadServiceOuterClass.Response> buildResponseObserver() {
        return new StreamObserver<>() {
            @Override
            public void onNext(UploadServiceOuterClass.Response value) {

            }

            @Override
            public void onError(Throwable t) {

            }

            @Override
            public void onCompleted() {

            }
        };
    }
}