package com.baidu.acg.piat.digitalhuman.videopipeline.proxy.service;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.time.Instant;

import com.baidu.acg.piat.digitalhuman.videopipeline.proxy.persistence.VideoProgressModel;

/**
 * Created on 2021/12/7 12:16 下午
 *
 * <AUTHOR>
 */

@Disabled
@SpringBootTest
class VideoProgressManagerFunctionalTest {

    @Autowired
    private VideoProgressManager videoProgressManager;

    private VideoProgressModel videoProgressModel;

    @BeforeEach
    void setUp() {
        videoProgressModel = VideoProgressModel.builder()
                .videoId("5a9ba969-ff66-4b83-ba15-214d30fb6896")
                .downloadUrl("https://digital-human-pipeline-output.cdn.bcebos.com/" +
                        "5a9ba969-ff66-4b83-ba15-214d30fb6896.mp4")
                .build();
    }

    @Test
    void testExtractAudio() throws Exception {

        var extractAudio = videoProgressManager.getClass().getDeclaredMethod("extractAudio", VideoProgressModel.class);
        extractAudio.setAccessible(true);

        var start = Instant.now().getEpochSecond();
        extractAudio.invoke(videoProgressManager, videoProgressModel);

        System.out.println(videoProgressModel.getAudioUrl() + "\n耗时：" + (Instant.now().getEpochSecond() - start));
    }
}