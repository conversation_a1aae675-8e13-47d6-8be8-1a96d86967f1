// Copyright (C) 2020 Baidu Inc. All rights reserved.
package com.baidu.acg.piat.digitalhuman.videopipeline.proxy.service;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.fasterxml.jackson.core.JsonProcessingException;
import io.grpc.stub.StreamObserver;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import org.assertj.core.util.Lists;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.Spy;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;

import com.baidu.acg.piat.digitalhuman.common.console.video.ProgressResult;
import com.baidu.acg.piat.digitalhuman.common.console.video.ProgressStatus;
import com.baidu.acg.piat.digitalhuman.common.console.video.RetryPolicy;
import com.baidu.acg.piat.digitalhuman.common.console.video.Text2VideoParams;
import com.baidu.acg.piat.digitalhuman.common.console.video.VideoFailRequest;
import com.baidu.acg.piat.digitalhuman.common.console.video.VideoSubmitRequest;
import com.baidu.acg.piat.digitalhuman.common.constans.PostProcessingType;
import com.baidu.acg.piat.digitalhuman.common.constans.RenderOpenParameters;
import com.baidu.acg.piat.digitalhuman.common.exception.DigitalHumanCommonException;
import com.baidu.acg.piat.digitalhuman.common.utils.IDGenerator;
import com.baidu.acg.piat.digitalhuman.notifier.config.EventNotifierConfig;
import com.baidu.acg.piat.digitalhuman.notifier.service.DigitalHumanEventNotifier;
import com.baidu.acg.piat.digitalhuman.videopipeline.proxy.config.ExtractAudioConfigure.ExtractAudioConfig;
import com.baidu.acg.piat.digitalhuman.videopipeline.proxy.persistence.VideoProgressModel;
import com.baidu.acg.piat.digitalhuman.videopipeline.proxy.persistence.VideoProgressRepository;
import com.baidu.acg.pie.UploadServiceOuterClass;

/**
 * VideoProgressManagerTest
 *
 * <AUTHOR> Junyi (<EMAIL>)
 */
class VideoProgressManagerTest {

    @Spy
    private IDGenerator videoIdGenerator = new IDGenerator(new IDGenerator.IDGeneratorConfig());

    @InjectMocks
    private VideoProgressManager videoProgressManager;

    @Mock
    private VideoProgressRepository videoProgressRepository;

    @Mock
    private DigitalHumanEventNotifier notifier;

    @Spy
    private EventNotifierConfig eventNotifierConfig = new EventNotifierConfig();

    @Spy
    private ExtractAudioConfig audioConfig = new ExtractAudioConfig();

    @Mock
    private Map<String, VideoProgressManager.VideoSession> progressMap;

    private VideoSubmitRequest videoSubmitRequest;

    private VideoFailRequest videoFailRequest;

    private VideoProgressModel progressModel;



    @BeforeEach
    public void init() throws JsonProcessingException {
        MockitoAnnotations.initMocks(this);

        videoSubmitRequest = new VideoSubmitRequest();
        videoSubmitRequest.setAppId("appId");
        videoSubmitRequest.setAppKey("appKey");
        videoSubmitRequest.setTexts(List.of("test1", "test2"));

        videoFailRequest = new VideoFailRequest();
        videoFailRequest.setId("videoId");
        videoFailRequest.setErrorCode(-1);
        videoFailRequest.setErrorMsg("error");
        videoFailRequest.setCanRetry(true);

        progressModel = new VideoProgressModel();
        progressModel.setVideoId("videoId");
        progressModel.setScheduledTimes(1);
        progressModel.setRetryPolicy(RetryPolicy.defaultPolicy);
        progressModel.setSubmitParams(Text2VideoParams.builder()
                .renderParams(Map.of(PostProcessingType.notifyHook.name(), "false",
                        RenderOpenParameters.extraInfo.name(), "extraInfo"))
                .build());
        progressModel.setLastScheduleStartTime(ZonedDateTime.now());


        eventNotifierConfig.setUrl("http://localhost:9090");
        eventNotifierConfig.setEmitPath("/digital-human/v1/hooks/event");

        Mockito.when(videoProgressRepository.saveAll(any())).thenReturn(List.of(
                VideoProgressModel.builder().text("test1").videoId("videoId").sessionId("sessionId").appId("appId")
                        .build(),
                VideoProgressModel.builder().text("test2").videoId("videoId").sessionId("sessionId").appId("appId")
                        .build()
        ));
        Mockito.when(videoProgressRepository.findByVideoId(any())).thenReturn(Optional.of(progressModel));
        Mockito.when(videoProgressRepository.save(any())).thenReturn(progressModel);
        Mockito.when(videoProgressRepository.findBySessionId(any())).thenReturn(
                Optional.of(
                        VideoProgressModel.builder().videoId("videoId").sessionId("sessionId").appId("appId").build()));

    }

    @Test
    public void addProgress() {
        var res = videoProgressManager.addProgress(VideoProgressModel.builder()
                .status(ProgressStatus.PROCESSING)
                .build());
        Assertions.assertTrue(res.isEmpty());

        res = videoProgressManager.addProgress(VideoProgressModel.builder()
                .status(ProgressStatus.SUCCEED)
                .build());
        Assertions.assertTrue(res.isPresent());
    }

    @Test
    public void fixRateHeartbeat() {
        videoProgressManager.fixRateHeartbeat();
    }

    @Test
    public void submitRequest() {
        videoProgressManager.submitProgress(videoSubmitRequest);
    }

    @Test
    public void successtoCallfailProgress() {
        videoProgressManager.failProgress(videoFailRequest);
    }

    @Test
    public void failProgressOutOflimit() {
        progressModel
                .setRetryPolicy(new RetryPolicy(false, RetryPolicy.RetryPolicyType.exponential, -1, 10000L, 1000L));
        ProgressResult ressult =
                videoProgressManager.failProgress(videoFailRequest);

        Assertions.assertEquals(ProgressStatus.ERROR, ressult.getStatus());
    }

    @Test
    public void failProgressNoVideo() {
        Mockito.when(videoProgressRepository.findByVideoId(any())).thenReturn(Optional.empty());

        Assertions.assertThrows(DigitalHumanCommonException.class, () -> {
            ProgressResult result = videoProgressManager.failProgress(videoFailRequest);
        });

    }

    @Test
    public void failProgressCannotRetry() {

        videoFailRequest.setCanRetry(false);
        ProgressResult ressult =
                videoProgressManager.failProgress(videoFailRequest);

        Assertions.assertEquals(ProgressStatus.ERROR, ressult.getStatus());
    }


    @Test
    public void startX264() {
        var session = videoProgressManager.startX264("sessionId", new StreamObserver<>() {
            @Override
            public void onNext(UploadServiceOuterClass.X264Frame value) {

            }

            @Override
            public void onError(Throwable t) {

            }

            @Override
            public void onCompleted() {

            }
        });
        Assertions.assertEquals("sessionId", session.getSessionId());
        Assertions.assertEquals("videoId", session.getVideoId());
        Assertions.assertEquals("appId", session.getAppId());
    }

    @Test
    public void pollProgress() {
        when(videoProgressRepository.findByVideoId(anyString())).thenReturn(Optional.of(VideoProgressModel.builder()
                .videoId("videoId")
                .id(1L)
                .build()));
        var res = videoProgressManager.pollProgress("videoId");
        Assertions.assertNotNull(res);
        Assertions.assertEquals("videoId", res.getVideoId());
        Assertions.assertNull(res.getDownloadUrl());
    }

    @Test
    public void pollBatchProgress() {
        when(videoProgressRepository.findAllByVideoIdIn(any()))
                .thenReturn(Lists.newArrayList(VideoProgressModel.builder()
                        .videoId("videoId")
                        .id(1L)
                        .build()));
        var res = videoProgressManager.pollBatchProgress(Lists.newArrayList("videoId"));
        Assertions.assertNotNull(res);
        Assertions.assertEquals(1, res.size());
    }

    @Test
    public void pollProgressByUserNameAndProjectName() {
        PageRequest pageRequest = PageRequest.of(0, 1);
        when(videoProgressRepository
                .findAllByUserNameAndProjectNameAndStatusNameOrderByCreateTimeDesc(
                        anyString(), anyString(), anyString(), any())).thenReturn(
                                new PageImpl(Lists.newArrayList(VideoProgressModel.builder()
                                        .videoId("videoId")
                                        .id(1L)
                                        .build()), pageRequest, 1));
        var res = videoProgressManager.pollProgressByUserNameAndProjectName("", "", pageRequest);
        Assertions.assertNotNull(res);
        Assertions.assertNotNull(res.getPage().getResult());
        Assertions.assertEquals(1, res.getPage().getResult().size());
    }

    @Test
    public void pollProgressByUserNameAndCharacterConfigId() {
        PageRequest pageRequest = PageRequest.of(0, 1);
        when(videoProgressRepository
                .findAllByUserNameAndCharacterConfigIdAndStatusNameOrderByCreateTimeDesc(
                        anyString(), anyString(), anyString(), any())).thenReturn(
                new PageImpl(Lists.newArrayList(VideoProgressModel.builder()
                        .videoId("videoId")
                        .id(1L)
                        .build()), pageRequest, 1));
        var res = videoProgressManager.pollProgressByUserNameAndCharacterConfigId("", "", pageRequest);
        Assertions.assertNotNull(res);
        Assertions.assertNotNull(res.getPage().getResult());
        Assertions.assertEquals(1, res.getPage().getResult().size());
    }

    @Test
    public void pollProgressByAppId() {
        PageRequest pageRequest = PageRequest.of(0, 1);

        when(videoProgressRepository.findAllByAppIdAndStatusNameOrderByCreateTimeDesc(
                anyString(), anyString(), any()))
                .thenReturn(new PageImpl(Lists.newArrayList(VideoProgressModel.builder()
                        .videoId("videoId")
                        .id(1L)
                        .build()), pageRequest, 1));
        var res = videoProgressManager.pollProgressByAppId("appId", pageRequest);
        Assertions.assertNotNull(res);
        Assertions.assertNotNull(res.getPage().getResult());
        Assertions.assertEquals(1, res.getPage().getResult().size());
    }

    @Test
    void postProcessing() throws Exception {

        progressModel.setDownloadUrl("url");
        var reviewVideoMethod = videoProgressManager.getClass()
                .getDeclaredMethod("postProcessing", VideoProgressModel.class);

        reviewVideoMethod.setAccessible(true);

        // 不需要消息通知
        reviewVideoMethod.invoke(videoProgressManager, progressModel);

        verify(notifier, never()).notifyAsync(any());

        // 需要消息通知
        progressModel.setSubmitParams(Text2VideoParams.builder()
                .renderParams(Map.of(PostProcessingType.notifyHook.name(), "true",
                        RenderOpenParameters.extraInfo.name(), "extraInfo"))
                .build());
        reviewVideoMethod.invoke(videoProgressManager, progressModel);

        verify(notifier).notifyAsync(any());
    }

    @Test
    void progressChangeCallback() throws Exception {
        when(progressMap.remove(anyString())).thenReturn(VideoProgressManager.VideoSession.builder().build());
        var progressChangeCallback = videoProgressManager.getClass().getDeclaredMethod("progressChangeCallback");
        progressChangeCallback.setAccessible(true);

        // 需要消息通知
        progressModel.setSubmitParams(Text2VideoParams.builder()
                .renderParams(Map.of(PostProcessingType.notifyHook.name(), "true",
                        RenderOpenParameters.extraInfo.name(), "extraInfo"))
                .build());
        var invoke = (VideoProgressManager.ProgressChangeCallback) progressChangeCallback.invoke(videoProgressManager);
        invoke.onStatusChange(VideoProgressManager.VideoSession.builder()
                        .sessionId("sessionId")
                        .build(), ProgressStatus.SUCCEED,
                UploadServiceOuterClass.Response.newBuilder()
                        .setVideoDownloadUrl("videoDownloadUrl")
                        .setAudioDownloadUrl("audioDownloadUrl")
                        .build());

        verify(notifier).notifyAsync(any());
    }
}