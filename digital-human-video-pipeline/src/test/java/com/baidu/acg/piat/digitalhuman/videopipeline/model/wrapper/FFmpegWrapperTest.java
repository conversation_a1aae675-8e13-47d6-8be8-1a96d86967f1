package com.baidu.acg.piat.digitalhuman.videopipeline.model.wrapper;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

import java.io.IOException;
import java.net.URL;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;

import javax.imageio.ImageIO;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Order;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import com.baidu.acg.piat.digitalhuman.common.image.Rect;
import com.baidu.acg.piat.digitalhuman.videopipeline.config.FFmpegConfigure;
import com.baidu.acg.piat.digitalhuman.videopipeline.exception.FFmpegBaseException;
import com.baidu.acg.piat.digitalhuman.videopipeline.model.VideoSource;

import lombok.extern.slf4j.Slf4j;

// 编译的机器上没有ffmpeg，提交代码的时候需要注释掉
@Slf4j
@Disabled
@ExtendWith(SpringExtension.class)
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
class FFmpegWrapperTest {
    @Mock
    private FFmpegConfigure.FFmpegConf fFmpegConf;

    private FFmpegWrapper fFmpegWrapper;

    private static final String rootPath = "src/test/resources/ffmpeg";

    private static final String imageSubDirectory = "image";

    private static final String audioSubDirectory = "audio";

    private static final String videoSubDirectory = "video";

    private static final String audioListFileName = "list.txt";

    private static final String silenceVideoFilename = "silenceVideo.mp4";

    private static final String finalVideoFilename = "video.mp4";

    private static final String finalThumbnailFilename = "thumbnail.png";

    private static final String mergedAudioFilename = "mergeAudio.mp3";

    private static final String imageExtension = "jpg";

    @BeforeEach
    void beforeEach() {
        fFmpegConf = new FFmpegConfigure.FFmpegConf();
        fFmpegConf.setExeTimeoutSeconds(120);
        fFmpegWrapper = new FFmpegWrapper(fFmpegConf);
    }


    // region public method
    @Test
    void createVideoFromImagesAsStream() throws
            FFmpegBaseException, InterruptedException, IOException {
        var silenceVideoPath = getSilenceVideoPath();
        Files.deleteIfExists(silenceVideoPath);

        var process = fFmpegWrapper.createVideoFromImagesAsStream(silenceVideoPath);

        var ffmpegOutputStream = process.getInputStream();
        var ffmpegInputStream = process.getOutputStream();

        var testImage = "https://bj.bcebos.com/v1/digital-human-material/test/character/yidong/front.jpg";
        var image = ImageIO.read(new URL(testImage));
        for (int i = 0; i < 10; i++) {
            ImageIO.write(image, "jpg", ffmpegInputStream);
            log.info("ffmpeg process status:{}", process.isAlive());
            Thread.sleep(100);
        }
        ffmpegInputStream.close();
        var result = process.waitFor();
        log.info("wait for result: {}", result);

        var exitValue = process.exitValue();
        log.info("exit result: {}", exitValue);

        assertEquals(0, exitValue);
        var content = new String(ffmpegOutputStream.readAllBytes());
        log.info(content);

        log.info(silenceVideoPath.toString());
        assertTrue(Files.exists(silenceVideoPath));
    }


    /**
     * 函数最后的数字表示执行顺序
     *
     * @throws IOException
     * @throws InterruptedException
     */
    @Test
    @Order(1)
    void createVideoFromImages() throws IOException, FFmpegBaseException {
        var result = fFmpegWrapper.createVideoFromImages(
                getImageSubDirectory(),
                getSilenceVideoPath().toAbsolutePath(),
                imageExtension,
                5
        );
        System.out.println(fFmpegWrapper.getLastErrorMessage());
        assertTrue(Files.exists(result));
    }

    /**
     * 函数最后的数字表示执行顺序
     *
     * @throws IOException
     * @throws InterruptedException
     */
    @Test
    void createVideoThumbnail() throws IOException, FFmpegBaseException {
        var result = fFmpegWrapper.createVideoThumbnail(
                getFinalVideoPath(),
                getFinalThumbnailPath());
        assertTrue(Files.exists(result));
    }

    @Test
    @Order(2)
    void joinAudioFragment() throws IOException, FFmpegBaseException {
        var result = fFmpegWrapper.joinAudioFragment(
                getAudioListFilePath(),
                getMergedAudioPath().toAbsolutePath()
        );
        System.out.println(getAudioListFilePath().toString());
        System.out.println(fFmpegWrapper.getLastErrorMessage());
        assertTrue(Files.exists(result));
    }

    @Test
    @Order(3)
    void mixAudio2Video() throws IOException, FFmpegBaseException {
        assertTrue(Files.exists(getMergedAudioPath()));
        assertTrue(Files.exists(getSilenceVideoPath()));

        var result = fFmpegWrapper.mixAudio2Video(
                25,
                getSilenceVideoPath().toAbsolutePath(),
                getMergedAudioPath().toAbsolutePath(),
                getFinalVideoPath().toAbsolutePath(),
                48000,
                2
        );
        System.out.println(fFmpegWrapper.getLastErrorMessage());
        assertTrue(Files.exists(result));
    }

    @Test
    void test_Mix2VideoByKeepInnerAspectRatio() throws FFmpegBaseException, IOException {
        Path res = fFmpegWrapper.mix2VideoByKeepInnerAspectRatio(
                new VideoSource.LocalFileVideoSource(Path.of("/Users/<USER>/tmp", "60f39f717a5d1d6d3692cecd.mp4")),
                new VideoSource.LocalFileVideoSource(Path.of("/Users/<USER>/tmp", "mov_bbb.mp4")),
                Path.of("/Users/<USER>/tmp", "out.mp4"),
                500,
                -200, 300,
                0);
        System.out.println(fFmpegWrapper.getLastErrorMessage());
    }

    @Test
    void testMix2Video() throws FFmpegBaseException, IOException {
        Path res = fFmpegWrapper.mix2Video(
                new VideoSource.LocalFileVideoSource(Path.of("/Users/<USER>/tmp", "60f39f717a5d1d6d3692cecd.mp4")),
                new VideoSource.LocalFileVideoSource(Path.of("/Users/<USER>/tmp", "mov_bbb.mp4")),
                Path.of("/Users/<USER>/tmp", "out.mp4"),
                Rect.builder()
                        .x(500)
                        .y(600)
                        .width(400)
                        .build(),
                3000);
        System.out.println(fFmpegWrapper.getLastErrorMessage());
        assertTrue(Files.exists(res));
    }

    // endregion public method

    // region private method

    private Path getImageSubDirectory() {
        return Paths.get(rootPath, imageSubDirectory);
    }


    private Path getAudioSubDirectory() {
        return Paths.get(rootPath, audioSubDirectory);
    }

    private Path getVideoSubDirectory() {
        return Paths.get(rootPath, videoSubDirectory);
    }

    private Path getFinalVideoPath() {
        return Paths.get(getVideoSubDirectory().toString(), finalVideoFilename);
    }

    private Path getFinalThumbnailPath() {
        return Paths.get(getImageSubDirectory().toString(), finalThumbnailFilename);
    }

    private Path getSilenceVideoPath() {
        return Paths.get(getVideoSubDirectory().toString(), silenceVideoFilename);
    }

    private Path getMergedAudioPath() {
        return Paths.get(getVideoSubDirectory().toString(), mergedAudioFilename);
    }

    private Path getAudioListFilePath() {
        return Paths.get(getAudioSubDirectory().toString(), audioListFileName);
    }

    // endregion private method
}