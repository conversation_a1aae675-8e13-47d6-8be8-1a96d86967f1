package com.baidu.acg.piat.digitalhuman.videopipeline.proxy.service.impl;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.Mockito.when;

import java.nio.file.Path;
import java.nio.file.Paths;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.core.io.ClassPathResource;

import com.baidu.acg.piat.digitalhuman.videopipeline.config.ChromakeyConfigure;
import com.baidu.acg.piat.digitalhuman.videopipeline.model.wrapper.FFmpegWrapper;
import com.baidu.acg.piat.digitalhuman.videopipeline.proxy.service.ChromaKeyService;

import lombok.extern.slf4j.Slf4j;

@Slf4j
class ChromaKeyServiceImplTest {

    @Mock
    private FFmpegWrapper mockFfmpegWrapper;

    @Mock
    private ChromakeyConfigure.ChromakeyConfig config;

    @InjectMocks
    private ChromaKeyServiceImpl chromaKeyServiceImplUnderTest;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    void testChromaKey() throws Exception {
        when(config.getFragmentShaderTemplatePath())
                .thenReturn(new ClassPathResource("/chromakey_fragment_shader.glsl").getFile().toPath().toString());
        when(config.getVertexShaderTemplatePath())
                .thenReturn(new ClassPathResource("/chromakey_vertex_shader.glsl").getFile().toPath().toString());

        // Setup
        final ChromaKeyService.ChromaKeyParamV2 param = new ChromaKeyService.ChromaKeyParamV2();
        param.setWidth(1080);
        param.setHeight(1920);
        param.setScreen(new int[] {0, 100, 0});
        param.setSimilarity(0);
        param.setSmoothness(0);
        param.setOpacity(0.0f);
        param.setContrast(0.0f);
        param.setBrightness(0.0f);
        param.setSpill(0);

        Path target = Paths.get("filename.txt");
        when(mockFfmpegWrapper.chromakey(
            anyInt(),
            anyInt(),
            any(),
            any(),
            any(),
            any(),
            any()
        )).thenReturn(target);

        // Run the test
        final Path result = chromaKeyServiceImplUnderTest.chromaKeyV2(Path.of("video_path.mp4"), param);

        // Verify the results
        assertThat(result).isEqualTo(target);
    }

    @Test
    public void test(){

        int width = 1080;
        int height = 1920;
        Path videoPath = Paths.get("src.mp4");
        Path fragmentShaderPath = Paths.get("fragment_shader.glsl");
        Path vertexShaderPath = Paths.get("vertex_shader.glsl");

        String srcVideoPathStr = videoPath.toAbsolutePath().toString();
        String dstVideoPathStr = srcVideoPathStr.substring(0, srcVideoPathStr.length() - 4) + ".webm";

        String command = String.format(
                "xvfb-run -a --server-args=\"-screen 0 %dx%dx24 -ac -nolisten tcp -dpi 96 +extension RANDR\""
                        + " ffmpeg -v debug -i %s -filter_complex "
                        + "\"[0:v]plusglshader=sdsource='%s':vxsource='%s'[1outgl];"
                        + "[1outgl]scale=%d:%d,format=yuva420p\" "
                        + "-vcodec libvpx -pix_fmt yuva420p -auto-alt-ref 0 -y %s",
                width,
                height,
                videoPath.toAbsolutePath(),
                fragmentShaderPath.toAbsolutePath(),
                vertexShaderPath.toAbsolutePath(),
                width,
                height,
                dstVideoPathStr
        );
        log.debug("command: {}", command);
    }
}
