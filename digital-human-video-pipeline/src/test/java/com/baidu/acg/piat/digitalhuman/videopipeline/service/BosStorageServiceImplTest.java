package com.baidu.acg.piat.digitalhuman.videopipeline.service;

import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.baidubce.services.bos.BosClient;
import com.baidubce.services.bos.model.CannedAccessControlList;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.nio.file.Paths;

import com.baidu.acg.piat.digitalhuman.storage.config.StorageBosConfigure;
import com.baidu.acg.piat.digitalhuman.storage.service.StorageService;
import com.baidu.acg.piat.digitalhuman.storage.service.impl.BosStorageServiceImpl;

/**
 * Created on 2021/3/10 13:43.
 *
 * <AUTHOR>
 */
public class BosStorageServiceImplTest {

    @InjectMocks
    private BosStorageServiceImpl bosStorageService;

    @Mock
    private BosClient bosClient;

    @Mock
    private StorageBosConfigure.BosConf bosConf;

    @BeforeEach
    public void init() {
        MockitoAnnotations.initMocks(this);
    }


    @Test
    public void testSave() throws StorageService.ResourceException {
        when(bosConf.getRetryTimes()).thenReturn(3);
        when(bosConf.getBucket()).thenReturn("bucket");
        when(bosConf.getEndpoint()).thenReturn("https://digital-human-pipeline-output.cdn.bcebos.com");
        bosStorageService.save(Paths.get("videoId"));
        verify(bosClient, times(1)).putObject("bucket", "videoId",
                Paths.get("videoId").toFile());
        verify(bosClient, times(1)).setObjectAcl("bucket", "videoId",
                CannedAccessControlList.PublicRead);

    }
}
