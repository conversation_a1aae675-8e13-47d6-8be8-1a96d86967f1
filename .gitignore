# Compiled class file
*.class

# Log file
*.log

# BlueJ files
*.ctxt

# Mobile Tools for Java (J2ME)
.mtj.tmp/

# Package Files #
*.jar
*.war
*.nar
*.ear
*.zip
*.tar.gz
*.rar

# virtual machine crash logs, see http://www.java.com/en/download/help/error_hotspot.xml
hs_err_pid*

# ide
.vscode/


temp/

target/
pom.xml.tag
pom.xml.releaseBackup
pom.xml.versionsBackup
pom.xml.next
release.properties
dependency-reduced-pom.xml
buildNumber.properties
.mvn/timing.properties
.mvn/wrapper/maven-wrapper.jar

.idea/
*.iml

output/

*.DS_Store

digital-human-web/src/main/resources/static
character-manager/character-manager-service/*.jpg

# for vscode
.project
.settings/
.classpath
.factorypath

# node
node_modules
# package.json
package-lock.json
# local
.build_local

*.flattened-pom.xml

.svn
.idea
.tmp
.download
output
.*.swp
.*.swo

logs/
*.log
target/
!.mvn/wrapper/maven-wrapper.jar
!**/src/main/**/target/
!**/src/test/**/target/

*.iws
*.iml
*.ipr

.apt_generated
.classpath
.factorypath
.project
.settings
.springBeans
.sts4-cache

/nbproject/private/
/nbbuild/
/dist/
/nbdist/
/.nb-gradle/

.vscode/

.DS_Store
.venv
hahafont/
hahaImg/