condition=$1

command=package

script=$0
if [ ${script:0:1} == "/" ]; then
    workspace=`dirname $script`/..
else
    workspace=`pwd`/`dirname $script`/..
fi

cd ${workspace}

module="render-proxy-a2a"
if [[ ${condition} = "only_docker" ]]
then
  ver=`date +%Y%m%d%H%M%S`
  docker build -f ${workspace}/docker/${module}.Dockerfile -t registry.baidubce.com/digitalhuman/${module}:local-${ver} ${workspace}
  docker push registry.baidubce.com/digitalhuman/${module}:local-${ver}
  exit 0
fi

if [ ${command} = 'package' ]; then
    mvn -e clean ${command} -pl render-proxy/render-proxy-a2a -am -U -DskipTests -T4 --offline
else
    mvn -e clean ${command} -pl render-proxy/render-proxy-a2a -am -U -T4 --offline
fi

if [ $? -ne 0 ] ; then
  echo "mvn build error"
  exit -1
fi

if [ ${command} = "compile" -o ${command} = "test" ]; then
    exit 0
fi

rm -r output
mkdir output

proxy_dir=output/render-proxy/${module}
mkdir -p ${proxy_dir}/log/info
touch ${proxy_dir}/log/info/${module}.info.log
cp -r render-proxy/${module}/bin ${proxy_dir}
cp -r render-proxy/${module}/conf ${proxy_dir}
cp render-proxy/${module}/target/${module}.jar ${proxy_dir}/bin

# get brtc agent
wget  -O output/brtc_agent.tar.gz --no-check-certificate http://digital-human-brtcas.bj.bcebos.com/brtc_agent_20210130.tar.gz?authorization=bce-auth-v1/1a7242f0e18749fcbee46984c3725f37/2021-01-30T07:04:35Z/-1/host/a65d3706d0ae05fa80515c7aab6830020c7368249e9030384f0da1ed9810a538
mkdir -p output/brtc_agent
tar -zxvf output/brtc_agent.tar.gz -C output/brtc_agent

ver=`date +%Y%m%d%H%M%S`
docker build -f ${workspace}/docker/${module}.Dockerfile -t registry.baidubce.com/digitalhuman/${module}:local-${ver} ${workspace}
docker push registry.baidubce.com/digitalhuman/${module}:local-${ver}