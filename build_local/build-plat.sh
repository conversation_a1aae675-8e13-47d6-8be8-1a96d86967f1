set -x
condition=$1

command=package

script=$0
if [ ${script:0:1} == "/" ]; then
    workspace=`dirname $script`/..
else
    workspace=`pwd`/`dirname $script`/..
fi

cd ${workspace}

module="digital-human-plat"
ver=`date +%Y%m%d%H%M%S`
dockerFile=${workspace}/docker/digital-human-platform.Dockerfile
imageTag=registry.baidubce.com/digitalhuman/platform:local-${ver}

if [[ ${condition} = "only_docker" ]]
then
  docker build -f ${dockerFile} -t ${imageTag} ${workspace}
  docker push ${imageTag}
  exit 0
fi

if [ ${command} = 'package' ]; then
    mvn -e clean ${command} -U -DskipTests -T4
else
    mvn -e clean ${command} -U -T4
fi

if [ $? -ne 0 ] ; then
  echo "mvn build error"
  exit -1
fi

if [ ${command} = "compile" -o ${command} = "test" ]; then
    exit 0
fi

rm -r output
mkdir -p output/${module}/log/info
touch output/${module}/log/info/${module}.info.log
cp -r ${module}/bin output/${module}
cp -r ${module}/conf output/${module}
cp ${module}/target/${module}.jar output/${module}/bin

docker build -f ${dockerFile} -t ${imageTag} ${workspace}
docker push ${imageTag}