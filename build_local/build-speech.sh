condition=$1

command=package

script=$0
if [ ${script:0:1} == "/" ]; then
    workspace=`dirname $script`/..
else
    workspace=`pwd`/`dirname $script`/..
fi

cd ${workspace}

module="render-proxy-speech"
if [[ ${condition} = "only_docker" ]]
then
  ver=`date +%Y%m%d%H%M%S`
  docker build -f ${workspace}/docker/${module}.Dockerfile -t registry.baidubce.com/digitalhuman/${module}:local-${ver} ${workspace}
  docker push registry.baidubce.com/digitalhuman/${module}:local-${ver}
  exit 0
fi

if [ ${command} = 'package' ]; then
    mvn -e clean ${command} -U -DskipTests -T4
else
    mvn -e clean ${command} -U -T4
fi

if [ $? -ne 0 ] ; then
  echo "mvn build error"
  exit -1
fi

if [ ${command} = "compile" -o ${command} = "test" ]; then
    exit 0
fi

rm -r output
mkdir output

proxy_dir=output/render-proxy/${module}
mkdir -p ${proxy_dir}/log/info
touch ${proxy_dir}/log/info/${module}.info.log
cp -r render-proxy/${module}/bin ${proxy_dir}
cp -r render-proxy/${module}/conf ${proxy_dir}
cp render-proxy/${module}/target/${module}.jar ${proxy_dir}/bin

ver=`date +%Y%m%d%H%M%S`
docker build -f ${workspace}/docker/${module}.Dockerfile -t registry.baidubce.com/digitalhuman/${module}:local-${ver} ${workspace}
docker push registry.baidubce.com/digitalhuman/${module}:local-${ver}