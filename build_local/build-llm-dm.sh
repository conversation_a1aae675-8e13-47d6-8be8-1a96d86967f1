condition=$1

command=package

script=$0
if [ "${script:0:1}" == "/" ]; then
    workspace=$(dirname "$script")/..
else
    workspace=$(pwd)/$(dirname "$script")/..
fi

cd "${workspace}" || exit

module="digital-human-llm-dm"
if [[ ${condition} = "only_docker" ]]
then
  ver=$(date +%Y%m%d%H%M%S)
  docker build -f "${workspace}"/docker/${module}.Dockerfile -t registry.baidubce.com/digitalhuman/${module}:local-"${ver}" "${workspace}"
  docker push registry.baidubce.com/digitalhuman/${module}:local-"${ver}"
  exit 0
fi

if [ ${command} = 'package' ]; then
    mvn -e clean ${command} -pl "${module}" -am -U -DskipTests -T4 --offline
else
    mvn -e clean ${command} -pl "${module}" -am -U -T4 --offline
fi

if [ $? -ne 0 ] ; then
  echo "mvn build error"
  exit 1
fi

if [ ${command} = "compile" ] || [ ${command} = "test" ]; then
    exit 0
fi

rm -r output
mkdir output

proxy_dir=output/${module}
mkdir -p ${proxy_dir}/log/info
touch ${proxy_dir}/log/info/${module}.info.log
cp -r ${module}/bin ${proxy_dir}
cp -r ${module}/conf ${proxy_dir}
cp ${module}/target/${module}.jar ${proxy_dir}/bin/${module}.jar

ver=$(date +%Y%m%d%H%M%S)
docker build -f "${workspace}"/docker/${module}.Dockerfile -t ccr-246im3mw-pub.cnc.bj.baidubce.com/digitalhuman/llm-dm:local-"${ver}" "${workspace}"
docker push ccr-246im3mw-pub.cnc.bj.baidubce.com/digitalhuman/llm-dm:local-"${ver}"