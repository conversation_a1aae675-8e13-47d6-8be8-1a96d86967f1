condition=$1

command=package

script=$0
if [ ${script:0:1} == "/" ]; then
    workspace=`dirname $script`/..
else
    workspace=`pwd`/`dirname $script`/..
fi

cd ${workspace}

module="render-proxy-ue4"
if [[ ${condition} = "only_docker" ]]
then
  ver=`date +%Y%m%d%H%M%S`
  docker build -f ${workspace}/docker/${module}.Dockerfile -t registry.baidubce.com/digitalhuman/${module}:local-${ver} ${workspace}
  docker push registry.baidubce.com/digitalhuman/${module}:local-${ver}
  exit 0
fi

if [ ${command} = 'package' ]; then
    mvn -e clean ${command} -U -DskipTests -T4
else
    mvn -e clean ${command} -U -T4
fi

if [ $? -ne 0 ] ; then
  echo "mvn build error"
  exit -1
fi

if [ ${command} = "compile" -o ${command} = "test" ]; then
    exit 0
fi

rm -r output
mkdir output

wget https://digital-human-cloud.bj.bcebos.com/render-ue4/animoji-20200317-214631.zip?authorization=bce-auth-v1/931b804661f2462a9ae36eea84357241/2020-03-17T13:56:33Z/-1/host/b6c6109dc393251134e061b10190de99f95a6d8c9b800aac65d29b90554bf468 -O output/animoji.zip --no-check-certificate
unzip output/animoji.zip -d output/

mkdir -p output/render-proxy/${module}/render/log/info
touch output/render-proxy/${module}/render/log/info/render.info.log
cp -r render-proxy/${module}/render/bin output/render-proxy/${module}/render
cp -r render-proxy/${module}/render/conf output/render-proxy/${module}/render
cp render-proxy/${module}/render/target/render.jar output/render-proxy/${module}/render/bin
mkdir -p output/render-proxy/${module}/render/prepared-blendshapes
wget https://digital-human-cloud.bj.bcebos.com/blendshapes.tar.gz?authorization=bce-auth-v1/a113fb2802ff4221aa5dd07ba805a41c/2020-04-16T02:37:01Z/-1/host/65c94425af8fb633288ec88b8942b475608847af4594c1c919b1e134a0d5bbbd -O output/render-proxy/${module}/render/prepared-blendshapes/blendshapes.tar.gz --no-check-certificate
tar -zxvf output/render-proxy/${module}/render/prepared-blendshapes/blendshapes.tar.gz -C output/render-proxy/${module}/render/prepared-blendshapes/
rm output/render-proxy/${module}/render/prepared-blendshapes/blendshapes.tar.gz
if [[ render == "render" ]]
then
    cp -r output/animoji output/render-proxy/${module}/render
fi

ver=`date +%Y%m%d%H%M%S`
docker build -f ${workspace}/docker/${module}.Dockerfile -t registry.baidubce.com/digitalhuman/${module}:local-${ver} ${workspace}
docker push registry.baidubce.com/digitalhuman/${module}:local-${ver}