# mtail服务docker镜像制作

## 准备构建镜像的文件

### 应用程序下载

将下载的应用程序放入服务器/home/<USER>/mtail下，/home/<USER>

```
访问 https://github.com/google/mtail/releases, 下载对应的Linux版本
我们使用的版本为: mtail_v3.0.0-rc29_linux_amd64
```



### Dockerfile准备

在/home/<USER>

FROM的基础镜像也需要推送至仓库中，这里请填写您的基础镜像地址

```
FROM iregistry.baidu-int.com/ist/golang:1.12.3-alpine3.9
FROM iregistry.baidu-int.com/ist/registry.qianmo.baidu.com/qianmo/gpu/acu/1881/public/deploy-base:centos7-jdk
COPY mtail /root/apps/mtail
ENV CGO_ENABLED 0
ENV GOOS linux
ENV GOARCH amd64
RUN true
ENV LANG en_US.utf8
```



### 启动脚本

a.在/home/<USER>/mtail下新建startup.sh，启动的程序一定是您下载的，内容如下:

```
#!/bin/bash
echo '================ mtail is ok ==============='
export GOROOT=/usr/local/go
export GOBIN=$GOROOT/bin
cd /root/apps/mtail
exec ./mtail_v3.0.0-rc29_linux_amd64 -progs /root/apps/mtail/config ${config_params} $@
```



b.在/home/<USER>/mtail下新建start.py，内容如下:

```
import os
import sys
import json
import time
import socket
import subprocess

defaultencoding = 'utf-8'

if sys.getdefaultencoding() != defaultencoding:
    reload(sys)
    sys.setdefaultencoding(defaultencoding)

def make_up_configure():
    # make up command to start service
    config_params = ''
    for i in sys.argv[1:]:
        arg = i.strip()
        equal_sign = arg.find('=')
        key = arg[0: equal_sign].strip()
        value = arg[equal_sign + 1:].strip()

        if key.startswith('--config_params'):
            config_params = config_params +" "+ str(value)

        if key.startswith('--config_command'):
            os.system(str(value))

        if key.startswith('--config_logpath'):
            logpath = os.path.dirname(str(value))
            if os.path.exists(logpath) is False:
                os.system(' mkdir -p ' + str(logpath))

            if os.path.exists(str(value)) == False:
                os.system('touch ' + str(value))

            config_params = config_params + " -logs " + str(value)

    with open('/root/apps/mtail/startup.sh', 'r') as f:
        startup_template = f.read()
        startup_template = startup_template.replace('${config_params}', config_params)

    with open('/root/apps/mtail/startup.sh', 'w') as f_out:
            f_out.write(startup_template)

if __name__ == '__main__':

    # add volume folder
    subprocess.call(['mkdir', '-p', '/root/log'])
    cmd = ['sh', '/root/apps/mtail/startup.sh']
    make_up_configure()
    subprocess.call(cmd)
```



c.在/home/<USER>/mtail/config下新建log-error.mtail文件，内容如下:

```
#agg error log metric_name
counter digital_human_log_mtail_error

/(\[ERROR\])/{
    digital_human_log_mtail_error++
}
```



## 构建镜像

切换至/home/<USER>

```
docker build -t hub.baidubce.com/digitalhuman/mtail:0.17 .       #此处的镜像名最好是您的仓库地址+服务名:版本
```



## 推送至harbor仓库

此处以harbor仓库为例:

```
#登录
docker login hub.baidubce.com --username 用户名 --password 密码

#推送
docker push hub.baidubce.com/digitalhuman/mtail:0.17            #此处推送的是步骤2构建出的镜像
```

