package routers

import (
	"dhlive-console/handler"
	"github.com/gin-gonic/gin"
)

// Routers 路由列表
func Routers(e *gin.Engine) {
	thirdParty := e.Group("api/v1")

	// 这个接口是给人像广场使用的
	thirdParty.POST("user/gmv", handler.UserGMV)

	// 工作台使用 授权上报
	thirdParty.POST("user/authorization", handler.UserAuthUpload)

	// 第三方用户删除
	thirdParty.POST("user/auth/del", handler.UserAuthDel)

	// Launch
	thirdParty.POST("broadcast/start/upload", handler.UserLaunchData)

	// 工作台
	thirdParty.POST("live/workbench/gmv/list", handler.LiveWorkbenchGmv)

	// 获取当前正在使用的用户
	thirdParty.POST("live/broadcast/user", handler.UseThirdParty)

	// 上报当前正在使用的用户
	thirdParty.POST("live/broadcast/user/data", handler.UseThirdPartyData)

	// 心跳维护曦灵开关播逻辑
	thirdParty.GET("ws/live/broadcast", handler.WsLiveData)

	// -----------------------------------------------------------------------------------------------------------------

	// 这里的接口是给统计使用的
	statisticsGroup := e.Group("api/v1/statistics/")
	statisticsGroup.POST("user", handler.User)
	statisticsGroup.POST("gmv", handler.Gmv)
}
