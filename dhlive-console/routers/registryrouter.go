package routers

import (
	"acg-ai-go-common/global"
	"acg-ai-go-common/logger"
	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
)

var (
	GinRouter *gin.Engine
)

// InitRouter 初始化gin routers
func InitRouter() {
	switch global.ServerSetting.RunEnv {
	case global.DevEnv:
		gin.SetMode(gin.DebugMode)
		GinRouter = gin.New()
	case global.TestEnv:
		gin.SetMode(gin.DebugMode)
		GinRouter = gin.New()
	case global.ProdEnv:
		gin.SetMode(gin.ReleaseMode)
		GinRouter = gin.New()
	default:
		gin.SetMode(gin.DebugMode)
		GinRouter = gin.New()
	}

	GinRouter.Use(
		logger.GinLog(
			// 开启日志保存Es，默认不开启
			logger.SetOpenReportEs(true),
			// 使用异步保存Es，true为同步上传(会影响API响应速度)
			logger.SetSyncReportEs(false),
			// 设置输出保存的日志级别
			logger.SetLogLevel(logrus.InfoLevel),
		),
	)
	// 健康检查Router
	HealthRouter(GinRouter)
	Routers(GinRouter)
}
