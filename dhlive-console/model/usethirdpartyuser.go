package model

import (
	"acg-ai-go-common/gomysql"
	"gorm.io/gorm"
	"time"
)

type UseThirdPartyUser struct {
	ID             uint64         `json:"id" gorm:"column:id;primarykey"`
	UserId         string         `json:"userId" gorm:"user_id;index:idx_id_os"` // 曦灵用户ID
	LivePlatform   string         `json:"livePlatform" gorm:"live_platform"`     // 直播平台
	ThirdPartyName string         `json:"thirdPartyName" gorm:"third_party_name"`
	ThirdPartyId   string         `json:"thirdPartyId" gorm:"third_party_id"`
	AccountType    string         `json:"accountType" gorm:"account_type"`
	Os             string         `json:"os" gorm:"os;index:idx_id_os"`
	CreatedAt      time.Time      `json:"createdAt" gorm:"column:create_at"`       // 创建时间
	UpdatedAt      time.Time      `json:"updatedAt" gorm:"column:update_at"`       // 更新时间
	DeletedAt      gorm.DeletedAt `json:"deletedAt" gorm:"column:delete_at;index"` // 删除时间/标记删除
}

func (data *UseThirdPartyUser) TableName() string {
	return "use_third_party_user"
}

func (data *UseThirdPartyUser) Save() error {
	// 创建数据
	return gomysql.DB.Save(&data).Error
}

func (data *UseThirdPartyUser) Update() error {
	// 更新数据
	return gomysql.DB.Save(&data).Error
}

func (data *UseThirdPartyUser) FindByUserIdAndOs(userId, os string) error {
	db := gomysql.DB.Where("user_id = ?", userId)
	if os != "" {
		db.Where("os = ?", os)
	}
	err := db.Order("update_at desc").
		Find(&data).Error
	if err == gorm.ErrRecordNotFound {
		return nil
	}
	return err
}

func (data *UseThirdPartyUser) FindByAnchorIds(anchorIds []string) (anchors []*RecentAnchor, err error) {
	err = gomysql.DB.Where("anchor_id in ?", anchorIds).
		Find(&anchors).Error
	if err == gorm.ErrRecordNotFound {
		return nil, nil
	}
	return anchors, err
}

func (data *UseThirdPartyUser) Delete() error {
	err := gomysql.DB.Where("id = ?", data.ID).Delete(&data).Error
	return err
}
