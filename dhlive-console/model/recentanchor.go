package model

import (
	"acg-ai-go-common/gomysql"
	"gorm.io/gorm"
	"time"
)

type RecentAnchor struct {
	ID           uint64         `json:"id" gorm:"column:id;primarykey"`
	AnchorId     string         `json:"anchorId" gorm:"anchor_id;index:idx_anchor_id"`
	AnchorName   string         `json:"anchorName" gorm:"column:anchor_name"`
	ThumbnailUrl string         `json:"thumbnailUrl" gorm:"column:thumbnail_url"`
	CreatedAt    time.Time      `json:"createdAt" gorm:"column:create_at"`       // 创建时间
	UpdatedAt    time.Time      `json:"updatedAt" gorm:"column:update_at"`       // 更新时间
	DeletedAt    gorm.DeletedAt `json:"deletedAt" gorm:"column:delete_at;index"` // 删除时间/标记删除
}

func (data *RecentAnchor) TableName() string {
	return "recent_anchor"
}

func (data *RecentAnchor) Save() error {
	// 创建数据
	return gomysql.DB.Save(&data).Error
}

func (data *RecentAnchor) Update() error {
	// 更新数据
	return gomysql.DB.Save(&data).Error
}

func (data *RecentAnchor) FindByAnchorId(anchorId string) error {
	err := gomysql.DB.Where("anchor_id = ?", anchorId).
		Find(&data).Error
	if err == gorm.ErrRecordNotFound {
		return nil
	}
	return err
}

func (data *RecentAnchor) FindByAnchorIds(anchorIds []string) (anchors []*RecentAnchor, err error) {
	err = gomysql.DB.Where("anchor_id in ?", anchorIds).
		Find(&anchors).Error
	if err == gorm.ErrRecordNotFound {
		return nil, nil
	}
	return anchors, err
}

func (data *RecentAnchor) Delete() error {
	err := gomysql.DB.Where("id = ?", data.ID).Delete(&data).Error
	return err
}
