package model

import (
	"acg-ai-go-common/gomysql"
	"acg-ai-go-common/logger"
	"acg-ai-go-common/utils"
	"gorm.io/gorm"
	"time"
)

const (
	LivePlatformDouYin    = "DOU_YIN"
	LivePlatformKuaiShou  = "KUAI_SHOU"
	LivePlatformMeiTuan   = "MEI_TUAN"
	LivePlatformTaoBao    = "TAO_BAO"
	LivePlatformJingDong  = "JING_DONG"
	LivePlatformXiLing    = "XI_LING"
	LivePlatformPinDuoDuo = "PIN_DUO_DUO"
)

type UserAuthData struct {
	ID             uint64         `json:"id" gorm:"column:id;primarykey"`
	UserId         string         `json:"userId" gorm:"user_id;index:idx_id_name_third_id"`             // 曦灵用户ID
	LivePlatform   string         `json:"livePlatform" gorm:"live_platform;index:idx_id_name_third_id"` // 直播平台
	AuthTime       int64          `json:"authTime" gorm:"auth_time"`
	ThirdPartyName string         `json:"thirdPartyName" gorm:"third_party_name"`
	ThirdPartyId   string         `json:"thirdPartyId" gorm:"third_party_id;index:idx_id_name_third_id"`
	AccountType    string         `json:"accountType" gorm:"account_type"`
	StartTime      int64          `json:"startTime" gorm:"start_time"`             // 最近开播时间
	RecentAnchors  string         `json:"recentAnchors" gorm:"recent_anchors"`     // 最近主播  逗号分隔主播ID
	CreatedAt      time.Time      `json:"createdAt" gorm:"column:create_at"`       // 创建时间
	UpdatedAt      time.Time      `json:"updatedAt" gorm:"column:update_at"`       // 更新时间
	DeletedAt      gorm.DeletedAt `json:"deletedAt" gorm:"column:delete_at;index"` // 删除时间/标记删除
}

func InitModel() {
	if err := gomysql.DB.AutoMigrate(&UserAuthData{}); err != nil {
		logger.Log.Errorf("InitModel UserAuthData error : %s", err)
	}
	if err := gomysql.DB.AutoMigrate(&RecentAnchor{}); err != nil {
		logger.Log.Errorf("InitModel RecentAnchor error : %s", err)
	}
	if err := gomysql.DB.AutoMigrate(&UseThirdPartyUser{}); err != nil {
		logger.Log.Errorf("InitModel RecentAnchor error : %s", err)
	}
}

func (data *UserAuthData) TableName() string {
	return "user_auth_data"
}

func (data *UserAuthData) Insert() error {
	// 创建数据
	return gomysql.DB.Create(&data).Error
}

func (data *UserAuthData) Save() error {
	// 创建数据
	return gomysql.DB.Save(&data).Error
}

func (data *UserAuthData) Update() error {
	// 更新数据
	return gomysql.DB.Save(&data).Error
}

func (data *UserAuthData) FindDeleteByNameAndThirdName(userId, thirdPlatformId, livePlatform string) bool {
	err := gomysql.DB.Unscoped().Where("user_id = ?", userId).
		Where("live_platform = ?", livePlatform).
		Where("third_party_id = ?", thirdPlatformId).
		Where("delete_at is not null").
		Find(&data).Error
	if err == gorm.ErrRecordNotFound {
		return false
	}
	return true
}

func (data *UserAuthData) DataRecovery() {
	//nullTime := &sql.NullTime{}
	update := gomysql.DB.Model(&data).Unscoped().Update("delete_at", nil).Error
	if update != nil {
		logger.Log.Errorf("UserAuthData DataRecovery error : %s", update)
	}
}

func (data *UserAuthData) FindByNameAndThirdName(userId, thirdPlatformId, livePlatform string) error {
	err := gomysql.DB.Where("user_id = ?", userId).
		Where("live_platform = ?", livePlatform).
		Where("third_party_id = ?", thirdPlatformId).
		Find(&data).Error
	if err == gorm.ErrRecordNotFound {
		return nil
	}
	return err
}

func (data *UserAuthData) Delete() error {
	err := gomysql.DB.Where("id = ?", data.ID).Delete(&data).Error
	return err
}

func (data *UserAuthData) SearchUserAuthList(userId, thirdPartyName string, pageNum, pageSize int64) (int64, []*UserAuthData, error) {
	var count int64
	var profiles []*UserAuthData
	conn := gomysql.DB.Model(&UserAuthData{})
	conn.Where("user_id = ?", userId)
	if thirdPartyName != "" {
		conn.Where("third_party_id like ?", "%"+thirdPartyName+"%")
	}
	skip, limit := utils.ConvertPageNumSize2Skip(pageNum, pageSize)
	if err := conn.Count(&count).Order("start_time desc").
		Limit(int(limit)).Offset(int(skip)).Find(&profiles).Error; err != nil {
		return 0, nil, err
	}
	return count, profiles, nil
}

func (data *UserAuthData) SearchAllUserAuthList(userId, thirdPartyName string) ([]*UserAuthData, error) {
	var profiles []*UserAuthData
	conn := gomysql.DB.Model(&UserAuthData{})
	conn.Where("user_id = ?", userId)
	if thirdPartyName != "" {
		conn.Where("third_party_id like ?", "%"+thirdPartyName+"%")
	}
	err := conn.Find(&profiles).Error
	return profiles, err
}

func (data *UserAuthData) FindByReq(livePlatform, userId string, startTime, endTime int64) (list []*UserAuthData, err error) {
	db := gomysql.DB.Model(data)

	if livePlatform != "" {
		db = db.Where("live_platform = ?", livePlatform)
	}

	if userId != "" {
		db = db.Where("user_id = ?", userId)
	}

	if startTime != 0 {
		db = db.Where("update_at >= ?", time.Unix(startTime, 0))
	}

	if endTime != 0 {
		db = db.Where("update_at <= ?", time.Unix(endTime, 0))
	}

	err = db.Find(&list).Error

	if err == gorm.ErrRecordNotFound {
		return nil, nil
	}
	return list, err
}
