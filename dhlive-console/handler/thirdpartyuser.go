package handler

import (
	commProto "acg-ai-go-common/beans/proto"
	"acg-ai-go-common/logger"
	"acg-ai-go-common/utils/httputil"
	"dhlive-console/beans"
	config "dhlive-console/conf"
	"dhlive-console/model"
	"encoding/json"
	"github.com/gin-gonic/gin"
	"github.com/gorilla/websocket"
	"net/http"
	"reflect"
)

var upGrader = websocket.Upgrader{
	ReadBufferSize:  1024,
	WriteBufferSize: 1024,
	CheckOrigin: func(r *http.Request) bool {
		// 这里可以根据需要进行源验证
		return true
	},
}

func UseThirdParty(c *gin.Context) {
	req := &beans.UseThirdUserRequest{}
	if err := c.ShouldBind(&req); err != nil {
		logger.Log.Errorf("UseThirdPartyData bind param fail, err:%v", err)
		c.JSON(http.StatusOK, commProto.NewCommRsp(100001, "参数异常"))
		return
	}

	oldData := &model.UseThirdPartyUser{}
	err := oldData.FindByUserIdAndOs(req.UserId, req.Os)
	if err != nil {
		logger.Log.Errorf("UseThirdPartyData find old data fail, err:%v", err)
		c.JSON(http.StatusOK, commProto.NewCommRsp(commProto.RSP_COM_CODE_SERVER_ERROR, "查询数据失败"))
		return
	}

	if oldData.ID == 0 {
		c.JSON(http.StatusOK, commProto.NewCommRsp(commProto.RSP_COM_CODE_SERVER_ERROR, "获取用户数据失败"))
		return
	}

	c.JSON(http.StatusOK, commProto.NewSuccessRsp(oldData))
}

func UseThirdPartyData(c *gin.Context) {
	req := &beans.UseThirdUserDataRequest{}
	if err := c.ShouldBind(&req); err != nil {
		logger.Log.Errorf("UseThirdPartyData bind param fail, err:%v", err)
		c.JSON(http.StatusOK, commProto.NewCommRsp(100001, "参数异常"))
		return
	}

	oldData := &model.UseThirdPartyUser{}
	err := oldData.FindByUserIdAndOs(req.UserId, req.Os)
	if err != nil {
		logger.Log.Errorf("UseThirdPartyData find old data fail, err:%v", err)
		c.JSON(http.StatusOK, commProto.NewCommRsp(commProto.RSP_COM_CODE_SERVER_ERROR, "查询数据失败"))
		return
	}

	oldData.UserId = req.UserId
	oldData.LivePlatform = req.LivePlatform
	oldData.ThirdPartyName = req.ThirdPartyName
	oldData.ThirdPartyId = req.ThirdPartyId
	oldData.AccountType = req.AccountType
	oldData.Os = req.Os
	oldData.Save()
	c.JSON(http.StatusOK, commProto.NewSuccessRsp(nil))
}

func WsLiveData(c *gin.Context) {
	ws, err := upGrader.Upgrade(c.Writer, c.Request, nil)
	if err != nil {
		return
	}
	defer func() {
		_ = ws.Close()
		logger.Log.Infof("WsLiveData 连接断开")
	}()

	userData := &beans.UserLiveData{}

	for {
		_, message, err := ws.ReadMessage()
		if err != nil {
			logger.Log.Errorf("WsLiveData 读取数据失败, err:%v", err)
			break
		}
		logger.Log.Infof("WsLiveData 收到消息:%s", message)

		req := &beans.WsLiveDataRequest{}
		err = json.Unmarshal(message, req)
		if err != nil {
			logger.Log.Errorf("WsLiveData 解析数据失败, err:%v", err)
			continue
		}
		logger.Log.Infof("WsLiveData 解析数据成功, req:%v", req)

		dataMap := make(map[string]interface{}, 0)
		var ok bool

		switch req.ActionType {
		case beans.ActionTypeLiveBroadcastData:
			if dataMap, ok = req.Action.(map[string]interface{}); !ok {
				logger.Log.Errorf("AnalysisData gmv data conv error data is not map, data :%+v", req.Action)
				ws.WriteJSON(commProto.NewCommRsp(100001, "解析数据错误"))
				return
			}
			liveData := &beans.LiveBroadcastData{}
			if mapToStruct(dataMap, liveData) {
				logger.Log.Infof("WsLiveData 收到直播数据:%v", liveData)
				ws.WriteJSON(commProto.NewSuccessRsp(nil))
				if liveData.LivePlatform != beans.DOU_YIN {
					continue
				}
				userData.UserIdXiling = liveData.UserId
				userData.LivePlatform = liveData.LivePlatform
				userData.LivePlatform = liveData.LivePlatform
				userData.ThirdPartyName = liveData.ThirdPartyName
				userData.ThirdPartyId = liveData.ThirdPartyId
				userData.StartTime = liveData.StartTime
				userData.XilingLiveId = liveData.SessionId
				NoticeLiveData(userData)
				continue
			}
			ws.WriteJSON(commProto.NewCommRsp(100001, "解析数据错误"))
			logger.Log.Errorf("WsLiveData 解析liveBroadcastData数据失败, data : %+v", dataMap)
		case beans.ActionTypeHeartbeat:
			if userData == nil || userData.UserIdXiling == "" {
				logger.Log.Errorf("WsLiveData 心跳数据失败,未找到开播数据 userData:%v", userData)
				ws.WriteJSON(commProto.NewCommRsp(100001, "请先发送开播数据"))
			}
			NoticeLiveData(userData)
			ws.WriteJSON(commProto.NewSuccessRsp(""))
		default:
			logger.Log.Errorf("WsLiveData 未知数据类型, data : %+v", req)
			ws.WriteJSON(commProto.NewCommRsp(100001, "未知数据类型"))
		}
	}
}

func NoticeLiveData(userData *beans.UserLiveData) {

	liveDataResp := &commProto.CommDataRsp{}

	err := httputil.PostJson(config.LocalConfig.BroadcastSetting.XilingReportLiveDataUrl, &userData, &liveDataResp)
	if err != nil {
		logger.Log.Errorf("LiveWorkbenchGmv post broadcast fail, err:%v", err)
	}

	if liveDataResp.Code != commProto.RSP_COM_CODE_SUCCESS {
		logger.Log.Errorf("LiveWorkbenchGmv post broadcast fail, resp:%+v", liveDataResp)
	}
}

func mapToStruct(m map[string]interface{}, s interface{}) bool {
	if m == nil || s == nil {
		return false
	}

	sType := reflect.TypeOf(s).Elem()
	sValue := reflect.ValueOf(s).Elem()

	for i := 0; i < sType.NumField(); i++ {
		field := sType.Field(i)
		fieldTag := field.Tag.Get("json")
		fieldValue, ok := m[fieldTag]
		if !ok {
			continue
		}

		fieldName := field.Name

		v := reflect.ValueOf(fieldValue)
		if v.Type() != field.Type {
			if v.Type().Kind() == reflect.Float64 && field.Type.Kind() == reflect.Int64 {
				intValue := int64(v.Float())
				v = reflect.ValueOf(intValue)
			} else if v.Type().Kind() == reflect.Float64 && field.Type.Kind() == reflect.Int {
				intValue := int(v.Float())
				v = reflect.ValueOf(intValue)
			} else {
				continue
			}
		}

		structField := sValue.FieldByName(fieldName)
		if !structField.IsValid() {
			continue
		}

		structField.Set(v)
	}

	if sValue.NumField() == 0 {
		logger.Log.Infof("mapToStruct %v", sValue.NumField())
		return false
	}
	return true
}
