package handler

import (
	"math"
	"net/url"
	"testing"
)

func TestRandom(t *testing.T) {
	pow10 := math.Pow10(2)
	t.<PERSON><PERSON>(278.01 * 100 / pow10)
}

func TestUrl(t *testing.T) {
	escape := url.QueryEscape("当地时间11日，以色列国防军发表声明，称将继续对加沙地带南部城市拉法展开军事行动，并要求拉法东南部地区的巴勒斯坦人向所谓的“人道主义区”撤离。 以军还宣布将对加沙北部部分地区展开猛烈打击。当天，以军一名阿拉伯语发言人对外宣布，要求拉法东南部的拉法难民营、沙布拉难民营、杰奈纳地区和基尔贝特阿达斯社区的巴勒斯坦人撤离，要求相关人员尽快前往位于马瓦西和汗尤尼斯的所谓“人道主义区”。此外，以军要求加沙北部杰巴利耶地区及周边的巴勒斯坦人前往加沙城以西寻求庇护。发言人称，哈马斯正在加沙北部上述地区重建军事能力，以军将对相关地区展开猛烈打击。以军发言人同时强调禁止加沙民众靠近边界隔离墙。")
	t.Log(escape)
}
