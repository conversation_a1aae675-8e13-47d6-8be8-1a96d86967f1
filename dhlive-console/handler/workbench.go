package handler

import (
	commProto "acg-ai-go-common/beans/proto"
	"acg-ai-go-common/logger"
	"acg-ai-go-common/utils/httputil"
	"dhlive-console/beans"
	config "dhlive-console/conf"
	"dhlive-console/model"
	"fmt"
	"github.com/gin-gonic/gin"
	"math"
	"net/http"
	"strings"
	"time"
)

// UserGMV 获取用户GMV
func UserGMV(c *gin.Context) {
	req := &beans.UserGmvRequest{}
	if err := c.ShouldBind(&req); err != nil {
		logger.Log.Errorf("UserGMV bind param fail, err:%v", err)
		c.JSO<PERSON>(http.StatusOK, commProto.NewCommRsp(100001, "参数异常"))
		return
	}

	gmvReq := &beans.BroadcastDataByUserNameRequest{
		UserIdXiling: req.UserId,
	}
	gmvResp := &beans.BroadcastDataByUserNameResponse{}

	err := httputil.PostJson(config.LocalConfig.BroadcastSetting.GmvUrl, &gmvReq, &gmvResp)
	if err != nil {
		logger.Log.Errorf("LiveWorkbenchGmv post broadcast fail, err:%v", err)
	}

	if gmvResp.Code != 0 {
		logger.Log.Errorf("LiveWorkbenchGmv post broadcast fail, code:%d", gmvResp.Code)
	}

	resp := beans.UserGmvResponse{}
	pow10 := math.Pow10(2)
	resp.Result.Gmv = gmvResp.Data.Gmv * 100 / pow10
	resp.Success = true

	logger.Log.Infof("UserGMV bind param succ param:%v", req)
	c.JSON(http.StatusOK, resp)
}

func UserAuthUpload(c *gin.Context) {
	req := &beans.UserAuthUploadRequest{}
	if err := c.ShouldBind(&req); err != nil {
		logger.Log.Errorf("UserAuthUpload bind param fail, err:%v", err)
		c.JSON(http.StatusOK, commProto.NewCommRsp(100001, "参数异常"))
		return
	}

	oldUser := &model.UserAuthData{}
	// 这里是之前已经删除的授权信息，需要加上
	if oldUser.FindDeleteByNameAndThirdName(req.UserId, req.ThirdPartyId, req.LivePlatform) {
		if oldUser.ID != 0 {
			oldUser.DataRecovery()
		}
	}

	user := &model.UserAuthData{}
	err := user.FindByNameAndThirdName(req.UserId, req.ThirdPartyId, req.LivePlatform)
	if err != nil {
		logger.Log.Errorf("UserAuthUpload select mysql fail, err:%v", err)
		c.JSON(http.StatusOK, commProto.NewCommRsp(commProto.RSP_COM_CODE_SERVER_ERROR, err.Error()))
		return
	}

	user.UserId = req.UserId
	user.ThirdPartyId = req.ThirdPartyId
	user.LivePlatform = req.LivePlatform
	user.AuthTime = req.AuthTime
	user.ThirdPartyName = req.ThirdPartyName
	user.AccountType = req.AccountType
	user.UpdatedAt = time.Now()

	err = user.Save()
	if err != nil {
		logger.Log.Errorf("UserAuthUpload update mysql fail, err:%v", err)
		c.JSON(http.StatusOK, commProto.NewCommRsp(commProto.RSP_COM_CODE_SERVER_ERROR, err.Error()))
		return
	}

	c.JSON(http.StatusOK, commProto.NewSuccessRsp(nil))
}

// UserAuthDel 这个是用户删除授权记录，具体的取消授权需要前端请求各个方向
func UserAuthDel(c *gin.Context) {
	req := &beans.UserAuthDelRequest{}
	if err := c.ShouldBind(&req); err != nil {
		logger.Log.Errorf("UserAuthDel bind param fail, err:%v", err)
		c.JSON(http.StatusOK, commProto.NewCommRsp(100001, "参数异常"))
		return
	}

	user := &model.UserAuthData{}
	err := user.FindByNameAndThirdName(req.UserId, req.ThirdPartyId, req.LivePlatform)
	if err != nil {
		logger.Log.Errorf("UserAuthDel select mysql fail, err:%v", err)
		c.JSON(http.StatusOK, commProto.NewCommRsp(commProto.RSP_COM_CODE_SERVER_ERROR, err.Error()))
		return
	}

	if user.ID == 0 {
		logger.Log.Errorf("UserAuthDel select user is not fount")
		c.JSON(http.StatusOK, commProto.NewCommRsp(commProto.RSP_COM_CODE_PERMISSIONS_INSUFFICIENT, "用户不存在"))
		return
	}

	err = user.Delete()
	if err != nil {
		logger.Log.Errorf("UserAuthDel delete mysql fail, err:%v", err)
		c.JSON(http.StatusOK, commProto.NewCommRsp(commProto.RSP_COM_CODE_SERVER_ERROR, err.Error()))
		return
	}

	c.JSON(http.StatusOK, commProto.NewSuccessRsp(nil))
}

func UpdateRecentAnchor(data []beans.RecentAnchor) {
	for _, v := range data {
		anchor := &model.RecentAnchor{}
		err := anchor.FindByAnchorId(v.AnchorId)
		if err != nil {
			logger.Log.Errorf("UpdateRecentAnchor select mysql fail, err:%v", err)
			continue
		}
		if anchor.ID == 0 {
			anchor.AnchorId = v.AnchorId
			anchor.AnchorName = v.AnchorName
			anchor.ThumbnailUrl = v.ThumbnailUrl
			anchor.Save()
		} else if (anchor.AnchorName != v.AnchorName) || (anchor.ThumbnailUrl != v.ThumbnailUrl) {
			anchor.AnchorName = v.AnchorName
			anchor.ThumbnailUrl = v.ThumbnailUrl
			anchor.UpdatedAt = time.Now()
			anchor.Save()
		}

	}
}

func UserLaunchData(c *gin.Context) {
	req := &beans.UserLaunchDataRequest{}
	if err := c.ShouldBind(&req); err != nil {
		logger.Log.Errorf("UserLaunchData bind param fail, err:%v", err)
		c.JSON(http.StatusOK, commProto.NewCommRsp(100001, fmt.Sprintf("参数异常, %s", err.Error())))
		return
	}

	userAuth := &model.UserAuthData{}
	err := userAuth.FindByNameAndThirdName(req.UserId, req.ThirdPartyId, req.LivePlatform)
	if err != nil {
		logger.Log.Errorf("UserLaunchData select mysql fail, err:%v", err)
		c.JSON(http.StatusOK, commProto.NewCommRsp(commProto.RSP_COM_CODE_SERVER_ERROR, err.Error()))
		return
	}
	oldAnchors := userAuth.RecentAnchors

	go UpdateRecentAnchor(req.RecentAnchors)

	recentAnchors := ""
	for _, v := range req.RecentAnchors {
		recentAnchors += v.AnchorId + ","
		if strings.Contains(oldAnchors, v.AnchorId) {
			oldAnchors = strings.Replace(oldAnchors, v.AnchorId+",", "", -1)
		}
	}
	// 体验模式没有授权记录
	if userAuth.ID == 0 && req.LivePlatform == model.LivePlatformXiLing {
		userAuth.UserId = req.UserId
		userAuth.LivePlatform = model.LivePlatformXiLing
		userAuth.ThirdPartyId = req.ThirdPartyId
		userAuth.StartTime = req.StartTime
		userAuth.ThirdPartyName = req.ThirdPartyName
		userAuth.RecentAnchors = recentAnchors
		userAuth.Save()
		c.JSON(http.StatusOK, commProto.NewSuccessRsp(""))
		return
	}

	if userAuth.ID == 0 {
		logger.Log.Errorf("UserLaunchData select user is not fount")
		c.JSON(http.StatusOK, commProto.NewCommRsp(commProto.RSP_COM_CODE_PERMISSIONS_INSUFFICIENT, "授权记录不存在"))
		return
	}

	anchor := recentAnchors + oldAnchors
	split := strings.Split(anchor, ",")
	if len(split) > 3 {
		split = split[:3]
	}
	s := strings.Join(split, ",")

	userAuth.StartTime = req.StartTime
	userAuth.RecentAnchors = s
	userAuth.UpdatedAt = time.Now()
	userAuth.Save()

	c.JSON(http.StatusOK, commProto.NewSuccessRsp(nil))
}

func LiveWorkbenchGmv(c *gin.Context) {
	req := &beans.LiveWorkBenchRequest{}
	if err := c.ShouldBind(&req); err != nil {
		logger.Log.Errorf("LiveWorkbenchGmv bind param fail, err:%v", err)
		c.JSON(http.StatusOK, commProto.NewCommRsp(100001, fmt.Sprintf("参数异常, %s", err.Error())))
		return
	}
	authData := &model.UserAuthData{}
	count, data, err := authData.SearchUserAuthList(req.UserId, req.ThirdPartyName, req.PageNo, req.PageSize)
	if err != nil {
		logger.Log.Errorf("LiveWorkbenchGmv select mysql fail, err:%v", err)
		c.JSON(http.StatusOK, commProto.NewCommRsp(commProto.RSP_COM_CODE_SERVER_ERROR, err.Error()))
		return
	}

	header := c.GetHeader("Username")
	logger.Log.Infof("LiveWorkbenchGmv, user:%s", header)

	// 目前有效的用户授权列表
	allUserAuth, err := authData.SearchAllUserAuthList(req.UserId, req.ThirdPartyName)
	if err != nil {
		logger.Log.Errorf("LiveWorkbenchGmv select all user fail, err:%v", err)
		c.JSON(http.StatusOK, commProto.NewCommRsp(commProto.RSP_COM_CODE_SERVER_ERROR, err.Error()))
		return
	}

	// 获取所有用户授权信息 key为平台+用户ID
	userAllMap := GenerateAllUser(allUserAuth)

	// 拼接获取gmv的请求参数
	gmvReq := &beans.BroadcastDataByUserRequest{
		UserIdXiling:      req.UserId,
		LivePlatformUsers: nil,
	}

	// 这个是用户所有的gmv数据key为平台+用户ID
	dataMap := make(map[string]beans.BroadcastDataByUserGmv7Day, 0)
	gmvResp := &beans.BroadcastDataByUserResponse{}
	// 这里会获取这个用户下的所有gmv数据，然后根据平台进行聚合
	err = httputil.PostJson(config.LocalConfig.BroadcastSetting.Gmv7DUrl, gmvReq, gmvResp)
	if err != nil {
		logger.Log.Errorf("LiveWorkbenchGmv post broadcast fail, err:%v", err)
	}

	if gmvResp.Code != 0 {
		logger.Log.Errorf("LiveWorkbenchGmv post broadcast fail, code:%d", gmvResp.Code)
	}

	if gmvResp.Data != nil {
		dataMap = gmvResp.Data
	}

	resp := &beans.LiveWorkBenchResponse{
		Count:             count,
		List:              make([]beans.UserList, 0),
		Gmv:               0,
		LiveBroadcastNum:  0,
		LiveDuration:      0,
		UniqueUsers:       0,
		AvgDurationOfStay: 0,
		NewFans:           0,
		BulletChatNum:     0,
	}

	for key := range userAllMap {
		if m, ok := dataMap[key]; ok {
			resp.LiveDuration += m.LiveDuration
			resp.Gmv += m.Gmv
			resp.UniqueUsers += m.UniqueUsers
			resp.LiveBroadcastNum += m.LiveBroadcastNum
		}
	}

	recentMap := make(map[string]model.RecentAnchor, 0)
	recentAnchor := &model.RecentAnchor{}

	anchorIds := make([]string, 0)
	for _, v := range data {
		anchorIds = append(anchorIds, strings.Split(v.RecentAnchors, ",")...)
	}

	ids, err := recentAnchor.FindByAnchorIds(anchorIds)
	if err != nil {
		logger.Log.Errorf("LiveWorkbenchGmv select recent anchor fail, err:%v", err)
	}

	for _, v := range ids {
		recentMap[v.AnchorId] = *v
	}

	for _, v := range data {
		recents := make([]beans.RecentAnchor, 0)
		for _, anchor := range strings.Split(v.RecentAnchors, ",") {
			if anchor == "" {
				continue
			}
			if FindAuthorIdInList(recents, anchor) {
				continue
			}
			if d, ok := recentMap[anchor]; ok {
				recents = append(recents, beans.RecentAnchor{
					AnchorId:     anchor,
					AnchorName:   d.AnchorName,
					ThumbnailUrl: d.ThumbnailUrl,
				})
			}

		}
		fieldId := ""
		//1抖音、2快手、3美团、4备用
		switch v.LivePlatform {
		case model.LivePlatformDouYin:
			fieldId = fmt.Sprintf("%d-%s", 1, v.ThirdPartyId)
		case model.LivePlatformKuaiShou:
			fieldId = fmt.Sprintf("%d-%s", 2, v.ThirdPartyId)
		case model.LivePlatformMeiTuan:
			fieldId = fmt.Sprintf("%d-%s", 3, v.ThirdPartyId)
		case model.LivePlatformTaoBao:
			fieldId = fmt.Sprintf("%d-%s", 4, v.ThirdPartyId)
		case model.LivePlatformJingDong:
			fieldId = fmt.Sprintf("%d-%s", 5, v.ThirdPartyId)
		case model.LivePlatformPinDuoDuo:
			fieldId = fmt.Sprintf("%d-%s", 6, v.ThirdPartyId)
		case model.LivePlatformWeiPinHui:
			fieldId = fmt.Sprintf("%d-%s", 7, v.ThirdPartyId)
		case model.LivePlatformXiaoHongShu:
			fieldId = fmt.Sprintf("%d-%s", 8, v.ThirdPartyId)
		case model.LivePlatformShiPinHao:
			fieldId = fmt.Sprintf("%d-%s", 9, v.ThirdPartyId)
		default:
			fieldId = fmt.Sprintf("%d-%s", 0, v.ThirdPartyId)
		}

		userData := beans.UserList{
			UserId:         v.UserId,
			ThirdPartyName: v.ThirdPartyName,
			ThirdPartyId:   v.ThirdPartyId,
			StartTime:      v.StartTime,
			LivePlatform:   v.LivePlatform,
			AccountType:    v.AccountType,
			Fans:           0,
			Gmv7d:          0,
			RecentAnchors:  recents,
		}

		if gmv, ok := dataMap[fieldId]; ok {
			userData.Gmv7d = gmv.Gmv
		}
		resp.List = append(resp.List, userData)
	}

	c.JSON(http.StatusOK, commProto.NewSuccessRsp(resp))

}

func FindAuthorIdInList(list []beans.RecentAnchor, authorId string) bool {
	for _, v := range list {
		if v.AnchorId == authorId {
			return true
		}
	}
	return false
}

func GenerateAllUser(data []*model.UserAuthData) map[string]bool {
	userMap := make(map[string]bool, 0)
	for _, v := range data {
		fieldId := ""
		switch v.LivePlatform {
		case model.LivePlatformDouYin:
			fieldId = fmt.Sprintf("%d-%s", 1, v.ThirdPartyId)
		case model.LivePlatformKuaiShou:
			fieldId = fmt.Sprintf("%d-%s", 2, v.ThirdPartyId)
		case model.LivePlatformMeiTuan:
			fieldId = fmt.Sprintf("%d-%s", 3, v.ThirdPartyId)
		case model.LivePlatformTaoBao:
			fieldId = fmt.Sprintf("%d-%s", 4, v.ThirdPartyId)
		case model.LivePlatformJingDong:
			fieldId = fmt.Sprintf("%d-%s", 5, v.ThirdPartyId)
		case model.LivePlatformPinDuoDuo:
			fieldId = fmt.Sprintf("%d-%s", 6, v.ThirdPartyId)
		case model.LivePlatformWeiPinHui:
			fieldId = fmt.Sprintf("%d-%s", 7, v.ThirdPartyId)
		case model.LivePlatformXiaoHongShu:
			fieldId = fmt.Sprintf("%d-%s", 8, v.ThirdPartyId)
		case model.LivePlatformShiPinHao:
			fieldId = fmt.Sprintf("%d-%s", 9, v.ThirdPartyId)
		default:
			fieldId = fmt.Sprintf("%d-%s", 0, v.ThirdPartyId)
		}
		userMap[fieldId] = true
	}
	return userMap
}
