package handler

import (
	commProto "acg-ai-go-common/beans/proto"
	"acg-ai-go-common/logger"
	"acg-ai-go-common/utils/httputil"
	"dhlive-console/beans"
	config "dhlive-console/conf"
	"dhlive-console/model"
	"github.com/gin-gonic/gin"
	"net/http"
)

func User(c *gin.Context) {
	req := &beans.StatisticsReq{}
	if err := c.ShouldBind(&req); err != nil {
		logger.Log.Errorf("Gvm bind param fail, err:%v", err)
		c.JSO<PERSON>(http.StatusOK, commProto.NewCommRsp(100001, "参数异常"))
		return
	}

	oldData := &model.UserAuthData{}
	list, err := oldData.FindByReq(req.LivePlatform, req.UserIdXiling, req.StartTime, req.EndTime)
	if err != nil {
		logger.Log.Errorf("Gvm find data fail, err:%v", err)
		c.<PERSON>(http.StatusOK, commProto.NewCommRsp(commProto.RSP_COM_CODE_SERVER_ERROR, "查询数据失败"))
		return
	}

	c.JSON(http.StatusOK, commProto.NewSuccessRsp(list))
}

func Gmv(c *gin.Context) {
	req := &beans.StatisticsReq{}
	if err := c.ShouldBind(&req); err != nil {
		logger.Log.Errorf("Gvm bind param fail, err:%v", err)
		c.JSON(http.StatusOK, commProto.NewCommRsp(100001, "参数异常"))
		return
	}

	gmvReq := &beans.PlatformLiveReq{
		StartTime: req.StartTime,
		EndTime:   req.EndTime,
	}
	gmvResp := &beans.PlatformLiveResponse{}

	err := httputil.PostJson(config.LocalConfig.BroadcastSetting.PlatformGmvUrl, &gmvReq, &gmvResp)
	if err != nil {
		logger.Log.Errorf("LiveWorkbenchGmv post broadcast fail, err:%v", err)
	}

	if gmvResp.Code != 0 {
		logger.Log.Errorf("LiveWorkbenchGmv post broadcast fail, code:%d", gmvResp.Code)
	}

	c.JSON(http.StatusOK, commProto.NewSuccessRsp(gmvResp.Data))
}
