---
apiVersion: v1
kind: ConfigMap
metadata:
  labels:
    app: digital-human
    module: dhlive-console
  name: dhlive-console
  namespace: ppe-2d-v3-bk
data:
  conf.toml: |
    server-port = 8080
    server-name = "dhlive-console"
    log-file-prefix = "localhost"
    log-file-path = "./logs/"
    log-show-code-file = true
    log-show-code-func = true
    # consul配置
    [consul-setting]
    host = "127.0.0.1"
    port = 8500
    name = ""

    # 健康检查地址
    health-url = "/health"
    check-timeout = "10s"
    check-interval = "10s"
    deregister-critical-service-after = "30s"

    # mysql配置
    [mysql-setting]
    host = "*************"
    port = 3306
    database = "dhlive_third_platform_online"
    username = "dhlive_tp_plat"
    password = "Hi109@123"
    maxOpenConns = 1000
    maxIdlenConns = 100

    # redis配置
    [redis-setting]
    addr = "redis-ppe-v3.ppe-dh-v3-bk:6379"
    username = ""
    password = ""

    # 日志上传的elasticsearch配置
    [log-es-setting]
    host = "http://*************:8200"
    username = "superuser"
    password = "Baidu_dh123"
    
    [broadcast-setting]
    gmv7DUrl = "http://robot.baidu.com/test02-comm-cgi/ecloud-broadcast-statistics/api/live/broadcast/find/user/gmv7d"
    gmvUrl = "http://robot.baidu.com/test02-comm-cgi/ecloud-broadcast-statistics/api/live/broadcast/find/user/gmv"
    platformGmvUrl = "http://robot.baidu.com/test02-comm-cgi/ecloud-broadcast-statistics/api/live/broadcast/find/platform/gmv"
    xilingReportLiveDataUrl = "http://robot.baidu.com/test02-comm-cgi/ecloud-broadcast-statistics/api/live/broadcast/xiling/start"


---
apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: digital-human
    module: dhlive-console
  name: dhlive-console
  namespace: ppe-2d-v3-bk
spec:
  replicas: 1
  minReadySeconds: 0
  strategy:
    type: RollingUpdate # 策略：滚动更新
    rollingUpdate:
      maxSurge: 25%
      maxUnavailable: 25%
  selector:
    matchLabels:
      app: digital-human
      module: dhlive-console
  template:
    metadata:
      labels:
        app: digital-human
        module: dhlive-console
    spec:
      restartPolicy: Always
      tolerations:
        - key: "limit"
          operator: "Equal"
          value: "ppe-2d-v3-bk"
          effect: "NoSchedule"
      containers:
        - name: dhlive-console
          image: ccr-246im3mw-pub.cnc.bj.baidubce.com/digitalhuman/dhlive-console:20240815_1723708866087
          imagePullPolicy: Always
          command: ["sh"]
          env:
            - name: POD_NAMESPACE
              valueFrom:
                fieldRef:
                  fieldPath: metadata.namespace
          args: ["/home/<USER>/sbin/start.sh"]
          ports:
            - containerPort: 8080
          resources: # 资源限制
            limits:
              cpu: 250m
              memory: 512Mi
            requests:
              cpu: 250m
              memory: 512Mi
          livenessProbe: # 健康检查：存活探针
            httpGet:
              path: /health
              port: 8080
            initialDelaySeconds: 20
            timeoutSeconds: 5
            periodSeconds: 5
          readinessProbe: # 健康检查：就绪探针
            httpGet:
              path: /health
              port: 8080
            initialDelaySeconds: 5
            timeoutSeconds: 1
            periodSeconds: 5
          volumeMounts:
            - mountPath: /home/<USER>/conf/conf.toml
              name: config-volume
              subPath: conf.toml
      volumes:
        - configMap:
            defaultMode: 420
            name: dhlive-third-party
          name: config-volume
      imagePullSecrets:
        - name: regcred
        - name: regcred-ccr
        - name: regcred-eccr
      nodeSelector:
        beta.kubernetes.io/os: linux

---
apiVersion: v1
kind: Service
metadata:
  labels:
    app: digital-human
    module: dhlive-console
  name: dhlive-console
  namespace: ppe-2d-v3-bk
spec:
  selector:
    app: digital-human
    module: dhlive-console
  ports:
    - protocol: TCP
      port: 8080
      targetPort: 8080
  type: ClusterIP