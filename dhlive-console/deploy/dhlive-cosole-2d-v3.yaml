apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: digital-human
    module: dhlive-console
  name: dhlive-console
  namespace: 2d-v3
spec:
  replicas: 1
  minReadySeconds: 0
  strategy:
    type: RollingUpdate # 策略：滚动更新
    rollingUpdate:
      maxSurge: 25%
      maxUnavailable: 25%
  selector:
    matchLabels:
      app: digital-human
      module: dhlive-console
  template:
    metadata:
      labels:
        app: digital-human
        module: dhlive-console
    spec:
      restartPolicy: Always
      tolerations:
        - key: "limit"
          operator: "Equal"
          value: "v3"
          effect: "NoSchedule"
      # restartPolicy: OnFailure
      # restartPolicy: Never
      containers:
        - name: dhlive-console
          image: ccr-246im3mw-pub.cnc.bj.baidubce.com/digitalhuman/dhlive-console:20240617_1718607903846
          imagePullPolicy: Always
          command: ["sh"]
          args: ["/home/<USER>/sbin/start.sh"]
          # imagePullPolicy: IfNotPresent
          # imagePullPolicy: Never
          ports:
            - containerPort: 8080
          resources: # 资源限制
            limits:
              cpu: 250m
              memory: 512Mi
            requests:
              cpu: 250m
              memory: 512Mi
          livenessProbe: # 健康检查：存活探针
            httpGet:
              path: /health
              port: 8080
            initialDelaySeconds: 20
            timeoutSeconds: 5
            periodSeconds: 5
          readinessProbe: # 健康检查：就绪探针
            httpGet:
              path: /health
              port: 8080
            initialDelaySeconds: 5
            timeoutSeconds: 1
            periodSeconds: 5
          # volumeMounts:
          #   - name: nginx-volume
          #     mountPath: "/usr/share/nginx"
      imagePullSecrets:
        - name: regcred
        - name: regcred-ccr
        - name: regcred-eccr
      # volumes:
      #   - name: nginx-volume
      #     flexVolume:
      #       driver: "baidubce/cds"
      #       fsType: "ext4"
      #       options:
      #         volumeID: "{id}" # 填写cds的id，注意：cds必须和pod在同一可用区！！
      nodeSelector:
        beta.kubernetes.io/os: linux
      # tolerations: # 容忍污点
      #   - effect: NoExecute
      #     operator: Exists
      #   - effect: NoSchedule
      #     operator: Exists
      # affinity: # 亲和性
      #   nodeAffinity:
      #     requiredDuringSchedulingIgnoredDuringExecution:
      #       nodeSelectorTerms:
      #         - matchExpressions:
      #             - key: failure-domain.beta.kubernetes.io/zone
      #               operator: In
      #               values:
      #                 - zoneA
      #                 - zoneB
      #                 - zoneC
---
apiVersion: v1
kind: Service
metadata:
  labels:
    app: digital-human
    module: dhlive-console
  name: dhlive-console
  namespace: 2d-v3
spec:
  selector:
    app: digital-human
    module: dhlive-console
  ports:
    - protocol: TCP
      port: 8080
      targetPort: 8080
  type: ClusterIP