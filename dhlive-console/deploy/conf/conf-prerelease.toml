####################################################### 服务配置-生产环境 #######################################################
server-port = 8080
server-name = "dhlive-console"
log-file-prefix = "localhost"
log-file-path = "./logs/"
log-show-code-file = true
log-show-code-func = true
# consul配置
[consul-setting]
host = "127.0.0.1"
port = 8500
name = ""

# 健康检查地址
health-url = "/health"
check-timeout = "10s"
check-interval = "10s"
deregister-critical-service-after = "30s"

# mysql配置
[mysql-setting]
host = "mysql57.rdsmkszsv0111y4.rds.bj.baidubce.com"
port = 3306
database = "dhlive_third_platform_ppe_v3"
username = "ppe_v3"
password = "!Digitalhuman@baidu123"
maxOpenConns = 1000
maxIdlenConns = 100

[redis-setting]
addr = "************:6379"
username = ""
password = ""

# 日志上传的elasticsearch配置
[log-es-setting]
host = "http://*************:8200"
username = "robot"
password = "Hi109.3"

[broadcast-setting]
gmv7DUrl = "http://robot.baidu.com/test02-comm-cgi/ecloud-broadcast-statistics/api/live/broadcast/find/user/gmv7d"
gmvUrl = "http://robot.baidu.com/test02-comm-cgi/ecloud-broadcast-statistics/api/live/broadcast/find/user/gmv"
platformGmvUrl = "http://robot.baidu.com/test02-comm-cgi/ecloud-broadcast-statistics/api/live/broadcast/find/platform/gmv"
xilingReportLiveDataUrl = "http://robot.baidu.com/test02-comm-cgi/ecloud-broadcast-statistics/api/live/broadcast/xiling/start"