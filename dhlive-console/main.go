package main

import (
	"acg-ai-go-common/global"
	"acg-ai-go-common/gomysql"
	"acg-ai-go-common/logger"
	"acg-ai-go-common/server"
	config "dhlive-console/conf"
	"dhlive-console/model"
	"dhlive-console/routers"
	"github.com/BurntSushi/toml"
	"log"
)

func main() {
	// 初始化公共配置
	server.InitGlobalSetting()
	logger.Log.Info("初始化公共配置完成")
	if _, err := toml.DecodeFile(global.ConfFilePath, config.LocalConfig); err != nil {
		log.Panicf("本地个性化配置加载失败: {%v}", err)
	}
	logger.Log.Info("本地个性化配置加载完成")

	// 初始化mysql
	gomysql.InitDB(global.ServerSetting.MysqlSetting)
	logger.Log.Info("初始化mysql完成")

	logger.SetLogger()
	logger.Log.Info("初始化日志完成")

	// 初始化路由
	routers.InitRouter()
	logger.Log.Info("路由初始化完成")

	model.InitModel()
	logger.Log.Info("初始化数据库完成")
	// 启动服务
	server.Run(routers.GinRouter)
}
