package beans

const (
	ActionTypeLiveBroadcastData = "liveBroadcastData"
	ActionTypeHeartbeat         = "heartbeat"
)

const (
	DOU_YIN   = "DOU_YIN"
	KUAI_SHOU = "KUAI_SHOU"
	MEI_TUAN  = "MEI_TUAN"
	TAO_BAO   = "TAO_BAO"
	JING_DONG = "JING_DONG"
	XI_LING   = "XI_LING"
)

type UserGmvRequest struct {
	UserId string `json:"userId"`
}

//type UserGmvResponse struct {
//	Gmv float64 `json:"gmv"`
//}

type UserGmvResponse struct {
	Code    int         `json:"code"`
	Success bool        `json:"success"`
	Message interface{} `json:"message"`
	Result  struct {
		Gmv float64 `json:"gmv"`
	} `json:"result"`
}

type UserAuthUploadRequest struct {
	UserId string `json:"userId"  binding:"required"`

	//平台类型
	//DOU_YIN 抖音、
	//KUAI_SHOU 快手、
	//MEI_TUAN 美团、
	//TAO_BAO 淘宝、
	//JING_DONG 京东、
	//XI_LING 体验模式
	LivePlatform   string `json:"livePlatform"  binding:"required"`
	AuthTime       int64  `json:"authTime"  binding:"required"`
	ThirdPartyName string `json:"thirdPartyName"  binding:"required"`
	ThirdPartyId   string `json:"thirdPartyId"  binding:"required"`
	AccountType    string `json:"accountType"`
}

type UserAuthDelRequest struct {
	UserId       string `json:"userId"  binding:"required"`
	LivePlatform string `json:"livePlatform"  binding:"required"`
	ThirdPartyId string `json:"thirdPartyId"  binding:"required"`
}

type UserLaunchDataRequest struct {
	UserId string `json:"userId"  binding:"required"`
	LiveId string `json:"liveId"`

	LivePlatform   string         `json:"livePlatform"  binding:"required"`
	StartTime      int64          `json:"startTime"  binding:"required"`
	ThirdPartyName string         `json:"thirdPartyName"  binding:"required"`
	ThirdPartyId   string         `json:"thirdPartyId"  binding:"required"`
	RecentAnchors  []RecentAnchor `json:"recentAnchors" binding:"required"`
}

type LiveWorkBenchRequest struct {
	UserId         string `json:"userId"  binding:"required"`
	ThirdPartyName string `json:"thirdPartyName"`
	PageNo         int64  `json:"pageNo"`
	PageSize       int64  `json:"pageSize"`
}

type LiveWorkBenchResponse struct {
	Count             int64      `json:"count"`
	List              []UserList `json:"list"`
	Gmv               float64    `json:"gmv"`
	LiveBroadcastNum  int64      `json:"liveBroadcastNum"`  // 七日直播场次
	LiveDuration      int64      `json:"liveDuration"`      // 七日开播直播时长
	UniqueUsers       int64      `json:"uniqueUsers"`       // 七日观看数量
	AvgDurationOfStay int        `json:"avgDurationOfStay"` // 七日平均停留时长
	NewFans           int64      `json:"newFans"`           // 七日新增粉丝
	BulletChatNum     int64      `json:"bulletChatNum"`     // 七日弹幕数量
}

type UserList struct {
	UserId         string         `json:"userId"`
	ThirdPartyName string         `json:"thirdPartyName"`
	ThirdPartyId   string         `json:"thirdPartyId"`
	AccountType    string         `json:"accountType"`
	StartTime      int64          `json:"startTime"`
	LivePlatform   string         `json:"livePlatform"`
	Fans           int64          `json:"fans"`
	Gmv7d          float64        `json:"gmv7d"`
	RecentAnchors  []RecentAnchor `json:"recentAnchors"`
}

type RecentAnchor struct {
	AnchorId     string `json:"anchorId" binding:"required"`
	AnchorName   string `json:"anchorName"`
	ThumbnailUrl string `json:"thumbnailUrl"`
}

type BroadcastDataByUserRequest struct {
	UserIdXiling      string             `json:"userIdXiling"`
	LivePlatformUsers []LivePlatformUser `json:"livePlatforms"`
}

type LivePlatformUser struct {
	LivePlatform string `json:"livePlatform"`
	ThirdPartyId string `json:"thirdPartyId"`
}

type BroadcastDataByUserResponse struct {
	Code int                                   `json:"code"`
	Msg  string                                `json:"msg"`
	Data map[string]BroadcastDataByUserGmv7Day `json:"data"`
}

type BroadcastDataByUserGmv7Day struct {
	LivePlatform      int     `json:"livePlatform"`
	ThirdPartyId      string  `json:"thirdPartyId"`
	Gmv               float64 `json:"gmv"`
	LiveBroadcastNum  int64   `json:"liveBroadcastNum"`  // 七日直播场次
	LiveDuration      int64   `json:"liveDuration"`      // 七日开播直播时长
	UniqueUsers       int64   `json:"uniqueUsers"`       // 七日观看数量
	AvgDurationOfStay int64   `json:"avgDurationOfStay"` // 七日平均停留时长
	NewFans           int64   `json:"newFans"`           // 七日新增粉丝
	BulletChatNum     int64   `json:"bulletChatNum"`     // 七日弹幕数量
}

type BroadcastDataByUserNameRequest struct {
	UserIdXiling string `json:"userIdXiling"`
}

type BroadcastDataByUserNameResponse struct {
	Code int    `json:"code"`
	Msg  string `json:"msg"`
	Data struct {
		Gmv float64 `json:"gmv"`
	} `json:"data"`
}

type PlatformLiveResponse struct {
	Code int              `json:"code"`
	Msg  string           `json:"msg"`
	Data PlatformLiveResp `json:"data"`
}

type PlatformLiveResp struct {
	DOUYIN   float64 `json:"DOU_YIN"`
	KUAISHOU float64 `json:"KUAI_SHOU"`
	MEITUAN  float64 `json:"MEI_TUAN"`
	TAOBAO   float64 `json:"TAO_BAO"`
	JINGDONG float64 `json:"JING_DONG"`
	All      float64 `json:"ALL"`
}

type UseThirdUserDataRequest struct {
	UserId string `json:"userId"  binding:"required"`

	//平台类型
	//DOU_YIN 抖音、
	//KUAI_SHOU 快手、
	//MEI_TUAN 美团、
	//TAO_BAO 淘宝、
	//JING_DONG 京东、
	//XI_LING 体验模式
	LivePlatform   string `json:"livePlatform"  binding:"required"`
	ThirdPartyName string `json:"thirdPartyName"  binding:"required"`
	ThirdPartyId   string `json:"thirdPartyId"  binding:"required"`
	AccountType    string `json:"accountType"`
	Os             string `json:"os"`
}

type UseThirdUserRequest struct {
	UserId string `json:"userId"  binding:"required"`
	Os     string `json:"os"`
}

type WsLiveDataRequest struct {
	ActionType string      `json:"actionType"  binding:"required"`
	Action     interface{} `json:"action"  binding:"required"`
	RequestId  string      `json:"requestId"  binding:"required"`
}

type LiveBroadcastData struct {
	UserId         string         `json:"userId"  binding:"required"`
	LivePlatform   string         `json:"livePlatform"  binding:"required"`
	ThirdPartyName string         `json:"thirdPartyName"`
	ThirdPartyId   string         `json:"thirdPartyId"  binding:"required"`
	AccountType    string         `json:"accountType"`
	Os             string         `json:"os"`
	StartTime      int64          `json:"startTime"`
	EndTime        int64          `json:"endTime"`
	LiveId         string         `json:"liveId"`
	SessionId      string         `json:"sessionId"`
	RecentAnchors  []RecentAnchor `json:"recentAnchors"`
}

type UserLiveData struct {
	UserIdXiling   string `json:"userIdXiling"`
	XilingLiveId   string `json:"xilingLiveId"`
	LivePlatform   string `json:"livePlatform"`
	ThirdPartyName string `json:"thirdPartyName"`
	ThirdPartyId   string `json:"thirdPartyId"`
	StartTime      int64  `json:"startTime"`
	EndTime        int64  `json:"endTime"`
}

type StatisticsReq struct {
	LivePlatform string `json:"livePlatform"`
	UserIdXiling string `json:"userIdXiling"`
	UserType     string `json:"userType"`
	StartTime    int64  `json:"startTime"`
	EndTime      int64  `json:"endTime"`
}

type PlatformLiveReq struct {
	StartTime int64 `json:"startTime" binding:"required"`
	EndTime   int64 `json:"endTime" binding:"required"`
}
