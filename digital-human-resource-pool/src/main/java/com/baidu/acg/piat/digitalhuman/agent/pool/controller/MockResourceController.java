package com.baidu.acg.piat.digitalhuman.agent.pool.controller;

import com.baidu.acg.piat.digitalhuman.common.webrtc.resource.pool.model.AcquireRequest;
import com.baidu.acg.piat.digitalhuman.common.webrtc.resource.pool.model.AcquireResponse;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * Mock the response from different web rtc resources.
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/digitalhuman")
public class MockResourceController {

    @Value("${digitalhuman.pool.configs.mock.ue4-url:test}")
    private String ue4Url = "test";

    @PostMapping("/agent/acquire")
    public AcquireResponse agentAcquire(@RequestBody AcquireRequest request) {
        var res = new AcquireResponse();
        res.setCode(0);
        res.setMessage("OK");
        return res;
    }

    @PostMapping("/proxy/acquire")
    public AcquireResponse proxyAcquire(@RequestBody AcquireRequest request) {
        var res = new AcquireResponse();
        res.setCode(0);
        res.setMessage("OK");
        return res;
    }

    @GetMapping("/ue4/acquire")
    public String ue4Acquire() {
        return ue4Url;
    }
}
