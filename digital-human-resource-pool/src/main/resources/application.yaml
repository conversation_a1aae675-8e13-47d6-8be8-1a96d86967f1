server:
  port: 8580

logging:
  level:
    com.baidu.acg.piat.digitalhuman: DEBUG

digitalhuman:
  plat:
    config:
      baseUrl: http://localhost:8480/
  zookeeper:
    config:
      namespace: digital-human-cloud
      url: localhost:2181
      baseSleepTimeMs: 1000
      maxRetry: 3
  hypervisor:
    client:
      callTimeoutMillis: 1500
      acquireApiPath: /api/digitalhuman/hypervisor/resource/acquire
      releaseApiPath: /api/digitalhuman/hypervisor/resource/release
      startApiPath: /api/digitalhuman/hypervisor/process/start
      stopApiPath: /api/digitalhuman/hypervisor/process/stop
  pool:
    configs:
      mock:
        ue4Url: dev-ue4-url
      instance:
        expireInSecond: 20
      client:
        connectTimeoutMills: 500
        readTimeoutMills: 500
      pool:
        acquireRetry: 1
        acquireTimeoutSeconds: 4
        acquireApi: /api/digitalhuman/render/proxy/vis2d/resource/acquire
        executorCoreSize: 10
        executorMaxSize: 10
        executorKeepAliveSeconds: 60
        executorWorkQueueSize: 2147483647
