package dao

import (
	"acg-ai-go-common/global"
	"acg-ai-go-common/logger"
	"database/sql/driver"
	"dhlive-saas-alert/config"
	"dhlive-saas-alert/constant"
	"encoding/json"
	"errors"
	"fmt"
	"sync"
	"time"

	"gorm.io/driver/mysql"
	"gorm.io/gorm"
)

var dbMap = sync.Map{}

func InitMysql() error {
	for name, dsn := range config.AppConfig.DSN {
		db, err := gorm.Open(mysql.Open(dsn), &gorm.Config{})
		logger.Log.Debugf("初始化数据库[%s]start,dsn=%s", name, dsn)

		if err != nil || db == nil {
			logger.Log.Errorf("connect to db=%s,dsn=%s, error:%v", name, dsn, err)
			return fmt.Errorf("无法从平台服务获取数据库连接,db=%s , dsn=%s, error=%v", name, dsn, err)
		}
		conn, err := db.DB()
		if err != nil {
			logger.Log.Error(err)
			return fmt.Errorf("连接数据库失败,db=%s , error=%v", name, err)
		}

		conn.SetMaxIdleConns(global.ServerSetting.MysqlSetting.MaxIdleConns)
		conn.SetMaxOpenConns(global.ServerSetting.MysqlSetting.MaxOpenConns)
		// SetConnMaxLifetime 设置连接可复用的最长时间。
		conn.SetConnMaxLifetime(time.Hour)

		setDB(name, db)
		logger.Log.Infof("初始化数据库[%s]成功", name)
	}
	logger.Log.Infof("初始化数据库,MysqlSetting.MaxIdleConns:%d,MysqlSetting.MaxOpenConns:%d", global.ServerSetting.MysqlSetting.MaxIdleConns, global.ServerSetting.MysqlSetting.MaxOpenConns)
	return nil
}

func setDB(dbName string, db *gorm.DB) {
	dbMap.Store(dbName, db)
}

func GetDB(dbName string) (gormDB *gorm.DB, ok bool) {
	db, ok := dbMap.Load(dbName)
	logger.Log.Infof("GetDB,dbName=%s,db=%v,ok=%v", dbName, db, ok)
	return db.(*gorm.DB), ok
}

func GetDefultDB() (gormDB *gorm.DB, ok bool) {
	return GetDB(constant.DBVideoProduct)
}
func GetDBVideoProductWrite() (gormDB *gorm.DB, ok bool) {
	return GetDB(constant.DBVideoProductWrite)
}

func GetText2FigureDB() (gormDB *gorm.DB, ok bool) {
	return GetDB(constant.DBText2Figure)
}

func GetPPT2ImageDB() (gormDB *gorm.DB, ok bool) {
	return GetDB(constant.DBppt2Image)
}

func GetRiskControlDB() (gormDB *gorm.DB, ok bool) {
	return GetDB(constant.DBppt2Image)
}

func GetVideoPipeLineDB() (gormDB *gorm.DB, ok bool) {
	return GetDB(constant.DBVideoPipeLine)
}

func GetTtsCloneDB() (gormDB *gorm.DB, ok bool) {
	return GetDB(constant.DBTtsClone)
}

func GetStarLightDB() (gormDB *gorm.DB, ok bool) {
	return GetDB(constant.DBStarLight)
}

func GetPlatDB() (gormDB *gorm.DB, ok bool) {
	return GetDB(constant.BDPlat)
}
func GetDhUserDb() (gormDB *gorm.DB, ok bool) {
	return GetDB(constant.DHUserAndAccount)
}

type JSONMap map[string]interface{}

// Value 方法实现 driver.Valuer 接口，用于将 JSONMap 转换为 JSON 字符串存储在数据库中。
func (jm JSONMap) Value() (driver.Value, error) {
	if jm == nil {
		return nil, nil
	}
	b, err := json.Marshal(jm)
	if err != nil {
		return nil, err
	}
	return string(b), nil
}

// Scan 方法实现 sql.Scanner 接口，用于将数据库中的 JSON 字符串转换回 JSONMap。
func (jm *JSONMap) Scan(input interface{}) error {
	if input == nil {
		return nil
	}
	bytes, ok := input.([]byte)
	if !ok {
		return errors.New("type assertion to []byte failed")
	}
	return json.Unmarshal(bytes, jm)
}
