package dao

import (
	"acg-ai-go-common/logger"
	"fmt"
)

type DhUser struct {
	ID       int    `gorm:"primaryKey;autoIncrement;column:id;comment:主键" json:"id"`
	UserID   string `gorm:"type:varchar(64);column:user_id" json:"userId"`
	UserName string `gorm:"type:varchar(128);column:user_name" json:"userName"`
	IsDelete int    `gorm:"type:varchar(128);column:is_delete" json:"isDelete"`
}

// 用户信息表
func (p *DhUser) TableName() string {
	return "dh_user"
}

// 根据userName查询对应的用户信息
func (p *DhUser) QueryUserIdByName(logId string, userName string) ([]DhUser, error) {
	var users []DhUser
	db, ok := GetDhUserDb()
	if !ok {
		logger.Log.Errorf("logId=%s,加载数据库，%v,失败", logId, p.<PERSON>())
		return nil, fmt.Errorf("加载数据库，%v,失败", p.TableName())
	}
	logger.Log.Infof("logId=%s,start qurey db,table:%v,userName:%s", logId, p.TableName(), userName)
	err := db.Where("user_name =? and is_delete = 0 ", userName).Find(&users).Error
	if err != nil {
		logger.Log.Errorf("logId=%s,查询数据库，%v,失败,error:%v", logId, p.TableName(), err)
		return nil, fmt.Errorf("查询数据库，%v,失败,error:%v", p.TableName(), err)
	}
	logger.Log.Infof("logId=%s,查询数据库成功,%v,users:%+v", logId, p.TableName(), users)
	return users, nil
}
