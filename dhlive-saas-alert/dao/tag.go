package dao

import (
	"acg-ai-go-common/logger"
	"encoding/json"
	"fmt"
	"strings"

	"gorm.io/gorm"
)

// 语音标签

type Tag struct {
	ID       int    `json:"id" gorm:"primaryKey;autoIncrement;column:id"`
	Tag      string `json:"tag" gorm:"column:tag;type:varchar(64);not null;default:''"`
	Describe string `json:"describe" gorm:"column:describe;type:varchar(128);not null"`       // tag内容
	Type     int    `json:"type" gorm:"column:type;type:tinyint(1);not null;default:0"`       // 0:性别，1:年龄，2:风格，3:场景，4:语言
	Module   int    `json:"module" gorm:"column:module;type:tinyint(4);not null;default:0"`   // 0:语音,1:人像
	Visible  int    `json:"visible" gorm:"column:visible;type:tinyint(4);not null;default:1"` // 前端是否可见，0:不可见,1:可见
}

func (p *Tag) TableName() string {
	return "tag"
}

func (p *Tag) Create(gormDB *gorm.DB) error {
	if gormDB == nil {
		return fmt.Errorf("gormDB is nil")
	}
	err := gormDB.Create(p).Error
	return err
}

func (p *Tag) CreateBatch(gormDB *gorm.DB, tags []Tag) error {
	if gormDB == nil {
		return fmt.Errorf("gormDB is nil")
	}
	err := gormDB.Create(&tags).Error
	return err
}

func GetTagList() {

	tagStr := "ARA,BUL,HRV,CS,DAN,NL,EN,EN,EN,FIL,FIN,FRN,EL,HI,HU,ID,IT,MAY,NOR,PL,POT,PT,ROM,SK,SPA,SPA,SWE,TAM,TR,UKR,VIE"
	describeStr := "Arabic (阿拉伯),Bulgarian(保加利亚语),croatian(克罗地亚语),捷克语,丹麦语,荷兰语,英语（美）,英语（澳）,英语 （加拿大）,菲律宾语,芬兰语,法语 （加拿大）,希腊语,印地语,匈牙利语,印度尼西亚语,意大利语,马来语,挪威语,波兰语,葡萄牙语（巴西）,葡萄牙语（葡萄牙）,罗马尼亚语,斯洛伐克语,西班牙语,西班牙语 （墨西哥）,瑞典语,泰米尔语,土耳其语,乌克兰语,越南语"

	tagArray := strings.Split(tagStr, ",")
	describeArray := strings.Split(describeStr, ",")

	tagSize := len(tagArray)
	describeSize := len(describeArray)

	if tagSize != describeSize {
		logger.Log.Errorf("tagArray.len != describeArray.len, tagArray.len: %d, describeArray.len: %d", tagSize, describeSize)
		return
	}
	tags := make([]Tag, 0)
	for idx, tag := range tagArray {
		item := Tag{
			Tag:      tag,
			Describe: describeArray[idx],
			Type:     4,
			Module:   0,
			Visible:  1,
		}
		tags = append(tags, item)
	}
	tagsJson, err := json.Marshal(tags)
	if err != nil {
		logger.Log.Errorf("json.Marshal error: %s", err.Error())
		return
	}
	logger.Log.Infof("tags: %+v", string(tagsJson))
}

func UpdateTags(tags []Tag) {

	gormDB, ok := GetTtsCloneDB()
	if !ok {
		logger.Log.Errorf("GetTtsCloneDB error")
		return
	}

	err := (&Tag{}).CreateBatch(gormDB, tags)

	logger.Log.Infof("UpdateTags err: %s", err.Error())
}
