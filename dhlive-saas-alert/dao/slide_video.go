package dao

import (
	"acg-ai-go-common/logger"
	"acg-ai-go-common/utils"
	"dhlive-saas-alert/config"
	"fmt"
	"time"
)

type SlideVideo struct {
	ID           int64     `gorm:"primaryKey;column:id;autoIncrement" json:"id"`
	VideoID      string    `gorm:"type:varchar(32);comment:视频ID" json:"videoId"`
	TaskID       string    `gorm:"type:varchar(32);comment:任务ID" json:"taskId"`
	Name         string    `gorm:"type:varchar(128);" json:"name"`
	DraftID      string    `gorm:"type:varchar(32);comment:草稿ID" json:"draftId"`
	UserID       string    `gorm:"type:varchar(128);" json:"userId"`
	LastUpdateBy string    `gorm:"type:varchar(128);" json:"lastUpdateBy"`
	VideoURL     string    `gorm:"type:varchar(512);comment:视频URL" json:"videoUrl"`
	AudioUrl     string    `gorm:"type:varchar(512);comment:音频URL" json:"audioUrl"`
	PreviewURL   string    `gorm:"type:varchar(512);comment:视频预览URL" json:"previewUrl"`
	Duration     int64     `gorm:"type:int(11);" json:"duration"`
	Size         int       `gorm:"type:int(11);" json:"size"`
	Width        int       `gorm:"type:int(11);" json:"width"`
	Height       int       `gorm:"type:int(11);" json:"height"`
	IsDelete     bool      `gorm:"type:tinyint(4)" json:"isDelete"`
	Status       string    `gorm:"type:varchar(128);comment:状态" json:"status"`
	Thumbnail    string    `gorm:"type:varchar(1024);comment:缩略图" json:"thumbnail"`
	CreateTime   time.Time `gorm:"type:dateTime" json:"createTime"`
	UpdateTime   time.Time `gorm:"type:dateTime" json:"updateTime"`

	// 一些查询需要的json信息，数据库中没有
	VpId     string      `json:"vpId,omitempty" gorm:"-"`
	VpTask   interface{} `json:"vpTask,omitempty" gorm:"-"`
	EditId   string      `json:"editId,omitempty" gorm:"-"`
	EditTask interface{} `json:"edtTask,omitempty" gorm:"-"`
}

func (p *SlideVideo) TableName() string {
	return "slide_video"
}

// 夯住
func (p *SlideVideo) FindByTimeDifferenceAndStatusAndNotDeleted(logId string) ([]*SlideVideo, error) {
	var slideVideos []*SlideVideo
	now := time.Now()
	// slide服务时间晚了8个小时，所以需要向前推8小时
	firstTimeAgo := now.Add(-time.Duration(config.AppConfig.VideoHangingTime.FirstTimeHour+8) * time.Hour).Format("2006-01-02 15:04:05")
	lastTimeAgo := now.Add(-time.Duration(config.AppConfig.VideoHangingTime.LastTimeHour+8) * time.Hour).Format("2006-01-02 15:04:05")
	db, ok := GetDefultDB()
	if !ok {
		logger.Log.Errorf("logId=%s,加载数据库，%v,失败", logId, p.TableName())
		return nil, fmt.Errorf("加载数据库，%v,失败", p.TableName())
	}
	logger.Log.Infof("logId=%s,start qurey db,table:%v,firstTime=%v,lastTime=%v", logId, p.TableName(), firstTimeAgo, lastTimeAgo)
	err := db.Where("create_time  BETWEEN ? AND ? AND status IN (?) AND is_delete= ?", lastTimeAgo, firstTimeAgo, []string{"SUBMITTED", "GENERATING"}, false).Find(&slideVideos).Error
	if err != nil {
		logger.Log.Errorf("logId=%s,查询数据库，%v,失败,error:%v", logId, p.TableName(), err)
		return nil, fmt.Errorf("查询数据库，%v,失败,error:%v", p.TableName(), err)
	}
	logger.Log.Infof("logId=%s,查询数据库成功,%v,videoSize:%d", logId, p.TableName(), len(slideVideos))
	return slideVideos, nil
}

// 失败
func (p *SlideVideo) FindByTimeDifferenceAndStatus(logId string, status string, minute int) ([]*SlideVideo, error) {
	var slideVideos []*SlideVideo
	now := time.Now()

	// slide服务时间晚了8个小时，所以需要向前推480分钟
	firstTimeAgo := now.Add(-time.Duration(minute+480) * time.Minute).Format("2006-01-02 15:04:05")
	lastTimeAgo := now.Add(-time.Duration(config.AppConfig.VideoFailedTime.VideoFailedTimeDuration+480) * time.Minute).Format("2006-01-02 15:04:05")

	db, ok := GetDefultDB()
	if !ok {
		logger.Log.Errorf("logId=%s,加载数据库，%v,失败", logId, p.TableName())
		return nil, fmt.Errorf("加载数据库，%v,失败", p.TableName())
	}
	logger.Log.Infof("logId=%s,start qurey db,table:%v,firstTimeAgo=%v,lastTimeAgo=%v,status=%s", logId, p.TableName(), firstTimeAgo, lastTimeAgo, status)

	// 查询前一分钟到前15分钟之间失败的数据
	err := db.Where("update_time BETWEEN ? AND ? AND  status=? AND is_delete=?", lastTimeAgo, firstTimeAgo, status, 0).Find(&slideVideos).Error
	if err != nil {
		logger.Log.Errorf("logId=%s,查询数据库，%v,失败,error:%v", logId, p.TableName(), err)
		return nil, fmt.Errorf("查询数据库，%v,失败,error:%v", p.TableName(), err)
	}
	logger.Log.Infof("logId=%s,查询数据库成功,%v,videoSize:%d", logId, p.TableName(), len(slideVideos))
	return slideVideos, nil
}

func (p *SlideVideo) FindModelByVideoId(videoId string) ([]*SlideVideo, error) {
	var slideVideos []*SlideVideo
	db, ok := GetDefultDB()
	if !ok {
		logger.Log.Errorf("logId=%s,加载数据库，%v,失败", videoId, p.TableName())
		return nil, fmt.Errorf("加载数据库，%v,失败", p.TableName())
	}
	logger.Log.Infof("logId=%s,start qurey db,table:%v,videoId:%s", videoId, p.TableName(), videoId)

	err := db.Where("video_id = ?", videoId).Find(&slideVideos).Error
	if err != nil {
		logger.Log.Errorf("logId=%s,查询数据库，%v,失败,error:%v", videoId, p.TableName(), err)
		return nil, fmt.Errorf("查询数据库，%v,失败,error:%v", p.TableName(), err)
	}
	logger.Log.Infof("logId=%s,查询数据库成功,%v,videoSize:%d", videoId, p.TableName(), len(slideVideos))
	return slideVideos, nil
}

func (p *SlideVideo) FindModelByUserId(userId string) ([]*SlideVideo, error) {
	var slideVideos []*SlideVideo
	db, ok := GetDefultDB()
	if !ok {
		return nil, fmt.Errorf("get mysql %v failed", p.TableName())
	}
	err := db.Where("user_id = ?", userId).Find(&slideVideos).Error
	if err != nil {
		return nil, fmt.Errorf("find by userId in %v failed, error:%v", p.TableName(), err)
	}
	return slideVideos, nil
}

func (p *SlideVideo) FindModelByKeyword(keyword string) ([]*SlideVideo, error) {
	var slideVideos []*SlideVideo
	db, ok := GetDefultDB()
	if !ok {
		return nil, fmt.Errorf("get mysql %v failed", p.TableName())
	}
	err := db.Where("name LIKE ?", "%"+keyword+"%").Find(&slideVideos).Error
	if err != nil {
		return nil, fmt.Errorf("find by keyword in %v failed, error:%v", p.TableName(), err)
	}
	return slideVideos, nil
}

// 查询一段时间内的成功的且未删除的视频
func (p *SlideVideo) QuerySuccessVideoInDuration(logId string) ([]*SlideVideo, error) {
	var slideVideos []*SlideVideo
	timeNow := time.Now()
	limitTime := timeNow.Add(-time.Duration(config.AppConfig.QuerySuccessDuration.SuccessDurationMinute) * time.Minute).Format("2006-01-02 15:04:05")
	db, ok := GetDefultDB()
	if !ok {
		logger.Log.Errorf("logId=%s,加载数据库，%v,失败", logId, p.TableName())
		return nil, fmt.Errorf("加载数据库，%v,失败", p.TableName())
	}
	logger.Log.Infof("logId=%s,start qurey db,table:%v,timeNow=%v,limitTime=%v", logId, p.TableName(), timeNow, limitTime)
	err := db.Where("update_time  BETWEEN ? AND ? AND status = ? AND is_delete= ?", limitTime, timeNow, "SUCCEEDED", false).Find(&slideVideos).Error
	if err != nil {
		logger.Log.Errorf("logId=%s,查询数据库，%v,失败,error:%v", logId, p.TableName(), err)
		return nil, fmt.Errorf("查询数据库，%v,失败,error:%v", p.TableName(), err)
	}
	logger.Log.Infof("logId=%s,查询数据库成功,%v,videoSize:%d", logId, p.TableName(), len(slideVideos))
	return slideVideos, nil
}

func (p *SlideVideo) Search(userID, status, startTime, endTime string, pageNum, pageSize int64) (int64, []*SlideVideo, error) {
	var total int64
	var videos []*SlideVideo
	db, ok := GetDefultDB()
	if !ok {
		return 0, nil, fmt.Errorf("加载数据库-表[%v]失败", p.TableName())
	}
	if userID != "" {
		db = db.Where("user_id = ?", userID)
	}
	if status != "" {
		db = db.Where("status = ?", status)
	}
	if startTime != "" && endTime != "" {
		db = db.Where("create_time BETWEEN ? AND ?", startTime, endTime)
	}
	err := db.Model(&SlideVideo{}).Count(&total).Error
	if err != nil {
		return 0, nil, fmt.Errorf("查询数据库-表[%v]失败,error:%v", p.TableName(), err)
	}

	skip, limit := utils.ConvertPageNumSize2Skip(pageNum, pageSize)
	err = db.Offset(int(skip)).Limit(int(limit)).Find(&videos).Error
	if err != nil {
		return 0, nil, fmt.Errorf("查询数据库-表[%v]失败,error:%v", p.TableName(), err)
	}
	return total, videos, nil
}

// 查询7天之内 每天的数据量
func (p *SlideVideo) FindSlideInSevenDays(logId string, status string, startTime time.Time) ([]config.DailyTotal, error) {
	db, ok := GetDefultDB()
	if !ok {
		logger.Log.Errorf("logId=%s,加载数据库，%v,失败", logId, p.TableName())
		return nil, fmt.Errorf("加载数据库，%v,失败", p.TableName())
	}
	logger.Log.Infof("logId=%s,SlideVideo start qurey db,table:%v,status=%v,startTime=%v", logId, p.TableName(), status, startTime)

	dailyTotals := make([]config.DailyTotal, 0)

	// 查询7天内全部数据
	if status == "" {
		err := db.Model(&SlideVideo{}).Where("create_time >= ?", startTime).
			Select("DATE(create_time) as date, COUNT(*) as count").
			Group("DATE(create_time)").
			Order("date ASC").
			Scan(&dailyTotals).Error
		if err != nil {
			logger.Log.Errorf("logId=%s,SlideVideo fetching daily totals error:%v", logId, err)
			return nil, fmt.Errorf("fetching daily totals error:%v", err)
		}
	} else {
		// 查询7天内全部失败数据
		err := db.Model(&SlideVideo{}).Where("status = ? AND create_time >= ?", status, startTime).
			Select("DATE(create_time) as date, COUNT(*) as count").
			Group("DATE(create_time)").
			Order("date ASC").
			Scan(&dailyTotals).Error
		if err != nil {
			logger.Log.Errorf("logId=%s,SlideVideo fetching daily failed totals error:%v", logId, err)
			return nil, fmt.Errorf("fetching daily failed totals error:%v", err)
		}
	}

	logger.Log.Infof("logId=%s,查询数据库成功,%v,status=%v,data:%+v", logId, p.TableName(), status, dailyTotals)
	return dailyTotals, nil
}
