package dao

import (
	"acg-ai-go-common/logger"
	"dhlive-saas-alert/config"
	"fmt"
	"time"
)

type TwinVideo struct {
	ID           int64     `gorm:"primaryKey;column:id;autoIncrement" json:"id"`
	VideoID      string    `gorm:"type:varchar(32);comment:视频ID" json:"videoId"`
	TaskID       string    `gorm:"type:varchar(32);comment:任务ID" json:"taskId"`
	DraftID      string    `gorm:"type:varchar(32);comment:草稿ID" json:"draftId"`
	VideoURL     string    `gorm:"type:varchar(512);comment:视频URL" json:"videoUrl"`
	PreviewURL   string    `gorm:"type:varchar(512);comment:视频预览URL" json:"previewUrl"`
	Status       string    `gorm:"type:varchar(128);comment:状态" json:"status"`
	Thumbnail    string    `gorm:"type:varchar(1024);comment:缩略图" json:"thumbnail"`
	SpeakType    string    `gorm:"type:varchar(128);comment:视频ID" json:"speakType"`
	Name         string    `gorm:"type:varchar(128);" json:"name"`
	UserID       string    `gorm:"type:varchar(128);" json:"userId"`
	Duration     int       `gorm:"type:int(11);" json:"duration"`
	Width        int       `gorm:"type:int(11);" json:"width"`
	Height       int       `gorm:"type:int(11);" json:"height"`
	AudioUrl     string    `gorm:"type:varchar(512);comment:音频URL" json:"audioUrl"`
	IsDelete     bool      `gorm:"type:tinyint(4)" json:"isDelete"`
	Reviewed     bool      `gorm:"type:tinyint(4)" json:"reviewed"`
	LastUpdateBy string    `gorm:"type:varchar(128);" json:"lastUpdateBy"`
	CreateTime   time.Time `gorm:"type:timestamp" json:"createTime"`
	UpdateTime   time.Time `gorm:"type:timestamp" json:"updateTime"`
}

func (p *TwinVideo) TableName() string {
	return "twin_video"
}

// 夯住
func (p *TwinVideo) FindByTimeDifferenceAndStatusAndNotDeleted(logId string) ([]*TwinVideo, error) {
	var twinVideos []*TwinVideo
	now := time.Now()
	firstTimeAgo := now.Add(-time.Duration(config.AppConfig.VideoHangingTime.FirstTimeHour) * time.Hour).Format("2006-01-02 15:04:05")
	lastTimeAgo := now.Add(-time.Duration(config.AppConfig.VideoHangingTime.LastTimeHour) * time.Hour).Format("2006-01-02 15:04:05")
	db, ok := GetDefultDB()
	if !ok {
		logger.Log.Errorf("logId:%s,加载数据库，%v,失败", logId, p.TableName())
		return nil, fmt.Errorf("加载数据库，%v,失败", p.TableName())
	}
	logger.Log.Infof("logId:%s,start qurey db,table:%v,firstTime=%v,lastTime=%v", logId, p.TableName(), firstTimeAgo, lastTimeAgo)
	err := db.Where("create_time  BETWEEN ? AND ? AND status IN (?) AND is_delete= ?", lastTimeAgo, firstTimeAgo, []string{"SUBMITTED", "GENERATING"}, false).Find(&twinVideos).Error
	if err != nil {
		logger.Log.Errorf("logId:%s,查询数据库，%v,失败,error:%v", logId, p.TableName(), err)
		return nil, fmt.Errorf("查询数据库，%v,失败,error:%v", p.TableName(), err)
	}
	logger.Log.Infof("logId:%s,查询数据库成功,%v,videoSize:%d", logId, p.TableName(), len(twinVideos))
	return twinVideos, nil
}

// 失败
func (p *TwinVideo) FindByTimeDifferenceAndStatus(logId string, status string, minute int) ([]*TwinVideo, error) {
	var twinVideos []*TwinVideo
	now := time.Now()
	firstTimeAgo := now.Add(-time.Duration(minute) * time.Minute).Format("2006-01-02 15:04:05")
	lastTimeAgo := now.Add(-time.Duration(config.AppConfig.VideoFailedTime.VideoFailedTimeDuration) * time.Minute).Format("2006-01-02 15:04:05")
	db, ok := GetDefultDB()
	if !ok {
		logger.Log.Errorf("logId:%s,加载数据库，%v,失败", logId, p.TableName())
		return nil, fmt.Errorf("加载数据库，%v,失败", p.TableName())
	}
	logger.Log.Infof("logId:%s,start qurey db,table:%v,firstTimeAgo=%v,lastTimeAgo=%v,status=%s", logId, p.TableName(), firstTimeAgo, lastTimeAgo, status)

	// 查询前一分钟到前两个小时之间失败的数据
	err := db.Where("update_time BETWEEN ? AND ? AND  status=? AND is_delete=?", lastTimeAgo, firstTimeAgo, status, 0).Find(&twinVideos).Error

	if err != nil {
		logger.Log.Errorf("logId:%s,查询数据库，%v,失败,error:%v", logId, p.TableName(), err)
		return nil, fmt.Errorf("查询数据库，%v,失败,error:%v", p.TableName(), err)
	}
	logger.Log.Infof("logId:%s,查询数据库成功,%v,videoSize:%d", logId, p.TableName(), len(twinVideos))
	return twinVideos, nil
}

func (p *TwinVideo) FindModelByVideoId(videoId string) ([]*TwinVideo, error) {
	var twinVideos []*TwinVideo
	db, ok := GetDefultDB()
	if !ok {
		logger.Log.Errorf("logId:%s,加载数据库，%v,失败", videoId, p.TableName())
		return nil, fmt.Errorf("加载数据库，%v,失败", p.TableName())
	}
	logger.Log.Infof("logId:%s,start qurey db,table:%v,videoId:%s", videoId, p.TableName(), videoId)

	err := db.Where("video_id = ?", videoId).Find(&twinVideos).Error
	if err != nil {
		logger.Log.Errorf("logId:%s,查询数据库，%v,失败,error:%v", videoId, p.TableName(), err)
		return nil, fmt.Errorf("查询数据库，%v,失败,error:%v", p.TableName(), err)
	}
	logger.Log.Infof("logId:%s,查询数据库成功,%v,videoSize:%d", videoId, p.TableName(), len(twinVideos))
	return twinVideos, nil
}

// 查询一段时间内的成功的且未删除的视频
func (p *TwinVideo) QuerySuccessVideoInDuration(logId string) ([]*TwinVideo, error) {
	var twinVideos []*TwinVideo
	timeNow := time.Now()
	limitTime := timeNow.Add(-time.Duration(config.AppConfig.QuerySuccessDuration.SuccessDurationMinute) * time.Minute).Format("2006-01-02 15:04:05")
	db, ok := GetDefultDB()
	if !ok {
		logger.Log.Errorf("logId=%s,加载数据库，%v,失败", logId, p.TableName())
		return nil, fmt.Errorf("加载数据库，%v,失败", p.TableName())
	}
	logger.Log.Infof("logId=%s,start qurey db,table:%v,timeNow=%v,limitTime=%v", logId, p.TableName(), timeNow, limitTime)
	err := db.Where("update_time  BETWEEN ? AND ? AND status = ? AND is_delete= ?", limitTime, timeNow, "SUCCEEDED", false).Find(&twinVideos).Error
	if err != nil {
		logger.Log.Errorf("logId=%s,查询数据库，%v,失败,error:%v", logId, p.TableName(), err)
		return nil, fmt.Errorf("查询数据库，%v,失败,error:%v", p.TableName(), err)
	}
	logger.Log.Infof("logId=%s,查询数据库成功,%v,videoSize:%d", logId, p.TableName(), len(twinVideos))
	return twinVideos, nil
}
