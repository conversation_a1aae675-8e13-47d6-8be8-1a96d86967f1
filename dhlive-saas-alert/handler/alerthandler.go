package handler

import (
	"acg-ai-go-common/logger"

	"github.com/robfig/cron/v3"
)

// 初始化定时任务
func InitScheduleTask() {
	// 新建一个定时任务对象
	cron := cron.New(cron.WithSeconds())
	_, err := cron.AddFunc("@every 10m", TaskPer10min)
	if err != nil {
		logger.Log.Errorf("InitScheduleTaskPer10min,cron.AddFunc,error:%v", err)
		panic(err)
	}

	_, errone := cron.AddFunc("@every 30m", TaskPer30min)
	if errone != nil {
		logger.Log.Errorf("InitScheduleTaskPer30min,cron.AddFunc,error:%v", errone)
		panic(errone)
	}

	_, errtwo := cron.AddFunc("@every 5m", TaskPer5min)
	if errtwo != nil {
		logger.Log.Errorf("InitScheduleTaskPer5min,cron.AddFunc,error:%v", errtwo)
		panic(errtwo)
	}

	_, errThree := cron.AddFunc("@every 3m", TaskPer3min)
	if errThree != nil {
		logger.Log.Errorf("InitScheduleTaskPer3min,cron.AddFunc,error:%v", errThree)
		panic(errThree)
	}

	// 测试用
	// _, errTest := cron.AddFunc("@every 5s", TestTask)
	// if errTest != nil {
	// 	logger.Log.Errorf("InitScheduleTaskPer20s,cron.AddFunc,error:%v", errTest)
	// 	panic(errTest)
	// }

	// 启动定时器
	cron.Start()

}

func TestTask() {
}

// 3分钟定时任务
func TaskPer3min() {
	// 文生3D任务报警
	Text2FigureTaskPer3min()
	// 风控任务失败报警
	RiskControlFailedTask()
	// 风控不合规报警
	RiskControlNonCompliance()
}

// 5分钟定时任务
func TaskPer5min() {
	// 视频生产监控报警
	VideoAlertTaskPer5min()
	// 文生3D的机器报警
	Text2FigureTaskPer5min()
}

// 10分钟定时任务
func TaskPer10min() {
	// 视频生产监控报警
	VideoAlertTaskPer10min()
	// 文生3D任务的报警
	Text2FigureTaskPer10min()
	// PPT课件任务的报警
	PPTToImageMonitorPer10min()
	// 声音克隆任务的报警
	TimbreCloneMonitorPer10min()
	// 人像训练任务报警
	FigureTrainAlertTaskPer10min()
	// cloudSession错误数量报警
	CloudSaasTaskPer10min()
	// Es日志监控报警
	// EsLogMonitorPer10min()
}

// 30分钟定时任务
func TaskPer30min() {
	// 声音ID可用数量报警
	HsSpeakersNotEnoughAleart()
	// 人像训练任务报警
	FigureTrainAlertTaskPer30min()
}
