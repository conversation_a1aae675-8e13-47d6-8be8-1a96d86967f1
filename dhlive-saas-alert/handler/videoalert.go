package handler

import (
	"acg-ai-go-common/logger"
	"dhlive-saas-alert/config"
	"dhlive-saas-alert/dao"
	"dhlive-saas-alert/util"
	"encoding/json"
	"fmt"
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm/utils"
)

var hangingEcardVideoSet []string
var hangingSlideVideoSet []string
var hangingTwinVideoSet []string
var hangingFissionVideoSet []string
var hangingCoursewareVideoSet []string
var failedEcardVideoSet []string
var failedSlideVideoSet []string
var failedTwinVideoSet []string
var failedTwinTemplateSet []string
var failedFissionVideoSet []string
var failedCoursewareVideoSet []string

var hangingVideoTranslateRecordingArray []string
var failedVideoTranslateRecordingArray []string

type VideoHangingSendMsg struct {
	VideoId    string    `json:"videoId"`
	VideoName  string    `json:"videoName"`
	CreateTime time.Time `json:"createTime"`
}

type FailedSendMsg struct {
	VideoId      string `json:"videoId"`
	VideoName    string `json:"videoName"`
	LastUpdateBy string `json:"lastUpdateBy"`
}

type TwinTemplateFailedMsg struct {
	TemplateId   string `json:"TemplateId"`
	VideoName    string `json:"videoName"`
	LastUpdateBy string `json:"lastUpdateBy"`
}

// 夯住视频报警定时任务，每10分钟执行一次
func VideoAlertTaskPer10min() {
	go monitorEcardVideoHanging()
	go monitorSlideVideoHanging()
	go monitorTwinVideoHanging()
	go monitorFissionVideoHanging()
	go monitorCoursewareVideoHanging()

	go MonitorVideoTranslateHanging()
	go MonitorVideoTranslateFailed()
}

// 失败视频报警定时任务，每5分钟执行一次
func VideoAlertTaskPer5min() {
	go monitorEcardVideoFailed()
	go monitorSlideVideoFailed()
	go monitorTwinVideoFailed()
	go monitorTwinTemplateFailed()
	go monitorFissionVideoFailed()
	go monitorCoursewareVideoFailed()
}

/*
AI卡片视频夯住监控
*/
func monitorEcardVideoHanging() {
	logId := uuid.New().String()
	logger.Log.Infof("logId=%s,monitorEcardVideoHanging task receive", logId)
	ecardVideoModesHanging, err := (&dao.EcardVideo{}).FindByTimeDifferenceAndStatusAndNotDeleted(logId)
	if err != nil {
		logger.Log.Errorf("logId=%s,monitorEcardVideoHanging task receive error:%v", logId, err)
		return
	}
	logger.Log.Infof("logId=%s,monitorEcardVideoHanging ecardVideoModesHanging.size:%d", logId, len(ecardVideoModesHanging))

	var receiveSendMsgList []VideoHangingSendMsg
	logger.Log.Infof("logId=%s,monitorEcardVideoHanging ,receiveSendMsgList=%v", logId, receiveSendMsgList)
	for _, model := range ecardVideoModesHanging {
		msg := VideoHangingSendMsg{
			VideoId:    model.VideoID,
			VideoName:  model.Name,
			CreateTime: model.CreateTime,
		}
		receiveSendMsgList = append(receiveSendMsgList, msg)
	}

	logger.Log.Infof("logId=%s,monitorEcardVideoHanging receiveSendMsgList.size:%d", logId, len(receiveSendMsgList))

	var sendVideoList []VideoHangingSendMsg
	if len(hangingEcardVideoSet) > 0 {
		for _, modelMsg := range receiveSendMsgList {
			if !utils.Contains(hangingEcardVideoSet, modelMsg.VideoId) {
				sendVideoList = append(sendVideoList, modelMsg)
			} else {
				logger.Log.Infof("logId=%s,monitorEcardVideoHanging hangingEcardVideoSet.has this msg,dont send,VideoId:%s", logId, modelMsg.VideoId)
			}
		}
	} else {
		sendVideoList = receiveSendMsgList
	}
	logger.Log.Infof("logId=%s,monitorEcardVideoHanging sendVideoList.size:%d", logId, len(sendVideoList))

	for _, model := range sendVideoList {
		hangingEcardVideoSet = util.SliceStrCustomAppend(hangingEcardVideoSet, model.VideoId)
	}
	logger.Log.Infof("logId=%s,monitorEcardVideoHanging hangingEcardVideoSet.size:%d", logId, len(hangingEcardVideoSet))

	logger.Log.Infof("logId=%s,当前时间: %v", logId, time.Now())
	logger.Log.Infof("logId=%s,AI卡片夯住数量: %v", logId, len(sendVideoList))
	if len(sendVideoList) > 0 {
		logger.Log.Infof("logId=%s,AI卡片夯住数量大于0, 发送信息到如流", logId)
		jsonData, err := json.Marshal(sendVideoList)
		if err != nil {
			logger.Log.Errorf("logId=%s,monitorEcardVideoHanging json.Marshal() error:%v", logId, err)
			return
		}
		messageStr := fmt.Sprintf("I卡片夯住视频有 %d 个：\n %v", len(sendVideoList), string(jsonData))
		logger.Log.Infof("logId=%s,sendMessage:%s", logId, messageStr)
		util.SendMsgToRuLiu(logId, messageStr, config.AppConfig.RuLiuGroup.AtBodyVideoHangingTask)
	} else {
		logger.Log.Infof("logId=%s,AI卡片夯住数量==0", logId)
	}
}

/*
制作播报夯住监控
*/
func monitorSlideVideoHanging() {
	logId := uuid.New().String()
	logger.Log.Infof("logId=%s,monitorSlideVideoHanging task receive", logId)
	slideVideoModesHanging, err := (&dao.SlideVideo{}).FindByTimeDifferenceAndStatusAndNotDeleted(logId)
	if err != nil {
		logger.Log.Errorf("logId=%s,monitorSlideVideoHanging task receive error:%v", logId, err)
		return
	}
	logger.Log.Infof("logId=%s,monitorSlideVideoHanging slideVideoModesHanging.size:%d", logId, len(slideVideoModesHanging))

	var receiveSendMsgList []VideoHangingSendMsg
	for _, model := range slideVideoModesHanging {
		msg := VideoHangingSendMsg{
			VideoId:    model.VideoID,
			VideoName:  model.Name,
			CreateTime: model.CreateTime,
		}
		receiveSendMsgList = append(receiveSendMsgList, msg)
	}

	var sendVideoList []VideoHangingSendMsg
	if len(hangingSlideVideoSet) > 0 {
		for _, modelMsg := range receiveSendMsgList {
			if !utils.Contains(hangingSlideVideoSet, modelMsg.VideoId) {
				sendVideoList = append(sendVideoList, modelMsg)
			}
		}
	} else {
		sendVideoList = receiveSendMsgList
	}

	for _, model := range sendVideoList {
		hangingSlideVideoSet = util.SliceStrCustomAppend(hangingSlideVideoSet, model.VideoId)
	}

	logger.Log.Infof("logId=%s,当前时间: %v", logId, time.Now())
	logger.Log.Infof("logId=%s,制作播报夯住数量: %v", logId, len(sendVideoList))
	if len(sendVideoList) > 0 {
		logger.Log.Infof("logId=%s,制作播报夯住数量大于0, 发送信息到如流", logId)
		jsonData, err := json.Marshal(sendVideoList)
		if err != nil {
			logger.Log.Errorf("logId=%s,monitorSlideVideoHanging json.Marshal() error:%v", logId, err)
			return
		}
		messageStr := fmt.Sprintf("制作播报夯住视频有 %d 个：\n %v", len(sendVideoList), string(jsonData))
		logger.Log.Infof("logId=%s,sendMessage:%s", logId, messageStr)
		util.SendMsgToRuLiu(logId, messageStr, config.AppConfig.RuLiuGroup.AtBodyVideoHangingTask)
	} else {
		logger.Log.Infof("logId=%s,制作播报夯住数量==0", logId)
	}
}

/*
分身视频夯住监控
*/
func monitorTwinVideoHanging() {
	logId := uuid.New().String()
	logger.Log.Infof("logId=%s,monitorTwinVideoHanging task receive", logId)
	twinVideoModesHanging, err := (&dao.TwinVideo{}).FindByTimeDifferenceAndStatusAndNotDeleted(logId)
	if err != nil {
		logger.Log.Errorf("logId=%s,monitorTwinVideoHanging task receive error:%v", logId, err)
		return
	}
	logger.Log.Infof("logId=%s,monitorTwinVideoHanging twinVideoModesHanging.size:%d", logId, len(twinVideoModesHanging))
	var receiveSendMsgList []VideoHangingSendMsg
	for _, model := range twinVideoModesHanging {
		msg := VideoHangingSendMsg{
			VideoId:    model.VideoID,
			VideoName:  model.Name,
			CreateTime: model.CreateTime,
		}
		receiveSendMsgList = append(receiveSendMsgList, msg)
	}

	var sendVideoList []VideoHangingSendMsg
	if len(hangingTwinVideoSet) > 0 {
		for _, modelMsg := range receiveSendMsgList {
			if !utils.Contains(hangingTwinVideoSet, modelMsg.VideoId) {
				sendVideoList = append(sendVideoList, modelMsg)
			}
		}
	} else {
		sendVideoList = receiveSendMsgList
	}

	for _, model := range sendVideoList {
		hangingTwinVideoSet = util.SliceStrCustomAppend(hangingTwinVideoSet, model.VideoId)
	}

	logger.Log.Infof("logId=%s,当前时间: %v", logId, time.Now())
	logger.Log.Infof("logId=%s,分身视频夯住数量: %v", logId, len(sendVideoList))
	if len(sendVideoList) > 0 {
		logger.Log.Infof("logId=%s,分身视频夯住数量大于0, 发送信息到如流", logId)
		jsonData, err := json.Marshal(sendVideoList)
		if err != nil {
			logger.Log.Errorf("logId=%s,monitorTwinVideoHanging json.Marshal() error:%v", logId, err)
			return
		}
		messageStr := fmt.Sprintf("分身视频夯住视频有 %d 个：\n %v", len(sendVideoList), string(jsonData))
		logger.Log.Infof("logId=%s,sendMessage:%s", logId, messageStr)
		util.SendMsgToRuLiu(logId, messageStr, config.AppConfig.RuLiuGroup.AtBodyVideoHangingTask)
	} else {
		logger.Log.Infof("logId=%s,分身视频夯住数量==0", logId)
	}
}

/*
AI卡片失败监控
*/
func monitorEcardVideoFailed() {
	logId := uuid.New().String()
	logger.Log.Infof("logId=%s,monitorEcardVideoFailed task receive", logId)
	ecardVideoModesFailed, err := (&dao.EcardVideo{}).FindByTimeDifferenceAndStatus(logId, "FAILED", 1)
	if err != nil {
		logger.Log.Errorf("logId=%s,monitorEcardVideoFailed task receive error:%v", logId, err)
		return
	}
	logger.Log.Infof("logId=%s,monitorEcardVideoFailed ecardVideoModesFailed.size:%d", logId, len(ecardVideoModesFailed))
	var failedVideoIdList []FailedSendMsg
	for _, model := range ecardVideoModesFailed {
		msg := FailedSendMsg{
			VideoId:      model.VideoID,
			VideoName:    model.Name,
			LastUpdateBy: model.LastUpdateBy,
		}
		failedVideoIdList = append(failedVideoIdList, msg)
	}

	logger.Log.Infof("logId=%s,当前时间: %v", logId, time.Now())
	logger.Log.Infof("logId=%s,AI卡片失败告警数量: %v", logId, len(failedVideoIdList))

	var sendVideoList []FailedSendMsg
	if len(failedEcardVideoSet) > 0 {
		for _, modelMsg := range failedVideoIdList {
			if !utils.Contains(failedEcardVideoSet, modelMsg.VideoId) {
				sendVideoList = append(sendVideoList, modelMsg)
			}
		}
	} else {
		sendVideoList = failedVideoIdList
	}

	for _, model := range sendVideoList {
		failedEcardVideoSet = util.SliceStrCustomAppend(failedEcardVideoSet, model.VideoId)
	}
	if len(sendVideoList) > 0 {
		logger.Log.Infof("logId=%s,AI卡片失败数量大于0, 发送信息到如流", logId)
		jsonData, err := json.Marshal(sendVideoList)
		if err != nil {
			logger.Log.Errorf("logId=%s,monitorEcardVideoFailed json.Marshal() error:%v", logId, err)
			return
		}
		messageStr := fmt.Sprintf("AI卡片失败视频有 %d 个：\n %v", len(sendVideoList), string(jsonData))
		logger.Log.Infof("logId=%s,sendMessage:%s", logId, messageStr)
		util.SendMsgToRuLiu(logId, messageStr, config.AppConfig.RuLiuGroup.AtBodyVideoTask)
	} else {
		logger.Log.Infof("logId=%s,AI卡片失败数量<=0", logId)
	}
}

/*
制作播报失败监控
*/
func monitorSlideVideoFailed() {
	logId := uuid.New().String()
	logger.Log.Infof("logId=%s,monitorSlideVideoFailed task receive", logId)
	slideVideoModesFailed, err := (&dao.SlideVideo{}).FindByTimeDifferenceAndStatus(logId, "FAILED", 1)
	if err != nil {
		logger.Log.Errorf("logId=%s,monitorSlideVideoFailed task receive error:%v", logId, err)
		return
	}
	logger.Log.Infof("logId=%s,monitorSlideVideoFailed slideVideoModesFailed.size:%d", logId, len(slideVideoModesFailed))

	var failedVideoIdList []*FailedSendMsg
	for _, model := range slideVideoModesFailed {
		msg := FailedSendMsg{
			VideoId:      model.VideoID,
			VideoName:    model.Name,
			LastUpdateBy: model.LastUpdateBy,
		}
		failedVideoIdList = append(failedVideoIdList, &msg)
	}

	logger.Log.Infof("logId=%s,当前时间: %v", logId, time.Now())
	logger.Log.Infof("logId=%s,制作播报失败告警数量: %v", logId, len(failedVideoIdList))

	var sendVideoList []*FailedSendMsg
	if len(failedSlideVideoSet) > 0 {
		for _, modelMsg := range failedVideoIdList {
			if !utils.Contains(failedSlideVideoSet, modelMsg.VideoId) {
				sendVideoList = append(sendVideoList, modelMsg)
			}
		}
	} else {
		sendVideoList = failedVideoIdList
	}

	for _, model := range sendVideoList {
		failedSlideVideoSet = util.SliceStrCustomAppend(failedSlideVideoSet, model.VideoId)
	}

	if len(sendVideoList) > 0 {
		logger.Log.Infof("logId=%s,制作播报失败数量大于0, 发送信息到如流", logId)
		jsonData, err := json.Marshal(sendVideoList)
		if err != nil {
			logger.Log.Errorf("logId=%s,monitorSlideVideoFailed json.Marshal() error:%v", logId, err)
			return
		}
		messageStr := fmt.Sprintf("制作播报失败视频有 %d 个：\n %v", len(sendVideoList), string(jsonData))
		logger.Log.Infof("logId=%s,sendMessage:%s", logId, messageStr)
		util.SendMsgToRuLiu(logId, messageStr, config.AppConfig.RuLiuGroup.AtBodyVideoTask)
	} else {
		logger.Log.Infof("logId=%s,制作播报失败数量==0", logId)
	}
}

/*
分身视频失败监控
*/
func monitorTwinVideoFailed() {
	logId := uuid.New().String()
	logger.Log.Infof("logId=%s,monitorTwinVideoFailed task receive", logId)
	twinVideoModesFailed, err := (&dao.TwinVideo{}).FindByTimeDifferenceAndStatus(logId, "FAILED", 1)
	if err != nil {
		logger.Log.Errorf("logId=%s,monitorTwinVideoFailed task receive error:%v", logId, err)
		return
	}
	logger.Log.Infof("logId=%s,monitorTwinVideoFailed twinVideoModesFailed.size:%d", logId, len(twinVideoModesFailed))
	var failedVideoIdList []*FailedSendMsg
	for _, model := range twinVideoModesFailed {
		msg := FailedSendMsg{
			VideoId:      model.VideoID,
			VideoName:    model.Name,
			LastUpdateBy: model.LastUpdateBy,
		}
		failedVideoIdList = append(failedVideoIdList, &msg)
	}

	logger.Log.Infof("logId=%s,当前时间: %v", logId, time.Now())
	logger.Log.Infof("logId=%s,分身视频失败告警数量: %v", logId, len(failedVideoIdList))

	var sendVideoList []*FailedSendMsg
	if len(failedTwinVideoSet) > 0 {
		for _, modelMsg := range failedVideoIdList {
			if !utils.Contains(failedTwinVideoSet, modelMsg.VideoId) {
				sendVideoList = append(sendVideoList, modelMsg)
			}
		}
	} else {
		sendVideoList = failedVideoIdList
	}

	for _, model := range sendVideoList {
		failedTwinVideoSet = util.SliceStrCustomAppend(failedTwinVideoSet, model.VideoId)
	}

	if len(sendVideoList) > 0 {
		logger.Log.Infof("logId=%s,分身视频失败数量大于0, 发送信息到如流", logId)
		jsonData, err := json.Marshal(sendVideoList)
		if err != nil {
			logger.Log.Errorf("logId=%s,monitorSlideVideoFailed json.Marshal() error:%v", logId, err)
			return
		}
		messageStr := fmt.Sprintf("分身视频失败视频有 %d 个：\n %v", len(sendVideoList), string(jsonData))
		logger.Log.Infof("logId=%s,sendMessage:%s", messageStr, logId)
		util.SendMsgToRuLiu(logId, messageStr, config.AppConfig.RuLiuGroup.AtBodyVideoTask)
	} else {
		logger.Log.Infof("logId=%s,分身视频失败数量=0", logId)
	}
}

/*
分身模板失败监控
*/
func monitorTwinTemplateFailed() {
	logId := uuid.New().String()
	logger.Log.Infof("logId=%s,monitorTwinTemplateFailed task receive", logId)
	twinTemplateModesFailed, err := (&dao.TwinTemplate{}).FindByTimeDifferenceAndStatus(logId, "FAILED", 1)
	if err != nil {
		logger.Log.Errorf("logId=%s,monitorTwinTemplateFailed task receive error:%v", logId, err)
		return
	}
	logger.Log.Infof("logId=%s,monitorTwinTemplateFailed twinTemplateModesFailed.size:%d", logId, len(twinTemplateModesFailed))
	var failedVideoIdList []*TwinTemplateFailedMsg
	for _, model := range twinTemplateModesFailed {
		msg := TwinTemplateFailedMsg{
			TemplateId:   model.TemplateId,
			VideoName:    model.Name,
			LastUpdateBy: model.LastUpdateBy,
		}
		failedVideoIdList = append(failedVideoIdList, &msg)
	}

	logger.Log.Infof("logId=%s,当前时间: %v", logId, time.Now())
	logger.Log.Infof("logId=%s,分身模板告警数量: %v", logId, len(failedVideoIdList))

	var sendVideoList []*TwinTemplateFailedMsg
	if len(failedTwinTemplateSet) > 0 {
		for _, modelMsg := range failedVideoIdList {
			if !utils.Contains(failedTwinTemplateSet, modelMsg.TemplateId) {
				sendVideoList = append(sendVideoList, modelMsg)
			}
		}
	} else {
		sendVideoList = failedVideoIdList
	}

	for _, model := range sendVideoList {
		failedTwinTemplateSet = util.SliceStrCustomAppend(failedTwinTemplateSet, model.TemplateId)
	}

	if len(sendVideoList) > 0 {
		logger.Log.Infof("logId=%s,分身模板失败数量大于0, 发送信息到如流", logId)
		jsonData, err := json.Marshal(sendVideoList)
		if err != nil {
			logger.Log.Errorf("logId=%s,monitorSlideVideoFailed json.Marshal() error:%v", logId, err)
			return
		}
		messageStr := fmt.Sprintf("分身模板失败视频有 %d 个：\n %v", len(sendVideoList), string(jsonData))
		logger.Log.Infof("logId=%s,sendMessage:%s", logId, messageStr)
		util.SendMsgToRuLiu(logId, messageStr, config.AppConfig.RuLiuGroup.AtBodyVideoTask)
	} else {
		logger.Log.Infof("logId=%s,分身模板失败数量=0", logId)
	}
}

/*
裂变视频夯住监控
*/
func monitorFissionVideoHanging() {
	logId := uuid.New().String()
	logger.Log.Infof("logId=%s,monitorFissionVideoHanging task receive", logId)
	fissionVideoModesHanging, err := (&dao.FissionVideo{}).FindByTimeDifferenceAndStatusAndNotDeleted(logId)
	if err != nil {
		logger.Log.Errorf("logId=%s,monitorFissionVideoHanging task receive error:%v", logId, err)
		return
	}
	logger.Log.Infof("logId=%s,monitorFissionVideoHanging fissionVideoModesHanging.size:%d", logId, len(fissionVideoModesHanging))
	var receiveSendMsgList []VideoHangingSendMsg
	for _, model := range fissionVideoModesHanging {
		msg := VideoHangingSendMsg{
			VideoId:    model.VideoID,
			VideoName:  model.Name,
			CreateTime: model.CreateTime,
		}
		receiveSendMsgList = append(receiveSendMsgList, msg)
	}

	var sendVideoList []VideoHangingSendMsg
	if len(hangingFissionVideoSet) > 0 {
		for _, modelMsg := range receiveSendMsgList {
			if !utils.Contains(hangingFissionVideoSet, modelMsg.VideoId) {
				sendVideoList = append(sendVideoList, modelMsg)
			}
		}
	} else {
		sendVideoList = receiveSendMsgList
	}

	for _, model := range sendVideoList {
		hangingFissionVideoSet = util.SliceStrCustomAppend(hangingFissionVideoSet, model.VideoId)
	}

	logger.Log.Infof("logId=%s,当前时间: %v", logId, time.Now())
	logger.Log.Infof("logId=%s,裂变视频夯住数量: %v", logId, len(sendVideoList))
	if len(sendVideoList) > 0 {
		logger.Log.Infof("logId=%s,裂变视频夯住数量大于0, 发送信息到如流", logId)
		jsonData, err := json.Marshal(sendVideoList)
		if err != nil {
			logger.Log.Errorf("logId=%s,monitorFissionVideoHanging json.Marshal() error:%v", logId, err)
			return
		}
		messageStr := fmt.Sprintf("裂变视频夯住视频有 %d 个：\n %v", len(sendVideoList), string(jsonData))
		logger.Log.Infof("logId=%s,sendMessage:%s", logId, messageStr)
		util.SendMsgToRuLiu(logId, messageStr, config.AppConfig.RuLiuGroup.AtBodyVideoHangingTask)
	} else {
		logger.Log.Infof("logId=%s,裂变视频夯住数量=0", logId)
	}
}

/*
列变视频失败监控
*/
func monitorFissionVideoFailed() {
	logId := uuid.New().String()
	logger.Log.Infof("logId=%s,monitorFissionVideoFailed task receive", logId)
	fissionVideoModesFailed, err := (&dao.FissionVideo{}).FindByTimeDifferenceAndStatus(logId, 1)
	if err != nil {
		logger.Log.Errorf("logId=%s,monitorFissionVideoFailed task receive error:%v", logId, err)
	}
	logger.Log.Infof("logId=%s,monitorFissionVideoFailed fissionVideoModesFailed.size:%d", logId, len(fissionVideoModesFailed))
	var failedVideoIdList []*FailedSendMsg
	for _, model := range fissionVideoModesFailed {
		msg := FailedSendMsg{
			VideoId:      model.VideoID,
			VideoName:    model.Name,
			LastUpdateBy: model.LastUpdateBy,
		}
		failedVideoIdList = append(failedVideoIdList, &msg)
	}

	logger.Log.Infof("logId=%s,当前时间: %v", logId, time.Now())
	logger.Log.Infof("logId=%s,裂变视频失败告警数量: %v", logId, len(failedVideoIdList))

	var sendVideoList []*FailedSendMsg
	if len(failedFissionVideoSet) > 0 {
		for _, modelMsg := range failedVideoIdList {
			if !utils.Contains(failedFissionVideoSet, modelMsg.VideoId) {
				sendVideoList = append(sendVideoList, modelMsg)
			}
		}
	} else {
		sendVideoList = failedVideoIdList
	}

	for _, model := range sendVideoList {
		failedFissionVideoSet = util.SliceStrCustomAppend(failedFissionVideoSet, model.VideoId)
	}

	if len(sendVideoList) > 0 {
		logger.Log.Infof("logId=%s,裂变视频失败数量大于0, 发送信息到如流", logId)
		jsonData, err := json.Marshal(sendVideoList)
		if err != nil {
			logger.Log.Errorf("logId=%s,monitorFissionVideoFailed json.Marshal() error:%v", logId, err)
			return
		}
		messageStr := fmt.Sprintf("裂变视频失败视频有 %d 个：\n %v", len(sendVideoList), string(jsonData))
		logger.Log.Infof("logId=%s,sendMessage:%s", messageStr, logId)
		util.SendMsgToRuLiu(logId, messageStr, config.AppConfig.RuLiuGroup.AtBodyVideoTask)
	} else {
		logger.Log.Infof("logId=%s,列变视频失败数量=0", logId)
	}
}

/*
coursewareVideo 课件视频夯住监控
*/
func monitorCoursewareVideoHanging() {
	logId := uuid.New().String()
	logger.Log.Infof("logId=%s,monitorCoursewareVideoHanging task receive", logId)
	coursewareVideoModesHanging, err := (&dao.CoursewareVideo{}).FindHangingVideo(logId)
	if err != nil {
		logger.Log.Errorf("logId=%s,monitorCoursewareVideoHanging task receive error:%v", logId, err)
		return
	}
	logger.Log.Infof("logId=%s,monitorCoursewareVideoHanging coursewareVideoModesHanging.size:%d", logId, len(coursewareVideoModesHanging))
	var receiveSendMsgList []VideoHangingSendMsg
	for _, model := range coursewareVideoModesHanging {
		msg := VideoHangingSendMsg{
			VideoId:    model.VideoID,
			VideoName:  model.Name,
			CreateTime: model.CreateTime,
		}
		receiveSendMsgList = append(receiveSendMsgList, msg)
	}

	var sendVideoList []VideoHangingSendMsg
	if len(hangingCoursewareVideoSet) > 0 {
		for _, modelMsg := range receiveSendMsgList {
			if !utils.Contains(hangingCoursewareVideoSet, modelMsg.VideoId) {
				sendVideoList = append(sendVideoList, modelMsg)
			}
		}
	} else {
		sendVideoList = receiveSendMsgList
	}

	for _, model := range sendVideoList {
		hangingCoursewareVideoSet = util.SliceStrCustomAppend(hangingCoursewareVideoSet, model.VideoId)
	}

	logger.Log.Infof("logId=%s,当前时间: %v", logId, time.Now())
	logger.Log.Infof("logId=%s,课件视频夯住数量: %v", logId, len(sendVideoList))
	if len(sendVideoList) > 0 {
		logger.Log.Infof("logId=%s,课件视频夯住数量大于0, 发送信息到如流", logId)
		jsonData, err := json.Marshal(sendVideoList)
		if err != nil {
			logger.Log.Errorf("logId=%s,monitorCoursewareVideoHanging json.Marshal() error:%v", logId, err)
			return
		}
		messageStr := fmt.Sprintf("课件视频夯住视频有 %d 个：\n %v", len(sendVideoList), string(jsonData))
		logger.Log.Infof("logId=%s,sendMessage:%s", logId, messageStr)
		util.SendMsgToRuLiu(logId, messageStr, config.AppConfig.RuLiuGroup.AtBodyPptCoursewareTask)
	} else {
		logger.Log.Infof("logId=%s,课件视频夯住数量==0", logId)
	}
}

/*
coursewareVideo  课件视频失败监控
*/
func monitorCoursewareVideoFailed() {
	logId := uuid.New().String()
	logger.Log.Infof("logId=%s,monitorCoursewareVideoFailed task receive", logId)
	courseWareVideoModesFailed, err := (&dao.CoursewareVideo{}).FindFailedVideo(logId, "FAILED", 1)
	if err != nil {
		logger.Log.Errorf("logId=%s,monitorCoursewareVideoFailed task receive error:%v", logId, err)
	}
	logger.Log.Infof("logId=%s,monitorCoursewareVideoFailed courseWareVideoModesFailed.size:%d", logId, len(courseWareVideoModesFailed))
	var failedVideoIdList []*FailedSendMsg
	for _, model := range courseWareVideoModesFailed {
		msg := FailedSendMsg{
			VideoId:      model.VideoID,
			VideoName:    model.Name,
			LastUpdateBy: model.LastUpdateBy,
		}
		failedVideoIdList = append(failedVideoIdList, &msg)
	}

	logger.Log.Infof("logId=%s,当前时间: %v", logId, time.Now())
	logger.Log.Infof("logId=%s,课件视频失败告警数量: %v", logId, len(failedVideoIdList))

	var sendVideoList []*FailedSendMsg
	if len(failedCoursewareVideoSet) > 0 {
		for _, modelMsg := range failedVideoIdList {
			if !utils.Contains(failedCoursewareVideoSet, modelMsg.VideoId) {
				sendVideoList = append(sendVideoList, modelMsg)
			}
		}
	} else {
		sendVideoList = failedVideoIdList
	}

	for _, model := range sendVideoList {
		failedCoursewareVideoSet = util.SliceStrCustomAppend(failedCoursewareVideoSet, model.VideoId)
	}

	if len(sendVideoList) > 0 {
		logger.Log.Infof("logId=%s,课件视频失败数量大于0, 发送信息到如流", logId)
		jsonData, err := json.Marshal(sendVideoList)
		if err != nil {
			logger.Log.Errorf("logId=%s,monitorCoursewareVideoFailed json.Marshal() error:%v", logId, err)
			return
		}
		messageStr := fmt.Sprintf("课件视频失败视频有 %d 个：\n %v", len(sendVideoList), string(jsonData))
		logger.Log.Infof("logId=%s,sendMessage:%s", messageStr, logId)
		util.SendMsgToRuLiu(logId, messageStr, config.AppConfig.RuLiuGroup.AtBodyPptCoursewareTask)
	} else {
		logger.Log.Infof("logId=%s,课件视频失败数量=0", logId)
	}
}

/*
视频翻译--夯住监控
*/
func MonitorVideoTranslateHanging() {
	logId := uuid.New().String()
	logger.Log.Infof("logId=%s,monitorVideoTranslateHanging task receive", logId)
	videoTransHangingModels, err := (&dao.VideoTranslateEntity{}).FindHangingVideo(logId)
	if err != nil {
		logger.Log.Errorf("logId=%s,monitorVideoTranslateHanging task receive error:%v", logId, err)
		return
	}
	logger.Log.Infof("logId=%s,monitorVideoTranslateHanging videoTransHangingModels.size:%d", logId, len(videoTransHangingModels))

	var sendVideoList []*dao.VideoTranslateEntity
	if len(hangingVideoTranslateRecordingArray) > 0 {
		for _, modelMsg := range videoTransHangingModels {
			if !utils.Contains(hangingVideoTranslateRecordingArray, modelMsg.VideoID) {
				sendVideoList = append(sendVideoList, modelMsg)
			}
		}
	} else {
		sendVideoList = videoTransHangingModels
	}

	for _, model := range sendVideoList {
		hangingVideoTranslateRecordingArray = util.SliceStrCustomAppend(hangingVideoTranslateRecordingArray, model.VideoID)
	}

	logger.Log.Infof("logId=%s,当前时间: %v", logId, time.Now())
	logger.Log.Infof("logId=%s,视频翻译夯住数量: %v", logId, len(sendVideoList))
	if len(sendVideoList) > 0 {
		logger.Log.Infof("logId=%s,视频翻译夯住数量大于0, 发送信息到如流", logId)
		jsonData, err := json.Marshal(sendVideoList)
		if err != nil {
			logger.Log.Errorf("logId=%s,monitorVideoTranslateHanging json.Marshal() error:%v", logId, err)
			return
		}
		messageStr := fmt.Sprintf("视频翻译夯住有 %d 个：\n %v", len(sendVideoList), string(jsonData))
		logger.Log.Infof("logId=%s,sendMessage:%s", logId, messageStr)
		util.SendMsgToRuLiu(logId, messageStr, config.AppConfig.RuLiuGroup.AtBodyVideoTranslate)
	} else {
		logger.Log.Infof("logId=%s,视频翻译夯住数量==0", logId)
	}
}

/*
视频翻译--失败监控
*/
func MonitorVideoTranslateFailed() {
	logId := uuid.New().String()
	logger.Log.Infof("logId=%s,MonitorVideoTranslateFailed task receive", logId)
	videoTranslateFailedModels, err := (&dao.VideoTranslateEntity{}).FindFailedVideo(logId)
	if err != nil {
		logger.Log.Errorf("logId=%s,MonitorVideoTranslateFailed task receive error:%v", logId, err)
		return
	}
	logger.Log.Infof("logId=%s,MonitorVideoTranslateFailed videoTranslateFailedModels.size:%d", logId, len(videoTranslateFailedModels))

	var sendVideoList []*dao.VideoTranslateEntity
	if len(failedVideoTranslateRecordingArray) > 0 {
		for _, modelMsg := range videoTranslateFailedModels {
			if !utils.Contains(failedVideoTranslateRecordingArray, modelMsg.VideoID) {
				sendVideoList = append(sendVideoList, modelMsg)
			}
		}
	} else {
		sendVideoList = videoTranslateFailedModels
	}

	for _, model := range sendVideoList {
		failedVideoTranslateRecordingArray = util.SliceStrCustomAppend(failedVideoTranslateRecordingArray, model.VideoID)
	}

	logger.Log.Infof("logId=%s,当前时间: %v", logId, time.Now())
	logger.Log.Infof("logId=%s,视频翻译失败数量: %v", logId, len(sendVideoList))
	if len(sendVideoList) > 0 {
		logger.Log.Infof("logId=%s,视频翻译失败数量大于0, 发送信息到如流", logId)
		jsonData, err := json.Marshal(sendVideoList)
		if err != nil {
			logger.Log.Errorf("logId=%s,MonitorVideoTranslateFailed json.Marshal() error:%v", logId, err)
			return
		}
		messageStr := fmt.Sprintf("视频翻译失败有 %d 个：\n %v", len(sendVideoList), string(jsonData))
		logger.Log.Infof("logId=%s,sendMessage:%s", logId, messageStr)
		util.SendMsgToRuLiu(logId, messageStr, config.AppConfig.RuLiuGroup.AtBodyVideoTranslate)
	} else {
		logger.Log.Infof("logId=%s,视频翻译失败数量==0", logId)
	}
}
