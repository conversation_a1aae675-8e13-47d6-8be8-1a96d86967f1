package handler

import (
	commProto "acg-ai-go-common/beans/proto"
	"acg-ai-go-common/logger"
	"dhlive-saas-alert/constant"
	"dhlive-saas-alert/dao"
	"fmt"
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

type TimbreManagerRequest struct {
	FromAccountId string `json:"fromAccountId"` // 源账号id
	ToAccountId   string `json:"toAccountId"`   // 目标账号id
	ToUserId      string `json:"toUserId"`      // 目标用户id
	SpeakerId     string `json:"speakerId"`     // 音色Id
	Action        string `json:"action"`        // 操作类型  复制 COPY   迁移 MIGRATE
}

type TimbreInsertReq struct {
	UserID      string      `json:"user_id"`      // 用户ID
	AccountId   string      `json:"account_id"`   // 账户ID
	Name        string      `json:"name"`         // 名称
	Gender      int         `json:"gender"`       // 性别
	Per         string      `json:"per"`          // 语种
	Config      dao.JSONMap `json:"config"`       // 配置
	Describe    string      `json:"describe"`     // 描述
	Extra       dao.JSONMap `json:"extra"`        // 额外数据
	ExampleText string      `json:"example_text"` // 示例文本
	Status      int         `json:"status"`       // 状态
	IsNatural   int         `json:"is_natural"`   // 是否自然声
}
type TimbreModifyReq struct {
	UserID      string      `json:"user_id"`      // 用户ID
	AccountId   string      `json:"account_id"`   // 账户ID
	Name        string      `json:"name"`         // 名称
	Gender      int         `json:"gender"`       // 性别
	Per         string      `json:"per"`          // 语种
	Config      dao.JSONMap `json:"config"`       // 配置
	Describe    string      `json:"describe"`     // 描述
	Extra       dao.JSONMap `json:"extra"`        // 额外数据
	ExampleText string      `json:"example_text"` // 示例文本
	Status      int         `json:"status"`       // 状态
	IsNatural   int         `json:"is_natural"`   // 是否自然声
}

type TimbreQueryReq struct {
	Name      string `json:"name,omitempty"`       // 名称
	UserID    string `json:"user_id,omitempty"`    // 用户ID
	AccountId string `json:"account_id,omitempty"` // 账户ID
}

func TimbreMigrate(c *gin.Context) {
	logId := uuid.New().String()
	// 解析请求参数
	req := TimbreManagerRequest{}
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Log.Errorf("logId=%s,TimbreMigrate 解析请求参数失败 error:%v", logId, err)
		c.AbortWithStatusJSON(http.StatusOK, commProto.NewCommRsp(constant.CommCodeParamErr, "参数解析异常"))
		return
	}

	if len(req.FromAccountId) == 0 || len(req.ToAccountId) == 0 || len(req.SpeakerId) == 0 {
		logger.Log.Errorf("logId=%s,TimbreMigrate 参数错误", logId)
		c.AbortWithStatusJSON(http.StatusOK, commProto.NewCommRsp(constant.CommCodeParamErr, "参数错误"))
		return
	}
	logger.Log.Infof("logId=%s,TimbreMigrate reqBody:%v", logId, req)

	if !strings.Contains(req.SpeakerId, "minimax") && !strings.HasPrefix(req.SpeakerId, "HS_") {
		logger.Log.Errorf("logId=%s,TimbreMigrate 该音色Id不支持迁移", logId)
		c.AbortWithStatusJSON(http.StatusOK, commProto.NewCommRsp(constant.CommCodeParamErr, "只支持hs和minimax音色的迁移"))
		return
	}
	speakerId := req.SpeakerId
	toAccountId := req.ToAccountId
	toUserId := req.ToUserId

	// 一、如果toUserId为空，根据accountId 查询目标账号的用户名，再根据用户名查询userId
	if len(toUserId) == 0 {
		accountUsers, err := (&dao.DhAccountUser{}).QueryUserNameById(logId, toAccountId)
		if err != nil {
			logMsg := fmt.Sprintf("logId=%s,DhAccountUser.QueryUserNameById error:%v", logId, err)
			logger.Log.Errorf(logMsg)
			c.AbortWithStatusJSON(http.StatusOK, commProto.NewCommRsp(constant.CommCodeInternalErr, logMsg))
			return
		}
		if accountUsers == nil || len(accountUsers) <= 0 {
			logMsg := fmt.Sprintf("logId=%s,查询目标账号的userName为空", logId)
			logger.Log.Errorf(logMsg)
			c.AbortWithStatusJSON(http.StatusOK, commProto.NewCommRsp(constant.CommCodeInternalErr, logMsg))
			return
		}

		if len(accountUsers) != 1 {
			logMsg := fmt.Sprintf("logId=%s,查询目标账号的userName有多个记录,accountUsers:%+v", logId, accountUsers)
			logger.Log.Errorf(logMsg)
			c.AbortWithStatusJSON(http.StatusOK, commProto.NewCommRsp(constant.CommCodeInternalErr, logMsg))
			return
		}
		logger.Log.Infof("logId=%s,查询目标用户的userName成功:%v", logId, accountUsers)

		toUserName := accountUsers[0].UserName
		// 二、根据上一个步骤的user_name，查询目标账号的user_id
		users, err := (&dao.DhUser{}).QueryUserIdByName(logId, toUserName)
		if err != nil {
			logMsg := fmt.Sprintf("logId=%s,DhUser.QueryUserIdByName error:%v", logId, err)
			logger.Log.Errorf(logMsg)
			c.AbortWithStatusJSON(http.StatusOK, commProto.NewCommRsp(constant.CommCodeInternalErr, logMsg))
			return
		}
		if users == nil || len(users) <= 0 {
			logMsg := fmt.Sprintf("logId=%s,查询目标账号的userId为空", logId)
			logger.Log.Errorf(logMsg)
			c.AbortWithStatusJSON(http.StatusOK, commProto.NewCommRsp(constant.CommCodeInternalErr, logMsg))
			return
		}

		if len(users) != 1 {
			logMsg := fmt.Sprintf("logId=%s,查询到目标账号下有多个userId的记录,users:%+v", logId, users)
			logger.Log.Errorf(logMsg)
			c.AbortWithStatusJSON(http.StatusOK, commProto.NewCommRsp(constant.CommCodeInternalErr, logMsg))
			return
		}
		logger.Log.Infof("logId=%s,查询目标用户的userId成功:%v", logId, users)
		toUserId = users[0].UserID
	}

	if len(toUserId) == 0 {
		logMsg := fmt.Sprintf("logId=%s,目标账户的userId为空", logId)
		logger.Log.Errorf(logMsg)
		c.AbortWithStatusJSON(http.StatusOK, commProto.NewCommRsp(constant.CommCodeInternalErr, logMsg))
		return
	}

	// 迁移
	if req.Action == constant.TimbreActionMigrate {
		// 创建事务
		gormDB, ok := dao.GetTtsCloneDB()
		if !ok {
			logMsg := fmt.Sprintf("logId=%s,GetTtsCloneDB error", logId)
			logger.Log.Errorf(logMsg)
			c.AbortWithStatusJSON(http.StatusOK, commProto.NewCommRsp(constant.CommCodeInternalErr, logMsg))
			return
		}
		tx := gormDB.Begin()

		// 迁移音色第1步: 更新TtsPerson库的user_id
		id, err := (&dao.TtsPerson{}).UpdateUserIdByPer(tx, logId, speakerId, req.FromAccountId, toAccountId)
		if err != nil {
			tx.Rollback()
			failMsg := fmt.Sprintf("logId=%s,TtsPerson.UpdateUserIdByPer error:%v", logId, err)
			logger.Log.Errorf(failMsg)
			c.AbortWithStatusJSON(http.StatusOK, commProto.NewCommRsp(constant.CommCodeInternalErr, failMsg))
			return
		}
		logger.Log.Infof("logId=%s,TtsPerson.Update userId success", logId)

		// 迁移音色第2步: 更新TTSClone库的user_id和account_id
		err = (&dao.TTSClone{}).UpdateUserAndAccountIdByThirdId(tx, logId, id, speakerId, req.FromAccountId, toAccountId, toUserId)
		if err != nil {
			tx.Rollback()
			failMsg := fmt.Sprintf("logId=%s,TTSClone.UpdateUserAndAccountIdByThirdId error:%v", logId, err)
			logger.Log.Errorf(failMsg)
			c.AbortWithStatusJSON(http.StatusOK, commProto.NewCommRsp(constant.CommCodeInternalErr, failMsg))
			return
		}
		logger.Log.Infof("logId=%s,TTSClone.Update userId and accountId success", logId)

		// 迁移音色第3步:  commit 事务
		err = tx.Commit().Error
		if err != nil {
			tx.Rollback()
			failMsg := fmt.Sprintf("logId=%s,音色迁移,tx.Commit(),error:%v", logId, err)
			logger.Log.Errorf(failMsg)
			c.AbortWithStatusJSON(http.StatusOK, commProto.NewCommRsp(constant.CommCodeInternalErr, failMsg))
			return
		}
		logger.Log.Infof("logId:%s,音色迁移 success", logId)
		c.AbortWithStatusJSON(http.StatusOK, commProto.NewSuccessRsp(nil))
	} else if req.Action == constant.TimbreActionCopy {
		// 复制音色 创建事务
		gormDB, ok := dao.GetTtsCloneDB()
		if !ok {
			logMsg := fmt.Sprintf("logId=%s,GetTtsCloneDB error", logId)
			logger.Log.Errorf(logMsg)
			c.AbortWithStatusJSON(http.StatusOK, commProto.NewCommRsp(constant.CommCodeInternalErr, logMsg))
			return
		}

		tx := gormDB.Begin()
		// 复制音色第1步:查询 TtsPerson 库中是否有目标用户的对应记录
		ttsPersonRecord, err := (&dao.TtsPerson{}).QueryTtsPersonByUserAndPer(tx, logId, speakerId, req.ToAccountId)
		if err != nil && ttsPersonRecord.ID == 0 {
			// 没查到了对应记录 需要更新 ttsPerson 和 ttsClone库
			// 复制音色第1步:TtsPerson库中新插入一列
			id, err := (&dao.TtsPerson{}).SelectAndCreateByPer(tx, logId, speakerId, req.FromAccountId, toAccountId)
			if err != nil {
				tx.Rollback()
				failMsg := fmt.Sprintf("logId=%s,TtsPerson.SelectAndCreateByPer error:%v", logId, err)
				logger.Log.Errorf(failMsg)
				c.AbortWithStatusJSON(http.StatusOK, commProto.NewCommRsp(constant.CommCodeInternalErr, failMsg))
				return
			}
			logger.Log.Infof("logId=%s,TtsPerson insert success,newId:%d", logId, id)

			// 复制音色第2步:TtsClone库中新插入一列
			err = (&dao.TTSClone{}).SelectAndCreateByPer(tx, logId, id, speakerId, req.FromAccountId, toAccountId, toUserId)
			if err != nil {
				tx.Rollback()
				failMsg := fmt.Sprintf("logId=%s,TTSClone.SelectAndCreateByPer error:%v", logId, err)
				logger.Log.Errorf(failMsg)
				c.AbortWithStatusJSON(http.StatusOK, commProto.NewCommRsp(constant.CommCodeInternalErr, failMsg))
				return
			}
			logger.Log.Infof("logId=%s,TTSClone insert success", logId)

			// 复制音色第3步:  commit 事务
			err = tx.Commit().Error
			if err != nil {
				tx.Rollback()
				failMsg := fmt.Sprintf("logId=%s,音色迁移,tx.Commit(),error:%v", logId, err)
				logger.Log.Errorf(failMsg)
				c.AbortWithStatusJSON(http.StatusOK, commProto.NewCommRsp(constant.CommCodeInternalErr, failMsg))
				return
			}
		} else if ttsPersonRecord.ID > 0 {
			// 查到了对应记录，只更新ttsClone 库    TtsClone库中新插入一列
			err = (&dao.TTSClone{}).SelectAndCreateByPer(tx, logId, ttsPersonRecord.ID, ttsPersonRecord.Per, req.FromAccountId, toAccountId, toUserId)
			if err != nil {
				tx.Rollback()
				failMsg := fmt.Sprintf("logId=%s,TTSClone.SelectAndCreateByPer error:%v", logId, err)
				logger.Log.Errorf(failMsg)
				c.AbortWithStatusJSON(http.StatusOK, commProto.NewCommRsp(constant.CommCodeInternalErr, failMsg))
				return
			}
			logger.Log.Infof("logId=%s,TTSClone insert success", logId)

			// 复制音色第3步:  commit 事务
			err = tx.Commit().Error
			if err != nil {
				tx.Rollback()
				failMsg := fmt.Sprintf("logId=%s,音色迁移,tx.Commit(),error:%v", logId, err)
				logger.Log.Errorf(failMsg)
				c.AbortWithStatusJSON(http.StatusOK, commProto.NewCommRsp(constant.CommCodeInternalErr, failMsg))
				return
			}
		} else {
			tx.Rollback()
			failMsg := fmt.Sprintf("logId=%s,TTSClone.QueryTtsPersonByUserAndPer ttsPersonRecord=%+v, err=%+v", logId, ttsPersonRecord, err)
			logger.Log.Errorf(failMsg)
			c.AbortWithStatusJSON(http.StatusOK, commProto.NewCommRsp(constant.CommCodeInternalErr, failMsg))
		}
		logger.Log.Infof("logId:%s,音色迁移 success", logId)
		c.AbortWithStatusJSON(http.StatusOK, commProto.NewSuccessRsp(nil))
	} else {
		failMsg := fmt.Sprintf("logId=%s action is wrong", logId)
		logger.Log.Errorf(failMsg)
		c.AbortWithStatusJSON(http.StatusOK, commProto.NewCommRsp(constant.CommCodeInternalErr, failMsg))
	}

}

/*
*
工牌麦音色新增
*/
func TimbreInsert(c *gin.Context) {
	logId := uuid.New().String()
	req := TimbreInsertReq{}
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Log.Errorf("logId=%s,TimbreAdd 解析请求参数失败 error:%v", logId, err)
		c.AbortWithStatusJSON(http.StatusOK, commProto.NewCommRsp(constant.CommCodeParamErr, "参数解析异常"))
		return
	}
	if len(req.UserID) == 0 || len(req.AccountId) == 0 || len(req.Name) == 0 || len(req.Per) == 0 || len(req.ExampleText) == 0 {
		logger.Log.Errorf("logId=%s,TimbreAdd 参数错误", logId)
		c.AbortWithStatusJSON(http.StatusOK, commProto.NewCommRsp(constant.CommCodeParamErr, "参数错误"))
		return
	}
	// 新增音色第一步， 获取数据库实例
	gormDB, ok := dao.GetTtsCloneDB()
	if !ok {
		logMsg := fmt.Sprintf("logId=%s,GetTtsCloneDB error", logId)
		logger.Log.Errorf(logMsg)
		c.AbortWithStatusJSON(http.StatusOK, commProto.NewCommRsp(constant.CommCodeInternalErr, logMsg))
		return
	}
	tx := gormDB.Begin()
	// 新增音色第二步，新增工牌麦音色到TtsPerson 表中
	id, err := (&dao.TtsPerson{}).InsertPer(tx, logId, req.UserID, req.Name, req.Gender, req.Per, req.Config, req.Extra, req.Describe, req.IsNatural)
	if err != nil {
		tx.Rollback()
		failMsg := fmt.Sprintf("logId=%s,TtsPerson.InsertPer error:%v", logId, err)
		logger.Log.Errorf(failMsg)
		c.AbortWithStatusJSON(http.StatusOK, commProto.NewCommRsp(constant.CommCodeInternalErr, failMsg))
		return
	}
	// 新增音色第三步， 新增工牌麦音色到tts_clone表中
	err = (&dao.TTSClone{}).InsertPer(tx, logId, id, req.Per, req.AccountId, req.Name, req.Describe, req.ExampleText, req.UserID, req.Status, req.Gender)
	if err != nil {
		tx.Rollback()
		failMsg := fmt.Sprintf("logId=%s,TTSClone.InsertPer error:%v", logId, err)
		logger.Log.Errorf(failMsg)
		c.AbortWithStatusJSON(http.StatusOK, commProto.NewCommRsp(constant.CommCodeInternalErr, failMsg))
		return
	}
	err = tx.Commit().Error
	if err != nil {
		tx.Rollback()
		failMsg := fmt.Sprintf("logId=%s,音色新增,tx.Commit(),error:%v", logId, err)
		logger.Log.Errorf(failMsg)
		c.AbortWithStatusJSON(http.StatusOK, commProto.NewCommRsp(constant.CommCodeInternalErr, failMsg))
		return
	}
	logger.Log.Infof("logId:%s,音色新增 success", logId)
	c.AbortWithStatusJSON(http.StatusOK, commProto.NewSuccessRsp(nil))
}

/*
*
工牌麦音色查询
*/
func TimbreQuery(c *gin.Context) {
	logId := uuid.New().String()
	req := &TimbreQueryReq{}
	err := c.ShouldBindJSON(req)
	if err != nil {
		logger.Log.Errorf("logId=%s,TimbreQuery 解析请求参数失败 error:%v", logId, err)
		c.AbortWithStatusJSON(http.StatusOK, commProto.NewCommRsp(constant.CommCodeParamErr, "参数解析异常"))
		return
	}
	// 如果为空则直接返回
	if len(req.Name) == 0 && len(req.UserID) == 0 && len(req.AccountId) == 0 {
		logger.Log.Errorf("logId=%s,TimbreQuery 参数错误", logId)
		var data [0]int // 空数组
		c.AbortWithStatusJSON(http.StatusOK, commProto.NewSuccessRsp(data))
		return
	}

	gormDB, ok := dao.GetTtsCloneDB()
	if !ok {
		logMsg := fmt.Sprintf("logId=%s,GetTtsCloneDB error", logId)
		logger.Log.Errorf(logMsg)
		c.AbortWithStatusJSON(http.StatusOK, commProto.NewCommRsp(constant.CommCodeInternalErr, logMsg))
		return
	}
	tx := gormDB.Begin()
	var data []dao.TTSClone
	// 如果name 和userID 为空且accountId 则直接查询TTSClone 表
	if len(req.Name) == 0 && len(req.UserID) == 0 && len(req.AccountId) > 0 {
		ttsClones, err := (&dao.TTSClone{}).QueryTtsCloneByMaterialIdAndAccountId(tx, logId, -1, req.AccountId)
		if err != nil {
			tx.Rollback()
			failMsg := fmt.Sprintf("logId=%s,TTSClone.QueryTtsCloneByMaterialIdAndAccountId error:%v", logId, err)
			logger.Log.Errorf(failMsg)
			c.AbortWithStatusJSON(http.StatusOK, commProto.NewCommRsp(constant.CommCodeInternalErr, failMsg))
			return
		}
		for _, ttsClone := range ttsClones {
			data = append(data, *ttsClone)
		}
	} else {
		// 查询TtsPerson 表数据
		ttsPersons, err := (&dao.TtsPerson{}).QueryTtsPersonByNameAndUserId(tx, logId, req.Name, req.UserID)
		if err != nil {
			tx.Rollback()
			failMsg := fmt.Sprintf("logId=%s,TtsPerson.QueryTtsPersonByNameAndUserId error:%v", logId, err)
			logger.Log.Errorf(failMsg)
			c.AbortWithStatusJSON(http.StatusOK, commProto.NewCommRsp(constant.CommCodeInternalErr, failMsg))
			return
		}

		// 遍历TtsPerson 表数据并根据id查询TTSClone 数据
		for _, ttsPerson := range ttsPersons {
			ttsClones, err := (&dao.TTSClone{}).QueryTtsCloneByMaterialIdAndAccountId(tx, logId, ttsPerson.ID, req.AccountId)
			if err != nil {
				fmt.Printf("查询ttsClone表失败,accountID:%s,error:%v\n", req.AccountId, err)
				continue
			}
			// 遍历TTSClone 将数据插入到data中
			for _, ttsClone := range ttsClones {
				data = append(data, *ttsClone)
			}
		}
	}
	err = tx.Commit().Error
	if err != nil {
		tx.Rollback()
		failMsg := fmt.Sprintf("logId=%s,音色查询,tx.Commit(),error:%v", logId, err)
		logger.Log.Errorf(failMsg)
		c.AbortWithStatusJSON(http.StatusOK, commProto.NewCommRsp(constant.CommCodeInternalErr, failMsg))
		return
	}
	logger.Log.Infof("logId:%s,音色查询 success", logId)
	c.AbortWithStatusJSON(http.StatusOK, commProto.NewSuccessRsp(data))

}

/*
*
工牌麦音色修改
*/
func TimbreModify(c *gin.Context) {
	logId := uuid.New().String()
	req := TimbreModifyReq{}
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Log.Errorf("logId=%s,TimbreModify 解析请求参数失败 error:%v", logId, err)
		c.AbortWithStatusJSON(http.StatusOK, commProto.NewCommRsp(constant.CommCodeParamErr, "参数解析异常"))
		return
	}
	if len(req.UserID) == 0 || len(req.AccountId) == 0 || len(req.Name) == 0 || len(req.Per) == 0 || len(req.ExampleText) == 0 {
		logger.Log.Errorf("logId=%s,TimbreModify 参数错误", logId)
		c.AbortWithStatusJSON(http.StatusOK, commProto.NewCommRsp(constant.CommCodeParamErr, "参数错误"))
		return
	}
	gormDB, ok := dao.GetTtsCloneDB()
	if !ok {
		logMsg := fmt.Sprintf("logId=%s,GetTtsCloneDB error", logId)
		logger.Log.Errorf(logMsg)
		c.AbortWithStatusJSON(http.StatusOK, commProto.NewCommRsp(constant.CommCodeInternalErr, logMsg))
		return
	}
	tx := gormDB.Begin()
	// 更新音色第二步，更新tts_person 表数据
	id, err := (&dao.TtsPerson{}).UpdataTtsPerson(tx, logId, req.UserID, req.Name, req.Gender, req.Per, req.Config, req.Extra, req.Describe, req.IsNatural)
	if err != nil {
		failMsg := fmt.Sprintf("logId=%s,TtsPerson.UpdataTtsPerson error:%v", logId, err)
		logger.Log.Errorf(failMsg)
		c.AbortWithStatusJSON(http.StatusOK, commProto.NewCommRsp(constant.CommCodeInternalErr, failMsg))
		return
	}
	// 更新音色第二步， 更新tts_clone表数据
	err = (&dao.TTSClone{}).UpdataTtsClone(tx, logId, id, req.Per, req.AccountId, req.Name, req.Describe, req.ExampleText, req.Status, req.Gender)
	if err != nil {
		tx.Rollback()
		failMsg := fmt.Sprintf("logId=%s,TTSClone.UpdataTtsClone error:%v", logId, err)
		logger.Log.Errorf(failMsg)
		c.AbortWithStatusJSON(http.StatusOK, commProto.NewCommRsp(constant.CommCodeInternalErr, failMsg))
		return
	}
	err = tx.Commit().Error
	if err != nil {
		tx.Rollback()
		failMsg := fmt.Sprintf("logId=%s,音色更新,tx.Commit(),error:%v", logId, err)
		logger.Log.Errorf(failMsg)
		c.AbortWithStatusJSON(http.StatusOK, commProto.NewCommRsp(constant.CommCodeInternalErr, failMsg))
		return
	}
	logger.Log.Infof("logId:%s,音色更新 success", logId)
	c.AbortWithStatusJSON(http.StatusOK, commProto.NewSuccessRsp(nil))
}

// hs声音增加语言标签
func HsTtsPersonAddLanTag() {
	// logId := uuid.New().String()
	// logger.Log.Infof("logId:%s,HsTtsPersonAddLanTag start", logId)
	// ttsPersons, err := (&dao.TtsPerson{}).QueryTtsPersonByHs(logId)
	// if err != nil {
	// 	logger.Log.Errorf("logId=%s,QueryTtsPersonByHs error:%v", logId, err)
	// 	return
	// }

	// tagCreateList := []*dao.MaterialTagMapping{}
	// for idx, ttsPerson := range ttsPersons {
	// 	logger.Log.Infof("logId=%s,range ttsPersons idx:%d,ttsPerson:%+v", logId, idx, ttsPerson)
	// 	tagCreateList = append(tagCreateList, &dao.MaterialTagMapping{MaterialID: ttsPerson.ID, TagID: 42, MaterialType: 0})
	// 	tagCreateList = append(tagCreateList, &dao.MaterialTagMapping{MaterialID: ttsPerson.ID, TagID: 43, MaterialType: 0})
	// 	tagCreateList = append(tagCreateList, &dao.MaterialTagMapping{MaterialID: ttsPerson.ID, TagID: 44, MaterialType: 0})

	// }
	// logger.Log.Infof("logId=%s,range tagCreateList.size:%d", logId, len(tagCreateList))
	// err = (&dao.MaterialTagMapping{}).Create(logId, tagCreateList)
	// if err != nil {
	// 	logger.Log.Errorf("logId:%s,MaterialTagMapping.Create(),error:%v", logId, err)
	// } else {
	// 	logger.Log.Errorf("logId:%s,MaterialTagMapping.Create(),success!", logId)
	// }
	// logger.Log.Infof("logId:%s,HsTtsPersonAddLanTag end", logId)

}
