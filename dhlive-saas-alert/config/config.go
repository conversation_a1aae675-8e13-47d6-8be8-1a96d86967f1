package config

import (
	"acg-ai-go-common/global"
	"acg-ai-go-common/logger"
	"flag"

	"github.com/penguinn/go-sdk/config"
)

type appConfig struct {
	DSN                        map[string]string       `mapstructure:"dsn"`
	RuLiuGroup                 ruLiuGroup              `mapstructure:"ruLiuGroup"`
	EsV6Config                 esV6Config              `mapstructure:"esV6Config"`
	VideoHangingTime           videoHangingTime        `mapstructure:"videoHangingTime"`
	RiskControTime             riskControlTime         `mapstructure:"riskControlTime"`
	VideoFailedTime            videoFailedTime         `mapstructure:"videoFailedTime"`
	TtsCloneFailedTime         ttsCloneFailedTime      `mapstructure:"ttsCloneFailedTime"`
	FissionVideoHangingTime    fissionVideoHangingTime `mapstructure:"fissionVideoHangingTime"`
	QuerySuccessDuration       querySuccessDuration    `mapstructure:"querySuccessDuration"`
	PodProxySettings           []PodProxySetting       `mapstructure:"podProxySettings"`
	HsSpeakerNotEnoughAlertNum int                     `mapstructure:"hsSpeakerNotEnoughAlertNum"`
	TtsStaticsSetting          ttsStaticsSetting       `mapstructure:"ttsstatics-setting"`
	FigureTrainHangingTime     figureTrainHangingTime  `mapstructure:"figureTrainHangingTime"`
	FigureTrainFailedTime      figureTrainFailedTime   `mapstructure:"figureTrainFailedTime"`
	CloudSessionFailed         cloudSessionFailed      `mapstructure:"cloudSessionFailed"`
}
type ruLiuGroup struct {
	GroupId                        int      `mapstructure:"groupId"`
	AccessToken                    string   `mapstructure:"accessToken"`
	AtBodyVideoTask                []string `mapstructure:"atBodyVideoTask"`                // 视频制作@的人的列表
	AtBodyPptCoursewareTask        []string `mapstructure:"atBodyPptCoursewareTask"`        // ppt课件@的人的列表
	AtBodyUeTask                   []string `mapstructure:"atBodyUeTask"`                   // ue主机@的人的列表
	AtBodyText2FigureTask          []string `mapstructure:"atBodyText2FigureTask"`          // 文生3D@的人的列表
	AtBodyVideoHangingTask         []string `mapstructure:"atBodyVideoHangingTask"`         // 任务夯住@的人的列表
	AtBodyTtsCloneTask             []string `mapstructure:"atBodyTtsCloneTask"`             // 声音克隆任务@的人的列表
	AtBodyHsSpeakerNotEnough       []string `mapstructure:"atBodyHsSpeakerNotEnough"`       // hs音色不足报警@的人的列表
	AtBodyRiskControlNonCompliance []string `mapstructure:"atBodyRiskControlNonCompliance"` // 风控审核不合规的报警@的人列表
	AtBodyRiskControlFailed        []string `mapstructure:"atBodyRiskControlFailed"`        // 风控任务失败的报警@的人列表
	AtBodyFigureTrain              []string `mapstructure:"atBodyFigureTrain"`              // 人像训练任务@的人的列表
	AtBodyVideoTranslate           []string `mapstructure:"atBodyVideoTranslate"`           // 视频翻译@的人的列表
}
type esV6Config struct {
	UseEs                bool   `string:"useEs"`
	EsV6Address          string `string:"esV6Address"`
	EsIngressLogAddress  string `stringmapstructure:"esIngressLogAddress"`
	EsIngressLogName     string `stringmapstructure:"esIngressLogName"`
	EsIngressLogPassword string `stringmapstructure:"esIngressLogPassword"`
}

type querySuccessDuration struct {
	SuccessDurationMinute int `mapstructure:"successDurationMinute"`
	QpsDurationSecond     int `mapstructure:"qpsDurationSecond"`
}
type videoHangingTime struct {
	FirstTimeHour                 int `mapstructure:"firstTimeHour"`
	LastTimeHour                  int `mapstructure:"lastTimeHour"`
	VideoTranslateHangingTimeHour int `mapstructure:"videoTranslateHangingTimeHour"`
}
type riskControlTime struct {
	HangingFirstTimeMin int `mapstructure:"hangingFirstTimeMin"`
	HangingLastTimeMin  int `mapstructure:"hangingLastTimeMin"`
	FailedTimeMin       int `mapstructure:"failedTimeMin"`
}
type fissionVideoHangingTime struct {
	FirstTimeHour int `mapstructure:"firstTimeHour"`
	LastTimeHour  int `mapstructure:"lastTimeHour"`
}

type figureTrainHangingTime struct {
	PictureFirstTimeMinute int `mapstructure:"pictureFirstTimeMinute"`
	PictureLastTimeMinute  int `mapstructure:"pictureLastTimeMinute"`
	V2FirstTimeMinute      int `mapstructure:"V2FirstTimeMinute"`
	V2LastTimeMinute       int `mapstructure:"V2LastTimeMinute"`
	V4FirstTimeHour        int `mapstructure:"V4FirstTimeHour"`
	V4LastTimeHour         int `mapstructure:"V4LastTimeHour"`
}

type figureTrainFailedTime struct {
	FigureFailedTimeDuration int `mapstructure:"figureFailedTimeDuration"` // 单位分钟
}

type videoFailedTime struct {
	VideoFailedTimeDuration int `mapstructure:"videoFailedDuration"` // 单位分钟
}

type cloudSessionFailed struct {
	CloudSessionFailedDuration int `mapstructure:"cloudSessionFailedDuration"` // 单位分钟
	CloudSessionThresholdCount int `mapstructure:"cloudSessionThresholdCount"` // 阈值
}

type ttsCloneFailedTime struct {
	TtsCloneFailedTimeDuration int `mapstructure:"ttsCloneFailedDuration"` // 单位分钟
}

type PodProxySetting struct {
	ServerName      string            `string:"serverName"`
	ServerNameSpace string            `string:"serverNameSpace"`
	ServerPort      string            `string:"serverPort"`
	ApiProxy        []ProxyApiSetting `json:"apiProxy"`
}

type ProxyApiSetting struct {
	ApiGroup      string   `string:"apiGroup"`
	ApiPostList   []string `json:"apiPostList"`
	ApiGetList    []string `json:"apiGetList"`
	ApiPutList    []string `json:"apiPutList"`
	ApiDeleteList []string `json:"apiDeleteList"`
}

type ttsStaticsSetting struct {
	Url         string `string:"url"`
	CallTimeOut int    `json:"callTimeOut"`
}

// 每日数据统计
type DailyTotal struct {
	Date  string `gorm:"column:date"`
	Count int    `gorm:"column:count"`
}

var AppConfig = appConfig{}

var (
	configPath     string
	configFileType string
)

func Init() error {
	flag.StringVar(&configFileType, "t", "yaml", "config file type")
	flag.StringVar(&configPath, "f", "./conf/config.yaml", "config file path")
	flag.Parse()

	if global.ServerSetting.RunEnv == global.DevEnv {
		configPath = "./deploy/conf/config.yaml"
	}

	logger.Log.Infof("configPath: %s", configPath)
	// 优先级，环境变量 > 文件配置 > 默认配置
	err := config.Init(configFileType, configPath, &AppConfig)
	logger.Log.Infof("appConfig: %v", AppConfig)
	logger.Log.Infof("groupId: %v,accessToken: %v", AppConfig.RuLiuGroup.GroupId, AppConfig.RuLiuGroup.AccessToken)
	if err != nil {
		return err
	}
	return nil
}
