package essearch

import (
	"acg-ai-go-common/logger"
	"context"
	"dhlive-saas-alert/constant"
	"encoding/json"
	"fmt"
	"strings"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm/utils"

	"github.com/elastic/go-elasticsearch/v6/esapi"
	"github.com/elastic/go-elasticsearch/v6/esutil"
)

const (
	CommCodeParamErr = 100001
	CommMsgParamErr  = "参数错误"
)

// ServerInfo 日志数据服务端信息
type ServerInfo struct {
	ServerName string `json:"serverName,omitempty" bson:"serverName,omitempty"` // 服务名称
	HostName   string `json:"hostName,omitempty" bson:"hostName,omitempty"`     // 服务器HostName
	IPAddr     string `json:"ipAddr,omitempty" bson:"ipAddr,omitempty"`         // IP地址
}

type EsSearchResult struct {
	ScrollId string `json:"_scroll_id,omitempty"` // 滚动ID，使用滚动查询时需要
	Hits     EsHits `json:"hits"`
}

type EsHits struct {
	Total      int64       `json:"total"`
	Size       int         `json:"size"`
	RelatedIds string      `json:"relatedIds"`
	MaxScore   float64     `json:"max_score,omitempty"`
	Hits       []EsHitsSub `json:"hits"`
}

type EsHitsSub struct {
	Index  string      `json:"_index"`
	Type   string      `json:"_type"`
	Id     string      `json:"_id"`
	Source EsHitSource `json:"_source"`
}

type EsHitSource struct {
	Level     string `json:"level,omitempty"`
	Pid       string `json:"pid,omitempty"`
	Thread    string `json:"thread,omitempty"`
	Class     string `json:"class,omitempty"`
	Method    string `json:"method,omitempty"`
	Message   string `json:"message,omitempty"`
	Exception string `json:"exception,omitempty"`
	Container string `json:"container,omitempty"`
	Timestamp string `json:"@timestamp,omitempty"`
	// 以上java日志的字段，以下为go日志的字段
	Host        string      `json:"Host,omitempty"`
	MessageJava string      `json:"Message,omitempty"`
	Data        interface{} `json:"Data,omitempty"`
}

func CheckAuthHandler(c *gin.Context) {

}

func getEsQueryForFission(fissionParam *EsSearchFissionParam) (query []interface{}) {
	logger.Log.Infof("EsSearch.getEsQueryForFission,fissionParam:%v", fissionParam)

	var queryFilter []interface{}

	var splitVideoId string
	parts := strings.Split(fissionParam.VideoId, "-")
	if len(parts) > 1 {
		splitVideoId = parts[1]
	} else {
		splitVideoId = fissionParam.VideoId
	}

	var taskIdStr string
	for _, taskId := range fissionParam.TaskIds {
		parts := strings.Split(taskId, "-")
		if len(parts) > 1 {
			taskId = parts[1]
		}
		taskIdStr = taskIdStr + " " + taskId
	}

	var videoSubIdStr string
	for _, videoSubId := range fissionParam.VideoSubIds {
		parts := strings.Split(videoSubId, "-")
		if len(parts) > 1 {
			videoSubId = parts[1]
		}
		videoSubIdStr = videoSubIdStr + " " + videoSubId
	}

	var eidtIdStr string
	for _, eidtId := range fissionParam.EditIds {
		parts := strings.Split(eidtId, "-")
		if len(parts) > 1 {
			eidtId = parts[1]
		}
		eidtIdStr = eidtIdStr + " " + eidtId
	}

	var videoPipeLineIdStr string
	for _, videoPipeLineId := range fissionParam.VideoPiplelineIds {
		parts := strings.Split(videoPipeLineId, "-")
		if len(parts) > 1 {
			videoPipeLineId = parts[1]
		}
		videoPipeLineIdStr = videoPipeLineIdStr + " " + videoPipeLineId
	}

	var videoPipeSessionIdStr string
	for _, sessionId := range fissionParam.VideoPiplelineSessionIds {
		videoPipeSessionIdStr = videoPipeSessionIdStr + " " + sessionId
	}
	queryStr := splitVideoId + " " + taskIdStr+ " " + videoSubIdStr + " " + eidtIdStr + " " + videoPipeLineIdStr + " " + videoPipeSessionIdStr

	if len(queryStr) > 0 {
		// go和Java两种索引类型种的字段名不同，message 和 Message 所以查询语句都要支持
		queryFilter = append(queryFilter, map[string]interface{}{
			"match": map[string]interface{}{
				"message": queryStr,
			},
		})
		queryFilter = append(queryFilter, map[string]interface{}{
			"match": map[string]interface{}{
				"Message": queryStr,
			},
		})
	}
	return queryFilter
}

func getEsQuery(searchParam *EsSercehParam) (query []interface{}) {
	logger.Log.Infof("EsSearch.getEsQuery,searchParam:%v", searchParam)
	var queryFilter []interface{}
	if len(searchParam.VideoId) > 0 {
		// go和Java两种索引类型种的字段名不同，message 和 Message 所以查询语句都要支持
		queryFilter = append(queryFilter, map[string]interface{}{
			"match_phrase": map[string]interface{}{
				"message": searchParam.VideoId,
			},
		})
		queryFilter = append(queryFilter, map[string]interface{}{
			"match_phrase": map[string]interface{}{
				"Message": searchParam.VideoId,
			},
		})
	}

	if len(searchParam.TaskId) > 0 {
		queryFilter = append(queryFilter, map[string]interface{}{
			"match_phrase": map[string]interface{}{
				"message": searchParam.TaskId,
			},
		})
		queryFilter = append(queryFilter, map[string]interface{}{
			"match_phrase": map[string]interface{}{
				"Message": searchParam.TaskId,
			},
		})
	}

	if len(searchParam.VideoSubId) > 0 {
		queryFilter = append(queryFilter, map[string]interface{}{
			"match_phrase": map[string]interface{}{
				"message": searchParam.VideoSubId,
			},
		})
		queryFilter = append(queryFilter, map[string]interface{}{
			"match_phrase": map[string]interface{}{
				"Message": searchParam.VideoSubId,
			},
		})
	}

	if len(searchParam.EditID) > 0 {
		queryFilter = append(queryFilter, map[string]interface{}{
			"match_phrase": map[string]interface{}{
				"message": searchParam.EditID,
			},
		})
		queryFilter = append(queryFilter, map[string]interface{}{
			"match_phrase": map[string]interface{}{
				"Message": searchParam.EditID,
			},
		})
	}

	if len(searchParam.VideoPiplelineIds) > 0 {
		for _, pplineId := range searchParam.VideoPiplelineIds {
			parts := strings.Split(pplineId, "-")
			pplineId = parts[1]
			queryFilter = append(queryFilter, map[string]interface{}{
				"match_phrase": map[string]interface{}{
					"message": pplineId,
				},
			})
			queryFilter = append(queryFilter, map[string]interface{}{
				"match_phrase": map[string]interface{}{
					"Message": pplineId,
				},
			})
		}
	}

	if len(searchParam.VideoPiplelineSessionIds) > 0 {
		for _, sessionId := range searchParam.VideoPiplelineSessionIds {
			if len(sessionId) > 0 {
				queryFilter = append(queryFilter, map[string]interface{}{
					"match_phrase": map[string]interface{}{
						"message": sessionId,
					},
				})
				queryFilter = append(queryFilter, map[string]interface{}{
					"match_phrase": map[string]interface{}{
						"Message": sessionId,
					},
				})
			}
		}
	}

	if len(searchParam.PPT2IamgeTaskId) > 0 {
		queryFilter = append(queryFilter, map[string]interface{}{
			"match_phrase": map[string]interface{}{
				"message": searchParam.PPT2IamgeTaskId,
			},
		})
		queryFilter = append(queryFilter, map[string]interface{}{
			"match_phrase": map[string]interface{}{
				"Message": searchParam.PPT2IamgeTaskId,
			},
		})
	}
	return queryFilter
}

func (l *EsSearchResult) SearchLogForFission(fissionParam *EsSearchFissionParam) (*EsSearchResult, error) {
	logger.Log.Infof("EsSearch.SearchLogForFission.start,searchParam:%v", fissionParam)
	queryFilter := getEsQueryForFission(fissionParam)

	esParam := &EsSercehParam{
		VideoId:          fissionParam.VideoId,
		AscSort:          fissionParam.AscSort,
		AllIndices:       fissionParam.AllIndices,
		EsIndicesKeyWord: fissionParam.EsIndicesKeyWord,
	}
	result, error := l.handelEsSearch(esParam, queryFilter)
	return result, error
}

func (l *EsSearchResult) SearchLog(searchParam *EsSercehParam) (*EsSearchResult, error) {
	result, error := l.handelEsSearch(searchParam, nil)
	return result, error
}

func (l *EsSearchResult) handelEsSearch(searchParam *EsSercehParam, queryInterface []interface{}) (*EsSearchResult, error) {
	logger.Log.Infof("EsSearch.handelEsSearch.start,searchParam:%v,queryInterface:%v", searchParam, queryInterface)
	var queryFilter []interface{}
	// 如果不传 queryInterface 可从searchParam中生成
	if queryInterface == nil {
		queryFilter = getEsQuery(searchParam)
	} else {
		queryFilter = queryInterface
	}
	// 构建查询语句
	query := map[string]interface{}{
		"query": map[string]interface{}{
			"bool": map[string]interface{}{
				"should":               queryFilter,
				"minimum_should_match": 1, // 至少匹配一个关键词
			},
		},
	}

	// 所需要查询的索引名称的切片
	var indicesNeedNames []string

	// 查询所有索引
	if searchParam.AllIndices {
		indicesNeedNames = []string{"*"}
	} else {
		// 如果不查询全部索引，则只查询包含videoId editId pipelineId中的索引
		res, err := EsClientV6.Cat.Indices(
			EsClientV6.Cat.Indices.WithContext(context.Background()),
			EsClientV6.Cat.Indices.WithFormat("json"),
		)
		if err != nil {
			errOne := fmt.Errorf("videoId=%s,EsClientV6.Cat.getIndices,error: %s", searchParam.VideoId, err)
			logger.Log.Error(errOne.Error())
			return nil, errOne
		}
		defer res.Body.Close()

		// 所有索引列表
		var indices []map[string]interface{}
		if err := json.NewDecoder(res.Body).Decode(&indices); err != nil {
			errOne := fmt.Errorf("videoId=%s,json.Decode(&indices),error: %s", searchParam.VideoId, err)
			logger.Log.Error(errOne.Error())
			return nil, errOne
		}

		// 所有索引名称的列表
		var indicesNameSlice []string
		for _, index := range indices {
			indexName := index["index"].(string)
			indicesNameSlice = append(indicesNameSlice, indexName)
		}
		logger.Log.Infof("videoId=%s,EsSearch All indices.size:%v", searchParam.VideoId, len(indices))

		// 获取包含videoId editId pipelineId的索引的关键字的索引  放入indicesNeedNames中
		for _, indiceName := range indicesNameSlice {
			if !utils.Contains(indicesNeedNames, indiceName) {
				// 获取对应视频类型的索引
				if len(searchParam.VideoId) > 0 && len(searchParam.EsIndicesKeyWord) > 0 && strings.Contains(indiceName, searchParam.EsIndicesKeyWord) {
					indicesNeedNames = append(indicesNeedNames, indiceName)
				}
				// 添加videoPipeLine的索引
				if strings.Contains(indiceName, constant.VideoPipeLineIndicesKeyWord) {
					indicesNeedNames = append(indicesNeedNames, indiceName)
				}
				// 添加edit相关的索引
				if strings.Contains(indiceName, constant.EditIdIndicesKeyWord) {
					indicesNeedNames = append(indicesNeedNames, indiceName)
				}
			}
		}
		logger.Log.Infof("videoId=%s,EsSearch,needIndices.size:%v,needIndices:%v", searchParam.VideoId, len(indicesNeedNames), indicesNeedNames)
		// 如果没有匹配到索引，则查询所有索引
		if len(indicesNeedNames) == 0 {
			indicesNeedNames = []string{"*"}
		}
	}
	logger.Log.Infof("videoId=%s,EsSearch indicesNeedNames:%v", searchParam.VideoId, indicesNeedNames)

	searchRequest := esapi.SearchRequest{
		Index: indicesNeedNames,
		Body:  esutil.NewJSONReader(query),
		From:  esapi.IntPtr(int(0)),
		Size:  esapi.IntPtr(int(10000)),
		Sort:  []string{"@timestamp:desc"},
	}
	if searchParam.AscSort {
		searchRequest.Sort = []string{"@timestamp:asc"}
	}
	logger.Log.Infof("videoId=%s,es.Search(),default,Index:%v,query:%v", searchParam.VideoId, searchRequest.Index, query)
	searchResponse, err := searchRequest.Do(context.TODO(), EsClientV6)
	if err != nil {
		errOne := fmt.Errorf("videoId=%s,searchRequest.Do,error: %s", searchParam.VideoId, err)
		logger.Log.Error(errOne.Error())
		return nil, errOne
	}
	defer searchResponse.Body.Close()
	if searchResponse.IsError() {
		errOne := fmt.Errorf("videoId=%s,searchRequest.Do,searchResponse.IsErrorr,searchResponse:%s", searchParam.VideoId, searchResponse.String())
		logger.Log.Error(errOne.Error())
		return nil, errOne
	}

	logger.Log.Infof("videoId=%s,es.Search(),searchResponse.status:%s", searchParam.VideoId, searchResponse.Status())
	responseBody := searchResponse.Body
	// logger.Log.Infof("videoId=%s,es.Search(),responseBody:%s", searchParam.VideoId, responseBody)

	var searchResult *EsSearchResult
	if err = json.NewDecoder(responseBody).Decode(&searchResult); err != nil {
		errOne := fmt.Errorf("videoId=%s,es.Search(),searchResult,json.NewDecoder(responseBody):%v", searchParam.VideoId, err)
		logger.Log.Error(errOne.Error())
		return nil, errOne
	}
	if searchResult != nil {
		searchResult.Hits.Size = len(searchResult.Hits.Hits)
	}

	logger.Log.Infof("videoId=%s,es.Search(),searchResult.size:%v", searchParam.VideoId, searchResult.Hits.Size)
	return searchResult, nil
}
