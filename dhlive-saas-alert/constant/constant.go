package constant

const (
	DBVideoProduct      = "videoproduct"      // 视频生产相关的库
	DBVideoProductWrite = "videoproductwrite" // 视频生产相关的库,可写
	DBText2Figure       = "text2figure"       // 文生3D相关的库
	DBppt2Image         = "ppt2image"         // PPT课件视频相关的库
	DBVideoPipeLine     = "videopipeline"     // PPT课件视频相关的库
	DBStarLight         = "starlight"         // Vis2D人像等信息所在库
	BDPlat              = "plat"              // A2A人像等信息所在库
	DBTtsClone          = "ttsclone"          // tts声音克隆相关的库
	DHUserAndAccount    = "accountanduser"    // 用户id相关
	DHCloudSaas         = "cloudsaas"         // digital_human_cloud_saas 库
)

// 视频相关的ID的前缀
const (
	EcardVideoIdPrefix      = "cv-"
	FissionVideoIdPrefix    = "fv-"
	SlideVideoIdPrefix      = "sv-"
	TwinVideoIdPrefix       = "v-"
	VideoPipeLineIdPrefix   = "vp-"
	PPT2ImageTaskIdPrefix   = "fid-"
	CoursewareVideoIdPrefix = "ov-"
)

// 视频相关的es的索引关键字
const (
	EcardVideoIndicesKeyWord    = "ecard"
	FissionVideoIndicesKeyWord  = "mhe-studio"
	SlideVideoIdIndicesKeyWord  = "slide"
	TwinVideoIdIndicesKeyWord   = "twin"
	VideoPipeLineIndicesKeyWord = "pipeline"
	Ppt2ImageLineIndicesKeyWord = "dhlive-external"
	CoursewareIndicesKeyWord    = "courseware" // 暂未确定
	EditIdIndicesKeyWord        = "mhe-center"
)

// 人像训练类型
const (
	Figure_2D_LITE_VIS_V4      = "2D_LITE_VIS_V4"      // V4 精品人像  4小时内
	Figure_2D_LITE_VIS_V2      = "2D_LITE_VIS"         // v2 极速人像  30分钟
	Figure_2D_LITE_VIS_PICTURE = "2D_LITE_VIS_PICTURE" // 照片 秒级生成
)

// 通用错误码和消息
const (
	CommCodeParamErr = 100001
	CommMsgParamErr  = "参数错误"

	CommCodeInternalErr = 100002
	CommMsgInaternalErr = "服务内部错误"

	TimbreActionCopy    = "COPY"
	TimbreActionMigrate = "MIGRATE"

	HsStartId = 2000000001
)

// 视频翻译的整体状态
const (
	VideoStatus_Waiting = "WAITING"
	VideoStatus_Running = "RUNNING"
	VideoStatus_Success = "SUCCESS"
	VideoStatus_Failed  = "FAILED"
)

// 调用heygen翻译的状态
const (
	TransStatus_WaitingRiskControl   = "waiting_risk_control"   // 待风控
	TransStatus_WaitingProofread     = "waiting_proofread"      // 待校对
	TransStatus_Proofreading         = "proofreading"           // 校对中
	TransStatus_ProofreadFailed      = "proofread_failed"       // 校对失败
	TransStatus_VideoGenerating      = "video_generating"       // 视频生成中
	TransStatus_VideoGenerateSuccess = "video_generate_success" // 视频生成成功
	TransStatus_VideoGenerateFailed  = "video_generate_failed"  // 视频生成失败
)
