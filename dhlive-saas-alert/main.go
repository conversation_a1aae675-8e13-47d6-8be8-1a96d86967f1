package main

import (
	"acg-ai-go-common/logger"
	"acg-ai-go-common/server"
	"dhlive-saas-alert/config"
	"dhlive-saas-alert/dao"
	"dhlive-saas-alert/essearch"
	"dhlive-saas-alert/handler"
	"dhlive-saas-alert/routers"
)

func main() {
	// 初始化公共配置
	server.InitGlobalSetting()
	// 初始化日志
	logger.SetLogger()
	logger.Log.Info("Init logger success!")
	// 初始化配置
	errOne := config.Init()
	if errOne != nil {
		panic(errOne)
	}
	logger.Log.Info("config.yaml Init success!")
	// 初始化mysql
	errTwo := dao.InitMysql()
	if errTwo != nil {
		panic(errTwo)
	}
	logger.Log.Info("InitMysql success!")
	// 初始化es
	if config.AppConfig.EsV6Config.UseEs {
		errThree := essearch.InitEsV6Client()
		if errThree != nil {
			panic(errThree)
		} else {
			logger.Log.Info("InitEsV6 client success!")
		}
	}

	// 初始化定时任务
	handler.InitScheduleTask()
	logger.Log.Info("InitScheduleTask success!")

	// 初始化路由
	routers.InitRouter()
	logger.Log.Info("Init Router success!")
	// 启动服务
	server.Run(routers.GinRouter)

}
