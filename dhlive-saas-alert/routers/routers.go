package routers

import (
	"acg-ai-go-common/beans/proto"
	"acg-ai-go-common/global"
	"acg-ai-go-common/logger"
	"dhlive-saas-alert/apiproxy"
	"dhlive-saas-alert/config"
	"dhlive-saas-alert/essearch"
	"dhlive-saas-alert/handler"
	"dhlive-saas-alert/handler/activity"
	"dhlive-saas-alert/handler/statistics"
	"dhlive-saas-alert/handler/videoops"
	"dhlive-saas-alert/videoquery"
	"net/http"

	"github.com/gin-gonic/gin"
)

func HealthRouter(e *gin.Engine) {
	// 健康检查
	url := "/health"
	if global.ServerSetting.ConsulSetting != nil && len(global.ServerSetting.ConsulSetting.HealthUrl) > 0 {
		url = global.ServerSetting.ConsulSetting.HealthUrl
	}
	e.GET(url, handler.HealthHandler)
}

func Routers(e *gin.Engine) {
	{
		uriGroup := e.Group("/dhlive-saas-alert/log-search/v1", essearch.CheckAuthHandler)
		// 服务端日志查询
		uriGroup.POST("/server/log/search", CheckUserMiddleware, essearch.SearchServerLogHandler)
	}
	{
		uriGroup := e.Group("/dhlive-saas-alert/video-task/v1", videoquery.CheckAuthHandler)
		// 查询一段时间内成功的视频任务
		uriGroup.GET("/video/success/query", CheckUserMiddleware, videoquery.QuerySuccessVideoInDuraion)
	}
	{
		statisticsGroup := e.Group("/api/v1/manager/statistics")
		// 查询一段时间内视频中人像使用信息
		statisticsGroup.GET("/video/figure/detail", CheckUserMiddleware, statistics.GetVideoFigureUseDetail)
		// 查询一段时间内视频中音色使用信息
		statisticsGroup.GET("/video/timbre/detail", CheckUserMiddleware, statistics.GetVideoTimbreUseDetail)
	}
	{
		uriGroup := e.Group("/dhlive-saas-alert/timbre-manage/v1", CheckUserMiddleware, videoquery.CheckAuthHandler)
		// 音色迁移
		uriGroup.POST("/timbre/migrate", CheckUserMiddleware, handler.TimbreMigrate)
		// 工牌麦音色新增
		uriGroup.POST("/timbre/insert", CheckUserMiddleware, handler.TimbreInsert)
		// 工牌麦音色修改
		uriGroup.POST("/timbre/modify", CheckUserMiddleware, handler.TimbreModify)
		//// 工牌麦音色列表查询
		uriGroup.POST("/timbre/query", CheckUserMiddleware, handler.TimbreQuery)
	}
	{
		// AI加速器   AI迎春活动接口
		accountStatusGroup := e.Group("/api/dhlive-saas-alert/account/v1")
		accountStatusGroup.Use(Cors())
		// 查询账号注册信息
		accountStatusGroup.GET("/status", CheckUserMiddleware, handler.QueryAccountStatus)
	}
	// tts统计
	{
		ttsStaticsGroup := e.Group("/dhlive-saas-alert/tts-statics")
		ttsStaticsGroup.POST("/callcount", CheckUserMiddleware, statistics.GetTtsCallStatics)
	}

	// SLA统计
	{
		slaStaticsGroup := e.Group("/dhlive-saas-alert/sla-statics/v1")
		slaStaticsGroup.GET("/video/production/statics", CheckUserMiddleware, statistics.GetVideoProductionSLA)
		slaStaticsGroup.GET("/interface/invoke/statics", CheckUserMiddleware, essearch.GetIngressInterfaceSLA)
	}

	// 活动banner管理
	{
		activityBannerGroup := e.Group("/dhlive-saas-alert/activity-manager/v1")
		// 新增
		activityBannerGroup.POST("/homepage/activity/banner", CheckUserMiddleware, activity.Create)
		// 编辑
		activityBannerGroup.PUT("/homepage/activity/banner", CheckUserMiddleware, activity.Edit)
		// 删除
		activityBannerGroup.DELETE("/homepage/activity/banner", CheckUserMiddleware, activity.Delete)
		// 查询
		activityBannerGroup.POST("/homepage/activity/banner/list", CheckUserMiddleware, activity.ListHomeActivityBanner)
	}

	// 视频任务查询
	{
		videoops.RoutersInit(e)
	}

	// 代理转发
	for _, pods := range config.AppConfig.PodProxySettings {
		for _, proxy := range pods.ApiProxy {
			proxyGroup := e.Group(proxy.ApiGroup)
			for _, path := range proxy.ApiPostList {

				proxyGroup.POST(
					path,
					CheckUserMiddleware,
					WithProxyHost(pods.ServerName, pods.ServerNameSpace, pods.ServerPort),
					apiproxy.ApiProxyHandler,
				)
			}
			for _, path := range proxy.ApiGetList {
				proxyGroup.GET(
					path,
					CheckUserMiddleware,
					WithProxyHost(pods.ServerName, pods.ServerNameSpace, pods.ServerPort),
					apiproxy.ApiProxyHandler,
				)
			}
			for _, path := range proxy.ApiPutList {
				proxyGroup.PUT(
					path,
					CheckUserMiddleware,
					WithProxyHost(pods.ServerName, pods.ServerNameSpace, pods.ServerPort),
					apiproxy.ApiProxyHandler,
				)
			}
			for _, path := range proxy.ApiDeleteList {
				proxyGroup.DELETE(
					path,
					CheckUserMiddleware,
					WithProxyHost(pods.ServerName, pods.ServerNameSpace, pods.ServerPort),
					apiproxy.ApiProxyHandler,
				)
			}
		}
	}
}

func Cors() gin.HandlerFunc {
	return func(c *gin.Context) {
		method := c.Request.Method

		c.Header("Access-Control-Allow-Origin", "https://cloud.baidu.com")
		c.Header("Access-Control-Allow-Headers", "Content-Type,AccessToken,X-CSRF-Token, Authorization, Token")
		c.Header("Access-Control-Allow-Methods", "POST, GET, OPTIONS")
		c.Header("Access-Control-Expose-Headers", "Content-Length, Access-Control-Allow-Origin, Access-Control-Allow-Headers, Content-Type")
		c.Header("Access-Control-Allow-Credentials", "true")

		//放行所有OPTIONS方法
		if method == "OPTIONS" {
			c.AbortWithStatus(http.StatusNoContent)
		}
		// 处理请求
		c.Next()
	}
}

func WithProxyHost(serverName, serverNameSpace, serverPort string) gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Set(apiproxy.ContextProxyServerName, serverName)
		c.Set(apiproxy.ContextProxyServerNameSpace, serverNameSpace)
		c.Set(apiproxy.ContextProxyServerPort, serverPort)
		baseUrl := "http://" + serverName + "." + serverNameSpace + ":" + serverPort
		c.Set(apiproxy.ContextProxyBaseUrl, baseUrl)
		logger.Log.Infof("proxy base url:%v", baseUrl)
		c.Next()
	}
}

// 添加爱速撘鉴权
func CheckUserMiddleware(c *gin.Context) {
	managerUserName := c.GetHeader("x-manager-user-name")
	logger.CtxLog(c).Infof("checkInstanceMiddleware GetHeader x-manager-user-name: %v", managerUserName)
	//查询instanceName
	if managerUserName == "" {
		c.AbortWithStatusJSON(http.StatusOK, proto.NewCommRsp(400001, "权限不足"))
		return
	}
	c.Next()
}
