// AgentService.proto
syntax = "proto3";

package com.baidu.acg.pie;

enum UploadStatus {
    NOT_START = 0;
    PROCESSING = 1;
    FAILED = 2;
    SUCCEED = 3;
}

message Response {
    bool success = 1;
    string message = 2;
    UploadStatus uploadStatus = 3; // 上传状态
    string videoDownloadUrl = 4; // 视频下载地址
    int64 video_size = 5; // 合成的视频大小
    string audioDownloadUrl = 6; // 音频下载地址
    string thumbnailUrl = 7; // 缩略图下载地址
}

// 分辨率
message Resolution {
    uint32 width = 1; // 水平像素点个数
    uint32 height = 2; // 垂直像素点个数
}

message X264Frame {
    bytes payload = 1;
    string video_id = 2; // video/session id
    string frame_id = 3;
    bytes audio = 4;
    string app_id = 5;
    string video_name = 6;
    string directory = 7;

    map<string, string> parameter = 12;
    bool flag_write_down = 30;
}

message Video {
    bytes image = 1;
    bytes audio = 2;
    uint32 image_width = 3;
    uint32 image_height = 4;
    uint32 serial_num = 5;
    uint32 logid = 6;
    uint32 template_id = 7;
    string id = 8; // 视频流id
    string character_image_type = 9; // 人像类型，china_mobile 或 system等
    string background_url = 10; // 背景url

    Resolution resolution = 11;
    string app_id = 12;
    string video_name = 13;
    string directory = 14;

    bool flag_write_down = 30; // 是否要求生成视频下载地址
}

message Image {
    string image_id = 1; // sessionId_replyId
    string image_name = 2;
    string directory = 3;
    bytes data = 4;
}

message AudioConfig {
    uint32 sample_rate = 1;
    uint32 channel_num = 2;
    uint32 bit_depth = 3;
}

message InitialRequest {
    AudioConfig audio_config = 1;
    string app_id = 2;

    string video_format = 3;
}

service UploadService {
    rpc video (stream Video) returns (stream Response) {
    };

    rpc x264 (stream X264Frame) returns (stream Response) {
    };

    rpc x264InLive (stream X264Frame) returns (stream Response) {
    };

    rpc image (Image) returns (Response) {
    };
}