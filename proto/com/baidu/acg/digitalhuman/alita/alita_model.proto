syntax = "proto3";

package com.baidu.acg.digitalhuman.alita;

import "com/baidu/acg/digitalhuman/common/common_model.proto";

option java_multiple_files = true;
option java_package = "com.baidu.acg.piat.digitalhuman.alita.model";

message RtcConnection {
    string serverUrl = 1;
    string appId = 2;
    string roomName = 3;
    string clientId = 4;
    string clientToken = 5;
    string feedId = 6;
    string chargeId = 7;
    string chargeToken = 8;
    // for internal server url
    string internalServerUrl = 9;
}

message HistoryDialogs {
    string sessionId = 1;
    int64 startTimeMs = 2;
    string Dialogs = 3;
    string sort=4;
    int32 page=5;
    int32 pageSize=6;
}

message ChatMessage {
    enum ChatEvent {
        UNKNOWN = 0;
        REGISTER = 1;
        UNREGISTER = 2;
        CHARGE = 3;
        DISCHARGE = 4;
        CHAT = 5;
        CLOSE = 6;
        RULE_REGISTER = 7;
        FACE_CHARGE = 8;
        VOICE_CHARGE = 9;
        APPLE_ARKIT_CHARGE = 10;
        STAFF_RTC_CHARGE = 11;
        CHARGE_RULE_HIT = 12;
        ACTION_RECOG = 13;
        MOCAP_CHARGE = 14;
        SUPERVISE_CHARGE = 15;
        FORCE_CHARGE = 16;
        UN_FORCE_CHARGE = 17;
        VIS_MOCAP_CHARGE = 18;
        ERROR = 19;
        HISTORY_DIALOGS = 20;
    }
    ChatEvent event = 1;
    string sessionId = 2;
    RtcConnection connection = 3;
    string chargeToken = 4;
    com.baidu.acg.digitalhuman.common.Dialog dialog = 5;
    string leafletWebSocketSessionId = 6;
    string chargeMessage = 8;
    string dialog_id = 9;
    map<string, string> connect_parameters = 10;
    HistoryDialogs historyDialogs = 11;
}
