package com.baidu.acg.piat.digitalhuman.plat.client;

import com.baidu.acg.piat.digitalhuman.common.callback.CallbackTaskCreateRequest;
import com.baidu.acg.piat.digitalhuman.common.callback.CallbackTaskCreateResponse;
import com.baidu.acg.piat.digitalhuman.common.model.Response;
import retrofit2.Call;
import retrofit2.http.Body;
import retrofit2.http.POST;

public interface CallbackService {
    @POST("/api/digitalhuman/plat/callback/submit")
    Call<Response<CallbackTaskCreateResponse>> submit(@Body CallbackTaskCreateRequest request);
}
