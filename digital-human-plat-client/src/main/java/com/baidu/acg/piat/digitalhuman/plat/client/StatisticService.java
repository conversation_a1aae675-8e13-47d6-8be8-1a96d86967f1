package com.baidu.acg.piat.digitalhuman.plat.client;

import com.baidu.acg.piat.digitalhuman.common.model.PageResult;
import com.baidu.acg.piat.digitalhuman.common.model.Response;
import com.baidu.acg.piat.digitalhuman.common.statistic.CharacterPreferencesData;
import com.baidu.acg.piat.digitalhuman.common.statistic.CharacterResourceResponse;
import com.baidu.acg.piat.digitalhuman.common.statistic.CharacterUsageTrendData;
import com.baidu.acg.piat.digitalhuman.common.statistic.ProductionStatisticData;
import com.baidu.acg.piat.digitalhuman.common.statistic.SessionStatisticPageResult;
import com.baidu.acg.piat.digitalhuman.common.statistic.StatisticResponse;
import retrofit2.Call;
import retrofit2.http.GET;
import retrofit2.http.Query;

import java.util.List;

public interface StatisticService {

    @GET("/api/digitalhuman/v1/statistic/activeSession")
    Call<Response<StatisticResponse>> getCurrentSession(@Query(value = "appId") String appId,
                                                        @Query(value = "startInMs") long startInMs,
                                                        @Query(value = "endInMs") long endInMs);

    @GET("/api/digitalhuman/v1/statistic/totalSession")
    Call<Response<StatisticResponse>> getTotalSession(@Query(value = "appId") String appId,
                                                      @Query(value = "startInMs") long startInMs,
                                                      @Query(value = "endInMs") long endInMs);

    @GET("/api/digitalhuman/v1/statistic/avgSessionTime")
    Call<Response<StatisticResponse>> getAvgSessionTime(@Query(value = "appId") String appId,
                                                        @Query(value = "startInMs") long startInMs,
                                                        @Query(value = "endInMs") long endInMs);

    @GET("/api/digitalhuman/v1/statistic/totalDialog")
    Call<Response<StatisticResponse>> getTotalDialog(@Query(value = "appId") String appId,
                                                     @Query(value = "startInMs") long startInMs,
                                                     @Query(value = "endInMs") long endInMs);

    @GET("/api/digitalhuman/v1/statistic/sessionTrend")
    Call<Response<StatisticResponse>> getSessionTrend(@Query(value = "appId") String appId,
                                                      @Query(value = "startInMs") long startInMs,
                                                      @Query(value = "endInMs") long endInMs,
                                                      @Query(value = "intervalInSeconds") long intervalInSeconds);


    @GET("/api/digitalhuman/v1/statistic/characterResource")
    Call<Response<CharacterResourceResponse>> characterResource(@Query(value = "platCode") String platCode,
                                                                @Query(value = "accountId") String accountId,
                                                                @Query(value = "roleLevel") int roleLevel,
                                                                @Query(value = "accountVisibleCharacters")
                                                                String accountVisibleCharacters);

    @GET("/api/digitalhuman/v1/statistic/characterUsageTrend")
    Call<Response<List<CharacterUsageTrendData>>> characterUsageTrend(@Query(value = "platCode") String platCode,
                                                                      @Query(value = "startInMs") long startInMs,
                                                                      @Query(value = "endInMs") long endInMs,
                                                                      @Query(value = "intervalInSeconds")
                                                                      long intervalInSeconds,
                                                                      @Query(value = "accountId") String accountId,
                                                                      @Query(value = "roleLevel") int roleLevel,
                                                                      @Query(value = "accountVisibleCharacters")
                                                                      String accountVisibleCharacters);

    @GET("/api/digitalhuman/v1/statistic/characterPreferences")
    Call<Response<List<CharacterPreferencesData>>> characterPreferences(@Query(value = "platCode") String platCode
            , @Query(value = "accountId") String accountId
            , @Query(value = "roleLevel") int roleLevel
            , @Query(value = "accountVisibleCharacters") String accountVisibleCharacters
            , @Query(value = "accountMenus") List<String> accountMenus);

    @GET("/api/digitalhuman/v1/statistic/sessionService")
    Call<Response<SessionStatisticPageResult>> sessionService(
            @Query(value = "platCode") String platCode
            , @Query(value = "accountId") String accountId
            , @Query(value = "roleLevel") int roleLevel
            , @Query(value = "pageNo") Integer pageNo
            , @Query(value = "pageSize") Integer pageSize
            , @Query(value = "startInDate") int startInDate
            , @Query(value = "endInDate") int endInDate
            , @Query(value = "orderBy") String orderBy
            , @Query(value = "order") String order);

    @GET("/api/digitalhuman/v1/statistic/production")
    Call<Response<PageResult<ProductionStatisticData>>> production(
            @Query(value = "platCode") String platCode
            , @Query(value = "accountId") String accountId
            , @Query(value = "roleLevel") int roleLevel
            , @Query(value = "pageNo") Integer pageNo
            , @Query(value = "pageSize") Integer pageSize
            , @Query(value = "startInDate") int startInDate
            , @Query(value = "endInDate") int endInDate);
}
