package com.baidu.acg.piat.digitalhuman.plat.client;

import com.baidu.acg.piat.digitalhuman.common.background.BackgroundImage;
import com.baidu.acg.piat.digitalhuman.common.background.BackgroundUploadRequest;
import com.baidu.acg.piat.digitalhuman.common.background.SpecBackgroundRequest;
import com.baidu.acg.piat.digitalhuman.common.entity.CharacterModel;
import com.baidu.acg.piat.digitalhuman.common.model.PageResponse;
import com.baidu.acg.piat.digitalhuman.common.model.Response;
import retrofit2.Call;
import retrofit2.http.Body;
import retrofit2.http.DELETE;
import retrofit2.http.GET;
import retrofit2.http.POST;
import retrofit2.http.PUT;
import retrofit2.http.Path;
import retrofit2.http.Query;

import java.util.Map;

public interface CharacterManagerService {

    // background related
    @POST("/api/digitalhuman/v1/background")
    Call<Response<BackgroundImage>> createBackground(@Body BackgroundUploadRequest uploadRequest);

    @DELETE("/api/digitalhuman/v1/background/{id}")
    Call<Response<Void>> deleteBackground(@Path(value = "id") String backgroundId);

    @GET("/api/digitalhuman/v1/background/{id}")
    Call<Response<BackgroundImage>> findBackgroundById(@Path(value = "id") String backgroundId);

    @GET("/api/digitalhuman/v1/background")
    Call<PageResponse<BackgroundImage>> listBackgroundByUserId(@Query("userId") String userId,
                                                               @Query("pageNo") int pageNo,
                                                               @Query("pageSize") int pageSize);

    // character related
    @PUT("/api/digitalhuman/v1/character/{type}/preview")
    Call<byte[]> preview(@Path(value = "type") String characterImageType,
                         @Body SpecBackgroundRequest specBackgroundRequest);

    @POST("/api/digitalhuman/v1/character")
    Call<Response<CharacterModel>> createCharacter(@Body CharacterModel characterRequest);

    @DELETE("/api/digitalhuman/v1/character/type/{type}")
    Call<Response<Void>> deleteCharacterByType(@Path(value = "type") String characterImageType);

    @GET("/api/digitalhuman/v1/character")
    Call<PageResponse<CharacterModel>> findAllCharacter(@Query("pageNo") int pageNo,
                                                       @Query("pageSize") int pageSize);

    @GET("/api/digitalhuman/v1/character/sce")
    Call<PageResponse<CharacterModel>> findCharacterVisibleForSce(
        @Query(value = "visibleForSce") boolean visibleForSce);

    @GET("/api/digitalhuman/v1/character/type/{type}")
    Call<Response<CharacterModel>> findCharacterByType(@Path(value = "type") String characterImageType,
                                                       @Query("apiVersion") Integer apiVersion);

    @PUT("/api/digitalhuman/v1/character/type/{type}")
    Call<Response<CharacterModel>> updateCharacterByType(@Path(value = "type") String characterImageType,
                                                        @Body CharacterModel request);

    @GET("/api/digitalhuman/v1/character/{id}")
    Call<Response<CharacterModel>> findCharacterById(@Path(value = "id") String characterId);

    @PUT("/api/digitalhuman/v1/character/{id}")
    Call<Response<CharacterModel>> updateCharacterById(@Path(value = "id") String characterId,
                                                      @Body CharacterModel request);

    @GET("/api/digitalhuman/v1/character/figurename")
    Call<Response<CharacterModel>> findCharacterType(@Query(value = "figureName") String figureName);

    // character v2
    @PUT("/api/digitalhuman/v2/plat/characters/{type}/preview")
    Call<byte[]> previewV2(@Path(value = "type") String characterImageType,
                         @Body SpecBackgroundRequest specBackgroundRequest);

    @POST("/api/internal/digitalhuman/plat/v2/characters")
    Call<Response<CharacterModel>> createCharacterV2(@Body CharacterModel characterRequest);

    @DELETE("/api/digitalhuman/v2/plat/characters/type/{type}")
    Call<Response<Void>> deleteCharacterByTypeV2(@Path(value = "type") String characterImageType);

    @GET("/api/internal/digitalhuman/plat/v2/characters")
    Call<PageResponse<CharacterModel>> findCharacterV2(@Query("pageNo") int pageNo,
            @Query("pageSize") int pageSize,
            @Query("queryForLive") Boolean queryForLive,
            @Query("queryForSce") Boolean queryForSce,
            @Query("tags") Map<String, String> tags
    );

    @DELETE("/api/internal/digitalhuman/plat/v2/characters/{id}")
    Call<Response<Void>> deleteByIdV2(@Path("id") String id);

    @GET("/api/digitalhuman/v2/plat/characters/sce")
    Call<PageResponse<CharacterModel>> findCharacterVisibleForSceV2(
        @Query(value = "visibleForSce") boolean visibleForSce);

    @GET("/api/digitalhuman/v2/plat/characters/type/{type}")
    Call<Response<CharacterModel>> findCharacterByTypeV2(@Path(value = "type") String characterImageType);

    @PUT("/api/digitalhuman/v2/plat/characters/type/{type}")
    Call<Response<CharacterModel>> updateCharacterByTypeV2(@Path(value = "type") String characterImageType,
                                                         @Body CharacterModel request);

    @GET("/api/digitalhuman/v2/plat/characters/{id}")
    Call<Response<CharacterModel>> findCharacterByIdV2(@Path(value = "id") String characterId);

    @PUT("/api/internal/digitalhuman/plat/v2/characters/{id}")
    Call<Response<CharacterModel>> updateCharacterByIdV2(@Path(value = "id") String characterId,
                                                       @Body CharacterModel request);
}
