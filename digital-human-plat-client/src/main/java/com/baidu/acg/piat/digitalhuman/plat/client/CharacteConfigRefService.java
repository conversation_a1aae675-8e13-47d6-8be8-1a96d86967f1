package com.baidu.acg.piat.digitalhuman.plat.client;

import com.baidu.acg.piat.digitalhuman.common.character.CharacterConfigRef;
import com.baidu.acg.piat.digitalhuman.common.character.CharacterConfigRefDeleteReq;
import com.baidu.acg.piat.digitalhuman.common.character.CharacterConfigRefListReq;
import com.baidu.acg.piat.digitalhuman.common.character.CharacterConfigRefUpsertReq;
import com.baidu.acg.piat.digitalhuman.common.model.Response;
import retrofit2.Call;
import retrofit2.http.Body;
import retrofit2.http.POST;

import java.util.List;

public interface CharacteConfigRefService {

    @POST("/api/internal/digitalhuman/plat/v2/characterconfigref/upsert")
    Call<Response<List<CharacterConfigRef>>> upsert(@Body CharacterConfigRefUpsertReq req);

    @POST("/api/internal/digitalhuman/plat/v2/characterconfigref/list")
    Call<Response<List<CharacterConfigRef>>> list(@Body CharacterConfigRefListReq req);

    @POST("/api/internal/digitalhuman/plat/v2/characterconfigref/delete")
    Call<Response<Void>> delete(@Body CharacterConfigRefDeleteReq req);
}
