package routers

import (
	"acg-ai-go-common/global"
	"acg-ai-go-common/logger"
	"dhlive-cgi/handler"

	"github.com/gin-gonic/gin"
)

var (
	GinRouter = gin.Default()
)

// InitRouter 初始化gin routers
func InitRouter() {
	// 统一gin的日志到统一的logger里面
	GinRouter.Use(logger.GinLog())
	HealthRouter(GinRouter)
	CgiRouter(GinRouter)
}

func CgiRouter(e *gin.Engine) {
	// e.GET("/dhlive-cgi/comminfo", handler.Comminfo)
	e.Any("/dhlive-cgi/*path", handler.HttpRequest)
}

func HealthRouter(e *gin.Engine) {
	// 健康检查
	url := "/actuator/health"
	if global.ServerSetting.ConsulSetting != nil && len(global.ServerSetting.ConsulSetting.HealthUrl) > 0 {
		url = global.ServerSetting.ConsulSetting.HealthUrl
	}
	e.GET(url, handler.HealthHandler)
}
