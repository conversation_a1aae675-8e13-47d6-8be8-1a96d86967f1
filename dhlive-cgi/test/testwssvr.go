package main

import (
	"fmt"

	"github.com/gin-gonic/gin"
	"github.com/gorilla/websocket"
)

var upGrader websocket.Upgrader

func wsTest(c *gin.Context) {
	ws, err := upGrader.Upgrade(c.<PERSON>, c.Request, nil)
	if err != nil {
		fmt.Println("upGrader.Upgrade error")
		return
	}

	defer func() {
		ws.Close()
	}()

	for {
		// 1、读取请求数据并解析
		_, message, err := ws.ReadMessage()

		if err != nil {
			fmt.Printf("asr websocket request read fail: err: [%v] message: [%s]", err, string(message))
			break // 读取参数异常，意味着整个请求过程，可能无法恢复了，则直接结束
		}

		fmt.Printf("asr websocket request read: message: %s", string(message))
	}
}

func main() {
	GinRouter := gin.Default()
	chatGroup := GinRouter.Group("comm/")
	// websocket识别
	chatGroup.GET("ws", wsTest)

	GinRouter.Run(":8881")
}
