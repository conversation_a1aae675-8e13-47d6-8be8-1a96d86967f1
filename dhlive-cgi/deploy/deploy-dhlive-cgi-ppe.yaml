apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: digital-human
    module: dhlive-cgi
  name: dhlive-cgi
  namespace: digital-human-ppe-artiste
spec:
  replicas: 1
  minReadySeconds: 0
  strategy:
    type: RollingUpdate # 策略：滚动更新
    rollingUpdate:
      maxSurge: 25%
      maxUnavailable: 25%
  selector:
    matchLabels:
      app: digital-human
      module: dhlive-cgi
  template:
    metadata:
      labels:
        app: digital-human
        module: dhlive-cgi
    spec:
      restartPolicy: Always
      # restartPolicy: OnFailure
      # restartPolicy: Never
      containers:
        - name: dhlive-cgi
          image: ccr-246im3mw-pub.cnc.bj.baidubce.com/digitalhuman/dhlive-cgi:20240411-2001
          imagePullPolicy: Always
          command: ["sh"]
          env:
            - name: POD_NAMESPACE
              valueFrom:
                fieldRef:
                  fieldPath: metadata.namespace
          args: ["/home/<USER>/sbin/start.sh"]
          # imagePullPolicy: IfNotPresent
          # imagePullPolicy: Never
          ports:
            - containerPort: 8255
          resources: # 资源限制
          livenessProbe: # 健康检查：存活探针
            httpGet:
              path: /health
              port: 8255
            initialDelaySeconds: 20
            timeoutSeconds: 5
            periodSeconds: 5
          readinessProbe: # 健康检查：就绪探针
            httpGet:
              path: /health
              port: 8255
            initialDelaySeconds: 5
            timeoutSeconds: 1
            periodSeconds: 5
          # volumeMounts:
          #   - name: nginx-volume
          #     mountPath: "/usr/share/nginx"
      imagePullSecrets:
        - name: regcred
        - name: regcred-ccr
        - name: regcred-eccr
      # volumes:
      #   - name: nginx-volume
      #     flexVolume:
      #       driver: "baidubce/cds"
      #       fsType: "ext4"
      #       options:
      #         volumeID: "{id}" # 填写cds的id，注意：cds必须和pod在同一可用区！！
      # nodeSelector:
      #   beta.kubernetes.io/os: linux
      # tolerations: # 容忍污点
      #   - effect: NoExecute
      #     operator: Exists
      #   - effect: NoSchedule
      #     operator: Exists
      # affinity: # 亲和性
      #   nodeAffinity:
      #     requiredDuringSchedulingIgnoredDuringExecution:
      #       nodeSelectorTerms:
      #         - matchExpressions:
      #             - key: failure-domain.beta.kubernetes.io/zone
      #               operator: In
      #               values:
      #                 - zoneA
      #                 - zoneB
      #                 - zoneC
