####################################################### 服务配置-开发环境 #######################################################
server-port = 8255
server-name = "dhlive-cgi"
log-file-prefix = "localhost"
log-file-path = "./logs/"
log-show-code-file = true
log-show-code-func = false

#cce配置

# consul配置
[consul-setting]
host = "127.0.0.1"
port = 8500
name = ""

# 健康检查地址
health-url = "/health"
check-timeout = "10s"
check-interval = "10s"
deregister-critical-service-after = "30s"
