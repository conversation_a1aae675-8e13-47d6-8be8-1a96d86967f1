####################################################### 服务配置-开发环境 #######################################################
server-port = 8255
server-name = "dhlive-cgi"
log-file-prefix = "localhost"
log-file-path = "./logs/"
log-show-code-file = true
log-show-code-func = false

# consul配置
[consul-setting]
host = "127.0.0.1"
port = 8500
name = ""

# 健康检查地址
health-url = "/health"
check-timeout = "10s"
check-interval = "10s"
deregister-critical-service-after = "30s"

# mongodb配置
[mongo-setting]
uri = "******************************************************"
db-name = "custom_unit"

# mysql配置
[mysql-setting]
url = "**********************************************************************"
user-name = "root"
password = "123456"

# redis配置
[redis-setting]
# 暂时使用的是数字人的测试环境redis
sentinelNodes = "*************:8480,*************:8481,*************:8482" # 哨兵集群节点，ip:port,ip:port,ip:port...，多个节点使用,分割，eg: "127.0.0.1:26379,127.0.0.1:26380,127.0.0.1:26381"
sentinelUsername = ""
sentinelPassword = "Hi109.3"
routeByLatency = false
masterName = "mymaster"
username = ""
password = "Hi109.3"
poolSize = 1000
minIdleConns = 100

# elasticsearch配置
[es-setting]
host = "127.0.0.1"
port = 9200
user-name = ""
password = ""