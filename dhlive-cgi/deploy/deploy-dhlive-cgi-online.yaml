apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: digital-human
    module: dhlive-cgi
  name: dhlive-cgi
  namespace: live2d
spec:
  replicas: 3
  minReadySeconds: 0
  strategy:
    type: RollingUpdate # 策略：滚动更新
    rollingUpdate:
      maxSurge: 25%
      maxUnavailable: 25%
  selector:
    matchLabels:
      app: digital-human
      module: dhlive-cgi
  template:
    metadata:
      labels:
        app: digital-human
        module: dhlive-cgi
    spec:
      affinity:
        podAntiAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            - labelSelector:
                matchExpressions:
                  - key: app
                    operator: In
                    values:
                      - digital-human
                  - key: module
                    operator: In
                    values:
                      - dhlive-cgi
              topologyKey: kubernetes.io/hostname
      restartPolicy: Always
      tolerations:
        - effect: "NoSchedule"
          key: "limit"
          operator: "Equal"
          value: "lite"
      containers:
        - name: dhlive-cgi
          image: ccr-246im3mw-pub.cnc.bj.baidubce.com/digitalhuman/dhlive-cgi:20240423_1713874262929
          imagePullPolicy: Always
          command: ["sh"]
          env:
            - name: POD_NAMESPACE
              valueFrom:
                fieldRef:
                  fieldPath: metadata.namespace
          args: ["/home/<USER>/sbin/start.sh"]
          # imagePullPolicy: IfNotPresent
          # imagePullPolicy: Never
          ports:
            - containerPort: 8255
          resources: # 资源限制
          livenessProbe: # 健康检查：存活探针
            httpGet:
              path: /health
              port: 8255
            initialDelaySeconds: 20
            timeoutSeconds: 5
            periodSeconds: 5
          readinessProbe: # 健康检查：就绪探针
            httpGet:
              path: /health
              port: 8255
            initialDelaySeconds: 5
            timeoutSeconds: 1
            periodSeconds: 5
          # volumeMounts:
          #   - name: nginx-volume
          #     mountPath: "/usr/share/nginx"
      imagePullSecrets:
        - name: regcred
        - name: regcred-ccr
        - name: regcred-eccr
#      nodeSelector:
#        beta.kubernetes.io/os: linux
#        tolerations: # 容忍污点
#          - effect: NoExecute
#            operator: Exists
#          - effect: NoSchedule
#            operator: Exists
---
apiVersion: v1
kind: Service
metadata:
  labels:
    app: digital-human
    module: dhlive-cgi
  name: dhlive-cgi
  namespace: live2d
spec:
  selector:
    app: digital-human
    module: dhlive-cgi
  ports:
    - protocol: TCP
      port: 8255
      targetPort: 8255
  type: ClusterIP