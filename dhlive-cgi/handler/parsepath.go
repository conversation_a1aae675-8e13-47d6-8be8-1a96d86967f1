package handler

import "strings"

// 返回：是否成功, 服务名，服务路径
func parsePath(path string) (bool, string, string) {
	idx := strings.Index(path, CigPath)
	if idx == -1 || idx != 0 {
		return false, "", ""
	}

	pathLen := len(path)
	cigLen := len(CigPath)

	if pathLen <= cigLen {
		return false, "", ""
	}

	// 服务名称
	svrName := ""
	findIdx := 0
	for findIdx = cigLen; findIdx < pathLen; findIdx++ {
		if findIdx == cigLen && path[findIdx] == '/' {
			continue
		}

		if path[findIdx] != '/' {
			svrName = svrName + path[findIdx:findIdx+1]
			continue
		}

		if path[findIdx] == '/' {
			break
		}
	}

	svrPath := ""
	if pathLen > cigLen+len(svrName) {
		svrPath = path[cigLen+len(svrName)+1 : pathLen]
	}

	return svrName != "", svrName, svrPath
}
