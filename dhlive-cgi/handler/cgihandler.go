package handler

import (
	"acg-ai-go-common/logger"
	"fmt"
	"net/http"
	"net/http/httputil"
	"net/url"
	"os"

	"github.com/gin-gonic/gin"
)

var namespace = ""

func init() {
	namespace = os.Getenv("POD_NAMESPACE")
	fmt.Println("namespace: ", namespace)
}

type CommCgiRsp struct {
	ErrCode int    `json:"errCode"`
	ErrMSg  string `json:"errMsg"`
}

const (
	CgiErrCodeSucess            = 0       // 正常
	CgiErrCodeServiceError      = -500001 // 服务异常
	CgiErrCodeServiceOffLine    = -500002 // 服务下线
	CgiErrCodeServiceNotExist   = -500003 // 服务不存在
	CgiErrCodeServiceReqPathErr = -500004 // 请求的path路径不正确
	CgiErrCodeServiceSvrUrlErr  = -500005 // 服务的url地址异常

	CgiErrMsgSucess            = "sucess"
	CgiErrMsgServiceReqPathErr = "request path error"

	CigPath = "/dhlive-cgi"
)

type PathInfo struct {
	ServiceName string // 服务名称
	ServicePath string // 服务路径
}

type LiveChkRsp struct {
	Code int    `json:"code"`
	Msg  string `json:"msg"`
}

func Comminfo(c *gin.Context) {
	c.JSON(http.StatusOK, LiveChkRsp{Code: 0, Msg: "comminfo"})
}

func HttpRequest(c *gin.Context) {
	request := c.Request
	ok, svrName, svrPath := parsePath(request.URL.Path)
	logger.Log.Printf("httpRequest: host[%s] url:[%s] ok:[%t] svrName:[%s] svrPath:[%s]",
		request.Host, request.URL.String(), ok, svrName, svrPath)

	if !ok {
		c.JSON(http.StatusOK, CommCgiRsp{
			ErrCode: CgiErrCodeServiceReqPathErr,
			ErrMSg:  CgiErrMsgServiceReqPathErr,
		})
		logger.Log.Errorf("parsePath err ok:%t", ok)
		return
	}

	// 请求转发
	var err error
	var reverseHost *url.URL

	// svrPath = url.QueryEscape(svrPath)
	serviceUrl := fmt.Sprintf("%s.%s.svc.cluster.local:%d/", svrName, namespace, 8080)
	reverseHost, err = url.Parse("http://" + serviceUrl)
	if err != nil {
		c.JSON(http.StatusOK, CommCgiRsp{
			ErrCode: CgiErrCodeServiceSvrUrlErr,
			ErrMSg:  err.Error(),
		})
		logger.Log.Errorf("url.Parse host err:[%s]", err.Error())
		return
	}

	proxy := httputil.NewSingleHostReverseProxy(reverseHost)
	outreq := c.Request.Clone(c.Request.Context())
	outreq.URL.Path = svrPath
	logger.Log.Printf("proxy: outreq url:[%s]", outreq.URL.String())
	proxy.ServeHTTP(c.Writer, outreq)
}
