package controller

import (
	"acg-ai-go-common/gomysql"
	"acg-ai-go-common/logger"
	"acg-ai-go-common/utils"
	"context"
	"digital-human-micro-chroma/beans/enum"
	"digital-human-micro-chroma/beans/model"
	"digital-human-micro-chroma/beans/proto"
	"digital-human-micro-chroma/conf"
	"digital-human-micro-chroma/handler/sutils/jsonutils"
	"fmt"
	"github.com/gin-gonic/gin"
	"net/http"
	"time"
)

type TaskHandler struct {
}

// HandleSubmitTask 提交任务
func (p TaskHandler) HandleSubmitTask(c *gin.Context) {
	taskId := "coma-" + utils.RandStringRunes(24)
	logCtx := context.WithValue(context.TODO(), utils.CtxKeyLogID, utils.RandStringRunes(16))

	resp := proto.ChromaSubmitResponseData{
		LogId: utils.GetLogID(logCtx),
	}

	req := proto.ChromaSubmitRequest{}
	if err := c.<PERSON>(&req); err != nil {
		logger.Log.<PERSON>(utils.MMark(logCtx)+"HandleSubmitTask BindJSON fail, err:%v", err)
		c.JSON(http.StatusOK, proto.NewCommonResponse(100001, "参数错误", resp))
		return
	}

	// 基础参数校验
	if len(req.VideoURL) < 1 || req.Config.Params == nil {
		logger.Log.Errorf(utils.MMark(logCtx) + "HandleSubmitTask check fail, params invalid")
		c.JSON(http.StatusOK, proto.NewCommonResponse(100001, "参数不完整", resp))
		return
	}

	// 抠绿版本校验
	isSupport := false
	for _, version := range conf.LocalConfig.ChromaSettings.SupportVersion {
		if version == req.Config.Version {
			isSupport = true
			break
		}
	}
	if !isSupport {
		logger.Log.Errorf(utils.MMark(logCtx)+"HandleSubmitTask fail, version=%v not supported", req.Config.Version)
		c.JSON(http.StatusOK, proto.NewCommonResponse(100002, "不支持该版本", resp))
		return
	}

	// 抠绿参数校验
	isValid, err := checkParams(&req)
	if err != nil || !isValid {
		logger.Log.Errorf(utils.MMark(logCtx)+"HandleSubmitTask fail, err: %v", err)
		c.JSON(http.StatusOK, proto.NewCommonResponse(100003, "参数不合法", resp))
		return
	}

	// 调度信息初始化
	_, scheduleInfo, err := model.NewScheduleInfo(conf.LocalConfig.ScheduleSettings.MaxTaskRetryCount)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"HandleSubmitTask fail, update schedule err: %v", err)
		c.JSON(http.StatusOK, proto.NewCommonResponse(100004, "更新调度失败", resp))
		return
	}

	// 增加一些不是必须的提交数据
	submitInfo := jsonutils.JSONMap{}
	if req.Additional != nil && len(req.Additional.EditCallback) > 0 {
		submitInfo[proto.KeyEditCallback] = req.Additional.EditCallback
	}

	// 插入条目
	item := &model.ChromaTask{
		TaskId:       taskId,
		VideoUrl:     req.VideoURL,
		CallbackUrl:  req.CallbackURL,
		Version:      req.Config.Version,
		Width:        req.Config.Width,
		Height:       req.Config.Height,
		Config:       req.Config.Params,
		Status:       enum.Submit,
		Retry:        1,
		RetryCount:   0,
		RetryMax:     conf.LocalConfig.ScheduleSettings.MaxTaskRetryCount,
		SegmentCount: 0,
		SegmentTime:  0,
		ScheduleInfo: scheduleInfo,
		SubmitInfo:   submitInfo,
		CreatedAt:    time.Now(),
		UpdatedAt:    time.Now(),
	}

	if len(req.VideoId) > 0 {
		item.VideoId = req.VideoId
	}

	err = item.Create(gomysql.DB)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"HandleSubmitTask fail, update database err: %v", err)
		c.JSON(http.StatusOK, proto.NewCommonResponse(100005, "更新数据库失败", resp))
		return
	}

	// 返回 taskId
	resp.TaskId = taskId
	c.JSON(http.StatusOK, proto.NewCommonResponse(0, "success", resp))

	logger.Log.Infof(utils.MMark(logCtx)+"HandleSubmitTask, add task=%v success", item.TaskId)
	return
}

func checkParams(item *proto.ChromaSubmitRequest) (ret bool, er error) {
	ret = false
	er = nil

	for {
		if item.Config.Height < 1 || item.Config.Width < 1 {
			er = fmt.Errorf("视频分辨率参数不合法")
			break
		}

		if item.Config.Version == proto.VERSION1 || item.Config.Version == proto.VERSION2 {
			str, err := jsonutils.GetStringFromJson(item.Config.Params, proto.KeyVertexShader)
			if err != nil || len(str) < 1 {
				er = fmt.Errorf("参数 %v 不合法", proto.KeyVertexShader)
				break
			}
		}

		if item.Config.Version == proto.VERSION1 || item.Config.Version == proto.VERSION2 {
			str, err := jsonutils.GetStringFromJson(item.Config.Params, proto.KeyFragmentShader)
			if err != nil || len(str) < 1 {
				er = fmt.Errorf("参数 %v 不合法", proto.KeyFragmentShader)
				break
			}
		}

		if item.Config.Version == proto.VERSION2 {
			str, err := jsonutils.GetStringFromJson(item.Config.Params, proto.KeyPostVertexShader)
			if err != nil || len(str) < 1 {
				er = fmt.Errorf("参数 %v 不合法", proto.KeyPostVertexShader)
				break
			}
		}

		if item.Config.Version == proto.VERSION2 {
			str, err := jsonutils.GetStringFromJson(item.Config.Params, proto.KeyPostFragmentShader)
			if err != nil || len(str) < 1 {
				er = fmt.Errorf("参数 %v 不合法", proto.KeyPostFragmentShader)
				break
			}
		}

		// 所有检查都没问题才认为合理
		ret = true
		break
	}

	return
}
