package fileutils

import (
	"crypto/sha256"
	"encoding/hex"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"os"
	"path"
	"path/filepath"
	"strings"
	"time"
)

// GetFilenameAndExt 从 URL 获取文件名和后缀
func GetFilenameAndExt(fileURL string) (filename, ext string, err error) {
	// 解析 URL，确保格式正确
	parsedURL, err := url.Parse(fileURL)
	if err != nil {
		return "", "", fmt.Errorf("invalid URL: %v", err)
	}

	// 取出路径部分，并去除 URL 参数
	cleanPath := strings.Split(parsedURL.Path, "?")[0]

	// 获取文件名部分
	base := path.Base(cleanPath)
	if base == "." || base == "/" || base == "" {
		return "", "", fmt.Errorf("URL no filename")
	}

	// 获取后缀名
	ext = path.Ext(base)
	filename = strings.TrimSuffix(base, ext)

	return filename, ext, nil
}

// ListFiles 列出匹配 pattern 的文件 (支持绝对路径)
func ListFiles(pattern string) ([]string, error) {
	files, err := filepath.Glob(pattern)
	if err != nil {
		return nil, fmt.Errorf("get file list: %v", err)
	}
	return files, nil
}

// EnsureDir 确保文件或文件夹所在的目录存在
func EnsureDir(path string) error {
	// 获取绝对路径
	absPath, err := filepath.Abs(path)
	if err != nil {
		return fmt.Errorf("parse path failed: %v", err)
	}

	// 如果是文件路径（含后缀），获取其所在目录
	dir := absPath
	if filepath.Ext(absPath) != "" {
		dir = filepath.Dir(absPath)
	}

	// 创建目录（如果已存在，不会报错）
	err = os.MkdirAll(dir, os.ModePerm)
	if err != nil {
		return fmt.Errorf("create path failed: %v", err)
	}
	return nil
}

// GetFileDetails 获取文件名（不带后缀）、文件路径和后缀名的函数
func GetFileDetails(filePath string) (fileName string, fileDir string, fileExt string) {
	fileNameWithExt := filepath.Base(filePath)
	fileName = strings.TrimSuffix(fileNameWithExt, filepath.Ext(fileNameWithExt))
	fileDir = filepath.Dir(filePath)
	fileExt = filepath.Ext(filePath)
	return
}

// Sha256Hash 根据url计算一个文件名出来
func Sha256Hash(s string) string {
	hash := sha256.Sum256([]byte(s))
	return hex.EncodeToString(hash[:])
}

// IsExists 判断文件是否存在
func IsExists(filePath string) bool {
	info, err := os.Stat(filePath)
	if err != nil {
		if os.IsNotExist(err) {
			return false
		}
	}
	return !info.IsDir()
}

// DownloadFile 从给定的URL下载文件并保存到本地路径
func DownloadFile(url string, destPath string) error {
	// 创建一个HTTP请求
	resp, err := http.Get(url)
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	// 检查HTTP响应状态码
	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("server returned non-200 status: %d %s", resp.StatusCode, resp.Status)
	}

	// 创建目标目录（如果不存在）
	dir := filepath.Dir(destPath)
	if err := os.MkdirAll(dir, 0755); err != nil {
		return err
	}

	// 创建文件
	outFile, err := os.Create(destPath)
	if err != nil {
		return err
	}
	defer outFile.Close()

	// 复制响应流到文件
	_, err = io.Copy(outFile, resp.Body)
	return err
}

// RetryDownloadFile 从给定的URL下载文件并保存到本地路径
func RetryDownloadFile(url string, destPath string, retry int) error {
	err := fmt.Errorf("")
	for i := 0; i < retry; i++ {
		if err = DownloadFile(url, destPath); err != nil {
			time.Sleep(1 * time.Second)
			continue
		} else {
			return nil
		}
	}

	return fmt.Errorf("download failed after retry %d times, err: %v", retry, err)
}

// CopyFile 拷贝文件
func CopyFile(src string, dst string) error {
	srcFile, err := os.Open(src)
	if err != nil {
		return fmt.Errorf("cannot open src file: %v", err)
	}
	defer srcFile.Close()

	dstDir := filepath.Dir(dst)
	err = os.MkdirAll(dstDir, os.ModePerm)
	if err != nil {
		return fmt.Errorf("cretae dst folder err: %v", err)
	}

	dstFile, err := os.Create(dst)
	if err != nil {
		return fmt.Errorf("cannot create dst file: %v", err)
	}
	defer dstFile.Close()

	_, err = io.Copy(dstFile, srcFile)
	if err != nil {
		return fmt.Errorf("copy file err: %v", err)
	}

	err = dstFile.Sync()
	if err != nil {
		return fmt.Errorf("file synv err: %v", err)
	}

	return nil
}

func ClearFolder(dir string) error {
	err := os.RemoveAll(dir)
	if err != nil {
		return err
	}
	return os.MkdirAll(dir, 0755)
}

func DeleteFolder(dir string) error {
	err := os.RemoveAll(dir)
	if err != nil {
		return err
	}
	return nil
}

func GetEditID(rawURL string) (string, error) {
	parsedURL, err := url.Parse(rawURL)
	if err != nil {
		return "", fmt.Errorf("parse url failed: %w", err)
	}

	editID := parsedURL.Query().Get("editId")
	if editID == "" {
		return "", fmt.Errorf("editId not found")
	}
	return editID, nil
}
