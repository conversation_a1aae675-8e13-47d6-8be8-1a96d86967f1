package fileutils

import (
	"context"
	"fmt"
	"github.com/baidubce/bce-sdk-go/services/bos"
	"github.com/google/uuid"
	"path/filepath"
	"time"
)

var (
	BosAk       = "ALTAKxdVkHMs4sp0Tgkpa3zCJP"
	BosSk       = "e4f46ff5d1bf45c5b81dd867d6e3c148"
	BosEndpoint = "bj.bcebos.com"
	BosBucket   = "xiling-dh"
	BosHost     = "https://xiling-dh.bj.bcebos.com"
	BosCdnHost  = "https://xiling-dh.cdn.bcebos.com"
)

func InitBosConfig(ak string, sk string, endpoint string, bucket string, host string, cdnHost string) {
	BosAk = ak
	BosSk = sk
	BosEndpoint = endpoint
	BosBucket = bucket
	BosHost = host
	BosCdnHost = cdnHost
}

func RetryUploadBosServiceFromFile(logCtx context.Context, objectKeyPath string, filePath string,
	fileFormat string) (string, error) {
	url := ""
	var err error
	for i := 0; i < 3; i++ {
		url, err = UploadBosServiceFromFile(logCtx, objectKeyPath, filePath, fileFormat)
		if err == nil && url != "" {
			return url, nil
		}
		time.Sleep(1 * time.Second)
	}
	return url, err
}

func UploadBosServiceFromFile(logCtx context.Context, objectKeyPath string, filePath string, fileFormat string) (string, error) {
	bosClient, err := bos.NewClient(BosAk, BosSk, BosEndpoint)
	if err != nil {
		return "", fmt.Errorf("create bos client err: %v", err)
	}
	// 生成文件名
	filename := uuid.NewString() + fileFormat
	objectKey := filepath.Join(objectKeyPath, time.Now().Format("2006-01-02"), filename)

	// 从本地文件上传
	_, err = bosClient.PutObjectFromFile(BosBucket, objectKey, filePath, nil)
	if err != nil {
		return "", fmt.Errorf("put obj err: %v", err)
	}
	downloadUrl := BosCdnHost + objectKey
	return downloadUrl, nil
}
