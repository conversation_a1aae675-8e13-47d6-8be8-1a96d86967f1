package ffmpegutils

import (
	"acg-ai-go-common/logger"
	"acg-ai-go-common/utils"
	"context"
	"fmt"
	"os"
	"os/exec"
	"strings"
)

// SplitVideoIntoParts 将文件按时间分割成多个文件
func SplitVideoIntoParts(ctx context.Context, input string, output string, dur int) error {
	cmd := exec.Command("ffmpeg", "-i", input, "-c", "copy", "-f", "segment", "-segment_time", fmt.Sprintf("%d", dur),
		"-reset_timestamps", "1", "-y", output)

	cmdStr := strings.Join(cmd.Args, " ")
	logger.Log.Infof(utils.MMark(ctx)+"ffmpeg split video cmd: { %s }", cmdStr)

	cmd.Stdout = os.Stdout
	cmd.Stderr = os.Stderr

	if err := cmd.Run(); err != nil {
		return err
	}

	return nil
}

// SplitVideoIntoPartsWithNoAudio 将文件按时间分割成多个无音频的视频片段
func SplitVideoIntoPartsWithNoAudio(ctx context.Context, input string, output string, dur int) error {
	cmd := exec.Command("ffmpeg", "-i", input, "-c", "copy", "-an", "-f", "segment",
		"-segment_time", fmt.Sprintf("%d", dur), "-reset_timestamps", "1", "-y", output)

	cmdStr := strings.Join(cmd.Args, " ")
	logger.Log.Infof(utils.MMark(ctx)+"ffmpeg split video with no audio cmd: { %s }", cmdStr)

	cmd.Stdout = os.Stdout
	cmd.Stderr = os.Stderr

	if err := cmd.Run(); err != nil {
		return err
	}

	return nil
}

// ChromaKey 抠绿算法
func ChromaKey(ctx context.Context, input string, output string, threads int, width int, height int, framerate int,
	fragmentShaderPath string,
	vertexShaderPath string, postFragmentShaderPath string, postVertexShaderPath string) error {

	// 组装后处理着色器
	postShaderFilterStr := ""
	if postVertexShaderPath != "" && postFragmentShaderPath != "" {
		postShaderFilterStr = fmt.Sprintf("plusglshader=sdsource='%s':vxsource='%s'[out2];[out2]", postFragmentShaderPath, postVertexShaderPath)
	}

	// 组装 filter_complex
	filterComplex := fmt.Sprintf(
		"[0:v]format=rgba,plusglshader=sdsource='%s':vxsource='%s'[out1];[out1]%s scale=%d:%d,format=yuva420p",
		fragmentShaderPath, vertexShaderPath, postShaderFilterStr, width, height)

	// 组装 ffmpeg 指令
	ffmpegCmd := fmt.Sprintf("ffmpeg -i %s -filter_complex \"%s\" -vcodec libvpx -b:v 5000k -cpu-used 4 -threads %d"+
		" -acodec libopus -pix_fmt yuva420p -r %d -g %d -keyint_min %d -sc_threshold 0 -auto-alt-ref 0 -y %s",
		input, filterComplex, threads, framerate, framerate, framerate, output)

	// 组装整体指令
	cmd := exec.Command("xvfb-run", "-a",
		"--server-args=-screen 0 "+fmt.Sprintf("%dx%dx24 -ac -nolisten tcp -dpi 96 +extension RANDR", width, height),
		"sh", "-c", fmt.Sprintf("%s", ffmpegCmd))

	cmdStr := strings.Join(cmd.Args, " ")
	logger.Log.Infof(utils.MMark(ctx)+"ffmpeg chromakey cmd: { %s }", cmdStr)

	cmd.Stdout = os.Stdout
	cmd.Stderr = os.Stderr
	if err := cmd.Run(); err != nil {
		return err
	}

	return nil
}

// ConcatVideo 将多个文件整合
func ConcatVideo(ctx context.Context, inputList string, output string) error {
	cmd := exec.Command("ffmpeg", "-f", "concat", "-safe", "0", "-i", inputList, "-c", "copy", "-y", output)

	cmdStr := strings.Join(cmd.Args, " ")
	logger.Log.Infof(utils.MMark(ctx)+"ffmpeg concat video cmd: { %s }", cmdStr)

	cmd.Stdout = os.Stdout
	cmd.Stderr = os.Stderr

	if err := cmd.Run(); err != nil {
		return err
	}

	return nil
}

// ExtractAudio 从视频文件中提取音频
func ExtractAudio(ctx context.Context, inputVideo string, outputAudio string) error {
	cmd := exec.Command("ffmpeg", "-i", inputVideo, "-q:a", "0", "-map", "a", "-y", outputAudio)

	cmdStr := strings.Join(cmd.Args, " ")
	logger.Log.Infof(utils.MMark(ctx)+"ffmpeg extract audio cmd: { %s }", cmdStr)

	cmd.Stdout = os.Stdout
	cmd.Stderr = os.Stderr

	if err := cmd.Run(); err != nil {
		return err
	}

	return nil
}

// MergeAudioVideo 将音频和视频合成，并可指定音频编码、声道数和采样率
func MergeAudioVideo(ctx context.Context, inputVideo string, inputAudio string, output string, audioCodec string, channels int, sampleRate int) error {
	cmd := exec.Command("ffmpeg", "-i", inputVideo, "-i", inputAudio,
		"-map", "0:v:0", "-map", "1:a:0", // 选择第一个视频流 + 独立音频流
		"-c:v", "copy",
		"-c:a", audioCodec,
		"-ac", fmt.Sprintf("%d", channels),
		"-ar", fmt.Sprintf("%d", sampleRate),
		"-y", output)

	cmdStr := strings.Join(cmd.Args, " ")
	logger.Log.Infof(utils.MMark(ctx)+"ffmpeg merge audio cmd: { %s }", cmdStr)

	cmd.Stdout = os.Stdout
	cmd.Stderr = os.Stderr

	if err := cmd.Run(); err != nil {
		return err
	}

	return nil
}
