package services

import (
	"acg-ai-go-common/logger"
	"digital-human-micro-chroma/conf"
	"digital-human-micro-chroma/handler/services/schedeuler"
	"digital-human-micro-chroma/handler/sutils/cron"
)

// InitScheduler 初始化调度器
func InitScheduler() error {
	schedeuler.Scheduler = schedeuler.NewTaskScheduler(conf.LocalConfig.ScheduleSettings.MaxConcurrency)

	c := cron.GetCron()
	_, err := c.<PERSON><PERSON>ron("@every 5s", schedeuler.Scheduler.RunScheduler)
	if err != nil {
		logger.Log.Fatalf("RegisterCron RunScheduler error: %v\n", err)
		return err
	}

	c.StartCron()
	return nil
}
