package schedeuler

import (
	"acg-ai-go-common/logger"
	"acg-ai-go-common/utils"
	"acg-ai-go-common/utils/httputil"
	"bytes"
	"context"
	"digital-human-micro-chroma/beans/model"
	"digital-human-micro-chroma/beans/proto"
	"digital-human-micro-chroma/conf"
	"digital-human-micro-chroma/handler/sutils/fileutils"
	"digital-human-micro-chroma/handler/sutils/jsonutils"
	"encoding/json"
	"fmt"
	"time"
)

const (
	WorkerIdFormat string = "sub-task-worker-%02d"

	// TaskScheduleLockFmt 处理任务的redis锁
	TaskScheduleLockFmt        = "%s:TaskSchedule:%s"
	TaskScheduleLockExpireTime = 1 * time.Hour
)

const (
	SplitPartFileNameFmt  = "_part_%03d"
	SearchPartFileNameFmt = "_part_*"

	TaskCacheRootPathFmt = "%s/%s/chromakey/%s"

	TaskCacheShaderPathFmt = "%s/shader"
	TaskCachePartPathFmt   = "%s/part"
	TaskCacheOutputPathFmt = "%s/output"

	TaskCacheInFileNameFmt  = "%s/%s%s"
	TaskCacheOutFileNameFmt = "%s/%s.webm"
)

const (
	BosChromaKeyOutPath string = "/micro-chromakey/"
)

const (
	CacheTypeCfs   string = "cfs"
	CacheTypeLocal string = "local"
)

// 处理某个task时，缓存所有文件的根目录，输入的文件名以及文件后缀
func getTaskCacheInfo(task *model.ChromaTask) (cache string, filename string, suffix string, orr error) {
	_, suffix, err := fileutils.GetFilenameAndExt(task.VideoUrl)
	if err != nil {
		orr = fmt.Errorf("parse url for fileinfo err: %v", err)
	}

	// 文件名，默认用url的sha256来生成
	filename = fileutils.Sha256Hash(task.VideoUrl)

	cacheRoot := conf.LocalConfig.CacheSettings.LocalCacheRoot
	// 如果要使用 CFS，缓存根目录需要切换
	if conf.LocalConfig.CacheSettings.CacheType == CacheTypeCfs && len(conf.LocalConfig.CacheSettings.CfsCacheRoot) > 0 {
		cacheRoot = conf.LocalConfig.CacheSettings.CfsCacheRoot
	}

	for {
		// 如果有 edit-id
		editCallbackUrl, err := jsonutils.GetStringFromJson(task.SubmitInfo, proto.KeyEditCallback)
		if err == nil && len(editCallbackUrl) > 0 {
			editId, err := fileutils.GetEditID(editCallbackUrl)
			if err == nil && len(editId) > 0 {
				cache = fmt.Sprintf(TaskCacheRootPathFmt, cacheRoot, editId, filename)
				break
			}
		}

		// 如果有 vp 携带的 videoId
		vpVideoId, err := jsonutils.GetStringFromJson(task.SubmitInfo, proto.KeyVpVideoId)
		if err == nil && len(vpVideoId) > 0 {
			cache = fmt.Sprintf(TaskCacheRootPathFmt, cacheRoot, vpVideoId, filename)
			break

		}

		// 啥也没有，只能使用默认的
		cache = fmt.Sprintf(TaskCacheRootPathFmt, cacheRoot, filename, filename)
		break
	}

	// 一次性创建一些基础文件夹
	if err := fileutils.EnsureDir(cache); err != nil {
		orr = fmt.Errorf("create path: %s err: %v", cache, err)
		return
	}

	partPath := fmt.Sprintf(TaskCachePartPathFmt, cache)
	if err := fileutils.EnsureDir(partPath); err != nil {
		orr = fmt.Errorf("create path: %s err: %v", partPath, err)
		return
	}

	shaderPath := fmt.Sprintf(TaskCacheShaderPathFmt, cache)
	if err := fileutils.EnsureDir(shaderPath); err != nil {
		orr = fmt.Errorf("create path: %s err: %v", shaderPath, err)
		return
	}

	outPath := fmt.Sprintf(TaskCacheOutputPathFmt, cache)
	if err := fileutils.EnsureDir(outPath); err != nil {
		orr = fmt.Errorf("create path: %s err: %v", outPath, err)
		return
	}

	return
}

func CallbackToVideoService(ctx context.Context, url string, videoId string, status proto.VpVideoTaskStatus, msg string,
	output string) error {
	client := httputil.NewRetryHTTPClient(5*time.Second, 3)

	req := proto.ChromaTaskCallbackRequest{
		VideoId:     videoId,
		Status:      string(status),
		Description: msg,
		DownloadUrl: output,
	}

	body, err := json.Marshal(req)
	if err != nil {
		return fmt.Errorf("json Marshal error: %v", err)
	}

	headers := map[string]string{
		"Content-Type": "application/json",
	}

	rsp := proto.ChromaTaskCallbackResponse{}

	res, err := client.DoRequest(ctx, "POST", url, headers, bytes.NewReader(body))
	logger.Log.Infof(utils.MMark(ctx)+"callback to: { %v } request: { %v }, response: { %v }", url, req, string(res))

	err = json.Unmarshal(res, &rsp)
	if err != nil {
		return fmt.Errorf("callback chroma task, Unmarshal: { %v }, error: %v", string(res), err)
	}

	if rsp.Code != 0 || !rsp.Success {
		return fmt.Errorf("callback chroma task err, response: %v", rsp)
	}

	return nil
}
