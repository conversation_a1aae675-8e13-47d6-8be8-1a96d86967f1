package schedeuler

import (
	"digital-human-micro-chroma/beans/model"
	"digital-human-micro-chroma/beans/proto"
	"digital-human-micro-chroma/conf"
	"digital-human-micro-chroma/handler/sutils/fileutils"
	"digital-human-micro-chroma/handler/sutils/jsonutils"
	"fmt"
)

// ClearProgressFile 清除中间文件，分割文件及其输出
func ClearProgressFile(task *model.ChromaTask) error {
	// 计算文件的基础信息，路径、文件名、后缀
	taskCachePath, _, _, err := getTaskCacheInfo(task)
	if err != nil {
		return fmt.Errorf("parse video url failed: %v", err)
	}

	// 需要清除两个文件 part-分割缓存，output-生成缓存
	partFolder := fmt.Sprintf(TaskCachePartPathFmt, taskCachePath)
	if err := fileutils.ClearFolder(partFolder); err != nil {
		return fmt.Errorf("clear part cache failed: %v", err)
	}

	outputFolder := fmt.Sprintf(TaskCacheOutputPathFmt, taskCachePath)
	if err := fileutils.ClearFolder(outputFolder); err != nil {
		return fmt.Errorf("clear output cache failed: %v", err)
	}

	return nil
}

// ClearAllTaskCache 清除任务的所有缓存
func ClearAllTaskCache(task *model.ChromaTask) error {
	taskCachePath, _, _, err := getTaskCacheInfo(task)
	if err != nil {
		return fmt.Errorf("parse video url failed: %v", err)
	}

	if err := fileutils.DeleteFolder(fmt.Sprintf("%s/..", taskCachePath)); err != nil {
		return fmt.Errorf("delete cache failed: %v", err)
	}

	return nil
}

// PostCopyOutput 拷贝文件到指定的路径下去
func PostCopyOutput(task *model.ChromaTask) (string, error) {
	// 不需要或者不是CFS
	if !conf.LocalConfig.CacheSettings.PostCopy || conf.LocalConfig.CacheSettings.CacheType != CacheTypeCfs || len(
		conf.LocalConfig.CacheSettings.CfsCacheRoot) < 1 {
		return "", fmt.Errorf("cfs not set or no need to copy")
	}

	if len(task.OutputUrl) < 1 {
		return "", fmt.Errorf("output url is empty")
	}

	// 没有指定editId不处理
	editCallbackUrl, err := jsonutils.GetStringFromJson(task.SubmitInfo, proto.KeyEditCallback)
	if err != nil || len(editCallbackUrl) < 1 {
		return "", fmt.Errorf("edit callback is null")
	}

	editId, err := fileutils.GetEditID(editCallbackUrl)
	if err != nil && len(editId) < 1 {
		return "", fmt.Errorf("no valid edit-id")
	}

	// 计算文件的基础信息，路径、文件名、后缀
	// 文件名，默认用url的sha256来生成
	newFilenameNoExt := fileutils.Sha256Hash(task.OutputUrl)

	// 后缀
	_, suffix, err := fileutils.GetFilenameAndExt(task.OutputUrl)
	if err != nil {
		return "", fmt.Errorf("parse url for fileinfo err: %v", err)
	}

	// 缓存根目录
	taskCachePath, srcFilenameNoExt, _, err := getTaskCacheInfo(task)
	if err != nil {
		return "", fmt.Errorf("parse video url failed: %v", err)
	}

	// output 输出的文件
	outputFile := fmt.Sprintf("%s/%s%s", taskCachePath, srcFilenameNoExt, suffix)
	dstFile := fmt.Sprintf("%s/../../%s%s", taskCachePath, newFilenameNoExt, suffix)
	err = fileutils.CopyFile(outputFile, dstFile)
	if err != nil {
		return "", fmt.Errorf("copy file to %s err: %v", dstFile, err)
	}

	return dstFile, nil
}
