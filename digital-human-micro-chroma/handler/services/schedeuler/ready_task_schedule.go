package schedeuler

import (
	"acg-ai-go-common/gomysql"
	"acg-ai-go-common/logger"
	"acg-ai-go-common/utils"
	"acg-ai-go-common/utils/redisproxy"
	"context"
	"digital-human-micro-chroma/beans/enum"
	"digital-human-micro-chroma/beans/model"
	"digital-human-micro-chroma/handler/sutils"
	"digital-human-micro-chroma/handler/sutils/redislock"
	"fmt"
	"gorm.io/gorm"
	"time"
)

func ScheduleSubmitSubTask(task *model.ChromaSubTask) {
	logCtx := context.WithValue(context.TODO(), utils.CtxKeyLogID, fmt.Sprintf("%s:%s", task.TaskId, task.SubId))
	redisProxy := redisproxy.GetRedisProxy()

	// 加锁
	redisLockKey := fmt.Sprintf(TaskScheduleLockFmt, sutils.GetNameByRunEnv(), task.SubId)
	redisLock := redislock.NewRedisLock(redisProxy.Rdb, redisLockKey, TaskScheduleLockExpireTime)
	ok, err := redisLock.Lock(context.Background())
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"Lock key: %s error: %+v\n", redisLockKey, err)
		return
	} else if !ok {
		return
	}
	defer redisLock.Unlock(context.Background())

	// 重新查询
	item, err := task.GetTasksWithSubId(gomysql.DB, task.SubId)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"refresh task err: %v", err)
		return
	}

	// 状态变了，就不用处理了
	if item.Status != enum.Submit {
		return
	}

	start := time.Now()

	// 核心处理的事务内容
	err = gomysql.DB.Transaction(func(tx *gorm.DB) error {
		for _, worker := range Scheduler.workers {
			if !worker.isAvailable() {
				continue
			}
			logger.Log.Infof(utils.MMark(logCtx)+"get available task worker: %v", worker.ID)

			// 先更新子任务
			err = UpdateChromaSubTask(tx, item, start, enum.Running, enum.ErrCodeSuccess, "")
			if err != nil {
				return err
			}

			// 尝试更新主任务的状态
			err = UpdateChromaTaskWithLock(tx, item.TaskId, start, enum.Running, enum.ErrCodeSuccess, "")
			if err != nil {
				return err
			}

			// 在空闲的执行器上执行该任务
			worker.Execute(*item)
			logger.Log.Infof(utils.MMark(logCtx)+"update task status: %s, dispatch task to worker: %v", enum.Running,
				worker.ID)
			break
		}
		return nil
	})

	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"sub task schedule failed, err: %+v\n", err)

		// 更新任务状态
		err := UpdateChromaSubTask(gomysql.DB, item, start, enum.Failed, enum.ErrCodeSchedule, fmt.Sprintf("%v", err))
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+"sub task update database failed, err: %+v\n", err)
		}
		return
	}

	logger.Log.Infof(utils.MMark(logCtx) + "success schedule sub task to worker")
}
