package schedeuler

import (
	"acg-ai-go-common/logger"
	"acg-ai-go-common/utils"
	"acg-ai-go-common/utils/redisproxy"
	"context"
	"digital-human-micro-chroma/beans/enum"
	"digital-human-micro-chroma/beans/model"
	"digital-human-micro-chroma/beans/proto"
	"digital-human-micro-chroma/conf"
	"digital-human-micro-chroma/handler/sutils"
	"digital-human-micro-chroma/handler/sutils/jsonutils"
	"digital-human-micro-chroma/handler/sutils/redislock"
	"fmt"
	"gorm.io/gorm"
	"time"
)

func UpdateSchedulerNode(start time.Time, item *model.ChromaTask) error {
	node := model.NewScheduleNode()

	var scheduleInfo model.ScheduleInfo
	err := jsonutils.JsonMapToStruct(item.ScheduleInfo, &scheduleInfo)
	if err != nil {
		info, _, err := model.NewScheduleInfo(conf.LocalConfig.ScheduleSettings.MaxTaskRetryCount)
		if err != nil {
			return err
		}
		scheduleInfo = *info
	}

	// 更新调度器
	node.StartTime = start
	node.Duration = int64(time.Since(start).Seconds())
	node.TargetStatus = string(item.Status)
	scheduleInfo.Nodes = append(scheduleInfo.Nodes, *node)

	if item.Status == enum.Failed || item.Status == enum.Success {
		item.RetryCount += 1
	}

	scheTemp, err := jsonutils.StructToJsonMap(scheduleInfo)
	if err != nil {
		return err
	}
	item.ScheduleInfo = scheTemp

	return nil
}

func UpdateSubSchedulerNode(start time.Time, item *model.ChromaSubTask) error {
	node := model.NewScheduleNode()

	var scheduleInfo model.ScheduleInfo
	err := jsonutils.JsonMapToStruct(item.ScheduleInfo, &scheduleInfo)
	if err != nil {
		info, _, err := model.NewScheduleInfo(conf.LocalConfig.ScheduleSettings.MaxChromaRetryCount)
		if err != nil {
			return err
		}
		scheduleInfo = *info
	}

	// 更新调度器
	node.StartTime = start
	node.Duration = int64(time.Since(start).Seconds())
	node.TargetStatus = string(item.Status)
	scheduleInfo.Nodes = append(scheduleInfo.Nodes, *node)
	if item.Status == enum.Failed || item.Status == enum.Success {
		item.RetryCount += 1
	}

	scheTemp, err := jsonutils.StructToJsonMap(scheduleInfo)
	if err != nil {
		return err
	}
	item.ScheduleInfo = scheTemp

	return nil
}

func UpdateChromaTask(tx *gorm.DB, item *model.ChromaTask, start time.Time, status enum.TaskStatus, code int, msg string) error {
	item.Status = status
	item.Code = code
	item.Message = msg

	err := UpdateSchedulerNode(start, item)
	if err != nil {
		return fmt.Errorf("update schedule node failed: %v", err)
	}

	err = item.Update(tx)
	if err != nil {
		return fmt.Errorf("update task database failed: %v", err)
	}

	return nil
}

func UpdateChromaSubTask(tx *gorm.DB, item *model.ChromaSubTask, start time.Time, status enum.TaskStatus, code int,
	msg string) error {
	item.Status = status
	item.Code = code
	item.Message = msg

	err := UpdateSubSchedulerNode(start, item)
	if err != nil {
		return fmt.Errorf("update sub schedule node failed: %v", err)
	}

	err = item.Update(tx)
	if err != nil {
		return fmt.Errorf("update sub task database failed: %v", err)
	}

	return nil
}

func UpdateChromaTaskWithLock(tx *gorm.DB, taskId string, start time.Time, status enum.TaskStatus, code int,
	msg string) error {
	redisProxy := redisproxy.GetRedisProxy()

	// 加锁
	redisLockKey := fmt.Sprintf(TaskScheduleLockFmt, sutils.GetNameByRunEnv(), taskId)
	redisLock := redislock.NewRedisLock(redisProxy.Rdb, redisLockKey, TaskScheduleLockExpireTime)
	ok, err := redisLock.Lock(context.Background())
	if err != nil {
		return fmt.Errorf("set redis fail: %v", err)
	} else if !ok {
		return nil
	}
	defer redisLock.Unlock(context.Background())

	item, err := (&model.ChromaTask{}).GetTasksWithTaskId(tx, taskId)
	if err != nil {
		return fmt.Errorf("get database fail: %v", err)
	}

	// 已经是这个状态了，就不用修改了
	if item.Status == status {
		return nil
	}

	// 需要回调的任务，这里进行回调
	if item.CallbackUrl != "" && len(item.VideoId) > 0 && status == enum.Running {
		logCtx := context.WithValue(context.TODO(), utils.CtxKeyLogID, item.TaskId)
		err := CallbackToVideoService(logCtx, item.CallbackUrl, item.VideoId, proto.PostProcessing, item.Message,
			item.OutputUrl)
		logger.Log.Infof(utils.MMark(logCtx)+"callback err: %v", err)
	}

	item.Status = status
	item.Code = code
	item.Message = msg

	err = UpdateSchedulerNode(start, item)
	if err != nil {
		return fmt.Errorf("update schedule node failed: %v", err)
	}

	err = item.Update(tx)
	if err != nil {
		return fmt.Errorf("update task database failed: %v", err)
	}

	return nil
}
