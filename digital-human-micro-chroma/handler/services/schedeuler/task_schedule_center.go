package schedeuler

import (
	"acg-ai-go-common/gomysql"
	"acg-ai-go-common/logger"
	"digital-human-micro-chroma/beans/enum"
	"digital-human-micro-chroma/beans/model"
	"digital-human-micro-chroma/conf"
	"digital-human-micro-chroma/handler/sutils"
	"fmt"
)

var (
	Scheduler *TaskScheduler
)

type TaskScheduler struct {
	workers []*TaskWorker // 注册的执行器
}

// NewTaskScheduler 初始化调度器，需要指定可用的执行器数量
func NewTaskScheduler(workerCount int) *TaskScheduler {
	podName, err := sutils.GetPodName()
	if err != nil {
		podName = "scheduler"
		logger.Log.Errorf("get pod name error，relace with [%s]", podName)
	}

	tc := &TaskScheduler{}
	for i := 0; i < workerCount; i++ {
		workerId := fmt.Sprintf(WorkerIdFormat, i)
		tc.workers = append(tc.workers, NewTaskWorker(workerId))
		logger.Log.Infof("add new task worker[%d]: %v", i, workerId)
	}
	return tc
}

func (p *TaskScheduler) RunScheduler() {
	logger.Log.Info("start run center scheduler")

	if conf.LocalConfig.ScheduleSettings.SubSwitch {
		// 处理已经拆分完毕的任务-子任务
		p.SubmitSubTaskSchedule()
	}

	if conf.LocalConfig.ScheduleSettings.MainSwitch {
		// 处理提交状态的任务
		p.SubmitTaskSchedule()

		// 处理进行中的任务
		p.RunningTaskSchedule()

		// 处理整合完需要回调的任务
		p.ConcatTaskSchedule()

		// 处理失败的任务，主要是为了重试
		p.FailedTaskSchedule()

		// 兜底策略，在服务更新等操作中被迫打断的任务，在此可以重置状态
		p.TimeoutTaskSchedule()
	}
}

// HasAvailableWorker 检查当前有没有空闲的执行器
func (p *TaskScheduler) HasAvailableWorker() bool {
	available := false

	for _, item := range p.workers {
		if item.isAvailable() {
			available = true
			break
		}
	}

	return available
}

func (p *TaskScheduler) SubmitTaskSchedule() {
	// 获取提交状态的任务，这种任务需要进行下载和拆分
	taskList, err := (&model.ChromaTask{}).GetTasksWithStatus(gomysql.DB, enum.Submit)
	if err != nil {
		logger.Log.Errorf("get task with status: %s, err: %v", enum.Submit, err)
		return
	}

	if len(taskList) < 1 {
		logger.Log.Infof("no task with status: %s need schedule", enum.Submit)
		return
	}

	// 这里进行简单的限流，当处于ready的任务超过一定的数量时，限制新进来的任务预处理的过程
	// 防止本地文件下载过多，或者短时间造成了大量的文件操作
	{
		readyTaskList, err := (&model.ChromaTask{}).GetTasksWithStatus(gomysql.DB, enum.Ready)
		if err != nil {
			logger.Log.Errorf("get task with status: %s, err: %v", enum.Ready, err)
			return
		}
		if conf.LocalConfig.ScheduleSettings.MaxRunningSize < len(readyTaskList) {
			logger.Log.Warnf("to many task: %d with status: %s, slow down task prepare", len(taskList), enum.Ready)
			return
		}
	}

	scheduleCount := 0
	for _, item := range taskList {
		// 逐个调度，错误已经在内部输出过了，这里只是判断调度成功与否
		err := ScheduleSubmitTask(item)
		if err == nil {
			scheduleCount++
		}

		// 这是个初级策略，存在大量提交任务时，不需要一次性处理完
		// 这里是任务的起点，这个环节限制之后，后续的环节就会减少队列数量，不需要再次限制
		if scheduleCount > conf.LocalConfig.ScheduleSettings.MaxRunningSize {
			logger.Log.Warnf("already schedule task: %d with status: %s, break and wait next round", scheduleCount,
				enum.Submit)
			break
		}
	}
	logger.Log.Infof("scheduled task: %d with status: %s", scheduleCount, enum.Submit)
}

func (p *TaskScheduler) SubmitSubTaskSchedule() {
	// 在子任务表中获取已提交状态的任务，这种任务已经进行了下载和拆分，调度到执行器上即可
	taskList, err := (&model.ChromaSubTask{}).GetTasksWithStatus(gomysql.DB, enum.Submit)
	if err != nil {
		logger.Log.Errorf("get subtask with status: %s, err: %v", enum.Submit, err)
		return
	}

	if len(taskList) < 1 {
		logger.Log.Infof("no subtask with status: %s need schedule", enum.Submit)
		return
	}

	logger.Log.Infof("start schedule subtask: %d with status: %s", len(taskList), enum.Submit)
	for _, item := range taskList {
		// 在这个副本上，如果没有空闲的执行器了就不用调度了
		if !p.HasAvailableWorker() {
			break
		}
		// 逐个调度
		ScheduleSubmitSubTask(item)
	}
}

func (p *TaskScheduler) RunningTaskSchedule() {
	// 获取运行中的任务，如果它的子任务都成功了，那么可以进行整合操作了
	taskList, err := (&model.ChromaTask{}).GetTasksWithStatus(gomysql.DB, enum.Running)
	if err != nil {
		logger.Log.Errorf("get task with status: %s, err: %v", enum.Running, err)
		return
	}

	if len(taskList) < 1 {
		logger.Log.Infof("no task with status: %s need schedule", enum.Running)
		return
	}

	logger.Log.Infof("start schedule task: %d with status: %s", len(taskList), enum.Running)
	for _, item := range taskList {
		// 逐个检查
		ScheduleRunningTask(item)
	}
}

func (p *TaskScheduler) ConcatTaskSchedule() {
	// 已经整合的任务，这类任务只需要上报回调即可
	taskList, err := (&model.ChromaTask{}).GetTasksWithStatus(gomysql.DB, enum.ConCat)
	if err != nil {
		logger.Log.Errorf("get task with status: %s, err: %v", enum.ConCat, err)
		return
	}

	if len(taskList) < 1 {
		logger.Log.Infof("no task with status: %s need schedule", enum.ConCat)
		return
	}

	logger.Log.Infof("start schedule task: %d with status: %s", len(taskList), enum.ConCat)
	for _, item := range taskList {
		// 逐个检查
		ScheduleConcatTask(item)
	}
}

func (p *TaskScheduler) FailedTaskSchedule() {
	for {
		taskList, err := (&model.ChromaTask{}).GetTasksWithStatusWithRetry(gomysql.DB, enum.Failed)
		if err != nil {
			logger.Log.Errorf("get task with status: %s, err: %v", enum.Failed, err)
			break
		}

		if len(taskList) < 1 {
			logger.Log.Infof("no task with status: %s need schedule", enum.Failed)
			break
		}

		logger.Log.Infof("start schedule task: %d with status: %s", len(taskList), enum.Failed)
		for _, item := range taskList {
			// 逐个检查
			ScheduleFailedTask(item)
		}

		break
	}
	for {
		taskList, err := (&model.ChromaSubTask{}).GetTasksWithStatusWithRetry(gomysql.DB, enum.Failed)
		if err != nil {
			logger.Log.Errorf("get subtask with status: %s, err: %v", enum.Failed, err)
			break
		}

		if len(taskList) < 1 {
			logger.Log.Infof("no subtask with status: %s need schedule", enum.Failed)
			break
		}

		logger.Log.Infof("start schedule subtask: %d with status: %s", len(taskList), enum.Failed)
		for _, item := range taskList {
			// 逐个检查
			ScheduleFailedSubTask(item)
		}

		break
	}
}

func (p *TaskScheduler) TimeoutTaskSchedule() {
	// 当前对于主任务，有 submit\ready\running\concat\failed\success 几种状态
	// 但不管停在哪一种，都可以通过调度继续下一步或者置为失败状态，可以不进行兜底
	// ...

	// 对于子任务，有 submit\running\failed\success 几种状态
	// 其中，running 态是ffmpeg进程的执行过程，如果副本异常，该进程终止，状态不可恢复，这里进行超时处理
	{
		taskList, err := (&model.ChromaSubTask{}).GetTasksWithStatus(gomysql.DB, enum.Running)
		if err != nil {
			logger.Log.Errorf("get subtask with status: %s, err: %v", enum.Running, err)
		}

		if len(taskList) < 1 {
			logger.Log.Infof("no subtask with status: %s need schedule", enum.Running)
		}

		// 这种子任务，一次处理，主要是置为失败状态，等待下一次自然重试即可
		for _, item := range taskList {
			ScheduleTimeoutSubTask(item)
		}
	}

}
