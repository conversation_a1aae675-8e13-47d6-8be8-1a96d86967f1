package schedeuler

import (
	"acg-ai-go-common/gomysql"
	"acg-ai-go-common/logger"
	"acg-ai-go-common/utils"
	"acg-ai-go-common/utils/redisproxy"
	"context"
	"digital-human-micro-chroma/beans/enum"
	"digital-human-micro-chroma/beans/model"
	"digital-human-micro-chroma/beans/proto"
	"digital-human-micro-chroma/conf"
	"digital-human-micro-chroma/handler/sutils"
	"digital-human-micro-chroma/handler/sutils/ffmpegutils"
	"digital-human-micro-chroma/handler/sutils/fileutils"
	"digital-human-micro-chroma/handler/sutils/jsonutils"
	"digital-human-micro-chroma/handler/sutils/redislock"
	"fmt"
	"gorm.io/gorm"
	"path/filepath"
	"time"
)

func ScheduleSubmitTask(task *model.ChromaTask) error {
	logCtx := context.WithValue(context.TODO(), utils.CtxKeyLogID, task.TaskId)
	redisProxy := redisproxy.GetRedisProxy()

	// 加锁
	redisLockKey := fmt.Sprintf(TaskScheduleLockFmt, sutils.GetNameByRunEnv(), task.TaskId)
	redisLock := redislock.NewRedisLock(redisProxy.Rdb, redisLockKey, TaskScheduleLockExpireTime)
	ok, err := redisLock.Lock(context.Background())
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"ScheduleSubmitTask Lock key: %s error: %+v\n", redisLockKey, err)
		return fmt.Errorf("get redis lock err: %v", err)
	} else if !ok {
		return fmt.Errorf("redis key locked by other instance")
	}
	defer redisLock.Unlock(context.Background())

	start := time.Now()

	// 重新查询
	item, err := task.GetTasksWithTaskId(gomysql.DB, task.TaskId)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"refresh task err: %v", err)
		return fmt.Errorf("get refresh task err: %v", err)
	}

	// 状态变了，就不用处理了
	if item.Status != enum.Submit {
		return fmt.Errorf("task status nolonger %s", enum.Submit)
	}

	err = gomysql.DB.Transaction(func(tx *gorm.DB) error {
		taskFileFullPath := item.VideoUrl

		// 清空错误状态
		item.Code = enum.ErrCodeSuccess
		item.Message = ""

		// 计算文件的基础信息，路径、文件名、后缀
		taskCachePath, filenameNoExt, suffix, err := getTaskCacheInfo(item)
		if err != nil {
			return fmt.Errorf("parse video url failed: %v", err)
		}

		// 创建缓存目录
		err = fileutils.EnsureDir(taskCachePath)
		if err != nil {
			return fmt.Errorf("create cache folder failed: %v", err)
		}

		// 拆分后的文件名命名规则和搜索分片的匹配规则
		partName := fmt.Sprintf(TaskCachePartPathFmt, taskCachePath) + "/" + filenameNoExt + SplitPartFileNameFmt + suffix
		audioName := fmt.Sprintf(TaskCacheOutputPathFmt, taskCachePath) + "/" + filenameNoExt + ".wav"
		searchName := fmt.Sprintf(TaskCachePartPathFmt, taskCachePath) + "/" + filenameNoExt + SearchPartFileNameFmt + suffix

		// 本地输入文件的完整路径
		tempFileFullPath := fmt.Sprintf(TaskCacheInFileNameFmt, taskCachePath, filenameNoExt, suffix)

		// 检查一下视频地址，如果本地有文件，则直接使用，否则下载下来
		if fileutils.IsExists(tempFileFullPath) {
			taskFileFullPath = tempFileFullPath
			logger.Log.Infof(utils.MMark(logCtx)+"found local file: %v, use it", taskFileFullPath)
		} else {
			// 下载
			logger.Log.Infof(utils.MMark(logCtx)+"start to download file: %v", tempFileFullPath)
			err = fileutils.RetryDownloadFile(item.VideoUrl, tempFileFullPath, conf.LocalConfig.ScheduleSettings.HttpRetryCount)
			if err != nil {
				return fmt.Errorf("download video failed: %v", err)
			}

			logger.Log.Infof(utils.MMark(logCtx)+"download to local done, file: %v", tempFileFullPath)
			taskFileFullPath = tempFileFullPath
		}

		// 用 ffprobe 检查，视频数据不合法都认为有问题
		videoInfo, err := ffmpegutils.GetVideoInfo(taskFileFullPath)
		if err != nil {
			return fmt.Errorf("get video info failed: %v", err)
		}
		logger.Log.Infof(utils.MMark(logCtx)+"get video info: %v", videoInfo)

		// 下载抠绿需要的依赖组件
		config, err := PrepareChromaKeyDepends(taskCachePath, item)
		if err != nil {
			return fmt.Errorf("prepare shader failed: %v", err)
		}
		logger.Log.Infof(utils.MMark(logCtx)+"download all shader: %v", config)

		// 计算允许分割的时间
		need, segmentTime := CalculateSegmentTime(videoInfo)
		if !need {
			// 不需要拆分，拷贝文件到 part 目录下
			tarPartFileFullPath := fmt.Sprintf(TaskCachePartPathFmt,
				taskCachePath) + "/" + filenameNoExt + "_part_000" + suffix
			if err = fileutils.CopyFile(taskFileFullPath, tarPartFileFullPath); err != nil {
				return fmt.Errorf("file copy: %s error: %v", tarPartFileFullPath, err)
			}

			// 单独拆出来音频文件
			logger.Log.Infof(utils.MMark(logCtx)+"start ffmpeg split the audio data to %s", audioName)
			err = ffmpegutils.ExtractAudio(logCtx, taskFileFullPath, audioName)
			if err != nil {
				return fmt.Errorf("ffmpeg split audio failed: %v", err)
			}

			err = SubmitChromaSubTask(tx, taskCachePath, tarPartFileFullPath, config, item)
			if err != nil {
				return fmt.Errorf("no segment task submit to sub error: %v", err)
			}

			err = UpdateChromaTask(tx, item, start, enum.Ready, enum.ErrCodeSuccess, "")
			if err != nil {
				return fmt.Errorf("no segment task update status: %s error: %v", enum.Ready, err)
			}

			logger.Log.Infof(utils.MMark(logCtx) + "success submit to sub task with no segment")
			return nil
		}
		logger.Log.Infof(utils.MMark(logCtx)+"need split: %v, segmentTime: %v", need, segmentTime)

		// 创建缓存路径
		err = fileutils.EnsureDir(partName)
		if err != nil {
			return fmt.Errorf("create cache folder failed: %v", err)
		}

		// 进行拆分，不包含音频数据
		logger.Log.Infof(utils.MMark(logCtx)+"start ffmpeg split with segmentTime: %d", segmentTime)
		err = ffmpegutils.SplitVideoIntoPartsWithNoAudio(logCtx, taskFileFullPath, partName, segmentTime)
		if err != nil {
			return fmt.Errorf("ffmpeg split video failed: %v", err)
		}

		// 单独拆出来音频文件
		logger.Log.Infof(utils.MMark(logCtx)+"start ffmpeg split the audio data to %s", audioName)
		err = ffmpegutils.ExtractAudio(logCtx, taskFileFullPath, audioName)
		if err != nil {
			return fmt.Errorf("ffmpeg split audio failed: %v", err)
		}

		// 罗列所有拆分后的文件
		list, err := fileutils.ListFiles(searchName)
		if err != nil || len(list) < 1 {
			return fmt.Errorf("list video parts failed: %v", err)
		}
		logger.Log.Infof(utils.MMark(logCtx)+"split done, now files: %v", list)

		// 批量提交到子任务的数据表
		err = BatchSubmitChromaSubTask(tx, taskCachePath, list, config, item)
		if err != nil {
			return fmt.Errorf("submit batch: %d to sub task error: %v", len(list), err)
		}

		// 更新任务状态
		item.SegmentCount = len(list)
		item.SegmentTime = segmentTime
		err = UpdateChromaTask(tx, item, start, enum.Ready, enum.ErrCodeSuccess, "")
		if err != nil {
			return fmt.Errorf("update task status: %v error: %+v", item.Status, err)
		}

		logger.Log.Infof(utils.MMark(logCtx)+"update task status: %s", enum.Ready)
		return nil
	})

	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"schedule failed, err: %+v\n", err)
		err = UpdateChromaTask(gomysql.DB, item, start, enum.Failed, enum.ErrCodeSchedule, fmt.Sprintf("%v", err))
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+"update task status: %s error: %+v\n", task.Status, err)
		}
		return fmt.Errorf("schedule task err: %v", err)
	}

	logger.Log.Infof(utils.MMark(logCtx) + "success submit to sub task")
	return nil
}

func CalculateSegmentTime(info *ffmpegutils.VideoInfo) (bool, int) {
	segmentTime := -1
	need := false

	if info == nil || info.Duration < 0 {
		return false, segmentTime
	}

	// 默认配置是不拆分
	if conf.LocalConfig.PreProcessSettings.SegmentCount <= 1 {
		return false, segmentTime
	}

	// 小于最小分割视频时长，则不分割
	if info.Duration < float64(conf.LocalConfig.PreProcessSettings.MinSegmentDuration) {
		return false, segmentTime
	}

	// 计算分割次数，按默认分割和可分割的大值来计算
	count := int(info.Duration) / conf.LocalConfig.PreProcessSettings.MinSegmentUnitTime
	if count > conf.LocalConfig.PreProcessSettings.SegmentCount {
		count = conf.LocalConfig.PreProcessSettings.SegmentCount
	}

	// 计算分割时间，分割成整数时间即可
	segmentTime = int(info.Duration) / count

	// 如果时间向下取整，则加1个单位,防止出现尾巴很小的片段
	if float64(segmentTime)*float64(count) < info.Duration {
		segmentTime += conf.LocalConfig.PreProcessSettings.MinSegmentUnitTime
	}

	// 至少分割两个片段且每个片段时间都不为0
	if count > 1 && segmentTime > 0 {
		need = true
	}

	return need, segmentTime
}

func SubmitChromaSubTask(tx *gorm.DB, rootPath string, filename string, config jsonutils.JSONMap,
	task *model.ChromaTask) error {
	_, scheduleInfo, err := model.NewScheduleInfo(conf.LocalConfig.ScheduleSettings.MaxChromaRetryCount)
	if err != nil {
		return err
	}

	item := model.ChromaSubTask{
		TaskId:       task.TaskId,
		SubId:        "scma-" + utils.RandStringRunes(24),
		Video:        filepath.Base(filename),
		Path:         rootPath,
		Version:      task.Version,
		Width:        task.Width,
		Height:       task.Height,
		Config:       config,
		Retry:        1,
		RetryCount:   0,
		RetryMax:     conf.LocalConfig.ScheduleSettings.MaxChromaRetryCount,
		Status:       enum.Submit,
		ScheduleInfo: scheduleInfo,
		CreatedAt:    time.Now(),
		UpdatedAt:    time.Now(),
	}

	err = item.Create(tx)
	if err != nil {
		return err
	}
	return nil

}

func BatchSubmitChromaSubTask(tx *gorm.DB, rootPath string, fileList []string, config jsonutils.JSONMap, task *model.ChromaTask) error {
	for _, file := range fileList {
		err := SubmitChromaSubTask(tx, rootPath, file, config, task)
		if err != nil {
			return err
		}
	}

	return nil
}

func PrepareChromaKeyDepends(cachePath string, task *model.ChromaTask) (jsonutils.JSONMap, error) {
	result := jsonutils.JSONMap{}

	if task.Version == proto.VERSION1 || task.Version == proto.VERSION2 {
		str, err := jsonutils.GetStringFromJson(task.Config, proto.KeyVertexShader)
		if err != nil || len(str) < 1 {
			return nil, fmt.Errorf("invalid param: %v", proto.KeyVertexShader)
		}

		filename, err := PrepareShader(cachePath, str)
		if err != nil {
			return nil, err
		}

		result[proto.KeyVertexShader] = filename
	}

	if task.Version == proto.VERSION1 || task.Version == proto.VERSION2 {
		str, err := jsonutils.GetStringFromJson(task.Config, proto.KeyFragmentShader)
		if err != nil || len(str) < 1 {
			return nil, fmt.Errorf("invalid param: %v", proto.KeyFragmentShader)
		}

		filename, err := PrepareShader(cachePath, str)
		if err != nil {
			return nil, err
		}

		result[proto.KeyFragmentShader] = filename
	}

	if task.Version == proto.VERSION2 {
		str, err := jsonutils.GetStringFromJson(task.Config, proto.KeyPostVertexShader)
		if err != nil || len(str) < 1 {
			return nil, fmt.Errorf("invalid param: %v", proto.KeyPostVertexShader)
		}

		filename, err := PrepareShader(cachePath, str)
		if err != nil {
			return nil, err
		}

		result[proto.KeyPostVertexShader] = filename
	}

	if task.Version == proto.VERSION2 {
		str, err := jsonutils.GetStringFromJson(task.Config, proto.KeyPostFragmentShader)
		if err != nil || len(str) < 1 {
			return nil, fmt.Errorf("invalid param: %v", proto.KeyPostFragmentShader)
		}

		filename, err := PrepareShader(cachePath, str)
		if err != nil {
			return nil, err
		}

		result[proto.KeyPostFragmentShader] = filename
	}

	return result, nil
}

func PrepareShader(cachePath string, url string) (string, error) {
	// 计算文件的基础信息，路径、文件名、后缀
	_, suffix, err := fileutils.GetFilenameAndExt(url)
	if err != nil {
		return "", fmt.Errorf("parse shader url failed: %v", err)
	}

	// 生成无后缀的文件名，以及缓存的根目录
	filenameNoExt := fileutils.Sha256Hash(url)

	downloadFileName := filenameNoExt + suffix
	downloadFullPath := fmt.Sprintf(TaskCacheShaderPathFmt, cachePath) + "/" + downloadFileName

	// 如果本地已经有了文件了，就不需要再下载了
	if fileutils.IsExists(downloadFullPath) {
		return downloadFileName, nil
	}

	err = fileutils.RetryDownloadFile(url, downloadFullPath, conf.LocalConfig.ScheduleSettings.HttpRetryCount)
	if err != nil {
		return "", fmt.Errorf("download shader: %s failed, err: %v", url, err)
	}

	return downloadFileName, nil
}
