package schedeuler

import (
	"acg-ai-go-common/gomysql"
	"acg-ai-go-common/logger"
	"acg-ai-go-common/utils"
	"acg-ai-go-common/utils/redisproxy"
	"context"
	"digital-human-micro-chroma/beans/enum"
	"digital-human-micro-chroma/beans/model"
	"digital-human-micro-chroma/beans/proto"
	"digital-human-micro-chroma/handler/sutils"
	"digital-human-micro-chroma/handler/sutils/jsonutils"
	"digital-human-micro-chroma/handler/sutils/redislock"
	"fmt"
	"gorm.io/gorm"
	"time"
)

func ScheduleFailedTask(task *model.ChromaTask) {
	logCtx := context.WithValue(context.TODO(), utils.CtxKeyLogID, task.TaskId)
	redisProxy := redisproxy.GetRedisProxy()

	// 加锁
	redisLockKey := fmt.Sprintf(TaskScheduleLockFmt, sutils.GetNameByRunEnv(), task.TaskId)
	redisLock := redislock.NewRedisLock(redisProxy.Rdb, redisLockKey, TaskScheduleLockExpireTime)
	ok, err := redisLock.Lock(context.Background())
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"Lock key: %s error: %+v\n", redisLockKey, err)
		return
	} else if !ok {
		return
	}
	defer redisLock.Unlock(context.Background())

	// 重新查询
	item, err := task.GetTasksWithTaskId(gomysql.DB, task.TaskId)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"get new task err: %v", err)
		return
	}

	// 状态变了，或者已经不可以再重试了
	if item.Status != enum.Failed || item.Retry != 1 {
		return
	}

	start := time.Now()

	// 核心处理的事务内容
	err = gomysql.DB.Transaction(func(tx *gorm.DB) error {
		item, err := item.GetTasksWithTaskId(tx, item.TaskId)
		if err != nil {
			return fmt.Errorf("get task status failed: %v", err)
		}

		// 不再是失败状态了，不用管
		if item.Status != enum.Failed {
			return nil
		}

		var scheduleInfo model.ScheduleInfo
		err = jsonutils.JsonMapToStruct(item.ScheduleInfo, &scheduleInfo)
		if err != nil {
			return fmt.Errorf("parse schedule failed: %v", err)
		}

		// 任务可以重试
		if item.RetryCount < item.RetryMax {
			logger.Log.Infof(utils.MMark(logCtx) + "task can be retry, refresh it.")

			// 删除提交的子任务
			if err = (&model.ChromaSubTask{}).DeleteByTaskId(tx, item.TaskId); err != nil {
				return fmt.Errorf("delete binding subtasks err: %v", err)
			}

			// 重新等待调度
			err := UpdateChromaTask(tx, item, start, enum.Submit, enum.ErrCodeSuccess, "")
			if err != nil {
				return fmt.Errorf("update status: Submit err: %v", err)
			}
			logger.Log.Infof(utils.MMark(logCtx)+"update subtask status: %s", enum.Submit)
		} else {
			// 不可以重试了
			if item.CallbackUrl != "" && len(item.VideoId) > 0 {
				err := CallbackToVideoService(logCtx, item.CallbackUrl, item.VideoId, proto.Failed, item.Message,
					item.OutputUrl)
				if err != nil {
					logger.Log.Errorf(utils.MMark(logCtx)+"callback err: %v", err)
				}
			}

			item.Retry = 0
			err = UpdateChromaTask(tx, item, start, enum.Failed, enum.ErrCodeFile, "")
			if err != nil {
				return fmt.Errorf("update status: Failed err: %v", err)
			}
			logger.Log.Infof(utils.MMark(logCtx)+"update task status: %s", enum.Failed)
		}

		return nil
	})

	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"running task schedule failed, err: %+v\n", err)

		// 更新任务状态
		err := UpdateChromaTask(gomysql.DB, item, start, enum.Failed, enum.ErrCodeSchedule, fmt.Sprintf("%v", err))
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+"running task update database failed, err: %+v\n", err)
		}
		return
	}

	logger.Log.Infof(utils.MMark(logCtx) + "success schedule failed task")
}

func ScheduleFailedSubTask(task *model.ChromaSubTask) {
	logCtx := context.WithValue(context.TODO(), utils.CtxKeyLogID, task.SubId)
	redisProxy := redisproxy.GetRedisProxy()

	// 加锁
	redisLockKey := fmt.Sprintf(TaskScheduleLockFmt, sutils.GetNameByRunEnv(), task.SubId)
	redisLock := redislock.NewRedisLock(redisProxy.Rdb, redisLockKey, TaskScheduleLockExpireTime)
	ok, err := redisLock.Lock(context.Background())
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"Lock key: %s error: %+v\n", redisLockKey, err)
		return
	} else if !ok {
		return
	}
	defer redisLock.Unlock(context.Background())

	// 重新查询
	item, err := task.GetTasksWithSubId(gomysql.DB, task.SubId)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"get new task err: %v", err)
		return
	}

	// 状态变了，就不用处理了
	if item.Status != enum.Failed || item.Retry != 1 {
		return
	}

	start := time.Now()

	// 核心处理的事务内容
	err = gomysql.DB.Transaction(func(tx *gorm.DB) error {
		item, err := item.GetTasksWithSubId(tx, item.SubId)
		if err != nil {
			return fmt.Errorf("get task status failed: %v", err)
		}

		// 不再是失败状态了，不用管
		if item.Status != enum.Failed {
			return nil
		}

		var scheduleInfo model.ScheduleInfo
		err = jsonutils.JsonMapToStruct(item.ScheduleInfo, &scheduleInfo)
		if err != nil {
			return fmt.Errorf("parse schedule failed: %v", err)
		}

		// 任务可以重试
		if item.RetryCount < item.RetryMax {
			logger.Log.Infof(utils.MMark(logCtx) + "subtask can be retry, refresh it.")

			err := UpdateChromaSubTask(tx, item, start, enum.Submit, enum.ErrCodeSuccess, "")
			if err != nil {
				return fmt.Errorf("update status: Submit err: %v", err)
			}
			logger.Log.Infof(utils.MMark(logCtx)+"update subtask status: %s", enum.Submit)
		} else {
			// 不可以重试了
			logger.Log.Infof(utils.MMark(logCtx) + "subtask cannot be retry, stop it.")

			item.Retry = 0
			err := UpdateChromaSubTask(tx, item, start, enum.Failed, enum.ErrCodeFile, "")
			if err != nil {
				return fmt.Errorf("update status: Failed err: %v", err)
			}
			logger.Log.Infof(utils.MMark(logCtx)+"update subtask status: %s", enum.Failed)
		}
		return nil
	})

	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"running task schedule failed, err: %+v\n", err)

		// 更新任务状态
		err := UpdateChromaSubTask(gomysql.DB, item, start, enum.Failed, enum.ErrCodeSchedule, fmt.Sprintf("%v", err))
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+"running task update database failed, err: %+v\n", err)
		}
		return
	}

	logger.Log.Infof(utils.MMark(logCtx) + "success schedule failed subtask")
}
