package schedeuler

import (
	"acg-ai-go-common/gomysql"
	"acg-ai-go-common/logger"
	"acg-ai-go-common/utils"
	"acg-ai-go-common/utils/redisproxy"
	"context"
	"digital-human-micro-chroma/beans/enum"
	"digital-human-micro-chroma/beans/model"
	"digital-human-micro-chroma/conf"
	"digital-human-micro-chroma/handler/sutils"
	"digital-human-micro-chroma/handler/sutils/ffmpegutils"
	"digital-human-micro-chroma/handler/sutils/fileutils"
	"digital-human-micro-chroma/handler/sutils/redislock"
	"fmt"
	"gorm.io/gorm"
	"os"
	"time"
)

func ScheduleRunningTask(task *model.ChromaTask) {
	logCtx := context.WithValue(context.TODO(), utils.CtxKeyLogID, task.TaskId)
	redisProxy := redisproxy.GetRedisProxy()

	// 加锁
	redisLockKey := fmt.Sprintf(TaskScheduleLockFmt, sutils.GetNameByRunEnv(), task.TaskId)
	redisLock := redislock.NewRedisLock(redisProxy.Rdb, redisLockKey, TaskScheduleLockExpireTime)
	ok, err := redisLock.Lock(context.Background())
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"Lock key: %s error: %+v\n", redisLockKey, err)
		return
	} else if !ok {
		return
	}
	defer redisLock.Unlock(context.Background())

	// 重新查询
	item, err := task.GetTasksWithTaskId(gomysql.DB, task.TaskId)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"refresh task err: %v", err)
		return
	}

	// 状态变了，就不用处理了
	if item.Status != enum.Running {
		return
	}

	start := time.Now()

	// 核心处理的事务内容
	err = gomysql.DB.Transaction(func(tx *gorm.DB) error {
		finish, success, err := CheckAllSubTaskSuccess(tx, item)
		if err != nil {
			return fmt.Errorf("check sub task status failed: %v", err)
		}

		// 这个查询比较多，只有在 失败 或 已结束 的情况下打印一下日志
		if !success || finish {
			logger.Log.Infof(utils.MMark(logCtx)+"check all subtask status, finish: %v, success: %v", finish, success)
		}

		// 失败了
		if !success {
			err = UpdateChromaTask(tx, item, start, enum.Failed, enum.ErrCodeSchedule, "")
			if err != nil {
				return fmt.Errorf("update status: Failed err: %v", err)
			}
			return nil
		}

		// 还在进行，不用管
		if !finish {
			return nil
		}

		// 整合文件
		outputFile, err := ConcatOutputVideo(logCtx, tx, item)
		if err != nil {
			return fmt.Errorf("make concat video failed: %v", err)
		}
		logger.Log.Infof(utils.MMark(logCtx)+"make concat video output to: %s", outputFile)

		// 上传文件
		_, _, outSuffix := fileutils.GetFileDetails(outputFile)
		url, err := fileutils.RetryUploadBosServiceFromFile(logCtx, BosChromaKeyOutPath, outputFile, outSuffix)
		if err != nil {
			return fmt.Errorf("upload bos err: %v", err)
		}
		item.OutputUrl = url
		logger.Log.Infof(utils.MMark(logCtx)+"upload video to bos: %s", item.OutputUrl)

		// 更新任务状态
		err = UpdateChromaTask(tx, item, start, enum.ConCat, enum.ErrCodeSuccess, "")
		if err != nil {
			return fmt.Errorf("update status: %s err: %v", enum.ConCat, err)
		}
		logger.Log.Infof(utils.MMark(logCtx)+"update task status: %s", enum.ConCat)

		// 如果需要后处理，把文件复制到指定位置去
		if cpOut, err := PostCopyOutput(item); err != nil {
			logger.Log.Warnf(utils.MMark(logCtx)+"post copy output, ps: only for speed up, mistake: %v", err)
		} else {
			logger.Log.Infof(utils.MMark(logCtx)+"post copy output to %s", cpOut)
		}

		// 清理缓存
		err = ClearProgressFile(item)
		if err != nil {
			return fmt.Errorf("clear cache failed: %v", err)
		}
		logger.Log.Infof(utils.MMark(logCtx) + "clear task progress cache")

		return nil
	})

	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"running task schedule failed, err: %+v\n", err)

		// 更新任务状态
		err := UpdateChromaTask(gomysql.DB, item, start, enum.Failed, enum.ErrCodeSchedule, fmt.Sprintf("%v", err))
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+"running task update database failed, err: %+v\n", err)
		}
		return
	}

	logger.Log.Infof(utils.MMark(logCtx) + "success schedule running task")
}

func CheckAllSubTaskSuccess(tx *gorm.DB, task *model.ChromaTask) (bool, bool, error) {
	finish := false
	success := true

	subTaskList, err := (&model.ChromaSubTask{}).GetTasksWithTaskId(tx, task.TaskId)
	if err != nil {
		return false, true, fmt.Errorf("get subtask list failed: %v", err)
	}

	// 有一个子任务失败了，且不允许再重试了，那整个都失败了
	for _, item := range subTaskList {
		if item.Status == enum.Failed && item.Retry == 0 {
			finish = true
			success = false
			break
		}
	}

	if !finish {
		// 有一个子任务还在运行中
		for _, item := range subTaskList {
			if item.Status != enum.Success {
				finish = false
				break
			} else {
				finish = true
			}
		}
	}

	return finish, success, nil
}

func ConcatOutputVideo(ctx context.Context, tx *gorm.DB, task *model.ChromaTask) (string, error) {
	// 计算文件的基础信息，路径、文件名、后缀
	taskCachePath, filenameNoExt, _, err := getTaskCacheInfo(task)
	if err != nil {
		return "", fmt.Errorf("parse video url failed: %v", err)
	}

	// 列举所有的文件
	searchName := fmt.Sprintf("%s/output/%s", taskCachePath, filenameNoExt) + SearchPartFileNameFmt
	list, err := fileutils.ListFiles(searchName)
	if err != nil || len(list) < 1 {
		return "", fmt.Errorf("search output file empty, or err: %v", err)
	}

	// 输出的列表描述文件
	outListFileName := fmt.Sprintf("%s/output/output.txt", taskCachePath)
	err = WriteFileList(outListFileName, list)
	if err != nil {
		return "", fmt.Errorf("create list file failed: %v", err)
	}

	// 可以整合的文件名
	outputVideoFullPath := fmt.Sprintf(TaskCacheOutFileNameFmt, taskCachePath, fmt.Sprintf("%s_na", filenameNoExt))
	err = ffmpegutils.ConcatVideo(ctx, outListFileName, outputVideoFullPath)
	if err != nil {
		return "", fmt.Errorf("ffmpeg concat files err: %v", err)
	}

	// 整合音频
	audioFullPath := fmt.Sprintf(TaskCacheOutputPathFmt, taskCachePath) + "/" + filenameNoExt + ".wav"
	outputFullPath := fmt.Sprintf(TaskCacheOutFileNameFmt, taskCachePath, filenameNoExt)

	err = ffmpegutils.MergeAudioVideo(ctx, outputVideoFullPath, audioFullPath, outputFullPath,
		conf.LocalConfig.AudioSettings.Codec, conf.LocalConfig.AudioSettings.Channels,
		conf.LocalConfig.AudioSettings.SampleRate)
	if err != nil {
		return "", fmt.Errorf("ffmpeg merge audio err: %v", err)
	}

	return outputFullPath, nil
}

func WriteFileList(filePath string, files []string) error {
	f, err := os.Create(filePath)
	if err != nil {
		return fmt.Errorf("create file failed: %v", err)
	}
	defer f.Close()

	for _, file := range files {
		line := fmt.Sprintf("file '%s'\n", file)
		if _, err := f.WriteString(line); err != nil {
			return fmt.Errorf("write to file failed: %v", err)
		}
	}

	return nil
}
