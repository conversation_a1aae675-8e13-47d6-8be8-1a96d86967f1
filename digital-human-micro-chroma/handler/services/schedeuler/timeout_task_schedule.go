package schedeuler

import (
	"acg-ai-go-common/gomysql"
	"acg-ai-go-common/logger"
	"acg-ai-go-common/utils"
	"acg-ai-go-common/utils/redisproxy"
	"context"
	"digital-human-micro-chroma/beans/enum"
	"digital-human-micro-chroma/beans/model"
	"digital-human-micro-chroma/conf"
	"digital-human-micro-chroma/handler/sutils"
	"digital-human-micro-chroma/handler/sutils/redislock"
	"fmt"
	"gorm.io/gorm"
	"time"
)

func ScheduleTimeoutSubTask(task *model.ChromaSubTask) {
	logCtx := context.WithValue(context.TODO(), utils.CtxKeyLogID, fmt.Sprintf("%s:%s", task.TaskId, task.SubId))
	redisProxy := redisproxy.GetRedisProxy()

	// 加锁
	redisLockKey := fmt.Sprintf(TaskScheduleLockFmt, sutils.GetNameByRunEnv(), task.SubId)
	redisLock := redislock.NewRedisLock(redisProxy.Rdb, redisLockKey, TaskScheduleLockExpireTime)
	ok, err := redisLock.Lock(context.Background())
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"Lock key: %s error: %+v\n", redisLockKey, err)
		return
	} else if !ok {
		return
	}
	defer redisLock.Unlock(context.Background())

	// 重新查询
	item, err := task.GetTasksWithSubId(gomysql.DB, task.SubId)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"refresh task err: %v", err)
		return
	}

	// 状态变了，就不用处理了
	if item.Status != enum.Running {
		return
	}

	start := time.Now()

	// 核心处理的事务内容
	err = gomysql.DB.Transaction(func(tx *gorm.DB) error {
		expirationDuration := time.Second * time.Duration(conf.LocalConfig.ChromaSettings.Timeout)

		if time.Since(item.UpdatedAt) < expirationDuration {
			return nil
		}

		// 超时了，任务判为失败，下次会重试
		logger.Log.Errorf(utils.MMark(logCtx)+"sub task ffmpeg running timeout, try reset, start at %v", item.UpdatedAt)
		err := UpdateChromaSubTask(gomysql.DB, item, start, enum.Failed, enum.ErrCodeSchedule,
			fmt.Sprintf("ffmpeg timeout"))
		if err != nil {
			return fmt.Errorf("reset status to %s cause ffmpeg timeout, err: %v", enum.Failed, err)
		}

		logger.Log.Errorf(utils.MMark(logCtx)+"sub task reset to %s", enum.Failed)
		return nil
	})

	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"sub task timeout schedule failed, err: %+v\n", err)

		// 更新任务状态
		err := UpdateChromaSubTask(gomysql.DB, item, start, enum.Failed, enum.ErrCodeSchedule, fmt.Sprintf("%v", err))
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+"sub task update database failed, err: %+v\n", err)
		}
		return
	}
}
