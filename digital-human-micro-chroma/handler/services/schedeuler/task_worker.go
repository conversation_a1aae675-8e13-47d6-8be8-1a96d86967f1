package schedeuler

import (
	"acg-ai-go-common/gomysql"
	"acg-ai-go-common/logger"
	"acg-ai-go-common/utils"
	"context"
	"digital-human-micro-chroma/beans/enum"
	"digital-human-micro-chroma/beans/model"
	"digital-human-micro-chroma/beans/proto"
	"digital-human-micro-chroma/conf"
	"digital-human-micro-chroma/handler/sutils/ffmpegutils"
	"digital-human-micro-chroma/handler/sutils/fileutils"
	"digital-human-micro-chroma/handler/sutils/jsonutils"
	"fmt"
	"gorm.io/gorm"
	"sync"
	"time"
)

type TaskWorker struct {
	ID       string                   // 执行器ID
	taskChan chan model.ChromaSubTask // 用于接收任务的channel
	isBusy   bool                     // 忙碌
	lock     sync.Mutex
}

func NewTaskWorker(id string) *TaskWorker {
	s := &TaskWorker{
		ID:       id,
		taskChan: make(chan model.ChromaSubTask, 1),
		isBusy:   false,
	}

	go s.run()
	return s
}

func (p *TaskWorker) run() {
	logger.Log.Infof("%v enter core thread", p.ID)
	for {
		select {
		case task := <-p.taskChan:
			p.start()

			start := time.Now()

			logCtx := context.WithValue(context.TODO(), utils.CtxKeyLogID, fmt.Sprintf("%s:%s:%s", p.ID, task.TaskId,
				task.SubId))

			err := gomysql.DB.Transaction(func(tx *gorm.DB) error {
				logger.Log.Infof(utils.MMark(logCtx) + "start chroma process")

				// 检查输入文件
				fileFullPath := fmt.Sprintf("%s/part/%s", task.Path, task.Video)
				if !fileutils.IsExists(fileFullPath) {
					return fmt.Errorf("file not found: %v", fileFullPath)
				}
				filename, _, err := fileutils.GetFilenameAndExt(fileFullPath)
				if err != nil {
					return fmt.Errorf("parse file path: %s failed: %v", fileFullPath, err)
				}

				// 组装输出文件名
				outputFileName := fmt.Sprintf("%s.webm", filename)
				outputFullPath := fmt.Sprintf("%s/output/%s", task.Path, outputFileName)

				// 创建输出文件夹
				err = fileutils.EnsureDir(outputFullPath)
				if err != nil {
					return fmt.Errorf("create folder: %s failed: %v", outputFullPath, err)
				}

				// 检查shader是否满足要求
				fragPath, vertexPath, postFragPath, postVertexPath, err := p.CheckShaders(&task)
				if err != nil {
					return fmt.Errorf("shader check failed: %v", err)
				}

				// 抠绿核心调用
				err = ffmpegutils.ChromaKey(logCtx, fileFullPath, outputFullPath,
					conf.LocalConfig.ChromaSettings.MaxThreads,
					task.Width, task.Height, conf.LocalConfig.VideoSettings.FrameRate,
					fragPath, vertexPath, postFragPath, postVertexPath)
				if err != nil {
					return fmt.Errorf("chromakey failed: %v", err)
				}

				// 更新任务状态
				task.Output = outputFileName
				err = UpdateChromaSubTask(tx, &task, start, enum.Success, enum.ErrCodeSuccess, "success")
				if err != nil {
					logger.Log.Errorf(utils.MMark(logCtx)+"update task status error: %+v\n", err)
					return err
				}

				logger.Log.Infof(utils.MMark(logCtx) + "success process sub task with chromakey")
				return nil
			})

			if err != nil {
				logger.Log.Errorf(utils.MMark(logCtx)+"chroma failed, err: %+v\n", err)

				// 更新任务状态
				err := UpdateChromaSubTask(gomysql.DB, &task, start, enum.Failed, enum.ErrCodeFfmpeg,
					fmt.Sprintf("%v", err))
				if err != nil {
					logger.Log.Errorf(utils.MMark(logCtx)+"update subtask schedule info error: %+v\n", err)
				}
			}

			p.release()
		}
	}
}

func (p *TaskWorker) start() {
	p.lock.Lock()
	defer p.lock.Unlock()
	p.isBusy = true
}

func (p *TaskWorker) release() {
	p.lock.Lock()
	defer p.lock.Unlock()
	p.isBusy = false
}

func (p *TaskWorker) isAvailable() bool {
	p.lock.Lock()
	defer p.lock.Unlock()
	return !p.isBusy
}

func (p *TaskWorker) Execute(task model.ChromaSubTask) {
	p.taskChan <- task
}

func (p *TaskWorker) CheckShaders(task *model.ChromaSubTask) (fPath string, vPath string, pfPath string, pvPath string,
	oErr error) {

	if task.Version == proto.VERSION1 || task.Version == proto.VERSION2 {
		str, err := jsonutils.GetStringFromJson(task.Config, proto.KeyVertexShader)
		if err != nil || len(str) < 1 {
			oErr = fmt.Errorf("invalid param: %v, err: %v", proto.KeyVertexShader, err)
			return
		}

		vPath = fmt.Sprintf("%s/shader/%s", task.Path, str)
		if !fileutils.IsExists(vPath) {
			oErr = fmt.Errorf("vertex shader not found: %v", proto.KeyVertexShader)
			return
		}
	}

	if task.Version == proto.VERSION1 || task.Version == proto.VERSION2 {
		str, err := jsonutils.GetStringFromJson(task.Config, proto.KeyFragmentShader)
		if err != nil || len(str) < 1 {
			oErr = fmt.Errorf("invalid param: %v, err: %v", proto.KeyFragmentShader, err)
			return
		}

		fPath = fmt.Sprintf("%s/shader/%s", task.Path, str)
		if !fileutils.IsExists(vPath) {
			oErr = fmt.Errorf("fragment shader not found: %v", proto.KeyFragmentShader)
			return
		}
	}

	if task.Version == proto.VERSION2 {
		str, err := jsonutils.GetStringFromJson(task.Config, proto.KeyPostVertexShader)
		if err != nil || len(str) < 1 {
			oErr = fmt.Errorf("invalid param: %v, err: %v", proto.KeyPostVertexShader, err)
			return
		}

		pvPath = fmt.Sprintf("%s/shader/%s", task.Path, str)
		if !fileutils.IsExists(vPath) {
			oErr = fmt.Errorf("post vertex shader not found: %v", proto.KeyPostVertexShader)
			return
		}
	}

	if task.Version == proto.VERSION2 {
		str, err := jsonutils.GetStringFromJson(task.Config, proto.KeyPostFragmentShader)
		if err != nil || len(str) < 1 {
			oErr = fmt.Errorf("invalid param: %v, err: %v", proto.KeyPostFragmentShader, err)
			return
		}

		pfPath = fmt.Sprintf("%s/shader/%s", task.Path, str)
		if !fileutils.IsExists(vPath) {
			oErr = fmt.Errorf("post fragment shader not found: %v", proto.KeyPostFragmentShader)
			return
		}
	}
	return
}
