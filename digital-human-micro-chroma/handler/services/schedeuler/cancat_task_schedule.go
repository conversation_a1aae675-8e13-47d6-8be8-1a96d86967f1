package schedeuler

import (
	"acg-ai-go-common/gomysql"
	"acg-ai-go-common/logger"
	"acg-ai-go-common/utils"
	"acg-ai-go-common/utils/redisproxy"
	"context"
	"digital-human-micro-chroma/beans/enum"
	"digital-human-micro-chroma/beans/model"
	"digital-human-micro-chroma/beans/proto"
	"digital-human-micro-chroma/handler/sutils"
	"digital-human-micro-chroma/handler/sutils/redislock"
	"fmt"
	"gorm.io/gorm"
	"time"
)

func ScheduleConcatTask(task *model.ChromaTask) {
	logCtx := context.WithValue(context.TODO(), utils.CtxKeyLogID, task.TaskId)
	redisProxy := redisproxy.GetRedisProxy()

	// 加锁
	redisLockKey := fmt.Sprintf(TaskScheduleLockFmt, sutils.GetNameByRunEnv(), task.TaskId)
	redisLock := redislock.NewRedisLock(redisProxy.Rdb, redisLockKey, TaskScheduleLockExpireTime)
	ok, err := redisLock.Lock(context.Background())
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"Lock key: %s error: %+v\n", redisLockKey, err)
		return
	} else if !ok {
		return
	}
	defer redisLock.Unlock(context.Background())

	// 重新查询
	item, err := task.GetTasksWithTaskId(gomysql.DB, task.TaskId)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"get new task err: %v", err)
		return
	}

	// 状态变了，就不用处理了
	if item.Status != enum.ConCat {
		return
	}

	start := time.Now()

	// 核心处理的事务内容
	err = gomysql.DB.Transaction(func(tx *gorm.DB) error {
		// 需要回调的任务，这里进行回调
		if item.CallbackUrl != "" && len(item.VideoId) > 0 {
			logger.Log.Infof(utils.MMark(logCtx)+"task need callback to %v", item.CallbackUrl)

			err := CallbackToVideoService(logCtx, item.CallbackUrl, item.VideoId, proto.Succeed, item.Message,
				item.OutputUrl)
			if err != nil {
				logger.Log.Errorf(utils.MMark(logCtx)+"callback err: %v", err)
			}
			logger.Log.Infof(utils.MMark(logCtx) + "callback success")
		}

		// 更新任务状态
		err := UpdateChromaTask(gomysql.DB, item, start, enum.Success, enum.ErrCodeSuccess, "")
		if err != nil {
			return fmt.Errorf("update task status: Success err: %v", err)
		}

		logger.Log.Infof(utils.MMark(logCtx)+"update task status: %s", enum.Success)

		return nil
	})

	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"running task schedule failed, err: %+v\n", err)

		// 更新任务状态
		err := UpdateChromaTask(gomysql.DB, item, start, enum.Failed, enum.ErrCodeSchedule, fmt.Sprintf("%v", err))
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+"running task update database failed, err: %+v\n", err)
		}
		return
	}

	logger.Log.Infof(utils.MMark(logCtx) + "success schedule concat task")
}
