package main

import (
	"acg-ai-go-common/global"
	"acg-ai-go-common/logger"
	"acg-ai-go-common/server"
	"acg-ai-go-common/storage"
	"acg-ai-go-common/utils/mysqlproxy"
	"acg-ai-go-common/utils/redisproxy"
	"digital-human-micro-chroma/beans/model"
	"digital-human-micro-chroma/conf"
	"digital-human-micro-chroma/handler/services"
	"digital-human-micro-chroma/routers"
	"github.com/BurntSushi/toml"
	"github.com/sirupsen/logrus"
	"log"
)

func main() {
	// 初始化公共配置
	server.InitGlobalSetting()
	if _, err := toml.DecodeFile(global.ConfFilePath, conf.LocalConfig); err != nil {
		log.Panicf("本地个性化配置加载失败: {%v}", err)
	}

	// 初始化日志
	logger.SetLogger(
		// 初始化日志
		// 开启日志保存Es，默认不开启
		logger.SetOpenReportEs(true),
		// 使用异步保存Es，true为同步上传(会影响API响应速度)
		logger.SetSyncReportEs(false),
		// 设置输出保存的日志级别
		logger.SetLogLevel(logrus.InfoLevel),
	)
	logger.Log.Info("本地个性化配置加载成功")

	// 初始化redis
	usrRedisProxy := redisproxy.GetRedisProxy()
	// 开启携程监听redis是否发生网络中断并进行重连
	go usrRedisProxy.MonitorRedisConnection()

	// 初始化mysql
	usrSqlProxy := mysqlproxy.GetMysqlProxy()
	// 开启携程监听mysql是否发生网络中断并进行重连
	go usrSqlProxy.MonitorMysqlConnection()

	// 初始化数据库
	if err := model.InitTable(); err != nil {
		log.Panicf("初始化数据库失败: {%v}", err)
	}

	// 初始化对象存储
	if err := storage.Init(global.ServerSetting); err != nil {
		log.Panicf("初始化对象存储: {%v}", err)
	}
	logger.Log.Infof("init storage type：%s\n", global.ServerSetting.StorageSetting.Type)

	// 初始化调度器
	if err := services.InitScheduler(); err != nil {
		log.Panicf("初始化调度器失败: {%v}", err)
	}
	logger.Log.Infof("调度器初始化成功")

	// 初始化路由
	routers.InitRouter()
	// 启动服务
	server.Run(routers.GinRouter)
}
