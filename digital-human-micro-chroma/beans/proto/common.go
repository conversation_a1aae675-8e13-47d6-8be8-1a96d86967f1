package proto

type CommonMessage struct {
	Global string `json:"global"`
}

type CommonResponse struct {
	Code    int           `json:"code"`
	Message CommonMessage `json:"message"`
	Success bool          `json:"success"`
	Result  interface{}   `json:"result"`
}

func NewCommonResponse(code int, msg string, result interface{}) CommonResponse {
	return CommonResponse{
		Code:    code,
		Message: CommonMessage{Global: msg},
		Success: code == 0,
		Result:  result,
	}
}
