package proto

type ChromaConfig struct {
	Version string                 `json:"version" binding:"required"`
	Width   int                    `json:"width" binding:"required"`
	Height  int                    `json:"height" binding:"required"`
	Params  map[string]interface{} `json:"params" binding:"required"`
}

type AdditionalInfo struct {
	EditCallback string `json:"editCallback,omitempty"` // 携带的edit callback
}

type ChromaSubmitRequest struct {
	VideoId     string          `json:"videoId,omitempty"`           // 在业务方的唯一ID
	VideoURL    string          `json:"videoUrl" binding:"required"` // 视频地址
	CallbackURL string          `json:"callbackUrl,omitempty"`       // 回调地址
	Config      *ChromaConfig   `json:"config" binding:"required"`   // 抠绿的参数配置
	Additional  *AdditionalInfo `json:"additional,omitempty"`        // 额外的参数
}

type ChromaSubmitResponseData struct {
	LogId  string `json:"logId"`
	TaskId string `json:"taskId,omitempty"`
}

// ChromaCallbackResponseData 用来给业务方回调
type ChromaCallbackResponseData struct {
	VideoId  string `json:"videoId"`  // 在业务方的唯一ID
	VideoURL string `json:"videoURL"` // 输出的视频地址
}

const (
	KeyVertexShader       string = "vertexShader"
	KeyFragmentShader     string = "fragmentShader"
	KeyPostVertexShader   string = "postVertexShader"
	KeyPostFragmentShader string = "postFragmentShader"

	KeyEditCallback string = "editCallback"
	KeyVpVideoId    string = "videoId"
)

const (
	VERSION1 string = "v1"
	VERSION2 string = "v2"
)
