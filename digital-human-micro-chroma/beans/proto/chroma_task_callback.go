package proto

type ChromaTaskCallbackRequest struct {
	VideoId     string `json:"videoId"`      // 在业务方的唯一ID
	DownloadUrl string `json:"downloadUrl" ` // 视频地址
	Status      string `json:"status"`       // 任务状态
	Description string `json:"description"`  // 描述，携带一些错误状态等等
}

type ChromaTaskCallbackResponse struct {
	Code    int         `json:"code"`
	Success bool        `json:"success"`
	Message interface{} `json:"message"`
	Result  interface{} `json:"result"`
}

type VpVideoTaskStatus string

const (
	Succeed        VpVideoTaskStatus = "SUCCEED"
	Error          VpVideoTaskStatus = "ERROR"
	Failed         VpVideoTaskStatus = "FAILED"
	PostProcessing VpVideoTaskStatus = "POST_PROCESSING"
)
