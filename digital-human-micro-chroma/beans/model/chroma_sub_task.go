package model

import (
	"digital-human-micro-chroma/beans/enum"
	"digital-human-micro-chroma/handler/sutils/jsonutils"
	"gorm.io/gorm"
	"time"
)

// ChromaSubTask 抠绿主任务被拆分后产生的子任务
type ChromaSubTask struct {
	ID           int64             `json:"id" gorm:"primarykey"`
	SubId        string            `json:"subId" gorm:"column:subId;not null;type:varchar(50);index"`   // 子任务ID
	TaskId       string            `json:"taskId" gorm:"column:taskId;not null;type:varchar(50);index"` // 任务ID
	Path         string            `json:"Path" gorm:"column:Path;type:varchar(1024)"`                  // 视频文件地址
	Video        string            `json:"video" gorm:"column:video;type:varchar(1024)"`                // 视频文件地址
	Output       string            `json:"output" gorm:"column:output;type:varchar(1024)"`              // 输出的视频文件地址
	Version      string            `json:"version" gorm:"column:version;type:varchar(64)"`              // 抠绿版本
	Width        int               `json:"width" gorm:"column:width"`                                   // 视频宽度
	Height       int               `json:"height" gorm:"column:height"`                                 // 视频高度
	Config       jsonutils.JSONMap `json:"config" gorm:"column:config;type:text"`                       // 参数配置
	Status       enum.TaskStatus   `json:"status" gorm:"column:status;type:varchar(64);index"`          // 状态
	Code         int               `json:"code" gorm:"column:code;index"`                               // 代码
	Message      string            `json:"message" gorm:"column:message;type:text"`                     // 详情
	ScheduleInfo jsonutils.JSONMap `json:"scheduleInfo" gorm:"column:scheduleInfo;type:text"`           // 任务调度的信息
	Retry        int               `json:"retry" gorm:"column:retry;index"`                             // 重试
	RetryCount   int               `json:"retryCount" gorm:"column:retryCount"`                         // 重试次数
	RetryMax     int               `json:"retryMax" gorm:"column:retryMax"`                             // 重试最大值
	CreatedAt    time.Time         `json:"createdAt" gorm:"column:createdAt"`                           // 创建时间
	UpdatedAt    time.Time         `json:"updatedAt" gorm:"column:updatedAt"`                           // 更新时间
	DeletedAt    gorm.DeletedAt    `json:"-" gorm:"column:deletedAt;index"`                             // 删除时间/标记删除
}

func (p *ChromaSubTask) TableName() string {
	return "micro_chroma_sub_task"
}

func (p *ChromaSubTask) Create(conn *gorm.DB) error {
	err := conn.Create(p).Error
	return err
}

func (p *ChromaSubTask) Update(conn *gorm.DB) error {
	err := conn.Save(p).Error
	return err
}

func (p *ChromaSubTask) GetTasksWithTaskId(db *gorm.DB, taskId string) ([]*ChromaSubTask, error) {
	var tasks []*ChromaSubTask
	err := db.Where("taskId = ?", taskId).Find(&tasks).Error
	if err != nil {
		return nil, err
	}
	return tasks, nil
}

func (p *ChromaSubTask) GetTasksWithSubId(db *gorm.DB, subId string) (*ChromaSubTask, error) {
	var task ChromaSubTask
	err := db.Where("subId = ?", subId).First(&task).Error
	if err != nil {
		return nil, err
	}
	return &task, nil
}

func (p *ChromaSubTask) GetTasksWithStatus(db *gorm.DB, status enum.TaskStatus) ([]*ChromaSubTask, error) {
	var tasks []*ChromaSubTask
	err := db.Where("status = ?", string(status)).Order("createdAt ASC").Find(&tasks).Error
	if err != nil {
		return nil, err
	}
	return tasks, nil
}

func (p *ChromaSubTask) GetTasksWithStatusWithRetry(db *gorm.DB, status enum.TaskStatus) ([]*ChromaSubTask, error) {
	var tasks []*ChromaSubTask
	err := db.Where("status = ? AND retry = ?", string(status), 1).Order("createdAt ASC").Find(&tasks).Error
	if err != nil {
		return nil, err
	}
	return tasks, nil
}

func (p *ChromaSubTask) DeleteById(db *gorm.DB, id int64) error {
	result := db.Delete(&ChromaSubTask{}, id)
	if result.Error != nil {
		return result.Error
	}
	return nil
}

func (p *ChromaSubTask) DeleteByTaskId(db *gorm.DB, taskId string) error {
	result := db.Where("taskId = ?", taskId).Delete(&ChromaSubTask{})
	if result.Error != nil {
		return result.Error
	}
	return nil
}
