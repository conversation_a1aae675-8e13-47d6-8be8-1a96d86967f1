package model

import (
	"digital-human-micro-chroma/beans/enum"
	"digital-human-micro-chroma/handler/sutils"
	"digital-human-micro-chroma/handler/sutils/jsonutils"
	"time"
)

type ScheduleNodeInfo struct {
	Name         string    `json:"name"`
	TargetStatus string    `json:"targetStatus"`
	StartTime    time.Time `json:"startTime"`
	Duration     int64     `json:"duration"`
}

type ScheduleInfo struct {
	Nodes []ScheduleNodeInfo `json:"nodes"`
}

func NewScheduleNode() *ScheduleNodeInfo {
	podName, err := sutils.GetPodName()
	if err != nil {
		podName = "scheduler"
	}
	node := ScheduleNodeInfo{
		Name: podName, StartTime: time.Now(), Duration: 0, TargetStatus: string(enum.Submit),
	}
	return &node
}

func NewScheduleInfo(retryMax int) (*ScheduleInfo, jsonutils.JSONMap, error) {
	podName, err := sutils.GetPodName()
	if err != nil {
		podName = "scheduler"
	}
	nodes := []ScheduleNodeInfo{
		{Name: podName, StartTime: time.Now(), Duration: 0, TargetStatus: string(enum.Submit)},
	}
	scheInfo := ScheduleInfo{
		Nodes: nodes,
	}
	scheduleInfo, err := jsonutils.StructToJsonMap(scheInfo)
	if err != nil {
		return &scheInfo, nil, err
	}

	return &scheInfo, scheduleInfo, nil
}
