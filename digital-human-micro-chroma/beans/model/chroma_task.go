package model

import (
	"digital-human-micro-chroma/beans/enum"
	"digital-human-micro-chroma/handler/sutils/jsonutils"
	"gorm.io/gorm"
	"time"
)

// ChromaTask 抠绿的主任务
type ChromaTask struct {
	ID           int64             `json:"id" gorm:"primarykey"`
	TaskId       string            `json:"taskId" gorm:"column:taskId;not null;type:varchar(50);index"`   // 任务ID
	VideoId      string            `json:"videoId" gorm:"column:videoId;not null;type:varchar(50);index"` // 视频任务ID
	VideoUrl     string            `json:"videoUrl" gorm:"column:videoUrl;type:varchar(1024)"`            // 视频文件地址
	OutputUrl    string            `json:"outputUrl" gorm:"column:outputUrl;type:varchar(1024)"`          // 输出的视频文件地址
	CallbackUrl  string            `json:"callbackUrl" gorm:"column:callbackUrl;type:varchar(1024)"`      // 回调地址
	Version      string            `json:"version" gorm:"column:version;type:varchar(64);index"`          // 抠绿版本
	Width        int               `json:"width" gorm:"column:width"`                                     // 视频宽度
	Height       int               `json:"height" gorm:"column:height"`                                   // 视频高度
	Config       jsonutils.JSONMap `json:"config" gorm:"column:config;type:text"`                         // 参数配置
	Status       enum.TaskStatus   `json:"status" gorm:"column:status;type:varchar(64);index"`            // 状态
	Code         int               `json:"code" gorm:"column:code;index"`                                 // 代码
	Message      string            `json:"message" gorm:"column:message;type:text"`                       // 详情
	SubmitInfo   jsonutils.JSONMap `json:"submitInfo" gorm:"column:submitInfo;type:text"`                 // 业务侧的信息
	ScheduleInfo jsonutils.JSONMap `json:"scheduleInfo" gorm:"column:scheduleInfo;type:text"`             // 任务调度的信息
	SegmentCount int               `json:"segmentCount" gorm:"column:segmentCount;index"`                 // 分割次数
	SegmentTime  int               `json:"segmentTime" gorm:"column:segmentTime;index"`                   // 分割时长
	Retry        int               `json:"retry" gorm:"column:retry;index"`                               // 重试
	RetryCount   int               `json:"retryCount" gorm:"column:retryCount"`                           // 重试次数
	RetryMax     int               `json:"retryMax" gorm:"column:retryMax"`                               // 重试最大值
	CreatedAt    time.Time         `json:"createdAt" gorm:"column:createdAt"`                             // 创建时间
	UpdatedAt    time.Time         `json:"updatedAt" gorm:"column:updatedAt"`                             // 更新时间
	DeletedAt    gorm.DeletedAt    `json:"-" gorm:"column:deletedAt;index"`                               // 删除时间/标记删除
}

func (p *ChromaTask) TableName() string {
	return "micro_chroma_task"
}

func (p *ChromaTask) Create(conn *gorm.DB) error {
	err := conn.Create(p).Error
	return err
}

func (p *ChromaTask) Update(conn *gorm.DB) error {
	err := conn.Save(p).Error
	return err
}

func (p *ChromaTask) GetTasksWithTaskId(db *gorm.DB, taskId string) (*ChromaTask, error) {
	var task ChromaTask
	err := db.Where("taskId = ?", taskId).First(&task).Error
	if err != nil {
		return nil, err
	}
	return &task, nil
}

func (p *ChromaTask) GetTasksWithStatus(db *gorm.DB, status enum.TaskStatus) ([]*ChromaTask, error) {
	var tasks []*ChromaTask
	err := db.Where("status = ?", string(status)).Order("createdAt ASC").Find(&tasks).Error
	if err != nil {
		return nil, err
	}
	return tasks, nil
}

func (p *ChromaTask) GetTasksWithStatusWithRetry(db *gorm.DB, status enum.TaskStatus) ([]*ChromaTask, error) {
	var tasks []*ChromaTask
	err := db.Where("status = ? AND retry = ?", string(status), 1).Order("createdAt ASC").Find(&tasks).Error
	if err != nil {
		return nil, err
	}
	return tasks, nil
}

func (p *ChromaTask) DeleteById(db *gorm.DB, id int64) error {
	result := db.Delete(&ChromaTask{}, id)
	if result.Error != nil {
		return result.Error
	}
	return nil
}

func (p *ChromaTask) DeleteByTaskId(db *gorm.DB, taskId string) error {
	result := db.Where("taskId = ?", taskId).Delete(&ChromaTask{})
	if result.Error != nil {
		return result.Error
	}
	return nil
}
