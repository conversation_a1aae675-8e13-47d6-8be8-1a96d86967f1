server-port = 8080
server-name = "dhlive-micro-chroma"
log-file-prefix = "localhost"
log-file-path = "./logs/"
log-show-code-file = true
log-show-code-func = true

# consul配置
[consul-setting]
host = "127.0.0.1"
port = 8500
name = ""

# 健康检查地址
health-url = "/health"
check-timeout = "10s"
check-interval = "10s"
deregister-critical-service-after = "30s"

# mysql配置
[mysql-setting]
host = "127.0.0.1"
port = 3306
database = "digital_human_micro_service"
username = "root"
password = "123456"
maxOpenConns = 1000
maxIdlenConns = 5

# redis配置
[redis-setting]
addr = "127.0.0.1:6777"
username = ""
password = ""
redisEnv = "dev"

# 日志上传的elasticsearch配置
[log-es-setting]
# host = "http://*************:8200"
host = "http://127.0.0.1:9202"
username = ""
password = ""

[bos-setting]
# apikey
ak = "ALTAKxdVkHMs4sp0Tgkpa3zCJP"
# Secret Key
sk = "e4f46ff5d1bf45c5b81dd867d6e3c148"
endpoint = "bj.bcebos.com"
bucket   = "xiling-dh"
host     = "https://xiling-dh.bj.bcebos.com"
cdn-host = "https://xiling-dh.cdn.bcebos.com"

[schedule-setting]
maxConcurrency = 4
maxRunningSize = 10
maxTaskRetryCount = 1
maxChromaRetryCount = 3
httpRetryCount = 3

[preprocess-setting]
minSegmentDuration = 10
minSegmentUnitTime = 2
segmentCount = 4

[chroma-setting]
supportVersion = ["v1", "v2"]
maxThreads = 16
timeout = 7200

[cache-setting]
cacheType = "local"
cfsCacheRoot = "/home/<USER>/meta-human-editor-node/cache"
localCacheRoot = "/Users/<USER>/Documents/cache"
postCopy = true

[audio-setting]
codec = "libopus"
sampleRate = 48000
channels = 1

[video-setting]
frameRate = 25