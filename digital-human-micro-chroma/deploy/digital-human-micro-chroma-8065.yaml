apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: digital-human
    module: digital-human-micro-chroma
  name: digital-human-micro-chroma
  namespace: ppe-dh-v3-bk
spec:
  progressDeadlineSeconds: 600
  replicas: 2
  revisionHistoryLimit: 10
  selector:
    matchLabels:
      app: digital-human
      module: digital-human-micro-chroma
  strategy:
    rollingUpdate:
      maxSurge: 25%
      maxUnavailable: 25%
    type: RollingUpdate
  template:
    metadata:
      annotations:
        logging.kubesphere.io/logsidecar-config: '{}'
      creationTimestamp: null
      labels:
        app: digital-human
        module: digital-human-micro-chroma
    spec:
      affinity:
        podAntiAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            - labelSelector:
                matchExpressions:
                  - key: app
                    operator: In
                    values:
                      - digital-human
                  - key: module
                    operator: In
                    values:
                      - digital-human-micro-chroma
              topologyKey: kubernetes.io/hostname
      containers:
        - args:
            - /home/<USER>/sbin/start.sh
          command:
            - sh
          env:
            - name: POD_NAMESPACE
              valueFrom:
                fieldRef:
                  apiVersion: v1
                  fieldPath: metadata.namespace
          image: ccr-246im3mw-pub.cnc.bj.baidubce.com/digitalhuman/digital-human-micro-chroma:20250409_1744185025792
          imagePullPolicy: Always
          livenessProbe:
            failureThreshold: 3
            httpGet:
              path: /health
              port: 8080
              scheme: HTTP
            initialDelaySeconds: 20
            periodSeconds: 5
            successThreshold: 1
            timeoutSeconds: 5
          name: digital-human-micro-chroma
          ports:
            - containerPort: 8080
              protocol: TCP
          readinessProbe:
            failureThreshold: 3
            httpGet:
              path: /health
              port: 8080
              scheme: HTTP
            initialDelaySeconds: 5
            periodSeconds: 5
            successThreshold: 1
            timeoutSeconds: 1
          resources:
            limits:
              cpu: "16"
              memory: 2Gi
            requests:
              cpu: "10"
              memory: 2Gi
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
            - mountPath: /home/<USER>/conf/conf.toml
              name: config-volume
              subPath: conf.toml
            - mountPath: /home/<USER>/meta-human-editor-node/cache/
              name: mhe-cache
      dnsPolicy: ClusterFirst
      imagePullSecrets:
        - name: regcred
        - name: regcred-ccr
        - name: regcred-eccr
      restartPolicy: Always
      schedulerName: default-scheduler
      terminationGracePeriodSeconds: 30
      tolerations:
        - effect: NoSchedule
          key: limit
          operator: Equal
          value: lite-test
      volumes:
        - configMap:
            defaultMode: 420
            name: digital-human-micro-chroma
          name: config-volume
        - name: mhe-cache
          persistentVolumeClaim:
            claimName: ppe-v3-mhe-cache-pvc
---
apiVersion: v1
kind: Service
metadata:
  labels:
    app: digital-human
    module: digital-human-micro-chroma
  name: digital-human-micro-chroma
  namespace: ppe-dh-v3-bk
spec:
  selector:
    app: digital-human
    module: digital-human-micro-chroma
  ports:
    - protocol: TCP
      port: 8080
      targetPort: 8080
  type: ClusterIP
---
apiVersion: v1
kind: ConfigMap
metadata:
  labels:
    app: digital-human
    module: digital-human-micro-chroma
  name: digital-human-micro-chroma
  namespace: ppe-dh-v3-bk
data:
  conf.toml: |
    ####################################################### 服务配置-测试环境 #######################################################
    server-port = 8080
    server-name = "dhlive-micro-chroma"
    log-file-prefix = "localhost"
    log-file-path = "./logs/"
    log-show-code-file = true
    log-show-code-func = true

    # consul配置
    [consul-setting]
    host = "127.0.0.1"
    port = 8500
    name = ""

    # 健康检查地址
    health-url = "/health"
    check-timeout = "10s"
    check-interval = "10s"
    deregister-critical-service-after = "30s"

    # mysql配置
    [mysql-setting]
    host = "mysql57-ppe-v3-bk.rdsmdubngebjdwc.rds.bj.baidubce.com"
    port = 3306
    database = "meta_human_editor_saas"
    username = "saas"
    password = "!Digitalhuman@baidu123"
    maxOpenConns = 1000
    maxIdlenConns = 5

    # redis配置
    [redis-setting]
    addr = "redis-ppe-v3:6379"
    username = ""
    password = ""
    redisEnv = "prerelease-dh-v3-bk"

    # 日志上传的elasticsearch配置
    [log-es-setting]
    host = "http://*************:8200"
    username = "superuser"
    password = "Baidu_dh123"

    [bos-setting]
    # apikey
    ak = "ALTAKxdVkHMs4sp0Tgkpa3zCJP"
    # Secret Key
    sk = "e4f46ff5d1bf45c5b81dd867d6e3c148"
    endpoint = "bj.bcebos.com"
    bucket   = "xiling-dh"
    host     = "https://xiling-dh.bj.bcebos.com"
    cdn-host = "https://xiling-dh.cdn.bcebos.com"

    [schedule-setting]
    # 重要:单副本进程并发数量,单并发需要8C,此项修改必须配合资源申请调整资源
    maxConcurrency = 1
    maxRunningSize = 20
    maxTaskRetryCount = 1
    maxChromaRetryCount = 3
    httpRetryCount = 3

    [preprocess-setting]
    minSegmentDuration = 10
    minSegmentUnitTime = 2
    # 重要:单文件切分数量,拆分后并发执行
    segmentCount = 4
    defaultFrameRate = 25

    [chroma-setting]
    supportVersion = ["v1", "v2"]
    maxThreads = 16
    timeout = 4800
    
    [cache-setting]
    cacheType = "cfs"
    cfsCacheRoot = "/home/<USER>/meta-human-editor-node/cache"
    localCacheRoot = "/home/<USER>"
    postCopy = true
    
    [audio-setting]
    codec = "libopus"
    sampleRate = 48000
    channels = 1
    
    [video-setting]
    frameRate = 25
---