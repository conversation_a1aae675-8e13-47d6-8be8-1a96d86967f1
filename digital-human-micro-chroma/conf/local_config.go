package conf

var (
	LocalConfig = &localConfig{}
)

type localConfig struct {
	MySqlSettings      *MysqlSetting      `toml:"mysql-setting"`
	RedisSettings      *RedisSetting      `toml:"redis-setting"`
	BosSettings        *BosSetting        `toml:"bos-setting"`
	ScheduleSettings   *ScheduleSetting   `toml:"schedule-setting"`
	PreProcessSettings *PreProcessSetting `toml:"preprocess-setting"`
	ChromaSettings     *ChromaSetting     `toml:"chroma-setting"`
	CacheSettings      *CacheSetting      `toml:"cache-setting"`
	AudioSettings      *AudioSetting      `toml:"audio-setting"`
	VideoSettings      *VideoSetting      `toml:"video-setting"`
}

type RedisSetting struct {
	SentinelNodes    string `toml:"sentinelNodes"`    // 哨兵集群节点，ip:port，多个节点使用,分割，eg: "127.0.0.1:26379,127.0.0.1:26380,127.0.0.1:26381"
	SentinelUsername string `toml:"sentinelUsername"` // 哨兵节点用户名，可为""
	SentinelPassword string `toml:"sentinelPassword"` // 哨兵节点密码，可为""
	RouteByLatency   bool   `toml:"routeByLatency"`   // 是否将读取命令路由到从节点
	MasterName       string `toml:"masterName"`       // redis集群主节点名称
	Addr             string `toml:"addr"`             // 单节点地址，格式为ip:port，eg: "127.0.0.1:6379"
	Username         string `toml:"username"`         // redis集群主节点用户名
	Password         string `toml:"password"`         // redis集群主节点密码
	PoolSize         int    `toml:"poolSize"`         // redis连接池大小
	MinIdleConns     int    `toml:"minIdleConns"`     // 最大空闲连接数
	RedisEnv         string `toml:"redisEnv"`         // redis环境，dev/test/prod
}

type BosSetting struct {
	AK       string `toml:"ak"`
	SK       string `toml:"sk"`
	Endpoint string `toml:"endpoint"`
	Bucket   string `toml:"bucket"`
	Host     string `toml:"host"`
	CDNHost  string `toml:"cdn-host"`
}

type HostPortSetting struct {
	Host string `toml:"host"`
	Port int    `toml:"port"`
}

type UserNamePwdSetting struct {
	Username string `toml:"username"`
	Password string `toml:"password"`
}

type MysqlSetting struct {
	HostPortSetting
	UserNamePwdSetting
	Database     string `toml:"database"`
	MaxOpenConns int    `toml:"maxOpenConns"`
	MaxIdleConns int    `toml:"maxIdleConns"`
}

type ScheduleSetting struct {
	MaxConcurrency      int `toml:"maxConcurrency"`      // 抠绿子任务进程并发最大值
	MaxRunningSize      int `toml:"maxRunningSize"`      // 抠绿主任务并发的最大值
	MaxTaskRetryCount   int `toml:"maxTaskRetryCount"`   // 任务出错重试的最大次数
	MaxChromaRetryCount int `toml:"maxChromaRetryCount"` // 子任务出错重试的最大次数
	HttpRetryCount      int `toml:"httpRetryCount"`      // http 请求的重试次数
}

type PreProcessSetting struct {
	MinSegmentDuration int `toml:"minSegmentDuration"` // 可以进行拆分的视频最短时长
	MinSegmentUnitTime int `toml:"minSegmentUnitTime"` // 拆分后视频片段的最短时长
	SegmentCount       int `toml:"segmentCount"`       // 默认的拆分次数
}

type ChromaSetting struct {
	MaxThreads     int      `toml:"maxThreads"`     // 抠绿子任务线程池最大值
	SupportVersion []string `toml:"supportVersion"` // 支持的抠绿版本
	Timeout        int      `toml:"timeout"`        // 抠绿进程的超时时间
}

type CacheSetting struct {
	CacheType      string `toml:"cacheType"`      // 缓存类型,cfs\local
	CfsCacheRoot   string `toml:"cfsCacheRoot"`   // CFS 缓存的根目录
	LocalCacheRoot string `toml:"localCacheRoot"` // 本地缓存的根目录
	PostCopy       bool   `toml:"postCopy"`       // 处理完复制
}

type VideoSetting struct {
	FrameRate int `toml:"frameRate"` // 默认的输出帧率
}

type AudioSetting struct {
	Codec      string `toml:"codec"`      // 音频 codec
	SampleRate int    `toml:"sampleRate"` // 音频采样率
	Channels   int    `toml:"channels"`   // 音频声道
}
