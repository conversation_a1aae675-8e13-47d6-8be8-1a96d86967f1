package model

import (
	"dhlive-update-service/beans/enum"
	"errors"
	"time"

	"gorm.io/gorm"
)

type SoftConfig struct {
	ID                    int64            `json:"id" gorm:"column:id;primaryKey;autoIncrement"`              // 数据库ID，通常为主键自增
	SoftName              string           `json:"softName" gorm:"column:softName;not null"`                  // 软件名称
	SoftExeName           string           `json:"softExeName" gorm:"column:softExeName;not null"`            // 软件执行名称
	MachineName           string           `json:"machineName" gorm:"column:machineName"`                     // 机器名称
	SoftVersion           string           `json:"softVersion" gorm:"column:softVersion;not null"`            // 软件版本
	MachineType           enum.MachineType `json:"machineType" gorm:"column:machineType"`                     // 机器类型
	SoftUrl               string           `json:"softUrl" gorm:"column:softUrl"`                             // 软件下载地址
	SoftInstallPath       string           `json:"softInstallPath" gorm:"column:softInstallPath"`             // 软件安装路径
	SoftMd5               string           `json:"softMd5" gorm:"column:softMd5"`                             // 软件md5值
	SoftRunCmd            string           `json:"softRunCmd" gorm:"column:softRunCmd"`                       // 软件启动命令
	SoftCloseCmd          string           `json:"softCloseCmd" gorm:"column:softCloseCmd"`                   // 软件关闭命令
	SoftUninstallCmd      string           `json:"softUninstallCmd" gorm:"column:softUninstallCmd"`           // 注意字段名修正：软件卸载命令
	SoftFlag              int64            `json:"softFlag" gorm:"column:softFlag;not null"`                  // 1:强制升级 2:非强制升级 3:不升级 4.强制升级并卸载
	SoftConfigPath        string           `json:"softConfigPath" gorm:"column:softConfigPath"`               // 软件配置文件路径
	SoftConfigVersionName string           `json:"softConfigVersionName" gorm:"column:softConfigVersionName"` // 软件配置文件版本名称
	CreatedAt             time.Time        `json:"createdAt" gorm:"column:createdAt;not null"`                // 创建时间，日期时间类型
	UpdatedAt             time.Time        `json:"updatedAt" gorm:"column:updatedAt;not null"`                // 更新时间，日期时间类型
	DeletedAt             gorm.DeletedAt   `json:"-" gorm:"column:deletedAt;index"`                           // 删除时间/标记删除
}

func (soft *SoftConfig) TableName() string {
	return "ups_soft_config"
}

func (soft *SoftConfig) CreateSoftConfig(db *gorm.DB) error {
	result := db.Create(soft)
	if result.Error != nil {
		return result.Error
	}
	return nil
}

func (soft *SoftConfig) GetSoftConfig(db *gorm.DB, softName string) error {
	result := db.Where("softName = ?", softName).First(&soft)
	if result.Error != nil {
		return result.Error
	}
	return nil
}

func (soft *SoftConfig) GetSoftConfigByMachineType(db *gorm.DB, softName string, machineType enum.MachineType) error {
	result := db.Where("softName = ?", softName).Where("machineType = ?", string(machineType)).First(&soft)
	if result.Error != nil {
		return result.Error
	}
	return nil
}

func (soft *SoftConfig) GetSoftConfigList(db *gorm.DB, page, pageSize int) ([]SoftConfig, error) {
	// 验证页码和每页条数是否合法
	if page <= 0 || pageSize <= 0 {
		return nil, errors.New("invalid page or page size")
	}

	// 计算跳过的记录数
	offset := (page - 1) * pageSize
	softs := make([]SoftConfig, 0)
	result := db.Limit(pageSize).Offset(offset).Find(&softs)
	if result.Error != nil {
		return softs, result.Error
	}
	return softs, nil
}

func (soft *SoftConfig) UpdateSoftVersion(db *gorm.DB) error {
	result := db.Save(soft)
	if result.Error != nil {
		return result.Error
	}
	return nil
}

func (soft *SoftConfig) DeleteSoftConfigByID(db *gorm.DB, id int64) error {
	// 假设ID字段在SoftConfig结构体中是uint类型，且字段名为ID
	result := db.Where("id = ?", id).Delete(&SoftConfig{})
	if result.Error != nil {
		return result.Error
	}
	return nil
}

func (soft *SoftConfig) DeleteSoftConfig(db *gorm.DB, softName string) error {
	result := db.Where("softName = ?", softName).Delete(&SoftConfig{})
	if result.Error != nil {
		return result.Error
	}
	return nil
}
func (soft *SoftConfig) DeleteSoftConfigBymachineType(db *gorm.DB, softName, machineType string) error {
	result := db.Where("softName = ?", softName).Where("machineType = ?", machineType).Delete(&SoftConfig{})
	if result.Error != nil {
		return result.Error
	}
	return nil
}
func (soft *SoftConfig) GetWinClientSoftConfig(db *gorm.DB, machineName string, machineType enum.MachineType) ([]SoftConfig, error) {
	softs := make([]SoftConfig, 0)
	result := db.Where("machineName = ? OR machineName = '' OR machineName IS NULL", machineName).Where("machineType = ?", machineType).Find(&softs)
	if result.Error != nil {
		return softs, result.Error
	}
	return softs, nil
}
