package model

import (
	"dhlive-update-service/beans/enum"
	"time"

	"gorm.io/gorm"
)

type MachineConfig struct {
	ID           int64             `json:"id" gorm:"column:id;primaryKey;autoIncrement"` // 数据库ID，通常为主键自增
	MachineName  string            `json:"machineName" gorm:"column:machineName"`        // 机器名称
	MachineType  enum.MachineType  `json:"machineType" gorm:"column:machineType"`        // 机器类型
	MachineDesc  string            `json:"machineDesc" gorm:"column:machineDesc"`        // 机器描述
	MachineIp    string            `json:"machineIp" gorm:"column:machineIp"`            // 机器IP
	SoftConfig   string            `json:"softConfig" gorm:"column:softConfig"`          // 软件配置
	MachineState enum.MachineState `json:"machineState" gorm:"column:machineState"`      // 机器状态
	CreatedAt    time.Time         `json:"createdAt" gorm:"column:createdAt;not null"`   // 创建时间，日期时间类型
	UpdatedAt    time.Time         `json:"updatedAt" gorm:"column:updatedAt;not null"`   // 更新时间，日期时间类型
	DeletedAt    gorm.DeletedAt    `json:"-" gorm:"column:deletedAt;index"`              // 删除时间/标记删除
}

func (MachineConfig) TableName() string {
	return "ups_machine_config"
}

func (m *MachineConfig) CreateMachineConfig(db *gorm.DB) error {
	result := db.Create(m)
	if result.Error != nil {
		return result.Error
	}
	return nil
}

func (m *MachineConfig) UpdateMachineConfig(db *gorm.DB) error {
	result := db.Save(m)
	if result.Error != nil {
		return result.Error
	}
	return nil
}

func (m *MachineConfig) DeleteMachineConfig(db *gorm.DB) error {
	result := db.Delete(m, m.ID)
	if result.Error != nil {
		return result.Error
	}
	return nil
}

func (m *MachineConfig) GetMachineConfigById(db *gorm.DB, id int64) error {
	result := db.First(m, id)
	if result.Error != nil {
		return result.Error
	}
	return nil
}

func (m *MachineConfig) GetMachineConfigByMachineName(db *gorm.DB, machineName string) (MachineConfig, error) {
	m1 := MachineConfig{}
	result := db.Where("machineName = ?", machineName).First(&m1)
	if result.Error != nil {
		return m1, result.Error
	}
	return m1, nil
}
