package proto

import (
	"dhlive-update-service/beans/enum"
	"dhlive-update-service/beans/model"
)

type SoftConfigRequest struct {
	SoftName              string           `json:"softName" gorm:"column:softName;not null"`                  // 软件名称
	SoftExeName           string           `json:"softExeName" gorm:"column:softExeName;not null"`            // 软件执行名称
	MachineName           string           `json:"machineName" gorm:"column:machineName"`                     // 机器名称
	SoftVersion           string           `json:"softVersion" gorm:"column:softVersion;not null"`            // 软件版本
	MachineType           enum.MachineType `json:"machineType" gorm:"column:machineType"`                     // 机器类型
	SoftUrl               string           `json:"softUrl" gorm:"column:softUrl"`                             // 软件下载地址
	SoftInstallPath       string           `json:"softInstallPath" gorm:"column:softInstallPath"`             // 软件安装路径
	SoftMd5               string           `json:"softMd5" gorm:"column:softMd5"`                             // 软件md5值
	SoftRunCmd            string           `json:"softRunCmd" gorm:"column:softRunCmd"`                       // 软件启动命令
	SoftCloseCmd          string           `json:"softCloseCmd" gorm:"column:softCloseCmd"`                   // 软件关闭命令
	SoftUninstallCmd      string           `json:"softUninstallCmd" gorm:"column:softUninstallCmd"`           // 注意字段名修正：软件卸载命令
	SoftFlag              int64            `json:"softFlag" gorm:"column:softFlag;not null"`                  // 1:强制升级 2:非强制升级 3:不升级 4.强制升级并卸载
	SoftConfigPath        string           `json:"softConfigPath" gorm:"column:softConfigPath"`               // 软件配置文件路径
	SoftConfigVersionName string           `json:"softConfigVersionName" gorm:"column:softConfigVersionName"` // 软件配置文件版本名称
}

type AddSoftConfigResponse struct {
	LogId string `json:"logId"`
}

type DelSoftConfigRequest struct {
	SoftName    string `json:"softName" form:"softName" binding:"required"`
	MachineName string `json:"machineName" form:"machineName" binding:"required"`
}

type DelSoftConfigResponse struct {
	LogId string `json:"logId"`
}

type UpdateSoftConfigResponse struct {
	LogId string `json:"logId"`
}

type SearchSoftConfigRequst struct {
	Page     int `json:"page" form:"page"`
	PageSize int `json:"pageSize" form:"pageSize"`
}

type SearchSoftConfigResponse struct {
	LogId       string             `json:"logId"`
	SoftConfigs []model.SoftConfig `json:"softConfigs"`
}

type WinClientGetSoftConfigRequest struct {
	MachineName string `json:"machineName" form:"machineName"`
}

type WinClientGetSoftConfigResponse struct {
	LogId       string              `json:"logId"`
	SoftConfigs []SoftConfigRequest `json:"softConfigs"`
}

type WinClientVersionReportRequest struct {
	MachineName string              `json:"machineName"`
	MachineIps  []string            `json:"machineIps"`
	SoftConfigs []SoftConfigRequest `json:"softConfigs"`
}

type WinClientVersionReportResponse struct {
	LogId string `json:"logId"`
}
