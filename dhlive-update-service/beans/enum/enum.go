package enum

// WsMsgAction Websocket消息动作事件
type WsMsgAction string

const (
	ActionHeartBeat         WsMsgAction = "HEART_BEAT"         // 心跳
	ActionConnInfo          WsMsgAction = "CONN_INFO"          // 连接信息
	ActionFigureInfo        WsMsgAction = "FIGURE_INFO"        // 当前人像信息
	ActionBRtcInfo          WsMsgAction = "BRTC_INFO"          // BRtc信息
	ActionUeEvent           WsMsgAction = "UE_EVENT"           // UE指令
	ActionEventRsp          WsMsgAction = "EVENT_RSP"          // UE指令响应
	ActionText2Figure       WsMsgAction = "TEXT2FIGURE"        // 文生人像
	ActionText2FigureStop   WsMsgAction = "TEXT2FIGURE_STOP"   // 停止文生人像
	ActionText2FigureResult WsMsgAction = "TEXT2FIGURE_RESULT" // 文生人像结果
	ActionAuthInvalid       WsMsgAction = "AUTH_INVALID"       // 授权过期通知
	ActionErrorInfo         WsMsgAction = "ERROR_INFO"         // 错误信息
	ActionClose             WsMsgAction = "CLOSE"              // 关闭连接
)

type TaskStatus string // TaskStatus READY/RUNNING/STOP/SUCCEED/FAILED

const (
	TaskReady       TaskStatus = "READY"
	TaskRunning     TaskStatus = "RUNNING"
	TaskStop        TaskStatus = "STOP"
	TaskSucceed     TaskStatus = "SUCCEED"
	TaskFailed      TaskStatus = "FAILED"
	TaskUnsubscribe TaskStatus = "UNSUBSCRIBE"
)

type SubscribeStatus string

const (
	SubscribeAccept SubscribeStatus = "accept"
	SubscribeReject SubscribeStatus = "reject"
)

type MiniProgramEventType string

const (
	SubscribeMsg       MiniProgramEventType = "subscribe_msg_popup_event"
	SubscribeRejectMsg MiniProgramEventType = "subscribe_msg_change_event"
	PushMessageResult  MiniProgramEventType = "subscribe_msg_sent_event"
)

type PushType string

const (
	WechatMiniProgram PushType = "WECHAT_MINIPROGRAM"
	BaiduMiniProgram  PushType = "BAIDU_MINIPROGRAM"
)

type MachineState string

const (
	MachineOnline   MachineState = "ONLINE"
	MachineOffline  MachineState = "OFFLINE"
	MachineAbnormal MachineState = "ABNORMAL"
)

type MachineType string

const (
	PpeMachine    MachineType = "PPE"
	OnlineMachine MachineType = "ONLINE"
)
