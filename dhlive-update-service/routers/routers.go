package routers

import (
	"dhlive-update-service/handler/figure"

	"github.com/gin-gonic/gin"
)

// Routers 路由列表
func Routers(e *gin.Engine) {
	{
		updateService := e.Group("/api/digitalhuman/dhlive/update/service/v1")
		updateService.GET("/software/config", figure.SearchSoftConfig)
		updateService.POST("/software/config", figure.AddSoftConfig)
		updateService.PUT("/software/config", figure.UpdateSoftConfig)
		updateService.DELETE("/software/config", figure.DelSoftConfig)

		updateService.GET("/software/config/windows", figure.WinClientGetSoftConfig)
		updateService.POST("/software/config/windows", figure.WinClientVersionReport)
	}
}
