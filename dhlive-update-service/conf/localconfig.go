package config

var (
	LocalConfig = &localConfig{}
)

type localConfig struct {
	RedisSetting  *RedisSetting  `toml:"redis-setting"`
	DhUserSetting *DhUserSetting `toml:"dh-user-setting"`
	BosSetting    *BosSetting    `toml:"bos-setting"`
}

type RedisSetting struct {
	SentinelNodes    string `toml:"sentinelNodes"`    // 哨兵集群节点，ip:port，多个节点使用,分割，eg: "127.0.0.1:26379,127.0.0.1:26380,127.0.0.1:26381"
	SentinelUsername string `toml:"sentinelUsername"` // 哨兵节点用户名，可为""
	SentinelPassword string `toml:"sentinelPassword"` // 哨兵节点密码，可为""
	RouteByLatency   bool   `toml:"routeByLatency"`   // 是否将读取命令路由到从节点
	MasterName       string `toml:"masterName"`       // redis集群主节点名称
	Addr             string `toml:"addr"`             // 单节点地址，格式为ip:port，eg: "127.0.0.1:6379"
	Username         string `toml:"username"`         // redis集群主节点用户名
	Password         string `toml:"password"`         // redis集群主节点密码
	PoolSize         int    `toml:"poolSize"`         // redis连接池大小
	MinIdleConns     int    `toml:"minIdleConns"`     // 最大空闲连接数
	RedisEnv         string `toml:"redisEnv"`         // redis环境，dev/test/prod
}

type DhUserSetting struct {
	BaseUrl string `toml:"baseUrl"`
}

type BosSetting struct {
	AK       string `toml:"ak"`
	SK       string `toml:"sk"`
	Endpoint string `toml:"endpoint"`
	Bucket   string `toml:"bucket"`
	Host     string `toml:"host"`
	CDNHost  string `toml:"cdn-host"`
}
