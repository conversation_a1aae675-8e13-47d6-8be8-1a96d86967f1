package dh_user

import "time"

type CommRsp struct {
	Code    int `json:"code"`
	Message struct {
		Global string `json:"global"`
	} `json:"message"`
	Success bool `json:"success"`
}

type CommResultRsp struct {
	CommRsp
	Result interface{} `json:"result"`
}

type DhUserInfoRsp struct {
	CommRsp
	Result *DhUserInfo `json:"result"`
}

type DhUserInfo struct {
	Username          string                 `json:"username"`
	AccountId         string                 `json:"accountId"`
	Uid               string                 `json:"uid"`
	Tags              *DhUserInfoTags        `json:"tags"`
	AccountMenus      []string               `json:"accountMenus"`
	RoleLevel         int                    `json:"roleLevel"`
	UserChannel       string                 `json:"userChannel"`
	BceAccountId      string                 `json:"bceAccountId"`
	BceUserId         string                 `json:"bceUserId"`
	BceSessionContext *BceUserSessionContext `json:"bceSessionContext"`
}

type DhUserInfoTags struct {
	VisibleCharacters        string `json:"visibleCharacters"`
	RESOURCEQUOTAA2AMeiyan   string `json:"RESOURCE_QUOTA_A2A_meiyan"`
	AccountVisibleCharacters string `json:"accountVisibleCharacters"`
	RESOURCEQUOTANull        string `json:"RESOURCE_QUOTA_null"`
}

type BceUserSessionContext struct {
	SessionId     string      `json:"sessionId"`
	LoginType     interface{} `json:"loginType"`
	LoginUserInfo struct {
		LoginType               string      `json:"loginType"`
		LoginUserId             string      `json:"loginUserId"`
		LoginUsername           string      `json:"loginUsername"`
		DisplayName             string      `json:"displayName"`
		Email                   string      `json:"email"`
		MobilePhone             string      `json:"mobilePhone"`
		BceUserId               string      `json:"bceUserId"`
		BceAccountId            string      `json:"bceAccountId"`
		BceAccountName          string      `json:"bceAccountName"`
		FederationLogin         bool        `json:"federationLogin"`
		FederationType          interface{} `json:"federationType"`
		Federation              interface{} `json:"federation"`
		SessionId               interface{} `json:"sessionId"`
		NeedResetPassword       bool        `json:"needResetPassword"`
		MfaEnabled              bool        `json:"mfaEnabled"`
		AccountActivate         bool        `json:"accountActivate"`
		VerifiedMobile          bool        `json:"verifiedMobile"`
		VerifiedEmail           bool        `json:"verifiedEmail"`
		OrganizationDelegate    bool        `json:"organizationDelegate"`
		AccountOrganizationInfo interface{} `json:"accountOrganizationInfo"`
		Credential              struct {
		} `json:"credential"`
		Roles []interface{} `json:"roles"`
	} `json:"loginUserInfo"`
	TokenId        interface{} `json:"tokenId"`
	TokenExpiresAt interface{} `json:"tokenExpiresAt"`
	StsCredential  struct {
		AccessKeyId     string    `json:"accessKeyId"`
		SecretAccessKey string    `json:"secretAccessKey"`
		SessionToken    string    `json:"sessionToken"`
		CreateTime      time.Time `json:"createTime"`
		Expiration      time.Time `json:"expiration"`
		UserId          string    `json:"userId"`
	} `json:"stsCredential"`
	AccessKey            interface{} `json:"accessKey"`
	ForwardActions       interface{} `json:"forwardActions"`
	RenderCookies        string      `json:"renderCookies"`
	ThirdPartySessionId  interface{} `json:"_thirdPartySessionId"`
	RequestInfo          interface{} `json:"_requestInfo"`
	ParentSessionId      interface{} `json:"_parentSessionId"`
	OrganizationDelegate bool        `json:"organizationDelegate"`
}

type QuotaCommParam struct {
	AccountId string `json:"accountId"`
	QuotaType string `json:"quotaType"`
	AssetId   string `json:"assetId"`
	ReqId     string `json:"reqId"`
}

type QuotaFreezeReq struct {
	QuotaCommParam
	UserId            string `json:"userId"`
	QuotaFreezeAmount int    `json:"quotaFreezeAmount"`
}

type QuotaFreezeRsp struct {
	CommResultRsp
}

type QuotaUnFreezeReq struct {
	QuotaCommParam
	QuotaUnFreezeAmount int `json:"quotaUnFreezeAmount"`
}

type QuotaUnFreezeRsp struct {
	CommResultRsp
}

type QuotaConfirmedCostReq struct {
	QuotaCommParam
	QuotaConfirmedCostAmount int `json:"quotaConfirmedCostAmount"`
}

type QuotaConfirmedCostRsp struct {
	CommResultRsp
}

const (
	Text2FigureQuotaType = "UE_3D_FIGURE_CREATE_AMOUNT"
)

type DhUserInfoListReq struct {
	PageNo    int    `json:"pageNo"`
	PageSize  int    `json:"pageSize"`
	AccountId string `json:"accountId"`
}
type DhUserInfoListRsp struct {
	Code    int           `json:"code"`
	Success bool          `json:"success"`
	Result  BceUserResult `json:"result"`
}

type BceUserResult struct {
	TotalCount int           `json:"totalCount"`
	Result     []BceUserInfo `json:"result"`
}

type BceUserInfo struct {
	Username  string `json:"username"`
	UserID    string `json:"userId"`
	Channel   string `json:"channel"` //"WEIXIN"
	WxOpenID  string `json:"wxOpenId"`
	WxUnionID string `json:"wxUnionId"`
	BdOpenID  string `json:"bdOpenId"`
}
