package figure

import (
	"acg-ai-go-common/gomysql"
	"acg-ai-go-common/logger"
	"acg-ai-go-common/utils"
	"context"
	"dhlive-update-service/beans/enum"
	"dhlive-update-service/beans/model"
	"dhlive-update-service/beans/proto"
	"encoding/json"
	"errors"
	"net/http"
	"regexp"
	"time"

	"github.com/gin-gonic/gin"
)

func AddSoftConfig(c *gin.Context) {
	logctx := context.WithValue(context.TODO(), utils.CtxKeyLogID, utils.RandStringRunes(16))
	req := &proto.SoftConfigRequest{}
	res := &proto.AddSoftConfigResponse{
		LogId: utils.GetLogID(logctx),
	}

	err := c.ShouldBindJSON(req)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logctx)+"AddSoftConfig req bind json err: %v", err)
		c.<PERSON>(http.StatusOK, proto.NewCommDataRsp(100001, "参数解析错误", res))
		return
	}

	err = checkSoftConfig(*req)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logctx)+"AddSoftConfig check soft config err: %v", err)
		c.JSON(http.StatusOK, proto.NewCommDataRsp(100002, "添加配置失败,参数异常", res))
		return
	}

	softconf := &model.SoftConfig{
		SoftName:              req.SoftName,
		SoftExeName:           req.SoftExeName,
		MachineName:           req.MachineName,
		SoftVersion:           req.SoftVersion,
		MachineType:           req.MachineType,
		SoftUrl:               req.SoftUrl,
		SoftInstallPath:       req.SoftInstallPath,
		SoftMd5:               req.SoftMd5,
		SoftRunCmd:            req.SoftRunCmd,
		SoftCloseCmd:          req.SoftCloseCmd,
		SoftUninstallCmd:      req.SoftUninstallCmd,
		SoftFlag:              req.SoftFlag,
		SoftConfigPath:        req.SoftConfigPath,
		SoftConfigVersionName: req.SoftConfigVersionName,
		CreatedAt:             time.Now(),
		UpdatedAt:             time.Now(),
	}

	err = softconf.GetSoftConfigByMachineType(gomysql.DB, softconf.SoftName, softconf.MachineType)
	if err == nil {
		logger.Log.Errorf(utils.MMark(logctx) + "AddSoftConfig soft config already exists")
		c.JSON(http.StatusOK, proto.NewCommDataRsp(100003, "添加配置失败,配置已存在", res))
		return
	}

	err = softconf.CreateSoftConfig(gomysql.DB)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logctx)+"AddSoftConfig insert soft config err: %v", err)
		c.JSON(http.StatusOK, proto.NewCommDataRsp(100004, "添加配置失败,数据库访问异常", res))
		return
	}

	c.JSON(http.StatusOK, proto.NewSuccessRsp(res))
}

func UpdateSoftConfig(c *gin.Context) {
	logctx := context.WithValue(context.TODO(), utils.CtxKeyLogID, utils.RandStringRunes(16))
	req := &proto.SoftConfigRequest{}
	res := &proto.UpdateSoftConfigResponse{
		LogId: utils.GetLogID(logctx),
	}

	err := c.ShouldBindJSON(req)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logctx)+"UpdateSoftConfig req bind json err: %v", err)
		c.JSON(http.StatusOK, proto.NewCommDataRsp(100001, "参数解析错误", res))
		return
	}

	err = checkSoftConfig(*req)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logctx)+"UpdateSoftConfig check soft config err: %v", err)
		c.JSON(http.StatusOK, proto.NewCommDataRsp(100002, "添加配置失败,参数异常", res))
		return
	}

	softconf := &model.SoftConfig{}
	err = softconf.GetSoftConfigByMachineType(gomysql.DB, req.SoftName, req.MachineType)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logctx) + "AddSoftConfig soft config not exists")
		c.JSON(http.StatusOK, proto.NewCommDataRsp(100003, "添加配置失败,配置不存在", res))
		return
	}

	softconf = &model.SoftConfig{
		ID:                    softconf.ID,
		SoftName:              req.SoftName,
		SoftExeName:           req.SoftExeName,
		MachineName:           req.MachineName,
		SoftVersion:           req.SoftVersion,
		MachineType:           req.MachineType,
		SoftUrl:               req.SoftUrl,
		SoftInstallPath:       req.SoftInstallPath,
		SoftMd5:               req.SoftMd5,
		SoftRunCmd:            req.SoftRunCmd,
		SoftCloseCmd:          req.SoftCloseCmd,
		SoftUninstallCmd:      req.SoftUninstallCmd,
		SoftFlag:              req.SoftFlag,
		SoftConfigPath:        req.SoftConfigPath,
		SoftConfigVersionName: req.SoftConfigVersionName,
		CreatedAt:             softconf.CreatedAt,
		UpdatedAt:             time.Now(),
	}

	err = softconf.UpdateSoftVersion(gomysql.DB)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logctx)+"UpdateSoftConfig insert soft config err: %v", err)
		c.JSON(http.StatusOK, proto.NewCommDataRsp(100004, "添加配置失败,数据库访问异常", res))
		return
	}

	c.JSON(http.StatusOK, proto.NewSuccessRsp(res))
}

func DelSoftConfig(c *gin.Context) {
	logctx := context.WithValue(context.TODO(), utils.CtxKeyLogID, utils.RandStringRunes(16))
	req := &proto.DelSoftConfigRequest{}
	res := &proto.DelSoftConfigResponse{
		LogId: utils.GetLogID(logctx),
	}

	err := c.ShouldBindQuery(req)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logctx)+"DelSoftConfig req bind json err: %v", err)
		c.JSON(http.StatusOK, proto.NewCommDataRsp(100001, "参数解析错误", res))
		return
	}

	if len(req.SoftName) == 0 {
		logger.Log.Errorf(utils.MMark(logctx) + "DelSoftConfig soft name is null")
		c.JSON(http.StatusOK, proto.NewCommDataRsp(100002, "软件名称不能为空", res))
		return
	}

	err = (&model.SoftConfig{}).DeleteSoftConfigBymachineType(gomysql.DB, req.SoftName, req.MachineName)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logctx)+"DelSoftConfig delete soft config err: %v", err)
		c.JSON(http.StatusOK, proto.NewCommDataRsp(100003, "删除配置失败,数据库访问异常", res))
		return
	}

	c.JSON(http.StatusOK, proto.NewSuccessRsp(res))
}

func SearchSoftConfig(c *gin.Context) {
	logctx := context.WithValue(context.TODO(), utils.CtxKeyLogID, utils.RandStringRunes(16))

	Softs := make([]model.SoftConfig, 0)
	req := &proto.SearchSoftConfigRequst{}
	err := c.ShouldBindQuery(req)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logctx)+"SearchSoftConfig req bind json err: %v", err)
		c.JSON(http.StatusOK, proto.NewCommDataRsp(100001, "参数解析错误", &proto.SearchSoftConfigResponse{
			LogId:       utils.GetLogID(logctx),
			SoftConfigs: Softs,
		}))
		return
	}

	Softs, err = (&model.SoftConfig{}).GetSoftConfigList(gomysql.DB, req.Page, req.PageSize)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logctx)+"SearchSoftConfig get soft config list err: %v", err)
		c.JSON(http.StatusOK, proto.NewCommDataRsp(100002, "查询配置失败,数据库访问异常", &proto.SearchSoftConfigResponse{
			LogId:       utils.GetLogID(logctx),
			SoftConfigs: Softs,
		}))
		return
	}

	c.JSON(http.StatusOK, proto.NewSuccessRsp(&proto.SearchSoftConfigResponse{
		LogId:       utils.GetLogID(logctx),
		SoftConfigs: Softs,
	}))
}

func checkSoftConfig(softconf proto.SoftConfigRequest) error {
	if len(softconf.SoftName) == 0 {
		return errors.New("softName is null")
	}
	if len(softconf.SoftExeName) == 0 {
		return errors.New("softExeName is null")
	}
	if len(softconf.SoftVersion) == 0 {
		return errors.New("softVersion is null")
	}
	if len(softconf.SoftUrl) == 0 {
		return errors.New("softUrl is null")
	}
	if len(softconf.SoftInstallPath) == 0 {
		return errors.New("softInstallPath is null")
	}

	if containsWindowsKeyPath(softconf.SoftInstallPath) {
		return errors.New("softInstallPath contains Windows key path")
	}

	if len(softconf.SoftMd5) == 0 {
		return errors.New("softConfigPath is null")
	}

	if len(softconf.SoftRunCmd) == 0 || len(softconf.SoftCloseCmd) == 0 {
		return errors.New("SoftRunCmd/SoftCloseCmd is null")
	}

	if softconf.SoftFlag == 0 {
		return errors.New("softFlag is null")
	}

	return nil
}

// containsWindowsKeyPath checks if a string contains critical Windows file paths.
func containsWindowsKeyPath(s string) bool {
	// Define a regular expression to match critical Windows paths
	// This regex matches paths like C:\, C:\\, C://, C:\Users\<USER>\\/|\\\\)(Users\\\\|System32\\\\|Windows\\\\|Program Files\\\\)?`)
	return re.MatchString(s)
}

func WinClientGetSoftConfig(c *gin.Context) {
	logctx := context.WithValue(context.TODO(), utils.CtxKeyLogID, utils.RandStringRunes(16))
	req := &proto.WinClientGetSoftConfigRequest{}
	res := &proto.WinClientGetSoftConfigResponse{
		SoftConfigs: make([]proto.SoftConfigRequest, 0),
		LogId:       utils.GetLogID(logctx),
	}

	err := c.ShouldBindQuery(req)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logctx)+"WinClientGetSoftConfig req bind json err: %v", err)
		c.JSON(http.StatusOK, proto.NewCommServiceRsp(100001, "参数解析错误", res))
		return
	}
	oldconf, err := (&model.MachineConfig{}).GetMachineConfigByMachineName(gomysql.DB, req.MachineName)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logctx)+"WinClientGetSoftConfig get machine config err: %v", err)
		c.JSON(http.StatusOK, proto.NewCommServiceRsp(100002, "查询机器配置失败,请查看机器配置是否正确", res))
		return
	}

	if len(oldconf.MachineType) == 0 {
		logger.Log.Errorf(utils.MMark(logctx) + "WinClientGetSoftConfig machine type is null")
		c.JSON(http.StatusOK, proto.NewCommServiceRsp(100003, "机器没有配置类型,请联系管理员", res))
		return
	}

	Softs, err := (&model.SoftConfig{}).GetWinClientSoftConfig(gomysql.DB, req.MachineName, oldconf.MachineType)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logctx)+"WinClientGetSoftConfig get soft config list err: %v", err)
		c.JSON(http.StatusOK, proto.NewCommServiceRsp(100004, "查询配置失败,数据库访问异常", res))
		return
	}

	for _, v := range Softs {
		softrequst := proto.SoftConfigRequest{
			SoftName:              v.SoftName,
			SoftExeName:           v.SoftExeName,
			MachineName:           v.MachineName,
			SoftVersion:           v.SoftVersion,
			MachineType:           v.MachineType,
			SoftUrl:               v.SoftUrl,
			SoftInstallPath:       v.SoftInstallPath,
			SoftMd5:               v.SoftMd5,
			SoftRunCmd:            v.SoftRunCmd,
			SoftCloseCmd:          v.SoftCloseCmd,
			SoftUninstallCmd:      v.SoftUninstallCmd,
			SoftFlag:              v.SoftFlag,
			SoftConfigPath:        v.SoftConfigPath,
			SoftConfigVersionName: v.SoftConfigVersionName,
		}
		if v.MachineName == oldconf.MachineName && v.MachineType == oldconf.MachineType {
			res.SoftConfigs = append(res.SoftConfigs, softrequst)
			continue
		}

		if len(v.MachineName) != 0 {
			continue
		}

		if v.MachineType == oldconf.MachineType || len(v.MachineType) == 0 {
			res.SoftConfigs = append(res.SoftConfigs, softrequst)
		}
	}

	c.JSON(http.StatusOK, proto.NewSuccessServiceRsp(res))
}

func WinClientVersionReport(c *gin.Context) {
	logctx := context.WithValue(context.TODO(), utils.CtxKeyLogID, utils.RandStringRunes(16))
	req := &proto.WinClientVersionReportRequest{}
	res := &proto.WinClientVersionReportResponse{
		LogId: utils.GetLogID(logctx),
	}

	err := c.ShouldBindJSON(req)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logctx)+"WinClientVersionReport req bind json err: %v", err)
		c.JSON(http.StatusOK, proto.NewCommServiceRsp(100001, "参数解析错误", res))
		return
	}
	jsonconfig, err := json.Marshal(req.SoftConfigs)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logctx)+"WinClientVersionReport json marshal err: %v", err)
		c.JSON(http.StatusOK, proto.NewCommServiceRsp(100002, "参数解析错误", res))
		return
	}

	jsonip, err := json.Marshal(req.MachineIps)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logctx)+"WinClientVersionReport json marshal err: %v", err)
		c.JSON(http.StatusOK, proto.NewCommServiceRsp(100003, "参数解析错误", res))
		return
	}

	oldconf, err := (&model.MachineConfig{}).GetMachineConfigByMachineName(gomysql.DB, req.MachineName)
	if err != nil {
		m := model.MachineConfig{
			MachineName:  req.MachineName,
			MachineType:  "",
			MachineDesc:  "",
			MachineIp:    string(jsonip),
			SoftConfig:   string(jsonconfig),
			MachineState: enum.MachineOnline,
			CreatedAt:    time.Now(),
			UpdatedAt:    time.Now(),
		}
		err = m.CreateMachineConfig(gomysql.DB)
		if err != nil {
			logger.Log.Errorf(utils.MMark(logctx)+"WinClientVersionReport create machine config err: %v", err)
			c.JSON(http.StatusOK, proto.NewCommServiceRsp(100002, "创建配置失败", res))
			return
		}
		return
	}

	oldconf.MachineIp = string(jsonip)
	oldconf.SoftConfig = string(jsonconfig)
	oldconf.MachineState = enum.MachineOnline
	oldconf.UpdatedAt = time.Now()

	err = oldconf.UpdateMachineConfig(gomysql.DB)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logctx)+"WinClientVersionReport update machine config err: %v", err)
		c.JSON(http.StatusOK, proto.NewCommServiceRsp(100002, "更新配置失败", res))
		return
	}

	c.JSON(http.StatusOK, proto.NewSuccessServiceRsp(res))
}
