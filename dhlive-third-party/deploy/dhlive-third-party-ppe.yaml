---
apiVersion: v1
kind: ConfigMap
metadata:
  labels:
    app: digital-human
    module: dhlive-third-party
  name: dhlive-third-party
  namespace: 2d-v3
data:
  conf.toml: |
    ####################################################### 服务配置-PPE环境 #######################################################
    server-port = 8080
    server-name = "dhlive-third-party"
    log-file-prefix = "localhost"
    log-file-path = "./logs/"
    log-show-code-file = true
    log-show-code-func = true
    # consul配置
    [consul-setting]
    host = "127.0.0.1"
    port = 8500
    name = ""

    # 健康检查地址
    health-url = "/health"
    check-timeout = "10s"
    check-interval = "10s"
    deregister-critical-service-after = "30s"

    # mysql配置
    [mysql-setting]
    host = "mysql57-test.rdsmltt6s1aa9rz.rds.bj.baidubce.com"
    port = 3306
    database = "dhlive_third_platform_online"
    username = "dhlive_tp_plat"
    password = "Hi109@123"
    maxOpenConns = 1000
    maxIdlenConns = 100

    # redis配置
    [redis-setting]
    addr = "************:6379"
    username = ""
    password = ""

    # 日志上传的elasticsearch配置
    [log-es-setting]
    host = "http://*************:8200"
    username = "superuser"
    password = "Baidu_dh123"

    # 京东配置-百数
    [jd-setting-baishu]
    appKey = "7EE2AA4C39F0BFB800ACA957BD95C309"
    secretKey = "88069acbcae545539f957a996bf0357a"
    callbackUrl = "https://persona.baidu.com:8050/live2d/dhlive-cgi/dhlive-third-party/openapi/v1/oauth/callback"

    # 京东配置-慧播星
    [jd-setting-huiboxing]
    appKey = "********************************"
    secretKey = "63032f8a98f047cbbdf0b218119a78c6"
    callbackUrl = "https://persona.baidu.com:8050/live2d/dhlive-cgi/dhlive-third-party/openapi/v1/oauth/callback"

    # 京东配置-曦灵
    [jd-setting-xiling]
    appKey = "07A2B012C6C716E78DF98550C99CFEE3"
    secretKey = "5cfb663a0dd44f64ba87f08754b0de5f"
    callbackUrl = "https://persona.baidu.com:8050/live2d/dhlive-cgi/dhlive-third-party/openapi/v1/oauth/callback"

    # 美团配置
    [mt-setting]
    developerId = 112419
    signKey = "2o6mfo9cunaeag1g"
    businessId = 50

    # 淘宝配置-百数
    [tb-setting-baishu]
    appKey = "34666042"
    secretKey = "4f751269ddbeeaaf7214fa02aedc603d"
    callbackUrl = "https://robot.baidu.com/rdtest001/live2d-taobao/oauth/callback"

    # 淘宝配置-慧播星
    [tb-setting-huiboxing]
    appKey = "34666042"
    secretKey = "4f751269ddbeeaaf7214fa02aedc603d"
    callbackUrl = "https://robot.baidu.com/rdtest001/live2d-taobao/oauth/callback"

    # 淘宝配置-曦灵
    [tb-setting-xiling]
    appKey = "34666042"
    secretKey = "4f751269ddbeeaaf7214fa02aedc603d"
    callbackUrl = "https://robot.baidu.com/rdtest001/live2d-taobao/oauth/callback"

    [oauth-comm-setting]
    statePre = "ppe_"
    taoBaoBaseUrl = "https://oauth.taobao.com"
---
apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: digital-human
    module: dhlive-third-party
  name: dhlive-third-party
  namespace: 2d-v3
spec:
  replicas: 1
  minReadySeconds: 0
  strategy:
    type: RollingUpdate # 策略：滚动更新
    rollingUpdate:
      maxSurge: 25%
      maxUnavailable: 25%
  selector:
    matchLabels:
      app: digital-human
      module: dhlive-third-party
  template:
    metadata:
      labels:
        app: digital-human
        module: dhlive-third-party
    spec:
      affinity:
        podAntiAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            - labelSelector:
                matchExpressions:
                  - key: app
                    operator: In
                    values:
                      - digital-human
                  - key: module
                    operator: In
                    values:
                      - dhlive-third-party
              topologyKey: kubernetes.io/hostname
      restartPolicy: Always
      tolerations:
        - key: "limit"
          operator: "Equal"
          value: "lite"
          effect: "NoSchedule"
      containers:
        - name: dhlive-third-party
          image: ccr-246im3mw-pub.cnc.bj.baidubce.com/digitalhuman/dhlive-third-party:20241129_1732874888813
          imagePullPolicy: Always
          command: [ "sh" ]
          env:
            - name: POD_NAMESPACE
              valueFrom:
                fieldRef:
                  fieldPath: metadata.namespace
          args: [ "/home/<USER>/sbin/start.sh" ]
          # imagePullPolicy: IfNotPresent
          # imagePullPolicy: Never
          ports:
            - containerPort: 8080
          livenessProbe: # 健康检查：存活探针
            httpGet:
              path: /health
              port: 8080
            initialDelaySeconds: 20
            timeoutSeconds: 5
            periodSeconds: 5
          readinessProbe: # 健康检查：就绪探针
            httpGet:
              path: /health
              port: 8080
            initialDelaySeconds: 5
            timeoutSeconds: 1
            periodSeconds: 5
          volumeMounts:
            - mountPath: /home/<USER>/conf/conf.toml
              name: config-volume
              subPath: conf.toml
      volumes:
        - configMap:
            defaultMode: 420
            name: dhlive-third-party
          name: config-volume
      imagePullSecrets:
        - name: regcred
        - name: regcred-ccr
        - name: regcred-eccr
---
apiVersion: v1
kind: Service
metadata:
  labels:
    app: digital-human
    module: dhlive-third-party
  name: dhlive-third-party
  namespace: 2d-v3
spec:
  selector:
    app: digital-human
    module: dhlive-third-party
  ports:
    - protocol: TCP
      port: 8080
      targetPort: 8080
  type: ClusterIP