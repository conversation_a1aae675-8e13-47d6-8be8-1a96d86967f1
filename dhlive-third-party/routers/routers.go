package routers

import (
	"dhlive-third-party/handler"
	"github.com/gin-gonic/gin"
	"net/http"
)

// Routers 路由列表
func Routers(e *gin.Engine) {
	openAPI := e.Group("/openapi/v1")
	// 第三方oauth2授权回调地址
	openAPI.GET("/oauth/callback", handler.ThirdPartyOauthCallback)
	openAPI.Any("/oauth/cancel/callback", handler.ThirdPartyOauthCancelCallback)

	thirdParty := e.Group("/api/v1", handler.ThirdPartyCheckHeaderParam)
	// 第三方oauth2授权
	thirdParty.POST("/oauth", handler.ThirdPartyOauth)
	thirdParty.GET("/oauth", handler.ThirdPartyOauth)
	// 校验第三方oauth2授权
	thirdParty.POST("/oauth/check", handler.GetThirdPartyUserInfo)
	// 批量校验第三方oauth2授权
	thirdParty.POST("/oauth/batchCheck", handler.GetThirdPartyUserInfoBatch)
	// 取消第三方oauth2授权
	thirdParty.POST("/oauth/cancel", handler.ThirdPartyOauthCancel)
	// 第三方直播间商品获取
	thirdParty.POST("/live/goods", handler.GetThirdPartyLiveGoods)
	// 第三方直播间商品弹出
	thirdParty.POST("/live/goods/push", handler.ThirdPartyLiveGoodsPush)
	// 第三方红盾文本检测
	thirdParty.POST("/detect/text", handler.ThirdPartyDetectText)
	// 第三方红盾图片检测
	thirdParty.POST("/detect/images", handler.ThirdPartyDetectImages)
	// 第三方直播间获取
	thirdParty.POST("/live/search", handler.SearchThirdPartyLives)
	// 第三方直播间选择
	thirdParty.POST("/live/choice", handler.ChoiceThirdPartyLive)
	// 直播间信息上报-淘宝
	thirdParty.POST("/live/info/report/taobao", handler.ReportLiveInfo2TaoBao)
	// 第三方直播间消息推送
	thirdParty.GET("/live/message/:userId", handler.ThirdPartyLiveMessagePush)
	// 美团推流地址获取
	thirdParty.POST("/meituan/live/stream/url", handler.GetMTStreamPushUrl)
	// 获取美团直播间评论
	thirdParty.POST("/meituan/live/comment/query", handler.GetMtLiveComment)
	// 留资信息查询
	thirdParty.GET("/user/profile", handler.TempLocalAuthCheck, handler.SearchUserProfile)
	// 留资信息导出/下载csv
	thirdParty.GET("/user/profile/download", handler.GetUserProfile2Csv)
	// 留资
	thirdParty.POST("/user/profile", handler.AddUserProfile)
	// 第三方授权按钮配置获取
	thirdParty.GET("/btn/config", handler.GetWebConfig)
	// 第三方授权按钮配置
	thirdParty.POST("/btn/config", handler.TempLocalAuthCheck, handler.AddOrUpdateWebConfig)
	// 测试API
	thirdParty.GET("/server/test", handler.ServerTest)
}

// 跨域中间件
func corsMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Writer.Header().Set("Access-Control-Allow-Origin", "*")
		c.Writer.Header().Set("Access-Control-Allow-Methods", "POST, GET, OPTIONS, PUT, DELETE")
		c.Writer.Header().Set("Access-Control-Max-Age", "86400")
		c.Writer.Header().Set("Access-Control-Allow-Headers",
			"Content-Type, Content-Length, Accept-Encoding, X-CSRF-Token, Authorization")
		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(http.StatusOK)
			return
		}
		c.Next()
	}
}
