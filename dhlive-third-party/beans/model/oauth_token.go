package model

import (
	"acg-ai-go-common/gomysql"
	"dhlive-third-party/beans/enum"
	"gorm.io/gorm"
	"time"
)

// OauthToken 授权令牌记录表结构
type OauthToken struct {
	ID           uint64         `json:"id" gorm:"column:id;primarykey;autoIncrement"`              // 数据ID
	UserID       string         `json:"userId" gorm:"column:userId;type:varchar(150)"`             // 曦灵平台用户ID
	Platform     enum.Platform  `json:"platform" gorm:"column:platform;type:varchar(150)"`         // 第三方平台
	AppKey       string         `json:"appKey" gorm:"column:appKey;type:varchar(150)"`             // 第三方平台APPKey
	PuID         string         `json:"pUid" gorm:"column:pUid;type:varchar(150)"`                 // 第三方平台用户ID
	OpenID       string         `json:"openId" gorm:"column:openId;type:varchar(150)"`             // 第三方平台开放用户ID
	NickName     string         `json:"nickName" gorm:"column:nickName;type:varchar(255)"`         // 第三方用户昵称
	HeadImgURL   string         `json:"headImgUrl" gorm:"column:headImgUrl;type:varchar(1024)"`    // 第三方用户头像
	Gender       int            `json:"gender" gorm:"column:gender"`                               // 第三方用户性别
	VenderID     string         `json:"venderId" gorm:"column:venderId;type:varchar(150)"`         // 第三方平台商家ID
	ColType      int            `json:"colType" gorm:"column:colType"`                             // 第三方平台商家类型0-SOP;1-FBP;2-LBP;5-SOPL;6-虚卡;7-手机充值;8-机票;9-彩票;10-团购开放平台;11-酒店;12-火车票;13-电影票;14-景点;15-旅游路线;16-租车;17-票务;20-域名;21-拍卖;22-页面游戏;23-即开型彩票;24-车险商家;25-非车险商家;26-京东帮;27-网页游戏（技术服务;28-缴费;29-O2O;30-团购商家;31-EPT商家;32-机票开放平台;33-汽车票;55-测试商家;100-FCS;101-IBS;102-万商B2B;110-SCF模式;111-全渠道;113-ICF；
	ShopID       string         `json:"shopId" gorm:"column:shopId;type:varchar(150)"`             // 第三方平台店铺ID
	ShopName     string         `json:"shopName" gorm:"column:shopName;type:varchar(255)"`         // 第三方平台店铺名称
	AccessToken  string         `json:"accessToken" gorm:"column:accessToken;type:varchar(255)"`   // 鉴权令牌
	ExpiresIn    int            `json:"expiresIn" gorm:"column:expiresIn"`                         // 有效时间/秒
	TakeEffect   int64          `json:"takeEffect" gorm:"column:takeEffect"`                       // 开始生效时间/毫秒
	RefreshToken string         `json:"refreshToken" gorm:"column:refreshToken;type:varchar(255)"` // 刷新token令牌
	CreatedAt    time.Time      `json:"createdAt" gorm:"column:createdAt"`                         // 创建时间
	UpdatedAt    time.Time      `json:"updatedAt" gorm:"column:updateAt"`                          // 更新时间
	DeletedAt    gorm.DeletedAt `json:"deletedAt" gorm:"column:deletedAt;index"`                   // 删除时间/标记删除
}

func (ot *OauthToken) TableName() string {
	return "oauth_token_v1"
}

func (ot *OauthToken) Insert() error {
	// 创建数据
	return gomysql.DB.Create(&ot).Error
}

func (ot *OauthToken) Update() error {
	// 保存数据
	return gomysql.DB.Save(&ot).Error
}

func (ot *OauthToken) FindByID(id uint64) error {
	err := gomysql.DB.Where("id = ?", id).Find(&ot).Error
	if err == nil || err == gorm.ErrRecordNotFound {
		return nil
	}
	return err
}

func (ot *OauthToken) FindByOpenIDAndAppKey(platform enum.Platform, openId string, appKey string) error {
	err := gomysql.DB.Order("updateAt desc").
		Where("platform = ? and openId = ? and appKey = ?", platform, openId, appKey).
		First(&ot).Error
	if err == nil || err == gorm.ErrRecordNotFound {
		return nil
	}
	return err
}

func (ot *OauthToken) FindByUserIDAndAppKey(userId string, platform enum.Platform, openId string, appKey string) error {
	err := gomysql.DB.Order("updateAt desc").
		Where("userId = ? and platform = ? and openId = ? and appKey = ?", userId, platform, openId, appKey).
		First(&ot).Error
	if err == nil || err == gorm.ErrRecordNotFound {
		return nil
	}
	return err
}

func (ot *OauthToken) DeleteByUserIDAndOpenID(userId string, platform enum.Platform, openId string) error {
	return gomysql.DB.
		Where("userId = ? and platform = ? and openId = ?", userId, platform, openId).
		Delete(&ot).Error
}

func (ot *OauthToken) DeleteByOpenID(openId string, platform enum.Platform) error {
	return gomysql.DB.
		Where("openId = ? and platform = ?", openId, platform).
		Delete(&ot).Error
}
