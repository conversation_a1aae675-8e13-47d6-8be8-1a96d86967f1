package config

var (
	LocalConfig = &localConfig{}
)

type localConfig struct {
	JDSettingBaiShu    *JDOauthAppInfo  `toml:"jd-setting-baishu"`    // 京东配置-百数
	JDSettingHuiBoXing *JDOauthAppInfo  `toml:"jd-setting-huiboxing"` // 京东配置-慧播星
	JDSettingXiLing    *JDOauthAppInfo  `toml:"jd-setting-xiling"`    // 京东配置-曦灵
	MTSetting          *MTOauthAppInfo  `toml:"mt-setting"`
	TBSettingBaiShu    *TBOauthAppInfo  `toml:"tb-setting-baishu"`    // 淘宝配置-百数
	TBSettingHuiBoXing *TBOauthAppInfo  `toml:"tb-setting-huiboxing"` // 淘宝配置-慧播星
	TBSettingXiLing    *TBOauthAppInfo  `toml:"tb-setting-xiling"`    // 淘宝配置-曦灵
	OauthCommSetting   OauthCommSetting `toml:"oauth-comm-setting"`   // Oauth2.0授权公共配置
}

type JDOauthAppInfo struct {
	oauthAppInfo
}

type MTOauthAppInfo struct {
	DeveloperId int64  `toml:"developerId"` // 开发者ID
	SignKey     string `toml:"signKey"`
	BusinessId  int    `toml:"businessId"` // 业务ID
}

type TBOauthAppInfo struct {
	oauthAppInfo
}

type oauthAppInfo struct {
	AppKey      string `toml:"appKey"`
	SecretKey   string `toml:"secretKey"`
	CallbackURL string `toml:"callbackUrl"`
}

type OauthCommSetting struct {
	StatePre      string `toml:"statePre"`
	TaoBaoBaseURL string `toml:"taoBaoBaseUrl"`
}
