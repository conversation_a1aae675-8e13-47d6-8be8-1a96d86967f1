package handler

import (
	commProto "acg-ai-go-common/beans/proto"
	"acg-ai-go-common/global"
	"acg-ai-go-common/goredis"
	"acg-ai-go-common/logger"
	"acg-ai-go-common/utils"
	"dhlive-third-party/beans/enum"
	"dhlive-third-party/beans/model"
	"dhlive-third-party/beans/proto"
	"dhlive-third-party/beans/util"
	config "dhlive-third-party/conf"
	"dhlive-third-party/handler/meituan"
	"dhlive-third-party/handler/meituan/live"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/gorilla/websocket"
	"net/http"
	"strconv"
	"strings"
	"time"
)

const (
	ServerErrorMsg        = "服务繁忙"
	TPMsgOnlineUserKeyFmt = "%v:third-party:%v:msgPush:online:%v:%v:%v" // 推送第三方直播间消息在线用户标识Fmt, 示例third-party:${platform}:msgPush:${userId}:${liveId}
	TPMsgOnlineUserKeyExp = 5 * time.Second                             // 推送第三方直播间消息在线用户标识过期时间
	TPMsgQueueKeyFmt      = "%v:third-party:jdBarrage:%v:%v"            // 推送京东直播间消息队列标识Fmt, 示例third-party:jdBarrage:${appKey}:${liveId}
	TPMsgQueueKeyExp      = 5 * time.Minute                             // 推送京东直播间消息队列标识过期时间
	KBJDMsgQueueKeyFmt    = "%v:kafka-barrage:jdBarrage:%v:%v"          // 京东直播间消息队列标识Fmt, 示例kafka-barrage:jdBarrage:${appKey}:${liveId}
	TPWsCheckOauthExp     = 15 * time.Second                            // websocket校验权限间隔
)

func ThirdPartyLiveMessagePush(c *gin.Context) {
	nowTime := time.Now()
	requestId := c.GetHeader(global.HeaderRequestID)
	if len(requestId) == 0 {
		requestId = uuid.NewString()
	}
	wsUp := websocket.Upgrader{
		HandshakeTimeout: 0,
		// 缓冲区为0时默认使用HTTP的缓冲区，大小为4096
		ReadBufferSize:  0,
		WriteBufferSize: 0,
		WriteBufferPool: nil,
		Subprotocols:    nil,
		Error:           nil,
		CheckOrigin: func(r *http.Request) bool {
			return true
		},
		EnableCompression: false,
	}
	ws, err := wsUp.Upgrade(c.Writer, c.Request, nil)
	if err != nil {
		logger.CtxLog(c).Errorf("RequestId:[%v], ThirdPartyLiveMessagePush Upgrade ws fail, err:%v",
			requestId, err)
		c.JSON(http.StatusOK, commProto.NewCommRsp(500001, ServerErrorMsg))
		return
	}
	defer func() {
		logger.CtxLog(c).Infof("RequestId:[%v], ThirdPartyLiveMessagePush 连接断开, 在线时长:%v",
			requestId, time.Since(nowTime))
		_ = ws.Close()
	}()
	// 授权状态校验、获取当前用户信息及第三方信息、组装ClientInfo
	client := generateClientHandler(c, ws, requestId, nowTime.Unix())
	if client == nil || client.LiveChannel.ID == 0 {
		return
	}
	defer func() {
		client.LiveChannel.EndTime = time.Now().Unix()
		if err := client.LiveChannel.Update(); err != nil {
			logger.CtxLog(c).Infof("RequestId:[%v], ThirdPartyLiveMessagePush LiveChannel Update fail, err:%v",
				requestId, err)
		}
	}()
	go client.startSendMsgTask(c)
	// 健康检查
	go client.heartbeats(c)
	// 定期校验授权状态
	go client.startCheckOauth(c)
	// 监听redis队列，消费消息推送到客户端
	go client.distributeLiveMsgHandler(c)
	// 接收消息并处理
	client.procClientReqHandler(c)
}

// ClientInfo 客户端信息结构
type ClientInfo struct {
	OauthToken         *model.OauthToken            `json:"oauthToken"`
	LiveChannel        *model.LiveChannel           `json:"liveChannel"`
	C                  *gin.Context                 `json:"-"`                  // gin context
	OnlineKey          string                       `json:"onlineKey"`          // 在线标识Key
	OnlineExp          time.Duration                `json:"onlineExp"`          // 在线标识Key过期时间
	KBQueueKey         string                       `json:"kbMsgQueueKey"`      // kafka-barrage服务消息队列Key
	ChannelMsgQueueKey string                       `json:"channelMsgQueueKey"` // 第三方平台直播间消息队列Key
	ChannelMsgQueueExp time.Duration                `json:"channelMsgQueueExp"` // 第三方平台直播间消息队列Key过期时间
	RequestID          string                       `json:"requestId"`          // 本次请求ID
	Conn               *websocket.Conn              `json:"-"`                  // Websocket连接实例
	SendMsgChan        chan *proto.WebsocketMessage `json:"-"`                  // 发送消息通道
}

func generateClientHandler(c *gin.Context, ws *websocket.Conn, requestId string, loginTime int64) *ClientInfo {
	userId := c.Param("userId")
	if len(userId) == 0 {
		_ = ws.WriteJSON(proto.NewWsErrorInfoMessage(
			requestId, commProto.NewCommRsp(100001, "参数异常, userId不能为空")))
		return nil
	}
	req := &proto.LiveMessagePushQueryParam{}
	if err := c.ShouldBind(&req); err != nil {
		logger.CtxLog(c).Errorf("generateClientHandler bind param fail, err:%v",
			requestId, err)
		_ = ws.WriteJSON(proto.NewWsErrorInfoMessage(
			requestId, commProto.NewCommRsp(100001, "参数异常")))
		return nil
	}
	c.Request.Header.Set(HeaderRequestPlat, req.RequestPlat)
	if req.Token == SrcPlatHuiBoXing {
		c.Request.Header.Set(HeaderRequestPlat, SrcPlatHuiBoXing)
	}
	client := &ClientInfo{
		LiveChannel: &model.LiveChannel{
			UserID:    userId,
			LiveID:    req.LiveId,
			Platform:  req.Platform,
			OpenID:    req.OpenId,
			ChannelNo: req.ChannelNo,
			StartTime: loginTime,
			EndTime:   time.Now().Unix(),
		},
		C:                  c,
		OnlineExp:          TPMsgOnlineUserKeyExp,
		ChannelMsgQueueExp: TPMsgQueueKeyExp,
		RequestID:          requestId,
		Conn:               ws,
		SendMsgChan:        make(chan *proto.WebsocketMessage, 10),
	}
	oldLiveChannel := &model.LiveChannel{}
	if err := oldLiveChannel.FindByUserIdAndChannel(userId, req.LiveId, req.ChannelNo, req.Platform, req.OpenId); err != nil {
		logger.CtxLog(c).Errorf("generateClientHandler LiveChannel FindByUserIdAndChannel fail, requestId:%v, err:%v",
			requestId, err)
		_ = ws.WriteJSON(proto.NewWsErrorInfoMessage(
			uuid.NewString(), commProto.NewCommRsp(500003, ServerErrorMsg)))
		return nil
	} else if oldLiveChannel.ID == 0 {
		logger.CtxLog(c).Warnf("generateClientHandler LiveChannel not exist, requestId:%v, err:%v",
			requestId, err)
		_ = ws.WriteJSON(proto.NewWsErrorInfoMessage(
			uuid.NewString(), commProto.NewCommRsp(100003, "请先选择直播间")))
		return nil
	}
	if oldLiveChannel.StartTime == 0 {
		oldLiveChannel.StartTime = loginTime
		_ = oldLiveChannel.Update()
	}
	client.LiveChannel = oldLiveChannel

	// 防止重复连接
	onlineTagKey := fmt.Sprintf(TPMsgOnlineUserKeyFmt, utils.GetNameByRunEnv(), req.Platform,
		userId, req.ChannelNo, req.OpenId)
	if lock, err := goredis.GetClientV2().SetNX(c, onlineTagKey, loginTime,
		TPMsgOnlineUserKeyExp).Result(); err != nil {
		logger.CtxLog(c).Errorf("generateClientHandler 设置在线状态失败, err:%v", err)
		_ = ws.WriteJSON(proto.NewWsErrorInfoMessage(
			requestId, commProto.NewCommRsp(500002, ServerErrorMsg)))
		return nil
	} else if !lock {
		logger.CtxLog(c).Warnf("generateClientHandler 用户[%v]已有连接存在，请勿重复连接, key:%v",
			userId, onlineTagKey)
		_ = ws.WriteJSON(proto.NewWsErrorInfoMessage(
			requestId, commProto.NewCommRsp(100004, "请勿重复连接")))
		return nil
	}
	client.OnlineKey = onlineTagKey

	// 用户授权校验
	oauthToken, userInfo := client.thirdPartyOauthCheckWS()
	if oauthToken == nil || userInfo == nil {
		return nil
	}
	client.OauthToken = oauthToken
	client.ChannelMsgQueueKey = fmt.Sprintf(TPMsgQueueKeyFmt, utils.GetNameByRunEnv(), oauthToken.AppKey, req.ChannelNo)
	client.KBQueueKey = fmt.Sprintf(KBJDMsgQueueKeyFmt, utils.GetNameByRunEnv(), oauthToken.AppKey, req.ChannelNo)

	logger.CtxLog(c).Infof("generateClientHandler client info, onlineKey:%v, channelMsgKey:%v, kbQueueKey:%v",
		client.OnlineKey, client.ChannelMsgQueueKey, client.KBQueueKey)
	return client
}

// thirdPartyOauthCheck 校验第三方Oauth2授权并将第三方平台用户信息放入context的${platform_UserInfo}中
func (client *ClientInfo) thirdPartyOauthCheckWS() (*model.OauthToken, *ThirdPartyUserInfo) {
	nowTime := time.Now()
	switch client.LiveChannel.Platform {
	case enum.JD:
		// 查询授权APP的AK/SK
		oauthApp := choiceJDOauthApp(client.C.GetHeader(HeaderRequestPlat))
		client.C.Set(ContextJDOauthAppKey, oauthApp)
		appKey := oauthApp.AppKey
		secretKey := oauthApp.SecretKey
		// 查询最新Token
		oauthToken := &model.OauthToken{}
		if err := oauthToken.FindByUserIDAndAppKey(client.LiveChannel.UserID,
			client.LiveChannel.Platform, client.LiveChannel.OpenID, appKey); err != nil {
			logger.CtxLog(client.C).Errorf("thirdPartyOauthCheckWS OauthToken FindByUserIDAndAppKey fail, requestId:%v, err:%v",
				client.RequestID, err)
			_ = client.Conn.WriteJSON(proto.NewWsErrorInfoMessage(
				client.RequestID, commProto.NewCommRsp(500001, ServerErrorMsg)))
			return nil, nil
		} else if oauthToken.ID <= 0 ||
			len(oauthToken.AccessToken) == 0 {
			_ = client.Conn.WriteJSON(proto.NewWsAuthInvalidMessage(client.RequestID))
			return nil, nil
		} else if int64(oauthToken.ExpiresIn)+oauthToken.TakeEffect/1000 <= nowTime.Unix() {
			if err := refreshJDToken(oauthToken, appKey, secretKey, client.RequestID); err != nil {
				logger.CtxLog(client.C).Errorf("thirdPartyOauthCheckWS refreshJDToken fail, requestId:%v, err:%v",
					client.RequestID, err)
				_ = client.Conn.WriteJSON(proto.NewWsAuthInvalidMessage(uuid.NewString()))
				return nil, nil
			}
		}
		venderInfo, jdErrRsp, err := getJDVenderInfo(oauthToken, appKey, secretKey, client.RequestID)
		if err != nil {
			logger.CtxLog(client.C).Errorf("thirdPartyOauthCheckWS refreshJDToken fail, requestId:%v, err:%v",
				client.RequestID, err)
			_ = client.Conn.WriteJSON(proto.NewWsErrorInfoMessage(
				client.RequestID, commProto.NewCommRsp(510001, ServerErrorMsg)))
			return nil, nil
		} else if jdErrRsp != nil {
			_ = client.Conn.WriteJSON(proto.NewWsErrorInfoMessage(
				client.RequestID, commProto.NewCommDataRsp(510002, ServerErrorMsg, jdErrRsp)))
			return nil, nil
		} else if venderInfo.VenderId != 0 {
			if oauthToken.VenderID != fmt.Sprintf("%v", venderInfo.VenderId) ||
				oauthToken.ColType != venderInfo.ColType ||
				oauthToken.ShopID != fmt.Sprintf("%v", venderInfo.ShopId) ||
				oauthToken.ShopName != venderInfo.ShopName {
				oauthToken.VenderID = fmt.Sprintf("%v", venderInfo.VenderId)
				oauthToken.ColType = venderInfo.ColType
				oauthToken.ShopID = fmt.Sprintf("%v", venderInfo.ShopId)
				oauthToken.ShopName = venderInfo.ShopName
				if err = oauthToken.Update(); err != nil {
					logger.CtxLog(client.C).Errorf("thirdPartyOauthCheckWS Update OauthToken Vender fail, requestId:%v, err:%v",
						client.RequestID, err)
				}
			}
		}

		if venderInfo.VenderId == 0 && oauthToken.ColType != -1 {
			oauthToken.ColType = -1
			if err = oauthToken.Update(); err != nil {
				logger.CtxLog(client.C).Errorf("thirdPartyOauthCheckWS Update OauthToken colType fail, requestId:%v, err:%v",
					client.RequestID, err)
			}
		}
		// 根据最新Token获取第三方平台用户信息
		params := GenJDOpenApiParamsStr(map[string]string{
			"openId": oauthToken.OpenID,
		}, appKey, secretKey, oauthToken.AccessToken, JDGetUserInfoMethod)
		jdUserUrl := fmt.Sprintf(JDOpenAPICommURLFmt, params)
		jdUserRsp := &proto.JDUserRsp{}
		if err := httpGet(jdUserUrl, &jdUserRsp); err != nil {
			logger.CtxLog(client.C).Errorf("thirdPartyOauthCheckWS call uri[%v] fail, cost:%v, requestId:%v, err:%v",
				jdUserUrl, time.Since(nowTime), client.RequestID, err)
			_ = client.Conn.WriteJSON(proto.NewWsErrorInfoMessage(
				client.RequestID, commProto.NewCommRsp(500002, "call JD openAPI failed")))
			return nil, nil
		} else if jdUserRsp.ErrorResponse != nil ||
			jdUserRsp.JDUserGetUserInfoByOpenIdResponse.GetuserinfobyappidandopenidResult.Code != 0 {
			logger.CtxLog(client.C).Errorf("thirdPartyOauthCheckWS call uri[%v] fail, cost:%v, requestId:%v, rsp:%v",
				jdUserUrl, time.Since(nowTime), client.RequestID, jdUserRsp)
			_ = client.Conn.WriteJSON(proto.NewWsErrorInfoMessage(
				client.RequestID, commProto.NewCommDataRsp(400002, "call JD openAPI failed", jdUserRsp)))
			return nil, nil
		}
		logger.CtxLog(client.C).Infof("thirdPartyOauthCheckWS call uri[%v] succ, cost:%v, requestId:%v, rsp:%v",
			jdUserUrl, time.Since(nowTime), client.RequestID, jdUserRsp)
		jdUserInfo := jdUserRsp.JDUserGetUserInfoByOpenIdResponse.GetuserinfobyappidandopenidResult.Data
		return oauthToken, &ThirdPartyUserInfo{
			Platform: client.LiveChannel.Platform,
			VenderID: oauthToken.VenderID,
			ShopID:   oauthToken.ShopID,
			ShopName: oauthToken.ShopName,
			OpenID:   oauthToken.OpenID,
			NickName: jdUserInfo.NickName,
			ImageURL: jdUserInfo.ImageUrl,
			Gender:   jdUserInfo.Gendar,
		}
	case enum.MT:
		var developerId = config.LocalConfig.MTSetting.DeveloperId
		// 查询最新Token
		oauthToken := &model.OauthToken{}
		if err := oauthToken.FindByUserIDAndAppKey(client.LiveChannel.UserID, client.LiveChannel.Platform,
			client.LiveChannel.OpenID, fmt.Sprintf("%v", developerId)); err != nil {
			_ = client.Conn.WriteJSON(proto.NewWsErrorInfoMessage(
				client.RequestID, commProto.NewCommRsp(500001, ServerErrorMsg)))
			return nil, nil
		} else if oauthToken.ID <= 0 ||
			len(oauthToken.AccessToken) == 0 {
			_ = client.Conn.WriteJSON(proto.NewWsAuthInvalidMessage(client.RequestID))
			return nil, nil
		} else if int64(oauthToken.ExpiresIn)+oauthToken.TakeEffect/1000 <= nowTime.Unix() {
			if err := refreshMTToken(oauthToken, client.RequestID); err != nil {
				logger.CtxLog(client.C).Errorf("thirdPartyOauthCheckWS refreshMtToken fail, requestId:%v, err:%v",
					client.RequestID, err)
				_ = client.Conn.WriteJSON(proto.NewWsAuthInvalidMessage(uuid.NewString()))
				return nil, nil
			}
		}
		return oauthToken, &ThirdPartyUserInfo{
			Platform: client.LiveChannel.Platform,
			VenderID: oauthToken.VenderID,
			ColType:  oauthToken.ColType,
			ShopID:   oauthToken.ShopID,
			ShopName: oauthToken.ShopName,
			OpenID:   oauthToken.OpenID,
			// TODO 当前美图用户信息没有API可获取
			NickName: "",
			ImageURL: "",
			Gender:   -1,
		}
	default:
		_ = client.Conn.WriteJSON(proto.NewWsErrorInfoMessage(
			client.RequestID, commProto.NewCommRsp(100002,
				fmt.Sprintf("暂不支持%v平台", client.LiveChannel.Platform))))
		return nil, nil
	}
}

// 心跳
func (client *ClientInfo) heartbeats(context *gin.Context) {
	onlineTicker := time.NewTicker(time.Second)
	defer onlineTicker.Stop()
	queueTicker := time.NewTicker(time.Second)
	defer queueTicker.Stop()
	for {
		select {
		case <-context.Request.Context().Done():
			if err := goredis.GetClientV2().Del(context, client.OnlineKey).Err(); err != nil {
				logger.CtxLog(client.C).Errorf("RequestId:[%v], heartbeats 删除在线状态失败, err:%v",
					client.RequestID, err)
			}
			logger.CtxLog(client.C).Infof("RequestId:[%v], heartbeats is end! userId:%v, err:%v",
				client.RequestID, client.LiveChannel.UserID, context.Request.Context().Err())
			return
		case <-onlineTicker.C:
			onlineTicker = time.NewTicker(client.OnlineExp * 9 / 10)
			client.sendMsg(proto.NewWsSendHeartBertMessage(uuid.NewString()))
			existsNum, err := goredis.GetClientV2().Exists(context, client.OnlineKey).Result()
			if err != nil {
				logger.CtxLog(client.C).Errorf("RequestId:[%v], heartbeats Redis Exists key[%v] fail, err:%v",
					client.RequestID, client.OnlineKey, err)
				continue
			}
			if existsNum == 0 {
				goredis.GetClientV2().SetNX(context, client.OnlineKey, time.Now().Unix(), client.OnlineExp)
			}
			if suc, err := goredis.GetClientV2().Expire(context, client.OnlineKey,
				client.OnlineExp).Result(); err != nil || !suc {
				logger.CtxLog(client.C).Errorf("RequestId:[%v], heartbeats Redis Expire key[%v] fail, err:%v",
					client.RequestID, client.ChannelMsgQueueKey, err)
				continue
			}
		case <-queueTicker.C:
			queueTicker = time.NewTicker(client.ChannelMsgQueueExp * 9 / 10)
			_, err := goredis.GetClientV2().Set(context, client.ChannelMsgQueueKey,
				client.RequestID, client.ChannelMsgQueueExp).Result()
			if err != nil {
				logger.CtxLog(client.C).Errorf("RequestId:[%v], heartbeats Redis Set key[%v] fail, err:%v",
					client.RequestID, client.ChannelMsgQueueKey, err)
				continue
			}
		}
	}
}

// 下发任务处理函数
func (client *ClientInfo) distributeLiveMsgHandler(context *gin.Context) {
	var cursor int64
	endTime := time.Now().Add(-2 * time.Second)
	startTime := endTime.Add(-10 * time.Second)
	for {
		select {
		case <-context.Request.Context().Done():
			logger.CtxLog(client.C).Infof("RequestId:[%v], distributeLiveMsgHandler is end! userId:%v, err:%v",
				client.RequestID, client.LiveChannel.UserID, context.Request.Context().Err())
			return
		default:
			switch client.LiveChannel.Platform {
			case enum.JD:
				// 监听队列并下发消息
				rcV2 := goredis.GetClientV2()
				count, err := rcV2.LLen(context, client.KBQueueKey).Result()
				if err != nil {
					logger.CtxLog(client.C).Errorf("RequestId:[%v], distributeLiveMsgHandler LLen fail, err:%v",
						client.RequestID, err)
					time.Sleep(time.Second)
					continue
				}
				if count > 0 {
					var liveMessages []*proto.BarrageOrAct
					r, err := rcV2.RPop(context, client.KBQueueKey).Result()
					if err != nil {
						logger.CtxLog(client.C).Errorf("RequestId:[%v], distributeLiveMsgHandler RPopCount fail, key:%v, count:%v, err:%v",
							client.RequestID, client.KBQueueKey, count, err)
						time.Sleep(time.Second)
						continue
					} else if len(r) == 0 {
						continue
					}
					var jdLiveMsg []*proto.JDLiveMsg
					// 序列化数据
					if err := json.Unmarshal([]byte(r), &jdLiveMsg); err != nil {
						logger.CtxLog(client.C).Errorf("RequestId:[%v], distributeLiveMsgHandler json Unmarshal fail, r:%v, err:%v",
							client.RequestID, r, err)
						continue
					}
					// 转换数据, 组装结果
					for _, msg := range jdLiveMsg {
						barrageOrAct := convertJdLiveMsg2BarrageOrAct(msg)
						// TODO 过滤掉超过5分钟的消息？
						//if barrageOrAct.Time < time.Now().UnixMilli()-5*60*1000 {
						//
						//}
						if barrageOrAct != nil {
							liveMessages = append(liveMessages, barrageOrAct)
						}
					}
					if len(liveMessages) > 0 {
						// 下发结果
						client.sendMsg(proto.NewWsMessage(enum.ActionMessage, uuid.NewString(), liveMessages))
					}
				}
			case enum.MT:
				if len(client.LiveChannel.OtherInfo) == 0 {
					continue
				}
				var liveMaterial *proto.LiveMaterial
				if err := json.Unmarshal([]byte(client.LiveChannel.OtherInfo), &liveMaterial); err != nil {
					logger.CtxLog(client.C).Errorf("RequestId:[%v], distributeLiveMsgHandler json Unmarshal fail, userId:%v, err:%v",
						client.RequestID, client.LiveChannel.UserID, err)
					continue
				}
				channelIntNo, _ := strconv.ParseInt(client.LiveChannel.ChannelNo, 10, 64)
				var limit int64 = 200
				// 获取美团评论消息并下发
				queryMtLiveCommentReq := &live.QueryMTLiveCommentReq{
					LiveId:         channelIntNo,
					DigitalImageId: liveMaterial.FigureId,
					StartTime:      startTime.Format("2006-01-02 15:04:05"),
					EndTime:        endTime.Format("2006-01-02 15:04:05"),
					Cursor:         0,
					Limit:          limit,
				}
				nt := time.Now()
				rsp, err := queryMtLiveCommentReq.DoInvoke(meituan.NewDefaultClient(),
					config.LocalConfig.MTSetting.BusinessId,
					client.OauthToken.AccessToken)
				if err != nil {
					logger.CtxLog(client.C).Errorf("RequestId:[%v], distributeLiveMsgHandler queryMtLiveCommentReq fail, req:%v, err:%v",
						client.RequestID, queryMtLiveCommentReq, err)
					client.sendMsg(proto.NewWsErrorInfoMessage(client.RequestID, err.Error()))
					continue
				}
				logger.CtxLog(client.C).Infof("RequestId:[%v], distributeLiveMsgHandler queryMtLiveCommentReq success, curTime:%v, MaxCursor:%v, startTime:%v, endTime:%v, rspData:%v",
					client.RequestID, nt.Format("2006-01-02 15:04:05"), cursor, queryMtLiveCommentReq.StartTime, queryMtLiveCommentReq.EndTime, rsp.Data)
				switch rsp.Code {
				case "0", meituan.SuccessCode:
					var liveMessages []*proto.BarrageOrAct
					var mtBarrageData []*model.MTBarrageData
					for _, mtLiveComment := range rsp.Data {
						if mtLiveComment.Cursor > cursor {
							cursor = mtLiveComment.Cursor
						}
						if strings.HasPrefix(mtLiveComment.NickName, "【主播】") {
							continue
						}
						mtBarrage := &model.MTBarrageData{}
						if err := mtBarrage.FindByCursor(mtLiveComment.Cursor,
							client.LiveChannel.ChannelNo, client.LiveChannel.LiveID); err != nil {
							logger.CtxLog(client.C).Errorf("RequestId:[%v], distributeLiveMsgHandler FindByCursor fail, err:%v",
								client.RequestID, err)
						}
						if mtBarrage.ID > 0 {
							continue
						}
						barrage := convertMtLiveMsg2BarrageOrAct(&mtLiveComment)
						liveMessages = append(liveMessages, barrage)
						mtBarrageData = append(mtBarrageData, &model.MTBarrageData{
							LiveID:       client.LiveChannel.ChannelNo,
							XlLiveID:     client.LiveChannel.LiveID,
							MsgCursor:    mtLiveComment.Cursor,
							NickName:     mtLiveComment.NickName,
							Message:      mtLiveComment.Message,
							MtCreateTime: mtLiveComment.CreateTime,
							RequestParam: util.Struct2JsonStr(queryMtLiveCommentReq),
						})
					}
					if len(liveMessages) > 0 {
						mtBarrage := &model.MTBarrageData{}
						if err := mtBarrage.InsertBatch(mtBarrageData); err != nil {
							logger.CtxLog(client.C).Errorf("RequestId:[%v], distributeLiveMsgHandler mtBarrage InsertBatch fail, err:%v",
								client.RequestID, err)
						}
						logger.CtxLog(client.C).Infof("sendMsg %v", liveMessages)
						// 下发结果
						client.sendMsg(proto.NewWsMessage(enum.ActionMessage, uuid.NewString(), liveMessages))
					}
				case "OP_UNIAUTH_FAILED":
					client.sendMsg(proto.NewWsAuthInvalidMessage(client.RequestID))
				default:
					client.sendMsg(proto.NewWsErrorInfoMessage(client.RequestID, rsp))
				}
				startTime = endTime
				time.Sleep(3 * time.Second)
				// 当前美团的弹幕会有延迟问题，因此每次获取两秒前的数据
				endTime = time.Now().Add(-2 * time.Second)
			}
		}
	}
}

// 处理客户端消息
func (client *ClientInfo) procClientReqHandler(context *gin.Context) {
	readTimeout := time.Second * 30
	defer func() {
		logger.CtxLog(client.C).Infof("RequestId:[%v], procClientMsgHandler is end! userId:%v, err:%v",
			client.RequestID, client.LiveChannel.UserID, context.Request.Context().Err())
	}()
	for {
		select {
		case <-context.Request.Context().Done():
			return
		default:
			_ = client.Conn.SetReadDeadline(time.Now().Add(readTimeout))
			req := &proto.WebsocketMessage{}
			if err := client.Conn.ReadJSON(&req); err != nil {
				var closeErr *websocket.CloseError
				if errors.As(err, &closeErr) {
					logger.CtxLog(client.C).Warnf("RequestId:[%v], procClientReqHandler ws is close! userId:%v, err:%v",
						client.RequestID, client.LiveChannel.UserID, closeErr)
					return
				}
				if strings.Contains(err.Error(), "i/o timeout") {
					logger.CtxLog(client.C).Warnf("RequestId:[%v], procClientMsgHandler ReadJson is timeout! userId:%v, err:%v",
						client.RequestID, client.LiveChannel.UserID, err)
					client.sendMsg(proto.NewWsErrorInfoMessage(
						uuid.NewString(),
						commProto.NewCommRsp(230001, "消息等待超时/"+readTimeout.String())))
					return
				}
				logger.CtxLog(client.C).Errorf("RequestId:[%v], procClientReqHandler bind param fail, err:%v",
					client.RequestID, err)
				client.sendMsg(proto.NewWsErrorInfoMessage(
					uuid.NewString(),
					commProto.NewCommRsp(100001, "参数异常, 请检查")))
				continue
			}
			logger.CtxLog(client.C).Infof("RequestId:[%v], procClientReqHandler req:%v", client.RequestID, req)
			switch req.Action {
			case enum.ActionHeartBeat: // 心跳
				logger.CtxLog(client.C).Infof("RequestId:[%v], procClientReqHandler Action:%v, logId:%v",
					client.RequestID, req.Action, req.RequestId)
				client.sendMsg(proto.NewWsHeartBertMessage(req.RequestId))
				continue
			}
		}
	}
}

// 向客户端发送消息
func (client *ClientInfo) sendMsg(msg *proto.WebsocketMessage) {
	client.SendMsgChan <- msg
}

// 启动向客户端发送消息的任务
func (client *ClientInfo) startSendMsgTask(context *gin.Context) {
	for {
		select {
		case <-context.Request.Context().Done():
			logger.CtxLog(client.C).Infof("RequestId:[%v], startSendMsgTask is end! userId:%v, err:%v",
				client.RequestID, client.LiveChannel.UserID, context.Request.Context().Err())
			return
		case msg := <-client.SendMsgChan:
			if msg.Action == enum.ActionSendHeartBeat {
				_ = client.Conn.WriteMessage(websocket.PingMessage, nil)
				continue
			}
			if err := client.Conn.WriteJSON(msg); err != nil {
				logger.CtxLog(client.C).Errorf("RequestId:[%v], startSendMsgTask WriteJSON fail, msg:%v, err:%v",
					client.RequestID, msg, err)
			}
			if msg.Action == enum.ActionAuthInvalid {
				_ = client.Conn.Close()
			}
		}
	}
}

// 转换jdLiveMsg结构到BarrageOrAct结构
func convertJdLiveMsg2BarrageOrAct(jdLiveMsg *proto.JDLiveMsg) *proto.BarrageOrAct {
	var msgType enum.LiveMsgType
	switch jdLiveMsg.MsgType {
	case "3":
		msgType = enum.FollowLiveMsg
	case "4":
		msgType = enum.LikeLiveMsg
	case "5":
		msgType = enum.EnterLiveMsg
	case "8":
		msgType = enum.BarrageLiveMsg
	case "10":
		msgType = enum.BarrageLiveMsg
	default:
		return nil
	}
	return &proto.BarrageOrAct{
		Type:    msgType,
		Id:      jdLiveMsg.MsgID,
		Time:    jdLiveMsg.Time,
		Message: jdLiveMsg.Content,
		Info: proto.BarrageOrActInfo{
			Nickname: jdLiveMsg.Nickname,
		},
	}
}

// 转换美团直播间消息结构到BarrageOrAct结构
func convertMtLiveMsg2BarrageOrAct(mtLiveComment *live.MTLiveComment) *proto.BarrageOrAct {
	return &proto.BarrageOrAct{
		Type:    enum.BarrageLiveMsg,
		Id:      fmt.Sprintf("%v", mtLiveComment.Cursor),
		Time:    mtLiveComment.CreateTime,
		Message: mtLiveComment.Message,
		Info: proto.BarrageOrActInfo{
			Nickname: mtLiveComment.NickName,
		},
	}
}

func (client *ClientInfo) startCheckOauth(context *gin.Context) {
	ticker := time.NewTicker(TPWsCheckOauthExp)
	var appKey string
	var secretKey string
	defer ticker.Stop()
	for {
		select {
		case <-context.Request.Context().Done():
			logger.CtxLog(client.C).Infof("RequestId:[%v], startCheckOauth is end! userId:%v, err:%v",
				client.RequestID, client.LiveChannel.UserID, context.Request.Context().Err())
			return
		case <-ticker.C:
			switch client.LiveChannel.Platform {
			case enum.JD:
				// 查询授权APP的AK/SK
				oauthAppInterface, exists := client.C.Get(ContextJDOauthAppKey)
				if !exists || oauthAppInterface == nil {
					client.sendMsg(proto.NewWsErrorInfoMessage(
						client.RequestID, commProto.NewCommRsp(460066, "invalid Source Header")))
				}
				oauthApp := oauthAppInterface.(*config.JDOauthAppInfo)
				appKey = oauthApp.AppKey
				secretKey = oauthApp.SecretKey
			case enum.MT:
				// 查询授权APP的AK/SK
				appKey = fmt.Sprintf("%v", config.LocalConfig.MTSetting.DeveloperId)
			}

			// 查询最新Token
			oauthToken := &model.OauthToken{}
			if err := oauthToken.FindByUserIDAndAppKey(client.LiveChannel.UserID,
				client.LiveChannel.Platform, client.LiveChannel.OpenID, appKey); err != nil {
				logger.CtxLog(client.C).Errorf("RequestId:[%v], startCheckOauth OauthToken FindByUserIDAndAppKey fail, err:%v",
					client.RequestID, err)
				client.sendMsg(proto.NewWsErrorInfoMessage(
					client.RequestID, commProto.NewCommRsp(500001, ServerErrorMsg)))
			} else if oauthToken.ID <= 0 ||
				len(oauthToken.AccessToken) == 0 {
				client.sendMsg(proto.NewWsAuthInvalidMessage(client.RequestID))
			} else if int64(oauthToken.ExpiresIn)+oauthToken.TakeEffect/1000 <= time.Now().Unix() {
				switch client.LiveChannel.Platform {
				case enum.JD:
					if err := refreshJDToken(oauthToken, appKey, secretKey, client.RequestID); err != nil {
						logger.CtxLog(client.C).Errorf("RequestId:[%v], startCheckOauth refreshJDToken fail, err:%v",
							client.RequestID, err)
						client.sendMsg(proto.NewWsAuthInvalidMessage(client.RequestID))
					}
				case enum.MT:
					if err := refreshMTToken(oauthToken, client.RequestID); err != nil {
						logger.CtxLog(client.C).Errorf("RequestId:[%v], startCheckOauth refreshMTToken fail, err:%v",
							client.RequestID, err)
						client.sendMsg(proto.NewWsAuthInvalidMessage(client.RequestID))
					}
				}
			}
			client.OauthToken = oauthToken
		}
	}
}

func GetMTStreamPushUrl(c *gin.Context) {
	nowTime := time.Now()
	req := &proto.GetMTStreamPushUrlReq{}
	if err := c.ShouldBind(&req); err != nil {
		logger.CtxLog(c).Errorf("GetMTStreamPushUrl bind param fail, err:%v", err)
		c.JSON(http.StatusOK, commProto.NewCommRsp(100001, "参数异常"))
		return
	}
	oauthToken, ok := thirdPartyOauthCheck(c, req.UserID, req.Platform, req.OpenID)
	if oauthToken == nil || !ok {
		logger.CtxLog(c).Errorf("GetMTStreamPushUrl thirdPartyOauthCheck fail, cost:%v",
			time.Since(nowTime))
		return
	}
	parseIntLiveId, err := strconv.ParseInt(req.LiveID, 10, 64)
	if err != nil {
		logger.CtxLog(c).Errorf("GetMTStreamPushUrl ParseInt LiveID fail, err:%v", err)
		c.JSON(http.StatusOK, commProto.NewCommRsp(100002, "直播间ID参数异常，应为数字字符串"))
		return
	}
	req.LiveIntID = parseIntLiveId
	pushUrlWithLiveMaterialReq := convertMtStreamPushUrlReq(req)
	rsp, err := pushUrlWithLiveMaterialReq.DoInvoke(meituan.NewDefaultClient(),
		config.LocalConfig.MTSetting.BusinessId,
		oauthToken.AccessToken)
	if err != nil {
		logger.CtxLog(c).Errorf("GetMTStreamPushUrl pushUrlWithLiveMaterialReq DoInvoke fail, err:%v", err)
		c.JSON(http.StatusOK, commProto.NewCommRsp(500001, ServerErrorMsg))
		return
	}
	switch rsp.Code {
	case "0", meituan.SuccessCode:
		c.JSON(http.StatusOK, commProto.NewSuccessRsp(rsp.Data))
		return
	case "10004":
		c.JSON(http.StatusOK, commProto.NewCommRsp(110004, rsp.Msg))
		return
	case "OP_UNIAUTH_FAILED":
		c.JSON(http.StatusOK, commProto.NewCommRsp(400001, "未授权"))
		return
	default:
		c.JSON(http.StatusOK, commProto.NewCommDataRsp(500002, ServerErrorMsg, rsp))
		return
	}
}

func convertMtStreamPushUrlReq(req *proto.GetMTStreamPushUrlReq) *live.PushStreamUrlGetWithMaterialReq {
	var products []*live.Products
	for _, product := range req.Products {
		var questions []*live.MTQuestion
		for _, question := range product.Questions {
			questions = append(questions, &live.MTQuestion{
				Question: question.Question,
				Answer:   question.Answer,
			})
		}
		products = append(products, &live.Products{
			ProductName: product.ProductName,
			Content:     product.Content,
			Questions:   questions,
		})
	}
	return &live.PushStreamUrlGetWithMaterialReq{
		LiveId: req.LiveIntID,
		LiveMateria: &live.LiveMateria{
			CharacterId:    req.FigureId,
			GenerateType:   2,
			InteractType:   1,
			LiveType:       1,
			PreviewLayouts: req.PreviewLayouts,
			PreviewVideos:  req.PreviewVideos,
			Products:       products,
			VoiceId:        req.VoiceID,
		},
		StreamParam: &live.StreamParam{
			LiveModel: req.LiveModel,
		},
	}
}

func GetMtLiveComment(c *gin.Context) {
	nowTime := time.Now()
	req := &proto.GetMtLiveCommentReq{}
	if err := c.ShouldBind(&req); err != nil {
		logger.CtxLog(c).Errorf("GetMtLiveComment bind param fail, err:%v", err)
		c.JSON(http.StatusOK, commProto.NewCommRsp(100001, "参数异常"))
		return
	}
	oauthToken, ok := thirdPartyOauthCheck(c, req.UserID, req.Platform, req.OpenID)
	if oauthToken == nil || !ok {
		logger.CtxLog(c).Errorf("GetMtLiveComment thirdPartyOauthCheck fail, cost:%v",
			time.Since(nowTime))
		return
	}
	queryMtLiveCommentReq := &live.QueryMTLiveCommentReq{
		LiveId:         req.LiveId,
		DigitalImageId: req.DigitalImageId,
		StartTime:      req.StartTime,
		EndTime:        req.EndTime,
		Cursor:         req.Cursor,
		Limit:          req.Limit,
	}
	rsp, err := queryMtLiveCommentReq.DoInvoke(meituan.NewDefaultClient(),
		config.LocalConfig.MTSetting.BusinessId,
		oauthToken.AccessToken)
	if err != nil {
		logger.CtxLog(c).Errorf("GetMtLiveComment queryMtLiveCommentReq fail, err:%v", err)
		c.JSON(http.StatusOK, commProto.NewCommRsp(500001, ServerErrorMsg))
		return
	}
	switch rsp.Code {
	case "0", meituan.SuccessCode:
		c.JSON(http.StatusOK, commProto.NewSuccessRsp(&rsp.Data))
		return
	case "10005":
		c.JSON(http.StatusOK, commProto.NewCommRsp(510005, rsp.Msg))
		return
	case "OP_UNIAUTH_FAILED":
		c.JSON(http.StatusOK, commProto.NewCommRsp(400001, "未授权"))
		return
	default:
		c.JSON(http.StatusOK, commProto.NewCommDataRsp(500002, ServerErrorMsg, rsp))
		return
	}
}
