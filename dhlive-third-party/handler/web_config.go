package handler

import (
	commProto "acg-ai-go-common/beans/proto"
	"acg-ai-go-common/logger"
	"dhlive-third-party/beans/model"
	"dhlive-third-party/beans/proto"
	"encoding/json"
	"github.com/gin-gonic/gin"
	"net/http"
)

func GetWebConfig(c *gin.Context) {
	req := &proto.GetWebConfigReq{}
	if err := c.ShouldBind(&req); err != nil {
		logger.CtxLog(c).<PERSON><PERSON><PERSON>("GetWebConfig bind param fail, err:%v", err)
		c.<PERSON><PERSON><PERSON>(http.StatusOK, commProto.NewCommRsp(100001, "参数异常"))
		return
	}
	webConfig := &model.WebConfig{}
	if err := webConfig.FindByName(req.Name); err != nil {
		logger.CtxLog(c).<PERSON><PERSON><PERSON>("GetWebConfig FindByName fail, err:%v", err)
		c.<PERSON>(http.StatusOK, commProto.NewCommRsp(500001, ServerErrorMsg))
		return
	}
	c.<PERSON>(http.StatusOK, commProto.NewSuccessRsp(map[string]interface{}{
		"name":    webConfig.Name,
		"content": webConfig.Content,
	}))
}

func AddOrUpdateWebConfig(c *gin.Context) {
	req := &proto.AddOrUpdateWebConfigReq{}
	if err := c.ShouldBind(&req); err != nil {
		logger.CtxLog(c).Errorf("AddOrUpdateWebConfig bind param fail, err:%v", err)
		c.JSON(http.StatusOK, commProto.NewCommRsp(100001, "参数异常"))
		return
	}
	m := make(map[string]interface{})
	if err := json.Unmarshal([]byte(req.Content), &m); err != nil {
		c.JSON(http.StatusOK, commProto.NewCommRsp(100002, "content is not json string"))
		return
	}
	webConfig := &model.WebConfig{}
	if err := webConfig.FindByName(req.Name); err != nil {
		logger.CtxLog(c).Errorf("AddOrUpdateWebConfig FindByName fail, err:%v", err)
		c.JSON(http.StatusOK, commProto.NewCommRsp(500001, err.Error()))
		return
	}
	webConfig.Name = req.Name
	webConfig.Content = req.Content
	if err := webConfig.SaveOrUpdate(); err != nil {
		c.JSON(http.StatusOK, commProto.NewCommRsp(500002, err.Error()))
		return
	}
	c.JSON(http.StatusOK, commProto.NewSuccessRsp(nil))
	return
}
