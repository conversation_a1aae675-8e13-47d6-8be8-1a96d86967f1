package response

import (
	"dhlive-third-party/handler/taobao/topsdk/defaultability/domain"
)

type TaobaoLiveVirtualanchorGetLiveDetailResponse struct {

	/*
	   System request id
	*/
	RequestId string `json:"request_id,omitempty" `

	/*
	   System body
	*/
	Body string

	/*
	   traceId
	*/
	TraceId string `json:"trace_id,omitempty" `
	/*
	   返回数据对象
	*/
	Data domain.TaobaoLiveVirtualanchorGetLiveDetailLiveDetailDTO `json:"data,omitempty" `
	/*
	   执行是否成功 business_success：成功;false：失败
	*/
	BusinessSuccess bool `json:"business_success,omitempty" `
	/*
	   错误码code
	*/
	ResultCode string `json:"result_code,omitempty" `
	/*
	   文案信息
	*/
	Info string `json:"info,omitempty" `
}
