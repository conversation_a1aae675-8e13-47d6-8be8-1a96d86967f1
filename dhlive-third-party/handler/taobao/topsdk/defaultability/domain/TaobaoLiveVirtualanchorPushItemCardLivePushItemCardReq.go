package domain

type TaobaoLiveVirtualanchorPushItemCardLivePushItemCardReq struct {
	/*
	   商品Id     */
	ItemId *int64 `json:"item_id,omitempty" `

	/*
	   直播间Id     */
	LiveId *int64 `json:"live_id,omitempty" `
}

func (s *TaobaoLiveVirtualanchorPushItemCardLivePushItemCardReq) SetItemId(v int64) *TaobaoLiveVirtualanchorPushItemCardLivePushItemCardReq {
	s.ItemId = &v
	return s
}
func (s *TaobaoLiveVirtualanchorPushItemCardLivePushItemCardReq) SetLiveId(v int64) *TaobaoLiveVirtualanchorPushItemCardLivePushItemCardReq {
	s.LiveId = &v
	return s
}
