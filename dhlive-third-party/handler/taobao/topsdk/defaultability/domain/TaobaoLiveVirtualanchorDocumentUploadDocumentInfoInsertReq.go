package domain

type TaobaoLiveVirtualanchorDocumentUploadDocumentInfoInsertReq struct {
	/*
	   服务商名称     */
	IsvName *string `json:"isv_name,omitempty" `

	/*
	   appKey     */
	IsvAppKey *string `json:"isv_app_key,omitempty" `

	/*
	   文档地址链接     */
	DocumentUrl *string `json:"document_url,omitempty" `

	/*
	   文档版本信息，从1开始，逐渐递增     */
	DocumentVersion *string `json:"document_version,omitempty" `
}

func (s *TaobaoLiveVirtualanchorDocumentUploadDocumentInfoInsertReq) SetIsvName(v string) *TaobaoLiveVirtualanchorDocumentUploadDocumentInfoInsertReq {
	s.IsvName = &v
	return s
}
func (s *TaobaoLiveVirtualanchorDocumentUploadDocumentInfoInsertReq) SetIsvAppKey(v string) *TaobaoLiveVirtualanchorDocumentUploadDocumentInfoInsertReq {
	s.IsvAppKey = &v
	return s
}
func (s *TaobaoLiveVirtualanchorDocumentUploadDocumentInfoInsertReq) SetDocumentUrl(v string) *TaobaoLiveVirtualanchorDocumentUploadDocumentInfoInsertReq {
	s.DocumentUrl = &v
	return s
}
func (s *TaobaoLiveVirtualanchorDocumentUploadDocumentInfoInsertReq) SetDocumentVersion(v string) *TaobaoLiveVirtualanchorDocumentUploadDocumentInfoInsertReq {
	s.DocumentVersion = &v
	return s
}
