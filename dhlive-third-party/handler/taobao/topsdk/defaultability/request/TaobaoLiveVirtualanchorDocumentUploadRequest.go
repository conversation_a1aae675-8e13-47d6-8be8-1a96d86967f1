package request

import (
	"dhlive-third-party/handler/taobao/topsdk/defaultability/domain"
	"dhlive-third-party/handler/taobao/topsdk/util"
)

type TaobaoLiveVirtualanchorDocumentUploadRequest struct {
	/*
	   上传视频规范文档对象     */
	DocumentInfoInsertReq *domain.TaobaoLiveVirtualanchorDocumentUploadDocumentInfoInsertReq `json:"document_info_insert_req" required:"true" `
}

func (s *TaobaoLiveVirtualanchorDocumentUploadRequest) SetDocumentInfoInsertReq(v domain.TaobaoLiveVirtualanchorDocumentUploadDocumentInfoInsertReq) *TaobaoLiveVirtualanchorDocumentUploadRequest {
	s.DocumentInfoInsertReq = &v
	return s
}

func (req *TaobaoLiveVirtualanchorDocumentUploadRequest) ToMap() map[string]interface{} {
	paramMap := make(map[string]interface{})
	if req.DocumentInfoInsertReq != nil {
		paramMap["document_info_insert_req"] = util.ConvertStruct(*req.DocumentInfoInsertReq)
	}
	return paramMap
}

func (req *TaobaoLiveVirtualanchorDocumentUploadRequest) ToFileMap() map[string]interface{} {
	fileMap := make(map[string]interface{})
	return fileMap
}
