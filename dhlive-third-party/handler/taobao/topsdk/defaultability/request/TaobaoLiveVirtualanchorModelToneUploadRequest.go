package request

type TaobaoLiveVirtualanchorModelToneUploadRequest struct {
	/*
	   音色的唯一标识     */
	AudioId *string `json:"audio_id" required:"true" `
	/*
	   需要什么级别的订购版本才可以使用该音色信息     */
	PluginVersion *string `json:"plugin_version" required:"true" `
	/*
	   音色样例音频地址     */
	SampleUrl *string `json:"sample_url" required:"true" `
	/*
	   音色介绍简介     */
	Intro *string `json:"intro,omitempty" required:"false" `
	/*
	   音色模型名称     */
	Name *string `json:"name" required:"true" `
	/*
	   insert(默认)/update     */
	Action *string `json:"action,omitempty" required:"false" `
}

func (s *TaobaoLiveVirtualanchorModelToneUploadRequest) SetAudioId(v string) *TaobaoLiveVirtualanchorModelToneUploadRequest {
	s.AudioId = &v
	return s
}
func (s *TaobaoLiveVirtualanchorModelToneUploadRequest) SetPluginVersion(v string) *TaobaoLiveVirtualanchorModelToneUploadRequest {
	s.PluginVersion = &v
	return s
}
func (s *TaobaoLiveVirtualanchorModelToneUploadRequest) SetSampleUrl(v string) *TaobaoLiveVirtualanchorModelToneUploadRequest {
	s.SampleUrl = &v
	return s
}
func (s *TaobaoLiveVirtualanchorModelToneUploadRequest) SetIntro(v string) *TaobaoLiveVirtualanchorModelToneUploadRequest {
	s.Intro = &v
	return s
}
func (s *TaobaoLiveVirtualanchorModelToneUploadRequest) SetName(v string) *TaobaoLiveVirtualanchorModelToneUploadRequest {
	s.Name = &v
	return s
}
func (s *TaobaoLiveVirtualanchorModelToneUploadRequest) SetAction(v string) *TaobaoLiveVirtualanchorModelToneUploadRequest {
	s.Action = &v
	return s
}

func (req *TaobaoLiveVirtualanchorModelToneUploadRequest) ToMap() map[string]interface{} {
	paramMap := make(map[string]interface{})
	if req.AudioId != nil {
		paramMap["audio_id"] = *req.AudioId
	}
	if req.PluginVersion != nil {
		paramMap["plugin_version"] = *req.PluginVersion
	}
	if req.SampleUrl != nil {
		paramMap["sample_url"] = *req.SampleUrl
	}
	if req.Intro != nil {
		paramMap["intro"] = *req.Intro
	}
	if req.Name != nil {
		paramMap["name"] = *req.Name
	}
	if req.Action != nil {
		paramMap["action"] = *req.Action
	}
	return paramMap
}

func (req *TaobaoLiveVirtualanchorModelToneUploadRequest) ToFileMap() map[string]interface{} {
	fileMap := make(map[string]interface{})
	return fileMap
}
