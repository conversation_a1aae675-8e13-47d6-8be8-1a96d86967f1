package handler

import (
	commProto "acg-ai-go-common/beans/proto"
	"acg-ai-go-common/global"
	"acg-ai-go-common/logger"
	"acg-ai-go-common/utils"
	"crypto/tls"
	"dhlive-third-party/beans/enum"
	"dhlive-third-party/beans/model"
	"dhlive-third-party/beans/proto"
	"dhlive-third-party/beans/util"
	config "dhlive-third-party/conf"
	"dhlive-third-party/handler/meituan"
	"dhlive-third-party/handler/taobao"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/gin-gonic/gin"
	"io/ioutil"
	"net/http"
	"net/url"
	"regexp"
	"time"
)

const (
	JDOauthLoginURLFmt = "https://open-oauth.jd.com/oauth2/to_login?" +
		"app_key=%v&response_type=code&redirect_uri=%v&state=%v&scope=snsapi_base" // 京东授权登录地址fmt
	JDOauthTokenURLFmt = "https://open-oauth.jd.com/oauth2/access_token?" +
		"app_key=%v&app_secret=%v&grant_type=authorization_code&code=%v" // 京东获取Token地址fmt
	JDOauthTokenRefreshURLFmt = "https://open-oauth.jd.com/oauth2/refresh_token?" +
		"app_key=%v&app_secret=%v&grant_type=refresh_token&refresh_token=%v" // 京东刷新Token时长地址fmt
	JDOpenAPICommURLFmt         = "https://api.jd.com/routerjson?%v"
	JDOpenAPIVersion            = "2.0" // 京东OpenAPI版本号
	JDOpenAPICommParamsFmt      = "360buy_param_json=%v&access_token=%v&app_key=%v&method=%v&sign=%v&timestamp=%v&v=" + JDOpenAPIVersion
	JDCommParamCalcSignStrFmt   = "360buy_param_json%vaccess_token%vapp_key%vmethod%vtimestamp%vv" + JDOpenAPIVersion // 京东OpenAPI计算签名参数fmt
	JDGetUserInfoMethod         = "jingdong.user.getUserInfoByOpenId"                                                 // 京东获取当前用户信息方法
	JDGetVenderInfoMethod       = "jingdong.seller.vender.info.get"                                                   // 京东获取商家信息方法
	JDSearchLiveMethod          = "jingdong.live.liveAppService.livesList.search"                                     // 京东查询其下直播间方法
	JDLiveAndSkuDetailMethod    = "jingdong.live.liveAndSku.detail.get"                                               // 京东获取直播间及其下商品列表方法
	JDSkuSearchDetailMethod     = "jingdong.sku.read.searchSkuList"                                                   // 京东搜索商品列表方法
	JDLiveSkuPushMethod         = "jingdong.live.isvOperation.sku.push"                                               // 京东弹出直播间卡片方法
	JDTextRedLineDetectMethod   = "jingdong.detection.textRedLineDetect"                                              // 京东红盾文本检测
	JDImagesRedLineDetectMethod = "jingdong.detection.imagesRedLineDetectBatch"                                       // 图片红线违规批量检测 一次至多8张
)

// 自定义请求头常量
const (
	ContextJDOauthAppKey = "JDOauthApp"  // JDOauthApp 上下文key
	ContextTBOauthAppKey = "TBOauthApp"  // TBOauthApp 上下文key
	HeaderRequestPlat    = "requestPlat" // 请求平台 慧博星、曦灵
	SrcPlatHuiBoXing     = "HUI_BO_XING" // requestPlat 值 慧博星
	SrcPlatXiLing        = "XI_LING"     // requestPlat 值 曦灵
	SrcPlatBaiShu        = "BAI_SHU"     // requestPlat 值 百数
)

// ThirdPartyCheckHeaderParam 对请求header中的requestPlat参数进行校验
func ThirdPartyCheckHeaderParam(c *gin.Context) {
	requestPlat := c.GetHeader(HeaderRequestPlat)
	logger.CtxLog(c).Infof("ThirdPartyCheckHeaderParam requestPlat:%v", requestPlat)
	if requestPlat == SrcPlatHuiBoXing {
		// todo 如果是慧博星请求，则调用慧博星的接口进行校验
	}
	jdOauthApp := choiceJDOauthApp(requestPlat)
	c.Set(ContextJDOauthAppKey, jdOauthApp)
	tbOauthApp := choiceTBOauthApp(requestPlat)
	c.Set(ContextTBOauthAppKey, tbOauthApp)
	logger.CtxLog(c).Infof("ThirdPartyCheckHeaderParam jdOauthApp:%v, tbOauthApp:%v", jdOauthApp, tbOauthApp)
	c.Next()
}

// ThirdPartyOauth 第三方Oauth2授权
func ThirdPartyOauth(c *gin.Context) {
	requestId := c.GetHeader(global.HeaderRequestID)
	req := &proto.OauthReq{}
	if err := c.ShouldBind(&req); err != nil {
		logger.CtxLog(c).Errorf("ThirdPartyOauth bind param fail, requestId:%v, err:%v",
			requestId, err)
		c.JSON(http.StatusOK, commProto.NewCommRsp(100001, "参数异常"))
		return
	}
	logger.CtxLog(c).Infof("ThirdPartyOauth bind param succ, requestId:%v, param:%v", requestId, req)
	if _, err := url.Parse(req.RedirectUri); err != nil {
		c.JSON(http.StatusOK, commProto.NewCommRsp(100002, "参数 redirectUri 异常"))
		return
	}
	var loginURL string
	// 记录授权请求, 返回ID
	oauthReq := model.OauthReq{
		RequestID:   requestId,
		UserID:      req.UserID,
		Platform:    req.Platform,
		RedirectURI: req.RedirectUri,
		State:       config.LocalConfig.OauthCommSetting.StatePre + utils.RandStringRunes(6),
	}
	defer func() {
		if err := oauthReq.Insert(); err != nil {
			logger.CtxLog(c).Errorf("ThirdPartyOauth Insert OauthReq fail, requestId:%v, err:%v", requestId, err)
			c.JSON(http.StatusOK, commProto.NewCommRsp(500001, "服务繁忙"))
			return
		}
		if len(loginURL) > 0 {
			c.JSON(http.StatusOK, commProto.NewSuccessRsp(&proto.OauthRsp{
				LoginURL: loginURL,
			}))
		}
	}()

	switch req.Platform {
	case enum.JD:
		// 查询授权APP的AK/SK
		oauthAppInterface, exists := c.Get(ContextJDOauthAppKey)
		if !exists || oauthAppInterface == nil {
			c.JSON(http.StatusOK, commProto.NewCommRsp(460066, "invalid Source Header"))
			return
		}
		oauthApp := oauthAppInterface.(*config.JDOauthAppInfo)
		appKey := oauthApp.AppKey
		secretKey := oauthApp.SecretKey
		callback := oauthApp.CallbackURL
		oauthReq.AppKey = appKey
		oauthReq.SecretKey = secretKey
		loginURL = fmt.Sprintf(JDOauthLoginURLFmt, appKey, callback, oauthReq.State)
	case enum.MT:
		var developerId = config.LocalConfig.MTSetting.DeveloperId
		var signKey = config.LocalConfig.MTSetting.SignKey
		var businessId = config.LocalConfig.MTSetting.BusinessId
		oauthReq.AppKey = fmt.Sprintf("%v", developerId)
		oauthReq.SecretKey = signKey
		var err error
		loginURL, err = meituan.BuildOauthUrl(signKey, businessId, developerId, oauthReq.State, "")
		if err != nil {
			logger.CtxLog(c).Errorf("ThirdPartyOauth mtclient BuildOauth2Url fail, err:%v", err)
			c.JSON(http.StatusOK, commProto.NewCommRsp(500002, "服务繁忙"))
			return
		}
	case enum.TB:
		// 查询授权APP的AK/SK
		oauthAppInterface, exists := c.Get(ContextTBOauthAppKey)
		if !exists || oauthAppInterface == nil {
			c.JSON(http.StatusOK, commProto.NewCommRsp(460066, "invalid Source Header"))
			return
		}
		oauthApp := oauthAppInterface.(*config.TBOauthAppInfo)
		appKey := oauthApp.AppKey
		secretKey := oauthApp.SecretKey
		callbackURL := oauthApp.CallbackURL
		oauthReq.AppKey = appKey
		oauthReq.SecretKey = secretKey
		loginURL = fmt.Sprintf(config.LocalConfig.OauthCommSetting.TaoBaoBaseURL+taobao.AuthorizeURIFmt,
			appKey, callbackURL, oauthReq.State)
	default:
		c.JSON(http.StatusOK, commProto.NewCommRsp(100003,
			fmt.Sprintf("暂不支持[%v]平台授权", req.Platform)))
		return
	}
}

// ThirdPartyOauthCallback 第三方Oauth2授权回调
func ThirdPartyOauthCallback(c *gin.Context) {
	nowTime := time.Now()
	requestId := c.GetHeader(global.HeaderRequestID)
	req := &proto.OauthCallbackReq{}
	if err := c.ShouldBindQuery(&req); err != nil {
		logger.CtxLog(c).Errorf("ThirdPartyOauthCallback bind param fail, requestId:%v, err:%v",
			requestId, err)
		c.JSON(http.StatusOK, commProto.NewCommRsp(100001, "参数异常"))
		return
	}
	// 根据req.State查询授权请求记录
	oauthReqRecord := model.OauthReq{}
	if err := oauthReqRecord.FindByState(req.State); err != nil {
		logger.CtxLog(c).Errorf("ThirdPartyOauthCallback OauthReq FindByState fail, requestId:%v, err:%v",
			requestId, err)
		c.JSON(http.StatusOK, commProto.NewCommRsp(500001, "服务繁忙"))
		return
	} else if oauthReqRecord.ID <= 0 {
		// 判断state规则，跳转对应地址
		platform, redirectURL, err := tryGetOauthCallbackFixedRedirect(req.State)
		if err != nil {
			logger.CtxLog(c).Errorf("ThirdPartyOauthCallback tryGetOauthCallbackFixedRedirect fail, err:%v", err)
			c.JSON(http.StatusOK, commProto.NewCommRsp(500001, "服务繁忙"))
			return
		} else if len(redirectURL) > 0 {
			logger.CtxLog(c).Infof("ThirdPartyOauthCallback tryGetOauthCallbackFixedRedirect success, platform:%v, redirectURL:%v",
				platform, redirectURL)
			c.Redirect(http.StatusMovedPermanently, redirectURL)
			return
		}
		c.JSON(http.StatusOK, commProto.NewCommRsp(430001, "回调授权码异常"))
		return
	}
	result := map[string]string{
		"code":   "0",
		"msg":    "success",
		"openId": "",
		"pUid":   "",
	}
	defer func() {
		// 301 Moved Permanently(永久移动) 302 Found(临时移除)
		finalUrl, err := util.URLAddQueryParam(oauthReqRecord.RedirectURI, result)
		if err != nil {
			c.Redirect(http.StatusMovedPermanently, oauthReqRecord.RedirectURI+"code=555555")
		} else {
			c.Redirect(http.StatusMovedPermanently, finalUrl)
		}
	}()
	var token *model.OauthToken
	switch oauthReqRecord.Platform {
	case enum.JD:
		// 请求京东OpenAPI获取Token
		oauthTokenUrl := fmt.Sprintf(JDOauthTokenURLFmt, oauthReqRecord.AppKey, oauthReqRecord.SecretKey, req.Code)
		jdTokenRsp := &proto.JDTokenRsp{}
		if err := httpGet(oauthTokenUrl, &jdTokenRsp); err != nil {
			logger.CtxLog(c).Errorf("ThirdPartyOauthCallback call uri[%v] fail, err:%v", oauthTokenUrl, err)
			result["code"] = "500001"
			result["msg"] = "服务繁忙"
			return
		} else if jdTokenRsp.Code != 0 {
			logger.CtxLog(c).Errorf("ThirdPartyOauthCallback call uri[%v] fail, cost:%v, requestId:%v, rsp:%v",
				oauthTokenUrl, time.Since(nowTime), requestId, jdTokenRsp)
			if jdTokenRsp.Code == 405 {
				result["code"] = "430003"
				result["msg"] = jdTokenRsp.Msg
				return
			}
			result["code"] = "500002"
			result["msg"] = "服务繁忙"
			return
		}
		logger.CtxLog(c).Infof("ThirdPartyOauthCallback call uri[%v] succ, cost:%v, requestId:%v, rsp:%v",
			oauthTokenUrl, time.Since(nowTime), requestId, jdTokenRsp)
		// 删除旧数据信息
		oldToken := model.OauthToken{}
		if err := oldToken.DeleteByOpenID(jdTokenRsp.OpenId, oauthReqRecord.Platform); err != nil {
			logger.CtxLog(c).Errorf("ThirdPartyOauthCallback DeleteByOpenID OauthToken fail, requestId:%v, err:%v",
				requestId, err)
			result["code"] = "500003"
			result["msg"] = "服务繁忙"
			return
		}
		// 获取Token成功, 存储Token信息
		token = &model.OauthToken{
			UserID:       oauthReqRecord.UserID,
			Platform:     oauthReqRecord.Platform,
			AppKey:       oauthReqRecord.AppKey,
			PuID:         jdTokenRsp.Uid,
			OpenID:       jdTokenRsp.OpenId,
			AccessToken:  jdTokenRsp.AccessToken,
			ExpiresIn:    jdTokenRsp.ExpiresIn,
			TakeEffect:   jdTokenRsp.Time,
			RefreshToken: jdTokenRsp.RefreshToken,
			ColType:      -1,
		}
	case enum.MT:
		var businessId = config.LocalConfig.MTSetting.BusinessId
		mtc := meituan.NewDefaultClient()
		mtTokenRsp, err := mtc.GetToken(businessId, req.Code)
		if err != nil {
			logger.CtxLog(c).Errorf("ThirdPartyOauthCallback mtclient GetToken fail, err:%v", err)
			result["code"] = "500002"
			result["msg"] = "服务繁忙"
			return
		} else if !mtTokenRsp.IsSuccess() {
			result["code"] = fmt.Sprintf("%v", mtTokenRsp.Code)
			result["msg"] = mtTokenRsp.Msg
			return
		}
		// 删除旧数据信息
		oldToken := model.OauthToken{}
		if err := oldToken.DeleteByOpenID(mtTokenRsp.Data.OpBizCode, oauthReqRecord.Platform); err != nil {
			logger.CtxLog(c).Errorf("ThirdPartyOauthCallback DeleteByOpenID OauthToken fail, requestId:%v, err:%v",
				requestId, err)
			result["code"] = "500003"
			result["msg"] = "服务繁忙"
			return
		}
		// 获取Token成功, 存储Token信息
		token = &model.OauthToken{
			UserID:       oauthReqRecord.UserID,
			Platform:     oauthReqRecord.Platform,
			AppKey:       fmt.Sprintf("%v", config.LocalConfig.MTSetting.DeveloperId),
			PuID:         "",
			OpenID:       mtTokenRsp.Data.OpBizCode,
			AccessToken:  mtTokenRsp.Data.AccessToken,
			ExpiresIn:    mtTokenRsp.Data.ExpireIn,
			TakeEffect:   nowTime.UnixMilli(),
			RefreshToken: mtTokenRsp.Data.RefreshToken,
			ColType:      -1,
		}
	case enum.TB:
		if req.Error == "invalid_client" {
			result["code"] = "430003"
			result["msg"] = req.ErrorDescription
			return
		} else if len(req.Error) > 0 {
			result["code"] = "430034"
			result["msg"] = fmt.Sprintf("%v,%v", req.Error, req.ErrorDescription)
			return
		}
		// 获取token并存储
		tokenInfo, err := taobao.GetToken(oauthReqRecord.AppKey, oauthReqRecord.SecretKey, req.Code)
		if err != nil {
			logger.CtxLog(c).Errorf("ThirdPartyOauthCallback taobao.GetToken fail, err:%v", err)
			result["code"] = "500001"
			result["msg"] = err.Error()
			return
		}
		// 删除旧数据信息
		oldToken := model.OauthToken{}
		if err := oldToken.DeleteByOpenID(tokenInfo.TaobaoOpenUid, oauthReqRecord.Platform); err != nil {
			logger.CtxLog(c).Errorf("ThirdPartyOauthCallback DeleteByOpenID OauthToken fail, requestId:%v, err:%v",
				requestId, err)
			result["code"] = "500003"
			result["msg"] = "服务繁忙"
			return
		}
		// 获取Token成功, 存储Token信息
		token = &model.OauthToken{
			UserID:       oauthReqRecord.UserID,
			Platform:     oauthReqRecord.Platform,
			AppKey:       oauthReqRecord.AppKey,
			PuID:         tokenInfo.TaobaoUserId,
			OpenID:       tokenInfo.TaobaoOpenUid,
			NickName:     tokenInfo.TaobaoUserNick,
			AccessToken:  tokenInfo.AccessToken,
			ExpiresIn:    tokenInfo.ExpiresIn,
			TakeEffect:   nowTime.UnixMilli(),
			RefreshToken: tokenInfo.RefreshToken,
			ColType:      -1,
		}
	default:
		result["code"] = "100002"
		result["msg"] = fmt.Sprintf("暂不支持%v平台授权", oauthReqRecord.Platform)
		return
	}
	if token != nil {
		if err := token.Insert(); err != nil {
			logger.CtxLog(c).Errorf("ThirdPartyOauthCallback Insert OauthToken fail, err:%v", err)
			result["code"] = "500004"
			result["msg"] = "服务繁忙"
			return
		}
		result["openId"] = token.OpenID
		result["pUid"] = token.PuID
	}
}

func tryGetOauthCallbackFixedRedirect(state string) (enum.Platform, string, error) {
	// 获取所有正则
	var ocf *model.OauthCallbackConf
	ocfs, err := ocf.FindAll()
	if err != nil {
		return "", "", err
	}
	for _, o := range ocfs {
		if ok, _ := regexp.MatchString(o.Regex, state); ok {
			return o.Platform, o.RedirectURI, nil
		}
	}
	return "", "", nil
}

// GetThirdPartyUserInfo 校验第三方Oauth2授权并获取第三方平台用户信息
func GetThirdPartyUserInfo(c *gin.Context) {
	nowTime := time.Now()
	requestId := c.GetHeader(global.HeaderRequestID)
	req := &proto.ThirdPartyOauthCheckReq{}
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.CtxLog(c).Errorf("GetThirdPartyUserInfo bind param fail, requestId:%v, err:%v",
			requestId, err)
		c.JSON(http.StatusOK, commProto.NewCommRsp(100001, "参数异常"))
		return
	}
	oauthToken, userInfo := thirdPartyOauthCheckGetUserInfo(c, req.UserID, req.Platform, req.OpenID)
	if oauthToken == nil || userInfo == nil {
		logger.CtxLog(c).Errorf("GetThirdPartyUserInfo thirdPartyOauthCheck fail, requestId:%v, cost:%v",
			requestId, time.Since(nowTime))
		return
	}
	c.JSON(http.StatusOK, commProto.NewSuccessRsp(&proto.ThirdPartyOauthCheckRsp{
		Platform:    req.Platform,
		OpenID:      oauthToken.OpenID,
		PUid:        oauthToken.PuID,
		NickName:    userInfo.NickName,
		ImageURL:    userInfo.ImageURL,
		Gender:      userInfo.Gender,
		AccountType: userInfo.ColType,
	}))
}

// GetThirdPartyUserInfoBatch 批量校验第三方Oauth2授权并获取第三方平台用户信息
func GetThirdPartyUserInfoBatch(c *gin.Context) {
	requestId := c.GetHeader(global.HeaderRequestID)
	req := &proto.ThirdPartyOauthBatchCheckReq{}
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.CtxLog(c).Errorf("GetThirdPartyUserInfo bind param fail, requestId:%v, err:%v",
			requestId, err)
		c.JSON(http.StatusOK, commProto.NewCommRsp(100001, "参数异常"))
		return
	}
	var result []*proto.ThirdPartyOauthBatchCheckData
	for _, l := range req.List {
		login, userInfo := getThirdPartyOauthStatusAndUserInfo(c, req.UserID, l.Platform, l.OpenID)
		if login {
			result = append(result, &proto.ThirdPartyOauthBatchCheckData{
				Platform:    userInfo.Platform,
				OpenID:      userInfo.OpenID,
				PUid:        userInfo.Puid,
				Login:       login,
				NickName:    userInfo.NickName,
				ImageURL:    userInfo.ImageURL,
				Gender:      userInfo.Gender,
				AccountType: userInfo.ColType,
			})
		} else {
			result = append(result, &proto.ThirdPartyOauthBatchCheckData{
				Platform: l.Platform,
				OpenID:   l.OpenID,
				Login:    false,
			})
		}
	}
	c.JSON(http.StatusOK, commProto.NewSuccessRsp(result))
}

// thirdPartyOauthCheck 校验第三方Oauth2授权并将第三方平台用户信息放入context的${platform_UserInfo}中
func thirdPartyOauthCheckGetUserInfo(c *gin.Context,
	userId string, platform enum.Platform, openId string) (*model.OauthToken, *ThirdPartyUserInfo) {
	nowTime := time.Now()
	requestId := c.GetHeader(global.HeaderRequestID)
	switch platform {
	case enum.JD:
		// 查询授权APP的AK/SK
		oauthAppInterface, exists := c.Get(ContextJDOauthAppKey)
		if !exists || oauthAppInterface == nil {
			c.AbortWithStatusJSON(http.StatusOK, commProto.NewCommRsp(460066, "invalid Source Header"))
			return nil, nil
		}
		oauthApp := oauthAppInterface.(*config.JDOauthAppInfo)
		appKey := oauthApp.AppKey
		secretKey := oauthApp.SecretKey
		// 查询最新Token
		oauthToken := &model.OauthToken{}
		if err := oauthToken.FindByUserIDAndAppKey(userId, platform, openId, appKey); err != nil {
			logger.CtxLog(c).Errorf("thirdPartyOauthCheck OauthToken FindByUserIDAndAppKey fail, requestId:%v, err:%v",
				requestId, err)
			c.AbortWithStatusJSON(http.StatusOK, commProto.NewCommRsp(500001, "服务繁忙"))
			return nil, nil
		} else if oauthToken.ID <= 0 ||
			len(oauthToken.AccessToken) == 0 {
			c.AbortWithStatusJSON(http.StatusOK, commProto.NewCommRsp(400001, "未授权"))
			return nil, nil
		} else if int64(oauthToken.ExpiresIn)+oauthToken.TakeEffect/1000 <= nowTime.Unix() {
			if err := refreshJDToken(oauthToken, appKey, secretKey, requestId); err != nil {
				logger.CtxLog(c).Errorf("thirdPartyOauthCheck refreshJDToken fail, requestId:%v, err:%v",
					requestId, err)
				c.AbortWithStatusJSON(http.StatusOK, commProto.NewCommRsp(400001, "未授权"))
				return nil, nil
			}
		}
		venderInfo, jdErrRsp, err := getJDVenderInfo(oauthToken, appKey, secretKey, requestId)
		if err != nil {
			logger.CtxLog(c).Errorf("thirdPartyOauthCheck refreshJDToken fail, requestId:%v, err:%v",
				requestId, err)
			c.AbortWithStatusJSON(http.StatusOK, commProto.NewCommRsp(510001, "服务繁忙"))
			return nil, nil
		} else if jdErrRsp != nil {
			c.AbortWithStatusJSON(http.StatusOK, commProto.NewCommDataRsp(510002, "服务繁忙", jdErrRsp))
			return nil, nil
		} else if venderInfo.VenderId > 0 {
			if oauthToken.VenderID != fmt.Sprintf("%v", venderInfo.VenderId) ||
				oauthToken.ColType != venderInfo.ColType ||
				oauthToken.ShopID != fmt.Sprintf("%v", venderInfo.ShopId) ||
				oauthToken.ShopName != venderInfo.ShopName {
				oauthToken.VenderID = fmt.Sprintf("%v", venderInfo.VenderId)
				oauthToken.ColType = venderInfo.ColType
				oauthToken.ShopID = fmt.Sprintf("%v", venderInfo.ShopId)
				oauthToken.ShopName = venderInfo.ShopName
				if err = oauthToken.Update(); err != nil {
					logger.CtxLog(c).Errorf("thirdPartyOauthCheck Update OauthToken Vender fail, requestId:%v, err:%v",
						requestId, err)
				}
			}
		}
		if venderInfo.VenderId == 0 && oauthToken.ColType != -1 {
			oauthToken.ColType = -1
			if err = oauthToken.Update(); err != nil {
				logger.CtxLog(c).Errorf("thirdPartyOauthCheck Update OauthToken colType fail, requestId:%v, err:%v",
					requestId, err)
			}
		}

		// 根据最新Token获取第三方平台用户信息
		params := GenJDOpenApiParamsStr(map[string]string{
			"openId": oauthToken.OpenID,
		}, appKey, secretKey, oauthToken.AccessToken, JDGetUserInfoMethod)
		jdUserUrl := fmt.Sprintf(JDOpenAPICommURLFmt, params)
		jdUserRsp := &proto.JDUserRsp{}
		if err := httpGet(jdUserUrl, &jdUserRsp); err != nil {
			logger.CtxLog(c).Errorf("thirdPartyOauthCheck call uri[%v] fail, cost:%v, requestId:%v, err:%v",
				jdUserUrl, time.Since(nowTime), requestId, err)
			c.AbortWithStatusJSON(http.StatusOK,
				commProto.NewCommRsp(500002, "call JD openAPI failed"))
			return nil, nil
		} else if jdUserRsp.ErrorResponse != nil ||
			jdUserRsp.JDUserGetUserInfoByOpenIdResponse.GetuserinfobyappidandopenidResult.Code != 0 {
			logger.CtxLog(c).Errorf("thirdPartyOauthCheck call uri[%v] fail, cost:%v, requestId:%v, rsp:%v",
				jdUserUrl, time.Since(nowTime), requestId, jdUserRsp)
			c.AbortWithStatusJSON(http.StatusOK,
				commProto.NewCommDataRsp(400002, "call JD openAPI failed", jdUserRsp))
			return nil, nil
		}
		logger.CtxLog(c).Infof("thirdPartyOauthCheck call uri[%v] succ, cost:%v, requestId:%v, rsp:%v",
			jdUserUrl, time.Since(nowTime), requestId, jdUserRsp)
		jdUserInfo := jdUserRsp.JDUserGetUserInfoByOpenIdResponse.GetuserinfobyappidandopenidResult.Data
		return oauthToken, &ThirdPartyUserInfo{
			Platform: platform,
			VenderID: oauthToken.VenderID,
			ColType:  oauthToken.ColType,
			ShopID:   oauthToken.ShopID,
			ShopName: oauthToken.ShopName,
			OpenID:   oauthToken.OpenID,
			Puid:     oauthToken.PuID,
			NickName: jdUserInfo.NickName,
			ImageURL: jdUserInfo.ImageUrl,
			Gender:   jdUserInfo.Gendar,
		}
	case enum.MT:
		var developerId = config.LocalConfig.MTSetting.DeveloperId
		// 查询最新Token
		oauthToken := &model.OauthToken{}
		if err := oauthToken.FindByUserIDAndAppKey(userId, platform, openId,
			fmt.Sprintf("%v", developerId)); err != nil {
			logger.CtxLog(c).Errorf("thirdPartyOauthCheck OauthToken FindByUserIDAndAppKey fail, err:%v", err)
			c.AbortWithStatusJSON(http.StatusOK, commProto.NewCommRsp(500001, "服务繁忙"))
			return nil, nil
		} else if oauthToken.ID <= 0 ||
			len(oauthToken.AccessToken) == 0 {
			c.AbortWithStatusJSON(http.StatusOK, commProto.NewCommRsp(400001, "未授权"))
			return nil, nil
		} else if int64(oauthToken.ExpiresIn)+oauthToken.TakeEffect/1000 <= nowTime.Unix() {
			if err := refreshMTToken(oauthToken, requestId); err != nil {
				logger.CtxLog(c).Errorf("thirdPartyOauthCheck refreshMtToken fail, err:%v", err)
				c.AbortWithStatusJSON(http.StatusOK, commProto.NewCommRsp(400001, "未授权"))
				return nil, nil
			}
		}
		return oauthToken, &ThirdPartyUserInfo{
			Platform: platform,
			VenderID: oauthToken.VenderID,
			ColType:  oauthToken.ColType,
			ShopID:   oauthToken.ShopID,
			ShopName: oauthToken.ShopName,
			OpenID:   oauthToken.OpenID,
			Puid:     oauthToken.PuID, // 美团的openId和puid一致
			// TODO 当前美图用户信息没有API可获取
			NickName: oauthToken.NickName,
			ImageURL: oauthToken.HeadImgURL,
			Gender:   -1,
		}
	case enum.TB:
		// 查询授权APP的AK/SK
		oauthAppInterface, exists := c.Get(ContextTBOauthAppKey)
		if !exists || oauthAppInterface == nil {
			c.AbortWithStatusJSON(http.StatusOK, commProto.NewCommRsp(460066, "invalid Source Header"))
			return nil, nil
		}
		oauthApp := oauthAppInterface.(*config.TBOauthAppInfo)
		appKey := oauthApp.AppKey
		secretKey := oauthApp.SecretKey
		// 查询最新Token
		oauthToken := &model.OauthToken{}
		if err := oauthToken.FindByUserIDAndAppKey(userId, platform, openId, appKey); err != nil {
			logger.CtxLog(c).Errorf("thirdPartyOauthCheck OauthToken FindByUserIDAndAppKey fail, requestId:%v, err:%v",
				requestId, err)
			c.AbortWithStatusJSON(http.StatusOK, commProto.NewCommRsp(500001, "服务繁忙"))
			return nil, nil
		} else if oauthToken.ID <= 0 ||
			len(oauthToken.AccessToken) == 0 {
			c.AbortWithStatusJSON(http.StatusOK, commProto.NewCommRsp(400001, "未授权"))
			return nil, nil
		} else if int64(oauthToken.ExpiresIn)+oauthToken.TakeEffect/1000 <= nowTime.Unix() {
			if err := taobao.RefreshToken(oauthToken, secretKey); err != nil {
				logger.CtxLog(c).Errorf("thirdPartyOauthCheck refreshJDToken fail, requestId:%v, err:%v",
					requestId, err)
				c.AbortWithStatusJSON(http.StatusOK, commProto.NewCommRsp(400001, "未授权"))
				return nil, nil
			}
		}
		return oauthToken, &ThirdPartyUserInfo{
			Platform: platform,
			VenderID: oauthToken.VenderID,
			ColType:  oauthToken.ColType,
			ShopID:   oauthToken.ShopID,
			ShopName: oauthToken.ShopName,
			OpenID:   oauthToken.OpenID,
			Puid:     oauthToken.PuID,
			NickName: oauthToken.NickName,
			ImageURL: oauthToken.HeadImgURL,
			Gender:   -1,
		}
	default:
		c.AbortWithStatusJSON(http.StatusOK,
			commProto.NewCommRsp(100002, fmt.Sprintf("暂不支持%v平台", platform)))
		return nil, nil
	}
}

func thirdPartyOauthCheck(c *gin.Context,
	userId string, platform enum.Platform, openId string) (*model.OauthToken, bool) {
	nowTime := time.Now()
	requestId := c.GetHeader(global.HeaderRequestID)
	var appKey string
	var secretKey string
	switch platform {
	case enum.JD:
		// 查询授权APP的AK/SK
		oauthAppInterface, exists := c.Get(ContextJDOauthAppKey)
		if !exists || oauthAppInterface == nil {
			c.AbortWithStatusJSON(http.StatusOK, commProto.NewCommRsp(460066, "invalid Source Header"))
			return nil, false
		}
		oauthApp := oauthAppInterface.(*config.JDOauthAppInfo)
		appKey = oauthApp.AppKey
		secretKey = oauthApp.SecretKey
	case enum.MT:
		appKey = fmt.Sprintf("%v", config.LocalConfig.MTSetting.DeveloperId)
	case enum.TB:
		// 查询授权APP的AK/SK
		oauthAppInterface, exists := c.Get(ContextTBOauthAppKey)
		if !exists || oauthAppInterface == nil {
			c.AbortWithStatusJSON(http.StatusOK, commProto.NewCommRsp(460066, "invalid Source Header"))
			return nil, false
		}
		oauthApp := oauthAppInterface.(*config.TBOauthAppInfo)
		appKey = oauthApp.AppKey
		secretKey = oauthApp.SecretKey
	default:
		c.AbortWithStatusJSON(http.StatusOK,
			commProto.NewCommRsp(100002, fmt.Sprintf("暂不支持%v平台", platform)))
		return nil, false
	}
	// 查询最新Token
	oauthToken := &model.OauthToken{}
	if err := oauthToken.FindByUserIDAndAppKey(userId, platform, openId, appKey); err != nil {
		logger.CtxLog(c).Errorf("thirdPartyOauthCheck OauthToken FindByUserIDAndAppKey fail, requestId:%v, err:%v",
			requestId, err)
		c.AbortWithStatusJSON(http.StatusOK, commProto.NewCommRsp(500001, "服务繁忙"))
		return nil, false
	} else if oauthToken.ID <= 0 ||
		len(oauthToken.AccessToken) == 0 {
		c.AbortWithStatusJSON(http.StatusOK, commProto.NewCommRsp(400001, "未授权"))
		return nil, false
	} else if int64(oauthToken.ExpiresIn)+oauthToken.TakeEffect/1000 <= nowTime.Unix() {
		switch platform {
		case enum.JD:
			if err := refreshJDToken(oauthToken, appKey, secretKey, requestId); err != nil {
				logger.CtxLog(c).Errorf("thirdPartyOauthCheck refreshJDToken fail, requestId:%v, err:%v",
					requestId, err)
				c.AbortWithStatusJSON(http.StatusOK, commProto.NewCommRsp(400001, "未授权"))
				return nil, false
			}
		case enum.MT:
			if err := refreshMTToken(oauthToken, requestId); err != nil {
				logger.CtxLog(c).Errorf("thirdPartyOauthCheck refreshMTToken fail, requestId:%v, err:%v",
					requestId, err)
				c.AbortWithStatusJSON(http.StatusOK, commProto.NewCommRsp(400001, "未授权"))
				return nil, false
			}
		case enum.TB:
			if err := taobao.RefreshToken(oauthToken, secretKey); err != nil {
				logger.CtxLog(c).Errorf("thirdPartyOauthCheck taobao RefreshToken fail, requestId:%v, err:%v",
					requestId, err)
				c.AbortWithStatusJSON(http.StatusOK, commProto.NewCommRsp(400001, "未授权"))
				return nil, false
			}
		}
	}
	return oauthToken, true
}

// getThirdPartyOauthStatusAndUserInfo 获取第三方平台用户信息和授权状态
func getThirdPartyOauthStatusAndUserInfo(c *gin.Context,
	userId string, platform enum.Platform, openId string) (bool, *ThirdPartyUserInfo) {
	nowTime := time.Now()
	requestId := c.GetHeader(global.HeaderRequestID)
	switch platform {
	case enum.JD:
		// 查询授权APP的AK/SK
		oauthAppInterface, exists := c.Get(ContextJDOauthAppKey)
		if !exists || oauthAppInterface == nil {
			//c.AbortWithStatusJSON(http.StatusOK, commProto.NewCommRsp(460066, "invalid Source Header"))
			return false, nil
		}
		oauthApp := oauthAppInterface.(*config.JDOauthAppInfo)
		appKey := oauthApp.AppKey
		secretKey := oauthApp.SecretKey
		// 查询最新Token
		oauthToken := &model.OauthToken{}
		if err := oauthToken.FindByUserIDAndAppKey(userId, platform, openId, appKey); err != nil {
			logger.CtxLog(c).Errorf("getThirdPartyOauthStatusAndUserInfo FindByUserIDAndAppKey fail, requestId:%v, err:%v",
				requestId, err)
			return false, nil
		} else if oauthToken.ID <= 0 ||
			len(oauthToken.AccessToken) == 0 {
			return false, nil
		} else if int64(oauthToken.ExpiresIn)+oauthToken.TakeEffect/1000 <= nowTime.Unix() {
			if err := refreshJDToken(oauthToken, appKey, secretKey, requestId); err != nil {
				logger.CtxLog(c).Errorf("getThirdPartyOauthStatusAndUserInfo refreshJDToken fail, requestId:%v, err:%v",
					requestId, err)
				return false, nil
			}
		}
		venderInfo, jdErrRsp, err := getJDVenderInfo(oauthToken, appKey, secretKey, requestId)
		if err != nil {
			logger.CtxLog(c).Errorf("getThirdPartyOauthStatusAndUserInfo refreshJDToken fail, requestId:%v, err:%v",
				requestId, err)
			//c.AbortWithStatusJSON(http.StatusOK, commProto.NewCommRsp(510001, "服务繁忙"))
			return false, nil
		} else if jdErrRsp != nil {
			//c.AbortWithStatusJSON(http.StatusOK, commProto.NewCommDataRsp(510002, "服务繁忙", jdErrRsp))
			return false, nil
		} else if venderInfo.VenderId > 0 {
			if oauthToken.VenderID != fmt.Sprintf("%v", venderInfo.VenderId) ||
				oauthToken.ColType != venderInfo.ColType ||
				oauthToken.ShopID != fmt.Sprintf("%v", venderInfo.ShopId) ||
				oauthToken.ShopName != venderInfo.ShopName {
				oauthToken.VenderID = fmt.Sprintf("%v", venderInfo.VenderId)
				oauthToken.ColType = venderInfo.ColType
				oauthToken.ShopID = fmt.Sprintf("%v", venderInfo.ShopId)
				oauthToken.ShopName = venderInfo.ShopName
				if err = oauthToken.Update(); err != nil {
					logger.CtxLog(c).Errorf("getThirdPartyOauthStatusAndUserInfo Update OauthToken Vender fail, requestId:%v, err:%v",
						requestId, err)
				}
			}
		}
		if venderInfo.VenderId == 0 && oauthToken.ColType != -1 {
			oauthToken.ColType = -1
			if err = oauthToken.Update(); err != nil {
				logger.CtxLog(c).Errorf("getThirdPartyOauthStatusAndUserInfo Update OauthToken colType fail, requestId:%v, err:%v",
					requestId, err)
			}
		}

		// 根据最新Token获取第三方平台用户信息
		params := GenJDOpenApiParamsStr(map[string]string{
			"openId": oauthToken.OpenID,
		}, appKey, secretKey, oauthToken.AccessToken, JDGetUserInfoMethod)
		jdUserUrl := fmt.Sprintf(JDOpenAPICommURLFmt, params)
		jdUserRsp := &proto.JDUserRsp{}
		if err := httpGet(jdUserUrl, &jdUserRsp); err != nil {
			logger.CtxLog(c).Errorf("getThirdPartyOauthStatusAndUserInfo call uri[%v] fail, cost:%v, requestId:%v, err:%v",
				jdUserUrl, time.Since(nowTime), requestId, err)
			return false, nil
		} else if jdUserRsp.ErrorResponse != nil ||
			jdUserRsp.JDUserGetUserInfoByOpenIdResponse.GetuserinfobyappidandopenidResult.Code != 0 {
			logger.CtxLog(c).Errorf("getThirdPartyOauthStatusAndUserInfo call uri[%v] fail, cost:%v, requestId:%v, rsp:%v",
				jdUserUrl, time.Since(nowTime), requestId, jdUserRsp)
			return false, nil
		}
		logger.CtxLog(c).Infof("getThirdPartyOauthStatusAndUserInfo call uri[%v] succ, cost:%v, requestId:%v, rsp:%v",
			jdUserUrl, time.Since(nowTime), requestId, jdUserRsp)
		jdUserInfo := jdUserRsp.JDUserGetUserInfoByOpenIdResponse.GetuserinfobyappidandopenidResult.Data
		return true, &ThirdPartyUserInfo{
			Platform: platform,
			VenderID: oauthToken.VenderID,
			ColType:  oauthToken.ColType,
			ShopID:   oauthToken.ShopID,
			ShopName: oauthToken.ShopName,
			OpenID:   oauthToken.OpenID,
			NickName: jdUserInfo.NickName,
			ImageURL: jdUserInfo.ImageUrl,
			Gender:   jdUserInfo.Gendar,
		}
	case enum.MT:
		var developerId = config.LocalConfig.MTSetting.DeveloperId
		// 查询最新Token
		oauthToken := &model.OauthToken{}
		if err := oauthToken.FindByUserIDAndAppKey(userId, platform, openId,
			fmt.Sprintf("%v", developerId)); err != nil {
			logger.CtxLog(c).Errorf("getThirdPartyOauthStatusAndUserInfo OauthToken FindByUserIDAndAppKey fail, err:%v", err)
			return false, nil
		} else if oauthToken.ID <= 0 || len(oauthToken.AccessToken) == 0 {
			return false, nil
		} else if int64(oauthToken.ExpiresIn)+oauthToken.TakeEffect/1000 <= nowTime.Unix() {
			if err := refreshMTToken(oauthToken, requestId); err != nil {
				logger.CtxLog(c).Errorf("getThirdPartyOauthStatusAndUserInfo refreshMtToken fail, err:%v", err)
				return false, nil
			}
		}
		return true, &ThirdPartyUserInfo{
			Platform: platform,
			VenderID: oauthToken.VenderID,
			ColType:  oauthToken.ColType,
			ShopID:   oauthToken.ShopID,
			ShopName: oauthToken.ShopName,
			OpenID:   oauthToken.OpenID,
			Puid:     oauthToken.OpenID, // 美团的openId和pUid相同
			// TODO 当前美图用户信息没有API可获取
			NickName: oauthToken.NickName,
			ImageURL: oauthToken.HeadImgURL,
			Gender:   -1,
		}
	case enum.TB:
		// 查询授权APP的AK/SK
		oauthAppInterface, exists := c.Get(ContextTBOauthAppKey)
		if !exists || oauthAppInterface == nil {
			//c.AbortWithStatusJSON(http.StatusOK, commProto.NewCommRsp(460066, "invalid Source Header"))
			return false, nil
		}
		oauthApp := oauthAppInterface.(*config.TBOauthAppInfo)
		appKey := oauthApp.AppKey
		secretKey := oauthApp.SecretKey
		// 查询最新Token
		oauthToken := &model.OauthToken{}
		if err := oauthToken.FindByUserIDAndAppKey(userId, platform, openId, appKey); err != nil {
			logger.CtxLog(c).Errorf("getThirdPartyOauthStatusAndUserInfo OauthToken FindByUserIDAndAppKey fail, err:%v", err)
			return false, nil
		} else if oauthToken.ID <= 0 || len(oauthToken.AccessToken) == 0 {
			return false, nil
		} else if int64(oauthToken.ExpiresIn)+oauthToken.TakeEffect/1000 <= nowTime.Unix() {
			if err := taobao.RefreshToken(oauthToken, secretKey); err != nil {
				logger.CtxLog(c).Errorf("getThirdPartyOauthStatusAndUserInfo refreshMtToken fail, err:%v", err)
				return false, nil
			}
		}
		return true, &ThirdPartyUserInfo{
			Platform: platform,
			VenderID: oauthToken.VenderID,
			ColType:  oauthToken.ColType,
			ShopID:   oauthToken.ShopID,
			ShopName: oauthToken.ShopName,
			OpenID:   oauthToken.OpenID,
			Puid:     oauthToken.PuID,
			NickName: oauthToken.NickName,
			// TODO 当前淘宝用户信息没有API可获取
			ImageURL: oauthToken.HeadImgURL,
			Gender:   -1,
		}
	default:
		return false, nil
	}
}

// ThirdPartyOauthCancel 取消第三方Oauth2授权
func ThirdPartyOauthCancel(c *gin.Context) {
	requestId := c.GetHeader(global.HeaderRequestID)
	req := &proto.ThirdPartyOauthCancelReq{}
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.CtxLog(c).Errorf("ThirdPartyOauthCancel bind param fail, requestId:%v, err:%v",
			requestId, err)
		c.JSON(http.StatusOK, commProto.NewCommRsp(100001, "参数异常"))
		return
	}
	oauthToken := model.OauthToken{}
	switch req.Platform {
	case enum.JD, enum.TB:
		// 京东、淘宝未提供解除授权API
		// 删除该用户所有该第三方授权Token记录
		if err := oauthToken.DeleteByUserIDAndOpenID(req.UserID, req.Platform, req.OpenID); err != nil {
			logger.CtxLog(c).Errorf("ThirdPartyOauthCancel OauthToken DeleteByUserIDAndOpenID fail, err:%v", err)
			c.JSON(http.StatusOK, commProto.NewCommRsp(500001, "服务繁忙"))
			return
		}
		c.JSON(http.StatusOK, commProto.NewSuccessRsp(nil))
	case enum.MT:
		var developerId = config.LocalConfig.MTSetting.DeveloperId
		var signKey = config.LocalConfig.MTSetting.SignKey
		var businessId = config.LocalConfig.MTSetting.BusinessId
		// 查询授权状态
		if err := oauthToken.FindByUserIDAndAppKey(req.UserID, req.Platform, req.OpenID,
			fmt.Sprintf("%v", developerId)); err != nil {
			logger.CtxLog(c).Errorf("ThirdPartyOauthCancel OauthToken FindByUserIDAndAppKey fail, err:%v", err)
			c.JSON(http.StatusOK, commProto.NewCommRsp(500002, "服务繁忙"))
		} else if oauthToken.ID > 0 {
			// TODO 移除accessToken
			if err = oauthToken.DeleteByOpenID(req.OpenID, req.Platform); err != nil {
				logger.CtxLog(c).Errorf("ThirdPartyOauthCancel DeleteByOpenID fail, err:%v", err)
				c.JSON(http.StatusOK, commProto.NewCommRsp(500004, ServerErrorMsg))
				return
			}
		}
		// 生成美团解除授权API
		unOauthUrl, err := meituan.BuildUnOauthUrl(signKey, businessId, developerId, utils.RandStringRunes(8))
		if err != nil {
			logger.CtxLog(c).Errorf("ThirdPartyOauthCancel BuildUnOauthUrl fail, err:%v", err)
			c.JSON(http.StatusOK, commProto.NewCommRsp(500003, "获取授权解除链接失败"))
			return
		}
		// 返回美团解除授权API
		c.JSON(http.StatusOK, commProto.NewSuccessRsp(map[string]string{
			"url": unOauthUrl,
		}))
		return
	}
}

// ThirdPartyOauthCancelCallback 第三方Oauth2解除授权回调
func ThirdPartyOauthCancelCallback(c *gin.Context) {
	requestId := c.GetHeader(global.HeaderRequestID)
	req := &proto.OauthCancelCallbackReq{}
	if err := c.ShouldBind(&req); err != nil {
		logger.CtxLog(c).Errorf("ThirdPartyOauthCancelCallback bind param fail, requestId:%v, err:%v",
			requestId, err)
		c.JSON(http.StatusOK, commProto.NewCommRsp(100001, "参数异常"))
		return
	}
	logger.CtxLog(c).Infof("ThirdPartyOauthCancelCallback bind param success, param:%v", req)
	if req.MsgType == "510001" {
		var message *proto.MtCancelCallbackMessage
		if err := json.Unmarshal([]byte(req.Message), &message); err != nil {
			logger.CtxLog(c).Errorf("ThirdPartyOauthCancelCallback json Unmarshal fail, err:%v", err)
			c.JSON(http.StatusOK, commProto.NewCommRsp(100003, "参数异常"))
			return
		}
		oauthToken := &model.OauthToken{}
		if err := oauthToken.DeleteByOpenID(message.OpBizCode, enum.MT); err != nil {
			logger.CtxLog(c).Errorf("ThirdPartyOauthCancelCallback DeleteByOpenID fail, err:%v", err)
			c.JSON(http.StatusOK, commProto.NewCommRsp(500001, ServerErrorMsg))
			return
		}
		c.JSON(http.StatusOK, commProto.NewSuccessRsp(nil))
		return
	}
	c.JSON(http.StatusOK, commProto.NewCommRsp(100002, "参数异常"))
}

func httpGet(URL string, rsp interface{}) error {
	// 超时时间：10秒
	client := &http.Client{
		Timeout: 10 * time.Second,
		Transport: &http.Transport{
			TLSClientConfig: &tls.Config{
				InsecureSkipVerify: true, // 忽略证书校验
			},
		},
	}
	resp, err := client.Get(URL)
	if err != nil {
		return err
	}
	defer func() {
		_ = resp.Body.Close()
	}()
	result, _ := ioutil.ReadAll(resp.Body)
	return json.Unmarshal(result, rsp)
}

func getJDVenderInfo(oauthToken *model.OauthToken, appKey, secretKey, requestId string) (*proto.VenderInfo, *proto.JDErrorRsp, error) {
	nowTime := time.Now()
	jdVenderRsp := &proto.JDVenderRsp{}
	params := GenJDOpenApiParamsStr(map[string]string{},
		appKey, secretKey, oauthToken.AccessToken, JDGetVenderInfoMethod)
	jdVenderUrl := fmt.Sprintf(JDOpenAPICommURLFmt, params)
	if err := httpGet(jdVenderUrl, &jdVenderRsp); err != nil {
		logger.CtxLog(nil).Errorf("getJDVenderInfo call uri[%v] fail, cost:%v, requestId:%v, err:%v",
			jdVenderUrl, time.Since(nowTime), requestId, err)
		return nil, nil, err
	} else if jdVenderRsp.ErrorResponse != nil {
		logger.CtxLog(nil).Errorf("getJDVenderInfo call uri[%v] fail, cost:%v, requestId:%v, rsp:%v",
			jdVenderUrl, time.Since(nowTime), requestId, jdVenderRsp)
		if jdVenderRsp.ErrorResponse.Code == "67" {
			return &proto.VenderInfo{}, nil, nil
		} else {
			return nil, jdVenderRsp.ErrorResponse, nil
		}
	}
	logger.CtxLog(nil).Infof("getJDVenderInfo call uri[%v] success, cost:%v, requestId:%v, rsp:%v",
		jdVenderUrl, time.Since(nowTime), requestId, jdVenderRsp)
	return &jdVenderRsp.JingdongSellerVenderInfoGetResponse.VenderInfoResult, nil, nil
}

func refreshJDToken(oauthToken *model.OauthToken, appKey, secretKey, requestId string) error {
	nowTime := time.Now()
	// 查询授权APP的AK/SK
	refreshTokenUrl := fmt.Sprintf(JDOauthTokenRefreshURLFmt, appKey, secretKey, oauthToken.RefreshToken)
	jdTokenRsp := &proto.JDTokenRsp{}
	if err := httpGet(refreshTokenUrl, &jdTokenRsp); err != nil {
		logger.CtxLog(nil).Errorf("refreshJDToken call uri[%v] fail, err:%v", refreshTokenUrl, err)
		return err
	} else if jdTokenRsp.Code != 0 {
		logger.CtxLog(nil).Errorf("refreshJDToken call uri[%v] fail, cost:%v, requestId:%v, rsp:%v",
			refreshTokenUrl, time.Since(nowTime), requestId, jdTokenRsp)
		return errors.New(jdTokenRsp.Msg)
	}
	logger.CtxLog(nil).Infof("refreshJDToken call uri[%v] success, cost:%v, requestId:%v, rsp:%v",
		refreshTokenUrl, time.Since(nowTime), requestId, jdTokenRsp)
	// 获取Token成功, 存储Token信息
	oauthToken.AccessToken = jdTokenRsp.AccessToken
	oauthToken.RefreshToken = jdTokenRsp.RefreshToken
	oauthToken.TakeEffect = jdTokenRsp.Time
	oauthToken.ExpiresIn = jdTokenRsp.ExpiresIn
	oauthToken.PuID = jdTokenRsp.Uid
	oauthToken.OpenID = jdTokenRsp.OpenId
	return oauthToken.Update()
}

func refreshMTToken(oauthToken *model.OauthToken, requestId string) error {
	nowTime := time.Now()
	tokenRsp, err := meituan.NewDefaultClient().RefreshToken(config.LocalConfig.MTSetting.BusinessId,
		oauthToken.RefreshToken)
	if err != nil {
		logger.CtxLog(nil).Errorf("RequestId:[%v], startCheckOauth refreshMtToken fail, err:%v",
			requestId, err)
		return err
	} else if !tokenRsp.IsSuccess() {
		return errors.New(tokenRsp.Msg)
	}
	logger.CtxLog(nil).Infof("refreshMTToken success, cost:%v, requestId:%v, rsp:%v",
		time.Since(nowTime), requestId, tokenRsp)
	// 获取Token成功, 存储Token信息
	oauthToken.AccessToken = tokenRsp.Data.AccessToken
	oauthToken.RefreshToken = tokenRsp.Data.RefreshToken
	oauthToken.TakeEffect = time.Now().UnixMilli()
	oauthToken.ExpiresIn = tokenRsp.Data.ExpireIn
	oauthToken.OpenID = tokenRsp.Data.OpBizCode
	return oauthToken.Update()
}

func choiceJDOauthApp(sourceTag string) *config.JDOauthAppInfo {
	switch sourceTag {
	case SrcPlatBaiShu:
		return config.LocalConfig.JDSettingBaiShu
	case SrcPlatHuiBoXing:
		return config.LocalConfig.JDSettingHuiBoXing
	case SrcPlatXiLing:
		return config.LocalConfig.JDSettingXiLing
	default:
		return config.LocalConfig.JDSettingXiLing
	}
}

func choiceTBOauthApp(sourceTag string) *config.TBOauthAppInfo {
	switch sourceTag {
	case SrcPlatBaiShu:
		return config.LocalConfig.TBSettingBaiShu
	case SrcPlatHuiBoXing:
		return config.LocalConfig.TBSettingHuiBoXing
	case SrcPlatXiLing:
		return config.LocalConfig.TBSettingXiLing
	default:
		return config.LocalConfig.TBSettingXiLing
	}
}
