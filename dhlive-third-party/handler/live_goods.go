package handler

import (
	commProto "acg-ai-go-common/beans/proto"
	"acg-ai-go-common/global"
	"acg-ai-go-common/logger"
	"dhlive-third-party/beans/enum"
	"dhlive-third-party/beans/model"
	"dhlive-third-party/beans/proto"
	config "dhlive-third-party/conf"
	"dhlive-third-party/handler/meituan"
	"dhlive-third-party/handler/weipinhui/sdk"
	"dhlive-third-party/handler/weipinhui/sdk/response"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/gin-gonic/gin"
	"net/http"
	"sort"
	"strconv"
	"strings"
	"time"
)

const (
	maxLiveNumber             int    = 7
	searchJDLiveMaxPageNumber int    = 100
	jdSkuImagePrefix          string = "https://m.360buyimg.com/mobilecms/"
)

// GetThirdPartyLiveGoods 获取第三方直播间商品
func GetThirdPartyLiveGoods(c *gin.Context) {
	nowTime := time.Now()
	requestId := c.<PERSON>er(global.HeaderRequestID)
	req := &proto.GetThirdPartyLiveGoodsReq{}
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.CtxLog(c).Errorf("GetThirdPartyLiveGoods bind param fail, requestId:%v, err:%v",
			requestId, err)
		c.JSON(http.StatusOK, commProto.NewCommRsp(100001, "参数异常"))
		return
	}
	oauthToken, ok := thirdPartyOauthCheck(c, req.UserID, req.Platform, req.OpenID)
	if oauthToken == nil || !ok {
		logger.CtxLog(c).Errorf("GetThirdPartyLiveGoods thirdPartyOauthCheck fail, requestId:%v, cost:%v",
			requestId, time.Since(nowTime))
		return
	}
	switch oauthToken.Platform {
	case enum.JD:
		// 查询授权APP的AK/SK
		oauthAppInterface, exists := c.Get(ContextJDOauthAppKey)
		if !exists || oauthAppInterface == nil {
			c.AbortWithStatusJSON(http.StatusOK, commProto.NewCommRsp(460066, "invalid Source Header"))
			return
		}
		oauthApp := oauthAppInterface.(*config.JDOauthAppInfo)
		appKey := oauthApp.AppKey
		secretKey := oauthApp.SecretKey
		// 先获取在播直播间
		liveStreamingReq := &proto.JDLiveSearchReq{
			State:    6,
			Page:     1,
			PageSize: searchJDLiveMaxPageNumber,
		}
		lives, err := searchJDLives(liveStreamingReq, oauthToken.AccessToken, appKey, secretKey, requestId)
		if err != nil {
			logger.CtxLog(c).Errorf("GetThirdPartyLiveGoods searchJDLives fail, cost:%v, requestId:%v, err:%v",
				time.Since(nowTime), requestId, err)
			c.JSON(http.StatusOK, commProto.NewCommDataRsp(500002, "call JD openAPI failed", err))
			return
		}
		// 开播时间从小到大排序
		sort.Slice(lives, func(i, j int) bool {
			return lives[i].LiveTime < lives[j].LiveTime
		})
		if len(lives) > maxLiveNumber {
			lives = lives[:maxLiveNumber]
		}
		// 再获取待播直播间
		liveReadyReq := &proto.JDLiveSearchReq{
			State:    5,
			Page:     1,
			PageSize: searchJDLiveMaxPageNumber,
		}
		readyLives, err := searchJDLives(liveReadyReq, oauthToken.AccessToken, appKey, secretKey, requestId)
		if err != nil {
			logger.CtxLog(c).Errorf("GetThirdPartyLiveGoods searchJDReadyLives fail, cost:%v, requestId:%v, err:%v",
				time.Since(nowTime), requestId, err)
			c.JSON(http.StatusOK, commProto.NewCommDataRsp(500003, "call JD openAPI failed", err))
			return
		}
		// 开播时间从小到大排序
		sort.Slice(readyLives, func(i, j int) bool {
			return readyLives[i].LiveTime < readyLives[j].LiveTime
		})
		if len(readyLives) > maxLiveNumber {
			readyLives = readyLives[:maxLiveNumber]
		}
		lives = append(lives, readyLives...)
		logger.CtxLog(c).Infof("GetThirdPartyLiveGoods getLives success, requestId:%v, lives num:%v",
			requestId, len(lives))
		// 获取当前用户直播间下sku信息
		resultData := &proto.GetThirdPartyLiveGoodsData{
			GoodsList: []proto.GoodsInfo{},
		}
		var skuIds []string
		skuIdMap := make(map[string]int, 0)
		for _, live := range lives {
			// TODO 如果很慢需要后续改为并发请求
			jdLiveSkuReq := &proto.JDLiveDetailReq{LiveID: fmt.Sprintf("%v", live.LiveId)}
			liveDetail, err := getJDLiveSku(jdLiveSkuReq, oauthToken.AccessToken, appKey, secretKey, requestId)
			if err != nil {
				logger.CtxLog(c).Errorf("GetThirdPartyLiveGoods getJDLiveSku fail, requestId:%v, err:%v",
					requestId, err)
				continue
			}
			if liveDetail == nil || len(liveDetail.SkuInfos) == 0 {
				continue
			}
			for _, skuInfo := range liveDetail.SkuInfos {
				// 对skuId去重
				if _, ok := skuIdMap[skuInfo.SkuId]; !ok {
					skuIdMap[skuInfo.SkuId] = 1
					skuIds = append(skuIds, skuInfo.SkuId)
				}
			}
		}
		logger.CtxLog(c).Infof("GetThirdPartyLiveGoods getSkuIds success, requestId:%v, skuIds:%v", requestId, skuIds)
		// 搜索所有商品信息，每个请求最大20个skuId
		skuIdsCollection := strArraySplit(skuIds, 20)
		var skuDetailList []proto.JDLiveSkuDetail
		for _, subSkuIds := range skuIdsCollection {
			jdLiveSkuReq := &proto.JDLiveSkuDetailReq{
				SkuIds:   strings.Join(subSkuIds, ","),
				Fields:   "wareTitle,jdPrice,logo,skuName,status",
				PageNo:   1,
				PageSize: 50,
			}
			skuDetail, err := searchJDSkuDetail(jdLiveSkuReq, oauthToken.AccessToken, appKey, secretKey, requestId)
			if err != nil {
				logger.CtxLog(c).Errorf("GetThirdPartyLiveGoods searchJDSkuDetail fail, requestId:%v, err:%v",
					requestId, err)
				continue
			}
			skuDetailList = append(skuDetailList, skuDetail...)
		}
		logger.CtxLog(c).Infof("GetThirdPartyLiveGoods searchJDSkuDetail success, requestId:%v, skuDetail num:%v",
			requestId, len(skuDetailList))
		skuResultMap := make(map[string]proto.GoodsInfo)
		for _, skuDetail := range skuDetailList {
			if !strings.HasPrefix(skuDetail.Logo, "https://") {
				skuDetail.Logo = jdSkuImagePrefix + skuDetail.Logo
			}
			skuResultMap[fmt.Sprintf("%v", skuDetail.SkuId)] = proto.GoodsInfo{
				Id:           fmt.Sprintf("%v", skuDetail.SkuId),
				Source:       "Jingdong",
				Name:         skuDetail.SkuName,
				SellingPrice: fmt.Sprintf("%v", skuDetail.JdPrice),
				OriginPrice:  fmt.Sprintf("%v", skuDetail.JdPrice),
				Images:       []string{skuDetail.Logo},
				Attributes:   nil,
			}
		}
		// 按照skuIds的列表排序组装结果集
		for _, skuId := range skuIds {
			if v, ok := skuResultMap[skuId]; ok {
				resultData.GoodsList = append(resultData.GoodsList, v)
			}
		}

		c.JSON(http.StatusOK, commProto.NewSuccessRsp(resultData))
		return
	case enum.MT:
		parseIntLiveId, err := strconv.ParseInt(req.LiveID, 10, 64)
		if err != nil {
			logger.CtxLog(c).Errorf("GetThirdPartyLiveGoods bind param fail, requestId:%v, err:%v",
				requestId, err)
			c.JSON(http.StatusOK, commProto.NewCommRsp(100002, "直播间ID应为数字字符串"))
			return
		}
		getMTLiveGoods(c, oauthToken, parseIntLiveId)
	default:
		c.JSON(http.StatusOK, commProto.NewCommRsp(100002,
			fmt.Sprintf("暂不支持%v平台", oauthToken.Platform)))
		return
	}
}

// 搜索京东直播间
func searchJDLives(searchJDLivesReq *proto.JDLiveSearchReq, accessToken, appKey, secretKey,
	requestId string) ([]proto.JDLiveLimitInfo, error) {
	nowTime := time.Now()
	// 先获取在播直播间，再获取待播直播间
	params := GenJDOpenApiParamsStr(searchJDLivesReq, appKey, secretKey, accessToken, JDSearchLiveMethod)
	jdLivesUrl := fmt.Sprintf(JDOpenAPICommURLFmt, params)
	jdLivesRsp := &proto.JDLiveSearchRsp{}
	if err := httpGet(jdLivesUrl, &jdLivesRsp); err != nil {
		logger.CtxLog(nil).Errorf("searchJDLives call uri[%v] fail, cost:%v, requestId:%v, err:%v",
			jdLivesUrl, time.Since(nowTime), requestId, err)
		return nil, err
	} else if jdLivesRsp.ErrorResponse != nil {
		logger.CtxLog(nil).Errorf("searchJDLives call uri[%v] fail, cost:%v, requestId:%v, rsp:%v",
			jdLivesUrl, time.Since(nowTime), requestId, jdLivesRsp)
		return nil, errors.New(jdLivesRsp.ErrorResponse.ZhDesc)
	} else if !jdLivesRsp.JingdongLiveAppServiceLivesListSearchResponse.ReturnType.Success {
		logger.CtxLog(nil).Errorf("searchJDLives call uri[%v] fail, cost:%v, requestId:%v, rsp:%v",
			jdLivesUrl, time.Since(nowTime), requestId, jdLivesRsp)
		return nil, errors.New("call jd openApi fail")
	}
	return jdLivesRsp.JingdongLiveAppServiceLivesListSearchResponse.ReturnType.Data.LiveList.List, nil
}

// 获取直播间详情>Sku信息
func getJDLiveSku(jdLiveSkuReq *proto.JDLiveDetailReq, accessToken, appKey, secretKey,
	requestId string) (*proto.JDLiveDetailInfo, error) {
	nowTime := time.Now()
	params := GenJDOpenApiParamsStr(jdLiveSkuReq, appKey, secretKey, accessToken, JDLiveAndSkuDetailMethod)
	jdLiveSkuUrl := fmt.Sprintf(JDOpenAPICommURLFmt, params)
	jdLiveSkuRsp := &proto.JDLiveDetailRsp{}
	if err := httpGet(jdLiveSkuUrl, &jdLiveSkuRsp); err != nil {
		logger.CtxLog(nil).Errorf("getJDLiveSku call uri[%v] fail, cost:%v, requestId:%v, err:%v",
			jdLiveSkuUrl, time.Since(nowTime), requestId, err)
		return nil, err
	} else if jdLiveSkuRsp.ErrorResponse != nil {
		logger.CtxLog(nil).Errorf("getJDLiveSku call uri[%v] fail, cost:%v, requestId:%v, rsp:%v",
			jdLiveSkuUrl, time.Since(nowTime), requestId, jdLiveSkuRsp.ErrorResponse)
		return nil, errors.New(jdLiveSkuRsp.ErrorResponse.ZhDesc)
	} else if !jdLiveSkuRsp.JingdongLiveLiveAndSkuDetailGetResponse.ReturnType.Success {
		logger.CtxLog(nil).Errorf("getJDLiveSku call uri[%v] fail, cost:%v, requestId:%v, rsp:%v",
			jdLiveSkuUrl, time.Since(nowTime), requestId, jdLiveSkuRsp)
		return nil, errors.New(jdLiveSkuRsp.JingdongLiveLiveAndSkuDetailGetResponse.ReturnType.Msg)
	}
	return &jdLiveSkuRsp.JingdongLiveLiveAndSkuDetailGetResponse.ReturnType.Attribute1, nil
}

// 搜索Sku详情
func searchJDSkuDetail(jdLiveSkuReq *proto.JDLiveSkuDetailReq, accessToken, appKey, secretKey,
	requestId string) ([]proto.JDLiveSkuDetail, error) {
	nowTime := time.Now()
	params := GenJDOpenApiParamsStr(jdLiveSkuReq, appKey, secretKey, accessToken, JDSkuSearchDetailMethod)
	jdSkuDetailUrl := fmt.Sprintf(JDOpenAPICommURLFmt, params)
	jdSkuDetailRsp := &proto.JDLiveSkuDetailRsp{}
	if err := httpGet(jdSkuDetailUrl, &jdSkuDetailRsp); err != nil {
		logger.CtxLog(nil).Errorf("searchJDSkuDetail call uri[%v] fail, cost:%v, requestId:%v, err:%v",
			jdSkuDetailUrl, time.Since(nowTime), requestId, err)
		return nil, err
	} else if jdSkuDetailRsp.ErrorResponse != nil {
		logger.CtxLog(nil).Errorf("searchJDSkuDetail call uri[%v] fail, cost:%v, requestId:%v, rsp:%v",
			jdSkuDetailUrl, time.Since(nowTime), requestId, jdSkuDetailRsp)
		return nil, errors.New(jdSkuDetailRsp.ErrorResponse.ZhDesc)
	}
	return jdSkuDetailRsp.JingdongSkuReadSearchSkuListResponse.Page.Data, nil
}

// ThirdPartyLiveGoodsPush 第三方直播间商品弹出卡片
func ThirdPartyLiveGoodsPush(c *gin.Context) {
	nowTime := time.Now()
	requestId := c.GetHeader(global.HeaderRequestID)

	req := &proto.ThirdPartyLiveGoodsPushReq{}
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.CtxLog(c).Errorf("ThirdPartyLiveGoodsPush bind param fail, err:%v", err)
		c.JSON(http.StatusOK, commProto.NewCommRsp(100001, "参数异常"))
		return
	}

	oauthToken, ok := thirdPartyOauthCheck(c, req.UserID, req.Platform, req.OpenID)
	if oauthToken == nil || !ok {
		logger.CtxLog(c).Errorf("ThirdPartyLiveGoodsPush thirdPartyOauthCheck fail, requestId:%v, cost:%v",
			requestId, time.Since(nowTime))
		return
	}

	switch req.Platform {
	case enum.JD:
		JDGoodsPush(c, req, requestId, oauthToken.AccessToken, nowTime)
	case enum.WPH:
		WPHGoodsPush(c, req, requestId, oauthToken.AccessToken, nowTime)
	default:
		JDGoodsPush(c, req, requestId, oauthToken.AccessToken, nowTime)
	}

}

func WPHGoodsPush(c *gin.Context, req *proto.ThirdPartyLiveGoodsPushReq, requestId string, token string, nowTime time.Time) {
	// 查询授权APP的AK/SK
	oauthAppInterface, exists := c.Get(ContextWPHOauthAppKey)
	if !exists || oauthAppInterface == nil {
		c.JSON(http.StatusOK, commProto.NewCommRsp(460066, "invalid Source Header"))
		return
	}
	oauthApp := oauthAppInterface.(*config.WPHOauthAppInfo)
	appKey := oauthApp.AppKey
	secretKey := oauthApp.SecretKey
	client := sdk.NewDefaultTopClient(appKey, secretKey, sdk.WPHVipServerUrl, 3000, 3000)

	recommendRequest := make(map[string]interface{}, 1)
	request := make(map[string]string, 2)
	request["room_id"] = req.LiveID
	request["goods_id"] = req.GoodsID
	recommendRequest["request"] = request

	resp, err := client.ExecuteWithSession(sdk.WPHVipPushGoodsServer, sdk.WPHVipPushGoodsMethod, sdk.WPHVipPushGoodsVersion, recommendRequest, token)
	if err != nil {
		logger.CtxLog(c).Errorf("WPHGoodsPush call uri[%v] fail, cost:%v, requestId:%v, err:%v",
			sdk.WPHVipPushGoodsServer, time.Since(nowTime), requestId, err)
		c.JSON(http.StatusOK, commProto.NewCommRsp(500002, "call WPH openAPI failed"))
		return
	}
	logger.CtxLog(c).Infof("WPHGoodsPush call uri[%v] resp, cost:%v, requestId:%v, rsp:%v",
		sdk.WPHVipPushGoodsServer, time.Since(nowTime), requestId, resp)
	recommendResp := &response.RecommendResponse{}
	err = json.Unmarshal([]byte(resp), recommendResp)
	if err != nil {
		logger.CtxLog(c).Errorf("WPHGoodsPush call uri[%v] fail, cost:%v, requestId:%v, err:%v",
			sdk.WPHVipPushGoodsServer, time.Since(nowTime), requestId, err)
		c.JSON(http.StatusOK, commProto.NewCommRsp(500002, "call WPH openAPI json failed"))
		return
	}
	if recommendResp.ReturnCode != "0" {
		logger.CtxLog(c).Errorf("WPHGoodsPush call uri[%v] fail, code : %v , msg : %v",
			sdk.WPHVipPushGoodsServer, recommendResp.ReturnCode, recommendResp.ReturnMessage)
		// 唯品会accesstoken失效了需要重新授权
		if recommendResp.ReturnCode == "vipapis.oauth-invalidate-failure" {
			c.AbortWithStatusJSON(http.StatusOK, commProto.NewCommRsp(400001, "未授权"))
			return
		}
		c.AbortWithStatusJSON(http.StatusOK, commProto.NewCommRsp(500003, recommendResp.ReturnMessage))
		return
	}

	c.JSON(http.StatusOK, commProto.NewSuccessRsp(nil))
}

func JDGoodsPush(c *gin.Context, req *proto.ThirdPartyLiveGoodsPushReq, requestId string, token string, nowTime time.Time) {
	// 查询授权APP的AK/SK
	oauthAppInterface, exists := c.Get(ContextJDOauthAppKey)
	if !exists || oauthAppInterface == nil {
		c.JSON(http.StatusOK, commProto.NewCommRsp(460066, "invalid Source Header"))
		return
	}
	oauthApp := oauthAppInterface.(*config.JDOauthAppInfo)
	appKey := oauthApp.AppKey
	secretKey := oauthApp.SecretKey

	// 查询在播直播间
	if len(req.LiveID) == 0 {
		liveStreamingReq := &proto.JDLiveSearchReq{
			State:    6,
			Page:     1,
			PageSize: 1,
		}
		lives, err := searchJDLives(liveStreamingReq, token, appKey, secretKey, requestId)
		if err != nil {
			logger.CtxLog(c).Errorf("GetThirdPartyLiveGoods searchJDLives fail, cost:%v, requestId:%v, err:%v",
				time.Since(nowTime), requestId, err)
			c.JSON(http.StatusOK, commProto.NewCommRsp(500002, "call JD openAPI failed"))
			return
		}
		if len(lives) == 1 {
			req.LiveID = fmt.Sprintf("%v", lives[0].LiveId)
		} else {
			c.JSON(http.StatusOK, commProto.NewCommRsp(300002, "未查询到开播直播间"))
			return
		}
	}
	jdLivePushSkuReq := &proto.JDLiveSkuPushReq{
		LiveID: req.LiveID,
		SkuID:  req.GoodsID,
	}
	jdLivePushSkuRsp := &proto.JDLiveSkuPushRsp{}
	params := GenJDOpenApiParamsStr(jdLivePushSkuReq, appKey, secretKey, token, JDLiveSkuPushMethod)
	jdLivePushSkuUrl := fmt.Sprintf(JDOpenAPICommURLFmt, params)
	if err := httpGet(jdLivePushSkuUrl, &jdLivePushSkuRsp); err != nil {
		logger.CtxLog(c).Errorf("ThirdPartyLiveGoodsPush call uri[%v] fail, cost:%v, requestId:%v, err:%v",
			jdLivePushSkuUrl, time.Since(nowTime), requestId, err)
		c.JSON(http.StatusOK, commProto.NewCommRsp(500001, "服务繁忙"))
		return
	} else if jdLivePushSkuRsp.ErrorResponse != nil {
		logger.CtxLog(c).Errorf("ThirdPartyLiveGoodsPush call uri[%v] fail, cost:%v, requestId:%v, rsp:%v",
			jdLivePushSkuUrl, time.Since(nowTime), requestId, jdLivePushSkuRsp)
		c.JSON(http.StatusOK,
			commProto.NewCommDataRsp(500002, "服务繁忙", jdLivePushSkuRsp.ErrorResponse))
		return
	} else if !jdLivePushSkuRsp.JingdongLiveIsvOperationSkuPushResponse.ReturnType.Success {
		logger.CtxLog(c).Errorf("ThirdPartyLiveGoodsPush call uri[%v] fail, cost:%v, requestId:%v, rsp:%v",
			jdLivePushSkuUrl, time.Since(nowTime), requestId, jdLivePushSkuRsp)
		c.JSON(http.StatusOK,
			commProto.NewCommRsp(300001, fmt.Sprintf("直播间ID[%v]弹出商品ID[%v]卡片异常:%v",
				req.LiveID, req.GoodsID, jdLivePushSkuRsp.JingdongLiveIsvOperationSkuPushResponse.ReturnType.ErrorMsg)))
		return
	}
	c.JSON(http.StatusOK, commProto.NewSuccessRsp(nil))
}

func getMTLiveGoods(c *gin.Context, oauthToken *model.OauthToken, liveId int64) {
	params := &proto.MTQueryLiveGoodsReq{
		LiveId: liveId,
	}
	bytes, _ := json.Marshal(params)
	logger.Log.Errorf("param: %v", bytes)
	rsp := make(map[string]interface{})
	rspBytes, err := meituan.NewDefaultClient().InvokeApi("/mlive/goods/query",
		config.LocalConfig.MTSetting.BusinessId, oauthToken.AccessToken, params)
	if err != nil {
		logger.CtxLog(c).Errorf("getMTPushUrlWithLiveMaterial InvokeApi fail, err:%v", err)
		c.JSON(http.StatusOK, commProto.NewCommRsp(500002, ServerErrorMsg))
		return
	}
	if err = json.Unmarshal(rspBytes, &rsp); err != nil {
		logger.CtxLog(nil).Errorf("getMTPushUrlWithLiveMaterial json Unmarshal fail, err:%v", err)
		c.JSON(http.StatusOK, commProto.NewCommRsp(500003, ServerErrorMsg))
		return
	}
	logger.CtxLog(nil).Infof("getMTPushUrlWithLiveMaterial call API success, rsp:%v", rsp)
	switch rsp["code"] {
	case 0:
	case "OP_REMOTE_ERROR":
		c.JSON(http.StatusOK, commProto.NewCommDataRsp(500004, ServerErrorMsg, rsp))
		return
	case "OP_UNIAUTH_FAILED":
		c.JSON(http.StatusOK, commProto.NewCommRsp(400001, "未授权"))
		return
	}
	c.JSON(http.StatusOK, commProto.NewSuccessRsp(rsp["data"]))
}
