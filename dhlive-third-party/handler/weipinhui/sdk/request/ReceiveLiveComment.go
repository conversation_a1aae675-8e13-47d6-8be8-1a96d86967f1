package request

import "encoding/json"

type ReceiveLiveCommentQueryRequest struct {
	Sign       string `form:"sign"`
	Timestamp  string `form:"timestamp"`
	AppKey     string `form:"appKey"`
	ApiService string `form:"apiService"`
}

func (p *ReceiveLiveCommentQueryRequest) ToJsonString() string {
	bytes, err := json.Marshal(p)
	if err != nil {
		return "{}"
	}
	return string(bytes)
}

type ReceiveLiveCommentBodyRequest struct {
	RoomId   string    `json:"room_id"`
	Comments []Comment `json:"comments"`
}

func (p *ReceiveLiveCommentBodyRequest) ToJsonString() string {
	bytes, err := json.Marshal(p)
	if err != nil {
		return "{}"
	}
	return string(bytes)
}

type Comment struct {
	GoodsInfo GoodsInfo `json:"goods_info"`
	UserId    string    `json:"user_id"`
	UserName  string    `json:"user_name"`
	Type      int32     `json:"type"`
	CommentId string    `json:"comment_id"`
	Priority  int32     `json:"priority"`
	Content   string    `json:"content"`
}

type GoodsInfo struct {
	GoodsName string `json:"goods_name"`
	Sort      int32  `json:"sort"`
}

type RecommendRequest struct {
	Request struct {
		RoomId  string `json:"room_id"`
		GoodsId string `json:"goods_id"`
	} `json:"request"`
}
