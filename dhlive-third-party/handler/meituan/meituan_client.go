package meituan

import (
	"acg-ai-go-common/logger"
	"crypto/sha1"
	"dhlive-third-party/beans/proto"
	config "dhlive-third-party/conf"
	"encoding/json"
	"fmt"
	"io"
	"io/ioutil"
	"net/http"
	"net/url"
	"sort"
	"strconv"
	"strings"
	"time"
)

type MtClient interface {
	InvokeApi(apiPath string, businessId int, appAuthToken string, data interface{}) ([]byte, error)

	GetToken(businessId int, code string) (*proto.MtTokenRsp, error)

	RefreshToken(businessId int, refreshToken string) (*proto.MtTokenRsp, error)
}

type ApiCommRspLimit struct {
	Code    string `json:"code"`
	Msg     string `json:"msg"`
	TraceId string `json:"traceId"`
}

type ApiCommRsp struct {
	Code    string      `json:"code"`
	Msg     string      `json:"msg"`
	TraceId string      `json:"traceId"`
	Data    interface{} `json:"data"`
}

const (
	MtOpenDomain    = "https://api-open-cater.meituan.com"
	MtOauth2Url     = "https://open-erp.meituan.com/general/auth"
	MtUnOauthUrl    = "https://open-erp.meituan.com/general/unauth"
	SuccessCode     = "OP_SUCCESS"
	TimestampKey    = "timestamp"
	BusinessIdKey   = "businessId"
	DeveloperIdKey  = "developerId"
	Charset         = "UTF-8"
	CharsetKey      = "charset"
	VersionKey      = "version"
	Version         = "2"
	BizKey          = "biz"
	AppAuthTokenKey = "appAuthToken"
	SignMapKey      = "sign"
)

type HttpClient struct {
	*http.Client
}

type DefaultMtClient struct {
	DeveloperId int64
	SignKey     string
	HttpClient  *http.Client
}

func NewDefaultClient() MtClient {
	httpClient := &http.Client{
		Timeout: time.Second * 10,
	}
	return &DefaultMtClient{
		DeveloperId: config.LocalConfig.MTSetting.DeveloperId,
		SignKey:     config.LocalConfig.MTSetting.SignKey,
		HttpClient:  httpClient,
	}
}

func NewClient(developerId int64, signKey string) MtClient {
	httpClient := &http.Client{
		Timeout: time.Second * 10,
	}
	return &DefaultMtClient{DeveloperId: developerId, SignKey: signKey, HttpClient: httpClient}
}

func NeeClientWithTimeout(developerId int64, signKey string, timeoutSec int64) MtClient {
	httpClient := &http.Client{
		Timeout: time.Second * time.Duration(timeoutSec),
	}
	return &DefaultMtClient{DeveloperId: developerId, SignKey: signKey, HttpClient: httpClient}
}

// InvokeApi 方法用于自定义路径和参数调用美团api，
// apiPath用于指定API的路径（不包含域名），例如/waimai/order/queryById
// data字段用于指定业务参数，可使用map[string]string，也可使用包含业务字段的结构体
func (client *DefaultMtClient) InvokeApi(apiPath string, businessId int,
	appAuthToken string, data interface{}) ([]byte, error) {
	paramMap, err := buildRequestParam(client.SignKey, businessId, appAuthToken, client.DeveloperId, data)
	if err != nil {
		logger.Log.Warn(fmt.Sprintf("calcSign encounted error:%s", err))
	}

	encodedData := encodeParamMap(paramMap)
	logger.Log.Debug(fmt.Sprintf("encoded request data is:%s", encodedData))

	req, err := http.NewRequest(http.MethodPost, MtOpenDomain+apiPath, strings.NewReader(encodedData))
	if err != nil {
		logger.CtxLog(nil).Errorf("meituan InvokeApi apiPath[%v] fail, err:%v", apiPath, err)
		return nil, err
	}

	req.Header.Add("Content-Type", "application/x-www-form-urlencoded")
	req.Header.Add("DeveloperId", paramMap[DeveloperIdKey])

	response, err := client.HttpClient.Do(req)
	if err != nil {
		return nil, err
	}

	defer response.Body.Close()
	return ioutil.ReadAll(response.Body)
}

func (client *DefaultMtClient) GetToken(businessId int, code string) (*proto.MtTokenRsp, error) {
	paramMap := make(map[string]string)
	paramMap[TimestampKey] = strconv.FormatInt(time.Now().Unix(), 10)
	paramMap[BusinessIdKey] = strconv.Itoa(businessId)
	paramMap[DeveloperIdKey] = strconv.FormatInt(client.DeveloperId, 10)
	paramMap[CharsetKey] = Charset
	paramMap["code"] = code
	paramMap["grantType"] = "authorization_code"
	sign, err := genMtOpenApiSign(paramMap, client.SignKey)
	if err != nil {
		return nil, err
	}
	paramMap[SignMapKey] = sign

	encodedData := encodeParamMap(paramMap)

	req, err := http.NewRequest("POST", MtOpenDomain+"/oauth/token", strings.NewReader(encodedData))
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", "application/x-www-form-urlencoded")
	req.Header.Add("DeveloperId", paramMap[DeveloperIdKey])

	resp, err := client.HttpClient.Do(req)
	if err != nil {
		return nil, err
	}

	defer func() {
		_ = resp.Body.Close()
	}()
	result, _ := ioutil.ReadAll(resp.Body)
	rsp := &proto.MtTokenRsp{}
	err = json.Unmarshal(result, &rsp)
	return rsp, err
}

func (client *DefaultMtClient) RefreshToken(businessId int, refreshToken string) (*proto.MtTokenRsp, error) {
	paramMap := make(map[string]string)
	paramMap[TimestampKey] = strconv.FormatInt(time.Now().Unix(), 10)
	paramMap[BusinessIdKey] = strconv.Itoa(businessId)
	paramMap[DeveloperIdKey] = strconv.FormatInt(client.DeveloperId, 10)
	paramMap[CharsetKey] = Charset
	paramMap["refreshToken"] = refreshToken
	paramMap["grantType"] = "refresh_token"
	paramMap["scope"] = "all"
	sign, err := genMtOpenApiSign(paramMap, client.SignKey)
	if err != nil {
		return nil, err
	}
	paramMap[SignMapKey] = sign

	encodedData := encodeParamMap(paramMap)
	logger.Log.Debug(fmt.Sprintf("encoded request data is:%s", encodedData))

	req, err := http.NewRequest("POST", MtOpenDomain+"/oauth/refresh", strings.NewReader(encodedData))
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", "application/x-www-form-urlencoded")
	req.Header.Add("DeveloperId", paramMap[DeveloperIdKey])

	resp, err := client.HttpClient.Do(req)
	if err != nil {
		return nil, err
	}

	defer func() {
		_ = resp.Body.Close()
	}()
	result, _ := ioutil.ReadAll(resp.Body)
	rsp := &proto.MtTokenRsp{}
	err = json.Unmarshal(result, &rsp)
	return rsp, err
}

func buildRequestParam(signKey string, businessId int, appAuthToken string,
	developerId int64, data interface{}) (map[string]string, error) {
	paramMap := make(map[string]string)

	paramMap[TimestampKey] = strconv.FormatInt(time.Now().Unix(), 10)
	paramMap[BusinessIdKey] = strconv.Itoa(businessId)
	paramMap[DeveloperIdKey] = strconv.FormatInt(developerId, 10)
	paramMap[CharsetKey] = Charset
	paramMap[VersionKey] = Version

	if data != nil {
		paramMap[BizKey] = toJson(data)
	}

	if appAuthToken != "" {
		paramMap[AppAuthTokenKey] = appAuthToken
	}

	sign, err := genMtOpenApiSign(paramMap, signKey)
	if err != nil {
		return nil, err
	}
	paramMap[SignMapKey] = sign

	return paramMap, nil
}

func BuildOauthUrl(signKey string, businessId int, developerId int64, state string, scope string) (string, error) {
	paramMap := make(map[string]string)

	paramMap[TimestampKey] = strconv.FormatInt(time.Now().Unix(), 10)
	paramMap[BusinessIdKey] = strconv.Itoa(businessId)
	paramMap[DeveloperIdKey] = strconv.FormatInt(developerId, 10)
	paramMap[CharsetKey] = Charset
	paramMap[VersionKey] = Version
	paramMap["qrMode"] = "2" // 二维码登录？
	if len(state) > 0 {
		paramMap["state"] = state
	}
	if len(scope) > 0 {
		paramMap["scope"] = scope
	}

	sign, err := genMtOpenApiSign(paramMap, signKey)
	if err != nil {
		return "", err
	}
	paramMap[SignMapKey] = sign
	encodedData := encodeParamMap(paramMap)

	return MtOauth2Url + "?" + encodedData, nil
}

func BuildUnOauthUrl(signKey string, businessId int, developerId int64, state string) (string, error) {
	paramMap := make(map[string]string)

	paramMap[TimestampKey] = strconv.FormatInt(time.Now().Unix(), 10)
	paramMap[BusinessIdKey] = strconv.Itoa(businessId)
	paramMap[DeveloperIdKey] = strconv.FormatInt(developerId, 10)
	paramMap[CharsetKey] = Charset
	paramMap[VersionKey] = Version
	if len(state) > 0 {
		paramMap["state"] = state
	}

	sign, err := genMtOpenApiSign(paramMap, signKey)
	if err != nil {
		return "", err
	}
	paramMap[SignMapKey] = sign
	encodedData := encodeParamMap(paramMap)

	return MtUnOauthUrl + "?" + encodedData, nil
}

func encodeParamMap(paramMap map[string]string) string {
	urlParamValues := url.Values{}
	for key, value := range paramMap {
		urlParamValues.Add(key, value)
	}

	return urlParamValues.Encode()
}

func toJson(data interface{}) string {
	bData, err := json.Marshal(data)
	if err != nil {
		return ""
	}

	return string(bData)
}

// genMtOpenApiSign 计算美团OpenAPI请求的签名，具体规则见 https://developer.meituan.com/docs/biz/comm-dev-isv-sign-rule
func genMtOpenApiSign(param map[string]string, signKey string) (string, error) {
	sortedString := getSortedString(param, signKey)
	return sha1Encode(sortedString)
}

func getSortedString(param map[string]string, signKey string) string {
	keys := make([]string, 0, len(param))
	for key, value := range param {
		//剔除value为空的key
		if value != "" {
			keys = append(keys, key)
		}
	}

	sort.Strings(keys)

	var sb strings.Builder
	sb.WriteString(signKey)

	for _, k := range keys {
		sb.WriteString(k)
		sb.WriteString(param[k])
	}

	return sb.String()
}

// string计算sha1
func sha1Encode(data string) (string, error) {
	t := sha1.New()
	_, err := io.WriteString(t, data)
	if err != nil {
		return "", err
	}
	return fmt.Sprintf("%x", t.Sum(nil)), nil
}
