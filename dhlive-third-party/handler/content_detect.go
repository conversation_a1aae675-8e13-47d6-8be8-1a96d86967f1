package handler

import (
	commProto "acg-ai-go-common/beans/proto"
	"acg-ai-go-common/global"
	"acg-ai-go-common/goredis"
	"acg-ai-go-common/logger"
	"acg-ai-go-common/utils"
	"context"
	"dhlive-third-party/beans/enum"
	"dhlive-third-party/beans/model"
	"dhlive-third-party/beans/proto"
	config "dhlive-third-party/conf"
	"errors"
	"fmt"
	"github.com/gin-gonic/gin"
	"github.com/go-redis/redis/v8"
	"net/http"
	"strings"
	"sync"
	"time"
)

const (
	JdSensitiveWordsKeyFmt   = "%v:dhlive-third-party:Jingdong:SensitiveWords:ZSet" // 京东敏感词有序集合，根据命中次数排序
	JdDetectTextResultKeyFmt = "%v:dhlive-third-party:Jingdong:DetectTextResult:%v" // 京东敏感词有序集合，根据命中次数排序, 示例：dhlive-third-party:Jingdong:DetectResult:${TextMd5}
	JdDetectResultKeyExp     = 24 * time.Hour
)

var detectImageItem = "1,2,3"

func ThirdPartyDetectText(c *gin.Context) {
	nowTime := time.Now()
	requestId := c.GetHeader(global.HeaderRequestID)
	req := &proto.ThirdPartyDetectTextReq{}
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.CtxLog(c).Errorf("ThirdPartyDetectText bind param fail, requestId:%v, err:%v",
			requestId, err)
		c.JSON(http.StatusOK, commProto.NewCommRsp(100001, "参数异常"))
		return
	}
	oauthToken, ok := thirdPartyOauthCheck(c, req.UserID, req.Platform, req.OpenID)
	if oauthToken == nil || !ok {
		logger.CtxLog(c).Errorf("ThirdPartyDetectText thirdPartyOauthCheck fail, requestId:%v, cost:%v",
			requestId, time.Since(nowTime))
		return
	}
	logger.CtxLog(c).Infof("ThirdPartyDetectText thirdPartyOauthCheck success, requestId:%v, cost:%v",
		requestId, time.Since(nowTime))
	switch oauthToken.Platform {
	case enum.JD:
		jdDetectText(c, req.Text, req.Mode, oauthToken, requestId)
		return
	default:
		c.JSON(http.StatusOK, commProto.NewCommRsp(100002,
			fmt.Sprintf("暂不支持%v平台", oauthToken.Platform)))
		return
	}
}

func jdDetectText(c *gin.Context, text string, mode enum.DetectMode,
	oauthToken *model.OauthToken, requestId string) {
	nowTime := time.Now()
	// 查询授权APP的AK/SK
	oauthAppInterface, exists := c.Get(ContextJDOauthAppKey)
	if !exists || oauthAppInterface == nil {
		c.JSON(http.StatusOK, commProto.NewCommRsp(460066, "invalid Source Header"))
		return
	}
	oauthApp := oauthAppInterface.(*config.JDOauthAppInfo)
	appKey := oauthApp.AppKey
	secretKey := oauthApp.SecretKey
	result := &proto.ThirdPartyDetectTextResult{
		SensitiveWords: []string{},
	}
	detectSuccess := true // 成功检测标识
	isCache := false      // 是否使用的缓存标识
	rc := goredis.GetClientV2()
	defer func() {
		if detectSuccess && !isCache {
			if err := cacheDetectTextResult(model.JDDetectTextResult{
				Text:           text,
				TextMd5:        utils.Md5(text),
				Sensitive:      len(result.SensitiveWords) > 0,
				SensitiveWords: strings.Join(result.SensitiveWords, ","),
			}); err != nil {
				logger.CtxLog(c).Errorf("jdDetectText cacheDetectTextResult fail, requestId:%v, err:%v",
					requestId, err)
			}
			for _, sensitiveWord := range result.SensitiveWords {
				key := fmt.Sprintf(JdSensitiveWordsKeyFmt, utils.GetNameByRunEnv())
				if err := rc.ZIncrBy(c, key, 1, sensitiveWord).Err(); err != nil {
					logger.CtxLog(c).Errorf("jdDetectText defer ZIncrBy sensitiveWords fail, requestId:%v, err:%v",
						requestId, err)
				}
			}
		}
	}()
	switch mode {
	case enum.CACHE:
		isCache = true
		// 使用缓存检测
		detectTextResult, err := hitByCacheDetectTextResult(utils.Md5(text))
		if err != nil {
			logger.CtxLog(c).Errorf("jdDetectText hitByCacheDetectTextResult fail, cost:%v, requestId:%v, err:%v",
				time.Since(nowTime), requestId, err)
		} else if detectTextResult != nil && detectTextResult.ID > 0 {
			if len(detectTextResult.SensitiveWords) > 0 {
				result.SensitiveWords = strings.Split(detectTextResult.SensitiveWords, ",")
			}
		}
		logger.CtxLog(c).Infof("jdDetectText hitByCacheDetectTextResult success, cost:%v, requestId:%v",
			time.Since(nowTime), requestId)
		c.JSON(http.StatusOK, commProto.NewSuccessRsp(result))
		return
	case enum.FORCE:
		jdDetectTextReq := &proto.JDDetectTextReq{
			Text: text,
		}
		jdDetectTextRsp := &proto.JDDetectTextRsp{}
		params := GenJDOpenApiParamsStr(jdDetectTextReq, appKey, secretKey, oauthToken.AccessToken,
			JDTextRedLineDetectMethod)
		jdDetectTextUrl := fmt.Sprintf(JDOpenAPICommURLFmt, params)
		if err := httpGet(jdDetectTextUrl, &jdDetectTextRsp); err != nil {
			detectSuccess = false
			logger.CtxLog(c).Errorf("jdDetectText call uri[%v] fail, cost:%v, requestId:%v, err:%v",
				jdDetectTextUrl, time.Since(nowTime), requestId, err)
			c.JSON(http.StatusOK, commProto.NewCommRsp(500001, "服务繁忙"))
			return
		} else if jdDetectTextRsp.ErrorResponse != nil {
			detectSuccess = false
			logger.CtxLog(c).Errorf("jdDetectText call uri[%v] fail, cost:%v, requestId:%v, rsp:%v",
				jdDetectTextUrl, time.Since(nowTime), requestId, jdDetectTextRsp)
			c.JSON(http.StatusOK,
				commProto.NewCommRsp(500002, "服务繁忙"))
			return
		}
		logger.CtxLog(c).Infof("jdDetectText call uri[%v] success, cost:%v, requestId:%v, rsp:%v",
			jdDetectTextUrl, time.Since(nowTime), requestId, jdDetectTextRsp)
		if !jdDetectTextRsp.JingdongDetectionTextRedLineDetectResponse.TextDetectResult.Sensitive {
			c.JSON(http.StatusOK, commProto.NewSuccessRsp(result))
			return
		}
		// 组装响应结果
		sensitiveWordsStr := jdDetectTextRsp.JingdongDetectionTextRedLineDetectResponse.TextDetectResult.SensitiveWords
		result.SensitiveWords = strings.Split(sensitiveWordsStr, ",")
		c.JSON(http.StatusOK, commProto.NewSuccessRsp(result))
		return
	default:
		// 使用缓存检测
		detectTextResult, err := hitByCacheDetectTextResult(utils.Md5(text))
		if err != nil {
			logger.CtxLog(c).Errorf("jdDetectText hitByCacheDetectTextResult fail, requestId:%v, err:%v",
				requestId, err)
		} else if detectTextResult != nil && detectTextResult.ID > 0 {
			isCache = true
			// 组装响应结果
			if len(detectTextResult.SensitiveWords) > 0 {
				result.SensitiveWords = strings.Split(detectTextResult.SensitiveWords, ",")
			}
			c.JSON(http.StatusOK, commProto.NewSuccessRsp(result))
			return
		}
		// 直接通过JD API检测
		jdDetectTextReq := &proto.JDDetectTextReq{
			Text: text,
		}
		jdDetectTextRsp := &proto.JDDetectTextRsp{}
		params := GenJDOpenApiParamsStr(jdDetectTextReq, appKey, secretKey, oauthToken.AccessToken,
			JDTextRedLineDetectMethod)
		jdDetectTextUrl := fmt.Sprintf(JDOpenAPICommURLFmt, params)
		if err := httpGet(jdDetectTextUrl, &jdDetectTextRsp); err != nil {
			detectSuccess = false
			logger.CtxLog(c).Errorf("jdDetectText call uri[%v] fail, cost:%v, requestId:%v, err:%v",
				jdDetectTextUrl, time.Since(nowTime), requestId, err)
			c.JSON(http.StatusOK, commProto.NewCommRsp(500001, "服务繁忙"))
			return
		} else if jdDetectTextRsp.ErrorResponse != nil {
			detectSuccess = false
			logger.CtxLog(c).Errorf("jdDetectText call uri[%v] fail, cost:%v, requestId:%v, rsp:%v",
				jdDetectTextUrl, time.Since(nowTime), requestId, jdDetectTextRsp)
			c.JSON(http.StatusOK,
				commProto.NewCommRsp(500002, "服务繁忙"))
			return
		}
		logger.CtxLog(c).Infof("jdDetectText call uri[%v] success, cost:%v, requestId:%v, rsp:%v",
			jdDetectTextUrl, time.Since(nowTime), requestId, jdDetectTextRsp)
		if !jdDetectTextRsp.JingdongDetectionTextRedLineDetectResponse.TextDetectResult.Sensitive {
			c.JSON(http.StatusOK, commProto.NewSuccessRsp(result))
			return
		}
		// 组装响应结果
		sensitiveWordsStr := jdDetectTextRsp.JingdongDetectionTextRedLineDetectResponse.TextDetectResult.SensitiveWords
		result.SensitiveWords = strings.Split(sensitiveWordsStr, ",")
		c.JSON(http.StatusOK, commProto.NewSuccessRsp(result))
		return
	}
}

// 缓存文本检测结果
func cacheDetectTextResult(newResult model.JDDetectTextResult) error {
	var cacheId uint64
	defer func() {
		if cacheId > 0 {
			key := fmt.Sprintf(JdDetectTextResultKeyFmt, utils.GetNameByRunEnv(), newResult.TextMd5)
			if _, err := goredis.GetClientV2().Set(context.Background(),
				key, cacheId, JdDetectResultKeyExp).Result(); err != nil && err != redis.Nil {
				logger.CtxLog(nil).Errorf("cacheDetectTextResult redis Set fail, err:%v", err)
			}
		}
	}()
	oldResult := model.JDDetectTextResult{}
	if err := oldResult.FindByTextMd5(newResult.TextMd5); err != nil {
		return err
	} else if oldResult.ID != 0 {
		cacheId = oldResult.ID
		oldResult.Sensitive = newResult.Sensitive
		oldResult.SensitiveWords = newResult.SensitiveWords
		return oldResult.Update()
	}
	if err := newResult.Insert(); err != nil {
		return err
	}
	cacheId = newResult.ID
	return nil
}

// 从文本检测结果缓存中获取检测结果
func hitByCacheDetectTextResult(textMd5 string) (*model.JDDetectTextResult, error) {
	// 从redis获取
	key := fmt.Sprintf(JdDetectTextResultKeyFmt, utils.GetNameByRunEnv(), textMd5)
	result, err := goredis.GetClientV2().Get(context.Background(), key).Result()
	if err != nil {
		logger.CtxLog(nil).Errorf("hitByCacheDetectTextResult redis Get fail, err:%v", err)
	}
	// 从mysql获取
	if len(result) > 0 {
		jdDetectTextResult := model.JDDetectTextResult{}
		if err := jdDetectTextResult.FindByTextMd5(textMd5); err != nil {
			return nil, err
		}
		return &jdDetectTextResult, nil
	}
	return nil, nil
}

func ThirdPartyDetectImages(c *gin.Context) {
	nowTime := time.Now()
	requestId := c.GetHeader(global.HeaderRequestID)
	req := &proto.ThirdPartyDetectImagesReq{}
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.CtxLog(c).Errorf("GetThirdPartyLiveGoods bind param fail, requestId:%v, err:%v",
			requestId, err)
		c.JSON(http.StatusOK, commProto.NewCommRsp(100001, "参数异常"))
		return
	}
	oauthToken, ok := thirdPartyOauthCheck(c, req.UserID, req.Platform, req.OpenID)
	if oauthToken == nil || !ok {
		logger.CtxLog(c).Errorf("ThirdPartyDetectImages thirdPartyOauthCheck fail, cost:%v, requestId:%v",
			time.Since(nowTime), requestId)
		return
	}
	// 对 images 去重
	req.Images = strArrayDeduplication(req.Images)
	switch oauthToken.Platform {
	case enum.JD:
		jdDetectImages(c, req.Images, req.Mode, oauthToken, requestId)
		logger.CtxLog(c).Infof("ThirdPartyDetectImages jdDetectImages end, cost:%v, requestId:%v",
			time.Since(nowTime), requestId)
		return
	default:
		c.JSON(http.StatusOK, commProto.NewCommRsp(100002,
			fmt.Sprintf("暂不支持%v平台", oauthToken.Platform)))
		return
	}
}

func jdDetectImages(c *gin.Context, images []string, mode enum.DetectMode,
	oauthToken *model.OauthToken, requestId string) {
	nowTime := time.Now()
	// 查询授权APP的AK/SK
	oauthAppInterface, exists := c.Get(ContextJDOauthAppKey)
	if !exists || oauthAppInterface == nil {
		c.JSON(http.StatusOK, commProto.NewCommRsp(460066, "invalid Source Header"))
		return
	}
	oauthApp := oauthAppInterface.(*config.JDOauthAppInfo)
	appKey := oauthApp.AppKey
	secretKey := oauthApp.SecretKey
	result := &proto.ThirdPartyDetectImagesResult{
		ABImages: []proto.ABImageResult{},
	}
	var err error
	detectSuccess := true // 成功检测标识
	defer func() {
		if detectSuccess {
			detectResultMap := map[string]*model.JDDetectImageResult{}
			for _, imageUrl := range images {
				imgMd5 := utils.Md5(imageUrl)
				detectResultMap[imgMd5] = &model.JDDetectImageResult{
					ImgMd5: imgMd5,
					ImgURL: imageUrl,
				}
			}
			for _, abImage := range result.ABImages {
				imgMd5 := utils.Md5(abImage.URL)
				if abImage.IsCache {
					delete(detectResultMap, imgMd5)
					continue
				}
				detectResultMap[imgMd5].IsRisk = len(abImage.Reason) > 0
				detectResultMap[imgMd5].Reason = abImage.Reason
			}
			var detectResult []*model.JDDetectImageResult
			for _, dr := range detectResultMap {
				detectResult = append(detectResult, dr)
			}
			if err := cacheDetectImagesResult(detectResult); err != nil {
				logger.CtxLog(c).Errorf("jdDetectImages cacheDetectImagesResult fail, requestId:%v, err:%v",
					requestId, err)
			}
		}
	}()
	var imgMd5s []string
	for _, image := range images {
		imgMd5s = append(imgMd5s, utils.Md5(image))
	}
	switch mode {
	case enum.CACHE:
		detectSuccess = false
		// 缓存检测
		cacheResults, err := hitByCacheDetectImagesResult(imgMd5s)
		if err != nil {
			logger.CtxLog(c).Errorf("jdDetectImages hitByCacheDetectImagesResult fail, requestId:%v, err:%v",
				requestId, err)
		} else if cacheResults != nil && len(cacheResults) > 0 {
			// 组装响应结果
			for _, cacheResult := range cacheResults {
				if cacheResult.IsRisk {
					result.ABImages = append(result.ABImages, proto.ABImageResult{
						IsCache: true,
						URL:     cacheResult.ImgURL,
						Reason:  cacheResult.Reason,
					})
				}
			}
		}
		logger.CtxLog(c).Infof("ThirdPartyDetectImages hitByCacheDetectImagesResult success, cost:%v, requestId:%v",
			time.Since(nowTime), requestId)
		c.JSON(http.StatusOK, commProto.NewSuccessRsp(result))
		return
	case enum.FORCE:
		var abResults []proto.ABImageResult
		abResults, detectSuccess, err = jdApiDetectImagesBatch(images, requestId, oauthToken, appKey, secretKey)
		if err != nil {
			c.JSON(http.StatusOK, commProto.NewCommRsp(500011, ServerErrorMsg))
			return
		} else if len(abResults) > 0 {
			result.ABImages = append(result.ABImages, abResults...)
		}
		logger.CtxLog(c).Infof("jdDetectImages result format end, cost:%v, requestId:%v",
			time.Since(nowTime), requestId)
		c.JSON(http.StatusOK, commProto.NewSuccessRsp(result))
		return
	default:
		// 缓存检测
		detectImagesResultCache, err := hitByCacheDetectImagesResult(imgMd5s)
		if err != nil {
			logger.CtxLog(c).Errorf("jdDetectImages hitByCacheDetectImagesResult fail, requestId:%v, err:%v",
				requestId, err)
		}
		// 去重
		cacheResultMap := make(map[string]string)
		for _, cacheResult := range detectImagesResultCache {
			if _, ok := cacheResultMap[cacheResult.ImgMd5]; !ok {
				cacheResultMap[cacheResult.ImgMd5] = cacheResult.ImgURL
				if cacheResult.IsRisk {
					result.ABImages = append(result.ABImages, proto.ABImageResult{
						IsCache: true,
						URL:     cacheResult.ImgURL,
						Reason:  cacheResult.Reason,
					})
				}
			}
		}

		// 筛选出未有结果的数据
		var noCacheImages []string
		for _, image := range images {
			if _, ok := cacheResultMap[utils.Md5(image)]; !ok {
				noCacheImages = append(noCacheImages, image)
			}
		}
		if len(noCacheImages) == 0 {
			c.JSON(http.StatusOK, commProto.NewSuccessRsp(result))
			return
		}
		var abResults []proto.ABImageResult
		abResults, detectSuccess, err = jdApiDetectImagesBatch(noCacheImages, requestId, oauthToken, appKey, secretKey)
		if err != nil {
			c.JSON(http.StatusOK, commProto.NewCommRsp(500011, ServerErrorMsg))
			return
		} else if len(abResults) > 0 {
			result.ABImages = append(result.ABImages, abResults...)
		}
		c.JSON(http.StatusOK, commProto.NewSuccessRsp(result))
		return
	}
}

func jdApiDetectImagesBatch(images []string, requestId string,
	oauthToken *model.OauthToken, appKey, secretKey string) ([]proto.ABImageResult, bool, error) {
	nowTime := time.Now()
	var abResults []proto.ABImageResult
	abResultsMutex := sync.Mutex{}
	// 警告：JD API文档虽然写最大8个图片，但实测最大7个，7个以上时返回结果是异常的
	imagesArray := strArraySplit(images, 7)
	var wg sync.WaitGroup
	for _, imgs := range imagesArray {
		wg.Add(1)
		go func(imgs []string) {
			defer wg.Done()
			curResults, _, err := jdApiDetectImages(imgs, requestId, oauthToken, appKey, secretKey)
			if err != nil {
				logger.CtxLog(nil).Errorf("jdApiDetectImagesBatch jdApiDetectImages fail, requestId:%v, err:%v",
					requestId, err)
			}
			logger.CtxLog(nil).Infof("jdApiDetectImagesBatch jdApiDetectImages success, requestId:%v, curResult:%v",
				requestId, curResults)
			abResultsMutex.Lock()
			abResults = append(abResults, curResults...)
			abResultsMutex.Unlock()
		}(imgs)
	}
	wg.Wait()
	logger.CtxLog(nil).Infof("jdApiDetectImagesBatch success, requestId:%v, cost:%v",
		requestId, time.Since(nowTime))
	return abResults, true, nil
}

func jdApiDetectImages(images []string, requestId string,
	oauthToken *model.OauthToken, appKey, secretKey string) ([]proto.ABImageResult, bool, error) {
	nowTime := time.Now()
	var abResults []proto.ABImageResult
	// 通过JD API检测未查到缓存结果的数据
	jdDetectImgReq := &proto.JDDetectImagesReq{
		DetectItem: detectImageItem,
		ImageUrl:   strings.Join(images, ","),
	}
	jdDetectImgRsp := &proto.JDDetectImagesRsp{}
	params := GenJDOpenApiParamsStr(jdDetectImgReq, appKey, secretKey, oauthToken.AccessToken,
		JDImagesRedLineDetectMethod)
	jdDetectImgUrl := fmt.Sprintf(JDOpenAPICommURLFmt, params)
	if err := httpGet(jdDetectImgUrl, &jdDetectImgRsp); err != nil {
		logger.CtxLog(nil).Errorf("jdDetectImages call uri[%v] fail, cost:%v, requestId:%v, err:%v",
			jdDetectImgUrl, time.Since(nowTime), requestId, err)
		return nil, false, err
	} else if jdDetectImgRsp.ErrorResponse != nil {
		logger.CtxLog(nil).Errorf("jdDetectImages call uri[%v] fail, cost:%v, requestId:%v, rsp:%v",
			jdDetectImgUrl, time.Since(nowTime), requestId, jdDetectImgRsp)
		return nil, false, errors.New(jdDetectImgRsp.ErrorResponse.ZhDesc)
	}
	logger.CtxLog(nil).Infof("jdDetectImages call uri[%v] success, cost:%v, requestId:%v, rsp:%v",
		jdDetectImgUrl, time.Since(nowTime), requestId, jdDetectImgRsp)
	if jdDetectImgRsp.JingdongDetectionImagesRedLineDetectBatchResponse.ReturnType.Safe {
		return abResults, true, nil
	}
	// 组装响应结果
	detectResult := jdDetectImgRsp.JingdongDetectionImagesRedLineDetectBatchResponse.ReturnType.ImagesDetectResult
	for imgUrl, detail := range detectResult {
		if detail.Sensitive || detail.GovFace ||
			detail.PornScore > detail.ReferPornThreshold ||
			detail.VulgarScore > detail.ReferVulgarThreshold {
			abResults = append(abResults, proto.ABImageResult{
				URL:    imgUrl,
				Reason: genABImageReason(detail),
			})
		}
	}
	return abResults, true, nil
}

func hitByCacheDetectImagesResult(imgMd5s []string) ([]*model.JDDetectImageResult, error) {
	dir := model.JDDetectImageResult{}
	s, err := dir.FindByImgMd5s(imgMd5s)
	return s, err
}

func cacheDetectImagesResult(newResults []*model.JDDetectImageResult) error {
	nowTime := time.Now()
	var curImgMd5s []string
	newResultsMap := make(map[string]*model.JDDetectImageResult)
	for _, nr := range newResults {
		curImgMd5s = append(curImgMd5s, nr.ImgMd5)
		newResultsMap[nr.ImgMd5] = nr
	}
	if len(curImgMd5s) == 0 {
		return nil
	}
	dImgR := model.JDDetectImageResult{}
	oldResults, err := dImgR.FindByImgMd5s(curImgMd5s)
	if err != nil {
		return err
	}
	logger.CtxLog(nil).Infof("cacheDetectImagesResult JDDetectImageResult FindByImgMd5s cost:%v", time.Since(nowTime))
	if len(oldResults) == 0 {
		if err := dImgR.InsertBatch(newResults); err != nil {
			return err
		}
		logger.CtxLog(nil).Infof("cacheDetectImagesResult JDDetectImageResult InsertBatch cost:%v", time.Since(nowTime))
		return nil
	}

	for _, or := range oldResults {
		if nr, ok := newResultsMap[or.ImgMd5]; ok {
			if or.IsRisk != nr.IsRisk || or.Reason != nr.Reason {
				or.IsRisk = nr.IsRisk
				or.Reason = nr.Reason
				if err := or.Update(); err != nil {
					return err
				}
			}
			delete(newResultsMap, or.ImgMd5)
		}
	}
	logger.CtxLog(nil).Infof("cacheDetectImagesResult JDDetectImageResult Update success, cost:%v",
		time.Since(nowTime))
	if len(newResultsMap) > 0 {
		var nrs []*model.JDDetectImageResult
		for _, nr := range newResultsMap {
			nrs = append(nrs, nr)
		}
		nr := model.JDDetectImageResult{}
		err := nr.InsertBatch(nrs)
		if err != nil {
			return err
		}
		logger.CtxLog(nil).Infof("cacheDetectImagesResult JDDetectImageResult InsertBatch success, cost:%v",
			time.Since(nowTime))
	}
	return nil
}

func genABImageReason(detail proto.JDImgDetectDetail) string {
	reason := ""
	if detail.Sensitive {
		reason = reason + fmt.Sprintf("含有敏感词:%v ", detail.SensitiveWords)
	}
	if detail.GovFace {
		reason = reason + fmt.Sprintf("涉及政治人物:%v ", detail.Name)
	}
	if detail.PornScore > detail.ReferPornThreshold {
		reason = reason + "涉黄 "
	}
	if detail.VulgarScore > detail.ReferVulgarThreshold {
		reason = reason + "涉及低俗 "
	}
	return reason
}
