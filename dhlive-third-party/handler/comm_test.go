package handler

import (
	"fmt"
	"testing"
	"time"
)

func TestGetJDSkuIDs(t *testing.T) {
	//appKey := "EA881DF95412B4D75119789A2EBB251C"
	//appSecret := "297e4a8fe2a84d318c9b670ee388bc80"
	appKey := "4D731E6A0B1D6F34AC39B7DC88E8EF88"
	appSecret := "0c82afe7382040369c49a2abc3e05764"
	//accessToken := "ec218cccca334fb1aef946bef7a75b0bthjy"
	accessToken := "a66a0d9ce2ac454a80aaef43709a3c05tbko"

	// 根据openId获取用户信息
	//method := "jingdong.user.getUserInfoByOpenId"
	//buyParamJson := `{"openId":"yVjzSRVIWD20SYIq2gEG3GRoGDv50aj3xPRZzw2DN1o"}`

	// 查询商家信息ByPin  -----通
	//method := "jingdong.vender.info.queryByPin"
	//buyParamJson := `{}`

	// 查询商家基本信息  -----通
	//method := "jingdong.seller.vender.info.get"
	//buyParamJson := `{}`

	// 查询店铺信息  -----通
	//method := "jingdong.vender.shop.query"
	//buyParamJson := `{}`

	// 获取我的商品列表  -----调通 但响应结果return_message为"权限不足"
	//method := "jingdong.vc.item.products.find"
	//buyParamJson := `{"offset":1,page_size:10}`

	// 分页获取商品列表  -----不通 appKey=EA881DF95412B4D75119789A2EBB251C无权调用
	//method := "shangling.shangling.product.isv.queryProductInfoByPageForIsv"
	//buyParamJson := `{}`

	// 查询商家SKU列表  -----通
	//method := "jingdong.new.ware.vender.skus.query"
	//buyParamJson := `{"index":1}`

	// skuId查询商品信息  -----通
	//method := "jingdong.sku.read.searchSkuList"
	//buyParamJson := `{"skuId":10099581827407,"field":"wareTitle,jdPrice,logo,skuName,status"}`

	// skuId查询sku详细信息
	//method := "jingdong.sku.read.findSkuById"
	//buyParamJson := `{"skuId":10099581827407,"field":"wareTitle,jdPrice,logo,skuName,status"}`

	//method := "jingdong.ware.read.findWareById"
	//buyParamJson := `{"wareId":10025523554821}`

	// 获取所有对应直播列表信息
	//method := "jingdong.live.liveAppService.livesList.search"
	//buyParamJson := `{pageSize:10,page:1,state:6}`
	//buyParamJson := `{pageSize:10,page:1,state:5}`
	//buyParamJson := `{startPublishTime:"2024-01-12 09:01:01",pageSize:50,endPublishTime:"2024-04-12 12:59:59",page:1,state:5}`

	// 获取直播间信息  -----通
	//method := "jingdong.live.liveAndSku.detail.get"
	//buyParamJson := `{liveId:23008322}`

	// 弹出直播商品卡片
	//method := "jingdong.live.isvOperation.sku.push"
	//buyParamJson := `{"liveId":"23015936","skuId":"10099581827407"}`

	// 获取直播弹幕  -----通
	//method := "jingdong.LiveBulletChatApiService.query"
	//buyParamJson := `{"type":"0",liveId:22702698}`

	// 文本检测
	method := "jingdong.detection.textRedLineDetect"
	//buyParamJson := `{text:""}`
	buyParamJson := `{text:"新建商品 习近平03/25 12毛主席:55:17"}`
	//buyParamJson := `{text:"你好"}`
	//buyParamJson := `{text:"习近平最近去哪里了呢，我们一起来看看吧～，非常怀念毛泽东"}`

	// 图片检测
	//method := "jingdong.detection.imagesRedLineDetectBatch"
	//buyParamJson := `{detectItem:"1,2,3",imageUrl:"http://cpc.people.com.cn/NMediaFile/2022/1023/MAIN202210232118197271589523184.jpg"}`
	//buyParamJson := `{detectItem:[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20],imageUrl:["https://img1.baidu.com/it/u=839003739,**********&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=315","https://game.qidian.com/CpGameHome/Ad/land/serverId/27/name/fmzg?qd_game_key=fmzg1200x60.jpg&qd_dd_p1=140936","https://digital-human-js-cdn.cdn.bcebos.com/digital-human-portal/20240403143734/digital-human-portal/img/shipinpingtai.jpg","http://cpc.people.com.cn/NMediaFile/2022/1023/MAIN202210232118197271589523184.jpg"]}`

	fmt.Println("method: ", method)
	timestamp := time.Now().Format("2006-01-02 15:04:05")
	fmt.Println("timestamp: ", timestamp)
	// 360buy_param_json,access_token,app_key,method,timestamp,v
	params := fmt.Sprintf("360buy_param_json%vaccess_token%vapp_key%vmethod%vtimestamp%vv%v",
		buyParamJson, accessToken, appKey, method, timestamp, "2.0")
	sign := GenJDOpenApiSign(params, appSecret)
	fmt.Println("sign: ", sign)
}
