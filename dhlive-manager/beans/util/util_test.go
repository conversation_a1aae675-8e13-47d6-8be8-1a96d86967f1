package util

import (
	"fmt"
	"testing"
	"time"
)

func TestGetNMonthAgoTime(t *testing.T) {
	nowTime := time.Now()
	n := 1
	nMonthAgoTime := GetNMonthAgoTime(nowTime, n)
	fmt.Println("当前时间：", nowTime.Format("2006-01-02 15:04:05"))
	fmt.Printf("%v个月前的时间：%v\n", n, nMonthAgoTime.Format("2006-01-02 15:04:05"))

	n = 2
	nMonthAgoTime = GetNMonthAgoTime(nowTime, n)
	fmt.Printf("%v个月前的时间：%v\n", n, nMonthAgoTime.Format("2006-01-02 15:04:05"))

	n = 3
	nMonthAgoTime = GetNMonthAgoTime(nowTime, n)
	fmt.Printf("%v个月前的时间：%v\n", n, nMonthAgoTime.Format("2006-01-02 15:04:05"))
}
