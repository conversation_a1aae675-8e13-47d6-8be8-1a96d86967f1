package proto

type Live2DCommSkipPage struct {
	PageNo   int `json:"pageNo" form:"pageNo"`
	PageSize int `json:"pageSize" form:"pageSize"`
}

type Live2DCommRsp struct {
	Code    int  `json:"code"`
	Success bool `json:"success"`
	Message struct {
		Global string `json:"global"`
	} `json:"message"`
}

type Live2DCommPageLimitResult struct {
	PageNo     int `json:"pageNo"`
	PageSize   int `json:"pageSize"`
	TotalCount int `json:"totalCount"`
}

type GetLive2DFiguresReq struct {
	Live2DCommSkipPage
	Tags      []string `json:"tags"`
	Collected bool     `json:"collected"`
	System    bool     `json:"system"`
}

type Live2DFigureResultPage struct {
	PageNo     int                 `json:"pageNo"`
	PageSize   int                 `json:"pageSize"`
	TotalCount int                 `json:"totalCount"`
	Result     []*Live2DFigureInfo `json:"result"`
}

type Live2DFigureResult struct {
	Live2DCommRsp
	Page *Live2DFigureResultPage `json:"page"`
}

type Live2DFigureInfo struct {
	Id               uint64      `json:"id"`
	Source           string      `json:"source"`
	Name             string      `json:"name"`
	Status           string      `json:"status"`
	ResourceLabel    string      `json:"resourceLabel"`
	TemplateImg      string      `json:"templateImg"`
	TemplateVideo    string      `json:"templateVideo"`
	FigureName       string      `json:"figureName"`
	SubmitTime       string      `json:"submitTime"`
	ConsumTime       int         `json:"consumTime"`
	Info             interface{} `json:"info"`
	IsNew            bool        `json:"isNew"`
	LastUsedTime     interface{} `json:"lastUsedTime"`
	Collected        bool        `json:"collected"`
	RecentlyUsed     bool        `json:"recentlyUsed"`
	SystemProvided   bool        `json:"systemProvided"`
	Type             string      `json:"type"`
	ResolutionWidth  int         `json:"resolutionWidth"`
	ResolutionHeight int         `json:"resolutionHeight"`
	VideoUrl         string      `json:"videoUrl"`
	EffectsThumbnail string      `json:"effectsThumbnail"`
	Effects          struct {
		ChromaKey struct {
			Screen    []int   `json:"screen"`
			Weight    float64 `json:"weight"`
			Balance   float64 `json:"balance"`
			ClipBlack float64 `json:"clipBlack"`
			ClipWhite float64 `json:"clipWhite"`
		} `json:"chromaKey"`
		Version int `json:"version"`
	} `json:"effects"`
}

type Live2DGetAppTokenRsp struct {
	Live2DCommRsp
	Result struct {
		AppToken string `json:"appToken"`
	} `json:"result"`
}

type Live2DGenerateFigureVideoReq struct {
	ContinueTexts      []string                               `json:"continueTexts"`
	BackgroundImageUrl string                                 `json:"backgroundImageUrl"`
	VideoName          string                                 `json:"videoName"`
	VideoDuration      int                                    `json:"videoDuration"`
	CharacterConfig    string                                 `json:"characterConfig"`
	RenderParams       *Live2DGenerateFigureVideoRenderParams `json:"renderParams"`
	Selectors          *Live2DGenerateFigureVideoSelectors    `json:"selectors"`
	ResolutionWidth    int                                    `json:"resolutionWidth"`
	ResolutionHeight   int                                    `json:"resolutionHeight"`
}

type Live2DGenerateFigureVideoRenderParams struct {
	X264ParamRcIBitrate int `json:"x264_param_rc_i_bitrate"`
}

type Live2DGenerateFigureVideoSelectors struct {
	Filters []*Live2DGenerateFigureVideoFilter `json:"filters"`
}

type Live2DGenerateFigureVideoFilter struct {
	Operator string `json:"operator"`
	Label    string `json:"label"`
	Data     string `json:"data"`
}

type Live2DGenerateFigureVideoRsp struct {
	Live2DCommRsp
	Result *Live2DGenerateFigureVideoResult `json:"result"`
}

type Live2DGenerateFigureVideoResult struct {
	VideoId                 string `json:"videoId"`
	Text                    string `json:"text"`
	Status                  string `json:"status"`
	DownloadUrl             string `json:"downloadUrl"`
	AudioUrl                string `json:"audioUrl"`
	ErrorCode               int    `json:"errorCode"`
	FailureCause            string `json:"failureCause"`
	ScheduleTimes           int    `json:"scheduleTimes"`
	SubmitTime              string `json:"submitTime"`
	LastScheduleStartTime   string `json:"lastScheduleStartTime"`
	LastScheduleFinishTime  string `json:"lastScheduleFinishTime"`
	VideoGenerateFinishTime string `json:"videoGenerateFinishTime"`
	VideoGenerateCostMillis int    `json:"videoGenerateCostMillis"`
	Thumbnail               string `json:"thumbnail"`
	ResolutionWidth         int    `json:"resolutionWidth"`
	ResolutionHeight        int    `json:"resolutionHeight"`
	VideoName               string `json:"videoName"`
	VideoDuration           int    `json:"videoDuration"`
}

type Live2DGenerateFigureVideoProgressReq struct {
	Ids []string `json:"ids"`
}

type Live2DGenerateFigureVideoProgressRsp struct {
	Live2DCommRsp
	Result []*Live2DGenerateFigureVideoResult `json:"result"`
}

type Live2DAudioPerson struct {
	Id           uint64      `json:"id"`
	UserId       string      `json:"userId"`
	Name         string      `json:"name"`
	Gender       string      `json:"gender"`
	Describe     string      `json:"describe"`
	ThumbnailUrl string      `json:"thumbnailUrl"`
	Per          string      `json:"per"`
	Sort         int         `json:"sort"`
	Config       string      `json:"config"`
	Extra        string      `json:"extra"`
	Format       interface{} `json:"format"`
	Tags         []struct {
		Id       int         `json:"id"`
		Tag      string      `json:"tag"`
		Describe interface{} `json:"describe"`
		Module   interface{} `json:"module"`
		Type     interface{} `json:"type"`
		Visible  bool        `json:"visible"`
		Extra    string      `json:"extra"`
	} `json:"tags"`
	Collect bool `json:"collect"`
}

type Live2DAudioPersonsPage struct {
	PageNo     int                  `json:"pageNo"`
	PageSize   int                  `json:"pageSize"`
	TotalCount int                  `json:"totalCount"`
	Result     []*Live2DAudioPerson `json:"result"`
}

type Live2DGetAudioPersonsRsp struct {
	Live2DCommRsp
	Page Live2DAudioPersonsPage `json:"page"`
}

type Live2DGenerateAudioURLReq struct {
	Tex   string `json:"tex"`
	Per   string `json:"per"`
	Pit   int    `json:"pit"`
	Spd   int    `json:"spd"`
	Vol   int    `json:"vol"`
	Extra string `json:"extra"`
}

type Live2DGenerateAudioURLResult struct {
	Duration int    `json:"duration"`
	Size     int    `json:"size"`
	Url      string `json:"url"`
}

type Live2DGenerateAudioURLRsp struct {
	Live2DCommRsp
	Result *Live2DGenerateAudioURLResult `json:"result"`
}
