package enum

// Platform 平台枚举
type Platform string

const (
	JD Platform = "JING_DONG"
	MT Platform = "MEI_TUAN"
	KS Platform = "KUAI_SHOU"
	DY Platform = "DOU_YIN"
)

// DetectMode 检测模式枚举
type DetectMode string

const (
	MIX   = "mix"
	CACHE = "cache"
	FORCE = "force"
)

type WsMsgAction string // Websocket消息动作事件

const (
	ActionSendHeartBeat WsMsgAction = "SEND_HEART_BEAT" // 发送心跳
	ActionHeartBeat     WsMsgAction = "HEART_BEAT"      // 心跳
	ActionAuthInvalid   WsMsgAction = "AUTH_INVALID"    // 授权过期通知
	ActionErrorInfo     WsMsgAction = "ERROR_INFO"      // 错误信息
	ActionMessage       WsMsgAction = "MESSAGE"         // 实时消息
)

type LiveMsgType string // 直播间消息类型 BARRAGE/ENTER/LIKE/FOLLOW/GIFT

const (
	BarrageLiveMsg LiveMsgType = "BARRAGE" // 弹幕
	EnterLiveMsg   LiveMsgType = "ENTER"   // 进入直播间
	LikeLiveMsg    LiveMsgType = "LIKE"    // 点赞
	FollowLiveMsg  LiveMsgType = "FOLLOW"  // 关注直播间
	GiftLiveMsg    LiveMsgType = "GIFT"    // 送礼物
)

const (
	FigureGenderMale   = "MALE"
	FigureGenderFemale = "FEMALE"
)

const (
	GenerateAbcInit          = "ABC_INIT"
	Live2DFigureVideoSucceed = "SUCCEED"
	Live2DFigureVideoError   = "ERROR"
	Live2DFigureVideoFailed  = "FAILED"
	Live2DText2AudioSucceed  = "SUCCEED"
	Live2DText2AudioError    = "ERROR"
)
