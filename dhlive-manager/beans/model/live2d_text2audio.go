package model

import (
	"acg-ai-go-common/gomysql"
	"acg-ai-go-common/utils"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"time"
)

// Text2Audio 文本转音频表结构
type Text2Audio struct {
	ID         uint64         `json:"id" gorm:"column:id;primarykey;autoIncrement"`
	PersonId   uint64         `json:"personId" gorm:"column:personId;uniqueIndex:person_id_per"`         // 音色ID
	Per        string         `json:"per" gorm:"column:per;type:varchar(255);uniqueIndex:person_id_per"` // 音色
	Config     string         `json:"config" gorm:"column:config;type:varchar(255)"`                     // 语速、音调、音量配置
	Extra      string         `json:"extra" gorm:"column:extra;type:varchar(255)"`                       //
	Gender     string         `json:"gender" gorm:"column:gender;type:varchar(255)"`                     // 性别，（根据性别选择使用的语音）
	Name       string         `json:"name" gorm:"column:name;type:varchar(255)"`                         // 音色中文名
	Tags       string         `json:"tags" gorm:"column:tags;type:varchar(255)"`                         // 标签
	Text       string         `json:"text" gorm:"column:text"`                                           // 文本
	AudioURL   string         `json:"audioUrl" gorm:"column:audioUrl;type:varchar(255)"`                 // 使用的音频地址
	Duration   int            `json:"duration" gorm:"column:duration"`                                   // 音频时长
	Size       int            `json:"size" gorm:"column:size"`                                           // 音频大小
	Status     string         `json:"status" gorm:"column:status;type:varchar(255)"`                     // 视频生成状态
	StartTime  int64          `json:"startTime" gorm:"column:startTime"`                                 // 视频开始生成时间
	FinishTime int64          `json:"finishTime" gorm:"column:finishTime"`                               // 视频完成时间
	Result     string         `json:"result" gorm:"column:result"`                                       // 2D返回的结果JsonString
	CreatedAt  time.Time      `json:"createdAt" gorm:"column:createdAt"`                                 // 创建时间
	UpdatedAt  time.Time      `json:"updatedAt" gorm:"column:updateAt"`                                  // 更新时间
	DeletedAt  gorm.DeletedAt `json:"deletedAt" gorm:"column:deletedAt;index"`                           // 删除时间/标记删除
}

func (t *Text2Audio) TableName() string {
	return "text2audio_v1"
}

func (t *Text2Audio) Insert() error {
	// 创建数据
	return gomysql.DB.Clauses(clause.OnConflict{DoNothing: true}).Create(&t).Error
}

func (t *Text2Audio) InsertBatch(results []*Text2Audio) error {
	// 创建数据
	return gomysql.DB.Clauses(clause.OnConflict{DoNothing: true}).CreateInBatches(&results, len(results)).Error
}

func (t *Text2Audio) Update() error {
	// 创建数据
	return gomysql.DB.Save(&t).Error
}

func (t *Text2Audio) FindByID(id uint64) error {
	err := gomysql.DB.Where("id = ?", id).Find(&t).Error
	if err == nil || err == gorm.ErrRecordNotFound {
		return nil
	}
	return err
}

func (t *Text2Audio) FindByPersonID(personId uint64) error {
	err := gomysql.DB.Order("id desc").Where("personId = ?", personId).First(&t).Error
	if err == nil || err == gorm.ErrRecordNotFound {
		return nil
	}
	return err
}

func (t *Text2Audio) FindByStatus(status []string) ([]*Text2Audio, error) {
	var figureVideos []*Text2Audio
	err := gomysql.DB.Where("status in ?", status).Find(&figureVideos).Error
	return figureVideos, err
}

func (t *Text2Audio) FindByExcludeStatus(excludeStatus []string) ([]*Text2Audio, error) {
	var figureVideos []*Text2Audio
	err := gomysql.DB.Where("status not in ?", excludeStatus).Find(&figureVideos).Error
	return figureVideos, err
}

func (t *Text2Audio) FindByExcludeStatusAndGender(excludeStatus []string, gender string) ([]*Text2Audio, error) {
	var figureVideos []*Text2Audio
	query := gomysql.DB.Model(&Text2Audio{})
	if len(excludeStatus) > 0 {
		query.Where("status not in ?", excludeStatus)
	}
	if len(gender) > 0 {
		query.Where("gender = ?", gender)
	}
	err := query.Find(&figureVideos).Error
	return figureVideos, err
}

func (t *Text2Audio) Search(excludeStatus []string, gender string, pageNum, pageSize int64) ([]*Text2Audio, error) {
	var figureVideos []*Text2Audio
	query := gomysql.DB.Model(&Text2Audio{})
	if len(excludeStatus) > 0 {
		query.Where("status not in ?", excludeStatus)
	}
	if len(gender) > 0 {
		query.Where("gender = ?", gender)
	}
	skip, limit := utils.ConvertPageNumSize2Skip(pageNum, pageSize)
	err := query.Find(&figureVideos).Offset(int(skip)).Limit(int(limit)).Error
	return figureVideos, err
}
