package model

import (
	"acg-ai-go-common/gomysql"
	"acg-ai-go-common/utils"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"time"
)

// FigureVideo 人像视频表结构
type FigureVideo struct {
	ID            uint64         `json:"id" gorm:"column:id;primarykey;autoIncrement"`
	FigureID      uint64         `json:"figureId" gorm:"column:figureId;uniqueIndex:figure_id_name"`                       // 人像ID
	FigureName    string         `json:"figureName" gorm:"column:figureName;type:varchar(255);uniqueIndex:figure_id_name"` // 人像名称
	Name          string         `json:"name" gorm:"column:name;type:varchar(255)"`                                        // 中文名称
	Gender        string         `json:"gender" gorm:"column:gender;type:varchar(255)"`                                    // 性别，（根据性别选择使用的语音）
	VideoID       string         `json:"videoId" gorm:"column:videoId;type:varchar(255)"`                                  // 视频ID
	VideoURL      string         `json:"videoUrl" gorm:"column:videoUrl;type:varchar(255)"`                                // 视频链接地址
	Thumbnail     string         `json:"thumbnail" gorm:"column:thumbnail;type:varchar(255)"`                              // 视频缩略图
	VideoDuration int            `json:"videoDuration" gorm:"column:videoDuration"`                                        // 视频时长
	AudioURL      string         `json:"audioUrl" gorm:"column:audioUrl;type:varchar(255)"`                                // 使用的音频地址
	Status        string         `json:"status" gorm:"column:status;type:varchar(255)"`                                    // 视频生成状态
	StartTime     int64          `json:"startTime" gorm:"column:startTime"`                                                // 视频开始生成时间
	FinishTime    int64          `json:"finishTime" gorm:"column:finishTime"`                                              // 视频完成时间
	Result        string         `json:"result" gorm:"column:result"`                                                      // 2D返回的结果JsonString
	CreatedAt     time.Time      `json:"createdAt" gorm:"column:createdAt"`                                                // 创建时间
	UpdatedAt     time.Time      `json:"updatedAt" gorm:"column:updateAt"`                                                 // 更新时间
	DeletedAt     gorm.DeletedAt `json:"deletedAt" gorm:"column:deletedAt;index"`                                          // 删除时间/标记删除
}

func (f *FigureVideo) TableName() string {
	return "figure_video_v1"
}

func (f *FigureVideo) Insert() error {
	// 创建数据
	return gomysql.DB.Clauses(clause.OnConflict{DoNothing: true}).Create(&f).Error
}

func (f *FigureVideo) InsertBatch(results []*FigureVideo) error {
	// 创建数据
	return gomysql.DB.Clauses(clause.OnConflict{DoNothing: true}).CreateInBatches(&results, len(results)).Error
}

func (f *FigureVideo) Update() error {
	// 创建数据
	return gomysql.DB.Save(&f).Error
}

func (f *FigureVideo) FindByID(id uint64) error {
	err := gomysql.DB.Where("id = ?", id).Find(&f).Error
	if err == nil || err == gorm.ErrRecordNotFound {
		return nil
	}
	return err
}

func (f *FigureVideo) FindByFigureID(figureId uint64) error {
	err := gomysql.DB.Order("id desc").Where("figureId = ?", figureId).First(&f).Error
	if err == nil || err == gorm.ErrRecordNotFound {
		return nil
	}
	return err
}

func (f *FigureVideo) FindByFigureName(figureName string) error {
	err := gomysql.DB.Order("id desc").Where("figureName = ?", figureName).First(&f).Error
	if err == nil || err == gorm.ErrRecordNotFound {
		return nil
	}
	return err
}

func (f *FigureVideo) FindByStatus(status []string) ([]*FigureVideo, error) {
	var figureVideos []*FigureVideo
	err := gomysql.DB.Where("status in ?", status).Find(&figureVideos).Error
	return figureVideos, err
}

func (f *FigureVideo) FindByStatusAndTime(status []string, startTime, endTime time.Time) ([]*FigureVideo, error) {
	var figureVideos []*FigureVideo
	err := gomysql.DB.Where("status in ? and updateAt BETWEEN ? and ?",
		status, startTime, endTime).Find(&figureVideos).Error
	return figureVideos, err
}

func (f *FigureVideo) FindByExcludeStatus(excludeStatus []string) ([]*FigureVideo, error) {
	var figureVideos []*FigureVideo
	err := gomysql.DB.Where("status not in ?", excludeStatus).Find(&figureVideos).Error
	return figureVideos, err
}

func (f *FigureVideo) FindByExcludeStatusAndGender(excludeStatus []string, gender string) ([]*FigureVideo, error) {
	var figureVideos []*FigureVideo
	query := gomysql.DB.Model(&FigureVideo{})
	if len(excludeStatus) > 0 {
		query.Where("status not in ?", excludeStatus)
	}
	if len(gender) > 0 {
		query.Where("gender = ?", gender)
	}
	err := query.Find(&figureVideos).Error
	return figureVideos, err
}

func (f *FigureVideo) Search(excludeStatus []string, gender string, pageNum, pageSize int64) ([]*FigureVideo, error) {
	var figureVideos []*FigureVideo
	query := gomysql.DB.Model(&FigureVideo{})
	if len(excludeStatus) > 0 {
		query.Where("status not in ?", excludeStatus)
	}
	if len(gender) > 0 {
		query.Where("gender = ?", gender)
	}
	skip, limit := utils.ConvertPageNumSize2Skip(pageNum, pageSize)
	err := query.Find(&figureVideos).Offset(int(skip)).Limit(int(limit)).Error
	return figureVideos, err
}
