package main

import (
	"acg-ai-go-common/global"
	"acg-ai-go-common/gomysql"
	"acg-ai-go-common/goredis"
	"acg-ai-go-common/logger"
	"acg-ai-go-common/server"
	"dhlive-manager/beans/model"
	config "dhlive-manager/conf"
	"dhlive-manager/routers"
	"github.com/BurntSushi/toml"
	"log"
)

func main() {
	// 初始化公共配置
	server.InitGlobalSetting()
	if _, err := toml.DecodeFile(global.ConfFilePath, config.LocalConfig); err != nil {
		log.Panicf("本地个性化配置加载失败: {%v}", err)
	}

	// 初始化redis
	goredis.InitRedisV2()
	// 初始化mysql
	gomysql.InitDB(global.ServerSetting.MysqlSetting)
	// 初始化数据库表
	if err := model.InitMysqlDBTable(); err != nil {
		log.Panicf("InitMysqlDBTable fail, err:%v", err)
		return
	}

	logger.SetLogger()
	// 初始化路由
	routers.InitRouter()
	// 启动服务
	server.Run(routers.GinRouter)
}
