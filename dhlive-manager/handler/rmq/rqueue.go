package rmq

import (
	"acg-ai-go-common/goredis"
	"context"
	"github.com/go-redis/redis/v8"
)

// AddTask2Queue 添加任务到队列
func AddTask2Queue(queueName, taskId string) bool {
	ctx := context.Background()
	// 先删除再添加，避免一个任务多次添加到队列中
	if !DelTask2Queue(queueName, taskId) {
		return false
	}
	err := goredis.GetClientV2().LPush(ctx, queueName, taskId).Err()
	return err == nil || err == redis.Nil
}

// GetTask2Queue 从队头取出一个任务
func GetTask2Queue(queueName string) (string, bool) {
	ctx := context.Background()
	result, err := goredis.GetClientV2().RPop(ctx, queueName).Result()
	return result, err == nil || err == redis.Nil
}

// DelTask2Queue 删除队列中的任务
func DelTask2Queue(queueName, taskId string) bool {
	ctx := context.Background()
	err := goredis.GetClientV2().LRem(ctx, queueName, 0, taskId).Err()
	return err == nil || err == redis.Nil
}
