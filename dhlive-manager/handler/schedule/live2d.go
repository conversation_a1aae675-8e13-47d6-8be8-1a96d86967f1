package schedule

import (
	"acg-ai-go-common/goredis"
	"acg-ai-go-common/logger"
	"acg-ai-go-common/utils"
	"acg-ai-go-common/utils/httputil"
	"bytes"
	"context"
	"dhlive-manager/beans/enum"
	"dhlive-manager/beans/model"
	"dhlive-manager/beans/proto"
	"dhlive-manager/beans/util"
	"encoding/json"
	"errors"
	"fmt"
	"io/ioutil"
	"math"
	"net/http"
	"time"
)

const (
	autoGenerateFigureVideoTimer           = 5 * time.Second
	autoGenerateFigureVideoScheduleLockKey = "dhlive-manager:schedules:autoGenerateFigureVideo"
	live2dManAudioURL                      = "http://star-light-lite.bj.bcebos.com/974921ab-7131-4a9a-97e8-3cc90dfae89f/tts/tts-qdztyam62jy3hwgw.wav"
	live2dManAudioDuration                 = 203.76
	live2dWomanAudioURL                    = "http://star-light-lite.bj.bcebos.com/974921ab-7131-4a9a-97e8-3cc90dfae89f/tts/tts-qdyng3rb1vndaek6.wav"
	live2dWomanAudioDuration               = 205.39
	live2dQueryFiguresURL                  = "https://digitalhuman.baidu.com/live2d/api/digitalhuman/console/v1/star/figure/query"
	live2dGetAppTokenURLFmt                = "https://digitalhuman.baidu.com/live2d/api/external/digitalhuman/v2/apps/%v/tokens?expire=%v" // 示例：https://digitalhuman.baidu.com/live2d/api/external/digitalhuman/v2/apps/i-qdtrf6d4sqahj/tokens?expire=2024-04-26T08:51:24.635Z
	live2dGenerateFigureVideo              = "https://digitalhuman.baidu.com/live2d/api/digitalhuman/starlight/v1/video"
	live2dBackgroundImageURL               = "https://digital-human-artiste.bj.bcebos.com/d2b745ff-f17a-4d9d-8801-a658da44a1f2/m-pfbf9mi4u56t03rqbz.png"
	live2dGetVideoProgress                 = "https://digitalhuman.baidu.com/live2d/api/digitalhuman/console/v1/video/progress/batch"
	live2dGetAudioPersons                  = "https://digitalhuman.baidu.com/live2d/api/digitalhuman/starlight/v1/text2audio/persons"
	live2dGenerateAudioURL                 = "https://digitalhuman.baidu.com/live2d/api/digitalhuman/starlight/v1/text2audio/url"
	live2dToken                            = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************************************************************************************************.BUXvJ4OZPjSzOvVXseT5hWBG7H3egZqpf2gs55VZAh4"
	live2dAppId                            = "i-qdtrf6d4sqahj"
)

func init() {
	//go schedules()
}

// 美团直播链接更新
func schedules() {
	ticker := time.NewTicker(autoGenerateFigureVideoTimer)
	defer ticker.Stop()
	for {
		select {
		case <-ticker.C:
			autoGenerateFigureVideoTask()
		}
	}
}

// 自动生成人像视频任务
func autoGenerateFigureVideoTask() {
	nowTime := time.Now()
	logger.Log.Infof("autoGenerateFigureVideo start, time: %v", nowTime.Format("2006-01-02 15:04:05"))
	token := live2dToken
	// TODO 校验当前任务需要生成的任务数量
	if err := getLive2DAllFiguresAndInsert(token, enum.FigureGenderMale); err != nil {
		logger.Log.Errorf("autoGenerateFigureVideoTask getLive2DAllFiguresAndInsert fail, gender:%v, cost:%v, err:%v",
			enum.FigureGenderMale, time.Since(nowTime), err)
		return
	}
	if err := getLive2DAllFiguresAndInsert(token, enum.FigureGenderFemale); err != nil {
		logger.Log.Errorf("autoGenerateFigureVideoTask getLive2DAllFiguresAndInsert fail, gender:%v, cost:%v, err:%v",
			enum.FigureGenderFemale, time.Since(nowTime), err)
		return
	}
	if err := getLive2DAllFiguresAndInsert(token, ""); err != nil {
		logger.Log.Errorf("autoGenerateFigureVideoTask getLive2DAllFiguresAndInsert fail, gender:%v, cost:%v, err:%v",
			"all", time.Since(nowTime), err)
		return
	}
	// TODO 根据任务数量设置任务锁时间
	lockExp := 5 * time.Minute
	var figureVideo *model.FigureVideo
	figureVideos, err := figureVideo.FindByExcludeStatus([]string{"SUCCESS"})
	if err != nil {
		logger.Log.Errorf("autoGenerateFigureVideoTask FindByExcludeStatus fail, cost:%v, err:%v",
			time.Since(nowTime), err)
		return
	}
	if len(figureVideos) > 0 {
		lockExp = time.Duration(len(figureVideos)) * lockExp
	}
	// 加锁
	lockKey := utils.GetNameByRunEnv() + ":" + autoGenerateFigureVideoScheduleLockKey
	ctx := context.Background()
	ip, _ := utils.FindFirstNonLoopbackIP()
	if locked, err := goredis.GetClientV2().SetNX(ctx, lockKey, ip, lockExp).Result(); err != nil {
		logger.Log.Errorf("autoGenerateFigureVideo lock fail, err: %v", err)
		return
	} else if !locked {
		val := goredis.GetClientV2().Get(ctx, lockKey).Val()
		logger.Log.Infof("autoGenerateFigureVideo lock fail, lock in IP: %v", val)
		return
	}
	generateCount := 0
	defer func() {
		logger.Log.Infof("autoGenerateFigureVideo end, generateCount:%v, cost: %v",
			generateCount, time.Since(nowTime))
		if err := goredis.GetClientV2().Del(ctx, lockKey).Err(); err != nil {
			logger.Log.Errorf("autoGenerateFigureVideo unlock fail, err: %v", err)
		}
	}()
	// TODO 开始自动生成
	//generateCount = generateFigureVideoBatch(token, figures)
}

func getLive2DAllFiguresAndInsert(token, gender string) error {
	var pageNo = 1
	var pageSize = 50
	for {
		resultPage, err := getLive2DFigures(token, gender, pageNo, pageSize)
		if err != nil {
			logger.Log.Errorf("getLive2DAllFigures getLive2DFigures fail, err:%v", err)
			return err
		}
		var figureVideos []*model.FigureVideo
		for _, figureInfo := range resultPage.Result {
			figureVideo := &model.FigureVideo{
				FigureID:   figureInfo.Id,
				FigureName: figureInfo.FigureName,
				Name:       figureInfo.Name,
				Gender:     gender,
				Result:     "{}",
				Status:     enum.GenerateAbcInit,
			}
			switch gender {
			case enum.FigureGenderMale:
				figureVideo.VideoDuration = int(math.Round(live2dManAudioDuration))
				figureVideo.AudioURL = live2dManAudioURL
			case enum.FigureGenderFemale:
				figureVideo.VideoDuration = int(math.Round(live2dWomanAudioDuration))
				figureVideo.AudioURL = live2dWomanAudioURL
			}
			figureVideos = append(figureVideos, figureVideo)
		}
		var figureVideo *model.FigureVideo
		if err := figureVideo.InsertBatch(figureVideos); err != nil {
			logger.Log.Errorf("getLive2DAllFigures InsertBatch fail, err:%v", err)
			return err
		}
		if pageNo*pageSize >= resultPage.TotalCount {
			break
		}
		pageNo++
	}
	return nil
}

func getLive2DFigures(token, gender string, pageNo, pageSize int) (*proto.Live2DFigureResultPage, error) {
	now := time.Now()
	headers := map[string]string{
		"Content-Type": "application/json",
		"Token":        token,
	}
	var figuresRsp *proto.Live2DFigureResult
	req := &proto.GetLive2DFiguresReq{
		Live2DCommSkipPage: proto.Live2DCommSkipPage{
			PageNo:   pageNo,
			PageSize: pageSize,
		},
		Collected: false,
		System:    true,
	}
	if len(gender) > 0 {
		req.Tags = []string{gender}
	}
	if err := httputil.PostJsonV2(live2dQueryFiguresURL, headers, req, &figuresRsp); err != nil {
		logger.Log.Errorf("getLive2DFigures call url[%v] fail, err:%v", live2dQueryFiguresURL, err)
		return nil, err
	} else if figuresRsp.Code > 0 || !figuresRsp.Success {
		logger.Log.Errorf("getLive2DFigures call url[%v] fail, gender:%v, code:%v, msg:%v",
			live2dQueryFiguresURL, gender, figuresRsp.Code, figuresRsp.Message.Global)
		return nil, errors.New(figuresRsp.Message.Global)
	}
	logger.Log.Infof("getLive2DFigures call url[%v] success, cost:%v, gender:%v, pageNo:%v, pageSize:%v, totalCount:%v",
		live2dQueryFiguresURL, time.Since(now), gender, pageNo, pageSize, figuresRsp.Page.TotalCount)
	return figuresRsp.Page, nil
}

func getLive2DAppToken(token, appId string) (string, error) {
	now := time.Now()
	headers := map[string]string{
		"Content-Type": "application/json",
		"Token":        token,
	}
	expire := time.Now().Add(time.Hour).Format("2006-01-02T15:04:05.000Z")
	reqURL := fmt.Sprintf(live2dGetAppTokenURLFmt, appId, expire)
	var rsp *proto.Live2DGetAppTokenRsp
	if err := httputil.GetV2(reqURL, headers, &rsp); err != nil {
		logger.Log.Errorf("getLive2DAppToken call url[%v] fail, err:%v", reqURL, err)
		return "", err
	} else if rsp.Code > 0 || !rsp.Success {
		logger.Log.Errorf("getLive2DAppToken call url[%v] fail, appId:%v, code:%v, msg:%v",
			reqURL, appId, rsp.Code, rsp.Message.Global)
		return "", errors.New(rsp.Message.Global)
	}
	logger.Log.Infof("getLive2DAppToken call url[%v] success, cost:%v, appToken:%v",
		reqURL, time.Since(now), rsp.Result.AppToken)
	appToken := fmt.Sprintf("BDH %v/%v/%v", appId, rsp.Result.AppToken, expire)
	return appToken, nil
}

// 生成人像视频
func generateLive2DFigureVideo(token, appToken string, figureVideo *model.FigureVideo) error {
	now := time.Now()
	headers := map[string]string{
		"Content-Type":  "application/json",
		"Token":         token,
		"Authorization": appToken,
	}
	var rsp *proto.Live2DGenerateFigureVideoRsp
	req := &proto.Live2DGenerateFigureVideoReq{
		ContinueTexts: []string{
			fmt.Sprintf(`<speak><audio src="%v"/></speak>`, figureVideo.AudioURL),
			"<finishVideoTask></finishVideoTask>",
		},
		BackgroundImageUrl: live2dBackgroundImageURL,
		VideoName:          fmt.Sprintf("%v-Auto", figureVideo.Name),
		CharacterConfig:    fmt.Sprintf(`{"figure":{"name":"%v"}}`, figureVideo.FigureName),
		RenderParams: &proto.Live2DGenerateFigureVideoRenderParams{
			X264ParamRcIBitrate: 5000000,
		},
		Selectors: &proto.Live2DGenerateFigureVideoSelectors{
			Filters: []*proto.Live2DGenerateFigureVideoFilter{
				{
					Operator: "MATCH",
					Label:    "subtype",
					Data:     "2D_LITE_VIS",
				},
			},
		},
		ResolutionWidth:  1080,
		ResolutionHeight: 1920,
	}
	if err := httputil.PostJsonV2(live2dGenerateFigureVideo, headers, req, &rsp); err != nil {
		logger.Log.Errorf("generateLive2DFigureVideo call url[%v] fail, err:%v", live2dGenerateFigureVideo, err)
		return err
	} else if rsp.Code > 0 || !rsp.Success {
		logger.Log.Errorf("generateLive2DFigureVideo call url[%v] fail, gender:%v, code:%v, msg:%v",
			live2dGenerateFigureVideo, figureVideo.Gender, rsp.Code, rsp.Message.Global)
		return errors.New(rsp.Message.Global)
	}
	logger.Log.Infof("generateLive2DFigureVideo call url[%v] success, cost:%v",
		live2dGenerateFigureVideo, time.Since(now))
	// 更新figureVideo数据
	figureVideo.VideoID = rsp.Result.VideoId
	figureVideo.Status = rsp.Result.Status
	figureVideo.StartTime = now.Unix()
	if err := figureVideo.Update(); err != nil {
		logger.Log.Errorf("generateLive2DFigureVideo Update figureVideo fail, err:%v", err)
		return err
	}
	return nil
}

func queryLive2DVideoGenerateProgress(token, appToken string, figureVideo *model.FigureVideo) (bool, error) {
	now := time.Now()
	headers := map[string]string{
		"Content-Type":  "application/json",
		"Token":         token,
		"Authorization": appToken,
	}
	videoIds := []string{figureVideo.VideoID}
	var rsp *proto.Live2DGenerateFigureVideoProgressRsp
	req := &proto.Live2DGenerateFigureVideoProgressReq{Ids: videoIds}
	if err := httputil.PostJsonV2(live2dGetVideoProgress, headers, req, &rsp); err != nil {
		logger.Log.Errorf("queryLive2DVideoGenerateProgress call url[%v] fail, err:%v",
			live2dGetVideoProgress, err)
		return false, err
	} else if rsp.Code > 0 || !rsp.Success {
		logger.Log.Errorf("queryLive2DVideoGenerateProgress call url[%v] fail, code:%v, msg:%v",
			live2dGetVideoProgress, rsp.Code, rsp.Message.Global)
		return false, errors.New(rsp.Message.Global)
	}
	logger.Log.Infof("queryLive2DVideoGenerateProgress call url[%v] success, cost:%v",
		live2dGetVideoProgress, time.Since(now))
	result := rsp.Result[0]
	isUpdate := false
	isEnd := false
	if result.Status != figureVideo.Status {
		figureVideo.Status = result.Status
		isUpdate = true
	}
	if result.Status == enum.Live2DFigureVideoSucceed {
		figureVideo.VideoURL = result.DownloadUrl
		figureVideo.Thumbnail = result.Thumbnail
		figureVideo.FinishTime = time.Now().Unix()
		isUpdate = true
	}
	if isUpdate {
		figureVideo.Result = util.Struct2JsonStr(result)
		if err := figureVideo.Update(); err != nil {
			logger.Log.Errorf("queryLive2DVideoGenerateProgress Update figureVideo fail, err:%v", err)
			return false, err
		}
	}
	if figureVideo.Status == enum.Live2DFigureVideoSucceed ||
		figureVideo.Status == enum.Live2DFigureVideoError ||
		figureVideo.Status == enum.Live2DFigureVideoFailed {
		isEnd = true
	}
	return isEnd, nil
}

func getLive2DAudioPersons(token string) ([]*proto.Live2DAudioPerson, error) {
	headers := map[string]string{
		"Content-Type": "application/json",
		"Token":        token,
	}
	var rsp *proto.Live2DGetAudioPersonsRsp
	if err := httputil.GetV2(live2dGetAudioPersons, headers, &rsp); err != nil {
		logger.Log.Errorf("getLive2DAudioPersons call[%v] fail, err:%v", live2dGetAudioPersons, err)
		return nil, err
	} else if rsp.Code > 0 || !rsp.Success {
		logger.Log.Errorf("getLive2DAudioPersons call[%v] fail, rsp:%v", live2dGetAudioPersons, rsp)
		return nil, err
	}
	return rsp.Page.Result, nil
}

type PerConf struct {
	Pit int `json:"pit"`
	Spd int `json:"spd"`
	Vol int `json:"vol"`
}

func generateLive2DAudioURL(token string,
	text string, peron *proto.Live2DAudioPerson) (*proto.Live2DGenerateAudioURLResult, error) {
	nowTime := time.Now()
	headers := map[string]string{
		"Content-Type": "application/json",
		"Token":        token,
	}
	var perConf *PerConf
	_ = json.Unmarshal([]byte(peron.Config), &perConf)
	var rsp *proto.Live2DGenerateAudioURLRsp
	req := &proto.Live2DGenerateAudioURLReq{
		Tex:   text,
		Per:   peron.Per,
		Pit:   perConf.Pit,
		Spd:   perConf.Spd,
		Vol:   perConf.Vol,
		Extra: peron.Extra,
	}
	if err := postJson(live2dGenerateAudioURL, headers, req, &rsp); err != nil {
		logger.Log.Errorf("generateLive2DAudioURL call[%v] fail, err:%v", live2dGenerateAudioURL, err)
		return nil, err
	} else if rsp.Code > 0 || !rsp.Success {
		logger.Log.Errorf("generateLive2DAudioURL call[%v] fail, rsp:%v", live2dGenerateAudioURL, rsp)
		return nil, errors.New(rsp.Message.Global)
	}
	logger.Log.Infof("generateLive2DAudioURL call[%v] success, cost:%v, rsp:%v",
		live2dGenerateAudioURL, time.Since(nowTime), rsp)
	return rsp.Result, nil
}

func postJson(URL string, headers map[string]string, data interface{}, rsp interface{}) error {
	bodyBytes, _ := json.Marshal(data)
	body := bytes.NewBuffer(bodyBytes)
	request, err := http.NewRequest(http.MethodPost, URL, body)
	for k, v := range headers {
		request.Header.Set(k, v)
	}
	// 超时时间：10秒
	client := &http.Client{Timeout: 5 * time.Minute}
	resp, err := client.Do(request)
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	result, _ := ioutil.ReadAll(resp.Body)
	return json.Unmarshal(result, rsp)
}
