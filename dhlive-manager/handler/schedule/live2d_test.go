package schedule

import (
	"acg-ai-go-common/global"
	"acg-ai-go-common/gomysql"
	"acg-ai-go-common/goredis"
	"acg-ai-go-common/utils"
	"dhlive-manager/beans/enum"
	"dhlive-manager/beans/model"
	"dhlive-manager/beans/util"
	"encoding/csv"
	"fmt"
	"log"
	"os"
	"path/filepath"
	"sync"
	"testing"
	"time"
)

func initTest() {
	global.ServerSetting = &global.ServerConfig{
		RunEnv: global.DevEnv,
		MysqlSetting: &global.MysqlSetting{
			HostPortSetting: global.HostPortSetting{
				Host: "127.0.0.1",
				Port: 3306,
			},
			UserNamePwdSetting: global.UserNamePwdSetting{
				Username: "root",
				Password: "123456",
			},
			Database: "dhlive_third_platform",
		},
		RedisSetting: &global.RedisSetting{
			Addr:     "*************:8379",
			Username: "",
			Password: "",
		},
	}
	gomysql.InitDB(global.ServerSetting.MysqlSetting)
	goredis.InitRedisV2()
	if err := model.InitMysqlDBTable(); err != nil {
		log.Panic(err)
	}
}

var testToken = live2dToken
var testText = `哈喽宝宝们，欢迎来到今天的直播间，今天我们的直播活动筹备了很久，为大家一次性准备了包括吃喝玩乐的各种套餐，包括美食套餐、旅行套餐、同城娱乐套餐等等，大家可以点击下方购物袋了解详情，一会儿主播将给大家一一进行讲解。新进来的宝宝们，点击关注不迷路，后续主播还会持续给大家送福利！好了，我们现在开始发一波福利优惠券，现在点赞十下并给主播点一个关注可以领取一个10元优惠券，今天所有的商品都是最低优惠，每个整点我们还准备一个抽奖，想要礼品的宝宝们666扣起来~

好了，我们开始介绍今天的第一个套餐，是老哥火锅提供的一个超级实惠的美食套餐。这个套餐包含好几个他们家的特色美食。第一个，特色美蛙：牛蛙是4只起点，满4只锅底只要3元，蛙现杀现做。弹弹嫩嫩的美蛙，配经典麻辣锅底很赞，不愧为他们家的招牌菜；接下来是我爱的不行的老上海罗宋汤：酸酸甜甜的很像汤底，可以直接拿来喝，汤汁浓稠、酸甜可口真的好好喝，每次去都连喝好几碗 (小tips:用它做汤底烫豆皮魔芋等味道简直绝了），第三个川门毛肚七绝：如果爱吃毛肚儿类，这道是个不错的选择:鲜毛肚、黑白千层肚、牛大肚和金钱肚~种类多，量也足。最后一个也是我好喜欢的，手作咸蛋黄虾滑：虾肉中包裹着咸蛋黄，咬一口都能流出来，就算不蘸任何酱料依然很好吃，味道在线。我们今天的直播间的这个套餐，全国100家+门店，都可以使用，包括周六日都通用。宝宝们还在犹豫什么，赶快囤起来。

接下来我要为大家介绍一款向前冲旅行社提供的，极具性价比的旅行团购套餐——张家界6日游！你没听错，现在只要899.99元，你就能畅游张家界六大著名景点，并享受高品质的住宿环境。接下来，就让我们一起走进这个美丽而神秘的世界吧！
首先，让我们来了解一下张家界的著名景点。第一个景点是“黄龙洞”。黄龙洞以其壮丽的石笋、石幔、石瀑等景观而著称，仿佛一座地下宫殿，让人流连忘返。
接下来，我们将前往“天子山”，天子山因古代土家族领袖向大坤在此率众起义，自称“天王”，后来当地官吏为了讨吉祥，将其名改为“天子山”。站在山顶，俯瞰群山，云雾缭绕，仿佛置身仙境。
第三天，我们将游览“金鞭溪”，金鞭溪是一条美丽的溪流，沿途可以欣赏到许多奇特的植物和动物，还有壮观的瀑布和峡谷。
第四天，我们将前往“袁家界”，袁家界以险峻著称，有着许多令人叹为观止的悬崖峭壁和天然桥洞。
最后一天，我们将参观“天门山”，天门山因其天门洞而闻名，站在洞口，你可以感受到大自然的鬼斧神工和无尽的魅力。
除了景点之外，我们的团购商品还包括高品质的住宿环境。我们为大家提供了舒适宽敞、设施完备的酒店或客栈，让你在旅途中享受宾至如归的体验。
现在，只要899.99元，你就能拥有这趟梦幻的张家界6日游！机会难得，千万不要错过哦！赶快加入我们，与大自然来一场亲密接触吧！`

func TestGetLive2DFigures(t *testing.T) {
	initTest()
	nowTime := time.Now()
	if err := getLive2DAllFiguresAndInsert(testToken, enum.FigureGenderMale); err != nil {
		t.Errorf("TestGetLive2DFigures getLive2DAllFiguresAndInsert fail, gender:%v, cost:%v, err:%v",
			enum.FigureGenderMale, time.Since(nowTime), err)
		return
	}
	if err := getLive2DAllFiguresAndInsert(testToken, enum.FigureGenderFemale); err != nil {
		t.Errorf("TestGetLive2DFigures getLive2DAllFiguresAndInsert fail, gender:%v, cost:%v, err:%v",
			enum.FigureGenderFemale, time.Since(nowTime), err)
		return
	}
	if err := getLive2DAllFiguresAndInsert(testToken, ""); err != nil {
		t.Errorf("TestGetLive2DFigures getLive2DAllFiguresAndInsert fail, gender:%v, cost:%v, err:%v",
			"all", time.Since(nowTime), err)
		return
	}
}

func TestAutoGenerateFigureVideo(t *testing.T) {
	initTest()
	//maleFigureNum := 1005
	//femaleFigureNum := 1005
	excludeStatus := []string{
		enum.Live2DFigureVideoSucceed,
	}
	figureVideo := &model.FigureVideo{}
	//maleFigures, err := figureVideo.FindByExcludeStatusAndGender(excludeStatus, enum.FigureGenderMale)
	//if err != nil {
	//	t.Errorf("TestAutoGenerateFigureVideo FindByExcludeStatusAndGender fail, status:%v, gender:%v",
	//		excludeStatus, enum.FigureGenderMale)
	//	t.Fail()
	//	return
	//}
	//if len(maleFigures) > maleFigureNum {
	//	rand.Seed(time.Now().UnixNano())
	//	rand.Shuffle(len(maleFigures), func(i, j int) {
	//		maleFigures[i], maleFigures[j] = maleFigures[j], maleFigures[i]
	//	})
	//	maleFigures = maleFigures[:maleFigureNum]
	//}
	//
	//femaleFigures, err := figureVideo.FindByExcludeStatusAndGender(excludeStatus, enum.FigureGenderFemale)
	//if err != nil {
	//	t.Errorf("TestAutoGenerateFigureVideo FindByExcludeStatusAndGender fail, status:%v, gender:%v",
	//		excludeStatus, enum.FigureGenderFemale)
	//	t.Fail()
	//	return
	//}
	//if len(femaleFigures) > femaleFigureNum {
	//	rand.Seed(time.Now().UnixNano())
	//	rand.Shuffle(len(femaleFigures), func(i, j int) {
	//		femaleFigures[i], femaleFigures[j] = femaleFigures[j], femaleFigures[i]
	//	})
	//	femaleFigures = femaleFigures[:femaleFigureNum]
	//}
	//allFigures := append(maleFigures, femaleFigures...)
	allFigures, err := figureVideo.FindByExcludeStatus(excludeStatus)
	if err != nil {
		t.Errorf("TestAutoGenerateFigureVideo FindByExcludeStatus fail, status:%v, gender:%v",
			excludeStatus, enum.FigureGenderFemale)
		t.Fail()
		return
	}
	var wg sync.WaitGroup
	array := splitArray(allFigures, 2)
	for _, videos := range array {
		wg.Add(1)
		go func(videos []*model.FigureVideo) {
			defer wg.Done()
			for _, figure := range videos {
				appToken, err := getLive2DAppToken(testToken, live2dAppId)
				if err != nil {
					t.Errorf("TestAutoGenerateFigureVideo getLive2DAppToken fail, err:%v", err)
					t.Fail()
					return
				}
				if err := generateLive2DFigureVideo(testToken, appToken, figure); err != nil {
					t.Errorf("TestAutoGenerateFigureVideo generateLive2DFigureVideo fail, figure:%v", figure)
					t.Fail()
					return
				}
				startTime := time.Now()
				for {
					isEnd, err := queryLive2DVideoGenerateProgress(testToken, appToken, figure)
					if err != nil {
						t.Errorf("TestAutoGenerateFigureVideo queryLive2DVideoGenerateProgress fail, figure:%v, err:%v",
							figure, err)
						t.Fail()
						continue
					}
					if time.Since(startTime) >= 5*time.Minute {
						t.Errorf("TestAutoGenerateFigureVideo timeout 5 min, figure:%v", figure)
						startTime = time.Now()
						break
					}
					if isEnd {
						t.Logf("TestAutoGenerateFigureVideo finish, figure:%v", figure)
						startTime = time.Now()
						break
					}
					time.Sleep(3 * time.Second)
				}
				time.Sleep(5 * time.Second)
			}
		}(videos)
	}
	wg.Wait()
}

func TestExportFigureVideo2Csv(t *testing.T) {
	initTest()
	nowTime := time.Now()
	figureVideo := &model.FigureVideo{}
	//figureVideos, err := figureVideo.FindByStatus([]string{enum.Live2DFigureVideoSucceed})
	startTime := time.Date(nowTime.Year(), nowTime.Month(), nowTime.Day()-2,
		22, 45, 9, 0, nowTime.Location())
	figureVideos, err := figureVideo.FindByStatusAndTime([]string{enum.Live2DFigureVideoSucceed}, startTime, time.Now())
	if err != nil {
		t.Errorf("TestExportFigureVideo2Csv FindByStatus fail, err:%v", err)
		t.Fail()
		return
	}
	if len(figureVideos) == 0 {
		t.Logf("TestExportFigureVideo2Csv end, no data")
		return
	}
	homeDir, err := os.UserHomeDir()
	if err != nil {
		log.Fatalf("Failed to get user home directory: %v", err)
	}
	filePath := fmt.Sprintf("%v/figure-video/figure-video_%v_%v.csv",
		homeDir, nowTime.Format("20060102_150405"), utils.RandStringRunes(4))
	// 确保目录存在，如果目录不存在则创建
	dir := filepath.Dir(filePath)
	if err := os.MkdirAll(dir, 0755); err != nil {
		t.Errorf("TestExportFigureVideo2Csv Failed to create directory: %v", err)
		t.Fail()
		return
	}
	file, err := os.Create(filePath)
	if err != nil {
		t.Errorf("TestExportFigureVideo2Csv Create file fail, err:%v", err)
		t.Fail()
		return
	}
	defer file.Close()

	// 写入 BOM 头
	bom := []byte{0xEF, 0xBB, 0xBF}
	if _, err := file.Write(bom); err != nil {
		t.Errorf("TestExportFigureVideo2Csv Failed to write BOM: %v", err)
		t.Fail()
		return
	}
	writer := csv.NewWriter(file)
	defer writer.Flush()

	// 写入 CSV 头部
	header := []string{"人像ID", "人像名称", "人像视频地址"}
	if err := writer.Write(header); err != nil {
		t.Errorf("TestExportFigureVideo2Csv to write header: %v", err)
		t.Fail()
		return
	}
	for _, fv := range figureVideos {
		if err := writer.Write([]string{fmt.Sprintf("%v_%v", fv.FigureName, fv.FigureID),
			fv.Name, fv.VideoURL}); err != nil {
			t.Errorf("TestExportFigureVideo2Csv to write header: %v", err)
			t.Fail()
			return
		}
	}
	t.Logf("TestExportFigureVideo2Csv end, filePath:%v", filePath)
}

func TestAutoGenerateAudioURL(t *testing.T) {
	initTest()
	persons, err := getLive2DAudioPersons(testToken)
	if err != nil {
		t.Errorf("TestAutoGenerateAudioURL getLive2DAudioPersons fail, err:%v", err)
		t.Fail()
		return
	}

	for _, person := range persons {
		t2a := &model.Text2Audio{}
		if err := t2a.FindByPersonID(person.Id); err != nil {
			t.Errorf("TestAutoGenerateFigureVideo FindByPersonID fail, personId:%v, err:%v", person.Id, err)
			t.Fail()
			return
		}
		if t2a.ID > 0 && t2a.Status == enum.Live2DText2AudioSucceed {
			t.Logf("TestAutoGenerateFigureVideo exists, person:%v", person)
			continue
		}
		//isRun := false
		//for _, tag := range person.Tags {
		//	if tag.Tag == "推荐" {
		//		isRun = true
		//	}
		//}
		//if !isRun {
		//	continue
		//}
		//appToken, err := getLive2DAppToken(testToken, live2dAppId)
		//if err != nil {
		//	t.Errorf("TestAutoGenerateFigureVideo getLive2DAppToken fail, err:%v", err)
		//	t.Fail()
		//	return
		//}
		nowTime := time.Now()
		result, err := generateLive2DAudioURL(testToken, testText, person)
		if err != nil {
			t.Errorf("TestAutoGenerateAudioURL generateLive2DAudioURL fail, person:%v, err:%v", person, err)
			t.Fail()
			return
		}
		text2Audio := &model.Text2Audio{
			PersonId:   person.Id,
			Per:        person.Per,
			Config:     person.Config,
			Extra:      person.Extra,
			Gender:     person.Gender,
			Name:       person.Name,
			Tags:       util.Struct2JsonStr(person.Tags),
			Text:       testText,
			AudioURL:   result.Url,
			Duration:   result.Duration,
			Size:       result.Size,
			Status:     "SUCCEED",
			StartTime:  nowTime.Unix(),
			FinishTime: time.Now().Unix(),
			Result:     util.Struct2JsonStr(result),
		}
		if err = text2Audio.Insert(); err != nil {
			t.Errorf("TestAutoGenerateAudioURL Insert fail, person:%v, err:%v", person, err)
			t.Fail()
			return
		}
		text2Audio.AudioURL = result.Url
		text2Audio.Duration = result.Duration
		text2Audio.Size = result.Size
		text2Audio.Status = enum.Live2DText2AudioSucceed
		text2Audio.StartTime = nowTime.Unix()
		text2Audio.FinishTime = time.Now().Unix()
		text2Audio.Result = util.Struct2JsonStr(result)
		if err = text2Audio.Update(); err != nil {
			t.Errorf("TestAutoGenerateAudioURL Update fail, person:%v, err:%v", person, err)
			t.Fail()
			return
		}
		t.Logf("TestAutoGenerateAudioURL generateLive2DAudioURL success, cost:%v, person:%v, result:%v",
			time.Since(nowTime), person, result)
	}
}

func TestExportTextAudio2Csv(t *testing.T) {
	initTest()
	nowTime := time.Now()
	text2Audio := &model.Text2Audio{}
	text2Audios, err := text2Audio.FindByStatus([]string{enum.Live2DText2AudioSucceed})
	if err != nil {
		t.Errorf("TestExportTextAudio2Csv FindByStatus fail, err:%v", err)
		t.Fail()
		return
	}
	if len(text2Audios) == 0 {
		t.Logf("TestExportTextAudio2Csv end, no data")
		return
	}
	homeDir, err := os.UserHomeDir()
	if err != nil {
		log.Fatalf("Failed to get user home directory: %v", err)
	}
	filePath := fmt.Sprintf("%v/text2audio/text2audio_%v_%v.csv",
		homeDir, nowTime.Format("20060102_150405"), utils.RandStringRunes(4))
	// 确保目录存在，如果目录不存在则创建
	dir := filepath.Dir(filePath)
	if err := os.MkdirAll(dir, 0755); err != nil {
		t.Errorf("TestExportTextAudio2Csv Failed to create directory: %v", err)
		t.Fail()
		return
	}
	file, err := os.Create(filePath)
	if err != nil {
		t.Errorf("TestExportTextAudio2Csv Create file fail, err:%v", err)
		t.Fail()
		return
	}
	defer file.Close()

	// 写入 BOM 头
	bom := []byte{0xEF, 0xBB, 0xBF}
	if _, err := file.Write(bom); err != nil {
		t.Errorf("TestExportTextAudio2Csv Failed to write BOM: %v", err)
		t.Fail()
		return
	}
	writer := csv.NewWriter(file)
	defer writer.Flush()

	// 写入 CSV 头部
	header := []string{"id", "音频链接"}
	if err := writer.Write(header); err != nil {
		t.Errorf("TestExportTextAudio2Csv to write header: %v", err)
		t.Fail()
		return
	}
	for _, tts := range text2Audios {
		if err := writer.Write([]string{fmt.Sprintf("%v", tts.PersonId), tts.AudioURL}); err != nil {
			t.Errorf("TestExportTextAudio2Csv to write header: %v", err)
			t.Fail()
			return
		}
	}
	t.Logf("TestExportTextAudio2Csv end, filePath:%v", filePath)
}

func splitArray(arr []*model.FigureVideo, n int) [][]*model.FigureVideo {
	if n <= 0 {
		return nil
	}

	length := len(arr)
	if n > length {
		n = length
	}

	// Calculate the size of each part
	partSize := length / n
	remainder := length % n

	var result [][]*model.FigureVideo
	start := 0

	for i := 0; i < n; i++ {
		end := start + partSize
		if remainder > 0 {
			end++
			remainder--
		}
		result = append(result, arr[start:end])
		start = end
	}

	return result
}
