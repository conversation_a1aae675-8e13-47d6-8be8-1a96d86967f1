package handler

import (
	"fmt"
	"log"
	"os"
	"strings"
)

const (
	NameSpaceEnvKey = "POD_NAMESPACE"
)

var namespace = "" // 当前K8s Pod所属的命名空间

func init() {
	namespace = os.Getenv(NameSpaceEnvKey)
	log.Print("namespace: ", namespace)
}

func getK8sServerUrl(serverName string, port int) string {
	if port == 0 {
		port = 80
	}
	return fmt.Sprintf("%s.%s.svc.cluster.local:%v/", serverName, namespace, port)
}

func getK8sServerUrl2(serverAddr string) string {
	s := strings.Split(serverAddr, ":")
	if len(s) != 2 {
		return ""
	}
	return fmt.Sprintf("%s.%s.svc.cluster.local:%v/", s[0], namespace, s[1])
}
