####################################################### 服务配置-开发环境 #######################################################
server-port = 8080
server-name = "dhlive-manager"
log-file-prefix = "localhost"
log-file-path = "./logs/"
log-show-code-file = true
log-show-code-func = true
# consul配置
[consul-setting]
host = "127.0.0.1"
port = 8500
name = ""
# 健康检查地址
health-url = "/health"
check-timeout = "10s"
check-interval = "10s"
deregister-critical-service-after = "30s"

# mysql配置
[mysql-setting]
host = "localhost"
port = 3306
database = "dhlive_third_platform"
username = "root"
password = "123456"
maxOpenConns = 1000
maxIdlenConns = 10

# redis配置
[redis-setting]
addr = "*************:8379"
username = ""
password = ""

# 日志上传的elasticsearch配置
[log-es-setting]
host = "http://*************:8200"
username = ""
password = ""

# 京东配置
[jd-setting]
# TestOauth6
appKey = "4D731E6A0B1D6F34AC39B7DC88E8EF88"
secretKey = "0c82afe7382040369c49a2abc3e05764"
# 演示专用
# appKey = "25348E37A8F601CC60A646527CE47DF1"
# secretKey = "d8cba2e28e5b4b49bcf1254a38820fa5"
callbackUrl = "http://127.0.0.1:8080/openapi/v1/oauth/callback"