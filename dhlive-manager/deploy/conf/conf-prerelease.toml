####################################################### 服务配置-生产环境 #######################################################
server-port = 8080
server-name = "dhlive-manager"
log-file-prefix = "localhost"
log-file-path = "./logs/"
log-show-code-file = true
log-show-code-func = true
# consul配置
[consul-setting]
host = "127.0.0.1"
port = 8500
name = ""

# 健康检查地址
health-url = "/health"
check-timeout = "10s"
check-interval = "10s"
deregister-critical-service-after = "30s"

# mysql配置
[mysql-setting]
host = "mysql57.rdsml05scrp4zn8.rds.bj.baidubce.com"
port = 3306
database = "dhlive_third_platform_ppe"
username = "dhlive_tp_plat"
password = "Hi109@123"
maxOpenConns = 1000
maxIdlenConns = 100

# redis配置
[redis-setting]
addr = "redis.zytsboaklxlp.scs.bj.baidubce.com:6379"
username = ""
password = ""

# 日志上传的elasticsearch配置
[log-es-setting]
host = "http://127.0.0.1:9200"
username = ""
password = ""

# 京东配置
[jd-setting]
# TestOauthApp
appKey = "EA881DF95412B4D75119789A2EBB251C"
secretKey = "297e4a8fe2a84d318c9b670ee388bc80"
# TestOauthApp2(not mq)
# appKey = "0988265B79617626A1E3C88DD0EF0CE5"
# secretKey = "b7650df0051043ea9c9b16c01d0fe284"
# TestOauth2-1(not mq)
# appKey = "33D1739C1B9F7528B28B5E0B2DEFDCEF"
# secretKey = "0bff1463b8e147b6ae6489e9ddfdabcf"
# TestOauth3(not mq)
# appKey = "3986C9209C73234FB061390E6C63FA49"
# secretKey = "6236db9aa5da4e6cae3f2c3fc493ebb3"
# TestOauth4-1(not mq)
# appKey = "D7DFDDFF2EB5E1B351EBA3DB2CD215AA"
# secretKey = "f8ed4d4cc6214d0783fde878bb78f7dc"
# TestOauth5
# appKey = "AE04ED3755F261849107106E037CCE0C"
# secretKey = "f919b954a4ca45199ad3ba3f0c108baa"
# TestOauth6
# appKey = "4D731E6A0B1D6F34AC39B7DC88E8EF88"
# secretKey = "0c82afe7382040369c49a2abc3e05764"
# 演示专用
# appKey = "25348E37A8F601CC60A646527CE47DF1"
# secretKey = "d8cba2e28e5b4b49bcf1254a38820fa5"
callbackUrl = "https://persona.baidu.com:8444/dhlive-cgi/dhlive-manager/openapi/v1/oauth/callback"
