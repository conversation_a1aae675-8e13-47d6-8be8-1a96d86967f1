apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: digital-human
    module: dhlive-manager
  name: dhlive-manager
  namespace: live2d
spec:
  replicas: 3
  minReadySeconds: 0
  strategy:
    type: RollingUpdate # 策略：滚动更新
    rollingUpdate:
      maxSurge: 25%
      maxUnavailable: 25%
  selector:
    matchLabels:
      app: digital-human
      module: dhlive-manager
  template:
    metadata:
      labels:
        app: digital-human
        module: dhlive-manager
    spec:
      affinity:
        podAntiAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            - labelSelector:
                matchExpressions:
                  - key: app
                    operator: In
                    values:
                      - digital-human
                  - key: module
                    operator: In
                    values:
                      - dhlive-manager
              topologyKey: kubernetes.io/hostname
      restartPolicy: Always
      tolerations:
        - effect: "NoSchedule"
          key: "limit"
          operator: "Equal"
          value: "lite"
      containers:
        - name: dhlive-manager
          image: ccr-246im3mw-pub.cnc.bj.baidubce.com/digitalhuman/dhlive-manager:20240415-1900
          imagePullPolicy: Always
          command: [ "sh" ]
          env:
            - name: POD_NAMESPACE
              valueFrom:
                fieldRef:
                  fieldPath: metadata.namespace
          args: [ "/home/<USER>/sbin/start.sh" ]
          # imagePullPolicy: IfNotPresent
          # imagePullPolicy: Never
          ports:
            - containerPort: 8080
          livenessProbe: # 健康检查：存活探针
            httpGet:
              path: /health
              port: 8080
            initialDelaySeconds: 20
            timeoutSeconds: 5
            periodSeconds: 5
          readinessProbe: # 健康检查：就绪探针
            httpGet:
              path: /health
              port: 8080
            initialDelaySeconds: 5
            timeoutSeconds: 1
            periodSeconds: 5
      imagePullSecrets:
        - name: regcred
        - name: regcred-ccr
        - name: regcred-eccr
---
apiVersion: v1
kind: Service
metadata:
  labels:
    app: digital-human
    module: dhlive-manager
  name: dhlive-manager
  namespace: live2d
spec:
  selector:
    app: digital-human
    module: dhlive-manager
  ports:
    - protocol: TCP
      port: 8080
      targetPort: 8080
  type: ClusterIP