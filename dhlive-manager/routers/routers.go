package routers

import (
	"github.com/gin-gonic/gin"
	"net/http"
)

// Routers 路由列表
func Routers(e *gin.Engine) {
	//openAPI := e.Group("/openapi/v1", corsMiddleware())
	// 第三方oauth2授权回调地址

	//thirdParty := e.Group("/api/v1")

}

// 跨域中间件
func corsMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.<PERSON>.Header().Set("Access-Control-Allow-Origin", "*")
		c.<PERSON>.Header().Set("Access-Control-Allow-Methods", "POST, GET, OPTIONS, PUT, DELETE")
		c.Writer.Header().Set("Access-Control-Max-Age", "86400")
		c.Writer.Header().Set("Access-Control-Allow-Headers",
			"Content-Type, Content-Length, Accept-Encoding, X-CSRF-Token, Authorization")
		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(http.StatusOK)
			return
		}
		c.Next()
	}
}
