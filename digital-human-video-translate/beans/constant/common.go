package constant

const (
	VideoTransTypeHg   = 0 // 调用heygen
	MaterialTypeFigure = 1 //自研

	DigitalHumanAccountID = "AccountId"
	DigitalHumanUserID    = "UserId"
	DigitalHumanUserName  = "UserName"
	DigitalHumanLanguage  = "language"
)

// 视频翻译的整体状态
const (
	VideoStatus_Waiting = "WAITING"
	VideoStatus_Running = "RUNNING"
	VideoStatus_Success = "SUCCESS"
	VideoStatus_Failed  = "FAILED"
)

// 调用heygen翻译的状态
const (
	TransStatus_WaitingRiskControl      = "waiting_risk_control"       // 待风控
	TransStatus_WaitingGenerateByHeygen = "waiting_generate_by_heygen" // 等待直接提交给heygen 做视频翻译不生成 proofread
	TransStatus_VideoProduceByBaidu     = "video_generating_by_baidu"  // 视频翻译生成中，by百度
	TransStatus_WaitingProofread        = "waiting_proofread"          // 待校对
	TransStatus_Proofreading            = "proofreading"               // 校对中
	TransStatus_ProofreadFailed         = "proofread_failed"           // 校对失败
	TransStatus_VideoGenerating         = "video_generating_by_heygen" // 视频生成中,by heygen
	TransStatus_VideoGenerateSuccess    = "video_generate_success"     // 视频生成成功
	TransStatus_VideoGenerateFailed     = "video_generate_failed"      // 视频生成失败
)

const (
	HeygenStatus_Processing = "processing" // 处理中
	HeygenStatus_Completed  = "completed"  // 已完成
	HeygenStatus_Running    = "running"
	HeygenStatus_Pending    = "pending"
	HeygenStatus_Success    = "success"
	HeygenStatus_Failed     = "failed"
)

const (
	FailedReaseon_Timeout24hours          = ""                                                      // 超过24小时失败
	FailedReaseon_RiskControlCensorFailed = "Content violates policy. Please modify and try again." // 风控审核未通过
	FailedReaseon_ServerInternalError     = ""                                                      // 服务内部错误
	FailedReaseon_VideoDownLoadError      = ""                                                      // 视频下载失败
	FailedReaseon_QuotaConformFailed      = "quota conform failed"                                  // 权益扣除失败

)
