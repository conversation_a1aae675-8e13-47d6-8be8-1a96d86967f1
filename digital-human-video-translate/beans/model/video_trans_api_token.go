package model

import (
	"acg-ai-go-common/logger"
	"errors"
	"gorm.io/gorm"
)

type VideoTranslateApiToken struct {
	ID            int64  `json:"id" gorm:"primaryKey;autoIncrement;column:id"`
	AccountEmail  string `json:"accountEmail" gorm:"column:account_email;type:varchar(64);comment:heygen账号邮箱"`
	ApiToken      string `json:"apiToken" gorm:"column:api_token;type:varchar(100)"`
	AmountOfMonth int    `json:"amountOfMonth" gorm:"column:amount_of_month"`
	IsPublish     int    `json:"isPublish" gorm:"column:is_publish;comment:0未发布,1已发布;发布后才可使用"`
}

func (VideoTranslateApiToken) TableName() string {
	return "video_translate_api_tokens"
}
func (d *VideoTranslateApiToken) Create(db *gorm.DB) error {
	result := db.Create(d)
	if result.Error != nil {
		return result.Error
	}
	return nil
}

func (d *VideoTranslateApiToken) CreateModels(db *gorm.DB, videoItems []VideoTranslateApiToken) error {
	result := db.Create(&videoItems)
	if result.Error != nil {
		return result.Error
	}
	return nil
}

// 获取一定数量的数据
func (d *VideoTranslateApiToken) GetPublishTokenAndUpdateAmoutOfMonth(db *gorm.DB) (string, error) {
	var entity VideoTranslateApiToken
	err := db.Model(&VideoTranslateApiToken{}).Where("is_publish = ?", 1).Order("amount_of_month ASC").First(&entity).Error
	if err != nil {
		logger.Log.Errorf("GetPublishItemsByAsc error:%+v", err)
		return "", err
	}

	// 更新amount_of_month字段+1
	updateErr := db.Model(&VideoTranslateApiToken{}).Where("id = ?", entity.ID).
		Update("amount_of_month", entity.AmountOfMonth+1).Error
	if updateErr != nil {
		logger.Log.Errorf("Update amount_of_month error:%+v", updateErr)
		return entity.ApiToken, updateErr
	}

	return entity.ApiToken, nil
}

// 更新对应字段的数据
func (d *VideoTranslateApiToken) UpdateFieldsByToken(db *gorm.DB, apiToken string, fields map[string]interface{}) error {
	logger.Log.Infof("UpdateFieldsByToken api_token:%s,fields:%+v", apiToken, fields)
	result := db.Model(&VideoTranslateApiToken{}).Where("api_token = ?", apiToken).Updates(fields)
	if result.Error != nil {
		return result.Error
	}
	// 未查询到对应的数据
	if result.RowsAffected == 0 {
		logger.Log.Errorf("UpdateFieldsByToken api_token:%s,result.RowsAffected == 0", apiToken)
		return errors.New("no matching record found")
	}
	return nil
}

// 把 amount_of_month 字段的值重置为0
func ResetAllAmountOfMonth(db *gorm.DB) error {
	result := db.Model(&VideoTranslateApiToken{}).
		Update("amount_of_month", 0)

	if result.Error != nil {
		logger.Log.Errorf("ResetAllAmountOfMonth error:%v", result.Error)
		return result.Error
	}
	return nil
}
