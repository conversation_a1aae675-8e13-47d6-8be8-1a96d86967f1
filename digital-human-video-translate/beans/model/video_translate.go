package model

import (
	"acg-ai-go-common/logger"
	"digital-human-video-translate/beans/constant"
	"errors"
	"time"

	"gorm.io/gorm"
)

type VideoTranslateEntity struct {
	ID                    int64          `json:"id" gorm:"primaryKey;autoIncrement;column:id"`
	VideoID               string         `json:"videoId" gorm:"column:video_id;type:varchar(64);uniqueIndex;comment:视频id"`
	Name                  string         `json:"name" gorm:"column:name;type:varchar(2048);comment:视频名称"`
	UserID                string         `json:"userID" gorm:"column:user_id;type:varchar(100);index;comment:userId"`
	LastUpdateBy          string         `json:"lastUpdateBy" gorm:"column:last_update_by;type:varchar(100)"`
	VideoUrl              string         `json:"videoUrl" gorm:"column:video_url;type:varchar(2048);comment:视频url"` // 最终的翻译视频url
	Thumbnail             string         `json:"thumbnail" gorm:"column:thumbnail;type:varchar(2048);comment:缩略图url地址"`
	Duration              int64          `json:"duration" gorm:"column:duration;comment:视频时长,单位毫秒"`
	Width                 int            `json:"width" gorm:"column:width"`
	Height                int            `json:"height" gorm:"column:height"`
	IsDelete              int            `json:"isDelete" gorm:"column:is_delete"`
	OriginVideoUrl        string         `json:"originVideoUrl" gorm:"column:origin_video_url;type:varchar(2048);comment:原始视频url"`
	HgVideoTranslateUrl   string         `json:"hegVideoTranslateUrl" gorm:"column:translate_video_url;type:varchar(2048);comment:翻译后视频url"` // 翻译后视频url
	HgCaptionUrl          string         `json:"hgCaptionUrl" gorm:"column:caption_url;type:varchar(2048);comment:字幕url"`
	OriginDuration        int64          `json:"originDuration" gorm:"column:origin_duration;comment:原始视频时长,单位毫秒"`
	TranslateType         int            `json:"translateType" gorm:"column:translate_type;"`                                                         // 翻译类型，0 heygen  1 自研
	SpeakerNum            int            `json:"speakerNum" gorm:"column:speaker_num;type:tinyint;comment:发音人个数"`                                     // 发音人个数
	DetectSpeakerNum      int            `json:"detectSpeakerNum" gorm:"column:detect_speaker_num;type:tinyint;comment:检测到的发音人个数"`                    // 检测到的发音人个数
	TranslateAudioOnly    int            `json:"translateAudioOnly" gorm:"column:translate_audio_only;type:tinyint;comment:是否仅翻译音频,1-是,0-否"`          // 是否仅翻译音频 1 是  0 否
	EnableDynamicDuration int            `json:"enableDynamicDuration" gorm:"column:enable_dynamic_duration;type:tinyint;comment:是否动态调整音频时长,1-是,0-否"` // 是否动态调整音频时长 1 是  0 否
	EnableCaption         int            `json:"enableCaption" gorm:"column:enable_caption;type:tinyint;comment:是否需要字幕,1-是,0-否"`                      // 是否需要字幕 1 是  0 否
	TranslateLan          string         `json:"translateLan" gorm:"column:translate_lan;type:varchar(512)"`                                           // 翻译的目标语种
	ProofreadID           string         `json:"proofreadId" gorm:"column:proofread_id;type:varchar(512);comment:heygen校对文件的id"`                      // 调用heygen生成的校对文件的id
	VideoTranslateId      string         `json:"videoTranslateId" gorm:"column:video_translate_id;type:varchar(512);comment:heygen视频翻译id"`            // 调用heygen生成的校对文件的id
	TranslateStatus       string         `json:"translateStatus" gorm:"column:translate_status;type:varchar(100);index"`                              // 翻译状态，逻辑需要
	Status                string         `json:"status" gorm:"column:status;type:varchar(100);index"`                                                 // 视频状态，对用户展示
	FailedReason          string         `json:"failedReason" gorm:"column:failed_reason;type:mediumtext;"`                                           // 技术层面的失败原因
	ReasonToUser          string         `json:"reasonToUser" gorm:"column:reason_to_user;type:mediumtext;comment:向客户展示的失败原因"`                        // 向客户展示的失败原因
	ApiToken              string         `json:"apiToken" gorm:"column:api_token;type:varchar(100)"`
	CreatedAt             time.Time      `json:"createdAt" gorm:"column:create_at"`
	UpdatedAt             time.Time      `json:"updatedAt" gorm:"column:update_at"`
	DeletedAt             gorm.DeletedAt `json:"-" gorm:"column:deleted_at;index"`
}

func (VideoTranslateEntity) TableName() string {
	return "video_translate"
}
func (d *VideoTranslateEntity) Create(db *gorm.DB) error {
	result := db.Create(d)
	if result.Error != nil {
		return result.Error
	}
	return nil
}

func (d *VideoTranslateEntity) CreateModels(db *gorm.DB, videoItems []VideoTranslateEntity) error {
	result := db.Create(&videoItems)
	if result.Error != nil {
		return result.Error
	}
	return nil
}

// 获取没有结束的任务数
func (d *VideoTranslateEntity) GetTranslateRunningCount(db *gorm.DB) (int64, error) {
	var count int64
	// 执行带条件的Count查询
	err := db.Model(&VideoTranslateEntity{}).
		Where("status = ? AND is_delete = 0", constant.VideoStatus_Running).Count(&count).Error
	if err != nil {
		return 0, err // 返回错误信息
	}
	return count, nil
}

func (d *VideoTranslateEntity) QueryItemNotDeleteById(db *gorm.DB, accountID string, videoID string) (VideoTranslateEntity, error) {
	logger.Log.Infof("QueryItemById accountID:%s,videoID:%s", accountID, videoID)
	var entity VideoTranslateEntity
	logger.Log.Infof("QueryItemById videoID:%s", videoID)
	err := db.Model(&VideoTranslateEntity{}).Where("user_id = ? AND video_id = ? AND is_delete = 0", accountID, videoID).First(&entity).Error
	if err != nil {
		logger.Log.Errorf("QueryItemById error:%+v", err)
		return entity, err
	}
	return entity, nil
}

// 获取一定数量的数据
func (d *VideoTranslateEntity) GetLimitedCountItemInRunningByStatus(db *gorm.DB, limitCount int) ([]VideoTranslateEntity, error) {
	logger.Log.Infof("GetLimitedCountItemInRunningByStatus limitCount:%d", limitCount)
	var entitys []VideoTranslateEntity
	err := db.Model(&VideoTranslateEntity{}).Where("status IN  (?,?) AND is_delete = 0", constant.VideoStatus_Waiting, constant.VideoStatus_Running).Order("create_at ASC").Limit(limitCount).Find(&entitys).Error
	if err != nil {
		logger.Log.Errorf("GetLimitedCountItemByStatus error:%+v", err)
		return entitys, err
	}
	return entitys, nil
}

// 更新对应字段的数据
func (d *VideoTranslateEntity) UpdateFieldsByVideoID(db *gorm.DB, accountID string, videoId string, fields map[string]interface{}) error {
	logger.Log.Infof("UpdateFieldsByVideoID accountID:%s,videoId:%s,fields:%+v", accountID, videoId, fields)
	result := db.Model(&VideoTranslateEntity{}).Where("user_id = ? AND video_id= ?", accountID, videoId).Updates(fields)
	if result.Error != nil {
		return result.Error
	}
	// 未查询到对应的数据
	if result.RowsAffected == 0 {
		logger.Log.Errorf("UpdateFieldsByVideoID videoId:%s,result.RowsAffected == 0", videoId)
		return errors.New("no matching record found")
	}
	return nil
}

// 分页查询
func QueryVideosWithCondition(db *gorm.DB, accountID string, name string, pageNo, pageSize int) ([]VideoTranslateEntity, int64, error) {
	logger.Log.Infof("QueryVideosWithCondition start  accountID:%s,name:%s,pageNo:%d,pageSize:%d", accountID, name, pageNo, pageSize)
	var videos []VideoTranslateEntity
	var total int64

	query := db.Model(&VideoTranslateEntity{}).
		Where("user_id = ? AND deleted_at IS NULL AND is_delete = 0", accountID)

	// 动态添加Name条件
	if name != "" {
		query = query.Where("name LIKE ?", "%"+name+"%")
	}

	// 执行分页查询
	err := query.
		Count(&total).
		Offset((pageNo - 1) * pageSize).
		Limit(pageSize).
		Order("create_at DESC").
		Find(&videos).Error
	logger.Log.Infof("QueryVideosWithCondition end  accountID:%s,name:%s,pageNo:%d,pageSize:%d,videos.size=%d", accountID, name, pageNo, pageSize, len(videos))
	return videos, total, err
}

// 删除视频
func BatchSoftDeleteByVideoIDs(db *gorm.DB, accountID string, videoIDs []string) ([]VideoTranslateEntity, error) {
	logger.Log.Infof("BatchSoftDeleteByVideoIDs start  accountID:%s,videoIDs%+v", accountID, videoIDs)
	var deletedVideos []VideoTranslateEntity
	// 开启事务确保原子性
	err := db.Transaction(func(tx *gorm.DB) error {
		// 执行批量软删除（自动设置deleted_at）
		if err := tx.Where("user_id = ? AND video_id IN ?", accountID, videoIDs).
			Delete(&VideoTranslateEntity{}).Error; err != nil {
			return err
		}
		// 查询更新后的数据（包含软删除记录）
		if err := tx.Unscoped().
			Where("user_id = ? AND video_id IN ?", accountID, videoIDs).
			Find(&deletedVideos).Error; err != nil {
			return err
		}
		// 批量更新is_delete字段
		if err := tx.Unscoped().
			Model(&VideoTranslateEntity{}).
			Where("user_id = ? AND video_id IN ?", accountID, videoIDs).
			Update("is_delete", 1).Error; err != nil {
			return err
		}
		return nil
	})
	return deletedVideos, err
}

func (d *VideoTranslateEntity) FindByUserIdAndNameIn(db *gorm.DB, accountId string, candidateNames []string) ([]VideoTranslateEntity, error) {
	logger.Log.Infof("FindByUserIdAndNameIn start  accountID:%s,candidateNames%s", accountId, candidateNames)
	var entitys []VideoTranslateEntity
	err := db.Model(&VideoTranslateEntity{}).Where("user_id = ? AND name IN ?", accountId, candidateNames).Find(&entitys).Error
	if err != nil {
		logger.Log.Errorf("FindByUserIdAndNameIn error:%+v", err)
		return entitys, err
	}
	logger.Log.Infof("FindByUserIdAndNameIn end  accountID:%s,entitys.size=%d", accountId, len(entitys))
	return entitys, nil
}

// 查询分流到heygen的数量
func (d *VideoTranslateEntity) GetCountOfTranslateByHeygen(db *gorm.DB) (int64, error) {
	logger.Log.Infof("GetCountOfTranslateByHeygen start")
	var count int64

	err := db.Model(&VideoTranslateEntity{}).Where("translate_type = ? AND (speaker_num = ? OR detect_speaker_num = ?)", 0, 1, 1).Count(&count).Error
	if err != nil {
		logger.Log.Errorf("GetCountOfTranslateByHeygen error:%+v", err)
		return count, err
	}
	logger.Log.Infof("GetCountOfTranslateByHeygen count:%+v", count)
	return count, nil
}

// 查询分流到自研的数量
func (d *VideoTranslateEntity) GetCountOfTranslateByBaidu(db *gorm.DB) (int64, error) {
	logger.Log.Infof("GetCountOfTranslateByBaidu start")
	var count int64

	err := db.Model(&VideoTranslateEntity{}).Where("translate_type = ? AND (speaker_num = ? OR detect_speaker_num = ?)", 1, 1, 1).Count(&count).Error
	if err != nil {
		logger.Log.Errorf("GetCountOfTranslateByBaidu error:%+v", err)
		return count, err
	}
	logger.Log.Infof("GetCountOfTranslateByBaidu count:%+v", count)
	return count, nil
}

