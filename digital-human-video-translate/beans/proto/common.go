package proto

type RspMessage struct {
	Global   string      `json:"global"`             // 表示通用错误处理。一般会由全局错误处理以Dialog的形式展示
	Redirect string      `json:"redirect,omitempty"` // 重定向地址
	Field    interface{} `json:"field,omitempty"`    // 表示表单字段错误。在表单提交给后端后，如果某些字段未能通过后端校验，则返回对应字段的错误信息。（field 中的key与空间中的name相对应，错误信息就可以展示在空间后面
}

type CommRsp struct {
	Code    int        `json:"code"`
	Success bool       `json:"success"`
	Message RspMessage `json:"message"`
}
type CommonMessage struct {
	Global string `json:"global"`
}

type CommonResponse struct {
	Code    int           `json:"code"`
	Message CommonMessage `json:"message"`
	Success bool          `json:"success"`
	Result  interface{}   `json:"result"`
}

func NewCommonResponse(code int, msg string, result interface{}) CommonResponse {
	return CommonResponse{
		Code:    code,
		Message: CommonMessage{Global: msg},
		Success: code == 0,
		Result:  result,
	}
}
