package enums


type SubscribeStatus string

const (
	SubscribeAccept SubscribeStatus = "accept"
	SubscribeReject SubscribeStatus = "reject"
)

type MiniProgramEventType string

const (
	SubscribeMsg       MiniProgramEventType = "subscribe_msg_popup_event"
	SubscribeRejectMsg MiniProgramEventType = "subscribe_msg_change_event"
	PushMessageResult  MiniProgramEventType = "subscribe_msg_sent_event"
)

type PushType string

const (
	WechatMiniProgram PushType = "WECHAT_MINIPROGRAM"
	BaiduMiniProgram  PushType = "BAIDU_MINIPROGRAM"
)

type AnimationType string

const (
	AnimationTypeNone AnimationType = "none"
	AnimationTypeIn   AnimationType = "in"
	AnimationTypeOut  AnimationType = "out"
)

type MaterialType string

const (
	MaterialImage MaterialType = "IMAGE"
	MaterialAudio MaterialType = "AUDIO"
	MaterialVideo MaterialType = "VIDEO"
)

type FfmpegProcessType string

const (
	FfmpegProcessTypeAudioURL     FfmpegProcessType = "AUDIOURL"
	FfmpegProcessTypePreviewVideo FfmpegProcessType = "PREVIEWVIDEO"
	FfmpegProcessTypeThumbnailURL FfmpegProcessType = "THUMBNAILURL"
	FfmpegProcessTypeRecode       FfmpegProcessType = "RECODE"
)



type FigureType string

const (
	FigureTypeVideoKouboMp4Template  FigureType = "video_koubo_mp4_template"
	FigureTypeVideoKouboWebmTemplate FigureType = "video_koubo_webm_template"
	FigureTypeVideoKouboMp4From3d    FigureType = "video_koubo_mp4_from_3d"
	FigureTypeVideoKouboMp4Excellent FigureType = "video_koubo_mp4_excellent"
	FigureTypeOldFigure              FigureType = "OLD_FIGURE"
	FigureTypeFigure                 FigureType = "FIGURE"
)

func (f FigureType) GetFigureTypeByIndex() int {
	switch f {
	case FigureTypeVideoKouboMp4Template:
		return 0
	case FigureTypeVideoKouboWebmTemplate:
		return 1
	case FigureTypeVideoKouboMp4From3d:
		return 2
	case FigureTypeVideoKouboMp4Excellent:
		return 3
	default:
		return -1
	}
}
func GetFigureTypeByString(index uint8) FigureType {
	switch index {
	case 0:
		return FigureTypeVideoKouboMp4Template
	case 1:
		return FigureTypeVideoKouboWebmTemplate
	case 2:
		return FigureTypeVideoKouboMp4From3d
	case 3:
		return FigureTypeVideoKouboMp4Excellent
	default:
		return ""
	}
}
func GetFigureTypeByList(types []string) []int {
	indexs := []int{}
	for _, t := range types {
		switch FigureType(t) {
		case FigureTypeVideoKouboMp4Template:
			indexs = append(indexs, 0)
		case FigureTypeVideoKouboWebmTemplate:
			indexs = append(indexs, 1)
		case FigureTypeVideoKouboMp4From3d:
			indexs = append(indexs, 2)
		default:
			indexs = append(indexs, -1)
		}
	}
	return indexs
}

type CharacterConfigMode int

const (
	CHARACTER_CONFIG CharacterConfigMode = iota
	CHARACTER_FACTORY
)
