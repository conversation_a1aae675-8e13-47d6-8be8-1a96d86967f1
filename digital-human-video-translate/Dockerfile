# FROM iregistry.baidu-int.com/abc-robot/dhlive-ubantu22.04:ffmpeg-20241008-1520

# 支持思源黑体简体中文
# FROM iregistry.baidu-int.com/abc-robot/dhlive-ubantu22.04:ffmpeg-20250606  

# 增加支持字体中、日、韩、阿拉伯、印地、等语言
# FROM iregistry.baidu-int.com/abc-robot/dhlive-ubantu22.04:ffmpeg-fonts-20250610-1420 

# 增加支持字体中、日、韩、泰、阿拉伯、拉丁字母语等
FROM iregistry.baidu-int.com/abc-robot/dhlive-ubantu22.04:ffmpeg-fonts-20250620-1650

# 安装unzip
# RUN apt-get update && apt-get install -y wget unzip fontconfig

# # 安装字体工具和依赖
# RUN apt-get update && apt-get install -y fontconfig wget

# # 下载思源黑体-简体中文
# RUN wget -O /tmp/SourceHanSansCN-Regular.otf  https://github.com/adobe-fonts/source-han-sans/raw/release/SubsetOTF/CN/SourceHanSansCN-Regular.otf &&\
# # 创建字体目录并解压字体
#     mkdir -p /usr/share/fonts/opentype/source-han-sans && \
#     cp /tmp/SourceHanSansCN-Regular.otf /usr/share/fonts/opentype/source-han-sans/ 

# # 下载思源黑体-日文常规体
# RUN wget -O /tmp/SourceHanSansJP-Regular.otf https://github.com/adobe-fonts/source-han-sans/raw/release/SubsetOTF/JP/SourceHanSansJP-Regular.otf && \
# # 创建字体目录并复制日文字体
#     mkdir -p /usr/share/fonts/opentype/source-han-sans && \
#     cp /tmp/SourceHanSansJP-Regular.otf /usr/share/fonts/opentype/source-han-sans/ 

# # 下载思源黑体-韩文常规体
# RUN wget -O /tmp/SourceHanSansKR-Regular.otf https://github.com/adobe-fonts/source-han-sans/raw/release/SubsetOTF/KR/SourceHanSansKR-Regular.otf && \
# # 创建字体目录并复制韩文字体
#     mkdir -p /usr/share/fonts/opentype/source-han-sans && \
#     cp /tmp/SourceHanSansKR-Regular.otf /usr/share/fonts/opentype/source-han-sans/ 

# # 下载并解压 Noto 阿拉伯语字体
# RUN mkdir -p /usr/share/fonts/noto-arabic && \
#     wget -O /tmp/NotoSansArabic-unhinted.zip https://noto-website-2.storage.googleapis.com/pkgs/NotoSansArabic-unhinted.zip && \
#     unzip /tmp/NotoSansArabic-unhinted.zip -d /usr/share/fonts/noto-arabic && \
#     rm /tmp/NotoSansArabic-unhinted.zip 

# # 下载并解压 Noto 泰语字体
# RUN mkdir -p /usr/share/fonts/noto-thai && \
#     wget -O /tmp/NotoSansThai-unhinted.zip https://noto-website-2.storage.googleapis.com/pkgs/NotoSansThai-unhinted.zip && \
#     unzip /tmp/NotoSansThai-unhinted.zip -d /usr/share/fonts/noto-thai && \
#     rm /tmp/NotoSansThai-unhinted.zip 

# # 下载并解压 Noto 印地语
# RUN mkdir -p /usr/share/fonts/noto-hindi && \
#     wget -O /tmp/NotoSansHindi.zip https://noto-website-2.storage.googleapis.com/pkgs/NotoSansDevanagari-unhinted.zip && \
#     unzip /tmp/NotoSansHindi.zip -d /usr/share/fonts/noto-hindi/ && \
#     rm /tmp/NotoSansHindi.zip 

# # 下载并解压 NotoSans 支持拉丁语系、斯拉夫语、西里尔语，默认走该字体
# RUN mkdir -p /usr/share/fonts/noto-sans && \
#     wget -O /tmp/NotoSans.zip https://noto-website-2.storage.googleapis.com/pkgs/NotoSans-unhinted.zip && \
#     unzip /tmp/NotoSans.zip -d /usr/share/fonts/noto-sans/ && \
#     rm /tmp/NotoSans.zip 

# # 更新字体缓存
# RUN fc-cache -fv


RUN mkdir -p /home/<USER>
WORKDIR /home/<USER>
ADD output/digital-human-video-translate /home/<USER>
RUN chmod +x /home/<USER>/sbin/start.sh
EXPOSE 8080
ENTRYPOINT ["/home/<USER>/sbin/start.sh", "provide"]