apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: digital-human-video-translate
  name: digital-human-video-translate
  namespace: dh-v3
spec:
  progressDeadlineSeconds: 600
  replicas: 1
  revisionHistoryLimit: 10
  selector:
    matchLabels:
      app: digital-human-video-translate
  strategy:
    rollingUpdate:
      maxSurge: 25%
      maxUnavailable: 25%
    type: RollingUpdate
  template:
    metadata:
      labels:
        app: digital-human-video-translate
    spec:
      containers:
        - args:
            - /home/<USER>/sbin/start.sh
          command:
            - sh
          env:
            - name: POD_NAMESPACE
              valueFrom:
                fieldRef:
                  apiVersion: v1
                  fieldPath: metadata.namespace
          image: ccr-246im3mw-pub.cnc.bj.baidubce.com/digitalhuman/digital-human-video-translate:20250610_1749553267177
          imagePullPolicy: IfNotPresent
          livenessProbe:
            failureThreshold: 3
            initialDelaySeconds: 30
            periodSeconds: 10
            successThreshold: 1
            tcpSocket:
              port: 8080
            timeoutSeconds: 1
          name: digital-human-video-translate
          ports:
            - containerPort: 8080
              name: http-0
              protocol: TCP
          readinessProbe:
            failureThreshold: 3
            initialDelaySeconds: 30
            periodSeconds: 10
            successThreshold: 1
            tcpSocket:
              port: 8080
            timeoutSeconds: 1
          resources:
            limits:
              cpu: "2"
              memory: 4Gi
          startupProbe:
            failureThreshold: 3
            initialDelaySeconds: 30
            periodSeconds: 10
            successThreshold: 1
            tcpSocket:
              port: 8080
            timeoutSeconds: 1
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
            - mountPath: /home/<USER>/conf/conf.toml
              name: conf
              readOnly: true
              subPath: conf.toml
            - mountPath: /home/<USER>/conf/gcs_credentials.json
              name: gcs-credentials
              readOnly: true
              subPath: gcs-credentials.json
            - mountPath: /home/<USER>/logs
              name: log
        - env:
            - name: LOG_ES_INDEX
              value: digital-human-video-translate
            - name: LOG_PATH
              value: /home/<USER>/logs/*log
          image: ccr-246im3mw-pub.cnc.bj.baidubce.com/digitalhuman/fluentd-kafka:v0.17.5
          imagePullPolicy: IfNotPresent
          name: fluentd
          ports:
            - containerPort: 80
              name: http-0
              protocol: TCP
          resources:
            limits:
              cpu: 500m
              memory: 512Mi
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
            - mountPath: /etc/fluent/config.d
              name: fluentd-public-cm
              readOnly: true
            - mountPath: /home/<USER>/logs
              name: log
      dnsPolicy: ClusterFirst
      imagePullSecrets:
        - name: regcred-eccr
      restartPolicy: Always
      schedulerName: default-scheduler
      serviceAccount: default
      serviceAccountName: default
      terminationGracePeriodSeconds: 30
      volumes:
        - configMap:
            defaultMode: 420
            name: digital-human-video-translate
          name: conf
        - name: gcs-credentials
          secret:
            defaultMode: 420
            secretName: gcs-credentials
        - name: log
        - configMap:
            defaultMode: 420
            name: fluentd-public-cm
          name: fluentd-public-cm

---
apiVersion: v1
data:
  conf.toml: |-
    server-port = 8080
    server-name = "digital-human-video-translate"
    log-file-prefix = "localhost"
    log-file-path = "./logs/"
    log-show-code-file = true
    log-show-code-func = true

    # consul配置
    [consul-setting]
    host = "127.0.0.1"
    port = 8500
    name = ""

    # 健康检查地址
    health-url = "/health"
    check-timeout = "10s"
    check-interval = "10s"
    deregister-critical-service-after = "30s"

    # mysql配置
    [mysql-setting]
    host = "mysql80-saas-test.saas-xl-middleware.com"
    port = 3306
    database = "meta_human_editor_saas"
    username = "saas"
    password = "!Digitalhuman@baidu123"
    maxOpenConns = 1000
    maxIdlenConns = 5

    # redis配置
    [redis-setting]
    addr = "redis6-saas-test.saas-xl-middleware.com:6379"
    username = ""
    password = ""
    redisEnv = "dev"

    # 日志上传的elasticsearch配置
    [log-es-setting]
    host = "http://elasticsearch:9200"
    username = "superuser"
    password = "Baidu_dh123"

    [storage-setting]
    type = "gcs"

    [storage-setting.bos]
    # apikey
    ak = "ALTAKxdVkHMs4sp0Tgkpa3zCJP"
    # Secret Key
    sk = "e4f46ff5d1bf45c5b81dd867d6e3c148"
    endpoint = "bj.bcebos.com"
    bucket   = "xiling-dh"
    host     = "https://xiling-dh.bj.bcebos.com"
    cdn-host = "https://xiling-dh.cdn.bcebos.com"
    obfuscate = true
    retryTimes = 3
    retrySleepMillis = 1000

    [storage-setting.gcs]
    credential = "/home/<USER>/conf/gcs_credentials.json"
    bucket = "xiling_asia-southeast1_bucket"
    host = "https://storage.googleapis.com"
    cdn-host = "https://test.keevx.com"
    # path = "backend-saas-cdn/video_translate"
    path = "video_translate"
    timestamp = false
    obfuscate = true
    retryTimes = 3
    retrySleepMillis = 1000

    [schedule-setting]
    maxConcurrency = 4
    maxRunningSize = 10
    maxTaskRetryCount = 1
    maxChromaRetryCount = 3
    httpRetryCount = 3
    scheduleCronHeygenSpec = "@every 60s"

    [music-ai-setting]
    apiKey = "2e2a5d2e-467d-4ded-9c1e-d3c3257d6318"
    workflow = "music-ai/stems-vocals-accompaniment"
    user = "video-trans-dev"

    [eleven-labs-setting]
    apiKey = "sk_7fed6bb09723dd3b82ab37764aa22d13e67e290c0f5f5cc6"
    asrModelId = "scribe_v1"
    asrSentence = 1.0

    [translate-setting]
    googleCredential = "/home/<USER>/conf/gcs_credentials.json"

    [gemini-setting]
    googleCredential = "/home/<USER>/conf/gcs_credentials.json"
    projectId = "xiling-453607"
    location = "europe-west4"
    model = "gemini-2.0-flash-lite-001"

    [dh-engine-setting]
    url = "https://persona.baidu.com:8850/api/digitalhuman/open/v1/video"
    appId = "i-qicsmyfe2vfkp"
    appKey = "10qydimkanjfaa0w6edu"


    [heygen-setting]
    # 调用heygen时,使用的token类型,true从数据库中取,false从yaml配置文件中取
    useDatabaseToken = false
    # 是否开启分流至自研
    openTranslateByBaidu = false
     # 分流到自研的比例
    translateByBaidRatio= 0.6
    apiKey = "MTQyZDFmOTRjNWNjNDNmMjg5YzRlNzcxYzE1NWFkNzctMTcyOTA4MDY3Ng=="
    # 调用heygen时允许同时处理的最大视频长度,单位秒,目前限制30分钟
    hgTranslateMaxDuration = 1800 
    # 调用heygen时,超过该时间未结束,算失败,单位小时,目前限制24个小时
    hgTranslateOuttimeInHours = 24
    hgTranslateMaxNum = 200
    hgTranslateNumPerPod = 100
    hgDownLoadSyncNumPerPod = 2
    getVideoTranslateUrl = "https://api.heygen.com/v2/video_translate"
    getTargetLanguagesUrl = "https://api.heygen.com/v2/video_translate/target_languages"
    generateProofreadUrl = "https://api.heygen.com/v2/video_translate/proofread"
    checkProofreadUrl = "https://api.heygen.com/v2/video_translate/proofread/status/"
    generateVideoFromProofreadUrl = "https://api.heygen.com/v2/video_translate/proofread/"
    checkTranslateStatusUrl = "https://api.heygen.com/v2/video_translate/"
    videoTranslateCaptionUrl = "https://api.heygen.com/v2/video_translate/caption"
    # 不需要处理的失败信息，多个用逗号隔开
    excludeFailedMessage = "video pending moderation by our team,"
    # 需要排除的语言，多个语言用逗号隔开
    excludeLanguages = "Chinese,"
     # 不分流至百度的语言,多个语言用逗号隔开
    notBypassToBaiduLanguages = "Korean,"

    [dh-user-setting]
    billingBaseHost = "http://dh-user.dh-v3:80"
    quotaType = "SLIDE_DURATION"

    [risk-control-setting]
    riskControlBaseHost = "http://digital-human-figure-resource:8080"
    censorVideoPath = "/api/digitalhuman/external/riskcontrol/v1/internal/figure/video"

kind: ConfigMap
metadata:
  annotations:
    kubesphere.io/creator: admin
  name: digital-human-video-translate
  namespace: dh-v3

---
apiVersion: v1
kind: Service
metadata:
  labels:
    app: digital-human-video-translate
  name: digital-human-video-translate
  namespace: dh-v3
spec:
  ipFamilies:
    - IPv4
  ipFamilyPolicy: SingleStack
  ports:
    - name: http
      port: 8080
  selector:
    app: digital-human-video-translate

---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: digital-human-video-translate
  namespace: dh-v3
  annotations:
    nginx.ingress.kubernetes.io/proxy-body-size: 1024m
    nginx.ingress.kubernetes.io/use-regex: "true"
spec:
  ingressClassName: nginx
  rules:
    - http:
        paths:
          - backend:
              service:
                name: digital-human-video-translate
                port:
                  number: 8080
            path: /api/digitalhuman/video/translate/v1/*
            pathType: ImplementationSpecific
