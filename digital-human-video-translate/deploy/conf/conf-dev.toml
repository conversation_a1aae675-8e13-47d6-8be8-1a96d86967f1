server-port = 8080
server-name = "digital-human-video-translate"
log-file-prefix = "localhost"
log-file-path = "./logs/"
log-show-code-file = true
log-show-code-func = true

# consul配置
[consul-setting]
host = "127.0.0.1"
port = 8500
name = ""

# 健康检查地址
health-url = "/health"
check-timeout = "10s"
check-interval = "10s"
deregister-critical-service-after = "30s"

# mysql配置
[mysql-setting]
host = "127.0.0.1"
#port = 3380
port = 3306
database = "meta_human_editor_saas"
username = "saas"
password = "!Digitalhuman@baidu123"
maxOpenConns = 1000
maxIdlenConns = 5

# redis配置
[redis-setting]
# addr = "127.0.0.1:6777"
addr = "127.0.0.1:6379"
username = ""
password = ""
redisEnv = "dev"

# 日志上传的elasticsearch配置
[log-es-setting]
# host = "http://*************:8200"
host = "http://127.0.0.1:9202"
username = ""
password = ""

[storage-setting]
type = "gcs"

[storage-setting.bos]
# apikey
ak = "ALTAKxdVkHMs4sp0Tgkpa3zCJP"
# Secret Key
sk = "e4f46ff5d1bf45c5b81dd867d6e3c148"
endpoint = "bj.bcebos.com"
bucket   = "xiling-dh"
host     = "https://xiling-dh.bj.bcebos.com"
cdn-host = "https://xiling-dh.cdn.bcebos.com"
obfuscate = false
retryTimes = 3
retrySleepMillis = 1000

[storage-setting.gcs]
credential = "./deploy/conf/gcs_credentials.json"
bucket = "xiling_asia-southeast1_bucket"
path = "video_translate"
obfuscate = false
retryTimes = 3
retrySleepMillis = 1000

[schedule-setting]
maxConcurrency = 4
maxRunningSize = 10
maxTaskRetryCount = 1
maxChromaRetryCount = 3
httpRetryCount = 3
scheduleCronHeygenSpec = "@every 60s"


[music-ai-setting]
apiKey = "2e2a5d2e-467d-4ded-9c1e-d3c3257d6318"
workflow = "music-ai/stems-vocals-accompaniment"
user = "video-trans-dev"

[eleven-labs-setting]
asrHost = "https://api.elevenlabs.io/v1/speech-to-text"
apiKey = "***************************************************"
asrModelId = "scribe_v1"
asrSentence = 1.0

[translate-setting]
googleCredential = "./deploy/conf/gcs_credentials.json"
videoTranslateByBaiduHost = "http://digital-human-micro-video-trans:8080"
videoTranslateByBaiduSubmitPath = "/api/digitalhuman/video/translate/acg/v1/submit"
videoTranslateByBaiduQueryPath = "/api/digitalhuman/video/translate/acg/v1/query"


[gemini-setting]
googleCredential = "./deploy/conf/gcs_credentials.json"
projectId = "xiling-453607"
location = "europe-west4"
model = "gemini-2.0-flash-001"

[dh-engine-setting]
host = "https://persona.baidu.com:8850"
appId = "i-qicsmyfe2vfkp"
appKey = "10qydimkanjfaa0w6edu"

[heygen-setting]
apiKey = "OGM4YmZlMzA4Y2Q4NDBjM2E1ZDVjMWYxMjRhMmY4YjMtMTczNTk3NzQwNg=="
# 调用heygen时允许同时处理的最大视频长度,单位秒,目前限制30分钟
hgTranslateMaxDuration = 1800
# 调用heygen时,超过该时间未结束，算失败单位小时，目前限制24个小时
hgTranslateOuttimeInHours = 24
hgTranslateMaxNum = 100
getTargetLanguagesUrl = "https://api.heygen.com/v2/video_translate/target_languages"
generateProofreadUrl = "https://api.heygen.com/v2/video_translate/proofread"
checkProofreadUrl = "https://api.heygen.com/v2/video_translate/proofread/status/"
generateVideoFromProofreadUrl = "https://api.heygen.com/v2/video_translate/proofread/"
checkTranslateStatusUrl = "https://api.heygen.com/v2/video_translate/"
videoTranslateCaptionUrl = "https://api.heygen.com/v2/video_translate/caption"
translateVideo = "https://api.heygen.com/v2/video_translate"
# 需要排除的语言，多个语言用逗号隔开
excludeLanguages = "Chinese,"

[dh-user-setting]
billingBaseHost = "http://dh-user.dh-v3:80"
quotaType = "SLIDE_DURATION"

[risk-control-setting]
riskControlBaseHost = "http://digital-human-figure-resource:8080"
censorVideoPath = "/api/digitalhuman/external/riskcontrol/v1/internal/figure/video"
