package conf

var (
	LocalConfig = &localConfig{}
)

type localConfig struct {
	MySqlSettings      *MysqlSetting       `toml:"mysql-setting"`
	RedisSettings      *RedisSetting       `toml:"redis-setting"`
	ScheduleSettings   *ScheduleSetting    `toml:"schedule-setting"`
	MusicAiSettings    *MusicAiSetting     `toml:"music-ai-setting"`
	ElevenLabsSettings *ElevenLabsSetting  `toml:"eleven-labs-setting"`
	HeygenSettings     *HeygenSetting      `toml:"heygen-setting"`
	TranslateSettings  *TranslateSetting   `toml:"translate-setting"`
	GeminiSettings     *GeminiSetting      `toml:"gemini-setting"`
	DhEngineSettings   *DhEngineSetting    `toml:"dh-engine-setting"`
	DhUserSetting      *DhUserSetting      `toml:"dh-user-setting"`
	RiskControlSetting *RiskControlSetting `toml:"risk-control-setting"`
}

type RedisSetting struct {
	SentinelNodes    string `toml:"sentinelNodes"`    // 哨兵集群节点，ip:port，多个节点使用,分割，eg: "127.0.0.1:26379,127.0.0.1:26380,127.0.0.1:26381"
	SentinelUsername string `toml:"sentinelUsername"` // 哨兵节点用户名，可为""
	SentinelPassword string `toml:"sentinelPassword"` // 哨兵节点密码，可为""
	RouteByLatency   bool   `toml:"routeByLatency"`   // 是否将读取命令路由到从节点
	MasterName       string `toml:"masterName"`       // redis集群主节点名称
	Addr             string `toml:"addr"`             // 单节点地址，格式为ip:port，eg: "127.0.0.1:6379"
	Username         string `toml:"username"`         // redis集群主节点用户名
	Password         string `toml:"password"`         // redis集群主节点密码
	PoolSize         int    `toml:"poolSize"`         // redis连接池大小
	MinIdleConns     int    `toml:"minIdleConns"`     // 最大空闲连接数
	RedisEnv         string `toml:"redisEnv"`         // redis环境，dev/test/prod
}

type HostPortSetting struct {
	Host string `toml:"host"`
	Port int    `toml:"port"`
}

type UserNamePwdSetting struct {
	Username string `toml:"username"`
	Password string `toml:"password"`
}

type MysqlSetting struct {
	HostPortSetting
	UserNamePwdSetting
	Database     string `toml:"database"`
	MaxOpenConns int    `toml:"maxOpenConns"`
	MaxIdleConns int    `toml:"maxIdleConns"`
}

type ScheduleSetting struct {
	MaxConcurrency         int    `toml:"maxConcurrency"`         // 子任务进程并发最大值
	MaxRunningSize         int    `toml:"maxRunningSize"`         // 主任务并发的最大值
	MaxTaskRetryCount      int    `toml:"maxTaskRetryCount"`      // 任务出错重试的最大次数
	MaxChromaRetryCount    int    `toml:"maxChromaRetryCount"`    // 子任务出错重试的最大次数
	HttpRetryCount         int    `toml:"httpRetryCount"`         // http 请求的重试次数
	ScheduleCronHeygenSpec string `toml:"scheduleCronHeygenSpec"` // heygen 任务调度cron表达式
}

type MusicAiSetting struct {
	ApiKey   string `toml:"apiKey"`
	Workflow string `toml:"workflow"`
	User     string `toml:"user"`
}

type ElevenLabsSetting struct {
	AsrHost     string  `toml:"asrHost"`
	ApiKey      string  `toml:"apiKey"`
	AsrModelId  string  `toml:"asrModelId"`
	AsrSentence float64 `toml:"asrSentence"`
}

type HeygenSetting struct {
	UseDatabaseToken              bool    `toml:"useDatabaseToken"`     // 调用heygen时,使用的token类型,true从数据库中取,false从yaml配置文件中取
	OpenTranslateByBaidu          bool    `toml:"openTranslateByBaidu"` // 是否开启分流至自研
	TranslateByBaidRatio          float32 `toml:"translateByBaidRatio"` // 分流到自研的比例
	ApiKey                        string  `toml:"apiKey"`
	HgTranslateMaxDuration        int     `toml:"hgTranslateMaxDuration"`        // 调用heygen时允许同时处理的最大视频长度,单位秒
	HgTranslateOuttimeInHours     int     `toml:"hgTranslateOuttimeInHours"`     // 调用heygen时,超过该时间未结束，算失败
	HgTranslateMaxNum             int     `toml:"hgTranslateMaxNum"`             // 调用heygen时允许同时处理的最大任务数
	HgTranslateNumPerPod          int     `toml:"hgTranslateNumPerPod"`          // 调用heygen时,每个pod每次的处理数量
	HgDownLoadSyncNumPerPod       int     `toml:"hgDownLoadSyncNumPerPod"`       // 调用heygen时,每个pod可同时下载处理视频的数量
	GetTargetLanguages            string  `toml:"getTargetLanguagesUrl"`         // get  https://api.heygen.com/v2/video_translate/target_languages
	GenerateProofreadUrl          string  `toml:"generateProofreadUrl"`          // post  https://api.heygen.com/v2/video_translate/proofread
	CheckProofreadUrl             string  `toml:"checkProofreadUrl"`             // get   https://api.heygen.com/v2/video_translate/proofread/status/
	GenerateVideoFromProofreadUrl string  `toml:"generateVideoFromProofreadUrl"` // post  https://api.heygen.com/v2/video_translate/proofread/<proofread_id>/generate
	CheckTranslateStatusUrl       string  `toml:"checkTranslateStatusUrl"`       // get   https://api.heygen.com/v2/video_translate/{video_translate_id}
	VideoTranslateCaptionUrl      string  `toml:"videoTranslateCaptionUrl"`      // get   https://api.heygen.com/v2/video_translate/caption
	TranslateVideoUrl             string  `toml:"translateVideoUrl"`             // post  https://api.heygen.com/v2/video_translate
	ExcludeFailedMessage          string  `toml:"excludeFailedMessage"`          // 需要排除的失败信息，当heygen返回的错误信息为这些时，不作为失败,等待下次查询
	ExcludeLanguages              string  `toml:"excludeLanguages"`              // 需要排除的语言，多个语言用逗号隔开
	NotBypassToBaiduLanguages     string  `toml:"notBypassToBaiduLanguages"`     // 不分流至百度的语言，多个语言用逗号隔开
}

type TranslateSetting struct {
	GoogleCredential                string `toml:"googleCredential"`
	VideoTranslateByBaiduHost       string `toml:"videoTranslateByBaiduHost"`
	VideoTranslateByBaiduSubmitPath string `toml:"videoTranslateByBaiduSubmitPath"`
	VideoTranslateByBaiduQueryPath  string `toml:"videoTranslateByBaiduQueryPath"`
}

type GeminiSetting struct {
	GoogleCredential string `toml:"googleCredential"`
	ProjectId        string `toml:"projectId"`
	Location         string `toml:"location"`
	Model            string `toml:"model"`
}

type DhEngineSetting struct {
	Host   string `toml:"host"`
	AppId  string `toml:"appId"`
	AppKey string `toml:"appKey"`
}

type DhUserSetting struct {
	BillingBaseHost string `toml:"billingBaseHost"`
	QuotaType       string `toml:"quotaType"` // SLIDE_DURATION 使用这个
}

type RiskControlSetting struct {
	RiskControlBaseHost string `toml:"riskControlBaseHost"`
	CensorVideoPath     string `toml:"censorVideoPath"` //  /api/digitalhuman/external/riskcontrol/v1/internal/figure/video
}
