package musicai

import (
	"acg-ai-go-common/utils/httputil"
	"bytes"
	"encoding/json"
	"fmt"
	"golang.org/x/net/context"
	"net/http"
	"time"
)

const (
	BaseUrl string = "https://api.music.ai/api"
)

// SubmitJob 提交处理任务
func SubmitJob(logCtx context.Context, name, workflow, inputURL, user, apikey string) (jobID string, err error) {
	payload := SubmitJobRequest{
		Name:     name,
		Workflow: workflow,
		Params: &Params{
			InputUrl: inputURL,
		},
		MetaData: &MetaData{
			User: user,
		},
	}
	body, err := json.Marshal(payload)
	if err != nil {
		return "", fmt.Errorf("marshal failed: %v", err)
	}

	headers := map[string]string{
		"Authorization": apikey,
		"Content-Type":  "application/json",
	}

	httpclient := httputil.NewRetryHTTPClient(15*time.Minute, 3)
	respBody, err := httpclient.DoRequest(logCtx, http.MethodPost, BaseUrl+"/job", headers, bytes.NewReader(body))
	if err != nil {
		return "", fmt.Errorf("http request failed: %v", err)
	}

	rsp := &SubmitJobResponse{}
	err = json.Unmarshal(respBody, &rsp)
	if err != nil {
		return "", fmt.Errorf("unmarshal failed: %v", err)
	}

	if len(rsp.ID) < 1 {
		return "", fmt.Errorf("resp include some error: %s", string(respBody))
	}

	return rsp.ID, nil
}

// GetJobResult 查询任务状态
func GetJobResult(logCtx context.Context, jobID, apikey string) (*CheckJobResponse, error) {
	headers := map[string]string{
		"Authorization": apikey,
	}
	url := fmt.Sprintf("%s/job/%s", BaseUrl, jobID)
	httpclient := httputil.NewRetryHTTPClient(15*time.Minute, 3)
	respBody, err := httpclient.DoRequest(logCtx, http.MethodGet, url, headers, bytes.NewReader([]byte{}))
	if err != nil {
		return nil, fmt.Errorf("http request failed: %v", err)
	}

	rsp := &CheckJobResponse{}
	err = json.Unmarshal(respBody, &rsp)
	if err != nil {
		return nil, fmt.Errorf("unmarshal failed: %v", err)
	}

	if len(rsp.ID) < 1 {
		return nil, fmt.Errorf("resp include some error: %s", string(respBody))
	}

	return rsp, nil
}

// DeleteJob 删除任务
func DeleteJob(logCtx context.Context, jobID, apikey string) (id string, err error) {
	headers := map[string]string{
		"Authorization": apikey,
	}
	url := fmt.Sprintf("%s/job/%s", BaseUrl, jobID)
	httpclient := httputil.NewRetryHTTPClient(15*time.Minute, 3)
	respBody, err := httpclient.DoRequest(logCtx, http.MethodDelete, url, headers, bytes.NewReader([]byte{}))
	if err != nil {
		return "", fmt.Errorf("http request failed: %v", err)
	}

	rsp := &SubmitJobResponse{}
	err = json.Unmarshal(respBody, &rsp)
	if err != nil {
		return "", fmt.Errorf("unmarshal failed: %v", err)
	}

	if len(rsp.ID) < 1 {
		return "", fmt.Errorf("resp include some error: %s", string(respBody))
	}

	return rsp.ID, nil
}
