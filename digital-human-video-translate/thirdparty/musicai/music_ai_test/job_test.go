package music_ai_test

import (
	"acg-ai-go-common/utils"
	"context"
	"digital-human-video-translate/thirdparty/musicai"
	"encoding/json"
	"fmt"
	"testing"
)

const (
	Name     string = "test-triple"
	InputUrl string = "https://storage.googleapis.com/xiling_us_central1_bucket/rd-test/triple.wav"

	JobId  string = "a619dee1-03e8-497c-ab80-6b8e49e6f72d"
	ApiKey string = "2e2a5d2e-467d-4ded-9c1e-d3c3257d6318"
)

func TestSubmitJob(t *testing.T) {
	logCtx := context.WithValue(context.TODO(), utils.CtxKeyLogID, utils.RandStringRunes(10))

	jobId, err := musicai.SubmitJob(logCtx, Name, "music-ai/stems-vocals-accompaniment", InputUrl, "Ray", ApiKey)
	if err != nil {
		fmt.Printf("SubmitJob failed: %v\n", err)
		return
	}

	fmt.Printf("SubmitJob result id: %s\n", jobId)
}

func TestCheckJob(t *testing.T) {
	logCtx := context.WithValue(context.TODO(), utils.CtxKeyLogID, utils.RandStringRunes(10))

	job, err := musicai.GetJobResult(logCtx, JobId, ApiKey)
	if err != nil {
		fmt.Printf("GetJobResult failed: %v\n", err)
		return
	}

	b, _ := json.MarshalIndent(job, "", "  ")
	fmt.Printf("GetJobResult result: %v\n", string(b))

	if job.Status == "SUCCEEDED" {
		fmt.Printf("GetJobResult result vocal: %v\n", job.Result.Vocals)
		fmt.Printf("GetJobResult result bgm: %v\n", job.Result.Accompaniments)
	}
}

func TestDeleteJob(t *testing.T) {
	logCtx := context.WithValue(context.TODO(), utils.CtxKeyLogID, utils.RandStringRunes(10))

	job, err := musicai.DeleteJob(logCtx, "ss", ApiKey)
	if err != nil {
		fmt.Printf("DeleteJob failed: %v\n", err)
		return
	}

	b, _ := json.MarshalIndent(job, "", "  ")
	fmt.Printf("DeleteJob result: %v\n", string(b))
}
