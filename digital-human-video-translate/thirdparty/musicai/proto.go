package musicai

type Params struct {
	InputUrl string `json:"inputUrl,omitempty"`
}

type MetaData struct {
	User string `json:"user,omitempty"`
}

type SubmitJobRequest struct {
	Name     string    `json:"name,omitempty"`
	Workflow string    `json:"workflow,omitempty"`
	Params   *Params   `json:"params,omitempty"`
	MetaData *MetaData `json:"metadata,omitempty"`
}

type SubmitJobResponse struct {
	ID string `json:"id,omitempty"`
}

type Result struct {
	Vocals         string `json:"vocals,omitempty"`
	Accompaniments string `json:"accompaniment,omitempty"`
}

type CheckJobResponse struct {
	ID             string    `json:"id,omitempty"`
	App            string    `json:"app,omitempty"`
	Workflow       string    `json:"workflow,omitempty"`
	Status         string    `json:"status,omitempty"`
	BatchName      string    `json:"batchName,omitempty"`
	WorkflowParams *Params   `json:"workflowParams,omitempty"`
	MetaData       *MetaData `json:"metaData,omitempty"`
	Result         *Result   `json:"result,omitempty"`
	Name           string    `json:"name,omitempty"`
	CreatedAt      string    `json:"createdAt,omitempty"`
	StartedAt      string    `json:"startedAt,omitempty"`
	CompletedAt    string    `json:"completedAt,omitempty"`
}
