package ffmpeg

import (
	"acg-ai-go-common/logger"
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"os/exec"
	"strconv"
	"strings"
	"time"

	"gopkg.in/vansante/go-ffprobe.v2"
)

type VideoInfo struct {
	Width        int     `json:"width,omitempty"`
	Height       int     `json:"height,omitempty"`
	FrameRateStr string  `json:"avg_frame_rate,omitempty"`
	FrameRate    float64 `json:"framerate,omitempty"`
	DurationStr  string  `json:"duration,omitempty"`
	Duration     float64 `json:"time,omitempty"`
	GOPSize      int     `json:"gop_size,omitempty"`
}

// FfprobeJSONResult 用于解析 ffprobe 输出的 JSON 格式
type FfprobeJSONResult struct {
	Streams []VideoInfo `json:"streams"`
}

// parseFrameRate 将 "30000/1001" 转换为 float
func parseFrameRate(rateStr string) float64 {
	parts := strings.Split(rateStr, "/")
	if len(parts) == 2 {
		num, _ := strconv.ParseFloat(parts[0], 64)
		den, _ := strconv.ParseFloat(parts[1], 64)
		if den != 0 {
			return num / den
		}
	}
	return 0
}

// getGOPSize 通过分析 I 帧的间隔计算 GOP 大小
func getGOPSize(inputFile string, frameRate float64) (int, error) {
	cmd := exec.Command("ffprobe", "-v", "error",
		"-select_streams", "v:0",
		"-show_entries", "frame=pkt_pts_time,key_frame",
		"-of", "csv", inputFile)

	output, err := cmd.Output()
	if err != nil {
		return 0, fmt.Errorf("failed to get frame info: %v", err)
	}

	// 分析帧数据，获取 I 帧的时间戳
	lines := strings.Split(string(output), "\n")
	var keyFrameTimes []float64
	for _, line := range lines {
		columns := strings.Split(line, ",")
		if len(columns) == 2 {
			time, err := strconv.ParseFloat(columns[0], 64)
			if err != nil {
				continue
			}
			keyFrameFlag := columns[1]
			if keyFrameFlag == "1" { // I 帧标志
				keyFrameTimes = append(keyFrameTimes, time)
			}
		}
	}

	// 计算 I 帧之间的平均时间间隔
	if len(keyFrameTimes) < 2 {
		return 0, fmt.Errorf("not enough I frames to calculate GOP size")
	}

	intervals := make([]float64, len(keyFrameTimes)-1)
	for i := 1; i < len(keyFrameTimes); i++ {
		intervals[i-1] = keyFrameTimes[i] - keyFrameTimes[i-1]
	}

	avgInterval := sum(intervals) / float64(len(intervals))

	// GOP 大小 = 平均 I 帧间隔 * 帧率
	gopSize := int(avgInterval * frameRate)
	return gopSize, nil
}

// sum 计算浮动数值数组的和
func sum(values []float64) float64 {
	var total float64
	for _, value := range values {
		total += value
	}
	return total
}

// GetVideoInfo 获取视频的基本信息，包括 GOP 大小
func GetVideoInfo(inputFile string) (*VideoInfo, error) {
	cmd := exec.Command("ffprobe", "-v", "error",
		"-select_streams", "v:0",
		"-show_entries", "stream=width,height,avg_frame_rate,duration",
		"-of", "json", inputFile)

	output, err := cmd.Output()
	if err != nil {
		return nil, fmt.Errorf("ffprobe exec failed: %v", err)
	}

	var result FfprobeJSONResult
	if err := json.Unmarshal(output, &result); err != nil {
		return nil, fmt.Errorf("parse JSON failed: %v", err)
	}
	if len(result.Streams) == 0 {
		return nil, fmt.Errorf("video stream not found")
	}

	info := result.Streams[0]
	info.FrameRate = parseFrameRate(info.FrameRateStr)
	info.Duration, _ = strconv.ParseFloat(info.DurationStr, 64)

	// 检查是否获取到有效信息
	if info.FrameRate < 0 || info.Duration < 0 {
		return nil, fmt.Errorf("parse time param failed")
	}
	info.GOPSize = int(info.FrameRate)

	return &info, nil
}

// 获取视频的时长
func GetDurationFromURL(url string) (int64, error) {
	ctx, cancel := context.WithTimeout(context.Background(), 120*time.Second)
	defer cancel()

	data, err := ffprobe.ProbeURL(ctx, url)
	if err != nil {
		logger.Log.Errorf("GetDurationFromURL,error: %v", err)
		return 0, fmt.Errorf("ffprobe GetDurationFromURL error: %w", err)
	}
	return data.Format.Duration().Milliseconds(), nil
}

func GetIamgeWidthAndHeight(imageUrl string) (int, int, error) {
	cmd := exec.Command("ffprobe",
		"-v", "error",
		"-select_streams", "v:0",
		"-show_entries", "stream=width,height",
		"-of", "csv=p=0",
		imageUrl,
	)
	var out bytes.Buffer
	cmd.Stdout = &out
	if err := cmd.Run(); err != nil {
		logger.Log.Errorf("GetIamgeWidthAndHeight,error: %v", err)
		return 0, 0, err
	}
	output := strings.TrimSpace(out.String())
	parts := strings.Split(output, ",")
	fmt.Printf("output:%+v,parts:%+v", output, parts)

	if len(parts) != 2 {
		fmt.Println("Error executing ffprobe 111:")
		logger.Log.Errorf("GetIamgeWidthAndHeight,invalid output format")
		return 0, 0, fmt.Errorf("invalid output format")
	}
	width, _ := strconv.Atoi(parts[0])
	height, _ := strconv.Atoi(parts[1])
	logger.Log.Infof("GetIamgeWidthAndHeight ok width:%d,height:%d", width, height)
	return width, height, nil

}
