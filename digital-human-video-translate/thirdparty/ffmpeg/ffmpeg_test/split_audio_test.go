package ffmpeg_test

import (
	"acg-ai-go-common/utils"
	"context"
	"digital-human-video-translate/thirdparty/ffmpeg"
	"fmt"
	"testing"
)

func TestSplitAudioFromVideo(t *testing.T) {
	inputFile := "./input/triple.mp4"
	outputFile := "./output/triple.wav"

	logCtx := context.WithValue(context.TODO(), utils.CtxKeyLogID, utils.RandStringRunes(10))
	err := ffmpeg.ExtractAudio(logCtx, inputFile, outputFile)
	if err != nil {
		fmt.Printf("ExtractAudio failed: %v\n", err)
		return
	}

	fmt.Printf("ExtractAudio result: %s\n", outputFile)
}
