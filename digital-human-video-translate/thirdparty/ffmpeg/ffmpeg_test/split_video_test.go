package ffmpeg

import (
	"acg-ai-go-common/utils"
	"context"
	"digital-human-video-translate/ffmpeg"
	"fmt"
	"testing"
)

func TestSplitVideo(t *testing.T) {
	inputFile := "./input/triple.mp4"
	outputFile := "./output/triple_clip_1.mp4"

	logCtx := context.WithValue(context.TODO(), utils.CtxKeyLogID, utils.RandStringRunes(10))
	err := ffmpeg.ExtractVideoClip(logCtx, inputFile, outputFile, 25, 31, 57)
	if err != nil {
		fmt.Printf("ExtractVideoClip failed: %v\n", err)
		return
	}

	fmt.Printf("ExtractVideoClip result: %s\n", outputFile)
}
