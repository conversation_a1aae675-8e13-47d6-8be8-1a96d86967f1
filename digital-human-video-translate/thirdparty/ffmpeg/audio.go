package ffmpeg

import (
	"acg-ai-go-common/logger"
	"acg-ai-go-common/utils"
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"os"
	"os/exec"
	"strconv"
	"strings"
)

// ExtractAudio 从视频文件中提取音频
func ExtractAudio(ctx context.Context, inputVideo string, outputAudio string) error {
	cmd := exec.Command(
		"ffmpeg",
		"-i", inputVideo, // 输入视频
		"-vn",                  // 禁用视频流
		"-acodec", "pcm_s16le", // 16-bit PCM
		"-ar", "16000", // 采样率 16kHz
		"-ac", "1", // 单声道
		"-y", // 覆盖输出
		outputAudio,
	)

	cmdStr := strings.Join(cmd.Args, " ")
	logger.Log.Infof(utils.MMark(ctx)+"ffmpeg extract audio cmd: { %s }", cmdStr)

	cmd.Stdout = os.Stdout
	cmd.Stderr = os.Stderr

	if err := cmd.Run(); err != nil {
		return err
	}

	return nil
}

// MergeAudioVideo 将音频和视频合成，并可指定音频编码、声道数和采样率
func MergeAudioVideo(ctx context.Context, inputVideo string, inputAudio string, output string, audioCodec string,
	channels int, sampleRate int) error {
	cmd := exec.Command("ffmpeg", "-i", inputVideo, "-i", inputAudio,
		"-map", "0:v:0", "-map", "1:a:0", // 选择第一个视频流 + 独立音频流
		"-c:v", "copy",
		"-c:a", audioCodec,
		"-ac", fmt.Sprintf("%d", channels),
		"-ar", fmt.Sprintf("%d", sampleRate),
		"-y", output)

	cmdStr := strings.Join(cmd.Args, " ")
	logger.Log.Infof(utils.MMark(ctx)+"ffmpeg merge audio cmd: { %s }", cmdStr)

	cmd.Stdout = os.Stdout
	cmd.Stderr = os.Stderr

	if err := cmd.Run(); err != nil {
		return err
	}

	return nil
}

// ExtractAudioSegment 提取视频中指定时间段的音频为 16kHz 单声道 WAV 文件
func ExtractAudioSegment(ctx context.Context, inputVideo string, start float64, duration float64, audioCodec string,
	channels int, sampleRate int, outputWav string) error {
	cmd := exec.Command("ffmpeg",
		"-ss", fmt.Sprintf("%.3f", start),
		"-t", fmt.Sprintf("%.3f", duration),
		"-i", inputVideo,
		"-c:a", audioCodec,
		"-ar", fmt.Sprintf("%d", sampleRate),
		"-ac", fmt.Sprintf("%d", channels),
		"-vn",
		"-y",
		outputWav,
	)

	cmdStr := strings.Join(cmd.Args, " ")
	logger.Log.Infof(utils.MMark(ctx)+"ffmpeg extract audio cmd: { %s }", cmdStr)

	cmd.Stdout = os.Stdout
	cmd.Stderr = os.Stderr

	if err := cmd.Run(); err != nil {
		return fmt.Errorf("ffmpeg extract audio failed: %w", err)
	}
	return nil
}

// MixAudioFilesResample 统一音频格式后混音，保证采样率和声道一致。
// 最终以较长音频为准混合。
func MixAudioFilesResample(ctx context.Context, inputAudio1, inputAudio2, output string, sampleRate, channels int) error {
	temp1 := "temp_input1.wav"
	temp2 := "temp_input2.wav"

	// Step 1: 预处理 - 统一采样率和声道数
	preprocess := func(input, temp string) error {
		cmd := exec.Command("ffmpeg", "-i", input,
			"-ar", fmt.Sprintf("%d", sampleRate),
			"-ac", fmt.Sprintf("%d", channels),
			"-y", temp)
		cmd.Stdout = os.Stdout
		cmd.Stderr = os.Stderr
		return cmd.Run()
	}

	if err := preprocess(inputAudio1, temp1); err != nil {
		return fmt.Errorf("preprocess input1 failed: %w", err)
	}
	if err := preprocess(inputAudio2, temp2); err != nil {
		return fmt.Errorf("preprocess input2 failed: %w", err)
	}

	// Step 2: 混音 - 以较长音频为准
	cmd := exec.Command("ffmpeg",
		"-i", temp1,
		"-i", temp2,
		"-filter_complex", "[0:a][1:a]amix=inputs=2:duration=longest:dropout_transition=0",
		"-y", output)

	cmdStr := strings.Join(cmd.Args, " ")
	logger.Log.Infof(utils.MMark(ctx)+"ffmpeg mix (resample) cmd: { %s }", cmdStr)

	cmd.Stdout = os.Stdout
	cmd.Stderr = os.Stderr

	if err := cmd.Run(); err != nil {
		return fmt.Errorf("mix audio failed: %w", err)
	}

	// Step 3: 清理临时文件
	_ = os.Remove(temp1)
	_ = os.Remove(temp2)

	return nil
}

// MixMultipleAudioFiles 将多个音频文件混音成一个音频文件，自动统一采样率和声道数。
// 支持任意数量输入，以最长音频为准，自动清理中间文件。
func MixMultipleAudioFiles(ctx context.Context, tempPath string, inputs []string, output string, sampleRate,
	channels int) error {
	if len(inputs) < 2 {
		return fmt.Errorf("need at least two input files")
	}

	// Step 1: 对每个音频文件做 resample 统一格式
	tempFiles := make([]string, len(inputs))
	for i, input := range inputs {
		temp := fmt.Sprintf("%s/temp_input_%d.wav", tempPath, i)
		tempFiles[i] = temp

		cmd := exec.Command("ffmpeg", "-i", input,
			"-ar", fmt.Sprintf("%d", sampleRate),
			"-ac", fmt.Sprintf("%d", channels),
			"-y", temp)
		cmd.Stdout = os.Stdout
		cmd.Stderr = os.Stderr

		if err := cmd.Run(); err != nil {
			return fmt.Errorf("preprocess input %d failed: %w", i, err)
		}
	}

	// Step 2: 构建混音命令
	args := []string{}
	filterInputs := []string{}
	for i, temp := range tempFiles {
		args = append(args, "-i", temp)
		filterInputs = append(filterInputs, fmt.Sprintf("[%d:a]", i))
	}
	filter := fmt.Sprintf("%samix=inputs=%d:duration=longest:dropout_transition=0",
		strings.Join(filterInputs, ""), len(tempFiles))

	args = append(args, "-filter_complex", filter, "-y", output)

	cmd := exec.Command("ffmpeg", args...)
	cmdStr := strings.Join(cmd.Args, " ")
	logger.Log.Infof(utils.MMark(ctx)+"ffmpeg mix multiple cmd: { %s }", cmdStr)

	cmd.Stdout = os.Stdout
	cmd.Stderr = os.Stderr

	if err := cmd.Run(); err != nil {
		return fmt.Errorf("mixing failed: %w", err)
	}

	// Step 3: 删除临时文件
	for _, temp := range tempFiles {
		_ = os.Remove(temp)
	}

	return nil
}

// HasValidAudio 判断音频是否有效（使用 ffprobe）
func HasValidAudio(ctx context.Context, inputFile string) (bool, error) {
	cmd := exec.Command(
		"ffprobe",
		"-v", "quiet",
		"-print_format", "json",
		"-show_entries", "frame_tags=lavfi.astats.Overall.Max_level",
		"-f", "lavfi",
		fmt.Sprintf("amovie=%s,astats=metadata=1:reset=1", inputFile),
	)

	var out bytes.Buffer
	cmd.Stdout = &out

	cmdStr := strings.Join(cmd.Args, " ")
	logger.Log.Infof(utils.MMark(ctx)+"ffprobe check audio cmd: { %s }", cmdStr)

	if err := cmd.Run(); err != nil {
		return false, fmt.Errorf("ffprobe run error: %v", err)
	}

	// 解析 JSON 输出
	var result struct {
		Frames []struct {
			Tags map[string]string `json:"tags"`
		} `json:"frames"`
	}

	if err := json.Unmarshal(out.Bytes(), &result); err != nil {
		return false, fmt.Errorf("failed to parse ffprobe json: %v", err)
	}

	// 查找最大 dB 值
	maxDB := -1000.0
	for _, frame := range result.Frames {
		if val, ok := frame.Tags["lavfi.astats.Overall.Max_level"]; ok {
			dbValue, err := strconv.ParseFloat(val, 64)
			if err == nil && dbValue > maxDB {
				maxDB = dbValue
			}
		}
	}

	// 判断是否超过阈值（-60dB）
	if maxDB < -60 {
		return false, nil
	}
	return true, nil
}