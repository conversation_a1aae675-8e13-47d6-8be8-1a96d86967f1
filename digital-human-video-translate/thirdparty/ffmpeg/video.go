package ffmpeg

import (
	"acg-ai-go-common/logger"
	"acg-ai-go-common/utils"
	"context"
	"fmt"
	"os"
	"os/exec"
	"strings"
)

// ExtractVideoClip 截取视频的 [start, end) 段保存为一个文件
func ExtractVideoClip(ctx context.Context, input string, output string, framerate int, start, end float32) error {
	if end <= start {
		return fmt.Errorf("invalid time range: end (%d) must be greater than start (%d)", end, start)
	}
	args := []string{
		"-ss", fmt.Sprintf("%.3f", start),
		"-to", fmt.Sprintf("%.3f", end),
		"-i", input,
		"-r", fmt.Sprintf("%d", framerate),
		"-g", fmt.Sprintf("%d", framerate),
		"-keyint_min", fmt.Sprintf("%d", framerate),
		"-sc_threshold", "0",
		"-c:v", "libx264",
		"-preset", "slow",
		"-crf", "23",
		"-y",
		output,
	}

	cmd := exec.Command("ffmpeg", args...)

	cmdStr := strings.Join(cmd.Args, " ")
	logger.Log.Infof(utils.MMark(ctx)+"ffmpeg extract video clip cmd: { %s }", cmdStr)

	cmd.Stdout = os.Stdout
	cmd.Stderr = os.Stderr

	if err := cmd.Run(); err != nil {
		return fmt.Errorf("ffmpeg extract error: %v", err)
	}

	return nil
}

// ConvertToMP4 将任意格式的视频转为 H.264 编码 MP4 文件
func ConvertToMP4(ctx context.Context, frameRate int, input string, output string) error {
	args := []string{
		"-i", input, // 输入文件
		"-r", fmt.Sprintf("%d", frameRate), // 设置输出帧率为 25fps
		"-c:v", "libx264", // 视频编码器
		"-preset", "slow", // 编码速度与压缩效率的平衡
		"-crf", "23", // 质量控制（范围：0~51，越小质量越高）
		"-pix_fmt", "yuv420p", // 保证兼容性（尤其在浏览器中）
		"-y", // 自动覆盖输出文件
		output,
	}

	cmd := exec.Command("ffmpeg", args...)

	// 打印日志（可选）
	cmdStr := strings.Join(cmd.Args, " ")
	logger.Log.Infof(utils.MMark(ctx)+"ffmpeg convert to mp4 cmd: { %s }", cmdStr)

	// 标准输出和错误输出重定向
	cmd.Stdout = os.Stdout
	cmd.Stderr = os.Stderr

	// 执行命令
	if err := cmd.Run(); err != nil {
		return fmt.Errorf("ffmpeg convert error: %v", err)
	}

	return nil
}
