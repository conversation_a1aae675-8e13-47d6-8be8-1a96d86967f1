package cloud_speech2text

import (
	speech "cloud.google.com/go/speech/apiv1"
	"cloud.google.com/go/speech/apiv1/speechpb"
	"context"
	"fmt"
	"google.golang.org/api/option"
)

func TranscribeLongAudio(logCtx context.Context, credential string, language string, altLanguages []string,
	gcsURI string, sampleRate, channel int32, timestamp, diarize bool) (*speechpb.LongRunningRecognizeResponse,
	error) {
	client, err := speech.NewClient(logCtx, option.WithCredentialsFile(credential))
	if err != nil {
		return nil, fmt.Errorf("failed to create client: %v", err)
	}
	defer client.Close()

	req := &speechpb.LongRunningRecognizeRequest{
		Config: &speechpb.RecognitionConfig{
			Encoding:                   speechpb.RecognitionConfig_LINEAR16,
			SampleRateHertz:            sampleRate,
			AudioChannelCount:          channel,
			LanguageCode:               language,
			AlternativeLanguageCodes:   altLanguages,
			EnableAutomaticPunctuation: true,
			EnableWordTimeOffsets:      timestamp,
			DiarizationConfig: &speechpb.SpeakerDiarizationConfig{
				EnableSpeakerDiarization: diarize,
				MinSpeakerCount:          1,
				MaxSpeakerCount:          10,
			},
		},
		Audio: &speechpb.RecognitionAudio{
			AudioSource: &speechpb.RecognitionAudio_Uri{
				Uri: gcsURI,
			},
		},
	}

	op, err := client.LongRunningRecognize(logCtx, req)
	if err != nil {
		return nil, fmt.Errorf("failed to start recognition: %v", err)
	}

	// 等待结果返回
	resp, err := op.Wait(logCtx)
	if err != nil {
		return nil, fmt.Errorf("failed to wait for operation: %v", err)
	}
	return resp, nil
}
