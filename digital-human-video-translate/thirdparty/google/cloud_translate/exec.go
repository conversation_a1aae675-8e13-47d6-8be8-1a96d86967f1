package cloud_translate

import (
	"cloud.google.com/go/translate"
	"context"
	"fmt"
	"golang.org/x/text/language"
	"google.golang.org/api/option"
)

// DetectLanguage 检测语种
func DetectLanguage(logCtx context.Context, credential string, contents []string) ([][]translate.Detection, error) {
	client, err := translate.NewClient(logCtx, option.WithCredentialsFile(credential))
	if err != nil {
		return nil, fmt.Errorf("new client failed: %v", err)
	}
	ds, err := client.DetectLanguage(logCtx, contents)
	if err != nil {
		return nil, fmt.Errorf("detect failed: %v", err)
	}
	return ds, nil
}

func Translate(logCtx context.Context, credential string, targetLanguage, text string) (string, error) {
	lang, err := language.Parse(targetLanguage)
	if err != nil {
		return "", fmt.Errorf("language.Parse: %w", err)
	}

	client, err := translate.NewClient(logCtx, option.WithCredentialsFile(credential))
	if err != nil {
		return "", fmt.Errorf("new client failed: %v", err)
	}
	defer client.Close()

	resp, err := client.Translate(logCtx, []string{text}, lang, nil)
	if err != nil {
		return "", fmt.Errorf("translate: %w", err)
	}
	if len(resp) == 0 {
		return "", fmt.Errorf("translate returned empty response to text: %s", text)
	}
	return resp[0].Text, nil
}
