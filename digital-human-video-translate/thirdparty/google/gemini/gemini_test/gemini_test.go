package google

import (
	"acg-ai-go-common/utils"
	"context"
	"digital-human-video-translate/thirdparty/google/gemini"
	"fmt"
	"testing"
)

var (
	GoogleCredential        = "./../../../../deploy/conf/gcs_credentials.json"
	ProjectId               = "xiling-453607"
	Location                = "europe-west4"
	Model                   = "gemini-2.0-flash-lite-001"
	System           string = ""
	Content          string = "你支持多少个语种，给我一个list"
)

func TestGemini(t *testing.T) {
	logCtx := context.WithValue(context.TODO(), utils.CtxKeyLogID, utils.RandStringRunes(10))
	result, err := gemini.GenerateGeminiContent(logCtx, GoogleCredential, ProjectId, Location, Model, System, Content)
	if err != nil {
		fmt.Printf("gemini get failed: %v\n", err)
		return
	}

	fmt.Printf("result: %v\n", result)
}
