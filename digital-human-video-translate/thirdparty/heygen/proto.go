package heygen

const (
	TranslateTaskRunning string = "running"
	TranslateTaskSuccess string = "success"
	TranslateTaskFailed  string = "failed"
	TranslateTaskPending string = "pending"
)

type TranslateVideoRequest struct {
	VideoURL              string `json:"video_url"`                         // 视频地址
	Title                 string `json:"title,omitempty"`                   // 视频标题（可选）
	OutputLanguage        string `json:"output_language"`                   // 目标语言
	TranslateAudioOnly    bool   `json:"translate_audio_only,omitempty"`    // 仅翻译音频，忽略人脸（可选）
	SpeakerNum            int    `json:"speaker_num,omitempty"`             // 说话人数（可选）
	CallbackID            string `json:"callback_id,omitempty"`             // 回调标识 ID（可选）
	EnableDynamicDuration bool   `json:"enable_dynamic_duration,omitempty"` // 是否启用动态时长（可选）
	BrandVoiceID          string `json:"brand_voice_id,omitempty"`          // 语音 ID（可选）
	CallbackURL           string `json:"callback_url,omitempty"`            // 回调地址（可选）
}

type GenerateProofreadRequest struct {
	VideoURL              string `json:"video_url"`                // 视频地址
	Title                 string `json:"title,omitempty"`          // 视频标题（可选）
	OutputLanguage        string `json:"output_language"`          // 目标语言
	BrandVoiceID          string `json:"brand_voice_id,omitempty"` // 语音 ID（可选）
	SpeakerNum            int    `json:"speaker_num,omitempty"`    // 说话人数（可选）
	FolderID              string `json:"folder_id,omitempty"`      // 文件夹 ID（可选）
	EnableDynamicDuration bool   `json:"enable_dynamic_duration"`  // 是否启用动态时长（可选）
	// DisableMusicTrack       bool   `json:"disable_music_track,omitempty"`       // 禁用音乐轨道（可选）默认 false
	// EnableSpeechEnhancement bool   `json:"enable_speech_enhancement,omitempty"` // 启用音乐增强（可选）默认true
}

type GenerateVideoFromProofreadRequest struct {
	Captions             bool `json:"captions"`             // 是否需要字幕
	Translate_audio_only bool `json:"translate_audio_only"` // 仅翻译音频，忽略人脸（可选）默认 false
}

type ResponseError struct {
	Code    string `json:"code,omitempty"`
	Message string `json:"message,omitempty"`
}

type ResponseData struct {
	ProofreadId      string `json:"proofread_id,omitempty"`
	VideoTranslateId string `json:"video_translate_id,omitempty"`
	Title            string `json:"title,omitempty"`
	OutputLanguage   string `json:"output_language,omitempty"`
	Status           string `json:"status"`
	Url              string `json:"url,omitempty"`
	Message          string `json:"message,omitempty"`
	CallbackId       string `json:"callback_id,omitempty"`
	CaptionUrl       string `json:"caption_url,omitempty"` // 字幕文件的url
}

type TranslateVideoResponse struct {
	Error *ResponseError `json:"error,omitempty"`
	Data  *ResponseData  `json:"data,omitempty"`
}

type TranslateCaptionResponse struct {
	Error *ResponseError `json:"error,omitempty"`
	Data  struct {
		CaptionUrl string `json:"caption_url,omitempty"`
	} `json:"data,omitempty"`
}

type TranslateLanguageListResponse struct {
	Error *ResponseError `json:"error,omitempty"`
	Data  struct {
		Languages []string `json:"languages,omitempty"`
	} `json:"data,omitempty"`
}
