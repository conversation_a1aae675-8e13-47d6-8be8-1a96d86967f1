package fileutils

import (
	"bytes"
	"encoding/binary"
	"fmt"
	"os"
)

type WavInfo struct {
	SampleRate    uint32
	NumChannels   uint16
	BitsPerSample uint16
	PCMData       []byte
}

// WritePCMAsWav 构造 WAV 文件头并写入文件
func WritePCMAsWav(pcm []byte, filename string, sampleRate int, numChannels int) error {
	byteRate := sampleRate * numChannels * 2
	dataLength := uint32(len(pcm))
	fileSize := 36 + dataLength

	// 构建 WAV 头
	buf := &bytes.Buffer{}
	buf.WriteString("RIFF")
	err := binary.Write(buf, binary.LittleEndian, fileSize)
	if err != nil {
		return err
	}
	buf.WriteString("WAVE")

	// fmt 子块
	buf.WriteString("fmt ")
	binary.Write(buf, binary.LittleEndian, uint32(16))            // fmt 块大小
	binary.Write(buf, binary.LittleEndian, uint16(1))             // PCM 格式
	binary.Write(buf, binary.LittleEndian, uint16(numChannels))   // 通道数
	binary.Write(buf, binary.LittleEndian, uint32(sampleRate))    // 采样率
	binary.Write(buf, binary.LittleEndian, uint32(byteRate))      // 字节率
	binary.Write(buf, binary.LittleEndian, uint16(numChannels*2)) // block align
	binary.Write(buf, binary.LittleEndian, uint16(16))            // 每个样本位数

	// data 子块
	buf.WriteString("data")
	err = binary.Write(buf, binary.LittleEndian, dataLength)
	if err != nil {
		return err
	}

	// 添加 PCM 音频数据
	buf.Write(pcm)

	// 写入文件
	return os.WriteFile(filename, buf.Bytes(), 0644)
}

// ReadWavFile 读取 wav 文件并提取基本信息与 PCM 数据
func ReadWavFile(filename string) (*WavInfo, error) {
	data, err := os.ReadFile(filename)
	if err != nil {
		return nil, fmt.Errorf("读取文件失败: %w", err)
	}

	if !bytes.HasPrefix(data, []byte("RIFF")) || !bytes.Contains(data, []byte("WAVE")) {
		return nil, fmt.Errorf("不是有效的 WAV 文件")
	}

	r := bytes.NewReader(data)

	// 跳过 RIFF Header
	_, err = r.Seek(12, 0)
	if err != nil {
		return nil, err
	}

	var sampleRate uint32
	var numChannels uint16
	var bitsPerSample uint16
	var pcmData []byte

	for {
		var chunkID [4]byte
		var chunkSize uint32

		if err := binary.Read(r, binary.LittleEndian, &chunkID); err != nil {
			break
		}
		if err := binary.Read(r, binary.LittleEndian, &chunkSize); err != nil {
			break
		}

		switch string(chunkID[:]) {
		case "fmt ":
			var audioFormat uint16
			if err := binary.Read(r, binary.LittleEndian, &audioFormat); err != nil {
				return nil, err
			}
			if err := binary.Read(r, binary.LittleEndian, &numChannels); err != nil {
				return nil, err
			}
			if err := binary.Read(r, binary.LittleEndian, &sampleRate); err != nil {
				return nil, err
			}
			if _, err := r.Seek(6, 1); err != nil {
				return nil, err
			} // Skip byte rate + block align
			if err := binary.Read(r, binary.LittleEndian, &bitsPerSample); err != nil {
				return nil, err
			}
			// 跳过剩余部分（若存在）
			if _, err := r.Seek(int64(chunkSize)-16, 1); err != nil {
				return nil, err
			}

		case "data":
			pcmData = make([]byte, chunkSize)
			if _, err := r.Read(pcmData); err != nil {
				return nil, fmt.Errorf("读取 PCM 数据失败: %w", err)
			}
			// 读取完毕，跳出循环
			break

		default:
			// 跳过其他块
			if _, err := r.Seek(int64(chunkSize), 1); err != nil {
				return nil, err
			}
		}
	}

	return &WavInfo{
		SampleRate:    sampleRate,
		NumChannels:   numChannels,
		BitsPerSample: bitsPerSample,
		PCMData:       pcmData,
	}, nil
}

func ScalePCMWithChannels(srcPCM []byte, sampleRate int, start, end, tarStart, tarEnd float64,
	channels int, nextStart, lastEnd float64) ([]byte, float64, float64) {
	if channels <= 0 || len(srcPCM) == 0 || sampleRate <= 0 {
		return nil, tarStart, tarEnd
	}

	bytesPerSample := 2 // 16-bit
	sampleCount := len(srcPCM) / bytesPerSample
	frames := sampleCount / channels

	if frames == 0 || end <= start || tarEnd <= tarStart {
		return nil, tarStart, tarEnd
	}

	srcDuration := end - start
	tarDuration := tarEnd - tarStart

	// 如果距离下一个段落还有时间，也不用严格对齐
	if nextStart > tarEnd {
		tarDuration = nextStart - tarStart
	}

	// 如果已有的音频时长比较短，那也不需要缩放了
	if tarDuration > srcDuration {
		tarEnd = tarStart + srcDuration
		return srcPCM, tarStart, tarEnd
	}

	//// 如果音频也可以前移
	//if lastEnd < tarStart {
	//	// 前移之后不需要缩放的情况
	//	if tarEnd-srcDuration > lastEnd {
	//		tarStart = tarEnd - srcDuration
	//	} else {
	//		tarStart = lastEnd
	//	}
	//	tarDuration = tarEnd - tarStart
	//}
	//
	//// 如果已有的音频时长比较短，那也不需要缩放了
	//if tarDuration > srcDuration {
	//	return srcPCM, tarStart, tarEnd
	//}

	targetFrames := int(float64(frames) * (tarDuration / srcDuration))
	if targetFrames <= 0 {
		return nil, tarStart, tarEnd
	}
	// 输出数据
	result := make([]byte, targetFrames*channels*bytesPerSample)

	// 遍历每个声道
	for ch := 0; ch < channels; ch++ {
		// 提取当前声道数据为 int16
		srcChan := make([]int16, frames)
		for i := 0; i < frames; i++ {
			offset := (i*channels + ch) * bytesPerSample
			srcChan[i] = int16(binary.LittleEndian.Uint16(srcPCM[offset : offset+2]))
		}

		// 插值缩放该声道
		for i := 0; i < targetFrames; i++ {
			srcPos := float64(i) * float64(frames-1) / float64(targetFrames-1)
			left := int(srcPos)
			right := left + 1
			if right >= frames {
				right = frames - 1
			}
			frac := srcPos - float64(left)
			val := float64(srcChan[left])*(1-frac) + float64(srcChan[right])*frac
			sample := int16(val)

			// 写回交错数据
			dstOffset := (i*channels + ch) * bytesPerSample
			binary.LittleEndian.PutUint16(result[dstOffset:], uint16(sample))
		}
	}

	return result, tarStart, tarEnd
}
