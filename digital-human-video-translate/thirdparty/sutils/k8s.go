package sutils

import (
	"digital-human-video-translate/conf"
	"fmt"
	"os"
)

func GetPodName() (string, error) {
	if podName := os.Getenv("HOSTNAME"); podName != "" {
		return podName, nil
	}

	if podName := os.Getenv("POD_NAME"); podName != "" {
		return podName, nil
	}

	return "", fmt.<PERSON><PERSON><PERSON>("无法获取 Pod 名称")
}

func GetNameByRunEnv() string {
	return conf.LocalConfig.RedisSettings.RedisEnv
}
