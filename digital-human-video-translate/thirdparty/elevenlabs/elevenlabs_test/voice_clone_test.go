package elevenlabs

import (
	"context"
	"digital-human-video-translate/thirdparty/elevenlabs/voice_clone"
	"digital-human-video-translate/thirdparty/sutils/fileutils"
	"encoding/base64"
	"fmt"
	"testing"
)

func TestVoiceCLone(t *testing.T) {
	logCtx := context.Background()

	apiKey := "***************************************************"
	req := voice_clone.AddVoiceRequest{
		Name:                  "test",
		Description:           "test",
		RemoveBackgroundNoise: true,
	}
	files := []string{"/Users/<USER>/Downloads/video-translate/cache/task/vte-cZ3ynPGywMJVczD37ba7dYrA/input/item." +
		"wav"}

	voice, err := voice_clone.AddVoice(logCtx, apiKey, files, req)
	if err != nil {
		fmt.Printf("voice clone failed: %v\n", err)
		return
	}

	fmt.Printf("voice id: %v\n", voice.VoiceID)
}

func TestVoiceDelete(t *testing.T) {
	logCtx := context.Background()

	apiKey := "***************************************************"
	err := voice_clone.DeleteVoice(logCtx, apiKey, "PsJJU66cc81WcNXu0JDq")
	if err != nil {
		fmt.Printf("voice clone failed: %v\n", err)
		return
	}

	fmt.Printf("delete voice\n")
}

func TestVoiceTts(t *testing.T) {
	logCtx := context.Background()

	apiKey := "***************************************************"
	tts, err := voice_clone.DoTts(logCtx, apiKey, "VBc8F8QMf1xOOHh1sxH7", "hello")
	if err != nil {
		fmt.Printf("voice tts failed: %v\n", err)
		return
	}
	pcmData, err := base64.StdEncoding.DecodeString(tts.AudioBase64)
	if err != nil {
		fmt.Printf("base64 decode failed: %v\n", err)
		return
	}

	filePath := "./output/tts.wav"
	err = fileutils.WritePCMAsWav(pcmData, filePath, 16000, 1)
	if err != nil {
		fmt.Printf("write wav failed: %v", err)
		return
	}

	fmt.Printf("tts: %d\n", len(tts.AudioBase64))
}
