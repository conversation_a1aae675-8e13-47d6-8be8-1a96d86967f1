package elevenlabs_test

import (
	"context"
	"digital-human-video-translate/thirdparty/elevenlabs/audio_isolation"
	"fmt"
	"os"
	"path/filepath"
	"testing"
)

func TestAudioIsolate(t *testing.T) {
	logCtx := context.Background()
	resp, err := audio_isolation.DoAudioIsolate(logCtx, "***************************************************",
		"./input/triple.wav")
	if err != nil {
		fmt.Printf("do audio isolate failed: %v\n", err)
		return
	}

	outPath := "./output/triple.wav"
	dir := filepath.Dir(outPath)
	if err := os.MkdirAll(dir, os.ModePerm); err != nil {
		fmt.Printf("create out path failed: %v\n", err)
		return
	}

	err = os.WriteFile(outPath, resp, 0644)
	if err != nil {
		fmt.Printf("write to out file failed: %v\n", err)
		return
	}

	fmt.Printf("result at: %s\n", outPath)
}
