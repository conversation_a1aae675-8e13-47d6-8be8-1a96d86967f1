package speech2text

import (
	"acg-ai-go-common/utils/httputil"
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"mime/multipart"
	"net/http"
	"os"
	"path/filepath"
	"strconv"
	"strings"
	"time"
)

const (
	URL string = "https://api.elevenlabs.io/v1/speech-to-text"
)

func DoSpeechToText(
	logCtx context.Context,
	apiKey string,
	req *Request,
) (*Response, error) {
	if req == nil {
		return nil, fmt.Errorf("request struct is null")
	}
	if req.ModelId == "" {
		return nil, fmt.Errorf("model_id is required")
	}
	if req.File == "" && req.CloudStorageUrl == "" {
		return nil, fmt.Errorf("either file or cloud_storage_url is required")
	}

	var buf bytes.Buffer
	writer := multipart.NewWriter(&buf)

	write := func(field, value string) error {
		return writer.WriteField(field, value)
	}

	if err := write("model_id", req.ModelId); err != nil {
		return nil, fmt.Errorf("write model_id: %v", err)
	}
	if req.LanguageCode != "" {
		if err := write("language_code", req.LanguageCode); err != nil {
			return nil, fmt.Errorf("write language_code: %v", err)
		}
	}
	if req.TagAudioEvents {
		if err := write("tag_audio_events", "true"); err != nil {
			return nil, fmt.Errorf("write tag_audio_events: %v", err)
		}
	}
	if req.NumSpeakers > 0 {
		if err := write("num_speakers", strconv.Itoa(req.NumSpeakers)); err != nil {
			return nil, fmt.Errorf("write num_speakers: %v", err)
		}
	}
	if req.TimestampsGranularity != "" {
		if err := write("timestamps_granularity[]", req.TimestampsGranularity); err != nil {
			return nil, fmt.Errorf("write timestamps_granularities: %v", err)
		}
	}
	if req.Diarize {
		if err := write("diarize", "true"); err != nil {
			return nil, fmt.Errorf("write diarize: %v", err)
		}
	}
	if req.FileFormat != "" {
		if err := write("file_format", req.FileFormat); err != nil {
			return nil, fmt.Errorf("write file_format: %v", err)
		}
	}

	for _, format := range req.AdditionalFormats {
		if format == nil {
			continue
		}
		prefix := "additional_formats[]"
		if err := write(prefix+"[format]", format.Format); err != nil {
			return nil, fmt.Errorf("write format: %v", err)
		}
		if err := write(prefix+"[include_speakers]", strconv.FormatBool(format.IncludeSpeakers)); err != nil {
			return nil, fmt.Errorf("write include_speakers: %v", err)
		}
		if err := write(prefix+"[include_timestamps]", strconv.FormatBool(format.IncludeTimestamps)); err != nil {
			return nil, fmt.Errorf("write include_timestamps: %v", err)
		}
		if format.MaxCharactersPerLine > 0 {
			if err := write(prefix+"[max_characters_per_line]", strconv.Itoa(format.MaxCharactersPerLine)); err != nil {
				return nil, fmt.Errorf("write max_characters_per_line: %v", err)
			}
		}
		if format.MaxSegmentChars > 0 {
			if err := write(prefix+"[max_segment_chars]", strconv.Itoa(format.MaxSegmentChars)); err != nil {
				return nil, fmt.Errorf("write max_segment_chars: %v", err)
			}
		}
		if format.MaxSegmentDurationS > 0 {
			if err := write(prefix+"[max_segment_duration_s]", fmt.Sprintf("%.2f", format.MaxSegmentDurationS)); err != nil {
				return nil, fmt.Errorf("write max_segment_duration_s: %v", err)
			}
		}
		if format.SegmentOnSilenceLongerThanS > 0 {
			if err := write(prefix+"[segment_on_silence_longer_than_s]", fmt.Sprintf("%.2f", format.SegmentOnSilenceLongerThanS)); err != nil {
				return nil, fmt.Errorf("write segment_on_silence_longer_than_s: %v", err)
			}
		}
	}

	if req.File != "" {
		file, err := os.Open(req.File)
		if err != nil {
			return nil, fmt.Errorf("open file: %v", err)
		}
		defer file.Close()
		part, err := writer.CreateFormFile("file", filepath.Base(req.File))
		if err != nil {
			return nil, fmt.Errorf("create form file: %v", err)
		}
		if _, err := io.Copy(part, file); err != nil {
			return nil, fmt.Errorf("copy file: %v", err)
		}
	} else if req.CloudStorageUrl != "" {
		if err := write("url", req.CloudStorageUrl); err != nil {
			return nil, fmt.Errorf("write cloud_storage_url: %v", err)
		}
	}

	if err := writer.Close(); err != nil {
		return nil, fmt.Errorf("close writer: %v", err)
	}

	headers := map[string]string{
		"xi-api-key":   apiKey,
		"Content-Type": writer.FormDataContentType(),
	}

	httpclient := httputil.NewRetryHTTPClient(15*time.Minute, 3)
	respBody, err := httpclient.DoRequest(logCtx, http.MethodPost, URL, headers, &buf)
	if err != nil {
		return nil, fmt.Errorf("DoRequest failed: %v", err)
	}

	var resp Response
	if err := json.Unmarshal(respBody, &resp); err != nil {
		return nil, fmt.Errorf("unmarshal response: %v, body: %s", err, string(respBody))
	}

	return &resp, nil
}

func SplitResponseBySpeakerAndPause(resp *Response, gapThreshold float64, totalDuration float64) []*Speaker {
	if resp == nil || len(resp.Words) == 0 {
		return nil
	}

	speakerMap := make(map[string][]*Word)
	speakerOrder := []string{} // 顺序记录

	// Step 1: Group words by speaker and maintain order
	for _, word := range resp.Words {
		if word == nil || word.Type != "word" {
			continue
		}
		if _, exists := speakerMap[word.SpeakerId]; !exists {
			speakerOrder = append(speakerOrder, word.SpeakerId)
		}
		speakerMap[word.SpeakerId] = append(speakerMap[word.SpeakerId], word)
	}

	var speakers []*Speaker

	// Step 2: Process in order of appearance
	for _, speakerID := range speakerOrder {
		words := speakerMap[speakerID]
		sentences := []*Sentence{}
		var currentWords []*Word

		for i, word := range words {
			if len(currentWords) == 0 {
				currentWords = append(currentWords, word)
				continue
			}

			prev := currentWords[len(currentWords)-1]
			timeGap := word.Start - prev.End

			if timeGap > gapThreshold {
				sentences = append(sentences, createSentenceFromWords(currentWords))
				currentWords = []*Word{}
			}
			currentWords = append(currentWords, word)

			if i == len(words)-1 && len(currentWords) > 0 {
				sentences = append(sentences, createSentenceFromWords(currentWords))
			}
		}

		for i, _ := range sentences {
			// 这里要处理一下静音区，这个识别引擎多加了2s的静音拖尾
			if totalDuration > 0 && totalDuration-sentences[i].End > 2 && sentences[i].End-2 > sentences[i].LastStart {
				sentences[i].End -= 2
			}
		}

		speakers = append(speakers, &Speaker{
			Id:        speakerID,
			Sentences: sentences,
			Lang:      resp.LanguageCode,
		})
	}

	return speakers
}

func createSentenceFromWords(words []*Word) *Sentence {
	if len(words) == 0 {
		return nil
	}
	textParts := make([]string, 0, len(words))
	for _, w := range words {
		textParts = append(textParts, w.Text)
	}
	return &Sentence{
		Text:      strings.Join(textParts, ""),
		Start:     words[0].Start,
		End:       words[len(words)-1].End,
		LastStart: words[len(words)-2].Start,
	}
}
