package routers

import (
	"acg-ai-go-common/global"
	"acg-ai-go-common/logger"
	"bytes"
	"io"
	"io/ioutil"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/sirupsen/logrus"
)

var (
	GinRouter *gin.Engine
)

// InitRouter 初始化gin routers
func InitRouter() {
	switch global.ServerSetting.RunEnv {
	case global.DevEnv:
		gin.SetMode(gin.DebugMode)
		GinRouter = gin.New()
	case global.TestEnv:
		gin.SetMode(gin.DebugMode)
		GinRouter = gin.New()
	case global.ProdEnv:
		gin.SetMode(gin.ReleaseMode)
		GinRouter = gin.New()
	default:
		gin.SetMode(gin.DebugMode)
		GinRouter = gin.New()
	}

	GinRouter.MaxMultipartMemory = 60 << 20 // 最大上传文件大小60MB

	// 日志设置
	GinRouter.Use(
		GinLog(
			// 开启日志保存Es，默认不开启
			logger.SetOpenReportEs(true),
			// 使用异步保存Es，true为同步上传(会影响API响应速度)
			logger.SetSyncReportEs(false),
			// 设置输出保存的日志级别
			logger.SetLogLevel(logrus.InfoLevel),
		),
	)

	// 健康检查 Router
	HealthRouter(GinRouter)

	// 业务 Router
	Routers(GinRouter)
}

// GinLog Gin框架日志输出中间件
func GinLog(options ...logger.OptionFunc) gin.HandlerFunc {
	// 初始化GinLog logrus对象，Info级别及以上日志异步上报ES，Es信息需要通过toml文件的log-es-setting配置
	// 这里避免本地调试时无Elasticsearch，影响开发进度，因此不考虑error
	l, _ := logger.GetLogger(options...)
	return func(c *gin.Context) {
		// 判断请求路径是否是健康检查的路由，如果是则跳过日志后续处理
		if strings.HasSuffix(c.Request.URL.Path, "/health") {
			c.Next()
			return
		}

		// 开始时间
		startTime := time.Now()

		// 链路ID
		traceID := c.GetHeader(global.HeaderTraceID)
		if len(traceID) == 0 {
			traceID = uuid.NewString()
			c.Request.Header.Set(global.HeaderTraceID, traceID)
		}

		// 请求ID
		requestID := c.GetHeader(global.HeaderRequestID)
		if len(requestID) == 0 {
			requestID = uuid.NewString()
			c.Request.Header.Set(global.HeaderRequestID, requestID)
		}

		// 用户ID TODO 可能需要使用某API通过Token等自行转换
		userID := c.GetHeader(global.HeaderUserID)

		// 请求方式
		reqMethod := c.Request.Method

		// 请求路由
		reqURI := c.Request.RequestURI
		// 状态码
		statusCode := c.Writer.Status()

		// 请求IP
		clientIP := c.ClientIP()
		reqBody := make([]byte, 0)

		contentTypeReq := c.Request.Header.Get("Content-Type")
		if !strings.Contains(contentTypeReq, "form-data") &&
			reqURI != "/api/digitalhuman/external/riskcontrol/v1/figure/images" { // 请求Body
			reqBody, _ = ioutil.ReadAll(c.Request.Body)
			if len(reqBody) > 0 {
				c.Request.Body = io.NopCloser(bytes.NewBuffer(reqBody))
			}
		} else {
			reqBody = []byte("由于请求内容属于文件数据,暂不记录")
		}

		// 输出请求日志
		l.WithFields(logrus.Fields{
			logger.LogFieldHostname:      global.ServerSetting.Hostname,
			logger.LogFieldServerName:    global.ServerSetting.ServerName,
			logger.LogFieldEnv:           global.ServerSetting.RunEnv,
			logger.LogFieldTraceID:       traceID,
			logger.LogFieldRequestID:     requestID,
			logger.LogFieldClientIP:      clientIP,
			logger.LogFieldMethod:        reqMethod,
			logger.LogFieldURI:           reqURI,
			logger.LogFieldUserID:        userID,
			logger.LogFieldToken:         c.GetHeader(global.HeaderToken),
			logger.LogFieldAuthorization: c.GetHeader(global.HeaderAuthorization),
			logger.LogFieldRequestBody:   string(reqBody),
			logger.LogFieldCreateTime:    startTime.UnixMilli(),
		}).Infof("clientIP: %15s, reqMethod: %s, reqURI: %s",
			clientIP,
			reqMethod,
			reqURI,
		)

		blw := &logger.ResponseWriterWrapper{Body: bytes.NewBufferString(""), ResponseWriter: c.Writer}
		c.Writer = blw
		c.Writer.Header().Set(global.HeaderTraceID, traceID)
		c.Writer.Header().Set(global.HeaderRequestID, requestID)
		// 处理请求
		c.Next()

		// 执行时间
		latencyTime := time.Now().Sub(startTime).String()
		contentType := blw.Header().Get("Content-Type")
		bodyStr := "当前仅支持输出Json或SSE响应结果,当前类型是:" + contentType
		if strings.Contains(contentType, "application/json") ||
			strings.Contains(contentType, "text/event-stream") {
			bodyStr = blw.Body.String()
		}
		// 输出响应日志
		l.WithFields(logrus.Fields{
			logger.LogFieldHostname:     global.ServerSetting.Hostname,
			logger.LogFieldServerName:   global.ServerSetting.ServerName,
			logger.LogFieldEnv:          global.ServerSetting.RunEnv,
			logger.LogFieldUserID:       userID,
			logger.LogFieldTraceID:      traceID,
			logger.LogFieldRequestID:    requestID,
			logger.LogFieldCost:         latencyTime,
			logger.LogFieldStatusCode:   statusCode,
			logger.LogFieldResponseBody: bodyStr,
			logger.LogFieldCreateTime:   time.Now().UnixMilli(),
		}).Infof("cost: %13v, statusCode: %v",
			latencyTime,
			statusCode,
		)
	}
}
