package routers

import (
	"digital-human-video-translate/handler/controller"
	dh_user "digital-human-video-translate/handler/dh-user"

	"github.com/gin-gonic/gin"
)

// Routers 路由列表
func Routers(e *gin.Engine) {
	apiV1 := e.Group("/api/digitalhuman/video/translate/v1")
	{
		// 提交任务
		apiV1.POST("/submit", dh_user.<PERSON><PERSON><PERSON>ser<PERSON><PERSON><PERSON>, controller.TaskHandler{}.HandleSubmitTask)
		// 获取语言列表
		apiV1.GET("/language/list", dh_user.Dh<PERSON>serChe<PERSON>, controller.HandleLanguageList)

	}

	internalApiV1 := e.Group("/api/digitalhuman/video/translate/internal/v1")
	{
		// 查询任务列表
		internalApiV1.POST("/list", controller.TranslateVideoList)
		// 删除任务
		internalApiV1.POST("/batch/delete", controller.TranslateDelete)
	}
}
