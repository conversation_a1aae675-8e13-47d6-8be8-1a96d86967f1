package dh_user

import (
	"acg-ai-go-common/logger"
	"acg-ai-go-common/utils"
	"acg-ai-go-common/utils/httputil"
	"bytes"
	"context"
	"digital-human-video-translate/beans/proto"
	"digital-human-video-translate/conf"

	"encoding/json"
	"errors"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

const (
	DhUserInfoPath           = "/api/internal/workflow/v2/user/auth/info"              // 获取用户信息
	DhUserQuotaFreeze        = "/api/internal/workflow/v1/account/quota/freezeQuota"   // 冻结
	DhUserQuotaUnFreeze      = "/api/internal/workflow/v1/account/quota/unFreezeQuota" // 解冻
	DhUserQuotaConfirmedCost = "/api/internal/workflow/v1/account/quota/confirmedCost" // 确认消耗
	DhUserInfoListPath       = "/api/internal/workflow/v1/account/user/list"           // 配额列表
)

func DhUserCheck(c *gin.Context) {
	reqHeader := map[string]string{
		"Cookie":  c.GetHeader("Cookie"),
		"Host":    c.GetHeader("Host"),
		"Origin":  c.GetHeader("Origin"),
		"Referer": c.GetHeader("Referer"),
	}
	logger.CtxLog(c).Infof("DhUserCheck GetDhUserInfo strat, reqHeader:%+v", reqHeader)
	rsp, err := GetDhUserInfo(c)
	if err != nil {
		logger.CtxLog(c).Errorf("DhUserCheck GetDhUserInfo fail, header:%v, err:%v", reqHeader, err)
		c.AbortWithStatusJSON(http.StatusOK, proto.NewCommonResponse(590001, "服务内部异常", nil))
		return
	} else if rsp == nil || !rsp.Success {
		c.AbortWithStatusJSON(http.StatusOK, rsp)
		return
	}
	var subChannel string
	if rsp.Result.Tags != nil {
		subChannel = rsp.Result.Tags.SubChannel
	}
	logger.CtxLog(c).Infof("DhUserCheck GetDhUserInfo success, rsp:%+v,subChannel:%s", rsp, subChannel)
	c.Set("AccountId", rsp.Result.AccountId)
	c.Set("UserId", rsp.Result.Uid)
	c.Set("UserName", rsp.Result.Username)
	c.Set("UserChannel", subChannel)
	c.Next()
}

func DhUserCheckByPass(c *gin.Context) {
	reqHeader := map[string]string{
		"Cookie":         c.GetHeader("Cookie"),
		"Host":           c.GetHeader("Host"),
		"Origin":         c.GetHeader("Origin"),
		"Referer":        c.GetHeader("Referer"),
		"weixin-session": c.GetHeader("weixin-session"),
	}
	logger.CtxLog(c).Infof("DhUserCheck GetDhUserInfo strat, reqHeader:%+v", reqHeader)
	rsp, err := GetDhUserInfo(c)
	if err != nil {
		logger.CtxLog(c).Warnf("DhUserCheck GetDhUserInfo fail, header:%v, err:%v", reqHeader, err)
	} else if rsp != nil && rsp.Success {
		var subChannel string
		if rsp.Result.Tags != nil {
			subChannel = rsp.Result.Tags.SubChannel
		}
		logger.CtxLog(c).Infof("DhUserCheck GetDhUserInfo success, rsp:%+v,subChannel:%s", rsp, subChannel)
		c.Set("AccountId", rsp.Result.AccountId)
		c.Set("UserId", rsp.Result.Uid)
		c.Set("UserName", rsp.Result.Username)
		c.Set("UserChannel", subChannel)
	} else {
		logger.CtxLog(c).Warnf("User not login, DhUserCheck GetDhUserInfo fail, rsp:%+v", rsp)
	}
	c.Next()
}

func GetDhUserInfo(c *gin.Context) (*DhUserInfoRsp, error) {
	header := map[string]string{
		"Cookie":  c.GetHeader("Cookie"),
		"Host":    c.GetHeader("Host"),
		"Origin":  c.GetHeader("Origin"),
		"Referer": c.GetHeader("Referer"),
	}
	logger.CtxLog(c).Infof("GetDhUserInfo header:%v", header)
	url := conf.LocalConfig.DhUserSetting.BillingBaseHost + DhUserInfoPath
	rsp := &DhUserInfoRsp{}
	client := httputil.NewRetryHTTPClient(10*time.Second, 3)

	logCtx := context.WithValue(context.TODO(), utils.CtxKeyLogID, uuid.New().String())
	res, err := client.DoRequest(logCtx, "GET", url, header, nil)
	if err != nil {
		logger.CtxLog(c).Errorf("GetDhUserInfo DoRequest fail, err:%v", err)
		return nil, errors.New("服务内部异常")
	}

	err = json.Unmarshal(res, rsp)
	if err != nil {
		logger.CtxLog(c).Errorf("GetDhUserInfo Unmarshal fail, err:%v", err)
		return nil, errors.New("服务内部异常")
	}
	logger.CtxLog(c).Infof("GetDhUserInfo DoRequest end, res:%v", string(res))
	return rsp, err
}

// QuotaFreeze 配额冻结
func QuotaFreeze(req *QuotaFreezeReq) (*QuotaFreezeRsp, error) {
	url := conf.LocalConfig.DhUserSetting.BillingBaseHost + DhUserQuotaFreeze
	var rsp *QuotaFreezeRsp
	err := httputil.PostJson(url, req, &rsp)
	return rsp, err
}

// QuotaUnFreeze 配额解冻
func QuotaUnFreeze(req *QuotaUnFreezeReq) (*QuotaUnFreezeRsp, error) {
	url := conf.LocalConfig.DhUserSetting.BillingBaseHost + DhUserQuotaUnFreeze
	var rsp *QuotaUnFreezeRsp
	err := httputil.PostJson(url, req, &rsp)
	return rsp, err
}

// QuotaConfirmedCost 配额确认扣除
func QuotaConfirmedCost(req *QuotaConfirmedCostReq) (*QuotaConfirmedCostRsp, error) {
	url := conf.LocalConfig.DhUserSetting.BillingBaseHost + DhUserQuotaConfirmedCost
	var rsp *QuotaConfirmedCostRsp
	err := httputil.PostJson(url, req, &rsp)
	return rsp, err
}

func GetDhUserInfoList(logCtx context.Context, req *DhUserInfoListReq) (DhUserInfoListRsp, error) {
	url := conf.LocalConfig.DhUserSetting.BillingBaseHost + DhUserInfoListPath
	var rsp DhUserInfoListRsp
	client := httputil.NewRetryHTTPClient(15*time.Second, 3)

	body, err := json.Marshal(req)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"json Marshal error:%v", err)
		return rsp, err
	}

	headers := map[string]string{
		"Content-Type": "application/json",
	}

	res, err := client.DoRequest(logCtx, "POST", url, headers, bytes.NewReader(body))
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"GetDhUserInfoList DoRequest error:%v", err)
		return rsp, err
	}

	logger.Log.Infof(utils.MMark(logCtx)+"GetDhUserInfoList res:%s", string(res))

	err = json.Unmarshal(res, &rsp)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"GetDhUserInfoList Unmarshal error:%v", err)
		return rsp, err
	}

	return rsp, err
}
