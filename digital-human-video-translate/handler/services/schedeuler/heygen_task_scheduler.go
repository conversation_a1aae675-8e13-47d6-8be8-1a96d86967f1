package schedeuler

import (
	"acg-ai-go-common/gomysql"
	"acg-ai-go-common/logger"
	"acg-ai-go-common/storage"
	"acg-ai-go-common/utils"
	"acg-ai-go-common/utils/elevenlabs"
	"acg-ai-go-common/utils/httputil"
	"acg-ai-go-common/utils/redisproxy"
	"bytes"
	"context"
	"digital-human-video-translate/beans/constant"
	"digital-human-video-translate/beans/enums"
	"digital-human-video-translate/beans/model"
	"digital-human-video-translate/beans/proto"
	"digital-human-video-translate/conf"
	"digital-human-video-translate/handler/billing"
	"digital-human-video-translate/thirdparty/ffmpeg"
	"digital-human-video-translate/thirdparty/heygen"
	"digital-human-video-translate/thirdparty/sutils"
	"digital-human-video-translate/thirdparty/sutils/fileutils"
	"digital-human-video-translate/thirdparty/sutils/redislock"
	"encoding/json"
	"errors"
	"fmt"
	"math"
	"net/url"
	"os"
	"os/exec"
	"path/filepath"
	"strings"
	"time"

	"github.com/google/uuid"
)

const (
	VideoTranslateHandleKeyPre            = "HgVideoTranslateHandleKey"
	VideoTranslateHandleKeyFmt            = "%s:" + VideoTranslateHandleKeyPre + ":xiling-saas-v3" + ":%s"
	VideoTranslateHandleNumOutTimeLockExp = 30 * time.Minute

	CacheDir        = "./cache"
	DownloadPathFmt = CacheDir + "/%s_%s_%s" // accountID/uuid/fileName/
	// ServerName      = "/video-translate"
	VideoToBosFmt = "/%s/%s/%s" // accountID/uuid/fileName/

	VideoExtractAudioPathFmt = CacheDir + "/%s_%s_%s" // accountID/uuid/fileName/

)

// 当前pod正在下载处理翻译成功视频的数量
var translateSuccessDownLoadCurrentNum = 0

func HeygenTaskScheduler() {
	logId := uuid.New().String()
	logCtx := context.WithValue(context.TODO(), utils.CtxKeyLogID, logId)
	currentRunningNum, err := (&model.VideoTranslateEntity{}).GetTranslateRunningCount(gomysql.DB)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"HeygenTaskScheduler GetTranslateRunningCount,error:%v", err)
		return
	}

	logger.Log.Infof(utils.MMark(logCtx)+"HeygenTaskScheduler start currentRunningNum:%d", currentRunningNum)
	maxNum := conf.LocalConfig.HeygenSettings.HgTranslateMaxNum

	// 目前允许处理的最大数量
	canHandleNum := maxNum - int(currentRunningNum)
	// 当前pod本次能处理的数量
	perHandlleNum := conf.LocalConfig.HeygenSettings.HgTranslateNumPerPod
	if perHandlleNum > canHandleNum {
		perHandlleNum = canHandleNum
	}
	videoModels, err := (&model.VideoTranslateEntity{}).GetLimitedCountItemInRunningByStatus(gomysql.DB, perHandlleNum)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"HeygenTaskScheduler GetLimitedCountItemInRunningByStatus,error:%v", err)
		return
	}
	logger.Log.Infof(utils.MMark(logCtx)+"HeygenTaskScheduler GetLimitedCountItemInRunningByStatus,maxNum:%d,currentNum:%d,perHandlleNum:%d,videoModels.size:%d,", maxNum, currentRunningNum, perHandlleNum, len(videoModels))

	err = os.MkdirAll(CacheDir, 0755)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"HeygenTaskScheduler  MkdirAll() errer=%v", err)
		return
	}

	for idTemp, videoItemTemp := range videoModels {
		videoModel := videoItemTemp
		idx := idTemp
		handleVideoTranslateModel(logCtx, idx, videoModel)
	}
}

func handleVideoTranslateModel(logCtx context.Context, idx int, videoModel model.VideoTranslateEntity) {
	videoID := videoModel.VideoID
	userID := videoModel.UserID

	// 获取 redis 分布式锁
	redisproxy := redisproxy.GetRedisProxy()
	redisLockKey := fmt.Sprintf(VideoTranslateHandleKeyFmt, sutils.GetNameByRunEnv(), videoID)
	redisLock := redislock.NewRedisLock(redisproxy.Rdb, redisLockKey, VideoTranslateHandleNumOutTimeLockExp)
	ok, err := redisLock.Lock(context.Background())
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"HeygenTaskScheduler redisLock.Lock error key: %s error: %+v\n", redisLockKey, err)
		return
	} else if !ok {
		logger.Log.Infof(utils.MMark(logCtx)+"HeygenTaskScheduler not acquire redisLock.Lock, key: %s", redisLockKey)
		return
	}
	logger.Log.Infof(utils.MMark(logCtx)+"HeygenTaskScheduler acquire redisLock.Lock, key: %s", redisLockKey)

	// 释放 redis 分布式锁
	defer func() {
		err = redisLock.Unlock(context.Background())
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+"HeygenTaskScheduler redisLock.Unlock error: %+v, key: %s", err, redisLockKey)
		} else {
			logger.Log.Infof(utils.MMark(logCtx)+"HeygenTaskScheduler redisLock.Unlock success key: %s", redisLockKey)
		}
	}()

	// 超过24小时未处理，直接失败
	outiTimeInHours := float64(conf.LocalConfig.HeygenSettings.HgTranslateOuttimeInHours)
	if time.Since(videoModel.CreatedAt).Hours() > outiTimeInHours {
		logger.Log.Errorf(utils.MMark(logCtx)+"HeygenTaskScheduler outtime 24 hours failed idx:%d,videoItem:%+v", idx, videoModel)
		handleFailedStatus(logCtx, userID, videoModel.OriginDuration, videoModel.VideoID, constant.VideoStatus_Failed, constant.TransStatus_VideoGenerateFailed, "outtime 24 hours failed", constant.FailedReaseon_Timeout24hours)
		return
	}

	logger.Log.Infof(utils.MMark(logCtx)+"HeygenTaskScheduler start handle videoItem  outiTimeInHours:%v,idx:%d,videoItem:%+v", outiTimeInHours, idx, videoModel)

	// 再次查询该任务的状态，以免被其他pod或者定时任务处理过,并以新查询到的该数据状态为准
	videoRecord, err := (&model.VideoTranslateEntity{}).QueryItemNotDeleteById(gomysql.DB, userID, videoID)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"HeygenTaskScheduler query record item error, ccountId:%s,videoID:%s,errorMsg:%+v", userID, videoID, err)
		return
	}
	logger.Log.Infof(utils.MMark(logCtx)+"HeygenTaskScheduler query record item ok, ccountId:%s,videoID:%s,originTranslateStatus:%s,newTranslateStatus:%s", userID, videoID, videoModel.TranslateStatus, videoRecord.TranslateStatus)
	translateStatus := videoRecord.TranslateStatus
	apiKeyRecord := videoRecord.ApiToken

	switch translateStatus {
	// 待风控
	case constant.TransStatus_WaitingRiskControl:
		logger.Log.Infof(utils.MMark(logCtx)+"HeygenTaskScheduler,invokeRiskControl,videoID:%s, TranslateStatus:%s,idx:%d,", videoID, constant.TransStatus_WaitingRiskControl, idx)
		// 调用风控
		err := invokeRiskControl(logCtx, videoID, "", videoModel.OriginVideoUrl, enums.MaterialVideo)
		if err != nil {
			// 风控审核失败，进入失败状态
			logger.Log.Warnf(utils.MMark(logCtx)+"HeygenTaskScheduler Failed to censor file,videoID:%s, err:%v", videoID, err)
			handleFailedStatus(logCtx, userID, videoModel.OriginDuration, videoID, constant.VideoStatus_Failed, constant.TransStatus_VideoGenerateFailed, err.Error(), constant.FailedReaseon_RiskControlCensorFailed)
			break
		} else {
			// // 风控审核成功，修改任务状态 status为 RUNNING   translate_status为 waiting_proofread
			// logger.Log.Infof(utils.MMark(logCtx)+"HeygenTaskScheduler end to censor file,videoID:%s, fileUrl: %s", videoID, videoModel.OriginVideoUrl)
			// err := (&model.VideoTranslateEntity{}).UpdateFieldsByVideoID(gomysql.DB, userID, videoID,
			// 	map[string]interface{}{
			// 		"status":           constant.VideoStatus_Running,
			// 		"translate_status": constant.TransStatus_WaitingProofread,
			// 	})
			// if err != nil {
			// 	logger.Log.Errorf(utils.MMark(logCtx)+"HeygenTaskScheduler UpdateFieldsByVideoID videoId:%s, errorMsg: %+v\n", videoID, err)
			// 	break
			// }
		}
		logger.Log.Infof(utils.MMark(logCtx)+"HeygenTaskScheduler end to censor file ok,videoID:%s, fileUrl: %s", videoID, videoModel.OriginVideoUrl)

		// 通过检测有几个发音人，来进行自研分流；
		// 生成单声道音频文件
		uuidStr := uuid.New().String()
		audioFilePath := fmt.Sprintf(VideoExtractAudioPathFmt, userID, uuidStr, videoID+".wav")
		defer os.Remove(audioFilePath) // 删除音频文件
		err = ffmpeg.ExtractAudio(logCtx, videoRecord.OriginVideoUrl, audioFilePath)
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+"HeygenTaskScheduler ExtractAudio error videoId:%s, errorMsg: %+v\n", videoID, err)
			handleFailedStatus(logCtx, userID, videoModel.OriginDuration, videoID, constant.VideoStatus_Failed, constant.TransStatus_VideoGenerateFailed, "extract audio error:"+err.Error(), constant.FailedReaseon_ServerInternalError)
			break
		}
		logger.Log.Infof(utils.MMark(logCtx)+"HeygenTaskScheduler ExtractAudio success videoId:%s,audioFilePath:%s", videoID, audioFilePath)

		// 判断是否含有有效音频
		haValidAudio, err := ffmpeg.HasValidAudio(logCtx, audioFilePath)
		if err != nil {
			// 判断时出错，不进行处理
			logger.Log.Errorf(utils.MMark(logCtx)+"HeygenTaskScheduler ffmpeg.HasValidAudio error, videoId:%s,errMsg:%V", videoID, err)
		} else {
			// 不含有有效音频，直接失败
			if !haValidAudio {
				logger.Log.Errorf(utils.MMark(logCtx)+"HeygenTaskScheduler this video not has valid audio,videoId:%s", videoID)
				handleFailedStatus(logCtx, userID, videoModel.OriginDuration, videoID, constant.VideoStatus_Failed, constant.TransStatus_VideoGenerateFailed, "this video not has valid audio", constant.FailedReaseon_ServerInternalError)
				break
			}
		}

		asrHost := conf.LocalConfig.ElevenLabsSettings.AsrHost
		asrApiKey := conf.LocalConfig.ElevenLabsSettings.ApiKey
		asrModelId := conf.LocalConfig.ElevenLabsSettings.AsrModelId
		asrReq := elevenlabs.AsrRequest{
			ModelID: asrModelId,
			File:    audioFilePath,
			Diarize: true,
		}

		asrResponse, err := elevenlabs.DoSpeechToText(logCtx, asrHost, asrApiKey, &asrReq)
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+"HeygenTaskScheduler elevenlabs asr error,videoId:%s,errMsg:%v", videoID, err)
			handleFailedStatus(logCtx, userID, videoModel.OriginDuration, videoID, constant.VideoStatus_Failed, constant.TransStatus_VideoGenerateFailed, "run asr error:"+err.Error(), constant.FailedReaseon_ServerInternalError)
			break
		}

		audioDuration := float64(videoRecord.OriginDuration) / 1000
		asrSpeakersArray := elevenlabs.SplitResponseBySpeakerAndPause(asrResponse, 1, audioDuration)

		detectSpeakerNum := len(asrSpeakersArray)
		logger.Log.Infof(utils.MMark(logCtx)+"HeygenTaskScheduler detect speaker ok,videoId:%s,detectSpeakerNum:%d", videoID, detectSpeakerNum)

		// 更新detect_speaker_num
		err = (&model.VideoTranslateEntity{}).UpdateFieldsByVideoID(gomysql.DB, videoModel.UserID, videoID,
			map[string]interface{}{
				"detect_speaker_num": detectSpeakerNum,
			})
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+"handleFailedStatus UpdateFields detect_speaker_num videoId:%s, errorMsg: %+v\n", videoID, err)
			return
		}

		if detectSpeakerNum == 0 {
			// 没检测到发音人，失败
			logger.Log.Errorf(utils.MMark(logCtx)+"HeygenTaskScheduler detect video no speaker videoID:%s", videoID)
			handleFailedStatus(logCtx, videoModel.UserID, videoModel.OriginDuration, videoID, constant.VideoStatus_Failed, constant.TransStatus_VideoGenerateFailed, "detect video no speaker", constant.FailedReaseon_ServerInternalError)
			break
		} else if detectSpeakerNum == 1 || videoRecord.SpeakerNum == 1 {

			// 只有时长大于3s的视频 才往自研分流
			if audioDuration > 3 && !strings.Contains(conf.LocalConfig.HeygenSettings.NotBypassToBaiduLanguages, videoRecord.TranslateLan) {
				if conf.LocalConfig.HeygenSettings.OpenTranslateByBaidu {
					// 查询分流到百度的数量
					countByBaidu, err := (&model.VideoTranslateEntity{}).GetCountOfTranslateByBaidu(gomysql.DB)
					if err != nil {
						logger.Log.Errorf(utils.MMark(logCtx)+"HeygenTaskScheduler GetCountOfTranslateByBaidy videoId:%s, errorMsg: %+v\n", videoID, err)
					}
					// 查询分流到heygen的数量
					countByHeygen, err := (&model.VideoTranslateEntity{}).GetCountOfTranslateByHeygen(gomysql.DB)
					if err != nil {
						logger.Log.Errorf(utils.MMark(logCtx)+"HeygenTaskScheduler GetCountOfTranslateByHeygen videoId:%s, errorMsg: %+v\n", videoID, err)
					}
					// 计算比例
					currentRatio := float32(0.0)
					if countByHeygen+countByBaidu != 0 {
						currentRatio = float32(countByBaidu) / float32(countByHeygen+countByBaidu)
					}

					setRatio := conf.LocalConfig.HeygenSettings.TranslateByBaidRatio
					logger.Log.Infof(utils.MMark(logCtx)+"HeygenTaskScheduler dispath to baidu countByBaidu:%+v,countByHeygen:%+v,currentRatio:%+v,setRatio:%+v videoId:%s", countByBaidu, countByHeygen, currentRatio, setRatio, videoID)
					if currentRatio <= setRatio {
						// 如果开启了分流至自研，且检测到有一个发音人，或者用户制定了1个发音人，分流到百度自研
						logger.Log.Infof(utils.MMark(logCtx)+"HeygenTaskScheduler detect one speaker dispath to baidu,videoId:%s", videoID)
						submitResult, err := submitVideoTranslateToBaidu(logCtx, videoRecord)
						if err != nil {
							// 提交到百度自研失败
							logger.Log.Errorf(utils.MMark(logCtx)+"HeygenTaskScheduler submit to baidu error, videoID:%s,errMsg:%v", videoID, err)
							handleFailedStatus(logCtx, videoModel.UserID, videoModel.OriginDuration, videoID, constant.VideoStatus_Failed, constant.TransStatus_VideoGenerateFailed, "submit to baidu error:"+err.Error(), constant.FailedReaseon_ServerInternalError)
							break
						}
						err = (&model.VideoTranslateEntity{}).UpdateFieldsByVideoID(gomysql.DB, userID, videoID,
							map[string]interface{}{
								"status":             constant.VideoStatus_Running,
								"translate_status":   constant.TransStatus_VideoProduceByBaidu,
								"video_translate_id": submitResult.TaskIds[0],
								"translate_type":     1,
							})
						if err != nil {
							logger.Log.Errorf(utils.MMark(logCtx)+"HeygenTaskScheduler UpdateFieldsByVideoID videoId:%s, errorMsg: %+v\n", videoID, err)
							break
						}
						logger.Log.Infof(utils.MMark(logCtx)+"HeygenTaskScheduler detect one speaker dispath to baidu ok,videoId:%s,submitResult: %+v", videoID, submitResult)
						break
					}

				}
			}
		}

		// 有多个发音人，或者没有开启分流至自研，提交heygen处理
		// 风控审核成功，修改任务状态 status为 RUNNING   translate_status为 waiting_proofread
		logger.Log.Infof(utils.MMark(logCtx)+"HeygenTaskScheduler detect more speaker dispath to heygen,videoID:%s", videoID)
		err = (&model.VideoTranslateEntity{}).UpdateFieldsByVideoID(gomysql.DB, userID, videoID,
			map[string]interface{}{
				"status":           constant.VideoStatus_Running,
				"translate_status": constant.TransStatus_WaitingGenerateByHeygen,
			})
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+"HeygenTaskScheduler UpdateFieldsByVideoID videoId:%s, errorMsg: %+v\n", videoID, err)
			break
		}

	// 调用自研的查询接口，查询视频生成的状态
	case constant.TransStatus_VideoProduceByBaidu:
		queryResult, err := queryVideoTranslateFromBaidu(logCtx, videoRecord)

		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+"HeygenTaskScheduler query video translate from baidu error, videoID:%s,errMsg:%v", videoID, err)
			handleFailedStatus(logCtx, videoModel.UserID, videoModel.OriginDuration, videoID, constant.VideoStatus_Failed, constant.TransStatus_VideoGenerateFailed, "query video translate from baidu error:"+err.Error(), constant.FailedReaseon_ServerInternalError)
			break
		}
		logger.Log.Infof(utils.MMark(logCtx)+"HeygenTaskScheduler query video translate from baidu ok, videoID:%s,queryResult:%+v", videoID, queryResult)

		// 翻译失败
		if queryResult.Status == constant.VideoStatus_Failed {
			// 没检测到发音人，失败
			logger.Log.Errorf(utils.MMark(logCtx)+"HeygenTaskScheduler query video translate from baidu response failed, videoID:%s,failedMsg:%s", videoID, queryResult.Message)
			handleFailedStatus(logCtx, videoModel.UserID, videoModel.OriginDuration, videoID, constant.VideoStatus_Failed, constant.TransStatus_VideoGenerateFailed, "baidu response failed:"+queryResult.Message, constant.FailedReaseon_ServerInternalError)
			break
		} else if queryResult.Status == constant.VideoStatus_Success {
			logger.Log.Infof(utils.MMark(logCtx)+"videoID:%s, accountId:%s,video translate from baidu success", videoID, userID)
			// 更新成功数据至数据库
			err = (&model.VideoTranslateEntity{}).UpdateFieldsByVideoID(gomysql.DB, userID, videoID,
				map[string]interface{}{
					"thumbnail":           queryResult.Thumbnail,
					"video_url":           queryResult.VideoUrl,
					"translate_video_url": queryResult.FigureVideoUrl,
					"caption_url":         queryResult.CaptionUrl,
					"duration":            queryResult.Duration * 1000,
					"width":               queryResult.ResolutionWidth,
					"height":              queryResult.ResolutionHeight,
				})
			if err != nil {
				logger.Log.Errorf(utils.MMark(logCtx)+"UpdateFieldsByVideoID videoId:%s, errorMsg: %+v\n", videoID, err)
				break
			}

			// 翻译成功，扣除视频权益,videoId作为 assetId
			dhClient := billing.NewBillingClient()
			originDuration := int(math.Ceil(float64(videoRecord.OriginDuration) / 1000))
			err = dhClient.Confirm(userID, originDuration, videoID)
			if err != nil {
				// 账户权益扣除失败，
				logger.Log.Errorf(utils.MMark(logCtx)+"videoID:%s, accountId:%s,video translate from baidu quota conform failed,error:%v", videoID, userID, err)
				handleFailedStatus(logCtx, userID, videoModel.OriginDuration, videoID, constant.VideoStatus_Failed, constant.TransStatus_VideoGenerateFailed, "quota conform failed,"+err.Error(), constant.FailedReaseon_QuotaConformFailed)
				break
			} else {
				// 账户权益扣除成功
				logger.Log.Infof(utils.MMark(logCtx)+"videoID:%s, accountId:%s,originDuration:%d,video translate from baidu quota confirm ok", videoID, userID, originDuration)
				// 更新成功状态至数据库
				err = (&model.VideoTranslateEntity{}).UpdateFieldsByVideoID(gomysql.DB, userID, videoID,
					map[string]interface{}{
						"status":           constant.VideoStatus_Success,
						"translate_status": constant.TransStatus_VideoGenerateSuccess,
					})
				if err != nil {
					logger.Log.Errorf(utils.MMark(logCtx)+"UpdateFieldsByVideoID videoId:%s, errorMsg: %+v\n", videoID, err)
					break
				}
			}
		}
		// 待生成视频，直接调用视频生成接口，不需要proofread
	case constant.TransStatus_WaitingGenerateByHeygen:
		apiKey := conf.LocalConfig.HeygenSettings.ApiKey
		if conf.LocalConfig.HeygenSettings.UseDatabaseToken {
			apiKey, err = (&model.VideoTranslateApiToken{}).GetPublishTokenAndUpdateAmoutOfMonth(gomysql.DB)
			if err != nil {
				logger.Log.Errorf(utils.MMark(logCtx)+"HeygenTaskScheduler GetPublishTokenByAsc error videoID:%s, errorMsg: %+v\n", videoID, err)
				handleFailedStatus(logCtx, videoModel.UserID, videoModel.OriginDuration, videoID, constant.VideoStatus_Failed, constant.TransStatus_VideoGenerateFailed, "get heygen token from database error:"+err.Error(), constant.FailedReaseon_ServerInternalError)
				break
			}
		}
		logger.Log.Infof(utils.MMark(logCtx)+"HeygenTaskScheduler invokeTranslateVideo  videoID:%s, apiKey: %s", videoID, apiKey)
		err := (&model.VideoTranslateEntity{}).UpdateFieldsByVideoID(gomysql.DB, userID, videoID, map[string]interface{}{
			"api_token": apiKey,
		})
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+"HeygenTaskScheduler invokeTranslateVideo save api_token error,videoID:%s, err:%v", videoID, err)
			handleFailedStatus(logCtx, videoModel.UserID, videoModel.OriginDuration, videoID, constant.VideoStatus_Failed, constant.TransStatus_VideoGenerateFailed, "save api_token error"+err.Error(), constant.FailedReaseon_ServerInternalError)
			break
		}

		logger.Log.Infof(utils.MMark(logCtx)+"HeygenTaskScheduler,videoID:%s,invokeTranslateVideo, TranslateStatus:%s,idx:%d,", videoID, constant.TransStatus_WaitingProofread, idx)
		generateVideoId, err := invokeTranslateVideo(logCtx, apiKey, videoModel)
		if err != nil {
			// 调用直接视频翻译接口失败
			logger.Log.Errorf(utils.MMark(logCtx)+"HeygenTaskScheduler invokeTranslateVideo,videoID:%s, err:%v", videoID, err)
			handleFailedStatus(logCtx, videoModel.UserID, videoModel.OriginDuration, videoID, constant.VideoStatus_Failed, constant.TransStatus_VideoGenerateFailed, err.Error(), constant.FailedReaseon_ServerInternalError)
			break
		} else {
			// 调用直接视频翻译接口成功，修改任务状态 translate_status为 video_generating
			logger.Log.Infof(utils.MMark(logCtx)+"HeygenTaskScheduler invokeTranslateVideo end,videoID:%s, generateVideoId: %s", videoID, generateVideoId)
			err := (&model.VideoTranslateEntity{}).UpdateFieldsByVideoID(gomysql.DB, userID, videoID,
				map[string]interface{}{
					"video_translate_id": generateVideoId,
					"translate_status":   constant.TransStatus_VideoGenerating,
				})
			if err != nil {
				logger.Log.Errorf(utils.MMark(logCtx)+"HeygenTaskScheduler invokeTranslateVideo videoId:%s, errorMsg: %+v\n", videoID, err)
				break
			}
		}
	// 待校对状态，调用校对接口
	case constant.TransStatus_WaitingProofread:
		apiKey := conf.LocalConfig.HeygenSettings.ApiKey
		if conf.LocalConfig.HeygenSettings.UseDatabaseToken {
			apiKey, err = (&model.VideoTranslateApiToken{}).GetPublishTokenAndUpdateAmoutOfMonth(gomysql.DB)
			if err != nil {
				logger.Log.Errorf(utils.MMark(logCtx)+"HeygenTaskScheduler GetPublishTokenByAsc error videoID:%s, errorMsg: %+v\n", videoID, err)
				handleFailedStatus(logCtx, videoModel.UserID, videoModel.OriginDuration, videoID, constant.VideoStatus_Failed, constant.TransStatus_VideoGenerateFailed, "get heygen token from database error:"+err.Error(), constant.FailedReaseon_ServerInternalError)
				break
			}
		}
		logger.Log.Infof(utils.MMark(logCtx)+"HeygenTaskScheduler invokeGenerateProofread  videoID:%s, apiKey: %s", videoID, apiKey)
		err := (&model.VideoTranslateEntity{}).UpdateFieldsByVideoID(gomysql.DB, userID, videoID, map[string]interface{}{
			"api_token": apiKey,
		})
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+"HeygenTaskScheduler invokeGenerateProofread save api_token error,videoID:%s, err:%v", videoID, err)
			handleFailedStatus(logCtx, videoModel.UserID, videoModel.OriginDuration, videoID, constant.VideoStatus_Failed, constant.TransStatus_VideoGenerateFailed, "save api_token error"+err.Error(), constant.FailedReaseon_ServerInternalError)
			break
		}

		logger.Log.Infof(utils.MMark(logCtx)+"HeygenTaskScheduler,videoID:%s,invokeGenerateProofread, TranslateStatus:%s,idx:%d,", videoID, constant.TransStatus_WaitingProofread, idx)
		proofreadID, err := invokeGenerateProofread(logCtx, apiKey, videoModel)
		if err != nil {
			// 调用校对接口失败
			logger.Log.Errorf(utils.MMark(logCtx)+"HeygenTaskScheduler invokeGenerateProofread,videoID:%s, err:%v", videoID, err)
			handleFailedStatus(logCtx, videoModel.UserID, videoModel.OriginDuration, videoID, constant.VideoStatus_Failed, constant.TransStatus_VideoGenerateFailed, err.Error(), constant.FailedReaseon_ServerInternalError)
			break
		} else {
			// 调用校对接口成功，修改任务状态 translate_status为 proofreading
			logger.Log.Infof(utils.MMark(logCtx)+"HeygenTaskScheduler invokeGenerateProofread end,videoID:%s, proofreadID: %s", videoID, proofreadID)
			err := (&model.VideoTranslateEntity{}).UpdateFieldsByVideoID(gomysql.DB, userID, videoID,
				map[string]interface{}{
					"proofread_id":     proofreadID,
					"translate_status": constant.TransStatus_Proofreading,
				})
			if err != nil {
				logger.Log.Errorf(utils.MMark(logCtx)+"HeygenTaskScheduler UpdateFieldsByVideoID videoId:%s, errorMsg: %+v\n", videoID, err)
				break
			}
		}
	// 校对中，调用校对查询接口
	case constant.TransStatus_Proofreading:
		logger.Log.Infof(utils.MMark(logCtx)+"HeygenTaskScheduler,videoID:%s,invokeCheckProofreadStatus, TranslateStatus:%s,idx:%d,", videoID, constant.TransStatus_Proofreading, idx)
		proofreadStatus, err := invokeCheckProofreadStatus(logCtx, apiKeyRecord, videoModel)

		// 处理 校对失败、校对中、校对成功的情况，其他情况等待下查询
		if err != nil {
			// 校对失败--流程结束
			logger.Log.Errorf(utils.MMark(logCtx)+"HeygenTaskScheduler invokeCheckProofreadStatus,videoID:%s, err:%v", videoID, err)
			handleFailedStatus(logCtx, videoModel.UserID, videoModel.OriginDuration, videoID, constant.VideoStatus_Failed, constant.TransStatus_ProofreadFailed, err.Error(), constant.FailedReaseon_ServerInternalError)
			break
		}
		if proofreadStatus == constant.HeygenStatus_Processing {
			// 校对中--不处理等待下次查询
			logger.Log.Infof(utils.MMark(logCtx)+"HeygenTaskScheduler invokeGenerateVideoFromProofread processing,videoID:%s", videoID)
		} else if proofreadStatus == constant.HeygenStatus_Completed {
			// 校对成功--调用视频生成
			logger.Log.Infof(utils.MMark(logCtx)+"HeygenTaskScheduler invokeCheckProofreadStatus end,videoID:%s, proofreadStatus: %s", videoID, proofreadStatus)
			translateVideoID, err := invokeGenerateVideoFromProofread(logCtx, apiKeyRecord, videoModel)
			if err != nil {
				// 调用视频生成 error
				logger.Log.Errorf(utils.MMark(logCtx)+"HeygenTaskScheduler invokeGenerateVideoFromProofread,videoID:%s, err:%v", videoID, err)
				handleFailedStatus(logCtx, videoModel.UserID, videoModel.OriginDuration, videoID, constant.VideoStatus_Failed, constant.TransStatus_VideoGenerateFailed, err.Error(), constant.FailedReaseon_ServerInternalError)
				break
			} else {
				// 调用视频生成 ok,修改任务状态 translate_status为 video_generating ，且video_translate_id更新至数据库
				logger.Log.Infof(utils.MMark(logCtx)+"HeygenTaskScheduler invokeGenerateVideoFromProofread end,videoID:%s, translateVideoID: %s", videoID, translateVideoID)
				err := (&model.VideoTranslateEntity{}).UpdateFieldsByVideoID(gomysql.DB, userID, videoID,
					map[string]interface{}{
						"video_translate_id": translateVideoID,
						"translate_status":   constant.TransStatus_VideoGenerating,
					})
				if err != nil {
					logger.Log.Errorf(utils.MMark(logCtx)+"HeygenTaskScheduler UpdateFieldsByVideoID videoId:%s, errorMsg: %+v\n", videoID, err)
					break
				}
			}
		}
	// 视频生成中,调用视频查询接口
	case constant.TransStatus_VideoGenerating:
		logger.Log.Infof(utils.MMark(logCtx)+"HeygenTaskScheduler,videoID:%s,invokeCheckTranslateStatus, TranslateStatus:%s,idx:%d,", videoID, constant.TransStatus_VideoGenerating, idx)
		heygenResData, err := invokeCheckTranslateStatus(logCtx, apiKeyRecord, videoModel)
		// 只处理失败和成功的状态，其他情况等待下次查询
		if err != nil {
			// 调用视频查询接口 error
			logger.Log.Errorf(utils.MMark(logCtx)+"HeygenTaskScheduler invokeCheckTranslateStatus error,videoID:%s, err:%v", videoID, err)
			handleFailedStatus(logCtx, videoModel.UserID, videoModel.OriginDuration, videoID, constant.VideoStatus_Failed, constant.TransStatus_VideoGenerateFailed, err.Error(), constant.FailedReaseon_ServerInternalError)
			break
		} else {
			// 调用视频查询接口 ok
			logger.Log.Infof(utils.MMark(logCtx)+"HeygenTaskScheduler invokeCheckTranslateStatus end,videoID:%s, resData: %+v", videoID, heygenResData)
			if heygenResData.Status == constant.HeygenStatus_Failed {
				// 以下这些失败信息不做失败处理，等待下次查询
				msgArray := strings.Split(conf.LocalConfig.HeygenSettings.ExcludeFailedMessage, ",")
				for _, msgItem := range msgArray {
					if heygenResData.Message == msgItem {
						logger.Log.Infof(utils.MMark(logCtx)+"HeygenTaskScheduler invokeCheckTranslateStatus heygenResData.Message dont handle,videoID:%s,responseMsg:%s", videoID, msgItem)
						break
					}
				}

				// 视频生成失败
				logger.Log.Infof(utils.MMark(logCtx)+"HeygenTaskScheduler invokeCheckTranslateStatus generate video failed,videoID:%s", videoID)
				handleFailedStatus(logCtx, userID, videoModel.OriginDuration, videoID, constant.VideoStatus_Failed, constant.TransStatus_VideoGenerateFailed, heygenResData.Message, constant.FailedReaseon_ServerInternalError)
				break
			} else if heygenResData.Status == constant.HeygenStatus_Success {
				// 视频生成成功
				if translateSuccessDownLoadCurrentNum > conf.LocalConfig.HeygenSettings.HgDownLoadSyncNumPerPod {
					logger.Log.Infof(utils.MMark(logCtx)+"HeygenTaskScheduler handle TransStatus_VideoGenerating currentNum:%d > setNum:%d,break,videoID:%s", translateSuccessDownLoadCurrentNum, conf.LocalConfig.HeygenSettings.HgDownLoadSyncNumPerPod, videoID)
					break
				}

				translateSuccessDownLoadCurrentNum++
				defer func() {
					translateSuccessDownLoadCurrentNum--
				}()
				logger.Log.Infof(utils.MMark(logCtx)+"HeygenTaskScheduler invokeCheckTranslateStatus generate video currentNum:%d,,videoID:%s", translateSuccessDownLoadCurrentNum, videoID)
				captionUrl := "" // 字幕文件的url
				transVideoUrl := heygenResData.Url
				uuidStr := uuid.New().String()
				videoFilePath := fmt.Sprintf(DownloadPathFmt, userID, uuidStr, videoID+".mp4")
				defer os.Remove(videoFilePath) // 删除视频文件
				// 下载视频文件
				err := downloadFile(logCtx, transVideoUrl, videoFilePath)
				if err != nil {
					// 下载视频失败
					logger.Log.Errorf(utils.MMark(logCtx)+"HeygenTaskScheduler download tranalate video failed,videoID:%s, err:%v", videoID, err)
					handleFailedStatus(logCtx, userID, videoModel.OriginDuration, videoID, constant.VideoStatus_Failed, constant.TransStatus_VideoGenerateFailed, "download tranalate video failed,"+err.Error(), constant.FailedReaseon_VideoDownLoadError)
					break
				}
				logger.Log.Infof(utils.MMark(logCtx)+"HeygenTaskScheduler download tranalate video end,videoID:%s, videoFilePath:%v,transVideoUrl:%s,", videoID, videoFilePath, transVideoUrl)
				// 更新 translate_video_url 至数据库
				err = (&model.VideoTranslateEntity{}).UpdateFieldsByVideoID(gomysql.DB, userID, videoID,
					map[string]interface{}{
						"translate_video_url": transVideoUrl,
					})
				if err != nil {
					logger.Log.Errorf(utils.MMark(logCtx)+"Update transVideoUrl failed, videoId:%s, errorMsg: %+v\n", videoID, err)
				}

				// 需要字幕，
				if videoModel.EnableCaption == 1 {
					// 1、获取字幕url
					captionUrl, err = invokeTranslateCaption(logCtx, apiKeyRecord, videoModel)
					if err != nil {
						// 获取字幕文件 error
						logger.Log.Errorf(utils.MMark(logCtx)+"HeygenTaskScheduler invokeTranslateCaption,videoID:%s, err:%v", videoID, err)
						handleFailedStatus(logCtx, userID, videoModel.OriginDuration, videoID, constant.VideoStatus_Failed, constant.TransStatus_VideoGenerateFailed, "invokeTranslateCaption error,"+err.Error(), constant.FailedReaseon_ServerInternalError)
						break
					} else {
						logger.Log.Infof(utils.MMark(logCtx)+"HeygenTaskScheduler get captionurl ok,videoID:%s,captionUrl:%s,", videoID, captionUrl)
						// 更新 caption_url 至数据库
						err = (&model.VideoTranslateEntity{}).UpdateFieldsByVideoID(gomysql.DB, userID, videoID,
							map[string]interface{}{
								"caption_url": captionUrl,
							})
						if err != nil {
							logger.Log.Errorf(utils.MMark(logCtx)+"Update captionUrl failed, videoId:%s, errorMsg: %+v\n", videoID, err)
						}

						// 获取字幕文件 ok
						captionFilePath := fmt.Sprintf(DownloadPathFmt, userID, uuidStr, videoID+".vtt")
						defer os.Remove(captionFilePath) // 删除字幕文件
						// 2、下载字幕文件
						err := downloadFile(logCtx, captionUrl, captionFilePath)
						if err != nil {
							// 下载字幕文件失败
							logger.Log.Errorf(utils.MMark(logCtx)+"HeygenTaskScheduler download tranalate caption failed,videoID:%s, err:%v", videoID, err)
							handleFailedStatus(logCtx, userID, videoModel.OriginDuration, videoID, constant.VideoStatus_Failed, constant.TransStatus_VideoGenerateFailed, "download tranalate caption failed,"+err.Error(), constant.FailedReaseon_ServerInternalError)
							break
						}
						logger.Log.Infof(utils.MMark(logCtx)+"HeygenTaskScheduler download tranalate caption end,videoID:%s, captionFilePath:%v", videoID, captionFilePath)

						// 3、合并视频和字幕文件
						mergerVideoPath := fmt.Sprintf(DownloadPathFmt, userID, uuidStr, videoID+"_merger.mp4")
						defer os.Remove(mergerVideoPath) // 删除合并后的视频文件

						for i := 0; i < 3; i++ {
							err = mergerVideoAndCaption(logCtx, videoModel.TranslateLan, videoID, videoFilePath, captionFilePath, mergerVideoPath)
							if err != nil {
								logger.Log.Errorf(utils.MMark(logCtx)+"HeygenTaskScheduler mergerVideoAndCaption retry count:%d,videoID:%s, err:%v", i, videoID, err)
								continue
							}
							break
						}

						if err != nil {
							// 合并视频和字幕文件失败
							logger.Log.Errorf(utils.MMark(logCtx)+"HeygenTaskScheduler mergerVideoAndCaption final failed,videoID:%s, err:%v", videoID, err)
							handleFailedStatus(logCtx, userID, videoModel.OriginDuration, videoID, constant.VideoStatus_Failed, constant.TransStatus_VideoGenerateFailed, "mergerVideoAndCaption failed,"+err.Error(), constant.FailedReaseon_ServerInternalError)
							break
						}
						logger.Log.Infof(utils.MMark(logCtx)+"download tranalate caption ok,videoID:%s, captionFilePath:%v", videoID, captionFilePath)
						videoFilePath = mergerVideoPath
					}
				}
				// 生成缩略图
				thumbImagePath := fmt.Sprintf(DownloadPathFmt, userID, uuidStr, videoID+"_th.jpg")
				defer os.Remove(thumbImagePath) // 删除缩略图
				err = generateThumbImage(videoFilePath, thumbImagePath)
				if err != nil {
					// 生成缩略图失败
					logger.Log.Errorf(utils.MMark(logCtx)+"generateThumbImage failed,videoID:%s,thumbImagePath:%s, err:%v", videoID, thumbImagePath, err)
				}
				// 上传缩略图
				thumbImageBosKey := fmt.Sprintf(VideoToBosFmt, videoModel.UserID, uuidStr, videoID+"_th.jpg")
				thumbImageUrl, err := uploadBosServiceFromFile(logCtx, thumbImageBosKey, thumbImagePath)
				if err != nil {
					// 上传缩略图失败
					logger.Log.Errorf(utils.MMark(logCtx)+"upload thumbImage failed,videoID:%s,thumbImagePath:%s, err:%v", videoID, thumbImagePath, err)
				}
				logger.Log.Infof(utils.MMark(logCtx)+"upload thumbImage ok,videoID:%s,thumbImageUrl:%s", videoID, thumbImageUrl)
				// 更新 thumbnail 至数据库
				err = (&model.VideoTranslateEntity{}).UpdateFieldsByVideoID(gomysql.DB, userID, videoID,
					map[string]interface{}{
						"thumbnail": thumbImageUrl,
					})
				if err != nil {
					logger.Log.Errorf(utils.MMark(logCtx)+"Update thumbImage failed, videoId:%s, errorMsg: %+v\n", videoID, err)
				}

				// 转存视频至我们的存储空间
				videoBosKey := fmt.Sprintf(VideoToBosFmt, videoModel.UserID, uuidStr, videoModel.Name+".mp4")
				finalVideoUrl, err := uploadBosServiceFromFile(logCtx, videoBosKey, videoFilePath)
				if err != nil {
					// 转存视频失败
					logger.Log.Errorf(utils.MMark(logCtx)+"upload video failed,videoID:%s,transVideoUrl:%s, err:%v", videoID, transVideoUrl, err)
					break
				}
				// 转存视频成功
				logger.Log.Infof(utils.MMark(logCtx)+"upload upload video ok,videoID:%s,transVideoUrl:%s", videoID, transVideoUrl)
				// 更新 video_url 至数据库
				err = (&model.VideoTranslateEntity{}).UpdateFieldsByVideoID(gomysql.DB, userID, videoID,
					map[string]interface{}{
						"video_url": finalVideoUrl,
					})
				if err != nil {
					logger.Log.Errorf(utils.MMark(logCtx)+"Update video_url failed, videoId:%s, errorMsg: %+v\n", videoID, err)
				}

				videoInfo, err := getVideoInfo(videoID, videoFilePath)
				if err != nil {
					logger.Log.Errorf(utils.MMark(logCtx)+"getVideoInfo failed,videoID:%s, err:%v", videoID, err)
				}

				// 获取制作后的视频时长,单位毫秒
				duration, err := ffmpeg.GetDurationFromURL(videoFilePath)
				if err != nil {
					// 获取视频时长失败
					logger.Log.Errorf(utils.MMark(logCtx)+"get video duration failed, videoID:%s,error: %v", videoID, err)
					return
				}

				width := videoInfo.Streams[0].Width
				height := videoInfo.Streams[0].Height
				logger.Log.Infof(utils.MMark(logCtx)+"get video info ok,videoID:%s,width:%d,height:%d,duration:%d", videoID, width, height, duration)

				// 扣除视频权益,videoId作为 assetId
				dhClient := billing.NewBillingClient()
				originDuration := int(math.Ceil(float64(videoModel.OriginDuration) / 1000))
				err = dhClient.Confirm(userID, originDuration, videoID)
				if err != nil {
					// 账户权益扣除失败，
					logger.Log.Errorf(utils.MMark(logCtx)+"videoID:%s, accountId:%s,quota conform failed,error:%v", videoID, userID, err)
					handleFailedStatus(logCtx, userID, videoModel.OriginDuration, videoID, constant.VideoStatus_Failed, constant.TransStatus_VideoGenerateFailed, "quota conform failed,"+err.Error(), constant.FailedReaseon_QuotaConformFailed)
					break
				} else {
					// 账户权益扣除成功
					logger.Log.Infof(utils.MMark(logCtx)+"videoID:%s, accountId:%s,originDuration:%d,quota confirm ok", videoID, userID, originDuration)
					// 更新成功数据至数据库
					err = (&model.VideoTranslateEntity{}).UpdateFieldsByVideoID(gomysql.DB, userID, videoID,
						map[string]interface{}{
							"status":              constant.VideoStatus_Success,
							"translate_status":    constant.TransStatus_VideoGenerateSuccess,
							"thumbnail":           thumbImageUrl,
							"video_url":           finalVideoUrl,
							"translate_video_url": transVideoUrl,
							"caption_url":         captionUrl,
							"duration":            duration,
							"width":               width,
							"height":              height,
						})
					if err != nil {
						logger.Log.Errorf(utils.MMark(logCtx)+"UpdateFieldsByVideoID videoId:%s, errorMsg: %+v\n", videoID, err)
						break
					}
				}
			} else {
				logger.Log.Infof(utils.MMark(logCtx)+"HeygenTaskScheduler invokeCheckTranslateStatus video is  generating ,videoID:%s", videoID)
			}
		}

	}
}

// 更新失败状态，并解冻权益
func handleFailedStatus(logCtx context.Context, accountID string, duration int64, videoID string, videoStatus string, transStatus string, failedReason string, reasonTouser string) {
	// 更新失败状态
	err := (&model.VideoTranslateEntity{}).UpdateFieldsByVideoID(gomysql.DB, accountID, videoID,
		map[string]interface{}{
			"status":           videoStatus,
			"translate_status": transStatus,
			"failed_reason":    failedReason,
			"reason_to_user":   reasonTouser,
		})
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"handleFailedStatus UpdateFieldsByVideoID videoId:%s, errorMsg: %+v\n", videoID, err)
		return
	}

	// 解冻权益,videoId作为 assetId
	dhClient := billing.NewBillingClient()
	durationSecond := int(math.Ceil(float64(duration) / 1000))
	err = dhClient.Unfreeze(accountID, durationSecond, videoID)
	if err != nil {
		// 账户权益冻结失败，
		logger.Log.Errorf(utils.MMark(logCtx)+"videoID:%s,handleFailedStatus quota unfreeze failed, accountId:%s,error:%v", videoID, accountID, err)
		return
	} else {
		// 账户权益冻结成功
		logger.Log.Infof(utils.MMark(logCtx)+"videoID:%s,handleFailedStatus quota unfreeze ok accountId:%s", videoID, accountID)
	}
}

// 调用风控接口
func invokeRiskControl(logCtx context.Context, videoID, cookie, url string, fileType enums.MaterialType) error {
	httpclient := httputil.NewRetryHTTPClient(60*time.Minute, 3)
	if fileType != enums.MaterialVideo {
		logger.Log.Errorf(utils.MMark(logCtx)+"videoID:%s,censor type error", videoID)
		return errors.New("不支持的文件类型")
	}
	beginTime := time.Now()

	var info = &proto.RiskControlShortVideoSubmitRequest{
		VideoURL: url,
	}
	// 序列化请求数据
	jsonData, err := json.Marshal(info)
	requstUrl := "http://dhlive-external-api:8080/api/digitalhuman/external/riskcontrol/v1/figure/video"
	if len(cookie) == 0 {
		requstUrl = "http://dhlive-external-api:8080/api/digitalhuman/external/riskcontrol/v1/internal/figure/video"
	}

	if err != nil {
		logger.Log.Errorf("videoID%s,censor marshal error: %v", videoID, err)
		return errors.New("序列化数据失败")
	}

	targetLanguage := fmt.Sprintf("%s", logCtx.Value("Language"))
	logger.Log.Infof(utils.MMark(logCtx)+"videoID%s,censor language: %s", videoID, targetLanguage)

	headers := make(map[string]string)
	headers["Content-Type"] = "application/json"
	headers["Cookie"] = cookie
	headers["Language"] = targetLanguage

	resp, err := httpclient.DoRequest(logCtx, "POST", requstUrl, headers, bytes.NewBuffer(jsonData))

	costTime := time.Since(beginTime)

	if err != nil {
		logger.Log.Errorf("videoID%s,censor http request error: %v", videoID, err)
		return errors.New("risk control censor requet failed")
	}
	logger.Log.Infof(utils.MMark(logCtx)+"videoID%s,risk control censor response: %s,costTime:%v", videoID, string(resp), costTime)

	errmsg := ""

	switch fileType {

	case enums.MaterialVideo:
		res := &proto.RiskControlShortVideoResponse{}
		err = json.Unmarshal(resp, res)
		if err != nil {
			logger.Log.Errorf("videoID%s,censor unmarshal error: %v", videoID, err)
			return errors.New("审核结果解析失败")
		}
		// 先判断返回结果是否有报错
		if res.Code != 0 {
			errmsg = res.Message.Global
			break
		}

		if res.Result.Data.ErrorCode != 0 {
			errmsg = res.Result.Data.ErrorMsg
		} else if res.Result.Data.ConclusionType == 2 {
			for _, v := range res.Result.Data.ConclusionTypeGroupInfos {
				errmsg += v.Msg
				errmsg += ";"
			}
		}
	}

	if len(errmsg) <= 0 {
		return nil
	}
	return errors.New(errmsg)
}

// 调用hg的生成校对的接口
func invokeGenerateProofread(logCtx context.Context, apiKey string, videoModel model.VideoTranslateEntity) (string, error) {
	url := conf.LocalConfig.HeygenSettings.GenerateProofreadUrl
	beginTime := time.Now()
	reqModel := heygen.GenerateProofreadRequest{
		Title:                 videoModel.Name,
		VideoURL:              videoModel.OriginVideoUrl,
		OutputLanguage:        videoModel.TranslateLan,
		SpeakerNum:            int(videoModel.SpeakerNum),
		EnableDynamicDuration: videoModel.EnableDynamicDuration == 1,
	}

	logger.Log.Infof(utils.MMark(logCtx)+"videoID:%s,invokeGenerateProofread url: %s,apiKey:%s, request: %+v\n", videoModel.VideoID, url, apiKey, reqModel)
	// 序列化请求数据
	jsonData, err := json.Marshal(reqModel)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"videoID:%s,invokeGenerateProofread Marshal request failed: %+v\n", videoModel.VideoID, err)
		return "", err
	}
	// 创建请求对象
	httpclient := httputil.NewRetryHTTPClient(120*time.Second, 3)
	header := make(map[string]string)
	header["accept"] = "application/json"
	header["content-type"] = "application/json"
	header["x-api-key"] = apiKey

	resp, err := httpclient.DoRequest(logCtx, "POST", url, header, bytes.NewBuffer(jsonData))
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"videoID:%s,invokeGenerateProofread request failed: %+v\n", videoModel.VideoID, err)
		return "", err
	}
	responseModel := &heygen.TranslateVideoResponse{}
	err = json.Unmarshal(resp, responseModel)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"videoID:%s,invokeGenerateProofread resp json.Unmarshal error: %v,responseModel:%+v", videoModel.VideoID, err, responseModel)
		return "", err
	}
	if responseModel.Error != nil || responseModel.Data == nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"videoID:%s,invokeGenerateProofread  heygen resp error, responseModel:%+v", videoModel.VideoID, responseModel)
		return "", errors.New(responseModel.Error.Code + "," + responseModel.Error.Message)
	}

	if responseModel.Data == nil || responseModel.Data.ProofreadId == "" {
		logger.Log.Errorf(utils.MMark(logCtx)+"videoID:%s,invokeGenerateProofread heygen response data error, responseModel:%+v", videoModel.VideoID, responseModel)
		return "", errors.New("heygen response data error")
	}

	costTime := time.Since(beginTime)
	logger.Log.Infof(utils.MMark(logCtx)+"videoID:%s,invokeGenerateProofread end costIime: %+v, responseModel: %s\n", videoModel.VideoID, costTime, resp)
	return responseModel.Data.ProofreadId, nil
}

// 调用heygen的校对状态查询
func invokeCheckProofreadStatus(logCtx context.Context, apiKey string, videoModel model.VideoTranslateEntity) (string, error) {
	videoID := videoModel.VideoID
	url := conf.LocalConfig.HeygenSettings.CheckProofreadUrl + videoModel.ProofreadID
	beginTime := time.Now()
	// 创建请求对象
	httpclient := httputil.NewRetryHTTPClient(120*time.Second, 3)
	header := make(map[string]string)
	header["accept"] = "application/json"
	header["content-type"] = "application/json"
	header["x-api-key"] = apiKey
	logger.Log.Infof(utils.MMark(logCtx)+"videoID:%s,invokeCheckProofreadStatus apiKey: %s,url: %s", videoID, apiKey, url)

	resp, err := httpclient.DoRequest(logCtx, "GET", url, header, nil)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"videoID:%s,invokeCheckProofreadStatus request failed: %+v\n", videoID, err)
		return "", nil
	}
	responseModel := &heygen.TranslateVideoResponse{}
	err = json.Unmarshal(resp, responseModel)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"videoID:%s,invokeCheckProofreadStatus resp json.Unmarshal error: %v,responseModel:%+v", videoID, err, responseModel)
		return "", nil
	}
	if responseModel.Error != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"videoID:%s,invokeCheckProofreadStatus  heygen resp error, responseModel:%+v", videoID, responseModel)
		return "", errors.New(responseModel.Error.Code + "," + responseModel.Error.Message)
	}

	if responseModel.Data == nil || responseModel.Data.Status == "" {
		logger.Log.Errorf(utils.MMark(logCtx)+"videoID:%s,invokeCheckProofreadStatus heygen response data error, responseModel:%+v", videoID, responseModel)
		return "", errors.New("heygen response data error")
	}

	costTime := time.Since(beginTime)
	logger.Log.Infof(utils.MMark(logCtx)+"videoID:%s,invokeCheckProofreadStatus end costIime: %+v, responseModel: %s\n", videoID, costTime, resp)
	return responseModel.Data.Status, nil
}

// 调用hg的根据 proofread 生成视频的 接口
func invokeGenerateVideoFromProofread(logCtx context.Context, apiKey string, videoModel model.VideoTranslateEntity) (string, error) {
	videoID := videoModel.VideoID
	url := conf.LocalConfig.HeygenSettings.GenerateVideoFromProofreadUrl + videoModel.ProofreadID + "/generate"
	beginTime := time.Now()

	reqModel := heygen.GenerateVideoFromProofreadRequest{
		Captions:             videoModel.EnableCaption == 1,
		Translate_audio_only: videoModel.TranslateAudioOnly == 1,
	}

	logger.Log.Infof(utils.MMark(logCtx)+"videoID:%s,invokeGenerateVideoFromProofread apiKey:%s, url: %s, request: %+v\n", videoID, apiKey, url, reqModel)
	// 序列化请求数据
	jsonData, err := json.Marshal(reqModel)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"videoID:%s,invokeGenerateVideoFromProofread Marshal request failed: %+v\n", videoID, err)
		return "", err
	}

	// 创建请求对象
	httpclient := httputil.NewRetryHTTPClient(120*time.Second, 3)
	header := make(map[string]string)
	header["accept"] = "application/json"
	header["content-type"] = "application/json"
	header["x-api-key"] = apiKey

	resp, err := httpclient.DoRequest(logCtx, "POST", url, header, bytes.NewBuffer(jsonData))
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"videoID:%s,invokeGenerateVideoFromProofread request failed: %+v\n", videoID, err)
		return "", err
	}
	responseModel := &heygen.TranslateVideoResponse{}
	err = json.Unmarshal(resp, responseModel)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"videoID:%s,invokeGenerateVideoFromProofread resp json.Unmarshal error: %v,responseModel:%+v", videoID, err, responseModel)
		return "", err
	}
	if responseModel.Error != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"videoID:%s,invokeGenerateVideoFromProofread  heygen resp error, responseModel:%+v", videoID, responseModel)
		return "", errors.New(responseModel.Error.Code + "," + responseModel.Error.Message)
	}

	if responseModel.Data == nil || responseModel.Data.VideoTranslateId == "" {
		logger.Log.Errorf(utils.MMark(logCtx)+"videoID:%s,invokeGenerateVideoFromProofread heygen response data error, responseModel:%+v", videoID, responseModel)
		return "", errors.New("heygen response data error")
	}

	costTime := time.Since(beginTime)
	logger.Log.Infof(utils.MMark(logCtx)+"videoID:%s,invokeGenerateVideoFromProofread end costIime: %+v, responseModel: %s\n", videoID, costTime, resp)
	return responseModel.Data.VideoTranslateId, nil
}

// 调用 heygen的直接视频翻译的接口
func invokeTranslateVideo(logCtx context.Context, apiKey string, videoModel model.VideoTranslateEntity) (string, error) {
	videoID := videoModel.VideoID
	url := conf.LocalConfig.HeygenSettings.TranslateVideoUrl
	beginTime := time.Now()

	reqModel := heygen.GenerateProofreadRequest{
		Title:                 videoModel.Name,
		VideoURL:              videoModel.OriginVideoUrl,
		OutputLanguage:        videoModel.TranslateLan,
		SpeakerNum:            int(videoModel.SpeakerNum),
		EnableDynamicDuration: videoModel.EnableDynamicDuration == 1,
	}

	logger.Log.Infof(utils.MMark(logCtx)+"videoID:%s,invokeTranslateVideo apiKey:%s, url: %s, request: %+v\n", videoID, apiKey, url, reqModel)
	// 序列化请求数据
	jsonData, err := json.Marshal(reqModel)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"videoID:%s,invokeTranslateVideo Marshal request failed: %+v\n", videoID, err)
		return "", err
	}

	// 创建请求对象
	httpclient := httputil.NewRetryHTTPClient(120*time.Second, 3)
	header := make(map[string]string)
	header["accept"] = "application/json"
	header["content-type"] = "application/json"
	header["x-api-key"] = apiKey

	resp, err := httpclient.DoRequest(logCtx, "POST", url, header, bytes.NewBuffer(jsonData))
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"videoID:%s,invokeTranslateVideo request failed: %+v\n", videoID, err)
		return "", err
	}
	responseModel := &heygen.TranslateVideoResponse{}
	err = json.Unmarshal(resp, responseModel)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"videoID:%s,invokeTranslateVideo resp json.Unmarshal error: %v,responseModel:%+v", videoID, err, responseModel)
		return "", err
	}
	if responseModel.Error != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"videoID:%s,invokeTranslateVideo  heygen resp error, responseModel:%+v", videoID, responseModel)
		return "", errors.New(responseModel.Error.Code + "," + responseModel.Error.Message)
	}

	if responseModel.Data == nil || responseModel.Data.VideoTranslateId == "" {
		logger.Log.Errorf(utils.MMark(logCtx)+"videoID:%s,invokeTranslateVideo heygen response data error, responseModel:%+v", videoID, responseModel)
		return "", errors.New("heygen response data error")
	}

	costTime := time.Since(beginTime)
	logger.Log.Infof(utils.MMark(logCtx)+"videoID:%s,invokeTranslateVideo end costIime: %+v, responseModel: %s\n", videoID, costTime, resp)
	return responseModel.Data.VideoTranslateId, nil
}

// 调用hg的视频生成查询接口
func invokeCheckTranslateStatus(logCtx context.Context, apiKey string, videoModel model.VideoTranslateEntity) (*heygen.ResponseData, error) {
	videoID := videoModel.VideoID
	url := conf.LocalConfig.HeygenSettings.CheckTranslateStatusUrl + videoModel.VideoTranslateId
	logger.Log.Infof(utils.MMark(logCtx)+"videoID:%s,invokeCheckTranslateStatus start apiKey:%s, url: %s", videoID, apiKey, url)
	beginTime := time.Now()
	// 创建请求对象
	httpclient := httputil.NewRetryHTTPClient(120*time.Second, 3)
	header := make(map[string]string)
	header["accept"] = "application/json"
	header["content-type"] = "application/json"
	header["x-api-key"] = apiKey

	resp, err := httpclient.DoRequest(logCtx, "GET", url, header, nil)
	responseModel := &heygen.TranslateVideoResponse{}
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"videoID:%s,invokeCheckTranslateStatus request failed: %+v\n", videoID, err)
		resData := &heygen.ResponseData{}
		return resData, nil
	}
	err = json.Unmarshal(resp, responseModel)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"videoID:%s,invokeCheckTranslateStatus resp json.Unmarshal error: %v,responseModel:%+v", videoID, err, responseModel)
		resData := &heygen.ResponseData{}
		return resData, nil
	}
	if responseModel.Error != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"videoID:%s,invokeCheckTranslateStatus  heygen resp error:%+v", videoID, responseModel.Error.Code+","+responseModel.Error.Message)
		return responseModel.Data, errors.New(responseModel.Error.Code + "," + responseModel.Error.Message)
	}

	if responseModel.Data == nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"videoID:%s,invokeCheckTranslateStatus heygen response data error, responseModel:%+v", videoID, responseModel)
		esData := &heygen.ResponseData{}
		return esData, errors.New("heygen response data error")
	}

	costTime := time.Since(beginTime)
	logger.Log.Infof(utils.MMark(logCtx)+"videoID:%s,invokeCheckTranslateStatus end costIime: %+v, responseModel: %s\n", videoID, costTime, resp)
	return responseModel.Data, nil
}

// 调用hg的字幕接口
func invokeTranslateCaption(logCtx context.Context, apiKey string, videoModel model.VideoTranslateEntity) (string, error) {
	videoID := videoModel.VideoID
	baseUrl := conf.LocalConfig.HeygenSettings.VideoTranslateCaptionUrl
	params := url.Values{}
	params.Add("video_translate_id", videoModel.VideoTranslateId)
	params.Add("caption_type", "vtt")
	tempUrl, _ := url.Parse(baseUrl)
	tempUrl.RawQuery = params.Encode()
	reqUrl := tempUrl.String()
	logger.Log.Infof(utils.MMark(logCtx)+"videoID:%s,invokeTranslateCaption start apiKey:%s, url: %s", videoID, apiKey, reqUrl)

	beginTime := time.Now()
	// 创建请求对象
	httpclient := httputil.NewRetryHTTPClient(120*time.Second, 3)
	header := make(map[string]string)
	header["accept"] = "application/json"
	header["content-type"] = "application/json"
	header["x-api-key"] = apiKey

	resp, err := httpclient.DoRequest(logCtx, "GET", reqUrl, header, nil)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"videoID:%s,invokeTranslateCaption request failed: %+v\n", videoID, err)
		return "", err
	}
	responseModel := &heygen.TranslateVideoResponse{}
	err = json.Unmarshal(resp, responseModel)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"videoID:%s,invokeTranslateCaption resp json.Unmarshal error: %v,responseModel:%+v", videoID, err, responseModel)
		return "", err
	}
	if responseModel.Error != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"videoID:%s,invokeTranslateCaption  heygen resp error, responseModel:%+v", videoID, responseModel)
		return "", errors.New(responseModel.Error.Code + "," + responseModel.Error.Message)
	}

	if responseModel.Data == nil || responseModel.Data.CaptionUrl == "" {
		logger.Log.Errorf(utils.MMark(logCtx)+"videoID:%s,invokeTranslateCaption heygen response data error, responseModel:%+v", videoID, responseModel)
		return "", errors.New("heygen response data error")
	}

	costTime := time.Since(beginTime)
	logger.Log.Infof(utils.MMark(logCtx)+"videoID:%s,invokeTranslateCaption end costIime: %+v, responseModel: %s\n", videoID, costTime, resp)
	return responseModel.Data.CaptionUrl, nil
}

// 调用百度-自研的视频生成接口
func submitVideoTranslateToBaidu(logCtx context.Context, videoModel model.VideoTranslateEntity) (proto.SubmitToBaiduResult, error) {
	videoID := videoModel.VideoID
	url := conf.LocalConfig.TranslateSettings.VideoTranslateByBaiduHost + conf.LocalConfig.TranslateSettings.VideoTranslateByBaiduSubmitPath
	beginTime := time.Now()
	responseModel := &proto.SubmitToBaiduResponse{}

	reqModel := proto.SubmitToBaiduRequest{
		UserId:    videoModel.UserID,
		FileName:  videoModel.Name,
		VideoUrl:  videoModel.OriginVideoUrl,
		Thumbnail: videoModel.Thumbnail,
		SubmitConfig: proto.SubmitConfig{
			TargetLangs:           []string{videoModel.TranslateLan},
			SpeakerNum:            1,
			TranslateAudioOnly:    videoModel.TranslateAudioOnly == 1,
			EnableDynamicDuration: videoModel.EnableDynamicDuration == 1,
			EnableCaptions:        videoModel.EnableCaption == 1,
		},
	}

	logger.Log.Infof(utils.MMark(logCtx)+"videoID:%s,submitVideoTranslateToBaidu url: %s, request: %+v\n", videoID, url, reqModel)
	// 序列化请求数据
	jsonData, err := json.Marshal(reqModel)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"videoID:%s,submitVideoTranslateToBaidu Marshal request failed: %+v\n", videoID, err)
		return responseModel.Result, err
	}

	// 创建请求对象
	httpclient := httputil.NewRetryHTTPClient(120*time.Second, 3)
	header := make(map[string]string)
	header["accept"] = "application/json"
	header["content-type"] = "application/json"

	resp, err := httpclient.DoRequest(logCtx, "POST", url, header, bytes.NewBuffer(jsonData))
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"videoID:%s,submitVideoTranslateToBaidu request failed: %+v\n", videoID, err)
		return responseModel.Result, err
	}
	err = json.Unmarshal(resp, responseModel)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"videoID:%s,submitVideoTranslateToBaidu resp json.Unmarshal error: %v,resp:%+v", videoID, err, resp)
		return responseModel.Result, err
	}
	if responseModel.Code != 0 {
		logger.Log.Errorf(utils.MMark(logCtx)+"videoID:%s,submitVideoTranslateToBaidu resp error, responseModel:%+v", videoID, responseModel)
		return responseModel.Result, fmt.Errorf("code: %d, msg: %s", responseModel.Code, responseModel.Message.Global)
	}
	costTime := time.Since(beginTime)
	logger.Log.Infof(utils.MMark(logCtx)+"videoID:%s,submitVideoTranslateToBaidu end costIime: %+v, responseModel: %s\n", videoID, costTime, responseModel)
	return responseModel.Result, nil
}

// 调用百度-自研的视频翻译查询接口
func queryVideoTranslateFromBaidu(logCtx context.Context, videoModel model.VideoTranslateEntity) (proto.QueryResultModel, error) {
	videoID := videoModel.VideoID
	url := conf.LocalConfig.TranslateSettings.VideoTranslateByBaiduHost + conf.LocalConfig.TranslateSettings.VideoTranslateByBaiduQueryPath
	beginTime := time.Now()
	responseModel := &proto.QueryFromBaiduResponse{}
	reqModel := proto.QueryToBaiduRequest{
		UserId:   videoModel.UserID,
		Ids:      []string{videoModel.VideoTranslateId},
		PageNo:   1,
		PageSize: 10,
	}

	logger.Log.Infof(utils.MMark(logCtx)+"videoID:%s,queryVideoTranslateFromBaidu url: %s, request: %+v\n", videoID, url, reqModel)
	// 序列化请求数据
	jsonData, err := json.Marshal(reqModel)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"videoID:%s,queryVideoTranslateFromBaidu Marshal request failed: %+v\n", videoID, err)
		return proto.QueryResultModel{}, err
	}

	// 创建请求对象
	httpclient := httputil.NewRetryHTTPClient(120*time.Second, 3)
	header := make(map[string]string)
	header["accept"] = "application/json"
	header["content-type"] = "application/json"

	resp, err := httpclient.DoRequest(logCtx, "POST", url, header, bytes.NewBuffer(jsonData))
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"videoID:%s,queryVideoTranslateFromBaidu request failed: %+v\n", videoID, err)
		return proto.QueryResultModel{}, err
	}
	err = json.Unmarshal(resp, responseModel)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"videoID:%s,queryVideoTranslateFromBaidu resp json.Unmarshal error: %v,resp:%+v", videoID, err, resp)
		return proto.QueryResultModel{}, err
	}
	if responseModel.Code != 0 {
		logger.Log.Errorf(utils.MMark(logCtx)+"videoID:%s,queryVideoTranslateFromBaidu resp error, responseModel:%+v", videoID, responseModel)
		return proto.QueryResultModel{}, fmt.Errorf("code: %d, msg: %s", responseModel.Code, responseModel.Message.Global)
	}
	if len(responseModel.Result.Result) == 0 {
		logger.Log.Errorf(utils.MMark(logCtx)+"videoID:%s,queryVideoTranslateFromBaidu resp error, responseModel.Result is nill responseModel:%+v", videoID, responseModel)
		return proto.QueryResultModel{}, fmt.Errorf("code: %d, msg: %s", responseModel.Code, "queryVideoTranslateFromBaidu resp error, responseModel.Result is nill")
	}

	costTime := time.Since(beginTime)
	logger.Log.Infof(utils.MMark(logCtx)+"videoID:%s,queryVideoTranslateFromBaidu end costIime: %+v, responseModel: %s\n", videoID, costTime, responseModel)
	return responseModel.Result.Result[0], nil
}

// 下载文件
func downloadFile(logCtx context.Context, url string, filePath string) error {
	logger.Log.Infof(utils.MMark(logCtx)+"start to download file: %v", filePath)
	err := fileutils.RetryDownloadFile(url, filePath, 3)
	if err != nil {
		return fmt.Errorf("download file failed: %v", err)
	}
	logger.Log.Infof(utils.MMark(logCtx)+"download to local done, file: %v", filePath)
	return nil
}

// 合并字幕和视频
func mergerVideoAndCaption(logCtx context.Context, language string, videoID string, inputvideoPath string, captionPath string, outputVideoPath string) error {
	language = strings.ToLower(language)
	logger.Log.Infof(utils.MMark(logCtx)+"videoID:%s,mergerVideoAndCaption strart,language:%s, inputvideoPath: %s, captionPath: %s, outputVideoPath: %s", videoID, language, inputvideoPath, captionPath, outputVideoPath)
	fontPath := "fontsdir=/usr/share/fonts/noto-sans/NotoSans-Condensed.ttf"
	if strings.Contains(language, "arabic") {
		fontPath = "fontsdir=/usr/share/fonts/noto-arabic/NotoSansArabic-SemiCondensedThin.ttf"
	} else if strings.Contains(language, "hindi") {
		fontPath = "fontsdir=/usr/share/fonts/noto-hindi/NotoSansDevanagariUI-SemiCondensed.ttf"
	} else if strings.Contains(language, "chinese") {
		fontPath = "fontsdir=/usr/share/fonts/opentype/source-han-sans/SourceHanSansCN-Regular.otf"
	} else if strings.Contains(language, "japanese") {
		fontPath = "fontsdir=/usr/share/fonts/opentype/source-han-sans/SourceHanSansJP-Regular.otf"
	} else if strings.Contains(language, "korean") {
		fontPath = "fontsdir=/usr/share/fonts/opentype/source-han-sans/SourceHanSansKR-Regular.otf"
	} else if strings.Contains(language, "thai") {
		fontPath = "fontsdir=/usr/share/fonts/noto-thai/NotoSansThai-Regular.ttf"
	}

	cmd := exec.Command("ffmpeg",
		"-i", inputvideoPath,
		// "-vf", fmt.Sprintf("subtitles=%s", captionPath),
		"-vf", fmt.Sprintf("subtitles=%s:%s", captionPath, fontPath),
		"-c:v", "libx264",
		"-crf", "18",
		"-preset", "slow",
		"-c:a", "copy",
		outputVideoPath,
	)

	var stdoutBuf, stderrBuf bytes.Buffer
	cmd.Stdout = &stdoutBuf
	cmd.Stderr = &stderrBuf

	err := cmd.Run()
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"videoID:%s,mergerVideoAndCaption failed error:%v,stderrBuf:%s", videoID, err, stderrBuf.String())
		return err
	}
	logger.Log.Infof(utils.MMark(logCtx)+"videoID:%s,mergerVideoAndCaption success,cmd: %v", videoID, cmd.String())
	return nil
}

// 生成缩略图  推荐参数：ffmpeg -i input.mp4 -ss 00:00:01 -vframes 1 output.jpg
func generateThumbImage(videoPath string, imagePath string) error {
	cmd := exec.Command("ffmpeg", "-i", videoPath, "-ss", "00:00:01", "-vframes", "1", imagePath)
	var stdoutBuf, stderrBuf bytes.Buffer
	cmd.Stdout = &stdoutBuf
	cmd.Stderr = &stderrBuf
	err := cmd.Run()
	if err != nil {
		logger.Log.Errorf("generateThumbImage,Error executing ffmpeg command,error: %v imagePath: %s Stderr: %s\n", err, imagePath, stderrBuf.String())
		return err
	}
	logger.Log.Infof("generateThumbImage,ffmpeg Command executed successfully.\n %s Stdout: %s\n", imagePath, stdoutBuf.String())
	return nil
}

func GenerateThumbImageUrl(logCtx context.Context, videoUrl string, userID string, videoID string) (string, error) {
	// 生成缩略图
	uuidStr := uuid.New().String()
	thumbImagePath := fmt.Sprintf(DownloadPathFmt, userID, uuidStr, videoID+"_th.jpg")

	err := os.MkdirAll(filepath.Dir(thumbImagePath), 0755)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"GenerateThumbImageUrl MkdirAll() error=%v", err)
		return "", err
	}

	defer os.Remove(thumbImagePath) // 删除缩略图

	err = generateThumbImage(videoUrl, thumbImagePath)
	if err != nil {
		// 生成缩略图失败
		logger.Log.Errorf(utils.MMark(logCtx)+"GenerateThumbImageUrl failed,videoID:%s,thumbImagePath:%s, err:%v", videoID, thumbImagePath, err)
		return "", err
	}
	// 上传缩略图
	thumbImageBosKey := fmt.Sprintf(VideoToBosFmt, userID, uuidStr, videoID+"_th.jpg")
	thumbImageUrl, err := uploadBosServiceFromFile(logCtx, thumbImageBosKey, thumbImagePath)
	if err != nil {
		// 上传缩略图失败
		logger.Log.Errorf(utils.MMark(logCtx)+"GenerateThumbImageUrl upload thumbImage failed,videoID:%s,thumbImagePath:%s, err:%v", videoID, thumbImagePath, err)
		return "", err
	}
	logger.Log.Infof(utils.MMark(logCtx)+"GenerateThumbImageUrl upload thumbImage ok,videoID:%s,thumbImageUrl:%s", videoID, thumbImageUrl)
	return thumbImageUrl, nil
}

func uploadBosServiceFromFile(logCtx context.Context, objectKeyPath string, localFilePath string) (string, error) {
	extWithDot := filepath.Ext(localFilePath)
	// url, err := storage.RetryUploadFromFile(logCtx, objectKeyPath, localFilePath, extWithDot)
	url, err := storage.RetryUploadFromFileWithCDNAndContentType(logCtx, objectKeyPath, localFilePath, extWithDot, false, storage.ContentTypeStream)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"RetryUploadFromFile fail, err:%v", err)
		return "", err
	}
	logger.Log.Infof(utils.MMark(logCtx)+"RetryUploadFromFile success, url:%s", url)
	return url, nil
}

type VideoMeta struct {
	Format struct {
		Duration string `json:"duration"`
	} `json:"format"`
	Streams []struct {
		Width  int `json:"width"`
		Height int `json:"height"`
	} `json:"streams"`
}

// 获取视频的时长和宽高  推荐参数：ffmpeg -i input.mp4 -ss 00:00:01 -vframes 1 output.jpg
func getVideoInfo(videoID string, videoPath string) (*VideoMeta, error) {
	cmd := exec.Command("ffprobe", "-v", "quiet", "-print_format", "json",
		"-show_format", "-show_streams", videoPath)
	output, err := cmd.CombinedOutput()

	if err != nil {
		logger.Log.Errorf("videoID:%s,ffprobe getVideoInfo fail, err:%v", videoID, err)
		return nil, err
	}

	var meta VideoMeta
	err = json.Unmarshal(output, &meta)

	if err != nil {
		logger.Log.Errorf("videoID:%s,getVideoInfo json Unmarshal fail, err:%v", videoID, err)
		return nil, err
	}
	logger.Log.Infof("videoID:%s,getVideoInfo ok, meta:%+v", videoID, meta)
	return &meta, nil
}

// 每月1号重置Heygen的月度统计
func ResetHeygenAmountOfMonth() {
	err := model.ResetAllAmountOfMonth(gomysql.DB)
	logger.Log.Errorf("ResetHeygenAmountOfMonth, err:%v", err)
}
