package services

import (
	"acg-ai-go-common/logger"
	"digital-human-video-translate/conf"
	"digital-human-video-translate/handler/services/schedeuler"
	"digital-human-video-translate/thirdparty/sutils/cron"
)

// InitScheduler 初始化调度器
func InitScheduler() error {
	c := cron.GetCron()
	heygenShecdulerSpec := conf.LocalConfig.ScheduleSettings.ScheduleCronHeygenSpec
	_, err := c.<PERSON>(heygenShecdulerSpec, schedeuler.HeygenTaskScheduler)
	if err != nil {
		logger.Log.Fatalf("RegisterCron HgTaskScheduler error,heygenShecdulerSpec:%s,error: %v\n", heygenShecdulerSpec, err)
		return err
	}
	_, err = c.<PERSON><PERSON>ron("@monthly", schedeuler.ResetHeygenAmountOfMonth)
	if err != nil {
		logger.Log.Fatalf("RegisterCron ResetHeygenAmountOfMonth error: %v\n", err)
		return err
	}
	c.StartCron()
	logger.Log.Infof("RegisterCron TaskScheduler success,heygenShecdulerSpec:%s", heygenShecdulerSpec)

	return nil
}
