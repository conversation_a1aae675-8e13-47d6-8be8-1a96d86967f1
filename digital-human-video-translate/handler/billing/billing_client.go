package billing

import (
	"acg-ai-go-common/logger"
	"bytes"
	"digital-human-video-translate/conf"
	"encoding/json"
	"fmt"
	"github.com/google/uuid"
	"net/http"
	"time"
)

type BillingClient struct {
	httpClient *http.Client
}

var billingClient *BillingClient

var (
	FreezePath   = "/api/internal/workflow/v1/account/quota/freezeQuota"
	UnfreezePath = "/api/internal/workflow/v1/account/quota/unFreezeQuota"
	ConfirmPath  = "/api/internal/workflow/v1/account/quota/confirmedCost"
)

func NewBillingClient() *BillingClient {
	if billingClient == nil {
		billingClient = &BillingClient{
			httpClient: &http.Client{
				Timeout: 180 * time.Second,
			},
		}
	}
	return billingClient
}

type FreezeRequest struct {
	AccountID         string `json:"accountId"`         // 用户账号ID
	UserID            string `json:"userId"`            // 用户ID
	QuotaType         string `json:"quotaType"`         // 配额类型
	AssetID           string `json:"assetId"`           // 资产ID
	QuotaFreezeAmount int    `json:"quotaFreezeAmount"` // 扣除配额数量
	ReqID             string `json:"reqId"`             // 请求ID
}

type UnFreezeRequest struct {
	AccountID           string `json:"accountId"`           // 用户账号ID
	UserID              string `json:"userId"`              // 用户ID
	QuotaType           string `json:"quotaType"`           // 配额类型
	AssetID             string `json:"assetId"`             // 资产ID
	QuotaUnFreezeAmount int    `json:"quotaUnFreezeAmount"` // 扣除配额数量
	ReqID               string `json:"reqId"`               // 请求ID
}

type ConfirmedRequest struct {
	AccountID                string `json:"accountId"`                // 用户账号ID
	UserID                   string `json:"userId"`                   // 用户ID
	QuotaType                string `json:"quotaType"`                // 配额类型
	AssetID                  string `json:"assetId"`                  // 资产ID
	QuotaConfirmedCostAmount int    `json:"quotaConfirmedCostAmount"` // 扣除配额数量
	ReqID                    string `json:"reqId"`                    // 请求ID
}

type Message struct {
	Global   string `json:"global"`   // 全局消息
	Redirect string `json:"redirect"` // 重定向
}

type Response struct {
	Code    int     `json:"code"`    // 响应代码
	Message Message `json:"message"` // 消息体
	Result  any     `json:"result"`  // 结果
}

// Freeze 冻结
func (c *BillingClient) Freeze(accountID, userID string, durationSecond int, assetID string) error {
	logger.Log.Infof("BillingClient Freeze,start,accountID=%v,assetID=%v,durationSecond:%d", accountID, assetID, durationSecond)
	url := conf.LocalConfig.DhUserSetting.BillingBaseHost + FreezePath
	req := &FreezeRequest{
		AccountID:         accountID,
		UserID:            userID,
		QuotaType:         conf.LocalConfig.DhUserSetting.QuotaType,
		AssetID:           assetID,
		QuotaFreezeAmount: durationSecond,
		ReqID:             uuid.New().String(),
	}

	jsonData, _ := json.Marshal(req)

	logger.Log.Infof("BillingClient Freeze Request:%+v,assetID=%s", req, assetID)
	render := bytes.NewBuffer(jsonData)
	res, err := c.httpClient.Post(url, "application/json", render)
	if err != nil {
		logger.Log.Error(err)
		return err
	}
	defer res.Body.Close()
	response := &Response{}
	err = json.NewDecoder(res.Body).Decode(&response)
	if err != nil {
		logger.Log.Errorf("BillingClient Freeze response Decode error:%+v,assetID=%s", err, assetID)
		return err
	}
	logger.Log.Infof("BillingClient Freeze response:%+v,assetID=%s", response, assetID)

	if response.Code != 0 {
		if response.Code == 5001 {
			errMsg := fmt.Sprintf("accountId(%s), userId(%s) quota not enough", accountID, userID)
			logger.Log.Error(errMsg)
			return fmt.Errorf("quota Freeze error:%s" + errMsg)
		} else {
			logger.Log.Error(response.Message.Global)
			return fmt.Errorf(response.Message.Global)
		}
	}
	logger.Log.Infof("BillingClient Freeze ok accountID=%v,assetID=%v", accountID, assetID)
	return nil
}

// Unfreeze 解冻
func (c *BillingClient) Unfreeze(accountID string, durationSecond int, assetID string) error {
	logger.Log.Infof("BillingClient Unfreeze,start,accountID=%v,assetID=%v,durationSecond:%d", accountID, assetID, durationSecond)
	url := conf.LocalConfig.DhUserSetting.BillingBaseHost + UnfreezePath
	req := &UnFreezeRequest{
		AccountID: accountID,
		// UserID:              userID,
		QuotaType:           conf.LocalConfig.DhUserSetting.QuotaType,
		AssetID:             assetID,
		QuotaUnFreezeAmount: durationSecond,
		ReqID:               uuid.New().String(),
	}
	jsonData, _ := json.Marshal(req)
	logger.Log.Infof("BillingClient Unfreeze Request:%+v,assetID=%v", req, assetID)
	render := bytes.NewBuffer(jsonData)
	res, err := c.httpClient.Post(url, "application/json", render)
	if err != nil {
		logger.Log.Error(err)
		return err
	}
	defer res.Body.Close()
	response := &Response{}
	err = json.NewDecoder(res.Body).Decode(&response)
	if err != nil {
		logger.Log.Errorf("BillingClient Unfreeze Decode error,accountID:%s,assetID:%s,error:%+v", accountID, assetID, err.Error())
		return err
	}

	logger.Log.Infof("BillingClient Unfreeze response:%+v,assetID=%s", response, assetID)
	if response.Code != 0 {
		if response.Code == 5001 {
			errMsg := fmt.Sprintf("accountId(%s),quota not enough", accountID)
			logger.Log.Error(errMsg)
			return fmt.Errorf("quota unFreeze error:%s" + errMsg)
		} else {
			logger.Log.Error(response.Message.Global)
			return fmt.Errorf(response.Message.Global)
		}
	}
	logger.Log.Infof("BillingClient Unfreeze ok accountID=%v,assetID=%vd", accountID, assetID)
	return nil
}

// Confirm 确认扣除
func (c *BillingClient) Confirm(accountID string, durationSecond int, assetID string) error {
	logger.Log.Infof("BillingClient Confirm start,accountID=%v,assetID=%s,durationSecond:%d", accountID, assetID, durationSecond)
	url := conf.LocalConfig.DhUserSetting.BillingBaseHost + ConfirmPath
	req := &ConfirmedRequest{
		AccountID: accountID,
		// UserID:                   userID,
		QuotaType:                conf.LocalConfig.DhUserSetting.QuotaType,
		AssetID:                  assetID,
		QuotaConfirmedCostAmount: durationSecond,
		ReqID:                    uuid.New().String(),
	}
	jsonData, _ := json.Marshal(req)
	logger.Log.Infof("BillingClient Confirm req:%+v,assetID=%s", req, assetID)
	render := bytes.NewBuffer(jsonData)
	res, err := c.httpClient.Post(url, "application/json", render)
	if err != nil {
		logger.Log.Error(err)
		return err
	}
	defer res.Body.Close()
	response := &Response{}
	err = json.NewDecoder(res.Body).Decode(&response)
	if err != nil {
		logger.Log.Errorf("BillingClient Confirm response Decode error:%+v,assetID=%s", err.Error(), assetID)
		return err
	}
	logger.Log.Infof("BillingClient Confirm response:%+v,assetID=%s", response, assetID)

	if response.Code != 0 {
		if response.Code == 5001 {
			errMsg := fmt.Sprintf("accountId:%s,assetID:%s,message:%s", accountID, assetID, response.Message.Global)
			logger.Log.Error(errMsg)
			return fmt.Errorf("quota confirm error:%s", errMsg)
		} else {
			logger.Log.Error(response)
			return fmt.Errorf(response.Message.Global)
		}
	}
	logger.Log.Infof("BillingClient Confirm ok accountID=%v,assetID=%v", accountID, assetID)
	return nil
}
