package controller

import (
	"acg-ai-go-common/gomysql"
	"acg-ai-go-common/logger"
	"acg-ai-go-common/utils"
	"context"
	"digital-human-video-translate/beans/constant"
	"digital-human-video-translate/beans/model"
	"digital-human-video-translate/beans/proto"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

func TranslateVideoList(c *gin.Context) {
	logId := uuid.New().String()
	logCtx := context.WithValue(context.TODO(), utils.CtxKeyLogID, logId)

	beginTime := time.Now()
	req := proto.TranslateVideoListRequest{}
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"TranslateVideoList BindJSON fail, err:%v", err)
		c.JSO<PERSON>(http.StatusOK, proto.NewCommonResponse(100001, "params bind fail", nil))
		return
	}

	logger.Log.Infof(utils.MMark(logCtx)+"TranslateVideoList start req:%+v", req)
	videoModelTemps, totalCount, err := model.QueryVideosWithCondition(gomysql.DB, req.AccountId, req.Name, req.PageNo, req.PageSize)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"TranslateVideoList QueryVideos fail, err:%v", err)
		c.JSON(http.StatusOK, proto.NewCommonResponse(100002, "QueryVideos fail", nil))
		return
	}

	//1待翻译 WAITING、 2翻译中 RUNNING、 3成功 SUCCESS、 4失败 FAILED

	list := make([]proto.TranslateVideoListData, 0)
	for _, videoModelTemp := range videoModelTemps {
		videoUrlTemp := ""
		// ThumbnailTemp := ""
		// durationTemp := int64(0)
		if videoModelTemp.Status == constant.VideoStatus_Success {
			videoUrlTemp = videoModelTemp.VideoUrl
			// ThumbnailTemp = videoModelTemp.Thumbnail
			// durationTemp = videoModelTemp.Duration
		}

		list = append(list, proto.TranslateVideoListData{
			VideoID:      videoModelTemp.VideoID,
			Name:         videoModelTemp.Name,
			UserID:       videoModelTemp.UserID,
			LastUpdateBy: videoModelTemp.LastUpdateBy,
			VideoUrl:     videoUrlTemp,
			Thumbnail:    videoModelTemp.Thumbnail,
			Duration:     videoModelTemp.OriginDuration,
			Width:        videoModelTemp.Width,
			Height:       videoModelTemp.Height,
			Language:     videoModelTemp.TranslateLan,
			FailedReason: videoModelTemp.ReasonToUser,
			Status:       videoModelTemp.Status,
			CreateTime:   videoModelTemp.CreatedAt.Add(time.Hour * 8 * -1).Format("2006-01-02T15:04:05"),
			UpdateTime:   videoModelTemp.UpdatedAt.Add(time.Hour * 8 * -1).Format("2006-01-02T15:04:05"),
		})
	}
	resp := proto.TranslateVideoListResponse{
		PageNo:     req.PageNo,
		PageSize:   req.PageSize,
		TotalCount: int(totalCount),
		Result:     list,
	}
	costTime := time.Since(beginTime)
	logger.Log.Infof(utils.MMark(logCtx)+"TranslateVideoList end:%+v, cost:%v,resp:%+v", req, costTime, resp)
	c.JSON(http.StatusOK, proto.NewCommonResponse(0, "success", resp))
}

func TranslateDelete(c *gin.Context) {
	logId := uuid.New().String()
	logCtx := context.WithValue(context.TODO(), utils.CtxKeyLogID, logId)

	beginTime := time.Now()
	req := proto.TranslateDelRequest{}
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"TranslateDelete BindJSON fail, err:%v", err)
		c.JSON(http.StatusOK, proto.NewCommonResponse(100001, "params bind fail", nil))
		return
	}
	logger.Log.Infof(utils.MMark(logCtx)+"TranslateDelete start req:%+v", req)
	deleteModelTemps, err := model.BatchSoftDeleteByVideoIDs(gomysql.DB, req.AccountId, req.Ids)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"TranslateDelete deleteModelTemps fail, err:%v", err)
		c.JSON(http.StatusOK, proto.NewCommonResponse(100002, "delete videos fail", nil))
		return
	}
	list := make([]proto.TranslateVideoListData, 0)
	for _, deleteModelTemp := range deleteModelTemps {
		list = append(list, proto.TranslateVideoListData{
			VideoID:    deleteModelTemp.VideoID,
			Name:       deleteModelTemp.Name,
			CreateTime: deleteModelTemp.CreatedAt.Format("2006-01-02T15:04:05"),
			UpdateTime: deleteModelTemp.UpdatedAt.Format("2006-01-02T15:04:05"),
		})
	}
	costTime := time.Since(beginTime)
	logger.Log.Infof(utils.MMark(logCtx)+"TranslateDelete end:%+v, cost:%v,list:%+v", req, costTime, list)
	c.JSON(http.StatusOK, proto.NewCommonResponse(0, "success", list))
}
