package controller

import (
	"acg-ai-go-common/global"
	"acg-ai-go-common/storage"
	"acg-ai-go-common/utils"
	"context"
	"encoding/base64"
	"fmt"
	"github.com/google/uuid"
	"io/ioutil"
	"testing"
)

func init() {
	config := global.ServerConfig{
		StorageSetting: &global.StorageSetting{
			Type: "gcs",
			GcsSetting: &global.GcsSetting{
				Credential:       "/Users/<USER>/GolandProjects/acg-ai-go-common/storage/storage_test/gcs_credentials.json",
				Bucket:           "xiling_us_central1_bucket",
				Host:             "https://storage.googleapis.com",
				CDNHost:          "https://www.keevaai.com",
				Path:             "backend-saas-cdn/figure-resource",
				RetryTimes:       3,
				RetrySleepMillis: 1000},
		},
	}

	err := storage.Init(&config)
	if err != nil {
		fmt.Printf("init storage failed: %v", err)
		return
	}
}

func TestBase64ImageUpload(t *testing.T) {
	logId := uuid.New().String()
	imagePath := "./e5613d3f-ec5e-483e-94b5-697352e7f138.png"
	logCtx := context.WithValue(context.TODO(), utils.CtxKeyLogID, logId)
	// 图片转base64
	// 读取图片文件
	data, err := ioutil.ReadFile(imagePath)
	if err != nil {
		t.Fatalf("failed to read image: %v", err)
		return
	}
	// 编码为 base64
	base64Str := base64.StdEncoding.EncodeToString(data)

	// 加上 MIME 类型前缀（可选）
	//base64WithPrefix := "data:image/jpeg;base64," + base64Str
	url, err := base64ImageUpload(logCtx, base64Str)
	if err != nil {
		t.Errorf("base64ImageUpload error: %v", err)
		return
	}
	t.Logf("base64ImageUpload success, url: %s", url)
}
