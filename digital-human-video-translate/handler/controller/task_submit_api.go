package controller

import (
	"acg-ai-go-common/gomysql"
	"acg-ai-go-common/logger"
	"acg-ai-go-common/storage"
	"acg-ai-go-common/utils"
	"context"
	"digital-human-video-translate/beans/constant"
	"digital-human-video-translate/beans/model"
	"digital-human-video-translate/beans/proto"
	"digital-human-video-translate/conf"
	"digital-human-video-translate/handler/billing"
	"digital-human-video-translate/handler/services/schedeuler"
	"digital-human-video-translate/thirdparty/ffmpeg"
	"encoding/base64"
	"fmt"
	"math"
	"net/http"
	"os"
	"path/filepath"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

type TaskHandler struct {
}

// HandleSubmitTask 提交任务
func (p TaskHandler) HandleSubmitTask(c *gin.Context) {
	logId := uuid.New().String()
	logCtx := context.WithValue(context.TODO(), utils.CtxKeyLogID, logId)
	beginTime := time.Now()
	accountID := ""
	aid, ok := c.Get(constant.DigitalHumanAccountID)
	if ok {
		accountID = aid.(string)
	}
	userID := ""
	uid, ok := c.Get(constant.DigitalHumanUserID)
	if ok {
		userID = uid.(string)
	}
	userName := ""
	uName, ok := c.Get(constant.DigitalHumanUserName)
	if ok {
		userName = uName.(string)
	}
	logger.Log.Infof(utils.MMark(logCtx)+"HandleSubmitTask accountID:%s, userID:%s,userName:%v", accountID, userID, userName)

	req := proto.SubmitTaskRequest{}
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"HandleSubmitTask BindJSON fail, err:%v", err)
		c.JSON(http.StatusOK, proto.NewCommonResponse(100001, "params bind fail", nil))
		return
	}
	logger.Log.Infof(utils.MMark(logCtx)+"HandleSubmitTask accountID:%s,userName:%s,req:%+v", accountID, userName, req)
	// 判断如果封面图是base64转成图片文件上传对象存储
	if !strings.HasPrefix(req.Thumbnail, "http") {
		thumbnailUrl, err := base64ImageUpload(logCtx, req.Thumbnail)
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+"HandleSubmitTask base64imageUpload error, error:%v", err)
			c.JSON(http.StatusOK, proto.NewCommonResponse(100002, "thumbnail upload failed", nil))
			return
		}
		req.Thumbnail = thumbnailUrl
	}
	logger.Log.Infof(utils.MMark(logCtx)+"HandleSubmitTask accountID:%s,userName:%s,thumbnailUrl:%s",
		accountID, userName, req.Thumbnail)
	dhClient := billing.NewBillingClient()

	if len(req.VideoID) > 0 {
		// videoId不为空，为失败后重试提交
		videoRecord, err := (&model.VideoTranslateEntity{}).QueryItemNotDeleteById(gomysql.DB, accountID, req.VideoID)
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+"HaHandleSubmitTask retry sumbit QueryItemById error, ccountId:%s,userID:%s,req.VideoID:%s,error:%v", accountID, userID, req.VideoID, err)
			c.JSON(http.StatusOK, proto.NewCommonResponse(100004, "this video not found", nil))
			return
		}
		logger.Log.Infof(utils.MMark(logCtx)+"HaHandleSubmitTask retry sumbit query record ok, ccountId:%s,userID:%s,req.VideoID:%s,videoRecord:%+v", accountID, userID, req.VideoID, videoRecord)

		// 冻结视频时长权益,videoid作为权益的assetId
		originDuration := int(math.Ceil(float64(videoRecord.OriginDuration) / 1000))
		err = dhClient.Freeze(accountID, userID, originDuration, req.VideoID)
		if err != nil {
			// 账户权益冻结失败，
			logger.Log.Errorf(utils.MMark(logCtx)+"HandleSubmitTask retry sumbit quota freeze failed, accountId:%s,userID:%s,req.VideoID:%d,error:%v", accountID, userID, req.VideoID, err)
			c.JSON(http.StatusOK, proto.NewCommonResponse(100003, "quota freeze failed", nil))
			return
		}
		logger.Log.Infof(utils.MMark(logCtx)+"HandleSubmitTask retry sumbit quota freeze ok, accountId:%s,userID:%s,req.VideoID:%s,originDuration:%d", accountID, userID, req.VideoID, originDuration)
		// 更新该视频记录的状态,并且更新create_at为当前时间
		err = (&model.VideoTranslateEntity{}).UpdateFieldsByVideoID(gomysql.DB, accountID, req.VideoID,
			map[string]interface{}{
				"status":           constant.VideoStatus_Waiting,
				"translate_status": constant.TransStatus_WaitingRiskControl,
				"create_at":        time.Now(),
				"failed_reason":    "",
				"reason_to_user":   "",
			})
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+"HeygenTaskScheduler retry sumbit UpdateFieldsByVideoID videoId:%s, errorMsg: %+v\n", req.VideoID, err)
			c.JSON(http.StatusOK, proto.NewCommonResponse(100004, "update video status failed", nil))
			return
		}
		c.JSON(http.StatusOK, proto.NewCommonResponse(0, "success", req.VideoID))
		return
	}

	// 以下为新提交任务
	// 基础参数校验
	if len(req.VideoUrl) < 1 || len(req.TargetLangs) < 1 || len(req.FileName) < 1 || len(req.Thumbnail) < 1 {
		logger.Log.Errorf(utils.MMark(logCtx) + "HandleSubmitTask check fail, params invalid")
		c.JSON(http.StatusOK, proto.NewCommonResponse(100001, "params invalid", nil))
		return
	}

	duration, err := ffmpeg.GetDurationFromURL(req.VideoUrl)
	// 获取视频时长,单位毫秒
	if err != nil {
		// 获取视频时长失败
		logger.Log.Errorf(utils.MMark(logCtx)+"HandleSubmitTask ffmpeg.GetDurationFromURL,error: %v", err)
		c.JSON(http.StatusOK, proto.NewCommonResponse(100002, "get video duration failed", nil))
		return
	}
	durationSecond := duration / 1000
	if durationSecond > int64(conf.LocalConfig.HeygenSettings.HgTranslateMaxDuration) {
		// 视频超过限制长度直接返回错误
		logger.Log.Errorf(utils.MMark(logCtx)+"HandleSubmitTask the video duration too long duration: %d,durationSecond:%d,maxDuration:%d,", duration, durationSecond, conf.LocalConfig.HeygenSettings.HgTranslateMaxDuration)
		c.JSON(http.StatusOK, proto.NewCommonResponse(100002, "get video duration failed", nil))
		return
	}
	logger.Log.Infof(utils.MMark(logCtx)+"HandleSubmitTask get video duration ok,duration: %d,durationSecond:%d,maxDuration:%d,", duration, durationSecond, conf.LocalConfig.HeygenSettings.HgTranslateMaxDuration)

	// 通过缩略图，获取宽高
	width, height, err := ffmpeg.GetIamgeWidthAndHeight(req.Thumbnail)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"HandleSubmitTask get thumbnail info error: %v", err)
	}
	logger.Log.Infof(utils.MMark(logCtx)+"HandleSubmitTask get thumbnail info ok,width: %v,height:%v", width, height)

	transVideoArray := make([]model.VideoTranslateEntity, 0)

	videoIds := make([]string, 0)
	durations := make([]int64, 0)

	for id, langCode := range req.TargetLangs {
		// 初始状态
		videoStatus := constant.VideoStatus_Waiting
		transStatus := constant.TransStatus_WaitingRiskControl

		logger.Log.Infof(utils.MMark(logCtx)+"HandleSubmitTask ffmpeg.GetDurationFromURL id:%d, duration: %d", id, duration)
		videoId := "vt" + "-" + utils.RandStringRunes(17)

		// 去掉语言标记中的空格
		langCodeTemp := strings.ReplaceAll(langCode, " ", "")
		// 文件名中的空格以下划线代替
		fileNameTemp := strings.ReplaceAll(req.FileName, " ", "_")

		baseName := langCodeTemp + "-" + fileNameTemp
		videoName := generateUniqueName(logCtx, accountID, baseName)
		logger.Log.Infof(utils.MMark(logCtx)+"HandleSubmitTask  generateUniqueName ok,req.VideoUrl:%s,videoName:%s,langCode:%s", req.VideoUrl, videoName, langCode)

		translateAudioOnly := 0
		if req.TranslateAudioOnly {
			translateAudioOnly = 1
		}
		enableDynamicDuration := 0
		if req.EnableDynamicDuration {
			enableDynamicDuration = 1
		}
		enableCaption := 0
		if req.EnableCaption {
			enableCaption = 1
		}
		item := model.VideoTranslateEntity{
			VideoID:               videoId,
			Name:                  videoName,
			UserID:                accountID,
			LastUpdateBy:          userName,
			OriginVideoUrl:        req.VideoUrl,
			Status:                videoStatus,
			TranslateStatus:       transStatus,
			TranslateType:         constant.VideoTransTypeHg,
			SpeakerNum:            req.SpeakerNum,
			TranslateAudioOnly:    translateAudioOnly,
			EnableDynamicDuration: enableDynamicDuration,
			OriginDuration:        duration,
			Thumbnail:             req.Thumbnail,
			Width:                 width,
			Height:                height,
			EnableCaption:         enableCaption,
			TranslateLan:          langCode,
		}
		logger.Log.Infof(utils.MMark(logCtx)+"HandleSubmitTask idx:%d, itemVideo: %+v", id, item)
		transVideoArray = append(transVideoArray, item)
		// 冻结视频时长权益,videoid作为权益的assetId
		originDuration := int(math.Ceil(float64(duration) / 1000))
		err = dhClient.Freeze(accountID, userID, originDuration, videoId)
		if err != nil {
			// 账户权益冻结失败，
			logger.Log.Errorf(utils.MMark(logCtx)+"HandleSubmitTask quota freeze failed, accountId:%s,userID:%s,id:%d,videoIds:%+d,error:%v", accountID, userID, id, videoIds, err)
			// 账户权益冻结失败时，所有已冻结的权益全部解冻，任务不做提交
			for idx, videoIdTemp := range videoIds {
				dur := durations[idx]
				durationSecond := int(math.Ceil(float64(dur) / 1000))
				err = dhClient.Unfreeze(accountID, durationSecond, videoIdTemp)
				if err != nil {
					logger.Log.Errorf(utils.MMark(logCtx)+"HandleSubmitTask quota unfreeze failed accountId:%s,userID:%s,videoIdItem:%s,dur:%d,error:%v", accountID, userID, videoIdTemp, dur, err)
				}
			}
			c.JSON(http.StatusOK, proto.NewCommonResponse(100003, "quota freeze failed", nil))
			return
		}
		// 账户权益冻结成功，
		logger.Log.Infof(utils.MMark(logCtx)+"HandleSubmitTask quota freeze ok, accountId:%s,userID:%s,id:%d,videoIds:%+d,originDuration:%d", accountID, userID, id, videoIds, originDuration)
		videoIds = append(videoIds, videoId)
		durations = append(durations, duration)
	}

	// 账户权益冻结成功,数据入库
	logger.Log.Infof(utils.MMark(logCtx)+"HandleSubmitTask quota freeze ok accountId:%s,userID:%s,videoIds:%+v", accountID, userID, videoIds)
	err = (&model.VideoTranslateEntity{}).CreateModels(gomysql.DB, transVideoArray)
	if err != nil {
		// ToDo 数据入库失败，解冻权益
		logger.Log.Errorf(utils.MMark(logCtx)+"HaHandleSubmitTask CreateModels error, ccountId:%s,userID:%s,error:%v", accountID, userID, err)
		c.JSON(http.StatusOK, proto.NewCommonResponse(100004, "create models error", nil))
		return
	}
	costTime := time.Since(beginTime)
	logger.Log.Infof(utils.MMark(logCtx)+"HaHandleSubmitTask response ok, ccountId:%s,userID:%s,videoIds:%+v,costTime:%v", accountID, userID, videoIds, costTime)
	c.JSON(http.StatusOK, proto.NewCommonResponse(0, "success", videoIds))
}

func generateUniqueName(logCtx context.Context, accountId, baseName string) string {
	increment := 10
	start := 1

	for {
		// 生成候选名称列表
		var candidateNames []string
		candidateNames = append(candidateNames, baseName)
		for i := start; i < start+increment; i++ {
			candidateNames = append(candidateNames, baseName+"-"+strconv.Itoa(i))
		}
		// 查询已存在的名称（使用map提高查找效率）
		existingModels, err := (&model.VideoTranslateEntity{}).FindByUserIdAndNameIn(gomysql.DB, accountId, candidateNames)
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+"generateUniqueName FindByUserIdAndNameIn error, ccountId:%s,error:%v", accountId, err)
			return baseName
		}
		logger.Log.Infof(utils.MMark(logCtx)+"generateUniqueName ok, ccountId:%s,existingModels:%+v", accountId, existingModels)

		// 检查第一个未被使用的名称
		for _, itemName := range candidateNames {
			isContains := false
			for _, existingModel := range existingModels {
				if existingModel.Name == itemName {
					isContains = true
					break
				}
			}
			if !isContains {
				logger.Log.Infof(utils.MMark(logCtx)+"generateUniqueName end accountId:%s,itemName:%v", accountId, itemName)
				return itemName
			}
		}
		if start > 500 {
			logger.Log.Errorf(utils.MMark(logCtx)+"generateUniqueName end start>200 return baseName, ccountId:%s", accountId)
			return baseName
		}
		start += increment
	}
}

func base64ImageUpload(logCtx context.Context, base64Image string) (string, error) {
	// 清理 Base64 字符串头部（如 data:image/jpeg;base64,）
	if idx := strings.Index(base64Image, "base64,"); idx != -1 {
		base64Image = base64Image[idx+7:]
	}
	// 解码
	imgData, err := base64.StdEncoding.DecodeString(base64Image)
	if err != nil {
		return "", fmt.Errorf("failed to decode base64 image: %v", err)
	}
	// 生成唯一文件名
	filename := fmt.Sprintf("%s.jpg", uuid.New().String())
	// 保存为本地临时文件
	localPath := filepath.Join(os.TempDir(), filename)
	if err := os.WriteFile(localPath, imgData, 0644); err != nil {
		return "", fmt.Errorf("failed to write image to local file, path:%v, err:%v", localPath, err)
	}
	defer os.Remove(localPath)
	// 上传到 GCS
	objectKey := fmt.Sprintf("VIDEO_TRANSLATE/thumbnails/%s/%s", time.Now().Format("2006-01-02"),
		filename)
	return storage.RetryUploadFromFileWithCDNAndContentType(logCtx, objectKey, localPath, filepath.Ext(localPath),
		false, "image/jpeg")
}

func Test() {
	logId := uuid.New().String()
	logCtx := context.WithValue(context.TODO(), utils.CtxKeyLogID, logId)
	videoUrl := "https://storage.googleapis.com/xiling_us_central1_bucket/backend-saas-cdn/xjp-test/figure-resource/VIDEO_TRANSLATE/71e44dd1-68c7-4b47-9fe0-f26b70a3e870/2025-05-29/8e749051-ef1a-47a6-8c11-82e4920c6688.mp4"

	// 生成缩略图url
	thumbImageUrl, err := schedeuler.GenerateThumbImageUrl(logCtx, videoUrl, "0000", "1111")
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"HandleSubmitTask GenerateThumbImageUrl failed,error: %v", err)
	}
	logger.Log.Infof(utils.MMark(logCtx)+"HandleSubmitTask GenerateThumbImageUrl ok,thumbImageUrl:%s", thumbImageUrl)

}
