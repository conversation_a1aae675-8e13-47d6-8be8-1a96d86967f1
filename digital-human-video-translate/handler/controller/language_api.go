package controller

import (
	"acg-ai-go-common/logger"
	"acg-ai-go-common/utils"
	"acg-ai-go-common/utils/httputil"
	"context"
	"net/http"
	"strings"

	"digital-human-video-translate/beans/constant"
	"digital-human-video-translate/beans/proto"
	"digital-human-video-translate/conf"
	"digital-human-video-translate/thirdparty/heygen"
	"encoding/json"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

func HandleLanguageList(c *gin.Context) {
	logId := uuid.New().String()
	logCtx := context.WithValue(context.TODO(), utils.CtxKeyLogID, logId)
	beginTime := time.Now()

	accountID := c.GetHeader(constant.DigitalHumanAccountID)
	userID := c.GetHeader(constant.DigitalHumanUserID)
	userName := c.GetHeader(constant.DigitalHumanUserName)
	logger.Log.Infof(utils.MMark(logCtx)+"HandleLanguageList accountID:%s, userID:%s,userName:%v", accountID, userID, userName)

	url := conf.LocalConfig.HeygenSettings.GetTargetLanguages
	apiKey := conf.LocalConfig.HeygenSettings.ApiKey

	// 创建请求对象
	httpclient := httputil.NewRetryHTTPClient(120*time.Second, 3)
	header := make(map[string]string)
	header["accept"] = "application/json"
	header["content-type"] = "application/json"
	header["x-api-key"] = apiKey

	resp, err := httpclient.DoRequest(logCtx, "GET", url, header, nil)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"accountID:%s,HandleLanguageList request failed: %+v\n", accountID, err)
		c.JSON(http.StatusOK, proto.NewCommonResponse(100001, "get language list  error", nil))
		return
	}
	responseModel := &heygen.TranslateLanguageListResponse{}
	err = json.Unmarshal(resp, responseModel)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"accountID:%s,HandleLanguageList resp json.Unmarshal error: %v,responseModel:%+v", accountID, err, responseModel)
		c.JSON(http.StatusOK, proto.NewCommonResponse(100002, "json unmarshal language list  error", nil))
		return
	}
	if responseModel.Error != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"accountID:%s,HandleLanguageList  heygen resp error, responseModel:%+v", accountID, responseModel)
		c.JSON(http.StatusOK, proto.NewCommonResponse(100003, "get language api error", nil))
		return
	}

	languageArray := strings.Split(conf.LocalConfig.HeygenSettings.ExcludeLanguages, ",")
	excludeLanMap := make(map[string]bool)
	for _, excludeLan := range languageArray {
		excludeLanMap[excludeLan] = true
	}
	reslutLanguages := make([]string, 0)

	for _, lanItem := range responseModel.Data.Languages {
		if !excludeLanMap[lanItem] {
			reslutLanguages = append(reslutLanguages, lanItem)
		}
	}
	costTime := time.Since(beginTime)
	logger.Log.Infof(utils.MMark(logCtx)+"HandleLanguageList response ok, accountId:%s,userID:%s,costTime:%v", accountID, userID, costTime)
	c.JSON(http.StatusOK, proto.NewCommonResponse(0, "success", reslutLanguages))
}
