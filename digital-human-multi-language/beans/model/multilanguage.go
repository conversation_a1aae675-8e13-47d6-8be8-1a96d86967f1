package model

import (
	"acg-ai-go-common/logger"
	"digital-human-multi-language/beans/proto"
	"fmt"
	"strconv"
	"strings"
	"time"

	"gorm.io/gorm"
)

type MultiLanguage struct {
	ID          int64          `json:"id" gorm:"primaryKey;autoIncrement"`
	LanguageKey string         `json:"languageKey" gorm:"column:languageKey;index;type:varchar(512)"`
	ServiceName string         `json:"serviceName" gorm:"column:serviceName;index;type:varchar(100)"`
	SubTag      string         `json:"subTag" gorm:"column:subTag;index;type:varchar(100)"`
	LangText    string         `json:"langText" gorm:"column:langText;type:MEDIUMTEXT"`
	Description string         `json:"description" gorm:"column:description;type:varchar(1024)"`
	CreatedAt   time.Time      `json:"createdAt" gorm:"column:createdAt;autoCreateTime"` // 创建时间
	UpdatedAt   time.Time      `json:"updatedAt" gorm:"column:updatedAt;autoUpdateTime"` // 更新时间
	QueryAt     time.Time      `json:"queryAt" gorm:"column:queryAt;autoCreateTime"`
	DeletedAt   gorm.DeletedAt `json:"-" gorm:"column:deletedAt;index"`
}

// TableName 指定表名
func (MultiLanguage) TableName() string {
	return "dhmh_multi_language"
}

// 返回多语言
func (m *MultiLanguage) MultiLanguage(db *gorm.DB, languageKey string, serviceName, subTag string) (*MultiLanguage, error) {
	var multiLanguage MultiLanguage
	err := db.Model(&MultiLanguage{}).Where("languageKey = ?", languageKey).Where("serviceName = ?", serviceName).Where("subTag = ?", subTag).First(&multiLanguage).Error
	if err != nil {
		return nil, err
	}
	return &multiLanguage, nil
}

// 返回多语言
func (m *MultiLanguage) MultiLanguageByID(db *gorm.DB, id int64) (*MultiLanguage, error) {
	var multiLanguage MultiLanguage
	err := db.Model(&MultiLanguage{}).Where("id = ?", id).First(&multiLanguage).Error
	if err != nil {
		return nil, err
	}
	return &multiLanguage, nil
}

// 返回多语言
func (m *MultiLanguage) MultiLanguageByIDBatch(db *gorm.DB, id []int64) ([]*MultiLanguage, error) {
	var multiLanguage []*MultiLanguage
	err := db.Model(&MultiLanguage{}).Where("id in ?", id).Find(&multiLanguage).Error
	if err != nil {
		return nil, err
	}
	return multiLanguage, nil
}

// QueryMultiLanguage 返回多语言列表
func (m *MultiLanguage) QueryMultiLanguage(db *gorm.DB, req *proto.QueryMultiLanguageTextRequest) ([]*MultiLanguage, int64, error) {
	var multiLanguages []*MultiLanguage
	var totalCount int64

	tx := db.Model(&MultiLanguage{})
	if tx.Error != nil {
		return nil, 0, tx.Error
	}

	if len(req.SubTag) > 0 {
		tx = tx.Where("subTag LIKE ? ", "%"+req.SubTag+"%")
		if tx.Error != nil {
			return nil, 0, tx.Error
		}
	}

	if len(req.ServiceName) > 0 {
		tx = tx.Where("serviceName LIKE ?", "%"+req.ServiceName+"%")
		if tx.Error != nil {
			return nil, 0, tx.Error
		}
	}

	if len(req.LanguageText) > 0 {
		tx = tx.Where("langText LIKE ? ", "%"+req.LanguageText+"%")
		if tx.Error != nil {
			return nil, 0, tx.Error
		}
	}

	if len(req.LanguageKey) > 0 {
		tx = tx.Where("languageKey LIKE ? ", "%"+req.LanguageKey+"%")
		if tx.Error != nil {
			return nil, 0, tx.Error
		}
	}

	// 根据查询时间添加过滤条件
	for {
		times := strings.Split(req.QueryDateRange, ",")
		if len(times) != 2 {
			break
		}

		startTime := times[0]
		endTime := times[1]
		if len(startTime) == 0 || len(endTime) == 0 {
			break
		}

		nstartTime, err := strconv.ParseInt(startTime, 10, 64)
		if err != nil {
			break
		}

		nendtime, err := strconv.ParseInt(endTime, 10, 64)
		if err != nil {
			break
		}

		// 将时间戳转换为 time.Time 对象
		startTimer := time.Unix(nstartTime, 0).UTC() // 转换为 UTC 时间
		endTimer := time.Unix(nendtime, 0).UTC()     // 转换为 UTC 时间
		// 手动加上 8 小时（北京时间为 UTC+8）
		startTimerBeijing := startTimer.Add(8 * time.Hour)
		endTimerBeijing := endTimer.Add(8 * time.Hour)
		logger.Log.Infof("startTimerBeijing: %s, endTimerBeijing: %s", startTimerBeijing.Format("2006-01-02 15:04:05"), endTimerBeijing.Format("2006-01-02 15:04:05"))
		tx = tx.Where("queryAt BETWEEN ? AND ?", startTimerBeijing.Format("2006-01-02 15:04:05"), endTimerBeijing.Format("2006-01-02 15:04:05"))
		break
	}

	// **统计总数**
	if err := tx.Count(&totalCount).Error; err != nil {
		return nil, 0, err
	}

	err := tx.Offset((req.PageNo - 1) * req.PageSize).Limit(req.PageSize).
		Order("id ASC").Find(&multiLanguages).Error
	if err != nil {
		return nil, 0, err
	}

	return multiLanguages, totalCount, nil
}

// FuzzyQueryMultiLanguage 模糊查询返回多语言列表
func (m *MultiLanguage) FuzzyQueryMultiLanguage(db *gorm.DB, req *proto.FuzzyQueryMultilingualTextRequest) ([]*MultiLanguage, error) {
	var multiLanguages []*MultiLanguage
	tx := db.Model(&MultiLanguage{})
	if tx.Error != nil {
		return nil, tx.Error
	}

	if len(req.SubTag) > 0 {
		tx = tx.Where("subTag = ?", req.SubTag)
		if tx.Error != nil {
			return nil, tx.Error
		}
	}

	if len(req.ServiceName) > 0 {
		tx = tx.Where("serviceName = ?", req.ServiceName)
		if tx.Error != nil {
			return nil, tx.Error
		}
	}

	if len(req.LanguageText) > 0 {
		query := fmt.Sprintf(`LOWER(JSON_UNQUOTE(JSON_EXTRACT(langText, '$.%s'))) LIKE LOWER("%s") `, req.Language, `%`+req.LanguageText+`%`)
		tx = tx.Where(query)
		if tx.Error != nil {
			return nil, tx.Error
		}
	}

	err := tx.Offset((req.PageNo - 1) * req.PageSize).Limit(req.PageSize).Order("id ASC").Find(&multiLanguages).Error
	if err != nil {
		return nil, err
	}

	return multiLanguages, nil
}

// UpdateMultiLanguage 更新多语言
func (m *MultiLanguage) UpdateMultiLanguage(db *gorm.DB) error {
	return db.Save(m).Error
}

// UpdateMultiLanguage 更新多语言
func (m *MultiLanguage) UpdateMultiLanguageByFields(db *gorm.DB) error {
	return db.Model(m).Select("languageKey", "serviceName", "langText", "subTag", "description").Updates(m).Error
}

// HardDeleteMultiLanguageBatch 硬删除多语言
func (m *MultiLanguage) HardDeleteMultiLanguageBatch(db *gorm.DB, id []int64) error {
	err := db.Unscoped().Where("id in ?", id).Delete(&MultiLanguage{}).Error
	if err != nil {
		return err
	}
	return nil
}

// HardDeleteMultiLanguage硬删除多语言
func (m *MultiLanguage) HardDeleteMultiLanguage(db *gorm.DB, id int64) error {
	err := db.Unscoped().Where("id = ?", id).Delete(&MultiLanguage{}).Error
	if err != nil {
		return err
	}
	return nil
}

// 批量插入多语言
func (m *MultiLanguage) BatchInsertMultiLanguage(db *gorm.DB, multiLanguages []*MultiLanguage) error {
	err := db.Model(&MultiLanguage{}).CreateInBatches(multiLanguages, 1000).Error
	if err != nil {
		return err
	}
	return nil
}
