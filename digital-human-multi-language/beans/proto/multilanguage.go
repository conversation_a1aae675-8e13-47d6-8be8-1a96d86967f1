package proto

type GetMultiLanguageTextRequest struct {
	LogId        string   `json:"logId" form:"logId" binding:"required"`               // 请求唯一标识
	ServiceName  string   `json:"serviceName" form:"serviceName"  binding:"required"`  // 服务名
	SubTag       string   `json:"subTag" form:"subTag"`                                // 类型标签
	LanguageKeys []string `json:"languageKeys" form:"languageKeys" binding:"required"` // 语言键
	Language     string   `json:"language" form:"language" binding:"required"`         // 语言 "zh" ,"en"
}

type GetMultiLanguageTextResponse struct {
	LogId      string            `json:"logId"`   // 请求的唯一标识
	ErrCode    int               `json:"errCode"` // 错误码
	ErrMessage string            `json:"errMsg"`  // 错误信息
	Lang       map[string]string `json:"lang"`    // 返回的多语言文本
}

type UpsertMultiLanguageTextRequest struct {
	ServiceName string                 `json:"serviceName"` // 服务名
	SubTag      string                 `json:"subTag"`      // 类型标签
	LanguageMap map[string]interface{} `json:"languageMap"` // 语言键
	Description string                 `json:"description"` // 描述
}

type UpsertMultiLanguageTextResponse struct {
	LogId string `json:"logId"` // 请求的唯一标识
	Code  int    `json:"code"`  // 接口返回错误码，正常为0，其他为异常，依据每个接口的定义
	Msg   string `json:"msg"`   // 根据不同接口不同
}

type UpdateMultiLanguageTextRequest struct {
	ID          int64  `json:"id"  binding:"required"`
	LanguageKey string `json:"languageKey"`
	ServiceName string `json:"serviceName"`
	SubTag      string `json:"subTag"`
	LangText    string `json:"langText"`
	Description string `json:"description"` // 描述
}

type UpdateMultiLanguageTextResponse struct {
	LogId string `json:"logId"` // 请求的唯一标识
	Code  int    `json:"code"`  // 接口返回错误码，正常为0，其他为异常，依据每个接口的定义
	Msg   string `json:"msg"`   // 根据不同接口不同
}

type QueryMultiLanguageTextRequest struct {
	ServiceName    string `json:"serviceName" form:"serviceName"`   // 服务名
	SubTag         string `json:"subTag" form:"subTag"`             // 类型标签
	LanguageText   string `json:"languageText" form:"languageText"` // 模糊匹配的文本
	LanguageKey    string `json:"languageKey" form:"languageKey"`   // 模糊匹配的键
	PageNo         int    `json:"pageNum" form:"pageNum" binding:"required"`
	PageSize       int    `json:"pageSize" form:"pageSize" binding:"required"`
	QueryDateRange string `json:"queryDateRange"`
}

// CommPageRsp
// 通用分页返回结构体
type QueryMultiLanguageTextCommPageRsp struct {
	Count int64       `json:"count"`
	List  interface{} `json:"list"`
}

type QueryMultiLanguageTextResponse struct {
	LogId string                            `json:"logId"` // 请求的唯一标识
	Code  int                               `json:"code"`  // 接口返回错误码，正常为0，其他为异常，依据每个接口的定义
	Msg   string                            `json:"msg"`   // 根据不同接口不同
	Data  QueryMultiLanguageTextCommPageRsp `json:"data,omitempty"`
}

// FuzzyQueryMultilingualTextRequest 模糊查询多语言文本请求
// PageNo 和 PageSize 必填字段，通过分页控制sql扫库的数量从而避免慢sql问题
// LogId 必填字段，用于定位问题和日志追踪
type FuzzyQueryMultilingualTextRequest struct {
	LogId        string `json:"logId" form:"logId" binding:"required"`              // 请求唯一标识
	SubTag       string `json:"subTag" form:"subTag" binding:"required"`            // 类型标签
	ServiceName  string `json:"serviceName" form:"serviceName"  binding:"required"` // 服务名
	LanguageText string `json:"languageText" form:"languageText"`                   // 模糊匹配的文本
	Language     string `json:"language" form:"language" binding:"required"`        // 语言 "zh" ,"en"
	PageNo       int    `json:"pageNum" form:"pageNum" binding:"required"`          // 页码
	PageSize     int    `json:"pageSize" form:"pageSize" binding:"required"`        // 每页数量
}

type FuzzyQueryMultilingualTextResponse struct {
	LogId      string            `json:"logId"`   // 请求的唯一标识
	ErrCode    int               `json:"errCode"` // 错误码
	ErrMessage string            `json:"errMsg"`  // 错误信息
	Lang       map[string]string `json:"lang"`    // 返回的多语言文本
}

type DeleteMultiLanguageTextBatchRequest struct {
	IDs []int64 `json:"ids" form:"ids"  binding:"required"` // 请求唯一标识
}

type DeleteMultiLanguageTextBatchResponse struct {
	Code int    `json:"code"` // 接口返回错误码，正常为0，其他为异常，依据每个接口的定义
	Msg  string `json:"msg"`  // 根据不同接口不同
}

type DeleteMultiLanguageTextRequest struct {
	ID int64 `json:"id" form:"id"  binding:"required"` // 请求唯一标识
}

type DeleteMultiLanguageTextResponse struct {
	Code int    `json:"code"` // 接口返回错误码，正常为0，其他为异常，依据每个接口的定义
	Msg  string `json:"msg"`  // 根据不同接口不同
}

type GetWebMultiLanguageTextResponse struct {
	LogId string            `json:"logId"` // 请求的唯一标识
	Lang  map[string]string `json:"lang"`
}
