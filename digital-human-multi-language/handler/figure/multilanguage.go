package figure

import (
	"acg-ai-go-common/gomysql"
	"acg-ai-go-common/goredis"
	"acg-ai-go-common/logger"
	"acg-ai-go-common/utils"
	"context"
	"digital-human-multi-language/beans/model"
	"digital-human-multi-language/beans/proto"
	config "digital-human-multi-language/conf"
	"digital-human-multi-language/handler/handlerUtils"
	"digital-human-multi-language/mysqlclient"
	"encoding/json"
	"fmt"
	"net/http"
	"strings"
	"time"

	"acg-ai-go-common/utils/redisproxy"

	"github.com/gin-gonic/gin"
	"github.com/go-errors/errors"
	"gorm.io/gorm"

	"github.com/redis/go-redis/v9"
	"golang.org/x/sync/semaphore"
)

const (
	MultiLanguageRedisKeyFmt          = "%s:multi_language_text:%s_%s:%s"             // 多语言文本 redis key
	MultiLanguageRedisKeyExpiration   = 7 * 24 * time.Hour                            // redis key 过期时间 7 天
	MultiLanguageErrorFlag            = "MultiLanguageErrorFlag"                      // 多语言错误日志标记
	WebMultiLanguageRedisKeyFmt       = "%s:multi_language_text:web_%s"               // web 多语言文本 redis key  服务环境+:multi_language_text:web_ + 语言类型 例如：dev:multi_language_text:web_en
	WebMultiLanguageServiceName       = "xiling_web"                                  // web 服务名称
	WebMultiLanguageSubTag            = "web"                                         // web 服务子标签
	WebMultiLnaguageCacheUpdateKeyFmt = "%s:multi_language_text:web_cache_update_key" // web 多语言文本缓存更新 key 格式
	WebMultiLnaguageUpdateInterval    = 2 * time.Minute                               // 更新间隔
	WebMultiLnaguageRedisLockTimeout  = 300 * time.Second                             // Redis 锁的超时时间
	_MultiLanguageErrorMsg            = "Service Unavailable, Text error."
)

var (
	mysqlSemaphore *semaphore.Weighted
	cacheTime      = time.Now()
)

// InitMultiLanguage 初始化多语言相关配置
func InitMultiLanguage() {
	// 创建一个信号量，限制并发数
	mysqlSemaphore = semaphore.NewWeighted(config.LocalConfig.MultiLanguageSetting.MysqlConcurrencyNumber)
}

// AddRedisMapValue 添加 redis hash map 的值
// @param key redis hash key
// @param value redis hash map 的值
func AddRedisMapValue(key string, value map[string]string) error {
	redisproxy := redisproxy.GetRedisProxy()
	err := redisproxy.Rdb.HSet(context.Background(), key, value).Err()
	if err != nil {
		return fmt.Errorf("failed to increment count: %w", err)
	}
	return nil
}

// GetRedisMapAll 获取 redis hash map 的所有值
// @param key redis hash key
func GetRedisMapAll(key string) (map[string]string, error) {
	redisproxy := redisproxy.GetRedisProxy()
	// 获取 Redis 哈希表中的所有键值对
	result, err := redisproxy.Rdb.HGetAll(context.Background(), key).Result()
	if err != nil {
		return nil, err
	}
	return result, nil
}

// DelRedisMapKey - 删除 redis hash map 的值
// @param key redis hash key
// @param fields redis hash map 的key值
// @return error
func DelRedisMapKey(key string, fields ...string) error {
	redisproxy := redisproxy.GetRedisProxy()
	// 删除 Redis 哈希表中的指定字段
	err := redisproxy.Rdb.HDel(context.Background(), key, fields...).Err()
	if err != nil {
		return err
	}
	return nil
}

// GetMultiLanguageText - 获取多语言文本接口
func GetMultiLanguageText(c *gin.Context) {
	req := proto.GetMultiLanguageTextRequest{}
	res := proto.GetMultiLanguageTextResponse{
		ErrCode:    0,
		ErrMessage: "",
		Lang:       make(map[string]string),
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Log.Errorf(MultiLanguageErrorFlag+" req error: %+v", err)
		c.JSON(http.StatusOK, &res)
		return
	}

	res.LogId = req.LogId
	logCtx := context.WithValue(context.TODO(), utils.CtxKeyLogID, req.LogId)
	for _, v := range req.LanguageKeys {
		// 查询 redis 缓存
		languageText, err := getMultiLanguageTextRedis(logCtx, req.ServiceName, req.SubTag, v)
		if err != nil {
			// res.ErrCode = 100002
			// res.ErrMessage = v + "-" + req.Language + ":获取多语言缓存失败"
			logger.Log.Errorf(utils.MMark(logCtx)+" "+MultiLanguageErrorFlag+" redis get error: %+v", err)
			continue
		}

		// 判断是否为空
		if len(languageText) > 0 {
			if _MultiLanguageErrorMsg == languageText {
				logger.Log.Warnf(utils.MMark(logCtx)+" "+MultiLanguageErrorFlag+" redis get key: %s", v)
				res.Lang[v] = _MultiLanguageErrorMsg
				continue
			}

			languageTextMap, err := parseMultiLanguageText(logCtx, languageText)
			if err != nil {
				logger.Log.Errorf(utils.MMark(logCtx)+" "+MultiLanguageErrorFlag+" parseMultiLanguageText languageText: %+v error: %+v ", languageText, err)
				continue
			}

			text, ok := languageTextMap[req.Language]
			if !ok {
				logger.Log.Errorf(utils.MMark(logCtx)+" "+MultiLanguageErrorFlag+" parseMultiLanguageText is empty: %+v", languageTextMap)
				continue
			}

			res.Lang[v] = text
			// redis 缓存增加过期时间
			go setKeyExpiration(logCtx, req.ServiceName, req.SubTag, v, MultiLanguageRedisKeyExpiration)
			continue
		}

		// 查询数据库
		languageText, err = getMultiLanguageTextDB(logCtx, v, req.ServiceName, req.SubTag)
		if len(languageText) == 0 || err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+" "+MultiLanguageErrorFlag+" getMultiLanguageTextDB error: %+v languageText: %+v", err, languageText)
			if err == gorm.ErrRecordNotFound {
				// 更新 redis 缓存
				go saveMultiLanguageTextRedis(logCtx, req.ServiceName, req.SubTag, v, _MultiLanguageErrorMsg)
			}
			continue
		}

		languageTextMap, err := parseMultiLanguageText(logCtx, languageText)
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+" "+MultiLanguageErrorFlag+" parseMultiLanguageText languageText: %s error: %+v", languageText, err)
			continue
		}

		text, ok := languageTextMap[req.Language]
		if !ok {
			logger.Log.Errorf(utils.MMark(logCtx)+" "+MultiLanguageErrorFlag+" parseMultiLanguageText is empty: %+v", languageTextMap)
			continue
		}

		res.Lang[v] = text

		// 更新 redis 缓存
		go saveMultiLanguageTextRedis(logCtx, req.ServiceName, req.SubTag, v, languageText)
	}

	// 返回结果
	logger.Log.Infof(utils.MMark(logCtx)+" res: %+v", res)
	c.JSON(http.StatusOK, &res)
}

// getMultiLanguageTextDB - 从数据库获取多语言文本
// @param mulKey 多语言文本的 key
// @param serviceName 服务名
// @param subTag 类型标签
// @return string 多语言文本的值
// @return error
func getMultiLanguageTextDB(logCtx context.Context, mulKey, serviceName, subTag string) (string, error) {
	// 创建带超时的 context，最多等待 15 秒
	ctx, cancel := context.WithTimeout(context.Background(), 15*time.Second)
	defer cancel()
	// 限制并发数
	mysqlSemaphore.Acquire(ctx, 1)
	defer mysqlSemaphore.Release(1)

	mulLang, err := (&model.MultiLanguage{}).MultiLanguage(gomysql.DB, mulKey, serviceName, subTag)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+" MultiLanguage error: %+v", err)
		return "", err
	}

	mulLang.QueryAt = time.Now()
	err = mulLang.UpdateMultiLanguage(gomysql.DB)
	if err != nil {
		// 这里更新一下查询时间，即使报错也不需要返回错误
		logger.Log.Errorf(utils.MMark(logCtx)+" UpdateMultiLanguage error: %+v", err)
	}
	return mulLang.LangText, nil
}

// savetMultiLanguageTextRedisKey - 保存多语言文本到 redis 缓存
// @param mulKey 多语言文本的 key
// @param value 多语言文本的值
// @param expiration 过期时间秒
// @return error
func saveMultiLanguageTextRedis(logCtx context.Context, serviceName, subTag, mulKey, value string) error {
	textRedisKey := fmt.Sprintf(MultiLanguageRedisKeyFmt, handlerUtils.GetNameByRunEnv(), serviceName, subTag, mulKey)
	redisProxy := redisproxy.GetRedisProxy()
	err := redisProxy.SetKeyValue(textRedisKey, value, MultiLanguageRedisKeyExpiration)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+" redis set error: %+v", err)
		return err
	}

	logger.Log.Infof(utils.MMark(logCtx)+" textRedisKey: %s value: %s", textRedisKey, value)
	return nil
}

// deleteMultiLanguageTextRedis - 删除多语言redis 缓存
// @param serviceName 服务名
// @param subTag 类型标签
// @param mulKey 多语言文本的key key
// @return error
func deleteMultiLanguageTextRedis(logCtx context.Context, serviceName, subTag, mulKey string) error {
	textRedisKey := fmt.Sprintf(MultiLanguageRedisKeyFmt, handlerUtils.GetNameByRunEnv(), serviceName, subTag, mulKey)
	redisProxy := redisproxy.GetRedisProxy()
	err := redisProxy.DeleteKey(textRedisKey)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+" redis del error: %+v", err)
		return err
	}

	logger.Log.Infof(utils.MMark(logCtx)+" textRedisKey: %s", textRedisKey)
	return nil
}

// saveWebMultiLanguageTextRedis - 保存多语言文本到 redis 缓存，web 版本
// 存储到 hash map 中
// @param mulKey 多语言文本的 key
// @param langText 多语言文本的值
// @return error
func saveWebMultiLanguageTextRedis(logCtx context.Context, mulKey, langText string) error {
	textMap := make(map[string]string)
	err := json.Unmarshal([]byte(langText), &textMap)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+" json unmarshal error: %+v", err)
		return err
	}

	for language, v := range textMap {
		webMap := make(map[string]string)
		webMap[mulKey] = v

		webRedisKey := fmt.Sprintf(WebMultiLanguageRedisKeyFmt, handlerUtils.GetNameByRunEnv(), language)
		err := AddRedisMapValue(webRedisKey, webMap)
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+" redis set error: %+v", err)
			return err
		}

		logger.Log.Infof(utils.MMark(logCtx)+" textRedisKey: %s value: %v", webRedisKey, webMap)
	}

	return nil
}

// deleteWebMultiLanguageTextRedis - web 删除web 多语言 redis 缓存
// 删除 hash map 中的 key
// @param mulKey 多语言文本的 key
// @param langText 多语言文本的值
// @return error
func deleteWebMultiLanguageTextRedis(logCtx context.Context, mulKey, langText string) error {
	textMap := make(map[string]string)
	err := json.Unmarshal([]byte(langText), &textMap)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+" json unmarshal error: %+v", err)
		return err
	}

	for language, _ := range textMap {
		webRedisKey := fmt.Sprintf(WebMultiLanguageRedisKeyFmt, handlerUtils.GetNameByRunEnv(), language)
		err := DelRedisMapKey(webRedisKey, mulKey)
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+" redis set error: %+v", err)
			return err
		}
		logger.Log.Infof(utils.MMark(logCtx)+" textRedisKey: %s value: %v", webRedisKey, mulKey)
	}

	return nil
}

// saveMultiLanguageRedisAndWeb - 保存多语言文本到 redis 缓存
// 整合后端合前端的缓存
// @param mulItem 多语言文本数据库对象
// @return error
func saveMultiLanguageRedisAndWeb(logCtx context.Context, mulItem *model.MultiLanguage) error {
	// 判断是否为 web 服务
	if mulItem.ServiceName == WebMultiLanguageServiceName {
		err := saveWebMultiLanguageTextRedis(logCtx, mulItem.LanguageKey, mulItem.LangText)
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+" saveWebMultiLanguageTextRedis error: %+v", err)
			return err
		}
		return nil
	}

	err := saveMultiLanguageTextRedis(logCtx, mulItem.ServiceName, mulItem.SubTag, mulItem.LanguageKey, mulItem.LangText)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+" saveMultiLanguageTextRedis error: %+v", err)
		return err
	}
	return nil
}

// deleteMultiLanguageRedisAndWeb - 删除多语言文本 redis 缓存
// 删除后端或者前端的缓存
// @param mulItem 多语言文本数据库对象
// @return error
func deleteMultiLanguageRedisAndWeb(logCtx context.Context, mulItem *model.MultiLanguage) error {
	// 判断是否为 web 服务
	if mulItem.ServiceName == WebMultiLanguageServiceName {
		err := deleteWebMultiLanguageTextRedis(logCtx, mulItem.LanguageKey, mulItem.LangText)
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+" deleteWebMultiLanguageTextRedis error: %+v", err)
			return err
		}
		logger.Log.Infof(utils.MMark(logCtx)+" deleteWebMultiLanguageTextRedis success key: %s", mulItem.LanguageKey)
		return nil
	}

	err := deleteMultiLanguageTextRedis(logCtx, mulItem.ServiceName, mulItem.SubTag, mulItem.LanguageKey)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+" deleteMultiLanguageTextRedis error: %+v", err)
		return err
	}
	logger.Log.Infof(utils.MMark(logCtx)+" deleteMultiLanguageTextRedis success key: %s", mulItem.LanguageKey)
	return nil
}

// getMultiLanguageTextRedisKey - 获取多语言文本 redis 缓存
// @param serviceName 服务名
// @param subTag 类型标签
// @param mulKey 多语言文本的 key
// @return string, error 返回缓存中的值
func getMultiLanguageTextRedis(logCtx context.Context, serviceName, subTag, mulKey string) (string, error) {
	textRedisKey := fmt.Sprintf(MultiLanguageRedisKeyFmt, handlerUtils.GetNameByRunEnv(), serviceName, subTag, mulKey)
	redisProxy := redisproxy.GetRedisProxy()
	value, err := redisProxy.GetKeyValue(textRedisKey)
	if err != nil && string(err.Error()) != string(redis.Nil) {
		logger.Log.Errorf(utils.MMark(logCtx)+" redis get error: %+v", err)
		return "", err
	}
	logger.Log.Infof(utils.MMark(logCtx)+" textRedisKey: %s , value: %s", textRedisKey, value)
	return value, nil
}

// setKeyExpiration - redis Key 设置过期时间
// @param serviceName 服务名
// @param subTag 类型标签
// @param mulKey 多语言文本的 key
// @param expiration 过期时间
// @return error
func setKeyExpiration(logCtx context.Context, serviceName, subTag, mulKey string, expiration time.Duration) error {
	textRedisKey := fmt.Sprintf(MultiLanguageRedisKeyFmt, handlerUtils.GetNameByRunEnv(), serviceName, subTag, mulKey)
	return setKeyExpirationByKey(logCtx, textRedisKey, expiration)
}

// setKeyExpirationByKey - 给已存在的 Key 设置过期时间
// @param key redis 的 key
// @param expiration 过期时间
// @return error
func setKeyExpirationByKey(logCtx context.Context, key string, expiration time.Duration) error {
	// 如果缓存时间在5天内，则不刷新缓存时间
	if time.Now().Before(cacheTime.Add(5 * 24 * time.Hour)) {
		logger.Log.Infof(utils.MMark(logCtx)+" skip set expiration: Key [%s] cacheTime %v", key, cacheTime)
		return nil
	} else if time.Now().Before(cacheTime.Add(6 * 24 * time.Hour)) {
		// 如果缓存时间在6天内，则不刷新缓存时间 但是要更新一下本地缓存过期时间key的值
		if IsCachedInLocalLru(logCtx, key) {
			upsertLocalCacheKey(key, false)
		}

		logger.Log.Infof(utils.MMark(logCtx)+" skip set expiration: Key [%s] cacheTime %v", key, cacheTime)
		return nil
	} else if time.Now().After(cacheTime.Add(7 * 24 * time.Hour)) {
		// 如果缓存时间在8天内，则不刷新缓存时间 但是要更新一下本地缓存过期时间key的值
		cacheTime = time.Now()
		logger.Log.Infof(utils.MMark(logCtx)+" Resetting cache time: cacheTime %v", cacheTime)
		return nil
	}

	// 只有redis key 到第7天才有可能刷新缓存时间
	// 线判断本地缓存是否存在，如果存在，则不设置过期时间
	if ok := IsCachedInLocalLru(logCtx, key); ok {
		logger.Log.Infof(utils.MMark(logCtx)+" Key exists in local cache [%s] cacheTime: %v", key, cacheTime)
		return nil
	}

	redisProxy := redisproxy.GetRedisProxy()
	if redisProxy.Rdb == nil {
		return errors.New("Redis client is not initialized")
	}

	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()
	// 设置 Key 的过期时间
	success, err := redisProxy.Rdb.Expire(ctx, key, expiration).Result()
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+" redis set expiration error: %+v", err)
		return err
	}

	if success {
		upsertLocalCacheKey(key, true)
		logger.Log.Infof(utils.MMark(logCtx)+" redis set expiration success: Key [%s] 过期时间已设置为 %v", key, expiration)
		return nil
	} else {
		logger.Log.Errorf(utils.MMark(logCtx) + " redis set expiration error: Key not exist")
		return nil
	}
}

// UpsertMultiLanguageText - 批量 插入/更新 多语言文本
func UpsertMultiLanguageText(c *gin.Context) {
	logCtx := context.WithValue(context.TODO(), utils.CtxKeyLogID, utils.RandStringRunes(18))
	res := proto.UpsertMultiLanguageTextResponse{
		Code:  0,
		Msg:   "",
		LogId: utils.GetLogID(logCtx),
	}

	req := proto.UpsertMultiLanguageTextRequest{}
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Log.Errorf(" req error: %+v", err)
		res.Code = 100001
		res.Msg = "参数异常"
		c.JSON(http.StatusOK, &res)
		return
	}

	for k, v := range req.LanguageMap {
		text, err := json.Marshal(v)
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+" json marshal error: %+v", err)
			res.Code = 100002
			res.Msg = "解析文本失败"
			c.JSON(http.StatusOK, &res)
			return
		}

		k = convertEscapedChars(k)
		mulText := string(text)

		mulLang, err := (&model.MultiLanguage{}).MultiLanguage(gomysql.DB, k, req.ServiceName, req.SubTag)
		if err != nil {
			logger.Log.Infof(utils.MMark(logCtx)+" MultiLanguage error: %+v", err)
			mulLang = &model.MultiLanguage{
				LanguageKey: k,
				SubTag:      req.SubTag,
				LangText:    mulText,
				ServiceName: req.ServiceName,
				Description: req.Description,
			}
		} else {
			mulLang.LangText = mulText
			mulLang.Description = req.Description
			mulLang.QueryAt = time.Now()
		}

		err = mulLang.UpdateMultiLanguage(gomysql.DB)
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+" InsertOrUpdateMultiLanguage mulLang:%+v error: %+v", mulLang, err)
			res.Code = 100003
			res.Msg = "插入/更新多语言文本失败"
			c.JSON(http.StatusOK, &res)
			return
		}

		err = saveMultiLanguageRedisAndWeb(logCtx, mulLang)
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+" saveMultiLanguageRedisAndWeb error: %+v", err)
			res.Code = 100004
			res.Msg = "更新 redis 缓存失败"
			c.JSON(http.StatusOK, &res)
			return
		}

	}

	c.JSON(http.StatusOK, &res)
}

// UpdateMultiLanguageText - 插入/更新 单条多语言文本
func UpdateMultiLanguageText(c *gin.Context) {
	logCtx := context.WithValue(context.TODO(), utils.CtxKeyLogID, utils.RandStringRunes(18))
	res := proto.UpdateMultiLanguageTextResponse{
		Code:  0,
		Msg:   "",
		LogId: utils.GetLogID(logCtx),
	}

	req := proto.UpdateMultiLanguageTextRequest{}
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Log.Errorf(" req error: %+v", err)
		res.Code = 100001
		res.Msg = "参数异常"
		c.JSON(http.StatusOK, &res)
		return
	}

	if !isValidJSON(req.LangText) {
		res.Code = 100001
		res.Msg = "多语言文本不是合法的 JSON 格式"
		c.JSON(http.StatusOK, &res)
		return
	}

	mul := &model.MultiLanguage{
		ID:          req.ID,
		LanguageKey: convertEscapedChars(req.LanguageKey),
		ServiceName: req.ServiceName,
		SubTag:      req.SubTag,
		LangText:    req.LangText,
		Description: req.Description,
		QueryAt:     time.Now(),
	}

	err := mul.UpdateMultiLanguageByFields(gomysql.DB)
	if err != nil {
		logger.Log.Infof(utils.MMark(logCtx)+" MultiLanguage error: %+v", err)
		res.Code = 100002
		res.Msg = "更新多语言文本失败"
		c.JSON(http.StatusOK, &res)
		return
	}

	err = saveMultiLanguageRedisAndWeb(logCtx, mul)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+" saveMultiLanguageRedisAndWeb error: %+v", err)
		res.Code = 100003
		res.Msg = "更新 redis 缓存失败"
		c.JSON(http.StatusOK, &res)
		return
	}

	c.JSON(http.StatusOK, &res)
}

// parseMultiLanguageText - 解析多语言文本
// 解析多语言文本，返回 key - 多语言文本 map
// @param mulitLangText 多语言文本
// @return map[string]string key - 多语言文本 map
func parseMultiLanguageText(logCtx context.Context, mulitLangText string) (map[string]string, error) {
	mulitLangTextMap := make(map[string]string)

	if err := json.Unmarshal([]byte(mulitLangText), &mulitLangTextMap); err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+" json unmarshal mulitLangText:%s error: %+v", mulitLangText, err)
		return nil, err
	}

	return mulitLangTextMap, nil
}

// QueryMultiLanguageText - 查询多语言文本
// 爱速搭调用接口，返回多语言文本
// 通过多个搜索条件获取多语言文本，返回 key - 多语言文本 map 方便调用的服务通过key获取对应的文本
func QueryMultiLanguageText(c *gin.Context) {
	res := proto.QueryMultiLanguageTextResponse{
		Code:  0,
		Msg:   "",
		Data:  proto.QueryMultiLanguageTextCommPageRsp{},
		LogId: utils.RandStringRunes(18),
	}

	req := proto.QueryMultiLanguageTextRequest{}
	if err := c.ShouldBindQuery(&req); err != nil {
		logger.Log.Errorf(" req error: %+v", err)
		res.Code = 100001
		res.Msg = "参数异常"
		c.JSON(http.StatusOK, &res)
		return
	}
	res.LogId = utils.RandStringRunes(18)
	logCtx := context.WithValue(context.TODO(), utils.CtxKeyLogID, res.LogId)

	list, count, err := (&model.MultiLanguage{}).QueryMultiLanguage(mysqlclient.DbMap[mysqlclient.MetaHumanEditorSaasReadOnleDBName], &req)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+" MultiLanguageList error: %+v", err)
		res.Code = 100002
		res.Msg = "获取多语言文本列表失败"
		c.JSON(http.StatusOK, &res)
		return
	}

	res.Data.Count = count
	res.Data.List = list
	// 返回结果
	logger.Log.Infof(utils.MMark(logCtx)+" res: %+v", res)
	c.JSON(http.StatusOK, &res)
}

// FuzzyQueryMultilingualText - 模糊查询多语言文本
// 模糊查询多语言文本，主要是给内部服务使用，模糊匹配语言文本
// @param FuzzyQueryMultilingualTextRequest @see proto.FuzzyQueryMultilingualTextRequest
func FuzzyQueryMultilingualText(c *gin.Context) {
	res := proto.FuzzyQueryMultilingualTextResponse{
		ErrCode:    0,
		ErrMessage: "",
		Lang:       make(map[string]string),
	}

	req := proto.FuzzyQueryMultilingualTextRequest{}
	if err := c.ShouldBindQuery(&req); err != nil {
		logger.Log.Errorf(MultiLanguageErrorFlag+" req error: %+v", err)
		res.ErrCode = 100001
		res.ErrMessage = "参数异常"
		c.JSON(http.StatusOK, &res)
		return
	}

	logCtx := context.WithValue(context.TODO(), utils.CtxKeyLogID, req.LogId)
	res.LogId = req.LogId

	// 创建带超时的 context，最多等待 15 秒
	ctx, cancel := context.WithTimeout(context.Background(), 15*time.Second)
	defer cancel()
	// 限制并发数
	mysqlSemaphore.Acquire(ctx, 1)
	defer mysqlSemaphore.Release(1)

	list, err := (&model.MultiLanguage{}).FuzzyQueryMultiLanguage(gomysql.DB, &req)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+" "+MultiLanguageErrorFlag+" MultiLanguageList error: %+v", err)
		res.ErrCode = 100002
		res.ErrMessage = "获取多语言文本列表失败"
		c.JSON(http.StatusOK, &res)
		return
	}
	logger.Log.Infof(utils.MMark(logCtx)+" list: %+v", list)

	for _, v := range list {
		vMap := make(map[string]string)
		if err = json.Unmarshal([]byte(v.LangText), &vMap); err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+" "+MultiLanguageErrorFlag+" json unmarshal LangText: %s error: %+v", v.LangText, err)
			continue
		}

		text, ok := vMap[req.Language]
		if !ok {
			logger.Log.Errorf(utils.MMark(logCtx)+" "+MultiLanguageErrorFlag+" language not found: %+v v.LangText: %s", req.Language, v.LangText)
			continue
		}

		res.Lang[v.LanguageKey] = text
	}

	// 返回结果
	logger.Log.Infof(utils.MMark(logCtx)+" res: %+v", res)
	c.JSON(http.StatusOK, &res)
}

// DeleteMultiLanguageTextBatch - 批量删除多语言文本
func DeleteMultiLanguageTextBatch(c *gin.Context) {
	logCtx := context.WithValue(context.TODO(), utils.CtxKeyLogID, utils.RandStringRunes(18))
	req := proto.DeleteMultiLanguageTextBatchRequest{}
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Log.Errorf(MultiLanguageErrorFlag+" req error: %+v", err)
		c.JSON(http.StatusOK, &proto.DeleteMultiLanguageTextBatchResponse{
			Code: 100001,
			Msg:  "参数异常",
		})
		return
	}

	list, err := (&model.MultiLanguage{}).MultiLanguageByIDBatch(gomysql.DB, req.IDs)
	if err != nil {
		logger.Log.Errorf(" MultiLanguageList error: %+v", err)
		c.JSON(http.StatusOK, &proto.DeleteMultiLanguageTextBatchResponse{
			Code: 100002,
			Msg:  "不存在语言文本，无法删除",
		})
		return
	}

	if err := (&model.MultiLanguage{}).HardDeleteMultiLanguageBatch(gomysql.DB, req.IDs); err != nil {
		logger.Log.Errorf(" MultiLanguageList error: %+v", err)
		c.JSON(http.StatusOK, &proto.DeleteMultiLanguageTextBatchResponse{
			Code: 100003,
			Msg:  "删除多语言文本失败",
		})
		return
	}

	for _, mul := range list {
		err = deleteMultiLanguageRedisAndWeb(logCtx, mul)
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+" deleteMultiLanguageRedisAndWeb error: %+v", err)
			c.JSON(http.StatusOK, &proto.DeleteMultiLanguageTextBatchResponse{
				Code: 100004,
				Msg:  "删除 redis 缓存失败",
			})
			return
		}
	}

	c.JSON(http.StatusOK, &proto.DeleteMultiLanguageTextBatchResponse{
		Code: 0,
		Msg:  "success",
	})
}

// DeleteMultiLanguageText - 单条删除多语言文本
func DeleteMultiLanguageText(c *gin.Context) {
	logCtx := context.WithValue(context.TODO(), utils.CtxKeyLogID, utils.RandStringRunes(18))
	req := proto.DeleteMultiLanguageTextRequest{}
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Log.Errorf(MultiLanguageErrorFlag+" req error: %+v", err)
		c.JSON(http.StatusOK, &proto.DeleteMultiLanguageTextResponse{
			Code: 100001,
			Msg:  "参数异常",
		})
		return
	}

	mul, err := (&model.MultiLanguage{}).MultiLanguageByID(gomysql.DB, req.ID)
	if err != nil {
		logger.Log.Errorf(MultiLanguageErrorFlag+" MultiLanguageList error: %+v", err)
		c.JSON(http.StatusOK, &proto.DeleteMultiLanguageTextResponse{
			Code: 100002,
			Msg:  "不存在语言文本，无法删除",
		})
		return
	}

	if err := (&model.MultiLanguage{}).HardDeleteMultiLanguage(gomysql.DB, req.ID); err != nil {
		logger.Log.Errorf(MultiLanguageErrorFlag+" MultiLanguageList error: %+v", err)
		c.JSON(http.StatusOK, &proto.DeleteMultiLanguageTextResponse{
			Code: 100003,
			Msg:  "删除多语言文本失败",
		})
		return
	}

	err = deleteMultiLanguageRedisAndWeb(logCtx, mul)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+" deleteMultiLanguageRedisAndWeb error: %+v", err)
		c.JSON(http.StatusOK, &proto.DeleteMultiLanguageTextBatchResponse{
			Code: 100004,
			Msg:  "删除 redis 缓存失败",
		})
		return
	}

	c.JSON(http.StatusOK, &proto.DeleteMultiLanguageTextResponse{
		Code: 0,
		Msg:  "success",
	})
}

// GetWebMultiLanguageText - 前端调用多语言接口
// 返回多语言文本
func GetWebMultiLanguageText(c *gin.Context) {
	logCtx := context.WithValue(context.TODO(), utils.CtxKeyLogID, utils.RandStringRunes(18))
	res := &proto.GetWebMultiLanguageTextResponse{
		LogId: utils.GetLogID(logCtx),
		Lang:  make(map[string]string),
	}

	language := c.GetHeader("Language")
	if len(language) == 0 {
		logger.Log.Errorf(MultiLanguageErrorFlag + " language is empty")
		c.JSON(http.StatusOK, proto.NewCommDataRsp(100001, "参数异常", res))
		return
	}

	webRedisKey := fmt.Sprintf(WebMultiLanguageRedisKeyFmt, handlerUtils.GetNameByRunEnv(), language)
	mulMap, err := GetRedisMapAll(webRedisKey)
	if err == nil && len(mulMap) > 10 {
		// 缓存命中, 直接返回 这个缓存目前肯定是超过 10 个，防止出现 redis 重启，有人操作爱速搭添加了一个数据，导致缓存数据不准确
		// 这里设置一个缓存个数少于 10 就查询数据库的一个逻辑判断。
		res.Lang = mulMap
		c.JSON(http.StatusOK, proto.NewSuccessRsp(res))
		return
	}

	// 查询数据库
	logger.Log.Warnf(" redis error: %+v", err)
	fuzzyReq := &proto.FuzzyQueryMultilingualTextRequest{
		LogId:       utils.GetLogID(logCtx),
		SubTag:      WebMultiLanguageSubTag,
		ServiceName: WebMultiLanguageServiceName,
		Language:    language,
		PageNo:      1,
		PageSize:    2000,
	}

	list, err := (&model.MultiLanguage{}).FuzzyQueryMultiLanguage(gomysql.DB, fuzzyReq)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+" "+MultiLanguageErrorFlag+" MultiLanguageList error: %+v", err)
		c.JSON(http.StatusOK, proto.NewCommDataRsp(100002, "获取多语言文本列表失败", res))
		return
	}

	for _, v := range list {
		vMap := make(map[string]string)
		if err = json.Unmarshal([]byte(v.LangText), &vMap); err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+" "+MultiLanguageErrorFlag+" json unmarshal LangText: %s error: %+v", v.LangText, err)
			continue
		}

		text, ok := vMap[language]
		if !ok {
			logger.Log.Errorf(utils.MMark(logCtx)+" "+MultiLanguageErrorFlag+" language not found: %+v v.LangText: %s", language, v.LangText)
			continue
		}
		res.Lang[v.LanguageKey] = text
		// 存入redis
		go saveWebMultiLanguageTextRedis(logCtx, v.LanguageKey, v.LangText)
	}

	c.JSON(http.StatusOK, proto.NewSuccessRsp(res))
}

// UpdateWebMultiLanguageTextCache - 定时更新前端多语言缓存
// 因为前端多语言缓存使用的 hash map 结构，无法保证 hash map中的值是最新，所以需要定时更新缓存
// 但是可能会出现脏数据，因为在爱速搭删除时 会同步删除缓存，如果缓存删除失败 会导致缓存中存在脏数据，但是不影响前端使用
func UpdateWebMultiLanguageTextCache() {
	logCtx := context.WithValue(context.TODO(), utils.CtxKeyLogID, utils.RandStringRunes(18))
	// 查询数据库
	fuzzyReq := &proto.FuzzyQueryMultilingualTextRequest{
		SubTag:      WebMultiLanguageSubTag,
		ServiceName: WebMultiLanguageServiceName,
		PageNo:      1,
		PageSize:    2000,
	}

	if ok := checkAndUpdateWebRedisCache(logCtx); !ok {
		logger.Log.Infof("UpdateWebMultiLanguageTextCache no need to update")
		return
	}

	redisproxy := redisproxy.GetRedisProxy()
	lockV2 := goredis.NewRedisLockV2(redisproxy.Rdb, fmt.Sprintf(WebMultiLnaguageCacheUpdateKeyFmt+":lock", handlerUtils.GetNameByRunEnv()), WebMultiLnaguageRedisLockTimeout)
	if ok, err := lockV2.Lock(context.Background()); err != nil || !ok {
		logger.Log.Errorf(" lock error: %+v", err)
		return
	}
	defer lockV2.Unlock(context.Background())

	// 查询数据库
	list, err := (&model.MultiLanguage{}).FuzzyQueryMultiLanguage(mysqlclient.DbMap[mysqlclient.MetaHumanEditorSaasReadOnleDBName], fuzzyReq)
	if err != nil {
		logger.Log.Errorf(" FuzzyQueryMultiLanguage error: %+v", err)
		return
	}
	// 获取数据库中的数据，并检查语种
	webRedisCacheMap := make(map[string]map[string]string)
	webMysqlCacheMap := make(map[string]string)
	// 更新缓存时间
	for _, v := range list {
		vMap := make(map[string]string)
		if err = json.Unmarshal([]byte(v.LangText), &vMap); err != nil {
			logger.Log.Errorf(MultiLanguageErrorFlag+" json unmarshal LangText: %s error: %+v", v.LangText, err)
			continue
		}
		// 记录 mysql 数据
		webMysqlCacheMap[v.LanguageKey] = v.LangText

		// 判断是否需要更新缓存
		isSaveCache := false
		for language, langText := range vMap {
			cacheMap, ok := webRedisCacheMap[language]
			if !ok {
				// 说明没有从缓存中获取缓存数据，现在获取缓存
				// 查询缓存
				webRedisKey := fmt.Sprintf(WebMultiLanguageRedisKeyFmt, handlerUtils.GetNameByRunEnv(), language)
				mulMap, err := GetRedisMapAll(webRedisKey)
				if err == nil {
					webRedisCacheMap[language] = mulMap
					setKeyExpirationByKey(logCtx, webRedisKey, MultiLanguageRedisKeyExpiration)
					if text, ok := mulMap[v.LanguageKey]; ok && text == langText {
						continue
					}
				}
			} else {
				if text, ok := cacheMap[v.LanguageKey]; ok && text == langText {
					continue
				}
			}
			isSaveCache = true
		}

		// 如果不需要更新缓存，则跳过
		if !isSaveCache {
			continue
		}

		// 存入redis
		saveWebMultiLanguageTextRedis(logCtx, v.LanguageKey, v.LangText)
	}

	// 删除 redis 缓存中不存在的 key
	for language, redisTextMap := range webRedisCacheMap {
		for key, _ := range redisTextMap {
			if _, ok := webMysqlCacheMap[key]; !ok {
				// 说明缓存中不存在该 key 或者值不一致，需要更新缓存
				webRedisKey := fmt.Sprintf(WebMultiLanguageRedisKeyFmt, handlerUtils.GetNameByRunEnv(), language)
				err := DelRedisMapKey(webRedisKey, key)
				if err != nil {
					logger.Log.Errorf(utils.MMark(logCtx)+" redis set error: %+v", err)
					continue
				}
				logger.Log.Infof(utils.MMark(logCtx)+" web redis delete textRedisKey: %s languageKey: %v", webRedisKey, key)
			}
		}
	}

	updateWebMultiLanguageTextCacheTime(logCtx)
	logger.Log.Infof("UpdateWebMultiLanguageTextCache success, list len: %d", len(list))
}

// checkAndUpdateWebRedisCache - 检查并更新 web redis 缓存
// @ return bool - 是否需要更新缓存 true: 需要更新 false: 不需要更新
func checkAndUpdateWebRedisCache(logCtx context.Context) bool {
	// 获取当前时间
	now := time.Now()
	redisKey := fmt.Sprintf(WebMultiLnaguageCacheUpdateKeyFmt, handlerUtils.GetNameByRunEnv())
	redisproxy := redisproxy.GetRedisProxy()
	// 获取缓存中的更新时间
	lastUpdateStr, err := redisproxy.GetKeyValue(redisKey)
	if err != nil && string(err.Error()) != string(redis.Nil) {
		logger.Log.Errorf(utils.MMark(logCtx)+" redis GetKeyValue error: %+v", err)
		return false
	}

	// 如果缓存中没有时间，或者时间超过 10 分钟，则尝试更新
	if lastUpdateStr == "" || isTimeExpired(lastUpdateStr, now) {
		return true
	}

	return false
}

// isTimeExpired - 检查时间是否过期
// @ param lastUpdateStr - 缓存中的时间字符串
// @ param now - 当前时间
// @ return bool - 是否过期 true: 已过期 false: 未过期
func isTimeExpired(lastUpdateStr string, now time.Time) bool {
	lastUpdate, err := time.Parse(time.RFC3339, lastUpdateStr)
	if err != nil {
		return true // 如果解析失败，认为时间已过期
	}
	return now.Sub(lastUpdate) > WebMultiLnaguageUpdateInterval
}

// updateWebMultiLanguageTextCacheTime - 更新web 缓存的时间
// 这里设置一个过期时间防止这个缓存不在使用时，缓存时间一直存在，导致缓存占用内存
func updateWebMultiLanguageTextCacheTime(logCtx context.Context) {
	redisKey := fmt.Sprintf(WebMultiLnaguageCacheUpdateKeyFmt, handlerUtils.GetNameByRunEnv())
	redisproxy := redisproxy.GetRedisProxy()
	// 更新缓存时间
	err := redisproxy.SetKeyValue(redisKey, time.Now().Format(time.RFC3339), MultiLanguageRedisKeyExpiration)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+" redis SetKeyValue error: %+v", err)
	}

	logger.Log.Infof("updateWebMultiLanguageTextCacheTime success , key: %s", redisKey)
}

func isValidJSON(s string) bool {
	return json.Valid([]byte(s))
}

// convertEscapedChars 替换常见的转义字符
func convertEscapedChars(input string) string {
	replacer := strings.NewReplacer(
		"\\n", "\n",
		"\\t", "\t",
		"\\r", "\r",
		"\\\"", "\"", // 处理 `\"` 为 `"`, 避免影响 JSON 解析
		"\\\\", "\\", // 处理 `\\` 为 `\`
	)
	return replacer.Replace(input)
}
