package scanner

import (
	"acg-ai-go-common/logger"
	"digital-human-multi-language/handler/figure"
	"digital-human-multi-language/handler/handlerUtils/cron"
)

func Init() error {
	c := cron.GetCron()
	_, err := c.<PERSON><PERSON>("@every 2m", figure.UpdateWebMultiLanguageTextCache)
	if err != nil {
		logger.Log.Fatalf("RegisterCron QueryJobStatusFromDB error: %v\n", err)
		return err
	}
	c.StartCron()

	return nil
}
