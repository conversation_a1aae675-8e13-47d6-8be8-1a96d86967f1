// nolint
package figure

import (
	"acg-ai-go-common/global"
	"acg-ai-go-common/utils"
	"context"
	config "digital-human-multi-language/conf"
	"log"
	"testing"
	"time"

	"github.com/BurntSushi/toml"
	"github.com/stretchr/testify/assert"
)

func TestSetKeyExpirationByKey(t *testing.T) {
	// 初始化公共配置
	if _, err := toml.DecodeFile("/Users/<USER>/Project/baidu/acg-digital-human/digital-human-saas/digital-human-multi-language/deploy/conf/conf-dev.toml", global.ServerSetting); err != nil {
		log.Panicf("env:%v, 配置加载失败: {%v}", global.ServerSetting.RunEnv, err)
	}
	if _, err := toml.DecodeFile("/Users/<USER>/Project/baidu/acg-digital-human/digital-human-saas/digital-human-multi-language/deploy/conf/conf-dev.toml", config.LocalConfig); err != nil {
		log.Panicf("本地个性化配置加载失败: {%v}", err)
	}
	testKey := "dev:multi_language_text:xiling_web1_web1:合规"

	t.Run("should return nil when cacheTime is within 1 day", func(t *testing.T) {
		cacheTime = time.Now()
		ctx := context.WithValue(context.Background(), utils.CtxKeyLogID, "test_log_id")
		err := setKeyExpirationByKey(ctx, testKey, 7*24*time.Hour)
		assert.Nil(t, err)
	})

	t.Run("should update local cache when cacheTime is within 6 day", func(t *testing.T) {
		cacheTime = time.Now().Add(-5.5 * 24 * time.Hour)
		ctx := context.WithValue(context.Background(), utils.CtxKeyLogID, "test_log_id")
		err := setKeyExpirationByKey(ctx, testKey, 7*24*time.Hour)
		assert.Nil(t, err)
	})

	t.Run("should update local cache when cacheTime is within 6.5 days", func(t *testing.T) {
		cacheTime = time.Now().Add(-6.5 * 24 * time.Hour)
		ctx := context.WithValue(context.Background(), utils.CtxKeyLogID, "test_log_id")
		err := setKeyExpirationByKey(ctx, testKey, 7*24*time.Hour)
		assert.Nil(t, err)

		ctx = context.WithValue(context.Background(), utils.CtxKeyLogID, "test_log_id")
		err = setKeyExpirationByKey(ctx, testKey, 7*24*time.Hour)
		assert.Nil(t, err)
	})

	t.Run("should reset cacheTime when after 7 days", func(t *testing.T) {
		cacheTime = time.Now().Add(-7 * 24 * time.Hour)
		ctx := context.WithValue(context.Background(), utils.CtxKeyLogID, "test_log_id")
		err := setKeyExpirationByKey(ctx, testKey, 7*24*time.Hour)
		assert.Nil(t, err)
	})

	t.Run("should return nil when key exists in local cache", func(t *testing.T) {
		cacheTime = time.Now().Add(-8 * 24 * time.Hour) // simulate 7 days
		ctx := context.WithValue(context.Background(), utils.CtxKeyLogID, "test_log_id")
		upsertLocalCacheKey(testKey, true)
		err := setKeyExpirationByKey(ctx, testKey, 7*24*time.Hour)
		assert.Nil(t, err)
	})
}
