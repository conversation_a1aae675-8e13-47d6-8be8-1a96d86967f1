package figure

import (
	"acg-ai-go-common/logger"
	"acg-ai-go-common/utils"
	"context"
	"sync"

	lru "github.com/hashicorp/golang-lru"
)

// LRU 本地缓存
var localCache *ThreadSafeLRU

var localCacheOnce sync.Once

type ThreadSafeLRU struct {
	cache *lru.Cache
	lock  sync.RWMutex
}

func getLocalCache() *ThreadSafeLRU {
	// 单例函数实现
	localCacheOnce.Do(func() {
		localCache = NewThreadSafeLRU(5000)
	})

	return localCache
}

// upsertLocalCacheKey 将指定的 redisKey 添加到本地缓存中，如果已存在则更新为 "-"
func upsertLocalCacheKey(redisKey string, value bool) {
	getLocalCache().Add(redisKey, value)
}

// IsCachedInLocalLru 函数用于检查指定的 redisKey 是否存在于本地 LRU 缓存中。
//
// 参数：
// logCtx context.Context: 日志上下文，用于记录日志。
// redisKey string: 要检查的 redis 键。
//
// 返回值：
// bool: 如果 redisKey 存在于本地 LRU 缓存中，则返回 true；否则返回 false。
func IsCachedInLocalLru(logCtx context.Context, redisKey string) bool {
	// 1. 先查本地 LRU 缓存
	if value, found := getLocalCache().Get(redisKey); found {
		return value.(bool)
	}

	logger.Log.Errorf(utils.MMark(logCtx)+" key: %+v not found in response\n", redisKey)
	return false
}

func NewThreadSafeLRU(size int) *ThreadSafeLRU {
	c, err := lru.New(size)
	if err != nil {
		panic("failed to create LRU cache") // 或者以某种方式处理错误
	}
	return &ThreadSafeLRU{cache: c}
}

func (t *ThreadSafeLRU) Get(key interface{}) (value interface{}, ok bool) {
	t.lock.RLock()
	value, ok = t.cache.Get(key)
	t.lock.RUnlock()
	return
}

func (t *ThreadSafeLRU) Add(key, value interface{}) {
	t.lock.Lock()
	t.cache.Add(key, value)
	t.lock.Unlock()
}
