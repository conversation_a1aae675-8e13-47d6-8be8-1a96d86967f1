package dh_user

import (
	"acg-ai-go-common/logger"
	"acg-ai-go-common/utils/httputil"
	"digital-human-multi-language/beans/proto"
	config "digital-human-multi-language/conf"
	"net/http"

	"github.com/gin-gonic/gin"
)

const (
	DhUserInfoPath           = "/api/internal/workflow/v2/user/auth/info"              // 获取用户信息
	DhUserQuotaFreeze        = "/api/internal/workflow/v1/account/quota/freezeQuota"   // 冻结
	DhUserQuotaUnFreeze      = "/api/internal/workflow/v1/account/quota/unFreezeQuota" // 解冻
	DhUserQuotaConfirmedCost = "/api/internal/workflow/v1/account/quota/confirmedCost" // 确认消耗
)

func DhUserCheck(c *gin.Context) {
	reqHeader := map[string]string{
		"Cookie":         c.<PERSON>("<PERSON><PERSON>"),
		"Host":           <PERSON><PERSON>("Host"),
		"Origin":         <PERSON><PERSON>("Origin"),
		"Referer":        <PERSON><PERSON>("Referer"),
		"weixin-session": <PERSON><PERSON>("weixin-session"),
	}
	rsp, err := GetDhUserInfo(c)
	if err != nil {
		logger.CtxLog(c).Errorf("DhUserCheck GetDhUserInfo fail, header:%v, err:%v", reqHeader, err)
		c.AbortWithStatusJSON(http.StatusOK, proto.NewCommRsp(590001, "服务内部异常"))
		return
	} else if rsp == nil || !rsp.Success {
		c.AbortWithStatusJSON(http.StatusOK, rsp)
		return
	}
	logger.CtxLog(c).Infof("DhUserCheck GetDhUserInfo success, info:%v", rsp.Result)
	c.Set("AccountId", rsp.Result.AccountId)
	c.Set("UserId", rsp.Result.Uid)
	c.Set("UserName", rsp.Result.Username)
	c.Next()
}

func GetDhUserInfo(c *gin.Context) (*DhUserInfoRsp, error) {
	header := map[string]string{
		"Cookie":         c.GetHeader("Cookie"),
		"Host":           c.GetHeader("Host"),
		"Origin":         c.GetHeader("Origin"),
		"Referer":        c.GetHeader("Referer"),
		"weixin-session": c.GetHeader("weixin-session"),
	}
	logger.CtxLog(c).Infof("GetDhUserInfo header:%v", header)
	url := config.LocalConfig.DhUserSetting.BaseUrl + DhUserInfoPath
	var rsp *DhUserInfoRsp
	err := httputil.GetV2(url, header, &rsp)
	return rsp, err
}

// QuotaFreeze 配额冻结
func QuotaFreeze(req *QuotaFreezeReq) (*QuotaFreezeRsp, error) {
	url := config.LocalConfig.DhUserSetting.BaseUrl + DhUserQuotaFreeze
	var rsp *QuotaFreezeRsp
	err := httputil.PostJson(url, req, &rsp)
	return rsp, err
}

// QuotaUnFreeze 配额解冻
func QuotaUnFreeze(req *QuotaUnFreezeReq) (*QuotaUnFreezeRsp, error) {
	url := config.LocalConfig.DhUserSetting.BaseUrl + DhUserQuotaUnFreeze
	var rsp *QuotaUnFreezeRsp
	err := httputil.PostJson(url, req, &rsp)
	return rsp, err
}

// QuotaConfirmedCost 配额确认扣除
func QuotaConfirmedCost(req *QuotaConfirmedCostReq) (*QuotaConfirmedCostRsp, error) {
	url := config.LocalConfig.DhUserSetting.BaseUrl + DhUserQuotaConfirmedCost
	var rsp *QuotaConfirmedCostRsp
	err := httputil.PostJson(url, req, &rsp)
	return rsp, err
}
