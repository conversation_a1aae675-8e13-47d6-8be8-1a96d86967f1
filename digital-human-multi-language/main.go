package main

import (
	"acg-ai-go-common/global"
	"acg-ai-go-common/logger"

	"acg-ai-go-common/server"
	"acg-ai-go-common/utils/mysqlproxy"
	"acg-ai-go-common/utils/redisproxy"
	"digital-human-multi-language/beans/model"
	config "digital-human-multi-language/conf"
	"digital-human-multi-language/handler/figure"
	"digital-human-multi-language/handler/figure/scanner"
	"digital-human-multi-language/mysqlclient"
	"digital-human-multi-language/routers"
	"log"
	"net/http"
	_ "net/http/pprof"

	"github.com/BurntSushi/toml"
)

func main() {
	go func() {
		log.Println(http.ListenAndServe("localhost:6060", nil))
	}()
	// 初始化公共配置
	server.InitGlobalSetting()
	if _, err := toml.DecodeFile(global.ConfFilePath, config.LocalConfig); err != nil {
		log.Panicf("本地个性化配置加载失败: {%v}", err)
	}

	// 初始化日志
	logger.SetLogger()
	logger.Log.Info("本地个性化配置加载成功")
	// 初始化redis
	proxy := redisproxy.GetRedisProxy()
	// 开启携程监听redis是否发生网络中断并进行重连
	go proxy.MonitorRedisConnection()

	// 初始化mysql
	mysqlproxy := mysqlproxy.GetMysqlProxy()
	// 开启携程监听mysql是否发生网络中断并进行重连
	go mysqlproxy.MonitorMysqlConnection()
	// 初始化mysql 因为存在多个数据库这里初始化全局map数据库，在使用时使用map[dbName]即可
	mysqlclient.InitDB(mysqlclient.MetaHumanEditorSaasReadOnleDBName, config.LocalConfig.MetaHumanEditorSaasMysqlSetting)
	// 开启携程监听mysql是否发生网络中断并进行重连
	go mysqlclient.MonitorMysqlConnection()

	// 初始化数据库
	model.InitMysqlDBTable()
	// 初始化路由
	routers.InitRouter()
	// 开启定时器 定时加水印
	scanner.Init()

	// 初始化业务相关变量
	figure.InitMultiLanguage()
	// 启动服务
	server.Run(routers.GinRouter)
}
