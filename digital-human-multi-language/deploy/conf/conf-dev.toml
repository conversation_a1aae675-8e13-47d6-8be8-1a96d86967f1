####################################################### 服务配置-测试环境 #######################################################
server-port = 8080
server-name = "digital-human-multi-language"
log-file-prefix = "localhost"
log-file-path = "./logs/"
log-show-code-file = true
log-show-code-func = true
# consul配置
[consul-setting]
host = "127.0.0.1"
port = 8500
name = ""
# 健康检查地址
health-url = "/health"
check-timeout = "10s"
check-interval = "10s"
deregister-critical-service-after = "30s"

# mysql配置
[mysql-setting]
# host = "mysql57.rdsm9dvaqfvz285.rds.bj.baidubce.com"
host = "127.0.0.1"
port = 3369
database = "meta_human_editor_saas"
username = "saas"
password = "!Digitalhuman@baidu123"
maxOpenConns = 100
maxIdlenConns = 5

# mysql配置
[meta-human-editor-saas-mysql-setting]
# host = "mysql57.rdsm9dvaqfvz285.rds.bj.baidubce.com"
host = "127.0.0.1"
port = 3369
database = "meta_human_editor_saas"
username = "saas"
password = "!Digitalhuman@baidu123"
maxOpenConns = 100
maxIdlenConns = 5

# redis配置
[redis-setting]
# addr = "************:6379"
addr = "127.0.0.1:6777"
username = ""
password = ""
redisEnv = "dev"

# 日志上传的elasticsearch配置
[log-es-setting]
# host = "http://*************:8200"
host = "http://127.0.0.1:9202"
username = ""
password = ""

# 鉴权
[dh-user-setting]
baseUrl = "http://dh-user:80"

[bos-setting]
# apikey
ak = "ALTAKxdVkHMs4sp0Tgkpa3zCJP"
# Secret Key
sk = "e4f46ff5d1bf45c5b81dd867d6e3c148"
endpoint = "bj.bcebos.com"
bucket   = "xiling-dh"
host     = "https://xiling-dh.bj.bcebos.com"
cdn-host = "https://xiling-dh.cdn.bcebos.com"

[multi-language-setting]
mysql-concurrency-number = 1000