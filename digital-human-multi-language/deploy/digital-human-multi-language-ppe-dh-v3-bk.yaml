apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: digital-human
    module: digital-human-multi-language
  name: digital-human-multi-language
  namespace: ppe-dh-v3-bk
spec:
  replicas: 1
  minReadySeconds: 0
  strategy:
    type: RollingUpdate # 策略：滚动更新
    rollingUpdate:
      maxSurge: 25%
      maxUnavailable: 25%
  selector:
    matchLabels:
      app: digital-human
      module: digital-human-multi-language
  template:
    metadata:
      labels:
        app: digital-human
        module: digital-human-multi-language
    spec:
      affinity:
        podAntiAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            - labelSelector:
                matchExpressions:
                  - key: app
                    operator: In
                    values:
                      - digital-human
                  - key: module
                    operator: In
                    values:
                      - digital-human-multi-language
              topologyKey: kubernetes.io/hostname
      restartPolicy: Always
      tolerations:
        - key: "limit"
          operator: "Equal"
          value: "ppe-dh-v3-bk"
          effect: "NoSchedule"
      containers:
        - name: digital-human-multi-language
          image: ccr-246im3mw-pub.cnc.bj.baidubce.com/digitalhuman/digital-human-multi-language:20250318_1742290132734
          imagePullPolicy: Always
          command: [ "sh" ]
          env:
            - name: POD_NAMESPACE
              valueFrom:
                fieldRef:
                  fieldPath: metadata.namespace
          args: [ "/home/<USER>/sbin/start.sh" ]
          # imagePullPolicy: IfNotPresent
          # imagePullPolicy: Never
          ports:
            - containerPort: 8080
          livenessProbe: # 健康检查：存活探针
            httpGet:
              path: /health
              port: 8080
            initialDelaySeconds: 20
            timeoutSeconds: 5
            periodSeconds: 5
          readinessProbe: # 健康检查：就绪探针
            httpGet:
              path: /health
              port: 8080
            initialDelaySeconds: 5
            timeoutSeconds: 1
            periodSeconds: 5
          volumeMounts:
            - mountPath: /home/<USER>/conf/conf.toml
              name: config-volume
              subPath: conf.toml
      volumes:
        - configMap:
            defaultMode: 420
            name: digital-human-multi-language
          name: config-volume
      imagePullSecrets:
        - name: regcred
        - name: regcred-ccr
        - name: regcred-eccr
---
apiVersion: v1
kind: Service
metadata:
  labels:
    app: digital-human
    module: digital-human-multi-language
  name: digital-human-multi-language
  namespace: ppe-dh-v3-bk
spec:
  selector:
    app: digital-human
    module: digital-human-multi-language
  ports:
    - protocol: TCP
      port: 8080
      targetPort: 8080
  type: ClusterIP
---
apiVersion: v1
kind: ConfigMap
metadata:
  labels:
    app: digital-human
    module: digital-human-multi-language
  name: digital-human-multi-language
  namespace: ppe-dh-v3-bk
data:
  conf.toml: |
    ####################################################### 服务配置-生产环境 #######################################################
    server-port = 8080
    server-name = "digital-human-multi-language"
    log-file-prefix = "localhost"
    log-file-path = "./logs/"
    log-show-code-file = true
    log-show-code-func = true
    # consul配置
    [consul-setting]
    host = "127.0.0.1"
    port = 8500
    name = ""

    # 健康检查地址
    health-url = "/health"
    check-timeout = "10s"
    check-interval = "10s"
    deregister-critical-service-after = "30s"

    # mysql配置
    [mysql-setting]
    host = "mysql57-ppe-v3-bk.rdsmdubngebjdwc.rds.bj.baidubce.com"
    port = 3306
    database = "meta_human_editor_saas"
    username = "saas"
    password = "!Digitalhuman@baidu123"
    maxOpenConns = 100
    maxIdlenConns = 5

    # mysql配置
    [meta-human-editor-saas-mysql-setting]
    host = "mysql57-ppe-v3-bk.rdsmdubngebjdwc.rds.bj.baidubce.com"
    port = 3306
    database = "meta_human_editor_saas"
    username = "saas"
    password = "!Digitalhuman@baidu123"
    maxOpenConns = 100
    maxIdlenConns = 5

    # redis配置
    [redis-setting]
    addr = "redis-ppe-v3:6379"
    username = ""
    password = ""
    redisEnv = "dh-v3-ppe-bk"

    # 日志上传的elasticsearch配置
    [log-es-setting]
    host = "http://*************:8200"
    username = "superuser"
    password = "Baidu_dh123"

    [dh-user-setting]
    baseUrl = "http://dh-user:80"

    [bos-setting]
    # apikey
    ak = "ALTAKxdVkHMs4sp0Tgkpa3zCJP"
    # Secret Key
    sk = "e4f46ff5d1bf45c5b81dd867d6e3c148"
    endpoint = "bj.bcebos.com"
    bucket   = "xiling-dh"
    host     = "https://xiling-dh.bj.bcebos.com"
    cdn-host = "https://xiling-dh.cdn.bcebos.com"
    
    [multi-language-setting]
    mysql-concurrency-number = 1000
---
apiVersion: extensions/v1beta1
kind: Ingress
metadata:
  name: digital-human-multi-language-ingress
  namespace: ppe-dh-v3-bk
  annotations:
    nginx.ingress.kubernetes.io/proxy-body-size: 1024m  
    nginx.ingress.kubernetes.io/use-regex: "true"
spec:
  rules:
    - http:
        paths:
          - backend:
              serviceName: digital-human-multi-language
              servicePort: 8080
            path: /api/digitalhuman/multi/language/v1/*          
---