package routers

import (
	"acg-ai-go-common/global"
	"fmt"
	"net/http"

	"github.com/gin-gonic/gin"
)

func HealthRouter(e *gin.Engine) {
	// 健康检查
	url := "/health"
	if global.ServerSetting.ConsulSetting != nil && len(global.ServerSetting.ConsulSetting.HealthUrl) > 0 {
		url = global.ServerSetting.ConsulSetting.HealthUrl
	}
	e.GET(url, healthHandler)
}

// healthHandler 健康检测接口，只要是 200 就认为成功了
func healthHandler(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{
		"message": fmt.Sprintf("hello, %v", global.ServerSetting.ServerName),
	})
}
