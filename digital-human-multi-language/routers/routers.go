package routers

import (
	"digital-human-multi-language/handler/figure"

	"github.com/gin-gonic/gin"
)

// Routers 路由列表
func Routers(e *gin.Engine) {
	api := e.Group("/api/digitalhuman/multi/language/v1")
	{
		// 内部服务请求接口
		api.POST("/figure/text", figure.GetMultiLanguageText)
		api.GET("/figure/text/internal/query", figure.FuzzyQueryMultilingualText)

		// 外部请求接口，不需要配置ingress 通过 alert 服务进行转发即可
		api.POST("/figure/text/update", figure.UpdateMultiLanguageText)
		api.POST("/figure/text/upsert", figure.UpsertMultiLanguageText)
		api.GET("/figure/text/query", figure.QueryMultiLanguageText)
		api.POST("/figure/text/batch/delete", figure.DeleteMultiLanguageTextBatch)
		api.POST("/figure/text/delete", figure.DeleteMultiLanguageText)

		// 前端请求接口 无鉴权，需要配置ingress
		api.GET("figure/text/json", figure.GetWebMultiLanguageText)
	}
}
