package dh_user

import (
	"acg-ai-go-common/logger"
	"acg-ai-go-common/utils"
	"acg-ai-go-common/utils/httputil"
	"bytes"
	"context"
	"dhlive-push-service/beans/enum"
	"dhlive-push-service/beans/proto"
	config "dhlive-push-service/conf"

	"encoding/json"
	"errors"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
)

const (
	DhUserInfoPath           = "/api/internal/workflow/v2/user/auth/info"              // 获取用户信息
	DhUserQuotaFreeze        = "/api/internal/workflow/v1/account/quota/freezeQuota"   // 冻结
	DhUserQuotaUnFreeze      = "/api/internal/workflow/v1/account/quota/unFreezeQuota" // 解冻
	DhUserQuotaConfirmedCost = "/api/internal/workflow/v1/account/quota/confirmedCost" // 确认消耗
	DhUserInfoListPath       = "/api/internal/workflow/v1/account/user/list"           // 配额列表
)

func DhUserCheck(c *gin.Context) {
	reqHeader := map[string]string{
		"Cookie":  c.GetHeader("Cookie"),
		"Host":    c.GetHeader("Host"),
		"Origin":  c.GetHeader("Origin"),
		"Referer": c.GetHeader("Referer"),
	}
	rsp, err := GetDhUserInfo(c)
	if err != nil {
		logger.CtxLog(c).Errorf("DhUserCheck GetDhUserInfo fail, header:%v, err:%v", reqHeader, err)
		c.AbortWithStatusJSON(http.StatusOK, proto.NewCommRsp(590001, "服务内部异常"))
		return
	} else if rsp == nil || !rsp.Success {
		c.AbortWithStatusJSON(http.StatusOK, rsp)
		return
	}
	logger.CtxLog(c).Infof("DhUserCheck GetDhUserInfo success, info:%v", rsp.Result)
	c.Set("AccountId", rsp.Result.AccountId)
	c.Set("UserId", rsp.Result.Uid)
	c.Set("UserName", rsp.Result.Username)
	c.Next()
}

func GetDhUserInfo(c *gin.Context) (*DhUserInfoRsp, error) {
	header := map[string]string{
		"Cookie":  c.GetHeader("Cookie"),
		"Host":    c.GetHeader("Host"),
		"Origin":  c.GetHeader("Origin"),
		"Referer": c.GetHeader("Referer"),
	}
	logger.CtxLog(c).Infof("GetDhUserInfo header:%v", header)
	url := config.LocalConfig.DhUserSetting.BaseUrl + DhUserInfoPath
	var rsp *DhUserInfoRsp
	err := httputil.GetV2(url, header, &rsp)
	return rsp, err
}

// QuotaFreeze 配额冻结
func QuotaFreeze(req *QuotaFreezeReq) (*QuotaFreezeRsp, error) {
	url := config.LocalConfig.DhUserSetting.BaseUrl + DhUserQuotaFreeze
	var rsp *QuotaFreezeRsp
	err := httputil.PostJson(url, req, &rsp)
	return rsp, err
}

// QuotaUnFreeze 配额解冻
func QuotaUnFreeze(req *QuotaUnFreezeReq) (*QuotaUnFreezeRsp, error) {
	url := config.LocalConfig.DhUserSetting.BaseUrl + DhUserQuotaUnFreeze
	var rsp *QuotaUnFreezeRsp
	err := httputil.PostJson(url, req, &rsp)
	return rsp, err
}

// QuotaConfirmedCost 配额确认扣除
func QuotaConfirmedCost(req *QuotaConfirmedCostReq) (*QuotaConfirmedCostRsp, error) {
	url := config.LocalConfig.DhUserSetting.BaseUrl + DhUserQuotaConfirmedCost
	var rsp *QuotaConfirmedCostRsp
	err := httputil.PostJson(url, req, &rsp)
	return rsp, err
}

func GetDhUserInfoList(logCtx context.Context, req *DhUserInfoListReq) (DhUserInfoListRsp, error) {
	url := config.LocalConfig.DhUserSetting.BaseUrl + DhUserInfoListPath
	var rsp DhUserInfoListRsp
	client := httputil.NewRetryHTTPClient(15*time.Second, 3)

	body, err := json.Marshal(req)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"json Marshal error:%v", err)
		return rsp, err
	}

	headers := map[string]string{
		"Content-Type": "application/json",
	}

	res, err := client.DoRequest(logCtx, "POST", url, headers, bytes.NewReader(body))
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"GetDhUserInfoList DoRequest error:%v", err)
		return rsp, err
	}

	logger.Log.Infof(utils.MMark(logCtx)+"GetDhUserInfoList res:%s", string(res))

	err = json.Unmarshal(res, &rsp)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"GetDhUserInfoList Unmarshal error:%v", err)
		return rsp, err
	}

	return rsp, err
}
func GetDhUserOpenID(logCtx context.Context, req *DhUserInfoListReq, pushType enum.PushType) ([]string, error) {
	openid := make([]string, 0)
	dhres, err := GetDhUserInfoList(logCtx, req)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"get dhuser info list error: %v", err)
		return openid, err
	}

	if dhres.Code != 0 {
		logger.Log.Errorf(utils.MMark(logCtx)+"get dhuser info list code is not zero: %v", dhres)
		return openid, errors.New("dhuser info list code is not zero")
	}

	openidtype := "WEIXIN"
	if pushType == enum.BaiduMiniProgram {
		openidtype = "BCE"
	}

	for _, v := range dhres.Result.Result {
		if v.Channel == openidtype {
			if pushType == enum.BaiduMiniProgram && len(v.BdOpenID) != 0 {
				openid = append(openid, v.BdOpenID)
			} else if pushType == enum.WechatMiniProgram {
				openid = append(openid, v.WxOpenID)
			}
		}
	}

	if req.PageNo*req.PageSize < dhres.Result.TotalCount {
		req.PageNo += 1
		tmpopids, err := GetDhUserOpenID(logCtx, req, pushType)
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+"get dhuser openid error: %v", err)
			return openid, err
		} else {
			openid = append(openid, tmpopids...)
		}
	}

	logger.Log.Infof(utils.MMark(logCtx)+"get dhuser req: %v res:%+v", req, dhres)
	return openid, nil
}
