package figure

import (
	"acg-ai-go-common/gomysql"
	"acg-ai-go-common/logger"
	"acg-ai-go-common/utils"
	"acg-ai-go-common/utils/redisproxy"
	"context"
	"dhlive-push-service/beans/enum"
	"dhlive-push-service/beans/model"
	"dhlive-push-service/beans/proto"
	config "dhlive-push-service/conf"
	dh_user "dhlive-push-service/handler/dh-user"
	"dhlive-push-service/handler/handlerUtils"
	"dhlive-push-service/handler/handlerUtils/redislock"
	"dhlive-push-service/handler/handlerUtils/wechatauth"
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"sort"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/go-redis/redis/v8"
)

const (
	WechatMiniProgramKeyPre             = "WechatMiniProgramKey"                  // 用户归属资源集合前缀
	WechatMiniProgramKeyFmt             = "%v:" + WechatMiniProgramKeyPre + ":%v" // 先填写环境ID，在添加唯一ID，组合起来是一个redis key
	WechatMiniProgramLockTimeDuration   = 5 * time.Minute
	WechatMiniProgramStatusTimeDuration = 1 * time.Hour

	WechatTokenUrlFromDHUserFmt = "%s/api/user/v3/open/weixin/access_secret"
)

func CheckSignatureWechat(c *gin.Context) {
	req := proto.PushValidationRequest{
		Signature: c.Query("signature"),
		Echostr:   c.Query("echostr"),
		Timestamp: c.Query("timestamp"),
		Nonce:     c.Query("nonce"),
	}

	// 将参数放入切片中进行排序
	params := []string{config.LocalConfig.WechatMiniProgramSetting.Token, req.Nonce, req.Timestamp}
	sort.Strings(params)

	signature := params[0] + params[1] + params[2]
	sha1signature := handlerUtils.Sha1CalculateSignatures(signature)
	if sha1signature != req.Signature {
		logger.Log.Errorf("check signature failed: req: %v signature:%s sha1signature:%s", req, signature, sha1signature)
		c.JSON(http.StatusOK, gin.H{"error": "signature error"})
		return
	}

	logger.Log.Infof("check signature success: req: %v signature:%s", req, signature)
	c.Next()
}

// PushValidationWechat  推送验证接口
// 将token、timestamp、nonce三个参数进行字典序排序，排序后结果为:["1514711492","1714036504","AAAAA"]。
// 将三个参数字符串拼接成一个字符串："15147114921714036504AAAAA"
// 进行sha1计算签名：f464b24fc39322e44b38aa78f5edd27bd1441696
// 与URL链接中的signature参数进行对比，相等说明请求来自微信服务器，合法。
// 构造回包返回微信，回包消息体内容为URL链接中的echostr参数4375120948345356249。
func PushValidationWechat(c *gin.Context) {
	req := proto.PushValidationRequest{
		Signature: c.Query("signature"),
		Echostr:   c.Query("echostr"),
		Timestamp: c.Query("timestamp"),
		Nonce:     c.Query("nonce"),
	}
	logger.Log.Infof("PushValidation success: req: %v", req)
	c.String(http.StatusOK, req.Echostr)
}

func ExternalPushMessageWechat(c *gin.Context) {
	req := proto.ExternalPushMessageRequest{}
	err := c.ShouldBindJSON(&req)
	if err != nil {
		logger.Log.Errorf("ExternalPushMessage error: %v", err)
		c.JSON(http.StatusOK, gin.H{"error": err.Error()})
		return
	}
	req.ToUserName = config.LocalConfig.WechatMiniProgramSetting.AppID

	if req.Event == enum.SubscribeMsg || req.Event == enum.SubscribeRejectMsg {
		err = subscribeMessageWechat(req)
	} else if req.Event == enum.PushMessageResult {
		err = pushMessageResultWechat(req)
	} else {
		c.JSON(http.StatusOK, gin.H{})
	}

	if err != nil {
		c.JSON(http.StatusOK, gin.H{"error": err.Error()})
		return
	}
	c.JSON(http.StatusOK, gin.H{})
}

func subscribeMessageWechat(req proto.ExternalPushMessageRequest) error {
	logCtx := context.WithValue(context.TODO(), utils.CtxKeyLogID, utils.RandStringRunes(16))
	mini := model.MiniProgramSend{}

	eventdata, err := json.Marshal(req)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"subscribeMessageWechat error: %v,req: %v", err, req)
		return err
	}

	newmini, err := mini.GetMiniProgramSendFromOpenId(gomysql.DB, req.ToUserName,
		req.List.TemplateId, req.FromUserName, string(enum.TaskReady))

	if err == nil {
		newmini.Event = req.Event
		newmini.SubscribeData = string(eventdata)
		newmini.SubscribeStatus = req.List.SubscribeStatusString
		newmini.UpdatedAt = time.Now()
		err = newmini.UpdateMiniProgramSend(gomysql.DB)
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+"subscribeMessageWechat error: %v,newmini: %v", err, newmini)
			return err
		}

		logger.Log.Infof(utils.MMark(logCtx)+"subscribeMessageWechat UpdateMiniProgramSend success: req: %v", req)
		return nil
	}

	mini = model.MiniProgramSend{
		AppID:           req.ToUserName,
		AccountID:       "",
		OpenID:          req.FromUserName,
		TemplateID:      req.List.TemplateId,
		TaskID:          "",
		Type:            enum.WechatMiniProgram,
		LogID:           utils.GetLogID(logCtx),
		MsgType:         req.MsgType,
		Event:           req.Event,
		SubscribeData:   string(eventdata),
		SendData:        "",
		SendResult:      "",
		SubscribeStatus: req.List.SubscribeStatusString,
		Status:          enum.TaskReady,
		Duration:        0,
		CreatedAt:       time.Now(),
		UpdatedAt:       time.Now(),
	}
	err = mini.CreateMiniProgramSend(gomysql.DB)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"subscribeMessageWechat error: %v,mini: %v", err, mini)
		return err
	}

	logger.Log.Infof(utils.MMark(logCtx)+"subscribeMessageWechat CreateMiniProgramSend not found: req: %v", req)
	return nil
}

func pushMessageResultWechat(req proto.ExternalPushMessageRequest) error {
	logCtx := context.WithValue(context.TODO(), utils.CtxKeyLogID, utils.RandStringRunes(16))

	mini := model.MiniProgramSend{}

	sendResult, err := json.Marshal(req)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"pushMessageResultWechat  json.Marshal error: %v, req: %v", err, req)
		return err
	}

	newmini, err := mini.GetMiniProgramSendFromOpenId(gomysql.DB, req.ToUserName, req.List.TemplateId, req.FromUserName, string(enum.TaskReady))
	if err == nil {
		logCtx = context.WithValue(context.TODO(), utils.CtxKeyLogID, newmini.LogID)
		newmini.Event = req.Event
		newmini.SendResult = string(sendResult)
		// newmini.Status = enum.TaskSucceed //只记录结果不修改状态
		newmini.UpdatedAt = time.Now()
		newmini.Duration = int(newmini.UpdatedAt.Sub(newmini.CreatedAt).Milliseconds()/1000 + 1)
		err = newmini.UpdateMiniProgramSend(gomysql.DB)
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+"pushMessageResultWechat UpdateMiniProgramSend error: %v, newmini: %v", err, newmini)
			return err
		}
		return nil
	}

	return nil
}

func InternalPushMessage(c *gin.Context) {
	req := proto.InternalPushMessageRequest{}
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Log.Errorf("InternalPushMessage error: %v", err)
		c.JSON(http.StatusOK, proto.NewCommServiceRsp(100001, err.Error()))
		return
	}
	var err error
	if req.Type == enum.WechatMiniProgram {
		err = InternalPushMessageWechat(req)
	} else if req.Type == enum.BaiduMiniProgram {
		err = InternalPushMessageBaidu(req)
	} else {
		err = errors.New("not support type")
	}
	if err != nil {
		logger.Log.Errorf("InternalPushMessage error: %v", err)
		c.JSON(http.StatusOK, proto.NewCommServiceRsp(100002, err.Error()))
		return
	}
	c.JSON(http.StatusOK, proto.NewSuccessServiceRsp())
}

func InternalPushMessageWechat(req proto.InternalPushMessageRequest) error {
	logCtx := context.WithValue(context.TODO(), utils.CtxKeyLogID, utils.RandStringRunes(16))

	// 获取openid
	openids, err := dh_user.GetDhUserOpenID(logCtx, &dh_user.DhUserInfoListReq{
		PageNo:    1,
		PageSize:  100,
		AccountId: req.AccountID,
	}, enum.WechatMiniProgram)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"InternalPushMessageWechat GetDhUserOpenID error: %v,req: %v", err, req)
		return err
	}

	if len(openids) == 0 {
		logger.Log.Infof(utils.MMark(logCtx)+"InternalPushMessageWechat GetDhUserOpenID failed openid is null .req: %v", req)
		return nil
	}

	// 赋值openid
	for _, v := range openids {
		req.OpenID = v
		insertMiniProgramDBWechat(logCtx, req)
	}

	return nil
}

func insertMiniProgramDBWechat(logCtx context.Context, req proto.InternalPushMessageRequest) error {
	req.Appid = config.LocalConfig.WechatMiniProgramSetting.AppID
	req.TemplateID = config.LocalConfig.WechatMiniProgramSetting.TemplateID

	sendData, err := json.Marshal(req)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"insertMiniProgramDBWechat json.Marshal error: %v,req: %v", err, req)
		return err
	}

	mini := model.MiniProgramSend{
		AppID:           req.Appid,
		AccountID:       req.AccountID,
		OpenID:          req.OpenID,
		TemplateID:      req.TemplateID,
		TaskID:          "twx-" + utils.RandStringRunes(16),
		Type:            enum.WechatMiniProgram,
		LogID:           utils.GetLogID(logCtx),
		MsgType:         "",
		Event:           "",
		SubscribeData:   "",
		SendData:        string(sendData),
		SendResult:      "",
		SubscribeStatus: "",
		Status:          enum.TaskRunning,
		Duration:        0,
		CreatedAt:       time.Now(),
		UpdatedAt:       time.Now(),
	}

	err = mini.CreateMiniProgramSend(gomysql.DB)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"insertMiniProgramDBWechat CreateMiniProgramSend error: %v,mini: %v", err, mini)
		return err
	}
	return nil
}

func TimePushMessageWechat() {
	mini := model.MiniProgramSend{}
	lists, err := mini.GetTasksWithStatusNotSubstatus(gomysql.DB, enum.WechatMiniProgram, enum.TaskRunning)
	if err != nil {
		logger.Log.Errorf("get tasks with status error: %v", err)
		return
	}
	for _, v := range lists {
		PushMessageWechat(v)
	}
}

func PushMessageWechat(mini *model.MiniProgramSend) {
	logCtx := context.WithValue(context.TODO(), utils.CtxKeyLogID, mini.LogID)
	redisProxy := redisproxy.GetRedisProxy()

	// 获取锁
	redisLockKey := fmt.Sprintf(WechatMiniProgramKeyFmt, handlerUtils.GetNameByRunEnv(), mini.TaskID+"_wecaht_lock")
	redisLock := redislock.NewRedisLock(redisProxy.Rdb, redisLockKey, WechatMiniProgramLockTimeDuration)

	ok, err := redisLock.Lock(context.Background())
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"PushMessageWechat Lock key: %s error: %+v\n", redisLockKey, err)
		return
	} else if !ok {
		return
	}

	// 完成任务后释放锁
	defer redisLock.Unlock(context.Background())

	// 查询缓存中的任务状态
	statusRedisKey := fmt.Sprintf(WechatMiniProgramKeyFmt, handlerUtils.GetNameByRunEnv(), mini.TaskID+"_wecaht_status")
	status, err := redisProxy.GetKeyValue(statusRedisKey)
	if err != nil {
		if string(err.Error()) != string(redis.Nil) {
			logger.Log.Errorf(utils.MMark(logCtx)+"PushMessageWechat key %s task status error: %v\n", statusRedisKey, err)
			return
		}
		newmini, err := mini.GetMiniProgramSend(gomysql.DB, mini.ID)
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+"PushMessageWechat key %s GetWechatMiniProgramFromFileId error: %v,mini: %v\n", statusRedisKey, err, mini)
			return
		}
		// 更新 WechatMiniProgram
		mini = newmini

		err = redisProxy.SetKeyValue(statusRedisKey, string(mini.Status), WechatMiniProgramStatusTimeDuration)
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+"PushMessageWechat key %s SetKeyValue error: %v\n", statusRedisKey, err)
			return
		}
		status = string(mini.Status)
		// 任务状态不存在，还继续查询任务状态，最后将结果写入redis，防止其他服务重复查询并写入数据库
		logger.Log.Warnf(utils.MMark(logCtx)+"PushMessageWechat key %s task status is nil set status %s : %v\n", statusRedisKey, status)
	}

	// 任务已经处理过，跳过
	if status == string(enum.TaskSucceed) || status == string(enum.TaskFailed) {
		return
	}
	req := proto.InternalPushMessageRequest{}
	err = json.Unmarshal([]byte(mini.SendData), &req)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"PushMessageWechat req %v unmarshal error: %v\n", req, err)
		return
	}

	if len(req.Lang) == 0 {
		req.Lang = "zh_CN"
	}

	if len(req.MiniProgramState) == 0 {
		req.MiniProgramState = "formal"
	}

	if handlerUtils.GetNameByRunEnv() == "prod" {
		req.MiniProgramState = "formal"
	} else {
		req.MiniProgramState = "trial"
	}

	pushrequset := wechatauth.WechatPushMessageRequset{
		Touser:           req.OpenID,
		TemplateId:       req.TemplateID,
		Page:             req.Page,
		MiniprogramState: req.MiniProgramState,
		Lang:             req.Lang,
		Data:             req.Data,
	}

	auth := wechatauth.GetWechatAuth()
	tokenurl := fmt.Sprintf(WechatTokenUrlFromDHUserFmt, config.LocalConfig.DhUserSetting.BaseUrl)
	token, err := auth.TokenFormDhuser(logCtx, tokenurl)
	//token, err := auth.Token(logCtx, config.LocalConfig.WechatMiniProgramSetting.AppID, config.LocalConfig.WechatMiniProgramSetting.SecretKey)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"PushMessageWechat url: %s get token error: %+v", tokenurl, err)
		mini.Status = enum.TaskFailed
	} else {
		logger.Log.Infof(utils.MMark(logCtx)+"TokenFormDhuser token is: %s", token)

		response, err := auth.SendPushMessage(logCtx, token, pushrequset)
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+"PushMessageWechat pushrequset %v send push message error: %v", pushrequset, err)
			return
		}
		if response.Code == 43101 {
			mini.Status = enum.TaskUnsubscribe
		} else if response.Code != 0 {
			mini.Status = enum.TaskFailed
			logger.Log.Errorf(utils.MMark(logCtx)+"SendPushMessage fialed is openid: %s response: %v ", mini.OpenID, response)
		} else {
			mini.Status = enum.TaskSucceed
		}
	}

	mini.UpdatedAt = time.Now()
	err = mini.UpdateMiniProgramSend(gomysql.DB)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"PushMessageWechat mini %v update task status error: %v", mini, err)
		return
	}
	err = redisProxy.SetKeyValue(statusRedisKey, string(mini.Status), WechatMiniProgramStatusTimeDuration)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"PushMessageWechat key %s SetKeyValue error: %v", statusRedisKey, err)
	}

	logger.Log.Infof(utils.MMark(logCtx)+"SendPushMessage end is openid: %s status: %s", mini.OpenID, string(mini.Status))
}
