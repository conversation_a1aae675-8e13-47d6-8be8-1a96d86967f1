package scanner

import (
	"acg-ai-go-common/logger"
	"dhlive-push-service/handler/figure"
	"dhlive-push-service/handler/handlerUtils/cron"
)

func Init() error {
	c := cron.GetCron()
	_, err := c.<PERSON>Cron("@every 10s", figure.TimePushMessageWechat)
	if err != nil {
		logger.Log.Fatalf("RegisterCron TimePushMessageWechat error: %v\n", err)
		return err
	}
	_, err = c.<PERSON><PERSON>ron("@every 10s", figure.TimePushMessageBaidu)
	if err != nil {
		logger.Log.Fatalf("RegisterCron QueryJobStatusFromDB error: %v\n", err)
		return err
	}
	c.StartCron()

	return nil
}
