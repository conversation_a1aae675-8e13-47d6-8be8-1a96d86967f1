package figure

import (
	"acg-ai-go-common/gomysql"
	"acg-ai-go-common/logger"
	"acg-ai-go-common/utils"
	"acg-ai-go-common/utils/redisproxy"
	"context"
	"dhlive-push-service/beans/enum"
	"dhlive-push-service/beans/model"
	"dhlive-push-service/beans/proto"
	config "dhlive-push-service/conf"
	dh_user "dhlive-push-service/handler/dh-user"
	"dhlive-push-service/handler/handlerUtils"
	"dhlive-push-service/handler/handlerUtils/baiduauth"
	"dhlive-push-service/handler/handlerUtils/redislock"

	"encoding/json"
	"fmt"
	"net/http"
	"sort"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/go-redis/redis/v8"
)

const (
	BaiduMiniProgramKeyPre             = "BaiduMiniProgramKey"                  // 用户归属资源集合前缀
	BaiduMiniProgramKeyFmt             = "%v:" + BaiduMiniProgramKeyPre + ":%v" // 先填写环境ID，在添加唯一ID，组合起来是一个redis key
	BaiduMiniProgramLockTimeDuration   = 5 * time.Minute
	BaiduMiniProgramStatusTimeDuration = 1 * time.Hour

	BaiduTokenUrlFromDHUserFmt = "%s/api/user/v3/open/baidu/access_secret"
)

func CheckSignatureBaidu(c *gin.Context) {
	req := proto.PushValidationRequest{
		Signature: c.Query("signature"),
		Echostr:   c.Query("echostr"),
		Timestamp: c.Query("timestamp"),
		Nonce:     c.Query("nonce"),
	}

	// 将参数放入切片中进行排序
	params := []string{config.LocalConfig.BaiduMiniProgramSetting.Token, req.Nonce, req.Timestamp}
	sort.Strings(params)

	signature := params[0] + params[1] + params[2]
	sha1signature := handlerUtils.Sha1CalculateSignatures(signature)
	if sha1signature != req.Signature {
		logger.Log.Errorf("CheckSignatureBaidu failed: req: %v signature:%s sha1signature:%s", req, signature, sha1signature)
		c.String(http.StatusOK, "success")
		return
	}

	logger.Log.Infof("CheckSignatureBaidu success: req: %v signature:%s", req, signature)
	c.Next()
}

// PushValidationBaidu  推送验证接口
// 将token、timestamp、nonce三个参数进行字典序排序，排序后结果为:["1514711492","1714036504","AAAAA"]。
// 将三个参数字符串拼接成一个字符串："15147114921714036504AAAAA"
// 进行sha1计算签名：f464b24fc39322e44b38aa78f5edd27bd1441696
// 与URL链接中的signature参数进行对比，相等说明请求来自微信服务器，合法。
// 构造回包返回微信，回包消息体内容为URL链接中的echostr参数4375120948345356249。
func PushValidationBaidu(c *gin.Context) {
	req := proto.PushValidationRequest{
		Signature: c.Query("signature"),
		Echostr:   c.Query("echostr"),
		Timestamp: c.Query("timestamp"),
		Nonce:     c.Query("nonce"),
	}
	logger.Log.Infof("PushValidationBaidu success: req: %v", req)
	c.String(http.StatusOK, req.Echostr)
}

func InternalPushMessageBaidu(req proto.InternalPushMessageRequest) error {
	logCtx := context.WithValue(context.TODO(), utils.CtxKeyLogID, utils.RandStringRunes(16))
	// 获取openid
	openids, err := dh_user.GetDhUserOpenID(logCtx, &dh_user.DhUserInfoListReq{
		PageNo:    1,
		PageSize:  100,
		AccountId: req.AccountID,
	}, enum.BaiduMiniProgram)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"InternalPushMessageBaidu GetDhUserOpenID error: %v,req: %v", err, req)
		return err
	}

	if len(openids) == 0 {
		logger.Log.Infof(utils.MMark(logCtx)+"InternalPushMessageBaidu GetDhUserOpenID failed openid is null .req: %v", req)
		return nil
	}

	for _, openid := range openids {
		req.OpenID = openid
		insertMiniProgramDBBaidu(logCtx, req)
	}

	return nil
}

func insertMiniProgramDBBaidu(logCtx context.Context, req proto.InternalPushMessageRequest) error {
	req.Appid = config.LocalConfig.BaiduMiniProgramSetting.AppID
	req.TemplateID = config.LocalConfig.BaiduMiniProgramSetting.TemplateID

	sendData, err := json.Marshal(req)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"insertMiniProgramDBBaidu json.Marsha error: %v,req: %v", err, req)
		return err
	}

	mini := model.MiniProgramSend{
		AppID:           req.Appid,
		AccountID:       req.AccountID,
		OpenID:          req.OpenID,
		TemplateID:      req.TemplateID,
		TaskID:          "tbd-" + utils.RandStringRunes(16),
		Type:            enum.BaiduMiniProgram,
		LogID:           utils.GetLogID(logCtx),
		MsgType:         "",
		Event:           "",
		SubscribeData:   "",
		SendData:        string(sendData),
		SendResult:      "",
		SubscribeStatus: "",
		Status:          enum.TaskRunning,
		Duration:        0,
		CreatedAt:       time.Now(),
		UpdatedAt:       time.Now(),
	}

	err = mini.CreateMiniProgramSend(gomysql.DB)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"insertMiniProgramDBBaidu CreateMiniProgramSend error: %v,mini: %v", err, mini)
		return err
	}
	return nil
}

func TimePushMessageBaidu() {
	mini := model.MiniProgramSend{}
	lists, err := mini.GetTasksWithStatus(gomysql.DB, enum.BaiduMiniProgram, enum.TaskRunning, "")
	if err != nil {
		logger.Log.Errorf("get tasks with status error: %v", err)
		return
	}
	for _, v := range lists {
		PushMessageBaidu(v)
	}
}

func PushMessageBaidu(mini *model.MiniProgramSend) {
	logCtx := context.WithValue(context.TODO(), utils.CtxKeyLogID, mini.LogID)
	redisProxy := redisproxy.GetRedisProxy()

	// 获取锁
	redisLockKey := fmt.Sprintf(BaiduMiniProgramKeyFmt, handlerUtils.GetNameByRunEnv(), mini.TaskID+"_baidu_lock")
	redisLock := redislock.NewRedisLock(redisProxy.Rdb, redisLockKey, BaiduMiniProgramLockTimeDuration)

	ok, err := redisLock.Lock(context.Background())
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"PushMessageBaidu Lock key: %s error: %v", redisLockKey, err)
		return
	} else if !ok {
		return
	}

	// 完成任务后释放锁
	defer redisLock.Unlock(context.Background())

	// 查询缓存中的任务状态
	statusRedisKey := fmt.Sprintf(BaiduMiniProgramKeyFmt, handlerUtils.GetNameByRunEnv(), mini.TaskID+"_baidu_status")
	status, err := redisProxy.GetKeyValue(statusRedisKey)
	if err != nil {
		if string(err.Error()) != string(redis.Nil) {
			logger.Log.Errorf(utils.MMark(logCtx)+"PushMessageBaidu key %s task status error: %v", statusRedisKey, err)
			return
		}
		newmini, err := mini.GetMiniProgramSend(gomysql.DB, mini.ID)
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+"PushMessageBaidu GetBaiduMiniProgramFromFileId error: %v,mini: %v", err, mini)
			return
		}
		// 更新 BaiduMiniProgram
		mini = newmini

		err = redisProxy.SetKeyValue(statusRedisKey, string(mini.Status), BaiduMiniProgramStatusTimeDuration)
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+"PushMessageBaidu key %s SetKeyValue error: %v", statusRedisKey, err)
			return
		}
		status = string(mini.Status)
		// 任务状态不存在，还继续查询任务状态，最后将结果写入redis，防止其他服务重复查询并写入数据库
		logger.Log.Warnf(utils.MMark(logCtx)+"PushMessageBaidu key %s task status is nil set status %s", statusRedisKey, status)
	}

	// 任务已经处理过，跳过
	if status == string(enum.TaskSucceed) || status == string(enum.TaskFailed) {
		return
	}
	req := proto.InternalPushMessageRequest{}
	err = json.Unmarshal([]byte(mini.SendData), &req)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"PushMessageWechat req %v unmarshal error: %+v", req, err)
		return
	}

	auth := baiduauth.GetBaiduAuth()
	tokenurl := fmt.Sprintf(BaiduTokenUrlFromDHUserFmt, config.LocalConfig.DhUserSetting.BaseUrl)
	token, err := auth.TokenFormDhuser(logCtx, tokenurl)
	//token, err := auth.Token(logCtx, config.LocalConfig.BaiduMiniProgramSetting.Appkey, config.LocalConfig.BaiduMiniProgramSetting.SecretKey)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"PushMessageBaidu tokenurl %s get token error: %v", tokenurl, err)
		mini.Status = enum.TaskFailed
	} else {
		logger.Log.Infof(utils.MMark(logCtx)+"PushMessageBaidu get token is: %s openid: %s", token, mini.OpenID)
		reqdata, err := json.Marshal(req.Data)
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+"PushMessageBaidu req %v marshal error: %v", req, err)
			return
		}

		// 如果开发者不想传非必需参数，可以将设置该参数的行注释
		reqParams := baiduauth.SubscribeSendRequest{
			AccessToken:  token,           // 文档中对应字段：access_token，实际使用时请替换成真实参数
			TemplateID:   req.TemplateID,  // 文档中对应字段：template_id，实际使用时请替换成真实参数
			TouserOpenID: req.OpenID,      // 文档中对应字段：touser_openId，实际使用时请替换成真实参数
			SubscribeID:  "xiling",        // 文档中对应字段：subscribe_id，实际使用时请替换成真实参数
			Data:         string(reqdata), // 文档中对应字段：data，实际使用时请替换成真实参数
			Page:         req.Page,        // 文档中对应字段：page，实际使用时请替换成真实参数
		}

		response, err := auth.SendPushMessage(logCtx, reqParams, config.LocalConfig.BaiduMiniProgramSetting.Appkey, config.LocalConfig.BaiduMiniProgramSetting.SecretKey)
		if err != nil {
			mini.Status = enum.TaskFailed
			logger.Log.Errorf(utils.MMark(logCtx)+"PushMessageBaidu key %s send push message error: %v response: %s", statusRedisKey, err, response)
		} else if response.Errno == 4007 || response.ErrMsg == "user not subscribed!" {
			mini.Status = enum.TaskUnsubscribe
		} else if response.Errno != 0 || response.ErrorCode != 0 {
			mini.Status = enum.TaskFailed
			logger.Log.Errorf(utils.MMark(logCtx)+"PushMessageBaidu key %s send push message  response: %s", statusRedisKey, response)
		} else {
			mini.Status = enum.TaskSucceed
		}
	}

	mini.UpdatedAt = time.Now()
	mini.Duration = int(mini.UpdatedAt.Sub(mini.CreatedAt).Milliseconds()/1000 + 1)
	err = mini.UpdateMiniProgramSend(gomysql.DB)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"PushMessageBaidu mini %v update task status error: %v", mini, err)
		return
	}
	err = redisProxy.SetKeyValue(statusRedisKey, string(mini.Status), BaiduMiniProgramStatusTimeDuration)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"PushMessageBaidu key %s SetKeyValue error: %v", statusRedisKey, err)
	}

	logger.Log.Infof(utils.MMark(logCtx)+"SendPushMessage end is openid: %s status: %s", mini.OpenID, string(mini.Status))
}
