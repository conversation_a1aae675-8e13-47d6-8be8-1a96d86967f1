package cron

import (
	"github.com/robfig/cron/v3"
)

type Cron struct {
	c *cron.Cron
}

var cr *Cron

func GetCron() *Cron {
	if cr == nil {
		c := cron.New()
		cr = &Cron{
			c: c,
		}
	}
	return cr
}

func (p *Cron) RegisterCron(spec string, cmd func()) (cron.EntryID, error) {
	return p.c.AddFunc(spec, cmd)
}

func (p *Cron) RegisterJob(spec string, cmd cron.Job) (cron.EntryID, error) {
	return p.c.AddJob(spec, cmd)
}

func (p *Cron) StartCron() {
	p.c.Start()
}

func (p *Cron) StopCron() {
	p.c.Stop()
}
