package wechatauth

import (
	"acg-ai-go-common/logger"
	"acg-ai-go-common/utils"
	"bytes"
	"context"
	"dhlive-push-service/handler/handlerUtils/retryhttpclient"
	"encoding/json"
	"fmt"
	"sync"
	"time"
)

const (
	WechatTokenUrl = "https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=%s&secret=%s"
	WechatPushUrl  = "https://api.weixin.qq.com/cgi-bin/message/subscribe/send?access_token=%s"
)

type WechatAuth struct {
	accessToken string
	expiresIn   int64 // 单位是秒
}
type WechatAuthResponse struct {
	Code    int                      `json:"code"`
	Success bool                     `json:"success"`
	Result  WechatAuthResponseResult `json:"result"`
}
type WechatAuthResponseResult struct {
	AccessToken string `json:"access_token"`
	ExpiresIn   int64  `json:"expires_in"`
}

type WechatPushMessageRequset struct {
	Touser           string                 `json:"touser"` // 用户openid
	TemplateId       string                 `json:"template_id"`
	Page             string                 `json:"page"`
	MiniprogramState string                 `json:"miniprogram_state"`
	Lang             string                 `json:"lang"`
	Data             map[string]interface{} `json:"data"`
}

type WechatPushMessageResponse struct {
	Code int    `json:"errcode"`
	Msg  string `json:"errmsg"`
}

var (
	// 实例化一个VideoWatermark
	wechatAuthInstance *WechatAuth
	// once 用于确保实例化操作只执行一次
	oncewechatAuth sync.Once
)

func GetWechatAuth() *WechatAuth {
	oncewechatAuth.Do(func() {
		wechatAuthInstance = newWechatAuth()
	})
	return wechatAuthInstance
}

func newWechatAuth() *WechatAuth {
	return &WechatAuth{}
}

func (w *WechatAuth) Token(logId context.Context, appid, secret string) (string, error) {
	if w.expiresIn <= time.Now().Unix() {
		w.frushToken(logId, appid, secret)
	}
	return w.accessToken, nil
}

func (w *WechatAuth) frushToken(logCtx context.Context, appid, secret string) (string, error) {
	client := retryhttpclient.NewRetryHTTPClient(15*time.Second, 3)
	res, err := client.DoRequest(logCtx, "GET", fmt.Sprintf(WechatTokenUrl, appid, secret), nil, nil)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx), "wechat auth token error:%v", err)
		return "", err
	}

	var WechatAuthResponseResult = &WechatAuthResponseResult{}
	err = json.Unmarshal(res, &WechatAuthResponseResult)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx), "json Unmarshal error:%v", err)
		return "", err
	}

	w.accessToken = WechatAuthResponseResult.AccessToken
	w.expiresIn = WechatAuthResponseResult.ExpiresIn + time.Now().Unix()
	return w.accessToken, nil
}

func (w *WechatAuth) TokenFormDhuser(logCtx context.Context, url string) (string, error) {
	if w.expiresIn <= time.Now().UnixNano()-int64(10*time.Second) {
		return w.frushTokenFormDhuser(logCtx, url)
	}
	return w.accessToken, nil
}

func (w *WechatAuth) frushTokenFormDhuser(logCtx context.Context, url string) (string, error) {
	client := retryhttpclient.NewRetryHTTPClient(15*time.Second, 3)
	res, err := client.DoRequest(logCtx, "GET", url, nil, nil)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx), "DoRequest error:%v", err)
		return "", err
	}

	var result = &WechatAuthResponse{}
	err = json.Unmarshal(res, &result)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx), "json Unmarshal error:%v", err)
		return "", err
	}

	if !result.Success {
		logger.Log.Errorf(utils.MMark(logCtx), "get token error:%v", result)
		return "", fmt.Errorf("wechat auth token error:%v", result)
	}

	w.accessToken = result.Result.AccessToken
	w.expiresIn = result.Result.ExpiresIn

	logger.Log.Infof(utils.MMark(logCtx), "get token:%v", w)
	return w.accessToken, nil
}

func (w *WechatAuth) SendPushMessage(logCtx context.Context, token string, message WechatPushMessageRequset) (WechatPushMessageResponse, error) {
	response := WechatPushMessageResponse{}
	client := retryhttpclient.NewRetryHTTPClient(15*time.Second, 3)

	body, err := json.Marshal(message)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx), "json Marshal error:%v", err)
		return response, err
	}

	res, err := client.DoRequest(logCtx, "POST", fmt.Sprintf(WechatPushUrl, token), nil, bytes.NewReader(body))
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx), "get push result error:%v", err)
		return response, err
	}

	err = json.Unmarshal(res, &response)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx), "json Unmarshal error:%v", err)
		return response, err
	}

	return response, nil
}
