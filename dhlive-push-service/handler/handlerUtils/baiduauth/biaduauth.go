package baiduauth

import (
	"acg-ai-go-common/logger"
	"acg-ai-go-common/utils"
	"context"
	"dhlive-push-service/handler/handlerUtils/retryhttpclient"
	"encoding/json"
	"fmt"
	"sync"
	"time"
)

const (
	BaiduTokenUrl = "https://openapi.baidu.com/oauth/2.0/token?grant_type=client_credentials&client_id=%s&client_secret=%s&scope=smartapp_snsapi_base"
	BaiduPushUrl  = "https://openapi.baidu.com/rest/2.0/smartapp/template/message/subscribe/send?access_token=%s"
)

type BaiduAuth struct {
	accessToken string
	expiresIn   int64 // 单位是秒
}

type BaiduAuthResponse struct {
	Code    int                     `json:"code"`
	Success bool                    `json:"success"`
	Result  BaiduAuthResponseResult `json:"result"`
}

type BaiduAuthResponseResult struct {
	AccessToken      string `json:"access_token"`
	ExpiresIn        int64  `json:"expires_in"`
	Error            string `json:"error"`
	ErrorDescription string `json:"error_description"`
}

type BaiduPushMessageRequset struct {
	Touser      string                 `json:"touser_openId"` // 用户openid
	TemplateId  string                 `json:"template_id"`
	SubscribeId string                 `json:"subscribe_id"`
	Page        string                 `json:"page"`
	Data        map[string]interface{} `json:"data"`
}

type BaiduPushMessageResponse struct {
	Code int         `json:"errno"`
	Msg  string      `json:"msg"`
	Data interface{} `json:"data"`
}

var (
	// 实例化一个VideoWatermark
	BaiduAuthInstance *BaiduAuth
	// once 用于确保实例化操作只执行一次
	onceBaiduAuth sync.Once
)

func GetBaiduAuth() *BaiduAuth {
	onceBaiduAuth.Do(func() {
		BaiduAuthInstance = newBaiduAuth()
	})
	return BaiduAuthInstance
}

func newBaiduAuth() *BaiduAuth {
	return &BaiduAuth{}
}

func (w *BaiduAuth) Token(logId context.Context, appid, secret string) (string, error) {
	if w.expiresIn <= time.Now().Unix() {
		return w.frushToken(logId, appid, secret)
	}
	return w.accessToken, nil
}

func (w *BaiduAuth) frushToken(logCtx context.Context, appid, secret string) (string, error) {
	client := retryhttpclient.NewRetryHTTPClient(15*time.Second, 3)
	res, err := client.DoRequest(logCtx, "GET", fmt.Sprintf(BaiduTokenUrl, appid, secret), nil, nil)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"Baidu auth token error:%v", err)
		return "", err
	}

	var BaiduAuthResponseResult = &BaiduAuthResponseResult{}
	err = json.Unmarshal(res, &BaiduAuthResponseResult)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"json Unmarshal error:%v", err)
		return "", err
	}

	if BaiduAuthResponseResult.Error != "" {
		logger.Log.Errorf(utils.MMark(logCtx)+"get token error:%v", BaiduAuthResponseResult)
		return "", fmt.Errorf("baidu auth token error:%s", BaiduAuthResponseResult.ErrorDescription)
	}

	w.accessToken = BaiduAuthResponseResult.AccessToken
	w.expiresIn = BaiduAuthResponseResult.ExpiresIn + time.Now().Unix()
	return w.accessToken, nil
}

func (w *BaiduAuth) TokenFormDhuser(logCtx context.Context, url string) (string, error) {
	if w.expiresIn <= time.Now().UnixNano() {
		return w.frushTokenFormDhuser(logCtx, url)
	}
	return w.accessToken, nil
}

func (w *BaiduAuth) frushTokenFormDhuser(logCtx context.Context, url string) (string, error) {
	client := retryhttpclient.NewRetryHTTPClient(15*time.Second, 3)
	res, err := client.DoRequest(logCtx, "GET", url, nil, nil)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"DoRequest error:%v", err)
		return "", err
	}

	var result = &BaiduAuthResponse{}
	err = json.Unmarshal(res, &result)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"json Unmarshal error:%v", err)
		return "", err
	}

	if !result.Success {
		logger.Log.Errorf(utils.MMark(logCtx)+"get token error:%v", result)
		return "", fmt.Errorf("baidu auth token error:%v", result)
	}

	w.accessToken = result.Result.AccessToken
	w.expiresIn = result.Result.ExpiresIn + time.Now().Unix()
	return w.accessToken, nil
}

func (w *BaiduAuth) SendPushMessage(logCtx context.Context, message SubscribeSendRequest, appkey, secret string) (SubscribeSendResponse, error) {
	var err error
	response := SubscribeSendResponse{}
	for i := 0; i < 3; i++ {
		response, err = SubscribeSend(message)
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+"SendPushMessage response %s ,error:%v", response, err)
			time.Sleep(time.Second * 2)
			continue
		}

		if response.ErrMsg == "user not subscribed!" || response.Errno == 4007 {
			logger.Log.Infof(utils.MMark(logCtx)+"SendPushMessage response %s", response)
			return response, nil
		} else if response.Errno != 0 || response.ErrorCode != 0 {
			logger.Log.Errorf(utils.MMark(logCtx)+"SendPushMessage response:%v", response)
			time.Sleep(time.Second * 2)
			continue
		}

		return response, nil
	}

	if err != nil {
		return SubscribeSendResponse{}, err
	}

	return response, nil
}
