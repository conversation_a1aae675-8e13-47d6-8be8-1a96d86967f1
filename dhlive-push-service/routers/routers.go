package routers

import (
	"dhlive-push-service/handler/figure"

	"github.com/gin-gonic/gin"
)

// Routers 路由列表
func Routers(e *gin.Engine) {
	miniProgram := e.Group("/xiling/push/service/mini-program/v1")
	{
		miniProgram.GET("/message/send", figure.CheckSignatureWechat, figure.PushValidationWechat)
		miniProgram.POST("/message/send", figure.CheckSignatureWechat, figure.ExternalPushMessageWechat)

		miniProgram.POST("/baidu/message/send", figure.CheckSignatureBaidu, figure.PushValidationBaidu)
	}

	pushService := e.Group("/api/digitalhuman/push/service/v1/")
	{
		pushService.POST("/message/send", figure.InternalPushMessage)
	}
}
