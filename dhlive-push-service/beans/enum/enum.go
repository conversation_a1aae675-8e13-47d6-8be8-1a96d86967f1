package enum

type TaskStatus string // TaskStatus READY/RUNNING/STOP/SUCCEED/FAILED

const (
	TaskReady       TaskStatus = "READY"
	TaskRunning     TaskStatus = "RUNNING"
	TaskStop        TaskStatus = "STOP"
	TaskSucceed     TaskStatus = "SUCCEED"
	TaskFailed      TaskStatus = "FAILED"
	TaskUnsubscribe TaskStatus = "UNSUBSCRIBE"
)

type SubscribeStatus string

const (
	SubscribeAccept SubscribeStatus = "accept"
	SubscribeReject SubscribeStatus = "reject"
)

type MiniProgramEventType string

const (
	SubscribeMsg       MiniProgramEventType = "subscribe_msg_popup_event"
	SubscribeRejectMsg MiniProgramEventType = "subscribe_msg_change_event"
	PushMessageResult  MiniProgramEventType = "subscribe_msg_sent_event"
)

type PushType string

const (
	WechatMiniProgram PushType = "WECHAT_MINIPROGRAM"
	BaiduMiniProgram  PushType = "BAIDU_MINIPROGRAM"
)
