package proto

import "dhlive-push-service/beans/enum"

type PushValidationRequest struct {
	Signature string `json:"signature"`
	Echostr   string `json:"echostr"`
	Timestamp string `json:"timestamp"`
	Nonce     string `json:"nonce"`
}

type ExternalPushMessageRequest struct {
	ToUserName   string                    `json:"ToUserName"`
	FromUserName string                    `json:"FromUserName"`
	CreateTime   int64                     `json:"CreateTime"`
	MsgType      string                    `json:"MsgType"`
	Event        enum.MiniProgramEventType `json:"Event"`
	List         ExternalPushMessageList   `json:"List"`
}

type ExternalPushMessageList struct {
	TemplateId            string               `json:"TemplateId"`
	MsgID                 string               `json:"MsgID"`
	ErrorCode             string               `json:"ErrorCode"`
	ErrorStatus           string               `json:"ErrorStatus"`
	SubscribeStatusString enum.SubscribeStatus `json:"SubscribeStatusString"`
	PopupScene            string               `json:"PopupScene"`
}

type InternalPushMessageRequest struct {
	Type             enum.PushType          `json:"type"`             // 类型，例如 "wechat" 或 "baidu"
	Appid            string                 `json:"appId"`            // 所需下发的应用id
	TemplateID       string                 `json:"templateId"`       // 所需下发的订阅模板id
	Page             string                 `json:"page"`             // 点击模板卡片后的跳转页面，可选
	OpenID           string                 `json:"openId"`           // 接收者（用户）的 openid
	AccountID        string                 `json:"accountId"`        // bce accountid
	Data             map[string]interface{} `json:"data"`             // 模板内容，JSON格式的字符串
	MiniProgramState string                 `json:"miniprogramState"` // 小程序类型，可选
	Lang             string                 `json:"lang"`             // 进入小程序查看的语言类型，可选
}
