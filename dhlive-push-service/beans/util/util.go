package util

import (
	"acg-ai-go-common/utils"
	"bytes"
	"crypto/aes"
	"crypto/hmac"
	"crypto/sha1"
	"encoding/base64"
	"encoding/csv"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"math/rand"
	"net/url"
	"strconv"
	"strings"
	"time"
)

func Struct2JsonStr(obj interface{}) string {
	jsonBytes, err := json.Marshal(obj)
	if err != nil {
		return "{}"
	}
	return string(jsonBytes)
}

func URLAddQueryParam(baseURL string, params map[string]string) (string, error) {
	u, err := url.Parse(baseURL)
	if err != nil {
		return "", err
	}

	q := u.Query()
	for k, v := range params {
		q.Set(k, v)
	}
	u.RawQuery = q.Encode()

	return u.String(), nil
}

func GenerateActAndPwd() (string, string) {
	return utils.RandStringRunes(7), utils.RandStringRunes(16)
}

func GenerateCsvBytes(tableHeads []string, data [][]string) (*bytes.Buffer, error) {
	//内容先写入buffer缓存
	buff := new(bytes.Buffer)
	//写入UTF-8 BOM,此处如果不写入就会导致写入的汉字乱码
	buff.WriteString("\xEF\xBB\xBF")
	csvWriter := csv.NewWriter(buff)
	// 写入表头
	if err := csvWriter.Write(tableHeads); err != nil {
		return nil, err
	}
	// 写入数据
	for _, d := range data {
		if err := csvWriter.Write(d); err != nil {
			return nil, err
		}
	}
	csvWriter.Flush()
	return buff, nil
}

// GetNMonthAgoTime 获取startTime N个月前的时间
func GetNMonthAgoTime(startTime time.Time, n int) time.Time {
	return startTime.AddDate(0, -n, 0)
}

func AesKeyGenerate(str string) []byte {
	str += "0000000000000000"
	return []byte(str[:16])
}

func AESEcbDecrypt(data, key []byte) (string, error) {
	data, err := base64.StdEncoding.DecodeString(string(data))
	if err != nil {
		return "", err
	}
	block, err := aes.NewCipher(key)
	if err != nil {
		return "", err
	}
	decrypted := make([]byte, len(data))
	size := block.BlockSize()

	for bs, be := 0, size; bs < len(data); bs, be = bs+size, be+size {
		block.Decrypt(decrypted[bs:be], data[bs:be])
	}

	return string(PKCS7UnPadding(decrypted)), nil
}

func AESEcbEncrypt(data, key []byte) (string, error) {
	block, err := aes.NewCipher(key)
	if err != nil {
		return "", err
	}
	data = PKCS7Padding(data, block.BlockSize())
	decrypted := make([]byte, len(data))
	size := block.BlockSize()

	for bs, be := 0, size; bs < len(data); bs, be = bs+size, be+size {
		block.Encrypt(decrypted[bs:be], data[bs:be])
	}

	return base64.StdEncoding.EncodeToString(decrypted), nil
}

func PKCS7Padding(ciphertext []byte, blocksize int) []byte {
	padding := blocksize - len(ciphertext)%blocksize
	if padding < 0 {
		return []byte("")
	}
	padtext := bytes.Repeat([]byte{byte(padding)}, padding)
	return append(ciphertext, padtext...)
}

func PKCS7UnPadding(origData []byte) []byte {
	length := len(origData)
	unpadding := int(origData[length-1])
	if length-unpadding < 0 {
		return []byte("")
	}
	return origData[:(length - unpadding)]
}

func ConstructBRTCToken(version, appId, appKey, roomName string, uid, expectTs int64) string {
	t := time.Now()
	tSecond := t.Unix()
	ts := complement(strconv.FormatInt(tSecond, 10), 10)
	expectTsStr := complement(strconv.FormatInt(expectTs, 10), 10)
	random := rand.New(rand.NewSource(time.Now().UnixNano()))
	randomString := fmt.Sprintf("%08x", random.Int31())

	data := fmt.Sprintf("ACS%s%s%s%s%d%s", appId, ts, randomString, roomName, uid, expectTsStr)

	hash := hmac.New(sha1.New, []byte(appKey))
	hash.Write([]byte(data))
	signature := hex.EncodeToString(hash.Sum(nil))

	return version + signature + ts + randomString + expectTsStr
}

func complement(s string, length int) string {
	if len(s) >= length {
		return s
	}
	return strings.Repeat("0", length-len(s)) + s
}
