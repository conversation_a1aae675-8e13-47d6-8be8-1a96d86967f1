package model

import (
	"dhlive-push-service/beans/enum"
	"time"

	"gorm.io/gorm"
)

type MiniProgramSend struct {
	ID              int64                     `json:"id" gorm:"column:id;primaryKey;autoIncrement"`                            // 数据库ID，通常为主键自增
	AppID           string                    `json:"appId" gorm:"column:appId;not null;type:varchar(50)"`                     // 小程序appid，字符串类型，用于唯一标识一个小程序
	AccountID       string                    `json:"accountId" gorm:"column:accountId;not null;type:varchar(50)"`             // 账号id，字符串类型，用于唯一标识一个账号
	OpenID          string                    `json:"openId" gorm:"column:openId;not null;type:varchar(50)"`                   // 用户id，字符串类型，用于唯一标识一个用户
	TemplateID      string                    `json:"templateId" gorm:"column:templateId;not null;type:varchar(100)"`          // 模版id，字符串类型，用于标识使用的消息模板
	TaskID          string                    `json:"taskId" gorm:"column:taskId;type:varchar(50)"`                            // 任务id，字符串类型，用于唯一标识一个推送任务
	Type            enum.PushType             `json:"type" gorm:"column:type;not null;type:varchar(50)"`                       // 业务类型 微信小程序、百度小程序
	LogID           string                    `json:"logId" gorm:"column:logId;not null;type:varchar(50)"`                     // logId，字符串类型，查询日志时使用
	MsgType         string                    `json:"msgType" gorm:"column:msgType;not null;type:varchar(50)"`                 // 消息类型，字符串类型，例如："event" 表示事件类型
	Event           enum.MiniProgramEventType `json:"event" gorm:"column:event;not null;type:varchar(50)"`                     // 事件类型，字符串类型，例如："subscribe_msg_sent_event"
	SubscribeData   string                    `json:"eventData" gorm:"column:eventData;not null;type:varchar(1024)"`           // 订阅内容，JSON格式的字符串
	SendData        string                    `json:"sendData" gorm:"column:sendData;not null;type:varchar(1024)"`             // 推送的内容，JSON格式的字符串
	SendResult      string                    `json:"sendResult" gorm:"column:sendResult;not null;type:varchar(1024)"`         // 推送结果，JSON格式的字符串
	SubscribeStatus enum.SubscribeStatus      `json:"subscribeStatus" gorm:"column:subscribeStatus;not null;type:varchar(50)"` // 订阅状态，字符串类型，如："ACCEPT"、"REJECT"
	Status          enum.TaskStatus           `json:"status" gorm:"column:status;not null;type:varchar(50)"`                   // 推送状态，字符串类型，如："READY"、"RUNNING"、"SUCCEED"、"FAILED"
	Duration        int                       `json:"duration" gorm:"column:duration;not null"`                                // 任务处理的耗时时间，整型，表示秒数或毫秒数等
	CreatedAt       time.Time                 `json:"createdAt" gorm:"column:createdAt;not null"`                              // 创建时间，日期时间类型
	UpdatedAt       time.Time                 `json:"updatedAt" gorm:"column:updatedAt;not null"`                              // 更新时间，日期时间类型
	DeletedAt       gorm.DeletedAt            `json:"-" gorm:"column:deletedAt;index"`                                         // 删除时间/标记删除
}

// TableName 获取表名
func (m *MiniProgramSend) TableName() string {
	return "push-service-mini-program"
}

// CreateMiniProgramSend 创建风险控制条目
func (m *MiniProgramSend) CreateMiniProgramSend(db *gorm.DB) error {
	result := db.Create(m)
	if result.Error != nil {
		return result.Error
	}
	return nil
}

// GetMiniProgramSend 根据ID查询风险控制条目
func (m *MiniProgramSend) GetMiniProgramSend(db *gorm.DB, id int64) (*MiniProgramSend, error) {
	var rcitem MiniProgramSend
	if err := db.First(&rcitem, id).Error; err != nil {
		return nil, err
	}
	return &rcitem, nil
}

// UpdateMiniProgramSend 更新风险控制条目
func (m *MiniProgramSend) UpdateMiniProgramSend(db *gorm.DB) error {
	result := db.Save(m)
	if result.Error != nil {
		return result.Error
	}
	return nil
}

// HardDeleteMiniProgramSend 执行硬删除，即从数据库中永久移除记录
func (m *MiniProgramSend) HardDeleteMiniProgramSend(db *gorm.DB, id int64) error {
	result := db.Unscoped().Delete(&MiniProgramSend{}, id)
	if result.Error != nil {
		return result.Error
	}
	return nil
}

func (m *MiniProgramSend) GetMiniProgramSendFromMd5(db *gorm.DB, md5 string) (*MiniProgramSend, error) {
	var rt MiniProgramSend
	if err := db.Where("md5 = ?", md5).First(&rt).Error; err != nil {
		return nil, err
	}
	return &rt, nil
}

func (m *MiniProgramSend) GetMiniProgramSendFromOpenId(db *gorm.DB, appId, templateId, openId, status string) (*MiniProgramSend, error) {
	var rt MiniProgramSend
	if err := db.Where("appId = ?", appId).
		Where("templateId = ?", templateId).
		Where("openId = ?", openId).
		Where("status = ?", string(status)).First(&rt).Error; err != nil {
		return nil, err
	}
	return &rt, nil
}

func (m *MiniProgramSend) GetTasksWithStatus(db *gorm.DB, pushtype enum.PushType, status enum.TaskStatus, substatus enum.SubscribeStatus) ([]*MiniProgramSend, error) {
	var tasks []*MiniProgramSend
	err := db.Where("type = ?", string(pushtype)).Where("status = ?", string(status)).Where("subscribeStatus = ?", string(substatus)).
		Order("createdAt ASC").Find(&tasks).Error
	if err != nil {
		return nil, err
	}

	return tasks, nil
}

func (m *MiniProgramSend) GetTasksWithStatusNotSubstatus(db *gorm.DB, pushtype enum.PushType, status enum.TaskStatus) ([]*MiniProgramSend, error) {
	var tasks []*MiniProgramSend
	err := db.Where("type = ?", string(pushtype)).Where("status = ?", string(status)).Order("createdAt ASC").Find(&tasks).Error
	if err != nil {
		return nil, err
	}

	return tasks, nil
}
