apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: digital-human
    module: dhlive-push-service
  name: dhlive-push-service
  namespace: dh-v3
spec:
  replicas: 2
  minReadySeconds: 0
  strategy:
    type: RollingUpdate # 策略：滚动更新
    rollingUpdate:
      maxSurge: 25%
      maxUnavailable: 25%
  selector:
    matchLabels:
      app: digital-human
      module: dhlive-push-service
  template:
    metadata:
      labels:
        app: digital-human
        module: dhlive-push-service
    spec:
      affinity:
        podAntiAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            - labelSelector:
                matchExpressions:
                  - key: app
                    operator: In
                    values:
                      - digital-human
                  - key: module
                    operator: In
                    values:
                      - dhlive-push-service
              topologyKey: kubernetes.io/hostname
      restartPolicy: Always
      tolerations:
        - effect: "NoSchedule"
          key: "limit"
          operator: "Equal"
          value: "dh-v3"
      containers:
        - name: dhlive-push-service
          image: ccr-246im3mw-pub.cnc.bj.baidubce.com/digitalhuman/dhlive-push-service:20240918_1726631214772
          imagePullPolicy: Always
          command: [ "sh" ]
          env:
            - name: POD_NAMESPACE
              valueFrom:
                fieldRef:
                  fieldPath: metadata.namespace
          args: [ "/home/<USER>/sbin/start.sh" ]
          # imagePullPolicy: IfNotPresent
          # imagePullPolicy: Never
          ports:
            - containerPort: 8080
          livenessProbe: # 健康检查：存活探针
            httpGet:
              path: /health
              port: 8080
            initialDelaySeconds: 20
            timeoutSeconds: 5
            periodSeconds: 5
          readinessProbe: # 健康检查：就绪探针
            httpGet:
              path: /health
              port: 8080
            initialDelaySeconds: 5
            timeoutSeconds: 1
            periodSeconds: 5
          volumeMounts:
            - mountPath: /home/<USER>/conf/conf.toml
              name: config-volume
              subPath: conf.toml
      volumes:
        - configMap:
            defaultMode: 420
            name: dhlive-push-service
          name: config-volume
      imagePullSecrets:
        - name: regcred
        - name: regcred-ccr
        - name: regcred-eccr
---
apiVersion: v1
kind: Service
metadata:
  labels:
    app: digital-human
    module: dhlive-push-service
  name: dhlive-push-service
  namespace: dh-v3
spec:
  selector:
    app: digital-human
    module: dhlive-push-service
  ports:
    - protocol: TCP
      port: 8080
      targetPort: 8080
  type: ClusterIP
---
apiVersion: v1
kind: ConfigMap
metadata:
  labels:
    app: digital-human
    module: dhlive-push-service
  name: dhlive-push-service
  namespace: dh-v3
data:
  conf.toml: |
    ####################################################### 服务配置-生产环境 #######################################################
    server-port = 8080
    server-name = "dhlive-push-service"
    log-file-prefix = "localhost"
    log-file-path = "./logs/"
    log-show-code-file = true
    log-show-code-func = true
    # consul配置
    [consul-setting]
    host = "127.0.0.1"
    port = 8500
    name = ""

    # 健康检查地址
    health-url = "/health"
    check-timeout = "10s"
    check-interval = "10s"
    deregister-critical-service-after = "30s"

    [mysql-setting]
    host = "mysql57-online-v3.rdsmidsh9s934wt.rds.bj.baidubce.com"
    port = 3306
    database = "dhlive_third_platform_online"
    username = "dhlive_tp_plat"
    password = "Hi109@123"
    maxOpenConns = 100
    maxIdlenConns = 5

    # redis配置
    [redis-setting]
    addr = "*************:6379"
    username = ""
    password = ""
    redisEnv = "prod"

    # 日志上传的elasticsearch配置
    [log-es-setting]
    host = "http://*************:8200"
    username = "superuser"
    password = "Baidu_dh123"

    [dh-user-setting]
    baseUrl = "http://dh-user:80"

    [bos-setting]
    # apikey
    ak = "ALTAKxdVkHMs4sp0Tgkpa3zCJP"
    # Secret Key
    sk = "e4f46ff5d1bf45c5b81dd867d6e3c148"
    endpoint = "bj.bcebos.com"
    bucket   = "xiling-dh"
    host     = "https://xiling-dh.bj.bcebos.com"
    cdn-host = "https://xiling-dh.cdn.bcebos.com"
    
    [wechat-mini-program-setting]
    appid = "wx0b249fd9072dfab1"
    secretkey = "f7584fd66f3eb70e1115ff2c5772a77d"
    templateid = "Mdky34I1mQflTJr-fAR-ZMivqeBV26-E5K8F5JYgzHU"
    token = "xiling"

    [baidu-mini-program-setting]
    appid = "112580510"
    appkey = "AkjgloosxYVXoeqbMFzP7yZ2wFhMltup"
    secretkey = "xxeYRqdhutfspYZ121YzQLYsRp2W4LD3"
    templateid = "8a077940d6cb4c2b8cd58106fc72f21e"
    token = "xiling"
---