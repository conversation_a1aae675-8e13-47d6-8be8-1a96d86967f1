apiVersion: extensions/v1beta1
kind: Ingress
metadata:
  name: dhlive-push-service-ingress
  namespace: dh-v3
  annotations:
    nginx.ingress.kubernetes.io/proxy-body-size: 1024m  
    nginx.ingress.kubernetes.io/use-regex: "true"
spec:
  rules:
    - http:
        paths:
          - backend:
              serviceName: dhlive-push-service
              servicePort: 8080
            path: /xiling/push/service/mini-program/v1/*
          - backend:
              serviceName: dhlive-push-service
              servicePort: 8080
            path: /api/digitalhuman/push/service/v1/*               