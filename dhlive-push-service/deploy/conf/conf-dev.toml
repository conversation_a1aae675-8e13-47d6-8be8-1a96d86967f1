####################################################### 服务配置-测试环境 #######################################################
server-port = 8080
server-name = "dhlive-push-service"
log-file-prefix = "localhost"
log-file-path = "./logs/"
log-show-code-file = true
log-show-code-func = true
# consul配置
[consul-setting]
host = "127.0.0.1"
port = 8500
name = ""
# 健康检查地址
health-url = "/health"
check-timeout = "10s"
check-interval = "10s"
deregister-critical-service-after = "30s"

# mysql配置
[mysql-setting]
# host = "mysql57.rdsm9dvaqfvz285.rds.bj.baidubce.com"
host = "127.0.0.1"
port = 3371
database = "dhlive_third_platform_v3"
username = "dhlive_tp_plat"
password = "Hi109@123"
maxOpenConns = 100
maxIdlenConns = 5

# redis配置
[redis-setting]
# addr = "************:6379"
addr = "127.0.0.1:6777"
username = ""
password = ""
redisEnv = "dev"

# 日志上传的elasticsearch配置
[log-es-setting]
# host = "http://*************:8200"
host = "http://127.0.0.1:9202"
username = ""
password = ""

# 鉴权
[dh-user-setting]
baseUrl = "http://dh-user:80"

[bos-setting]
# apikey
ak = "ALTAKxdVkHMs4sp0Tgkpa3zCJP"
# Secret Key
sk = "e4f46ff5d1bf45c5b81dd867d6e3c148"
endpoint = "bj.bcebos.com"
bucket   = "xiling-dh"
host     = "https://xiling-dh.bj.bcebos.com"
cdn-host = "https://xiling-dh.cdn.bcebos.com"

[wechat-mini-program-setting]
appid = "wx0b249fd9072dfab1"
secretkey = "f7584fd66f3eb70e1115ff2c5772a77d"
templateid = "Mdky34I1mQflTJr-fAR-ZMivqeBV26-E5K8F5JYgzHU"
token = "xiling"

[baidu-mini-program-setting]
appid = "112580510"
appkey = "AkjgloosxYVXoeqbMFzP7yZ2wFhMltup"
secretkey = "xxeYRqdhutfspYZ121YzQLYsRp2W4LD3"
templateid = "8a077940d6cb4c2b8cd58106fc72f21e"
token = "xiling"