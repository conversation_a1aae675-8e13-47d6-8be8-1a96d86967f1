WORK_SPACE=$(dirname "$0")
OUT_PUT=../output
binName="digital-human-figure-resource"

moduleBType=server
while getopts 'm:t': OPTION;do
    case $OPTION in
    t)
      moduleBType=$OPTARG
    ;;
    ?)echo "get a non option $OPTARG and OPTION is $OPTION"
    ;;
    esac
done

echo "WORK_SPACE": "$WORK_SPACE"
echo "bin name: "$binName

MODULE_OUT_PUT=$OUT_PUT/$binName
mkdir -p $OUT_PUT/$binName
mkdir -p $OUT_PUT/$binName/bin
mkdir -p $OUT_PUT/$binName/sbin
mkdir -p $OUT_PUT/$binName/logs
mkdir -p $OUT_PUT/$binName/conf
mkdir -p $OUT_PUT/$binName/cache
if [ "$RUN_ENVIRONMENT" == "" ]; then
  RUN_ENVIRONMENT="test"
fi
# 修改启动文件
echo "CUR_PATH=\$(pwd)" > $OUT_PUT/$binName/sbin/start.sh
echo "\$CUR_PATH/bin/app -env $RUN_ENVIRONMENT > \$CUR_PATH/logs/std_log.txt &>/dev/null & sleep 1" >> $OUT_PUT/$binName/sbin/start.sh
echo "tail -f logs/localhost_latest.log" >> $OUT_PUT/$binName/sbin/start.sh

# 复制发布目录
# cp -r $WORK_SPACE/deploy/conf $MODULE_OUT_PUT

# GNU/Linux操作系统
## 配置GONOPROXY环境变量，所有百度内代码，不走代理
go env -w GONOPROXY=\*\*.baidu.com\*\*
## 配置GONOSUMDB，目前有一些代码库还不支持sumdb索引，暂时屏蔽此功能
go env -w GONOSUMDB=\*
## 配置GOPROXY，可以下载墙外代码
go env -w GOPROXY=https://goproxy.baidu-int.com
go env -w GOPRIVATE=\*.baidu.com

# 编译为Linux应用程序
echo "$binName build start ..."
if [ "$moduleBType" == "server" ]; then
  CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build -o $MODULE_OUT_PUT/bin/app  *.go
else
  go build -o $MODULE_OUT_PUT/bin/app  *.go
fi

if [ $? -ne 0 ]; then
    echo "$binName build error"
    exit 1
fi

echo "$binName build end"
ls -a $MODULE_OUT_PUT/bin/app