apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: digital-human
    module: digital-human-figure-resource
  name: digital-human-figure-resource
  namespace: sandbox-dh-v3
spec:
  replicas: 1
  minReadySeconds: 0
  strategy:
    type: RollingUpdate # 策略：滚动更新
    rollingUpdate:
      maxSurge: 25%
      maxUnavailable: 25%
  selector:
    matchLabels:
      app: digital-human
      module: digital-human-figure-resource
  template:
    metadata:
      labels:
        app: digital-human
        module: digital-human-figure-resource
    spec:
      affinity:
        podAntiAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            - labelSelector:
                matchExpressions:
                  - key: app
                    operator: In
                    values:
                      - digital-human
                  - key: module
                    operator: In
                    values:
                      - digital-human-figure-resource
              topologyKey: kubernetes.io/hostname
      restartPolicy: Always
      tolerations:
        - key: "limit"
          operator: "Equal"
          value: "sandbox-dh-v3"
          effect: "NoSchedule"
      containers:
        - name: digital-human-figure-resource
          image: ccr-246im3mw-pub.cnc.bj.baidubce.com/digitalhuman/digital-human-figure-resource:20241206_1733477658294
          imagePullPolicy: Always
          command: [ "sh" ]
          env:
            - name: POD_NAMESPACE
              valueFrom:
                fieldRef:
                  fieldPath: metadata.namespace
          args: [ "/home/<USER>/sbin/start.sh" ]
          # imagePullPolicy: IfNotPresent
          # imagePullPolicy: Never
          ports:
            - containerPort: 8080
          livenessProbe: # 健康检查：存活探针
            httpGet:
              path: /health
              port: 8080
            initialDelaySeconds: 20
            timeoutSeconds: 5
            periodSeconds: 5
          readinessProbe: # 健康检查：就绪探针
            httpGet:
              path: /health
              port: 8080
            initialDelaySeconds: 5
            timeoutSeconds: 1
            periodSeconds: 5
          volumeMounts:
            - mountPath: /home/<USER>/conf/conf.toml
              name: config-volume
              subPath: conf.toml
      volumes:
        - configMap:
            defaultMode: 420
            name: digital-human-figure-resource
          name: config-volume
      imagePullSecrets:
        - name: regcred
        - name: regcred-ccr
        - name: regcred-eccr
---
apiVersion: v1
kind: Service
metadata:
  labels:
    app: digital-human
    module: digital-human-figure-resource
  name: digital-human-figure-resource
  namespace: sandbox-dh-v3
spec:
  selector:
    app: digital-human
    module: digital-human-figure-resource
  ports:
    - protocol: TCP
      port: 8080
      targetPort: 8080
  type: ClusterIP
---
apiVersion: v1
kind: ConfigMap
metadata:
  labels:
    app: digital-human
    module: digital-human-figure-resource
  name: digital-human-figure-resource
  namespace: sandbox-dh-v3
data:
  conf.toml: |
    ####################################################### 服务配置-测试环境 #######################################################
    server-port = 8080
    server-name = "digital-human-figure-resource"
    log-file-prefix = "localhost"
    log-file-path = "./logs/"
    log-show-code-file = true
    log-show-code-func = true
    # consul配置
    [consul-setting]
    host = "127.0.0.1"
    port = 8500
    name = ""
    # 健康检查地址
    health-url = "/health"
    check-timeout = "10s"
    check-interval = "10s"
    deregister-critical-service-after = "30s"

    # mysql配置
    [mysql-setting]
    host = "************"
    port = 3306
    database = "meta_human_editor_v3"
    username = "dh_v3"
    password = "!Digitalhuman@baidu123"
    maxOpenConns = 100
    maxIdlenConns = 5

    [starlight-mysql-setting]
    host = "************"
    port = 3306
    database = "star_light_v3"
    username = "dh_v3"
    password = "!Digitalhuman@baidu123"
    maxOpenConns = 100
    maxIdlenConns = 5
    
    [plat-mysql-setting]
    host = "************"
    port = 3306
    database = "digital_human_plat_v3"
    username = "dh_v3"
    password = "!Digitalhuman@baidu123"
    maxOpenConns = 100
    maxIdlenConns = 5

    # redis配置
    [redis-setting]
    addr = "************:6379"
    username = ""
    password = ""
    redisEnv = "test"
    
    # 日志上传的elasticsearch配置
    [log-es-setting]
    host = "http://elasticsearch:9200"
    username = ""
    password = ""

    # 鉴权
    [dh-user-setting]
    baseUrl = "http://dh-user:80"

    [bos-setting]
    # apikey
    ak = "fb1e15123edc43afa2d7b34f78177f55"
    # Secret Key
    sk = "16a4e80552a04d939a4c9d5b014427d5"
    endpoint = "https://meta-human-editor-test.cdn.bcebos.com"
    bucket   = "meta-human-editor-test"
    host     = "https://meta-human-editor-test.bcebos.com"
    cdn-host = "https://meta-human-editor-test.cdn.bcebos.com"

    [material-setting]
    maxImageSizeMB = 50
    maxVideoSizeMB = 1024
    maxAudioSizeMB = 50
    needEncode = true
    materialMaxSize = 500 #素材总个数
    
    [zg-push-config]
    appID = "BaiDuXiLing"
    appSecret = "b1a0dea0-f5ba-4db3-9457-25be00e43af8"
    pushUrl = "https://mlzj.zrtg.com:8443/baiduXiling/import/completeVideoImport"
    importFileMax = 5
    zgAuthPath = "/api/internal/workflow/v2/user/auth/lanyun"

    [videopipeline-setting]
    baseUrl = "http://digital-human-video-pipeline.dh-v3:8080"
    
    # 上传接口配置信息
    [upload-setting]
    # 图片支持格式配置
    imageSuffix = ["jpg", "gif", "png", "bmp", "jpeg"]
    # 视频支持格式配置
    videoSuffix = ["mov", ".mp4"]
    # 音频支持格式配置
    audioSuffix = ["mp3", "wav"]
    # 图片最大支持文件大小， 单位M
    maxImageSizeMB = 50
    # 视频最大支持文件大小， 单位M
    maxVideoSizeMB = 300
    # 图片最大支持文件大小， 单位M
    maxAudioSizeMB = 150
    # 是否启用审核功能
    isCensor = false
    # 最大支持并发数
    queueMaxNumber = 100
    # key 最大保留时间， 单位：秒
    queueMaxInterval = 30
---
