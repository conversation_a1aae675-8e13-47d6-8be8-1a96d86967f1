####################################################### 服务配置-测试环境 #######################################################
server-port = 8080
server-name = "digital-human-figure-resource"
log-file-prefix = "localhost"
log-file-path = "./logs/"
log-show-code-file = true
log-show-code-func = true
# consul配置
[consul-setting]
host = "127.0.0.1"
port = 8500
name = ""
# 健康检查地址
health-url = "/health"
check-timeout = "10s"
check-interval = "10s"
deregister-critical-service-after = "30s"

# mysql配置
[mysql-setting]
# host = "mysql57.rdsm9dvaqfvz285.rds.bj.baidubce.com"
host = "127.0.0.1"
port = 3368
database = "meta_human_editor_saas"
username = "saas"
password = "!Digitalhuman@baidu123"
maxOpenConns = 100
maxIdlenConns = 5

[starlight-mysql-setting]
host = "127.0.0.1"
port = 3368
database = "digital_human_star_light"
username = "saas"
password = "!Digitalhuman@baidu123"
maxOpenConns = 100
maxIdlenConns = 5

[starlight-mysql-read-setting]
host = "127.0.0.1"
port = 3368
database = "digital_human_star_light"
username = "saas"
password = "!Digitalhuman@baidu123"
maxOpenConns = 100
maxIdlenConns = 5

[plat-mysql-setting]
host = "127.0.0.1"
port = 3368
database = "digital_human_plat_saas"
username = "saas"
password = "!Digitalhuman@baidu123"
maxOpenConns = 100
maxIdlenConns = 5



# redis配置
[redis-setting]
# addr = "************:6379"
addr = "127.0.0.1:6777"
username = ""
password = ""
redisEnv = "dev"

# 日志上传的elasticsearch配置
[log-es-setting]
# host = "http://*************:8200"
host = "http://127.0.0.1:9202"
username = ""
password = ""

# 鉴权
[dh-user-setting]
baseUrl = "http://127.0.0.1:18080"

[bos-setting]
# apikey
ak = "ALTAKxdVkHMs4sp0Tgkpa3zCJP"
# Secret Key
sk = "e4f46ff5d1bf45c5b81dd867d6e3c148"
endpoint = "bj.bcebos.com"
bucket   = "xiling-dh"
host     = "https://xiling-dh.bj.bcebos.com"
cdn-host = "https://xiling-dh.cdn.bcebos.com"

[material-setting]
maxImageSizeMB = 50
maxVideoSizeMB = 1024
maxAudioSizeMB = 50
needEncode = true
materialMaxSize = 500 #素材总个数

[zg-push-config]
appID = "BaiDuXiLing"
appSecret = "b1a0dea0-f5ba-4db3-9457-25be00e43af8"
pushUrl = "https://mlzj.zrtg.com:8443/baiduXiling/import/completeVideoImport"
importFileMax = 5
zgAuthPath = "/api/internal/workflow/v2/user/auth/lanyun"


[videopipeline-setting]
baseUrl = "http://127.0.0.1:28080"

# 上传接口配置信息
[upload-setting]
# 图片支持格式配置
imageSuffix = ["jpg", "gif", "png", "bmp", "jpeg"]
# 视频支持格式配置
videoSuffix = ["mov", ".mp4"]
# 音频支持格式配置
audioSuffix = ["mp3", "wav"]
# 图片最大支持文件大小， 单位M
maxImageSizeMB = 50
# 视频最大支持文件大小， 单位M
maxVideoSizeMB = 300
# 图片最大支持文件大小， 单位M
maxAudioSizeMB = 150
# 是否启用审核功能
isCensor = false
# 最大支持并发数
queueMaxNumber = 100
# key 最大保留时间， 单位：秒
queueMaxInterval = 30

[ai-accelerator-setting]
origin-host = "https://cloudtest.baidu.com"