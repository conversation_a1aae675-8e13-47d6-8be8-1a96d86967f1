apiVersion: extensions/v1beta1
kind: Ingress
metadata:
  name: digital-human-figure-resource-ingress
  namespace: dh-v3
  annotations:
    nginx.ingress.kubernetes.io/proxy-body-size: 1024m  
    nginx.ingress.kubernetes.io/use-regex: "true"
spec:
  rules:
    - http:
        paths:
          - backend:
              serviceName: digital-human-figure-resource
              servicePort: 8080
            path: /api/digitalhuman/figure/resource/v1/animation/template  
          - backend:
              serviceName: digital-human-figure-resource
              servicePort: 8080
            path: /api/digitalhuman/figure/resource/v1/material 
          - backend:
              serviceName: digital-human-figure-resource
              servicePort: 8080
            path: /api/digitalhuman/figure/resource/v1/figure/   
          - backend:
              serviceName: digital-human-figure-resource
              servicePort: 8080
            path: /api/digitalhuman/figure/resource/v1/video/zg/push
          - backend:
              serviceName: digital-human-figure-resource
              servicePort: 8080
            path: /api/digitalhuman/figure/resource/v1/homepage/activity/banner

