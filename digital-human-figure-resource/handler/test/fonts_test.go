// nolint
package modeltest

import (
	"acg-ai-go-common/global"
	"acg-ai-go-common/gomysql"
	"acg-ai-go-common/server"
	"acg-ai-go-common/storage"
	"acg-ai-go-common/utils"
	"bytes"
	"context"
	"digital-human-figure-resource/beans/model"
	"digital-human-figure-resource/beans/proto"
	config "digital-human-figure-resource/conf"
	"digital-human-figure-resource/handler/figure"
	"encoding/json"
	"fmt"
	"io/fs"
	"io/ioutil"
	"log"
	"net/http"
	"net/http/httptest"
	"path/filepath"
	"strings"
	"testing"

	"acg-ai-go-common/utils/mysqlproxy"

	"github.com/BurntSushi/toml"
	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"golang.org/x/image/font/sfnt"
	"gorm.io/gorm"
)

// ListFilesInDir 读取指定目录下所有文件，返回文件的绝对路径切片
func ListFilesInDir(dirPath string) ([]string, error) {
	var files []string
	err := filepath.WalkDir(dirPath, func(path string, d fs.DirEntry, err error) error {
		if err != nil {
			return err
		}
		// 只收集文件（排除文件夹）
		if !d.IsDir() {
			files = append(files, path)
		}
		return nil
	})

	if err != nil {
		return nil, err
	}
	return files, nil
}

// CleanFileName 从文件路径提取文件名，去后缀并将空格替换为 `-`
func CleanFileName(filePath string) string {
	base := filepath.Base(filePath)                           // 提取文件名（带扩展名）
	ext := filepath.Ext(base)                                 // 提取扩展名
	nameWithoutExt := strings.TrimSuffix(base, ext)           // 去掉扩展名
	cleanName := strings.ReplaceAll(nameWithoutExt, " ", "-") // 替换空格为 -
	return cleanName
}

// CleanFileName 从文件路径提取文件名，去后缀并将空格替换为 `-`
func CleanFileNameForSpace(filePath string) string {
	base := filepath.Base(filePath)                 // 提取文件名（带扩展名）
	ext := filepath.Ext(base)                       // 提取扩展名
	nameWithoutExt := strings.TrimSuffix(base, ext) // 去掉扩展名
	return nameWithoutExt
}

// setupTestDB 初始化测试数据库连接
func setupTestDB() *gorm.DB {
	// 初始化公共配置
	server.InitGlobalSetting()
	if _, err := toml.DecodeFile(global.ConfFilePath, config.LocalConfig); err != nil {
		log.Panicf("本地个性化配置加载失败: {%v}", err)
	}
	// 初始化mysql
	mysqlproxy := mysqlproxy.GetMysqlProxy()
	// 开启携程监听mysql是否发生网络中断并进行重连
	go mysqlproxy.MonitorMysqlConnection()

	// 初始化对象存储
	if err := storage.Init(global.ServerSetting); err != nil {
		log.Panicf("初始化对象存储失败: {%v}", err)
	}
	return gomysql.DB
}

// GetFontFullName 从字体文件路径读取字体全称（Full Name）
func GetFontFullName(fontPath string) (string, error) {
	data, err := ioutil.ReadFile(fontPath)
	if err != nil {
		return "", err
	}

	font, err := sfnt.Parse(data)
	if err != nil {
		return "", err
	}

	var buf sfnt.Buffer
	name, err := font.Name(&buf, sfnt.NameIDFull)
	if err != nil {
		return "", err
	}

	return name, nil
}

func TestUpdateFonsts_Success(t *testing.T) {
	db := setupTestDB()
	defer func() {
		sqlDB, _ := db.DB()
		sqlDB.Close()
	}()
	// 读区 excel 文件
	fileList, err := ListFilesInDir("/Users/<USER>/Project/baidu/acg-digital-human/digital-human-saas/digital-human-figure-resource/handler/test/hahafont")
	if err != nil {
		fmt.Println("读取文件夹失败:", err)
		return
	}

	for i, filePath := range fileList {
		fileName := filepath.Base(filePath)
		if !strings.HasSuffix(fileName, ".ttf") {
			continue
		}
		fontName, err := GetFontFullName(filePath)
		if err != nil {
			log.Panicf("获取字体全称失败: {%v}", err)
		}
		fontsInfo, err := (&model.Fonts{}).GetFonts(gomysql.DB, fontName)
		if err == nil {
			// if fontsInfo.Sort == int64(len(fileList)-i)  && fontName == fontsInfo.FontID {
			// 	continue
			// }

			fontsInfo.FontID = fontName
			// if fontsInfo.PngURL == "-" || path.Ext(fontsInfo.PngURL) == ".png" {
			pngPath := "/Users/<USER>/Project/baidu/acg-digital-human/digital-human-saas/digital-human-figure-resource/handler/test/hahaImg/" + CleanFileNameForSpace(filePath) + ".svg"
			// 上传 gcs
			logCtx := context.WithValue(context.TODO(), utils.CtxKeyLogID, uuid.NewString())
			bosskey := fmt.Sprintf("/fonts_preview/%s", CleanFileNameForSpace(filePath)+".svg")
			pngUrl, err := figure.RetryUploadBosServiceFromFileByBosKeyWithCDN(logCtx, bosskey, pngPath, "image/svg+xml; charset=utf-8")
			if err != nil {
				log.Panicf("上传bos失败: {%v}", err)
			}
			fontsInfo.PngURL = pngUrl
			// }
			// {
			// 	// 上传 gcs
			// 	// logCtx := context.WithValue(context.TODO(), utils.CtxKeyLogID, uuid.NewString())
			// 	bosskey := fmt.Sprintf("/fonts/%s", fileName)
			// 	url, err := figure.RetryUploadBosServiceFromFileByBosKeyWithCDN(logCtx, bosskey, filePath, "")
			// 	if err != nil {
			// 		log.Panicf("上传bos失败: {%v}", err)
			// 	}
			// 	fontsInfo.FontsURL = url
			// }

			fontsInfo.Sort = int64(len(fileList) - i)
			err = fontsInfo.UpdateFonts(gomysql.DB)
			if err != nil {
				log.Panicf("更新字体失败: {%v}", err)
			}
			continue
		}

		// 上传 gcs
		logCtx := context.WithValue(context.TODO(), utils.CtxKeyLogID, uuid.NewString())
		bosskey := fmt.Sprintf("/fonts/%s", fileName)
		url, err := figure.RetryUploadBosServiceFromFileByBosKeyWithCDN(logCtx, bosskey, filePath, "")
		if err != nil {
			log.Panicf("上传bos失败: {%v}", err)
		}

		pngPath := "/Users/<USER>/Project/baidu/acg-digital-human/digital-human-saas/digital-human-figure-resource/handler/test/hahaImg/" + CleanFileNameForSpace(filePath) + ".svg"
		// 上传 gcs
		bosskey = fmt.Sprintf("/fonts_preview/%s", CleanFileNameForSpace(filePath)+".svg")
		pngUrl, err := figure.RetryUploadBosServiceFromFileByBosKeyWithCDN(logCtx, bosskey, pngPath, "image/svg+xml; charset=utf-8")
		if err != nil {
			log.Panicf("上传bos失败: {%v}", err)
		}

		router := gin.Default()
		router.POST("/update-fonts", figure.UpdateFonts)

		updateReq := proto.UpdateFonstRequest{
			Name:     CleanFileNameForSpace(filePath),
			FontID:   fontName,
			PngURL:   pngUrl,
			FontsURL: url,
			Sort:     int64(len(fileList) - i),
		}
		reqBody, _ := json.Marshal(updateReq)

		w := httptest.NewRecorder()
		req, _ := http.NewRequest("POST", "/update-fonts", bytes.NewBuffer(reqBody))
		req.Header.Set("Content-Type", "application/json")
		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response proto.CommRsp
		json.Unmarshal(w.Body.Bytes(), &response)
		assert.Equal(t, 0, response.Code)
	}

}

func TestUpdateFonsts_BindError(t *testing.T) {
	setupTestDB()
	router := gin.Default()
	router.POST("/update-fonts", figure.UpdateFonts)

	w := httptest.NewRecorder()
	req, _ := http.NewRequest("POST", "/update-fonts", bytes.NewBuffer([]byte("invalid json")))
	req.Header.Set("Content-Type", "application/json")
	router.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)

	var response proto.CommDataRsp
	json.Unmarshal(w.Body.Bytes(), &response)
	assert.Equal(t, 100001, response.Code)
}

func TestUpdateFonsts_DBError(t *testing.T) {
	// Setup with nil DB to force error
	gomysql.DB = nil

	router := gin.Default()
	router.POST("/update-fonts", figure.UpdateFonts)

	updateReq := proto.UpdateFonstRequest{
		Name:     "test-font",
		PngURL:   "http://test.png",
		FontsURL: "http://test.ttf",
	}
	reqBody, _ := json.Marshal(updateReq)

	w := httptest.NewRecorder()
	req, _ := http.NewRequest("POST", "/update-fonts", bytes.NewBuffer(reqBody))
	req.Header.Set("Content-Type", "application/json")
	router.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)

	var response proto.CommDataRsp
	json.Unmarshal(w.Body.Bytes(), &response)
	assert.Equal(t, 100002, response.Code)
}
