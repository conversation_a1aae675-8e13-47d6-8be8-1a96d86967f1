// nolint
package modeltest

import (
	"acg-ai-go-common/utils"
	"bytes"
	"fmt"
	"os/exec"
	"strconv"
	"strings"
	"testing"
)

type AudioItem struct {
	Name string
	URL  string
}

var audioList = []AudioItem{
	{"Action Strike", "https://test.keevx.com/backend-saas-cdn/materials/system/03b071ec-0bf0-4859-96be-771aa9551939.mp3"},
	{"Advertime", "https://test.keevx.com/backend-saas-cdn/materials/system/d38362f8-f887-41ce-8895-657dc52ea683.mp3"},
	{"Beginning of Conflict", "https://test.keevx.com/backend-saas-cdn/materials/system/04ffd05d-bb9a-45f0-8f3e-ca5c2f83647a.mp3"},
	{"Bollywood Groove", "https://test.keevx.com/backend-saas-cdn/materials/system/9036c5cd-3a2a-441c-8946-ec7e3dd9d4f5.mp3"},
	{"Brewing Potions", "https://test.keevx.com/backend-saas-cdn/materials/system/59d695af-acf4-4c2e-b0cc-8cfc82282d95.mp3"},
	{"Dreams of Vain", "https://test.keevx.com/backend-saas-cdn/materials/system/618447e0-9306-485c-b90d-0ced5fdc71fc.mp3"},
	{"Fresh Focus", "https://test.keevx.com/backend-saas-cdn/materials/system/8062cc4a-d989-49b5-84b8-4af25ee829fe.mp3"},
	{"From Page to Practice", "https://test.keevx.com/backend-saas-cdn/materials/system/41032458-ebcd-47c8-a186-13e2e3c19871.mp3"},
	{"Guerilla Tactics", "https://test.keevx.com/backend-saas-cdn/materials/system/e919b94f-3876-45a9-9e4e-3fc7bb9ec5ab.mp3"},
	{"Happy Whistling Ukulele", "https://test.keevx.com/backend-saas-cdn/materials/system/7686c419-4af1-498c-bb06-9d638b5af902.mp3"},
	{"Hold on a Sec", "https://test.keevx.com/backend-saas-cdn/materials/system/00396232-1e1f-4566-b4dc-2974781da0b5.mp3"},
	{"Hopeful", "https://test.keevx.com/backend-saas-cdn/materials/system/7239b502-5ddb-4565-9883-61e3d39358f6.mp3"},
	{"Inspiration", "https://test.keevx.com/backend-saas-cdn/materials/system/49ad33a5-a475-4626-96c9-bbb81a0d9ebf.mp3"},
	{"Isolation Waltz", "https://test.keevx.com/backend-saas-cdn/materials/system/b1a85e74-6b87-4d37-90c1-03db658bae2e.mp3"},
	{"Magical Transition", "https://test.keevx.com/backend-saas-cdn/materials/system/6333a82b-6cb5-4544-8f2f-b4f8e6290dc9.mp3"},
	{"Modern Island Jam", "https://test.keevx.com/backend-saas-cdn/materials/system/6d7e950c-2954-419b-add2-368d9c1c469d.mp3"},
	{"Motions", "https://test.keevx.com/backend-saas-cdn/materials/system/73ce10da-382f-4317-a652-144735745e52.mp3"},
	{"Natural Vibes", "https://test.keevx.com/backend-saas-cdn/materials/system/dbcf8689-1ea6-4f72-84fe-1382e4b6d5bc.mp3"},
	{"Night in Venice", "https://test.keevx.com/backend-saas-cdn/materials/system/5475cb17-fd77-4076-96eb-f99dd577b7ad.mp3"},
	{"Pickled Pink", "https://test.keevx.com/backend-saas-cdn/materials/system/0982b653-c8ac-43f2-949e-aa25ee739fd9.mp3"},
	{"Pond", "https://test.keevx.com/backend-saas-cdn/materials/system/f38abec3-0932-4393-95f4-05d91e7834d3.mp3"},
	{"Romantic Inspiration", "https://test.keevx.com/backend-saas-cdn/materials/system/bdce1eaf-ff8e-43db-bb60-c24c72db5f51.mp3"},
	{"Shining Stars", "https://test.keevx.com/backend-saas-cdn/materials/system/f48873be-1937-4819-bfe9-e8e64c0ab9e2.mp3"},
	{"Stereotype News", "https://test.keevx.com/backend-saas-cdn/materials/system/1a5d9067-f452-4a08-a795-d8ee2f248741.mp3"},
	{"The Drama", "https://test.keevx.com/backend-saas-cdn/materials/system/66cd1d8e-2136-4e30-ba16-b2e9a6952b97.mp3"},
	{"Trip Up North", "https://test.keevx.com/backend-saas-cdn/materials/system/96b76b11-6d0d-4824-a8ef-bc803e698a54.mp3"},
	{"Ukulele Song", "https://test.keevx.com/backend-saas-cdn/materials/system/7f95d9d3-5c2c-4827-8e69-3c3f4bc29b2c.mp3"},
	{"Village Tarantella", "https://test.keevx.com/backend-saas-cdn/materials/system/adbf84e2-6816-49bb-94f6-3888547d1e5f.mp3"},
}

// 上传公共素材
func TestUpLoadSystemMateria(t *testing.T) {
	// setupTestDB()
	// // 读区 excel 文件
	// fileList, err := ListFilesInDir("/Users/<USER>/Project/baidu/acg-digital-human/digital-human-saas/digital-human-figure-resource/handler/test/mp3")
	// if err != nil {
	// 	fmt.Println("读取文件夹失败:", err)
	// 	return
	// }

	// for _, filePath := range fileList {
	// 	// 上传 gcs
	// 	logCtx := context.WithValue(context.TODO(), utils.CtxKeyLogID, uuid.NewString())
	// 	bosskey := fmt.Sprintf("/system/%s", CleanFileNameForSpace(filePath)+".mp3")
	// 	mp3Url, err := figure.RetryUploadBosServiceFromFileByBosKeyWithCDN(logCtx, bosskey, filePath, "audio/mpeg")
	// 	if err != nil {
	// 		log.Panicf("上传bos失败: {%v}", err)
	// 	}
	// 	fmt.Printf("%s:%s", filePath, mp3Url)
	// }
	for _, item := range audioList {
		FormatSql(item.Name, item.URL)
	}

}

func FormatSql(name, url string) {
	// 生成 material_id，格式如 m-pj6ri7fyaxtmzu882d
	materialID := "m-" + utils.RandStringRunes(18)

	// 组装 SQL 语句
	sql := fmt.Sprintf(`INSERT INTO meta_human_editor_saas.slide_material 
(material_id, name, user_id, url, type, module, creator, thumbnail, preview_url, duration, height, width, extra_info) 
VALUES('%s', '%s', 'System', '%s', 'AUDIO', 'MUSIC', 'AI_Card', NULL, NULL, %d, -1, -1, NULL);`,
		materialID, name, url, GetAudioDurationMS(url))

	fmt.Println(sql)
}

// 获取音频 URL 的时长（单位：毫秒）
func GetAudioDurationMS(url string) int64 {
	cmd := exec.Command("ffprobe",
		"-v", "error",
		"-show_entries", "format=duration",
		"-of", "default=noprint_wrappers=1:nokey=1",
		url,
	)

	var out bytes.Buffer
	cmd.Stdout = &out
	cmd.Stderr = &out

	err := cmd.Run()
	if err != nil {
		fmt.Println("ffprobe 执行失败:", err)
		return 0
	}

	durationStr := strings.TrimSpace(out.String())
	seconds, err := strconv.ParseFloat(durationStr, 64)
	if err != nil {
		fmt.Println("解析音频时长失败:", err)
		return 0
	}

	return int64(seconds * 1000)
}
