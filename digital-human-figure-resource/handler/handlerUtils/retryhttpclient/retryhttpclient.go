package retryhttpclient

import (
	"acg-ai-go-common/logger"
	"acg-ai-go-common/utils"
	"context"
	"fmt"
	"io"
	"io/ioutil"
	"net/http"
	"time"
)

// RetryHTTPClient 是一个支持重试的 HTTP 客户端
type RetryHTTPClient struct {
	client     *http.Client
	timeout    time.Duration
	maxRetries int
}

// NewRetryHTTPClient 创建一个新的 RetryableHTTPClient 实例
// 使用示例：
// 创建一个 15s 超时时间，3 次重试的 RetryableHTTPClient 实例
// retclient := retryhttpclient.NewRetryHTTPClient(15*time.Second, 3)
//
// header := make(map[string]string)
// header["Content-Type"] = "application/json"
// resp, err := retclient.DoRequest(logCtx, "POST", callbackurl, header, bytes.NewBuffer(jsonData))
func NewRetryHTTPClient(timeout time.Duration, maxRetries int) *RetryHTTPClient {
	return &RetryHTTPClient{
		client:     &http.Client{},
		timeout:    timeout,
		maxRetries: maxRetries,
	}
}

// DoRequest 执行一个 HTTP 请求并支持重试
func (c *RetryHTTPClient) DoRequest(logCtx context.Context, method, url string, headers map[string]string, body io.Reader) ([]byte, error) {
	var resp *http.Response
	var err error
	buff := make([]byte, 0)
	for i := 0; i < c.maxRetries; i++ {
		ctx, cancel := context.WithTimeout(context.Background(), c.timeout)
		defer cancel()

		req, reqErr := http.NewRequestWithContext(ctx, method, url, body)
		if reqErr != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+"NewRequestWithContext faild: %v \n", err)
			return buff, reqErr
		}

		// 设置请求头
		for key, value := range headers {
			req.Header.Set(key, value)
		}

		resp, err = c.client.Do(req)
		if err == nil {
			defer func() { resp.Body.Close() }()
			body, err := ioutil.ReadAll(resp.Body)
			if err != nil {
				logger.Log.Errorf(utils.MMark(logCtx)+"SendCallbackRequse ioutil.ReadAllt failed: %+v\n", err)
				return buff, err
			}
			return body, nil
		}

		// 打印错误信息
		logger.Log.Errorf(utils.MMark(logCtx)+"requst faild: %v, Trying to reconnect (the number of retries: %d)\n", err, i+1)
		time.Sleep(1 * time.Second) // 等待一秒再重试
	}

	return buff, fmt.Errorf("requst faild: %v", err)
}
