package figure

import (
	"acg-ai-go-common/logger"
	"acg-ai-go-common/utils"
	"bytes"
	"context"
	"digital-human-figure-resource/handler/handlerUtils"
	"errors"
	"fmt"
	"github.com/gabriel-vasile/mimetype"
	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"image"
	"image/png"
	"io"
	"os"
	"os/exec"
	"path"
	"strings"

	_ "golang.org/x/image/bmp"  // BMP 格式支持
	_ "golang.org/x/image/tiff" // TIFF 格式支持
)

func downloadImageByRequest(logCtx context.Context, c *gin.Context) (string, string, error) {
	err := os.MkdirAll(CacheDir, 0755)
	if err != nil {
		logger.Log.Errorf("downloadMaterialFile MkdirAll() errer=%v", err)
		return "", "", errors.New("mkdir error:" + err.Error())
	}

	// 处理上传的文件
	reqfile, fileHeader, err := c.Request.FormFile("file")
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+" get file faild, err: %v", err)
		return "", fileHeader.Filename, err
	}
	defer reqfile.Close()

	newFileName := uuid.New().String() + path.Ext(fileHeader.Filename)
	// 创建文件
	downloadPath := fmt.Sprintf(DownloadPathFmt, newFileName)
	outFile, err := os.Create(downloadPath)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"Failed to create output file, err:%v", err)
		return "", fileHeader.Filename, err
	}
	defer outFile.Close()

	// 将请求中的文件内容复制到新文件中
	_, err = io.Copy(outFile, reqfile)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+" Failed to copy file content, err:%v", err)
		return "", fileHeader.Filename, err
	}

	return downloadPath, fileHeader.Filename, nil
}

func getFileFormat(logCtx context.Context, filePath string) (string, string, error) {
	// 打开文件
	file, err := os.Open(filePath) // 替换为你的文件名
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"Failed to open file, err:%v", err)
		return "", "", errors.New("打开文件失败")
	}
	defer file.Close() // 确保在函数结束时关闭文件

	// 检测 MIME 类型
	mime, err := mimetype.DetectReader(file)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"Failed to detect file type, err:%v", err)
		return "", "", errors.New("无法识别文件类型")
	}

	logger.Log.Infof(utils.MMark(logCtx)+"File type is %s", mime.String())
	// 分割文件类型
	contentSplit := strings.Split(mime.String(), "/")
	if len(contentSplit) != 2 {
		return "", "", errors.New("无法识别文件类型")
	}

	fileType := strings.ToUpper(contentSplit[0])
	fileFormat := contentSplit[1]

	return fileType, fileFormat, nil
}

// getImageDimensions 读取图片文件并返回其宽度和高度
func getImageDimensions(imagePath string) (int, int, error) {
	// 打开图片文件
	file, err := os.Open(imagePath)
	if err != nil {
		return 0, 0, fmt.Errorf("failed to open image: %w", err)
	}
	defer file.Close()

	// 解码图片配置信息（无需完整解码图片数据）
	imgConfig, _, err := image.DecodeConfig(file)
	if err != nil {
		return 0, 0, fmt.Errorf("failed to decode image: %w", err)
	}

	return imgConfig.Width, imgConfig.Height, nil
}

func convertImage(logCtx context.Context, fileFormat, inputPath string) (string, error) {
	outputPath := fmt.Sprintf(DownloadPathFmt, uuid.New().String())
	switch strings.ToLower(fileFormat) {
	case "png", "jpg", "jpeg":
		logger.Log.Infof(utils.MMark(logCtx)+"No conversion needed for %s format", fileFormat)
		outputPath = inputPath
	case "bmp", "tiff":
		logger.Log.Infof(utils.MMark(logCtx)+"Converting %s to PNG", fileFormat)
		outputPath += ".png"
		err := convertImageToPNG(inputPath, outputPath)
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+"Failed to convert image: %v", err)
			return "", fmt.Errorf("failed to convert image: %w", err)
		}
	case "gif":
		logger.Log.Infof(utils.MMark(logCtx) + "Converting GIF to PNG")
		outputPath += ".png"
		err := convertLivePhotoToJpg(inputPath, outputPath)
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+"Failed to convert image: %v", err)
			return "", fmt.Errorf("failed to convert image: %w", err)
		}
	case "mov", "heif":
		logger.Log.Infof(utils.MMark(logCtx) + "Converting MOV/HEIF to PNG")
		outputPath += ".png"
		err := convertLivePhotoToJpg(inputPath, outputPath)
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+"Failed to convert image: %v", err)
			return "", fmt.Errorf("failed to convert image: %w", err)
		}
	default:
		logger.Log.Errorf(utils.MMark(logCtx)+"Unsupported image format %s", fileFormat)
		return "", errors.New("不支持的图片格式")
	}

	return outputPath, nil
}

// ConvertImageToPNG 将静态图片（非 PNG/JPG/JPEG 格式）转换为 PNG
func convertImageToPNG(inputPath, outputPath string) error {
	// 打开输入文件
	file, err := os.Open(inputPath)
	if err != nil {
		return fmt.Errorf("failed to open file: %v", err)
	}
	defer file.Close()

	// 解码图像
	img, _, err := image.Decode(file)
	if err != nil {
		return fmt.Errorf("failed to decode image: %v", err)
	}

	// 创建输出文件
	outFile, err := os.Create(outputPath)
	if err != nil {
		return fmt.Errorf("failed to create output file: %v", err)
	}
	defer outFile.Close()

	// 将图像保存为 PNG
	err = png.Encode(outFile, img)
	if err != nil {
		return fmt.Errorf("failed to encode image to PNG: %v", err)
	}

	return nil
}

// ConvertGIFToPNG 使用 ffmpeg 将 GIF 的指定帧转换为 PNG
func convertLivePhotoToJpg(inputPath, outputPath string) error {
	// 使用 ffmpeg 提取 GIF 的第一帧
	cmd := exec.Command("ffmpeg", "-i", inputPath, "-vf", "select=eq(n\\,0)", "-vframes", "1", outputPath)
	var stderr bytes.Buffer
	cmd.Stderr = &stderr

	err := cmd.Run()
	if err != nil {
		return fmt.Errorf("ffmpeg error: %v, details: %s", err, stderr.String())
	}
	return nil
}

func checkImageFileSize(outputPath string, maxSizeMB int64) (int64, bool, error) {
	fileInfo, err := os.Stat(outputPath)
	if err != nil {
		return 0, false, fmt.Errorf("failed to get file info: %v", err)
	}

	if fileInfo.Size() > maxSizeMB {
		return fileInfo.Size(), true, nil
	}

	return fileInfo.Size(), false, nil
}

// adjustResolutionAndCompress 使用 ffmpeg 调整分辨率和压缩图片大小
func adjustResolutionAndCompress(logCtx context.Context, inputPath, outputPath string, maxSizeMB int64, origWidth, origHeight int) error {
	// 创建 ffmpeg 命令
	cmd := exec.Command(
		"ffmpeg",
		"-i", inputPath,
		"-vf", fmt.Sprintf("scale='%d:%d'", origWidth, origHeight),
		"-q:v", "5", // 设置压缩质量，值越小质量越高（适当调整）
		"-y", outputPath,
	)

	// 捕获 ffmpeg 的输出以便调试
	var stderr bytes.Buffer
	cmd.Stderr = &stderr

	// 执行命令
	err := cmd.Run()
	if err != nil {
		return fmt.Errorf("ffmpeg error: %v, details: %s", err, stderr.String())
	}

	// 检查输出文件是否符合大小限制
	fileSize, isTooLarge, err := checkImageFileSize(outputPath, maxSizeMB)
	if err != nil {
		return fmt.Errorf("failed to check output file size: %w", err)
	}

	// 如果仍然过大，尝试进一步压缩
	if isTooLarge {
		logger.Log.Warnf(utils.MMark(logCtx)+"Image size exceeds limit, attempting additional compression fileSize: %d", fileSize)
		cmd = exec.Command(
			"ffmpeg",
			"-i", outputPath,
			"-q:v", "10", // 设置压缩质量，值越小质量越高（适当调整）
			"-y", outputPath,
		)
		var stderr2 bytes.Buffer
		cmd.Stderr = &stderr2
		err = cmd.Run()
		if err != nil {
			return fmt.Errorf("ffmpeg compression error: %v, details: %s", err, stderr2.String())
		}
	}

	return nil
}
func DealImageSize(logCtx context.Context, filePath string) (int, int, string, error) {
	// 获取图片大小
	imageSize, err := handlerUtils.GetFileSize(filePath)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"getFileSize faild err:%v", err)
		return 0, 0, "", err
	}
	// 获取图片尺寸
	width, height, err := getImageDimensions(filePath)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"getImageDimensions faild err:%v", err)
		return 0, 0, "", err
	}

	if imageSize > 20*1024*1024 || width > 1920 || width < 320 || height > 1920 || height < 320 {
		logger.Log.Infof(utils.MMark(logCtx)+"DealImageSize filePath:%s width:%d height:%d imageSize:%d", filePath, width, height, imageSize)
		outputPath := fmt.Sprintf(DownloadPathFmt, uuid.New().String()+".jpg")

		newWidth, newHeight := CalculateScaledDimensions(width, height, 320, 1920, 320, 1920)
		logger.Log.Infof("CalculateScaledDimensions origWidth:%d origHeight:%d newWidth:%d newHeight:%d", width, height, newWidth, newHeight)

		err = adjustResolutionAndCompress(logCtx, filePath, outputPath, 20*1024*1024, newWidth, newHeight)
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+"adjustResolutionAndCompress faild err:%v", err)
			return 0, 0, "", err
		}
		return newWidth, newHeight, outputPath, nil
	}

	return width, height, filePath, nil
}

// CalculateScaledDimensions 计算等比缩放后的宽度和高度，并确保它们不小于最小分辨率，同时如果在范围内则不进行缩放
// 原始宽度、原始高度、最小宽度、最大宽度、最小高度、最大高度
func CalculateScaledDimensions(origWidth, origHeight, minWidth, maxWidth, minHeight, maxHeight int) (int, int) {
	// 如果宽度和高度已经在范围内，则无需修改
	if origWidth >= minWidth && origWidth <= maxWidth && origHeight >= minHeight && origHeight <= maxHeight {
		return origWidth, origHeight
	}

	widthRatio := 1.0
	heightRatio := 1.0
	scaleRatio := 1.0

	if origWidth > maxWidth || origHeight > maxHeight {
		widthRatio = float64(maxWidth) / float64(origWidth)
		heightRatio = float64(maxHeight) / float64(origHeight)
		// 选择较小的缩放比例，以确保宽高比不变
		scaleRatio = widthRatio
		if heightRatio < widthRatio {
			scaleRatio = heightRatio
		}
	} else {
		widthRatio = float64(minWidth) / float64(origWidth)
		heightRatio = float64(minHeight) / float64(origHeight)
		// 选择较小的缩放比例，以确保宽高比不变
		scaleRatio = widthRatio
		if heightRatio > widthRatio {
			scaleRatio = heightRatio
		}
	}

	// 计算新的宽度和高度
	newWidth := int(float64(origWidth) * scaleRatio)
	newHeight := int(float64(origHeight) * scaleRatio)

	// 确保宽度和高度不小于最小值
	if newWidth < minWidth {
		newWidth = minWidth
	} else if newWidth > maxWidth {
		newWidth = maxWidth
	}

	if newHeight < minHeight {
		newHeight = minHeight
	} else if newHeight > maxHeight {
		newHeight = maxHeight
	}

	// 返回新的宽度和高度
	return newWidth, newHeight
}

// 判断图像是否为灰度图
func isGrayImage(filePath string) (bool, error) {
	// 打开图像文件
	file, err := os.Open(filePath)
	if err != nil {
		return false, fmt.Errorf("failed to open image file: %w", err)
	}
	defer file.Close()

	// 解码图像
	img, _, err := image.Decode(file)
	if err != nil {
		return false, fmt.Errorf("failed to decode image: %w", err)
	}

	// 遍历像素判断是否为灰度图
	bounds := img.Bounds()
	for y := bounds.Min.Y; y < bounds.Max.Y; y++ {
		for x := bounds.Min.X; x < bounds.Max.X; x++ {
			c := img.At(x, y)
			r, g, b, _ := c.RGBA()
			// RGBA 返回的值范围是 0-65535，灰度图的 R、G、B 通道值应相等
			if r != g || g != b {
				return false, nil
			}
		}
	}

	return true, nil
}
