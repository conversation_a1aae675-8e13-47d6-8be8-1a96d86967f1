package figure

import (
	"acg-ai-go-common/logger"
	"acg-ai-go-common/utils"
	"acg-ai-go-common/utils/redisproxy"
	"context"
	config "digital-human-figure-resource/conf"
	"time"

	"github.com/baidubce/bce-sdk-go/services/bos"
	bosApi "github.com/baidubce/bce-sdk-go/services/bos/api"
)

const (
	CacheDir = "./cache"
)

var (
	BosAk       = "ALTAKxdVkHMs4sp0Tgkpa3zCJP"
	BosSk       = "e4f46ff5d1bf45c5b81dd867d6e3c148"
	BosEndpoint = "bj.bcebos.com"
	BosBucket   = "xiling-dh"
	BosHost     = "https://xiling-dh.bj.bcebos.com"
	BosCdnHost  = "https://xiling-dh.cdn.bcebos.com"
)

type BosObjectKeyPath string

const (
	FigureFilePath BosObjectKeyPath = "/figure-resources/"
)

func InitBosParam() {
	// 初始化bos
	BosAk = config.LocalConfig.BosSetting.AK
	BosSk = config.LocalConfig.BosSetting.SK
	BosEndpoint = config.LocalConfig.BosSetting.Endpoint
	BosBucket = config.LocalConfig.BosSetting.Bucket
	BosHost = config.LocalConfig.BosSetting.Host
	BosCdnHost = config.LocalConfig.BosSetting.CDNHost
}

func UploadBosServiceFromFileByBosKey(logCtx context.Context, objectKeyPath string, localFilePath string) (string, error) {
	bosClient, err := bos.NewClient(BosAk, BosSk, BosEndpoint)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"Create BOS client failed: %v\n", err)
		return "", err
	}
	// 从本地文件上传
	_, err = bosClient.PutObjectFromFile(BosBucket, objectKeyPath, localFilePath, nil)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"PutObjectFromFile fail, err:%v", err)
		return "", err
	}
	downloadUrl := BosCdnHost + "/" + objectKeyPath
	logger.Log.Infof(utils.MMark(logCtx)+"PutObjectFromFile success, url:%s", downloadUrl)
	return downloadUrl, nil
}

func UploadBosServiceFromFileByBosKeyAndContentType(logCtx context.Context, objectKeyPath string, localFilePath, contentType string) (string, error) {
	bosClient, err := bos.NewClient(BosAk, BosSk, BosEndpoint)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"Create BOS client failed: %v\n", err)
		return "", err
	}

	args := &bosApi.PutObjectArgs{}
	if contentType != "" {
		args.ContentType = contentType
	}
	// 从本地文件上传
	_, err = bosClient.PutObjectFromFile(BosBucket, objectKeyPath, localFilePath, args)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"PutObjectFromFile fail, err:%v", err)
		return "", err
	}
	downloadUrl := BosCdnHost + "/" + objectKeyPath
	logger.Log.Infof(utils.MMark(logCtx)+"PutObjectFromFile success, url:%s", downloadUrl)
	return downloadUrl, nil
}

func RetryUploadBosServiceFromFileByBosKey(logCtx context.Context, objectKeyPath string, localFilePath string) (string, error) {
	var err error
	for i := 0; i < 3; i++ {
		downloadUrl, tmperr := UploadBosServiceFromFileByBosKey(logCtx, objectKeyPath, localFilePath)
		if err == nil {
			return downloadUrl, nil
		} else {
			err = tmperr
			time.Sleep(1 * time.Second)
		}
	}
	return "", err
}

func checkQueueAllowance(queueKey string, queueMaxNumber int, queueMaxInterval int64) (bool, error) {
	ok, err := redisproxy.AllowFromLua(queueKey, queueMaxNumber, queueMaxInterval)
	if err != nil {
		return false, err
	}

	if !ok {
		return false, nil
	}

	return true, nil
}

func decrementCounter(queueKey string) (int64, error) {
	return redisproxy.DecrementCounter(queueKey)
}
