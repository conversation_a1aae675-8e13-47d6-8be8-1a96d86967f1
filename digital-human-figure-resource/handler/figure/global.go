package figure

import (
	"acg-ai-go-common/logger"
	"acg-ai-go-common/storage"
	"acg-ai-go-common/utils"
	"acg-ai-go-common/utils/redisproxy"
	"context"
	config "digital-human-figure-resource/conf"
	"path/filepath"
)

const (
	CacheDir = "./cache"
)

var (
	BosAk       = "ALTAKxdVkHMs4sp0Tgkpa3zCJP"
	BosSk       = "e4f46ff5d1bf45c5b81dd867d6e3c148"
	BosEndpoint = "bj.bcebos.com"
	BosBucket   = "xiling-dh"
	BosHost     = "https://xiling-dh.bj.bcebos.com"
	BosCdnHost  = "https://xiling-dh.cdn.bcebos.com"
)

type BosObjectKeyPath string

const (
	FigureFilePath BosObjectKeyPath = "/figure-resources/"
)

func InitBosParam() {
	// 初始化bos
	BosAk = config.LocalConfig.BosSetting.AK
	BosSk = config.LocalConfig.BosSetting.SK
	BosEndpoint = config.LocalConfig.BosSetting.Endpoint
	BosBucket = config.LocalConfig.BosSetting.Bucket
	BosHost = config.LocalConfig.BosSetting.Host
	BosCdnHost = config.LocalConfig.BosSetting.CDNHost
}

func RetryUploadBosServiceFromFileByBosKey(logCtx context.Context, objectKeyPath string, localFilePath string) (string, error) {
	extWithDot := filepath.Ext(localFilePath)

	url, err := storage.RetryUploadFromFile(logCtx, objectKeyPath, localFilePath, extWithDot)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"RetryUploadFromFile fail, err:%v", err)
		return "", err
	}
	logger.Log.Infof(utils.MMark(logCtx)+"RetryUploadFromFile success, url:%s", url)
	return url, nil
}

func RetryUploadBosServiceFromFileByBosKeyWithCDN(logCtx context.Context, objectKeyPath string, localFilePath string, contentType string) (string, error) {
	extWithDot := filepath.Ext(localFilePath)

	url, err := storage.RetryUploadFromFileWithCDNAndContentType(logCtx, objectKeyPath, localFilePath, extWithDot, true, contentType)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"RetryUploadFromFile fail, err:%v", err)
		return "", err
	}
	logger.Log.Infof(utils.MMark(logCtx)+"RetryUploadFromFile success, url:%s", url)
	return url, nil
}

func checkQueueAllowance(queueKey string, queueMaxNumber int, queueMaxInterval int64) (bool, error) {
	ok, err := redisproxy.AllowFromLua(queueKey, queueMaxNumber, queueMaxInterval)
	if err != nil {
		return false, err
	}

	if !ok {
		return false, nil
	}

	return true, nil
}

func decrementCounter(queueKey string) (int64, error) {
	return redisproxy.DecrementCounter(queueKey)
}
