package figure

import (
	"acg-ai-go-common/global"
	"acg-ai-go-common/gomysql"
	"acg-ai-go-common/logger"
	"acg-ai-go-common/utils"
	"context"
	"digital-human-figure-resource/beans/model"
	"digital-human-figure-resource/beans/proto"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
)

func Fonts(c *gin.Context) {
	logCtx := context.WithValue(context.TODO(), utils.CtxKeyLogID, utils.RandStringRunes(16))

	req := proto.FontsRequest{}
	if err := c.ShouldBind(&req); err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"Fonts req error: %+v", err)
		c.JSON(http.StatusOK, proto.NewCommDataRsp(100001, "内部错误", &[]proto.FontsResponse{{
			LogId: <PERSON><PERSON>(global.HeaderTraceID),
		}}))
		return
	}

	res := []proto.FontsResponse{}
	// 获取字幕模版列表
	_, list, err := (&model.Fonts{}).ListFonts(gomysql.DB, req.PageNo, req.PageSize)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"Fonts list error: %+v", err)
		c.JSON(http.StatusOK, proto.NewCommDataRsp(100002, "内部错误", &[]proto.FontsResponse{{
			LogId: c.GetHeader(global.HeaderTraceID),
		}}))
		return
	}

	for _, v := range list {
		res = append(res, proto.FontsResponse{
			ID:        v.ID,
			FontID:    v.FontID,
			Name:      v.Name,
			PngURL:    v.PngURL,
			FontsURL:  v.FontsURL,
			Sort:      v.Sort,
			IsDefault: v.IsDefault,
		})
	}

	c.JSON(http.StatusOK, proto.NewSuccessRsp(res))
}

func FontsWithInternal(c *gin.Context) {
	logCtx := context.WithValue(context.TODO(), utils.CtxKeyLogID, utils.RandStringRunes(16))
	targetLanguage := c.GetHeader("Language")
	if targetLanguage == "" {
		targetLanguage = "zh"
	}

	req := proto.FontsRequest{}
	if err := c.ShouldBind(&req); err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"Fonts req error: %+v", err)
		c.JSON(http.StatusOK, proto.NewCommDataRsp(100001, "内部错误", &[]proto.FontsResponse{{
			LogId: c.GetHeader(global.HeaderTraceID),
		}}))
		return
	}

	res := &proto.FontsResponseWithInternal{
		TotalCount: 0,
		List:       []proto.FontsResponse{},
	}

	// 获取字幕模版列表
	count, list, err := (&model.Fonts{}).ListFonts(gomysql.DB, req.PageNo, req.PageSize)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"Fonts list error: %+v", err)
		c.JSON(http.StatusOK, proto.NewCommDataRsp(100002, "内部错误", &[]proto.FontsResponse{{
			LogId: c.GetHeader(global.HeaderTraceID),
		}}))
		return
	}

	res.TotalCount = count

	for _, v := range list {
		res.List = append(res.List, proto.FontsResponse{
			ID:        v.ID,
			FontID:    v.FontID,
			Name:      v.Name,
			PngURL:    v.PngURL,
			FontsURL:  v.FontsURL,
			Sort:      v.Sort,
			IsDefault: v.IsDefault,
		})
	}

	c.JSON(http.StatusOK, proto.NewSuccessRsp(res))
}

func UpdateFonts(c *gin.Context) {
	logCtx := context.WithValue(context.TODO(), utils.CtxKeyLogID, utils.RandStringRunes(16))

	req := proto.UpdateFonstRequest{}
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"UpdateFonst req error: %+v", err)
		c.JSON(http.StatusOK, proto.NewCommDataRsp(100001, "内部错误", &[]proto.FontsResponse{}))
		return
	}

	font := &model.Fonts{
		ID:        req.ID,
		FontID:    req.FontID,
		Name:      req.Name,
		PngURL:    req.PngURL,
		FontsURL:  req.FontsURL,
		Sort:      req.Sort,
		IsDefault: req.IsDefault,
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	if err := font.UpdateFonts(gomysql.DB); err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"UpdateFonst error: %+v", err)
		c.JSON(http.StatusOK, proto.NewCommDataRsp(100002, "内部错误", &[]proto.FontsResponse{}))
		return
	}

	c.JSON(http.StatusOK, proto.NewSuccessRsp(nil))
}

// 删除字体包
func DeleteFonts(c *gin.Context) {
	logCtx := context.WithValue(context.TODO(), utils.CtxKeyLogID, utils.RandStringRunes(16))
	req := &proto.DeleteFontsRequest{}
	if err := c.ShouldBindJSON(req); err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"DeleteFonts req error: %+v", err)
		c.JSON(http.StatusOK, proto.NewCommDataRsp(100001, "内部错误", &[]proto.FontsResponse{}))
		return
	}

	if err := (&model.Fonts{}).DeleteFonts(gomysql.DB, req.IDs); err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"DeleteFonts error: %+v", err)
		c.JSON(http.StatusOK, proto.NewCommDataRsp(100002, "内部错误", &[]proto.FontsResponse{}))
		return
	}

	c.JSON(http.StatusOK, proto.NewSuccessRsp(nil))
}
