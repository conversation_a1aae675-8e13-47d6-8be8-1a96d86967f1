package figure

import (
	"acg-ai-go-common/logger"
	"acg-ai-go-common/utils"
	"context"
	"digital-human-figure-resource/beans/proto"
	config "digital-human-figure-resource/conf"
	"net/http"
	"strings"
	"sync"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

const (
	AddMaterialKeyPre          = "addMaterialKey"                  // 用户归属资源集合前缀
	AddMaterialKeyFmt          = "%v:" + AddMaterialKeyPre + ":%v" // 先填写环境ID，在添加唯一ID，组合起来是一个redis key
	AddMaterialKeyTimeDuration = 1 * time.Minute
)

func AddMaterialForZg(c *gin.Context) {
	reqID := uuid.New().String()
	logCtx := context.WithValue(context.TODO(), utils.CtxKeyLogID, reqID)

	if c.Request.Header.Get("Content-Type") != "application/json" {
		logger.Log.Errorf(utils.MMark(logCtx)+"AddMaterialForZg, Content-Type :%s, is wrong, need 'application/json'", c.Request.Header.Get("Content-Type"))
		c.JSON(http.StatusOK, proto.NewCommDataRsp(100001, "参数异常,Content-Type is wrong,need 'application/json", &proto.MaterialImportZgResResult{ReqId: reqID}))
		return
	}
	targetLanguage := c.GetHeader("Language")
	logCtx = context.WithValue(logCtx, "Language", targetLanguage)
	logger.Log.Infof(utils.MMark(logCtx)+"AddMaterial langugae in header is: %s", targetLanguage)

	accountID := ""
	aid, ok := c.Get("AccountId")
	if ok {
		accountID = aid.(string)
	}
	userName := ""
	if uid, ok := c.Get("UserName"); ok {
		userName = uid.(string)
	}

	urlParam := proto.MaterialImportZgUrlParam{}
	reqBody := proto.MaterialImportZgRequest{}

	// 绑定reqBody数据到结构体
	if err := c.ShouldBind(&reqBody); err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"AddMaterialForZg,reqBody bind faild, err: %v", err)
		c.JSON(http.StatusOK, proto.NewCommDataRsp(100002, "参数异常,请求体解析失败", &proto.MaterialImportZgResResult{ReqId: reqID}))
		return
	}

	// 绑定url中的参数 到结构体
	if err := c.ShouldBindQuery(&urlParam); err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"AddMaterialForZg,urlParam bind faild, err: %v", err)
		c.JSON(http.StatusOK, proto.NewCommDataRsp(100002, "参数异常,url参数解析失败", &proto.MaterialImportZgResResult{ReqId: reqID}))
		return
	}
	logger.Log.Infof(utils.MMark(logCtx)+"AddMaterialForZg start accountID:%s,userName:%s, reqBody:%+v,urlParam:%+v,fileList.size:%d", accountID, userName, reqBody, urlParam, len(reqBody.FileList))

	// 校验文件数量  默认5
	fileCountMax := 5
	if config.LocalConfig.ZgPushConfig.ImportFileMax > 0 {
		fileCountMax = config.LocalConfig.ZgPushConfig.ImportFileMax
	}
	if len(reqBody.FileList) > fileCountMax {
		logger.Log.Errorf(utils.MMark(logCtx)+"AddMaterialForZg,import file count is more than max:%d", fileCountMax)
		c.JSON(http.StatusOK, proto.NewCommDataRsp(100002, "参数异常,导入文件数量超过最大值", &proto.MaterialImportZgResResult{ReqId: reqID}))
		return
	}

	var wg sync.WaitGroup
	accountIDCopy := accountID
	userNameCopy := userName
	var rwMutex sync.RWMutex
	resFileMap := make(map[int]proto.ResFileModel)

	for seqID, fileModle := range reqBody.FileList {
		wg.Add(1)
		fileName := fileModle.FileName
		fileUrl := fileModle.FileUrl
		seqIDCopy := seqID
		go func() {
			defer wg.Done()
			// 每个请求的logid后加上materialId，方便定位问题
			materialId := "m-" + uuid.New().String()
			logCtxApi := context.WithValue(context.TODO(), utils.CtxKeyLogID, reqID+"_"+materialId)
			logger.Log.Infof(utils.MMark(logCtx)+"AddMaterialForZg addMaterialInternal start,fileName:%s,fileUrl:%s,materialId:%s", fileName, fileUrl, materialId)

			module := "MATERIAL"
			if strings.HasSuffix(fileUrl, "MP3") || strings.HasSuffix(fileUrl, "mp3") || strings.HasSuffix(fileUrl, "WAV") || strings.HasSuffix(fileUrl, "wav") {
				module = "MUSIC"
			} else {
				module = "MATERIAL"
			}
			req := proto.AddMaterialRequest{
				FileUrl:       fileUrl,
				FileName:      fileName,
				WhetherSystem: "false",
				Module:        module,
				IsNeedCensor:  false,
			}

			resResult, code, err := addMaterial(logCtx, c, materialId, accountIDCopy, userNameCopy, "", req)
			if err != nil {
				logger.Log.Errorf(utils.MMark(logCtxApi)+"addMaterialInternal,err:%v", err)
			}
			logger.Log.Infof(utils.MMark(logCtx)+"AddMaterialForZg addMaterialInternal,fileName:%s,fileUrl:%s,code:%d,resResult:%+v", fileName, fileUrl, code, resResult)

			errMsg := ""
			if code != 0 && err != nil {
				errMsg = err.Error()
			}

			rwMutex.Lock()
			resFileMap[seqIDCopy] = proto.ResFileModel{
				FileName:  fileName,
				ErrorCode: code,
				ErrorMsg:  errMsg,
			}
			rwMutex.Unlock()
		}()
	}
	wg.Wait()
	logger.Log.Infof(utils.MMark(logCtx)+"AddMaterialForZg all goroutines end,resFileMap:%+v", resFileMap)
	resFileModels := make([]proto.ResFileModel, len(resFileMap))

	for key := range resFileModels {
		resFileModels[key] = resFileMap[key]
	}

	materialImportZgResResult := proto.MaterialImportZgResResult{
		ReqId:   reqID,
		FileRes: resFileModels,
	}
	c.JSON(http.StatusOK, proto.NewSuccessRsp(materialImportZgResResult))
}
