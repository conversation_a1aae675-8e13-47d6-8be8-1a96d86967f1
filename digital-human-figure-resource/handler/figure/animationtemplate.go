package figure

import (
	"acg-ai-go-common/gomysql"
	"acg-ai-go-common/logger"
	"acg-ai-go-common/utils"
	"context"
	"digital-human-figure-resource/beans/model"
	"digital-human-figure-resource/beans/proto"
	"digital-human-figure-resource/handler/i18n/animtempi18n"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
)

func CreateAnimationTemplate(c *gin.Context) {
	logCtx := context.WithValue(context.TODO(), utils.CtxKeyLogID, utils.RandStringRunes(16))

	req := proto.AnimationConfig{}
	if err := c.ShouldBindJ<PERSON>N(&req); err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"CreateAnimationTemplate req error: %+v", err)
		c.JSON(http.StatusOK, proto.NewCommDataRsp(100001, "参数异常", &[]proto.AnimationConfig{}))
		return
	}

	_, err := (&model.AnimationTemplate{}).GetAnimationTemplate(gomysql.DB, req.AnimationID)
	if err == nil {
		c.JSON(http.StatusOK, proto.NewCommDataRsp(100002, "该动画已存在", utils.GetLogID(logCtx)))
		return
	}

	animation := &model.AnimationTemplate{
		AnimationName:    req.Name,
		AnimationID:      req.AnimationID,
		AnimationType:    req.Type,
		AnimationContent: req.Attributes,
		Duration:         req.Duration,
		PngImg:           req.PngImg,
		GifImg:           req.GifImg,
		CreatedAt:        time.Now(),
		UpdatedAt:        time.Now(),
	}

	err = animation.CreateAnimationTemplate(gomysql.DB)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"CreateAnimationTemplate error: %+v", err)
		c.JSON(http.StatusOK, proto.NewCommDataRsp(100004, "创建异常", utils.GetLogID(logCtx)))
		return
	}

	c.JSON(http.StatusOK, proto.NewSuccessRsp(nil))
}

func UpdateAnimationTemplate(c *gin.Context) {
	logCtx := context.WithValue(context.TODO(), utils.CtxKeyLogID, utils.RandStringRunes(16))

	req := proto.AnimationConfig{}
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"UpdateAnimationTemplate ShouldBindJSON error: %+v", err)
		c.JSON(http.StatusOK, proto.NewCommDataRsp(100001, "参数异常", utils.GetLogID(logCtx)))
		return
	}

	animation, err := (&model.AnimationTemplate{}).GetAnimationTemplate(gomysql.DB, req.AnimationID)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"UpdateAnimationTemplate GetAnimationTemplate error: %+v", err)
		c.JSON(http.StatusOK, proto.NewCommDataRsp(100002, "动画不存在无法更新", utils.GetLogID(logCtx)))
		return
	}

	animation.AnimationName = req.Name
	animation.AnimationType = req.Type
	animation.AnimationContent = req.Attributes
	animation.Duration = req.Duration
	animation.PngImg = req.PngImg
	animation.GifImg = req.GifImg
	animation.UpdatedAt = time.Now()

	err = animation.UpdateAnimationTemplate(gomysql.DB)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"UpdateAnimationTemplate error: %+v", err)
		c.JSON(http.StatusOK, proto.NewCommDataRsp(100004, "更新异常", &[]proto.AnimationConfig{}))
		return
	}

	c.JSON(http.StatusOK, proto.NewSuccessRsp(nil))
}

func ListAnimationTemplate(c *gin.Context) {
	logCtx := context.WithValue(context.TODO(), utils.CtxKeyLogID, utils.RandStringRunes(16))

	req := proto.ListAnimationTemplateRequest{}
	res := []proto.AnimationConfig{
		{
			LogID: utils.GetLogID(logCtx),
		},
	}

	if err := c.ShouldBindQuery(&req); err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"ListAnimationTemplate req error: %+v", err)
		c.JSON(http.StatusOK, proto.NewCommDataRsp(100001, "参数异常", &res))
		return
	}

	animations, err := (&model.AnimationTemplate{}).ListAnimationTemplate(gomysql.DB, req.PageNo, req.PageSize, req.Type)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"ListAnimationTemplate error: %+v", err)
		c.JSON(http.StatusOK, proto.NewCommDataRsp(100002, "查询异常", &res))
		return
	}

	// 这里使用多语种接管服务
	targetLanguage := c.GetHeader("Language")
	if len(targetLanguage) > 0 {
		outList, err := animtempi18n.TransformAnimTempLocale(logCtx, targetLanguage, animations)
		if err == nil {
			animations = outList
		}
	}

	res = []proto.AnimationConfig{}
	for _, v := range animations {
		res = append(res, proto.AnimationConfig{
			LogID:       utils.GetLogID(logCtx),
			Name:        v.AnimationName,
			PngImg:      v.PngImg,
			GifImg:      v.GifImg,
			AnimationID: v.AnimationID,
			Type:        v.AnimationType,
			Attributes:  v.AnimationContent,
			Duration:    v.Duration,
		})
	}

	c.JSON(http.StatusOK, proto.NewSuccessRsp(&res))
}
