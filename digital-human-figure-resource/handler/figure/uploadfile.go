package figure

import (
	"acg-ai-go-common/logger"
	"acg-ai-go-common/utils"
	"context"
	"digital-human-figure-resource/beans/enum"
	"digital-human-figure-resource/beans/proto"
	config "digital-human-figure-resource/conf"
	"digital-human-figure-resource/handler/handlerUtils"
	"fmt"
	"github.com/gin-gonic/gin"
	"net/http"
	"os"
	"path"
	"time"
)

const (
	MaterialImageBosKeyFmt = "%s/%s/%s/%s" // accountID/acc
)

func UploadFile(c *gin.Context) {
	logCtx := context.WithValue(context.TODO(), utils.CtxKeyLogID, utils.RandStringRunes(18))
	res := &proto.ImageConverterResponse{
		LogId: utils.GetLogID(logCtx),
		Url:   "",
	}
	// 申请队列
	queueKey := fmt.Sprintf("uploadQueueKey_%s", handlerUtils.GetNameByRunEnv())

	// 校验队列
	ok, err := checkQueueAllowance(queueKey, config.LocalConfig.UploadSetting.QueueMaxNumber, config.LocalConfig.UploadSetting.QueueMaxInterval)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+" uploadfile checkQueueAllowance is err:%v", err)
		c.JSON(http.StatusOK, proto.NewCommDataRsp(100001, err.Error(), res))
		return
	}
	// 并发超限
	if !ok {
		logger.Log.Errorf(utils.MMark(logCtx) + "queue is full")
		c.JSON(http.StatusOK, proto.NewCommDataRsp(100001, "并发超限，请稍后重试", res))
		return
	}
	// 函数结束队列数 -1
	defer decrementCounter(queueKey)

	// 默认未登录
	accountID := "not-login"
	// 获取登录信息
	aid, ok := c.Get("AccountId")
	if ok {
		accountID = aid.(string)
	}
	logger.Log.Info(utils.MMark(logCtx)+"accountID:%v", accountID)
	// 下载文件到缓存
	downloadPath, fileName, err := downloadImageByRequest(logCtx, c)
	res.FileName = fileName
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+" downloadImageByRequest faild err:%v", err)
		c.JSON(http.StatusOK, proto.NewCommDataRsp(100001, err.Error(), res))
		return
	}
	// 函数结束删除缓存文件
	defer os.Remove(downloadPath)

	// 获取文件类型
	fileType, fileForMat, err := getFileFormat(logCtx, downloadPath)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+" getFileFormat faild err:%v", err)
		c.JSON(http.StatusOK, proto.NewCommDataRsp(100002, err.Error(), res))
		return
	}
	// 获取文件基本信息
	fileInfo, err := os.Stat(downloadPath)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+" os.Stat faild err:%v", err)
		c.JSON(http.StatusOK, proto.NewCommDataRsp(100002, "文件信息获取失败", res))
		return
	}
	// 获取文件大小
	fileSize := fileInfo.Size()
	// 如果是图片则校验图片格式
	if fileType == "IMAGE" {
		isImage := false
		for _, suffix := range config.LocalConfig.UploadSetting.ImageSuffix {
			if fileForMat == suffix {
				isImage = true
				break
			}
		}
		// 图片格式不符合要求， 返回结果
		if !isImage {
			logger.Log.Errorf(utils.MMark(logCtx)+" isImage == false   fileForMat:%v ", fileForMat)
			c.JSON(http.StatusOK, proto.NewCommDataRsp(100002, "图片仅支持JPG/JPEG/PNG格式！", res))
			return
		}
		// 图片大小不符合要求， 返回结果
		maxImageSize := config.LocalConfig.UploadSetting.MaxImageSizeMB * 1024 * 1024
		if fileSize > maxImageSize {
			logger.Log.Errorf(utils.MMark(logCtx)+" fileSize > maxImageSize   fileSize :%v ", fileSize)
			c.JSON(http.StatusOK, proto.NewCommDataRsp(100002, fmt.Sprintf("%s%sM", "图片不得超过: ", maxImageSize), res))
			return
		}
	} else if fileType == "VIDEO" {
		isVideo := false
		for _, suffix := range config.LocalConfig.UploadSetting.VideoSuffix {
			if fileForMat == suffix {
				isVideo = true
				break
			}
		}
		// 视频格式不符合要求， 返回结果
		if !isVideo {
			logger.Log.Errorf(utils.MMark(logCtx)+" isVideo == false   fileForMat:%v ", fileForMat)
			c.JSON(http.StatusOK, proto.NewCommDataRsp(100002, "视频仅支持MP4/MOV格式！", res))
			return
		}
		// 视频大小不符合要求， 返回结果
		maxVideoSize := config.LocalConfig.UploadSetting.MaxVideoSizeMB * 1024 * 1024
		if fileSize > maxVideoSize {
			logger.Log.Errorf(utils.MMark(logCtx)+" fileSize > maxVideoSize   fileSize :%v ", fileSize)
			c.JSON(http.StatusOK, proto.NewCommDataRsp(100002, fmt.Sprintf("%s%sM", "视频不得超过: ", maxVideoSize), res))
			return
		}
	} else if fileType == "AUDIO" {
		isAudio := false
		for _, suffix := range config.LocalConfig.UploadSetting.AudioSuffix {
			if fileForMat == suffix {
				isAudio = true
				break
			}
		}
		// 音频格式校验
		if !isAudio {
			logger.Log.Errorf(utils.MMark(logCtx)+" isAideo == false   fileForMat:%v ", fileForMat)
			c.JSON(http.StatusOK, proto.NewCommDataRsp(100002, "音频仅支持MP3/MAV格式！", res))
			return
		}
		// 音频文件大小校验
		maxAudioSize := config.LocalConfig.UploadSetting.MaxAudioSizeMB * 1024 * 1024
		if fileSize > maxAudioSize {
			logger.Log.Errorf(utils.MMark(logCtx)+" fileSize > maxAudioSize   fileSize :%v ", fileSize)
			c.JSON(http.StatusOK, proto.NewCommDataRsp(100002, fmt.Sprintf("%s%sM", "音频不得超过: ", maxAudioSize), res))
			return
		}
	}

	// 上传至bos
	uploadType := c.Request.FormValue("type")
	bosskey := fmt.Sprintf(MaterialImageBosKeyFmt, uploadType, time.Now().Format("2006-01-02"), accountID, path.Base(downloadPath))
	uploadUrl, err := RetryUploadBosServiceFromFileByBosKey(logCtx, bosskey, downloadPath)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"RetryUploadBosServiceFromFileByBosKey faild err:%v", err)
		c.JSON(http.StatusOK, proto.NewCommDataRsp(100003, "图片上传失败", res))
		return
	}

	// 图片审核能力
	if config.LocalConfig.UploadSetting.IsCensor {
		if fileType == "IMAGE" {
			err = censor(logCtx, "", uploadUrl, enum.MaterialImage)
		} else if fileType == "VIDEO" {
			err = censor(logCtx, "", uploadUrl, enum.MaterialVideo)
		} else if fileType == "AUDIO" {
			err = censor(logCtx, "", uploadUrl, enum.MaterialAudio)
		}
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+"Failed to censor file, err:%v", err)
			c.JSON(http.StatusOK, proto.NewCommDataRsp(100004, "审核失败", res))
			return
		}
	}

	// 图片url编码
	encodeUrl, err := encodeUrl(uploadUrl)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"Failed to encode url, err:%v", err)
		c.JSON(http.StatusOK, proto.NewCommDataRsp(10005, "图片编码失败", res))
		return
	}

	// 图片处理完成，返回结果
	res.Url = encodeUrl
	newFileName := path.Base(downloadPath)
	ext := path.Ext(newFileName)
	res.FileId = newFileName[:len(newFileName)-len(ext)]
	res.Type = uploadType
	logger.Log.Info(utils.MMark(logCtx)+"upload success:%v", uploadUrl)
	c.JSON(http.StatusOK, proto.NewSuccessRsp(res))
}
