package figure

import (
	"acg-ai-go-common/global"
	"acg-ai-go-common/logger"
	"acg-ai-go-common/storage"
	"acg-ai-go-common/utils"
	"acg-ai-go-common/utils/location"
	"context"
	"digital-human-figure-resource/beans/enum"
	"digital-human-figure-resource/beans/proto"
	config "digital-human-figure-resource/conf"
	"digital-human-figure-resource/handler/handlerUtils"
	"digital-human-figure-resource/handler/i18n"
	"digital-human-figure-resource/handler/i18n/signurli18n"
	"fmt"
	"net/http"
	"os"
	"path"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"gopkg.in/vansante/go-ffprobe.v2"
)

const (
	MaterialImageBosKeyFmt = "%s/%s/%s/%s" // accountID/acc
)

func UploadFile(c *gin.Context) {
	logCtx := context.WithValue(context.TODO(), utils.CtxKeyLogID, utils.RandStringRunes(18))
	res := &proto.ImageConverterResponse{
		LogId: utils.GetLogID(logCtx),
		Url:   "",
	}
	// 申请队列
	queueKey := fmt.Sprintf("uploadQueueKey_%s", handlerUtils.GetNameByRunEnv())

	// 校验队列
	ok, err := checkQueueAllowance(queueKey, config.LocalConfig.UploadSetting.QueueMaxNumber, config.LocalConfig.UploadSetting.QueueMaxInterval)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+" uploadfile checkQueueAllowance is err:%v", err)
		c.JSON(http.StatusOK, proto.NewCommDataRsp(100001, err.Error(), res))
		return
	}
	// 并发超限
	if !ok {
		logger.Log.Errorf(utils.MMark(logCtx) + "queue is full")
		c.JSON(http.StatusOK, proto.NewCommDataRsp(100001, "并发超限，请稍后重试", res))
		return
	}
	// 函数结束队列数 -1
	defer decrementCounter(queueKey)

	// 默认未登录
	accountID := "not-login"
	// 获取登录信息
	aid, ok := c.Get("AccountId")
	if ok {
		accountID = aid.(string)
	}
	uploadType := c.Request.FormValue("type")

	logger.Log.Info(utils.MMark(logCtx)+"accountID:%v,uploadType:%s", accountID, uploadType)
	// 下载文件到缓存
	downloadPath, fileName, err := downloadImageByRequest(logCtx, c)
	res.FileName = fileName
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+" downloadImageByRequest faild err:%v", err)
		c.JSON(http.StatusOK, proto.NewCommDataRsp(100001, err.Error(), res))
		return
	}
	// 函数结束删除缓存文件
	defer os.Remove(downloadPath)

	// 获取文件类型
	fileType, fileForMat, err := getFileFormat(logCtx, downloadPath)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+" getFileFormat faild err:%v", err)
		c.JSON(http.StatusOK, proto.NewCommDataRsp(100002, err.Error(), res))
		return
	}
	// 获取文件基本信息
	fileInfo, err := os.Stat(downloadPath)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+" os.Stat faild err:%v", err)
		c.JSON(http.StatusOK, proto.NewCommDataRsp(100002, "文件信息获取失败", res))
		return
	}
	logger.Log.Infof(utils.MMark(logCtx)+"fileType:%s,fileForMat:%s", fileType, fileForMat)
	// 获取文件大小
	fileSize := fileInfo.Size()

	if uploadType == "VIDEO_TRANSLATE" {
		// 视频大小不符合要求， 返回结果
		maxVideoSize := config.LocalConfig.UploadSetting.MaxTranslateVideoSizeMB * 1024 * 1024
		if fileSize > maxVideoSize {
			logger.Log.Errorf(utils.MMark(logCtx)+" fileSize > maxVideoSize, fileSize :%v,maxVideoSize:%v ", fileSize, maxVideoSize)
			c.JSON(http.StatusOK, proto.NewCommDataRsp(100002, "The video file is too large", res))
			return
		}
		// 处理上传的视频翻译类型
		duration, err := GetDurationFromURL(downloadPath)
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+" get video duration failed error:%v", err)
			c.JSON(http.StatusOK, proto.NewCommDataRsp(100002, "get video duration failed", res))
		}
		maxDuration := int64(30 * 60 * 1000)
		logger.Log.Infof(utils.MMark(logCtx)+"video duration:%d,maxDuration:%d", duration, maxDuration)
		if duration > maxDuration {
			logger.Log.Errorf(utils.MMark(logCtx) + "video duration exceed 30 minites")
			c.JSON(http.StatusOK, proto.NewCommDataRsp(100002, "video duration exceed 30 minites", res))
		}
		// 处理视频翻译的上传文件
		handleVideoTranslateType(logCtx, c, accountID, downloadPath, fileType, uploadType, res)
		return
	}

	// 如果是图片则校验图片格式
	if fileType == "IMAGE" {
		isImage := false
		for _, suffix := range config.LocalConfig.UploadSetting.ImageSuffix {
			if fileForMat == suffix {
				isImage = true
				break
			}
		}
		// 图片格式不符合要求， 返回结果
		if !isImage {
			logger.Log.Errorf(utils.MMark(logCtx)+" isImage == false   fileForMat:%v ", fileForMat)
			c.JSON(http.StatusOK, proto.NewCommDataRsp(100002, "图片仅支持JPG/JPEG/PNG格式！", res))
			return
		}
		// 图片大小不符合要求， 返回结果
		maxImageSize := config.LocalConfig.UploadSetting.MaxImageSizeMB * 1024 * 1024
		if fileSize > maxImageSize {
			logger.Log.Errorf(utils.MMark(logCtx)+" fileSize > maxImageSize   fileSize :%v ", fileSize)
			c.JSON(http.StatusOK, proto.NewCommDataRsp(100002, fmt.Sprintf("%s%sM", "图片不得超过: ", maxImageSize), res))
			return
		}
	} else if fileType == "VIDEO" {
		isVideo := false
		for _, suffix := range config.LocalConfig.UploadSetting.VideoSuffix {
			if fileForMat == suffix {
				isVideo = true
				break
			}
		}
		// 视频格式不符合要求， 返回结果
		if !isVideo {
			logger.Log.Errorf(utils.MMark(logCtx)+" isVideo == false   fileForMat:%v ", fileForMat)
			c.JSON(http.StatusOK, proto.NewCommDataRsp(100002, "视频仅支持MP4/MOV格式！", res))
			return
		}
		// 视频大小不符合要求， 返回结果
		maxVideoSize := config.LocalConfig.UploadSetting.MaxVideoSizeMB * 1024 * 1024
		if fileSize > maxVideoSize {
			logger.Log.Errorf(utils.MMark(logCtx)+" fileSize > maxVideoSize   fileSize :%v ", fileSize)
			c.JSON(http.StatusOK, proto.NewCommDataRsp(100002, fmt.Sprintf("%s%sM", "视频不得超过: ", maxVideoSize), res))
			return
		}
	} else if fileType == "AUDIO" {
		isAudio := false
		for _, suffix := range config.LocalConfig.UploadSetting.AudioSuffix {
			if fileForMat == suffix {
				isAudio = true
				break
			}
		}
		// 音频格式校验
		if !isAudio {
			logger.Log.Errorf(utils.MMark(logCtx)+" isAideo == false   fileForMat:%v ", fileForMat)
			c.JSON(http.StatusOK, proto.NewCommDataRsp(100002, "音频仅支持MP3/MAV格式！", res))
			return
		}
		// 音频文件大小校验
		maxAudioSize := config.LocalConfig.UploadSetting.MaxAudioSizeMB * 1024 * 1024
		if fileSize > maxAudioSize {
			logger.Log.Errorf(utils.MMark(logCtx)+" fileSize > maxAudioSize   fileSize :%v ", fileSize)
			c.JSON(http.StatusOK, proto.NewCommDataRsp(100002, fmt.Sprintf("%s%sM", "音频不得超过: ", maxAudioSize), res))
			return
		}
	}

	// 上传至bos
	bosskey := fmt.Sprintf(MaterialImageBosKeyFmt, uploadType, time.Now().Format("2006-01-02"), accountID, path.Base(downloadPath))
	uploadUrl, err := RetryUploadBosServiceFromFileByBosKey(logCtx, bosskey, downloadPath)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"RetryUploadBosServiceFromFileByBosKey faild err:%v", err)
		c.JSON(http.StatusOK, proto.NewCommDataRsp(100003, "图片上传失败", res))
		return
	}

	// 图片审核能力
	if config.LocalConfig.UploadSetting.IsCensor {
		if fileType == "IMAGE" {
			err = censor(logCtx, "", uploadUrl, enum.MaterialImage)
		} else if fileType == "VIDEO" {
			err = censor(logCtx, "", uploadUrl, enum.MaterialVideo)
		} else if fileType == "AUDIO" {
			err = censor(logCtx, "", uploadUrl, enum.MaterialAudio)
		}
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+"Failed to censor file, err:%v", err)
			c.JSON(http.StatusOK, proto.NewCommDataRsp(100004, "审核失败", res))
			return
		}
	}

	// 图片url编码
	encodeUrl, err := encodeUrl(uploadUrl)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"Failed to encode url, err:%v", err)
		c.JSON(http.StatusOK, proto.NewCommDataRsp(10005, "图片编码失败", res))
		return
	}

	// 图片处理完成，返回结果
	res.Url = encodeUrl
	newFileName := path.Base(downloadPath)
	ext := path.Ext(newFileName)
	res.FileId = newFileName[:len(newFileName)-len(ext)]
	res.Type = uploadType
	logger.Log.Info(utils.MMark(logCtx)+"upload success:%v", uploadUrl)
	c.JSON(http.StatusOK, proto.NewSuccessRsp(res))
}

func handleVideoTranslateType(logCtx context.Context, c *gin.Context, accountID string, downloadPath string, fileType string, uploadType string, res *proto.ImageConverterResponse) {
	if !strings.Contains(fileType, "VIDEO") && !strings.Contains(fileType, "video") {
		logger.Log.Errorf(utils.MMark(logCtx)+"handleVideoTranslateType fileType is  not VIDEO, fileType:%s", fileType)
		c.JSON(http.StatusOK, proto.NewCommDataRsp(100002, "fileType is not VIDEO,", res))
		return
	}
	currentTime := time.Now().Format("2006-01-02")
	// 上传至bos
	bosskey := fmt.Sprintf(MaterialImageBosKeyFmt, uploadType, accountID, currentTime, path.Base(downloadPath))
	uploadUrl, err := RetryUploadBosServiceFromFileByBosKey(logCtx, bosskey, downloadPath)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"handleVideoTranslateType file upload faild err:%v", err)
		c.JSON(http.StatusOK, proto.NewCommDataRsp(100003, "file upload failed", res))
		return
	}
	logger.Log.Infof(utils.MMark(logCtx)+"handleVideoTranslateType  upload success:%v", uploadUrl)

	encodeFileUrl, err := encodeUrl(uploadUrl)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"handleVideoTranslateType Failed to encode url, err:%v", err)
		c.JSON(http.StatusOK, proto.NewCommDataRsp(10005, "Failed to encode url", res))
		return
	}
	logger.Log.Infof(utils.MMark(logCtx)+"handleVideoTranslateType  encodeUrl success:%v", encodeUrl)

	// 视频需要获取缩略图
	uuidStr := uuid.New().String()
	thumbnailPath, err := materialProcess(logCtx, downloadPath, uuidStr, enum.FfmpegProcessTypeThumbnailURL)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"Failed to generate thumbnail url, err:%v", err)
		c.JSON(http.StatusOK, proto.NewCommDataRsp(10003, "thumbnail generate failed!", res))
		return
	}

	defer os.Remove(thumbnailPath)

	thumbnailBosskey := fmt.Sprintf(MaterialImageBosKeyFmt, uploadType, accountID, currentTime, path.Base(thumbnailPath))

	thumbnailUrl, err := RetryUploadBosServiceFromFileByBosKey(logCtx, thumbnailBosskey, thumbnailPath)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"Failed to upload thumbnail, err:%v", err)
		c.JSON(http.StatusOK, proto.NewCommDataRsp(10003, "Failed to upload thumbnail!", res))
		return
	}

	encodeThumbnailUrl, err := encodeUrl(thumbnailUrl)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"Failed to encodeUrl thumbnailUrl, err:%v", err)
		c.JSON(http.StatusOK, proto.NewCommDataRsp(10003, "Failed to encodeUrl thumbnailUrl!", res))
		return
	}
	// 图片处理完成，返回结果
	res.Url = encodeFileUrl
	res.Thumbnail = encodeThumbnailUrl
	newFileName := path.Base(downloadPath)
	ext := path.Ext(newFileName)
	res.FileId = newFileName[:len(newFileName)-len(ext)]
	res.Type = uploadType
	logger.Log.Info(utils.MMark(logCtx)+"handleVideoTranslateType upload success:%v", uploadUrl)
	c.JSON(http.StatusOK, proto.NewSuccessRsp(res))
}

// 获取视频的时长
func GetDurationFromURL(url string) (int64, error) {
	ctx, cancel := context.WithTimeout(context.Background(), 120*time.Second)
	defer cancel()

	data, err := ffprobe.ProbeURL(ctx, url)
	if err != nil {
		return 0, fmt.Errorf("ffprobe GetDurationFromURL error: %w", err)
	}
	return data.Format.Duration().Milliseconds(), nil
}

func UploadFileSign(c *gin.Context) {
	req := &proto.UploadFileSignReq{}
	if err := c.ShouldBindJSON(req); err != nil {
		logger.Log.Errorf("UploadFileSign bind param fail, err: %v", err)
		c.JSON(http.StatusOK, proto.NewCommDataRsp(100001, "param error", nil))
		return
	}
	bucket := choiceBucket(c)
	object := fmt.Sprintf("%s/%s", time.Now().Format("2006-01-02"), req.FileName)
	signedURL, err := storage.GenerateGCSV4PutObjectSignedURL(bucket, object, []string{
		"Content-Type:" + req.ContentType,
		"FileID:" + req.FileID,
	}, time.Now().Add(10*time.Minute))
	if err != nil {
		logger.Log.Errorf("UploadFileSign generate signedURL fail, err: %v", err)
		c.JSON(http.StatusOK, proto.NewCommDataRsp(530002, "generate sign fail", nil))
		return
	}
	c.JSON(http.StatusOK, proto.NewSuccessRsp(proto.UploadFileSignRsp{SignedURL: signedURL}))
}

// 这里是默认数据，其他数据从多语言库中动态获取
var regionBucketMap = map[string]string{
	"default": "xiling_us_central1_bucket",
	"中国":      "xiling_asia-southeast1_bucket",
	"荷兰":      "xiling_europe_west4_upload_buckent",
	"美国":      "xiling_us_central1_bucket",
}

// 根据客户端IP地址选择存储桶
func choiceBucket(c *gin.Context) string {
	startTime := time.Now()
	bucket := regionBucketMap["default"]
	// 从多语言服务中获取映射信息
	logCtx := context.WithValue(context.TODO(), utils.CtxKeyLogID, c.GetHeader(global.HeaderRequestID))
	i18nRBMap, err := signurli18n.GetRegionBucketMappingI18n(logCtx, i18n.GetHeaderLanguage(c))
	if err != nil {
		logger.CtxLog(c).Errorf("UploadFileSign GetRegionBucketMappingI18n fail, err: %v", err)
		return bucket
	}
	defer func() {
		logger.CtxLog(c).Infof("UploadFileSign choiceBucket end, cost: %v", time.Since(startTime))
	}()
	clientIP := c.ClientIP()
	logger.CtxLog(c).Infof("UploadFileSign clientIP: %s", clientIP)
	// 查询IP地址所属区域
	regionInfo := location.SearchByIP(clientIP)
	logger.CtxLog(c).Infof("UploadFileSign region detail: %s", regionInfo)
	// 根据区域查询对应的存储桶
	newBucket, ok := i18nRBMap[regionInfo.Country]
	if !ok {
		logger.CtxLog(c).Errorf("UploadFileSign region not found, country: %s", regionInfo.Country)
		return bucket
	}
	bucket = newBucket
	return bucket
}
