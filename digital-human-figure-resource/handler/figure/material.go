package figure

import (
	"acg-ai-go-common/gomysql"
	"acg-ai-go-common/goredis"
	"acg-ai-go-common/logger"
	"acg-ai-go-common/utils"
	"acg-ai-go-common/utils/httputil"
	"acg-ai-go-common/utils/redisproxy"
	"bytes"
	"context"
	"digital-human-figure-resource/beans/enum"
	"digital-human-figure-resource/beans/model"
	"digital-human-figure-resource/beans/proto"
	config "digital-human-figure-resource/conf"
	"digital-human-figure-resource/handler/handlerUtils"
	"digital-human-figure-resource/handler/i18n/respi18n"
	"encoding/json"
	"errors"
	"fmt"
	"image"
	"io"
	"net/http"
	"net/url"
	"os"
	"os/exec"
	"path"
	"strconv"
	"strings"
	"time"

	_ "image/jpeg" // 注册JPEG解码器

	"github.com/disintegration/imaging"
	"github.com/gabriel-vasile/mimetype"
	"github.com/gin-gonic/gin"
	"github.com/google/uuid"

	exif "github.com/dsoprea/go-exif/v3"
)

const (
	DownloadPathFmt       = CacheDir + "/%s"
	MaterialBosKeyFmt     = "%s/%s/%s" // accountID/uuid/fileName/
	ThumbnailUrlBosKeyFmt = "%s/%s"    // materialId/fileName
)

var (
	imageFileSuffix = []string{".jpg", ".gif", ".png", ".ico", ".bmp", ".jpeg"}
	videoFileSuffix = []string{".mov", ".mp4"}
	audioFileSuffix = []string{".mp3", ".wav"}
)

type FFprobeInfo struct {
	Streams []FFprobeStreamsInfo `json:"streams"`
}

type FFprobeStreamsInfo struct {
	Nb_frames string `json:"nb_frames"`
	Width     int    `json:"width"`
	Height    int    `json:"height"`
	Duration  string `json:"duration"`
}

func AddMaterial(c *gin.Context) {
	materialId := "m-" + utils.RandStringRunes(18)
	logCtx := context.WithValue(context.TODO(), utils.CtxKeyLogID, materialId)
	accountID := ""
	aid, ok := c.Get("AccountId")
	if ok {
		accountID = aid.(string)
	}
	userName := ""
	if uid, ok := c.Get("UserName"); ok {
		userName = uid.(string)
	}

	targetLanguage := c.GetHeader("Language")
	logCtx = context.WithValue(logCtx, "Language", targetLanguage)
	logger.Log.Infof(utils.MMark(logCtx)+"AddMaterial langugae in header is: %s", targetLanguage)

	cookie := c.GetHeader("Cookie")
	req := proto.AddMaterialRequest{}
	if !strings.Contains(c.Request.Header.Get("Content-Type"), "multipart/form-data") {
		logger.Log.Errorf(utils.MMark(logCtx) + "CheckFile get Header faild")
		c.JSON(http.StatusOK, proto.NewCommDataRsp(100001, respi18n.TransformResponseLocaleByDefault(logCtx, targetLanguage,
			"参数异常"), &proto.CommResponse{RequestId: utils.MMark(logCtx)}))
		return
	}

	// 绑定表单数据到结构体
	if err := c.ShouldBind(&req); err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"CheckFile bind faild, err: %v", err)
		c.JSON(http.StatusOK, proto.NewCommDataRsp(100002, respi18n.TransformResponseLocaleByDefault(logCtx,
			targetLanguage, "参数异常"), &proto.CommResponse{RequestId: utils.MMark(logCtx)}))
		return
	}

	res, code, err := addMaterial(logCtx, c, materialId, accountID, userName, cookie, req)
	if err != nil {
		c.JSON(http.StatusOK, proto.NewCommDataRsp(code, err.Error(), &proto.CommResponse{RequestId: utils.MMark(logCtx)}))
		return
	}

	c.JSON(http.StatusOK, proto.NewSuccessRsp(res))
}

func addMaterial(logCtx context.Context, c *gin.Context, materialId, accountID, userName, cookie string, req proto.AddMaterialRequest) (*proto.AddMaterialResponse, int, error) {
	targetLanguage := fmt.Sprintf("%s", logCtx.Value("Language"))
	logger.Log.Infof(utils.MMark(logCtx)+"addMaterial, lang: %s, accountID: %s,materialId: %s,fileName: %s,"+
		"fileUrl: %s", targetLanguage, accountID, materialId, req.FileName, req.FileUrl)

	if !strings.HasPrefix(req.Module, "FISSION") {
		// 生成 材料的唯一标识
		md5Str := utils.Md5(accountID + req.FileName + req.FileUrl + req.Module)
		// 获取 redis 分布式锁
		redisLockKey := fmt.Sprintf(AddMaterialKeyFmt+"_lock", handlerUtils.GetNameByRunEnv(), md5Str)
		redisLock := goredis.NewRedisLockV2(redisproxy.GetRedisProxy().Rdb, redisLockKey, AddMaterialKeyTimeDuration)
		ok, err := redisLock.Lock(context.Background())

		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+utils.RedisEsErrMsg+"addMaterial Lock key: %s error: %+v\n", redisLockKey, err)
			return nil, 200001, errors.New("redisLock.Lock error:" + err.Error())
		} else if !ok {
			logger.Log.Errorf(utils.MMark(logCtx)+"addMaterial Lock is occupied key: %+s \n", redisLockKey)
			return nil, 200001, errors.New(respi18n.TransformResponseLocaleByDefault(logCtx, targetLanguage,
				"该文件已存在，请重新上传"))
		}
		defer redisLock.Unlock(context.Background())
	}

	if req.FileUrl != "" {
		// 校验文件名与文件url的后缀
		fileNameSuffix := path.Ext(req.FileName)
		fileUrlSuffix := path.Ext(req.FileUrl)
		if fileNameSuffix != fileUrlSuffix {
			logger.Log.Errorf(utils.MMark(logCtx) + "CheckFileName faild,fileNameSuffix != fileUrlSuffixv")
			return nil, 100003, errors.New(respi18n.TransformResponseLocaleByDefault(logCtx, targetLanguage,
				"文件校验失败: 文件名后缀需与文件URL后缀一致"))
		}
	}

	// 校验文件名
	if err := validateFileName(req.FileName); err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"CheckFile validate faild, err: %v", err)
		return nil, 100003, errors.New(respi18n.TransformResponseLocaleByDefault(logCtx, targetLanguage,
			fmt.Sprintf("%s", err.Error())))
	}
	// 校验用户素材个数有没有超限
	ok, err := isValidMaterialSize(logCtx, accountID)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"CheckFile validate faild, err: %v", err)
		return nil, 100005, errors.New(respi18n.TransformResponseLocaleByDefault(logCtx, targetLanguage, "素材个数校验失败"))
	} else if !ok {
		return nil, 100006, errors.New(respi18n.TransformResponseLocaleByDefault(logCtx, targetLanguage, "素材个数超限"))
	}

	// 下载文件
	downloadPath, err := downloadMaterialFile(logCtx, req.FileUrl, req.FileName, c)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+" Failed to download file, err:%v", err)
		os.Remove(downloadPath)
		return nil, 100004, errors.New(respi18n.TransformResponseLocaleByDefault(logCtx, targetLanguage, "下载文件失败"))
	}
	defer os.Remove(downloadPath) // 删除临时文件

	// 获取文件大小
	fileSize, err := handlerUtils.GetFileSize(downloadPath)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"Failed to get file size, err:%v", err)
		return nil, 100008, errors.New(respi18n.TransformResponseLocaleByDefault(logCtx, targetLanguage, "获取文件大小失败"))
	}

	fileType, err := validateUpload(logCtx, downloadPath, fileSize)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"Failed to validate file, err:%v", err)
		return nil, 100009, errors.New(respi18n.TransformResponseLocaleByDefault(logCtx, targetLanguage,
			fmt.Sprintf("%s", err.Error())))
	}

	// 查看数据库中是否存在
	if !strings.HasPrefix(req.Module, "FISSION") {
		_, err := (&model.MaterialEntity{}).GetMaterialEntity(gomysql.DB, accountID, req.FileName, req.Module)
		if err == nil {
			return nil, 200001, errors.New(respi18n.TransformResponseLocaleByDefault(logCtx, targetLanguage,
				"该文件已存在，请重新上传"))
		}
	}

	// 获取文件信息
	ffprobeStreamsInfo, err := ExecuteFFprobe(logCtx, downloadPath, fileType)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"Failed to execute ffprobe, err:%v", err)
		return nil, 200005, errors.New(respi18n.TransformResponseLocaleByDefault(logCtx, targetLanguage,
			"获取文件信息失败，请重新上传"))
	}

	// 如果是图片,则可能需要处理图片方向
	if fileType == enum.MaterialImage {
		downloadPath, err = handleImageOrientation(logCtx, downloadPath, CacheDir)
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+"Failed to handle image orientation, err:%v", err)
			return nil, 200002, errors.New(respi18n.TransformResponseLocaleByDefault(logCtx, targetLanguage,
				"图片方向异常，请重新上传"))
		}
	}

	// 上传文件到bos
	bosskey := fmt.Sprintf(MaterialBosKeyFmt, accountID, uuid.New().String(), req.FileName)
	uploadUrl, err := RetryUploadBosServiceFromFileByBosKey(logCtx, bosskey, downloadPath)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"Failed to upload file, err:%v", err)
		return nil, 200003, errors.New(respi18n.TransformResponseLocaleByDefault(logCtx, targetLanguage, "上传文件失败"))
	}

	// 计算视频/音频的时长，单位为毫秒
	fDuration, err := strconv.ParseFloat(ffprobeStreamsInfo.Duration, 64)
	if err != nil {
		fDuration = 0.0
	}

	nDuration := int(fDuration * 1000)
	// 审核文件
	if req.IsNeedCensor {
		logger.Log.Infof(utils.MMark(logCtx)+"Start to censor file, fileUrl: %s", uploadUrl)
		err = censor(logCtx, cookie, uploadUrl, fileType)
		if err != nil {
			logger.Log.Warnf(utils.MMark(logCtx)+"Failed to censor file, err:%v", err)
			return nil, 200004, err
		}
		logger.Log.Infof(utils.MMark(logCtx)+"end to censor file, fileUrl: %s", uploadUrl)
	}

	// 判断是否需要将url转码
	if config.LocalConfig.MaterialSetting.NeedEncode {
		uploadUrl, err = encodeUrl(uploadUrl)
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+"Failed to encode url, err:%v", err)
			return nil, 200007, errors.New(respi18n.TransformResponseLocaleByDefault(logCtx, targetLanguage, "url转码失败,"+
				"请重新上传"))
		}
	}

	// 视频需要获取缩略图
	thumbnailUrl := ""
	if fileType == enum.MaterialVideo {
		uniqueTbId := "tb-" + utils.RandStringRunes(18)
		thumbnailPath, err := materialProcess(logCtx, downloadPath, uniqueTbId, enum.FfmpegProcessTypeThumbnailURL)
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+"Failed to get thumbnail url, err:%v", err)
			return nil, 200008, errors.New(respi18n.TransformResponseLocaleByDefault(logCtx, targetLanguage,
				"获取缩略图失败，请重新上传"))
		}

		defer os.Remove(thumbnailPath)

		bosskey = fmt.Sprintf(ThumbnailUrlBosKeyFmt, materialId, path.Base(thumbnailPath))
		thumbnailUrl, err = RetryUploadBosServiceFromFileByBosKey(logCtx, bosskey, thumbnailPath)
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+"Failed to upload thumbnail, err:%v", err)
			return nil, 200009, errors.New(respi18n.TransformResponseLocaleByDefault(logCtx, targetLanguage,
				"上传缩略图失败，请重新上传"))
		}
	}

	// 插入数据库
	mater := model.MaterialEntity{
		MaterialID: materialId,
		Name:       req.FileName,
		UserID:     accountID,
		Url:        uploadUrl,
		Type:       string(fileType),
		Module:     req.Module,
		Creator:    userName,
		Thumbnail:  thumbnailUrl,
		PreviewURL: "",
		Duration:   nDuration,
		Width:      ffprobeStreamsInfo.Width,
		Height:     ffprobeStreamsInfo.Height,
	}

	err = mater.CreateMaterialEntity(gomysql.DB)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"Failed to insert material, err:%v", err)
		return nil, 200010, errors.New(respi18n.TransformResponseLocaleByDefault(logCtx, targetLanguage, "插入数据库失败"))
	}

	res := &proto.AddMaterialResponse{
		CreateTime: mater.CreateTime,
		UpdateTime: mater.UpdateTime,
		MaterialId: mater.MaterialID,
		UserId:     mater.UserID,
		Name:       mater.Name,
		Url:        mater.Url,
		Type:       mater.Type,
		Module:     mater.Module,
		Creator:    mater.Creator,
		Thumbnail:  mater.Thumbnail,
		PreviewUrl: mater.PreviewURL,
		Duration:   int64(mater.Duration),
		Width:      int64(mater.Width),
		Height:     int64(mater.Height),
	}

	return res, 0, nil
}

func validateFileName(fileName string) error {
	if len(fileName) > 100 {
		return errors.New("文件名超过100个字,请重新上传")
	}

	if strings.Contains(fileName, " ") {
		return errors.New("文件名称包含空格，请重新上传")
	}

	ok, err := handlerUtils.RegexpMatches(fileName, "[/<>;:*?\\\\]|(\\00)")
	if err != nil || ok {
		return errors.New("文件名称包含非法字符，请重新上传")
	}
	isSuffix := false

	fileExtWithDot := path.Ext(fileName)
	fileExtWithDot = strings.ToLower(fileExtWithDot)
	for _, v := range imageFileSuffix {
		if v == fileExtWithDot {
			isSuffix = true
			return nil
		}
	}

	for _, v := range videoFileSuffix {
		if v == fileExtWithDot {
			isSuffix = true
			return nil
		}
	}

	for _, v := range audioFileSuffix {
		if v == fileExtWithDot {
			isSuffix = true
			return nil
		}
	}

	if !isSuffix {
		return errors.New("文件格式不合法，请重新上传")
	}

	return nil
}

// 校验上传的文件
func validateUpload(logCtx context.Context, filename string, fileSize int64) (enum.MaterialType, error) {
	// 打开文件
	file, err := os.Open(filename) // 替换为你的文件名
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"Failed to open file, err:%v", err)
		return "", errors.New("打开文件失败")
	}
	defer file.Close() // 确保在函数结束时关闭文件

	// 检测 MIME 类型
	mime, err := mimetype.DetectReader(file)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"Failed to detect file type, err:%v", err)
		return "", errors.New("无法识别文件类型")
	}

	logger.Log.Infof(utils.MMark(logCtx)+"File type is %s", mime.String())
	// 分割文件类型
	contentSplit := strings.Split(mime.String(), "/")
	if len(contentSplit) != 2 {
		return "", errors.New("无法识别文件类型")
	}
	fileType := strings.ToUpper(contentSplit[0])
	fileFormat := contentSplit[1]

	// 获取文件类型枚举
	var materialType enum.MaterialType
	switch fileType {
	case "IMAGE":
		materialType = enum.MaterialImage
	case "AUDIO":
		materialType = enum.MaterialAudio
	case "VIDEO":
		materialType = enum.MaterialVideo
	default:
		return "", errors.New("不支持上传此类型文件")
	}

	// 校验文件格式
	if materialType == enum.MaterialImage && !isValidImageFormat(fileFormat) {
		return "", errors.New("不支持上传此类型图片文件")
	}

	// 音频和视频通过MIME类型校验不是很准确，暂不校验
	if materialType == enum.MaterialAudio && !isValidAudioFormat(strings.ToLower(path.Ext(filename))) {
		return "", errors.New("不支持上传此类型音频文件")
	}
	if materialType == enum.MaterialVideo && !isValidVideoFormat(strings.ToLower(path.Ext(filename))) {
		return "", errors.New("不支持上传此类型视频文件")
	}

	// 限制文件大小
	mbLength := int64(1024 * 1024)
	if materialType == enum.MaterialImage && fileSize > config.LocalConfig.MaterialSetting.MaxImageSizeMB*mbLength {
		return "", errors.New("上传文件大小已超过限制")
	}
	if materialType == enum.MaterialVideo && fileSize > config.LocalConfig.MaterialSetting.MaxVideoSizeMB*mbLength {
		return "", errors.New("上传文件大小已超过限制")
	}
	if materialType == enum.MaterialAudio && fileSize > config.LocalConfig.MaterialSetting.MaxAudioSizeMB*mbLength {
		return "", errors.New("上传文件大小已超过限制")
	}

	return materialType, nil
}

func isValidImageFormat(fileFormat string) bool {
	for _, v := range imageFileSuffix {
		if strings.Contains(v, fileFormat) {
			return true
		}
	}
	return false
}

func isValidAudioFormat(fileFormat string) bool {
	for _, v := range audioFileSuffix {
		if strings.Contains(v, fileFormat) {
			return true
		}
	}
	return false
}

func isValidVideoFormat(fileFormat string) bool {
	for _, v := range videoFileSuffix {
		if strings.Contains(v, fileFormat) {
			return true
		}
	}
	return false
}

// 提取图像方向信息
func extractOrientation(logCtx context.Context, value interface{}) int {
	switch v := value.(type) {
	case int8:
		return int(v)
	case int:
		return v
	case int16:
		return int(v)
	case int32:
		return int(v)
	case int64:
		return int(v)
	case uint8:
		return int(v)
	case uint:
		return int(v)
	case uint16:
		return int(v)
	case uint32:
		return int(v)
	case uint64:
		return int(v)
	case []uint16:
		if len(v) == 0 {
			return 1
		}
		return int(v[0])
	default:
		logger.Log.Errorf(utils.MMark(logCtx)+"Unsupported orientation type: %T", value)
		return 1
	}
}

func handleImageOrientation(logCtx context.Context, imagePath, outputDir string) (string, error) {
	// 打开图像文件
	file, err := os.Open(imagePath)
	if err != nil {
		return "", err
	}
	defer file.Close()

	// 读取文件数据
	data := make([]byte, 64*1024)
	_, err = file.Read(data)
	if err != nil {
		return imagePath, nil
	}

	// 提取 EXIF 数据
	rawExif, err := exif.SearchAndExtractExif(data)
	if err != nil {
		return imagePath, nil
	}

	opt := exif.ScanOptions{}
	// 从原始 EXIF 数据中解析 EXIF 索引
	ets, _, err := exif.GetFlatExifData(rawExif, &opt)
	if err != nil {
		return imagePath, nil
	}

	orientation := 1
	for _, et := range ets {
		logger.Log.Infof(utils.MMark(logCtx)+"EXIF tag: %s, Value: %v", et.TagName, et.Value)
		if et.IfdPath == "IFD" && et.TagName == "Orientation" && et.Value != nil {
			orientation = extractOrientation(logCtx, et.Value)
			break
		}
	}

	logger.Log.Infof(utils.MMark(logCtx)+"Image orientation: %d", orientation)
	file.Seek(0, io.SeekStart)
	// 打开并解码图像
	img, _, err := image.Decode(file)
	if err != nil {
		return "", err
	}

	var transformedImg image.Image
	switch orientation {
	case 1: // 正常方向，不需要旋转或翻转
		//transformedImg = img
		logger.Log.Infof(utils.MMark(logCtx) + "Image orientation is normal")
		return imagePath, nil
	case 2: // 水平翻转
		transformedImg = imaging.FlipH(img)
		logger.Log.Infof(utils.MMark(logCtx) + "Image orientation is horizontal flip")
	case 3: // 顺时针旋转180度
		transformedImg = imaging.Rotate180(img)
		logger.Log.Infof(utils.MMark(logCtx) + "Image orientation is rotate 180")
	case 4: // 垂直翻转
		transformedImg = imaging.FlipV(img)
		logger.Log.Infof(utils.MMark(logCtx) + "Image orientation is vertical flip")
	case 5: // 顺时针旋转90度并水平翻转
		transformedImg = imaging.Rotate90(img)
		transformedImg = imaging.FlipH(transformedImg)
		logger.Log.Infof(utils.MMark(logCtx) + "Image orientation is rotate 90 and horizontal flip")
	case 6: // 顺时针旋转90度
		transformedImg = imaging.Rotate90(img)
		logger.Log.Infof(utils.MMark(logCtx) + "Image orientation is rotate 90")
	case 7: // 逆时针旋转90度并水平翻转
		transformedImg = imaging.Rotate270(img)
		transformedImg = imaging.FlipH(transformedImg)
		logger.Log.Infof(utils.MMark(logCtx) + "Image orientation is rotate 270 and horizontal flip")
	case 8: // 逆时针旋转90度
		transformedImg = imaging.Rotate270(img)
		logger.Log.Infof(utils.MMark(logCtx) + "Image orientation is rotate 270")
	default:
		logger.Log.Errorf("Unknown image orientation: %d", orientation)
		return imagePath, nil
	}
	newFileName := uuid.New().String() + path.Ext(imagePath)
	// 创建输出文件路径
	outputFile := outputDir + "/" + newFileName

	// 保存处理后的图像
	err = imaging.Save(transformedImg, outputFile)
	if err != nil {
		return "", err
	}

	return outputFile, nil
}

func censor(logCtx context.Context, cookie, url string, fileType enum.MaterialType) error {
	httpclient := httputil.NewRetryHTTPClient(15*time.Minute, 3)
	jsonData := make([]byte, 0)
	var err error
	requstUrl := ""
	switch fileType {
	case enum.MaterialImage:
		var info = &proto.RiskControlImageRequery{
			ImgUrls: []string{url},
		}
		// 序列化请求数据
		jsonData, err = json.Marshal(info)
		requstUrl = "http://dhlive-external-api:8080/api/digitalhuman/external/riskcontrol/v1/figure/images"
		if len(cookie) == 0 {
			requstUrl = "http://dhlive-external-api:8080/api/digitalhuman/external/riskcontrol/v1/internal/figure/images"
		}
	case enum.MaterialVideo:
		var info = &proto.RiskControlShortVideoSubmitRequest{
			VideoURL: url,
		}
		// 序列化请求数据
		jsonData, err = json.Marshal(info)
		requstUrl = "http://dhlive-external-api:8080/api/digitalhuman/external/riskcontrol/v1/figure/video"
		if len(cookie) == 0 {
			requstUrl = "http://dhlive-external-api:8080/api/digitalhuman/external/riskcontrol/v1/internal/figure/video"
		}
	case enum.MaterialAudio:
		var info = &proto.RiskControlShortAudioRequest{
			AudioUrl: url,
		}
		// 序列化请求数据
		jsonData, err = json.Marshal(info)
		requstUrl = "http://dhlive-external-api:8080/api/digitalhuman/external/riskcontrol/v1/figure/audio"
		if len(cookie) == 0 {
			requstUrl = "http://dhlive-external-api:8080/api/digitalhuman/external/riskcontrol/v1/internal/figure/audio"
		}
	default:
		logger.Log.Errorf("censor type error")
		return errors.New("文件类型错误")
	}

	if err != nil {
		logger.Log.Errorf("censor marshal error: %v", err)
		return errors.New("序列化数据失败")
	}

	targetLanguage := fmt.Sprintf("%s", logCtx.Value("Language"))
	logger.Log.Infof(utils.MMark(logCtx)+"censor language: %s", targetLanguage)

	headers := make(map[string]string)
	headers["Content-Type"] = "application/json"
	headers["Cookie"] = cookie
	headers["Language"] = targetLanguage

	resp, err := httpclient.DoRequest(logCtx, http.MethodPost, requstUrl, headers, bytes.NewBuffer(jsonData))
	if err != nil {
		logger.Log.Errorf("censor http request error: %v", err)
		return errors.New("请求审核接口失败")
	}
	logger.Log.Infof(utils.MMark(logCtx)+"censor response: %s", string(resp))

	errmsg := ""

	switch fileType {
	case enum.MaterialImage:
		res := &proto.RiskControlImageResponse{}
		err = json.Unmarshal(resp, res)
		if err != nil {
			logger.Log.Errorf("censor unmarshal error: %v", err)
			return err
		}
		// 先判断返回结果是否有报错
		if res.Code != 0 {
			errmsg = res.Message.Global
			break
		}

		for _, v := range res.Result.DataList {
			if v.ErrorCode != 0 {
				errmsg += v.ErrorMsg
				errmsg += ";"
				continue
			}

			if v.ConclusionType == 2 {
				for _, vv := range v.Data {
					if vv.ErrorCode != 0 {
						errmsg += vv.ErrorMsg
						errmsg += ";"
						continue
					}
					errmsg += vv.Msg
					errmsg += ";"
				}
			}
		}

	case enum.MaterialVideo:
		res := &proto.RiskControlShortVideoResponse{}
		err = json.Unmarshal(resp, res)
		if err != nil {
			logger.Log.Errorf("censor unmarshal error: %v", err)
			return errors.New("审核结果解析失败")
		}
		// 先判断返回结果是否有报错
		if res.Code != 0 {
			errmsg = res.Message.Global
			break
		}

		if res.Result.Data.ErrorCode != 0 {
			errmsg = res.Result.Data.ErrorMsg
		} else if res.Result.Data.ConclusionType == 2 {
			for _, v := range res.Result.Data.ConclusionTypeGroupInfos {
				errmsg += v.Msg
				errmsg += ";"
			}
		}
	case enum.MaterialAudio:
		res := &proto.RiskControlShortAudioResponse{}
		err = json.Unmarshal(resp, res)
		if err != nil {
			logger.Log.Errorf("censor unmarshal error: %v", err)
			return errors.New("审核结果解析失败")
		}
		// 先判断返回结果是否有报错
		if res.Code != 0 {
			errmsg = res.Message.Global
			break
		}

		if res.Result.Data.ErrorCode != 0 {
			errmsg = res.Result.Data.ErrorMsg
		} else if res.Result.Data.ConclusionType == 2 {
			for _, v := range res.Result.Data.ConclusionTypeGroupInfos {
				errmsg += v.Msg
				errmsg += ";"
				for _, word := range v.Words {
					errmsg += word
					errmsg += ";"
				}
			}
		}
	}

	if len(errmsg) <= 0 {
		return nil
	}

	return errors.New(errmsg)
}

// ExecuteFFprobe 执行ffprobe命令并返回编解码器名称、采样率和错误信息
func ExecuteFFprobe(logCtx context.Context, filePath string, materialType enum.MaterialType) (FFprobeStreamsInfo, error) {
	if materialType == enum.MaterialVideo {
		return ExecuteFFprobeVideoAndImage(logCtx, filePath)
	} else if materialType == enum.MaterialImage {
		return ExecuteFFprobeVideoAndImage(logCtx, filePath)
	} else if materialType == enum.MaterialAudio {
		return ExecuteFFprobeAudio(logCtx, filePath)
	} else {
		return FFprobeStreamsInfo{}, fmt.Errorf("material type error")
	}
}

// ExecuteFFprobe 执行ffprobe命令并返回编解码器名称、采样率和错误信息
func ExecuteFFprobeVideoAndImage(logCtx context.Context, filePath string) (FFprobeStreamsInfo, error) {
	// 构建ffprobe命令
	cmd := exec.Command("ffprobe", "-v", "error", "-select_streams", "v:0", "-show_entries", "stream=duration,width,height,nb_frames", "-of", "json", filePath)

	// 创建缓冲区来保存命令的输出和错误
	var stdoutBuf, stderrBuf bytes.Buffer
	cmd.Stdout = &stdoutBuf
	cmd.Stderr = &stderrBuf
	// 运行命令
	err := cmd.Run()
	// 获取命令的输出和错误信息
	stdout := stdoutBuf.String()
	stderr := stderrBuf.String()
	if err != nil {
		return FFprobeStreamsInfo{}, fmt.Errorf("ffprobe error: %s, stderr: %s", err.Error(), stderr)
	}
	logger.Log.Infof(utils.MMark(logCtx)+"ffprobe stdout: %s", stdout)
	// 解析ffprobe的输出
	var info FFprobeInfo
	err = json.Unmarshal([]byte(stdout), &info)
	if err != nil {
		return FFprobeStreamsInfo{}, fmt.Errorf("unmarshal ffprobe error: %s", err.Error())
	}
	if len(info.Streams) == 0 {
		return FFprobeStreamsInfo{}, fmt.Errorf("no ffprobe info")
	}
	return info.Streams[0], nil
}

// ExecuteFFprobe 执行ffprobe命令并返回编解码器名称、采样率和错误信息
func ExecuteFFprobeAudio(logCtx context.Context, filePath string) (FFprobeStreamsInfo, error) {
	// 构建ffprobe命令
	cmd := exec.Command("ffprobe", "-v", "error", "-select_streams", "a:0", "-show_entries", "stream=duration,width,height,nb_frames", "-of", "json", filePath)

	// 创建缓冲区来保存命令的输出和错误
	var stdoutBuf, stderrBuf bytes.Buffer
	cmd.Stdout = &stdoutBuf
	cmd.Stderr = &stderrBuf
	// 运行命令
	err := cmd.Run()
	// 获取命令的输出和错误信息
	stdout := stdoutBuf.String()
	stderr := stderrBuf.String()
	if err != nil {
		return FFprobeStreamsInfo{}, fmt.Errorf("ffprobe error: %s, stderr: %s", err.Error(), stderr)
	}
	logger.Log.Infof(utils.MMark(logCtx)+"ffprobe stdout: %s", stdout)
	// 解析ffprobe的输出
	var info FFprobeInfo
	err = json.Unmarshal([]byte(stdout), &info)
	if err != nil {
		return FFprobeStreamsInfo{}, fmt.Errorf("unmarshal ffprobe error: %s", err.Error())
	}
	if len(info.Streams) == 0 {
		return FFprobeStreamsInfo{}, fmt.Errorf("no ffprobe info")
	}
	return info.Streams[0], nil
}

func encodeUrl(fileUrl string) (string, error) {
	// 解析 URL
	parsedURL, err := url.Parse(fileUrl)
	if err != nil {
		fmt.Println("Error parsing URL:", err)
		return "", err
	}

	// 从 URL 的 Path 获取文件名
	fileName := path.Base(parsedURL.Path)
	// 对文件名进行 URL 编码
	encodedFileName := url.QueryEscape(fileName)
	encodedFileName = strings.Replace(encodedFileName, "+", "%20", -1)
	// 替换原始 URL 中的文件名
	return strings.Replace(fileUrl, fileName, encodedFileName, 1), nil
}

func materialProcess(logCtx context.Context, filePath, uniqueId string, ffmpegType enum.FfmpegProcessType) (string, error) {
	switch ffmpegType {
	case enum.FfmpegProcessTypeAudioURL:
	case enum.FfmpegProcessTypePreviewVideo:
	case enum.FfmpegProcessTypeThumbnailURL:
		return getThumbnail(logCtx, filePath, uniqueId)
	case enum.FfmpegProcessTypeRecode:
	}
	return "", nil
}

func getThumbnail(logCtx context.Context, filePath, uniqueId string) (string, error) {
	outfilepath := CacheDir + "/" + uniqueId + ".png"
	// 构建ffprobe命令
	cmd := exec.Command("ffmpeg", "-i", filePath, "-vframes", "1", "-f", "image2", outfilepath)

	// 创建缓冲区来保存命令的输出和错误
	var stdoutBuf, stderrBuf bytes.Buffer
	cmd.Stdout = &stdoutBuf
	cmd.Stderr = &stderrBuf
	// 运行命令
	err := cmd.Run()
	// 获取命令的输出和错误信息
	stdout := stdoutBuf.String()
	stderr := stderrBuf.String()
	if err != nil {
		return "", fmt.Errorf("ffmpeg error: %s, stderr: %s", err.Error(), stderr)
	}
	logger.Log.Infof(utils.MMark(logCtx)+"ffmpeg stdout: %s", stdout)
	return outfilepath, nil
}

func downloadMaterialFile(logCtx context.Context, fileUrl, fileName string, c *gin.Context) (string, error) {
	err := os.MkdirAll(CacheDir, 0755)
	if err != nil {
		logger.Log.Errorf("downloadMaterialFile MkdirAll() errer=%v", err)
		return "", errors.New("mkdir error:" + err.Error())
	}
	newFileName := uuid.New().String() + path.Ext(fileName)
	// 创建文件
	downloadPath := fmt.Sprintf(DownloadPathFmt, newFileName)
	outFile, err := os.Create(downloadPath)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"Failed to create output file, err:%v", err)
		return downloadPath, err
	}
	defer outFile.Close()

	if fileUrl != "" {
		err = retrydownloadFileFormUrl(logCtx, fileUrl, downloadPath)
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+" Failed to download file from url, err:%v", err)
			return downloadPath, err
		}
	} else {
		// 处理上传的文件
		reqfile, _, err := c.Request.FormFile("file")
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+" get file faild, err: %v", err)
			return downloadPath, err
		}
		defer reqfile.Close()

		// 将请求中的文件内容复制到新文件中
		_, err = io.Copy(outFile, reqfile)
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+" Failed to copy file content, err:%v", err)
			return downloadPath, err
		}
	}

	return downloadPath, nil
}

func retrydownloadFileFormUrl(logCtx context.Context, url, filepath string) error {
	var err error
	for i := 0; i < 3; i++ {
		err = downloadFileFormUrl(logCtx, url, filepath)
		if err == nil {
			return nil
		}
		time.Sleep(1 * time.Second)
	}

	return err
}

func downloadFileFormUrl(logCtx context.Context, url, filepath string) error {
	// client := &http.Client{}
	resp, err := http.Get(url)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"downloadFileFormUrl,failed to send GET request:%d", err)
		return fmt.Errorf("downloadFileFormUrl send GET request error: %w", err)
	}
	defer resp.Body.Close()

	// 检查响应状态码是否为 200
	if resp.StatusCode != http.StatusOK {
		logger.Log.Errorf(utils.MMark(logCtx)+"downloadFileFormUrl,resp.StatusCode:%d", resp.StatusCode)
		return fmt.Errorf("downloadFileFormUrl,unexpected HTTP status: %s", resp.Status)
	}

	// 创建本地文件用于保存下载的数据
	outFile, err := os.Create(filepath)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"downloadFileFormUrl,create file filepath:%s, error:%w", filepath, err)
		return fmt.Errorf("failed to create file: %w", err)
	}
	defer outFile.Close()

	// 将响应体中的数据复制到文件中
	written, err := io.Copy(outFile, resp.Body)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"downloadFileFormUrl,copy file written:%d, error:%w", written, err)
		return fmt.Errorf("failed to write to file: %w", err)
	}
	logger.Log.Infof(utils.MMark(logCtx)+"downloadFileFormUrl,File downloaded successfully,written:%d", written)
	return nil
}

func isValidMaterialSize(logCtx context.Context, accountID string) (bool, error) {
	count, err := (&model.MaterialEntity{}).GetMaterialCount(gomysql.DB, accountID)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"GetMaterialCount err:%v", err)
		return false, err
	}

	return count < config.LocalConfig.MaterialSetting.MaterialMaxSize, nil
}
