package figure

import (
	"acg-ai-go-common/global"
	"acg-ai-go-common/gomysql"
	"acg-ai-go-common/logger"
	"acg-ai-go-common/utils"
	"context"
	"digital-human-figure-resource/beans/model"
	"digital-human-figure-resource/beans/proto"
	"digital-human-figure-resource/handler/i18n/respi18n"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

func CaptionsTemplate(c *gin.Context) {
	logCtx := context.WithValue(context.TODO(), utils.CtxKeyLogID, c.<PERSON>(global.HeaderTraceID))
	targetLanguage := c.<PERSON>eader("Language")
	if targetLanguage == "" {
		targetLanguage = "zh"
	}

	req := proto.CaptionsTemplateRequest{}
	if err := c.ShouldBind(&req); err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"CaptionsTemplate req error: %+v", err)
		c.<PERSON>(http.StatusOK, proto.NewCommDataRsp(100001, respi18n.TransformResponseLocaleByDefault(logCtx,
			targetLanguage, "内部错误"), &[]proto.CaptionsTemplateResponse{
			{
				LogId: c.GetHeader(global.HeaderTraceID),
			}}))
		return
	}
	// 构造返回数据
	res := []proto.CaptionsTemplateResponse{}

	// 获取字幕模版列表
	_, list, err := (&model.CaptionsTemplate{}).ListCaptionsTemplate(gomysql.DB, req.PageNo, req.PageSize, req.Type)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"CaptionsTemplate list error: %+v", err)
		c.JSON(http.StatusOK, proto.NewCommDataRsp(100002, respi18n.TransformResponseLocaleByDefault(logCtx,
			targetLanguage, "内部错误"), &[]proto.CaptionsTemplateResponse{{
			LogId: c.GetHeader(global.HeaderTraceID),
		}}))
		return
	}

	for _, v := range list {
		res = append(res, proto.CaptionsTemplateResponse{
			ID:         v.ID,
			Name:       v.Name,
			TemplateID: v.TemplateID,
			Type:       v.Type,
			Content:    v.Content,
			PngURL:     v.PngURL,
			VideoURL:   v.VideoURL,
			Sort:       v.Sort,
		})
	}

	c.JSON(http.StatusOK, proto.NewSuccessRsp(res))
}

func CaptionsTemplateWithInternal(c *gin.Context) {
	logCtx := context.WithValue(context.TODO(), utils.CtxKeyLogID, c.GetHeader(global.HeaderTraceID))
	targetLanguage := c.GetHeader("Language")
	if targetLanguage == "" {
		targetLanguage = "zh"
	}

	req := proto.CaptionsTemplateRequest{}
	if err := c.ShouldBind(&req); err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"CaptionsTemplate req error: %+v", err)
		c.JSON(http.StatusOK, proto.NewCommDataRsp(100001, respi18n.TransformResponseLocaleByDefault(logCtx,
			targetLanguage, "内部错误"), &[]proto.CaptionsTemplateResponse{
			{
				LogId: c.GetHeader(global.HeaderTraceID),
			}}))
		return
	}
	// 构造返回数据
	res := &proto.CaptionsTemplateResponseWithInternal{
		TotalCount: int64(0),
		List:       []proto.CaptionsTemplateResponse{},
	}

	// 获取字幕模版列表
	count, list, err := (&model.CaptionsTemplate{}).ListCaptionsTemplate(gomysql.DB, req.PageNo, req.PageSize, req.Type)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"CaptionsTemplate list error: %+v", err)
		c.JSON(http.StatusOK, proto.NewCommDataRsp(100002, respi18n.TransformResponseLocaleByDefault(logCtx,
			targetLanguage, "内部错误"), &[]proto.CaptionsTemplateResponse{{
			LogId: c.GetHeader(global.HeaderTraceID),
		}}))
		return
	}
	res.TotalCount = count

	for _, v := range list {
		res.List = append(res.List, proto.CaptionsTemplateResponse{
			ID:         v.ID,
			Name:       v.Name,
			TemplateID: v.TemplateID,
			Type:       v.Type,
			Content:    v.Content,
			PngURL:     v.PngURL,
			VideoURL:   v.VideoURL,
			Sort:       v.Sort,
		})
	}

	c.JSON(http.StatusOK, proto.NewSuccessRsp(res))
}

func UpdateCaptionsTemplate(c *gin.Context) {
	logCtx := context.WithValue(context.TODO(), utils.CtxKeyLogID, utils.RandStringRunes(16))

	req := proto.UpdateAnimationTemplateRequest{}
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"UpdateCaptionsTemplate req error: %+v", err)
		c.JSON(http.StatusOK, proto.NewCommDataRsp(100001, "内部错误", &[]proto.AnimationConfig{}))
		return
	}

	if req.ID == 0 {
		req.TemplateID = uuid.NewString()
	}

	// 构造字幕模版对象
	cap := &model.CaptionsTemplate{
		ID:         req.ID,
		Name:       req.Name,
		TemplateID: req.TemplateID,
		Type:       req.Type,
		Content:    req.Content,
		PngURL:     req.PngURL,
		VideoURL:   req.VideoURL,
		Sort:       req.Sort,
		CreatedAt:  time.Now(),
		UpdatedAt:  time.Now(),
	}

	// 更新字幕模版
	err := cap.UpdateCaptionsTemplate(gomysql.DB)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"UpdateCaptionsTemplate error: %+v", err)
		c.JSON(http.StatusOK, proto.NewCommDataRsp(100002, "内部错误", &[]proto.AnimationConfig{}))
		return
	}

	c.JSON(http.StatusOK, proto.NewSuccessRsp(nil))
}

func DeleteCaptionsTemplate(c *gin.Context) {
	logCtx := context.WithValue(context.TODO(), utils.CtxKeyLogID, utils.RandStringRunes(16))

	req := proto.DeleteCaptionsTemplateRequest{}
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"DeleteCaptionsTemplate req error: %+v", err)
		c.JSON(http.StatusOK, proto.NewCommDataRsp(100001, "内部错误", &[]proto.AnimationConfig{}))
		return
	}

	if err := (&model.CaptionsTemplate{}).DeleteCaptionsTemplate(gomysql.DB, req.IDs); err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"DeleteCaptionsTemplate error: %+v", err)
		c.JSON(http.StatusOK, proto.NewCommDataRsp(100002, "内部错误", &[]proto.AnimationConfig{}))
		return
	}
	c.JSON(http.StatusOK, proto.NewSuccessRsp(nil))
}
