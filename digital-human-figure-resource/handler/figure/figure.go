package figure

import (
	commProto "acg-ai-go-common/beans/proto"
	"acg-ai-go-common/logger"
	"acg-ai-go-common/utils"
	"acg-ai-go-common/utils/httputil"
	"context"
	"digital-human-figure-resource/beans/enum"
	"digital-human-figure-resource/beans/model"
	"digital-human-figure-resource/beans/proto"
	"digital-human-figure-resource/beans/util"
	config "digital-human-figure-resource/conf"
	"digital-human-figure-resource/mysqlclient"
	"encoding/json"
	"errors"
	"net/http"
	"net/url"
	"sort"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
)

const (
	VideoPipelineCommonCharactersUrlPath = "/api/digitalhuman/video/v1/topCharacters"
)

// 定义一个新的类型 `VisType`，它表示 2D_LITE 的各种模式
type ResourceLabelType string

// 定义常量，模拟枚举
const (
	VIS          ResourceLabelType = "2D_LITE_VIS"
	VIS_V4       ResourceLabelType = "2D_LITE_VIS_V4"
	VIS_ALPHA    ResourceLabelType = "2D_LITE_VIS_ALPHA"
	VIS_V4_ALPHA ResourceLabelType = "2D_LITE_VIS_V4_ALPHA"
	VIS_PICTURE  ResourceLabelType = "2D_LITE_VIS_PICTURE"
	ACG          ResourceLabelType = "2D_LITE_ACG"
)

type MaterialCollectInfo struct {
	ID int64 `json:"id"`
}

func FigureQuery(c *gin.Context) {
	logCtx := context.WithValue(context.TODO(), utils.CtxKeyLogID, utils.RandStringRunes(16))
	accountID := ""
	aid, ok := c.Get("AccountId")
	if ok {
		accountID = aid.(string)
	}
	//if accountID == "" {
	//    c.JSON(http.StatusOK, proto.NewCommDataRsp(100001, "参数错误", nil))
	//    return
	//}
	req := proto.FigureQueryRequst{}
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"FigureQuery", "err", err)
		c.JSON(http.StatusOK, proto.NewCommDataRsp(100001, "参数错误", nil))
		return
	}
	tags := make([]string, 0)
	for _, tag := range req.Tags {
		if strings.HasPrefix(tag, "ALL") {
			continue
		}
		tags = append(tags, tag)
	}
	// 删除掉 ALL 标签
	req.Tags = tags
	list := make([]*model.UserFigure, 0)
	listcharacters := make([]*model.CharacterConfig, 0)
	// 查看是否收藏
	collectList, err := getUseFigureCollectByIdList(logCtx, accountID)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"getUserFavoriteFigureList", "err", err)
		c.JSON(http.StatusOK, proto.NewCommDataRsp(100002, "获取用户收藏的人像列表失败", nil))
		return
	}
	logger.Log.Infof(utils.MMark(logCtx)+"collectList total len:%d", len(collectList))
	// 找出v2v4 收藏id 和老人像收藏id
	collectListByV2V4 := make([]int64, 0) // 找出v2v4 收藏id
	collectListByOld := make([]int64, 0)  // 找出老人像收藏id
	for _, v := range collectList {
		if v.MaterialType == model.MaterialTypeFigure {
			collectListByV2V4 = append(collectListByV2V4, v.MaterialID)
		} else if v.MaterialType == model.MaterialTypeOldFigure {
			collectListByOld = append(collectListByOld, v.MaterialID)
		}
	}

	logger.Log.Infof(utils.MMark(logCtx)+"collectList v2v3 total len:%d , old total len:%d", len(collectListByV2V4), len(collectListByOld))
	// 找出常用人像
	// 通过http 请求获取常用人像列表
	recentlyUsedIds := make([]int64, 0)
	recentlyUsedOldIds := make([]string, 0)
	recentlyUsedWithIds := make([]proto.FigureQueryRequstWithId, 0)
	ids, idstrs, withIds, err := getVideoCommonFigureList(logCtx, accountID)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"getVideoCommonFigureList fail, accountId:%v, err: %v", accountID, err)
		// TODO 暂时忽略获取常用人像错误
		// c.JSON(http.StatusOK, proto.NewCommDataRsp(100003, "获取常用人像列表失败", nil))
		// return
	}
	recentlyUsedIds = append(recentlyUsedIds, ids...)
	recentlyUsedOldIds = append(recentlyUsedOldIds, idstrs...)
	recentlyUsedWithIds = append(recentlyUsedWithIds, withIds...)

	// 查看是否指定id查询
	if req.FigureIdList != nil && len(req.FigureIdList) > 0 {
		// 查找指定id的人像列表无需排序 只要返回指定id的人像列表
		userlist, err := getUserFigureByIdList(logCtx, &req, collectListByV2V4, recentlyUsedIds)
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+" err:%v", err)
			c.JSON(http.StatusOK, proto.NewCommDataRsp(100002, "获取指定的人像列表失败", nil))
			return
		}

		userlistcharacters, err := getUserCharacterByIdList(logCtx, &req, collectListByOld, recentlyUsedOldIds)
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+" err:%v", err)
			c.JSON(http.StatusOK, proto.NewCommDataRsp(100003, "获取指定的人像列表失败", nil))
			return
		}

		listcharacters = append(listcharacters, userlistcharacters...)
		list = append(list, userlist...)
	} else {
		if req.Example {
			// 获取示例人像列表
			exampleList, err := getUserFigureList(logCtx, &req, "example", make([]int64, 0), recentlyUsedIds)
			if err != nil {
				logger.Log.Errorf(utils.MMark(logCtx)+"getUserFigureList", "err", err)
				c.JSON(http.StatusOK, proto.NewCommDataRsp(100002, "获取示例/体验人像列表失败", nil))
				return
			}
			list = append(list, exampleList...)
		} else if !req.System {
			// 获取用户创建的人像列表 老人像是不能创建人像的
			userlist, err := getUserFigureList(logCtx, &req, accountID, make([]int64, 0), recentlyUsedIds)
			if err != nil {
				logger.Log.Errorf(utils.MMark(logCtx)+"getUserFigureList", "err", err)
				c.JSON(http.StatusOK, proto.NewCommDataRsp(100002, "获取用户创建的人像列表失败", nil))
				return
			}
			// 获取旧2D、3D的人像列表
			userlistcharacters, err := getUserCharacterConfig(logCtx, &req, accountID, make([]int64, 0), recentlyUsedOldIds)
			if err != nil {
				logger.Log.Errorf(utils.MMark(logCtx)+"getUserCharacterConfig", "err", err)
				c.JSON(http.StatusOK, proto.NewCommDataRsp(100003, "获取用户收藏的人像列表失败", nil))
				return
			}

			listcharacters = append(listcharacters, userlistcharacters...)
			list = append(list, userlist...)
		} else if req.System && req.Collected {
			// 用户收藏的人像列表
			collectList, err := getUseFigureCollectList(logCtx, &req, collectListByV2V4)
			if err != nil {
				logger.Log.Errorf(utils.MMark(logCtx)+"getUserFavoriteFigureList err:%v", err)
				c.JSON(http.StatusOK, proto.NewCommDataRsp(100004, "获取用户收藏的人像列表失败", nil))
				return
			}

			// 获取旧2D、3D的人像列表
			userlistcharacters, err := getUserCharacterCollect(logCtx, &req, collectListByOld)
			if err != nil {
				logger.Log.Errorf(utils.MMark(logCtx)+"getUserCharacterConfig", "err", err)
				c.JSON(http.StatusOK, proto.NewCommDataRsp(100003, "获取用户收藏的人像列表失败", nil))
				return
			}

			listcharacters = append(listcharacters, userlistcharacters...)
			list = append(list, collectList...)
		} else if req.System && !req.Collected {
			// 获取公共的人像列表
			publicFigureList, err := getPublicFigureList(logCtx, &req, collectListByV2V4, recentlyUsedIds)
			if err != nil {
				logger.Log.Errorf(utils.MMark(logCtx)+"getPublicFigureList err:%v", err)
				c.JSON(http.StatusOK, proto.NewCommDataRsp(100005, "获取用户创建的人像列表失败", nil))
				return
			}

			// 获取旧2D、3D的人像列表
			userlistcharacters, err := getPublicCharacterConfig(logCtx, &req, collectListByOld, recentlyUsedOldIds)
			if err != nil {
				logger.Log.Errorf(utils.MMark(logCtx)+"getUserCharacterConfig", "err", err)
				c.JSON(http.StatusOK, proto.NewCommDataRsp(100006, "获取用户收藏的人像列表失败", nil))
				return
			}

			listcharacters = append(listcharacters, userlistcharacters...)

			list = append(list, publicFigureList...)
		} else {
			logger.Log.Errorf(utils.MMark(logCtx)+"FigureQuery err: %v", errors.New("参数错误"))
			c.JSON(http.StatusOK, proto.NewCommDataRsp(100007, "参数错误", nil))
			return
		}

	}

	reslist, err := transformUserFigureResponse(logCtx, &req, list)
	if err != nil {
		c.JSON(http.StatusOK, proto.NewCommDataRsp(100008, "转换用户创建的人像列表失败", nil))
		return
	}

	reslistcharcter, err := CharacterConfigtransformUserFigureResponse(logCtx, &req, listcharacters)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"transformUserFigureResponse err:%v", err)
		c.JSON(http.StatusOK, proto.NewCommDataRsp(100009, "转换用户收藏的人像列表失败", nil))
		return
	}

	reslist.TotalCount += reslistcharcter.TotalCount
	reslist.FigureList = append(reslist.FigureList, reslistcharcter.FigureList...)

	// 查询收藏人像需要排序
	if len(req.FigureIdList) > 0 {
	} else if req.System && req.Collected {
		reslist.FigureList = sortCollectList(logCtx, collectList, reslist.FigureList)
	} else if req.System && !req.Collected { // 公共形象需要排序
		// 常用人像排序和2d精品 2d极速 3d人像排序
		reslist.FigureList = sortRecentlyUseList(recentlyUsedWithIds, reslist.FigureList)
	} else if !req.System {
		// 用户创建的人像列表需要按照创建时间排序
		reslist.FigureList = sortUpdateTimeList(recentlyUsedWithIds, reslist.FigureList)
	}

	// 分页
	reslist.FigureList = util.Paginate(req.PageNo, req.PageSize, reslist.FigureList)
	// 将所有人像列表合并
	c.JSON(http.StatusOK, proto.NewSuccessRsp(reslist))
}

func QuerySystemList(c *gin.Context) {
	logCtx := context.WithValue(context.TODO(), utils.CtxKeyLogID, utils.RandStringRunes(16))
	req := proto.FigureQueryRequst{}
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"FigureQuery", "err", err)
		c.JSON(http.StatusOK, proto.NewCommDataRsp(100001, "参数错误", nil))
		return
	}
	tags := make([]string, 0)
	for _, tag := range req.Tags {
		if strings.HasPrefix(tag, "ALL") {
			continue
		}
		tags = append(tags, tag)
	}
	// 删除掉 ALL 标签
	req.Tags = tags
	list := make([]*model.UserFigure, 0)
	listcharacters := make([]*model.CharacterConfig, 0)

	recentlyUsedIds := make([]int64, 0)
	recentlyUsedOldIds := make([]string, 0)
	recentlyUsedWithIds := make([]proto.FigureQueryRequstWithId, 0)
	// 查看是否指定id查询
	if req.FigureIdList != nil && len(req.FigureIdList) > 0 {
		// 查找指定id的人像列表无需排序 只要返回指定id的人像列表
		userlist, err := getUserFigureByIdList(logCtx, &req, []int64{}, recentlyUsedIds)
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+" err:%v", err)
			c.JSON(http.StatusOK, proto.NewCommDataRsp(100002, "获取指定的人像列表失败", nil))
			return
		}

		userlistcharacters, err := getUserCharacterByIdList(logCtx, &req, []int64{}, recentlyUsedOldIds)
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+" err:%v", err)
			c.JSON(http.StatusOK, proto.NewCommDataRsp(100003, "获取指定的人像列表失败", nil))
			return
		}
		listcharacters = append(listcharacters, userlistcharacters...)
		list = append(list, userlist...)
	} else {
		// 获取公共的人像列表
		publicFigureList, err := getPublicFigureList(logCtx, &req, []int64{}, recentlyUsedIds)
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+"getPublicFigureList err:%v", err)
			c.JSON(http.StatusOK, proto.NewCommDataRsp(100005, "获取用户创建的人像列表失败", nil))
			return
		}
		// 获取旧2D、3D的人像列表
		userlistcharacters, err := getPublicCharacterConfig(logCtx, &req, []int64{}, recentlyUsedOldIds)
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+"getUserCharacterConfig", "err", err)
			c.JSON(http.StatusOK, proto.NewCommDataRsp(100006, "获取用户收藏的人像列表失败", nil))
			return
		}
		listcharacters = append(listcharacters, userlistcharacters...)
		list = append(list, publicFigureList...)
	}

	reslist, err := transformUserFigureResponse(logCtx, &req, list)
	if err != nil {
		c.JSON(http.StatusOK, proto.NewCommDataRsp(100008, "转换用户创建的人像列表失败", nil))
		return
	}

	reslistcharcter, err := CharacterConfigtransformUserFigureResponse(logCtx, &req, listcharacters)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"transformUserFigureResponse err:%v", err)
		c.JSON(http.StatusOK, proto.NewCommDataRsp(100009, "转换用户收藏的人像列表失败", nil))
		return
	}

	reslist.TotalCount += reslistcharcter.TotalCount
	reslist.FigureList = append(reslist.FigureList, reslistcharcter.FigureList...)

	// 常用人像排序和2d精品 2d极速 3d人像排序
	reslist.FigureList = sortRecentlyUseList(recentlyUsedWithIds, reslist.FigureList)

	// 分页
	reslist.FigureList = util.Paginate(req.PageNo, req.PageSize, reslist.FigureList)
	// 将所有人像列表合并
	c.JSON(http.StatusOK, proto.NewSuccessRsp(reslist))
}

func getUserFigureList(logCtx context.Context, req *proto.FigureQueryRequst, accountID string, collectList []int64, recentlyUsedIds []int64) ([]*model.UserFigure, error) {
	if len(accountID) == 0 {
		return []*model.UserFigure{}, nil
	}
	// 查找用户创建的人像列表
	userFigureList, err := (&model.UserFigure{}).GetUserFigureList(logCtx,
		mysqlclient.DbMap[mysqlclient.StarLightDBName], accountID, req, recentlyUsedIds, collectList)
	if err != nil {
		return nil, err
	}
	logger.Log.Infof(utils.MMark(logCtx)+"getUserFigureList userFigureList size : %d", len(userFigureList))
	return userFigureList, nil
}

func getUseFigureCollectList(logCtx context.Context, req *proto.FigureQueryRequst, collectList []int64) ([]*model.UserFigure, error) {
	figureCollectList, err := (&model.UserFigure{}).GetUserCollectList(logCtx,
		mysqlclient.DbMap[mysqlclient.StarLightDBName], collectList, req)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"getUserFavoriteFigureList", "err", err)
		return nil, err
	}
	logger.Log.Infof(utils.MMark(logCtx)+"getUserFavoriteFigureList userFigureList size : %d", len(figureCollectList))
	return figureCollectList, nil
}

func getPublicFigureList(logCtx context.Context, req *proto.FigureQueryRequst, collectList []int64, recentlyUsedIds []int64) ([]*model.UserFigure, error) {
	// 查找用户创建的人像列表
	userFigureList, err := (&model.UserFigure{}).GetPublicFigureList(logCtx,
		mysqlclient.DbMap[mysqlclient.StarLightDBName], req, recentlyUsedIds, collectList)
	if err != nil {
		return nil, err
	}

	logger.Log.Infof(utils.MMark(logCtx)+"getPublicFigureList userFigureList size : %d", len(userFigureList))
	return userFigureList, nil
}

func getUseFigureCollectByIdList(logCtx context.Context, accountID string) ([]*model.UserCollectMapping, error) {
	if len(accountID) == 0 {
		return []*model.UserCollectMapping{}, nil
	}
	// 获取用户收藏的人像列表
	collectList, err := (&model.UserCollectMapping{}).GetUserCollectListByMaterialType(mysqlclient.DbMap[mysqlclient.StarLightDBName], accountID)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"getUserFavoriteFigureList", "err", err)
		return nil, err
	}

	return collectList, nil
}

func getUserFigureByIdList(logCtx context.Context, req *proto.FigureQueryRequst, collectList []int64, recentlyUsedIds []int64) ([]*model.UserFigure, error) {
	ids := make([]int64, 0)
	for _, v := range req.FigureIdList {
		if v.FigureType == enum.FigureTypeFigure {
			ids = append(ids, v.Id)
		}
	}

	if len(ids) == 0 {
		return make([]*model.UserFigure, 0), nil
	}

	// 查找用户创建的人像列表
	userFigureList, err := (&model.UserFigure{}).GetUserFigureByIdList(logCtx,
		mysqlclient.DbMap[mysqlclient.StarLightDBName], ids, recentlyUsedIds, collectList)
	if err != nil {
		return nil, err
	}
	logger.Log.Infof(utils.MMark(logCtx)+"getUserFigureList userFigureList size : %d", len(userFigureList))
	return userFigureList, nil
}

func getVideoCommonFigureList(logCtx context.Context, accountID string) ([]int64, []string, []proto.FigureQueryRequstWithId, error) {
	if len(accountID) == 0 {
		return []int64{}, []string{}, []proto.FigureQueryRequstWithId{}, nil
	}
	resslice := make([]int64, 0)
	resStrSlice := make([]string, 0)
	resWithId := make([]proto.FigureQueryRequstWithId, 0)
	headers := make(map[string]string)
	headers["Content-Type"] = "application/json"

	query := url.Values{}
	query.Set("userId", accountID)
	url := config.LocalConfig.VideoPipelineSetting.BaseUrl + VideoPipelineCommonCharactersUrlPath + "?" + query.Encode()
	// 获取公共的人像列表
	retryHttpClient := httputil.NewRetryHTTPClient(1*time.Minute, 3)
	res, err := retryHttpClient.DoRequest(logCtx, http.MethodGet, url, headers, nil)
	if err != nil {
		return resslice, resStrSlice, resWithId, err
	}
	logger.Log.Infof(utils.MMark(logCtx)+"getVideoCommonFigureList res: %s", res)

	resFigures := proto.RecentlyUsedResponse{}
	err = json.Unmarshal(res, &resFigures)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"getVideoCommonFigureList res: %s err:%v", string(res), err)
		return resslice, resStrSlice, resWithId, err
	}

	if resFigures.Code != 0 {
		return resslice, resStrSlice, resWithId, errors.New(resFigures.Message.Global)
	}
	for _, v := range resFigures.Result {
		withId := proto.FigureQueryRequstWithId{}
		id, err := strconv.Atoi(v.CharacterId)
		if err != nil {
			resStrSlice = append(resStrSlice, v.CharacterId)
			withId.CharacterID = v.CharacterId
			withId.FigureType = enum.FigureTypeOldFigure
		} else {
			resslice = append(resslice, int64(id))
			withId.Id = int64(id)
			withId.FigureType = enum.FigureTypeFigure
		}
		resWithId = append(resWithId, withId)
	}
	logger.Log.Infof(utils.MMark(logCtx)+"getVideoCommonFigureList filterresslice: %v resStrSlice: %v", resslice, resStrSlice)
	return resslice, resStrSlice, resWithId, nil
}

func transformUserFigureResponse(logCtx context.Context, req *proto.FigureQueryRequst, list []*model.UserFigure) (proto.FigureQueryResponse, error) {
	response := proto.FigureQueryResponse{
		PageNo:     req.PageNo,
		PageSize:   req.PageSize,
		TotalCount: len(list),
		FigureList: make([]*proto.FigureQueryDataItem, 0),
	}
	for _, v := range list {
		status := v.Status
		if status == string(enum.WaitCheck) || status == string(enum.Precessing) || status == string(enum.PreCheck) ||
			status == string(enum.FigureCreate) || status == string(enum.MakeTemplateVideo) {
			v.Status = string(enum.Running)
		}
		consumeTime := 0
		if v.ResourceLabel == string(VIS) {
			consumeTime = 30
		} else if v.ResourceLabel == string(VIS_V4) {
			consumeTime = 240
		}
		info := ""
		if status == string(enum.Failed) {
			info = v.ResultMessage
		}
		effects := proto.Effects{}
		if len(v.Effects) > 0 {
			err := json.Unmarshal([]byte(v.Effects), &effects)
			if err != nil {
				logger.Log.Errorf(utils.MMark(logCtx)+"Effects id: %d v.Effects: %s err:%v", v.ID, v.Effects, err)
				return response, err
			}
		}
		matchTts := proto.TtsVO{}
		if len(v.MatchTTS) > 0 {
			listMatchTts := make([]proto.TtsVO, 0)
			err := json.Unmarshal([]byte(v.MatchTTS), &listMatchTts)
			if err != nil {
				logger.Log.Errorf(utils.MMark(logCtx)+"TtsVO id: %d v.MatchTTS: %s err:%v", v.ID, v.MatchTTS, err)
				//return response, err
				continue
			}
			if len(listMatchTts) > 0 {
				matchTts = listMatchTts[0]
			}
		}

		figureData := &proto.FigureQueryDataItem{
			Sort:     v.Sort,
			VideoUrl: v.VideoURL,
			FigureQueryData: proto.FigureQueryData{
				Source:           v.Source,
				Status:           v.Status,
				ResourceLabel:    v.ResourceLabel,
				TemplateImg:      v.TemplateImg,
				TemplateVideo:    v.TemplateVideo,
				FigureName:       v.FigureName,
				SubmitTime:       v.CreateTime,
				ConsumeTime:      consumeTime,
				Info:             info,
				Collected:        v.Collected,
				RecentlyUsed:     v.RecentlyUsed,
				SystemProvided:   v.SystemProvided == 1,
				ResolutionWidth:  int(v.ResolutionWidth),
				ResolutionHeight: int(v.ResolutionHeight),
				VideoUrl:         v.VideoURL,
				FigureResult:     v.FigureResult,
				EffectsThumbnail: v.EffectsThumbnail,
				Effects:          &effects,
				MatchTts:         &matchTts,
				Scene:            v.Scene,
				PreviewVideoUrl:  v.PreviewVideoUrl,
				IsDelete:         int(v.IsDelete),
			},
			CharacterConfig: proto.CharacterConfig{
				ID:         v.ID,
				Thumbnail:  v.Thumbnail,
				Name:       v.Name,
				Type:       string(enum.GetFigureTypeByString(v.Type)),
				CreateTime: v.CreateTime,
				UpdateTime: v.UpdateTime,
			}}

		if len(v.FaceLocation) > 0 {
			face := proto.FigureFaceLocation{}
			err := json.Unmarshal([]byte(v.FaceLocation), &face)
			if err != nil {
				logger.Log.Errorf(utils.MMark(logCtx)+"FigureFaceLocation id: %d v.FaceLocation: %s err:%v", v.ID, v.FaceLocation, err)
				return response, err
			}
			figureData.FaceLocation = face
		}

		response.FigureList = append(response.FigureList, figureData)
	}
	return response, nil
}

func getUserCharacterConfig(logCtx context.Context, req *proto.FigureQueryRequst, accountID string, collectList []int64, recentlyUsedIds []string) ([]*model.CharacterConfig, error) {
	if len(accountID) == 0 {
		return []*model.CharacterConfig{}, nil
	}
	list, err := (&model.CharacterConfig{}).GetUserCharacterConfig(logCtx, mysqlclient.DbMap[mysqlclient.PlatDBName], model.CharacterConfigLabel2D, req, accountID, collectList, recentlyUsedIds)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"GetUserCharacterConfig err: %v", err)
		return list, err
	}
	logger.Log.Infof(utils.MMark(logCtx)+"getUserCharacterConfig CharacterConfigLabel2D list length: %d", len(list))

	list3D, err := (&model.CharacterConfig{}).GetUserCharacterConfig(logCtx, mysqlclient.DbMap[mysqlclient.PlatDBName], model.CharacterConfigLabel3D, req, accountID, collectList, recentlyUsedIds)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"GetUserCharacterConfig err: %v", err)
		return list, err
	}
	logger.Log.Infof(utils.MMark(logCtx)+"getUserCharacterConfig CharacterConfigLabel3D list length: %d", len(list3D))

	list = append(list, list3D...)
	return list, nil
}

func getPublicCharacterConfig(logCtx context.Context, req *proto.FigureQueryRequst, collectList []int64, recentlyUsedIds []string) ([]*model.CharacterConfig, error) {
	list, err := (&model.CharacterConfig{}).GetPublicCharacterConfig(logCtx, mysqlclient.DbMap[mysqlclient.PlatDBName], model.CharacterConfigLabel2D, req, collectList, recentlyUsedIds)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"GetPublicCharacterConfig err: %v", err)
		return list, err
	}
	logger.Log.Infof(utils.MMark(logCtx)+"GetPublicCharacterConfig CharacterConfigLabel2D list length: %d", len(list))
	list3D, err := (&model.CharacterConfig{}).GetPublicCharacterConfig(logCtx, mysqlclient.DbMap[mysqlclient.PlatDBName], model.CharacterConfigLabel3D, req, collectList, recentlyUsedIds)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"GetUserCharacterConfig err: %v", err)
		return list, err
	}
	logger.Log.Infof(utils.MMark(logCtx)+"GetPublicCharacterConfig CharacterConfigLabel3D list length: %d", len(list3D))
	list = append(list, list3D...)
	return list, nil
}

func getUserCharacterCollect(logCtx context.Context, req *proto.FigureQueryRequst, collectList []int64) ([]*model.CharacterConfig, error) {
	list, err := (&model.CharacterConfig{}).GetCollectCharacterConfig(logCtx, mysqlclient.DbMap[mysqlclient.PlatDBName], collectList, model.CharacterConfigLabel2D, req)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"GetUserCollectByMaterialType err: %v", err)
		return list, err
	}
	logger.Log.Infof(utils.MMark(logCtx)+"getUserCharacterCollect UserCollectMapping list length: %d", len(list))

	list3D, err := (&model.CharacterConfig{}).GetCollectCharacterConfig(logCtx, mysqlclient.DbMap[mysqlclient.PlatDBName], collectList, model.CharacterConfigLabel3D, req)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"GetCollectCharacterConfig err: %v", err)
		return list, err
	}
	logger.Log.Infof(utils.MMark(logCtx)+"GetCollectCharacterConfig CharacterConfigLabel3D list length: %d", len(list3D))

	list = append(list, list3D...)
	return list, nil
}

func getUserCharacterByIdList(logCtx context.Context, req *proto.FigureQueryRequst, collectList []int64, recentlyUsedIds []string) ([]*model.CharacterConfig, error) {
	ids := make([]string, 0)
	for _, v := range req.FigureIdList {
		if v.FigureType == enum.FigureTypeOldFigure {
			ids = append(ids, v.CharacterID)
		}
	}

	if len(ids) == 0 {
		return make([]*model.CharacterConfig, 0), nil
	}

	list, err := (&model.CharacterConfig{}).GetUserCharacterConfigByConfigId(logCtx, mysqlclient.DbMap[mysqlclient.PlatDBName], ids, collectList, recentlyUsedIds)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"GetUserCharacterConfig err: %v", err)
		return list, err
	}
	logger.Log.Infof(utils.MMark(logCtx)+"getUserCharacterConfig CharacterConfigLabel list length: %d", len(list))
	return list, nil
}

func CharacterConfigtransformUserFigureResponse(logCtx context.Context, req *proto.FigureQueryRequst, list []*model.CharacterConfig) (proto.FigureQueryResponse, error) {
	response := proto.FigureQueryResponse{
		PageNo:     req.PageNo,
		PageSize:   req.PageSize,
		TotalCount: len(list),
		FigureList: make([]*proto.FigureQueryDataItem, 0),
	}
	for _, v := range list {
		figureData := &proto.FigureQueryDataItem{
			Sort:     v.Sort,
			VideoUrl: v.VideoUrl,
			FigureQueryData: proto.
				FigureQueryData{
				SystemProvided: v.SystemProvided,
				Collected:      v.Collected,
				RecentlyUsed:   v.RecentlyUsed,
			},
			CharacterConfig: v.CharacterConfig,
		}

		if len(v.FaceLocation) > 0 {
			tmpface := []proto.FigureFaceLocationByOldFigure{}
			err := json.Unmarshal([]byte(v.FaceLocation), &tmpface)
			if err != nil {
				logger.Log.Errorf(utils.MMark(logCtx)+"FigureFaceLocation id: %d v.FaceLocation: %s err:%v", v.ID, v.FaceLocation, err)
				return response, err
			}

			// figureData.ResolutionWidth = int(tmpface.ImageWidth)
			// figureData.ResolutionHeight = int(tmpface.ImageHeight)
			figureData.FaceLocation = tmpface
		}

		if v.Label == string(model.CharacterConfigLabel2D) {
			figureData.FigureQueryData.ResourceLabel = "2D_LITE_VIS_V4"
		}

		response.FigureList = append(response.FigureList, figureData)
	}

	return response, nil
}

func sortCollectList(logCtx context.Context, collectList []*model.UserCollectMapping, figureItems []*proto.FigureQueryDataItem) []*proto.FigureQueryDataItem {
	tmpItem := make([]*proto.FigureQueryDataItem, 0)
	for _, v := range collectList {
		isFind := false
		for _, item := range figureItems {
			if item.ID == v.MaterialID {
				if len(item.ConfigID) == 0 && v.MaterialType == model.MaterialTypeFigure {
					tmpItem = append(tmpItem, item)
					isFind = true
				} else if len(item.ConfigID) > 0 && v.MaterialType == model.MaterialTypeOldFigure {
					tmpItem = append(tmpItem, item)
					isFind = true
				}
			}
		}

		if !isFind {
			logger.Log.Errorf(utils.MMark(logCtx)+"collectList not find figureItem id: %d ,type: %d", v.ID, v.MaterialType)
		}
	}

	return tmpItem
}

func sortRecentlyUseList(withIds []proto.FigureQueryRequstWithId, figureItems []*proto.FigureQueryDataItem) []*proto.FigureQueryDataItem {
	newFigureItems := make([]*proto.FigureQueryDataItem, 0)

	withIdsIntMap := make(map[int64]struct{})
	withIdsStrMap := make(map[string]struct{})
	// 将常用的人像放到最前面
	for _, v := range withIds {
		for _, item := range figureItems {
			if v.FigureType == enum.FigureTypeOldFigure {
				if v.CharacterID == item.CharacterID {
					newFigureItems = append(newFigureItems, item)
					withIdsStrMap[v.CharacterID] = struct{}{}
					break
				}
			} else {
				if len(item.CharacterID) <= 0 && v.Id == item.ID {
					newFigureItems = append(newFigureItems, item)
					withIdsIntMap[v.Id] = struct{}{}
					break
				}
			}

		}
	}

	// 按照排序字段排序
	sort.SliceStable(figureItems, func(i, j int) bool {
		return figureItems[i].Sort > figureItems[j].Sort
	})
	for _, item := range figureItems {
		if _, ok := withIdsIntMap[item.ID]; ok {
			continue
		}
		if _, ok := withIdsStrMap[item.CharacterID]; ok {
			continue
		}
		newFigureItems = append(newFigureItems, item)
	}
	return newFigureItems
}

func sortUpdateTimeList(withIds []proto.FigureQueryRequstWithId, figureItems []*proto.FigureQueryDataItem) []*proto.FigureQueryDataItem {
	newRecentlyUsedFigureItems := make([]*proto.FigureQueryDataItem, 0)
	newFigureItems := make([]*proto.FigureQueryDataItem, 0)
	withIdsIntMap := make(map[int64]struct{}, 0)
	withIdsStrMap := make(map[string]struct{}, 0)
	// 将常用的人像放到最前面
	for _, v := range withIds {
		for _, item := range figureItems {
			if v.FigureType == enum.FigureTypeOldFigure {
				if v.CharacterID == item.CharacterID {
					newFigureItems = append(newFigureItems, item)
					withIdsStrMap[v.CharacterID] = struct{}{}
					break
				}
			} else {
				if len(item.CharacterID) <= 0 && v.Id == item.ID {
					newFigureItems = append(newFigureItems, item)
					withIdsIntMap[v.Id] = struct{}{}
					break
				}
			}

		}
	}

	for _, v := range figureItems {
		if len(v.CharacterID) <= 0 {
			if _, ok := withIdsIntMap[v.ID]; ok {
				continue
			}
		} else {
			if _, ok := withIdsStrMap[v.CharacterID]; ok {
				continue
			}
		}
		newFigureItems = append(newFigureItems, v)
	}

	// sort.Slice(newFigureItems, func(i, j int) bool {
	// 	// 按照时间倒序排列
	// 	return newFigureItems[i].UpdateTime.After(newFigureItems[j].UpdateTime)
	// })

	newRecentlyUsedFigureItems = append(newRecentlyUsedFigureItems, newFigureItems...)
	return newRecentlyUsedFigureItems
}

func ModifySystem(c *gin.Context) {
	logCtx := context.WithValue(context.TODO(), utils.CtxKeyLogID, utils.RandStringRunes(16))
	// 绑定并校验参数
	req := proto.FigureUpdateRequest{}
	if err := c.BindJSON(&req); err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"system figure modify bind param fail, err: %v", err.Error())
		c.JSON(http.StatusOK, commProto.NewCommRsp(100001, err.Error()))
		return
	}
	if len(*req.ConfigId) > 0 {
		// 查询并修改plat库character_config表
		db := mysqlclient.DbMap[mysqlclient.PlatDBName]
		figure := &model.CharacterConfig{}
		if err := figure.FindByConfigIDAndUserId(db, *req.ConfigId, "System"); err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+"figure modify find character_config by id fail, err: %v", err.Error())
			c.JSON(http.StatusOK, commProto.NewCommDataRsp(530001, "查询人像失败", err.Error()))
			return
		} else if len(figure.CharacterID) == 0 {
			c.JSON(http.StatusOK, commProto.NewCommRsp(130001, "人像不存在"))
			return
		}
		if err := figure.UpdateNameAndSortByConfigID(db, *req.ConfigId, req.Name, *req.Sort); err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+"figure modify update character_config name and sort by id fail, err: %v", err.Error())
			c.JSON(http.StatusOK, commProto.NewCommDataRsp(530002, "更新人像信息失败", err.Error()))
		}
	} else if *req.ID > 0 {
		// 查询并修改star-light库user_figure表
		figure := &model.UserFigure{}
		db := mysqlclient.DbMap[mysqlclient.StarLightDBName]
		if err := figure.FindByIdAndUserId(db, *req.ID, "-1"); err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+"figure modify find figure by id fail, err: %v", err.Error())
			c.JSON(http.StatusOK, commProto.NewCommDataRsp(530001, "查询人像失败", err.Error()))
			return
		} else if figure.ID == 0 {
			c.JSON(http.StatusOK, commProto.NewCommRsp(130001, "人像不存在"))
			return
		}
		if err := figure.UpdateNameAndSortById(db, *req.ID, req.Name, *req.Sort); err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+"figure modify update figure name and sort by id fail, err: %v", err.Error())
			c.JSON(http.StatusOK, commProto.NewCommDataRsp(530002, "更新人像信息失败", err.Error()))
		}
	} else {
		c.JSON(http.StatusOK, commProto.NewCommRsp(100001, "参数异常"))
		return
	}
	c.JSON(http.StatusOK, commProto.NewSuccessRsp(nil))
}

// 判断一个用户维度下，人像名称是否使用过
func FigureIsNameUsed(c *gin.Context) {
	logCtx := context.WithValue(context.TODO(), utils.CtxKeyLogID, utils.RandStringRunes(16))
	accountID := "" // 登录用户id
	aid, ok := c.Get("AccountId")
	if ok {
		accountID = aid.(string)
	}

	logger.Log.Infof(utils.MMark(logCtx)+"FigureIsNameUsed, accountID:%v", accountID)
	req := proto.FigureIsNameUsedRequest{}
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"FigureIsNameUsed", "err", err)
		c.JSON(http.StatusOK, proto.NewCommDataRsp(100001, "参数错误", nil))
		return
	}

	// 查询名称是否使用过
	figure := &model.UserFigure{}
	db := mysqlclient.DbMap[mysqlclient.StarLightDBName]
	if err := figure.FindByUserIdAndName(db, accountID, req.FigureName, -1); err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"figure find fail, err: %v", err.Error())
		c.JSON(http.StatusOK, proto.NewCommDataRsp(100002, "查询人像失败", err.Error()))
		return
	} else if figure.ID == 0 {
		// 没有使用过这个名称
		c.JSON(http.StatusOK, proto.NewCommDataRsp(0, "sucess", map[string]bool{
			"isUsed": false,
		}))
		return
	} else {
		// 使用过人像名称
		c.JSON(http.StatusOK, proto.NewCommDataRsp(0, "sucess", map[string]bool{
			"isUsed": true,
		}))
	}
}

// 判断一个用户维度下，是否创建过人像,（删除过也算）
func FigureIsHaveCreated(c *gin.Context) {
	logCtx := context.WithValue(context.TODO(), utils.CtxKeyLogID, utils.RandStringRunes(16))
	accountID := "" // 登录用户id
	aid, ok := c.Get("AccountId")
	if ok {
		accountID = aid.(string)
	}

	logger.Log.Infof(utils.MMark(logCtx)+"FigureIsHaveCreated, accountID:%v", accountID)

	// 查询名称是否创建过
	figure := &model.UserFigure{}
	db := mysqlclient.DbMap[mysqlclient.StarLightDBName]
	count, err := figure.FindCountByUserId(db, accountID)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"figure find fail, err: %v", err.Error())
		c.JSON(http.StatusOK, proto.NewCommDataRsp(100002, "查询人像失败", err.Error()))
		return
	} else if count == 0 {
		// 没有使用过这个名称
		c.JSON(http.StatusOK, proto.NewCommDataRsp(0, "sucess", map[string]bool{
			"isCreated": false,
		}))
		return
	} else {
		// 使用过人像名称
		c.JSON(http.StatusOK, proto.NewCommDataRsp(0, "sucess", map[string]bool{
			"isCreated": true,
		}))
	}
}

// 判断用户是否创建过精品克隆人像
func IsHaveClonePremiumFigure(c *gin.Context) {
	logCtx := context.WithValue(context.TODO(), utils.CtxKeyLogID, utils.RandStringRunes(16))
	// 解析请求参数
	req := &proto.CloneFigureRequest{}
	if err := c.ShouldBindQuery(&req); err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+" ShouldBindQuery error:%v", err)
		c.JSON(http.StatusOK, proto.CloneFigureResponse{Success: true, Status: 100001, Result: false})
		return
	}

	if len(req.BceAccountId) == 0 {
		logger.Log.Errorf(utils.MMark(logCtx) + " BceAccountId is empty")
		c.JSON(http.StatusOK, proto.CloneFigureResponse{Success: true, Status: 100002, Result: false})
		return
	}

	logger.Log.Infof(utils.MMark(logCtx)+"IsHaveClonePremiumFigure, accountID:%v", req.BceAccountId)

	// 查询名称是否创建过
	figure := &model.UserFigure{}
	db := mysqlclient.DbMap[mysqlclient.StarLightReadDBName]
	count, err := figure.FindV4FigureCountByUserId(db, req.BceAccountId)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"figure find fail, err: %v", err.Error())
		c.JSON(http.StatusOK, proto.CloneFigureResponse{Success: true, Status: 100003, Result: false})
		return
	}

	if count == 0 {
		// 没有使用过这个名称
		logger.Log.Warnf(utils.MMark(logCtx) + "figure not find, count:0")
		c.JSON(http.StatusOK, proto.CloneFigureResponse{Success: true, Status: 200, Result: false})
		return
	}

	c.JSON(http.StatusOK, proto.CloneFigureResponse{Success: true, Status: 200, Result: true})
}
