package figure

import (
	"acg-ai-go-common/logger"
	"acg-ai-go-common/utils"
	"context"
	"digital-human-figure-resource/beans/model"
	"digital-human-figure-resource/beans/proto"
	"fmt"
	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/xuri/excelize/v2"
	"net/http"
	"os"
	"reflect"
	"strconv"
	"time"
)

func TemplateDownload(c *gin.Context) {
	logCtx := context.WithValue(context.TODO(), utils.CtxKeyLogID, utils.RandStringRunes(16))
	req := proto.ExportExcelRequest{}
	if err := c.ShouldBind<PERSON>uery(&req); err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"FigureQuery", "err", err)
		c.<PERSON>(http.StatusOK, proto.NewCommDataRsp(100001, "参数错误", nil))
		return
	}

	template := model.Template{}
	templates, err := template.FindTemplateByRequest(req.Name, req.IsRecommended, int(req.PageNo), int(req.PageSize))
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"FigureQuery", "err", err)
		c.JSON(http.StatusOK, proto.NewCommRsp(100002, err.Error()))
		return
	}
	path, err := GenerateExcel(logCtx, templates)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "生成 Excel 失败"})
		return
	}
	c.JSON(http.StatusOK, proto.NewSuccessRsp(path))
}

// GenerateExcel 生成 Excel 文件
func GenerateExcel(logCtx context.Context, templates []model.Template) (filePath string, err error) {
	f := excelize.NewFile()
	defer f.Close()

	sheetName := "Templates"
	index, err := f.NewSheet(sheetName)
	if err != nil {
		return "", err
	}
	f.SetActiveSheet(index)

	// 解析结构体字段，建立 JSON 标签到字段名的映射
	t := reflect.TypeOf(model.Template{})
	tagToField := make(map[string]string)
	var headers []string
	for i := 0; i < t.NumField(); i++ {
		field := t.Field(i)
		jsonTag := field.Tag.Get("json")
		if jsonTag == "" {
			continue // 跳过无 JSON 标签的字段
		}
		headers = append(headers, jsonTag)
		tagToField[jsonTag] = field.Name // 记录标签对应的字段名
	}

	// 写入表头
	for col, header := range headers {
		cellName, _ := excelize.CoordinatesToCellName(col+1, 1)
		f.SetCellValue(sheetName, cellName, header)
	}

	// 写入数据行（关键修复点：通过标签映射获取字段名）
	for row, template := range templates {
		obj := reflect.ValueOf(template)
		for col, tag := range headers {
			cellName, _ := excelize.CoordinatesToCellName(col+1, row+2)
			fieldName, ok := tagToField[tag]
			if !ok {
				continue // 忽略无法映射的标签
			}

			// 获取字段值（处理零值情况）
			fieldValue := obj.FieldByName(fieldName)
			if !fieldValue.IsValid() || fieldValue.IsZero() {
				// 处理零值（如默认值字段）
				switch tag {
				case "id", "aspect_width", "aspect_height":
					f.SetCellValue(sheetName, cellName, "0") // 或其他默认值
				default:
					f.SetCellValue(sheetName, cellName, "")
				}
				continue
			}

			// 类型转换（增加零值判断）
			var value string
			switch fieldValue.Kind() {
			case reflect.Int, reflect.Int64:
				value = strconv.FormatInt(fieldValue.Int(), 10)
			case reflect.String:
				value = fieldValue.String()
			case reflect.Struct:
				if t, ok := fieldValue.Interface().(time.Time); ok {
					value = t.Format("2006-01-02 15:04:05")
				}
				// 其他类型可按需添加
			}
			f.SetCellValue(sheetName, cellName, value)
		}
	}
	dateTime := time.Now().Format("2006-01-02")
	newUUID, _ := uuid.NewUUID()

	savePath := newUUID.String() + ".xlsx"
	if err = f.SaveAs(savePath); err != nil {
		return "", err
	}

	defer func() {
		if err = os.Remove(savePath); err != nil {
			logger.Log.Error("GenerateExcel", "err", err)
		}
	}()

	bosskey := fmt.Sprintf("%s/%s", "template", dateTime)
	return RetryUploadBosServiceFromFileByBosKey(logCtx, bosskey, savePath)
}

func TemplateRestore(c *gin.Context) {
	logCtx := context.WithValue(context.TODO(), utils.CtxKeyLogID, utils.RandStringRunes(16))
	req := proto.TemplateRestoreRequest{}
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"TemplateRestore", "err", err)
		c.JSON(http.StatusOK, proto.NewCommDataRsp(100001, "参数错误", nil))
		return
	}
	template := model.Template{}
	templateModel, err := template.FindTemplateByTemplateId(req.TemplateId)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"TemplateRestore", "err", err)
		c.JSON(http.StatusOK, proto.NewCommDataRsp(100002, "模板不存在", nil))
		return
	}
	templateModel.IsDelete = 0
	err = templateModel.Save()
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"TemplateRestore", "err", err)
		c.JSON(http.StatusOK, proto.NewCommDataRsp(100003, "恢复失败", nil))
		return
	}
	c.JSON(http.StatusOK, proto.NewCommDataRsp(0, "恢复成功", nil))
}
