package figure

import (
	"acg-ai-go-common/logger"
	"acg-ai-go-common/utils"
	"context"
	"digital-human-figure-resource/beans/enum"
	"digital-human-figure-resource/beans/model"
	"digital-human-figure-resource/beans/proto"
	"digital-human-figure-resource/mysqlclient"
	"net/http"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

func FigureCollect(c *gin.Context) {
	logCtx := context.WithValue(context.TODO(), utils.CtxKeyLogID, utils.RandStringRunes(16))
	accountID := "8b440b7b-1622-4826-8498-5047003c2c9b"
	aid, ok := c.Get("AccountId")
	if ok {
		accountID = aid.(string)
	}
	req := proto.FigureCollectRequse{}
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"ShouldBindJSON err: %v", err)
		c.<PERSON>(http.StatusOK, proto.NewCommDataRsp(100001, "参数错误", nil))
		return
	}

	if req.Type == string(enum.FigureTypeFigure) {
		// v2v4人像
		uf, err := (&model.UserFigure{}).GetUserFigureById(mysqlclient.DbMap[mysqlclient.StarLightDBName], req.FigureId)
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+"GetUserFigureById err: %v", err)
			c.JSON(http.StatusOK, proto.NewCommDataRsp(100002, "不存在人像资源", nil))
			return
		}

		if uf.UserID != "-1" && uf.UserID != accountID {
			logger.Log.Errorf(utils.MMark(logCtx)+"GetUserFigureById userid: %v", uf.UserID)
			c.JSON(http.StatusOK, proto.NewCommDataRsp(100003, "用户没有该人像权限", nil))
			return
		}

		olduc, err := (&model.UserCollectMapping{}).GetUserCollecById(mysqlclient.DbMap[mysqlclient.StarLightDBName], accountID, req.FigureId, model.MaterialTypeFigure)
		if req.Collect {
			uf.Collected = true
			// 收藏
			if err == nil {
				c.JSON(http.StatusOK, proto.NewSuccessRsp(uf))
				return
			}
			uc := model.UserCollectMapping{
				UserID:       accountID,
				MaterialID:   req.FigureId,
				MaterialType: model.MaterialTypeFigure,
			}
			err = uc.Create(mysqlclient.DbMap[mysqlclient.StarLightDBName])
			if err != nil {
				logger.Log.Errorf(utils.MMark(logCtx)+"Create err: %v", err)
				c.JSON(http.StatusOK, proto.NewCommDataRsp(100004, "收藏失败", nil))
				return
			}
			c.JSON(http.StatusOK, proto.NewSuccessRsp(uf))
			return
		} else {
			if err != nil && err == gorm.ErrRecordNotFound {
				c.JSON(http.StatusOK, proto.NewSuccessRsp(uf))
				return
			} else if err != nil {
				logger.Log.Errorf(utils.MMark(logCtx)+"GetUserCollecById err: %v", err)
				c.JSON(http.StatusOK, proto.NewCommDataRsp(100005, "取消收藏失败", nil))
				return
			}

			err = (&model.UserCollectMapping{}).Delete(mysqlclient.DbMap[mysqlclient.StarLightDBName], olduc.ID, model.MaterialTypeFigure)
			if err != nil {
				logger.Log.Errorf(utils.MMark(logCtx)+"Delete err: %v", err)
				c.JSON(http.StatusOK, proto.NewCommDataRsp(100006, "取消收藏失败", nil))
				return
			}

			c.JSON(http.StatusOK, proto.NewSuccessRsp(uf))
			return
		}
	} else if req.Type == string(enum.FigureTypeOldFigure) {
		// 老版本人像
		character, err := (&model.CharacterConfig{}).GetUserCharacterById(mysqlclient.DbMap[mysqlclient.PlatDBName], req.FigureId)
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+"GetUserCharacterById err: %v", err)
			c.JSON(http.StatusOK, proto.NewCommDataRsp(100002, "不存在人像资源", nil))
			return
		}
		if character.UserID != "System" && character.UserID != accountID {
			logger.Log.Errorf(utils.MMark(logCtx)+"GetUserCharacterById userid: %v", character.UserID)
			c.JSON(http.StatusOK, proto.NewCommDataRsp(100003, "用户没有该人像权限", nil))
			return
		}
		olduc, err := (&model.UserCollectMapping{}).GetUserCollecById(mysqlclient.DbMap[mysqlclient.StarLightDBName], accountID, req.FigureId, model.MaterialTypeOldFigure)
		if req.Collect {
			// 收藏
			if err == nil {
				c.JSON(http.StatusOK, proto.NewSuccessRsp(character))
				return
			}
			uc := model.UserCollectMapping{
				UserID:       accountID,
				MaterialID:   req.FigureId,
				MaterialType: model.MaterialTypeOldFigure,
			}
			err = uc.Create(mysqlclient.DbMap[mysqlclient.StarLightDBName])
			if err != nil {
				logger.Log.Errorf(utils.MMark(logCtx)+"Create err: %v", err)
				c.JSON(http.StatusOK, proto.NewCommDataRsp(100004, "收藏失败", nil))
				return
			}
			c.JSON(http.StatusOK, proto.NewSuccessRsp(character))
			return
		} else {
			if err != nil && err == gorm.ErrRecordNotFound {
				c.JSON(http.StatusOK, proto.NewSuccessRsp(character))
				return
			} else if err != nil {
				logger.Log.Errorf(utils.MMark(logCtx)+"GetUserCollecById err: %v", err)
				c.JSON(http.StatusOK, proto.NewCommDataRsp(100005, "取消收藏失败", nil))
				return
			}

			err = (&model.UserCollectMapping{}).Delete(mysqlclient.DbMap[mysqlclient.StarLightDBName], olduc.ID, model.MaterialTypeOldFigure)
			if err != nil {
				logger.Log.Errorf(utils.MMark(logCtx)+"Delete err: %v", err)
				c.JSON(http.StatusOK, proto.NewCommDataRsp(100006, "取消收藏失败", nil))
				return
			}

			c.JSON(http.StatusOK, proto.NewSuccessRsp(character))
			return
		}

	} else {
		c.JSON(http.StatusOK, proto.NewCommDataRsp(100007, "不支持的人像类型", nil))
		return
	}
}
