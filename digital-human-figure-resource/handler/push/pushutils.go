package push

import (
	"acg-ai-go-common/gomysql"
	"acg-ai-go-common/logger"
	"acg-ai-go-common/utils"
	"bytes"
	"context"
	"crypto/md5"
	"digital-human-figure-resource/beans/model"
	"digital-human-figure-resource/beans/proto"
	config "digital-human-figure-resource/conf"
	"digital-human-figure-resource/handler/handlerUtils/retryhttpclient"
	"encoding/hex"
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"gorm.io/gorm"
)

const (
	ZgUserChannel = "LANYUN"
)

// 服务内部调用，创建视频推送记录
func CreatePushVideoRecord(c *gin.Context) {
	logID := uuid.New().String()
	logCtx := context.WithValue(context.TODO(), utils.CtxKeyLogID, logID)
	logger.Log.Infof(utils.MMark(logCtx) + "CreatePushVideoRecord  request start")
	startTime := time.Now()

	req := model.PushVideoEntity{}
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"CreatePushVideoRecord req error: %+v", err)
		c.JSON(http.StatusOK, proto.NewCommRsp(100001, "参数异常"))
		return
	}

	logger.Log.Infof(utils.MMark(logCtx)+"CreatePushVideoRecord request body: %+v", req)
	if req.UserChannel != ZgUserChannel {
		logger.Log.Infof(utils.MMark(logCtx)+"CreatePushVideoRecord req.UserChannel: %+v, not LANYUN not push", req)
		c.JSON(http.StatusOK, proto.NewCommRsp(0, "该视频不需要推送"))
		return
	}

	err := (&req).Create(gomysql.DB)
	if err != nil {
		// 创建推送录失败
		logger.Log.Errorf(utils.MMark(logCtx)+"CreatePushVideoRecord  create push record error: %v", err)
		c.JSON(http.StatusOK, proto.NewCommRsp(100002, "创建视频推送记录失败"))
		return
	}
	elapsed := time.Since(startTime)
	logger.Log.Infof(utils.MMark(logCtx)+"CreatePushVideoRecord  end cost: %+v ", elapsed)
	c.JSON(http.StatusOK, proto.NewSuccessRsp(nil))
}

// 服务内部调用，推送视频
func PushVideoFromServerInternal(c *gin.Context) {
	logID := uuid.New().String()
	logCtx := context.WithValue(context.TODO(), utils.CtxKeyLogID, logID)
	logger.Log.Infof(utils.MMark(logCtx) + "PushVideoFromServerInternal  request start")
	startTime := time.Now()

	req := proto.VideoListModel{}
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"PushVideoFromServerInternal req error: %+v", err)
		c.JSON(http.StatusOK, proto.NewCommRsp(100001, "参数异常"))
		return
	}

	if len(req.VideoID) == 0 || len(req.VideoURL) == 0 {
		logger.Log.Error(utils.MMark(logCtx) + "PushVideoFromServerInternal  videoId or videoUrl is empty")
		c.JSON(http.StatusOK, proto.NewCommRsp(100002, "视频ID或者URL为空"))
		return
	}
	logger.Log.Infof(utils.MMark(logCtx)+"PushVideoFromServerInternal request body: %+v", req)

	video, err := (&model.PushVideoEntity{}).SelectByVideoID(gomysql.DB, req.VideoID)
	if err != nil {
		// 在推送表中没有查询到该视频，不进行推送
		if errors.Is(err, gorm.ErrRecordNotFound) {
			logger.Log.Errorf(utils.MMark(logCtx)+"PushVideoFromServerInternal  query push video size is 0: %v", err)
			c.JSON(http.StatusOK, proto.NewCommRsp(0, "没有该视频的推送信息，不需要推送"))
		} else {
			logger.Log.Errorf(utils.MMark(logCtx)+"PushVideoFromServerInternal  query push video error: %v", err)
			c.JSON(http.StatusOK, proto.NewCommRsp(100003, "查询该视频推送信息失败"))
		}
		return
	}

	if video.UserChannel != "LANYUN" {
		logger.Log.Errorf(utils.MMark(logCtx) + "PushVideoFromServerInternal  req userChannel is not LANYUN")
		c.JSON(http.StatusOK, proto.NewCommRsp(100004, "userChannel is Wrong!"))
		return
	}

	// 更新库里视频推送状态为  推送中 并且 把videoUrl写进库中
	logger.Log.Infof(utils.MMark(logCtx)+"PushVideoFromServerInternal UpdateVideoStatusOnPush start,VideoID:%s,VideoUrl:%s", req.VideoID, req.VideoURL)
	updateErr := (&model.PushVideoEntity{}).UpdateVideoUrlAndStatusOnPush(gomysql.DB, req.VideoID, req.VideoURL)
	if updateErr != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"PushVideoFromFrontEnd UpdateVideoStatusOnPush error: %v", updateErr)
		c.JSON(http.StatusOK, proto.NewCommRsp(100005, "更新视频推送状态失败"))
		return
	}
	logger.Log.Infof(utils.MMark(logCtx) + "PushVideoFromServerInternal UpdateVideoStatusOnPush end")

	var taskArray []proto.ZgTaskModel
	taskModel := proto.ZgTaskModel{
		FileHttpPath: req.VideoURL,
		RequestId:    req.VideoID,
		SiteCode:     video.SiteCode,
		ZgEntityData: []proto.ZgEntityData{
			{
				Key:   "name",
				Value: video.VideoName,
			},
		},
	}
	taskArray = append(taskArray, taskModel)

	imporReq := proto.ZgImportRequest{
		ZgTaskArray: taskArray,
	}
	// 调用浙广的推送接口
	logger.Log.Infof(utils.MMark(logCtx)+"PushVideoFromServerInternal PushVideoToZg strat imporReq: %+v", imporReq)
	pushResponse, err := PushVideoToZg(logCtx, imporReq)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"PushVideoFromFrontEnd failed  error: %v", err)
		c.JSON(http.StatusOK, proto.NewCommRsp(100005, fmt.Sprintf("视频推送失败:%v", err)))
		return
	}

	logger.Log.Infof(utils.MMark(logCtx)+"PushVideoFromServerInternal PushVideoToZg response: %+v", pushResponse)

	if len(pushResponse.ZgDataModel.List) == 0 {
		logger.Log.Error(utils.MMark(logCtx) + "PushVideoFromServerInternal  zg import response video list is empty")
		c.JSON(http.StatusOK, proto.NewCommRsp(100006, "视频推送失败,ZG返回数据为空"))
		return
	}

	if len(pushResponse.ZgDataModel.List) != 1 {
		logger.Log.Error(utils.MMark(logCtx)+"PushVideoFromServerInternal zg import response data error,size:%d,should be 1", len(pushResponse.ZgDataModel.List))
		c.JSON(http.StatusOK, proto.NewCommRsp(100006, "视频推送失败,ZG返回数据错误"))
		return
	}
	ZgResModel := pushResponse.ZgDataModel.List[0]
	response := proto.InternalResponseResultModel{
		ReqId:      logID,
		VideoId:    req.VideoID,
		PushStatus: ZgResModel.Code,
		PushMsg:    ZgResModel.Message,
	}
	elapsed := time.Since(startTime)
	logger.Log.Infof(utils.MMark(logCtx)+"PushVideoToZg  end cost: %+v pushResList: %+v", elapsed, response)
	c.JSON(http.StatusOK, proto.NewSuccessRsp(response))
}

// 前端调用，推送视频
func PushVideoFromFrontEnd(c *gin.Context) {
	logID := uuid.New().String()
	logCtx := context.WithValue(context.TODO(), utils.CtxKeyLogID, logID)
	logger.Log.Infof(utils.MMark(logCtx) + "PushVideoFromFrontEnd  request start")
	startTime := time.Now()
	accountID := ""
	aid, ok := c.Get("AccountId")
	if ok {
		accountID = aid.(string)
	}
	userChannel := ""
	if uch, ok := c.Get("UserChannel"); ok {
		userChannel = uch.(string)
	}
	logger.Log.Infof(utils.MMark(logCtx)+"PushVideoFromFrontEnd  accountID:%s , userChannel:%s", accountID, userChannel)

	// 如果channel不是LANYUN，则不进入推送流程
	if userChannel != "LANYUN" {
		logger.Log.Errorf(utils.MMark(logCtx) + "PushVideoFromFrontEnd req  param error,userChannel is not LANYUN")
		c.JSON(http.StatusOK, proto.NewCommRsp(100001, "参数异常,userChannel is Wrong!"))
		return
	}

	if len(accountID) == 0 {
		logger.Log.Error(utils.MMark(logCtx) + "PushVideoFromFrontEnd  accountId is empty")
		c.JSON(http.StatusOK, proto.NewCommRsp(100001, "参数异常,accountId is empty"))
		return
	}

	req := proto.PushVideoRequest{}
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"PushVideoFromFrontEnd req error: %+v", err)
		c.JSON(http.StatusOK, proto.NewCommRsp(100001, "参数异常"))
		return
	}

	if len(req.VideoList) == 0 {
		logger.Log.Error(utils.MMark(logCtx) + "PushVideoFromFrontEnd  videoList is empty")
		c.JSON(http.StatusOK, proto.NewCommRsp(100002, "视频列表为空"))
		return
	}
	logger.Log.Infof(utils.MMark(logCtx)+"PushVideoFromFrontEnd request body: %+v", req)

	videoIds := req.VideoList
	// 从库里查询视频推送信息，前端推送时，数据已经写到库里了
	videos, err := (&model.PushVideoEntity{}).SelectByVideoIDs(gomysql.DB, videoIds, accountID)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"PushVideoFromFrontEnd  query push video error: %v", err)
		c.JSON(http.StatusOK, proto.NewCommRsp(100003, "查询视频推送列表失败"))
		return
	}

	if len(videos) == 0 {
		logger.Log.Error(utils.MMark(logCtx) + "PushVideoFromFrontEnd  query push video list is empty")
		c.JSON(http.StatusOK, proto.NewCommRsp(100004, "查询视频推送列表列数据为空"))
		return
	}
	logger.Log.Infof(utils.MMark(logCtx)+"PushVideoFromFrontEnd query,accountID: %s, pushvideos: %+v", accountID, videos)

	// 更新库里视频推送状态为 推送中
	updateErr := (&model.PushVideoEntity{}).UpdateVideosStatusOnPush(gomysql.DB, videoIds)
	if updateErr != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"PushVideoFromFrontEnd  update push status error: %v", updateErr)
		c.JSON(http.StatusOK, proto.NewCommRsp(100005, "更新视频推送状态失败"))
		return
	}

	var taskArray []proto.ZgTaskModel
	for _, v := range videos {
		taskModel := proto.ZgTaskModel{
			FileHttpPath: v.VideoUrl,
			RequestId:    v.VideoID,
			SiteCode:     v.SiteCode,
			ZgEntityData: []proto.ZgEntityData{
				{
					Key:   "name",
					Value: v.VideoName,
				},
			},
		}
		if len(v.VideoUrl) == 0 {
			logger.Log.Error(utils.MMark(logCtx)+"PushVideoFromFrontEnd  videoUrl is empty,video: %v", v)
			c.JSON(http.StatusOK, proto.NewCommRsp(100005, "推送视频的url为空,videoName:"+v.VideoName))
			return
		}
		logger.Log.Infof(utils.MMark(logCtx)+"PushVideoFromFrontEnd query pushvideos: %+v", videos)

		taskArray = append(taskArray, taskModel)
	}
	imporReq := proto.ZgImportRequest{
		ZgTaskArray: taskArray,
	}
	// 调用浙广的推送接口
	pushResponse, err := PushVideoToZg(logCtx, imporReq)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"PushVideoFromFrontEnd failed  error: %v", err)
		c.JSON(http.StatusOK, proto.NewCommRsp(100005, "视频推送失败"))
		return
	}

	logger.Log.Infof(utils.MMark(logCtx)+"PushVideoToZg response: %+v", pushResponse)

	if len(pushResponse.ZgDataModel.List) == 0 {
		logger.Log.Error(utils.MMark(logCtx) + "zg import response video list is empty")
		c.JSON(http.StatusOK, proto.NewCommRsp(100006, "视频推送失败,ZG返回数据为空"))
		return
	}

	var pushResList []proto.PushResModel
	for i := 0; i < len(pushResponse.ZgDataModel.List); i++ {
		ZgListModel := pushResponse.ZgDataModel.List[i]
		pushResModel := proto.PushResModel{
			VideoID:    ZgListModel.RequestId,
			PushStatus: ZgListModel.Code,
			PushMsg:    ZgListModel.Message,
		}
		pushResList = append(pushResList, pushResModel)
	}
	response := proto.ResponseResultModel{
		ReqId:       logID,
		PushResList: pushResList,
	}
	elapsed := time.Since(startTime)
	logger.Log.Infof(utils.MMark(logCtx)+"PushVideoToZg  end cost: %+v pushResList: %+v", elapsed, pushResList)
	c.JSON(http.StatusOK, proto.NewSuccessRsp(response))
}

func PushVideoToZg(logCtx context.Context, reqBody proto.ZgImportRequest) (*proto.ZgImportResonse, error) {
	httpclient := retryhttpclient.NewRetryHTTPClient(1*time.Minute, 3)
	jsonData, err := json.Marshal(reqBody)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"PushVideoToZg json marshal error: %v", err)
		return nil, err
	}
	bodyContent := string(jsonData)
	logger.Log.Infof(utils.MMark(logCtx)+"PushVideoToZg start,repJsonData: %s", bodyContent)

	// 获取13位毫秒级时间戳
	timestamp := fmt.Sprintf("%d", time.Now().UnixNano()/int64(time.Millisecond))
	signString := getZgSignature(logCtx, timestamp, bodyContent)
	requstUrl := config.LocalConfig.ZgPushConfig.PushUrl
	logger.Log.Infof(utils.MMark(logCtx)+"PushVideoToZg requstUrl: %s,reqBody: %s", requstUrl, bodyContent)

	headers := make(map[string]string)
	headers["Content-Type"] = "application/json"
	headers["sign"] = signString
	headers["appId"] = config.LocalConfig.ZgPushConfig.AppID
	headers["timeStamp"] = timestamp
	startTime := time.Now() // 记录请求开始时间
	resp, err := httpclient.DoRequest(logCtx, http.MethodPost, requstUrl, headers, bytes.NewBuffer(jsonData))
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"PushVideoToZg request error: %v", err)
		return nil, errors.New("Zg推送接口,请求失败")
	}
	logger.Log.Infof(utils.MMark(logCtx)+"PushVideoToZg response: %s", string(resp))

	res := &proto.ZgImportResonse{}
	err = json.Unmarshal(resp, res)
	if err != nil {
		logger.Log.Errorf("PushVideoToZg response unmarshal error: %v", err)
		return nil, errors.New("ZG推送返回数据,解析失败")
	}

	for i := 0; i < len(res.ZgDataModel.List); i++ {
		ZgListModel := res.ZgDataModel.List[i]

		// 更新推送库中的 推送状态，code为200，为推送成功
		if ZgListModel.Code == 200 {
			err := (&model.PushVideoEntity{}).UpdateVideoStatusSuccess(gomysql.DB, ZgListModel.RequestId)
			if err != nil {
				logger.Log.Errorf(utils.MMark(logCtx)+"UpdateVideoStatusSuccess error: %v", err)
				return nil, errors.New("UpdateVideoStatusSuccess,error")
			}
		} else {
			// 更新推送库中的 推送状态，code为200，为推送失败
			err := (&model.PushVideoEntity{}).UpdateVideoStatusFail(gomysql.DB, ZgListModel.RequestId, ZgListModel.Message)
			if err != nil {
				logger.Log.Errorf(utils.MMark(logCtx)+"UpdateVideoStatusFail error: %v", err)
				return nil, errors.New("UpdateVideoStatusSuccess,error")
			}
		}
	}

	elapsed := time.Since(startTime) // 计算下接口请求时间
	logger.Log.Infof(utils.MMark(logCtx)+"PushVideoToZg requst end cost:%+v response: %+v", elapsed, res)
	return res, nil
}

func getZgSignature(logCtx context.Context, timestamp string, bodyStr string) string {
	// signContent的拼接方式为：appId+appSecret+content+timeStamp
	signContent := config.LocalConfig.ZgPushConfig.AppID + config.LocalConfig.ZgPushConfig.AppSecret + bodyStr + timestamp
	logger.Log.Infof(utils.MMark(logCtx)+"getZgSign, AppID:%s,AppSecret%s,timestamp:%s,signContent: %s", config.LocalConfig.ZgPushConfig.AppID,
		config.LocalConfig.ZgPushConfig.AppSecret, timestamp, signContent)
	hash := md5.New()
	hash.Write([]byte(signContent))          // 将输入的字符串转换为字节切片并写入哈希对象
	md5Hash := hash.Sum(nil)                 //  // 获取 MD5 的哈希值
	hexString := hex.EncodeToString(md5Hash) // 将 MD5 哈希值转为 16 进制字符串
	logger.Log.Infof(utils.MMark(logCtx)+"hexString,signContent: %s", hexString)
	return hexString
}
