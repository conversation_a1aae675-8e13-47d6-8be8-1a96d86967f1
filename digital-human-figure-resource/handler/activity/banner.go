package activity

import (
	"acg-ai-go-common/gomysql"
	"acg-ai-go-common/logger"
	"acg-ai-go-common/utils"
	"context"
	"digital-human-figure-resource/beans/model"
	"digital-human-figure-resource/beans/proto"
	"net/http"

	"github.com/gin-gonic/gin"
)

type listActivityBannerRequest struct {
	Channel string `json:"channel" form:"channel" binding:"required"`
}

func ListHomeActivityBanner(c *gin.Context) {
	logCtx := context.WithValue(context.TODO(), utils.CtxKeyLogID, utils.RandStringRunes(16))
	req := listActivityBannerRequest{}
	if err := c.Should<PERSON>ind<PERSON>(&req); err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"ListHomeActivityBanner req error: %+v", err)
		c.J<PERSON>(http.StatusOK, proto.NewCommDataRsp(100001, "参数异常", nil))
		return
	}
	logger.Log.Errorf(utils.MMark(logCtx)+"ListHomeActivityBanner req: %+v", req)

	banners, err := (&model.ActivityBannerEntity{}).SelectByChannelAndTime(utils.RandStringRunes(16), gomysql.DB, req.Channel)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"ListHomeActivityBanner SelectByChannelAndTime error: %v", err)
		c.JSON(http.StatusOK, proto.NewCommRsp(100002, "查询数据失败"))
		return
	}
	logger.Log.Infof(utils.MMark(logCtx)+"ListHomeActivityBanner success banners:%+v", banners)
	c.JSON(http.StatusOK, proto.NewSuccessRsp(&banners))
}
