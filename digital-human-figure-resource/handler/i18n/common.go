package i18n

import (
	"fmt"
	"github.com/gin-gonic/gin"
)

const (
	ServiceName string = "digital-human-figure-resource" // 服务名

	FigureDatabaseName  string = "digital_human_star_light" // 数据库名
	FigureDataTableName string = "user_figure"              // 数据表名

	OldFigureDatabaseName  string = "digital_human_plat_saas" // 数据库名
	OldFigureDataTableName string = "character_config"        // 数据表名

	AnimTempDatabaseName  string = "meta_human_editor_saas" // 数据库名
	AnimTempDataTableName string = "dhmh_animation_template"

	SubTag      string = "%s.%s.%s" // 标记类别，结果是 "数据表名.数据列名"
	LanguageKey string = "key_%s"   // 原始 key，结果是 "key_关键词"

	ResponseSubTag string = "response.message" // 接口中的报错信息的tag
)

// GetSubTag 组装 subTag
func GetSubTag(database string, table string, column string) string {
	return fmt.Sprintf(SubTag, database, table, column)
}

// GetLanguageKey 组装语种原始 key
func GetLanguageKey(key string) string {
	return fmt.Sprintf(LanguageKey, key)
}

func GetHeaderLanguage(c *gin.Context) string {
	language := c.GetHeader("Language")
	if len(language) == 0 {
		return "zh"
	}
	return language
}
