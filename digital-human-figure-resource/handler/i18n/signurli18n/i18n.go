package signurli18n

import (
	"acg-ai-go-common/logger"
	"acg-ai-go-common/multilanguage"
	"acg-ai-go-common/utils"
	"context"
	"digital-human-figure-resource/handler/i18n"
	"encoding/json"
)

const (
	tag                        = "signUrl"
	regionBucketMappingI18nKey = "KEY-REGION_BUCKET_MAPPING"
)

type RegionBucket struct {
	Region string `json:"region"` // 区域
	Bucket string `json:"bucket"` // 桶
}

func GetRegionBucketMappingI18n(logCtx context.Context, language string) (map[string]string, error) {
	multilangStr, err := multilanguage.GetMultilangStr(logCtx, i18n.ServiceName, tag, []string{regionBucketMappingI18nKey}, language)
	if err != nil {
		logger.Log.Errorf("%s RegionBucket GetMultilangStr fail, err: %v", utils.MMark(logCtx), err)
		return nil, err
	}
	rbsStr, ok := multilangStr[regionBucketMappingI18nKey]
	if !ok {
		logger.Log.Warnf("%s RegionBucket Mapping not found in i18n", utils.MMark(logCtx))
		return nil, nil
	}
	var rbs map[string]string
	err = json.Unmarshal([]byte(rbsStr), &rbs)
	if err != nil {
		logger.Log.Errorf("%s RegionBucket Unmarshal fail, err: %v", utils.MMark(logCtx), err)
		return nil, err
	}
	return rbs, nil
}
