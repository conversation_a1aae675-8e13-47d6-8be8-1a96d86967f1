package excelutils

import (
	"fmt"
	"github.com/xuri/excelize/v2"
	"reflect"
	"strconv"
)

// ReadExcelToStruct 读取 Excel 并映射到结构体列表
func ReadExcelToStruct[T any](filename string, sheetName string, skipRow int) ([]T, error) {
	f, err := excelize.OpenFile(filename)
	if err != nil {
		return nil, err
	}
	defer f.Close()

	// 获取所有行
	rows, err := f.GetRows(sheetName)
	if err != nil {
		return nil, err
	}

	if len(rows) < skipRow {
		return nil, fmt.Errorf("atleast 2%d rows", skipRow)
	}

	// 解析结构体字段的列号映射
	var result []T
	var structType T
	t := reflect.TypeOf(structType)

	// 创建字段索引映射
	fieldMap := make(map[int]reflect.StructField)
	for i := 0; i < t.NumField(); i++ {
		field := t.Field(i)
		colTag := field.Tag.Get("excel")
		if colTag != "" {
			colIndex, _ := strconv.Atoi(colTag) // 解析列索引
			fieldMap[colIndex] = field
		}
	}

	// 解析数据
	for _, row := range rows[skipRow:] { // 跳过表头
		obj := reflect.New(t).Elem()

		for colIndex, field := range fieldMap {
			if colIndex < len(row) {
				value := row[colIndex]
				fieldValue := obj.FieldByName(field.Name)

				// 根据字段类型转换数据
				switch fieldValue.Kind() {
				case reflect.String:
					fieldValue.SetString(value)
				case reflect.Int:
					if intVal, err := strconv.Atoi(value); err == nil {
						fieldValue.SetInt(int64(intVal))
					}
				default:
					fieldValue.SetString(value)
				}
			}
		}

		result = append(result, obj.Interface().(T))
	}

	return result, nil
}
