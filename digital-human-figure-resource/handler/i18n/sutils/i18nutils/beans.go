package i18nutils

type LanguageItem struct {
	Zh string `json:"zh"`
	En string `json:"en"`
}

type LanguageMap map[string]LanguageItem

const (
	ServerUrl string = "http://100.66.161.54:8723/api/digitalhuman/multi/language/v1/figure/text/upsert"

	DefaultErrorValue string = "Service Unavailable, Text error."
)

type UpdateRequest struct {
	ServiceName string      `json:"serviceName"`
	SubTag      string      `json:"subTag"`
	LanguageMap LanguageMap `json:"languageMap"`
}

type Response struct {
	Code    int    `json:"code"`
	Message string `json:"msg"`
	LogId   string `json:"logId"`
}
