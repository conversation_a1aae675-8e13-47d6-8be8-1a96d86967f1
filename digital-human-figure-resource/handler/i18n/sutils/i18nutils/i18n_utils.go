package i18nutils

import (
	"acg-ai-go-common/utils/httputil"
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"time"
)

// WriteKeyPairs 批量写入多语种键值对
func WriteKeyPairs(ctx context.Context, url string, serviceName string, subTag string, pairs LanguageMap) error {
	client := httputil.NewRetryHTTPClient(5*time.Second, 3)

	req := UpdateRequest{
		ServiceName: serviceName,
		SubTag:      subTag,
		LanguageMap: pairs,
	}

	body, err := json.Marshal(req)
	if err != nil {
		return fmt.Errorf("json Marshal error: %v", err)
	}

	headers := map[string]string{
		"Content-Type": "application/json",
	}

	rsp := Response{}

	res, err := client.DoRequest(ctx, "POST", url, headers, bytes.NewReader(body))

	err = json.Unmarshal(res, &rsp)
	if err != nil {
		return fmt.Errorf("response Unmarshal: { %v }, error: %v", string(res), err)
	}

	if rsp.Code != 0 {
		return fmt.Erro<PERSON>("err in response: %v", rsp)
	}

	return nil
}
