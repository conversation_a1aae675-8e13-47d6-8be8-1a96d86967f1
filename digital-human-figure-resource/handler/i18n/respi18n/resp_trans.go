package respi18n

import (
	"acg-ai-go-common/logger"
	"acg-ai-go-common/multilanguage"
	"acg-ai-go-common/utils"
	"context"
	"digital-human-figure-resource/handler/i18n"
	"digital-human-figure-resource/handler/i18n/sutils/i18nutils"
)

// TransformResponseLocaleByDefault 将回复的报错接入国际化
func TransformResponseLocaleByDefault(logCtx context.Context, lang string, key string) string {
	if len(key) < 1 || len(lang) < 1 {
		logger.Log.Warnf("%sno need for transform", utils.MMark(logCtx))
		return key
	}

	localeKeyList := []string{key}
	logger.Log.Infof("%strans resp: %d locales: %s", utils.MMark(logCtx), len(localeKeyList), lang)

	// 去多语言服务查一下语种结果
	nameMap, err := multilanguage.GetMultilangStr(logCtx, i18n.ServiceName, i18n.ResponseSubTag, localeKeyList, lang)
	if err != nil {
		logger.Log.Errorf("%sget name map in i18n failed: %v", utils.MMark(logCtx), err)
		return key
	}

	// 替换预览视频，有且不为空
	if ret, ok := nameMap[key]; ok && len(ret) > 0 && ret != i18nutils.DefaultErrorValue {
		logger.Log.Infof("%strans resp: %d locales: %s， value: %s", utils.MMark(logCtx), len(localeKeyList), lang, ret)
		return ret
	}

	logger.Log.Warnf("%skey: { %s } not found in i18n", utils.MMark(logCtx), key)
	return key
}
