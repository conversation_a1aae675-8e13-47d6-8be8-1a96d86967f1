package i18nupdate

import (
	"acg-ai-go-common/global"
	"acg-ai-go-common/gomysql"
	"acg-ai-go-common/server"
	"acg-ai-go-common/utils"
	"acg-ai-go-common/utils/mysqlproxy"
	"context"
	"digital-human-figure-resource/beans/model"
	config "digital-human-figure-resource/conf"
	"digital-human-figure-resource/handler/i18n"
	"digital-human-figure-resource/handler/i18n/i18nupdate/datatable"
	"digital-human-figure-resource/handler/i18n/sutils/excelutils"
	"digital-human-figure-resource/handler/i18n/sutils/i18nutils"
	"digital-human-figure-resource/mysqlclient"
	"github.com/BurntSushi/toml"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"log"
	"os"
	"strings"
	"testing"
	"time"
)

const (
	UpdateI18nServer_AnimTemp    bool = true  // 使能多语种服务写入
	UpdateI18nDataTable_AnimTemp bool = false // 使能数据修改
)

// 这个脚本是用来更新user_figure数据表的
func TestAnimationTemplateUpdate(t *testing.T) {
	// 初始化公共配置
	server.InitGlobalSetting()
	if _, err := toml.DecodeFile(global.ConfFilePath, config.LocalConfig); err != nil {
		log.Panicf("local config load failed: {%v}", err)
	}
	log.Printf("======================================================\n")
	log.Printf("local config load success\n")

	path, err := os.Getwd()
	if err != nil {
		log.Panicf("get cur dir failed: %v", err)
		return
	}
	log.Printf("current word dir at: %v.\n", path)

	// 初始化mysql
	mysqlproxy := mysqlproxy.GetMysqlProxy()
	// 开启携程监听mysql是否发生网络中断并进行重连
	go mysqlproxy.MonitorMysqlConnection()

	// 初始化mysql 因为存在多个数据库这里初始化全局map数据库，在使用时使用map[dbName]即可
	mysqlclient.InitDB(mysqlclient.StarLightDBName, config.LocalConfig.StarLightMysqlSetting)
	mysqlclient.InitDB(mysqlclient.PlatDBName, config.LocalConfig.PlatMysqlSetting)
	// 开启携程监听mysql是否发生网络中断并进行重连
	go mysqlclient.MonitorMysqlConnection()
	// 初始化数据库
	model.InitMysqlDBTable()

	// 读入需要更新的数据
	excelPath := "./deploy/dataset/dhmh_animation_template_250318.xlsx"
	sheetName := "animation_template"
	localAnimTempData, err := excelutils.ReadExcelToStruct[datatable.LocalAnimationTemp](excelPath, sheetName, 1)
	if err != nil {
		log.Panicf("read animation_template excel failed: %v", err)
	}
	log.Printf("get animation_template excel: %d\n", len(localAnimTempData))

	// 为了加速查询，生成一个map
	localAnimTempMap := map[string]datatable.LocalAnimationTemp{}
	for _, item := range localAnimTempData {
		localAnimTempMap[item.NameCh] = item
	}
	log.Printf("make animation_template map: %d\n", len(localAnimTempMap))

	animTemps, err := (&model.AnimationTemplate{}).GetAllAnimationTemplate(gomysql.DB)
	if err != nil {
		log.Panicf("get all animation temp failed: %v", err)
	}
	log.Printf("get all animation temp: %d\n", len(animTemps))

	// 用于向多语言服务提交kv键值对的数据
	nameLanguageMap := i18nutils.LanguageMap{}
	for i, _ := range animTemps {
		item, ok := localAnimTempMap[animTemps[i].AnimationName]
		if !ok {
			log.Printf("anim temp: %s not found", animTemps[i].AnimationName)
			continue
		}

		keyword := i18n.GetLanguageKey(convertString(item.NameEn))

		// 修改语种 key
		animTemps[i].LocaleKey = keyword

		// 添加Key
		nameLanguageMap[keyword] = i18nutils.LanguageItem{
			En: strings.TrimRight(item.NameEn, " "),
			Zh: item.NameCh,
		}
	}

	// 批量提交到多语言服务
	if UpdateI18nServer_AnimTemp {
		log.Printf("name: %d k-v pairs in anim temp need write to server\n", len(nameLanguageMap))

		logCtx := context.WithValue(context.TODO(), utils.CtxKeyLogID, utils.RandStringRunes(16))
		nameSubTag := i18n.GetSubTag("meta_human_editor_saas", "dhmh_animation_template", "animationName")

		err = i18nutils.WriteKeyPairs(logCtx, i18nutils.ServerUrl, i18n.ServiceName, nameSubTag, nameLanguageMap)
		if err != nil {
			log.Panicf("write name map failed: %v", err)
		}
	}

	// 批量修改原数据库
	if UpdateI18nDataTable_AnimTemp {
		log.Printf("name: %d columns in anim temp need update\n", len(nameLanguageMap))

		err = gomysql.DB.Transaction(func(tx *gorm.DB) error {
			batchSize := 50

			for i := 0; i < len(animTemps); i += batchSize {
				end := i + batchSize
				if end > len(animTemps) {
					end = len(animTemps)
				}
				batch := animTemps[i:end]

				if err := tx.Clauses(clause.OnConflict{
					Columns:   []clause.Column{{Name: "id"}},
					DoUpdates: clause.AssignmentColumns([]string{"locale_key"}),
				}).Create(&batch).Error; err != nil {
					return err // 发生错误，回滚
				}

				log.Printf("update %d items\n", len(batch))

				// 等待2s，降低压力
				time.Sleep(2 * time.Second)
			}
			return nil
		})

		if err != nil {
			log.Panicf("update dhmh_animation_template failed: %v", err)
		}
	}

	log.Printf("animation template update success\n")
}
