package i18nupdate

import (
	"acg-ai-go-common/global"
	"acg-ai-go-common/gomysql"
	"acg-ai-go-common/logger"
	"context"
	config "digital-human-figure-resource/conf"
	"digital-human-figure-resource/handler/i18n/sutils/i18nutils"
	"encoding/json"
	"flag"
	"fmt"
	"log"
	"os"
	"strings"
	"testing"
	"time"

	"github.com/BurntSushi/toml"
)

type SlideMaterial struct {
	Id         int64     `gorm:"column:id;primary_key;AUTO_INCREMENT" json:"id"`
	MaterialId string    `gorm:"column:material_id;NOT NULL" json:"material_id"`
	Name       string    `gorm:"column:name;NOT NULL" json:"name"`
	UserId     string    `gorm:"column:user_id;NOT NULL" json:"user_id"`
	Url        string    `gorm:"column:url;NOT NULL" json:"url"`
	Type       string    `gorm:"column:type;NOT NULL" json:"type"`
	Module     string    `gorm:"column:module;NOT NULL" json:"module"`
	Creator    string    `gorm:"column:creator;NOT NULL" json:"creator"`
	Thumbnail  string    `gorm:"column:thumbnail" json:"thumbnail"`
	PreviewUrl string    `gorm:"column:preview_url" json:"preview_url"`
	Duration   int       `gorm:"column:duration;default:0;NOT NULL" json:"duration"`
	Height     int       `gorm:"column:height;default:0;NOT NULL" json:"height"`
	Width      int       `gorm:"column:width;default:0;NOT NULL" json:"width"`
	CreateTime time.Time `gorm:"column:create_time;default:CURRENT_TIMESTAMP;NOT NULL" json:"create_time"`
	UpdateTime time.Time `gorm:"column:update_time;default:CURRENT_TIMESTAMP;NOT NULL" json:"update_time"`
	ExtraInfo  string    `gorm:"column:extra_info" json:"extra_info"` // 扩展字段
}

func (data *SlideMaterial) TableName() string {
	return "slide_material"
}

var translationMap = map[string]string{
	"一点星光-横版":        "Starlight-L",
	"健康.png":         "Health",
	"优雅纯净-横版":        "Elegant Pure-L",
	"两极光线-横版":        "Polar Light-L",
	"墨蓝展台-横版":        "Ink Blue Booth-L",
	"商务质感-竖版":        "Biz Texture-P",
	"动感节奏":           "Dynamic Rhythm",
	"办公室.png":        "Office",
	"划重点.gif":        "Highlight",
	"六角推进-动态":        "Hexagon-Dyn",
	"光绘长廊-竖版":        "Light Gallery-P",
	"光影错落-横版":        "Light & Shadow-L",
	"光影交错-竖版":        "Light Interweave-P",
	"元宝.gif":         "Gold Ingot",
	"健身房.png":        "Gym",
	"敲黑板.gif":        "Knock Board",
	"教室.png":         "Classroom",
	"快餐店.png":        "Fast Food",
	"忙碌空间-横版":        "Busy Space-L",
	"心理咨询.png":       "Psy Counsel",
	"彩色棱角-横版":        "Color Edges-L",
	"彩色光圈-横版":        "Color Halos-L",
	"展馆.png":         "Exhibition Hall",
	"宁静海岸":           "Tranquil Coast",
	"复古渐变-竖版":        "Vintage-P",
	"暖调渐变-竖版":        "Warm Gradient-P",
	"暖色环柱-横版":        "Warm Columns-L",
	"暖色光影-竖版":        "Warm Light-P",
	"春龙乐章.mp3":       "Spring Dragon",
	"春风满园.mp3":       "Spring Breeze",
	"春晓之歌.mp3":       "Spring Dawn",
	"春意盈盈.mp3":       "Spring Ambience",
	"春归龙谷.mp3":       "Dragon Valley",
	"明镜空间-竖版":        "Mirror Space-P",
	"明亮国风-横版":        "Bright CN Style-L",
	"方格走线-动态":        "Grid-Dyn",
	"横版-快餐店.png":     "Fast Food-L",
	"横版-心理咨询png.png": "Psy Counsel-L",
	"横版-展馆.png":      "Exhibition Hall-L",
	"横版-前台.png":      "Reception-L",
	"横版-健身房.png":     "Gym-L",
	"横版-健康.png":      "Health-L",
	"梦境舞者":           "Dream Dancer",
	"梦境之声":           "Dream Sound",
	"柔梦之窗-竖版":        "Soft Window-P",
	"木韵光庭-横版":        "Wood Light-L",
	"琴声悠扬":           "Piano Melody",
	"现代简约-横版":        "Modern Minimal-L",
	"玉龙贺新春.mp3":      "Jade Dragon",
	"点赞.gif":         "Like",
	"点击.gif":         "Click",
	"炫彩动线-动态":        "Color Motion-Dyn",
	"炫彩光线-竖版":        "Color Light-P",
	"泛蓝光圈-竖版":        "Blue Halo-P",
	"横版-车展.png":      "Car Exhibition-L",
	"横版-教室.png":      "Classroom-L",
	"空灵长廊-竖版":        "Ethereal-P",
	"空旷走廊-横版":        "Spacious Corridor-L",
	"秘密森林":           "Secret Forest",
	"科技边框-动态":        "Tech Frame-Dyn",
	"科技走线-竖版":        "Tech Lines-P",
	"科技网格-动态":        "Tech Grid-Dyn",
	"科技点线-动态":        "Tech Dots-Dyn",
	"白蓝褶皱-横版":        "White-Blue Fold-L",
	"白色肌理-横版":        "White Texture-L",
	"白色回廊-横版":        "White Corridor-L",
	"瑞雪兆丰登.mp3":      "Snow Harvest",
	"花市龙灯.mp3":       "Lantern Festival",
	"色彩玻璃-动态":        "Color Glass-Dyn",
	"色块转动-动态":        "Color Rotate-Dyn",
	"线条波动-动态":        "Wavy Lines-Dyn",
	"紫梦叠影-横版":        "Purple Shadow-L",
	"紫幻云雾-横版":        "Purple Mist-L",
	"素雅之空-横版":        "Elegant Space-L",
	"箭头.gif":         "Arrow",
	"笙歌满月.mp3":       "Full Moon",
	"窗边静谧-横版":        "Window Peace-L",
	"飘渺思绪":           "Fleeting Thoughts",
	"静谧阳光-竖版":        "Sunlight-P",
	"静谧时光":           "Quiet Time",
	"青色雾感-竖版":        "Cyan Mist-P",
	"问号.gif":         "Question",
	"锣鼓喧天.mp3":       "Drum Beats",
	"金色粒子-动态":        "Gold Particles-Dyn",
	"金色年华-动态":        "Golden Years-Dyn",
	"金币.gif":         "Coin",
	"酒店前台.png":       "Hotel Reception",
	"通透空间-竖版":        "Transparent-P",
	"车展.png":         "Car Exhibition",
	"蓝色渐变-动态":        "Blue Gradient-Dyn",
	"蓝色梦窗-竖版":        "Blue Window-P",
	"蓝绿晕染-横版":        "Turquoise Blend-L",
	"蓝绘空间-竖版":        "Blue Space-P",
	"蓝光之影-竖版":        "Blue Light-P",
}

func InitGlobalSetting() string {
	hostname, err := os.Hostname()
	if err != nil {
		log.Printf("os get hostname fail, err: %v", err)
	}
	global.ServerSetting.Hostname = hostname
	podNamespace := os.Getenv("POD_NAMESPACE")
	// 读取启动参数
	env := flag.String("env", global.DevEnv, "运行环境")
	flag.Parse()
	switch *env {
	case global.TestEnv:
		global.ConfFilePath = "./conf/conf-test.toml"
	case global.ProdEnv:
		global.ConfFilePath = "./conf/conf-prod.toml"
	case global.PreReleaseEnv:
		global.ConfFilePath = "./conf/conf-prerelease.toml"
	}
	global.ServerSetting.RunEnv = *env
	log.Printf("pod namespace: %v", podNamespace)
	if len(podNamespace) > 0 {
		global.ServerSetting.RunEnv = strings.ToLower(podNamespace)
		global.ConfFilePath = "./conf/conf.toml"
	}
	// 加载配置
	if _, err := toml.DecodeFile(global.ConfFilePath, global.ServerSetting); err != nil {
		log.Panicf("env:%v, 配置加载失败: {%v}", global.ServerSetting.RunEnv, err)
	}

	// 配置文件加载完毕后，初始化logger
	logger.SetLogger(logger.SetOpenReportEs(false))
	logger.Log.Printf("run env: [%v], hostname: [%v], conf path:%v",
		global.ServerSetting.RunEnv, hostname, global.ConfFilePath)
	return global.ServerSetting.RunEnv
}

func TestUploadMaterial(t *testing.T) {
	// 初始化公共配置
	InitGlobalSetting()
	if _, err := toml.DecodeFile(global.ConfFilePath, config.LocalConfig); err != nil {
		log.Panicf("local config load failed: {%v}", err)
	}
	log.Printf("======================================================\n")
	log.Printf("local config load success\n")

	gomysql.InitDB(global.ServerSetting.MysqlSetting)

	list := make([]SlideMaterial, 0)
	err := gomysql.DB.Model(&SlideMaterial{}).Where("user_id = ?", "SYSTEM").Find(&list).Error
	if err != nil {
		t.Log(err.Error())
		t.Fatal()
	}
	i := 0
	for _, material := range list {
		if value, ok := translationMap[material.Name]; ok {
			nameLanguageMap := i18nutils.LanguageMap{}
			languageKey := fmt.Sprintf("material:%s", material.MaterialId)
			nameLanguageMap[languageKey] = i18nutils.LanguageItem{
				Zh: material.Name,
				En: value,
			}
			err = i18nutils.WriteKeyPairs(context.Background(), "http://*************:8723/api/digitalhuman/multi/language/v1/figure/text/upsert", "digital-slide", "meta_human_editor_saas.slide_material.name", nameLanguageMap)
			if err != nil {
				t.Log(err.Error())
				t.Fatal()
			}
			i++
		}
	}
}

type CharacterMeta struct {
	Id                    int64     `gorm:"column:id;primary_key;AUTO_INCREMENT" json:"id"`   // id
	CharacterId           string    `gorm:"column:character_id;NOT NULL" json:"character_id"` // character id
	Type                  string    `gorm:"column:type;NOT NULL" json:"type"`                 // type
	Name                  string    `gorm:"column:name;NOT NULL" json:"name"`                 // name
	FigureAlias           string    `gorm:"column:figure_alias;NOT NULL" json:"figure_alias"`
	FigureName            string    `gorm:"column:figure_name;NOT NULL" json:"figure_name"`
	Description           string    `gorm:"column:description;NOT NULL" json:"description"`                                   // description
	Style                 string    `gorm:"column:style;NOT NULL" json:"style"`                                               // style
	CameraList            string    `gorm:"column:camera_list;NOT NULL" json:"camera_list"`                                   // camera list
	MakeupList            string    `gorm:"column:makeup_list" json:"makeup_list"`                                            // makeup_list
	FacialList            string    `gorm:"column:facial_list" json:"facial_list"`                                            // facial_list
	SceneList             string    `gorm:"column:scene_list" json:"scene_list"`                                              // scene_list
	EmotionList           string    `gorm:"column:emotion_list" json:"emotion_list"`                                          // emotion_list
	ShoeStyleList         string    `gorm:"column:shoe_style_list" json:"shoe_style_list"`                                    // shoe_style_list
	BadgeStyleList        string    `gorm:"column:badge_style_list" json:"badge_style_list"`                                  // badge_style_list
	ClothingStyleList     string    `gorm:"column:clothing_style_list" json:"clothing_style_list"`                            // clothing_style_list
	HairStyleList         string    `gorm:"column:hair_style_list" json:"hair_style_list"`                                    // hair_style_list
	AnimojiList           string    `gorm:"column:animoji_list" json:"animoji_list"`                                          // animoji list
	TtsList               string    `gorm:"column:tts_list;NOT NULL" json:"tts_list"`                                         // tts list
	HairStyle             string    `gorm:"column:hair_style;NOT NULL" json:"hair_style"`                                     // hair style
	ClothingStyle         string    `gorm:"column:clothing_style;NOT NULL" json:"clothing_style"`                             // clothing style
	BadgeStyle            string    `gorm:"column:badge_style;NOT NULL" json:"badge_style"`                                   // badge style
	Animojis              string    `gorm:"column:animojis;NOT NULL" json:"animojis"`                                         // animojis
	SupportCallback       int       `gorm:"column:support_callback;default:1;NOT NULL" json:"support_callback"`               // 是否支持回调(0：不支持，1：支持)
	SupportRtcDatachannel int       `gorm:"column:support_rtc_datachannel;default:1;NOT NULL" json:"support_rtc_datachannel"` // 是否支持该 data_channel(0：不支持，1：支持)
	VisibleForSce         int       `gorm:"column:visible_for_sce;default:0;NOT NULL" json:"visible_for_sce"`                 // 是否对SCE可见(0：不支持，1：支持)
	VisibleForLive        int       `gorm:"column:visible_for_live;default:0;NOT NULL" json:"visible_for_live"`               // 是否对直播可见(0：不支持，1：支持)
	VisibleForVisMocap    int       `gorm:"column:visible_for_vis_mocap;default:0;NOT NULL" json:"visible_for_vis_mocap"`     // 是否支持vis动面捕
	Label                 string    `gorm:"column:label;NOT NULL" json:"label"`                                               // character label (2D or 3D)
	AppId                 string    `gorm:"column:app_id;NOT NULL" json:"app_id"`                                             // default appId
	AppKey                string    `gorm:"column:app_key;NOT NULL" json:"app_key"`                                           // default appKey
	FrontImageUrl         string    `gorm:"column:front_image_url;NOT NULL" json:"front_image_url"`                           // front image url
	MaskImageUrl          string    `gorm:"column:mask_image_url;NOT NULL" json:"mask_image_url"`                             // mask image url
	BackgroundImageUrl    string    `gorm:"column:background_image_url;NOT NULL" json:"background_image_url"`                 // background image url
	ThumbnailImageUrl     string    `gorm:"column:thumbnail_image_url;NOT NULL" json:"thumbnail_image_url"`                   // thumbnail image url
	EnableLookFront       int       `gorm:"column:enable_look_front;default:0;NOT NULL" json:"enable_look_front"`
	DeployVersion         int       `gorm:"column:deploy_version;default:0;NOT NULL" json:"deploy_version"`           // deploy version
	CreateTime            time.Time `gorm:"column:create_time;default:CURRENT_TIMESTAMP;NOT NULL" json:"create_time"` // create time
	UpdateTime            time.Time `gorm:"column:update_time;default:CURRENT_TIMESTAMP;NOT NULL" json:"update_time"` // last update time
	Version               int       `gorm:"column:version;NOT NULL" json:"version"`                                   // for optimistic locking
	ConfigSchema          string    `gorm:"column:config_schema" json:"config_schema"`                                // character config schema
	ApiVersion            int       `gorm:"column:api_version;default:1" json:"api_version"`
	ConfigMode            int       `gorm:"column:config_mode;default:0;NOT NULL" json:"config_mode"`
	A2AEmotionList        string    `gorm:"column:a2a_emotion_list" json:"a2a_emotion_list"` // a2a emotion list
	BaseArUrl             string    `gorm:"column:base_ar_url" json:"base_ar_url"`           // base_ar_url
	BaseArMd5             string    `gorm:"column:base_ar_md5" json:"base_ar_md5"`           // base_ar_md5
	ResourceUrl           string    `gorm:"column:resource_url" json:"resource_url"`
	Md5                   string    `gorm:"column:md5" json:"md5"`                                       // md5
	AnimojiEmotionConfig  string    `gorm:"column:animoji_emotion_config" json:"animoji_emotion_config"` // 人像智能动作表情默认标签配置
}

func (data *SlideMaterial) CharacterMeta() string {
	return "character_meta"
}

type BaseCharacterInfo struct {
	Id     string `json:"id"`
	Name   string `json:"name"`
	PicUrl string `json:"picUrl"`
}

func TestPlat(t *testing.T) {
	InitGlobalSetting()
	if _, err := toml.DecodeFile(global.ConfFilePath, config.LocalConfig); err != nil {
		log.Panicf("local config load failed: {%v}", err)
	}
	log.Printf("======================================================\n")
	log.Printf("local config load success\n")

	gomysql.InitDB(global.ServerSetting.MysqlSetting)

	list := make([]CharacterMeta, 0)
	err := gomysql.DB.Model(&CharacterMeta{}).Where("animoji_list != ?", "").Find(&list).Error
	if err != nil {
		log.Panicf("query character meta failed: {%v}", err)
	}
	emMap := make(map[string]string, len(list))
	for _, v := range list {
		emList := make([]BaseCharacterInfo, 0)
		err = json.Unmarshal([]byte(v.AnimojiList), &emList)
		if err != nil {
			t.Log(err)
			continue
		}
		for _, em := range emList {
			emMap[em.Name] = em.Name + "\t"
		}
	}

	for v := range emMap {
		t.Log(v)
	}

}

var actions = map[string]string{
	"跳舞":         "Dance",
	"抬手背":        "Backhand raise",
	"摊左手":        "Left hand reveal",
	"换装":         "Costume change",
	"拜年":         "New Year’s bow",
	"垂手点赞":       "Hanging hand like",
	"竖食指":        "Raise index finger",
	"叉腰1":        "Hips-on-hands1",
	"左下摊手":       "Lower-left hand reveal",
	"右手比三":       "Right hand 3",
	"否定":         "Deny",
	"右手比3":       "Right hand 3",
	"右手指比心":      "Right finger heart",
	"左手大拇指点赞":    "Left thumb like",
	"右手OK":       "Right OK",
	"右手耶":        "Right peace sign",
	"左前摊手":       "Left front hand reveal",
	"右手拍胸":       "Right chest tap",
	"左点头":        "Left nod",
	"问好（不支持说话）":  "Greet (no speech)",
	"左手比1":       "Left hand 1",
	"捧心":         "Heart hold",
	"左手摊手":       "Left hand reveal",
	"展示左上方":      "Point upper left",
	"举左手（笔直）":    "Raise left arm (straight)",
	"抬起双手介绍":     "Introduce with both hands up",
	"右手比耶":       "Right peace sign",
	"左手抬起":       "Left hand lift",
	"右手在前方向右滑":   "Right hand swipe right",
	"单摊手":        "Single hand reveal",
	"弯腰谢谢":       "Bow thanks",
	"表演":         "Perform",
	"点赞":         "Thumbs up",
	"左手打招呼":      "Left hand wave",
	"左手OK":       "Left OK",
	"摇左手指":       "Left finger shake",
	"不同意摇头":      "Disagree head shake",
	"右侧斜向走向中间":   "Walk diagonally right to center",
	"思考":         "Think",
	"单手点赞":       "Single-hand like",
	"比心":         "Heart gesture",
	"摇右手":        "Right hand shake",
	"666点赞":      "666 like",
	"右中摊手":       "Mid-right hand reveal",
	"右手画圈":       "Right hand circle",
	"双手比心偏左":     "Left-leaning heart hands",
	"右手比耶（左手固定）": "Right peace (left fixed)",
	"插手点赞":       "Hands-on-hips like",
	"打招呼":        "Wave",
	"优雅行礼":       "Elegant bow",
	"偷看":         "Peek",
	"摇左手":        "Left hand shake",
	"右上摊手":       "Upper-right hand reveal",
	"加油3":        "Cheer3",
	"站立拜年":       "New Year’s bow",
	"左手上抬":       "Left hand raise",
	"双手指点赞":      "Double finger like",
	"转身向右摊手":     "Turn right + hand reveal",
	"右手手指比心":     "Right finger heart",
	"右手拜拜/打招呼":   "Right goodbye/wave",
	"手抬低比ok":     "Low OK",
	"高兴":         "Happy",
	"摇双手":        "Both hands shake",
	"半颗比心":       "Half-heart",
	"举手指":        "Raise finger",
	"我":          "Me",
	"双手捧于胸":      "Hands on chest",
	"左手比耶":       "Left peace",
	"向左挥手":       "Wave left",
	"双手捧于胸前":     "Hands clasped at chest",
	"右手赞":        "Right like",
	"双手食指拇指比心":   "Thumb-index heart",
	"打哈欠":        "Yawn",
	"左中摊手":       "Mid-left hand reveal",
	"闲时":         "Idle",
	"展示右下方":      "Point lower right",
	"没听清":        "Unclear",
	"基础站_思考":     "Base stance_think",
	"摆头":         "Head tilt",
	"单指拒绝":       "Single-finger refusal",
	"自信满满":       "Confident",
	"作揖":         "Chinese salute",
	"上摊手":        "Upward hand reveal",
	"可爱点头":       "Cute nod",
	"手指相对":       "Fingertips touch",
	"左手点赞":       "Left thumb like",
	"展示左下方":      "Point lower left",
	"举左手":        "Raise left arm",
	"指向右走":       "Point right",
	"右侧走出":       "Exit right",
	"拍手掌":        "Clap",
	"右下摊手":       "Lower-right hand reveal",
	"可爱打招呼":      "Cute wave",
	"右摊手":        "Right hand reveal",
	"向前走":        "Walk forward",
	"聆听":         "Listen",
	"前摊手1":       "Front hand reveal1",
	"右wink":      "Right wink",
	"向左走":        "Walk left",
	"连线坐姿":       "Seated connection",
	"右手倾听":       "Right listen",
	"合手站":        "Clasped hands stance",
	"恭候":         "Wait politely",
	"摇头no":       "Head shake no",
	"叉腰2":        "Hips-on-hands2",
	"鼓掌":         "Applause",
	"右手飞吻（左手固定）": "Right air kiss (left fixed)",
	"右点头":        "Right nod",
	"再见":         "Goodbye",
	"左手指比心":      "Left finger heart",
	"可爱思考":       "Cute think",
	"露齿笑":        "Smile with teeth",
	"左胸比心":       "Left chest heart",
	"疑惑":         "Confused",
	"阳光笑容":       "Sunny smile",
	"侧身鞠躬":       "Side bow",
	"前摊手3":       "Front hand reveal3",
	"转身向左摊手":     "Turn left + hand reveal",
	"双手比耶":       "Double peace",
	"比耶-胜利":      "Peace-victory",
	"做手势":        "Gesture",
	"右手飞吻":       "Right air kiss",
	"抬手心":        "Palm raise",
	"右手上抬":       "Right hand lift",
	"表示自己":       "Self-point",
	"右手在前方向左滑":   "Right hand swipe left",
	"右手NO":       "Right NO",
	"左手画圈":       "Left hand circle",
	"左手NO":       "Left NO",
	"双手比心偏右":     "Right-leaning heart hands",
	"展示前方":       "Point front",
	"同意":         "Agree",
	"左手666":      "Left 666",
	"摊右手":        "Right reveal",
	"可爱右摊手":      "Cute right reveal",
	"点头":         "Nod",
	"左手比2":       "Left hand 2",
	"DJ":         "DJ",
	"展示右":        "Point right",
	"左手边比3":      "Left hand 3",
	"左手挥手":       "Left wave",
	"右手大拇指点赞":    "Right thumb like",
	"思考1":        "Think1",
	"双臂微弯2":      "Bent arms2",
	"双手OK":       "Double OK",
	"左拳头打开":      "Left fist open",
	"摊双手":        "Both hands reveal",
	"展示左问题列表":    "Left Q-list",
	"双手赞":        "Double like",
	"双手打招呼":      "Double wave",
	"剪刀手":        "Scissors hands",
	"双手交叉摇晃":     "Crossed hands shake",
	"右手打招呼":      "Right wave",
	"拍手":         "Clap",
	"右手yes":      "Right YES",
	"加油":         "Cheer",
	"双手飞吻":       "Double air kiss",
	"生气":         "Angry",
	"右手比二":       "Right hand 2",
	"双手比心":       "Heart hands",
	"右手加油":       "Right cheer",
	"倒计时321":     "Countdown 3-2-1",
	"双臂比心":       "Arm heart",
	"举右手":        "Raise right arm",
	"加油2":        "Cheer2",
	"转圈":         "Spin",
	"问好":         "Greet",
	"哭泣":         "Cry",
	"emo":        "Emo",
	"向右挥手":       "Wave right",
	"欢迎":         "Welcome",
	"向右走":        "Walk right",
	"背手摇头":       "Hands-behind-head shake",
	"手指下巴思考2":    "Chin think2",
	"感谢/鞠躬":      "Thanks/bow",
	"左手比3":       "Left hand 3",
	"比耶":         "Peace",
	"标准双摊手":      "Standard double reveal",
	"左右摇摆":       "Sway",
	"手背后":        "Hands behind",
	"锤手":         "Fist pound",
	"左WINK":      "Left wink",
	"握手":         "Handshake",
	"背手摇曳":       "Sway hands behind",
	"挥手":         "Wave",
	"双手大拇指点赞":    "Double thumbs up",
	"摊手":         "Hand reveal",
	"展示中前方":      "Point center front",
	"展示右上方":      "Point upper right",
	"单手比心":       "Single-hand heart",
	"展示右问题列表":    "Right Q-list",
	"双手耶":        "Double peace",
	"侧身点头聆听":     "Side nod listen",
	"右手拜拜":       "Right goodbye",
	"惊讶":         "Surprised",
	"向左走出屏幕":     "Exit screen left",
	"双手指比心":      "Finger heart",
	"手抬高比ok":     "High OK",
	"害羞":         "Shy",
	"食指互戳":       "Finger poke",
	"左手加油":       "Left cheer",
	"伸手指引":       "Guide gesture",
	"抱臂思考":       "Arms-crossed think",
	"摇头":         "Head shake",
	"双手挥手":       "Double wave",
	"双手手指比心":     "Finger heart",
	"悄悄话":        "Whisper",
	"手指下巴思考1":    "Chin think1",
	"双手666":      "Double 666",
	"右手比2":       "Right hand 2",
	"自然握手":       "Casual handshake",
	"左手赞":        "Left like",
	"环形视频左滑":     "Video swipe left",
	"左手飞吻":       "Left air kiss",
	"手掌相对着抬起":    "Palms rise",
	"翻手":         "Hand flip",
	"左上摊手":       "Upper-left reveal",
	"右手666":      "Right 666",
	"倒数":         "Countdown",
	"不是哦":        "Nope",
	"右手指向自己":     "Right self-point",
	"右手摊手":       "Right hand reveal",
	"右手比心":       "Right heart",
	"卖萌":         "Cute pose",
	"左手摇手指":      "Left finger shake",
	"思考2":        "Think2",
	"踢脚":         "Kick",
	"请大声点":       "Speak louder",
	"抱歉":         "Sorry",
	"左侧走出":       "Exit left",
	"摇右手指":       "Right finger shake",
	"介绍自己":       "Introduce self",
	"鬼脸啊":        "Funny face",
	"指向左走":       "Point left",
	"手掌相对抬起":     "Palms rise",
	"双手摊手":       "Double reveal",
	"飞吻":         "Air kiss",
	"下摊手":        "Downward reveal",
	"撒娇":         "Coy",
	"左手耶":        "Left peace",
	"左手拜拜":       "Left goodbye",
	"右肩比心":       "Right shoulder heart",
	"右手抬起":       "Right hand lift",
	"双手抬起":       "Both hands lift",
	"环臂摇晃":       "Arm sway",
	"双手摊":        "Hands reveal",
	"走进":         "Enter",
	"举右手（笔直）":    "Raise right arm (straight)",
	"微笑":         "Smile",
	"双摊手":        "Double reveal",
	"右手大拇指点赞（左手固定）": "Right thumb like (left fixed)",
	"举左手指":     "Left finger raise",
	"标准右摊手":    "Standard right reveal",
	"展示左":      "Point left",
	"右手点赞":     "Right like",
	"可爱站":      "Cute stance",
	"双臂微弯1":    "Bent arms1",
	"双手点赞":     "Double like",
	"抬手":       "Hand raise",
	"环形视频右滑":   "Video swipe right",
	"可爱前摊手":    "Cute front reveal",
	"前摊手2":     "Front reveal2",
	"向右走出屏幕":   "Exit screen right",
	"左手拍胸":     "Left chest tap",
	"双手上抬":     "Both hands lift",
	"同意手势OK":   "Agree OK",
	"摆手拒绝":     "Wave refusal",
	"双手拍胸":     "Double chest tap",
	"左摊手":      "Left reveal",
	"呼吸态":      "Breathing pose",
	"合手":       "Clasped hands",
	"展示左侧列表":   "Left list",
	"鞠躬":       "Bow",
	"左手yes":    "Left YES",
	"单手强调":     "Single-hand emphasis",
	"左手比心":     "Left heart",
	"拜拜":       "Bye",
	"弯腰拜年":     "Bow New Year",
	"蠕动":       "Wiggle",
	"右手挥手":     "Right wave",
	"双手加油":     "Double cheer",
	"右手上指":     "Right point up",
	"右手比1":     "Right 1",
	"感谢":       "Gratitude",
	"右WINK":    "Right wink",
	"可爱左摊手":    "Cute left reveal",
	"双手ok":     "Double OK",
	"胜利":       "Victory",
	"出现":       "Appear",
	"十指交叉":     "Fingers interlock",
	"走出":       "Exit",
	"右手no":     "Right NO",
	"右手撩拨头发":   "Right hair flip",
	"双手恭拜":     "Double bow",
	"前摊手":      "Front reveal",
	"左手上指":     "Left point up",
	"右手比一":     "Right 1",
	"展示右侧列表":   "Right list",
	"初次见面":     "First meet",
	"左侧斜向走向中间": "Walk diagonally left to center",
	"右前摊手":     "Right front reveal",
	"标准左摊手":    "Standard left reveal",
	"招手":       "Beckon",
	"左手手指比心":   "Left finger heart",
}

var testActionMap = map[string]string{
	"左手加油": "Left cheer",
}

func TestUploadAction(t *testing.T) {

	for key, value := range actions {
		nameLanguageMap := i18nutils.LanguageMap{}
		languageKey := fmt.Sprintf("emotion:%s", key)
		nameLanguageMap[languageKey] = i18nutils.LanguageItem{
			Zh: key,
			En: value,
		}
		err := i18nutils.WriteKeyPairs(context.Background(), "http://*************:8723/api/digitalhuman/multi/language/v1/figure/text/upsert", "digital-human-plat", "digital_human_plat_saas.character_meta.emotion.name", nameLanguageMap)
		if err != nil {
			t.Log(err.Error())
			t.Fatal()
		}
	}
}
