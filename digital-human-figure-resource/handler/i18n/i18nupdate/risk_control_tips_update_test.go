package i18nupdate

import (
	"acg-ai-go-common/utils"
	"context"
	"digital-human-figure-resource/handler/i18n/i18nupdate/datatable"
	"digital-human-figure-resource/handler/i18n/sutils/excelutils"
	"digital-human-figure-resource/handler/i18n/sutils/i18nutils"
	"log"
	"strings"
	"testing"
)

const (
	UpdateI18nServerRiskControl bool = true // 使能多语种服务写入
)

// 这个脚本是用来更新部分面向前端的请求内包含了中文回复的
func TestRiskControlTipsUpdate(t *testing.T) {
	// 读入需要更新的数据
	excelPath := "./deploy/dataset/risk_control_tips_250319.xlsx"
	logCtx := context.WithValue(context.TODO(), utils.CtxKeyLogID, utils.RandStringRunes(16))

	serviceName := "dhlive-external-api"

	// 全部
	err := importSheetData(logCtx, excelPath, "public", 1, serviceName, "risk_control.err_msg.public",
		UpdateI18nServerRiskControl)
	if err != nil {
		log.Panicf("update risk control pic&text failed: %v", err)
	}

	log.Printf("risk control tips i18n update success\n")
}

func importSheetData(logCtx context.Context, file string, sheet string, skip int, serviceName string, subTag string,
	upload bool) error {
	// 用于向多语言服务提交kv键值对的数据
	languageMap := i18nutils.LanguageMap{}

	localData, err := excelutils.ReadExcelToStruct[datatable.LocalI18nTips](file, sheet, skip)
	if err != nil {
		return err
	}
	log.Printf("get %s excel: %d\n", sheet, len(localData))

	// 遍历更新数据条目
	for _, item := range localData {
		item.Keyword = strings.TrimRight(item.Keyword, " ")
		if len(item.Keyword) < 1 {
			continue
		}
		// 添加Key
		languageMap[item.Keyword] = i18nutils.LanguageItem{
			En: strings.TrimRight(item.DescriptionEn, " "),
			Zh: strings.TrimRight(item.DescriptionCh, " "),
		}
	}

	// 批量提交到多语言服务
	if upload {
		err = i18nutils.WriteKeyPairs(logCtx, i18nutils.ServerUrl, serviceName, subTag, languageMap)
		if err != nil {
			return err
		}
		log.Printf(" subTag: %s, %d  k-v pairs need write to server\n", subTag, len(languageMap))
	}
	return nil
}
