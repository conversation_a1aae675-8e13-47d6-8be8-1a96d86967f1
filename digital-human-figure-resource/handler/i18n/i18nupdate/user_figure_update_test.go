package i18nupdate

import (
	"acg-ai-go-common/global"
	"acg-ai-go-common/server"
	"acg-ai-go-common/utils"
	"acg-ai-go-common/utils/mysqlproxy"
	"context"
	"digital-human-figure-resource/beans/model"
	config "digital-human-figure-resource/conf"
	"digital-human-figure-resource/handler/i18n"
	"digital-human-figure-resource/handler/i18n/i18nupdate/datatable"
	"digital-human-figure-resource/handler/i18n/sutils/excelutils"
	"digital-human-figure-resource/handler/i18n/sutils/i18nutils"
	"digital-human-figure-resource/mysqlclient"
	"fmt"
	"github.com/BurntSushi/toml"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"log"
	"os"
	"strings"
	"testing"
	"time"
)

const (
	UpdateI18nServer bool = true  // 使能多语种服务写入
	UpdateUserFigure bool = false // 使能数据修改
)

// 这个脚本是用来更新user_figure数据表的
func TestUserFigureUpdate(t *testing.T) {
	// 初始化公共配置
	server.InitGlobalSetting()
	if _, err := toml.DecodeFile(global.ConfFilePath, config.LocalConfig); err != nil {
		log.Panicf("local config load failed: {%v}", err)
	}
	log.Printf("======================================================\n")
	log.Printf("local config load success\n")

	path, err := os.Getwd()
	if err != nil {
		log.Panicf("get cur dir failed: %v", err)
		return
	}
	log.Printf("current word dir at: %v.\n", path)

	// 初始化mysql
	mysqlproxy := mysqlproxy.GetMysqlProxy()
	// 开启携程监听mysql是否发生网络中断并进行重连
	go mysqlproxy.MonitorMysqlConnection()

	// 初始化mysql 因为存在多个数据库这里初始化全局map数据库，在使用时使用map[dbName]即可
	mysqlclient.InitDB(mysqlclient.StarLightDBName, config.LocalConfig.StarLightMysqlSetting)
	mysqlclient.InitDB(mysqlclient.PlatDBName, config.LocalConfig.PlatMysqlSetting)
	// 开启携程监听mysql是否发生网络中断并进行重连
	go mysqlclient.MonitorMysqlConnection()
	// 初始化数据库
	model.InitMysqlDBTable()

	// 读入需要更新的数据
	excelPath := "./deploy/dataset/user_figure_250318.xlsx"
	sheetName := "user_figure"
	localUserFigureData, err := excelutils.ReadExcelToStruct[datatable.LocalUserFigure](excelPath, sheetName, 2)
	if err != nil {
		log.Panicf("read user_figure excel failed: %v", err)
	}
	log.Printf("get user_figure excel: %d\n", len(localUserFigureData))

	// 为了加速查询，生成一个map
	localUserFigureMap := map[string]datatable.LocalUserFigure{}
	for _, item := range localUserFigureData {
		localUserFigureMap[item.NameCh] = item
	}
	log.Printf("make user_figure map: %d\n", len(localUserFigureMap))

	// 读出数据库中所有的公共人像
	userFigures, err := (&model.UserFigure{}).GetAllPublicUserFigure(mysqlclient.DbMap[mysqlclient.StarLightDBName])
	if err != nil {
		log.Panicf("get all public user_figure failed: %v", err)
	}
	log.Printf("get public user_figure: %d\n", len(userFigures))

	// 用于向多语言服务提交kv键值对的数据
	nameLanguageMap := i18nutils.LanguageMap{}
	urlLanguageMap := i18nutils.LanguageMap{}

	// 遍历更新数据条目
	for i, _ := range userFigures {
		item, ok := localUserFigureMap[userFigures[i].Name]
		if !ok {
			log.Printf("user figure: %s not found", userFigures[i].Name)
			continue
		}

		// 多语言的 keyword
		keyword := i18n.GetLanguageKey(convertString(item.NameEn))

		{
			// 这里用来增加序号,处理重复的英文名
			_, ok := nameLanguageMap[keyword]
			if ok {
				for m := 1; m < 100; m++ {
					newKeyword := fmt.Sprintf("%s_%02d", keyword, m)
					_, v := nameLanguageMap[newKeyword]
					if !v {
						log.Printf("user figure[%d]: %s, key: %s already existed, change to %s", userFigures[i].ID,
							userFigures[i].Name, keyword, newKeyword)

						keyword = newKeyword
						break
					}
				}
			}
		}

		// 修改语种 key 值
		userFigures[i].LocaleKey = keyword

		// 添加Key
		nameLanguageMap[keyword] = i18nutils.LanguageItem{
			En: convertString(item.NameEn),
			Zh: convertString(item.NameCh),
		}

		urlLanguageMap[keyword] = i18nutils.LanguageItem{
			En: item.VideoUrlEn,
			Zh: item.VideoUrlCh,
		}
	}

	// 批量提交到多语言服务
	if UpdateI18nServer {
		log.Printf("name: %d and url: %d  k-v pairs in user figure need write to server\n", len(nameLanguageMap),
			len(urlLanguageMap))

		logCtx := context.WithValue(context.TODO(), utils.CtxKeyLogID, utils.RandStringRunes(16))
		nameSubTag := i18n.GetSubTag("digital_human_star_light", "user_figure", "name")
		urlSubTag := i18n.GetSubTag("digital_human_star_light", "user_figure", "template_video")

		err = i18nutils.WriteKeyPairs(logCtx, i18nutils.ServerUrl, i18n.ServiceName, nameSubTag, nameLanguageMap)
		if err != nil {
			log.Panicf("write name map failed: %v", err)
		}

		err = i18nutils.WriteKeyPairs(logCtx, i18nutils.ServerUrl, i18n.ServiceName, urlSubTag, urlLanguageMap)
		if err != nil {
			log.Panicf("write url map failed: %v", err)
		}
	}

	// 批量修改原数据库
	if UpdateUserFigure {
		log.Printf("name: %d and url: %d columns in user figure need update\n", len(nameLanguageMap), len(urlLanguageMap))

		err = mysqlclient.DbMap[mysqlclient.StarLightDBName].Transaction(func(tx *gorm.DB) error {
			batchSize := 50

			for i := 0; i < len(userFigures); i += batchSize {
				end := i + batchSize
				if end > len(userFigures) {
					end = len(userFigures)
				}
				batch := userFigures[i:end]

				if err := tx.Clauses(clause.OnConflict{
					Columns:   []clause.Column{{Name: "id"}},
					DoUpdates: clause.AssignmentColumns([]string{"locale_key"}),
				}).Create(&batch).Error; err != nil {
					return err // 发生错误，回滚
				}

				log.Printf("update %d items\n", len(batch))

				// 等待2s，降低压力
				time.Sleep(2 * time.Second)
			}
			return nil
		})

		if err != nil {
			log.Panicf("update user_figure failed: %v", err)
		}
	}

	log.Printf("user figure update success\n")
}

func convertString(input string) string {
	result := strings.ReplaceAll(input, " ", "")
	return result
}
