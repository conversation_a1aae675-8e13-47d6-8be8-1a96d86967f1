package datatable

// LocalUserFigure 结构体，与 Excel 的列对应
type LocalUserFigure struct {
	ID         int    `excel:"0"`
	UserId     string `excel:"1"`
	NameCh     string `excel:"5"`
	NameEn     string `excel:"6"`
	VideoUrlCh string `excel:"12"`
	VideoUrlEn string `excel:"8"`
}

// LocalOldUserFigure 结构体，与 Excel 的列对应
type LocalOldUserFigure struct {
	Key    string `excel:"0"`
	NameZh string `excel:"1"`
	NameEn string `excel:"2"`
	UrlZh  string `excel:"3"`
	UrlEn  string `excel:"4"`
}
