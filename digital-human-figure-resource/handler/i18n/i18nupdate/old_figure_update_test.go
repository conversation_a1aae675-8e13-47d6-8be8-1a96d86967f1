package i18nupdate

import (
	"acg-ai-go-common/utils"
	"context"
	"digital-human-figure-resource/handler/i18n"
	"digital-human-figure-resource/handler/i18n/i18nupdate/datatable"
	"digital-human-figure-resource/handler/i18n/sutils/excelutils"
	"digital-human-figure-resource/handler/i18n/sutils/i18nutils"
	"log"
	"strings"
	"testing"
)

const (
	UpdateI18nServerOldFigure bool = true // 使能多语种服务写入
)

// 这个脚本是用来更新部分面向前端的请求内包含了中文回复的
func TestUpdateI18nServerOldFigureUpdate(t *testing.T) {
	// 读入需要更新的数据
	excelPath := "./deploy/dataset/old_figure_250326.xlsx"
	sheetName := "figure"
	localResponseData, err := excelutils.ReadExcelToStruct[datatable.LocalOldUserFigure](excelPath, sheetName, 1)
	if err != nil {
		log.Panicf("read old figrure excel failed: %v", err)
	}
	log.Printf("get old figrure excel: %d\n", len(localResponseData))

	// 用于向多语言服务提交kv键值对的数据
	nameLanguageMap := i18nutils.LanguageMap{}
	urlLanguageMap := i18nutils.LanguageMap{}

	// 遍历更新数据条目
	for _, item := range localResponseData {
		key := strings.TrimRight(item.Key, " ")

		// 添加Key
		nameLanguageMap[key] = i18nutils.LanguageItem{
			En: strings.TrimRight(item.NameEn, " "),
			Zh: strings.TrimRight(item.NameZh, " "),
		}

		// 添加Key
		urlLanguageMap[key] = i18nutils.LanguageItem{
			En: strings.TrimRight(item.UrlEn, " "),
			Zh: strings.TrimRight(item.UrlZh, " "),
		}
	}

	// 批量提交到多语言服务
	if UpdateI18nServerOldFigure {
		log.Printf(" %d  k-v pairs need write to server\n", len(nameLanguageMap))

		logCtx := context.WithValue(context.TODO(), utils.CtxKeyLogID, utils.RandStringRunes(16))
		nameSubTag := "digital_human_plat_saas.character_config.name"

		err = i18nutils.WriteKeyPairs(logCtx, i18nutils.ServerUrl, i18n.ServiceName, nameSubTag, nameLanguageMap)
		if err != nil {
			log.Panicf("write i18n k-v map failed: %v", err)
		}

		urlSubTag := "digital_human_plat_saas.character_config.video_url"

		err = i18nutils.WriteKeyPairs(logCtx, i18nutils.ServerUrl, i18n.ServiceName, urlSubTag, urlLanguageMap)
		if err != nil {
			log.Panicf("write i18n k-v map failed: %v", err)
		}
	}

	log.Printf("old figure tips i18n update success\n")
}
