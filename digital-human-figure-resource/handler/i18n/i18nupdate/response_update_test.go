package i18nupdate

import (
	"acg-ai-go-common/utils"
	"context"
	"digital-human-figure-resource/handler/i18n"
	"digital-human-figure-resource/handler/i18n/i18nupdate/datatable"
	"digital-human-figure-resource/handler/i18n/sutils/excelutils"
	"digital-human-figure-resource/handler/i18n/sutils/i18nutils"
	"log"
	"strings"
	"testing"
)

const (
	UpdateI18nServerResponse bool = true // 使能多语种服务写入
)

// 这个脚本是用来更新部分面向前端的请求内包含了中文回复的
func TestResponseUpdate(t *testing.T) {
	// 读入需要更新的数据
	excelPath := "./deploy/dataset/response_tips_250318.xlsx"
	sheetName := "response"
	localResponseData, err := excelutils.ReadExcelToStruct[datatable.LocalI18nTips](excelPath, sheetName, 1)
	if err != nil {
		log.Panicf("read response description excel failed: %v", err)
	}
	log.Printf("get response description excel: %d\n", len(localResponseData))

	// 用于向多语言服务提交kv键值对的数据
	languageMap := i18nutils.LanguageMap{}

	// 遍历更新数据条目
	for _, item := range localResponseData {
		// todo：这里需要注意，本脚本使用中文作为 key
		key := strings.TrimRight(item.DescriptionCh, " ")

		// 添加Key
		languageMap[key] = i18nutils.LanguageItem{
			En: strings.TrimRight(item.DescriptionEn, " "),
			Zh: strings.TrimRight(item.DescriptionCh, " "),
		}
	}

	// 批量提交到多语言服务
	if UpdateI18nServerResponse {
		log.Printf(" %d  k-v pairs need write to server\n", len(languageMap))

		logCtx := context.WithValue(context.TODO(), utils.CtxKeyLogID, utils.RandStringRunes(16))
		nameSubTag := "response.message"

		err = i18nutils.WriteKeyPairs(logCtx, i18nutils.ServerUrl, i18n.ServiceName, nameSubTag, languageMap)
		if err != nil {
			log.Panicf("write i18n k-v map failed: %v", err)
		}
	}

	log.Printf("response tips i18n update success\n")
}
