package timbrei18n

import (
	"acg-ai-go-common/logger"
	"acg-ai-go-common/multilanguage"
	"acg-ai-go-common/utils"
	"context"
	"digital-human-figure-resource/beans/model"
	"digital-human-figure-resource/handler/i18n/sutils/i18nutils"
	"digital-human-figure-resource/mysqlclient"
	"encoding/json"
	"fmt"
)

// TransformTimbreLocale 用来转换人像列表中TTS语言相关的字段
func TransformTimbreLocale(logCtx context.Context, lang string, list []*model.UserFigure) ([]*model.UserFigure,
	error) {
	outList := list

	// 整合ID列表
	idList := []int64{}
	for _, item := range list {
		matchList, err := UnmarshalTtsPerson(logCtx, item)
		if err != nil {
			logger.Log.Warnf("%strans figure tts: {%v}, failed: %v", utils.MMark(logCtx), item, err)
			continue
		}

		for _, tts := range matchList {
			if len(tts.Name) > 0 {
				idList = append(idList, tts.Id)
			}
		}
	}
	if len(idList) < 1 {
		logger.Log.Infof("%sno valid id need translate", utils.MMark(logCtx))
		return nil, fmt.Errorf("no need for trans")
	}
	logger.Log.Infof("%stotal trans timbre IDs: %d locale: %s", utils.MMark(logCtx), len(idList), lang)

	// 查询数据库
	timbreList, err := (&model.TtsPerson{}).GetPublicTimbre(logCtx, mysqlclient.DbMap[mysqlclient.StarLightDBName], idList)
	if err != nil {
		logger.Log.Errorf("%sget timbre from ttsPerson failed: %v", utils.MMark(logCtx), err)
		return nil, fmt.Errorf("get timbre from ttsPerson failed: %v", err)
	}
	if len(timbreList) < 1 {
		logger.Log.Infof("%sno public timbre need translate", utils.MMark(logCtx))
		return nil, fmt.Errorf("no need for trans")
	}
	logger.Log.Infof("%stotal public timbre IDs: %d locale: %s", utils.MMark(logCtx), len(idList), lang)

	// 整合多语种 key
	localeKeyList := []string{}
	timbreNameMap := map[int64]string{}
	for _, item := range timbreList {
		localeKeyList = append(localeKeyList, item.Name)
		timbreNameMap[int64(item.ID)] = item.Name
	}

	// 查询多语种
	nameMap, err := multilanguage.GetMultilangStr(logCtx, TimbreServiceName, TimbreNameSubTag, localeKeyList, lang)
	if err != nil {
		logger.Log.Errorf("%sget timbre name map in i18n failed: %v", utils.MMark(logCtx), err)
		return nil, fmt.Errorf("get timbre name map in i18n failed: %v", err)
	}
	logger.Log.Infof("%sGetMultilangStr: %d locale: %s", utils.MMark(logCtx), len(nameMap), lang)

	// 填充
	for i, _ := range list {
		matchList, err := UnmarshalTtsPerson(logCtx, list[i])
		if err != nil {
			logger.Log.Warnf("%strans figure tts: {%v}, failed: %v", utils.MMark(logCtx), list[i], err)
			continue
		}

		for j, _ := range matchList {
			// 需要翻译
			if key, tOk := timbreNameMap[matchList[j].Id]; tOk {
				if value, nOk := nameMap[key]; nOk && len(value) > 0 && value != i18nutils.DefaultErrorValue {
					matchList[j].Name = value
				}
			}
		}

		// 序列化回去
		jsonData, err := json.Marshal(matchList)
		if err != nil {
			logger.Log.Errorf("%smarshal timbre: %v, with i18n failed: %v", utils.MMark(logCtx), matchList, err)
			continue
		}
		list[i].MatchTTS = string(jsonData)
	}

	return outList, nil
}

// UnmarshalTtsPerson 将人像中的TTS配置解析为标准结构数据
func UnmarshalTtsPerson(logCtx context.Context, figure *model.UserFigure) (MatchTts, error) {
	if figure == nil {
		return nil, fmt.Errorf("input figure is empty")
	}
	if len(figure.MatchTTS) < 1 {
		return nil, fmt.Errorf("input figure.matchTTS is empty")
	}

	ret := MatchTts{}
	err := json.Unmarshal([]byte(figure.MatchTTS), &ret)
	if err != nil {
		return nil, fmt.Errorf("unmarshal figure.matchTTS failed: %v", err)
	}

	return ret, nil
}
