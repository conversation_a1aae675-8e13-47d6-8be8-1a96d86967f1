package timbrei18n

const (
	TimbreServiceName string = "tts-square"
	TimbreNameSubTag  string = "digital_human_star_light.tts_person.name"
)

type MatchTts []TtsPerson

type TtsPerson struct {
	Id           int64           `json:"id"`
	Name         string          `json:"name"`
	Per          string          `json:"per"`
	Config       TtsPersonConfig `json:"config"`
	Extra        TtsPersonExtra  `json:"extra"`
	Describe     string          `json:"describe"`
	ThumbnailUrl string          `json:"thumbnailUrl"`
}

type TtsPersonConfig struct {
	Pit string `json:"pit"`
	Spd string `json:"spd"`
	Vol string `json:"vol"`
}

type TtsPersonExtra struct {
	AudioCtrl string `json:"audio_ctrl"`
}
