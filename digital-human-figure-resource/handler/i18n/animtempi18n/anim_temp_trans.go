package animtempi18n

import (
	"acg-ai-go-common/logger"
	"acg-ai-go-common/multilanguage"
	"acg-ai-go-common/utils"
	"context"
	"digital-human-figure-resource/beans/model"
	"digital-human-figure-resource/handler/i18n"
	"digital-human-figure-resource/handler/i18n/sutils/i18nutils"
	"fmt"
)

// TransformAnimTempLocale 用来转换动画模板列表中所有的语言相关的字段
func TransformAnimTempLocale(logCtx context.Context, lang string, list []*model.AnimationTemplate) ([]*model.
	AnimationTemplate, error) {
	outList := list

	localeKeyList := []string{} // 语种的keyList
	for _, item := range list {
		localeKeyList = append(localeKeyList, item.LocaleKey)
	}
	logger.Log.Infof("%strans animationTemp: %d locales: %d", utils.MMark(logCtx), len(list), len(localeKeyList))

	if len(localeKeyList) < 1 {
		return nil, fmt.Errorf("no need for trans")
	}

	// 去多语言服务查一下语种结果
	nameMap, err := multilanguage.GetMultilangStr(logCtx, i18n.ServiceName, i18n.GetSubTag(i18n.AnimTempDatabaseName,
		i18n.AnimTempDataTableName, "AnimationName"), localeKeyList, lang)
	if err != nil {
		logger.Log.Errorf("%sget name map in i18n failed: %v", utils.MMark(logCtx), err)
		return nil, fmt.Errorf("get name map in i18n failed: %v", err)
	}

	// 填充结果
	for i, _ := range outList {
		// 多语种key不存在则不处理
		if len(outList[i].LocaleKey) < 1 {
			continue
		}

		// 替换名称，有且不为空
		if ret, ok := nameMap[outList[i].LocaleKey]; ok && len(ret) > 0 && ret != i18nutils.DefaultErrorValue {
			outList[i].AnimationName = ret
		}
	}

	return outList, nil
}
