package figurei18n

import (
	"acg-ai-go-common/logger"
	"acg-ai-go-common/multilanguage"
	"acg-ai-go-common/utils"
	"context"
	"digital-human-figure-resource/handler/i18n"
	"fmt"
)

// GetKeyWordListWithName 通过语种和关键词，反向查询所有的相关key
func GetKeyWordListWithName(logCtx context.Context, lang string, searchName string) ([]string, error) {
	if len(searchName) < 1 {
		return nil, fmt.Errorf("search name is empty")
	}

	keyMaps, err := multilanguage.GetMultilangKey(logCtx, i18n.ServiceName, i18n.GetSubTag(i18n.FigureDatabaseName,
		i18n.FigureDataTableName, "name"), searchName, lang, 1, 1000)
	if err != nil {
		logger.Log.Infof("%sGetMultilangKey: %s with locale: %s failed: %v", utils.MMark(logCtx), searchName, lang, err)
		return nil, err
	}
	logger.Log.Infof("%sGetMultilangKey name: %s with locale: %s, map size: %d", utils.MMark(logCtx), searchName, lang,
		len(keyMaps))

	outList := []string{}
	for k := range keyMaps {
		outList = append(outList, k)
	}

	return outList, nil
}
