package figurei18n

import (
	"acg-ai-go-common/logger"
	"acg-ai-go-common/multilanguage"
	"acg-ai-go-common/utils"
	"context"
	"digital-human-figure-resource/beans/model"
	"digital-human-figure-resource/handler/i18n"
	"digital-human-figure-resource/handler/i18n/sutils/i18nutils"
	"fmt"
)

// TransformFigureLocale 用来转换人像列表中所有的语言相关的字段
func TransformFigureLocale(logCtx context.Context, lang string, list []*model.UserFigure) ([]*model.UserFigure,
	error) {
	outList := list

	localeKeyList := []string{} // 语种的keyList
	for _, item := range list {
		// 只处理公共人像和体验人像，自行创建的不用关注
		if item.UserID != "-1" && item.UserID != "example" {
			continue
		}
		localeKeyList = append(localeKeyList, item.LocaleKey)
	}
	logger.Log.Infof("%strans figure: %d locales: %d", utils.MMark(logCtx), len(list), len(localeKeyList))

	if len(localeKeyList) < 1 {
		return nil, fmt.Errorf("no need for trans")
	}

	// 去多语言服务查一下语种结果
	nameMap, err := multilanguage.GetMultilangStr(logCtx, i18n.ServiceName, i18n.GetSubTag(i18n.FigureDatabaseName,
		i18n.FigureDataTableName, "name"), localeKeyList, lang)
	if err != nil {
		logger.Log.Errorf("%sget name map in i18n failed: %v", utils.MMark(logCtx), err)
		return nil, fmt.Errorf("get name map in i18n failed: %v", err)
	}
	urlMap, err := multilanguage.GetMultilangStr(logCtx, i18n.ServiceName, i18n.GetSubTag(i18n.FigureDatabaseName,
		i18n.FigureDataTableName, "template_video"), localeKeyList, lang)
	if err != nil {
		logger.Log.Errorf("%sget template_url map in i18n failed: %v", utils.MMark(logCtx), err)
		return nil, fmt.Errorf("get template_url map in i18n failed: %v", err)
	}
	logger.Log.Infof("%strans figure names: %d, urls: %d, locales: %d", utils.MMark(logCtx), len(nameMap), len(urlMap),
		len(localeKeyList))

	// 填充结果
	for i, _ := range outList {
		// 多语种key不存在则不处理
		if len(outList[i].LocaleKey) < 1 {
			continue
		}

		// 替换名称，有且不为空
		if ret, ok := nameMap[outList[i].LocaleKey]; ok && len(ret) > 0 && ret != i18nutils.DefaultErrorValue {
			outList[i].Name = ret
		}

		// 替换预览视频，有且不为空
		if ret, ok := urlMap[outList[i].LocaleKey]; ok && len(ret) > 0 && ret != i18nutils.DefaultErrorValue {
			if outList[i].UserID == "example" {
				outList[i].VideoURL = ret
			} else {
				outList[i].TemplateVideo = ret
			}
		}
	}

	return outList, nil
}
