package dh_user

import (
	"acg-ai-go-common/logger"
	"acg-ai-go-common/utils"
	"acg-ai-go-common/utils/httputil"
	"bytes"
	"context"
	"digital-human-figure-resource/beans/enum"
	"digital-human-figure-resource/beans/proto"
	config "digital-human-figure-resource/conf"

	"encoding/json"
	"errors"
	"net/http"
	"net/url"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

const (
	DhUserInfoPath           = "/api/internal/workflow/v2/user/auth/info"              // 获取用户信息
	DhUserQuotaFreeze        = "/api/internal/workflow/v1/account/quota/freezeQuota"   // 冻结
	DhUserQuotaUnFreeze      = "/api/internal/workflow/v1/account/quota/unFreezeQuota" // 解冻
	DhUserQuotaConfirmedCost = "/api/internal/workflow/v1/account/quota/confirmedCost" // 确认消耗
	DhUserInfoListPath       = "/api/internal/workflow/v1/account/user/list"           // 配额列表
)

func DhUserCheck(c *gin.Context) {
	reqHeader := map[string]string{
		"Cookie":  c.GetHeader("Cookie"),
		"Host":    c.GetHeader("Host"),
		"Origin":  c.GetHeader("Origin"),
		"Referer": c.GetHeader("Referer"),
	}
	logger.CtxLog(c).Infof("DhUserCheck GetDhUserInfo strat, reqHeader:%+v", reqHeader)
	rsp, err := GetDhUserInfo(c)
	if err != nil {
		logger.CtxLog(c).Errorf("DhUserCheck GetDhUserInfo fail, header:%v, err:%v", reqHeader, err)
		c.AbortWithStatusJSON(http.StatusOK, proto.NewCommRsp(590001, "服务内部异常"))
		return
	} else if rsp == nil || !rsp.Success {
		c.AbortWithStatusJSON(http.StatusOK, rsp)
		return
	}
	var subChannel string
	if rsp.Result.Tags != nil {
		subChannel = rsp.Result.Tags.SubChannel
	}
	logger.CtxLog(c).Infof("DhUserCheck GetDhUserInfo success, rsp:%+v,subChannel:%s", rsp, subChannel)
	c.Set("AccountId", rsp.Result.AccountId)
	c.Set("UserId", rsp.Result.Uid)
	c.Set("UserName", rsp.Result.Username)
	c.Set("UserChannel", subChannel)
	c.Next()
}

func DhUserCheckByPass(c *gin.Context) {
	reqHeader := map[string]string{
		"Cookie":         c.GetHeader("Cookie"),
		"Host":           c.GetHeader("Host"),
		"Origin":         c.GetHeader("Origin"),
		"Referer":        c.GetHeader("Referer"),
		"weixin-session": c.GetHeader("weixin-session"),
	}
	logger.CtxLog(c).Infof("DhUserCheck GetDhUserInfo strat, reqHeader:%+v", reqHeader)
	rsp, err := GetDhUserInfo(c)
	if err != nil {
		logger.CtxLog(c).Warnf("DhUserCheck GetDhUserInfo fail, header:%v, err:%v", reqHeader, err)
	} else if rsp != nil && rsp.Success {
		var subChannel string
		if rsp.Result.Tags != nil {
			subChannel = rsp.Result.Tags.SubChannel
		}
		logger.CtxLog(c).Infof("DhUserCheck GetDhUserInfo success, rsp:%+v,subChannel:%s", rsp, subChannel)
		c.Set("AccountId", rsp.Result.AccountId)
		c.Set("UserId", rsp.Result.Uid)
		c.Set("UserName", rsp.Result.Username)
		c.Set("UserChannel", subChannel)
	} else {
		logger.CtxLog(c).Warnf("User not login, DhUserCheck GetDhUserInfo fail, rsp:%+v", rsp)
	}
	c.Next()
}

func DhUserCheckForZg(c *gin.Context) {
	reqID := uuid.New().String()
	logCtx := context.WithValue(context.TODO(), utils.CtxKeyLogID, reqID)

	if c.Request.Header.Get("Content-Type") != "application/json" {
		logger.Log.Errorf(utils.MMark(logCtx)+"DhUserCheckForZg, Content-Type :%s, is wrong, need 'application/json'", c.Request.Header.Get("Content-Type"))
		c.JSON(http.StatusOK, proto.NewCommDataRsp(100001, "参数异常,Content-Type is wrong,need 'application/json", &proto.MaterialImportZgResResult{ReqId: reqID}))
		return
	}
	logger.Log.Infof(utils.MMark(logCtx)+"DhUserCheckForZg request url:%+v", c.Request.URL)

	urlParam := proto.MaterialImportZgUrlParam{}
	// 绑定url中的参数 到结构体
	if err := c.ShouldBindQuery(&urlParam); err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"DhUserCheckForZg,urlParam bind faild, err: %v", err)
		c.JSON(http.StatusOK, proto.NewCommDataRsp(100002, "参数异常,url参数解析失败", &proto.MaterialImportZgResResult{ReqId: reqID}))
		return
	}
	logger.Log.Infof(utils.MMark(logCtx)+"DhUserCheckForZg request urlParam:%+v", urlParam)
	header := map[string]string{
		"Cookie":  c.GetHeader("Cookie"),
		"Host":    c.GetHeader("Host"),
		"Origin":  c.GetHeader("Origin"),
		"Referer": c.GetHeader("Referer"),
	}
	logger.CtxLog(c).Infof("GetDhUserInfo header:%v", header)
	urlPath := config.LocalConfig.DhUserSetting.BaseUrl + config.LocalConfig.ZgPushConfig.ZgAuthPath
	rsp := &DhUserInfoRsp{}
	urlParamJson, err := json.Marshal(urlParam)
	if err != nil {
		logger.CtxLog(c).Errorf("DhUserCheckForZg request urlParamJson json.Marshal fail, err:%v", err)
		c.AbortWithStatusJSON(http.StatusOK, proto.NewCommRsp(590001, "服务内部异常,请求参数解析失败"))
		return
	}

	logger.Log.Infof(utils.MMark(logCtx)+"DhUserCheckForZg request url:%s, urlParamJson:%+v", urlPath, string(urlParamJson))
	client := httputil.NewRetryHTTPClient(3*time.Minute, 3)

	urlsValues := url.Values{}
	urlsValues.Set("token", urlParam.Token)
	urlsValues.Set("siteCode", urlParam.SiteCode)
	urlsValues.Set("domainId", urlParam.DomainId)
	urlsValues.Set("loginInfo", urlParam.LoginInfo)
	urlPath = urlPath + "?" + urlsValues.Encode()
	logger.Log.Infof(utils.MMark(logCtx)+"DhUserCheckForZg request url add param:%s", urlPath)

	// 请求鉴权接口
	res, err := client.DoRequest(logCtx, "GET", urlPath, header, nil)
	if err != nil {
		logger.CtxLog(c).Errorf("DhUserCheckForZg DoRequest fail, err:%v", err)
		c.AbortWithStatusJSON(http.StatusOK, proto.NewCommRsp(590001, "服务内部异常,鉴权信息请求失败"))
		return
	}
	logger.CtxLog(c).Infof("DhUserCheckForZg DoRequest res:%v", string(res))
	err = json.Unmarshal(res, rsp)
	if err != nil {
		logger.CtxLog(c).Errorf("DhUserCheckForZg res Unmarshal fail, err:%v", err)
		c.AbortWithStatusJSON(http.StatusOK, proto.NewCommRsp(590001, "服务内部异常,鉴权信息解析失败"))
		return
	} else if rsp == nil || !rsp.Success {
		c.AbortWithStatusJSON(http.StatusOK, rsp)
		return
	}

	var subChannel string
	if rsp.Result.Tags != nil {
		subChannel = rsp.Result.Tags.SubChannel
	}
	logger.CtxLog(c).Infof("DhUserCheckForZg GetDhUserInfo success, info:%v,subChannel:%s", rsp.Result, subChannel)

	c.Set("AccountId", rsp.Result.AccountId)
	c.Set("UserId", rsp.Result.Uid)
	c.Set("UserName", rsp.Result.Username)
	c.Set("UserChannel", subChannel)
	c.Next()
}

func GetDhUserInfo(c *gin.Context) (*DhUserInfoRsp, error) {
	header := map[string]string{
		"Cookie":  c.GetHeader("Cookie"),
		"Host":    c.GetHeader("Host"),
		"Origin":  c.GetHeader("Origin"),
		"Referer": c.GetHeader("Referer"),
	}
	logger.CtxLog(c).Infof("GetDhUserInfo header:%v", header)
	url := config.LocalConfig.DhUserSetting.BaseUrl + DhUserInfoPath
	rsp := &DhUserInfoRsp{}
	client := httputil.NewRetryHTTPClient(10*time.Second, 3)
	reqID := uuid.New().String()
	logCtx := context.WithValue(context.TODO(), utils.CtxKeyLogID, reqID)

	res, err := client.DoRequest(logCtx, "GET", url, header, nil)
	if err != nil {
		logger.CtxLog(c).Errorf("GetDhUserInfo DoRequest fail, err:%v", err)
		return nil, errors.New("服务内部异常")
	}

	err = json.Unmarshal(res, rsp)
	if err != nil {
		logger.CtxLog(c).Errorf("GetDhUserInfo Unmarshal fail, err:%v", err)
		return nil, errors.New("服务内部异常")
	}
	logger.CtxLog(c).Infof("GetDhUserInfo DoRequest end, res:%v", string(res))
	return rsp, err
}

// QuotaFreeze 配额冻结
func QuotaFreeze(req *QuotaFreezeReq) (*QuotaFreezeRsp, error) {
	url := config.LocalConfig.DhUserSetting.BaseUrl + DhUserQuotaFreeze
	var rsp *QuotaFreezeRsp
	err := httputil.PostJson(url, req, &rsp)
	return rsp, err
}

// QuotaUnFreeze 配额解冻
func QuotaUnFreeze(req *QuotaUnFreezeReq) (*QuotaUnFreezeRsp, error) {
	url := config.LocalConfig.DhUserSetting.BaseUrl + DhUserQuotaUnFreeze
	var rsp *QuotaUnFreezeRsp
	err := httputil.PostJson(url, req, &rsp)
	return rsp, err
}

// QuotaConfirmedCost 配额确认扣除
func QuotaConfirmedCost(req *QuotaConfirmedCostReq) (*QuotaConfirmedCostRsp, error) {
	url := config.LocalConfig.DhUserSetting.BaseUrl + DhUserQuotaConfirmedCost
	var rsp *QuotaConfirmedCostRsp
	err := httputil.PostJson(url, req, &rsp)
	return rsp, err
}

func GetDhUserInfoList(logCtx context.Context, req *DhUserInfoListReq) (DhUserInfoListRsp, error) {
	url := config.LocalConfig.DhUserSetting.BaseUrl + DhUserInfoListPath
	var rsp DhUserInfoListRsp
	client := httputil.NewRetryHTTPClient(15*time.Second, 3)

	body, err := json.Marshal(req)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"json Marshal error:%v", err)
		return rsp, err
	}

	headers := map[string]string{
		"Content-Type": "application/json",
	}

	res, err := client.DoRequest(logCtx, "POST", url, headers, bytes.NewReader(body))
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"GetDhUserInfoList DoRequest error:%v", err)
		return rsp, err
	}

	logger.Log.Infof(utils.MMark(logCtx)+"GetDhUserInfoList res:%s", string(res))

	err = json.Unmarshal(res, &rsp)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"GetDhUserInfoList Unmarshal error:%v", err)
		return rsp, err
	}

	return rsp, err
}

func GetDhUserOpenID(logCtx context.Context, req *DhUserInfoListReq, pushType enum.PushType) ([]string, error) {
	openid := make([]string, 0)
	dhres, err := GetDhUserInfoList(logCtx, req)
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"get dhuser info list error: %v", err)
		return openid, err
	}

	if dhres.Code != 0 {
		logger.Log.Errorf(utils.MMark(logCtx)+"get dhuser info list code is not zero: %v", dhres)
		return openid, errors.New("dhuser info list code is not zero")
	}

	openidtype := "WEIXIN"
	if pushType == enum.BaiduMiniProgram {
		openidtype = "BCE"
	}

	for _, v := range dhres.Result.Result {
		if v.Channel == openidtype {
			if pushType == enum.BaiduMiniProgram && len(v.BdOpenID) != 0 {
				openid = append(openid, v.BdOpenID)
			} else if pushType == enum.WechatMiniProgram {
				openid = append(openid, v.WxOpenID)
			}
		}
	}

	if req.PageNo*req.PageSize < dhres.Result.TotalCount {
		req.PageNo += 1
		tmpopids, err := GetDhUserOpenID(logCtx, req, pushType)
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+"get dhuser openid error: %v", err)
			return openid, err
		} else {
			openid = append(openid, tmpopids...)
		}
	}

	logger.Log.Infof(utils.MMark(logCtx)+"get dhuser req: %v res:%+v", req, dhres)
	return openid, nil
}
