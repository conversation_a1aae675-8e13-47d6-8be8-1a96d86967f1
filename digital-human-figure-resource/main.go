package main

import (
	"acg-ai-go-common/global"
	"acg-ai-go-common/logger"
	"acg-ai-go-common/server"
	"acg-ai-go-common/utils/mysqlproxy"
	"acg-ai-go-common/utils/redisproxy"
	"digital-human-figure-resource/beans/model"
	config "digital-human-figure-resource/conf"
	"digital-human-figure-resource/mysqlclient"

	"digital-human-figure-resource/handler/figure"
	"digital-human-figure-resource/handler/figure/scanner"

	"digital-human-figure-resource/routers"
	"log"
	"net/http"
	_ "net/http/pprof"

	"github.com/BurntSushi/toml"

	"github.com/sirupsen/logrus"
)

func main() {
	go func() {
		log.Println(http.ListenAndServe("localhost:6060", nil))
	}()
	// 初始化公共配置
	server.InitGlobalSetting()
	if _, err := toml.DecodeFile(global.ConfFilePath, config.LocalConfig); err != nil {
		log.Panicf("本地个性化配置加载失败: {%v}", err)
	}

	logger.SetLogger(
		// 初始化日志
		// 开启日志保存Es，默认不开启
		logger.SetOpenReportEs(true),
		// 使用异步保存Es，true为同步上传(会影响API响应速度)
		logger.SetSyncReportEs(false),
		// 设置输出保存的日志级别
		logger.SetLogLevel(logrus.InfoLevel),
	)

	logger.Log.Info("本地个性化配置加载成功")
	figure.InitBosParam()
	// 初始化redis
	proxy := redisproxy.GetRedisProxy()
	// 开启携程监听redis是否发生网络中断并进行重连
	go proxy.MonitorRedisConnection()

	// 初始化mysql
	mysqlproxy := mysqlproxy.GetMysqlProxy()
	// 开启携程监听mysql是否发生网络中断并进行重连
	go mysqlproxy.MonitorMysqlConnection()

	// 初始化mysql 因为存在多个数据库这里初始化全局map数据库，在使用时使用map[dbName]即可
	mysqlclient.InitDB(mysqlclient.StarLightDBName, config.LocalConfig.StarLightMysqlSetting)
	mysqlclient.InitDB(mysqlclient.StarLightReadDBName, config.LocalConfig.StarLightMysqlReadSetting)
	mysqlclient.InitDB(mysqlclient.PlatDBName, config.LocalConfig.PlatMysqlSetting)
	// 开启携程监听mysql是否发生网络中断并进行重连
	go mysqlclient.MonitorMysqlConnection()
	// 初始化数据库
	if err := model.InitMysqlDBTable(); err != nil {
		log.Panicf("数据库初始化失败: {%v}", err)
	}
	// 初始化路由
	routers.InitRouter()
	// 开启定时器
	scanner.Init()
	// 启动服务
	server.Run(routers.GinRouter)
}
