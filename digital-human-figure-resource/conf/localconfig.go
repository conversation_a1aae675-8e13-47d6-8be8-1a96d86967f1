package config

var (
	LocalConfig = &localConfig{}
)

type localConfig struct {
	StarLightMysqlSetting *MysqlSetting         `toml:"starlight-mysql-setting"`
	PlatMysqlSetting      *MysqlSetting         `toml:"plat-mysql-setting"`
	RedisSetting          *RedisSetting         `toml:"redis-setting"`
	DhUserSetting         *DhUserSetting        `toml:"dh-user-setting"`
	BosSetting            *BosSetting           `toml:"bos-setting"`
	MaterialSetting       *MaterialSetting      `toml:"material-setting"`
	VideoPipelineSetting  *VideoPipelineSetting `toml:"videopipeline-setting"`
	ZgPushConfig          *ZgPushConfig         `toml:"zg-push-config"`
	UploadSetting         *UploadSetting        `toml:"upload-setting"`
	FigureSearchSetting   *FigureSearchSetting  `toml:"figure-search-setting"`
}

type RedisSetting struct {
	SentinelNodes    string `toml:"sentinelNodes"`    // 哨兵集群节点，ip:port，多个节点使用,分割，eg: "127.0.0.1:26379,127.0.0.1:26380,127.0.0.1:26381"
	SentinelUsername string `toml:"sentinelUsername"` // 哨兵节点用户名，可为""
	SentinelPassword string `toml:"sentinelPassword"` // 哨兵节点密码，可为""
	RouteByLatency   bool   `toml:"routeByLatency"`   // 是否将读取命令路由到从节点
	MasterName       string `toml:"masterName"`       // redis集群主节点名称
	Addr             string `toml:"addr"`             // 单节点地址，格式为ip:port，eg: "127.0.0.1:6379"
	Username         string `toml:"username"`         // redis集群主节点用户名
	Password         string `toml:"password"`         // redis集群主节点密码
	PoolSize         int    `toml:"poolSize"`         // redis连接池大小
	MinIdleConns     int    `toml:"minIdleConns"`     // 最大空闲连接数
	RedisEnv         string `toml:"redisEnv"`         // redis环境，dev/test/prod
}

type HostPortSetting struct {
	Host string `toml:"host"`
	Port int    `toml:"port"`
}

type UserNamePwdSetting struct {
	Username string `toml:"username"`
	Password string `toml:"password"`
}

type MysqlSetting struct {
	HostPortSetting
	UserNamePwdSetting
	Database     string `toml:"database"`
	MaxOpenConns int    `toml:"maxOpenConns"`
	MaxIdleConns int    `toml:"maxIdleConns"`
}

type DhUserSetting struct {
	BaseUrl string `toml:"baseUrl"`
}

type BosSetting struct {
	AK       string `toml:"ak"`
	SK       string `toml:"sk"`
	Endpoint string `toml:"endpoint"`
	Bucket   string `toml:"bucket"`
	Host     string `toml:"host"`
	CDNHost  string `toml:"cdn-host"`
}

type MaterialSetting struct {
	MaxImageSizeMB  int64 `toml:"maxImageSizeMB"`
	MaxVideoSizeMB  int64 `toml:"maxVideoSizeMB"`
	MaxAudioSizeMB  int64 `toml:"maxAudioSizeMB"`
	NeedEncode      bool  `toml:"needEncode"`
	MaterialMaxSize int64 `toml:"materialMaxSize"`
}

type ZgPushConfig struct {
	AppID         string `toml:"appID"`         // 向浙广推送的AppId  浙广提供  BaiDuXiLing
	AppSecret     string `toml:"appSecret"`     // 向浙广推送的AppSecret  浙广提供  b1a0dea0-f5ba-4db3-9457-25be00e43af8
	PushUrl       string `toml:"pushUrl"`       // http://************:18331/test  浙广提供
	ImportFileMax int    `toml:"importFileMax"` // 浙广一次可导入的素材文件的最大数量
	ZgAuthPath    string `toml:"zgAuthPath"`    // 浙广鉴权接口的路径
}

type VideoPipelineSetting struct {
	BaseUrl string `toml:"baseUrl"`
}
type UploadSetting struct {
	ImageSuffix             []string `toml:"imageSuffix"`
	VideoSuffix             []string `toml:"videoSuffix"`
	AudioSuffix             []string `toml:"audioSuffix"`
	MaxImageSizeMB          int64    `toml:"maxImageSizeMB"`
	MaxVideoSizeMB          int64    `toml:"maxVideoSizeMB"`
	MaxTranslateVideoSizeMB int64    `toml:"maxTranslateVideoSizeMB"`
	MaxAudioSizeMB          int64    `toml:"maxAudioSizeMB"`
	IsCensor                bool     `toml:"isCensor"`
	QueueMaxNumber          int      `toml:"queueMaxNumber"`
	QueueMaxInterval        int64    `toml:"queueMaxInterval"`
}

type FigureSearchSetting struct {
	GlobalExcludeSceneLabel []string `toml:"globalExcludeSceneLabel"`
}
