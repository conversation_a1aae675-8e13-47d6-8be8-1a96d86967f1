package model

import (
	"time"

	"gorm.io/gorm"
)

type Fonts struct {
	ID        int64          `json:"id" gorm:"primaryKey;autoIncrement;"`
	FontID    string         `json:"fontId" gorm:"column:fontId;type:varchar(255);not null;uniqueIndex"`
	Name      string         `json:"name" gorm:"column:name;type:varchar(255);not null"`
	PngURL    string         `json:"pngUrl" gorm:"column:pngUrl;not null;type:varchar(1024)"`     // 图片地址
	FontsURL  string         `json:"fontsURL" gorm:"column:fontsURL;not null;type:varchar(1024)"` // 视频地址
	Sort      int64          `json:"sort" gorm:"column:sort;type:int;not null;default:0"`
	IsDefault int64          `json:"isDefault" gorm:"column:isDefault;type:int;not null;default:0"`
	CreatedAt time.Time      `json:"createdAt" gorm:"column:createdAt;autoCreateTime"` // 创建时间
	UpdatedAt time.Time      `json:"updatedAt" gorm:"column:updatedAt;autoUpdateTime"` // 更新时间
	DeletedAt gorm.DeletedAt `json:"-" gorm:"column:deletedAt;index"`                  // 删除时间/标记删除
}

func (f *Fonts) TableName() string {
	return "dhmh_fonts"
}

// CreateAnimationTemplate 创建动画模板
func (f *Fonts) CreateFonts(db *gorm.DB) error {
	result := db.Create(f)
	if result.Error != nil {
		return result.Error
	}
	return nil
}

// UpdateAnimationTemplate 更新动画模板
func (f *Fonts) UpdateFonts(db *gorm.DB) error {
	result := db.Save(f)
	if result.Error != nil {
		return result.Error
	}
	return nil
}

func (f *Fonts) ListFonts(db *gorm.DB, page int, pageSize int) (int64, []*Fonts, error) {
	var tasks []*Fonts
	offset := (page - 1) * pageSize

	var count int64

	// 获取总数量
	err := db.Model(&Fonts{}).Count(&count).Error
	if err != nil {
		return 0, nil, err
	}

	// 获取分页数据
	err = db.Model(&Fonts{}).
		Order("sort DESC").
		Order("id ASC").
		Limit(pageSize).
		Offset(offset).
		Find(&tasks).Error

	if err != nil {
		return 0, nil, err
	}

	return count, tasks, nil
}

func (f *Fonts) GetFonts(db *gorm.DB, fontId string) (Fonts, error) {
	var tasks Fonts

	err := db.Where("fontId = ?", fontId).First(&tasks).Error
	if err != nil {
		return tasks, err
	}
	return tasks, nil
}

// 删除字体包
func (f *Fonts) DeleteFonts(db *gorm.DB, IDs []int64) error {
	result := db.Model(&Fonts{}).Unscoped().Where("id IN ?", IDs).Delete(&Fonts{})
	if result.Error != nil {
		return result.Error
	}
	return nil
}
