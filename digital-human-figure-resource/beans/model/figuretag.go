package model

import "gorm.io/gorm"

// TagInfo 结构体对应数据库中的表
type FigureTagInfo struct {
	ID       int64  `gorm:"primaryKey;column:id"`    // 主键ID，类型为int64
	Tag      string `gorm:"column:tag;not null"`     // 标签名称，假设为字符串类型且非空
	Describe string `gorm:"column:describe"`         // 描述信息，假设为字符串类型
	Type     int    `gorm:"column:type;not null"`    // 类型，假设为字符串类型且非空
	Module   int    `gorm:"column:module;not null"`  // 模块，假设为字符串类型且非空
	Visible  int    `gorm:"column:visible;not null"` // 是否可见
}

func (f *FigureTagInfo) TableName() string {
	return "tag"
}

func (f *FigureTagInfo) GetTagListByTag(db *gorm.DB, tag []string) ([]*FigureTagInfo, error) {
	var tagList []*FigureTagInfo
	if err := db.Where("tag IN ?", tag).Where("module = ?", 1).Find(&tagList).Error; err != nil {
		return tagList, err
	}
	return tagList, nil
}

// MaterialTag 关联表结构体，表示素材和标签的关联关系
type MaterialTagMapping struct {
	ID           int64 `gorm:"primaryKey;column:id"`          // 主键ID
	MaterialID   int64 `gorm:"column:material_id;not null"`   // 素材ID，外键可能需要根据实际情况添加
	TagID        int64 `gorm:"column:tag_id;not null"`        // 标签ID，外键可能需要根据实际情况添加
	MaterialType int   `gorm:"column:material_type;not null"` // 素材类型
}

func (m *MaterialTagMapping) TableName() string {
	return "material_tag_mapping"
}

func (m *MaterialTagMapping) GetMaterialByTagID(db *gorm.DB, materialType MaterialType, tagIds []int64, tagCount int) ([]int, error) {
	var result []int

	err := db.Model(&MaterialTagMapping{}).Where("material_type = ?", materialType).
		Where("tag_id IN ?", tagIds).
		Group("material_id").
		Having("COUNT(DISTINCT tag_id) = ?", tagCount).
		Pluck("material_id", &result). // Pluck will extract only the 'materialId' values
		Error

	if err != nil {
		return nil, err
	}

	return result, nil
}

func (m *MaterialTagMapping) CreateMaterialTagMapping(db *gorm.DB) error {
	if err := db.Create(m).Error; err != nil {
		return err
	}
	return nil
}

func (m *MaterialTagMapping) GetMaterialByTagIDAndMaterialType(db *gorm.DB, materialType MaterialType, materialID, tagId int64) (*MaterialTagMapping, error) {
	var result MaterialTagMapping

	err := db.Model(&MaterialTagMapping{}).Where("material_type = ?", materialType).Where("material_id = ?", materialID).Where("tag_id = ?", tagId).First(&result).Error
	if err != nil {
		return nil, err
	}

	return &result, nil
}
