package model

import (
	"time"

	"gorm.io/gorm"
)

type CaptionsTemplate struct {
	ID         int64          `json:"id" gorm:"primaryKey;autoIncrement;"`
	Name       string         `json:"name" gorm:"column:name;type:varchar(255);not null"`
	TemplateID string         `json:"templateID" gorm:"column:templateID;type:varchar(255);not null;uniqueIndex"`
	Type       string         `json:"type" gorm:"column:type;type:varchar(255);not null;index"`
	Content    string         `json:"content" gorm:"column:content;type:text;not null"`
	PngURL     string         `json:"pngUrl" gorm:"column:pngUrl;not null;type:varchar(1024)"`     // 图片地址
	VideoURL   string         `json:"videoUrl" gorm:"column:videoUrl;not null;type:varchar(1024)"` // 视频地址
	Sort       int64          `json:"sort" gorm:"column:sort;type:int;not null;default:0"`
	CreatedAt  time.Time      `json:"createdAt" gorm:"column:createdAt;autoCreateTime"` // 创建时间
	UpdatedAt  time.Time      `json:"updatedAt" gorm:"column:updatedAt;autoUpdateTime"` // 更新时间
	DeletedAt  gorm.DeletedAt `json:"-" gorm:"column:deletedAt;index"`                  // 删除时间/标记删除
}

func (c *CaptionsTemplate) TableName() string {
	return "dhmh_captions_template"
}

// CreateAnimationTemplate 创建动画模板
func (c *CaptionsTemplate) CreateCaptionsTemplate(db *gorm.DB) error {
	result := db.Create(c)
	if result.Error != nil {
		return result.Error
	}
	return nil
}

// UpdateAnimationTemplate 更新动画模板
func (c *CaptionsTemplate) UpdateCaptionsTemplate(db *gorm.DB) error {
	result := db.Save(c)
	if result.Error != nil {
		return result.Error
	}
	return nil
}

func (c *CaptionsTemplate) ListCaptionsTemplate(db *gorm.DB, page int, pagesize int, captionsType string) (int64, []*CaptionsTemplate, error) {
	var tasks []*CaptionsTemplate
	offset := (page - 1) * pagesize
	count := int64(0)

	// 构建查询语句
	query := db.Model(&CaptionsTemplate{})

	if captionsType != "" && captionsType != "ALL" {
		query = query.Where("type = ?", captionsType)
	}

	// 查询总数
	if err := query.Count(&count).Error; err != nil {
		return 0, nil, err
	}

	// 查询分页数据
	if err := query.Order("sort DESC").Order("id ASC").
		Limit(pagesize).
		Offset(offset).
		Find(&tasks).Error; err != nil {
		return count, nil, err
	}

	return count, tasks, nil
}

func (c *CaptionsTemplate) GetCaptionsTemplate(db *gorm.DB, captionsTemplateID string) (CaptionsTemplate, error) {
	var tasks CaptionsTemplate

	err := db.Where("captionsTemplateID = ?", captionsTemplateID).First(&tasks).Error
	if err != nil {
		return tasks, err
	}
	return tasks, nil
}

// DeleteCaptionsTemplate 删除字幕模板
func (c *CaptionsTemplate) DeleteCaptionsTemplate(db *gorm.DB, IDs []int64) error {
	result := db.Unscoped().Where("id in ?", IDs).Delete(&CaptionsTemplate{})
	if result.Error != nil {
		return result.Error
	}
	return nil
}
