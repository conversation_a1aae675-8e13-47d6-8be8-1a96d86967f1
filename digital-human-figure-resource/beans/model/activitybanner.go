package model

import (
	"acg-ai-go-common/logger"
	"time"

	"gorm.io/gorm"
)

type ActivityBannerEntity struct {
	ID                int64     `json:"id" gorm:"primaryKey;autoIncrement;"`                                                              // 主键自增
	ActivityId        string    `json:"activityId" gorm:"column:activity_id;uniqueIndex:idx_activity_id;not null;type:varchar(64);index"` // 活动ID
	ActivityName      string    `json:"activityName" gorm:"column:activity_name;not null;type:varchar(256)"`                              // 活动名称
	ImgUrl            string    `json:"imgUrl" gorm:"column:img_url;type:varchar(512)"`                                                   // banner图片 url
	JumpUrl           string    `json:"jumpUrl" gorm:"column:jump_url;type:varchar(512)"`                                                 // 跳转链接 url
	Creator           string    `json:"creator" gorm:"column:creator;not null;type:varchar(64)"`                                          // 活动创建者
	Channel           string    `json:"channel" gorm:"column:channel;not null;type:varchar(64);comment:BCE/DH"`                           // 账号类型 BCE / DH
	Sort              int       `json:"sort" gorm:"column:sort;type:int(10)"`                                                             // 序号，越大越靠前
	ActivityState     int       `json:"activityState" gorm:"column:activity_state;type:int(10);comment:1待上线、2上线中、3已下线"`                   // 活动状态    1 待上线、 2 上线中  3 已下线
	ActivityStartTime time.Time `json:"activityStartTime" gorm:"column:activity_start_time;type:timestamp;"`                              // 活动开始时间
	ActivityEndTime   time.Time `json:"activityEndTime" gorm:"column:activity_end_time;type:timestamp;"`                                  // 活动结束时间
	CreateTime        time.Time `json:"createTime" gorm:"column:create_time;autoCreateTime;type:timestamp;"`                              // 创建时间
	UpdateTime        time.Time `json:"updateTime" gorm:"column:update_time;autoUpdateTime;type:timestamp;"`                              // 更新时间
}

func (m *ActivityBannerEntity) TableName() string {
	return "activity_banner"
}

func (p *ActivityBannerEntity) SelectByChannelAndTime(logId string, db *gorm.DB, channel string) ([]ActivityBannerEntity, error) {
	var banners []ActivityBannerEntity
	currentTime := time.Now()
	logger.Log.Errorf("logId:%s,SelectByChannelAndTime:channel:%s currentTime:%v", logId, channel, currentTime)
	result := db.Model(&ActivityBannerEntity{}).Where("channel = ? AND activity_state IN (1,2) AND activity_start_time <= ? AND activity_end_time >= ?", channel, currentTime, currentTime).Order("sort DESC").
		Limit(5).
		Find(&banners)

	if result.Error != nil {
		logger.Log.Errorf("logId:%s,SelectByChannelAndTime: %v", logId, result.Error)
		return nil, result.Error
	}
	logger.Log.Infof("logId:%s,SelectByChannelAndTime success,banners:%+v", logId, banners)
	return banners, nil
}
