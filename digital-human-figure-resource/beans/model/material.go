package model

import (
	"time"

	"gorm.io/gorm"
)

type MaterialEntity struct {
	ID         int64     `json:"id" gorm:"primaryKey;autoIncrement;"`                              // 主键自增
	MaterialID string    `json:"material_id" gorm:"column:material_id;not null;type:varchar(64)"`  // 材料ID
	Name       string    `json:"name" gorm:"column:name;not null;type:varchar(128)"`               // 材料名称
	UserID     string    `json:"user_id" gorm:"column:user_id;not null;type:varchar(128)"`         // 用户ID
	Url        string    `json:"url" gorm:"column:url;not null;type:varchar(512)"`                 // URL
	Type       string    `json:"type" gorm:"column:type;not null;type:char(5)"`                    // 类型
	Module     string    `json:"module" gorm:"column:module;not null;type:char(16)"`               // 模块信息
	Creator    string    `json:"creator" gorm:"column:creator;not null;type:varchar(128)"`         // 创建者
	Thumbnail  string    `json:"thumbnail" gorm:"column:thumbnail;not null;type:varchar(512)"`     // 缩略图URL
	PreviewURL string    `json:"preview_url" gorm:"column:preview_url;not null;type:varchar(512)"` // 预览URL
	Duration   int       `json:"duration" gorm:"column:duration;not null"`                         // 持续时间
	Width      int       `json:"width" gorm:"column:width;not null"`                               // 宽度
	Height     int       `json:"height" gorm:"column:height;not null"`                             // 高度
	CreateTime time.Time `json:"create_time" gorm:"column:create_time;autoCreateTime"`             // 创建时间
	UpdateTime time.Time `json:"update_time" gorm:"column:update_time;autoUpdateTime"`             // 更新时间
	ExtraInfo  string    `json:"extra_info,omitempty" gorm:"column:extra_info;type:text"`          // 附加信息，当为零值时省略
}

func (m *MaterialEntity) TableName() string {
	return "slide_material"
}

// CreateAnimationTemplate 创建动画模板
func (m *MaterialEntity) CreateMaterialEntity(db *gorm.DB) error {
	result := db.Create(m)
	if result.Error != nil {
		return result.Error
	}
	return nil
}

func (m *MaterialEntity) GetMaterialEntity(db *gorm.DB, userID, name, module string) (MaterialEntity, error) {
	var tasks MaterialEntity
	err := db.Where("user_id = ?", userID).Where("name = ?", name).Where("module = ?", module).First(&tasks).Error
	if err != nil {
		return tasks, err
	}
	return tasks, nil
}

func (m *MaterialEntity) GetMaterialCount(db *gorm.DB, userID string) (int64, error) {
	var count = int64(0)
	err := db.Table("slide_material").Where("user_id = ?", userID).Count(&count).Error
	if err != nil {
		return 0, err
	}
	return count, nil
}
