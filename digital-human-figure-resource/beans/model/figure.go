package model

import (
	"acg-ai-go-common/logger"
	"acg-ai-go-common/utils"
	"context"
	"digital-human-figure-resource/beans/enum"
	"digital-human-figure-resource/beans/proto"
	"digital-human-figure-resource/mysqlclient"
	"fmt"
	"time"

	"gorm.io/gorm"
)

// UserFigureModel 用户人像模型
type UserFigure struct {
	ID               int64     `json:"id" gorm:"primaryKey"`
	UserID           string    `json:"user_id" gorm:"column:user_id"`
	Source           string    `json:"source" gorm:"column:source"`
	ResourceLabel    string    `json:"resource_label" gorm:"column:resource_label"`
	SceneLabel       string    `json:"scene_label" gorm:"column:scene_label"`
	Name             string    `json:"name" gorm:"column:name"`
	VideoURL         string    `json:"video_url" gorm:"column:video_url"`
	VideoURL2        string    `json:"video_url2" gorm:"column:video_url2"`
	MaskVideoURL     string    `json:"mask_video_url" gorm:"column:mask_video_url"`
	TemplateImg      string    `json:"template_img" gorm:"column:template_img"`
	TemplateVideo    string    `json:"template_video" gorm:"column:template_video"`
	Status           string    `json:"status" gorm:"column:status"`
	TaskID           string    `json:"task_id" gorm:"column:task_id"`
	FigureName       string    `json:"figure_name" gorm:"column:figure_name"`
	ResultMessage    string    `json:"result_message" gorm:"column:result_message"`
	FigureResult     string    `json:"figure_result" gorm:"column:figure_result"`
	IsDelete         int64     `json:"is_delete" gorm:"column:is_delete"`
	SystemProvided   uint8     `json:"system_provided" gorm:"column:system_provided"`
	Type             uint8     `json:"type" gorm:"column:type"`
	Gender           int64     `json:"gender" gorm:"column:gender"`
	ResolutionWidth  int64     `json:"resolution_width" gorm:"column:resolution_width"`
	ResolutionHeight int64     `json:"resolution_height" gorm:"column:resolution_height"`
	LastUsedTime     time.Time `json:"last_used_time" gorm:"column:last_used_time"`
	CreateTime       time.Time `json:"create_time" gorm:"column:create_time"`
	UpdateTime       time.Time `json:"update_time" gorm:"column:update_time"`
	EffectsThumbnail string    `json:"effects_thumbnail" gorm:"column:effects_thumbnail"`
	Effects          string    `json:"effects" gorm:"column:effects"`
	QuotaRecordID    int64     `json:"quota_record_id" gorm:"column:quota_record_id"`
	MatchTTS         string    `json:"match_tts" gorm:"column:match_tts"`
	PictureURL       string    `json:"picture_url" gorm:"column:picture_url"`
	Thumbnail        string    `json:"thumbnail" gorm:"column:thumbnail"`
	FaceLocation     string    `json:"faceLocation" gorm:"column:face_location"`
	Sort             int64     `json:"sort" gorm:"column:sort"`
	PreviewVideoUrl  string    `json:"preview_video_url" gorm:"column:preview_video_url"`
	// 程序使用的字段，不是数据库中的字段
	Collected    bool   `json:"collected" gorm:"-"`
	RecentlyUsed bool   `json:"recentlyUsed" gorm:"-"`
	Scene        string `json:"scene,omitempty" gorm:"-"`
}

func (uf *UserFigure) TableName() string {
	return "user_figure"
}

func (uf *UserFigure) GetUserFigureById(db *gorm.DB, figureID int64) (*UserFigure, error) {
	var userf UserFigure
	if err := db.Where("id = ?", figureID).First(&userf).Error; err != nil {
		return nil, err
	}
	return &userf, nil
}

func (uf *UserFigure) GetUserFigureList(logCtx context.Context, db *gorm.DB, userid string, req *proto.FigureQueryRequst, recentlyUsedIds []int64, collectList []int64) ([]*UserFigure, error) {
	var userfigures []*UserFigure
	tx := db.Where("user_id = ?", userid)
	if tx.Error != nil {
		return nil, tx.Error
	}

	if req.ContainRemovedFigure {
		tx = tx.Where("(is_delete = 0 OR is_delete = 1)")
		if tx.Error != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+"GetUserFigureList tx.Error: %v", tx.Error)
			return nil, tx.Error
		}
	} else {
		tx = tx.Where("is_delete = ?", 0)
		if tx.Error != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+"GetUserFigureList tx.Error: %v", tx.Error)
			return nil, tx.Error
		}
	}

	if len(req.SearchName) > 0 {
		tx = tx.Where("name LIKE ?", "%"+req.SearchName+"%")
		if tx.Error != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+"GetUserFigureList tx.Error: %v", tx.Error)
			return nil, tx.Error
		}
	}

	if len(req.Status) > 0 {
		if req.Status == enum.Running {
			tx = tx.Where("status IN ?", []enum.FigureTaskStatus{enum.Running, enum.WaitCheck, enum.Precessing,
				enum.PreCheck, enum.FigureCreate, enum.MakeTemplateVideo})
		} else {
			tx = tx.Where("status = ?", req.Status)
		}
		if tx.Error != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+"GetUserFigureList tx.Error: %v", tx.Error)
			return nil, tx.Error
		}
	}

	if len(req.Tags) > 0 {
		tagindexs := make([]int64, 0)
		taginfos, err := (&FigureTagInfo{}).GetTagListByTag(mysqlclient.DbMap[mysqlclient.StarLightDBName], req.Tags)
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+"GetTagListByTag tx.Error: %v", err)
			return nil, err
		}

		for _, taginfo := range taginfos {
			tagindexs = append(tagindexs, taginfo.ID)
		}

		if len(tagindexs) > 0 {
			logger.Log.Infof(utils.MMark(logCtx)+"tagindexs: %v", tagindexs)
			materialIds, err := (&MaterialTagMapping{}).GetMaterialByTagID(mysqlclient.DbMap[mysqlclient.StarLightDBName], MaterialTypeFigure, tagindexs, len(req.Tags))
			if err != nil {
				logger.Log.Errorf(utils.MMark(logCtx)+"GetMaterialByTagID tx.Error: %v", err)
				return nil, err
			}

			if len(materialIds) > 0 {
				logger.Log.Infof(utils.MMark(logCtx)+"materialIds: %v", materialIds)
				tx = tx.Where("id IN ?", materialIds)
				if err != nil {
					logger.Log.Errorf(utils.MMark(logCtx)+"GetUserFigureList tx.Error: %v", tx.Error)
					return nil, err
				}
			} else {
				logger.Log.Warnf(utils.MMark(logCtx) + "don't find materialIds")
				// 未找到人像ids，返回空
				return userfigures, nil
			}
		} else {
			logger.Log.Warnf(utils.MMark(logCtx) + "don't find tagindexs")
			// 未找到标签，返回空
			return userfigures, nil
		}
	}

	// 常用人像列表排序
	tx = orderByRecentlyUsedIds(logCtx, tx, recentlyUsedIds)
	if tx.Error != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"GetUserFigureList tx.Error: %v", tx.Error)
		return nil, tx.Error
	}

	tx = tx.Find(&userfigures)
	if tx.Error != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"GetUserFigureList tx.Error: %v", tx.Error)
		return nil, tx.Error
	}

	setCollectAndRecentlyUsedByFigure(userfigures, recentlyUsedIds, collectList)
	return userfigures, nil
}

func (uf *UserFigure) GetUserCollectList(logCtx context.Context, db *gorm.DB, materialId []int64, req *proto.FigureQueryRequst) ([]*UserFigure, error) {
	var userfigures []*UserFigure

	tx := db.Where("id IN ?", materialId)
	if tx.Error != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"GetUserCollectList tx.Error: %v", tx.Error)
		return nil, tx.Error

	}

	if req.ContainRemovedFigure {
		tx = tx.Where("(is_delete = 0 OR is_delete = 1)")
		if tx.Error != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+"GetUserCollectList tx.Error: %v", tx.Error)
			return nil, tx.Error
		}
	} else {
		tx = tx.Where("is_delete = ?", 0)
		if tx.Error != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+"GetUserCollectList tx.Error: %v", tx.Error)
			return nil, tx.Error
		}
	}

	if len(req.SearchName) != 0 {
		tx = tx.Where("name LIKE ?", "%"+req.SearchName+"%")
		if tx.Error != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+"GetUserCollectList tx.Error: %v", tx.Error)
			return nil, tx.Error
		}
	}

	if len(req.Status) > 0 {
		if req.Status == enum.Running {
			tx = tx.Where("status IN ?", []enum.FigureTaskStatus{enum.Running, enum.WaitCheck, enum.Precessing,
				enum.PreCheck, enum.FigureCreate, enum.MakeTemplateVideo})
		} else {
			tx = tx.Where("status = ?", req.Status)
		}
		if tx.Error != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+"GetUserCollectList tx.Error: %v", tx.Error)
			return nil, tx.Error
		}
	}

	if len(req.Tags) > 0 {
		tagindexs := make([]int64, 0)
		taginfos, err := (&FigureTagInfo{}).GetTagListByTag(mysqlclient.DbMap[mysqlclient.StarLightDBName], req.Tags)
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+"GetTagListByTag tx.Error: %v", tx.Error)
			return nil, err
		}

		for _, taginfo := range taginfos {
			tagindexs = append(tagindexs, taginfo.ID)
		}
		if len(tagindexs) > 0 {
			logger.Log.Infof(utils.MMark(logCtx)+"tagindexs: %v", tagindexs)
			materialIds, err := (&MaterialTagMapping{}).GetMaterialByTagID(mysqlclient.DbMap[mysqlclient.StarLightDBName], MaterialTypeFigure, tagindexs, len(tagindexs))
			if err != nil {
				logger.Log.Errorf(utils.MMark(logCtx)+"GetMaterialByTagID tx.Error: %v", tx.Error)
				return nil, err
			}
			if len(materialIds) > 0 {
				logger.Log.Infof(utils.MMark(logCtx)+"materialIds: %v", materialIds)
				tx = tx.Where("id IN ?", materialIds)
				if err != nil {
					logger.Log.Errorf(utils.MMark(logCtx)+"GetUserCollectList tx.Error: %v", tx.Error)
					return nil, err
				}
			} else {
				logger.Log.Warnf(utils.MMark(logCtx) + "don't find materialIds")
				return userfigures, nil
			}
		} else {
			logger.Log.Warnf(utils.MMark(logCtx) + "don't find tagindexs")
			return userfigures, nil
		}
	}

	tx = tx.Order("id DESC").Find(&userfigures)
	if tx.Error != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"GetUserCollectList tx.Error: %v", tx.Error)
		return nil, tx.Error
	}

	for _, figure := range userfigures {
		figure.Collected = true
	}

	return userfigures, nil
}

func (uf *UserFigure) GetPublicFigureList(logCtx context.Context, db *gorm.DB, req *proto.FigureQueryRequst, recentlyUsedIds []int64, collectList []int64) ([]*UserFigure, error) {
	var userfigures []*UserFigure

	tx := db.Where("user_id = ?", "-1")
	if tx.Error != nil {
		return nil, tx.Error

	}

	if req.ContainRemovedFigure {
		tx = tx.Where("(is_delete = 0 OR is_delete = 1)")
		if tx.Error != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+"GetUserFigureList tx.Error: %v", tx.Error)
			return nil, tx.Error
		}
	} else {
		tx = tx.Where("is_delete = ?", 0)
		if tx.Error != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+"GetUserFigureList tx.Error: %v", tx.Error)
			return nil, tx.Error
		}
	}

	if len(req.SearchName) > 0 {
		tx = tx.Where("name LIKE ?", "%"+req.SearchName+"%")
		if tx.Error != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+"GetUserFigureList tx.Error: %v", tx.Error)
			return nil, tx.Error
		}
	}

	if len(req.Status) > 0 {
		if req.Status == enum.Running {
			tx = tx.Where("status IN ?", []enum.FigureTaskStatus{enum.Running, enum.WaitCheck, enum.Precessing,
				enum.PreCheck, enum.FigureCreate, enum.MakeTemplateVideo})
		} else {
			tx = tx.Where("status = ?", req.Status)
		}
		if tx.Error != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+"GetUserFigureList tx.Error: %v", tx.Error)
			return nil, tx.Error
		}
	}

	//

	if len(req.Tags) > 0 {
		tagindexs := make([]int64, 0)
		taginfos, err := (&FigureTagInfo{}).GetTagListByTag(mysqlclient.DbMap[mysqlclient.StarLightDBName], req.Tags)
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+"GetTagListByTag tx.Error: %v", err)
			return nil, err
		}

		for _, taginfo := range taginfos {
			tagindexs = append(tagindexs, taginfo.ID)
		}

		if len(tagindexs) > 0 {
			logger.Log.Infof(utils.MMark(logCtx)+"tagindexs: %v", tagindexs)
			materialIds, err := (&MaterialTagMapping{}).GetMaterialByTagID(mysqlclient.DbMap[mysqlclient.StarLightDBName], MaterialTypeFigure, tagindexs, len(tagindexs))
			if err != nil {
				logger.Log.Errorf(utils.MMark(logCtx)+"GetMaterialByTagID tx.Error: %v", err)
				return nil, err
			}

			if len(materialIds) > 0 {
				logger.Log.Infof(utils.MMark(logCtx)+"materialIds: %v", materialIds)
				tx = tx.Where("id IN ?", materialIds)
				if err != nil {
					logger.Log.Errorf(utils.MMark(logCtx)+"GetUserFigureList tx.Error: %v", tx.Error)
					return nil, err
				}
			} else {
				// 未找到人像ids，返回空
				return userfigures, nil
			}
		} else {
			// 未找到标签，返回空
			return userfigures, nil
		}
	}

	// 常用人像列表排序
	tx = orderByRecentlyUsedIds(logCtx, tx, recentlyUsedIds)
	if tx.Error != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"GetUserFigureList tx.Error: %v", tx.Error)
		return nil, tx.Error
	}

	tx = tx.Find(&userfigures)
	if tx.Error != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"GetUserFigureList tx.Error: %v", tx.Error)
		return nil, tx.Error
	}
	setCollectAndRecentlyUsedByFigure(userfigures, recentlyUsedIds, collectList)
	return userfigures, nil
}

func (uf *UserFigure) GetUserFigureByIdList(logCtx context.Context, db *gorm.DB, ids []int64, recentlyUsedIds []int64, collectList []int64) ([]*UserFigure, error) {
	var userfigures []*UserFigure

	if len(ids) == 0 {
		return userfigures, nil
	}

	tx := db.Where("id IN ?", ids)
	if tx.Error != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"GetUserFigureList tx.Error: %v", tx.Error)
		return nil, tx.Error
	}

	// 常用人像列表排序
	tx = orderByRecentlyUsedIds(logCtx, tx, recentlyUsedIds)
	if tx.Error != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"GetUserFigureList tx.Error: %v", tx.Error)
		return nil, tx.Error
	}

	tx.Find(&userfigures)
	if tx.Error != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+" tx.Error: %v", tx.Error)
		return nil, tx.Error
	}

	setCollectAndRecentlyUsedByFigure(userfigures, recentlyUsedIds, collectList)
	return userfigures, nil
}

func setCollectAndRecentlyUsedByFigure(userfigures []*UserFigure, recentlyUsedIds []int64, collectList []int64) {
	// 设置一些参数，用于前端展示
	recentlyUsedIdsMap := make(map[int64]bool)

	for _, recentlyId := range recentlyUsedIds {
		recentlyUsedIdsMap[recentlyId] = true
	}

	for _, figure := range userfigures {
		if v, ok := recentlyUsedIdsMap[figure.ID]; ok && v {
			figure.RecentlyUsed = true
			figure.Scene = "去视频"
		}
	}
	// 设置是否被收藏
	collectMap := make(map[int64]bool)
	for _, id := range collectList {
		collectMap[id] = true
	}

	for _, figure := range userfigures {
		if v, ok := collectMap[figure.ID]; ok && v {
			figure.Collected = true
		}
	}
}

func orderByRecentlyUsedIds(logCtx context.Context, tx *gorm.DB, recentlyUsedIds []int64) *gorm.DB {
	// recentlyUsedIds 的条件，按 id 排序，最近使用的 id 排在前面
	if len(recentlyUsedIds) > 0 {
		recentlySql := ""
		for i, recentlyId := range recentlyUsedIds {
			if i == len(recentlyUsedIds)-1 {
				recentlySql += fmt.Sprintf("%d", recentlyId)
			} else {
				recentlySql += fmt.Sprintf("%d,", recentlyId)
			}
		}
		tx = tx.Order("CASE WHEN id IN (" + recentlySql + ") THEN 0 ELSE 1 END, id DESC")
		if tx.Error != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+"GetUserFigureList tx.Error: %v", tx.Error)
			return tx
		}
	} else {
		tx = tx.Order("id DESC")
		if tx.Error != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+" tx.Error: %v", tx.Error)
			return tx
		}
	}
	return tx
}

func (uf *UserFigure) GetPublicFigureListByTest(db *gorm.DB) ([]*UserFigure, error) {
	var userfigures []*UserFigure

	tx := db.Where("user_id = ?", "-1").Where("is_delete = ?", 0).Where("status = ?", "success").Find(&userfigures)
	if tx.Error != nil {
		return nil, tx.Error

	}

	return userfigures, nil
}

func (uf *UserFigure) FindByIdAndUserId(db *gorm.DB, id int64, userId string) error {
	err := db.Model(uf).
		Where("id = ?", id).
		Where("user_id = ?", userId).
		Where("is_delete = ?", 0).Find(&uf).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		return nil
	}
	return err
}

func (uf *UserFigure) UpdateNameAndSortById(db *gorm.DB, id int64, name string, sort int64) error {
	return db.Model(uf).Where("id = ?", id).Updates(map[string]interface{}{"name": name, "sort": sort}).Error
}

// 根据name，userid, 以及是否是删除，查询人像
func (uf *UserFigure) FindByUserIdAndName(db *gorm.DB, userId string, figureName string, delete int) error {
	var err error

	if delete == 0 || delete == 1 {
		err = db.Model(uf).
			Where("user_id = ?", userId).
			Where("name = ?", figureName).
			Where("is_delete = ?", delete).Find(&uf).Error
	} else {
		err = db.Model(uf).
			Where("user_id = ?", userId).
			Where("name = ?", figureName).Find(&uf).Error
	}

	if err != nil && err != gorm.ErrRecordNotFound {
		return nil
	}
	return err
}

// 根据userid查询用户名下人像的数目
func (uf *UserFigure) FindCountByUserId(db *gorm.DB, userId string) (int64, error) {
	tx := db.Model(uf).Where("user_id = ?", userId)
	if tx.Error != nil {
		return 0, tx.Error
	}

	var count int64
	tx = tx.Count(&count)

	if tx.Error != nil {
		return 0, tx.Error
	}

	return count, nil
}

// 根据userid查询用户名下人像的数目
func (uf *UserFigure) FindV4FigureCountByUserId(db *gorm.DB, userId string) (int64, error) {
	tx := db.Model(uf).Where("user_id = ?", userId).Where("resource_label = ?", "2D_LITE_VIS_V4").Where("type != ?", 3)
	if tx.Error != nil {
		return 0, tx.Error
	}

	var count int64
	tx = tx.Count(&count)

	if tx.Error != nil {
		return 0, tx.Error
	}

	return count, nil
}
