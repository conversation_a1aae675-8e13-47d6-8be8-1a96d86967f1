package model

import (
	"context"
	"gorm.io/gorm"
)

type TtsPerson struct {
	ID              int                  `gorm:"primaryKey;autoIncrement;column:id" json:"id"`
	TagMappings     []MaterialTagMapping `gorm:"foreignKey:material_id;references:id" json:"-"`
	UserID          string               `gorm:"column:user_id;type:varchar(128);not null;default:'-1'" json:"userId"`           // 是否定制化音色，非定制化音色为-1
	Name            string               `gorm:"column:name;type:varchar(255);not null;uniqueIndex:idx_unique_name" json:"name"` // 发音人名称
	Gender          int                  `gorm:"column:gender;type:tinyint(4);not null" json:"gender"`                           // 0:男，1：女
	Per             string               `gorm:"column:per;type:varchar(255);not null;uniqueIndex:idx_unique_per" json:"per"`    // 唯一音色id
	Sort            int                  `gorm:"column:sort;type:int(11);not null;default:0" json:"sort"`
	Config          JSONMap              `gorm:"column:config;type:text" json:"config"`       // 语速，音调，音量
	Extra           JSONMap              `gorm:"column:extra;type:varchar(256)" json:"extra"` // 一个per里面的其他音色，采样率
	Format          string               `gorm:"column:format;type:varchar(64)" json:"-"`
	Describe        string               `gorm:"column:describe;type:varchar(128)" json:"describe"`
	ThumbnailURL    string               `gorm:"column:thumbnail_url;type:varchar(128);not null" json:"thumbnailUrl"` // 发音人头像
	ExampleAudioURL string               `gorm:"type:varchar(200);not null;default:'';column:example_audio_url;comment:试听音频URL" json:"exampleAudioUrl"`
	IsCollect       int                  `gorm:"-" json:"isCollect"` // 是否收藏
	TagList         string               `gorm:"-" json:"tagList"`
	TagListDetail   []string             `gorm:"-" json:"tagListDetail"`
	NewID           string               `gorm:"-" json:"newID"`
	IsNatural       int                  `gorm:"column:is_natural;type:tinyint(4);not null;default:0" json:"isNatural"` // 超自然
	IsPublish       int                  `gorm:"column:is_publish;type:tinyint(4);not null;default:1" json:"isPublish"` // 已发布
	Language        string               `gorm:"-" json:"language"`                                                     // 语种
}

func (dao *TtsPerson) TableName() string {
	return "tts_person"
}

func (dao *TtsPerson) GetPublicTimbre(logCtx context.Context, db *gorm.DB, ids []int64) ([]*TtsPerson, error) {
	var ttsPersons []*TtsPerson

	// ID 匹配
	tx := db.Where("id IN ?", ids)
	if tx.Error != nil {
		return nil, tx.Error
	}

	// 公共音色
	tx = db.Where("user_id IN ?", []string{"-1", "-2"})
	if tx.Error != nil {
		return nil, tx.Error
	}

	tx = tx.Find(&ttsPersons)
	if tx.Error != nil {
		return nil, tx.Error
	}

	return ttsPersons, nil
}
