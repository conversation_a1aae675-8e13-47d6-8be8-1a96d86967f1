package model

import (
	"acg-ai-go-common/gomysql"
)

func InitMysqlDBTable() error {
	if err := gomysql.DB.AutoMigrate(&AnimationTemplate{}); err != nil {
		return err
	}

	if err := gomysql.DB.AutoMigrate(&PushVideoEntity{}); err != nil {
		return err
	}

	if err := gomysql.DB.AutoMigrate(&ActivityBannerEntity{}); err != nil {
		return err
	}

	if err := gomysql.DB.AutoMigrate(&CaptionsTemplate{}); err != nil {
		return err
	}

	if err := gomysql.DB.AutoMigrate(&Fonts{}); err != nil {
		return err
	}
	return nil
}
