package model

import (
	"acg-ai-go-common/gomysql"
	"database/sql/driver"
	"encoding/json"
	"errors"
)

type JSONMap map[string]interface{}

// Value 方法实现 driver.Valuer 接口，用于将 JSONMap 转换为 JSON 字符串存储在数据库中。
func (jm JSONMap) Value() (driver.Value, error) {
	if jm == nil {
		return nil, nil
	}
	b, err := json.Marshal(jm)
	if err != nil {
		return nil, err
	}
	return string(b), nil
}

// Scan 方法实现 sql.Scanner 接口，用于将数据库中的 JSON 字符串转换回 JSONMap。
func (jm *JSONMap) Scan(input interface{}) error {
	if input == nil {
		return nil
	}
	bytes, ok := input.([]byte)
	if !ok {
		return errors.New("type assertion to []byte failed")
	}
	return json.Unmarshal(bytes, jm)
}

func InitMysqlDBTable() error {
	if err := gomysql.DB.AutoMigrate(&AnimationTemplate{}); err != nil {
		return err
	}

	if err := gomysql.DB.AutoMigrate(&PushVideoEntity{}); err != nil {
		return err
	}

	if err := gomysql.DB.AutoMigrate(&ActivityBannerEntity{}); err != nil {
		return err
	}

	if err := gomysql.DB.AutoMigrate(&CaptionsTemplate{}); err != nil {
		return err
	}

	if err := gomysql.DB.AutoMigrate(&Fonts{}); err != nil {
		return err
	}
	return nil
}
