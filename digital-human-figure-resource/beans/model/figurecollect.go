package model

import "gorm.io/gorm"

type MaterialType int

const (
	MaterialTypeTts MaterialType = iota
	MaterialTypeFigure
	MaterialTypeOldFigure
)

// Material 结构体对应数据库中的表
type UserCollectMapping struct {
	ID           int64        `gorm:"primaryKey;column:id"`          // 主键ID
	UserID       string       `gorm:"column:user_id;not null"`       // 用户ID，外键可能需要根据实际情况添加
	MaterialID   int64        `gorm:"column:material_id;not null"`   // 素材ID
	Config       string       `gorm:"column:config"`                 // 配置信息，假设为字符串类型
	MaterialType MaterialType `gorm:"column:material_type;not null"` // 素材类型
}

// TableName 指定gorm使用的表名，如果结构体名称为Material，并且表名也是material，则可以省略这个方法
func (u *UserCollectMapping) TableName() string {
	return "user_collect_mapping" // 这里假设数据库表名为material
}

func (u *UserCollectMapping) Create(db *gorm.DB) error {
	return db.Create(&u).Error
}

func (u *UserCollectMapping) Delete(db *gorm.DB, id int64, materialType MaterialType) error {
	return db.Where("id = ?", id).Where("material_type = ?", materialType).Unscoped().Delete(&UserCollectMapping{}).Error
}

func (u *UserCollectMapping) GetUserCollectListByMaterialType(db *gorm.DB, userID string) ([]*UserCollectMapping, error) {
	var userCollectList []*UserCollectMapping
	if err := db.Where("user_id = ?", userID).Order("id desc").Find(&userCollectList).Error; err != nil {
		return userCollectList, err
	}
	return userCollectList, nil
}

func (u *UserCollectMapping) GetUserCollecById(db *gorm.DB, userID string, figureID int64, materialType MaterialType) (*UserCollectMapping, error) {
	var userCollectList UserCollectMapping
	if err := db.Where("user_id = ?", userID).Where("material_type = ?", materialType).Where("material_id = ?", figureID).First(&userCollectList).Error; err != nil {
		return &userCollectList, err
	}
	return &userCollectList, nil
}
