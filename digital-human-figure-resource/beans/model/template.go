package model

import (
	"acg-ai-go-common/gomysql"
	"time"
)

type Template struct {
	Id               int64     `gorm:"column:id;primary_key;AUTO_INCREMENT" json:"id"`
	TemplateId       string    `gorm:"column:template_id;NOT NULL" json:"template_id"` // 模版id
	Name             string    `gorm:"column:name;NOT NULL" json:"name"`               // 模版名称
	Type             string    `gorm:"column:type;NOT NULL" json:"type"`               // 模版类型(BusinessCard/Slide)
	Tag              string    `gorm:"column:tag;NOT NULL" json:"tag"`                 // 模版标签
	SubTag           string    `gorm:"column:sub_tag" json:"sub_tag"`
	AspectWidth      int       `gorm:"column:aspect_width;default:16;NOT NULL" json:"aspect_width"`
	AspectHeight     int       `gorm:"column:aspect_height;default:9;NOT NULL" json:"aspect_height"`
	WhetherSystem    int       `gorm:"column:whether_system;default:1;NOT NULL" json:"whether_system"` // 是否属于系统库
	Config           string    `gorm:"column:config;NOT NULL" json:"config"`                           // 模板信息
	TrackConfig      string    `gorm:"column:track_config" json:"track_config"`
	Variables        string    `gorm:"column:variables;NOT NULL" json:"variables"`                               // 变量表
	OriginContent    string    `gorm:"column:origin_content;NOT NULL" json:"origin_content"`                     // 原始文本内容
	ThumbnailUrl     string    `gorm:"column:thumbnail_url;NOT NULL" json:"thumbnail_url"`                       // 缩略图地址
	VideoUrl         string    `gorm:"column:video_url;NOT NULL" json:"video_url"`                               // 模版视频地址
	AiCsvUrl         string    `gorm:"column:ai_csv_url;NOT NULL" json:"ai_csv_url"`                             // ai配音的csv文件地址
	RealPersonCsvUrl string    `gorm:"column:real_person_csv_url;NOT NULL" json:"real_person_csv_url"`           // 真人配音的csv文件地址
	CreateTime       time.Time `gorm:"column:create_time;default:CURRENT_TIMESTAMP;NOT NULL" json:"create_time"` // 创建时间
	UpdateTime       time.Time `gorm:"column:update_time;default:CURRENT_TIMESTAMP;NOT NULL" json:"update_time"` // 更新时间
	Duration         int64     `gorm:"column:duration;default:0;NOT NULL" json:"duration"`                       // duration
	LlmConfigId      string    `gorm:"column:llm_config_id" json:"llm_config_id"`                                // 大模型id
	IsDelete         int       `gorm:"column:is_delete;default:0;NOT NULL" json:"is_delete"`                     // 删除标记
	DefaultNum       int64     `gorm:"column:default_num;default:0;NOT NULL" json:"default_num"`                 // 默认使用数量
	Sort             int64     `gorm:"column:sort;default:0;NOT NULL" json:"sort"`                               // 排序权重
	IsRecommended    int       `gorm:"column:is_recommended;default:0;NOT NULL" json:"is_recommended"`           // 是否为推荐模板
}

func (t *Template) TableName() string {
	return "template"
}

func (t *Template) Save() error {
	return gomysql.DB.Save(t).Error
}

func (t *Template) FindTemplateByRequest(name string, isRecommended *int, pageNo, pageSize int) ([]Template, error) {
	var tasks []Template
	tx := gomysql.DB.Model(&Template{})
	if name != "" {
		tx = tx.Where("name LIKE ?", "%"+name+"%")
	}

	if isRecommended != nil {
		tx = tx.Where("is_recommended = ?", isRecommended)
	}
	if pageNo <= 0 {
		pageNo = 1
	}
	if pageSize <= 0 {
		pageSize = 10
	}
	offset := (pageNo - 1) * pageSize
	err := tx.Order("sort desc , create_time desc").Limit(pageSize).Offset(offset).Find(&tasks).Error
	if err != nil {
		return nil, err
	}
	return tasks, nil
}

func (t *Template) FindTemplateByTemplateId(templateId string) (*Template, error) {
	var task *Template
	err := gomysql.DB.Model(&Template{}).Where("template_id = ?", templateId).First(&task).Error
	if err != nil {
		return nil, err
	}
	return task, nil
}
