package model

import (
	"digital-human-figure-resource/beans/enum"
	"time"

	"gorm.io/gorm"
)

type AnimationTemplate struct {
	ID               int64              `json:"id" gorm:"primaryKey;autoIncrement;"`
	AnimationName    string             `json:"animationName" gorm:"column:animationName;not null;type:varchar(256)"`        // 动画名称
	AnimationID      string             `json:"animationID" gorm:"column:animationID;not null;type:varchar(50)"`             // 动画ID
	AnimationType    enum.AnimationType `json:"animationType" gorm:"column:animationType;not null;type:varchar(10)"`         // 动画类型
	AnimationContent string             `json:"animationContent" gorm:"column:animationContent;not null;type:varchar(4096)"` // 动画内容
	PngImg           string             `json:"pngImg" gorm:"column:pngImg;not null;type:varchar(1024)"`                     // 图片地址
	GifImg           string             `json:"gifImg" gorm:"column:gifImg;not null;type:varchar(1024)"`                     // gif地址
	Duration         int64              `json:"duration" gorm:"column:duration;not null"`                                    // 动画时长
	CreatedAt        time.Time          `json:"createdAt" gorm:"column:createdAt"`                                           // 创建时间
	UpdatedAt        time.Time          `json:"updatedAt" gorm:"column:updatedAt"`                                           // 更新时间
	DeletedAt        gorm.DeletedAt     `json:"-" gorm:"column:deletedAt;index"`                                             // 删除时间/标记删除
}

// TableName 指定表名
func (w *AnimationTemplate) TableName() string {
	return "dhmh_animation_template"
}

// CreateAnimationTemplate 创建动画模板
func (a *AnimationTemplate) CreateAnimationTemplate(db *gorm.DB) error {
	result := db.Create(a)
	if result.Error != nil {
		return result.Error
	}
	return nil
}

// UpdateAnimationTemplate 更新动画模板
func (a *AnimationTemplate) UpdateAnimationTemplate(db *gorm.DB) error {
	result := db.Save(a)
	if result.Error != nil {
		return result.Error
	}
	return nil
}

func (a *AnimationTemplate) ListAnimationTemplate(db *gorm.DB, page int, pagesize int, animationType enum.AnimationType) ([]*AnimationTemplate, error) {
	var tasks []*AnimationTemplate
	// 计算偏移量
	offset := (page - 1) * pagesize
	err := db.Where("animationType = ?", animationType).Order("id ASC").Limit(pagesize).Offset(offset).Find(&tasks).Error
	if err != nil {
		return nil, err
	}
	return tasks, nil
}

func (a *AnimationTemplate) GetAnimationTemplate(db *gorm.DB, animationID string) (AnimationTemplate, error) {
	var tasks AnimationTemplate

	err := db.Where("animationID = ?", animationID).First(&tasks).Error
	if err != nil {
		return tasks, err
	}
	return tasks, nil
}
