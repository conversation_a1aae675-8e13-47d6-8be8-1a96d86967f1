package model

import (
	"gorm.io/gorm"
	"time"
)

type PushVideoEntity struct {
	ID          int64     `json:"id" gorm:"primaryKey;autoIncrement;"`                                                             // 主键自增
	VideoID     string    `json:"videoId" gorm:"column:video_id;uniqueIndex:idx_video_id_user_id;not null;type:varchar(64);index"` // 视频ID
	DraftID     string    `json:"draftId" gorm:"column:draft_id;type:varchar(64)"`                                                 // 草稿ID
	VideoUrl    string    `json:"videoUrl" gorm:"column:video_url;type:varchar(512)"`                                              // 视频URL
	VideoName   string    `json:"videoName" gorm:"column:video_name;not null;type:varchar(256)"`                                   // 视频名称
	UserID      string    `json:"userId" gorm:"column:user_id;uniqueIndex:idx_video_id_user_id;not null;type:varchar(64);index"`   // 用户ID
	SiteCode    string    `json:"siteCode" gorm:"column:site_code;type:varchar(16)"`                                               // 浙广返回的数据，与用户绑定
	UserChannel string    `json:"userChannel" gorm:"column:user_channel;type:varchar(16)"`                                         // 渠道ID 浙广为 LANYUN
	PushStatus  string    `json:"pushStatus" gorm:"column:push_status;default:'UNKNOW';type:char(16)"`                             // 推送状态 UNKNOW、PUSHING、SUCCESS、FAIL
	PushCount   int32     `json:"pushCount" gorm:"column:push_count;type:int(10)"`                                                 // 推送次数
	PushMessage string    `json:"pushMessage" gorm:"column:push_message;type:varchar(512)"`                                        // 错误或者失败信息
	CreateTime  time.Time `json:"createTime" gorm:"column:create_time;autoCreateTime"`                                             // 创建时间
	UpdateTime  time.Time `json:"updateTime" gorm:"column:update_time;autoUpdateTime"`                                             // 更新时间
}

const (
	PushStatusUnknow  = "UNKNOW"
	PushStatusPUSHING = "PUSHING"
	PushStatusSUCCESS = "SUCCESS"
	PushStatusFAIL    = "FAILED"
)

func (m *PushVideoEntity) TableName() string {
	return "push_video"
}

func (p *PushVideoEntity) Create(db *gorm.DB) error {
	err := db.Model(&PushVideoEntity{}).Create(p).Error
	return err
}

func (p *PushVideoEntity) SelectByVideoID(db *gorm.DB, videoID string) (*PushVideoEntity, error) {
	var task *PushVideoEntity
	err := db.Model(&PushVideoEntity{}).Where("video_id = ?", videoID).First(&task).Error
	if err != nil {
		return nil, err
	}
	return task, nil
}

func (p *PushVideoEntity) SelectByVideoName(db *gorm.DB, videoName string) (*PushVideoEntity, error) {
	var task *PushVideoEntity
	err := db.Model(&PushVideoEntity{}).Where("video_name = ?", videoName).First(&task).Error
	if err != nil {
		return nil, err
	}
	return task, nil
}

func (p *PushVideoEntity) UpdateVideoIdByVideoName(db *gorm.DB, videoName string) error {

	if err := db.Model(&PushVideoEntity{}).Where("video_id = ?", videoName).Update("push_status", PushStatusSUCCESS).Error; err != nil {
		return err
	}
	return nil
}

// 开始推时调用，记录推送状态为 PUSHING
func (a *PushVideoEntity) UpdateVideoStatusOnPush(db *gorm.DB, videoID string) error {
	if err := db.Model(&PushVideoEntity{}).Where("video_id = ?", videoID).Update("push_status", PushStatusPUSHING).Error; err != nil {
		return err
	}
	return nil
}

// 开始推时调用，记录推送状态为 PUSHING 和视频地址
func (a *PushVideoEntity) UpdateVideoUrlAndStatusOnPush(db *gorm.DB, videoID string, videoURL string) error {
	if err := db.Model(&PushVideoEntity{}).Where("video_id = ?", videoID).Updates(map[string]interface{}{
		"push_status": PushStatusPUSHING,
		"video_url":   videoURL,
	}).Error; err != nil {
		return err
	}
	return nil
}

// 开始推时调用，记录推送状态为 PUSHING
func (a *PushVideoEntity) UpdateVideosStatusOnPush(db *gorm.DB, videoIDs []string) error {
	if err := db.Model(&PushVideoEntity{}).Where("video_id IN ?", videoIDs).Update("push_status", PushStatusPUSHING).Error; err != nil {
		return err
	}
	return nil
}

// 推送成功时调用，记录推送成功状态，增加推送次数
func (p *PushVideoEntity) UpdateVideoStatusSuccess(db *gorm.DB, videoID string) error {
	if err := db.Model(&PushVideoEntity{}).Where("video_id = ?", videoID).Updates(map[string]interface{}{
		"push_status": PushStatusSUCCESS,
		"push_count":  gorm.Expr("push_count + ?", 1),
	}).Error; err != nil {
		return err
	}
	return nil
}

// 推送失败时调用，记录推送成功状态，和原因
func (p *PushVideoEntity) UpdateVideoStatusFail(db *gorm.DB, videoID string, pushMsg string) error {
	if err := db.Model(&PushVideoEntity{}).Where("video_id = ?", videoID).Updates(map[string]interface{}{
		"push_status":  PushStatusFAIL,
		"push_message": pushMsg,
	}).Error; err != nil {
		return err
	}
	return nil
}

func (a *PushVideoEntity) CreatePushVideo(db *gorm.DB) error {
	result := db.Model(&PushVideoEntity{}).Create(a)
	if result.Error != nil {
		return result.Error
	}
	return nil
}

func (a *PushVideoEntity) SelectByVideoIDs(db *gorm.DB, videoIDs []string, userID string) ([]PushVideoEntity, error) {
	var videos []PushVideoEntity
	if err := db.Model(&PushVideoEntity{}).Where("video_id IN ?  AND user_id = ?", videoIDs, userID).Find(&videos).Error; err != nil {
		return nil, err
	}
	return videos, nil
}

func (a *PushVideoEntity) UpdateVideosStatus(db *gorm.DB, videoIDs []string, pushStatus string) error {
	if err := db.Model(&PushVideoEntity{}).Where("videoid IN ?", videoIDs).Update("push_status", pushStatus).Error; err != nil {
		return err
	}
	return nil
}
