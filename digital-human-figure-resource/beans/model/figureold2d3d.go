package model

import (
	"acg-ai-go-common/logger"
	"acg-ai-go-common/utils"
	"context"
	"digital-human-figure-resource/beans/proto"
	"digital-human-figure-resource/mysqlclient"

	"gorm.io/gorm"
)

type CharacterConfigLabel string

const (
	CharacterConfigLabel2D CharacterConfigLabel = "2D"
	CharacterConfigLabel3D CharacterConfigLabel = "3D"
)

type CharacterConfig struct {
	proto.CharacterConfig
	FaceLocation string `json:"faceLocation" gorm:"column:face_location;type:varchar(512)"`
	IsDelete     int64  `json:"isDelete" gorm:"column:is_delete"`
	// 程序使用的字段，不是数据库中的字段
	Collected      bool   `json:"collected" gorm:"-"`
	RecentlyUsed   bool   `json:"recentlyUsed" gorm:"-"`
	Scene          string `json:"scene,omitempty" gorm:"-"`
	SystemProvided bool   `json:"systemProvided" gorm:"-"`
}

func (c *CharacterConfig) TableName() string {
	return "character_config"
}

func (c *CharacterConfig) GetUserCharacterById(db *gorm.DB, figureID int64) (*CharacterConfig, error) {
	var userf CharacterConfig
	if err := db.Where("id = ?", figureID).Where("is_delete = ?", 0).First(&userf).Error; err != nil {
		return nil, err
	}
	return &userf, nil
}

func (c *CharacterConfig) GetUserCharacterConfig(logCtx context.Context, db *gorm.DB, label CharacterConfigLabel, req *proto.FigureQueryRequst, accountid string, collectList []int64, recentlyUsedIds []string) ([]*CharacterConfig, error) {
	var characterConfig []*CharacterConfig
	tx := db.Where("user_id = ?", accountid).Where("is_delete = ?", 0).Where("type != ?", "UE5_TEXT_TO_3D")
	if tx.Error != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"get character config error: %v", tx.Error)
		return characterConfig, tx.Error
	}

	if len(label) > 0 {
		tx = tx.Where("label = ?", label)
		if tx.Error != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+"get character config error: %v", tx.Error)
			return characterConfig, tx.Error
		}
	}

	if len(req.SearchName) > 0 {
		tx = tx.Where("name LIKE ?", "%"+req.SearchName+"%")
		if tx.Error != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+"get character config error: %v", tx.Error)
			return characterConfig, tx.Error
		}
	}

	if len(req.Tags) > 0 {
		tagindexs := make([]int64, 0)
		taginfos, err := (&FigureTagInfo{}).GetTagListByTag(mysqlclient.DbMap[mysqlclient.StarLightDBName], req.Tags)
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+"GetTagListByTag tx.Error: %v", err)
			return nil, err
		}

		for _, taginfo := range taginfos {
			tagindexs = append(tagindexs, taginfo.ID)
		}

		if len(tagindexs) > 0 {
			logger.Log.Infof(utils.MMark(logCtx)+"tagindexs: %v", tagindexs)
			materialIds, err := (&MaterialTagMapping{}).GetMaterialByTagID(mysqlclient.DbMap[mysqlclient.StarLightDBName], MaterialTypeOldFigure, tagindexs, len(tagindexs))
			if err != nil {
				logger.Log.Errorf(utils.MMark(logCtx)+"GetMaterialByTagID tx.Error: %v", err)
				return nil, err
			}

			if len(materialIds) > 0 {
				logger.Log.Infof(utils.MMark(logCtx)+"materialIds: %v", materialIds)
				tx = tx.Where("id IN ?", materialIds)
				if err != nil {
					logger.Log.Errorf(utils.MMark(logCtx)+"GetUserFigureList tx.Error: %v", tx.Error)
					return nil, err
				}
			} else {
				logger.Log.Warnf(utils.MMark(logCtx) + "don't find materialIds")
				// 未找到人像ids，返回空
				return characterConfig, nil
			}
		} else {
			logger.Log.Warnf(utils.MMark(logCtx) + "don't find tagindexs")
			// 未找到标签，返回空
			return characterConfig, nil
		}
	}

	// recentlyUsedIds 的条件，按 character_id 排序，最近使用的 character_id 排在前面
	tx = orderByRecentlyUsedIdsByCharacter(logCtx, tx, recentlyUsedIds)
	if tx.Error != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"GetUserFigureList tx.Error: %v", tx.Error)
		return nil, tx.Error
	}

	err := tx.Find(&characterConfig).Error
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"get character config error: %v", err)
		return characterConfig, err
	}

	setCollectAndRecentlyUsed(characterConfig, collectList, recentlyUsedIds, false)
	return characterConfig, nil
}

func (c *CharacterConfig) GetPublicCharacterConfig(logCtx context.Context, db *gorm.DB, label CharacterConfigLabel, req *proto.FigureQueryRequst, collectList []int64, recentlyUsedIds []string) ([]*CharacterConfig, error) {
	var characterConfig []*CharacterConfig
	tx := db.Where("user_id = ?", "System").Where("type != ?", "UE5_TEXT_TO_3D").Where("is_delete = ?", 0)
	if tx.Error != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"get character config error: %v", tx.Error)
		return characterConfig, tx.Error
	}

	if len(label) > 0 {
		tx = tx.Where("label = ?", label)
		if tx.Error != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+"get character config error: %v", tx.Error)
			return characterConfig, tx.Error
		}
	}

	if len(req.SearchName) > 0 {
		tx = tx.Where("name LIKE ?", "%"+req.SearchName+"%")
		if tx.Error != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+"get character config error: %v", tx.Error)
			return characterConfig, tx.Error
		}
	}

	if len(req.Tags) > 0 {
		tagindexs := make([]int64, 0)
		taginfos, err := (&FigureTagInfo{}).GetTagListByTag(mysqlclient.DbMap[mysqlclient.StarLightDBName], req.Tags)
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+"GetTagListByTag tx.Error: %v", err)
			return nil, err
		}

		for _, taginfo := range taginfos {
			tagindexs = append(tagindexs, taginfo.ID)
		}

		if len(tagindexs) > 0 {
			logger.Log.Infof(utils.MMark(logCtx)+"tagindexs: %v", tagindexs)
			materialIds, err := (&MaterialTagMapping{}).GetMaterialByTagID(mysqlclient.DbMap[mysqlclient.StarLightDBName], MaterialTypeOldFigure, tagindexs, len(tagindexs))
			if err != nil {
				logger.Log.Errorf(utils.MMark(logCtx)+"GetMaterialByTagID tx.Error: %v", err)
				return nil, err
			}

			if len(materialIds) > 0 {
				logger.Log.Infof(utils.MMark(logCtx)+"materialIds: %v", materialIds)
				tx = tx.Where("id IN ?", materialIds)
				if err != nil {
					logger.Log.Errorf(utils.MMark(logCtx)+"GetUserFigureList tx.Error: %v", tx.Error)
					return nil, err
				}
			} else {
				logger.Log.Warnf(utils.MMark(logCtx) + "don't find materialIds")
				// 未找到人像ids，返回空
				return characterConfig, nil
			}
		} else {
			logger.Log.Warnf(utils.MMark(logCtx) + "don't find tagindexs")
			// 未找到标签，返回空
			return characterConfig, nil
		}
	}

	// recentlyUsedIds 的条件，按 character_id 排序，最近使用的 character_id 排在前面
	tx = orderByRecentlyUsedIdsByCharacter(logCtx, tx, recentlyUsedIds)
	if tx.Error != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"GetUserFigureList tx.Error: %v", tx.Error)
		return nil, tx.Error
	}

	err := tx.Find(&characterConfig).Error
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"get character config error: %v", err)
		return characterConfig, err
	}

	setCollectAndRecentlyUsed(characterConfig, collectList, recentlyUsedIds, true)
	return characterConfig, nil
}

func (c *CharacterConfig) GetCollectCharacterConfig(logCtx context.Context, db *gorm.DB, materialId []int64, label CharacterConfigLabel, req *proto.FigureQueryRequst) ([]*CharacterConfig, error) {
	var characterConfig []*CharacterConfig
	tx := db.Where("id IN ?", materialId).Where("user_id = ?", "System").Where("type != ?", "UE5_TEXT_TO_3D").Where("is_delete = ?", 0)
	if tx.Error != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"get character config error: %v", tx.Error)
		return characterConfig, tx.Error
	}

	if len(label) > 0 {
		tx = tx.Where("label = ?", label)
		if tx.Error != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+"get character config error: %v", tx.Error)
			return characterConfig, tx.Error
		}
	}

	if len(req.SearchName) > 0 {
		tx = tx.Where("name LIKE ?", "%"+req.SearchName+"%")
		if tx.Error != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+"get character config error: %v", tx.Error)
			return characterConfig, tx.Error
		}
	}

	if len(req.Tags) > 0 {
		tagindexs := make([]int64, 0)
		taginfos, err := (&FigureTagInfo{}).GetTagListByTag(mysqlclient.DbMap[mysqlclient.StarLightDBName], req.Tags)
		if err != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+"GetTagListByTag tx.Error: %v", err)
			return nil, err
		}

		for _, taginfo := range taginfos {
			tagindexs = append(tagindexs, taginfo.ID)
		}

		if len(tagindexs) > 0 {
			logger.Log.Infof(utils.MMark(logCtx)+"tagindexs: %v", tagindexs)
			materialIds, err := (&MaterialTagMapping{}).GetMaterialByTagID(mysqlclient.DbMap[mysqlclient.StarLightDBName], MaterialTypeOldFigure, tagindexs, len(tagindexs))
			if err != nil {
				logger.Log.Errorf(utils.MMark(logCtx)+"GetMaterialByTagID tx.Error: %v", err)
				return nil, err
			}

			if len(materialIds) > 0 {
				logger.Log.Infof(utils.MMark(logCtx)+"materialIds: %v", materialIds)
				tx = tx.Where("id IN ?", materialIds)
				if err != nil {
					logger.Log.Errorf(utils.MMark(logCtx)+"GetUserFigureList tx.Error: %v", tx.Error)
					return nil, err
				}
			} else {
				logger.Log.Warnf(utils.MMark(logCtx) + "don't find materialIds")
				// 未找到人像ids，返回空
				return characterConfig, nil
			}
		} else {
			logger.Log.Warnf(utils.MMark(logCtx) + "don't find tagindexs")
			// 未找到标签，返回空
			return characterConfig, nil
		}
	}

	err := tx.Order("id DESC").Find(&characterConfig).Error
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"get character config error: %v", err)
		return characterConfig, err
	}

	setCollectAndRecentlyUsed(characterConfig, materialId, make([]string, 0), true)
	return characterConfig, nil
}

func (c *CharacterConfig) UpdataCharacterConfig(logCtx context.Context, db *gorm.DB) error {
	if err := db.Save(c).Error; err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"updata character config error: %v", err)
		return err
	}
	return nil
}

func (c *CharacterConfig) CreateCharacterConfig(logCtx context.Context, db *gorm.DB) error {
	if err := db.Create(c).Error; err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"updata character config error: %v", err)
		return err
	}
	return nil
}

func (c *CharacterConfig) GetUserCharacterConfigByConfigId(logCtx context.Context, db *gorm.DB, ids []string, collectList []int64, recentlyUsedIds []string) ([]*CharacterConfig, error) {
	var characterConfig []*CharacterConfig
	tx := db.Where("config_id IN ?", ids).Where("type != ?", "UE5_TEXT_TO_3D").Where("is_delete = ?", 0)
	if tx.Error != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"get character config error: %v", tx.Error)
		return characterConfig, tx.Error
	}

	// recentlyUsedIds 的条件，按 character_id 排序，最近使用的 character_id 排在前面
	tx = orderByRecentlyUsedIdsByCharacter(logCtx, tx, recentlyUsedIds)
	if tx.Error != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"GetUserFigureList tx.Error: %v", tx.Error)
		return nil, tx.Error
	}

	err := tx.Find(&characterConfig).Error
	if err != nil {
		logger.Log.Errorf(utils.MMark(logCtx)+"get character config error: %v", err)
		return characterConfig, err
	}

	setCollectAndRecentlyUsed(characterConfig, collectList, recentlyUsedIds, true)
	return characterConfig, nil
}

func (c *CharacterConfig) FindByConfigIDAndUserId(db *gorm.DB, configId string, userId string) error {
	err := db.Model(c).
		Where("config_id = ?", configId).
		Where("user_id = ?", userId).
		Where("is_delete = ?", 0).Find(c).Error
	if err == nil || err == gorm.ErrRecordNotFound {
		return nil
	}
	return err
}

func (c *CharacterConfig) UpdateNameAndSortByConfigID(db *gorm.DB, configId string, name string, sort int64) error {
	return db.Model(c).Where("config_id = ?", configId).Updates(map[string]interface{}{"name": name, "sort": sort}).Error
}

func setCollectAndRecentlyUsed(characterConfig []*CharacterConfig, collectList []int64, recentlyUsedIds []string, systemProvided bool) {
	// 设置一些参数，用于前端展示
	recentlyUsedIdsMap := make(map[string]bool)

	for _, recentlyId := range recentlyUsedIds {
		recentlyUsedIdsMap[recentlyId] = true
	}

	for _, figure := range characterConfig {
		if v, ok := recentlyUsedIdsMap[figure.CharacterID]; ok && v {
			figure.RecentlyUsed = true
			figure.Scene = "去视频"
		}
	}

	// 设置是否被收藏
	collectMap := make(map[int64]bool)
	for _, id := range collectList {
		collectMap[id] = true
	}

	for _, figure := range characterConfig {
		figure.SystemProvided = systemProvided
		if v, ok := collectMap[figure.ID]; ok && v {
			figure.Collected = true
		}
	}
}

func orderByRecentlyUsedIdsByCharacter(logCtx context.Context, tx *gorm.DB, recentlyUsedIds []string) *gorm.DB {
	// recentlyUsedIds 的条件，按 id 排序，最近使用的 id 排在前面
	if len(recentlyUsedIds) > 0 {
		recentlySql := ""
		for i, recentlyId := range recentlyUsedIds {
			if i == len(recentlyUsedIds)-1 {
				recentlySql += "'" + recentlyId + "'"
			} else {
				recentlySql += "'" + recentlyId + "'" + ","
			}
		}
		tx = tx.Order("CASE WHEN character_id IN (" + recentlySql + ") THEN 0 ELSE 1 END, id DESC")
		if tx.Error != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+"GetUserFigureList tx.Error: %v", tx.Error)
			return tx
		}
	} else {
		tx = tx.Order("id DESC")
		if tx.Error != nil {
			logger.Log.Errorf(utils.MMark(logCtx)+" tx.Error: %v", tx.Error)
			return tx
		}
	}
	return tx
}
