package proto

type RiskControlLongVideoSubmitRequest struct {
	VideoURL []string `json:"videoURL"` // 视频URL，字符串数组类型，JSON字段名为"StringArray"
}

// RiskControlLongVideoCensorSubmitResponse
type RiskControlLongVideoCensorSubmitResponse struct {
	ErrorCode int64                                       `json:"error_code,omitempty"` // 错误提示码，失败才返回，成功不返回
	ErrorMsg  string                                      `json:"error_msg,omitempty"`  // 错误提示信息，失败才返回，成功不返回
	LogId     int64                                       `json:"logId"`                // 请求唯一id，用于问题排查
	Msg       string                                      `json:"msg,omitempty"`        // 详细描述结果
	Ret       string                                      `json:"ret"`                  // 响应状态码，可取值：0处理成功，其他为处理失败
	Data      RiskControlLongVideoCensorSubmitDataDetails `json:"data"`                 // 结果详情
}

type RiskControlLongVideoCensorSubmitDataDetails struct {
	TaskId    string `json:"taskId"`    // 长视频的任务taskId
	QueueSize int64  `json:"queueSize"` // 队列中待处理的任务数
}

type RiskControlLongVideoPullRequest struct {
	TaskIds []string `json:"taskIds"` // 本次任务的唯一标识，JSON字段名为"taskId"
}

// Response 表示整个响应的结构体
type RiskControlLongVideoPullResponse struct {
	LogID    string                                 `json:"logId"`    // 请求唯一id
	DataList []RiskControlLongVideoPullDataListItem `json:"dataList"` // 审核结果列表
}

// DataListItem 表示审核结果列表中的单个项目
type RiskControlLongVideoPullDataListItem struct {
	Status                   string                    `json:"status"`                             // 业务状态
	ErrorCode                int64                     `json:"error_code,omitempty"`               // 错误提示码，失败才返回，成功不返回
	ErrorMsg                 string                    `json:"error_msg,omitempty"`                // 错误提示信息，失败才返回，成功不返回
	Conclusion               string                    `json:"conclusion,omitempty"`               // 审核结果
	ConclusionType           int                       `json:"conclusionType,omitempty"`           // 审核结果类型
	ConclusionTypeGroupInfos []ConclusionTypeGroupInfo `json:"conclusionTypeGroupInfos,omitempty"` // 审核结论汇总
}

type RiskControlLongVideoCensorPullResponse struct {
	ErrorCode int64                                     `json:"error_code,omitempty"` // 错误提示码，失败才返回，成功不返回
	ErrorMsg  string                                    `json:"error_msg,omitempty"`  // 错误提示信息，失败才返回，成功不返回
	LogId     int64                                     `json:"logId"`                // 请求唯一id，用于问题排查
	Msg       string                                    `json:"msg,omitempty"`        // 详细描述结果
	Ret       string                                    `json:"ret"`                  // 响应状态码，可取值：0处理成功，其他为处理失败
	Data      RiskControlLongVideoCensorPullDataDetails `json:"data"`                 // 结果详情
}

type RiskControlLongVideoCensorPullDataDetails struct {
	TaskId                   int64                             `json:"taskId"`                             // 长视频的任务taskId
	TaskDuration             int64                             `json:"taskDuration"`                       // 审核的视频时长，单位s
	ExtraInfo                string                            `json:"extraInfo"`                          // 用户提交任务时的extraInfo字段内容
	ErrorCode                int64                             `json:"error_code,omitempty"`               // 错误提示码，失败才返回，成功不返回
	ErrorMsg                 string                            `json:"error_msg,omitempty"`                // 错误提示信息，失败才返回，成功不返回
	Conclusion               string                            `json:"conclusion"`                         // 审核结果描述
	ConclusionType           int64                             `json:"conclusionType"`                     // 审核结果类型
	ConclusionTypeGroupInfos []ConclusionTypeGroupInfo         `json:"conclusionTypeGroupInfos,omitempty"` // 审核结论汇总
	Audios                   []RiskControlLongVideoCensorAudio `json:"audios"`                             // 音频审核结果
	Frames                   []Frame                           `json:"frames"`                             // 帧审核明细
}

type Frame struct {
	FrameTimeStamp    int64    `json:"frameTimeStamp"`    // 帧时间戳
	Conclusion        string   `json:"conclusion"`        // 帧审核结果描述
	ConclusionType    int      `json:"conclusionType"`    // 帧审核结果类型
	FrameUrl          string   `json:"frameUrl"`          // 帧url地址
	FrameThumbnailUrl string   `json:"frameThumbnailUrl"` // 帧缩略图url地址
	Data              []Detail `json:"data"`              // 各维度明细审核结果
}
type RiskControlLongVideoCensorAudio struct {
	StartTime        int64                                      `json:"startTime"`        // 音频开始时间
	EndTime          int64                                      `json:"endTime"`          // 音频结束时间
	AudioUrl         string                                     `json:"audioUrl"`         // 音频url地址
	AudioAuditResult []RiskControlLongAudioCensorPullDataDetail `json:"audioAuditResult"` // 音频审核结果
}
