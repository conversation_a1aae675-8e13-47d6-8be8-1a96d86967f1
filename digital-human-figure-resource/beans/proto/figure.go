package proto

import (
	"digital-human-figure-resource/beans/enum"
	"time"
)

type FigureQueryRequst struct {
	FigureIdList         []FigureQueryRequstWithId `json:"figureIdList"`
	SearchName           string                    `json:"searchName"`           // 人像名称
	PageNo               int                       `json:"pageNo"`               // 页码
	PageSize             int                       `json:"pageSize"`             // 每页数量
	Platform             string                    `json:"platform"`             // 平台
	Tags                 []string                  `json:"tags"`                 // 标签
	Status               enum.FigureTaskStatus     `json:"status"`               // 人像状态
	Collected            bool                      `json:"collected"`            // 是否查询已收藏人像
	System               bool                      `json:"system"`               // 是否查询系统人像
	Example              bool                      `json:"example"`              // 是否查询示例人像
	Resolution           string                    `json:"resolution"`           // 分辨率，格式宽x高:1080x1920（竖屏）1920x1080（横屏）
	SortBy               string                    `json:"sortBy"`               // 排序方式 recommended（推荐）按预置权重推荐值降序 newest（最新）按上线时间降序
	ContainRemovedFigure bool                      `json:"containRemovedFigure"` // 是否包含已删除的人像
	// Type       []string              `json:"type"`       // 人像类型
	// ResourceLabels       []string              `json:"resourceLabels"` // 人像资源标签
}
type FigureQueryRequstWithId struct {
	Id          int64           `json:"id"`
	CharacterID string          `json:"configId"`
	FigureType  enum.FigureType `json:"figureType"`
}
type RecentlyUsedResultItem struct {
	CharacterId       string `json:"characterId"`
	CharacterImageUrl string `json:"characterImageUrl"`
	PerformanceStage  string `json:"performanceStage"`
}

type RecentlyUsedResponse struct {
	CommRsp
	Result []RecentlyUsedResultItem `json:"result"`
}
type FigureQueryResponse struct {
	PageNo     int                    `json:"pageNo,omitempty"`
	PageSize   int                    `json:"pageSize,omitempty"`
	TotalCount int                    `json:"totalCount"`
	FigureList []*FigureQueryDataItem `json:"figureList"`
}

type FigureQueryDataItem struct {
	FaceLocation interface{} `json:"faceLocation,omitempty"`
	Sort         int64       `json:"sort"`
	VideoUrl     string      `json:"videoUrl"`
	FigureQueryData
	CharacterConfig
}

type FigureQueryData struct {
	Source           string    `json:"source,omitempty"`
	Status           string    `json:"status,omitempty"`
	ResourceLabel    string    `json:"resourceLabel,omitempty"`
	TemplateImg      string    `json:"templateImg,omitempty"`
	TemplateVideo    string    `json:"templateVideo,omitempty"`
	FigureName       string    `json:"figureName,omitempty"`
	SubmitTime       time.Time `json:"submitTime,omitempty"` // Depending on the format of the time, you might want to use time.Time
	ConsumeTime      int       `json:"consumeTime,omitempty"`
	Info             string    `json:"info,omitempty"`
	IsNew            bool      `json:"isNew,omitempty"`
	LastUsedTime     time.Time `json:"lastUsedTime,omitempty"`
	Collected        bool      `json:"collected"`
	RecentlyUsed     bool      `json:"recentlyUsed"`
	SystemProvided   bool      `json:"systemProvided"`
	ResolutionWidth  int       `json:"resolutionWidth,omitempty"`
	ResolutionHeight int       `json:"resolutionHeight,omitempty"`
	VideoUrl         string    `json:"videoUrl,omitempty"`
	FigureResult     string    `json:"figureResult,omitempty"`
	EffectsThumbnail string    `json:"effectsThumbnail,omitempty"`
	Effects          *Effects  `json:"effects,omitempty"`
	MatchTts         *TtsVO    `json:"matchTts,omitempty"`
	Scene            string    `json:"scene,omitempty"`
	Category         string    `json:"category,omitempty"`
	PreviewVideoUrl  string    `json:"previewVideoUrl,omitempty"`
	IsDelete         int       `json:"isDelete,omitempty"`
}

type Effects struct {
	ChromaKey *ChromaKey `json:"chromaKey,omitempty"`
	Version   int        `json:"version,omitempty"`
}

type ChromaKey struct {
	Screen     []int   `json:"screen,omitempty"`
	Weight     float32 `json:"weight,omitempty"`
	Balance    float32 `json:"balance,omitempty"`
	ClipBlack  float32 `json:"clipBlack,omitempty"`
	ClipWhite  float32 `json:"clipWhite,omitempty"`
	Similarity int     `json:"similarity,omitempty"`
	Smoothness int     `json:"smoothness,omitempty"`
	Opacity    float32 `json:"opacity,omitempty"`
	Contrast   float32 `json:"contrast,omitempty"`
	Brightness float32 `json:"brightness,omitempty"`
	Gamma      float32 `json:"gamma,omitempty"`
	Spill      int     `json:"spill,omitempty"`
	EdgeShrink float32 `json:"edgeShrink,omitempty"`
}

type TtsVO struct {
	ID           int64             `json:"id,omitempty"`
	Name         string            `json:"name,omitempty"`
	Per          string            `json:"per,omitempty"`
	Config       map[string]string `json:"config,omitempty"`
	Extra        map[string]string `json:"extra,omitempty"`
	Describe     string            `json:"describe,omitempty"`
	ThumbnailUrl string            `json:"thumbnailUrl,omitempty"`
	TagList      string            `json:"tagList,omitempty"`
}

// type CharacterConfig struct {
// 	ID                    int64     `json:"id,omitempty" gorm:"column:id;type:bigint;unique;index;autoIncrement"`              // 修改为自增主键
// 	ConfigID              string    `json:"configId,omitempty" gorm:"-;primaryKey;column:config_id;type:varchar(32)"`          // 唯一字段，非主键
// 	UserID                string    `json:"userId,omitempty" gorm:"-;column:user_id;type:varchar(64);index"`                   // 用户ID，保持索引
// 	Name                  string    `json:"name,omitempty" gorm:"-;column:name;type:varchar(128)"`                             // 配置名称
// 	Type                  string    `json:"type,omitempty" gorm:"-;column:type;type:varchar(128)"`                             // 配置类型
// 	CharacterName         string    `json:"characterName,omitempty" gorm:"-;column:character_name;type:varchar(128)"`          // 角色名称
// 	CharacterID           string    `json:"characterId,omitempty" gorm:"-;column:character_id;type:varchar(512)"`              // 角色ID
// 	Description           string    `json:"description,omitempty" gorm:"-;column:description;type:varchar(512)"`               // 描述
// 	Config                string    `json:"config,omitempty" gorm:"-;column:config;type:text"`                                 // 配置详细信息
// 	Thumbnail             string    `json:"thumbnail,omitempty" gorm:"-;column:thumbnail;type:varchar(512)"`                   // 缩略图
// 	Label                 string    `json:"label,omitempty" gorm:"-;column:label;type:varchar(32)"`                            // 标签
// 	Editor                string    `json:"editor,omitempty" gorm:"-;column:editor;type:varchar(128)"`                         // 最后编辑人
// 	CreateTime            time.Time `json:"createTime,omitempty" gorm:"-;column:create_time;type:timestamp;autoCreateTime"`    // 创建时间
// 	UpdateTime            time.Time `json:"updateTime,omitempty" gorm:"-;column:update_time;type:timestamp;autoUpdateTime"`    // 更新时间
// 	ConfigMode            int8      `json:"configMode,omitempty" gorm:"-;column:config_mode;type:tinyint"`                     // 配置模式
// 	BehaviorPatternConfig string    `json:"behaviorPatternConfig,omitempty" gorm:"-;column:behavior_pattern_config;type:text"` // 行为模式配置
// }

type CharacterConfig struct {
	ID                    int64     `json:"id,omitempty" gorm:"column:id;type:bigint;unique;index;autoIncrement"`            // 修改为自增主键
	ConfigID              string    `json:"configId,omitempty" gorm:"primaryKey;column:config_id;type:varchar(32)"`          // 唯一字段，非主键
	UserID                string    `json:"userId,omitempty" gorm:"column:user_id;type:varchar(64);index"`                   // 用户ID，保持索引
	Name                  string    `json:"name,omitempty" gorm:"column:name;type:varchar(128)"`                             // 配置名称
	Type                  string    `json:"type,omitempty" gorm:"column:type;type:varchar(128)"`                             // 配置类型
	CharacterName         string    `json:"characterName,omitempty" gorm:"column:character_name;type:varchar(128)"`          // 角色名称
	CharacterID           string    `json:"characterId,omitempty" gorm:"column:character_id;type:varchar(512)"`              // 角色ID
	Description           string    `json:"description,omitempty" gorm:"column:description;type:varchar(512)"`               // 描述
	Config                string    `json:"config,omitempty" gorm:"column:config;type:text"`                                 // 配置详细信息
	Thumbnail             string    `json:"thumbnail,omitempty" gorm:"column:thumbnail;type:varchar(512)"`                   // 缩略图
	Label                 string    `json:"label,omitempty" gorm:"column:label;type:varchar(32)"`                            // 标签
	Editor                string    `json:"editor,omitempty" gorm:"column:editor;type:varchar(128)"`                         // 最后编辑人
	CreateTime            time.Time `json:"createTime,omitempty" gorm:"column:create_time;type:timestamp;autoCreateTime"`    // 创建时间
	UpdateTime            time.Time `json:"updateTime,omitempty" gorm:"column:update_time;type:timestamp;autoUpdateTime"`    // 更新时间
	VideoUrl              string    `json:"videoUrl,omitempty" gorm:"column:video_url;type:varchar(256)"`                    // 视频链接
	ConfigMode            int8      `json:"configMode,omitempty" gorm:"column:config_mode;type:tinyint"`                     // 配置模式
	BehaviorPatternConfig string    `json:"behaviorPatternConfig,omitempty" gorm:"column:behavior_pattern_config;type:text"` // 行为模式配置
	UserFigureID          int64     `json:"userFigureId,omitempty" gorm:"column:user_figure_id;type:bigint"`                 // 关联 user_figure 表的 ID
	Sort                  int64     `json:"-" gorm:"column:sort;type:bigint"`                                                // 排序字段
}

type FigureFaceLocation struct {
	Height   float32 `json:"height"`
	Left     float32 `json:"left"`
	Top      float32 `json:"top"`
	Width    float32 `json:"width"`
	Rotation float32 `json:"rotation"`
}

type FigureFaceLocationByOldFigure struct {
	ID               string             `json:"id"`
	Name             string             `json:"name"`
	ResolutionWidth  int                `json:"resolutionWidth,omitempty"`
	ResolutionHeight int                `json:"resolutionHeight,omitempty"`
	FaceLocation     FigureFaceLocation `json:"faceLocation,omitempty"`
}

type FigureUpdateRequest struct {
	ID       *int64  `json:"id" binding:"required"`
	ConfigId *string `json:"configId" binding:"required"`
	Name     string  `json:"name" binding:"required"`
	Sort     *int64  `json:"sort" binding:"required"`
}

// 查询一个用户名下人像名称是否使用
type FigureIsNameUsedRequest struct {
	FigureName string `json:"figureName" binding:"required"` // 人像名称
}

type CloneFigureRequest struct {
	BceAccountId string `json:"bceAccountId" form:"bceAccountId"` // 账号id
}
type CloneFigureResponse struct {
	Success bool `json:"success"`
	Status  int  `json:"status"`
	Result  bool `json:"result"`
}
