package proto

type RiskControlShortAudioRequest struct {
	AudioUrl string `json:"audioUrl"` // 视频URL，字符串数组类型，JSON字段名为"StringArray"
}

type RiskControlShortAudioResponse struct {
	CommRsp
	Result RiskControlShortAudioCensorResult `json:"result"`
}

type RiskControlShortAudioCensorResult struct {
	LogID string                       `json:"logId"`          // 请求唯一id
	Data  RiskControlAudioResponseData `json:"data,omitempty"` // 音频信息

}

type RiskControlAudioResponseData struct {
	TaskId                   string                                     `json:"taskId"`                   // 任务id
	Status                   string                                     `json:"status"`                   // 业务状态
	ErrorCode                int64                                      `json:"error_code,omitempty"`     // 错误提示码，失败才返回，成功不返回
	ErrorMsg                 string                                     `json:"error_msg,omitempty"`      // 错误提示信息，失败才返回，成功不返回
	Conclusion               string                                     `json:"conclusion,omitempty"`     // 审核结果
	ConclusionType           int                                        `json:"conclusionType,omitempty"` // 审核结果类型
	ConclusionTypeGroupInfos []RiskControlAudioConclusionTypeGroupInfos `json:"conclusionTypeGroupInfos"` // 审核结果类型描述
}

type RiskControlAudioConclusionTypeGroupInfos struct {
	Text  string   `json:"text"` // 语音识别文本
	Msg   string   `json:"msg"`  // 不合规项描述信息
	Words []string `json:"words"`
}
