package proto

import "digital-human-figure-resource/beans/enum"

type AnimationConfig struct {
	LogID       string             `json:"logId"`
	Name        string             `json:"name"`
	PngImg      string             `json:"pngImg"`
	GifImg      string             `json:"gifImg"`
	AnimationID string             `json:"animationId"`
	Type        enum.AnimationType `json:"type"`
	Attributes  string             `json:"attributes"`
	Duration    int64              `json:"duration"`
}

type ListAnimationTemplateRequest struct {
	PageNo   int                `json:"pageNo" form:"pageNo" binding:"required"`
	PageSize int                `json:"pageSize" form:"pageSize" binding:"required"`
	Type     enum.AnimationType `json:"type" form:"type" binding:"required"`
}

type CreateAnimationTemplateRequest struct {
	PageNo   int                `json:"pageNo" form:"pageNo" binding:"required"`
	PageSize int                `json:"pageSize" form:"pageSize" binding:"required"`
	Type     enum.AnimationType `json:"type" form:"type" binding:"required"`
}
