package proto

type FontsRequest struct {
	PageNo   int `json:"pageNo" form:"pageNo" binding:"required"`
	PageSize int `json:"pageSize" form:"pageSize" binding:"required"`
}

type FontsResponse struct {
	ID        int64  `json:"id"`
	FontID    string `json:"fontId"`
	Name      string `json:"name"`     // 字幕模版名称
	PngURL    string `json:"pngUrl"`   // PNG 图片 URL
	FontsURL  string `json:"fontsUrl"` // 视频 URL
	Sort      int64  `json:"sort"`
	LogId     string `json:"logId,omitempty"`
	IsDefault int64  `json:"isDefault"`
}

type FontsResponseWithInternal struct {
	TotalCount int64           `json:"totalCount"`
	List       []FontsResponse `json:"list"`
}

type UpdateFonstRequest struct {
	ID        int64  `json:"id"`
	FontID    string `json:"fontId" binding:"required"`
	Name      string `json:"name"  binding:"required"`
	PngURL    string `json:"pngUrl"  binding:"required"`
	FontsURL  string `json:"fontsUrl"  binding:"required"`
	Sort      int64  `json:"sort"`
	IsDefault int64  `json:"isDefault"`
}

type DeleteFontsRequest struct {
	IDs []int64 `json:"ids" binding:"required"`
}
