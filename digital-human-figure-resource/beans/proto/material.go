package proto

import "time"

type AddMaterialRequest struct {
	FileUrl       string `form:"fileUrl"`
	FileName      string `form:"fileName"`
	Module        string `form:"module"`
	WhetherSystem string `form:"whetherSystem"`
	IsNeedCensor  bool   `form:"isNeedCensor"`
}

type AddMaterialResponse struct {
	CreateTime time.Time `json:"createTime"`
	UpdateTime time.Time `json:"updateTime"`
	MaterialId string    `json:"materialId"`
	UserId     string    `json:"userId"`
	Name       string    `json:"name"`
	Url        string    `json:"url"`
	Type       string    `json:"type"`
	Module     string    `json:"module"`
	Creator    string    `json:"creator"`
	Thumbnail  string    `json:"thumbnail"`
	PreviewUrl string    `json:"previewUrl"`
	Duration   int64     `json:"duration"`
	Width      int64     `json:"width"`
	Height     int64     `json:"height"`
}
