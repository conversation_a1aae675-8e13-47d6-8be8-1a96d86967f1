package proto

type RiskControlImageRequery struct {
	ImgUrls []string                      `json:"imgUrls"` // 图片url
	Images  []RiskControlImageItemRequery `json:"images"`  // 图片二进制
}

type RiskControlImageItemRequery struct {
	ImageName string `json:"imageName"` // 图片名称
	ImageData string `json:"imageData"` // 图片二进制
}

type RiskControlImageResponse struct {
	CommRsp
	Result RiskControlImageCensorResult `json:"result"`
}

type RiskControlImageCensorResult struct {
	LogID    string                 `json:"logId"`    // 请求唯一id
	DataList []RiskControlImageItem `json:"dataList"` // 审核列表结果，使用空接口以接受任意类型数据
}

type RiskControlImageItem struct {
	TaskId         string                       `json:"taskId"`              // 任务id
	ErrorCode      int64                        `json:"errorCode,omitempty"` // 错误提示码，失败才返回，成功不返回
	ErrorMsg       string                       `json:"errorMsg,omitempty"`  // 错误提示信息，失败才返回，成功不返回
	Conclusion     string                       `json:"conclusion"`          // 审核结果，可取值描述：合规、不合规、疑似、审核失败
	ConclusionType int64                        `json:"conclusionType"`      // 审核结果类型
	Data           []RiskControlImageItemDetail `json:"data,omitempty"`      // 不合规/疑似/命中白名单项详细信息
}

type RiskControlImageItemDetail struct {
	ErrorCode int64  `json:"errorCode,omitempty"` // 错误提示码，失败才返回，成功不返回
	ErrorMsg  string `json:"errorMsg,omitempty"`  // 错误提示信息，失败才返回，成功不返回
	Msg       string `json:"msg"`                 // 不合规项描述信息
}
