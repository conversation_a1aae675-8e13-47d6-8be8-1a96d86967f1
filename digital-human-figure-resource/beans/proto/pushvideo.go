package proto

type CreatePushVideoRequest struct {
	UserId      string `json:"userId" form:"userId" binding:"required"`
	UserName    string `json:"userName" form:"userName" binding:"required"`
	UserChannel string `json:"userChannel" form:"userChannel" binding:"required"`
	SiteCode    string `json:"siteCode" form:"siteCode" binding:"required"`
	VideoName   string `json:"videoName" form:"videoName" binding:"required"`
	VideoId     string `json:"videoId" form:"videoId" binding:"required"`
	DraftId     string `json:"draftId" form:"draftId" binding:"required"`
}
type PushVideoRequest struct {
	VideoList []string `json:"videoList" form:"videoList" binding:"required"`
}

type VideoListModel struct {
	VideoID  string `json:"videoId" form:"videoId" binding:"required"`
	VideoURL string `json:"videoUrl,omitempty" form:"videoUrl"`
}

type InternalResponseResultModel struct {
	ReqId      string `json:"reqId" form:"reqId" binding:"required"`
	VideoId    string `json:"videoId" form:"videoId" binding:"required"`
	PushStatus int    `json:"pushStatus" form:"pushStatus" binding:"required"`
	PushMsg    string `json:"pushMsg" form:"pushMsg" binding:"required"`
}

type ResponseResultModel struct {
	ReqId       string         `json:"reqId" form:"reqId" binding:"required"`
	PushResList []PushResModel `json:"pushResList" form:"pushResList" binding:"required"`
}

type PushResModel struct {
	VideoID    string `json:"videoId" form:"videoId" binding:"required"`
	PushStatus int    `json:"pushStatus" form:"pushStatus" binding:"required"`
	PushMsg    string `json:"pushMsg" form:"pushMsg" binding:"required"`
}

type ZgImportRequest struct {
	ZgTaskArray []ZgTaskModel `json:"taskArray" form:"taskArray" binding:"required"`
}

type ZgTaskModel struct {
	FileHttpPath string         `json:"fileHttpPath" form:"fileHttpPath" binding:"required"`
	RequestId    string         `json:"requestId" form:"requestId" binding:"required"`
	SiteCode     string         `json:"siteCode" form:"siteCode" binding:"required"`
	ZgEntityData []ZgEntityData `json:"entityData" form:"entityData" binding:"required"`
}

type ZgEntityData struct {
	Key   string `json:"key" form:"key" binding:"required"`
	Value string `json:"value" form:"value" binding:"required"`
}

type ZgImportResonse struct {
	Code        int         `json:"code" form:"code" binding:"required"`
	Message     string      `json:"message" form:"message" binding:"required"`
	ZgDataModel ZgDataModel `json:"data" form:"data" binding:"required"`
}

type ZgDataModel struct {
	List []ZgListModel `json:"list" form:"list" binding:"required"`
}

type ZgListModel struct {
	RequestId string `json:"requestId" form:"requestId" binding:"required"`
	TaskUUID  string `json:"taskUUID,omitempty" form:"taskUUID" binding:"required"`
	Code      int    `json:"code" form:"code" binding:"required"`
	Message   string `json:"message,omitempty" form:"message" binding:"required"`
}
