package proto

type MaterialImportZgRequest struct {
	FileList []ImportFileModel `json:"fileList"`
}
type MaterialImportZgUrlParam struct {
	Token     string `json:"token" form:"token"`
	SiteCode  string `json:"siteCode" form:"siteCode"`
	LoginInfo string `json:"logininfo" form:"logininfo"`
	DomainId  string `json:"domainId" form:"domainId"`
	LoginName string `json:"loginName" form:"loginName"`
}

type ImportFileModel struct {
	FileName string `json:"fileName" form:"fileName"`
	FileUrl  string `json:"fileUrl" form:"fileName"`
}

type MaterialImportZgResResult struct {
	ReqId   string         `json:"reqId"`
	FileRes []ResFileModel `json:"fileRes"`
}

type ResFileModel struct {
	FileName  string `json:"fileName" form:"fileName"`
	ErrorCode int    `json:"errorCode" form:"errorCode"`
	ErrorMsg  string `json:"errorMsg" form:"errorMsg"` 
}
