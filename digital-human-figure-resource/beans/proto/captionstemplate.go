package proto

type CaptionsTemplateRequest struct {
	PageNo   int    `json:"pageNo" form:"pageNo" binding:"required"`
	PageSize int    `json:"pageSize" form:"pageSize" binding:"required"`
	Type     string `json:"type" form:"type"`
}

type CaptionsTemplateResponse struct {
	ID         int64  `json:"id"`         //  数据库 ID 字段 可以为空
	Name       string `json:"name"`       // 字幕模版名称
	TemplateID string `json:"templateId"` // 模版 ID
	Type       string `json:"type"`       // 字幕类型：动态或者静态
	Content    string `json:"content"`    // 字幕模版内容
	PngURL     string `json:"pngUrl"`     // PNG 图片 URL
	VideoURL   string `json:"videoUrl"`   // 视频 URL
	Sort       int64  `json:"sort"`
	LogId      string `json:"logId,omitempty"`
}

type CaptionsTemplateResponseWithInternal struct {
	TotalCount int64                      `json:"totalCount"`
	List       []CaptionsTemplateResponse `json:"list"`
}

type UpdateAnimationTemplateRequest struct {
	ID         int64  `json:"id"`                         //  数据库 ID 字段 可以为空
	Name       string `json:"name" binding:"required"`    // 字幕模版名称
	TemplateID string `json:"templateId"`                 // 模版 ID
	Type       string `json:"type" binding:"required"`    // 字幕类型：动态或者静态
	Content    string `json:"content" binding:"required"` // 字幕模版内容
	PngURL     string `json:"pngUrl" binding:"required"`  // 图片地址
	VideoURL   string `json:"videoUrl"`                   // 视频地址
	Sort       int64  `json:"sort"`
}

type DeleteCaptionsTemplateRequest struct {
	IDs []int64 `json:"ids" binding:"required"`
}
