package routers

import (
	"digital-human-figure-resource/handler/activity"
	dh_user "digital-human-figure-resource/handler/dh-user"
	"digital-human-figure-resource/handler/figure"
	"digital-human-figure-resource/handler/push"

	"github.com/gin-gonic/gin"
)

// Routers 路由列表
func Routers(e *gin.Engine) {
	// 动画模版接口
	// Animation template
	api := e.Group("/api/digitalhuman/figure/resource/v1")
	{ // dh_user.DhUserCheck,
		api.GET("/animation/template", dh_user.DhUserCheck, figure.ListAnimationTemplate)
		api.POST("/animation/template", dh_user.DhUserCheck, figure.CreateAnimationTemplate)
		api.PUT("/animation/template", dh_user.DhUserCheck, figure.UpdateAnimationTemplate)
	}
	{
		api.POST("/material", dh_user.DhUserCheck, figure.AddMaterial)
		api.POST("/materialdfdosi", figure.AddMaterial)
	}

	{
		api.POST("/material/zg/custom", dh_user.DhUserCheckForZg, figure.AddMaterialForZg)
		api.POST("/video/create/push", push.CreatePushVideoRecord)
		api.POST("/video/zg/push", dh_user.DhUserCheck, push.PushVideoFromFrontEnd)
		api.POST("/video/zg/internal/push", push.PushVideoFromServerInternal)
	}
	{
		api.POST("/figure/query", dh_user.DhUserCheckByPass, figure.FigureQuery)
		api.POST("/figure/collect", dh_user.DhUserCheck, figure.FigureCollect)
	}
	{
		api.GET("/manager/template/download", figure.TemplateDownload)
		api.POST("/manager/template/restore", figure.TemplateRestore)
	}
	{
		api.POST("/manager/system/figure", figure.QuerySystemList)
		api.POST("/manager/system/figure/modify", figure.ModifySystem)
	}
	{
		api.GET("/homepage/activity/banner", activity.ListHomeActivityBanner)
	}
	{
		api.POST("/figure/queryisnameused", dh_user.DhUserCheck, figure.FigureIsNameUsed)
		api.GET("/figure/queryiscreated", dh_user.DhUserCheck, figure.FigureIsHaveCreated)
	}
	{
		api.POST("/upload/file", dh_user.DhUserCheckByPass, figure.UploadFile)
		api.POST("/upload/file/sign", dh_user.DhUserCheckByPass, figure.UploadFileSign)
	}

	{
		// 字幕模版接口
		api.GET("/captions/template", dh_user.DhUserCheck, figure.CaptionsTemplate)

		api.GET("/internal/captions/template", figure.CaptionsTemplateWithInternal) // 查
		api.POST("/internal/captions/template", figure.UpdateCaptionsTemplate)      // 增/改
		api.DELETE("/internal/captions/template", figure.DeleteCaptionsTemplate)
	}
	{
		// 字体包接口
		api.GET("/fonts", dh_user.DhUserCheck, figure.Fonts)

		api.GET("/internal/fonts", figure.FontsWithInternal) // 查
		api.POST("/internal/fonts", figure.UpdateFonts)      // 增/改
		api.DELETE("/internal/fonts", figure.DeleteFonts)    // 删
	}
}
