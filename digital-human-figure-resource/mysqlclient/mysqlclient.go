package mysqlclient

import (
	"acg-ai-go-common/logger"
	config "digital-human-figure-resource/conf"
	"fmt"
	"log"
	"time"

	"gorm.io/driver/mysql"
	"gorm.io/gorm"
)

var (
	DbMap     = make(map[string]*gorm.DB)
	DbSetting = make(map[string]*config.MysqlSetting)
)

type DBName = string

const (
	StarLightDBName DBName = "starlight"
	PlatDBName      DBName = "Plat"
)

// InitDB 初始化数据库连接
func InitDB(dbname DBName, mysqlConf *config.MysqlSetting) {
	dsn := fmt.Sprintf("%s:%s@tcp(%s:%v)/%s?charset=utf8mb4&parseTime=True&loc=Local",
		mysqlConf.Username, mysqlConf.Password, mysqlConf.Host, mysqlConf.Port, mysqlConf.Database)
	db, err := gorm.Open(mysql.Open(dsn), &gorm.Config{})
	if err != nil {
		log.Fatalf("Failed to connect to database: %v", err)
		return
	}
	sqlDB, err := db.DB()
	if err != nil {
		log.Fatalf("Failed to get DB instance: %v", err)
	}
	// SetMaxOpenConns 设置打开数据库连接的最大数量。
	sqlDB.SetMaxOpenConns(mysqlConf.MaxOpenConns)
	// SetMaxIdleConns 设置连接池中的最大闲置连接数。
	sqlDB.SetMaxIdleConns(mysqlConf.MaxIdleConns)
	// SetConnMaxLifetime 设置连接可复用的最长时间。
	sqlDB.SetConnMaxLifetime(time.Hour)

	// 添加到全局变量中
	DbMap[dbname] = db
	DbSetting[dbname] = mysqlConf
}

func MonitorMysqlConnection() {
	for {
		for dbname, db := range DbMap {
			sqldb, err := db.DB()
			if err != nil {
				logger.Log.Errorf("dbname %s failed to get db instance: %v", dbname, err)
			}

			err = sqldb.Ping()
			if err != nil {
				logger.Log.Errorf("dbname %s mysql connection lost, attempting to reconnect...", dbname)
				// 尝试重新连接 Redis
				if err := reconnect(dbname, DbSetting[dbname]); err != nil {
					logger.Log.Errorf("dbname %s Failed to reconnect to mysql: %v", dbname, err)
					time.Sleep(5 * time.Second) // 当连接失败时，每隔 5 秒检查一次连接状态
					continue
				}
			}
		}

		time.Sleep(30 * time.Second) // 每隔 10 秒检查一次连接状态
	}
}

func reconnect(dbname DBName, mysqlConf *config.MysqlSetting) error {
	sqldb, err := DbMap[dbname].DB()
	if err != nil {
		return fmt.Errorf("failed to get DB instance: %w", err)
	}

	if err = sqldb.Close(); err != nil {
		logger.Log.Errorf("Failed to close DB instance: %v", err)
	}
	// 尝试重新连接 Redis
	dsn := fmt.Sprintf("%s:%s@tcp(%s:%v)/%s?charset=utf8mb4&parseTime=True&loc=Local",
		mysqlConf.Username, mysqlConf.Password,
		mysqlConf.Host, mysqlConf.Port, mysqlConf.Database)

	db, err := gorm.Open(mysql.Open(dsn), &gorm.Config{})
	if err != nil {
		return fmt.Errorf("failed to connect to database: %w", err)
	}

	sqlDB, err := db.DB()
	if err != nil {
		return fmt.Errorf("failed to get DB instance: %w", err)
	}

	// Ping 测试连接是否可用
	if err = sqlDB.Ping(); err != nil {
		return fmt.Errorf("failed to ping database: %w", err)
	}

	// SetMaxOpenConns 设置打开数据库连接的最大数量。
	sqlDB.SetMaxOpenConns(mysqlConf.MaxOpenConns)
	// SetMaxIdleConns 设置连接池中的最大闲置连接数。
	sqlDB.SetMaxIdleConns(mysqlConf.MaxIdleConns)
	// SetConnMaxLifetime 设置连接可复用的最长时间。
	sqlDB.SetConnMaxLifetime(time.Hour)

	DbMap[dbname] = db
	return nil
}
