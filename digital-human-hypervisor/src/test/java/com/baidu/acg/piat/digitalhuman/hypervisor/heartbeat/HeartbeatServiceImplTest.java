package com.baidu.acg.piat.digitalhuman.hypervisor.heartbeat;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.BDDMockito.given;
import static org.mockito.BDDMockito.then;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.times;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import com.baidu.acg.piat.digitalhuman.common.hypervisor.Status;
import com.baidu.acg.piat.digitalhuman.common.resource.ResourceInstance;
import com.baidu.acg.piat.digitalhuman.common.webrtc.resource.model.ResourceType;
import com.baidu.acg.piat.digitalhuman.common.cloud.Selectors;
import com.baidu.acg.piat.digitalhuman.hypervisor.config.SurvivalConfigure;
import com.baidu.acg.piat.digitalhuman.hypervisor.context.HypervisorContext;
import com.baidu.acg.piat.digitalhuman.hypervisor.service.HypervisorService;
import com.baidu.acg.piat.digitalhuman.hypervisor.service.impl.HeartbeatServiceImpl;
import com.baidu.acg.piat.digitalhuman.resource.pool.client.ResourcePoolClient;
import com.baidu.acg.piat.digitalhuman.resource.pool.client.ResourcePoolClientException;

class HeartbeatServiceImplTest {

    @Mock
    private ResourceInstance resourceInstance;

    @Mock
    private HypervisorContext hypervisorContext;

    @Mock
    private ResourcePoolClient resourcePoolClient;

    @Mock
    private HypervisorService hypervisorService;

    private SurvivalConfigure.Config survivalConfig = new SurvivalConfigure.Config();

    @BeforeEach
    public void init() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testConstructor() {
        assertThrows(NullPointerException.class, () -> new HeartbeatServiceImpl(null, null, null, null,
                null));
        assertDoesNotThrow(() -> new HeartbeatServiceImpl(resourceInstance, hypervisorContext,
                resourcePoolClient, new SurvivalConfigure.Config(), hypervisorService));

    }

    @Test
    public void testHeartbeat() throws ResourcePoolClientException {
        var service = new HeartbeatServiceImpl(resourceInstance, hypervisorContext, resourcePoolClient,
                survivalConfig, hypervisorService);

        given(resourceInstance.getHost()).willReturn("");
        given(resourceInstance.getResourceType()).willReturn(ResourceType.AGENT.name());
        given(hypervisorContext.getStatus()).willReturn(Status.IDLE);
        given(resourceInstance.getLabels()).willReturn(new Selectors.Labels());
        doNothing().when(resourcePoolClient).heartBeat(anyString(), any(String.class), any(Status.class), any(Selectors.Labels.class));

        service.heartbeat();

        then(resourceInstance).should(times(2)).getHost();
        then(resourceInstance).should(times(2)).getResourceType();
        then(hypervisorContext).should(times(2)).getStatus();
        then(resourcePoolClient).should(times(1)).heartBeat("", ResourceType.AGENT.name(), Status.IDLE, new Selectors.Labels());
    }
}