package com.baidu.acg.piat.digitalhuman.hypervisor.util.impl;

import com.baidu.acg.piat.digitalhuman.common.hypervisor.WebRtcProcessArguments;
import com.baidu.acg.piat.digitalhuman.hypervisor.util.ProcessCommandParser;
import org.junit.jupiter.api.Test;

import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;

class ProcessCommandParserImplTest {

    private ProcessCommandParser parser = new ProcessCommandParserImpl();

    @Test
    public void testAgent() {
        var splits = parser.parse(
                "BRTCAgent -RTC=${rtcUrl} -APPID=${appId} -TOKEN=${token} -UID=${userId}" +
                        " -ROOMNAME=${roomId} -FEEDID=${feedId} -DHA=localhost:8090 -KILLSELF_WHEN_OTHERS_LEAVING" ,
                WebRtcProcessArguments.builder()
                        .ue4Url("ue4")
                        .userToken("token")
                        .userId("userId")
                        .roomName("roomId")
                        .appId("appId")
                        .rtcServerUrl("url")
                        .feedIds(List.of("feedId"))
                        .internalRtcPlatformUrl("internalRtcPlatformUrl")
                        .build());

        assertEquals(10, splits.length);
        assertEquals("BRTCAgent -RTC=url -APPID=appId -TOKEN=token -UID=userId -ROOMNAME=roomId" +
                        " -FEEDID=feedId -DHA=localhost:8090 -KILLSELF_WHEN_OTHERS_LEAVING" +
                        " -RTC_PLATFORM=internalRtcPlatformUrl",
                String.join(" ", splits));
    }

    @Test
    public void testProxyServer() {
        var splits = parser.parse(
                "BRTCProxy -UE4=${ue4} -RTC=${rtcUrl} -APPID=${appId} -TOKEN=${token} -UID=${userId}" +
                        " -ROOMNAME=${roomId} -DHA=localhost:8090 -KILLSELF_WHEN_OTHERS_LEAVING",
                WebRtcProcessArguments.builder()
                        .ue4Url("ue4")
                        .userToken("token")
                        .userId("userId")
                        .roomName("roomId")
                        .appId("appId")
                        .rtcServerUrl("url")
                        .build());

        assertEquals(9, splits.length);
        assertEquals("BRTCProxy -UE4=ue4 -RTC=url -APPID=appId -TOKEN=token -UID=userId -ROOMNAME=roomId" +
                        " -DHA=localhost:8090 -KILLSELF_WHEN_OTHERS_LEAVING",
                String.join(" ", splits));
    }
}