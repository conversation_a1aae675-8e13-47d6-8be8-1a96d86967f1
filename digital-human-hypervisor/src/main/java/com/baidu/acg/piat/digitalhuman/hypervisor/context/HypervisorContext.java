package com.baidu.acg.piat.digitalhuman.hypervisor.context;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Getter;
import lombok.Setter;

import com.baidu.acg.piat.digitalhuman.common.hypervisor.Status;

/**
 * Hypervisor context which indicating resource instance hypervised status.
 *
 * <AUTHOR>
 */
@Getter
@Setter
public class HypervisorContext {

    private volatile Status status = Status.IDLE;

    private volatile String owner;

    private volatile boolean running = false;

    @JsonIgnore
    private Process process;


    private long activeTime = System.currentTimeMillis();
}
