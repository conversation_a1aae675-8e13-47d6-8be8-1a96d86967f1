package com.baidu.acg.piat.digitalhuman.hypervisor.service;

import com.baidu.acg.piat.digitalhuman.hypervisor.exception.HypervisorServiceException;

/**
 * Health service.
 *
 * <AUTHOR>
 */
public interface HealthService {

    /**
     * Handle a health probe.
     *
     * @param probe probe source
     * @throws HypervisorServiceException
     */
    void probe(String probe) throws HypervisorServiceException;

    /**
     * Auto release resource.
     */
    void autoRelease();
}
