package com.baidu.acg.piat.digitalhuman.hypervisor.service.impl;

import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import com.baidu.acg.piat.digitalhuman.common.hypervisor.Status;
import com.baidu.acg.piat.digitalhuman.common.resource.ResourceInstance;
import com.baidu.acg.piat.digitalhuman.hypervisor.config.SurvivalConfigure;
import com.baidu.acg.piat.digitalhuman.hypervisor.context.HypervisorContext;
import com.baidu.acg.piat.digitalhuman.hypervisor.service.HeartbeatService;
import com.baidu.acg.piat.digitalhuman.hypervisor.service.HypervisorService;
import com.baidu.acg.piat.digitalhuman.resource.pool.client.ResourcePoolClient;
import com.baidu.acg.piat.digitalhuman.resource.pool.client.ResourcePoolClientException;

@Slf4j
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class HeartbeatServiceImpl implements HeartbeatService {

    @NonNull
    private final ResourceInstance resourceInstance;

    @NonNull
    private final HypervisorContext hypervisorContext;

    @NonNull
    private final ResourcePoolClient resourcePoolClient;

    private final SurvivalConfigure.Config survivalConfig;

    private final HypervisorService hypervisorService;

    @Override
    @Scheduled(fixedDelayString = "${digitalhuman.hypervisor.heartbeat.interval:2000}")
    public void heartbeat() {
        try {
            resourcePoolClient.heartBeat(resourceInstance.getHost(), resourceInstance.getResourceType(),
                    hypervisorContext.getStatus(), resourceInstance.getLabels());
            log.debug("Sent heart beat, host={} type={} status={}", resourceInstance.getHost(),
                    resourceInstance.getResourceType(), hypervisorContext.getStatus());
        } catch (ResourcePoolClientException e) {
            log.error("Fail to send heart beat", e);
        }
    }

    @Scheduled(fixedDelayString = "${digitalhuman.hypervisor.survival.config.scanIntervalMillis:2000}")
    public void survivalExpire() {
        if (!survivalConfig.isNeedHeartbeat()) {
            return;
        }
        try {
            synchronized (hypervisorContext) {
                long expireTime = System.currentTimeMillis() - survivalConfig.getExpireTimeSeconds() * 1000;
                if (hypervisorContext.getStatus() == Status.BUSY && hypervisorContext.getActiveTime() < expireTime) {
                    log.warn("hypervisor resource expired owner={} , resource switch busy to idle");
                    hypervisorService.release(hypervisorContext.getOwner());
                }
            }
        } catch (Exception e) {
            log.error("Fail to expire survival", e);
        }
    }
}
