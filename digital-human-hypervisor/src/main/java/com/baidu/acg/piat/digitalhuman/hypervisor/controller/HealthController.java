package com.baidu.acg.piat.digitalhuman.hypervisor.controller;

import com.baidu.acg.piat.digitalhuman.common.hypervisor.request.HealthRequest;
import com.baidu.acg.piat.digitalhuman.common.hypervisor.response.HealthResponse;
import com.baidu.acg.piat.digitalhuman.hypervisor.exception.HypervisorServiceException;
import com.baidu.acg.piat.digitalhuman.hypervisor.service.HealthService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/digitalhuman/hypervisor/resource")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@ConditionalOnProperty(name = "digitalhuman.hypervisor.hypervisor.config.healthEnabled")
public class HealthController {

    private final HealthService healthService;

    @PostMapping("/health")
    public HealthResponse health(@RequestBody HealthRequest request) {
        try {
            healthService.probe(request.getOwner());
            return HealthResponse.succeed();
        } catch (HypervisorServiceException e) {
            return HealthResponse.fail(e.getMessage());
        }
    }
}
