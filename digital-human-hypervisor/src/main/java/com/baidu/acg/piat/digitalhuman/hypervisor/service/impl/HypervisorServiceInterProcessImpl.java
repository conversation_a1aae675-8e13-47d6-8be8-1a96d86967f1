package com.baidu.acg.piat.digitalhuman.hypervisor.service.impl;

import com.baidu.acg.piat.digitalhuman.common.hypervisor.Status;
import com.baidu.acg.piat.digitalhuman.common.hypervisor.WebRtcProcessArguments;
import com.baidu.acg.piat.digitalhuman.common.resource.ResourceInstance;
import com.baidu.acg.piat.digitalhuman.hypervisor.config.HypervisorConfigure.HypervisorConfig;
import com.baidu.acg.piat.digitalhuman.hypervisor.config.HypervisorConfigure.ProcessConfig;
import com.baidu.acg.piat.digitalhuman.hypervisor.context.HypervisorContext;
import com.baidu.acg.piat.digitalhuman.hypervisor.exception.HypervisorServiceException;
import com.baidu.acg.piat.digitalhuman.hypervisor.service.HypervisorEventListener;
import com.baidu.acg.piat.digitalhuman.hypervisor.service.PreExecutionInterceptor;
import com.baidu.acg.piat.digitalhuman.hypervisor.util.ProcessCommandParser;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.IOException;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;

@Slf4j
public class HypervisorServiceInterProcessImpl extends HypervisorServiceBase {

    private final ProcessCommandParser processCommandParser;

    @Autowired
    public HypervisorServiceInterProcessImpl(
            HypervisorContext hypervisorContext, ResourceInstance resourceInstance, ProcessConfig processConfig,
            HypervisorConfig hypervisorConfig, List<PreExecutionInterceptor> preExecutionInterceptors,
            List<HypervisorEventListener> eventListeners, ProcessCommandParser processCommandParser) {
        super(hypervisorContext, resourceInstance, processConfig, hypervisorConfig, preExecutionInterceptors,
                eventListeners);
        this.processCommandParser = processCommandParser;
    }

    @Override
    public void start(String owner, WebRtcProcessArguments args) throws HypervisorServiceException {

        if (Status.IDLE == hypervisorContext.getStatus()) {
            throw new HypervisorServiceException("resource has not been acquired yet");
        }

        if (StringUtils.isEmpty(owner) || !owner.equals(hypervisorContext.getOwner())) {
            throw new HypervisorServiceException("owner is not same: expect " + hypervisorContext.getOwner()
                    + " but is " + owner);
        }

        if (hypervisorContext.isRunning()) {
            throw new HypervisorServiceException("web rtc process is already running");
        }

        if (!preProcess(args)) {
            throw new HypervisorServiceException("pre-processing failed");
        }

        var realCommand = processCommandParser.parse(processConfig.getCommand(), args);
        log.info("Try to create process with start command: {}", String.join(" ", realCommand));

        Process process;
        try {
            process = createProcess(realCommand);
        } catch (IOException e) {
            log.error("Fail to start web rtc process with arguments {}", args, e);
            throw new HypervisorServiceException("Fail to start web rtc process", e);
        }
        hypervisorContext.setRunning(true);
        hypervisorContext.setProcess(process);
        log.info("web rtc process {} started", process.pid());

        CompletableFuture.runAsync(() -> {
            try {
                int exitValue = process.waitFor();
                if (exitValue != 0) {
                    log.error("Process exit with error, exit value = {}", exitValue);
                }
            } catch (InterruptedException e) {
                log.error("Process waiting thread is interrupted", e);
            } finally {
                if (process.isAlive()) {
                    process.destroyForcibly();
                }
                onClose();
                hypervisorContext.setRunning(false);
            }
        }, executor);
    }

    protected void onClose() {
        // reactive close
        log.debug("web rtc process reactive exit ...");
        if (hypervisorContext.getStatus() == Status.BUSY) {
            Optional.ofNullable(eventListeners).ifPresent(listeners -> listeners.stream()
                    .filter(HypervisorEventListener::isEnabled).forEachOrdered(HypervisorEventListener::onExit));
        }
    }

    @Override
    @Deprecated
    public void close() {
        // proactive close
        log.debug("web rtc process proactive exit...");
        if (hypervisorContext.getStatus() == Status.BUSY) {
            hypervisorContext.setStatus(Status.IDLE);
            if (hypervisorContext.getProcess() != null && hypervisorContext.getProcess().isAlive()) {
                hypervisorContext.getProcess().destroyForcibly();
            }
        }
    }

    @Override
    public void stop(String owner) throws HypervisorServiceException {

        if (Status.IDLE == hypervisorContext.getStatus()) {
            throw new HypervisorServiceException("resource has not been acquired yet");
        }

        if (StringUtils.isEmpty(owner) || !owner.equals(hypervisorContext.getOwner())) {
            throw new HypervisorServiceException("owner is not same: expect " + hypervisorContext.getOwner()
                    + " but is " + owner);
        }

        if (hypervisorContext.isRunning()) {
            log.debug("try to stop web rtc process");
            if (hypervisorContext.getProcess() != null && hypervisorContext.getProcess().isAlive()) {
                hypervisorContext.getProcess().destroyForcibly();
            }
            hypervisorContext.setRunning(false);
        }
    }

    private Process createProcess(String[] command) throws IOException {
        ProcessBuilder processBuilder = new ProcessBuilder(command);

        if (!StringUtils.isEmpty(processConfig.getWorkDir())) {
            processBuilder.directory(Paths.get(processConfig.getWorkDir()).toFile());
        }

        var processTimestamp = LocalDateTime.now().format(dateTimeFormatter);

        processBuilder.redirectError(Paths.get(hypervisorConfig.getSubProcessLogDir(),
                processTimestamp.concat(".error")).toFile());
        processBuilder.redirectOutput(Paths.get(hypervisorConfig.getSubProcessLogDir(),
                processTimestamp.concat(".output")).toFile());
        return processBuilder.start();
    }
}
