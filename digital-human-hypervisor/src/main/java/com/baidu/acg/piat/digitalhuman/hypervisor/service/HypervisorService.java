package com.baidu.acg.piat.digitalhuman.hypervisor.service;

import com.baidu.acg.piat.digitalhuman.common.hypervisor.WebRtcProcessArguments;
import com.baidu.acg.piat.digitalhuman.common.resource.ResourceInstance;
import com.baidu.acg.piat.digitalhuman.hypervisor.exception.HypervisorServiceException;

public interface HypervisorService {

    /**
     * Acquire a resource for the owner if it is possible.
     *
     * @param owner the owner who want to acquire a resource
     * @return acquired resource instance
     * @throws HypervisorServiceException acquire exception
     */
    ResourceInstance acquire(String owner) throws HypervisorServiceException;

    /**
     * Release the resource if the owner acquired.
     *
     * @param owner the owner who acquired a resource
     * @return acquired resource instance
     * @throws HypervisorServiceException release exception
     */
    ResourceInstance release(String owner) throws HypervisorServiceException;

    void heartbeat(String owner) throws HypervisorServiceException;

    /**
     * Start web rtc process with specified arguments.
     *
     * @param owner the owner to start process
     * @param args  arguments used to start the process.
     * @throws HypervisorServiceException start exception
     */
    void start(String owner, WebRtcProcessArguments args) throws HypervisorServiceException;

    /**
     * Close the hypervised process.
     *
     * @deprecated use {@link HypervisorService#stop(String)}
     */
    @Deprecated
    void close();

    /**
     * Stop web rtc process.
     *
     * @param owner the owner to stop process
     */
    void stop(String owner) throws HypervisorServiceException;
}
