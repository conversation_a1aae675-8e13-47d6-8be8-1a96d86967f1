package com.baidu.acg.piat.digitalhuman.hypervisor.service.impl;

import com.baidu.acg.piat.digitalhuman.common.hypervisor.WebRtcProcessArguments;
import com.baidu.acg.piat.digitalhuman.common.resource.ResourceInstance;
import com.baidu.acg.piat.digitalhuman.hypervisor.config.HypervisorConfigure.HypervisorConfig;
import com.baidu.acg.piat.digitalhuman.hypervisor.config.HypervisorConfigure.ProcessConfig;
import com.baidu.acg.piat.digitalhuman.hypervisor.context.HypervisorContext;
import com.baidu.acg.piat.digitalhuman.hypervisor.service.HypervisorEventListener;
import com.baidu.acg.piat.digitalhuman.hypervisor.service.PreExecutionInterceptor;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

@Slf4j
public class HypervisorServiceNoopImpl extends HypervisorServiceBase {

    public HypervisorServiceNoopImpl(
            HypervisorContext hypervisorContext, ResourceInstance resourceInstance, ProcessConfig processConfig,
            HypervisorConfig hypervisorConfig, List<PreExecutionInterceptor> preExecutionInterceptors,
            List<HypervisorEventListener> eventListeners) {
        super(hypervisorContext, resourceInstance, processConfig, hypervisorConfig, preExecutionInterceptors,
                eventListeners);
    }

    @Override
    public void start(String owner, WebRtcProcessArguments args) {
    }

    @Override
    public void close() {
    }

    @Override
    public void stop(String owner) {
    }
}
