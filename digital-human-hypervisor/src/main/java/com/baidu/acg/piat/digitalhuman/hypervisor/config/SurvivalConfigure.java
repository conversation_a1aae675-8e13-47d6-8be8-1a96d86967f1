// Copyright (C) 2019 Baidu Inc. All rights reserved.
package com.baidu.acg.piat.digitalhuman.hypervisor.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * SurvivalConfigure
 *
 * <AUTHOR>
 * @since 2019-11-05
 */
@Configuration
public class SurvivalConfigure {

    @Bean
    @ConfigurationProperties(prefix = "digitalhuman.hypervisor.survival.config")
    public Config survivalConfig() {
        return new Config();
    }

    @Data
    public static class Config {

        private boolean needHeartbeat = false;

        private long expireTimeSeconds = 30;

        private long scanIntervalMillis = 2000;
    }
}
