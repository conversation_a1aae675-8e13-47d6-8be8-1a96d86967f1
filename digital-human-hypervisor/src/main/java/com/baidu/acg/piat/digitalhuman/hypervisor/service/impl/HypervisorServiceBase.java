package com.baidu.acg.piat.digitalhuman.hypervisor.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.concurrent.Executor;
import java.util.concurrent.Executors;

import javax.annotation.PostConstruct;

import com.baidu.acg.piat.digitalhuman.common.hypervisor.Status;
import com.baidu.acg.piat.digitalhuman.common.hypervisor.WebRtcProcessArguments;
import com.baidu.acg.piat.digitalhuman.common.resource.ResourceInstance;
import com.baidu.acg.piat.digitalhuman.hypervisor.config.HypervisorConfigure.HypervisorConfig;
import com.baidu.acg.piat.digitalhuman.hypervisor.config.HypervisorConfigure.ProcessConfig;
import com.baidu.acg.piat.digitalhuman.hypervisor.context.HypervisorContext;
import com.baidu.acg.piat.digitalhuman.hypervisor.exception.HypervisorServiceException;
import com.baidu.acg.piat.digitalhuman.hypervisor.service.HypervisorEventListener;
import com.baidu.acg.piat.digitalhuman.hypervisor.service.HypervisorService;
import com.baidu.acg.piat.digitalhuman.hypervisor.service.PreExecutionInterceptor;

@Slf4j
@RequiredArgsConstructor
public abstract class HypervisorServiceBase implements HypervisorService {

    protected final HypervisorContext hypervisorContext;

    protected final ResourceInstance resourceInstance;

    protected final ProcessConfig processConfig;

    protected final HypervisorConfig hypervisorConfig;

    private final List<PreExecutionInterceptor> preExecutionInterceptors;

    protected final List<HypervisorEventListener> eventListeners;

    protected final Executor executor = Executors.newFixedThreadPool(1);

    protected DateTimeFormatter dateTimeFormatter;

    @PostConstruct
    public void init() throws IOException {
        var path = Files.createDirectories(Paths.get(hypervisorConfig.getSubProcessLogDir()));
        dateTimeFormatter = DateTimeFormatter.ofPattern(hypervisorConfig.getSubProcessLogDateFormat());
        log.info("Initialized hypervisor service with: command = {}, subProcessLogDir = {}, workDir = {}",
                processConfig.getCommand(), path, processConfig.getWorkDir());
    }

    @Override
    public synchronized ResourceInstance acquire(String owner) throws HypervisorServiceException {
        if (Status.IDLE != hypervisorContext.getStatus()) {
            throw new HypervisorServiceException("resource is not available");
        }

        hypervisorContext.setStatus(Status.BUSY);
        hypervisorContext.setOwner(owner);
        return resourceInstance;
    }

    @Override
    public synchronized ResourceInstance release(String owner) throws HypervisorServiceException {
        if (Status.IDLE == hypervisorContext.getStatus()) {
            throw new HypervisorServiceException("resource has no need to release");
        }

        if (StringUtils.isEmpty(owner) || !owner.equals(hypervisorContext.getOwner())) {
            throw new HypervisorServiceException("owner is not same: expect "
                    + hypervisorContext.getOwner() + " but is " + owner);
        }

        hypervisorContext.setStatus(Status.IDLE);
        hypervisorContext.setOwner(null);
        return resourceInstance;
    }

    @Override
    public void heartbeat(String owner) throws HypervisorServiceException {
        if (!StringUtils.isEmpty(hypervisorContext.getOwner()) &&
                hypervisorContext.getOwner().equals(owner)) {
            hypervisorContext.setActiveTime(System.currentTimeMillis());
        }
    }

    protected boolean preProcess(WebRtcProcessArguments args) {
        if (preExecutionInterceptors != null) {
            for (PreExecutionInterceptor interceptor : preExecutionInterceptors) {
                if (interceptor.isEnabled() && !interceptor.preProcess(args)) {
                    return false;
                }
            }
        }
        return true;
    }
}
