package com.baidu.acg.piat.digitalhuman.hypervisor;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.scheduling.annotation.EnableScheduling;

import com.baidu.acg.piat.digitalhuman.common.advice.GlobalControllerAdvice;
import com.baidu.acg.piat.digitalhuman.common.healthz.CommonHealthzController;

@EnableScheduling
@SpringBootApplication(scanBasePackageClasses = {
        GlobalControllerAdvice.class,
        CommonHealthzController.class
})
public class DigitalHumanHypervisorApplication {

    public static void main(String[] args) {
        SpringApplication.run(DigitalHumanHypervisorApplication.class, args);
    }
}
