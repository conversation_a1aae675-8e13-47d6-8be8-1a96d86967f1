package com.baidu.acg.piat.digitalhuman.hypervisor.controller;

import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.baidu.acg.piat.digitalhuman.common.hypervisor.request.HypervisorRequest;
import com.baidu.acg.piat.digitalhuman.common.hypervisor.response.HypervisorResponse;
import com.baidu.acg.piat.digitalhuman.hypervisor.context.HypervisorContext;
import com.baidu.acg.piat.digitalhuman.hypervisor.exception.HypervisorServiceException;
import com.baidu.acg.piat.digitalhuman.hypervisor.service.HypervisorService;

@RestController
@RequestMapping("/api/digitalhuman")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class HypervisorController {

    private final HypervisorService hypervisorService;

    private final HypervisorContext hypervisorContext;

    /**
     * todo 优化资源acquire的接口，目前直接保持跟render-vis-2d的acquire 接口保持一致。
     *
     * @param request
     * @return
     */
    @PostMapping("/render/proxy/vis2d/resource/acquire")
    public HypervisorResponse acquire(@RequestBody HypervisorRequest request) {
        try {
            return HypervisorResponse.succeed(hypervisorService.acquire(request.getOwner()));
        } catch (HypervisorServiceException e) {
            return HypervisorResponse.fail(e.getMessage());
        }
    }

    @PostMapping("/hypervisor/resource/release")
    public HypervisorResponse release(@RequestBody HypervisorRequest request) {
        try {
            return HypervisorResponse.succeed(hypervisorService.release(request.getOwner()));
        } catch (HypervisorServiceException e) {
            return HypervisorResponse.fail(e.getMessage());
        }
    }

    @PostMapping("/hypervisor/resource/heartbeat")
    public HypervisorResponse heartbeat(@RequestBody HypervisorRequest request) {
        try {
            hypervisorService.heartbeat(request.getOwner());
            return HypervisorResponse.succeed();
        } catch (HypervisorServiceException e) {
            return HypervisorResponse.fail(e.getMessage());
        }
    }


    @PostMapping("/hypervisor/process/start")
    public HypervisorResponse start(@RequestBody HypervisorRequest request) {
        try {
            hypervisorService.start(request.getOwner(), request.getArgs());
            return HypervisorResponse.succeed();
        } catch (HypervisorServiceException e) {
            return HypervisorResponse.fail(e.getMessage());
        }
    }

    @PostMapping("/hypervisor/process/stop")
    public HypervisorResponse stop(@RequestBody HypervisorRequest request) {
        try {
            hypervisorService.stop(request.getOwner());
            return HypervisorResponse.succeed();
        } catch (HypervisorServiceException e) {
            return HypervisorResponse.fail(e.getMessage());
        }
    }


    @GetMapping("/hypervisor/resource/context")
    public HypervisorContext context() {
        return hypervisorContext;
    }


}
