package com.baidu.acg.piat.digitalhuman.hypervisor.service;

import com.baidu.acg.piat.digitalhuman.common.hypervisor.WebRtcProcessArguments;
import org.springframework.core.Ordered;

public interface PreExecutionInterceptor extends Ordered {

    /**
     * Do some pre-processing based on the execution arguments before process starts.
     *
     * @param args web rtc process arguments
     * @return true if pre-process succeeds
     */
    default boolean preProcess(WebRtcProcessArguments args) {
        return true;
    }

    /**
     * If the interceptor enabled.
     *
     * @return true if the interceptor is enabled.
     */
    boolean isEnabled();
}
