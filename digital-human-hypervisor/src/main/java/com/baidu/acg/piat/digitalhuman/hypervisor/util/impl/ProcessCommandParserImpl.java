package com.baidu.acg.piat.digitalhuman.hypervisor.util.impl;

import com.baidu.acg.piat.digitalhuman.common.hypervisor.WebRtcProcessArguments;
import com.baidu.acg.piat.digitalhuman.hypervisor.util.ProcessCommandParser;
import org.springframework.util.CollectionUtils;

public class ProcessCommandParserImpl implements ProcessCommandParser {

    @Override
    public String[] parse(String command, WebRtcProcessArguments args) {
        return command.replace("${rtcUrl}", args.getRtcServerUrl())
                .replace("${appId}", args.getAppId())
                .replace("${roomId}", args.getRoomName())
                .replace("${userId}", args.getUserId())
                .replace("${token}", args.getUserToken())
                .replace("${feedId}", CollectionUtils.isEmpty(args.getFeedIds()) ? "" : args.getFeedIds().get(0))
                .replace("${ue4}", args.getUe4Url() == null ? "" : args.getUe4Url())
                .concat(args.getInternalRtcPlatformUrl() == null ? "" : " -RTC_PLATFORM=".concat(
                        args.getInternalRtcPlatformUrl()))
                .split(" ");
    }
}
