package com.baidu.acg.piat.digitalhuman.hypervisor.config;

import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.List;

import com.baidu.acg.piat.digitalhuman.common.config.ResourceInstanceConfig;
import com.baidu.acg.piat.digitalhuman.common.hypervisor.Status;
import com.baidu.acg.piat.digitalhuman.common.resource.ResourceInstance;
import com.baidu.acg.piat.digitalhuman.hypervisor.context.HypervisorContext;
import com.baidu.acg.piat.digitalhuman.hypervisor.service.HypervisorEventListener;
import com.baidu.acg.piat.digitalhuman.hypervisor.service.HypervisorService;
import com.baidu.acg.piat.digitalhuman.hypervisor.service.PreExecutionInterceptor;
import com.baidu.acg.piat.digitalhuman.hypervisor.service.impl.HypervisorServiceInterProcessImpl;
import com.baidu.acg.piat.digitalhuman.hypervisor.service.impl.HypervisorServiceLibraryImpl;
import com.baidu.acg.piat.digitalhuman.hypervisor.service.impl.HypervisorServiceNoopImpl;
import com.baidu.acg.piat.digitalhuman.hypervisor.util.ProcessCommandParser;
import com.baidu.acg.piat.digitalhuman.hypervisor.util.impl.ProcessCommandParserImpl;

@Configuration
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class HypervisorConfigure {

    private final List<PreExecutionInterceptor> preExecutionInterceptors;

    private final List<HypervisorEventListener> hypervisorEventListeners;

    @Bean
    @ConfigurationProperties(prefix = "digitalhuman.hypervisor.process.config")
    public ProcessConfig processConfig() {
        return new ProcessConfig();
    }

    @Bean
    @ConfigurationProperties(prefix = "digitalhuman.hypervisor.hypervisor.config")
    public HypervisorConfig hypervisorConfig() {
        return new HypervisorConfig();
    }

    @Bean
    public HypervisorContext hypervisorContext() {
        var context = new HypervisorContext();
        context.setStatus(hypervisorConfig().getInitialStatus());
        return context;
    }

    @Bean
    @ConfigurationProperties(prefix = "digitalhuman.hypervisor.resource.config")
    public ResourceInstanceConfig resourceInstanceConfig() {
        return new ResourceInstanceConfig();
    }

    @Bean
    public ResourceInstance resourceInstance() {
        return new ResourceInstance(resourceInstanceConfig());
    }

    @Bean
    public ProcessCommandParser processCommandParser() {
        return new ProcessCommandParserImpl();
    }

    @Bean
    public HypervisorService hypervisorService() {
        HypervisorService service = null;
        switch (hypervisorConfig().getHypervisorType()) {
            case INTER_PROCESS:
                service = new HypervisorServiceInterProcessImpl(hypervisorContext(), resourceInstance(),
                        processConfig(), hypervisorConfig(), preExecutionInterceptors, hypervisorEventListeners,
                        processCommandParser());
                break;
            case LIBRARY:
                service = new HypervisorServiceLibraryImpl(hypervisorContext(), resourceInstance(), processConfig(),
                        hypervisorConfig(), preExecutionInterceptors, hypervisorEventListeners);
                break;
            case NO_OP:
                service = new HypervisorServiceNoopImpl(hypervisorContext(), resourceInstance(), processConfig(),
                        hypervisorConfig(), preExecutionInterceptors, hypervisorEventListeners);
                break;
        }
        return service;
    }

//
//    @Bean
//    @Deprecated
//    @ConfigurationProperties(prefix = "digitalhuman.hypervisor")
//    public LabelsConfig labels() {
//        return new LabelsConfig();
//    }
//
//    @Data
//    public static class LabelsConfig {
//        Selectors.Labels labels;
//    }

    @Getter
    @Setter
    public static class ProcessConfig {

        private String command;

        private String workDir;
    }

    @Getter
    @Setter
    public static class HypervisorConfig {

        private HypervisorType hypervisorType = HypervisorType.INTER_PROCESS;

        private String subProcessLogDir = "/home/<USER>/digital-human-hypervisor/logs/subprocess/";

        private String subProcessLogDateFormat = "yyyy-MM-dd'T'HH:mm:ss'Z'";

        private Status initialStatus = Status.IDLE;

        private long autoReleaseIntervalMilliseconds = 15000;
    }

    public enum HypervisorType {
        INTER_PROCESS, LIBRARY, NO_OP
    }
}
