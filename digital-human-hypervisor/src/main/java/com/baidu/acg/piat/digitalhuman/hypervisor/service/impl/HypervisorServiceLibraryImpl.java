package com.baidu.acg.piat.digitalhuman.hypervisor.service.impl;

import com.baidu.acg.piat.digitalhuman.common.hypervisor.WebRtcProcessArguments;
import com.baidu.acg.piat.digitalhuman.common.resource.ResourceInstance;
import com.baidu.acg.piat.digitalhuman.hypervisor.config.HypervisorConfigure.HypervisorConfig;
import com.baidu.acg.piat.digitalhuman.hypervisor.config.HypervisorConfigure.ProcessConfig;
import com.baidu.acg.piat.digitalhuman.hypervisor.context.HypervisorContext;
import com.baidu.acg.piat.digitalhuman.hypervisor.exception.HypervisorServiceException;
import com.baidu.acg.piat.digitalhuman.hypervisor.service.HypervisorEventListener;
import com.baidu.acg.piat.digitalhuman.hypervisor.service.PreExecutionInterceptor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * TODO: use gstreamer
 */
@Slf4j
public class HypervisorServiceLibraryImpl extends HypervisorServiceBase {

    @Autowired
    public HypervisorServiceLibraryImpl(
            HypervisorContext hypervisorContext, ResourceInstance resourceInstance, ProcessConfig processConfig,
            HypervisorConfig hypervisorConfig, List<PreExecutionInterceptor> preExecutionInterceptors,
            List<HypervisorEventListener> eventListeners) {
        super(hypervisorContext, resourceInstance, processConfig, hypervisorConfig, preExecutionInterceptors,
                eventListeners);
    }

    @Override
    public void start(String owner, WebRtcProcessArguments args) throws HypervisorServiceException {
        throw new HypervisorServiceException("Unimplemented");
    }

    @Override
    @Deprecated
    public void close() {
    }

    @Override
    public void stop(String owner) throws HypervisorServiceException {
        throw new HypervisorServiceException("Unimplemented");
    }

}
