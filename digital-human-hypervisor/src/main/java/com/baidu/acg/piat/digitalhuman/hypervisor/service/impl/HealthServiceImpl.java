package com.baidu.acg.piat.digitalhuman.hypervisor.service.impl;

import com.baidu.acg.piat.digitalhuman.common.hypervisor.Status;
import com.baidu.acg.piat.digitalhuman.hypervisor.context.HypervisorContext;
import com.baidu.acg.piat.digitalhuman.hypervisor.exception.HypervisorServiceException;
import com.baidu.acg.piat.digitalhuman.hypervisor.service.HealthService;
import com.baidu.acg.piat.digitalhuman.hypervisor.service.HypervisorService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Slf4j
@Service
@ConditionalOnProperty(name = "digitalhuman.hypervisor.hypervisor.config.healthEnabled")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class HealthServiceImpl implements HealthService {

    private final HypervisorContext hypervisorContext;

    private final HypervisorService hypervisorService;

    private Map<String, Boolean> probes = new ConcurrentHashMap<>();

    @Override
    public void probe(String probe) throws HypervisorServiceException {
        if (Status.IDLE == hypervisorContext.getStatus()) {
            throw new HypervisorServiceException("resource has not been acquired yet");
        }

        if (StringUtils.isEmpty(probe) || !probe.equals(hypervisorContext.getOwner())) {
            throw new HypervisorServiceException("owner is not same: expect " + hypervisorContext.getOwner()
                    + " but is " + probe);
        }

        probes.put(probe, true);
    }

    @Override
    @Scheduled(fixedRateString = "${digitalhuman.hypervisor.hypervisor.config.auto-release-interval-milliseconds}")
    public void autoRelease() {
        log.info("auto releasing...");
        probes.forEach((owner, heartBeat) -> {
            if (Status.BUSY == hypervisorContext.getStatus()) {
                if (!owner.equals(hypervisorContext.getOwner())) {
                    probes.remove(owner);
                } else {
                    if (!heartBeat) {
                        try {
                            hypervisorService.stop(owner);
                            hypervisorService.release(owner);
                        } catch (HypervisorServiceException e) {
                            log.error("Fail to auto release resource owned by {}", owner, e);
                        }
                        probes.remove(owner);
                        log.debug("auto released resource owned by {}", owner);
                    } else {
                        probes.put(owner, false);
                    }
                }
            }
        });
        if (Status.BUSY == hypervisorContext.getStatus() && !probes.containsKey(hypervisorContext.getOwner())) {
            log.debug("Auto release first meet owner {}", hypervisorContext.getOwner());
            probes.put(hypervisorContext.getOwner(), false);
        }
    }
}
